"""
Campaign Management System for NexusScan Desktop
Enterprise-grade campaign management with project organization and tracking.
"""

import asyncio
import logging
import json
import time
import uuid
from typing import Dict, List, Optional, Any, Set, Tuple
from enum import Enum
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from pathlib import Path

from core.config import Config
from core.database import DatabaseManager
from core.events import EventManager, EventTypes
from ai.ai_service import AIServiceManager

logger = logging.getLogger(__name__)


class CampaignStatus(Enum):
    """Campaign execution status"""
    DRAFT = "draft"
    PLANNED = "planned"
    ACTIVE = "active"
    PAUSED = "paused"
    COMPLETED = "completed"
    CANCELLED = "cancelled"
    ARCHIVED = "archived"


class CampaignType(Enum):
    """Types of security campaigns"""
    PENETRATION_TEST = "penetration_test"
    VULNERABILITY_ASSESSMENT = "vulnerability_assessment"
    COMPLIANCE_AUDIT = "compliance_audit"
    RED_TEAM_EXERCISE = "red_team_exercise"
    SECURITY_ASSESSMENT = "security_assessment"
    INCIDENT_RESPONSE = "incident_response"
    TRAINING_EXERCISE = "training_exercise"


class TaskStatus(Enum):
    """Individual task status"""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    SKIPPED = "skipped"
    BLOCKED = "blocked"


class Priority(Enum):
    """Task and campaign priority levels"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


@dataclass
class CampaignTask:
    """Individual task within a campaign"""
    task_id: str
    campaign_id: str
    title: str
    description: str
    task_type: str  # scan, exploit, report, etc.
    assigned_to: Optional[str]
    status: TaskStatus
    priority: Priority
    created_date: datetime
    due_date: Optional[datetime]
    started_date: Optional[datetime]
    completed_date: Optional[datetime]
    estimated_hours: Optional[float]
    actual_hours: Optional[float]
    dependencies: List[str]  # Task IDs that must complete first
    tags: List[str]
    attachments: List[str]  # File paths
    notes: List[str]
    progress_percentage: int
    execution_config: Dict[str, Any]
    results: Optional[Dict[str, Any]]


@dataclass
class CampaignMilestone:
    """Campaign milestone tracking"""
    milestone_id: str
    campaign_id: str
    title: str
    description: str
    due_date: datetime
    completed_date: Optional[datetime]
    status: str  # pending, completed, overdue
    associated_tasks: List[str]
    deliverables: List[str]


@dataclass
class CampaignMetrics:
    """Campaign performance metrics"""
    campaign_id: str
    total_tasks: int
    completed_tasks: int
    failed_tasks: int
    progress_percentage: float
    estimated_hours: float
    actual_hours: float
    budget_allocated: float
    budget_spent: float
    vulnerabilities_found: int
    critical_findings: int
    compliance_score: float
    team_utilization: float
    client_satisfaction: Optional[float]


@dataclass
class Campaign:
    """Complete campaign definition"""
    campaign_id: str
    name: str
    description: str
    campaign_type: CampaignType
    status: CampaignStatus
    priority: Priority
    client_name: str
    project_code: str
    created_by: str
    assigned_team: List[str]
    created_date: datetime
    start_date: Optional[datetime]
    end_date: Optional[datetime]
    actual_end_date: Optional[datetime]
    budget: Optional[float]
    scope: Dict[str, Any]  # Targets, objectives, constraints
    methodologies: List[str]  # OWASP, NIST, etc.
    compliance_frameworks: List[str]
    tasks: List[CampaignTask]
    milestones: List[CampaignMilestone]
    metrics: Optional[CampaignMetrics]
    configuration: Dict[str, Any]
    metadata: Dict[str, Any]


class CampaignManager:
    """Enterprise campaign management system"""
    
    def __init__(self, config: Config, db_manager: DatabaseManager,
                 event_manager: EventManager, ai_service: AIServiceManager):
        """Initialize campaign manager"""
        self.config = config
        self.db_manager = db_manager
        self.event_manager = event_manager
        self.ai_service = ai_service
        
        # Campaign storage
        self.campaigns: Dict[str, Campaign] = {}
        self.active_campaigns: Set[str] = set()
        self.campaign_templates: Dict[str, Dict[str, Any]] = {}
        
        # Load campaign templates
        self._load_campaign_templates()
        
        logger.info("Campaign management system initialized")
    
    def _load_campaign_templates(self):
        """Load built-in campaign templates"""
        # Penetration Testing Campaign Template
        pentest_template = {
            "name": "Standard Penetration Test",
            "description": "Comprehensive penetration testing campaign",
            "campaign_type": CampaignType.PENETRATION_TEST,
            "estimated_duration_days": 14,
            "default_tasks": [
                {
                    "title": "Information Gathering",
                    "description": "Reconnaissance and information collection",
                    "task_type": "reconnaissance",
                    "estimated_hours": 8,
                    "priority": Priority.HIGH
                },
                {
                    "title": "Vulnerability Scanning",
                    "description": "Automated vulnerability assessment",
                    "task_type": "vulnerability_scan",
                    "estimated_hours": 12,
                    "priority": Priority.HIGH
                },
                {
                    "title": "Manual Testing",
                    "description": "Manual security testing and exploitation",
                    "task_type": "manual_testing",
                    "estimated_hours": 24,
                    "priority": Priority.CRITICAL
                },
                {
                    "title": "Post-Exploitation",
                    "description": "Post-exploitation activities and persistence",
                    "task_type": "post_exploitation",
                    "estimated_hours": 16,
                    "priority": Priority.HIGH
                },
                {
                    "title": "Reporting",
                    "description": "Generate comprehensive security report",
                    "task_type": "reporting",
                    "estimated_hours": 8,
                    "priority": Priority.MEDIUM
                }
            ],
            "default_milestones": [
                {
                    "title": "Planning Complete",
                    "description": "Campaign planning and scoping completed",
                    "days_offset": 1
                },
                {
                    "title": "Discovery Phase Complete",
                    "description": "Information gathering and scanning completed",
                    "days_offset": 5
                },
                {
                    "title": "Testing Phase Complete",
                    "description": "Active testing and exploitation completed",
                    "days_offset": 10
                },
                {
                    "title": "Final Report Delivered",
                    "description": "Comprehensive report delivered to client",
                    "days_offset": 14
                }
            ]
        }
        
        # Vulnerability Assessment Template
        vuln_assessment_template = {
            "name": "Vulnerability Assessment",
            "description": "Comprehensive vulnerability assessment campaign",
            "campaign_type": CampaignType.VULNERABILITY_ASSESSMENT,
            "estimated_duration_days": 7,
            "default_tasks": [
                {
                    "title": "Asset Discovery",
                    "description": "Identify and catalog target assets",
                    "task_type": "asset_discovery",
                    "estimated_hours": 4,
                    "priority": Priority.HIGH
                },
                {
                    "title": "Vulnerability Scanning",
                    "description": "Automated vulnerability scanning",
                    "task_type": "vulnerability_scan",
                    "estimated_hours": 8,
                    "priority": Priority.HIGH
                },
                {
                    "title": "Manual Verification",
                    "description": "Manual verification of critical findings",
                    "task_type": "manual_verification",
                    "estimated_hours": 12,
                    "priority": Priority.MEDIUM
                },
                {
                    "title": "Risk Assessment",
                    "description": "Assess and prioritize identified risks",
                    "task_type": "risk_assessment",
                    "estimated_hours": 6,
                    "priority": Priority.HIGH
                },
                {
                    "title": "Vulnerability Report",
                    "description": "Generate vulnerability assessment report",
                    "task_type": "reporting",
                    "estimated_hours": 4,
                    "priority": Priority.MEDIUM
                }
            ],
            "default_milestones": [
                {
                    "title": "Scanning Complete",
                    "description": "All vulnerability scans completed",
                    "days_offset": 3
                },
                {
                    "title": "Analysis Complete",
                    "description": "Vulnerability analysis and verification completed",
                    "days_offset": 5
                },
                {
                    "title": "Report Delivered",
                    "description": "Final vulnerability report delivered",
                    "days_offset": 7
                }
            ]
        }
        
        # Compliance Audit Template
        compliance_template = {
            "name": "Compliance Audit",
            "description": "Security compliance audit campaign",
            "campaign_type": CampaignType.COMPLIANCE_AUDIT,
            "estimated_duration_days": 21,
            "default_tasks": [
                {
                    "title": "Compliance Framework Mapping",
                    "description": "Map requirements to applicable frameworks",
                    "task_type": "compliance_mapping",
                    "estimated_hours": 8,
                    "priority": Priority.HIGH
                },
                {
                    "title": "Evidence Collection",
                    "description": "Collect compliance evidence and documentation",
                    "task_type": "evidence_collection",
                    "estimated_hours": 24,
                    "priority": Priority.CRITICAL
                },
                {
                    "title": "Gap Analysis",
                    "description": "Identify compliance gaps and deficiencies",
                    "task_type": "gap_analysis",
                    "estimated_hours": 16,
                    "priority": Priority.HIGH
                },
                {
                    "title": "Remediation Planning",
                    "description": "Develop remediation recommendations",
                    "task_type": "remediation_planning",
                    "estimated_hours": 12,
                    "priority": Priority.MEDIUM
                },
                {
                    "title": "Compliance Report",
                    "description": "Generate comprehensive compliance report",
                    "task_type": "reporting",
                    "estimated_hours": 8,
                    "priority": Priority.MEDIUM
                }
            ],
            "default_milestones": [
                {
                    "title": "Evidence Collection Complete",
                    "description": "All compliance evidence collected",
                    "days_offset": 10
                },
                {
                    "title": "Gap Analysis Complete",
                    "description": "Compliance gap analysis completed",
                    "days_offset": 15
                },
                {
                    "title": "Final Report Delivered",
                    "description": "Compliance audit report delivered",
                    "days_offset": 21
                }
            ]
        }
        
        self.campaign_templates = {
            "penetration_test": pentest_template,
            "vulnerability_assessment": vuln_assessment_template,
            "compliance_audit": compliance_template
        }
        
        logger.info(f"Loaded {len(self.campaign_templates)} campaign templates")
    
    async def create_campaign(self, campaign_data: Dict[str, Any], 
                            template_id: Optional[str] = None) -> str:
        """Create new security campaign"""
        try:
            campaign_id = f"camp_{uuid.uuid4().hex[:8]}"
            
            # Apply template if specified
            if template_id and template_id in self.campaign_templates:
                template = self.campaign_templates[template_id]
                campaign_data = self._apply_template(campaign_data, template)
            
            # Create campaign object
            campaign = Campaign(
                campaign_id=campaign_id,
                name=campaign_data["name"],
                description=campaign_data.get("description", ""),
                campaign_type=CampaignType(campaign_data.get("campaign_type", "penetration_test")),
                status=CampaignStatus.DRAFT,
                priority=Priority(campaign_data.get("priority", "medium")),
                client_name=campaign_data.get("client_name", ""),
                project_code=campaign_data.get("project_code", f"PROJ-{campaign_id[:6].upper()}"),
                created_by=campaign_data.get("created_by", "unknown"),
                assigned_team=campaign_data.get("assigned_team", []),
                created_date=datetime.now(),
                start_date=None,
                end_date=None,
                actual_end_date=None,
                budget=campaign_data.get("budget"),
                scope=campaign_data.get("scope", {}),
                methodologies=campaign_data.get("methodologies", []),
                compliance_frameworks=campaign_data.get("compliance_frameworks", []),
                tasks=[],
                milestones=[],
                metrics=None,
                configuration=campaign_data.get("configuration", {}),
                metadata=campaign_data.get("metadata", {})
            )
            
            # Create default tasks if template was used
            if template_id and "default_tasks" in self.campaign_templates[template_id]:
                await self._create_default_tasks(campaign, self.campaign_templates[template_id])
            
            # Create default milestones if template was used
            if template_id and "default_milestones" in self.campaign_templates[template_id]:
                await self._create_default_milestones(campaign, self.campaign_templates[template_id])
            
            # Store campaign
            self.campaigns[campaign_id] = campaign
            
            # Emit campaign created event
            await self.event_manager.emit(
                EventTypes.CAMPAIGN_CREATED,
                {
                    "campaign_id": campaign_id,
                    "name": campaign.name,
                    "type": campaign.campaign_type.value,
                    "created_by": campaign.created_by
                },
                "campaign_manager"
            )
            
            logger.info(f"Created campaign: {campaign_id}")
            return campaign_id
            
        except Exception as e:
            logger.error(f"Failed to create campaign: {e}")
            raise
    
    def _apply_template(self, campaign_data: Dict[str, Any], template: Dict[str, Any]) -> Dict[str, Any]:
        """Apply campaign template to campaign data"""
        # Merge template defaults with provided data
        for key, value in template.items():
            if key not in campaign_data and key != "default_tasks" and key != "default_milestones":
                campaign_data[key] = value
        
        return campaign_data
    
    async def _create_default_tasks(self, campaign: Campaign, template: Dict[str, Any]):
        """Create default tasks from template"""
        for i, task_template in enumerate(template.get("default_tasks", [])):
            task_id = f"task_{uuid.uuid4().hex[:8]}"
            
            task = CampaignTask(
                task_id=task_id,
                campaign_id=campaign.campaign_id,
                title=task_template["title"],
                description=task_template["description"],
                task_type=task_template["task_type"],
                assigned_to=None,
                status=TaskStatus.PENDING,
                priority=task_template.get("priority", Priority.MEDIUM),
                created_date=datetime.now(),
                due_date=None,
                started_date=None,
                completed_date=None,
                estimated_hours=task_template.get("estimated_hours"),
                actual_hours=None,
                dependencies=[],
                tags=[],
                attachments=[],
                notes=[],
                progress_percentage=0,
                execution_config={},
                results=None
            )
            
            campaign.tasks.append(task)
    
    async def _create_default_milestones(self, campaign: Campaign, template: Dict[str, Any]):
        """Create default milestones from template"""
        for milestone_template in template.get("default_milestones", []):
            milestone_id = f"mile_{uuid.uuid4().hex[:8]}"
            
            # Calculate due date based on offset
            start_date = campaign.start_date or datetime.now()
            due_date = start_date + timedelta(days=milestone_template["days_offset"])
            
            milestone = CampaignMilestone(
                milestone_id=milestone_id,
                campaign_id=campaign.campaign_id,
                title=milestone_template["title"],
                description=milestone_template["description"],
                due_date=due_date,
                completed_date=None,
                status="pending",
                associated_tasks=[],
                deliverables=[]
            )
            
            campaign.milestones.append(milestone)
    
    async def start_campaign(self, campaign_id: str) -> bool:
        """Start campaign execution"""
        try:
            if campaign_id not in self.campaigns:
                raise ValueError(f"Campaign not found: {campaign_id}")
            
            campaign = self.campaigns[campaign_id]
            
            if campaign.status != CampaignStatus.PLANNED:
                raise ValueError(f"Campaign must be in PLANNED status to start")
            
            # Update campaign status
            campaign.status = CampaignStatus.ACTIVE
            campaign.start_date = datetime.now()
            self.active_campaigns.add(campaign_id)
            
            # Update milestone due dates if needed
            if campaign.start_date:
                for milestone in campaign.milestones:
                    if milestone.status == "pending":
                        # Recalculate due dates based on actual start date
                        pass  # Template already set relative dates
            
            # Initialize metrics
            campaign.metrics = self._calculate_campaign_metrics(campaign)
            
            # Start first available tasks
            await self._start_ready_tasks(campaign)
            
            # Emit campaign started event
            await self.event_manager.emit(
                EventTypes.CAMPAIGN_STARTED,
                {
                    "campaign_id": campaign_id,
                    "name": campaign.name,
                    "start_date": campaign.start_date.isoformat()
                },
                "campaign_manager"
            )
            
            logger.info(f"Started campaign: {campaign_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to start campaign: {e}")
            return False
    
    async def _start_ready_tasks(self, campaign: Campaign):
        """Start tasks that are ready for execution"""
        for task in campaign.tasks:
            if task.status == TaskStatus.PENDING and self._are_dependencies_met(task, campaign):
                task.status = TaskStatus.IN_PROGRESS
                task.started_date = datetime.now()
                
                # Emit task started event
                await self.event_manager.emit(
                    EventTypes.TASK_STARTED,
                    {
                        "task_id": task.task_id,
                        "campaign_id": campaign.campaign_id,
                        "title": task.title
                    },
                    "campaign_manager"
                )
    
    def _are_dependencies_met(self, task: CampaignTask, campaign: Campaign) -> bool:
        """Check if task dependencies are met"""
        if not task.dependencies:
            return True
        
        task_map = {t.task_id: t for t in campaign.tasks}
        
        for dep_id in task.dependencies:
            if dep_id in task_map:
                dep_task = task_map[dep_id]
                if dep_task.status != TaskStatus.COMPLETED:
                    return False
            else:
                # Dependency not found
                return False
        
        return True
    
    async def complete_task(self, campaign_id: str, task_id: str, 
                          results: Optional[Dict[str, Any]] = None) -> bool:
        """Mark task as completed"""
        try:
            if campaign_id not in self.campaigns:
                raise ValueError(f"Campaign not found: {campaign_id}")
            
            campaign = self.campaigns[campaign_id]
            task = None
            
            # Find task
            for t in campaign.tasks:
                if t.task_id == task_id:
                    task = t
                    break
            
            if not task:
                raise ValueError(f"Task not found: {task_id}")
            
            # Update task
            task.status = TaskStatus.COMPLETED
            task.completed_date = datetime.now()
            task.progress_percentage = 100
            task.results = results
            
            # Calculate actual hours if not provided
            if task.started_date and not task.actual_hours:
                duration = (task.completed_date - task.started_date).total_seconds() / 3600
                task.actual_hours = round(duration, 2)
            
            # Start dependent tasks
            await self._start_ready_tasks(campaign)
            
            # Update campaign metrics
            campaign.metrics = self._calculate_campaign_metrics(campaign)
            
            # Check if campaign is complete
            if self._is_campaign_complete(campaign):
                await self._complete_campaign(campaign)
            
            # Emit task completed event
            await self.event_manager.emit(
                EventTypes.TASK_COMPLETED,
                {
                    "task_id": task_id,
                    "campaign_id": campaign_id,
                    "title": task.title,
                    "actual_hours": task.actual_hours
                },
                "campaign_manager"
            )
            
            logger.info(f"Completed task: {task_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to complete task: {e}")
            return False
    
    def _is_campaign_complete(self, campaign: Campaign) -> bool:
        """Check if all campaign tasks are completed"""
        for task in campaign.tasks:
            if task.status not in [TaskStatus.COMPLETED, TaskStatus.SKIPPED]:
                return False
        return True
    
    async def _complete_campaign(self, campaign: Campaign):
        """Complete campaign and finalize metrics"""
        campaign.status = CampaignStatus.COMPLETED
        campaign.actual_end_date = datetime.now()
        
        if campaign.campaign_id in self.active_campaigns:
            self.active_campaigns.remove(campaign.campaign_id)
        
        # Final metrics calculation
        campaign.metrics = self._calculate_campaign_metrics(campaign)
        
        # Generate AI insights
        ai_insights = await self._generate_campaign_insights(campaign)
        campaign.metadata["ai_insights"] = ai_insights
        
        # Emit campaign completed event
        await self.event_manager.emit(
            EventTypes.CAMPAIGN_COMPLETED,
            {
                "campaign_id": campaign.campaign_id,
                "name": campaign.name,
                "duration_days": (campaign.actual_end_date - campaign.start_date).days,
                "completion_rate": campaign.metrics.progress_percentage
            },
            "campaign_manager"
        )
        
        logger.info(f"Campaign completed: {campaign.campaign_id}")
    
    def _calculate_campaign_metrics(self, campaign: Campaign) -> CampaignMetrics:
        """Calculate comprehensive campaign metrics"""
        total_tasks = len(campaign.tasks)
        completed_tasks = len([t for t in campaign.tasks if t.status == TaskStatus.COMPLETED])
        failed_tasks = len([t for t in campaign.tasks if t.status == TaskStatus.FAILED])
        
        progress_percentage = (completed_tasks / total_tasks * 100) if total_tasks > 0 else 0
        
        estimated_hours = sum(t.estimated_hours or 0 for t in campaign.tasks)
        actual_hours = sum(t.actual_hours or 0 for t in campaign.tasks)
        
        # Extract findings from task results
        vulnerabilities_found = 0
        critical_findings = 0
        
        for task in campaign.tasks:
            if task.results:
                vulnerabilities_found += task.results.get("vulnerabilities_count", 0)
                critical_findings += task.results.get("critical_findings", 0)
        
        return CampaignMetrics(
            campaign_id=campaign.campaign_id,
            total_tasks=total_tasks,
            completed_tasks=completed_tasks,
            failed_tasks=failed_tasks,
            progress_percentage=progress_percentage,
            estimated_hours=estimated_hours,
            actual_hours=actual_hours,
            budget_allocated=campaign.budget or 0,
            budget_spent=actual_hours * 150,  # Assuming $150/hour rate
            vulnerabilities_found=vulnerabilities_found,
            critical_findings=critical_findings,
            compliance_score=0.0,  # Would be calculated from compliance tasks
            team_utilization=actual_hours / estimated_hours if estimated_hours > 0 else 0,
            client_satisfaction=None  # Would be collected separately
        )
    
    async def _generate_campaign_insights(self, campaign: Campaign) -> str:
        """Generate AI insights for completed campaign"""
        try:
            metrics = campaign.metrics
            
            prompt = f"""
Analyze this completed security campaign and provide insights:

Campaign: {campaign.name}
Type: {campaign.campaign_type.value}
Duration: {(campaign.actual_end_date - campaign.start_date).days} days
Client: {campaign.client_name}

Metrics:
- Tasks Completed: {metrics.completed_tasks}/{metrics.total_tasks}
- Progress: {metrics.progress_percentage:.1f}%
- Estimated Hours: {metrics.estimated_hours}
- Actual Hours: {metrics.actual_hours}
- Team Utilization: {metrics.team_utilization:.1f}%
- Vulnerabilities Found: {metrics.vulnerabilities_found}
- Critical Findings: {metrics.critical_findings}

Provide insights on:
1. Campaign performance and efficiency
2. Team productivity analysis
3. Key findings and outcomes
4. Lessons learned and improvements
5. Client value delivered

Keep analysis concise and actionable.
"""
            
            response = await self.ai_service.analyze(
                prompt,
                context="campaign_analysis",
                provider_preference=["openai", "claude"]
            )
            
            return response.get("analysis", "No AI insights available")
            
        except Exception as e:
            logger.error(f"Failed to generate campaign insights: {e}")
            return "AI insights unavailable"
    
    async def pause_campaign(self, campaign_id: str) -> bool:
        """Pause active campaign"""
        try:
            if campaign_id not in self.campaigns:
                return False
            
            campaign = self.campaigns[campaign_id]
            if campaign.status == CampaignStatus.ACTIVE:
                campaign.status = CampaignStatus.PAUSED
                
                await self.event_manager.emit(
                    EventTypes.CAMPAIGN_PAUSED,
                    {"campaign_id": campaign_id},
                    "campaign_manager"
                )
                
                logger.info(f"Paused campaign: {campaign_id}")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Failed to pause campaign: {e}")
            return False
    
    async def resume_campaign(self, campaign_id: str) -> bool:
        """Resume paused campaign"""
        try:
            if campaign_id not in self.campaigns:
                return False
            
            campaign = self.campaigns[campaign_id]
            if campaign.status == CampaignStatus.PAUSED:
                campaign.status = CampaignStatus.ACTIVE
                self.active_campaigns.add(campaign_id)
                
                await self.event_manager.emit(
                    EventTypes.CAMPAIGN_RESUMED,
                    {"campaign_id": campaign_id},
                    "campaign_manager"
                )
                
                logger.info(f"Resumed campaign: {campaign_id}")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Failed to resume campaign: {e}")
            return False
    
    async def add_task(self, campaign_id: str, task_data: Dict[str, Any]) -> str:
        """Add new task to campaign"""
        try:
            if campaign_id not in self.campaigns:
                raise ValueError(f"Campaign not found: {campaign_id}")
            
            campaign = self.campaigns[campaign_id]
            task_id = f"task_{uuid.uuid4().hex[:8]}"
            
            task = CampaignTask(
                task_id=task_id,
                campaign_id=campaign_id,
                title=task_data["title"],
                description=task_data.get("description", ""),
                task_type=task_data.get("task_type", "general"),
                assigned_to=task_data.get("assigned_to"),
                status=TaskStatus.PENDING,
                priority=Priority(task_data.get("priority", "medium")),
                created_date=datetime.now(),
                due_date=datetime.fromisoformat(task_data["due_date"]) if task_data.get("due_date") else None,
                started_date=None,
                completed_date=None,
                estimated_hours=task_data.get("estimated_hours"),
                actual_hours=None,
                dependencies=task_data.get("dependencies", []),
                tags=task_data.get("tags", []),
                attachments=[],
                notes=[],
                progress_percentage=0,
                execution_config=task_data.get("execution_config", {}),
                results=None
            )
            
            campaign.tasks.append(task)
            
            # Update metrics
            campaign.metrics = self._calculate_campaign_metrics(campaign)
            
            logger.info(f"Added task to campaign {campaign_id}: {task_id}")
            return task_id
            
        except Exception as e:
            logger.error(f"Failed to add task: {e}")
            raise
    
    def get_campaign(self, campaign_id: str) -> Optional[Dict[str, Any]]:
        """Get campaign details"""
        if campaign_id not in self.campaigns:
            return None
        
        campaign = self.campaigns[campaign_id]
        return asdict(campaign)
    
    def list_campaigns(self, status_filter: Optional[CampaignStatus] = None,
                      assigned_user: Optional[str] = None) -> List[Dict[str, Any]]:
        """List campaigns with optional filtering"""
        campaigns = []
        
        for campaign in self.campaigns.values():
            # Apply filters
            if status_filter and campaign.status != status_filter:
                continue
            
            if assigned_user and assigned_user not in campaign.assigned_team:
                continue
            
            campaigns.append({
                "campaign_id": campaign.campaign_id,
                "name": campaign.name,
                "type": campaign.campaign_type.value,
                "status": campaign.status.value,
                "priority": campaign.priority.value,
                "client_name": campaign.client_name,
                "created_date": campaign.created_date.isoformat(),
                "start_date": campaign.start_date.isoformat() if campaign.start_date else None,
                "progress": campaign.metrics.progress_percentage if campaign.metrics else 0,
                "team_size": len(campaign.assigned_team),
                "task_count": len(campaign.tasks)
            })
        
        return sorted(campaigns, key=lambda x: x["created_date"], reverse=True)
    
    def get_campaign_tasks(self, campaign_id: str, status_filter: Optional[TaskStatus] = None) -> List[Dict[str, Any]]:
        """Get tasks for a campaign"""
        if campaign_id not in self.campaigns:
            return []
        
        campaign = self.campaigns[campaign_id]
        tasks = []
        
        for task in campaign.tasks:
            if status_filter and task.status != status_filter:
                continue
            
            tasks.append({
                "task_id": task.task_id,
                "title": task.title,
                "description": task.description,
                "task_type": task.task_type,
                "status": task.status.value,
                "priority": task.priority.value,
                "assigned_to": task.assigned_to,
                "progress_percentage": task.progress_percentage,
                "estimated_hours": task.estimated_hours,
                "actual_hours": task.actual_hours,
                "created_date": task.created_date.isoformat(),
                "due_date": task.due_date.isoformat() if task.due_date else None,
                "dependencies": task.dependencies,
                "tags": task.tags
            })
        
        return tasks
    
    def get_campaign_metrics(self, campaign_id: str) -> Optional[Dict[str, Any]]:
        """Get campaign performance metrics"""
        if campaign_id not in self.campaigns:
            return None
        
        campaign = self.campaigns[campaign_id]
        if not campaign.metrics:
            campaign.metrics = self._calculate_campaign_metrics(campaign)
        
        return asdict(campaign.metrics)
    
    def get_dashboard_summary(self) -> Dict[str, Any]:
        """Get campaign dashboard summary"""
        total_campaigns = len(self.campaigns)
        active_campaigns = len(self.active_campaigns)
        completed_campaigns = len([c for c in self.campaigns.values() if c.status == CampaignStatus.COMPLETED])
        
        # Calculate aggregate metrics
        total_tasks = sum(len(c.tasks) for c in self.campaigns.values())
        completed_tasks = sum(len([t for t in c.tasks if t.status == TaskStatus.COMPLETED]) for c in self.campaigns.values())
        
        # Recent activity
        recent_campaigns = sorted(
            self.campaigns.values(),
            key=lambda x: x.created_date,
            reverse=True
        )[:5]
        
        return {
            "total_campaigns": total_campaigns,
            "active_campaigns": active_campaigns,
            "completed_campaigns": completed_campaigns,
            "total_tasks": total_tasks,
            "completed_tasks": completed_tasks,
            "completion_rate": (completed_tasks / total_tasks * 100) if total_tasks > 0 else 0,
            "recent_campaigns": [
                {
                    "campaign_id": c.campaign_id,
                    "name": c.name,
                    "status": c.status.value,
                    "created_date": c.created_date.isoformat()
                }
                for c in recent_campaigns
            ]
        }
    
    def get_available_templates(self) -> List[Dict[str, Any]]:
        """Get available campaign templates"""
        templates = []
        
        for template_id, template in self.campaign_templates.items():
            templates.append({
                "template_id": template_id,
                "name": template["name"],
                "description": template["description"],
                "campaign_type": template["campaign_type"].value,
                "estimated_duration_days": template.get("estimated_duration_days", 0),
                "task_count": len(template.get("default_tasks", [])),
                "milestone_count": len(template.get("default_milestones", []))
            })
        
        return templates