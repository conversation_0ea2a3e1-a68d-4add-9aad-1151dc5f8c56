"""
Event System for NexusScan Desktop Application
Handles application-wide events and notifications.
"""

import logging
from typing import Dict, List, Callable, Any
from dataclasses import dataclass
from datetime import datetime
from enum import Enum
import asyncio

logger = logging.getLogger(__name__)


class EventType(Enum):
    """Event type enumeration"""
    # Scan events
    SCAN_STARTED = "scan_started"
    SCAN_PROGRESS = "scan_progress"
    SCAN_COMPLETED = "scan_completed"
    
    # Vulnerability events
    VULNERABILITY_FOUND = "vulnerability_found"
    
    # AI events
    AI_ANALYSIS_STARTED = "ai_analysis_started"
    AI_ANALYSIS_COMPLETED = "ai_analysis_completed"
    
    # Campaign events
    CAMPAIGN_CREATED = "campaign_created"
    CAMPAIGN_UPDATED = "campaign_updated"
    
    # General events
    STATUS_UPDATE = "status_update"
    ERROR_OCCURRED = "error_occurred"
    LOG_MESSAGE = "log_message"


@dataclass
class Event:
    """Event data structure"""
    type: str
    data: Dict[str, Any]
    timestamp: datetime
    source: str = "unknown"


class EventManager:
    """Centralized event management system"""
    
    def __init__(self):
        """Initialize event manager"""
        self.listeners: Dict[str, List[Callable]] = {}
        self.sync_listeners: Dict[str, List[Callable]] = {}
        self.event_history: List[Event] = []
        self.max_history = 1000
        
        logger.info("Event manager initialized")
    
    def subscribe(self, event_type: str, callback: Callable):
        """Subscribe to an event type"""
        if event_type not in self.listeners:
            self.listeners[event_type] = []
        
        self.listeners[event_type].append(callback)
        logger.debug(f"Subscribed to event: {event_type}")
    
    def unsubscribe(self, event_type: str, callback: Callable):
        """Unsubscribe from an event type"""
        if event_type in self.listeners:
            try:
                self.listeners[event_type].remove(callback)
                logger.debug(f"Unsubscribed from event: {event_type}")
            except ValueError:
                logger.warning(f"Callback not found for event: {event_type}")
    
    async def emit(self, event_type: str, data: Dict[str, Any], source: str = "unknown"):
        """Emit an event to all subscribers"""
        event = Event(
            type=event_type,
            data=data,
            timestamp=datetime.now(),
            source=source
        )
        
        # Add to history
        self.event_history.append(event)
        if len(self.event_history) > self.max_history:
            self.event_history.pop(0)
        
        # Notify listeners
        if event_type in self.listeners:
            for callback in self.listeners[event_type]:
                try:
                    if asyncio.iscoroutinefunction(callback):
                        await callback(event)
                    else:
                        callback(event)
                except Exception as e:
                    logger.error(f"Error in event callback for {event_type}: {e}")
        
        logger.debug(f"Event emitted: {event_type} from {source}")
    
    def emit_sync(self, event_type: str, data: Dict[str, Any], source: str = "unknown"):
        """Synchronous event emission for UI interactions"""
        event = Event(
            type=event_type,
            data=data,
            timestamp=datetime.now(),
            source=source
        )
        
        # Store event
        self._add_to_history(event)
        
        # Call sync listeners only
        if event_type in self.sync_listeners:
            for callback in self.sync_listeners[event_type]:
                try:
                    callback(event)
                except Exception as e:
                    logger.error(f"Sync event callback error: {e}")
        
        logger.debug(f"Sync event emitted: {event_type}")
    
    def subscribe_sync(self, event_type: str, callback: Callable):
        """Subscribe to events with synchronous callback"""
        if event_type not in self.sync_listeners:
            self.sync_listeners[event_type] = []
        self.sync_listeners[event_type].append(callback)
        logger.debug(f"Subscribed to sync event: {event_type}")
    
    def _add_to_history(self, event: Event):
        """Add event to history with size management"""
        self.event_history.append(event)
        
        # Maintain max history size
        if len(self.event_history) > self.max_history:
            self.event_history = self.event_history[-self.max_history:]
    
    def get_event_history(self, event_type: str = None, limit: int = 100) -> List[Event]:
        """Get event history, optionally filtered by type"""
        if event_type:
            events = [e for e in self.event_history if e.type == event_type]
        else:
            events = self.event_history
        
        return events[-limit:] if events else []


# Event type constants
class EventTypes:
    """Event type constants"""
    
    # Application events
    APP_STARTED = "app.started"
    SYSTEM_STARTUP = "system.startup"
    APP_SHUTDOWN = "app.shutdown"
    
    # User events
    USER_LOGIN = "user.login"
    USER_LOGOUT = "user.logout"
    
    # Campaign events
    CAMPAIGN_CREATED = "campaign.created"
    CAMPAIGN_UPDATED = "campaign.updated"
    CAMPAIGN_DELETED = "campaign.deleted"
    CAMPAIGN_STARTED = "campaign.started"
    CAMPAIGN_COMPLETED = "campaign.completed"
    
    # Scan events
    SCAN_STARTED = "scan.started"
    SCAN_PROGRESS = "scan.progress"
    SCAN_COMPLETED = "scan.completed"
    SCAN_FAILED = "scan.failed"
    
    # Vulnerability events
    VULNERABILITY_DISCOVERED = "vulnerability.discovered"
    VULNERABILITY_UPDATED = "vulnerability.updated"
    
    # AI events
    AI_PAYLOAD_GENERATED = "ai.payload_generated"
    AI_ANALYSIS_COMPLETED = "ai.analysis_completed"
    AI_ERROR = "ai.error"
    
    # Report events
    REPORT_GENERATED = "report.generated"
    REPORT_EXPORTED = "report.exported"
    
    # UI events
    UI_THEME_CHANGED = "ui.theme_changed"
    UI_PAGE_CHANGED = "ui.page_changed"
    
    # Error events
    ERROR_OCCURRED = "error.occurred"
    WARNING_OCCURRED = "warning.occurred"