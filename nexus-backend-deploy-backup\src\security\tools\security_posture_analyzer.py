"""
Security Posture Analyzer for NexusScan Desktop Application
AI-powered comprehensive security posture assessment and analysis.
"""

import json
import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Callable, Any
from dataclasses import dataclass
from enum import Enum

from security.tools.tool_registry import (
    ToolMetadata, ToolCategory, ToolCapabilities,
    ScanOptions, ScanResult, ToolStatus, register_tool
)
from security.tools.base_scanner import BaseScanner
from ai.services import AIServiceManager, AIProvider

logger = logging.getLogger(__name__)


class PostureLevel(Enum):
    """Security posture level enumeration"""
    EXCELLENT = "excellent"
    GOOD = "good"
    ADEQUATE = "adequate"
    POOR = "poor"
    CRITICAL = "critical"


class SecurityDomain(Enum):
    """Security domain enumeration"""
    NETWORK_SECURITY = "network_security"
    APPLICATION_SECURITY = "application_security"
    DATA_PROTECTION = "data_protection"
    IDENTITY_ACCESS = "identity_access"
    ENDPOINT_SECURITY = "endpoint_security"
    INCIDENT_RESPONSE = "incident_response"
    GOVERNANCE = "governance"
    COMPLIANCE = "compliance"


@dataclass
class SecurityPostureOptions(ScanOptions):
    """Security posture analysis options"""
    analysis_scope: str = "comprehensive"  # basic, comprehensive, detailed
    include_benchmarks: bool = True
    include_maturity_assessment: bool = True
    include_gap_analysis: bool = True
    include_roadmap: bool = True
    compliance_frameworks: List[str] = None
    industry_sector: str = ""
    organization_size: str = "medium"  # small, medium, large, enterprise
    security_budget: str = ""
    current_tools: List[str] = None
    
    def __post_init__(self):
        super().__post_init__()
        if self.compliance_frameworks is None:
            self.compliance_frameworks = []
        if self.current_tools is None:
            self.current_tools = []


@dataclass
class SecurityControl:
    """Security control definition"""
    id: str
    name: str
    domain: SecurityDomain
    description: str
    implementation_status: str
    effectiveness_score: float
    maturity_level: str
    gaps: List[str]
    recommendations: List[str]


@dataclass
class SecurityMetric:
    """Security metric definition"""
    name: str
    value: float
    unit: str
    benchmark: float
    status: str
    trend: str
    last_updated: datetime


class SecurityPostureAnalyzer(BaseScanner):
    """AI-powered comprehensive security posture assessment"""
    
    def __init__(self):
        super().__init__()
        self.tool_name = "security_posture_analyzer"
        # Scan types moved to default_options
        self.ai_manager = None
        
    def get_metadata(self) -> ToolMetadata:
        """Get tool metadata"""
        return ToolMetadata(
            name="security_posture_analyzer",
            display_name="Security Posture Analyzer",
            category=ToolCategory.AI_ANALYZER,
            description="AI-powered comprehensive security posture assessment",
            version="1.0.0",
            author="NexusScan AI Team",
            capabilities=ToolCapabilities(
                output_formats=["json", "html", "pdf"],
                supports_async=True,
                supports_progress=True,
                requires_root=False,
                network_access_required=True
            )
        )
    
    def is_available(self) -> bool:
        """Check if AI services are available"""
        try:
            from ai.services import AIServiceManager
            return True
        except ImportError:
            return False
    
    async def _get_ai_manager(self) -> AIServiceManager:
        """Get AI service manager"""
        if self.ai_manager is None:
            from ai.services import AIServiceManager
            self.ai_manager = AIServiceManager()
        return self.ai_manager
    
    async def scan(self, 
                   target: str, 
                   options: Optional[SecurityPostureOptions] = None,
                   progress_callback: Optional[Callable] = None) -> ScanResult:
        """Perform security posture analysis"""
        if options is None:
            options = SecurityPostureOptions(target=target)
        
        scan_id = f"posture_{target.replace('://', '_').replace('/', '_')}"
    
    def check_native_availability(self) -> bool:
        """Check if native tool is available"""
        return self.is_available()
    
    async def execute_native_scan(self, options: ScanOptions,
                                 progress_callback: Optional[Callable[[float, str], None]] = None) -> ScanResult:
        """Execute native scan"""
        if not isinstance(options, SecurityPostureOptions):
            options = SecurityPostureOptions(target=options.target)
        
        return await self.scan(options.target, options, progress_callback)
        start_time = datetime.now()
        
        logger.info(f"Starting security posture analysis: {scan_id}")
        
        if progress_callback:
            await progress_callback(0.1, "Initializing", "Preparing security posture analysis")
        
        try:
            ai_manager = await self._get_ai_manager()
            
            # Phase 1: Current state assessment
            if progress_callback:
                await progress_callback(0.15, "Current State", "Assessing current security state")
            
            current_state = await self._assess_current_state(target, options, ai_manager)
            
            # Phase 2: Security domain analysis
            if progress_callback:
                await progress_callback(0.3, "Domain Analysis", "Analyzing security domains")
            
            domain_analysis = await self._analyze_security_domains(target, current_state, options, ai_manager)
            
            # Phase 3: Control effectiveness assessment
            if progress_callback:
                await progress_callback(0.45, "Control Assessment", "Evaluating security controls")
            
            control_assessment = await self._assess_security_controls(target, domain_analysis, options, ai_manager)
            
            # Phase 4: Maturity assessment
            if progress_callback:
                await progress_callback(0.6, "Maturity Assessment", "Assessing security maturity")
            
            maturity_assessment = await self._assess_security_maturity(target, control_assessment, options, ai_manager)
            
            # Phase 5: Gap analysis
            if progress_callback:
                await progress_callback(0.75, "Gap Analysis", "Identifying security gaps")
            
            gap_analysis = await self._perform_gap_analysis(target, maturity_assessment, options, ai_manager)
            
            # Phase 6: Benchmark comparison
            if progress_callback:
                await progress_callback(0.85, "Benchmarking", "Comparing against industry benchmarks")
            
            benchmark_analysis = await self._perform_benchmark_analysis(target, gap_analysis, options, ai_manager)
            
            # Phase 7: Recommendations and roadmap
            if progress_callback:
                await progress_callback(0.95, "Recommendations", "Generating improvement roadmap")
            
            recommendations = await self._generate_recommendations(target, benchmark_analysis, options, ai_manager)
            
            # Phase 8: Generate comprehensive posture assessment
            if progress_callback:
                await progress_callback(0.98, "Report Generation", "Generating posture assessment report")
            
            posture_assessment = await self._generate_posture_assessment(
                target, current_state, domain_analysis, control_assessment, 
                maturity_assessment, gap_analysis, benchmark_analysis, 
                recommendations, options, ai_manager
            )
            
            if progress_callback:
                await progress_callback(1.0, "Complete", "Security posture analysis completed")
            
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            return ScanResult(
                scan_id=scan_id,
                tool_name="security_posture_analyzer",
                target=target,
                status=ToolStatus.COMPLETED,
                start_time=start_time,
                end_time=end_time,
                duration_seconds=duration,
                command_executed=f"security_posture_analyzer --target {target} --scope {options.analysis_scope}",
                exit_code=0,
                raw_output=json.dumps(posture_assessment, indent=2),
                error_output="",
                parsed_results=posture_assessment,
                vulnerabilities=posture_assessment.get("vulnerabilities", []),
                summary=posture_assessment.get("summary", {}),
                metadata={
                    "analysis_scope": options.analysis_scope,
                    "ai_analysis_enabled": True,
                    "domains_analyzed": len(domain_analysis),
                    "controls_assessed": len(control_assessment.get("controls", [])),
                    "overall_posture_score": posture_assessment.get("overall_posture_score", 0)
                }
            )
            
        except Exception as e:
            logger.error(f"Security posture analysis failed: {e}")
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            return ScanResult(
                scan_id=scan_id,
                tool_name="security_posture_analyzer",
                target=target,
                status=ToolStatus.FAILED,
                start_time=start_time,
                end_time=end_time,
                duration_seconds=duration,
                command_executed="",
                exit_code=1,
                raw_output="",
                error_output=str(e),
                parsed_results={},
                vulnerabilities=[],
                summary={"error": str(e)},
                metadata={}
            )
    
    async def _assess_current_state(self, target: str, options: SecurityPostureOptions, ai_manager: AIServiceManager) -> Dict[str, Any]:
        """Assess current security state"""
        prompt = f"""
        Assess the current security state for the following target:
        
        Target: {target}
        Organization Size: {options.organization_size}
        Industry Sector: {options.industry_sector}
        Current Tools: {options.current_tools}
        Security Budget: {options.security_budget}
        
        Please assess the current state across these areas:
        1. Network security posture
        2. Application security measures
        3. Data protection capabilities
        4. Identity and access management
        5. Endpoint security controls
        6. Incident response readiness
        7. Security governance framework
        8. Compliance status
        
        For each area, provide:
        - Current implementation status
        - Effectiveness rating (0-100)
        - Key strengths
        - Notable weaknesses
        - Immediate concerns
        
        Focus on realistic assessment based on typical organizations of this size and sector.
        """
        
        try:
            ai_response = await ai_manager.generate_analysis(
                prompt=prompt,
                analysis_type="current_state_assessment",
                provider=AIProvider.OPENAI
            )
            
            if ai_response and "analysis" in ai_response:
                return ai_response["analysis"]
            else:
                return self._generate_default_current_state(options)
        
        except Exception as e:
            logger.warning(f"AI current state assessment failed: {e}")
            return self._generate_default_current_state(options)
    
    def _generate_default_current_state(self, options: SecurityPostureOptions) -> Dict[str, Any]:
        """Generate default current state assessment"""
        return {
            "network_security": {
                "status": "partial",
                "effectiveness": 65,
                "strengths": ["Basic firewall", "Network segmentation"],
                "weaknesses": ["Limited monitoring", "Outdated IDS"]
            },
            "application_security": {
                "status": "basic",
                "effectiveness": 45,
                "strengths": ["Code reviews", "Basic testing"],
                "weaknesses": ["No SAST/DAST", "Limited secure coding"]
            },
            "data_protection": {
                "status": "partial",
                "effectiveness": 55,
                "strengths": ["Data classification", "Backup procedures"],
                "weaknesses": ["Encryption gaps", "DLP limitations"]
            },
            "identity_access": {
                "status": "basic",
                "effectiveness": 50,
                "strengths": ["Basic AD", "Password policies"],
                "weaknesses": ["No MFA", "Poor access reviews"]
            },
            "endpoint_security": {
                "status": "partial",
                "effectiveness": 60,
                "strengths": ["Antivirus", "Basic patching"],
                "weaknesses": ["No EDR", "BYOD risks"]
            },
            "incident_response": {
                "status": "basic",
                "effectiveness": 40,
                "strengths": ["IR plan exists", "Basic contact list"],
                "weaknesses": ["No testing", "Limited tools"]
            },
            "governance": {
                "status": "basic",
                "effectiveness": 45,
                "strengths": ["Security policies", "Awareness training"],
                "weaknesses": ["No risk framework", "Limited metrics"]
            },
            "compliance": {
                "status": "partial",
                "effectiveness": 55,
                "strengths": ["Basic compliance", "Documentation"],
                "weaknesses": ["Gap analysis needed", "Audit findings"]
            }
        }
    
    async def _analyze_security_domains(self, target: str, current_state: Dict[str, Any], 
                                       options: SecurityPostureOptions, ai_manager: AIServiceManager) -> Dict[str, Any]:
        """Analyze security domains in detail"""
        prompt = f"""
        Analyze the security domains in detail based on the current state:
        
        Target: {target}
        Current State: {current_state}
        Organization Size: {options.organization_size}
        Industry Sector: {options.industry_sector}
        
        For each security domain, provide detailed analysis including:
        1. Domain maturity level (Initial, Developing, Defined, Managed, Optimized)
        2. Key security controls and their status
        3. Technology stack assessment
        4. Process maturity evaluation
        5. People and skills assessment
        6. Risk exposure analysis
        7. Industry-specific considerations
        8. Regulatory requirements impact
        
        Focus on actionable insights and specific improvement areas.
        """
        
        try:
            ai_response = await ai_manager.generate_analysis(
                prompt=prompt,
                analysis_type="domain_analysis",
                provider=AIProvider.OPENAI
            )
            
            if ai_response and "analysis" in ai_response:
                return ai_response["analysis"]
            else:
                return self._generate_default_domain_analysis(current_state)
        
        except Exception as e:
            logger.warning(f"AI domain analysis failed: {e}")
            return self._generate_default_domain_analysis(current_state)
    
    def _generate_default_domain_analysis(self, current_state: Dict[str, Any]) -> Dict[str, Any]:
        """Generate default domain analysis"""
        return {
            "network_security": {
                "maturity_level": "developing",
                "controls": ["firewall", "ids", "network_segmentation"],
                "technology_stack": ["traditional_firewall", "basic_ids"],
                "process_maturity": "basic",
                "skill_assessment": "adequate",
                "risk_exposure": "medium",
                "improvement_areas": ["SIEM implementation", "Network monitoring"]
            },
            "application_security": {
                "maturity_level": "initial",
                "controls": ["code_review", "basic_testing"],
                "technology_stack": ["manual_review", "basic_scanners"],
                "process_maturity": "initial",
                "skill_assessment": "limited",
                "risk_exposure": "high",
                "improvement_areas": ["SAST/DAST tools", "Secure development lifecycle"]
            },
            "data_protection": {
                "maturity_level": "developing",
                "controls": ["data_classification", "backup", "access_controls"],
                "technology_stack": ["basic_encryption", "backup_systems"],
                "process_maturity": "developing",
                "skill_assessment": "adequate",
                "risk_exposure": "medium",
                "improvement_areas": ["DLP implementation", "Advanced encryption"]
            }
        }
    
    async def _assess_security_controls(self, target: str, domain_analysis: Dict[str, Any], 
                                      options: SecurityPostureOptions, ai_manager: AIServiceManager) -> Dict[str, Any]:
        """Assess security controls effectiveness"""
        prompt = f"""
        Assess the effectiveness of security controls based on domain analysis:
        
        Target: {target}
        Domain Analysis: {domain_analysis}
        Organization Size: {options.organization_size}
        
        For each security control, evaluate:
        1. Implementation completeness (0-100%)
        2. Effectiveness rating (0-100)
        3. Coverage assessment
        4. Configuration quality
        5. Monitoring and alerting
        6. Maintenance and updates
        7. Integration with other controls
        8. Compliance alignment
        
        Provide specific recommendations for improvement.
        """
        
        try:
            ai_response = await ai_manager.generate_analysis(
                prompt=prompt,
                analysis_type="control_assessment",
                provider=AIProvider.OPENAI
            )
            
            if ai_response and "analysis" in ai_response:
                return ai_response["analysis"]
            else:
                return self._generate_default_control_assessment()
        
        except Exception as e:
            logger.warning(f"AI control assessment failed: {e}")
            return self._generate_default_control_assessment()
    
    def _generate_default_control_assessment(self) -> Dict[str, Any]:
        """Generate default control assessment"""
        return {
            "controls": [
                {
                    "id": "firewall",
                    "name": "Network Firewall",
                    "domain": "network_security",
                    "implementation": 80,
                    "effectiveness": 70,
                    "coverage": "good",
                    "configuration": "adequate",
                    "monitoring": "limited",
                    "maintenance": "regular",
                    "integration": "basic",
                    "compliance": "aligned"
                },
                {
                    "id": "access_control",
                    "name": "Access Control System",
                    "domain": "identity_access",
                    "implementation": 60,
                    "effectiveness": 55,
                    "coverage": "partial",
                    "configuration": "basic",
                    "monitoring": "minimal",
                    "maintenance": "irregular",
                    "integration": "limited",
                    "compliance": "gaps"
                },
                {
                    "id": "endpoint_protection",
                    "name": "Endpoint Protection",
                    "domain": "endpoint_security",
                    "implementation": 70,
                    "effectiveness": 65,
                    "coverage": "good",
                    "configuration": "adequate",
                    "monitoring": "basic",
                    "maintenance": "automated",
                    "integration": "basic",
                    "compliance": "aligned"
                }
            ],
            "overall_effectiveness": 63,
            "critical_gaps": [
                "Multi-factor authentication",
                "Security information and event management",
                "Data loss prevention"
            ],
            "improvement_priorities": [
                "Implement MFA across all systems",
                "Deploy SIEM solution",
                "Enhance monitoring capabilities"
            ]
        }
    
    async def _assess_security_maturity(self, target: str, control_assessment: Dict[str, Any], 
                                      options: SecurityPostureOptions, ai_manager: AIServiceManager) -> Dict[str, Any]:
        """Assess security maturity levels"""
        prompt = f"""
        Assess the security maturity levels based on control assessment:
        
        Target: {target}
        Control Assessment: {control_assessment}
        Organization Size: {options.organization_size}
        
        Evaluate maturity across these dimensions:
        1. Technology maturity (tools, systems, automation)
        2. Process maturity (procedures, governance, documentation)
        3. People maturity (skills, training, awareness)
        4. Data maturity (metrics, reporting, insights)
        5. Integration maturity (orchestration, correlation)
        
        Use the following maturity levels:
        - Initial (Ad-hoc, reactive)
        - Developing (Basic processes, some documentation)
        - Defined (Standardized processes, clear ownership)
        - Managed (Measured, monitored, controlled)
        - Optimized (Continuous improvement, innovation)
        
        Provide specific examples and recommendations for advancement.
        """
        
        try:
            ai_response = await ai_manager.generate_analysis(
                prompt=prompt,
                analysis_type="maturity_assessment",
                provider=AIProvider.OPENAI
            )
            
            if ai_response and "analysis" in ai_response:
                return ai_response["analysis"]
            else:
                return self._generate_default_maturity_assessment()
        
        except Exception as e:
            logger.warning(f"AI maturity assessment failed: {e}")
            return self._generate_default_maturity_assessment()
    
    def _generate_default_maturity_assessment(self) -> Dict[str, Any]:
        """Generate default maturity assessment"""
        return {
            "technology_maturity": {
                "level": "developing",
                "score": 2.5,
                "characteristics": ["Basic tools deployed", "Limited automation"],
                "advancement_path": ["Implement SOAR", "Advanced analytics"]
            },
            "process_maturity": {
                "level": "developing",
                "score": 2.0,
                "characteristics": ["Basic procedures", "Some documentation"],
                "advancement_path": ["Standardize processes", "Improve documentation"]
            },
            "people_maturity": {
                "level": "initial",
                "score": 1.5,
                "characteristics": ["Limited skills", "Basic training"],
                "advancement_path": ["Skills development", "Specialized training"]
            },
            "data_maturity": {
                "level": "initial",
                "score": 1.0,
                "characteristics": ["Basic metrics", "Limited reporting"],
                "advancement_path": ["Metrics framework", "Advanced analytics"]
            },
            "integration_maturity": {
                "level": "initial",
                "score": 1.5,
                "characteristics": ["Siloed tools", "Manual processes"],
                "advancement_path": ["Tool integration", "Automation"]
            },
            "overall_maturity": {
                "level": "developing",
                "score": 1.7,
                "target_score": 3.0,
                "timeline": "12-18 months"
            }
        }
    
    async def _perform_gap_analysis(self, target: str, maturity_assessment: Dict[str, Any], 
                                  options: SecurityPostureOptions, ai_manager: AIServiceManager) -> Dict[str, Any]:
        """Perform comprehensive gap analysis"""
        prompt = f"""
        Perform comprehensive gap analysis based on maturity assessment:
        
        Target: {target}
        Maturity Assessment: {maturity_assessment}
        Compliance Frameworks: {options.compliance_frameworks}
        Industry Sector: {options.industry_sector}
        
        Identify gaps in:
        1. Technology gaps (missing tools, outdated systems)
        2. Process gaps (missing procedures, documentation)
        3. People gaps (skills, training, staffing)
        4. Compliance gaps (regulatory requirements)
        5. Industry-specific gaps (sector requirements)
        
        For each gap, provide:
        - Gap description and impact
        - Risk level (critical, high, medium, low)
        - Effort required to address
        - Timeline for remediation
        - Resource requirements
        - Dependencies
        
        Prioritize gaps based on risk and business impact.
        """
        
        try:
            ai_response = await ai_manager.generate_analysis(
                prompt=prompt,
                analysis_type="gap_analysis",
                provider=AIProvider.OPENAI
            )
            
            if ai_response and "analysis" in ai_response:
                return ai_response["analysis"]
            else:
                return self._generate_default_gap_analysis()
        
        except Exception as e:
            logger.warning(f"AI gap analysis failed: {e}")
            return self._generate_default_gap_analysis()
    
    def _generate_default_gap_analysis(self) -> Dict[str, Any]:
        """Generate default gap analysis"""
        return {
            "technology_gaps": [
                {
                    "gap": "Missing SIEM solution",
                    "impact": "Limited security monitoring and incident detection",
                    "risk_level": "high",
                    "effort": "medium",
                    "timeline": "3-6 months",
                    "resources": "Security analyst, budget allocation"
                },
                {
                    "gap": "No multi-factor authentication",
                    "impact": "Weak access controls and authentication",
                    "risk_level": "high",
                    "effort": "low",
                    "timeline": "1-2 months",
                    "resources": "IT administrator, MFA solution"
                }
            ],
            "process_gaps": [
                {
                    "gap": "Incomplete incident response procedures",
                    "impact": "Ineffective incident handling and recovery",
                    "risk_level": "medium",
                    "effort": "medium",
                    "timeline": "2-3 months",
                    "resources": "Security team, documentation effort"
                }
            ],
            "people_gaps": [
                {
                    "gap": "Limited security awareness training",
                    "impact": "Increased human error and social engineering risk",
                    "risk_level": "medium",
                    "effort": "low",
                    "timeline": "1-2 months",
                    "resources": "Training budget, HR support"
                }
            ],
            "compliance_gaps": [
                {
                    "gap": "GDPR compliance documentation",
                    "impact": "Regulatory compliance violations",
                    "risk_level": "high",
                    "effort": "high",
                    "timeline": "6-12 months",
                    "resources": "Legal team, privacy officer"
                }
            ],
            "priority_gaps": [
                "Missing SIEM solution",
                "No multi-factor authentication",
                "GDPR compliance documentation"
            ]
        }
    
    async def _perform_benchmark_analysis(self, target: str, gap_analysis: Dict[str, Any], 
                                        options: SecurityPostureOptions, ai_manager: AIServiceManager) -> Dict[str, Any]:
        """Perform benchmark analysis against industry standards"""
        prompt = f"""
        Perform benchmark analysis against industry standards:
        
        Target: {target}
        Gap Analysis: {gap_analysis}
        Industry Sector: {options.industry_sector}
        Organization Size: {options.organization_size}
        
        Compare against:
        1. Industry peers (same sector and size)
        2. Security frameworks (NIST, ISO 27001, etc.)
        3. Best practices and standards
        4. Regulatory requirements
        5. Threat landscape benchmarks
        
        Provide:
        - Current position vs benchmarks
        - Performance gaps and advantages
        - Industry-specific recommendations
        - Maturity level comparisons
        - Investment benchmarks
        
        Focus on actionable insights for improvement.
        """
        
        try:
            ai_response = await ai_manager.generate_analysis(
                prompt=prompt,
                analysis_type="benchmark_analysis",
                provider=AIProvider.OPENAI
            )
            
            if ai_response and "analysis" in ai_response:
                return ai_response["analysis"]
            else:
                return self._generate_default_benchmark_analysis()
        
        except Exception as e:
            logger.warning(f"AI benchmark analysis failed: {e}")
            return self._generate_default_benchmark_analysis()
    
    def _generate_default_benchmark_analysis(self) -> Dict[str, Any]:
        """Generate default benchmark analysis"""
        return {
            "industry_comparison": {
                "overall_score": 55,
                "industry_average": 65,
                "percentile_ranking": 35,
                "gap_to_average": -10,
                "top_performers": 85
            },
            "framework_alignment": {
                "nist_csf": {
                    "score": 60,
                    "target": 80,
                    "gap": -20,
                    "priority_areas": ["Detect", "Respond"]
                },
                "iso27001": {
                    "score": 50,
                    "target": 75,
                    "gap": -25,
                    "priority_areas": ["Risk assessment", "Incident management"]
                }
            },
            "maturity_benchmarks": {
                "current_level": "developing",
                "industry_average": "defined",
                "top_performers": "managed",
                "advancement_needed": "1-2 levels"
            },
            "investment_benchmarks": {
                "security_budget_percentage": 8,
                "industry_average": 12,
                "recommended_increase": 4,
                "priority_investments": ["SIEM", "MFA", "Training"]
            },
            "improvement_priorities": [
                "Implement advanced monitoring",
                "Strengthen access controls",
                "Enhance incident response",
                "Improve compliance posture"
            ]
        }
    
    async def _generate_recommendations(self, target: str, benchmark_analysis: Dict[str, Any], 
                                      options: SecurityPostureOptions, ai_manager: AIServiceManager) -> Dict[str, Any]:
        """Generate comprehensive recommendations and roadmap"""
        prompt = f"""
        Generate comprehensive recommendations and improvement roadmap:
        
        Target: {target}
        Benchmark Analysis: {benchmark_analysis}
        Organization Size: {options.organization_size}
        Budget Context: {options.security_budget}
        
        Provide:
        1. Strategic recommendations (high-level initiatives)
        2. Tactical recommendations (specific actions)
        3. Quick wins (immediate improvements)
        4. Long-term roadmap (12-24 months)
        5. Budget allocation guidance
        6. Resource requirements
        7. Success metrics and KPIs
        8. Risk mitigation priorities
        
        Structure recommendations by:
        - Priority level (critical, high, medium, low)
        - Timeline (immediate, short-term, medium-term, long-term)
        - Investment level (low, medium, high)
        - Business impact (operational, strategic, compliance)
        
        Make recommendations specific and actionable.
        """
        
        try:
            ai_response = await ai_manager.generate_analysis(
                prompt=prompt,
                analysis_type="recommendations",
                provider=AIProvider.OPENAI
            )
            
            if ai_response and "analysis" in ai_response:
                return ai_response["analysis"]
            else:
                return self._generate_default_recommendations()
        
        except Exception as e:
            logger.warning(f"AI recommendations generation failed: {e}")
            return self._generate_default_recommendations()
    
    def _generate_default_recommendations(self) -> Dict[str, Any]:
        """Generate default recommendations"""
        return {
            "strategic_recommendations": [
                {
                    "title": "Implement Security Operations Center",
                    "description": "Establish centralized security monitoring and incident response",
                    "priority": "high",
                    "timeline": "6-12 months",
                    "investment": "high",
                    "business_impact": "operational"
                },
                {
                    "title": "Develop Security Governance Framework",
                    "description": "Create comprehensive security policies and procedures",
                    "priority": "medium",
                    "timeline": "3-6 months",
                    "investment": "medium",
                    "business_impact": "strategic"
                }
            ],
            "tactical_recommendations": [
                {
                    "title": "Deploy Multi-Factor Authentication",
                    "description": "Implement MFA across all critical systems",
                    "priority": "critical",
                    "timeline": "1-2 months",
                    "investment": "low",
                    "business_impact": "operational"
                },
                {
                    "title": "Enhance Endpoint Protection",
                    "description": "Upgrade to next-generation endpoint security",
                    "priority": "high",
                    "timeline": "2-3 months",
                    "investment": "medium",
                    "business_impact": "operational"
                }
            ],
            "quick_wins": [
                "Enable logging on all systems",
                "Update password policies",
                "Conduct security awareness training",
                "Implement basic network segmentation"
            ],
            "roadmap": {
                "immediate": ["MFA deployment", "Security awareness training"],
                "short_term": ["SIEM implementation", "Endpoint protection upgrade"],
                "medium_term": ["SOC establishment", "Compliance framework"],
                "long_term": ["Security automation", "Advanced threat detection"]
            },
            "budget_allocation": {
                "technology": 60,
                "people": 25,
                "processes": 15
            },
            "success_metrics": [
                "Mean time to detect (MTTD)",
                "Mean time to respond (MTTR)",
                "Security awareness test scores",
                "Compliance audit results"
            ]
        }
    
    async def _generate_posture_assessment(self, target: str, current_state: Dict[str, Any], 
                                         domain_analysis: Dict[str, Any], control_assessment: Dict[str, Any],
                                         maturity_assessment: Dict[str, Any], gap_analysis: Dict[str, Any],
                                         benchmark_analysis: Dict[str, Any], recommendations: Dict[str, Any],
                                         options: SecurityPostureOptions, ai_manager: AIServiceManager) -> Dict[str, Any]:
        """Generate comprehensive security posture assessment"""
        
        # Calculate overall posture score
        posture_score = self._calculate_posture_score(
            current_state, control_assessment, maturity_assessment, benchmark_analysis
        )
        
        # Generate vulnerabilities from critical gaps
        vulnerabilities = []
        for gap_category in gap_analysis:
            if isinstance(gap_analysis[gap_category], list):
                for gap in gap_analysis[gap_category]:
                    if isinstance(gap, dict) and gap.get("risk_level") in ["critical", "high"]:
                        vulnerabilities.append({
                            "id": f"posture_gap_{len(vulnerabilities)}",
                            "type": "Security Gap",
                            "severity": gap.get("risk_level", "medium"),
                            "description": gap.get("gap", "Security gap identified"),
                            "impact": gap.get("impact", ""),
                            "category": gap_category,
                            "timeline": gap.get("timeline", ""),
                            "effort": gap.get("effort", ""),
                            "recommendation": f"Address gap: {gap.get('gap', 'Unknown gap')}"
                        })
        
        posture_assessment = {
            "target_info": {
                "target": target,
                "organization_size": options.organization_size,
                "industry_sector": options.industry_sector,
                "analysis_scope": options.analysis_scope,
                "assessment_timestamp": datetime.now().isoformat()
            },
            "current_state": current_state,
            "domain_analysis": domain_analysis,
            "control_assessment": control_assessment,
            "maturity_assessment": maturity_assessment,
            "gap_analysis": gap_analysis,
            "benchmark_analysis": benchmark_analysis,
            "recommendations": recommendations,
            "vulnerabilities": vulnerabilities,
            "overall_posture_score": posture_score,
            "posture_level": self._determine_posture_level(posture_score),
            "summary": {
                "total_domains_analyzed": len(domain_analysis),
                "controls_assessed": len(control_assessment.get("controls", [])),
                "critical_gaps": len([v for v in vulnerabilities if v.get("severity") == "critical"]),
                "high_priority_gaps": len([v for v in vulnerabilities if v.get("severity") == "high"]),
                "overall_maturity_level": maturity_assessment.get("overall_maturity", {}).get("level", "developing"),
                "industry_percentile": benchmark_analysis.get("industry_comparison", {}).get("percentile_ranking", 50),
                "quick_wins_available": len(recommendations.get("quick_wins", [])),
                "assessment_date": datetime.now().isoformat()
            }
        }
        
        return posture_assessment
    
    def _calculate_posture_score(self, current_state: Dict[str, Any], control_assessment: Dict[str, Any], 
                               maturity_assessment: Dict[str, Any], benchmark_analysis: Dict[str, Any]) -> float:
        """Calculate overall security posture score"""
        # Weight different components
        weights = {
            "current_state": 0.3,
            "control_effectiveness": 0.3,
            "maturity": 0.2,
            "benchmark": 0.2
        }
        
        # Calculate current state score
        current_state_scores = [state.get("effectiveness", 50) for state in current_state.values() if isinstance(state, dict)]
        current_state_score = sum(current_state_scores) / len(current_state_scores) / 100 if current_state_scores else 0.5
        
        # Control effectiveness score
        control_effectiveness = control_assessment.get("overall_effectiveness", 50) / 100
        
        # Maturity score
        maturity_score = maturity_assessment.get("overall_maturity", {}).get("score", 1.5) / 5.0
        
        # Benchmark score
        benchmark_score = benchmark_analysis.get("industry_comparison", {}).get("overall_score", 50) / 100
        
        # Calculate weighted average
        overall_score = (
            current_state_score * weights["current_state"] +
            control_effectiveness * weights["control_effectiveness"] +
            maturity_score * weights["maturity"] +
            benchmark_score * weights["benchmark"]
        )
        
        return round(overall_score, 2)
    
    def _determine_posture_level(self, score: float) -> str:
        """Determine posture level from score"""
        if score >= 0.9:
            return PostureLevel.EXCELLENT.value
        elif score >= 0.7:
            return PostureLevel.GOOD.value
        elif score >= 0.5:
            return PostureLevel.ADEQUATE.value
        elif score >= 0.3:
            return PostureLevel.POOR.value
        else:
            return PostureLevel.CRITICAL.value
    
    def get_frontend_interface_data(self) -> Dict[str, Any]:
        """Get data for frontend interface"""
        return {
            "tool_info": {
                "name": "Security Posture Analyzer",
                "description": "AI-powered comprehensive security posture assessment",
                "version": "1.0.0",
                "category": "AI Analyzer",
                "status": "available" if self.is_available() else "unavailable"
            },
            "scan_options": {
                "target": {
                    "type": "text",
                    "required": True,
                    "placeholder": "Organization or System Name",
                    "validation": "text"
                },
                "analysis_scope": {
                    "type": "select",
                    "options": ["basic", "comprehensive", "detailed"],
                    "default": "comprehensive",
                    "label": "Analysis scope"
                },
                "organization_size": {
                    "type": "select",
                    "options": ["small", "medium", "large", "enterprise"],
                    "default": "medium",
                    "label": "Organization size"
                },
                "industry_sector": {
                    "type": "select",
                    "options": ["financial", "healthcare", "retail", "manufacturing", "government", "education", "other"],
                    "default": "other",
                    "label": "Industry sector"
                },
                "include_benchmarks": {
                    "type": "boolean",
                    "default": True,
                    "label": "Include industry benchmarks"
                },
                "include_maturity_assessment": {
                    "type": "boolean",
                    "default": True,
                    "label": "Include maturity assessment"
                },
                "include_gap_analysis": {
                    "type": "boolean",
                    "default": True,
                    "label": "Include gap analysis"
                },
                "include_roadmap": {
                    "type": "boolean",
                    "default": True,
                    "label": "Include improvement roadmap"
                }
            },
            "output_formats": ["json", "html", "pdf"],
            "capabilities": [
                "AI-powered security assessment",
                "Multi-domain analysis",
                "Maturity level assessment",
                "Gap analysis",
                "Industry benchmarking",
                "Control effectiveness evaluation",
                "Comprehensive recommendations",
                "Improvement roadmap generation"
            ]
        }


# Register the tool
register_tool(SecurityPostureAnalyzer)