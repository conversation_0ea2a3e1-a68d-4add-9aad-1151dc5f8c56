/**
 * Test Electron Backend Manager Connection
 * This script tests the Electron backend manager to see why it's showing offline
 */

console.log('🧪 Testing Electron Backend Manager...');

// Test if we're in Electron
if (window.electronAPI) {
  console.log('✅ Running in Electron environment');
  
  // Test 1: Get current backend status
  console.log('📡 Testing backend status...');
  window.electronAPI.backend.getStatus()
    .then(status => {
      console.log('📊 Backend Status from Electron Manager:', status);
      console.log('🔗 Backend URL:', status.url || 'Not set');
      console.log('🟢 Connected:', status.connected);
      console.log('❌ Error:', status.error || 'None');
    })
    .catch(error => {
      console.error('❌ Failed to get backend status:', error);
    });
  
  // Test 2: Try to connect manually
  console.log('🔄 Testing manual backend connection...');
  window.electronAPI.backend.connect()
    .then(connected => {
      console.log('🔗 Manual connection result:', connected);
      
      // Get status again after connection attempt
      return window.electronAPI.backend.getStatus();
    })
    .then(status => {
      console.log('📊 Backend Status after connection attempt:', status);
    })
    .catch(error => {
      console.error('❌ Manual connection failed:', error);
    });
  
  // Test 3: Try to get available tools
  console.log('🔧 Testing available tools...');
  window.electronAPI.backend.getAvailableTools()
    .then(tools => {
      console.log(`🔧 Available tools count: ${tools.length}`);
      if (tools.length > 0) {
        console.log('🔧 First 3 tools:', tools.slice(0, 3));
      }
    })
    .catch(error => {
      console.error('❌ Failed to get available tools:', error);
    });
  
  // Test 4: Test direct API request
  console.log('🌐 Testing direct API request...');
  window.electronAPI.backend.executeRequest('GET', '/api/health', null)
    .then(response => {
      console.log('🌐 Direct API health response:', response);
    })
    .catch(error => {
      console.error('❌ Direct API request failed:', error);
    });
    
} else {
  console.log('❌ Not running in Electron - testing browser API client instead');
  
  // Test browser API client
  if (window.nexusScan && window.nexusScan.apiClient) {
    console.log('🌐 Testing browser API client...');
    
    window.nexusScan.apiClient.checkHealth()
      .then(status => {
        console.log('🌐 Browser API Client Health:', status);
      })
      .catch(error => {
        console.error('❌ Browser API Client failed:', error);
      });
  }
}

console.log('🧪 All tests initiated - check console for results');
