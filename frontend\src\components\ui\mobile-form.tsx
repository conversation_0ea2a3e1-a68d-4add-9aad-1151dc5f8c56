/**
 * Mobile-Responsive Form Components
 * Provides mobile-optimized form layouts and inputs
 */
import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { cn } from '@/lib/utils';

interface MobileFormGridProps {
  className?: string;
  children: React.ReactNode;
}

interface MobileFormCardProps {
  title: string;
  description?: string;
  icon?: React.ReactNode;
  className?: string;
  children: React.ReactNode;
}

interface MobileFormFieldProps {
  label: string;
  description?: string;
  required?: boolean;
  className?: string;
  children: React.ReactNode;
}

interface TouchInputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string;
  description?: string;
}

interface TouchSelectProps {
  label?: string;
  description?: string;
  value: string;
  onValueChange: (value: string) => void;
  placeholder?: string;
  className?: string;
  children: React.ReactNode;
}

interface TouchTextareaProps extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  label?: string;
  description?: string;
}

// Mobile-responsive form grid
export function MobileFormGrid({ className, children }: MobileFormGridProps) {
  return (
    <div className={cn(
      // Mobile: Single column with spacing
      "space-y-6",
      // Desktop: Two columns
      "lg:grid lg:grid-cols-2 lg:gap-6 lg:space-y-0",
      className
    )}>
      {children}
    </div>
  );
}

// Mobile-optimized form card
export function MobileFormCard({ title, description, icon, className, children }: MobileFormCardProps) {
  return (
    <Card className={cn("tool-card", className)}>
      <CardHeader className="pb-4">
        <CardTitle className="flex items-center gap-2 text-lg">
          {icon}
          {title}
        </CardTitle>
        {description && (
          <CardDescription className="text-sm">
            {description}
          </CardDescription>
        )}
      </CardHeader>
      <CardContent className="space-y-4">
        {children}
      </CardContent>
    </Card>
  );
}

// Mobile-optimized form field
export function MobileFormField({ label, description, required, className, children }: MobileFormFieldProps) {
  return (
    <div className={cn("space-y-2", className)}>
      <label className="text-sm font-medium flex items-center gap-1">
        {label}
        {required && <span className="text-red-500">*</span>}
      </label>
      {children}
      {description && (
        <p className="text-xs text-muted-foreground">
          {description}
        </p>
      )}
    </div>
  );
}

// Touch-friendly input
export function TouchInput({ label, description, className, ...props }: TouchInputProps) {
  const input = (
    <Input
      className={cn(
        // Mobile: Larger touch targets
        "min-h-[44px] text-base",
        // Desktop: Normal size
        "md:min-h-[40px] md:text-sm",
        className
      )}
      {...props}
    />
  );

  if (label) {
    return (
      <MobileFormField label={label} description={description}>
        {input}
      </MobileFormField>
    );
  }

  return input;
}

// Touch-friendly select
export function TouchSelect({ label, description, value, onValueChange, placeholder, className, children }: TouchSelectProps) {
  const select = (
    <Select value={value} onValueChange={onValueChange}>
      <SelectTrigger className={cn(
        // Mobile: Larger touch targets
        "min-h-[44px] text-base",
        // Desktop: Normal size
        "md:min-h-[40px] md:text-sm",
        className
      )}>
        <SelectValue placeholder={placeholder} />
      </SelectTrigger>
      <SelectContent>
        {children}
      </SelectContent>
    </Select>
  );

  if (label) {
    return (
      <MobileFormField label={label} description={description}>
        {select}
      </MobileFormField>
    );
  }

  return select;
}

// Touch-friendly textarea
export function TouchTextarea({ label, description, className, ...props }: TouchTextareaProps) {
  const textarea = (
    <Textarea
      className={cn(
        // Mobile: Larger touch targets
        "min-h-[88px] text-base",
        // Desktop: Normal size
        "md:min-h-[80px] md:text-sm",
        className
      )}
      {...props}
    />
  );

  if (label) {
    return (
      <MobileFormField label={label} description={description}>
        {textarea}
      </MobileFormField>
    );
  }

  return textarea;
}

// Touch-friendly button
export function TouchButton({ className, children, ...props }: React.ButtonHTMLAttributes<HTMLButtonElement> & { variant?: string; size?: string }) {
  return (
    <Button
      className={cn(
        // Mobile: Larger touch targets
        "min-h-[44px] px-6 text-base",
        // Desktop: Normal size
        "md:min-h-[40px] md:px-4 md:text-sm",
        className
      )}
      {...props}
    >
      {children}
    </Button>
  );
}
