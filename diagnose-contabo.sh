#!/bin/bash
# NexusScan Contabo Server Diagnostic Script
# Run this script ON the Contabo server after SSH connection

echo "🔍 NexusScan Backend Diagnostic Report"
echo "====================================="
echo ""

echo "1. 🖥️  System Information:"
echo "   Hostname: $(hostname)"
echo "   Uptime: $(uptime)"
echo "   Memory: $(free -h | grep Mem)"
echo ""

echo "2. 🐍 Python Processes:"
ps aux | grep python | grep -v grep || echo "   No Python processes found"
echo ""

echo "3. 🌐 Network Ports:"
echo "   Port 8090 (NexusScan Backend):"
netstat -tlnp | grep 8090 || echo "   Port 8090 not in use"
echo "   Port 8000 (Alternative Backend):"
netstat -tlnp | grep 8000 || echo "   Port 8000 not in use"
echo "   Port 22 (SSH):"
netstat -tlnp | grep :22 || echo "   SSH port not found"
echo ""

echo "4. 📁 Directory Search:"
echo "   Looking for NexusScan directories..."
find / -name "*nexus*" -type d 2>/dev/null | head -10 || echo "   No nexus directories found"
echo ""

echo "5. 🔧 System Services:"
echo "   NexusScan service status:"
systemctl status nexusscan 2>/dev/null || echo "   No nexusscan service found"
echo "   All running services with nexus:"
systemctl list-units --type=service --state=running | grep -i nexus || echo "   No nexus services running"
echo ""

echo "6. 📂 Current Directory Contents:"
echo "   Current location: $(pwd)"
echo "   Directory contents:"
ls -la
echo ""

echo "7. 🔍 Backend Search:"
echo "   Looking for main.py files:"
find / -name "main.py" 2>/dev/null | head -10 || echo "   No main.py files found"
echo "   Looking for Python backend files:"
find / -name "*backend*" -type f 2>/dev/null | head -10 || echo "   No backend files found"
echo ""

echo "8. 🐳 Docker Check:"
echo "   Docker status:"
systemctl status docker 2>/dev/null || echo "   Docker not installed/running"
echo "   Docker containers:"
docker ps 2>/dev/null || echo "   No docker containers or docker not available"
echo ""

echo "9. 🔄 Process Check:"
echo "   All processes containing 'nexus':"
ps aux | grep -i nexus | grep -v grep || echo "   No nexus processes found"
echo ""

echo "10. 📦 Package Check:"
echo "    Python3 version:"
python3 --version 2>/dev/null || echo "   Python3 not found"
echo "    Git version:"
git --version 2>/dev/null || echo "   Git not found"
echo ""

echo "✅ Diagnostic complete!"
echo ""
echo "📋 Next Steps:"
echo "   1. If you found nexus directories, navigate to them: cd /path/to/nexus"
echo "   2. If no backend is running, we need to start it manually"
echo "   3. Check if there are any backend files in common locations like /opt, /home, /root"
