#!/usr/bin/env python3
"""
DeepSeek AI Service Integration for NexusScan
Fallback AI service using DeepSeek-V3 model for security analysis
"""

import asyncio
import json
import logging
import time
from datetime import datetime
from typing import Dict, List, Optional, Any
import aiohttp

from .openai_service import (
    PayloadGenerationRequest, PayloadGenerationResponse,
    SecurityAnalysisRequest, SecurityAnalysisResponse
)

logger = logging.getLogger(__name__)

class DeepSeekService:
    """DeepSeek-V3 AI service implementation for security operations"""
    
    def __init__(self, api_key: str, model: str = "deepseek-chat"):
        """Initialize DeepSeek service
        
        Args:
            api_key: DeepSeek API key
            model: Model to use (default: deepseek-chat for DeepSeek-V3)
        """
        self.api_key = api_key
        self.model = model
        self.base_url = "https://api.deepseek.com/v1"
        self.request_timeout = 45  # seconds (slightly lower than OpenAI)
        self.max_retries = 3
        
        # Performance tracking
        self.stats = {
            "total_requests": 0,
            "successful_requests": 0,
            "failed_requests": 0,
            "average_response_time": 0.0,
            "payload_generations": 0,
            "security_analyses": 0
        }
        
        logger.info(f"DeepSeek service initialized with model: {model}")
    
    async def generate_payload(self, request: PayloadGenerationRequest) -> PayloadGenerationResponse:
        """Generate security payload using DeepSeek-V3 model
        
        Args:
            request: Payload generation request
            
        Returns:
            PayloadGenerationResponse with generated payload
        """
        start_time = time.time()
        self.stats["total_requests"] += 1
        self.stats["payload_generations"] += 1
        
        try:
            # Build DeepSeek-optimized prompts
            system_prompt = self._build_deepseek_payload_prompt()
            user_prompt = self._build_payload_user_prompt(request)
            
            # Make API request to DeepSeek
            payload = {
                "model": self.model,
                "messages": [
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                "temperature": 0.7,
                "max_tokens": 1800,
                "stream": False
            }
            
            async with aiohttp.ClientSession() as session:
                headers = {
                    "Authorization": f"Bearer {self.api_key}",
                    "Content-Type": "application/json"
                }
                
                async with session.post(
                    f"{self.base_url}/chat/completions",
                    json=payload,
                    headers=headers,
                    timeout=aiohttp.ClientTimeout(total=self.request_timeout)
                ) as response:
                    
                    if response.status != 200:
                        raise Exception(f"DeepSeek API error: {response.status}")
                    
                    result = await response.json()
                    response_content = result["choices"][0]["message"]["content"]
            
            # Parse DeepSeek response
            parsed_response = self._parse_payload_response(response_content)
            
            execution_time = time.time() - start_time
            self._update_stats(execution_time, success=True)
            
            return PayloadGenerationResponse(
                success=True,
                primary_payload=parsed_response.get("payload", ""),
                explanation=parsed_response.get("explanation", ""),
                confidence_score=parsed_response.get("confidence", 0.75),  # Slightly lower default confidence
                alternative_payloads=parsed_response.get("alternatives", []),
                execution_steps=parsed_response.get("steps", []),
                preconditions=parsed_response.get("preconditions", []),
                post_exploitation_options=parsed_response.get("post_exploitation", []),
                risk_assessment=parsed_response.get("risk_assessment", {}),
                remediation_advice=parsed_response.get("remediation", ""),
                detection_signatures=parsed_response.get("detection", []),
                execution_time=execution_time,
                model_used=f"DeepSeek-{self.model}"
            )
            
        except Exception as e:
            execution_time = time.time() - start_time
            self._update_stats(execution_time, success=False)
            
            logger.error(f"DeepSeek payload generation failed: {e}")
            return PayloadGenerationResponse(
                success=False,
                primary_payload="",
                explanation="",
                confidence_score=0.0,
                alternative_payloads=[],
                execution_steps=[],
                preconditions=[],
                post_exploitation_options=[],
                risk_assessment={},
                remediation_advice="",
                detection_signatures=[],
                execution_time=execution_time,
                model_used=f"DeepSeek-{self.model}",
                errors=[str(e)]
            )
    
    async def analyze_security_data(self, request: SecurityAnalysisRequest) -> SecurityAnalysisResponse:
        """Perform security analysis using DeepSeek-V3
        
        Args:
            request: Security analysis request
            
        Returns:
            SecurityAnalysisResponse with analysis results
        """
        start_time = time.time()
        self.stats["total_requests"] += 1
        self.stats["security_analyses"] += 1
        
        try:
            # Build analysis prompts optimized for DeepSeek
            system_prompt = self._build_deepseek_analysis_prompt()
            user_prompt = self._build_analysis_user_prompt(request)
            
            # Make API request
            payload = {
                "model": self.model,
                "messages": [
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                "temperature": 0.4,  # Lower temperature for analysis
                "max_tokens": 2200,
                "stream": False
            }
            
            async with aiohttp.ClientSession() as session:
                headers = {
                    "Authorization": f"Bearer {self.api_key}",
                    "Content-Type": "application/json"
                }
                
                async with session.post(
                    f"{self.base_url}/chat/completions",
                    json=payload,
                    headers=headers,
                    timeout=aiohttp.ClientTimeout(total=self.request_timeout)
                ) as response:
                    
                    if response.status != 200:
                        raise Exception(f"DeepSeek API error: {response.status}")
                    
                    result = await response.json()
                    response_content = result["choices"][0]["message"]["content"]
            
            # Parse analysis response
            parsed_analysis = self._parse_analysis_response(response_content)
            
            execution_time = time.time() - start_time
            self._update_stats(execution_time, success=True)
            
            return SecurityAnalysisResponse(
                success=True,
                executive_summary=parsed_analysis.get("executive_summary", ""),
                technical_analysis=parsed_analysis.get("technical_analysis", ""),
                risk_score=parsed_analysis.get("risk_score", 0.0),
                severity_breakdown=parsed_analysis.get("severity_breakdown", {}),
                key_findings=parsed_analysis.get("key_findings", []),
                attack_vectors=parsed_analysis.get("attack_vectors", []),
                business_impact=parsed_analysis.get("business_impact", ""),
                remediation_roadmap=parsed_analysis.get("remediation_roadmap", []),
                compliance_status=parsed_analysis.get("compliance_status", {}),
                recommendations=parsed_analysis.get("recommendations", []),
                execution_time=execution_time,
                model_used=f"DeepSeek-{self.model}"
            )
            
        except Exception as e:
            execution_time = time.time() - start_time
            self._update_stats(execution_time, success=False)
            
            logger.error(f"DeepSeek security analysis failed: {e}")
            return SecurityAnalysisResponse(
                success=False,
                executive_summary="",
                technical_analysis="",
                risk_score=0.0,
                severity_breakdown={},
                key_findings=[],
                attack_vectors=[],
                business_impact="",
                remediation_roadmap=[],
                compliance_status={},
                recommendations=[],
                execution_time=execution_time,
                model_used=f"DeepSeek-{self.model}",
                errors=[str(e)]
            )
    
    def _build_deepseek_payload_prompt(self) -> str:
        """Build system prompt optimized for DeepSeek model"""
        return """You are an expert penetration tester and security researcher. Your expertise includes:
        - Advanced web application security testing
        - Network penetration testing and exploitation
        - Binary exploitation and reverse engineering
        - Social engineering and phishing techniques
        - Post-exploitation and lateral movement
        - Security tool development and automation

        Generate educational security payloads for authorized penetration testing purposes.

        IMPORTANT CONSTRAINTS:
        - Only for authorized, legal security testing
        - Educational purpose with comprehensive explanations
        - Include remediation and defense strategies
        - Provide realistic success probabilities
        - Follow ethical hacking principles

        Respond in JSON format:
        {
            "payload": "functional exploit code",
            "explanation": "detailed explanation of technique",
            "confidence": 0.8,
            "alternatives": ["alt1", "alt2"],
            "steps": ["step1", "step2"],
            "preconditions": ["prereq1", "prereq2"],
            "post_exploitation": ["lateral movement", "persistence"],
            "risk_assessment": {"likelihood": "high", "impact": "medium"},
            "remediation": "specific mitigation steps",
            "detection": ["detection signature 1", "detection signature 2"]
        }"""
    
    def _build_deepseek_analysis_prompt(self) -> str:
        """Build analysis prompt optimized for DeepSeek"""
        return """You are a senior cybersecurity analyst with expertise in:
        - Vulnerability assessment and penetration testing
        - Risk analysis and threat modeling
        - Security architecture and controls assessment
        - Business impact analysis and risk quantification
        - Compliance frameworks (PCI DSS, SOC 2, GDPR, HIPAA)
        - Incident response and remediation planning

        Analyze security scan results and provide comprehensive assessments.

        KEY REQUIREMENTS:
        - Executive summaries for business stakeholders
        - Technical analysis for security teams
        - Quantitative risk scoring and prioritization
        - Actionable remediation recommendations
        - Compliance mapping when applicable
        - Business impact considerations

        Respond in JSON format:
        {
            "executive_summary": "business-focused summary",
            "technical_analysis": "detailed technical assessment",
            "risk_score": 0.85,
            "severity_breakdown": {"critical": 1, "high": 3, "medium": 5},
            "key_findings": [{"title": "finding", "severity": "high", "description": "desc", "impact": "impact", "remediation": "fix"}],
            "attack_vectors": ["vector1", "vector2"],
            "business_impact": "impact assessment",
            "remediation_roadmap": [{"phase": "immediate", "timeline": "24h", "actions": ["action1"], "priority": "critical"}],
            "compliance_status": {"pci_dss": "non_compliant"},
            "recommendations": ["rec1", "rec2"]
        }"""
    
    def _build_payload_user_prompt(self, request: PayloadGenerationRequest) -> str:
        """Build user prompt for payload generation"""
        return f"""
        Generate a {request.difficulty_level} level {request.vulnerability_type} payload:

        TARGET CONTEXT:
        Environment: {request.target_environment}
        Details: {json.dumps(request.target_context, indent=2)}
        Style: {request.payload_style}
        Constraints: {json.dumps(request.constraints, indent=2)}
        
        REQUIREMENTS:
        {request.custom_requirements}
        
        Provide:
        1. Primary functional payload
        2. Alternative approaches
        3. Execution instructions
        4. Prerequisites and conditions
        5. Post-exploitation possibilities
        6. Risk and impact assessment
        7. Detailed remediation guidance
        8. Detection signatures for blue teams
        
        Focus on practical, educational content with strong defensive guidance.
        """
    
    def _build_analysis_user_prompt(self, request: SecurityAnalysisRequest) -> str:
        """Build user prompt for security analysis"""
        return f"""
        Analyze these security scan results with {request.analysis_scope} scope:

        SCAN DATA:
        {json.dumps(request.scan_data, indent=2)}

        ANALYSIS CONTEXT:
        Focus Areas: {', '.join(request.focus_areas) if request.focus_areas else 'Comprehensive'}
        Business Context: {json.dumps(request.business_context, indent=2)}
        Compliance: {', '.join(request.compliance_frameworks) if request.compliance_frameworks else 'General'}

        Provide comprehensive analysis:
        1. Executive summary for leadership
        2. Technical details for security teams
        3. Risk scoring and prioritization
        4. Key findings with business impact
        5. Attack vectors and threat scenarios
        6. Phased remediation plan
        7. Compliance assessment
        8. Strategic recommendations

        Consider business context, resource constraints, and operational impact.
        """
    
    def _parse_payload_response(self, response_content: str) -> Dict[str, Any]:
        """Parse DeepSeek response for payload generation"""
        try:
            # Extract JSON from response (similar to OpenAI but adapted for DeepSeek format)
            if "```json" in response_content:
                json_start = response_content.find("```json") + 7
                json_end = response_content.find("```", json_start)
                json_content = response_content[json_start:json_end].strip()
            elif "{" in response_content and "}" in response_content:
                json_start = response_content.find("{")
                json_end = response_content.rfind("}") + 1
                json_content = response_content[json_start:json_end]
            else:
                # Fallback for DeepSeek text responses
                return {
                    "payload": response_content[:400],
                    "explanation": "DeepSeek provided text response, JSON parsing failed",
                    "confidence": 0.6,
                    "alternatives": [],
                    "steps": ["Manual review of response required"],
                    "preconditions": [],
                    "post_exploitation": [],
                    "risk_assessment": {"likelihood": "unknown", "impact": "unknown"},
                    "remediation": "Manual analysis required",
                    "detection": []
                }
            
            parsed = json.loads(json_content)
            
            # Validate required fields for DeepSeek responses
            required_fields = ["payload", "explanation", "confidence"]
            for field in required_fields:
                if field not in parsed:
                    parsed[field] = f"Missing {field} in DeepSeek response"
            
            return parsed
            
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse DeepSeek payload response: {e}")
            return {
                "payload": response_content[:400],
                "explanation": f"DeepSeek JSON parsing failed: {e}",
                "confidence": 0.4,
                "alternatives": [],
                "steps": [],
                "preconditions": [],
                "post_exploitation": [],
                "risk_assessment": {},
                "remediation": "Manual review required",
                "detection": []
            }
    
    def _parse_analysis_response(self, response_content: str) -> Dict[str, Any]:
        """Parse DeepSeek response for security analysis"""
        try:
            # Extract JSON from DeepSeek response
            if "```json" in response_content:
                json_start = response_content.find("```json") + 7
                json_end = response_content.find("```", json_start)
                json_content = response_content[json_start:json_end].strip()
            elif "{" in response_content and "}" in response_content:
                json_start = response_content.find("{")
                json_end = response_content.rfind("}") + 1
                json_content = response_content[json_start:json_end]
            else:
                # DeepSeek fallback parsing
                return {
                    "executive_summary": response_content[:250],
                    "technical_analysis": response_content,
                    "risk_score": 0.5,
                    "severity_breakdown": {"unknown": 1},
                    "key_findings": [{"title": "DeepSeek Response", "severity": "info", "description": response_content[:200], "impact": "Manual review needed", "remediation": "Analyze response manually"}],
                    "attack_vectors": [],
                    "business_impact": "DeepSeek analysis requires manual review",
                    "remediation_roadmap": [],
                    "compliance_status": {},
                    "recommendations": ["Review DeepSeek analysis manually", "Consider using primary AI service"]
                }
            
            parsed = json.loads(json_content)
            
            # Ensure required fields exist
            defaults = {
                "executive_summary": "DeepSeek analysis completed",
                "technical_analysis": response_content,
                "risk_score": 0.5,
                "severity_breakdown": {},
                "key_findings": [],
                "attack_vectors": [],
                "business_impact": "Analysis provided by DeepSeek fallback service",
                "remediation_roadmap": [],
                "compliance_status": {},
                "recommendations": []
            }
            
            for key, default_value in defaults.items():
                if key not in parsed:
                    parsed[key] = default_value
            
            return parsed
            
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse DeepSeek analysis response: {e}")
            return {
                "executive_summary": "DeepSeek parsing failed",
                "technical_analysis": response_content,
                "risk_score": 0.0,
                "severity_breakdown": {},
                "key_findings": [],
                "attack_vectors": [],
                "business_impact": f"DeepSeek JSON parsing error: {e}",
                "remediation_roadmap": [],
                "compliance_status": {},
                "recommendations": ["Manual review of DeepSeek response required"]
            }
    
    def _update_stats(self, execution_time: float, success: bool):
        """Update service statistics"""
        if success:
            self.stats["successful_requests"] += 1
        else:
            self.stats["failed_requests"] += 1
        
        # Update average response time
        total_requests = self.stats["successful_requests"] + self.stats["failed_requests"]
        if total_requests > 0:
            self.stats["average_response_time"] = (
                (self.stats["average_response_time"] * (total_requests - 1) + execution_time) / total_requests
            )
    
    def get_service_stats(self) -> Dict[str, Any]:
        """Get DeepSeek service statistics"""
        success_rate = 0.0
        if self.stats["total_requests"] > 0:
            success_rate = self.stats["successful_requests"] / self.stats["total_requests"]
        
        return {
            **self.stats,
            "success_rate": success_rate,
            "service_health": "healthy" if success_rate > 0.75 else "degraded" if success_rate > 0.4 else "unhealthy",
            "service_type": "fallback"
        }
    
    async def test_connection(self) -> Dict[str, Any]:
        """Test DeepSeek service connection"""
        try:
            start_time = time.time()
            
            # Simple test request
            payload = {
                "model": self.model,
                "messages": [
                    {"role": "user", "content": "Respond with 'DeepSeek service operational' if you can read this message."}
                ],
                "max_tokens": 30,
                "temperature": 0.1
            }
            
            async with aiohttp.ClientSession() as session:
                headers = {
                    "Authorization": f"Bearer {self.api_key}",
                    "Content-Type": "application/json"
                }
                
                async with session.post(
                    f"{self.base_url}/chat/completions",
                    json=payload,
                    headers=headers,
                    timeout=aiohttp.ClientTimeout(total=10)
                ) as response:
                    
                    if response.status != 200:
                        raise Exception(f"HTTP {response.status}")
                    
                    result = await response.json()
                    response_content = result["choices"][0]["message"]["content"]
            
            execution_time = time.time() - start_time
            
            return {
                "success": True,
                "model": self.model,
                "response_time": execution_time,
                "message": "DeepSeek service connection successful",
                "api_response": response_content
            }
            
        except Exception as e:
            return {
                "success": False,
                "model": self.model,
                "error": str(e),
                "message": "DeepSeek service connection failed"
            }