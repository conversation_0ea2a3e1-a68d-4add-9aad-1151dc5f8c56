"""
Role-Based Access Control (RBAC) Manager for NexusScan Desktop
Enterprise-grade user management with roles, permissions, and API key management.
"""

import asyncio
import logging
import json
import time
import uuid
import hashlib
import secrets
from typing import Dict, List, Optional, Any, Set, Tuple
from enum import Enum
from datetime import datetime, timed<PERSON>ta
from dataclasses import dataclass, asdict
from pathlib import Path
import bcrypt
import jwt

from core.config import Config
from core.database import DatabaseManager
from core.events import EventManager, EventTypes
from ai.ai_service import AIServiceManager

logger = logging.getLogger(__name__)


class UserRole(Enum):
    """User roles in the system"""
    SUPER_ADMIN = "super_admin"
    ADMIN = "admin"
    TEAM_LEAD = "team_lead"
    SENIOR_ANALYST = "senior_analyst"
    ANALYST = "analyst"
    JUNIOR_ANALYST = "junior_analyst"
    AUDITOR = "auditor"
    READ_ONLY = "read_only"
    GUEST = "guest"


class Permission(Enum):
    """System permissions"""
    # User Management
    USER_CREATE = "user.create"
    USER_READ = "user.read"
    USER_UPDATE = "user.update"
    USER_DELETE = "user.delete"
    USER_MANAGE_ROLES = "user.manage_roles"
    
    # Campaign Management
    CAMPAIGN_CREATE = "campaign.create"
    CAMPAIGN_READ = "campaign.read"
    CAMPAIGN_UPDATE = "campaign.update"
    CAMPAIGN_DELETE = "campaign.delete"
    CAMPAIGN_EXECUTE = "campaign.execute"
    CAMPAIGN_MANAGE_TEAM = "campaign.manage_team"
    
    # Scanning and Testing
    SCAN_CREATE = "scan.create"
    SCAN_READ = "scan.read"
    SCAN_EXECUTE = "scan.execute"
    SCAN_DELETE = "scan.delete"
    SCAN_REAL_MODE = "scan.real_mode"
    
    # Reporting
    REPORT_CREATE = "report.create"
    REPORT_READ = "report.read"
    REPORT_UPDATE = "report.update"
    REPORT_DELETE = "report.delete"
    REPORT_EXPORT = "report.export"
    REPORT_SHARE = "report.share"
    
    # Compliance
    COMPLIANCE_READ = "compliance.read"
    COMPLIANCE_EXECUTE = "compliance.execute"
    COMPLIANCE_MANAGE = "compliance.manage"
    
    # System Administration
    SYSTEM_CONFIG = "system.config"
    SYSTEM_LOGS = "system.logs"
    SYSTEM_BACKUP = "system.backup"
    SYSTEM_UPDATE = "system.update"
    
    # API and Integration
    API_ACCESS = "api.access"
    API_MANAGE_KEYS = "api.manage_keys"
    INTEGRATION_MANAGE = "integration.manage"


class APIKeyStatus(Enum):
    """API key status"""
    ACTIVE = "active"
    INACTIVE = "inactive"
    EXPIRED = "expired"
    REVOKED = "revoked"


@dataclass
class User:
    """User account information"""
    user_id: str
    username: str
    email: str
    password_hash: str
    display_name: str
    role: UserRole
    department: str
    created_date: datetime
    last_login: Optional[datetime]
    is_active: bool
    is_locked: bool
    failed_login_attempts: int
    password_changed_date: datetime
    must_change_password: bool
    two_factor_enabled: bool
    two_factor_secret: Optional[str]
    profile_data: Dict[str, Any]
    preferences: Dict[str, Any]
    metadata: Dict[str, Any]


@dataclass
class RoleDefinition:
    """Role definition with permissions"""
    role: UserRole
    name: str
    description: str
    permissions: Set[Permission]
    max_concurrent_sessions: int
    session_timeout_minutes: int
    can_delegate: bool
    restrictions: Dict[str, Any]


@dataclass
class APIKey:
    """API key for external integrations"""
    key_id: str
    user_id: str
    key_hash: str
    name: str
    description: str
    permissions: Set[Permission]
    status: APIKeyStatus
    created_date: datetime
    expires_date: Optional[datetime]
    last_used_date: Optional[datetime]
    usage_count: int
    ip_restrictions: List[str]
    rate_limit: int  # requests per minute
    metadata: Dict[str, Any]


@dataclass
class UserSession:
    """User session tracking"""
    session_id: str
    user_id: str
    created_date: datetime
    last_activity: datetime
    ip_address: str
    user_agent: str
    is_active: bool
    expires_date: datetime


class RBACManager:
    """Role-Based Access Control Manager"""
    
    def __init__(self, config: Config, db_manager: DatabaseManager,
                 event_manager: EventManager, ai_service: AIServiceManager):
        """Initialize RBAC manager"""
        self.config = config
        self.db_manager = db_manager
        self.event_manager = event_manager
        self.ai_service = ai_service
        
        # Data storage
        self.users: Dict[str, User] = {}
        self.role_definitions: Dict[UserRole, RoleDefinition] = {}
        self.api_keys: Dict[str, APIKey] = {}
        self.active_sessions: Dict[str, UserSession] = {}
        
        # Security settings
        self.jwt_secret = secrets.token_urlsafe(32)
        self.password_policy = {
            "min_length": 8,
            "require_uppercase": True,
            "require_lowercase": True,
            "require_numbers": True,
            "require_symbols": True,
            "max_age_days": 90,
            "history_count": 12
        }
        
        # Initialize role definitions
        self._initialize_role_definitions()
        
        # Create default admin user
        self._create_default_admin()
        
        logger.info("RBAC manager initialized")
    
    def _initialize_role_definitions(self):
        """Initialize built-in role definitions"""
        # Super Admin - Full system access
        self.role_definitions[UserRole.SUPER_ADMIN] = RoleDefinition(
            role=UserRole.SUPER_ADMIN,
            name="Super Administrator",
            description="Full system access with all permissions",
            permissions=set(Permission),  # All permissions
            max_concurrent_sessions=5,
            session_timeout_minutes=480,  # 8 hours
            can_delegate=True,
            restrictions={}
        )
        
        # Admin - Administrative access
        admin_permissions = {
            Permission.USER_CREATE, Permission.USER_READ, Permission.USER_UPDATE, Permission.USER_DELETE,
            Permission.CAMPAIGN_CREATE, Permission.CAMPAIGN_READ, Permission.CAMPAIGN_UPDATE, Permission.CAMPAIGN_DELETE,
            Permission.CAMPAIGN_EXECUTE, Permission.CAMPAIGN_MANAGE_TEAM,
            Permission.SCAN_CREATE, Permission.SCAN_READ, Permission.SCAN_EXECUTE, Permission.SCAN_DELETE,
            Permission.SCAN_REAL_MODE,
            Permission.REPORT_CREATE, Permission.REPORT_READ, Permission.REPORT_UPDATE, Permission.REPORT_DELETE,
            Permission.REPORT_EXPORT, Permission.REPORT_SHARE,
            Permission.COMPLIANCE_READ, Permission.COMPLIANCE_EXECUTE, Permission.COMPLIANCE_MANAGE,
            Permission.SYSTEM_CONFIG, Permission.SYSTEM_LOGS, Permission.SYSTEM_BACKUP,
            Permission.API_ACCESS, Permission.API_MANAGE_KEYS, Permission.INTEGRATION_MANAGE
        }
        
        self.role_definitions[UserRole.ADMIN] = RoleDefinition(
            role=UserRole.ADMIN,
            name="Administrator",
            description="Administrative access to most system functions",
            permissions=admin_permissions,
            max_concurrent_sessions=3,
            session_timeout_minutes=240,  # 4 hours
            can_delegate=True,
            restrictions={}
        )
        
        # Team Lead - Team management and execution
        team_lead_permissions = {
            Permission.USER_READ,
            Permission.CAMPAIGN_CREATE, Permission.CAMPAIGN_READ, Permission.CAMPAIGN_UPDATE,
            Permission.CAMPAIGN_EXECUTE, Permission.CAMPAIGN_MANAGE_TEAM,
            Permission.SCAN_CREATE, Permission.SCAN_READ, Permission.SCAN_EXECUTE, Permission.SCAN_DELETE,
            Permission.SCAN_REAL_MODE,
            Permission.REPORT_CREATE, Permission.REPORT_READ, Permission.REPORT_UPDATE,
            Permission.REPORT_EXPORT, Permission.REPORT_SHARE,
            Permission.COMPLIANCE_READ, Permission.COMPLIANCE_EXECUTE,
            Permission.API_ACCESS
        }
        
        self.role_definitions[UserRole.TEAM_LEAD] = RoleDefinition(
            role=UserRole.TEAM_LEAD,
            name="Team Lead",
            description="Team leadership with campaign and execution permissions",
            permissions=team_lead_permissions,
            max_concurrent_sessions=2,
            session_timeout_minutes=180,  # 3 hours
            can_delegate=False,
            restrictions={}
        )
        
        # Senior Analyst - Advanced testing capabilities
        senior_analyst_permissions = {
            Permission.USER_READ,
            Permission.CAMPAIGN_READ, Permission.CAMPAIGN_UPDATE,
            Permission.SCAN_CREATE, Permission.SCAN_READ, Permission.SCAN_EXECUTE, Permission.SCAN_DELETE,
            Permission.SCAN_REAL_MODE,
            Permission.REPORT_CREATE, Permission.REPORT_READ, Permission.REPORT_UPDATE,
            Permission.REPORT_EXPORT, Permission.REPORT_SHARE,
            Permission.COMPLIANCE_READ, Permission.COMPLIANCE_EXECUTE,
            Permission.API_ACCESS
        }
        
        self.role_definitions[UserRole.SENIOR_ANALYST] = RoleDefinition(
            role=UserRole.SENIOR_ANALYST,
            name="Senior Security Analyst",
            description="Advanced security testing and analysis capabilities",
            permissions=senior_analyst_permissions,
            max_concurrent_sessions=2,
            session_timeout_minutes=120,  # 2 hours
            can_delegate=False,
            restrictions={}
        )
        
        # Analyst - Standard security testing
        analyst_permissions = {
            Permission.USER_READ,
            Permission.CAMPAIGN_READ,
            Permission.SCAN_CREATE, Permission.SCAN_READ, Permission.SCAN_EXECUTE,
            Permission.REPORT_CREATE, Permission.REPORT_READ, Permission.REPORT_UPDATE,
            Permission.REPORT_EXPORT,
            Permission.COMPLIANCE_READ,
            Permission.API_ACCESS
        }
        
        self.role_definitions[UserRole.ANALYST] = RoleDefinition(
            role=UserRole.ANALYST,
            name="Security Analyst",
            description="Standard security testing and reporting capabilities",
            permissions=analyst_permissions,
            max_concurrent_sessions=2,
            session_timeout_minutes=120,  # 2 hours
            can_delegate=False,
            restrictions={"real_mode": False}
        )
        
        # Junior Analyst - Limited access
        junior_permissions = {
            Permission.USER_READ,
            Permission.CAMPAIGN_READ,
            Permission.SCAN_READ,
            Permission.REPORT_READ,
            Permission.COMPLIANCE_READ
        }
        
        self.role_definitions[UserRole.JUNIOR_ANALYST] = RoleDefinition(
            role=UserRole.JUNIOR_ANALYST,
            name="Junior Security Analyst",
            description="Limited access for training and observation",
            permissions=junior_permissions,
            max_concurrent_sessions=1,
            session_timeout_minutes=60,  # 1 hour
            can_delegate=False,
            restrictions={"simulation_only": True}
        )
        
        # Auditor - Compliance and audit access
        auditor_permissions = {
            Permission.USER_READ,
            Permission.CAMPAIGN_READ,
            Permission.SCAN_READ,
            Permission.REPORT_READ, Permission.REPORT_EXPORT,
            Permission.COMPLIANCE_READ, Permission.COMPLIANCE_EXECUTE,
            Permission.SYSTEM_LOGS
        }
        
        self.role_definitions[UserRole.AUDITOR] = RoleDefinition(
            role=UserRole.AUDITOR,
            name="Security Auditor",
            description="Compliance and audit-focused access",
            permissions=auditor_permissions,
            max_concurrent_sessions=1,
            session_timeout_minutes=240,  # 4 hours
            can_delegate=False,
            restrictions={}
        )
        
        # Read Only - View access only
        read_only_permissions = {
            Permission.USER_READ,
            Permission.CAMPAIGN_READ,
            Permission.SCAN_READ,
            Permission.REPORT_READ,
            Permission.COMPLIANCE_READ
        }
        
        self.role_definitions[UserRole.READ_ONLY] = RoleDefinition(
            role=UserRole.READ_ONLY,
            name="Read Only",
            description="View-only access to system resources",
            permissions=read_only_permissions,
            max_concurrent_sessions=1,
            session_timeout_minutes=60,  # 1 hour
            can_delegate=False,
            restrictions={}
        )
        
        logger.info(f"Initialized {len(self.role_definitions)} role definitions")
    
    def _create_default_admin(self):
        """Create default admin user"""
        admin_id = "admin"
        
        if admin_id not in self.users:
            admin_user = User(
                user_id=admin_id,
                username="admin",
                email="<EMAIL>",
                password_hash=self._hash_password("NexusScan@2025"),
                display_name="System Administrator",
                role=UserRole.SUPER_ADMIN,
                department="Security",
                created_date=datetime.now(),
                last_login=None,
                is_active=True,
                is_locked=False,
                failed_login_attempts=0,
                password_changed_date=datetime.now(),
                must_change_password=True,
                two_factor_enabled=False,
                two_factor_secret=None,
                profile_data={},
                preferences={"theme": "dark", "notifications": True},
                metadata={"created_by": "system"}
            )
            
            self.users[admin_id] = admin_user
            logger.info("Created default admin user")
    
    def _hash_password(self, password: str) -> str:
        """Hash password using bcrypt"""
        salt = bcrypt.gensalt()
        return bcrypt.hashpw(password.encode('utf-8'), salt).decode('utf-8')
    
    def _verify_password(self, password: str, password_hash: str) -> bool:
        """Verify password against hash"""
        return bcrypt.checkpw(password.encode('utf-8'), password_hash.encode('utf-8'))
    
    async def create_user(self, user_data: Dict[str, Any], created_by: str) -> str:
        """Create new user account"""
        try:
            # Check permissions
            if not await self.check_permission(created_by, Permission.USER_CREATE):
                raise PermissionError("Insufficient permissions to create users")
            
            user_id = f"user_{uuid.uuid4().hex[:8]}"
            
            # Validate required fields
            required_fields = ["username", "email", "password", "display_name", "role"]
            for field in required_fields:
                if field not in user_data:
                    raise ValueError(f"Missing required field: {field}")
            
            # Check if username/email already exists
            for user in self.users.values():
                if user.username == user_data["username"]:
                    raise ValueError("Username already exists")
                if user.email == user_data["email"]:
                    raise ValueError("Email already exists")
            
            # Validate password policy
            self._validate_password_policy(user_data["password"])
            
            # Create user
            user = User(
                user_id=user_id,
                username=user_data["username"],
                email=user_data["email"],
                password_hash=self._hash_password(user_data["password"]),
                display_name=user_data["display_name"],
                role=UserRole(user_data["role"]),
                department=user_data.get("department", "Security"),
                created_date=datetime.now(),
                last_login=None,
                is_active=user_data.get("is_active", True),
                is_locked=False,
                failed_login_attempts=0,
                password_changed_date=datetime.now(),
                must_change_password=user_data.get("must_change_password", False),
                two_factor_enabled=False,
                two_factor_secret=None,
                profile_data=user_data.get("profile_data", {}),
                preferences=user_data.get("preferences", {}),
                metadata={"created_by": created_by}
            )
            
            self.users[user_id] = user
            
            # Emit user created event
            await self.event_manager.emit(
                EventTypes.USER_CREATED,
                {
                    "user_id": user_id,
                    "username": user.username,
                    "role": user.role.value,
                    "created_by": created_by
                },
                "rbac_manager"
            )
            
            logger.info(f"Created user: {user_id}")
            return user_id
            
        except Exception as e:
            logger.error(f"Failed to create user: {e}")
            raise
    
    def _validate_password_policy(self, password: str):
        """Validate password against policy"""
        policy = self.password_policy
        
        if len(password) < policy["min_length"]:
            raise ValueError(f"Password must be at least {policy['min_length']} characters")
        
        if policy["require_uppercase"] and not any(c.isupper() for c in password):
            raise ValueError("Password must contain uppercase letters")
        
        if policy["require_lowercase"] and not any(c.islower() for c in password):
            raise ValueError("Password must contain lowercase letters")
        
        if policy["require_numbers"] and not any(c.isdigit() for c in password):
            raise ValueError("Password must contain numbers")
        
        if policy["require_symbols"] and not any(c in "!@#$%^&*()_+-=[]{}|;:,.<>?" for c in password):
            raise ValueError("Password must contain symbols")
    
    async def authenticate_user(self, username: str, password: str, ip_address: str = "127.0.0.1") -> Optional[str]:
        """Authenticate user and create session"""
        try:
            # Find user
            user = None
            for u in self.users.values():
                if u.username == username:
                    user = u
                    break
            
            if not user:
                logger.warning(f"Authentication failed: user not found - {username}")
                return None
            
            # Check if account is locked
            if user.is_locked:
                logger.warning(f"Authentication failed: account locked - {username}")
                return None
            
            # Check if account is active
            if not user.is_active:
                logger.warning(f"Authentication failed: account inactive - {username}")
                return None
            
            # Verify password
            if not self._verify_password(password, user.password_hash):
                user.failed_login_attempts += 1
                
                # Lock account after 5 failed attempts
                if user.failed_login_attempts >= 5:
                    user.is_locked = True
                    logger.warning(f"Account locked due to failed login attempts: {username}")
                
                logger.warning(f"Authentication failed: invalid password - {username}")
                return None
            
            # Reset failed login attempts on successful authentication
            user.failed_login_attempts = 0
            user.last_login = datetime.now()
            
            # Create session
            session_id = await self._create_session(user.user_id, ip_address)
            
            # Emit login event
            await self.event_manager.emit(
                EventTypes.USER_LOGIN,
                {
                    "user_id": user.user_id,
                    "username": user.username,
                    "ip_address": ip_address,
                    "session_id": session_id
                },
                "rbac_manager"
            )
            
            logger.info(f"User authenticated successfully: {username}")
            return session_id
            
        except Exception as e:
            logger.error(f"Authentication error: {e}")
            return None
    
    async def _create_session(self, user_id: str, ip_address: str, user_agent: str = "NexusScan Desktop") -> str:
        """Create user session"""
        session_id = f"sess_{uuid.uuid4().hex}"
        user = self.users[user_id]
        role_def = self.role_definitions[user.role]
        
        # Calculate session expiry
        expires_date = datetime.now() + timedelta(minutes=role_def.session_timeout_minutes)
        
        # Check concurrent session limit
        user_sessions = [s for s in self.active_sessions.values() if s.user_id == user_id and s.is_active]
        if len(user_sessions) >= role_def.max_concurrent_sessions:
            # Expire oldest session
            oldest_session = min(user_sessions, key=lambda s: s.created_date)
            oldest_session.is_active = False
            logger.info(f"Expired oldest session for concurrent limit: {oldest_session.session_id}")
        
        session = UserSession(
            session_id=session_id,
            user_id=user_id,
            created_date=datetime.now(),
            last_activity=datetime.now(),
            ip_address=ip_address,
            user_agent=user_agent,
            is_active=True,
            expires_date=expires_date
        )
        
        self.active_sessions[session_id] = session
        return session_id
    
    async def check_permission(self, user_id: str, permission: Permission) -> bool:
        """Check if user has specific permission"""
        try:
            if user_id not in self.users:
                return False
            
            user = self.users[user_id]
            
            if not user.is_active or user.is_locked:
                return False
            
            role_def = self.role_definitions[user.role]
            return permission in role_def.permissions
            
        except Exception as e:
            logger.error(f"Permission check error: {e}")
            return False
    
    async def validate_session(self, session_id: str) -> Optional[str]:
        """Validate session and return user ID"""
        try:
            if session_id not in self.active_sessions:
                return None
            
            session = self.active_sessions[session_id]
            
            if not session.is_active:
                return None
            
            if datetime.now() > session.expires_date:
                session.is_active = False
                return None
            
            # Update last activity
            session.last_activity = datetime.now()
            
            return session.user_id
            
        except Exception as e:
            logger.error(f"Session validation error: {e}")
            return None
    
    async def logout_user(self, session_id: str) -> bool:
        """Logout user and invalidate session"""
        try:
            if session_id in self.active_sessions:
                session = self.active_sessions[session_id]
                session.is_active = False
                
                await self.event_manager.emit(
                    EventTypes.USER_LOGOUT,
                    {
                        "user_id": session.user_id,
                        "session_id": session_id
                    },
                    "rbac_manager"
                )
                
                logger.info(f"User logged out: {session.user_id}")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Logout error: {e}")
            return False
    
    async def create_api_key(self, user_id: str, key_data: Dict[str, Any]) -> Tuple[str, str]:
        """Create API key for user"""
        try:
            # Check permissions
            if not await self.check_permission(user_id, Permission.API_MANAGE_KEYS):
                raise PermissionError("Insufficient permissions to create API keys")
            
            key_id = f"key_{uuid.uuid4().hex[:8]}"
            api_key = f"nxs_{secrets.token_urlsafe(32)}"
            key_hash = hashlib.sha256(api_key.encode()).hexdigest()
            
            # Parse permissions
            permissions = set()
            for perm_str in key_data.get("permissions", []):
                try:
                    permissions.add(Permission(perm_str))
                except ValueError:
                    logger.warning(f"Invalid permission in API key creation: {perm_str}")
            
            # Validate user has these permissions
            user_permissions = self.role_definitions[self.users[user_id].role].permissions
            if not permissions.issubset(user_permissions):
                raise PermissionError("Cannot grant permissions you don't have")
            
            # Set expiry date
            expires_date = None
            if key_data.get("expires_days"):
                expires_date = datetime.now() + timedelta(days=key_data["expires_days"])
            
            api_key_obj = APIKey(
                key_id=key_id,
                user_id=user_id,
                key_hash=key_hash,
                name=key_data["name"],
                description=key_data.get("description", ""),
                permissions=permissions,
                status=APIKeyStatus.ACTIVE,
                created_date=datetime.now(),
                expires_date=expires_date,
                last_used_date=None,
                usage_count=0,
                ip_restrictions=key_data.get("ip_restrictions", []),
                rate_limit=key_data.get("rate_limit", 100),  # 100 req/min default
                metadata={}
            )
            
            self.api_keys[key_id] = api_key_obj
            
            logger.info(f"Created API key: {key_id} for user {user_id}")
            return key_id, api_key
            
        except Exception as e:
            logger.error(f"Failed to create API key: {e}")
            raise
    
    async def validate_api_key(self, api_key: str) -> Optional[Tuple[str, Set[Permission]]]:
        """Validate API key and return user ID and permissions"""
        try:
            key_hash = hashlib.sha256(api_key.encode()).hexdigest()
            
            for key_obj in self.api_keys.values():
                if key_obj.key_hash == key_hash:
                    # Check status
                    if key_obj.status != APIKeyStatus.ACTIVE:
                        return None
                    
                    # Check expiry
                    if key_obj.expires_date and datetime.now() > key_obj.expires_date:
                        key_obj.status = APIKeyStatus.EXPIRED
                        return None
                    
                    # Update usage
                    key_obj.last_used_date = datetime.now()
                    key_obj.usage_count += 1
                    
                    return key_obj.user_id, key_obj.permissions
            
            return None
            
        except Exception as e:
            logger.error(f"API key validation error: {e}")
            return None
    
    async def revoke_api_key(self, key_id: str, user_id: str) -> bool:
        """Revoke API key"""
        try:
            if key_id not in self.api_keys:
                return False
            
            api_key = self.api_keys[key_id]
            
            # Check if user owns the key or has admin permissions
            if api_key.user_id != user_id and not await self.check_permission(user_id, Permission.API_MANAGE_KEYS):
                raise PermissionError("Cannot revoke API key")
            
            api_key.status = APIKeyStatus.REVOKED
            
            logger.info(f"Revoked API key: {key_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to revoke API key: {e}")
            return False
    
    def get_user_profile(self, user_id: str) -> Optional[Dict[str, Any]]:
        """Get user profile information"""
        if user_id not in self.users:
            return None
        
        user = self.users[user_id]
        role_def = self.role_definitions[user.role]
        
        return {
            "user_id": user.user_id,
            "username": user.username,
            "email": user.email,
            "display_name": user.display_name,
            "role": user.role.value,
            "role_name": role_def.name,
            "department": user.department,
            "created_date": user.created_date.isoformat(),
            "last_login": user.last_login.isoformat() if user.last_login else None,
            "is_active": user.is_active,
            "is_locked": user.is_locked,
            "two_factor_enabled": user.two_factor_enabled,
            "must_change_password": user.must_change_password,
            "profile_data": user.profile_data,
            "preferences": user.preferences,
            "permissions": [p.value for p in role_def.permissions]
        }
    
    def list_users(self, requesting_user_id: str) -> List[Dict[str, Any]]:
        """List all users (with permission check)"""
        users = []
        
        # Check if user can view other users
        if not asyncio.run(self.check_permission(requesting_user_id, Permission.USER_READ)):
            return []
        
        for user in self.users.values():
            role_def = self.role_definitions[user.role]
            
            users.append({
                "user_id": user.user_id,
                "username": user.username,
                "email": user.email,
                "display_name": user.display_name,
                "role": user.role.value,
                "role_name": role_def.name,
                "department": user.department,
                "is_active": user.is_active,
                "is_locked": user.is_locked,
                "last_login": user.last_login.isoformat() if user.last_login else None,
                "created_date": user.created_date.isoformat()
            })
        
        return sorted(users, key=lambda x: x["created_date"], reverse=True)
    
    def get_user_api_keys(self, user_id: str) -> List[Dict[str, Any]]:
        """Get API keys for user"""
        keys = []
        
        for key in self.api_keys.values():
            if key.user_id == user_id:
                keys.append({
                    "key_id": key.key_id,
                    "name": key.name,
                    "description": key.description,
                    "status": key.status.value,
                    "created_date": key.created_date.isoformat(),
                    "expires_date": key.expires_date.isoformat() if key.expires_date else None,
                    "last_used_date": key.last_used_date.isoformat() if key.last_used_date else None,
                    "usage_count": key.usage_count,
                    "permissions": [p.value for p in key.permissions],
                    "rate_limit": key.rate_limit
                })
        
        return sorted(keys, key=lambda x: x["created_date"], reverse=True)
    
    def get_role_definitions(self) -> List[Dict[str, Any]]:
        """Get all role definitions"""
        roles = []
        
        for role_def in self.role_definitions.values():
            roles.append({
                "role": role_def.role.value,
                "name": role_def.name,
                "description": role_def.description,
                "permissions": [p.value for p in role_def.permissions],
                "max_concurrent_sessions": role_def.max_concurrent_sessions,
                "session_timeout_minutes": role_def.session_timeout_minutes,
                "can_delegate": role_def.can_delegate,
                "restrictions": role_def.restrictions
            })
        
        return roles
    
    def get_security_metrics(self) -> Dict[str, Any]:
        """Get security and access metrics"""
        total_users = len(self.users)
        active_users = len([u for u in self.users.values() if u.is_active])
        locked_users = len([u for u in self.users.values() if u.is_locked])
        active_sessions = len([s for s in self.active_sessions.values() if s.is_active])
        total_api_keys = len(self.api_keys)
        active_api_keys = len([k for k in self.api_keys.values() if k.status == APIKeyStatus.ACTIVE])
        
        # Role distribution
        role_distribution = {}
        for user in self.users.values():
            role = user.role.value
            role_distribution[role] = role_distribution.get(role, 0) + 1
        
        return {
            "total_users": total_users,
            "active_users": active_users,
            "locked_users": locked_users,
            "active_sessions": active_sessions,
            "total_api_keys": total_api_keys,
            "active_api_keys": active_api_keys,
            "role_distribution": role_distribution,
            "session_timeout_enabled": True,
            "two_factor_adoption": len([u for u in self.users.values() if u.two_factor_enabled]) / total_users * 100 if total_users > 0 else 0
        }