#!/usr/bin/env python3
"""
Integration Ecosystem Framework
Comprehensive integration platform with API management, webhooks,
third-party connectors, and extensibility framework for enterprise integrations
"""

import asyncio
import aiohttp
import json
import logging
import hashlib
import time
import hmac
import base64
from typing import Any, Dict, List, Optional, Set, Tuple, Union, Callable
from dataclasses import dataclass, field, asdict
from datetime import datetime, timedelta
from enum import Enum
from pathlib import Path
import sqlite3
import threading
from urllib.parse import urljoin, urlparse
import uuid

try:
    from ..ai.orchestrator import AIOrchestrator, AIRequest, TaskType, get_ai_orchestrator
    from ..auth.rbac_system import get_rbac_manager, PermissionType
    from ..tenancy.multi_tenant_manager import get_multi_tenant_manager
    from ..core.events import EventManager, EventTypes
    from ..reporting.enterprise_dashboard import get_enterprise_dashboard
except ImportError:
    # Handle import for standalone testing
    import sys
    from pathlib import Path
    sys.path.append(str(Path(__file__).parent.parent))
    from ai.orchestrator import AIOrchestrator, AIRequest, TaskType, get_ai_orchestrator
    from auth.rbac_system import get_rbac_manager, PermissionType
    from tenancy.multi_tenant_manager import get_multi_tenant_manager
    from core.events import EventManager, EventTypes
    from reporting.enterprise_dashboard import get_enterprise_dashboard

logger = logging.getLogger(__name__)

class IntegrationType(Enum):
    """Types of integrations supported"""
    WEBHOOK = "webhook"
    REST_API = "rest_api"
    GRAPHQL = "graphql"
    DATABASE = "database"
    MESSAGE_QUEUE = "message_queue"
    FILE_TRANSFER = "file_transfer"
    EMAIL = "email"
    SLACK = "slack"
    TEAMS = "teams"
    JIRA = "jira"
    SPLUNK = "splunk"
    ELASTIC = "elastic"
    SIEM = "siem"
    TICKETING = "ticketing"
    CUSTOM = "custom"

class EventTrigger(Enum):
    """Events that can trigger integrations"""
    SCAN_STARTED = "scan_started"
    SCAN_COMPLETED = "scan_completed"
    VULNERABILITY_FOUND = "vulnerability_found"
    HIGH_SEVERITY_ALERT = "high_severity_alert"
    CAMPAIGN_CREATED = "campaign_created"
    REPORT_GENERATED = "report_generated"
    USER_LOGIN = "user_login"
    QUOTA_EXCEEDED = "quota_exceeded"
    SYSTEM_ALERT = "system_alert"
    COMPLIANCE_VIOLATION = "compliance_violation"
    THREAT_DETECTED = "threat_detected"
    CUSTOM_EVENT = "custom_event"

class AuthenticationType(Enum):
    """Authentication methods for integrations"""
    NONE = "none"
    API_KEY = "api_key"
    BEARER_TOKEN = "bearer_token"
    BASIC_AUTH = "basic_auth"
    OAUTH2 = "oauth2"
    JWT = "jwt"
    HMAC = "hmac"
    CUSTOM = "custom"

class IntegrationStatus(Enum):
    """Integration status levels"""
    ACTIVE = "active"
    INACTIVE = "inactive"
    ERROR = "error"
    TESTING = "testing"
    DISABLED = "disabled"

@dataclass
class AuthenticationConfig:
    """Authentication configuration for integrations"""
    auth_type: AuthenticationType
    credentials: Dict[str, str] = field(default_factory=dict)
    headers: Dict[str, str] = field(default_factory=dict)
    oauth_config: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary (exclude sensitive data)"""
        return {
            'auth_type': self.auth_type.value,
            'headers': self.headers,
            'oauth_config': {k: v for k, v in self.oauth_config.items() if 'secret' not in k.lower()}
        }

@dataclass
class WebhookConfig:
    """Webhook configuration"""
    url: str
    method: str = "POST"
    headers: Dict[str, str] = field(default_factory=dict)
    secret: Optional[str] = None
    timeout: int = 30
    retry_count: int = 3
    retry_delay: int = 5
    verify_ssl: bool = True
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary (exclude secret)"""
        result = asdict(self)
        if 'secret' in result:
            result['secret'] = '***' if self.secret else None
        return result

@dataclass
class IntegrationEndpoint:
    """Integration endpoint definition"""
    endpoint_id: str
    name: str
    integration_type: IntegrationType
    url: str
    authentication: AuthenticationConfig
    webhook_config: Optional[WebhookConfig] = None
    enabled: bool = True
    tenant_id: Optional[str] = None
    created_at: datetime = field(default_factory=datetime.now)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        result = asdict(self)
        result['integration_type'] = self.integration_type.value
        result['created_at'] = self.created_at.isoformat()
        result['authentication'] = self.authentication.to_dict()
        if self.webhook_config:
            result['webhook_config'] = self.webhook_config.to_dict()
        return result

@dataclass
class IntegrationRule:
    """Rule defining when and how to trigger integrations"""
    rule_id: str
    name: str
    trigger_events: List[EventTrigger]
    endpoint_ids: List[str]
    conditions: Dict[str, Any] = field(default_factory=dict)
    payload_template: Optional[str] = None
    enabled: bool = True
    tenant_id: Optional[str] = None
    created_at: datetime = field(default_factory=datetime.now)
    
    def matches_event(self, event_type: EventTrigger, event_data: Dict[str, Any]) -> bool:
        """Check if rule matches given event"""
        if event_type not in self.trigger_events:
            return False
        
        # Check conditions
        for key, expected_value in self.conditions.items():
            if key not in event_data:
                return False
            
            actual_value = event_data[key]
            
            # Support different condition types
            if isinstance(expected_value, dict):
                if 'operator' in expected_value:
                    operator = expected_value['operator']
                    value = expected_value['value']
                    
                    if operator == 'equals' and actual_value != value:
                        return False
                    elif operator == 'greater_than' and actual_value <= value:
                        return False
                    elif operator == 'less_than' and actual_value >= value:
                        return False
                    elif operator == 'contains' and value not in str(actual_value):
                        return False
                    elif operator == 'regex':
                        import re
                        if not re.match(value, str(actual_value)):
                            return False
            else:
                # Simple equality check
                if actual_value != expected_value:
                    return False
        
        return True
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        result = asdict(self)
        result['trigger_events'] = [e.value for e in self.trigger_events]
        result['created_at'] = self.created_at.isoformat()
        return result

@dataclass
class IntegrationExecution:
    """Record of integration execution"""
    execution_id: str
    rule_id: str
    endpoint_id: str
    event_type: EventTrigger
    triggered_at: datetime
    completed_at: Optional[datetime] = None
    status: str = "pending"
    response_code: Optional[int] = None
    response_body: Optional[str] = None
    error_message: Optional[str] = None
    retry_count: int = 0
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        result = asdict(self)
        result['event_type'] = self.event_type.value
        result['triggered_at'] = self.triggered_at.isoformat()
        result['completed_at'] = self.completed_at.isoformat() if self.completed_at else None
        return result

class IntegrationDatabase:
    """Database manager for integrations"""
    
    def __init__(self, db_path: str = "data/integrations.db"):
        self.db_path = db_path
        self._lock = threading.RLock()
        self._initialize_database()
    
    def _initialize_database(self):
        """Initialize integration database schema"""
        Path(self.db_path).parent.mkdir(parents=True, exist_ok=True)
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Endpoints table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS integration_endpoints (
                endpoint_id TEXT PRIMARY KEY,
                name TEXT NOT NULL,
                integration_type TEXT NOT NULL,
                url TEXT NOT NULL,
                authentication TEXT NOT NULL,
                webhook_config TEXT,
                enabled BOOLEAN DEFAULT 1,
                tenant_id TEXT,
                created_at TEXT NOT NULL,
                metadata TEXT
            )
        """)
        
        # Rules table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS integration_rules (
                rule_id TEXT PRIMARY KEY,
                name TEXT NOT NULL,
                trigger_events TEXT NOT NULL,
                endpoint_ids TEXT NOT NULL,
                conditions TEXT,
                payload_template TEXT,
                enabled BOOLEAN DEFAULT 1,
                tenant_id TEXT,
                created_at TEXT NOT NULL
            )
        """)
        
        # Executions table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS integration_executions (
                execution_id TEXT PRIMARY KEY,
                rule_id TEXT NOT NULL,
                endpoint_id TEXT NOT NULL,
                event_type TEXT NOT NULL,
                triggered_at TEXT NOT NULL,
                completed_at TEXT,
                status TEXT DEFAULT 'pending',
                response_code INTEGER,
                response_body TEXT,
                error_message TEXT,
                retry_count INTEGER DEFAULT 0,
                FOREIGN KEY (rule_id) REFERENCES integration_rules (rule_id),
                FOREIGN KEY (endpoint_id) REFERENCES integration_endpoints (endpoint_id)
            )
        """)
        
        # API keys table for third-party integrations
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS api_keys (
                key_id TEXT PRIMARY KEY,
                key_name TEXT NOT NULL,
                key_value TEXT NOT NULL,
                tenant_id TEXT,
                integration_type TEXT,
                created_at TEXT NOT NULL,
                expires_at TEXT,
                last_used TEXT
            )
        """)
        
        # Create indexes
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_endpoints_tenant ON integration_endpoints(tenant_id)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_endpoints_type ON integration_endpoints(integration_type)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_rules_tenant ON integration_rules(tenant_id)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_executions_rule ON integration_executions(rule_id)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_executions_status ON integration_executions(status)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_executions_triggered ON integration_executions(triggered_at)")
        
        conn.commit()
        conn.close()
    
    def create_endpoint(self, endpoint: IntegrationEndpoint) -> bool:
        """Create integration endpoint"""
        with self._lock:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            try:
                cursor.execute("""
                    INSERT INTO integration_endpoints 
                    (endpoint_id, name, integration_type, url, authentication, webhook_config,
                     enabled, tenant_id, created_at, metadata)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    endpoint.endpoint_id,
                    endpoint.name,
                    endpoint.integration_type.value,
                    endpoint.url,
                    json.dumps(endpoint.authentication.to_dict()),
                    json.dumps(endpoint.webhook_config.to_dict()) if endpoint.webhook_config else None,
                    endpoint.enabled,
                    endpoint.tenant_id,
                    endpoint.created_at.isoformat(),
                    json.dumps(endpoint.metadata)
                ))
                
                conn.commit()
                return True
                
            except sqlite3.IntegrityError:
                return False
            finally:
                conn.close()
    
    def get_endpoint(self, endpoint_id: str) -> Optional[IntegrationEndpoint]:
        """Get integration endpoint by ID"""
        with self._lock:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT endpoint_id, name, integration_type, url, authentication, webhook_config,
                       enabled, tenant_id, created_at, metadata
                FROM integration_endpoints WHERE endpoint_id = ?
            """, (endpoint_id,))
            
            row = cursor.fetchone()
            conn.close()
            
            if row:
                auth_data = json.loads(row[4])
                auth_config = AuthenticationConfig(
                    auth_type=AuthenticationType(auth_data['auth_type']),
                    headers=auth_data.get('headers', {}),
                    oauth_config=auth_data.get('oauth_config', {})
                )
                
                webhook_config = None
                if row[5]:
                    webhook_data = json.loads(row[5])
                    webhook_config = WebhookConfig(**webhook_data)
                
                return IntegrationEndpoint(
                    endpoint_id=row[0],
                    name=row[1],
                    integration_type=IntegrationType(row[2]),
                    url=row[3],
                    authentication=auth_config,
                    webhook_config=webhook_config,
                    enabled=bool(row[6]),
                    tenant_id=row[7],
                    created_at=datetime.fromisoformat(row[8]),
                    metadata=json.loads(row[9]) if row[9] else {}
                )
            
            return None
    
    def create_rule(self, rule: IntegrationRule) -> bool:
        """Create integration rule"""
        with self._lock:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            try:
                cursor.execute("""
                    INSERT INTO integration_rules 
                    (rule_id, name, trigger_events, endpoint_ids, conditions, payload_template,
                     enabled, tenant_id, created_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    rule.rule_id,
                    rule.name,
                    json.dumps([e.value for e in rule.trigger_events]),
                    json.dumps(rule.endpoint_ids),
                    json.dumps(rule.conditions),
                    rule.payload_template,
                    rule.enabled,
                    rule.tenant_id,
                    rule.created_at.isoformat()
                ))
                
                conn.commit()
                return True
                
            except sqlite3.IntegrityError:
                return False
            finally:
                conn.close()
    
    def get_rules_for_event(self, event_type: EventTrigger, tenant_id: Optional[str] = None) -> List[IntegrationRule]:
        """Get rules that should trigger for given event"""
        with self._lock:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            query = """
                SELECT rule_id, name, trigger_events, endpoint_ids, conditions, payload_template,
                       enabled, tenant_id, created_at
                FROM integration_rules WHERE enabled = 1
            """
            params = []
            
            if tenant_id:
                query += " AND (tenant_id = ? OR tenant_id IS NULL)"
                params.append(tenant_id)
            
            cursor.execute(query, params)
            rows = cursor.fetchall()
            conn.close()
            
            matching_rules = []
            for row in rows:
                trigger_events = [EventTrigger(e) for e in json.loads(row[2])]
                
                if event_type in trigger_events:
                    rule = IntegrationRule(
                        rule_id=row[0],
                        name=row[1],
                        trigger_events=trigger_events,
                        endpoint_ids=json.loads(row[3]),
                        conditions=json.loads(row[4]) if row[4] else {},
                        payload_template=row[5],
                        enabled=bool(row[6]),
                        tenant_id=row[7],
                        created_at=datetime.fromisoformat(row[8])
                    )
                    matching_rules.append(rule)
            
            return matching_rules
    
    def record_execution(self, execution: IntegrationExecution) -> bool:
        """Record integration execution"""
        with self._lock:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            try:
                cursor.execute("""
                    INSERT INTO integration_executions 
                    (execution_id, rule_id, endpoint_id, event_type, triggered_at, completed_at,
                     status, response_code, response_body, error_message, retry_count)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    execution.execution_id,
                    execution.rule_id,
                    execution.endpoint_id,
                    execution.event_type.value,
                    execution.triggered_at.isoformat(),
                    execution.completed_at.isoformat() if execution.completed_at else None,
                    execution.status,
                    execution.response_code,
                    execution.response_body,
                    execution.error_message,
                    execution.retry_count
                ))
                
                conn.commit()
                return True
                
            except Exception:
                return False
            finally:
                conn.close()

class WebhookExecutor:
    """Executes webhook integrations"""
    
    def __init__(self):
        self.session: Optional[aiohttp.ClientSession] = None
    
    async def initialize(self):
        """Initialize HTTP session"""
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=60),
            headers={'User-Agent': 'NexusScan-Integration/1.0'}
        )
    
    async def cleanup(self):
        """Cleanup HTTP session"""
        if self.session:
            await self.session.close()
    
    async def execute_webhook(self, endpoint: IntegrationEndpoint, 
                            payload: Dict[str, Any],
                            event_type: EventTrigger) -> IntegrationExecution:
        """Execute webhook call"""
        execution_id = str(uuid.uuid4())
        execution = IntegrationExecution(
            execution_id=execution_id,
            rule_id="webhook_direct",
            endpoint_id=endpoint.endpoint_id,
            event_type=event_type,
            triggered_at=datetime.now()
        )
        
        if not self.session:
            await self.initialize()
        
        try:
            # Prepare headers
            headers = {}
            if endpoint.webhook_config:
                headers.update(endpoint.webhook_config.headers)
            
            # Add authentication headers
            auth_headers = self._prepare_auth_headers(endpoint.authentication, payload)
            headers.update(auth_headers)
            
            # Add webhook signature if secret is configured
            if endpoint.webhook_config and endpoint.webhook_config.secret:
                signature = self._generate_webhook_signature(
                    endpoint.webhook_config.secret,
                    json.dumps(payload)
                )
                headers['X-Webhook-Signature'] = signature
            
            # Make request
            method = endpoint.webhook_config.method if endpoint.webhook_config else "POST"
            timeout = aiohttp.ClientTimeout(
                total=endpoint.webhook_config.timeout if endpoint.webhook_config else 30
            )
            
            async with self.session.request(
                method=method,
                url=endpoint.url,
                json=payload,
                headers=headers,
                timeout=timeout,
                ssl=endpoint.webhook_config.verify_ssl if endpoint.webhook_config else True
            ) as response:
                execution.response_code = response.status
                execution.response_body = await response.text()
                
                if 200 <= response.status < 300:
                    execution.status = "success"
                else:
                    execution.status = "failed"
                    execution.error_message = f"HTTP {response.status}: {execution.response_body[:500]}"
        
        except Exception as e:
            execution.status = "error"
            execution.error_message = str(e)
            logger.error(f"Webhook execution failed: {e}")
        
        execution.completed_at = datetime.now()
        return execution
    
    def _prepare_auth_headers(self, auth_config: AuthenticationConfig, 
                            payload: Dict[str, Any]) -> Dict[str, str]:
        """Prepare authentication headers"""
        headers = {}
        
        if auth_config.auth_type == AuthenticationType.API_KEY:
            api_key = auth_config.credentials.get('api_key')
            header_name = auth_config.credentials.get('header_name', 'X-API-Key')
            if api_key:
                headers[header_name] = api_key
        
        elif auth_config.auth_type == AuthenticationType.BEARER_TOKEN:
            token = auth_config.credentials.get('token')
            if token:
                headers['Authorization'] = f'Bearer {token}'
        
        elif auth_config.auth_type == AuthenticationType.BASIC_AUTH:
            username = auth_config.credentials.get('username')
            password = auth_config.credentials.get('password')
            if username and password:
                credentials = base64.b64encode(f"{username}:{password}".encode()).decode()
                headers['Authorization'] = f'Basic {credentials}'
        
        # Add custom headers
        headers.update(auth_config.headers)
        
        return headers
    
    def _generate_webhook_signature(self, secret: str, payload: str) -> str:
        """Generate HMAC signature for webhook"""
        signature = hmac.new(
            secret.encode(),
            payload.encode(),
            hashlib.sha256
        ).hexdigest()
        return f"sha256={signature}"

class ThirdPartyConnector:
    """Connector for popular third-party services"""
    
    def __init__(self):
        self.connectors = {
            IntegrationType.SLACK: self._slack_connector,
            IntegrationType.TEAMS: self._teams_connector,
            IntegrationType.JIRA: self._jira_connector,
            IntegrationType.SPLUNK: self._splunk_connector,
            IntegrationType.ELASTIC: self._elastic_connector
        }
    
    async def send_to_service(self, integration_type: IntegrationType,
                            endpoint: IntegrationEndpoint,
                            event_data: Dict[str, Any]) -> IntegrationExecution:
        """Send data to third-party service"""
        connector_func = self.connectors.get(integration_type)
        
        if connector_func:
            return await connector_func(endpoint, event_data)
        else:
            # Fallback to generic webhook
            webhook_executor = WebhookExecutor()
            return await webhook_executor.execute_webhook(
                endpoint, event_data, EventTrigger.CUSTOM_EVENT
            )
    
    async def _slack_connector(self, endpoint: IntegrationEndpoint, 
                             event_data: Dict[str, Any]) -> IntegrationExecution:
        """Slack integration connector"""
        # Transform data for Slack format
        slack_payload = {
            "text": f"NexusScan Alert: {event_data.get('event_type', 'Unknown Event')}",
            "blocks": [
                {
                    "type": "section",
                    "text": {
                        "type": "mrkdwn",
                        "text": f"*Event:* {event_data.get('event_type', 'Unknown')}\n*Time:* {event_data.get('timestamp', 'Unknown')}"
                    }
                }
            ]
        }
        
        # Add fields based on event type
        if 'vulnerability' in event_data:
            vuln = event_data['vulnerability']
            slack_payload["blocks"].append({
                "type": "section",
                "fields": [
                    {"type": "mrkdwn", "text": f"*Severity:* {vuln.get('severity', 'Unknown')}"},
                    {"type": "mrkdwn", "text": f"*Target:* {vuln.get('target', 'Unknown')}"},
                    {"type": "mrkdwn", "text": f"*CVE:* {vuln.get('cve', 'N/A')}"},
                    {"type": "mrkdwn", "text": f"*Score:* {vuln.get('cvss_score', 'N/A')}"}
                ]
            })
        
        webhook_executor = WebhookExecutor()
        return await webhook_executor.execute_webhook(
            endpoint, slack_payload, EventTrigger.CUSTOM_EVENT
        )
    
    async def _teams_connector(self, endpoint: IntegrationEndpoint,
                             event_data: Dict[str, Any]) -> IntegrationExecution:
        """Microsoft Teams integration connector"""
        # Transform data for Teams format
        teams_payload = {
            "@type": "MessageCard",
            "@context": "http://schema.org/extensions",
            "themeColor": "FF5722" if event_data.get('severity') == 'high' else "2196F3",
            "summary": f"NexusScan Alert: {event_data.get('event_type', 'Unknown Event')}",
            "sections": [
                {
                    "activityTitle": "NexusScan Security Alert",
                    "activitySubtitle": event_data.get('event_type', 'Unknown Event'),
                    "facts": [
                        {"name": "Event", "value": event_data.get('event_type', 'Unknown')},
                        {"name": "Time", "value": event_data.get('timestamp', 'Unknown')},
                        {"name": "Tenant", "value": event_data.get('tenant_id', 'System')}
                    ]
                }
            ]
        }
        
        webhook_executor = WebhookExecutor()
        return await webhook_executor.execute_webhook(
            endpoint, teams_payload, EventTrigger.CUSTOM_EVENT
        )
    
    async def _jira_connector(self, endpoint: IntegrationEndpoint,
                            event_data: Dict[str, Any]) -> IntegrationExecution:
        """JIRA integration connector"""
        # Create JIRA issue format
        jira_payload = {
            "fields": {
                "project": {"key": endpoint.metadata.get('project_key', 'SEC')},
                "summary": f"Security Alert: {event_data.get('event_type', 'Unknown')}",
                "description": f"Automated security alert from NexusScan\n\nEvent: {event_data.get('event_type')}\nTime: {event_data.get('timestamp')}\nDetails: {json.dumps(event_data, indent=2)}",
                "issuetype": {"name": endpoint.metadata.get('issue_type', 'Bug')},
                "priority": {"name": self._map_severity_to_jira_priority(event_data.get('severity', 'medium'))}
            }
        }
        
        webhook_executor = WebhookExecutor()
        return await webhook_executor.execute_webhook(
            endpoint, jira_payload, EventTrigger.CUSTOM_EVENT
        )
    
    async def _splunk_connector(self, endpoint: IntegrationEndpoint,
                              event_data: Dict[str, Any]) -> IntegrationExecution:
        """Splunk integration connector"""
        # Format for Splunk HEC (HTTP Event Collector)
        splunk_payload = {
            "time": int(datetime.now().timestamp()),
            "source": "nexusscan",
            "sourcetype": "security:alert",
            "index": endpoint.metadata.get('index', 'security'),
            "event": event_data
        }
        
        webhook_executor = WebhookExecutor()
        return await webhook_executor.execute_webhook(
            endpoint, splunk_payload, EventTrigger.CUSTOM_EVENT
        )
    
    async def _elastic_connector(self, endpoint: IntegrationEndpoint,
                               event_data: Dict[str, Any]) -> IntegrationExecution:
        """Elasticsearch integration connector"""
        # Format for Elasticsearch document
        elastic_payload = {
            "@timestamp": datetime.now().isoformat(),
            "source": "nexusscan",
            "event_type": event_data.get('event_type'),
            "tenant_id": event_data.get('tenant_id'),
            "data": event_data
        }
        
        webhook_executor = WebhookExecutor()
        return await webhook_executor.execute_webhook(
            endpoint, elastic_payload, EventTrigger.CUSTOM_EVENT
        )
    
    def _map_severity_to_jira_priority(self, severity: str) -> str:
        """Map security severity to JIRA priority"""
        mapping = {
            'critical': 'Highest',
            'high': 'High',
            'medium': 'Medium',
            'low': 'Low',
            'info': 'Lowest'
        }
        return mapping.get(severity.lower(), 'Medium')

class IntegrationEcosystem:
    """Main integration ecosystem manager"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        self.config = config or {}
        self.db = IntegrationDatabase(self.config.get('db_path', 'data/integrations.db'))
        self.webhook_executor = WebhookExecutor()
        self.third_party_connector = ThirdPartyConnector()
        self.ai_orchestrator = get_ai_orchestrator()
        
        # Execution queue and metrics
        self.execution_queue: asyncio.Queue = asyncio.Queue()
        self.execution_workers: List[asyncio.Task] = []
        self.metrics = {
            'total_executions': 0,
            'successful_executions': 0,
            'failed_executions': 0,
            'average_response_time': 0.0,
            'active_integrations': 0
        }
        
        # Rate limiting
        self.rate_limits: Dict[str, List[float]] = {}
        self.rate_limit_window = 60  # 1 minute
        
        # Initialize components
        self._initialize_workers()
    
    def _initialize_workers(self):
        """Initialize background workers"""
        # Start webhook executor
        asyncio.create_task(self.webhook_executor.initialize())
        
        # Start execution workers
        worker_count = self.config.get('worker_count', 3)
        for i in range(worker_count):
            worker = asyncio.create_task(self._execution_worker(f"worker_{i}"))
            self.execution_workers.append(worker)
    
    async def _execution_worker(self, worker_id: str):
        """Background worker for processing integrations"""
        logger.info(f"Integration worker {worker_id} started")
        
        while True:
            try:
                # Get execution task from queue
                execution_task = await self.execution_queue.get()
                
                if execution_task is None:  # Shutdown signal
                    break
                
                # Execute the integration
                await self._process_execution_task(execution_task)
                
                # Mark task as done
                self.execution_queue.task_done()
                
            except Exception as e:
                logger.error(f"Worker {worker_id} error: {e}")
                await asyncio.sleep(1)
        
        logger.info(f"Integration worker {worker_id} stopped")
    
    async def _process_execution_task(self, task: Dict[str, Any]):
        """Process individual execution task"""
        try:
            endpoint = task['endpoint']
            event_data = task['event_data']
            event_type = task['event_type']
            
            start_time = time.time()
            
            # Check rate limiting
            if not self._check_rate_limit(endpoint.endpoint_id):
                logger.warning(f"Rate limit exceeded for endpoint {endpoint.endpoint_id}")
                return
            
            # Execute based on integration type
            if endpoint.integration_type == IntegrationType.WEBHOOK:
                execution = await self.webhook_executor.execute_webhook(
                    endpoint, event_data, event_type
                )
            else:
                execution = await self.third_party_connector.send_to_service(
                    endpoint.integration_type, endpoint, event_data
                )
            
            # Record execution
            self.db.record_execution(execution)
            
            # Update metrics
            execution_time = time.time() - start_time
            self._update_metrics(execution, execution_time)
            
        except Exception as e:
            logger.error(f"Execution task failed: {e}")
    
    def _check_rate_limit(self, endpoint_id: str) -> bool:
        """Check if endpoint is within rate limits"""
        current_time = time.time()
        
        if endpoint_id not in self.rate_limits:
            self.rate_limits[endpoint_id] = []
        
        # Clean old entries
        cutoff_time = current_time - self.rate_limit_window
        self.rate_limits[endpoint_id] = [
            t for t in self.rate_limits[endpoint_id] if t > cutoff_time
        ]
        
        # Check limit (default 60 requests per minute)
        limit = self.config.get('rate_limit_per_minute', 60)
        if len(self.rate_limits[endpoint_id]) >= limit:
            return False
        
        # Add current request
        self.rate_limits[endpoint_id].append(current_time)
        return True
    
    def _update_metrics(self, execution: IntegrationExecution, execution_time: float):
        """Update execution metrics"""
        self.metrics['total_executions'] += 1
        
        if execution.status == 'success':
            self.metrics['successful_executions'] += 1
        else:
            self.metrics['failed_executions'] += 1
        
        # Update average response time
        total = self.metrics['total_executions']
        current_avg = self.metrics['average_response_time']
        self.metrics['average_response_time'] = ((current_avg * (total - 1)) + execution_time) / total
    
    async def create_integration_endpoint(self, name: str, integration_type: IntegrationType,
                                        url: str, authentication: AuthenticationConfig,
                                        webhook_config: Optional[WebhookConfig] = None,
                                        tenant_id: Optional[str] = None,
                                        metadata: Optional[Dict[str, Any]] = None) -> IntegrationEndpoint:
        """Create new integration endpoint"""
        endpoint_id = str(uuid.uuid4())
        
        endpoint = IntegrationEndpoint(
            endpoint_id=endpoint_id,
            name=name,
            integration_type=integration_type,
            url=url,
            authentication=authentication,
            webhook_config=webhook_config,
            tenant_id=tenant_id,
            metadata=metadata or {}
        )
        
        success = self.db.create_endpoint(endpoint)
        if not success:
            raise RuntimeError(f"Failed to create integration endpoint: {name}")
        
        self.metrics['active_integrations'] += 1
        logger.info(f"Created integration endpoint: {name} ({endpoint_id})")
        return endpoint
    
    async def create_integration_rule(self, name: str, trigger_events: List[EventTrigger],
                                    endpoint_ids: List[str],
                                    conditions: Optional[Dict[str, Any]] = None,
                                    payload_template: Optional[str] = None,
                                    tenant_id: Optional[str] = None) -> IntegrationRule:
        """Create new integration rule"""
        rule_id = str(uuid.uuid4())
        
        rule = IntegrationRule(
            rule_id=rule_id,
            name=name,
            trigger_events=trigger_events,
            endpoint_ids=endpoint_ids,
            conditions=conditions or {},
            payload_template=payload_template,
            tenant_id=tenant_id
        )
        
        success = self.db.create_rule(rule)
        if not success:
            raise RuntimeError(f"Failed to create integration rule: {name}")
        
        logger.info(f"Created integration rule: {name} ({rule_id})")
        return rule
    
    async def trigger_integrations(self, event_type: EventTrigger, 
                                 event_data: Dict[str, Any],
                                 tenant_id: Optional[str] = None):
        """Trigger integrations for given event"""
        # Get matching rules
        rules = self.db.get_rules_for_event(event_type, tenant_id)
        
        for rule in rules:
            if not rule.enabled:
                continue
            
            # Check if rule conditions match
            if not rule.matches_event(event_type, event_data):
                continue
            
            # Process each endpoint in the rule
            for endpoint_id in rule.endpoint_ids:
                endpoint = self.db.get_endpoint(endpoint_id)
                if not endpoint or not endpoint.enabled:
                    continue
                
                # Prepare payload
                payload = event_data.copy()
                if rule.payload_template:
                    payload = await self._apply_payload_template(rule.payload_template, payload)
                
                # Queue for execution
                task = {
                    'endpoint': endpoint,
                    'event_data': payload,
                    'event_type': event_type,
                    'rule_id': rule.rule_id
                }
                
                await self.execution_queue.put(task)
        
        logger.debug(f"Queued integrations for {event_type.value}: {len(rules)} rules processed")
    
    async def _apply_payload_template(self, template: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """Apply payload template transformation"""
        try:
            # Simple template substitution using AI if complex template
            if '{{' in template and '}}' in template:
                # Use AI for complex template processing
                ai_request = AIRequest(
                    task_type=TaskType.DATA_TRANSFORMATION,
                    prompt=f"Transform this data using the template:\nTemplate: {template}\nData: {json.dumps(data)}\nReturn only the JSON result.",
                    max_tokens=1000,
                    temperature=0.1
                )
                
                response = await self.ai_orchestrator.process_request(ai_request)
                return json.loads(response.content)
            else:
                # Simple JSON template
                return json.loads(template.format(**data))
                
        except Exception as e:
            logger.error(f"Template processing failed: {e}")
            return data
    
    async def test_integration(self, endpoint_id: str) -> IntegrationExecution:
        """Test integration endpoint"""
        endpoint = self.db.get_endpoint(endpoint_id)
        if not endpoint:
            raise ValueError(f"Endpoint not found: {endpoint_id}")
        
        # Prepare test payload
        test_payload = {
            "event_type": "test",
            "timestamp": datetime.now().isoformat(),
            "message": "This is a test integration from NexusScan",
            "test": True
        }
        
        # Execute test
        if endpoint.integration_type == IntegrationType.WEBHOOK:
            execution = await self.webhook_executor.execute_webhook(
                endpoint, test_payload, EventTrigger.CUSTOM_EVENT
            )
        else:
            execution = await self.third_party_connector.send_to_service(
                endpoint.integration_type, endpoint, test_payload
            )
        
        # Record test execution
        self.db.record_execution(execution)
        
        return execution
    
    def get_integration_metrics(self) -> Dict[str, Any]:
        """Get integration system metrics"""
        return {
            **self.metrics,
            'queue_size': self.execution_queue.qsize(),
            'active_workers': len([w for w in self.execution_workers if not w.done()]),
            'rate_limited_endpoints': len([k for k, v in self.rate_limits.items() if len(v) > 50])
        }
    
    async def shutdown(self):
        """Shutdown integration system"""
        # Stop workers
        for _ in self.execution_workers:
            await self.execution_queue.put(None)
        
        # Wait for workers to finish
        await asyncio.gather(*self.execution_workers, return_exceptions=True)
        
        # Cleanup resources
        await self.webhook_executor.cleanup()
        
        logger.info("Integration ecosystem shut down")

# Global integration ecosystem instance
integration_ecosystem: Optional[IntegrationEcosystem] = None

def get_integration_ecosystem() -> IntegrationEcosystem:
    """Get global integration ecosystem instance"""
    global integration_ecosystem
    
    if integration_ecosystem is None:
        integration_ecosystem = IntegrationEcosystem()
    
    return integration_ecosystem

def close_integration_ecosystem():
    """Close global integration ecosystem"""
    global integration_ecosystem
    integration_ecosystem = None

# Helper functions for common integrations
async def create_slack_integration(webhook_url: str, tenant_id: Optional[str] = None) -> IntegrationEndpoint:
    """Create Slack webhook integration"""
    ecosystem = get_integration_ecosystem()
    
    auth_config = AuthenticationConfig(auth_type=AuthenticationType.NONE)
    webhook_config = WebhookConfig(url=webhook_url)
    
    return await ecosystem.create_integration_endpoint(
        name="Slack Notifications",
        integration_type=IntegrationType.SLACK,
        url=webhook_url,
        authentication=auth_config,
        webhook_config=webhook_config,
        tenant_id=tenant_id
    )

async def create_jira_integration(base_url: str, username: str, api_token: str,
                                project_key: str, tenant_id: Optional[str] = None) -> IntegrationEndpoint:
    """Create JIRA REST API integration"""
    ecosystem = get_integration_ecosystem()
    
    auth_config = AuthenticationConfig(
        auth_type=AuthenticationType.BASIC_AUTH,
        credentials={'username': username, 'password': api_token}
    )
    
    return await ecosystem.create_integration_endpoint(
        name="JIRA Issue Tracking",
        integration_type=IntegrationType.JIRA,
        url=f"{base_url}/rest/api/2/issue",
        authentication=auth_config,
        tenant_id=tenant_id,
        metadata={'project_key': project_key, 'issue_type': 'Bug'}
    )

async def create_splunk_integration(hec_url: str, hec_token: str,
                                  index: str = "security", tenant_id: Optional[str] = None) -> IntegrationEndpoint:
    """Create Splunk HEC integration"""
    ecosystem = get_integration_ecosystem()
    
    auth_config = AuthenticationConfig(
        auth_type=AuthenticationType.BEARER_TOKEN,
        credentials={'token': hec_token}
    )
    
    return await ecosystem.create_integration_endpoint(
        name="Splunk Security Logs",
        integration_type=IntegrationType.SPLUNK,
        url=hec_url,
        authentication=auth_config,
        tenant_id=tenant_id,
        metadata={'index': index}
    )