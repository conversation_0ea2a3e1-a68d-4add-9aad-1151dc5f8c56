#!/usr/bin/env python3
"""
Real UI Functionality Test Script
Tests every single UI component for actual functionality, not just visual appearance
"""

import requests
import json
import time
import sys
from typing import Dict, List, Any

class UIFunctionalityTester:
    def __init__(self, backend_url: str = "http://localhost:8000"):
        self.backend_url = backend_url
        self.session = requests.Session()
        self.test_results = {}
        
    def test_backend_connectivity(self) -> bool:
        """Test if backend is actually responding"""
        try:
            response = self.session.get(f"{self.backend_url}/api/health", timeout=5)
            if response.status_code == 200:
                data = response.json()
                print(f"✅ Backend health: {data['data']['status']}")
                return True
            else:
                print(f"❌ Backend unhealthy: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ Backend connection failed: {e}")
            return False
    
    def test_campaigns_functionality(self) -> Dict[str, Any]:
        """Test all campaign-related UI functionality"""
        results = {
            "get_campaigns": False,
            "create_campaign": False, 
            "update_campaign": False,
            "delete_campaign": False
        }
        
        # Test GET campaigns
        try:
            response = self.session.get(f"{self.backend_url}/api/campaigns")
            if response.status_code == 200:
                campaigns = response.json()["data"]
                print(f"✅ GET Campaigns: {len(campaigns)} campaigns loaded")
                results["get_campaigns"] = True
            else:
                print(f"❌ GET Campaigns failed: {response.status_code}")
        except Exception as e:
            print(f"❌ GET Campaigns error: {e}")
        
        # Test CREATE campaign
        try:
            new_campaign = {
                "name": f"UI Test Campaign {int(time.time())}",
                "description": "Testing UI functionality for campaign creation",
                "targets": ["127.0.0.1", "localhost"],
                "priority": "medium"
            }
            response = self.session.post(f"{self.backend_url}/api/campaigns", 
                                       json=new_campaign)
            if response.status_code == 200:
                created = response.json()["data"]
                print(f"✅ CREATE Campaign: ID {created['id']} created")
                results["create_campaign"] = True
                self.test_campaign_id = created['id']
            else:
                print(f"❌ CREATE Campaign failed: {response.status_code}")
        except Exception as e:
            print(f"❌ CREATE Campaign error: {e}")
        
        # Test UPDATE campaign (if create succeeded)
        if results["create_campaign"]:
            try:
                update_data = {"description": "Updated via UI functionality test"}
                response = self.session.put(f"{self.backend_url}/api/campaigns/{self.test_campaign_id}",
                                          json=update_data)
                if response.status_code == 200:
                    print(f"✅ UPDATE Campaign: ID {self.test_campaign_id} updated")
                    results["update_campaign"] = True
                else:
                    print(f"❌ UPDATE Campaign failed: {response.status_code}")
            except Exception as e:
                print(f"❌ UPDATE Campaign error: {e}")
        
        return results
    
    def test_scans_functionality(self) -> Dict[str, Any]:
        """Test all scan-related UI functionality"""
        results = {
            "get_scans": False,
            "create_scan": False,
            "start_scan": False,
            "get_scan_results": False
        }
        
        # Test GET scans
        try:
            response = self.session.get(f"{self.backend_url}/api/scans")
            if response.status_code == 200:
                scans = response.json()["data"]
                print(f"✅ GET Scans: {len(scans)} scans loaded")
                results["get_scans"] = True
            else:
                print(f"❌ GET Scans failed: {response.status_code}")
        except Exception as e:
            print(f"❌ GET Scans error: {e}")
        
        # Test CREATE scan (need campaign first)
        if hasattr(self, 'test_campaign_id'):
            try:
                new_scan = {
                    "campaign_id": self.test_campaign_id,
                    "scan_type": "nmap",
                    "target": "127.0.0.1",
                    "priority": "medium"
                }
                response = self.session.post(f"{self.backend_url}/api/scans", json=new_scan)
                if response.status_code == 200:
                    created_scan = response.json()["data"]
                    print(f"✅ CREATE Scan: ID {created_scan['id']} created")
                    results["create_scan"] = True
                    self.test_scan_id = created_scan['id']
                else:
                    print(f"❌ CREATE Scan failed: {response.status_code}")
            except Exception as e:
                print(f"❌ CREATE Scan error: {e}")
        
        # Test START scan
        if results["create_scan"]:
            try:
                response = self.session.post(f"{self.backend_url}/api/scans/{self.test_scan_id}/start")
                if response.status_code == 200:
                    print(f"✅ START Scan: ID {self.test_scan_id} started")
                    results["start_scan"] = True
                    
                    # Wait a moment and check scan status
                    time.sleep(2)
                    status_response = self.session.get(f"{self.backend_url}/api/scans/{self.test_scan_id}")
                    if status_response.status_code == 200:
                        scan_data = status_response.json()["data"]
                        print(f"✅ GET Scan Results: Status = {scan_data['status']}, Progress = {scan_data.get('progress', 0)}%")
                        results["get_scan_results"] = True
                    else:
                        print(f"❌ GET Scan Results failed: {status_response.status_code}")
                    
                else:
                    print(f"❌ START Scan failed: {response.status_code}")
            except Exception as e:
                print(f"❌ START Scan error: {e}")
        
        return results
    
    def test_tools_functionality(self) -> Dict[str, Any]:
        """Test tools page functionality"""
        results = {
            "get_tools": False,
            "tool_availability": False
        }
        
        try:
            response = self.session.get(f"{self.backend_url}/api/tools")
            if response.status_code == 200:
                tools_data = response.json()
                tools = tools_data["tools"]
                print(f"✅ GET Tools: {len(tools)} tools loaded")
                results["get_tools"] = True
                
                # Check tool availability
                available_tools = [t for t in tools if t.get("available", False)]
                print(f"✅ Tool Availability: {len(available_tools)}/{len(tools)} tools available")
                results["tool_availability"] = len(available_tools) > 0
                
            else:
                print(f"❌ GET Tools failed: {response.status_code}")
        except Exception as e:
            print(f"❌ GET Tools error: {e}")
        
        return results
    
    def test_dashboard_functionality(self) -> Dict[str, Any]:
        """Test dashboard metrics and data loading"""
        results = {
            "get_metrics": False,
            "real_data": False
        }
        
        try:
            response = self.session.get(f"{self.backend_url}/api/dashboard/metrics")
            if response.status_code == 200:
                metrics = response.json()["data"]
                print(f"✅ GET Metrics: {metrics}")
                results["get_metrics"] = True
                
                # Check if we have real data (not just zeros)
                total_data = (metrics.get("total_campaigns", 0) + 
                            metrics.get("active_scans", 0) + 
                            metrics.get("total_vulnerabilities", 0))
                if total_data > 0:
                    print(f"✅ Real Data: Dashboard showing actual metrics")
                    results["real_data"] = True
                else:
                    print(f"⚠️ Mock Data: Dashboard showing empty/mock metrics")
                
            else:
                print(f"❌ GET Metrics failed: {response.status_code}")
        except Exception as e:
            print(f"❌ GET Metrics error: {e}")
        
        return results
    
    def test_ai_functionality(self) -> Dict[str, Any]:
        """Test AI services functionality"""
        results = {
            "ai_status": False,
            "ai_response": False
        }
        
        try:
            response = self.session.get(f"{self.backend_url}/api/ai/services/status")
            if response.status_code == 200:
                ai_status = response.json()
                print(f"✅ AI Status: {ai_status}")
                results["ai_status"] = True
                
                # Test AI generation (if available)
                if ai_status.get("success", False) and ai_status.get("data"):
                    try:
                        ai_request = {
                            "prompt": "Generate a brief vulnerability assessment summary",
                            "context": "UI functionality test"
                        }
                        ai_response = self.session.post(f"{self.backend_url}/api/ai/chat", 
                                                      json={"message": ai_request["prompt"], "context": ai_request["context"]}, timeout=10)
                        if ai_response.status_code == 200:
                            result = ai_response.json()
                            print(f"✅ AI Response: Generated {len(result.get('data', {}).get('message', ''))} chars")
                            results["ai_response"] = True
                        else:
                            print(f"⚠️ AI Response failed: {ai_response.status_code}")
                    except Exception as e:
                        print(f"⚠️ AI Response error: {e}")
                
            else:
                print(f"❌ AI Status failed: {response.status_code}")
        except Exception as e:
            print(f"❌ AI Status error: {e}")
        
        return results
    
    def test_reports_functionality(self) -> Dict[str, Any]:
        """Test reports generation functionality"""
        results = {
            "get_reports": False,
            "generate_report": False
        }
        
        try:
            response = self.session.get(f"{self.backend_url}/api/reports")
            if response.status_code == 200:
                reports = response.json().get("data", [])
                print(f"✅ GET Reports: {len(reports)} reports found")
                results["get_reports"] = True
            else:
                print(f"❌ GET Reports failed: {response.status_code}")
        except Exception as e:
            print(f"❌ GET Reports error: {e}")
        
        # Test report generation (if we have campaign and scan)
        if hasattr(self, 'test_campaign_id'):
            try:
                report_request = {
                    "name": f"UI Test Report {int(time.time())}",
                    "type": "technical",
                    "format": "json",
                    "campaign_id": self.test_campaign_id
                }
                response = self.session.post(f"{self.backend_url}/api/reports/generate", 
                                           json=report_request)
                if response.status_code == 200:
                    report = response.json()["data"]
                    print(f"✅ Generate Report: ID {report.get('id', 'N/A')} created")
                    results["generate_report"] = True
                else:
                    print(f"❌ Generate Report failed: {response.status_code}")
            except Exception as e:
                print(f"❌ Generate Report error: {e}")
        
        return results
    
    def run_comprehensive_test(self) -> Dict[str, Any]:
        """Run all UI functionality tests"""
        print("🔍 Starting Comprehensive UI Functionality Test")
        print("=" * 60)
        
        if not self.test_backend_connectivity():
            return {"error": "Backend not available"}
        
        print("\n🏗️ Testing Campaigns Functionality...")
        campaigns_results = self.test_campaigns_functionality()
        
        print("\n🔍 Testing Scans Functionality...")
        scans_results = self.test_scans_functionality()
        
        print("\n🛠️ Testing Tools Functionality...")
        tools_results = self.test_tools_functionality()
        
        print("\n📊 Testing Dashboard Functionality...")
        dashboard_results = self.test_dashboard_functionality()
        
        print("\n🤖 Testing AI Functionality...")
        ai_results = self.test_ai_functionality()
        
        print("\n📄 Testing Reports Functionality...")
        reports_results = self.test_reports_functionality()
        
        # Compile overall results
        all_results = {
            "campaigns": campaigns_results,
            "scans": scans_results,
            "tools": tools_results,
            "dashboard": dashboard_results,
            "ai": ai_results,
            "reports": reports_results
        }
        
        # Calculate success rate
        total_tests = sum(len(section.values()) for section in all_results.values())
        passed_tests = sum(sum(section.values()) for section in all_results.values())
        success_rate = (passed_tests / total_tests) * 100 if total_tests > 0 else 0
        
        print("\n" + "=" * 60)
        print(f"📊 OVERALL UI FUNCTIONALITY RESULTS:")
        print(f"✅ Passed: {passed_tests}/{total_tests} tests ({success_rate:.1f}%)")
        
        if success_rate >= 80:
            print(f"🎉 EXCELLENT: UI is fully functional for production!")
        elif success_rate >= 60:
            print(f"⚠️  GOOD: UI is mostly functional, minor issues to fix")
        else:
            print(f"❌ POOR: UI has significant functionality issues")
        
        return {
            "results": all_results,
            "summary": {
                "total_tests": total_tests,
                "passed_tests": passed_tests,
                "success_rate": success_rate
            }
        }

if __name__ == "__main__":
    tester = UIFunctionalityTester()
    results = tester.run_comprehensive_test()
    
    # Save results to file
    with open("/mnt/e/dev/nexusscan-desktop/ui_functionality_results.json", "w") as f:
        json.dump(results, f, indent=2, default=str)
    
    print(f"\n📁 Results saved to: ui_functionality_results.json")
    
    # Exit with proper code
    success_rate = results.get("summary", {}).get("success_rate", 0)
    sys.exit(0 if success_rate >= 80 else 1)