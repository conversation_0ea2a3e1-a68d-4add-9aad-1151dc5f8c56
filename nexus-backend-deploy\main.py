#!/usr/bin/env python3
"""
NexusScan Backend Application
AI-Powered Penetration Testing Platform

Backend services and core functionality.
"""

import sys
import os
import asyncio
import threading
import logging
from pathlib import Path

# Add src directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from core.config import Config
from core.database import DatabaseManager
from core.events import EventManager
from api.metrics_server import MetricsServer
from api.rest_server import RestAPIServer

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('nexusscan.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

class NexusScanApplication:
    """Main NexusScan Backend Application"""
    
    def __init__(self):
        self.config = None
        self.db_manager = None
        self.event_manager = None
        self.metrics_server = None
        self.rest_server = None
        
    def initialize_backend(self):
        """Initialize backend services"""
        logger.info("Initializing NexusScan backend services")
        
        # Initialize configuration
        self.config = Config()
        
        # Initialize database
        self.db_manager = DatabaseManager(self.config.get_database_path())
        self.db_manager.initialize_database()
        
        # Initialize event manager
        self.event_manager = EventManager()
        
        # Initialize metrics server with configured port
        self.metrics_server = MetricsServer(
            port=self.config.security.metrics_port, 
            config=self.config
        )
        
        # Initialize REST API server
        self.rest_server = RestAPIServer(config=self.config)
        
        logger.info("Backend services initialized successfully")
    
    async def start_backend_services(self):
        """Start backend services"""
        logger.info("Starting NexusScan Backend Services")
        
        # Initialize security tools
        try:
            from security.tools.unified_tool_manager_v2 import UnifiedToolManagerV2
            from security.tools.tool_loader import initialize_tools
            
            # Load all tools first
            loading_report = initialize_tools()
            logger.info(f"Loaded {loading_report['total_loaded']} tools, {loading_report['available_tools']} available")
            
            # Initialize security manager
            self.security_manager = UnifiedToolManagerV2()
            logger.info("Security tools manager initialized")
        except Exception as e:
            logger.error(f"Failed to initialize security tools: {e}")
        
        # Initialize AI services
        try:
            from ai.services import AIServiceManager
            self.ai_manager = AIServiceManager(self.config)
            logger.info("AI services manager initialized")
        except Exception as e:
            logger.error(f"Failed to initialize AI services: {e}")
        
        # Start servers
        await self.metrics_server.start()
        await self.rest_server.start()
        
        logger.info("Backend services are ready")
        logger.info("NexusScan backend is running - ready for frontend integration")
        logger.info("REST API available at: http://localhost:8000/api/")
        logger.info("WebSocket available at: ws://localhost:8000/ws")
    
    async def run(self):
        """Main application entry point"""
        try:
            # Initialize backend services
            self.initialize_backend()
            
            # Start backend services
            await self.start_backend_services()
            
            # Keep the application running
            logger.info("Backend services running. Press Ctrl+C to stop.")
            try:
                while True:
                    await asyncio.sleep(1)
            except KeyboardInterrupt:
                logger.info("Shutting down NexusScan backend...")
                if self.metrics_server:
                    await self.metrics_server.stop()
                if self.rest_server:
                    await self.rest_server.stop()
            
        except Exception as e:
            logger.error(f"Failed to start NexusScan Application: {e}")
            sys.exit(1)

def main():
    """Main entry point for NexusScan Backend Application"""
    app = NexusScanApplication()
    asyncio.run(app.run())

if __name__ == "__main__":
    main()