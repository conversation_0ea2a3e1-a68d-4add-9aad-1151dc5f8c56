/**
 * FFUF Scanner Component
 * Fast web fuzzer for comprehensive web application testing and fuzzing
 */
import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { 
  Zap,
  Play,
  Square,
  Download,
  Settings,
  Target,
  Globe,
  FileText,
  Terminal,
  Copy,
  RotateCcw,
  Clock,
  Filter,
  Eye,
  AlertTriangle,
  CheckCircle,
  TrendingUp,
  Activity,
  Lock,
  Search,
  List,
  BarChart3,
  Database,
  Server,
  Bug,
  Info,
  Hash,
  Shuffle,
  <PERSON>For<PERSON>,
  Timer,
  Layers,
  Code,
  Link
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useBackendStore } from '@/stores/backend-store';
import { apiClient, ToolExecution } from '@/services/api-client';
import { toast } from 'sonner';

/**
 * FFUF configuration interface
 */
interface FFUFConfig {
  url: string;
  mode: 'dir' | 'param' | 'vhost' | 'header' | 'extension' | 'custom';
  keyword: string;
  wordlist: string;
  customWordlist: string;
  extensions: string[];
  request: {
    method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH' | 'HEAD' | 'OPTIONS';
    headers: { [key: string]: string };
    data: string;
    cookies: string;
    userAgent: string;
  };
  authentication: {
    enabled: boolean;
    username: string;
    password: string;
    type: 'basic' | 'digest';
  };
  filters: {
    statusCodes: string[];
    excludeStatusCodes: string[];
    contentLength: {
      min: number;
      max: number;
    };
    excludeContentLength: string[];
    words: {
      min: number;
      max: number;
    };
    excludeWords: string[];
    lines: {
      min: number;
      max: number;
    };
    excludeLines: string[];
    regexp: string;
    excludeRegexp: string;
  };
  performance: {
    threads: number;
    delay: string;
    timeout: number;
    rate: number;
    maxTime: number;
  };
  output: {
    format: 'json' | 'ejson' | 'html' | 'md' | 'csv' | 'ecsv';
    file: string;
    verbose: boolean;
    colors: boolean;
    quiet: boolean;
  };
  proxy: {
    enabled: boolean;
    url: string;
    credentials: string;
  };
  recursion: {
    enabled: boolean;
    depth: number;
    strategy: 'all' | 'dir-only';
  };
  replay: {
    enabled: boolean;
    proxyUrl: string;
  };
  customArgs: string;
}

/**
 * FFUF fuzzing modes
 */
const FUZZING_MODES = {
  'dir': {
    name: 'Directory/File Fuzzing',
    description: 'Fuzz directories and files in URLs',
    icon: <Search className="h-4 w-4" />,
    placeholder: 'https://example.com/FUZZ',
    keyword: 'FUZZ'
  },
  'param': {
    name: 'Parameter Fuzzing',
    description: 'Fuzz GET or POST parameters',
    icon: <Code className="h-4 w-4" />,
    placeholder: 'https://example.com/?FUZZ=value',
    keyword: 'FUZZ'
  },
  'vhost': {
    name: 'Virtual Host Fuzzing',
    description: 'Fuzz virtual hosts via Host header',
    icon: <Globe className="h-4 w-4" />,
    placeholder: 'https://example.com/',
    keyword: 'FUZZ'
  },
  'header': {
    name: 'Header Fuzzing',
    description: 'Fuzz HTTP headers',
    icon: <Layers className="h-4 w-4" />,
    placeholder: 'https://example.com/',
    keyword: 'FUZZ'
  },
  'extension': {
    name: 'Extension Fuzzing',
    description: 'Fuzz file extensions',
    icon: <FileText className="h-4 w-4" />,
    placeholder: 'https://example.com/file.FUZZ',
    keyword: 'FUZZ'
  },
  'custom': {
    name: 'Custom Fuzzing',
    description: 'Custom fuzzing pattern',
    icon: <Hash className="h-4 w-4" />,
    placeholder: 'Custom URL pattern',
    keyword: 'FUZZ'
  }
};

/**
 * Common wordlists for FFUF
 */
const WORDLISTS = {
  'dirb_common': '/usr/share/dirb/wordlists/common.txt',
  'dirb_big': '/usr/share/dirb/wordlists/big.txt',
  'dirbuster_directory_2_3_medium': '/usr/share/dirbuster/wordlists/directory-list-2.3-medium.txt',
  'dirbuster_directory_2_3_small': '/usr/share/dirbuster/wordlists/directory-list-2.3-small.txt',
  'seclist_discovery_web_content': '/usr/share/seclists/Discovery/Web-Content/directory-list-2.3-medium.txt',
  'seclist_discovery_dns': '/usr/share/seclists/Discovery/DNS/subdomains-top1million-110000.txt',
  'seclist_parameters': '/usr/share/seclists/Discovery/Web-Content/burp-parameter-names.txt',
  'seclist_extensions': '/usr/share/seclists/Discovery/Web-Content/web-extensions.txt',
  'raft_directories': '/usr/share/seclists/Discovery/Web-Content/raft-medium-directories.txt',
  'raft_files': '/usr/share/seclists/Discovery/Web-Content/raft-medium-files.txt'
};

/**
 * HTTP methods for fuzzing
 */
const HTTP_METHODS = ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'HEAD', 'OPTIONS'];

/**
 * Output formats
 */
const OUTPUT_FORMATS = {
  'json': { name: 'JSON', description: 'Standard JSON output' },
  'ejson': { name: 'Extended JSON', description: 'JSON with additional metadata' },
  'html': { name: 'HTML', description: 'HTML report format' },
  'md': { name: 'Markdown', description: 'Markdown report format' },
  'csv': { name: 'CSV', description: 'Comma-separated values' },
  'ecsv': { name: 'Extended CSV', description: 'CSV with additional metadata' }
};

/**
 * Common status codes
 */
const STATUS_CODES = [
  '200', '201', '202', '204', '301', '302', '304', '307', '308',
  '400', '401', '403', '404', '405', '406', '429', '500', '502', '503'
];

export default function FFUFScanner() {
  const { connectionStatus } = useBackendStore();
  const [config, setConfig] = React.useState<FFUFConfig>({
    url: '',
    mode: 'dir',
    keyword: 'FUZZ',
    wordlist: 'dirb_common',
    customWordlist: '',
    extensions: [],
    request: {
      method: 'GET',
      headers: {},
      data: '',
      cookies: '',
      userAgent: 'ffuf/2.0.0'
    },
    authentication: {
      enabled: false,
      username: '',
      password: '',
      type: 'basic'
    },
    filters: {
      statusCodes: ['200', '204', '301', '302', '307', '401', '403', '405', '500'],
      excludeStatusCodes: [],
      contentLength: { min: 0, max: 0 },
      excludeContentLength: [],
      words: { min: 0, max: 0 },
      excludeWords: [],
      lines: { min: 0, max: 0 },
      excludeLines: [],
      regexp: '',
      excludeRegexp: ''
    },
    performance: {
      threads: 40,
      delay: '',
      timeout: 10,
      rate: 0,
      maxTime: 0
    },
    output: {
      format: 'json',
      file: '',
      verbose: false,
      colors: true,
      quiet: false
    },
    proxy: {
      enabled: false,
      url: '',
      credentials: ''
    },
    recursion: {
      enabled: false,
      depth: 1,
      strategy: 'all'
    },
    replay: {
      enabled: false,
      proxyUrl: ''
    },
    customArgs: ''
  });

  const [execution, setExecution] = React.useState<ToolExecution | null>(null);
  const [activeTab, setActiveTab] = React.useState('configure');

  /**
   * Handle configuration changes
   */
  const updateConfig = (path: string, value: any) => {
    setConfig(prev => {
      const keys = path.split('.');
      const newConfig = { ...prev };
      let current = newConfig;
      
      for (let i = 0; i < keys.length - 1; i++) {
        current[keys[i]] = { ...current[keys[i]] };
        current = current[keys[i]];
      }
      
      current[keys[keys.length - 1]] = value;
      return newConfig;
    });
  };

  /**
   * Handle preset configurations
   */
  const applyPreset = (preset: string) => {
    const presets = {
      'directory_discovery': {
        mode: 'dir',
        url: 'https://example.com/FUZZ',
        wordlist: 'dirb_common',
        filters: {
          ...config.filters,
          statusCodes: ['200', '301', '302', '403']
        },
        performance: { ...config.performance, threads: 20 }
      },
      'subdomain_enumeration': {
        mode: 'vhost',
        url: 'https://example.com/',
        wordlist: 'seclist_discovery_dns',
        filters: {
          ...config.filters,
          statusCodes: ['200', '301', '302']
        },
        performance: { ...config.performance, threads: 50 }
      },
      'parameter_fuzzing': {
        mode: 'param',
        url: 'https://example.com/?FUZZ=test',
        wordlist: 'seclist_parameters',
        filters: {
          ...config.filters,
          statusCodes: ['200', '301', '302', '400', '500']
        },
        performance: { ...config.performance, threads: 10 }
      },
      'extension_discovery': {
        mode: 'extension',
        url: 'https://example.com/index.FUZZ',
        wordlist: 'seclist_extensions',
        filters: {
          ...config.filters,
          statusCodes: ['200', '301', '302']
        },
        performance: { ...config.performance, threads: 30 }
      },
      'stealth_scan': {
        mode: 'dir',
        url: 'https://example.com/FUZZ',
        wordlist: 'dirb_common',
        performance: {
          threads: 5,
          delay: '500ms-1s',
          timeout: 30,
          rate: 0,
          maxTime: 0
        },
        filters: {
          ...config.filters,
          statusCodes: ['200', '301', '302']
        }
      },
      'aggressive_scan': {
        mode: 'dir',
        url: 'https://example.com/FUZZ',
        wordlist: 'dirbuster_directory_2_3_medium',
        performance: {
          threads: 100,
          delay: '',
          timeout: 5,
          rate: 0,
          maxTime: 0
        },
        filters: {
          ...config.filters,
          statusCodes: ['200', '204', '301', '302', '307', '401', '403', '405']
        }
      }
    };

    if (presets[preset]) {
      setConfig(prev => ({ ...prev, ...presets[preset] }));
    }
  };

  /**
   * Start FFUF scan
   */
  const startScan = async () => {
    if (!config.url.trim()) {
      toast.error('Please provide a target URL');
      return;
    }

    if (config.mode === 'vhost' && !config.url.includes('FUZZ') && !config.request.headers['Host']?.includes('FUZZ')) {
      updateConfig('request.headers.Host', 'FUZZ.example.com');
    }

    try {
      const execution = await apiClient.executeSecurityTool('ffuf', config);
      setExecution(execution);
      setActiveTab('output');
      toast.success('FFUF scan started successfully');
    } catch (error) {
      toast.error(`Failed to start FFUF scan: ${error.message}`);
    }
  };

  /**
   * Stop scan
   */
  const stopScan = async () => {
    if (execution) {
      try {
        await apiClient.stopToolExecution(execution.id);
        toast.success('FFUF scan stopped');
      } catch (error) {
        toast.error(`Failed to stop scan: ${error.message}`);
      }
    }
  };

  /**
   * Generate command line
   */
  const generateCommand = () => {
    let cmd = 'ffuf';
    
    // URL and keyword
    cmd += ` -u "${config.url}"`;
    
    if (config.mode === 'vhost' && config.request.headers['Host']) {
      cmd += ` -H "Host: ${config.request.headers['Host']}"`;
    }
    
    // Wordlist
    const wordlistPath = config.customWordlist || WORDLISTS[config.wordlist];
    cmd += ` -w "${wordlistPath}"`;
    
    // HTTP method
    if (config.request.method !== 'GET') {
      cmd += ` -X ${config.request.method}`;
    }
    
    // POST data
    if (config.request.data) {
      cmd += ` -d "${config.request.data}"`;
    }
    
    // Headers
    Object.entries(config.request.headers).forEach(([key, value]) => {
      if (key !== 'Host' || config.mode !== 'vhost') {
        cmd += ` -H "${key}: ${value}"`;
      }
    });
    
    // Cookies
    if (config.request.cookies) {
      cmd += ` -b "${config.request.cookies}"`;
    }
    
    // User Agent
    if (config.request.userAgent !== 'ffuf/2.0.0') {
      cmd += ` -H "User-Agent: ${config.request.userAgent}"`;
    }
    
    // Authentication
    if (config.authentication.enabled) {
      cmd += ` -H "Authorization: Basic $(echo -n '${config.authentication.username}:${config.authentication.password}' | base64)"`;
    }
    
    // Status code filters
    if (config.filters.statusCodes.length > 0) {
      cmd += ` -mc ${config.filters.statusCodes.join(',')}`;
    }
    
    if (config.filters.excludeStatusCodes.length > 0) {
      cmd += ` -fc ${config.filters.excludeStatusCodes.join(',')}`;
    }
    
    // Size filters
    if (config.filters.excludeContentLength.length > 0) {
      cmd += ` -fs ${config.filters.excludeContentLength.join(',')}`;
    }
    
    if (config.filters.excludeWords.length > 0) {
      cmd += ` -fw ${config.filters.excludeWords.join(',')}`;
    }
    
    if (config.filters.excludeLines.length > 0) {
      cmd += ` -fl ${config.filters.excludeLines.join(',')}`;
    }
    
    // Regex filters
    if (config.filters.regexp) {
      cmd += ` -mr "${config.filters.regexp}"`;
    }
    
    if (config.filters.excludeRegexp) {
      cmd += ` -fr "${config.filters.excludeRegexp}"`;
    }
    
    // Performance settings
    cmd += ` -t ${config.performance.threads}`;
    
    if (config.performance.delay) {
      cmd += ` -p ${config.performance.delay}`;
    }
    
    if (config.performance.timeout !== 10) {
      cmd += ` -timeout ${config.performance.timeout}`;
    }
    
    if (config.performance.rate > 0) {
      cmd += ` -rate ${config.performance.rate}`;
    }
    
    if (config.performance.maxTime > 0) {
      cmd += ` -maxtime ${config.performance.maxTime}`;
    }
    
    // Output format
    if (config.output.format !== 'json') {
      cmd += ` -of ${config.output.format}`;
    }
    
    if (config.output.file) {
      cmd += ` -o "${config.output.file}"`;
    }
    
    // Verbosity
    if (config.output.verbose) {
      cmd += ' -v';
    }
    
    if (config.output.quiet) {
      cmd += ' -s';
    }
    
    if (!config.output.colors) {
      cmd += ' -nc';
    }
    
    // Proxy
    if (config.proxy.enabled && config.proxy.url) {
      cmd += ` -x "${config.proxy.url}"`;
    }
    
    // Recursion
    if (config.recursion.enabled) {
      cmd += ` -recursion -recursion-depth ${config.recursion.depth}`;
      if (config.recursion.strategy === 'dir-only') {
        cmd += ' -recursion-strategy';
      }
    }
    
    // Replay proxy
    if (config.replay.enabled && config.replay.proxyUrl) {
      cmd += ` -replay-proxy "${config.replay.proxyUrl}"`;
    }
    
    // Custom arguments
    if (config.customArgs) {
      cmd += ` ${config.customArgs}`;
    }
    
    return cmd;
  };

  const isScanning = execution?.status === 'running';
  const canStart = connectionStatus === 'connected' && config.url.trim() && !isScanning;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center gap-3">
        <div className="flex items-center justify-center w-10 h-10 rounded-lg bg-gradient-to-br from-yellow-500 to-orange-600">
          <Zap className="h-5 w-5 text-white" />
        </div>
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100">FFUF Scanner</h2>
          <p className="text-gray-600 dark:text-gray-400">Fast web fuzzer for comprehensive web application testing</p>
        </div>
      </div>

      {/* Status Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Status</p>
                <p className="text-2xl font-bold">
                  {isScanning ? 'Running' : 'Ready'}
                </p>
              </div>
              <Activity className={cn("h-8 w-8", isScanning ? "text-green-600" : "text-gray-400")} />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Mode</p>
                <p className="text-2xl font-bold capitalize">{config.mode}</p>
              </div>
              {FUZZING_MODES[config.mode].icon}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Threads</p>
                <p className="text-2xl font-bold">{config.performance.threads}</p>
              </div>
              <TrendingUp className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Progress</p>
                <p className="text-2xl font-bold">{execution ? `${execution.progress}%` : '0%'}</p>
              </div>
              <BarChart3 className="h-8 w-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Interface */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <FastForward className="h-5 w-5" />
                FFUF Configuration
              </CardTitle>
              <CardDescription>
                Configure fuzzing parameters and start web application fuzzing
              </CardDescription>
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => navigator.clipboard.writeText(generateCommand())}
              >
                <Copy className="h-4 w-4 mr-2" />
                Copy Command
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setConfig(prev => ({ 
                  ...prev, 
                  url: '', 
                  customWordlist: '', 
                  request: { ...prev.request, data: '', cookies: '', headers: {} },
                  customArgs: ''
                }))}
              >
                <RotateCcw className="h-4 w-4 mr-2" />
                Reset
              </Button>
              {isScanning ? (
                <Button variant="destructive" size="sm" onClick={stopScan}>
                  <Square className="h-4 w-4 mr-2" />
                  Stop Scan
                </Button>
              ) : (
                <Button 
                  size="sm" 
                  onClick={startScan}
                  disabled={!canStart}
                >
                  <Play className="h-4 w-4 mr-2" />
                  Start Fuzzing
                </Button>
              )}
            </div>
          </div>
        </CardHeader>

        <CardContent>
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="configure">Configure</TabsTrigger>
              <TabsTrigger value="output">Output</TabsTrigger>
              <TabsTrigger value="results">Results</TabsTrigger>
              <TabsTrigger value="command">Command</TabsTrigger>
            </TabsList>

            <TabsContent value="configure" className="space-y-6">
              {/* Quick Presets */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Quick Presets</h3>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                  <Button variant="outline" onClick={() => applyPreset('directory_discovery')}>
                    <Search className="h-4 w-4 mr-2" />
                    Directory Discovery
                  </Button>
                  <Button variant="outline" onClick={() => applyPreset('subdomain_enumeration')}>
                    <Globe className="h-4 w-4 mr-2" />
                    Subdomain Enum
                  </Button>
                  <Button variant="outline" onClick={() => applyPreset('parameter_fuzzing')}>
                    <Code className="h-4 w-4 mr-2" />
                    Parameter Fuzzing
                  </Button>
                  <Button variant="outline" onClick={() => applyPreset('extension_discovery')}>
                    <FileText className="h-4 w-4 mr-2" />
                    Extension Discovery
                  </Button>
                  <Button variant="outline" onClick={() => applyPreset('stealth_scan')}>
                    <Lock className="h-4 w-4 mr-2" />
                    Stealth Scan
                  </Button>
                  <Button variant="outline" onClick={() => applyPreset('aggressive_scan')}>
                    <Zap className="h-4 w-4 mr-2" />
                    Aggressive Scan
                  </Button>
                </div>
              </div>

              {/* Target Configuration */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Target Configuration</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Fuzzing Mode</label>
                    <Select value={config.mode} onValueChange={(value) => updateConfig('mode', value)}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {Object.entries(FUZZING_MODES).map(([key, mode]) => (
                          <SelectItem key={key} value={key}>
                            <div className="flex items-center gap-2">
                              {mode.icon}
                              <div>
                                <div className="font-medium">{mode.name}</div>
                                <div className="text-xs text-gray-500">{mode.description}</div>
                              </div>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <label className="text-sm font-medium">Target URL</label>
                    <Input
                      value={config.url}
                      onChange={(e) => updateConfig('url', e.target.value)}
                      placeholder={FUZZING_MODES[config.mode].placeholder}
                    />
                    <p className="text-xs text-gray-500">
                      Use {FUZZING_MODES[config.mode].keyword} as the fuzzing keyword
                    </p>
                  </div>
                </div>
              </div>

              {/* Wordlist Configuration */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Wordlist Configuration</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Wordlist</label>
                    <Select value={config.wordlist} onValueChange={(value) => updateConfig('wordlist', value)}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="dirb_common">Dirb - Common</SelectItem>
                        <SelectItem value="dirb_big">Dirb - Big</SelectItem>
                        <SelectItem value="dirbuster_directory_2_3_medium">DirBuster - Medium</SelectItem>
                        <SelectItem value="dirbuster_directory_2_3_small">DirBuster - Small</SelectItem>
                        <SelectItem value="seclist_discovery_web_content">SecLists - Web Content</SelectItem>
                        <SelectItem value="seclist_discovery_dns">SecLists - DNS</SelectItem>
                        <SelectItem value="seclist_parameters">SecLists - Parameters</SelectItem>
                        <SelectItem value="seclist_extensions">SecLists - Extensions</SelectItem>
                        <SelectItem value="raft_directories">Raft - Directories</SelectItem>
                        <SelectItem value="raft_files">Raft - Files</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <label className="text-sm font-medium">Custom Wordlist Path</label>
                    <Input
                      value={config.customWordlist}
                      onChange={(e) => updateConfig('customWordlist', e.target.value)}
                      placeholder="/path/to/custom/wordlist.txt"
                    />
                  </div>
                </div>
              </div>

              {/* HTTP Request Configuration */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">HTTP Request Configuration</h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">HTTP Method</label>
                    <Select value={config.request.method} onValueChange={(value) => updateConfig('request.method', value)}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {HTTP_METHODS.map(method => (
                          <SelectItem key={method} value={method}>{method}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <label className="text-sm font-medium">User Agent</label>
                    <Input
                      value={config.request.userAgent}
                      onChange={(e) => updateConfig('request.userAgent', e.target.value)}
                      placeholder="ffuf/2.0.0"
                    />
                  </div>

                  <div className="space-y-2">
                    <label className="text-sm font-medium">Cookies</label>
                    <Input
                      value={config.request.cookies}
                      onChange={(e) => updateConfig('request.cookies', e.target.value)}
                      placeholder="session=abc123; token=xyz789"
                    />
                  </div>
                </div>

                {config.request.method !== 'GET' && config.request.method !== 'HEAD' && (
                  <div className="space-y-2">
                    <label className="text-sm font-medium">POST Data</label>
                    <Textarea
                      value={config.request.data}
                      onChange={(e) => updateConfig('request.data', e.target.value)}
                      placeholder="param1=value1&param2=FUZZ"
                      className="min-h-[80px]"
                    />
                  </div>
                )}

                <div className="space-y-2">
                  <label className="text-sm font-medium">Custom Headers</label>
                  <Textarea
                    value={Object.entries(config.request.headers).map(([k, v]) => `${k}: ${v}`).join('
')}
                    onChange={(e) => {
                      const headers = {};
                      e.target.value.split('
').forEach(line => {
                        const [key, ...valueParts] = line.split(':');
                        if (key && valueParts.length > 0) {
                          headers[key.trim()] = valueParts.join(':').trim();
                        }
                      });
                      updateConfig('request.headers', headers);
                    }}
                    placeholder="Authorization: Bearer token&#10;X-Custom-Header: value"
                    className="min-h-[80px]"
                  />
                </div>
              </div>

              {/* Performance Configuration */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Performance Configuration</h3>
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Threads</label>
                    <Input
                      type="number"
                      value={config.performance.threads}
                      onChange={(e) => updateConfig('performance.threads', parseInt(e.target.value) || 40)}
                      min="1"
                      max="200"
                    />
                  </div>

                  <div className="space-y-2">
                    <label className="text-sm font-medium">Delay</label>
                    <Input
                      value={config.performance.delay}
                      onChange={(e) => updateConfig('performance.delay', e.target.value)}
                      placeholder="100ms or 1s-2s"
                    />
                  </div>

                  <div className="space-y-2">
                    <label className="text-sm font-medium">Timeout (s)</label>
                    <Input
                      type="number"
                      value={config.performance.timeout}
                      onChange={(e) => updateConfig('performance.timeout', parseInt(e.target.value) || 10)}
                      min="1"
                      max="300"
                    />
                  </div>

                  <div className="space-y-2">
                    <label className="text-sm font-medium">Max Time (s)</label>
                    <Input
                      type="number"
                      value={config.performance.maxTime}
                      onChange={(e) => updateConfig('performance.maxTime', parseInt(e.target.value) || 0)}
                      min="0"
                      placeholder="0 = unlimited"
                    />
                  </div>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="output" className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold">Real-time Output</h3>
                <div className="flex items-center gap-2">
                  {isScanning && (
                    <Badge variant="secondary" className="animate-pulse">
                      <Activity className="h-3 w-3 mr-1" />
                      Fuzzing in progress...
                    </Badge>
                  )}
                  <Button variant="outline" size="sm">
                    <Download className="h-4 w-4 mr-2" />
                    Export Output
                  </Button>
                </div>
              </div>
              
              {execution?.progress !== undefined && (
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Progress</span>
                    <span>{execution.progress}%</span>
                  </div>
                  <Progress value={execution.progress} className="w-full" />
                </div>
              )}

              <Card>
                <CardContent className="p-4">
                  <div className="bg-gray-900 dark:bg-gray-800 rounded-lg p-4 min-h-[400px] font-mono text-sm overflow-auto">
                    <div className="text-green-400">
                      {execution?.output || 'FFUF output will appear here when fuzzing starts...'}
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="results" className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold">Fuzzing Results</h3>
                <div className="flex items-center gap-2">
                  <Button variant="outline" size="sm">
                    <Download className="h-4 w-4 mr-2" />
                    Export Results
                  </Button>
                  <Button variant="outline" size="sm">
                    <Filter className="h-4 w-4 mr-2" />
                    Filter Results
                  </Button>
                </div>
              </div>

              {execution?.results ? (
                <div className="space-y-4">
                  {/* Results Summary */}
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <Card>
                      <CardContent className="pt-6">
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Total Found</p>
                            <p className="text-2xl font-bold">{execution.results.total || 0}</p>
                          </div>
                          <CheckCircle className="h-8 w-8 text-green-600" />
                        </div>
                      </CardContent>
                    </Card>

                    <Card>
                      <CardContent className="pt-6">
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Status 200</p>
                            <p className="text-2xl font-bold">{execution.results.status_200 || 0}</p>
                          </div>
                          <CheckCircle className="h-8 w-8 text-blue-600" />
                        </div>
                      </CardContent>
                    </Card>

                    <Card>
                      <CardContent className="pt-6">
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Redirects</p>
                            <p className="text-2xl font-bold">{execution.results.redirects || 0}</p>
                          </div>
                          <Link className="h-8 w-8 text-yellow-600" />
                        </div>
                      </CardContent>
                    </Card>

                    <Card>
                      <CardContent className="pt-6">
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Errors</p>
                            <p className="text-2xl font-bold">{execution.results.errors || 0}</p>
                          </div>
                          <AlertTriangle className="h-8 w-8 text-red-600" />
                        </div>
                      </CardContent>
                    </Card>
                  </div>

                  {/* Results Table */}
                  <Card>
                    <CardContent className="p-0">
                      <div className="overflow-x-auto">
                        <table className="w-full">
                          <thead className="border-b">
                            <tr className="text-left">
                              <th className="p-4 font-medium">URL</th>
                              <th className="p-4 font-medium">Status</th>
                              <th className="p-4 font-medium">Size</th>
                              <th className="p-4 font-medium">Words</th>
                              <th className="p-4 font-medium">Lines</th>
                            </tr>
                          </thead>
                          <tbody>
                            {execution.results.findings?.map((finding, index) => (
                              <tr key={index} className="border-b hover:bg-gray-50 dark:hover:bg-gray-800">
                                <td className="p-4 font-mono text-sm">{finding.url}</td>
                                <td className="p-4">
                                  <Badge 
                                    variant={finding.status >= 200 && finding.status < 300 ? 'default' : 
                                            finding.status >= 300 && finding.status < 400 ? 'secondary' : 'destructive'}
                                  >
                                    {finding.status}
                                  </Badge>
                                </td>
                                <td className="p-4 text-sm">{finding.size}</td>
                                <td className="p-4 text-sm">{finding.words}</td>
                                <td className="p-4 text-sm">{finding.lines}</td>
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              ) : (
                <Card>
                  <CardContent className="p-8 text-center">
                    <Search className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">No Results Yet</h3>
                    <p className="text-gray-600 dark:text-gray-400">
                      Start a fuzzing scan to see results here. Results will update in real-time as the scan progresses.
                    </p>
                  </CardContent>
                </Card>
              )}
            </TabsContent>

            <TabsContent value="command" className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold">Generated Command</h3>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => navigator.clipboard.writeText(generateCommand())}
                >
                  <Copy className="h-4 w-4 mr-2" />
                  Copy Command
                </Button>
              </div>

              <Card>
                <CardContent className="p-4">
                  <div className="bg-gray-900 dark:bg-gray-800 rounded-lg p-4 overflow-x-auto">
                    <code className="text-green-400 whitespace-pre-wrap break-all">
                      {generateCommand()}
                    </code>
                  </div>
                </CardContent>
              </Card>

              <div className="space-y-4">
                <h4 className="font-semibold">Command Explanation</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                  <div>
                    <h5 className="font-medium mb-2">Target & Mode</h5>
                    <ul className="space-y-1 text-gray-600 dark:text-gray-400">
                      <li>• <code>-u</code>: Target URL with FUZZ keyword</li>
                      <li>• <code>-w</code>: Wordlist for fuzzing</li>
                      <li>• <code>-X</code>: HTTP method to use</li>
                      <li>• <code>-H</code>: Custom headers</li>
                    </ul>
                  </div>
                  <div>
                    <h5 className="font-medium mb-2">Filtering</h5>
                    <ul className="space-y-1 text-gray-600 dark:text-gray-400">
                      <li>• <code>-mc</code>: Match status codes</li>
                      <li>• <code>-fc</code>: Filter status codes</li>
                      <li>• <code>-fs</code>: Filter by size</li>
                      <li>• <code>-fw</code>: Filter by word count</li>
                    </ul>
                  </div>
                  <div>
                    <h5 className="font-medium mb-2">Performance</h5>
                    <ul className="space-y-1 text-gray-600 dark:text-gray-400">
                      <li>• <code>-t</code>: Number of threads</li>
                      <li>• <code>-p</code>: Delay between requests</li>
                      <li>• <code>-timeout</code>: Request timeout</li>
                      <li>• <code>-rate</code>: Requests per second</li>
                    </ul>
                  </div>
                  <div>
                    <h5 className="font-medium mb-2">Output</h5>
                    <ul className="space-y-1 text-gray-600 dark:text-gray-400">
                      <li>• <code>-of</code>: Output format</li>
                      <li>• <code>-o</code>: Output file</li>
                      <li>• <code>-v</code>: Verbose output</li>
                      <li>• <code>-s</code>: Silent mode</li>
                    </ul>
                  </div>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}