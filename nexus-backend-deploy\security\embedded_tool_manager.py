#!/usr/bin/env python3
"""
Embedded Tool Manager for NexusScan Desktop Production
Manages access to bundled security tools in production executable
"""

import os
import sys
import json
import subprocess
import asyncio
from pathlib import Path
from typing import Dict, List, Optional, Any, Union
import logging

logger = logging.getLogger(__name__)


class EmbeddedToolManager:
    """Manage embedded security tools in production bundle"""
    
    def __init__(self):
        """Initialize embedded tool manager"""
        self.bundle_mode = os.environ.get("NEXUSSCAN_BUNDLE_MODE", "false").lower() == "true"
        self.tools_dir = Path(os.environ.get("NEXUSSCAN_TOOLS_DIR", "tools"))
        self.embedded_tools = {}
        self.tool_executables = {}
        
        if self.bundle_mode:
            self._load_embedded_tools()
        
        self._setup_tool_paths()
    
    def _load_embedded_tools(self):
        """Load embedded tools configuration"""
        try:
            # Look for embedded tools config
            config_paths = [
                Path("embedded_tools.json"),
                Path(__file__).parent.parent.parent / "embedded_tools.json",
                self.tools_dir.parent / "embedded_tools.json"
            ]
            
            config_file = None
            for path in config_paths:
                if path.exists():
                    config_file = path
                    break
            
            if config_file:
                with open(config_file, 'r') as f:
                    config_data = json.load(f)
                    self.embedded_tools = config_data.get("tools", {})
                    logger.info(f"Loaded {len(self.embedded_tools)} embedded tools")
            else:
                logger.warning("No embedded tools configuration found")
                
        except Exception as e:
            logger.error(f"Failed to load embedded tools: {e}")
    
    def _setup_tool_paths(self):
        """Setup tool executable paths"""
        # Check for embedded tools first
        for tool_name, tool_info in self.embedded_tools.items():
            tool_path = Path(tool_info["path"])
            executable = tool_info["executable"]
            
            # Build full executable path
            exe_path = tool_path / executable
            if exe_path.exists():
                self.tool_executables[tool_name] = {
                    "path": str(exe_path),
                    "interpreter": tool_info.get("interpreter"),
                    "working_dir": str(tool_path),
                    "embedded": True
                }
            else:
                # Look for extracted directories
                for item in tool_path.iterdir():
                    if item.is_dir():
                        potential_exe = item / executable
                        if potential_exe.exists():
                            self.tool_executables[tool_name] = {
                                "path": str(potential_exe),
                                "interpreter": tool_info.get("interpreter"),
                                "working_dir": str(item),
                                "embedded": True
                            }
                            break
        
        # Check for system-installed tools as fallback
        system_tools = {
            "nmap": "nmap",
            "nuclei": "nuclei", 
            "sqlmap": "sqlmap",
            "gobuster": "gobuster",
            "hashcat": "hashcat",
            "john": "john",
            "nikto": "nikto",
            "dirb": "dirb"
        }
        
        for tool_name, executable in system_tools.items():
            if tool_name not in self.tool_executables:
                # Check if tool is available in PATH
                if self._check_tool_available(executable):
                    self.tool_executables[tool_name] = {
                        "path": executable,
                        "interpreter": None,
                        "working_dir": None,
                        "embedded": False
                    }
    
    def _check_tool_available(self, executable: str) -> bool:
        """Check if tool is available in system PATH"""
        try:
            result = subprocess.run(
                [executable, "--version"],
                capture_output=True,
                timeout=5
            )
            return result.returncode == 0
        except (subprocess.TimeoutExpired, FileNotFoundError, subprocess.CalledProcessError):
            try:
                # Try with different version flag
                result = subprocess.run(
                    [executable, "-version"],
                    capture_output=True,
                    timeout=5
                )
                return result.returncode == 0
            except:
                return False
    
    def get_available_tools(self) -> List[str]:
        """Get list of available tools"""
        return list(self.tool_executables.keys())
    
    def is_tool_available(self, tool_name: str) -> bool:
        """Check if specific tool is available"""
        return tool_name in self.tool_executables
    
    def get_tool_info(self, tool_name: str) -> Optional[Dict[str, Any]]:
        """Get information about a specific tool"""
        return self.tool_executables.get(tool_name)
    
    async def run_tool(self, tool_name: str, arguments: List[str], 
                      working_dir: Optional[str] = None) -> Dict[str, Any]:
        """Run a security tool with arguments"""
        if not self.is_tool_available(tool_name):
            return {
                "success": False,
                "error": f"Tool {tool_name} not available",
                "output": "",
                "stderr": ""
            }
        
        tool_info = self.tool_executables[tool_name]
        
        # Build command
        command = []
        
        # Add interpreter if needed
        if tool_info["interpreter"]:
            if tool_info["interpreter"] == "python":
                command.append(sys.executable)
            elif tool_info["interpreter"] == "perl":
                command.append("perl")
            elif tool_info["interpreter"] == "ruby":
                command.append("ruby")
        
        # Add tool executable
        command.append(tool_info["path"])
        
        # Add arguments
        command.extend(arguments)
        
        # Determine working directory
        work_dir = working_dir or tool_info["working_dir"] or os.getcwd()
        
        try:
            # Execute command
            process = await asyncio.create_subprocess_exec(
                *command,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                cwd=work_dir
            )
            
            stdout, stderr = await process.communicate()
            
            return {
                "success": process.returncode == 0,
                "returncode": process.returncode,
                "output": stdout.decode('utf-8', errors='ignore'),
                "stderr": stderr.decode('utf-8', errors='ignore'),
                "command": " ".join(command),
                "tool_info": tool_info
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "output": "",
                "stderr": str(e),
                "command": " ".join(command),
                "tool_info": tool_info
            }
    
    def get_tool_status_report(self) -> Dict[str, Any]:
        """Get comprehensive tool status report"""
        report = {
            "bundle_mode": self.bundle_mode,
            "tools_directory": str(self.tools_dir),
            "total_tools": len(self.tool_executables),
            "embedded_tools": 0,
            "system_tools": 0,
            "tools": {}
        }
        
        for tool_name, tool_info in self.tool_executables.items():
            if tool_info["embedded"]:
                report["embedded_tools"] += 1
            else:
                report["system_tools"] += 1
            
            report["tools"][tool_name] = {
                "available": True,
                "embedded": tool_info["embedded"],
                "path": tool_info["path"],
                "interpreter": tool_info["interpreter"],
                "working_dir": tool_info["working_dir"]
            }
        
        # Check for missing core tools
        core_tools = ["nmap", "nuclei", "sqlmap", "gobuster", "hashcat"]
        missing_tools = [tool for tool in core_tools if tool not in self.tool_executables]
        
        report["missing_core_tools"] = missing_tools
        report["core_tools_complete"] = len(missing_tools) == 0
        
        return report


# Convenience functions for specific tools

async def run_nmap_scan(target: str, scan_type: str = "basic", 
                       additional_args: List[str] = None) -> Dict[str, Any]:
    """Run Nmap scan with embedded tool"""
    manager = EmbeddedToolManager()
    
    # Build Nmap arguments based on scan type
    args = []
    
    if scan_type == "basic":
        args = ["-sV", "-sC", target]
    elif scan_type == "fast":
        args = ["-F", target]
    elif scan_type == "comprehensive":
        args = ["-sS", "-sV", "-sC", "-A", "-O", target]
    elif scan_type == "stealth":
        args = ["-sS", "-T2", target]
    else:
        args = [target]
    
    if additional_args:
        args.extend(additional_args)
    
    return await manager.run_tool("nmap", args)

async def run_nuclei_scan(target: str, template_tags: List[str] = None,
                         severity: str = None) -> Dict[str, Any]:
    """Run Nuclei vulnerability scan"""
    manager = EmbeddedToolManager()
    
    args = ["-u", target]
    
    if template_tags:
        args.extend(["-tags", ",".join(template_tags)])
    
    if severity:
        args.extend(["-severity", severity])
    
    # Add output format
    args.extend(["-json"])
    
    return await manager.run_tool("nuclei", args)

async def run_sqlmap_scan(target: str, risk_level: int = 1,
                         level: int = 1) -> Dict[str, Any]:
    """Run SQLMap SQL injection scan"""
    manager = EmbeddedToolManager()
    
    args = [
        "-u", target,
        "--risk", str(risk_level),
        "--level", str(level),
        "--batch"
    ]
    
    return await manager.run_tool("sqlmap", args)

async def run_gobuster_scan(target: str, wordlist: str = None,
                           scan_type: str = "dir") -> Dict[str, Any]:
    """Run Gobuster directory/file scan"""
    manager = EmbeddedToolManager()
    
    args = [scan_type, "-u", target]
    
    if wordlist:
        args.extend(["-w", wordlist])
    else:
        # Use common wordlist paths
        common_wordlists = [
            "/usr/share/wordlists/dirb/common.txt",
            "/usr/share/wordlists/dirbuster/directory-list-2.3-medium.txt",
            "wordlists/common.txt"
        ]
        
        for wl in common_wordlists:
            if Path(wl).exists():
                args.extend(["-w", wl])
                break
    
    return await manager.run_tool("gobuster", args)

async def run_hashcat_crack(hash_file: str, wordlist: str,
                           hash_type: int = 0) -> Dict[str, Any]:
    """Run Hashcat password cracking"""
    manager = EmbeddedToolManager()
    
    args = [
        "-m", str(hash_type),
        "-a", "0",
        hash_file,
        wordlist
    ]
    
    return await manager.run_tool("hashcat", args)


# Tool integration with existing scanner framework

class EmbeddedToolScanner:
    """Integration class for embedded tools with existing scanner framework"""
    
    def __init__(self):
        """Initialize embedded tool scanner"""
        self.manager = EmbeddedToolManager()
    
    async def execute_scan_campaign(self, targets: List[str], 
                                   scan_config: Dict[str, Any]) -> Dict[str, Any]:
        """Execute complete scan campaign using embedded tools"""
        results = {
            "targets": targets,
            "config": scan_config,
            "tool_results": {},
            "summary": {}
        }
        
        # Run enabled tools
        for target in targets:
            target_results = {}
            
            if scan_config.get("nmap_enabled", True):
                nmap_result = await run_nmap_scan(target, scan_config.get("nmap_type", "basic"))
                target_results["nmap"] = nmap_result
            
            if scan_config.get("nuclei_enabled", True):
                nuclei_result = await run_nuclei_scan(
                    target, 
                    scan_config.get("nuclei_tags"),
                    scan_config.get("nuclei_severity")
                )
                target_results["nuclei"] = nuclei_result
            
            if scan_config.get("gobuster_enabled", False):
                gobuster_result = await run_gobuster_scan(target)
                target_results["gobuster"] = gobuster_result
            
            results["tool_results"][target] = target_results
        
        # Generate summary
        results["summary"] = self._generate_scan_summary(results["tool_results"])
        
        return results
    
    def _generate_scan_summary(self, tool_results: Dict[str, Any]) -> Dict[str, Any]:
        """Generate summary of scan results"""
        summary = {
            "total_targets": len(tool_results),
            "successful_scans": 0,
            "failed_scans": 0,
            "vulnerabilities_found": 0,
            "tools_used": set()
        }
        
        for target, results in tool_results.items():
            target_success = False
            
            for tool_name, result in results.items():
                summary["tools_used"].add(tool_name)
                
                if result.get("success", False):
                    target_success = True
                    
                    # Count vulnerabilities (basic heuristic)
                    output = result.get("output", "")
                    if "vulnerability" in output.lower() or "exploit" in output.lower():
                        summary["vulnerabilities_found"] += 1
            
            if target_success:
                summary["successful_scans"] += 1
            else:
                summary["failed_scans"] += 1
        
        summary["tools_used"] = list(summary["tools_used"])
        
        return summary