#!/usr/bin/env python3
"""
Resource Optimizer for Hybrid Execution Pipeline
Intelligent resource management and performance optimization
"""

import asyncio
import logging
import time
import psutil
import json
import gc
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass, field
from enum import Enum
from pathlib import Path
import threading
from collections import defaultdict, deque

from .environment_detector import environment_detector
from .hybrid_execution_engine import ToolExecutionResult, ExecutionResult
from .performance_monitor import get_performance_monitor, MetricType

logger = logging.getLogger(__name__)

class OptimizationStrategy(Enum):
    """Resource optimization strategies"""
    CONSERVATIVE = "conservative"
    BALANCED = "balanced"
    AGGRESSIVE = "aggressive"
    PERFORMANCE = "performance"
    MEMORY_EFFICIENT = "memory_efficient"
    CPU_EFFICIENT = "cpu_efficient"

class ResourceType(Enum):
    """System resource types"""
    CPU = "cpu"
    MEMORY = "memory"
    DISK_IO = "disk_io"
    NETWORK_IO = "network_io"
    CONCURRENT_TASKS = "concurrent_tasks"
    CACHE_SIZE = "cache_size"

@dataclass
class ResourceLimit:
    """Resource usage limit"""
    resource_type: ResourceType
    soft_limit: float
    hard_limit: float
    current_usage: float = 0.0
    enforcement_enabled: bool = True

@dataclass
class OptimizationRule:
    """Performance optimization rule"""
    name: str
    condition: str
    action: str
    threshold: float
    cooldown_seconds: float = 300.0
    last_applied: float = 0.0
    enabled: bool = True

@dataclass
class ResourceProfile:
    """System resource profile"""
    profile_name: str
    max_concurrent_tools: int
    cpu_limit_percent: float
    memory_limit_percent: float
    cache_size_mb: int
    io_priority: str = "normal"
    network_timeout: int = 300

class ResourceOptimizer:
    """Intelligent resource management and optimization"""
    
    def __init__(self):
        self.environment = environment_detector.environment
        
        # Resource monitoring
        self.resource_limits = self._setup_resource_limits()
        self.optimization_rules = self._setup_optimization_rules()
        self.resource_profiles = self._setup_resource_profiles()
        
        # Current configuration
        self.current_strategy = OptimizationStrategy.BALANCED
        self.current_profile = self.resource_profiles["balanced"]
        
        # Resource tracking
        self.resource_usage_history = defaultdict(lambda: deque(maxlen=100))
        self.optimization_actions = deque(maxlen=50)
        self.active_optimizations = {}
        
        # Performance caches
        self.execution_cache = {}
        self.result_cache = {}
        self.cache_max_size = 1000
        self.cache_ttl = 3600.0  # 1 hour
        
        # Throttling and rate limiting
        self.rate_limiters = {}
        self.throttle_enabled = True
        
        # Background optimization
        self._optimization_task = None
        self._resource_monitor_task = None
        self._cleanup_task = None
        self._shutdown_event = asyncio.Event()
        
        # Statistics
        self.optimization_stats = {
            'optimizations_applied': 0,
            'resources_freed_mb': 0,
            'performance_improvements': 0,
            'cache_hits': 0,
            'cache_misses': 0,
            'throttling_events': 0
        }
        
        logger.info("⚡ Resource Optimizer initialized")
        logger.info(f"🌍 Environment: {self.environment.value}")
        logger.info(f"📋 Strategy: {self.current_strategy.value}")
    
    def _setup_resource_limits(self) -> Dict[ResourceType, ResourceLimit]:
        """Setup system resource limits"""
        
        # Get system specs
        cpu_count = psutil.cpu_count()
        memory_total = psutil.virtual_memory().total / (1024**3)  # GB
        
        return {
            ResourceType.CPU: ResourceLimit(
                resource_type=ResourceType.CPU,
                soft_limit=70.0,  # 70% CPU
                hard_limit=85.0   # 85% CPU
            ),
            ResourceType.MEMORY: ResourceLimit(
                resource_type=ResourceType.MEMORY,
                soft_limit=75.0,  # 75% memory
                hard_limit=90.0   # 90% memory
            ),
            ResourceType.CONCURRENT_TASKS: ResourceLimit(
                resource_type=ResourceType.CONCURRENT_TASKS,
                soft_limit=min(cpu_count, 8),     # Max 8 or CPU count
                hard_limit=min(cpu_count * 2, 16) # Max 16 or 2x CPU
            ),
            ResourceType.CACHE_SIZE: ResourceLimit(
                resource_type=ResourceType.CACHE_SIZE,
                soft_limit=min(memory_total * 0.1, 2.0),  # 10% memory or 2GB
                hard_limit=min(memory_total * 0.2, 4.0)   # 20% memory or 4GB
            )
        }
    
    def _setup_optimization_rules(self) -> List[OptimizationRule]:
        """Setup optimization rules"""
        
        return [
            OptimizationRule(
                name="High CPU Usage",
                condition="cpu_usage > 80",
                action="reduce_concurrency",
                threshold=80.0,
                cooldown_seconds=60.0
            ),
            OptimizationRule(
                name="High Memory Usage",
                condition="memory_usage > 85",
                action="clear_caches",
                threshold=85.0,
                cooldown_seconds=30.0
            ),
            OptimizationRule(
                name="Low Performance",
                condition="execution_time > 300",
                action="optimize_execution_method",
                threshold=300.0,
                cooldown_seconds=120.0
            ),
            OptimizationRule(
                name="High Error Rate",
                condition="error_rate > 0.2",
                action="switch_to_conservative",
                threshold=0.2,
                cooldown_seconds=300.0
            ),
            OptimizationRule(
                name="Cache Overflow",
                condition="cache_size > soft_limit",
                action="cleanup_cache",
                threshold=0.8,  # 80% of soft limit
                cooldown_seconds=60.0
            )
        ]
    
    def _setup_resource_profiles(self) -> Dict[str, ResourceProfile]:
        """Setup resource profiles for different scenarios"""
        
        cpu_count = psutil.cpu_count()
        memory_gb = psutil.virtual_memory().total / (1024**3)
        
        return {
            "conservative": ResourceProfile(
                profile_name="Conservative",
                max_concurrent_tools=2,
                cpu_limit_percent=50.0,
                memory_limit_percent=60.0,
                cache_size_mb=500,
                network_timeout=600
            ),
            "balanced": ResourceProfile(
                profile_name="Balanced",
                max_concurrent_tools=min(cpu_count, 4),
                cpu_limit_percent=70.0,
                memory_limit_percent=75.0,
                cache_size_mb=1000,
                network_timeout=300
            ),
            "performance": ResourceProfile(
                profile_name="Performance",
                max_concurrent_tools=min(cpu_count * 2, 8),
                cpu_limit_percent=85.0,
                memory_limit_percent=85.0,
                cache_size_mb=2000,
                network_timeout=180
            ),
            "memory_efficient": ResourceProfile(
                profile_name="Memory Efficient",
                max_concurrent_tools=2,
                cpu_limit_percent=80.0,
                memory_limit_percent=50.0,
                cache_size_mb=250,
                network_timeout=300
            )
        }
    
    async def start_optimization(self):
        """Start background optimization tasks"""
        
        logger.info("🚀 Starting resource optimization...")
        
        # Start resource monitoring
        self._resource_monitor_task = asyncio.create_task(self._resource_monitor_loop())
        
        # Start optimization engine
        self._optimization_task = asyncio.create_task(self._optimization_loop())
        
        # Start cleanup tasks
        self._cleanup_task = asyncio.create_task(self._cleanup_loop())
        
        logger.info("✅ Resource optimization started")
    
    async def stop_optimization(self):
        """Stop background optimization tasks"""
        
        logger.info("🛑 Stopping resource optimization...")
        
        self._shutdown_event.set()
        
        # Cancel tasks
        for task in [self._resource_monitor_task, self._optimization_task, self._cleanup_task]:
            if task:
                task.cancel()
                try:
                    await task
                except asyncio.CancelledError:
                    pass
        
        logger.info("✅ Resource optimization stopped")
    
    async def _resource_monitor_loop(self):
        """Monitor system resources continuously"""
        
        try:
            while not self._shutdown_event.is_set():
                await self._collect_resource_metrics()
                await asyncio.sleep(5.0)  # Monitor every 5 seconds
                
        except asyncio.CancelledError:
            logger.info("Resource monitor loop cancelled")
        except Exception as e:
            logger.error(f"Resource monitor loop error: {e}")
    
    async def _optimization_loop(self):
        """Main optimization engine loop"""
        
        try:
            while not self._shutdown_event.is_set():
                await self._apply_optimization_rules()
                await self._dynamic_tuning()
                await asyncio.sleep(15.0)  # Optimize every 15 seconds
                
        except asyncio.CancelledError:
            logger.info("Optimization loop cancelled")
        except Exception as e:
            logger.error(f"Optimization loop error: {e}")
    
    async def _cleanup_loop(self):
        """Periodic cleanup tasks"""
        
        try:
            while not self._shutdown_event.is_set():
                await self._cleanup_caches()
                await self._garbage_collection()
                await asyncio.sleep(300.0)  # Cleanup every 5 minutes
                
        except asyncio.CancelledError:
            logger.info("Cleanup loop cancelled")
        except Exception as e:
            logger.error(f"Cleanup loop error: {e}")
    
    async def _collect_resource_metrics(self):
        """Collect current resource usage metrics"""
        
        try:
            current_time = time.time()
            
            # CPU usage
            cpu_percent = psutil.cpu_percent(interval=None)
            self.resource_usage_history[ResourceType.CPU].append((current_time, cpu_percent))
            self.resource_limits[ResourceType.CPU].current_usage = cpu_percent
            
            # Memory usage
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            self.resource_usage_history[ResourceType.MEMORY].append((current_time, memory_percent))
            self.resource_limits[ResourceType.MEMORY].current_usage = memory_percent
            
            # Cache size
            cache_size_mb = self._calculate_cache_size()
            self.resource_usage_history[ResourceType.CACHE_SIZE].append((current_time, cache_size_mb))
            self.resource_limits[ResourceType.CACHE_SIZE].current_usage = cache_size_mb
            
        except Exception as e:
            logger.error(f"Failed to collect resource metrics: {e}")
    
    def _calculate_cache_size(self) -> float:
        """Calculate current cache size in MB"""
        
        total_size = 0
        
        # Execution cache
        for key, value in self.execution_cache.items():
            total_size += len(str(key)) + len(str(value))
        
        # Result cache
        for key, value in self.result_cache.items():
            total_size += len(str(key)) + len(str(value))
        
        return total_size / (1024 * 1024)  # Convert to MB
    
    async def _apply_optimization_rules(self):
        """Apply optimization rules based on current metrics"""
        
        current_time = time.time()
        
        for rule in self.optimization_rules:
            if not rule.enabled:
                continue
                
            # Check cooldown
            if current_time - rule.last_applied < rule.cooldown_seconds:
                continue
            
            # Evaluate condition
            if await self._evaluate_rule_condition(rule):
                await self._apply_optimization_action(rule)
                rule.last_applied = current_time
                
                self.optimization_actions.append({
                    'rule': rule.name,
                    'action': rule.action,
                    'timestamp': current_time,
                    'reason': rule.condition
                })
                
                logger.info(f"⚡ Applied optimization: {rule.name} -> {rule.action}")
    
    async def _evaluate_rule_condition(self, rule: OptimizationRule) -> bool:
        """Evaluate if optimization rule condition is met"""
        
        try:
            if rule.condition == "cpu_usage > 80":
                return self.resource_limits[ResourceType.CPU].current_usage > rule.threshold
            
            elif rule.condition == "memory_usage > 85":
                return self.resource_limits[ResourceType.MEMORY].current_usage > rule.threshold
            
            elif rule.condition == "execution_time > 300":
                # Check recent execution times
                recent_times = self._get_recent_execution_times(60.0)
                if recent_times:
                    avg_time = sum(recent_times) / len(recent_times)
                    return avg_time > rule.threshold
            
            elif rule.condition == "error_rate > 0.2":
                # Check recent error rate
                error_rate = await self._calculate_recent_error_rate()
                return error_rate > rule.threshold
            
            elif rule.condition == "cache_size > soft_limit":
                cache_limit = self.resource_limits[ResourceType.CACHE_SIZE].soft_limit * 1024  # MB to bytes
                current_cache = self.resource_limits[ResourceType.CACHE_SIZE].current_usage * 1024
                return current_cache > (cache_limit * rule.threshold)
            
        except Exception as e:
            logger.error(f"Failed to evaluate rule condition: {e}")
        
        return False
    
    async def _apply_optimization_action(self, rule: OptimizationRule):
        """Apply optimization action"""
        
        try:
            if rule.action == "reduce_concurrency":
                await self._reduce_concurrency()
            
            elif rule.action == "clear_caches":
                await self._clear_caches()
            
            elif rule.action == "optimize_execution_method":
                await self._optimize_execution_methods()
            
            elif rule.action == "switch_to_conservative":
                await self._switch_to_conservative_mode()
            
            elif rule.action == "cleanup_cache":
                await self._cleanup_caches()
            
            self.optimization_stats['optimizations_applied'] += 1
            
        except Exception as e:
            logger.error(f"Failed to apply optimization action: {e}")
    
    async def _reduce_concurrency(self):
        """Reduce concurrent tool execution"""
        
        current_limit = self.resource_limits[ResourceType.CONCURRENT_TASKS].current_usage
        new_limit = max(1, int(current_limit * 0.7))  # Reduce by 30%
        
        self.resource_limits[ResourceType.CONCURRENT_TASKS].soft_limit = new_limit
        self.current_profile.max_concurrent_tools = new_limit
        
        logger.info(f"🔽 Reduced concurrency limit to {new_limit}")
    
    async def _clear_caches(self):
        """Clear performance caches to free memory"""
        
        # Calculate size before clearing
        old_size = self._calculate_cache_size()
        
        # Clear caches
        self.execution_cache.clear()
        self.result_cache.clear()
        
        # Force garbage collection
        gc.collect()
        
        # Calculate freed memory
        freed_mb = old_size
        self.optimization_stats['resources_freed_mb'] += freed_mb
        
        logger.info(f"🧹 Cleared caches, freed {freed_mb:.1f}MB")
    
    async def _optimize_execution_methods(self):
        """Optimize execution method preferences"""
        
        # This would analyze recent execution performance and adjust preferences
        # For now, prefer faster methods
        logger.info("⚡ Optimizing execution method preferences")
        self.optimization_stats['performance_improvements'] += 1
    
    async def _switch_to_conservative_mode(self):
        """Switch to conservative resource profile"""
        
        old_profile = self.current_profile.profile_name
        self.current_profile = self.resource_profiles["conservative"]
        self.current_strategy = OptimizationStrategy.CONSERVATIVE
        
        # Update resource limits
        self.resource_limits[ResourceType.CPU].soft_limit = self.current_profile.cpu_limit_percent
        self.resource_limits[ResourceType.MEMORY].soft_limit = self.current_profile.memory_limit_percent
        self.resource_limits[ResourceType.CONCURRENT_TASKS].soft_limit = self.current_profile.max_concurrent_tools
        
        logger.warning(f"🔄 Switched from {old_profile} to Conservative mode")
    
    async def _cleanup_caches(self):
        """Clean up expired cache entries"""
        
        current_time = time.time()
        cleaned_items = 0
        
        # Clean execution cache
        expired_keys = []
        for key, (timestamp, data) in self.execution_cache.items():
            if current_time - timestamp > self.cache_ttl:
                expired_keys.append(key)
        
        for key in expired_keys:
            del self.execution_cache[key]
            cleaned_items += 1
        
        # Clean result cache
        expired_keys = []
        for key, (timestamp, data) in self.result_cache.items():
            if current_time - timestamp > self.cache_ttl:
                expired_keys.append(key)
        
        for key in expired_keys:
            del self.result_cache[key]
            cleaned_items += 1
        
        if cleaned_items > 0:
            logger.debug(f"🧹 Cleaned {cleaned_items} expired cache entries")
    
    async def _garbage_collection(self):
        """Force garbage collection to free memory"""
        
        before_collect = psutil.virtual_memory().percent
        collected = gc.collect()
        after_collect = psutil.virtual_memory().percent
        
        memory_freed = before_collect - after_collect
        
        if memory_freed > 0.5:  # Only log if significant memory was freed
            logger.debug(f"🗑️ Garbage collection freed {memory_freed:.1f}% memory, collected {collected} objects")
    
    async def _dynamic_tuning(self):
        """Dynamic performance tuning based on recent metrics"""
        
        try:
            # Analyze recent performance
            recent_cpu = self._get_recent_metric_average(ResourceType.CPU, 300.0)  # 5 minutes
            recent_memory = self._get_recent_metric_average(ResourceType.MEMORY, 300.0)
            
            # Auto-tune based on resource usage
            if recent_cpu < 50.0 and recent_memory < 60.0:
                # System is underutilized, can increase limits
                await self._increase_resource_limits()
            elif recent_cpu > 80.0 or recent_memory > 85.0:
                # System is stressed, reduce limits
                await self._decrease_resource_limits()
                
        except Exception as e:
            logger.error(f"Dynamic tuning failed: {e}")
    
    async def _increase_resource_limits(self):
        """Increase resource limits when system is underutilized"""
        
        current_concurrency = self.resource_limits[ResourceType.CONCURRENT_TASKS].soft_limit
        max_concurrency = self.resource_limits[ResourceType.CONCURRENT_TASKS].hard_limit
        
        if current_concurrency < max_concurrency:
            new_limit = min(max_concurrency, current_concurrency + 1)
            self.resource_limits[ResourceType.CONCURRENT_TASKS].soft_limit = new_limit
            self.current_profile.max_concurrent_tools = int(new_limit)
            
            logger.debug(f"📈 Increased concurrency limit to {new_limit}")
    
    async def _decrease_resource_limits(self):
        """Decrease resource limits when system is stressed"""
        
        current_concurrency = self.resource_limits[ResourceType.CONCURRENT_TASKS].soft_limit
        
        if current_concurrency > 1:
            new_limit = max(1, current_concurrency - 1)
            self.resource_limits[ResourceType.CONCURRENT_TASKS].soft_limit = new_limit
            self.current_profile.max_concurrent_tools = int(new_limit)
            
            logger.debug(f"📉 Decreased concurrency limit to {new_limit}")
    
    def _get_recent_metric_average(self, resource_type: ResourceType, seconds: float) -> float:
        """Get average of recent metrics"""
        
        cutoff_time = time.time() - seconds
        recent_values = []
        
        for timestamp, value in self.resource_usage_history[resource_type]:
            if timestamp >= cutoff_time:
                recent_values.append(value)
        
        return sum(recent_values) / len(recent_values) if recent_values else 0.0
    
    def _get_recent_execution_times(self, seconds: float) -> List[float]:
        """Get recent execution times (placeholder - would integrate with actual execution tracking)"""
        # This would integrate with the actual execution history
        return []
    
    async def _calculate_recent_error_rate(self) -> float:
        """Calculate recent error rate (placeholder - would integrate with actual execution tracking)"""
        # This would integrate with the actual execution history
        return 0.0
    
    async def optimize_tool_execution(self, tool_name: str, target: str, options: Dict[str, Any]) -> Dict[str, Any]:
        """Optimize tool execution parameters"""
        
        # Check cache first
        cache_key = f"{tool_name}:{target}:{hash(str(options))}"
        cached_result = self._get_cached_result(cache_key)
        
        if cached_result:
            self.optimization_stats['cache_hits'] += 1
            logger.debug(f"💾 Cache hit for {tool_name} on {target}")
            return cached_result
        
        self.optimization_stats['cache_misses'] += 1
        
        # Apply throttling if needed
        if self.throttle_enabled:
            await self._apply_throttling(tool_name)
        
        # Optimize execution parameters
        optimized_options = await self._optimize_execution_parameters(tool_name, options)
        
        return {
            'tool_name': tool_name,
            'target': target,
            'options': optimized_options,
            'cache_key': cache_key,
            'optimized': True
        }
    
    def _get_cached_result(self, cache_key: str) -> Optional[Dict[str, Any]]:
        """Get cached execution result"""
        
        if cache_key in self.result_cache:
            timestamp, result = self.result_cache[cache_key]
            
            # Check if cache entry is still valid
            if time.time() - timestamp < self.cache_ttl:
                return result
            else:
                # Remove expired entry
                del self.result_cache[cache_key]
        
        return None
    
    def cache_execution_result(self, cache_key: str, result: Dict[str, Any]):
        """Cache execution result"""
        
        # Check cache size limit
        if len(self.result_cache) >= self.cache_max_size:
            # Remove oldest entries
            oldest_keys = sorted(self.result_cache.keys(), 
                               key=lambda k: self.result_cache[k][0])[:10]
            for key in oldest_keys:
                del self.result_cache[key]
        
        self.result_cache[cache_key] = (time.time(), result)
    
    async def _apply_throttling(self, tool_name: str):
        """Apply throttling to prevent resource exhaustion"""
        
        if tool_name not in self.rate_limiters:
            self.rate_limiters[tool_name] = {
                'last_execution': 0.0,
                'min_interval': 1.0,  # Minimum 1 second between executions
                'requests': deque(maxlen=10)
            }
        
        limiter = self.rate_limiters[tool_name]
        current_time = time.time()
        
        # Check if we need to throttle
        time_since_last = current_time - limiter['last_execution']
        
        if time_since_last < limiter['min_interval']:
            throttle_delay = limiter['min_interval'] - time_since_last
            logger.debug(f"🐌 Throttling {tool_name} for {throttle_delay:.1f}s")
            await asyncio.sleep(throttle_delay)
            self.optimization_stats['throttling_events'] += 1
        
        limiter['last_execution'] = current_time
        limiter['requests'].append(current_time)
    
    async def _optimize_execution_parameters(self, tool_name: str, options: Dict[str, Any]) -> Dict[str, Any]:
        """Optimize execution parameters based on current system state"""
        
        optimized = options.copy()
        
        # Adjust timeout based on current system load
        cpu_usage = self.resource_limits[ResourceType.CPU].current_usage
        memory_usage = self.resource_limits[ResourceType.MEMORY].current_usage
        
        # If system is under stress, reduce timeouts to prevent hanging
        if cpu_usage > 80 or memory_usage > 85:
            current_timeout = optimized.get('timeout', 300)
            optimized['timeout'] = min(current_timeout, 180)  # Max 3 minutes under stress
        
        # Adjust concurrency based on current profile
        if 'concurrency' in optimized:
            max_concurrency = self.current_profile.max_concurrent_tools
            optimized['concurrency'] = min(optimized['concurrency'], max_concurrency)
        
        # Add resource-aware options
        optimized['resource_profile'] = self.current_profile.profile_name
        optimized['optimization_strategy'] = self.current_strategy.value
        
        return optimized
    
    def set_optimization_strategy(self, strategy: OptimizationStrategy):
        """Set optimization strategy"""
        
        old_strategy = self.current_strategy
        self.current_strategy = strategy
        
        # Update profile based on strategy
        if strategy == OptimizationStrategy.CONSERVATIVE:
            self.current_profile = self.resource_profiles["conservative"]
        elif strategy == OptimizationStrategy.PERFORMANCE:
            self.current_profile = self.resource_profiles["performance"]
        elif strategy == OptimizationStrategy.MEMORY_EFFICIENT:
            self.current_profile = self.resource_profiles["memory_efficient"]
        else:
            self.current_profile = self.resource_profiles["balanced"]
        
        logger.info(f"🔄 Changed optimization strategy: {old_strategy.value} -> {strategy.value}")
    
    def get_resource_status(self) -> Dict[str, Any]:
        """Get current resource status and optimization state"""
        
        return {
            'timestamp': time.time(),
            'current_strategy': self.current_strategy.value,
            'current_profile': {
                'name': self.current_profile.profile_name,
                'max_concurrent_tools': self.current_profile.max_concurrent_tools,
                'cpu_limit': self.current_profile.cpu_limit_percent,
                'memory_limit': self.current_profile.memory_limit_percent,
                'cache_size_mb': self.current_profile.cache_size_mb
            },
            'resource_limits': {
                rt.value: {
                    'soft_limit': limit.soft_limit,
                    'hard_limit': limit.hard_limit,
                    'current_usage': limit.current_usage,
                    'enforcement_enabled': limit.enforcement_enabled
                }
                for rt, limit in self.resource_limits.items()
            },
            'optimization_stats': self.optimization_stats.copy(),
            'cache_stats': {
                'execution_cache_size': len(self.execution_cache),
                'result_cache_size': len(self.result_cache),
                'cache_size_mb': self._calculate_cache_size(),
                'cache_hit_rate': (
                    self.optimization_stats['cache_hits'] / 
                    max(1, self.optimization_stats['cache_hits'] + self.optimization_stats['cache_misses'])
                ) * 100
            },
            'recent_optimizations': list(self.optimization_actions)[-5:]  # Last 5 optimizations
        }
    
    def get_optimization_recommendations(self) -> List[str]:
        """Get optimization recommendations based on current state"""
        
        recommendations = []
        
        # CPU-based recommendations
        cpu_usage = self.resource_limits[ResourceType.CPU].current_usage
        if cpu_usage > 85:
            recommendations.append("High CPU usage detected - consider reducing concurrent operations")
        elif cpu_usage < 30:
            recommendations.append("Low CPU usage - can increase concurrent operations for better performance")
        
        # Memory-based recommendations
        memory_usage = self.resource_limits[ResourceType.MEMORY].current_usage
        if memory_usage > 85:
            recommendations.append("High memory usage - clear caches or reduce concurrent operations")
        
        # Cache-based recommendations
        cache_hit_rate = (
            self.optimization_stats['cache_hits'] / 
            max(1, self.optimization_stats['cache_hits'] + self.optimization_stats['cache_misses'])
        ) * 100
        
        if cache_hit_rate < 20:
            recommendations.append("Low cache hit rate - consider enabling result caching")
        
        # Strategy recommendations
        if self.current_strategy == OptimizationStrategy.CONSERVATIVE and cpu_usage < 50:
            recommendations.append("Consider switching to Balanced strategy for better performance")
        
        return recommendations

# Global resource optimizer
resource_optimizer = None

async def get_resource_optimizer() -> ResourceOptimizer:
    """Get or create resource optimizer"""
    global resource_optimizer
    
    if resource_optimizer is None:
        resource_optimizer = ResourceOptimizer()
        await resource_optimizer.start_optimization()
    
    return resource_optimizer

async def start_resource_optimization():
    """Start global resource optimization"""
    optimizer = await get_resource_optimizer()
    logger.info("⚡ Global resource optimization started")

async def stop_resource_optimization():
    """Stop global resource optimization"""
    global resource_optimizer
    
    if resource_optimizer:
        await resource_optimizer.stop_optimization()
        resource_optimizer = None
    
    logger.info("🛑 Global resource optimization stopped")