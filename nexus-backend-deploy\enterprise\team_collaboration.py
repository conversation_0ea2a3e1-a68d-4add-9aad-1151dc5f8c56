"""
Team Collaboration System for NexusScan Desktop
Enterprise team collaboration with sharing, notifications, and real-time updates.
"""

import asyncio
import logging
import json
import time
import uuid
from typing import Dict, List, Optional, Any, Set, Tuple
from enum import Enum
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from pathlib import Path
import hashlib

from core.config import Config
from core.database import DatabaseManager
from core.events import EventManager, EventTypes
from ai.ai_service import AIServiceManager

logger = logging.getLogger(__name__)


class NotificationType(Enum):
    """Types of notifications"""
    TASK_ASSIGNED = "task_assigned"
    TASK_COMPLETED = "task_completed"
    CAMPAIGN_STARTED = "campaign_started"
    CAMPAIGN_COMPLETED = "campaign_completed"
    FINDING_SHARED = "finding_shared"
    COMMENT_ADDED = "comment_added"
    REPORT_READY = "report_ready"
    DEADLINE_APPROACHING = "deadline_approaching"
    SYSTEM_ALERT = "system_alert"
    TEAM_MENTION = "team_mention"


class ShareLevel(Enum):
    """Sharing permission levels"""
    READ_ONLY = "read_only"
    EDIT = "edit"
    FULL_ACCESS = "full_access"
    ADMIN = "admin"


class ActivityType(Enum):
    """Types of team activities"""
    CREATED = "created"
    UPDATED = "updated"
    DELETED = "deleted"
    SHARED = "shared"
    COMMENTED = "commented"
    REVIEWED = "reviewed"
    APPROVED = "approved"
    REJECTED = "rejected"


@dataclass
class TeamMember:
    """Team member information"""
    user_id: str
    username: str
    display_name: str
    email: str
    role: str
    department: str
    skills: List[str]
    availability: str  # available, busy, away, offline
    timezone: str
    last_active: datetime
    profile_image: Optional[str]
    preferences: Dict[str, Any]


@dataclass
class Notification:
    """Team notification"""
    notification_id: str
    recipient_id: str
    sender_id: str
    notification_type: NotificationType
    title: str
    message: str
    data: Dict[str, Any]  # Additional notification data
    created_date: datetime
    read_date: Optional[datetime]
    acknowledged: bool
    priority: str  # low, medium, high, urgent
    expires_date: Optional[datetime]


@dataclass
class SharedResource:
    """Shared resource (campaign, report, finding, etc.)"""
    resource_id: str
    resource_type: str  # campaign, report, finding, document
    resource_name: str
    owner_id: str
    shared_with: Dict[str, ShareLevel]  # user_id -> permission level
    created_date: datetime
    last_modified: datetime
    access_log: List[Dict[str, Any]]
    metadata: Dict[str, Any]


@dataclass
class Comment:
    """Comment on shared resources"""
    comment_id: str
    resource_id: str
    resource_type: str
    author_id: str
    content: str
    created_date: datetime
    edited_date: Optional[datetime]
    parent_comment_id: Optional[str]  # For replies
    mentions: List[str]  # Mentioned user IDs
    attachments: List[str]
    reactions: Dict[str, List[str]]  # emoji -> list of user_ids


@dataclass
class ActivityLog:
    """Team activity log entry"""
    activity_id: str
    user_id: str
    activity_type: ActivityType
    resource_type: str
    resource_id: str
    description: str
    timestamp: datetime
    metadata: Dict[str, Any]
    visibility: str  # public, team, private


class TeamCollaboration:
    """Enterprise team collaboration system"""
    
    def __init__(self, config: Config, db_manager: DatabaseManager,
                 event_manager: EventManager, ai_service: AIServiceManager):
        """Initialize team collaboration system"""
        self.config = config
        self.db_manager = db_manager
        self.event_manager = event_manager
        self.ai_service = ai_service
        
        # Team data storage
        self.team_members: Dict[str, TeamMember] = {}
        self.notifications: Dict[str, List[Notification]] = {}  # user_id -> notifications
        self.shared_resources: Dict[str, SharedResource] = {}
        self.comments: Dict[str, List[Comment]] = {}  # resource_id -> comments
        self.activity_logs: List[ActivityLog] = []
        
        # Real-time subscriptions
        self.subscribers: Dict[str, Set[str]] = {}  # resource_id -> set of user_ids
        self.online_users: Set[str] = set()
        
        # Initialize demo team members
        self._initialize_demo_team()
        
        logger.info("Team collaboration system initialized")
    
    def _initialize_demo_team(self):
        """Initialize demo team members"""
        demo_members = [
            TeamMember(
                user_id="admin",
                username="admin",
                display_name="System Administrator",
                email="<EMAIL>",
                role="administrator",
                department="Security",
                skills=["penetration_testing", "compliance", "management"],
                availability="available",
                timezone="UTC",
                last_active=datetime.now(),
                profile_image=None,
                preferences={"notifications": True, "email_alerts": True}
            ),
            TeamMember(
                user_id="pentest_lead",
                username="jsmith",
                display_name="John Smith",
                email="<EMAIL>",
                role="senior_pentester",
                department="Security",
                skills=["penetration_testing", "web_security", "network_security"],
                availability="available",
                timezone="EST",
                last_active=datetime.now(),
                profile_image=None,
                preferences={"notifications": True, "email_alerts": False}
            ),
            TeamMember(
                user_id="compliance_analyst",
                username="mjohnson",
                display_name="Mary Johnson",
                email="<EMAIL>",
                role="compliance_analyst",
                department="Compliance",
                skills=["compliance_auditing", "risk_assessment", "documentation"],
                availability="available",
                timezone="PST",
                last_active=datetime.now(),
                profile_image=None,
                preferences={"notifications": True, "email_alerts": True}
            )
        ]
        
        for member in demo_members:
            self.team_members[member.user_id] = member
            self.notifications[member.user_id] = []
        
        logger.info(f"Initialized {len(demo_members)} demo team members")
    
    async def add_team_member(self, member_data: Dict[str, Any]) -> str:
        """Add new team member"""
        try:
            user_id = member_data.get("user_id", f"user_{uuid.uuid4().hex[:8]}")
            
            member = TeamMember(
                user_id=user_id,
                username=member_data["username"],
                display_name=member_data["display_name"],
                email=member_data["email"],
                role=member_data.get("role", "analyst"),
                department=member_data.get("department", "Security"),
                skills=member_data.get("skills", []),
                availability="available",
                timezone=member_data.get("timezone", "UTC"),
                last_active=datetime.now(),
                profile_image=member_data.get("profile_image"),
                preferences=member_data.get("preferences", {})
            )
            
            self.team_members[user_id] = member
            self.notifications[user_id] = []
            
            # Log activity
            await self._log_activity(
                user_id="system",
                activity_type=ActivityType.CREATED,
                resource_type="team_member",
                resource_id=user_id,
                description=f"Added team member: {member.display_name}"
            )
            
            logger.info(f"Added team member: {user_id}")
            return user_id
            
        except Exception as e:
            logger.error(f"Failed to add team member: {e}")
            raise
    
    async def share_resource(self, resource_id: str, resource_type: str, 
                           resource_name: str, owner_id: str,
                           shared_with: Dict[str, str]) -> bool:
        """Share resource with team members"""
        try:
            # Convert string permissions to ShareLevel enum
            share_permissions = {}
            for user_id, permission in shared_with.items():
                if user_id in self.team_members:
                    share_permissions[user_id] = ShareLevel(permission)
            
            shared_resource = SharedResource(
                resource_id=resource_id,
                resource_type=resource_type,
                resource_name=resource_name,
                owner_id=owner_id,
                shared_with=share_permissions,
                created_date=datetime.now(),
                last_modified=datetime.now(),
                access_log=[],
                metadata={}
            )
            
            self.shared_resources[resource_id] = shared_resource
            
            # Send notifications to shared users
            for user_id in share_permissions.keys():
                await self._send_notification(
                    recipient_id=user_id,
                    sender_id=owner_id,
                    notification_type=NotificationType.FINDING_SHARED,
                    title=f"Resource Shared: {resource_name}",
                    message=f"{self.team_members[owner_id].display_name} shared {resource_type} '{resource_name}' with you",
                    data={
                        "resource_id": resource_id,
                        "resource_type": resource_type,
                        "permission": share_permissions[user_id].value
                    }
                )
            
            # Log activity
            await self._log_activity(
                user_id=owner_id,
                activity_type=ActivityType.SHARED,
                resource_type=resource_type,
                resource_id=resource_id,
                description=f"Shared {resource_type} '{resource_name}' with {len(share_permissions)} team members"
            )
            
            logger.info(f"Shared resource {resource_id} with {len(share_permissions)} users")
            return True
            
        except Exception as e:
            logger.error(f"Failed to share resource: {e}")
            return False
    
    async def add_comment(self, resource_id: str, resource_type: str,
                         author_id: str, content: str,
                         parent_comment_id: Optional[str] = None) -> str:
        """Add comment to shared resource"""
        try:
            comment_id = f"comment_{uuid.uuid4().hex[:8]}"
            
            # Extract mentions from content
            mentions = self._extract_mentions(content)
            
            comment = Comment(
                comment_id=comment_id,
                resource_id=resource_id,
                resource_type=resource_type,
                author_id=author_id,
                content=content,
                created_date=datetime.now(),
                edited_date=None,
                parent_comment_id=parent_comment_id,
                mentions=mentions,
                attachments=[],
                reactions={}
            )
            
            # Store comment
            if resource_id not in self.comments:
                self.comments[resource_id] = []
            self.comments[resource_id].append(comment)
            
            # Send notifications for mentions
            author_name = self.team_members[author_id].display_name
            for mentioned_user in mentions:
                if mentioned_user in self.team_members:
                    await self._send_notification(
                        recipient_id=mentioned_user,
                        sender_id=author_id,
                        notification_type=NotificationType.TEAM_MENTION,
                        title=f"You were mentioned by {author_name}",
                        message=f"{author_name} mentioned you in a comment",
                        data={
                            "resource_id": resource_id,
                            "comment_id": comment_id,
                            "content": content[:100] + "..." if len(content) > 100 else content
                        }
                    )
            
            # Notify resource subscribers
            if resource_id in self.subscribers:
                for subscriber_id in self.subscribers[resource_id]:
                    if subscriber_id != author_id and subscriber_id not in mentions:
                        await self._send_notification(
                            recipient_id=subscriber_id,
                            sender_id=author_id,
                            notification_type=NotificationType.COMMENT_ADDED,
                            title=f"New comment on {resource_type}",
                            message=f"{author_name} added a comment",
                            data={
                                "resource_id": resource_id,
                                "comment_id": comment_id
                            }
                        )
            
            # Log activity
            await self._log_activity(
                user_id=author_id,
                activity_type=ActivityType.COMMENTED,
                resource_type=resource_type,
                resource_id=resource_id,
                description=f"Added comment to {resource_type}"
            )
            
            logger.info(f"Added comment {comment_id} to resource {resource_id}")
            return comment_id
            
        except Exception as e:
            logger.error(f"Failed to add comment: {e}")
            raise
    
    def _extract_mentions(self, content: str) -> List[str]:
        """Extract @mentions from comment content"""
        import re
        mentions = []
        
        # Find @username patterns
        pattern = r'@(\w+)'
        matches = re.findall(pattern, content)
        
        for match in matches:
            # Find user by username
            for user_id, member in self.team_members.items():
                if member.username == match:
                    mentions.append(user_id)
                    break
        
        return mentions
    
    async def _send_notification(self, recipient_id: str, sender_id: str,
                               notification_type: NotificationType, title: str,
                               message: str, data: Dict[str, Any],
                               priority: str = "medium") -> str:
        """Send notification to team member"""
        try:
            notification_id = f"notif_{uuid.uuid4().hex[:8]}"
            
            notification = Notification(
                notification_id=notification_id,
                recipient_id=recipient_id,
                sender_id=sender_id,
                notification_type=notification_type,
                title=title,
                message=message,
                data=data,
                created_date=datetime.now(),
                read_date=None,
                acknowledged=False,
                priority=priority,
                expires_date=None
            )
            
            # Store notification
            if recipient_id in self.notifications:
                self.notifications[recipient_id].append(notification)
            
            # Emit real-time notification event
            await self.event_manager.emit(
                EventTypes.NOTIFICATION_SENT,
                {
                    "notification_id": notification_id,
                    "recipient_id": recipient_id,
                    "type": notification_type.value,
                    "title": title,
                    "priority": priority
                },
                "team_collaboration"
            )
            
            return notification_id
            
        except Exception as e:
            logger.error(f"Failed to send notification: {e}")
            return ""
    
    async def _log_activity(self, user_id: str, activity_type: ActivityType,
                          resource_type: str, resource_id: str, description: str,
                          visibility: str = "team") -> str:
        """Log team activity"""
        try:
            activity_id = f"activity_{uuid.uuid4().hex[:8]}"
            
            activity = ActivityLog(
                activity_id=activity_id,
                user_id=user_id,
                activity_type=activity_type,
                resource_type=resource_type,
                resource_id=resource_id,
                description=description,
                timestamp=datetime.now(),
                metadata={},
                visibility=visibility
            )
            
            self.activity_logs.append(activity)
            
            # Keep only recent activities (last 1000)
            if len(self.activity_logs) > 1000:
                self.activity_logs = self.activity_logs[-1000:]
            
            return activity_id
            
        except Exception as e:
            logger.error(f"Failed to log activity: {e}")
            return ""
    
    async def subscribe_to_resource(self, resource_id: str, user_id: str) -> bool:
        """Subscribe user to resource updates"""
        try:
            if resource_id not in self.subscribers:
                self.subscribers[resource_id] = set()
            
            self.subscribers[resource_id].add(user_id)
            
            logger.info(f"User {user_id} subscribed to resource {resource_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to subscribe to resource: {e}")
            return False
    
    async def unsubscribe_from_resource(self, resource_id: str, user_id: str) -> bool:
        """Unsubscribe user from resource updates"""
        try:
            if resource_id in self.subscribers and user_id in self.subscribers[resource_id]:
                self.subscribers[resource_id].remove(user_id)
                
                # Clean up empty subscriptions
                if not self.subscribers[resource_id]:
                    del self.subscribers[resource_id]
                
                logger.info(f"User {user_id} unsubscribed from resource {resource_id}")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Failed to unsubscribe from resource: {e}")
            return False
    
    async def mark_notification_read(self, notification_id: str, user_id: str) -> bool:
        """Mark notification as read"""
        try:
            if user_id in self.notifications:
                for notification in self.notifications[user_id]:
                    if notification.notification_id == notification_id:
                        notification.read_date = datetime.now()
                        return True
            
            return False
            
        except Exception as e:
            logger.error(f"Failed to mark notification as read: {e}")
            return False
    
    async def update_user_status(self, user_id: str, availability: str) -> bool:
        """Update user availability status"""
        try:
            if user_id in self.team_members:
                self.team_members[user_id].availability = availability
                self.team_members[user_id].last_active = datetime.now()
                
                # Update online users set
                if availability == "available":
                    self.online_users.add(user_id)
                else:
                    self.online_users.discard(user_id)
                
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Failed to update user status: {e}")
            return False
    
    def get_team_members(self) -> List[Dict[str, Any]]:
        """Get all team members"""
        members = []
        
        for member in self.team_members.values():
            members.append({
                "user_id": member.user_id,
                "username": member.username,
                "display_name": member.display_name,
                "email": member.email,
                "role": member.role,
                "department": member.department,
                "skills": member.skills,
                "availability": member.availability,
                "timezone": member.timezone,
                "last_active": member.last_active.isoformat(),
                "online": member.user_id in self.online_users
            })
        
        return members
    
    def get_user_notifications(self, user_id: str, unread_only: bool = False) -> List[Dict[str, Any]]:
        """Get notifications for user"""
        if user_id not in self.notifications:
            return []
        
        notifications = []
        for notification in self.notifications[user_id]:
            if unread_only and notification.read_date:
                continue
            
            sender_name = "System"
            if notification.sender_id in self.team_members:
                sender_name = self.team_members[notification.sender_id].display_name
            
            notifications.append({
                "notification_id": notification.notification_id,
                "type": notification.notification_type.value,
                "title": notification.title,
                "message": notification.message,
                "sender_name": sender_name,
                "created_date": notification.created_date.isoformat(),
                "read_date": notification.read_date.isoformat() if notification.read_date else None,
                "priority": notification.priority,
                "data": notification.data
            })
        
        return sorted(notifications, key=lambda x: x["created_date"], reverse=True)
    
    def get_shared_resources(self, user_id: str) -> List[Dict[str, Any]]:
        """Get resources shared with user"""
        resources = []
        
        for resource in self.shared_resources.values():
            # Check if user has access
            if user_id == resource.owner_id or user_id in resource.shared_with:
                permission = "owner" if user_id == resource.owner_id else resource.shared_with[user_id].value
                
                resources.append({
                    "resource_id": resource.resource_id,
                    "resource_type": resource.resource_type,
                    "resource_name": resource.resource_name,
                    "owner_name": self.team_members[resource.owner_id].display_name,
                    "permission": permission,
                    "created_date": resource.created_date.isoformat(),
                    "last_modified": resource.last_modified.isoformat(),
                    "shared_with_count": len(resource.shared_with)
                })
        
        return sorted(resources, key=lambda x: x["last_modified"], reverse=True)
    
    def get_resource_comments(self, resource_id: str) -> List[Dict[str, Any]]:
        """Get comments for resource"""
        if resource_id not in self.comments:
            return []
        
        comments = []
        for comment in self.comments[resource_id]:
            author_name = "Unknown"
            if comment.author_id in self.team_members:
                author_name = self.team_members[comment.author_id].display_name
            
            comments.append({
                "comment_id": comment.comment_id,
                "author_name": author_name,
                "content": comment.content,
                "created_date": comment.created_date.isoformat(),
                "edited_date": comment.edited_date.isoformat() if comment.edited_date else None,
                "parent_comment_id": comment.parent_comment_id,
                "mentions": comment.mentions,
                "reactions": comment.reactions
            })
        
        return sorted(comments, key=lambda x: x["created_date"])
    
    def get_team_activity(self, limit: int = 50, user_filter: Optional[str] = None) -> List[Dict[str, Any]]:
        """Get recent team activity"""
        activities = []
        
        for activity in self.activity_logs[-limit:]:
            # Apply user filter
            if user_filter and activity.user_id != user_filter:
                continue
            
            user_name = "System"
            if activity.user_id in self.team_members:
                user_name = self.team_members[activity.user_id].display_name
            
            activities.append({
                "activity_id": activity.activity_id,
                "user_name": user_name,
                "activity_type": activity.activity_type.value,
                "resource_type": activity.resource_type,
                "description": activity.description,
                "timestamp": activity.timestamp.isoformat(),
                "visibility": activity.visibility
            })
        
        return sorted(activities, key=lambda x: x["timestamp"], reverse=True)
    
    def get_collaboration_metrics(self) -> Dict[str, Any]:
        """Get team collaboration metrics"""
        total_members = len(self.team_members)
        online_members = len(self.online_users)
        
        # Count notifications by type
        notification_counts = {}
        for user_notifications in self.notifications.values():
            for notification in user_notifications:
                notif_type = notification.notification_type.value
                notification_counts[notif_type] = notification_counts.get(notif_type, 0) + 1
        
        # Count activities by type in last 7 days
        week_ago = datetime.now() - timedelta(days=7)
        recent_activities = [a for a in self.activity_logs if a.timestamp >= week_ago]
        
        activity_counts = {}
        for activity in recent_activities:
            activity_type = activity.activity_type.value
            activity_counts[activity_type] = activity_counts.get(activity_type, 0) + 1
        
        return {
            "total_members": total_members,
            "online_members": online_members,
            "shared_resources": len(self.shared_resources),
            "total_comments": sum(len(comments) for comments in self.comments.values()),
            "total_notifications": sum(len(notifications) for notifications in self.notifications.values()),
            "notification_types": notification_counts,
            "recent_activity_count": len(recent_activities),
            "activity_types": activity_counts,
            "collaboration_score": self._calculate_collaboration_score()
        }
    
    def _calculate_collaboration_score(self) -> float:
        """Calculate team collaboration effectiveness score"""
        if not self.team_members:
            return 0.0
        
        # Factors that contribute to collaboration score
        factors = []
        
        # Active participation (recent activity)
        week_ago = datetime.now() - timedelta(days=7)
        recent_activities = len([a for a in self.activity_logs if a.timestamp >= week_ago])
        participation_score = min(recent_activities / (len(self.team_members) * 5), 1.0)  # 5 activities per member target
        factors.append(participation_score)
        
        # Communication (comments and notifications)
        total_comments = sum(len(comments) for comments in self.comments.values())
        communication_score = min(total_comments / 100, 1.0)  # Target 100 comments
        factors.append(communication_score)
        
        # Resource sharing
        sharing_score = min(len(self.shared_resources) / 20, 1.0)  # Target 20 shared resources
        factors.append(sharing_score)
        
        # Team availability
        availability_score = len(self.online_users) / len(self.team_members)
        factors.append(availability_score)
        
        # Calculate weighted average
        return sum(factors) / len(factors) * 100
    
    async def send_team_announcement(self, sender_id: str, title: str, 
                                   message: str, priority: str = "medium") -> bool:
        """Send announcement to all team members"""
        try:
            sent_count = 0
            
            for user_id in self.team_members.keys():
                if user_id != sender_id:  # Don't send to sender
                    await self._send_notification(
                        recipient_id=user_id,
                        sender_id=sender_id,
                        notification_type=NotificationType.SYSTEM_ALERT,
                        title=title,
                        message=message,
                        data={"announcement": True},
                        priority=priority
                    )
                    sent_count += 1
            
            # Log activity
            await self._log_activity(
                user_id=sender_id,
                activity_type=ActivityType.CREATED,
                resource_type="announcement",
                resource_id=f"announce_{int(time.time())}",
                description=f"Sent team announcement: {title}"
            )
            
            logger.info(f"Sent team announcement to {sent_count} members")
            return True
            
        except Exception as e:
            logger.error(f"Failed to send team announcement: {e}")
            return False
    
    async def check_resource_access(self, resource_id: str, user_id: str) -> Optional[ShareLevel]:
        """Check user's access level to resource"""
        if resource_id not in self.shared_resources:
            return None
        
        resource = self.shared_resources[resource_id]
        
        # Owner has full access
        if resource.owner_id == user_id:
            return ShareLevel.FULL_ACCESS
        
        # Check shared permissions
        if user_id in resource.shared_with:
            return resource.shared_with[user_id]
        
        # No access
        return None
    
    async def log_resource_access(self, resource_id: str, user_id: str, action: str):
        """Log resource access for audit purposes"""
        if resource_id in self.shared_resources:
            access_entry = {
                "user_id": user_id,
                "action": action,
                "timestamp": datetime.now().isoformat(),
                "ip_address": "127.0.0.1"  # Would get real IP in production
            }
            
            self.shared_resources[resource_id].access_log.append(access_entry)
            
            # Keep only recent access logs (last 100)
            if len(self.shared_resources[resource_id].access_log) > 100:
                self.shared_resources[resource_id].access_log = self.shared_resources[resource_id].access_log[-100:]