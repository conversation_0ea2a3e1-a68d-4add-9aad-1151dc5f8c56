#!/usr/bin/env python3
"""
Verify Railway Backend Endpoints and Tools
Confirms all tools and endpoints listed in tools-based-frontend.md exist on Railway
"""

import asyncio
import aiohttp
import json
from typing import Dict, List, Any
from datetime import datetime
import sys

# Railway backend URL
RAILWAY_BASE_URL = "https://web-production-48c02.up.railway.app"

# Define all endpoints that should exist based on tools-based-frontend.md
ENDPOINTS_TO_VERIFY = {
    "Core Health & Status": [
        ("GET", "/api/health", "Backend health check"),
        ("GET", "/api/status", "System status"),
    ],
    
    "Ferrari AI Endpoints": [
        ("GET", "/api/orchestrator/capabilities", "Multi-Stage Attack Orchestrator"),
        ("GET", "/api/orchestrator/templates", "Orchestrator templates"),
        ("GET", "/api/orchestrator/analytics", "Orchestrator analytics"),
        ("GET", "/api/proxy/configurations", "AI Proxy configurations"),
        ("GET", "/api/ai/creative-exploits/capabilities", "Creative Exploit Engine"),
        ("GET", "/api/ai/behavioral-analysis/capabilities", "Behavioral Analysis"),
        ("GET", "/api/ai/evasion-techniques/capabilities", "Evasion Techniques"),
        ("GET", "/api/ai/capabilities/status", "AI Capabilities Status"),
    ],
    
    "Security Tools": [
        ("GET", "/api/tools/available", "List available security tools"),
        ("GET", "/api/tools/nmap/status", "Nmap scanner status"),
        ("GET", "/api/tools/nuclei/status", "Nuclei scanner status"),
        ("GET", "/api/tools/sqlmap/status", "SQLMap scanner status"),
        ("POST", "/api/tools/nmap/scan", "Nmap scan execution"),
        ("POST", "/api/tools/nuclei/scan", "Nuclei scan execution"),
        ("POST", "/api/tools/sqlmap/scan", "SQLMap scan execution"),
    ],
    
    "Campaign Management": [
        ("GET", "/api/campaigns", "List campaigns"),
        ("POST", "/api/campaigns", "Create campaign"),
        ("GET", "/api/campaigns/{id}", "Get campaign details"),
        ("PUT", "/api/campaigns/{id}", "Update campaign"),
        ("DELETE", "/api/campaigns/{id}", "Delete campaign"),
    ],
    
    "Vulnerability Management": [
        ("GET", "/api/vulnerabilities", "List vulnerabilities"),
        ("POST", "/api/vulnerabilities", "Create vulnerability"),
        ("GET", "/api/vulnerabilities/{id}", "Get vulnerability details"),
        ("PUT", "/api/vulnerabilities/{id}", "Update vulnerability"),
    ],
    
    "Compliance Testing": [
        ("GET", "/api/compliance/frameworks", "List compliance frameworks"),
        ("POST", "/api/compliance/test", "Run compliance test"),
        ("GET", "/api/compliance/results", "Get compliance results"),
    ],
    
    "Reports": [
        ("GET", "/api/reports", "List reports"),
        ("POST", "/api/reports/generate", "Generate report"),
        ("GET", "/api/reports/{id}", "Get report details"),
        ("GET", "/api/reports/{id}/download", "Download report"),
    ],
    
    "WebSocket": [
        ("WS", "/ws", "WebSocket connection for real-time updates"),
    ]
}

# Tools that should be available based on tools-based-frontend.md
EXPECTED_TOOLS = {
    "Core Scanners": ["nmap", "nuclei", "sqlmap"],
    "AI Tools": [
        "creative_exploit_engine",
        "multi_stage_orchestrator",
        "behavioral_analysis_engine",
        "adaptive_exploit_modifier",
        "evasion_technique_generator",
        "vulnerability_agent",
        "threat_intelligence_analyzer",
        "scan_recommendation_engine",
        "remediation_engine",
        "intelligent_payload_optimizer",
        "predictive_vulnerability_discovery",
        "automated_exploit_chaining",
        "realtime_threat_intelligence_correlator"
    ],
    "Intelligence Tools": [
        "ai_reconnaissance_engine",
        "tech_fingerprinter",
        "mitre_attack_mapper"
    ],
    "Compliance": ["automated_compliance_tester"],
    "Utilities": [
        "universal_parser",
        "evidence_collector",
        "proxy_engine",
        "metasploit_bridge",
        "credential_validator",
        "payload_encoder",
        "report_formatter"
    ],
    "Infrastructure": [
        "performance_monitor",
        "resource_optimizer",
        "self_healing_manager",
        "environment_detector",
        "execution_orchestrator",
        "hybrid_execution_engine",
        "tool_registry",
        "capability_mapper"
    ]
}


class RailwayEndpointVerifier:
    def __init__(self):
        self.base_url = RAILWAY_BASE_URL
        self.results = {
            "timestamp": datetime.now().isoformat(),
            "base_url": self.base_url,
            "endpoints": {},
            "tools": {},
            "summary": {
                "total_endpoints": 0,
                "successful_endpoints": 0,
                "failed_endpoints": 0,
                "total_tools": 0,
                "available_tools": 0,
                "missing_tools": 0
            }
        }
    
    async def verify_endpoint(self, session: aiohttp.ClientSession, method: str, path: str, description: str) -> Dict[str, Any]:
        """Verify a single endpoint"""
        url = f"{self.base_url}{path}"
        
        # Skip WebSocket endpoints for now
        if method == "WS":
            return {
                "url": url,
                "method": method,
                "description": description,
                "status": "skipped",
                "message": "WebSocket endpoint - manual verification needed"
            }
        
        # Skip endpoints with parameters
        if "{id}" in path:
            return {
                "url": url,
                "method": method,
                "description": description,
                "status": "skipped",
                "message": "Parameterized endpoint - requires valid ID"
            }
        
        try:
            # For POST endpoints, send minimal valid data
            json_data = None
            if method == "POST":
                if "scan" in path:
                    json_data = {"target": "test.example.com", "options": {}}
                elif "campaign" in path:
                    json_data = {"name": "Test Campaign", "description": "Verification test"}
                elif "compliance" in path:
                    json_data = {"framework": "SOC2", "scope": "test"}
                elif "report" in path:
                    json_data = {"campaign_id": 1, "format": "pdf"}
                else:
                    json_data = {}
            
            async with session.request(method, url, json=json_data, timeout=10) as response:
                result = {
                    "url": url,
                    "method": method,
                    "description": description,
                    "status_code": response.status,
                    "success": response.status < 500,  # 2xx, 3xx, 4xx are "working"
                }
                
                # Try to get response body
                try:
                    result["response"] = await response.json()
                except:
                    result["response"] = await response.text()
                
                return result
                
        except asyncio.TimeoutError:
            return {
                "url": url,
                "method": method,
                "description": description,
                "status": "timeout",
                "success": False,
                "error": "Request timed out after 10 seconds"
            }
        except Exception as e:
            return {
                "url": url,
                "method": method,
                "description": description,
                "status": "error",
                "success": False,
                "error": str(e)
            }
    
    async def verify_tools_endpoint(self, session: aiohttp.ClientSession) -> Dict[str, Any]:
        """Verify the tools availability endpoint"""
        url = f"{self.base_url}/api/tools/available"
        
        try:
            async with session.get(url, timeout=10) as response:
                if response.status == 200:
                    data = await response.json()
                    return {
                        "success": True,
                        "tools": data.get("tools", []),
                        "count": len(data.get("tools", []))
                    }
                else:
                    return {
                        "success": False,
                        "status_code": response.status,
                        "error": f"Failed to get tools list: {response.status}"
                    }
        except Exception as e:
            return {
                "success": False,
                "error": str(e)
            }
    
    async def run_verification(self):
        """Run complete verification"""
        print(f"\n🔍 Verifying Railway Backend at: {self.base_url}")
        print("=" * 80)
        
        async with aiohttp.ClientSession() as session:
            # Verify all endpoints
            for category, endpoints in ENDPOINTS_TO_VERIFY.items():
                print(f"\n📂 {category}:")
                self.results["endpoints"][category] = []
                
                for method, path, description in endpoints:
                    result = await self.verify_endpoint(session, method, path, description)
                    self.results["endpoints"][category].append(result)
                    
                    # Update summary
                    self.results["summary"]["total_endpoints"] += 1
                    if result.get("success", False) or result.get("status") == "skipped":
                        self.results["summary"]["successful_endpoints"] += 1
                        status_symbol = "✅" if result.get("success", False) else "⏭️"
                    else:
                        self.results["summary"]["failed_endpoints"] += 1
                        status_symbol = "❌"
                    
                    print(f"  {status_symbol} {method} {path} - {description}")
                    if not result.get("success", False) and result.get("status") != "skipped":
                        print(f"     Error: {result.get('error', result.get('status_code', 'Unknown'))}")
            
            # Verify tools availability
            print("\n📂 Security Tools Verification:")
            tools_result = await self.verify_tools_endpoint(session)
            
            if tools_result["success"]:
                available_tools = tools_result["tools"]
                print(f"\n  ✅ Found {len(available_tools)} tools on Railway backend")
                
                # Check each expected tool category
                for category, expected_list in EXPECTED_TOOLS.items():
                    print(f"\n  🔧 {category}:")
                    for tool in expected_list:
                        self.results["summary"]["total_tools"] += 1
                        if tool in available_tools:
                            self.results["summary"]["available_tools"] += 1
                            print(f"    ✅ {tool}")
                        else:
                            self.results["summary"]["missing_tools"] += 1
                            print(f"    ❌ {tool} (NOT FOUND)")
                
                # Check for unexpected tools
                all_expected = []
                for tools in EXPECTED_TOOLS.values():
                    all_expected.extend(tools)
                
                unexpected = set(available_tools) - set(all_expected)
                if unexpected:
                    print(f"\n  🆕 Additional tools found on backend:")
                    for tool in unexpected:
                        print(f"    ➕ {tool}")
            else:
                print(f"  ❌ Failed to get tools list: {tools_result.get('error', 'Unknown error')}")
        
        # Print summary
        print("\n" + "=" * 80)
        print("📊 VERIFICATION SUMMARY")
        print("=" * 80)
        print(f"Backend URL: {self.base_url}")
        print(f"\nEndpoints:")
        print(f"  Total: {self.results['summary']['total_endpoints']}")
        print(f"  ✅ Working: {self.results['summary']['successful_endpoints']}")
        print(f"  ❌ Failed: {self.results['summary']['failed_endpoints']}")
        print(f"\nSecurity Tools:")
        print(f"  Expected: {self.results['summary']['total_tools']}")
        print(f"  ✅ Available: {self.results['summary']['available_tools']}")
        print(f"  ❌ Missing: {self.results['summary']['missing_tools']}")
        
        # Save detailed results
        with open("railway_verification_results.json", "w") as f:
            json.dump(self.results, f, indent=2)
        print(f"\n💾 Detailed results saved to: railway_verification_results.json")
        
        # Overall status
        if self.results['summary']['failed_endpoints'] == 0 and self.results['summary']['missing_tools'] == 0:
            print("\n✅ All endpoints and tools verified successfully!")
        else:
            print("\n⚠️  Some endpoints or tools need attention!")
            if self.results['summary']['missing_tools'] > 0:
                print("\n📝 Note: Some tools may not be exposed via the /api/tools/available endpoint")
                print("   but could still be functional through other API endpoints.")


async def main():
    verifier = RailwayEndpointVerifier()
    await verifier.run_verification()


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n\n⚠️  Verification interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n❌ Verification failed: {e}")
        sys.exit(1)