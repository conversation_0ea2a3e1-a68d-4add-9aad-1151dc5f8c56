"""
Enterprise Reporting System for NexusScan Desktop
Advanced reporting with custom templates, multi-format export, and executive dashboards.
"""

import asyncio
import logging
import json
import time
import uuid
import base64
from typing import Dict, List, Optional, Any, Set, Tuple, Union
from enum import Enum
from datetime import datetime, timed<PERSON>ta
from dataclasses import dataclass, asdict
from pathlib import Path
import jinja2
from jinja2 import Template
import matplotlib.pyplot as plt
import seaborn as sns
import pandas as pd
from io import BytesIO
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots

from core.config import Config
from core.database import DatabaseManager
from core.events import EventManager, EventTypes
from ai.ai_service import AIServiceManager

logger = logging.getLogger(__name__)


class ReportType(Enum):
    """Types of enterprise reports"""
    EXECUTIVE_SUMMARY = "executive_summary"
    TECHNICAL_DETAILED = "technical_detailed"
    COMPLIANCE_AUDIT = "compliance_audit"
    VULNERABILITY_ASSESSMENT = "vulnerability_assessment"
    PENETRATION_TEST = "penetration_test"
    RISK_ASSESSMENT = "risk_assessment"
    SECURITY_POSTURE = "security_posture"
    INCIDENT_RESPONSE = "incident_response"
    TREND_ANALYSIS = "trend_analysis"
    CUSTOM = "custom"


class ReportFormat(Enum):
    """Supported report formats"""
    PDF = "pdf"
    HTML = "html"
    DOCX = "docx"
    XLSX = "xlsx"
    PPTX = "pptx"
    JSON = "json"
    CSV = "csv"


class ReportAudience(Enum):
    """Target audience for reports"""
    EXECUTIVE = "executive"
    TECHNICAL = "technical"
    COMPLIANCE = "compliance"
    MANAGEMENT = "management"
    EXTERNAL_AUDITOR = "external_auditor"
    CUSTOMER = "customer"


class ChartType(Enum):
    """Types of charts for visualization"""
    BAR_CHART = "bar_chart"
    PIE_CHART = "pie_chart"
    LINE_CHART = "line_chart"
    SCATTER_PLOT = "scatter_plot"
    HEATMAP = "heatmap"
    TREND_LINE = "trend_line"
    VULNERABILITY_TIMELINE = "vulnerability_timeline"
    RISK_MATRIX = "risk_matrix"
    COMPLIANCE_RADAR = "compliance_radar"


@dataclass
class ReportSection:
    """Individual report section"""
    section_id: str
    title: str
    content_type: str  # text, chart, table, image
    content: Any
    order: int
    styling: Dict[str, Any]
    visibility_rules: Dict[str, Any]


@dataclass
class ReportTemplate:
    """Report template definition"""
    template_id: str
    name: str
    description: str
    report_type: ReportType
    target_audience: ReportAudience
    sections: List[ReportSection]
    styling: Dict[str, Any]
    variables: Dict[str, Any]
    created_by: str
    created_date: datetime
    is_active: bool
    version: str


@dataclass
class ReportRequest:
    """Report generation request"""
    request_id: str
    template_id: str
    requester_id: str
    parameters: Dict[str, Any]
    data_sources: List[str]
    output_formats: List[ReportFormat]
    scheduled: bool
    schedule_config: Optional[Dict[str, Any]]
    recipients: List[str]
    created_date: datetime
    priority: str


@dataclass
class GeneratedReport:
    """Generated report metadata"""
    report_id: str
    request_id: str
    template_id: str
    title: str
    generated_by: str
    generated_date: datetime
    data_period: Tuple[datetime, datetime]
    file_paths: Dict[ReportFormat, str]
    size_bytes: int
    page_count: int
    chart_count: int
    table_count: int
    executive_summary: str
    key_findings: List[str]
    recommendations: List[str]
    status: str
    error_message: Optional[str]


class EnterpriseReporting:
    """Enterprise reporting system with custom templates"""
    
    def __init__(self, config: Config, db_manager: DatabaseManager,
                 event_manager: EventManager, ai_service: AIServiceManager):
        """Initialize enterprise reporting system"""
        self.config = config
        self.db_manager = db_manager
        self.event_manager = event_manager
        self.ai_service = ai_service
        
        # Report storage
        self.reports_dir = Path(config.data_dir) / "reports"
        self.templates_dir = Path(config.data_dir) / "report_templates"
        self.charts_dir = Path(config.data_dir) / "report_charts"
        
        # Create directories
        self.reports_dir.mkdir(exist_ok=True)
        self.templates_dir.mkdir(exist_ok=True)
        self.charts_dir.mkdir(exist_ok=True)
        
        # Report templates and requests
        self.report_templates: Dict[str, ReportTemplate] = {}
        self.report_requests: Dict[str, ReportRequest] = {}
        self.generated_reports: Dict[str, GeneratedReport] = {}
        
        # Jinja2 environment for template rendering
        self.jinja_env = jinja2.Environment(
            loader=jinja2.FileSystemLoader(self.templates_dir),
            autoescape=True
        )
        
        # Load built-in templates
        self._load_builtin_templates()
        
        # Configure visualization styling
        self._configure_chart_styling()
        
        logger.info("Enterprise reporting system initialized")
    
    def _load_builtin_templates(self):
        """Load built-in report templates"""
        
        # Executive Summary Template
        exec_template = ReportTemplate(
            template_id="executive_summary_v1",
            name="Executive Security Summary",
            description="High-level security posture summary for executives",
            report_type=ReportType.EXECUTIVE_SUMMARY,
            target_audience=ReportAudience.EXECUTIVE,
            sections=[
                ReportSection(
                    section_id="exec_overview",
                    title="Executive Overview",
                    content_type="text",
                    content="executive_overview_template",
                    order=1,
                    styling={"font_size": 14, "bold": True},
                    visibility_rules={}
                ),
                ReportSection(
                    section_id="key_metrics",
                    title="Key Security Metrics",
                    content_type="chart",
                    content="security_metrics_dashboard",
                    order=2,
                    styling={"chart_type": "dashboard"},
                    visibility_rules={}
                ),
                ReportSection(
                    section_id="risk_summary",
                    title="Risk Assessment Summary",
                    content_type="chart",
                    content="risk_matrix_chart",
                    order=3,
                    styling={"chart_type": "risk_matrix"},
                    visibility_rules={}
                ),
                ReportSection(
                    section_id="recommendations",
                    title="Strategic Recommendations",
                    content_type="text",
                    content="strategic_recommendations",
                    order=4,
                    styling={"format": "bullet_points"},
                    visibility_rules={}
                )
            ],
            styling={
                "theme": "executive",
                "colors": ["#2E86AB", "#A23B72", "#F18F01", "#C73E1D"],
                "font_family": "Arial",
                "page_size": "A4"
            },
            variables={
                "company_name": "{{company_name}}",
                "report_period": "{{report_period}}",
                "assessment_type": "{{assessment_type}}"
            },
            created_by="system",
            created_date=datetime.now(),
            is_active=True,
            version="1.0"
        )
        
        # Technical Detailed Template
        tech_template = ReportTemplate(
            template_id="technical_detailed_v1",
            name="Technical Security Assessment",
            description="Comprehensive technical security assessment report",
            report_type=ReportType.TECHNICAL_DETAILED,
            target_audience=ReportAudience.TECHNICAL,
            sections=[
                ReportSection(
                    section_id="tech_summary",
                    title="Technical Summary",
                    content_type="text",
                    content="technical_summary_template",
                    order=1,
                    styling={"technical_format": True},
                    visibility_rules={}
                ),
                ReportSection(
                    section_id="vulnerability_details",
                    title="Vulnerability Analysis",
                    content_type="table",
                    content="vulnerability_details_table",
                    order=2,
                    styling={"table_format": "detailed"},
                    visibility_rules={}
                ),
                ReportSection(
                    section_id="exploit_chain",
                    title="Exploitation Methodology",
                    content_type="text",
                    content="exploitation_methodology",
                    order=3,
                    styling={"code_format": True},
                    visibility_rules={}
                ),
                ReportSection(
                    section_id="remediation",
                    title="Technical Remediation",
                    content_type="text",
                    content="technical_remediation",
                    order=4,
                    styling={"priority_color_coding": True},
                    visibility_rules={}
                )
            ],
            styling={
                "theme": "technical",
                "colors": ["#1f2937", "#374151", "#6b7280", "#9ca3af"],
                "font_family": "Consolas",
                "page_size": "A4",
                "code_highlighting": True
            },
            variables={
                "target_systems": "{{target_systems}}",
                "methodology": "{{methodology}}",
                "tools_used": "{{tools_used}}"
            },
            created_by="system",
            created_date=datetime.now(),
            is_active=True,
            version="1.0"
        )
        
        # Compliance Audit Template
        compliance_template = ReportTemplate(
            template_id="compliance_audit_v1",
            name="Compliance Audit Report",
            description="Compliance assessment against regulatory frameworks",
            report_type=ReportType.COMPLIANCE_AUDIT,
            target_audience=ReportAudience.COMPLIANCE,
            sections=[
                ReportSection(
                    section_id="compliance_overview",
                    title="Compliance Overview",
                    content_type="text",
                    content="compliance_overview_template",
                    order=1,
                    styling={"official_format": True},
                    visibility_rules={}
                ),
                ReportSection(
                    section_id="framework_assessment",
                    title="Framework Assessment",
                    content_type="chart",
                    content="compliance_radar_chart",
                    order=2,
                    styling={"chart_type": "compliance_radar"},
                    visibility_rules={}
                ),
                ReportSection(
                    section_id="control_findings",
                    title="Control Findings",
                    content_type="table",
                    content="control_findings_table",
                    order=3,
                    styling={"compliance_table": True},
                    visibility_rules={}
                ),
                ReportSection(
                    section_id="attestation",
                    title="Management Attestation",
                    content_type="text",
                    content="management_attestation",
                    order=4,
                    styling={"signature_section": True},
                    visibility_rules={}
                )
            ],
            styling={
                "theme": "compliance",
                "colors": ["#059669", "#dc2626", "#f59e0b", "#3b82f6"],
                "font_family": "Times New Roman",
                "page_size": "Letter",
                "formal_layout": True
            },
            variables={
                "framework": "{{framework}}",
                "audit_period": "{{audit_period}}",
                "auditor": "{{auditor}}"
            },
            created_by="system",
            created_date=datetime.now(),
            is_active=True,
            version="1.0"
        )
        
        self.report_templates = {
            "executive_summary_v1": exec_template,
            "technical_detailed_v1": tech_template,
            "compliance_audit_v1": compliance_template
        }
        
        logger.info(f"Loaded {len(self.report_templates)} built-in report templates")
    
    def _configure_chart_styling(self):
        """Configure chart styling and themes"""
        # Set default matplotlib style
        plt.style.use('seaborn-v0_8')
        sns.set_palette("husl")
        
        # Plotly default configuration
        self.plotly_config = {
            'displayModeBar': False,
            'responsive': True
        }
        
        # Color schemes by audience
        self.color_schemes = {
            ReportAudience.EXECUTIVE: ["#2E86AB", "#A23B72", "#F18F01", "#C73E1D"],
            ReportAudience.TECHNICAL: ["#1f2937", "#374151", "#6b7280", "#9ca3af"],
            ReportAudience.COMPLIANCE: ["#059669", "#dc2626", "#f59e0b", "#3b82f6"]
        }
    
    async def create_custom_template(self, template_data: Dict[str, Any], 
                                   created_by: str) -> str:
        """Create custom report template"""
        try:
            template_id = f"custom_{uuid.uuid4().hex[:8]}"
            
            # Parse sections
            sections = []
            for i, section_data in enumerate(template_data.get("sections", [])):
                section = ReportSection(
                    section_id=f"section_{i+1}",
                    title=section_data["title"],
                    content_type=section_data["content_type"],
                    content=section_data["content"],
                    order=i + 1,
                    styling=section_data.get("styling", {}),
                    visibility_rules=section_data.get("visibility_rules", {})
                )
                sections.append(section)
            
            template = ReportTemplate(
                template_id=template_id,
                name=template_data["name"],
                description=template_data.get("description", ""),
                report_type=ReportType(template_data.get("report_type", "custom")),
                target_audience=ReportAudience(template_data.get("target_audience", "technical")),
                sections=sections,
                styling=template_data.get("styling", {}),
                variables=template_data.get("variables", {}),
                created_by=created_by,
                created_date=datetime.now(),
                is_active=True,
                version="1.0"
            )
            
            self.report_templates[template_id] = template
            
            # Save template to file
            template_file = self.templates_dir / f"{template_id}.json"
            with open(template_file, "w") as f:
                json.dump(asdict(template), f, indent=2, default=str)
            
            logger.info(f"Created custom report template: {template_id}")
            return template_id
            
        except Exception as e:
            logger.error(f"Failed to create custom template: {e}")
            raise
    
    async def generate_report(self, template_id: str, parameters: Dict[str, Any],
                            output_formats: List[ReportFormat] = None,
                            requester_id: str = "system") -> str:
        """Generate report from template"""
        try:
            if template_id not in self.report_templates:
                raise ValueError(f"Template not found: {template_id}")
            
            if output_formats is None:
                output_formats = [ReportFormat.PDF, ReportFormat.HTML]
            
            report_id = f"report_{uuid.uuid4().hex[:8]}"
            template = self.report_templates[template_id]
            
            # Create report request
            request_id = f"req_{uuid.uuid4().hex[:8]}"
            request = ReportRequest(
                request_id=request_id,
                template_id=template_id,
                requester_id=requester_id,
                parameters=parameters,
                data_sources=parameters.get("data_sources", []),
                output_formats=output_formats,
                scheduled=False,
                schedule_config=None,
                recipients=parameters.get("recipients", []),
                created_date=datetime.now(),
                priority="medium"
            )
            
            self.report_requests[request_id] = request
            
            # Generate report content
            report_data = await self._collect_report_data(template, parameters)
            rendered_content = await self._render_report_content(template, report_data)
            
            # Generate visualizations
            charts = await self._generate_charts(template, report_data)
            
            # Create report files in requested formats
            file_paths = {}
            for format_type in output_formats:
                file_path = await self._export_report(
                    report_id, template, rendered_content, charts, format_type
                )
                file_paths[format_type] = file_path
            
            # Generate AI insights
            ai_summary = await self._generate_ai_insights(report_data, template.target_audience)
            key_findings = await self._extract_key_findings(report_data)
            recommendations = await self._generate_recommendations(report_data, template.report_type)
            
            # Create report metadata
            generated_report = GeneratedReport(
                report_id=report_id,
                request_id=request_id,
                template_id=template_id,
                title=f"{template.name} - {datetime.now().strftime('%Y-%m-%d')}",
                generated_by=requester_id,
                generated_date=datetime.now(),
                data_period=(
                    parameters.get("start_date", datetime.now() - timedelta(days=30)),
                    parameters.get("end_date", datetime.now())
                ),
                file_paths={fmt: str(path) for fmt, path in file_paths.items()},
                size_bytes=sum(Path(path).stat().st_size for path in file_paths.values()),
                page_count=self._estimate_page_count(rendered_content),
                chart_count=len(charts),
                table_count=len([s for s in template.sections if s.content_type == "table"]),
                executive_summary=ai_summary,
                key_findings=key_findings,
                recommendations=recommendations,
                status="completed",
                error_message=None
            )
            
            self.generated_reports[report_id] = generated_report
            
            # Emit report generated event
            await self.event_manager.emit(
                EventTypes.REPORT_GENERATED,
                {
                    "report_id": report_id,
                    "template_id": template_id,
                    "requester_id": requester_id,
                    "formats": [fmt.value for fmt in output_formats]
                },
                "enterprise_reporting"
            )
            
            logger.info(f"Generated report: {report_id}")
            return report_id
            
        except Exception as e:
            logger.error(f"Failed to generate report: {e}")
            
            # Create failed report entry
            if 'report_id' in locals():
                self.generated_reports[report_id] = GeneratedReport(
                    report_id=report_id,
                    request_id=request_id if 'request_id' in locals() else "",
                    template_id=template_id,
                    title="Failed Report",
                    generated_by=requester_id,
                    generated_date=datetime.now(),
                    data_period=(datetime.now(), datetime.now()),
                    file_paths={},
                    size_bytes=0,
                    page_count=0,
                    chart_count=0,
                    table_count=0,
                    executive_summary="",
                    key_findings=[],
                    recommendations=[],
                    status="failed",
                    error_message=str(e)
                )
            
            raise
    
    async def _collect_report_data(self, template: ReportTemplate, 
                                 parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Collect data for report generation"""
        try:
            report_data = {
                "metadata": {
                    "template_name": template.name,
                    "generated_date": datetime.now(),
                    "parameters": parameters
                },
                "security_metrics": await self._get_security_metrics(parameters),
                "vulnerability_data": await self._get_vulnerability_data(parameters),
                "compliance_data": await self._get_compliance_data(parameters),
                "risk_assessment": await self._get_risk_assessment(parameters),
                "threat_intelligence": await self._get_threat_intelligence(parameters),
                "audit_logs": await self._get_audit_summary(parameters),
                "campaign_data": await self._get_campaign_data(parameters)
            }
            
            return report_data
            
        except Exception as e:
            logger.error(f"Failed to collect report data: {e}")
            return {"error": str(e)}
    
    async def _get_security_metrics(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Get security metrics for reporting"""
        # Mock implementation - would integrate with actual security systems
        return {
            "total_vulnerabilities": 245,
            "critical_vulnerabilities": 12,
            "high_vulnerabilities": 38,
            "medium_vulnerabilities": 95,
            "low_vulnerabilities": 100,
            "remediated_this_period": 67,
            "mean_time_to_remediation": 14.5,  # days
            "security_score": 78,
            "compliance_score": 85,
            "threat_level": "medium",
            "incidents_this_period": 3,
            "false_positive_rate": 12.3
        }
    
    async def _get_vulnerability_data(self, parameters: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Get vulnerability data for reporting"""
        # Mock implementation
        return [
            {
                "id": "CVE-2024-0001",
                "title": "SQL Injection in Login Form",
                "severity": "critical",
                "cvss_score": 9.1,
                "affected_systems": 3,
                "discovered_date": "2024-01-15",
                "status": "open",
                "remediation_effort": "medium"
            },
            {
                "id": "CVE-2024-0002", 
                "title": "Cross-Site Scripting in Dashboard",
                "severity": "high",
                "cvss_score": 7.8,
                "affected_systems": 5,
                "discovered_date": "2024-01-18",
                "status": "in_progress",
                "remediation_effort": "low"
            }
        ]
    
    async def _get_compliance_data(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Get compliance assessment data"""
        return {
            "frameworks_assessed": ["SOX", "PCI-DSS", "ISO 27001"],
            "overall_compliance_score": 87.5,
            "framework_scores": {
                "SOX": 92,
                "PCI-DSS": 85,
                "ISO 27001": 86
            },
            "control_gaps": 15,
            "high_priority_gaps": 3,
            "evidence_collected": 145,
            "audit_findings": 8
        }
    
    async def _get_risk_assessment(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Get risk assessment data"""
        return {
            "overall_risk_score": 6.2,
            "risk_categories": {
                "technical": 7.1,
                "operational": 5.8,
                "strategic": 4.9,
                "compliance": 6.8
            },
            "high_risk_assets": 12,
            "risk_trends": "improving",
            "residual_risk": 4.2,
            "risk_appetite": 5.0
        }
    
    async def _get_threat_intelligence(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Get threat intelligence data"""
        return {
            "active_threats": 23,
            "threat_actors": ["APT29", "Lazarus Group"],
            "attack_vectors": ["phishing", "malware", "social_engineering"],
            "industry_threats": 156,
            "geographic_threats": "High - Eastern Europe",
            "threat_level_trend": "increasing"
        }
    
    async def _get_audit_summary(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Get audit log summary"""
        return {
            "total_events": 12450,
            "security_events": 348,
            "failed_logins": 23,
            "privilege_escalations": 2,
            "data_access_events": 1250,
            "policy_violations": 5,
            "compliance_events": 89
        }
    
    async def _get_campaign_data(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Get security campaign data"""
        return {
            "active_campaigns": 3,
            "completed_campaigns": 12,
            "total_findings": 245,
            "campaigns_on_schedule": 2,
            "average_campaign_duration": 14.5,
            "team_utilization": 87.3
        }
    
    async def _render_report_content(self, template: ReportTemplate, 
                                   report_data: Dict[str, Any]) -> Dict[str, str]:
        """Render report content using templates"""
        rendered_sections = {}
        
        for section in template.sections:
            if section.content_type == "text":
                rendered_sections[section.section_id] = await self._render_text_content(
                    section, report_data, template.variables
                )
            elif section.content_type == "table":
                rendered_sections[section.section_id] = await self._render_table_content(
                    section, report_data
                )
        
        return rendered_sections
    
    async def _render_text_content(self, section: ReportSection, 
                                 report_data: Dict[str, Any],
                                 variables: Dict[str, Any]) -> str:
        """Render text content with AI enhancement"""
        try:
            if section.content == "executive_overview_template":
                return await self._generate_executive_overview(report_data)
            elif section.content == "technical_summary_template":
                return await self._generate_technical_summary(report_data)
            elif section.content == "compliance_overview_template":
                return await self._generate_compliance_overview(report_data)
            elif section.content == "strategic_recommendations":
                return await self._generate_strategic_recommendations(report_data)
            elif section.content == "technical_remediation":
                return await self._generate_technical_remediation(report_data)
            else:
                # Use Jinja2 template
                template_str = section.content
                template = Template(template_str)
                return template.render(**report_data, **variables)
        
        except Exception as e:
            logger.error(f"Failed to render text content: {e}")
            return f"Error rendering content: {str(e)}"
    
    async def _generate_executive_overview(self, report_data: Dict[str, Any]) -> str:
        """Generate AI-powered executive overview"""
        try:
            metrics = report_data.get("security_metrics", {})
            risk_data = report_data.get("risk_assessment", {})
            
            prompt = f"""
Generate an executive overview for a security assessment report with these metrics:

Security Metrics:
- Total Vulnerabilities: {metrics.get('total_vulnerabilities', 0)}
- Critical Vulnerabilities: {metrics.get('critical_vulnerabilities', 0)}
- Security Score: {metrics.get('security_score', 0)}/100
- Compliance Score: {metrics.get('compliance_score', 0)}/100

Risk Assessment:
- Overall Risk Score: {risk_data.get('overall_risk_score', 0)}/10
- High Risk Assets: {risk_data.get('high_risk_assets', 0)}
- Risk Trend: {risk_data.get('risk_trends', 'unknown')}

Create a concise executive summary that:
1. Highlights the current security posture
2. Identifies key risk areas
3. Provides high-level recommendations
4. Uses business language appropriate for executives

Keep it to 3-4 paragraphs maximum.
"""
            
            response = await self.ai_service.analyze(
                prompt,
                context="executive_reporting",
                provider_preference=["openai", "claude"]
            )
            
            return response.get("analysis", "Executive overview unavailable")
        
        except Exception as e:
            logger.error(f"Failed to generate executive overview: {e}")
            return "Executive overview generation failed"
    
    async def _generate_technical_summary(self, report_data: Dict[str, Any]) -> str:
        """Generate technical summary"""
        vuln_data = report_data.get("vulnerability_data", [])
        critical_vulns = [v for v in vuln_data if v.get("severity") == "critical"]
        
        summary = f"""
## Technical Assessment Summary

### Vulnerability Analysis
- Total vulnerabilities identified: {len(vuln_data)}
- Critical vulnerabilities: {len(critical_vulns)}
- Average CVSS score: {sum(v.get('cvss_score', 0) for v in vuln_data) / len(vuln_data) if vuln_data else 0:.1f}

### Critical Findings
"""
        
        for vuln in critical_vulns[:3]:  # Top 3 critical
            summary += f"- {vuln.get('title', 'Unknown')} (CVSS: {vuln.get('cvss_score', 0)})\n"
        
        return summary
    
    async def _generate_compliance_overview(self, report_data: Dict[str, Any]) -> str:
        """Generate compliance overview"""
        compliance_data = report_data.get("compliance_data", {})
        
        return f"""
## Compliance Assessment Overview

Overall compliance score: {compliance_data.get('overall_compliance_score', 0)}%

### Framework Assessment Results:
{chr(10).join(f"- {framework}: {score}%" for framework, score in compliance_data.get('framework_scores', {}).items())}

### Key Areas for Improvement:
- {compliance_data.get('control_gaps', 0)} control gaps identified
- {compliance_data.get('high_priority_gaps', 0)} high-priority remediation items
- {compliance_data.get('audit_findings', 0)} audit findings requiring attention
"""
    
    async def _generate_strategic_recommendations(self, report_data: Dict[str, Any]) -> str:
        """Generate strategic recommendations"""
        try:
            prompt = f"""
Based on this security assessment data, provide 3-5 strategic recommendations for executive leadership:

Security Metrics: {json.dumps(report_data.get('security_metrics', {}), indent=2)}
Risk Assessment: {json.dumps(report_data.get('risk_assessment', {}), indent=2)}
Compliance Data: {json.dumps(report_data.get('compliance_data', {}), indent=2)}

Focus on:
1. Business impact and risk reduction
2. Resource allocation and prioritization
3. Strategic security investments
4. Governance and process improvements

Format as numbered list with brief explanations.
"""
            
            response = await self.ai_service.analyze(
                prompt,
                context="strategic_recommendations",
                provider_preference=["openai", "claude"]
            )
            
            return response.get("analysis", "Strategic recommendations unavailable")
        
        except Exception as e:
            logger.error(f"Failed to generate strategic recommendations: {e}")
            return "Recommendation generation failed"
    
    async def _generate_technical_remediation(self, report_data: Dict[str, Any]) -> str:
        """Generate technical remediation guidance"""
        vuln_data = report_data.get("vulnerability_data", [])
        
        remediation = "## Technical Remediation Guide\n\n"
        
        for vuln in vuln_data[:5]:  # Top 5 vulnerabilities
            remediation += f"""
### {vuln.get('title', 'Unknown Vulnerability')}
- **Severity**: {vuln.get('severity', 'unknown').upper()}
- **CVSS Score**: {vuln.get('cvss_score', 0)}
- **Affected Systems**: {vuln.get('affected_systems', 0)}
- **Remediation Effort**: {vuln.get('remediation_effort', 'unknown')}

**Remediation Steps**:
1. Immediate patching required for critical systems
2. Implement input validation and sanitization
3. Deploy web application firewall rules
4. Conduct post-remediation testing

---
"""
        
        return remediation
    
    async def _render_table_content(self, section: ReportSection, 
                                  report_data: Dict[str, Any]) -> str:
        """Render table content"""
        if section.content == "vulnerability_details_table":
            vuln_data = report_data.get("vulnerability_data", [])
            return self._create_vulnerability_table(vuln_data)
        elif section.content == "control_findings_table":
            compliance_data = report_data.get("compliance_data", {})
            return self._create_compliance_table(compliance_data)
        
        return "Table content unavailable"
    
    def _create_vulnerability_table(self, vuln_data: List[Dict[str, Any]]) -> str:
        """Create HTML table for vulnerabilities"""
        if not vuln_data:
            return "<p>No vulnerabilities to display</p>"
        
        html = """
<table class="vulnerability-table">
<thead>
<tr>
<th>ID</th>
<th>Title</th>
<th>Severity</th>
<th>CVSS</th>
<th>Affected Systems</th>
<th>Status</th>
</tr>
</thead>
<tbody>
"""
        
        for vuln in vuln_data:
            severity_class = vuln.get('severity', 'low').lower()
            html += f"""
<tr class="severity-{severity_class}">
<td>{vuln.get('id', 'N/A')}</td>
<td>{vuln.get('title', 'Unknown')}</td>
<td>{vuln.get('severity', 'Unknown').upper()}</td>
<td>{vuln.get('cvss_score', 0)}</td>
<td>{vuln.get('affected_systems', 0)}</td>
<td>{vuln.get('status', 'Unknown')}</td>
</tr>
"""
        
        html += "</tbody></table>"
        return html
    
    def _create_compliance_table(self, compliance_data: Dict[str, Any]) -> str:
        """Create HTML table for compliance findings"""
        framework_scores = compliance_data.get("framework_scores", {})
        
        html = """
<table class="compliance-table">
<thead>
<tr>
<th>Framework</th>
<th>Score</th>
<th>Status</th>
</tr>
</thead>
<tbody>
"""
        
        for framework, score in framework_scores.items():
            status = "Compliant" if score >= 90 else "Partially Compliant" if score >= 70 else "Non-Compliant"
            status_class = status.lower().replace(" ", "-")
            
            html += f"""
<tr class="status-{status_class}">
<td>{framework}</td>
<td>{score}%</td>
<td>{status}</td>
</tr>
"""
        
        html += "</tbody></table>"
        return html
    
    async def _generate_charts(self, template: ReportTemplate, 
                             report_data: Dict[str, Any]) -> Dict[str, str]:
        """Generate charts and visualizations"""
        charts = {}
        
        for section in template.sections:
            if section.content_type == "chart":
                chart_path = await self._create_chart(section, report_data, template.target_audience)
                if chart_path:
                    charts[section.section_id] = chart_path
        
        return charts
    
    async def _create_chart(self, section: ReportSection, report_data: Dict[str, Any],
                          audience: ReportAudience) -> Optional[str]:
        """Create individual chart"""
        try:
            chart_file = self.charts_dir / f"{section.section_id}_{uuid.uuid4().hex[:8]}.png"
            
            if section.content == "security_metrics_dashboard":
                return await self._create_security_dashboard(report_data, chart_file, audience)
            elif section.content == "risk_matrix_chart":
                return await self._create_risk_matrix(report_data, chart_file, audience)
            elif section.content == "compliance_radar_chart":
                return await self._create_compliance_radar(report_data, chart_file, audience)
            
            return None
        
        except Exception as e:
            logger.error(f"Failed to create chart: {e}")
            return None
    
    async def _create_security_dashboard(self, report_data: Dict[str, Any], 
                                       file_path: Path, audience: ReportAudience) -> str:
        """Create security metrics dashboard"""
        metrics = report_data.get("security_metrics", {})
        colors = self.color_schemes.get(audience, self.color_schemes[ReportAudience.TECHNICAL])
        
        # Create subplots
        fig = make_subplots(
            rows=2, cols=2,
            subplot_titles=("Vulnerability Breakdown", "Security Score", 
                           "Remediation Progress", "Compliance Score"),
            specs=[[{"type": "pie"}, {"type": "indicator"}],
                   [{"type": "bar"}, {"type": "indicator"}]]
        )
        
        # Vulnerability pie chart
        vuln_labels = ["Critical", "High", "Medium", "Low"]
        vuln_values = [
            metrics.get("critical_vulnerabilities", 0),
            metrics.get("high_vulnerabilities", 0),
            metrics.get("medium_vulnerabilities", 0),
            metrics.get("low_vulnerabilities", 0)
        ]
        
        fig.add_trace(
            go.Pie(labels=vuln_labels, values=vuln_values, marker_colors=colors),
            row=1, col=1
        )
        
        # Security score gauge
        fig.add_trace(
            go.Indicator(
                mode="gauge+number",
                value=metrics.get("security_score", 0),
                domain={'x': [0, 1], 'y': [0, 1]},
                title={'text': "Security Score"},
                gauge={'axis': {'range': [None, 100]},
                       'bar': {'color': colors[0]},
                       'steps': [{'range': [0, 50], 'color': "lightgray"},
                                {'range': [50, 80], 'color': "gray"}],
                       'threshold': {'line': {'color': "red", 'width': 4},
                                   'thickness': 0.75, 'value': 90}}
            ),
            row=1, col=2
        )
        
        # Remediation progress bar
        remediation_data = ["Remediated", "Remaining"]
        remediated = metrics.get("remediated_this_period", 0)
        total = metrics.get("total_vulnerabilities", 0)
        remaining = total - remediated
        
        fig.add_trace(
            go.Bar(x=remediation_data, y=[remediated, remaining], marker_color=colors[:2]),
            row=2, col=1
        )
        
        # Compliance score gauge
        fig.add_trace(
            go.Indicator(
                mode="gauge+number",
                value=metrics.get("compliance_score", 0),
                domain={'x': [0, 1], 'y': [0, 1]},
                title={'text': "Compliance Score"},
                gauge={'axis': {'range': [None, 100]},
                       'bar': {'color': colors[1]},
                       'steps': [{'range': [0, 70], 'color': "lightgray"},
                                {'range': [70, 90], 'color': "gray"}],
                       'threshold': {'line': {'color': "green", 'width': 4},
                                   'thickness': 0.75, 'value': 95}}
            ),
            row=2, col=2
        )
        
        fig.update_layout(
            title_text="Security Metrics Dashboard",
            showlegend=False,
            height=600
        )
        
        fig.write_image(str(file_path))
        return str(file_path)
    
    async def _create_risk_matrix(self, report_data: Dict[str, Any], 
                                file_path: Path, audience: ReportAudience) -> str:
        """Create risk assessment matrix"""
        # Mock risk data - would come from actual risk assessment
        risk_items = [
            {"name": "SQL Injection", "likelihood": 8, "impact": 9},
            {"name": "XSS Vulnerability", "likelihood": 6, "impact": 7},
            {"name": "Weak Authentication", "likelihood": 7, "impact": 8},
            {"name": "Data Exposure", "likelihood": 5, "impact": 9},
            {"name": "Insider Threat", "likelihood": 4, "impact": 8}
        ]
        
        fig, ax = plt.subplots(figsize=(10, 8))
        
        # Create scatter plot
        for item in risk_items:
            ax.scatter(item["likelihood"], item["impact"], s=200, alpha=0.7)
            ax.annotate(item["name"], (item["likelihood"], item["impact"]), 
                       xytext=(5, 5), textcoords='offset points')
        
        # Color regions
        ax.axhspan(0, 3.33, 0, 3.33, alpha=0.3, color='green', label='Low Risk')
        ax.axhspan(3.33, 6.67, 3.33, 6.67, alpha=0.3, color='yellow', label='Medium Risk')
        ax.axhspan(6.67, 10, 6.67, 10, alpha=0.3, color='red', label='High Risk')
        
        ax.set_xlabel('Likelihood')
        ax.set_ylabel('Impact')
        ax.set_title('Risk Assessment Matrix')
        ax.set_xlim(0, 10)
        ax.set_ylim(0, 10)
        ax.grid(True, alpha=0.3)
        ax.legend()
        
        plt.tight_layout()
        plt.savefig(file_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        return str(file_path)
    
    async def _create_compliance_radar(self, report_data: Dict[str, Any], 
                                     file_path: Path, audience: ReportAudience) -> str:
        """Create compliance radar chart"""
        compliance_data = report_data.get("compliance_data", {})
        framework_scores = compliance_data.get("framework_scores", {})
        
        if not framework_scores:
            # Mock data
            framework_scores = {"SOX": 92, "PCI-DSS": 85, "ISO 27001": 86}
        
        categories = list(framework_scores.keys())
        values = list(framework_scores.values())
        
        # Close the radar chart
        values += values[:1]
        categories += categories[:1]
        
        fig = go.Figure()
        
        fig.add_trace(go.Scatterpolar(
            r=values,
            theta=categories,
            fill='toself',
            name='Compliance Score'
        ))
        
        fig.update_layout(
            polar=dict(
                radialaxis=dict(
                    visible=True,
                    range=[0, 100]
                )),
            showlegend=True,
            title="Compliance Framework Assessment"
        )
        
        fig.write_image(str(file_path))
        return str(file_path)
    
    async def _export_report(self, report_id: str, template: ReportTemplate,
                           content: Dict[str, str], charts: Dict[str, str],
                           format_type: ReportFormat) -> str:
        """Export report in specified format"""
        file_name = f"{report_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        if format_type == ReportFormat.HTML:
            return await self._export_html(file_name, template, content, charts)
        elif format_type == ReportFormat.PDF:
            return await self._export_pdf(file_name, template, content, charts)
        elif format_type == ReportFormat.JSON:
            return await self._export_json(file_name, template, content, charts)
        else:
            raise ValueError(f"Unsupported format: {format_type}")
    
    async def _export_html(self, file_name: str, template: ReportTemplate,
                         content: Dict[str, str], charts: Dict[str, str]) -> str:
        """Export report as HTML"""
        html_file = self.reports_dir / f"{file_name}.html"
        
        html_content = f"""
<!DOCTYPE html>
<html>
<head>
    <title>{template.name}</title>
    <style>
        body {{ font-family: {template.styling.get('font_family', 'Arial')}; margin: 40px; }}
        .header {{ text-align: center; border-bottom: 2px solid #333; padding-bottom: 20px; }}
        .section {{ margin: 30px 0; }}
        .chart {{ text-align: center; margin: 20px 0; }}
        .vulnerability-table, .compliance-table {{ width: 100%; border-collapse: collapse; }}
        .vulnerability-table th, .compliance-table th {{ background-color: #f4f4f4; padding: 10px; border: 1px solid #ddd; }}
        .vulnerability-table td, .compliance-table td {{ padding: 8px; border: 1px solid #ddd; }}
        .severity-critical {{ background-color: #ffebee; }}
        .severity-high {{ background-color: #fff3e0; }}
        .severity-medium {{ background-color: #f3e5f5; }}
        .severity-low {{ background-color: #e8f5e8; }}
    </style>
</head>
<body>
    <div class="header">
        <h1>{template.name}</h1>
        <p>Generated on {datetime.now().strftime('%B %d, %Y')}</p>
    </div>
"""
        
        # Add sections in order
        for section in sorted(template.sections, key=lambda s: s.order):
            html_content += f'<div class="section">\n<h2>{section.title}</h2>\n'
            
            if section.section_id in content:
                html_content += content[section.section_id]
            
            if section.section_id in charts:
                chart_path = Path(charts[section.section_id])
                with open(chart_path, "rb") as img_file:
                    img_data = base64.b64encode(img_file.read()).decode()
                html_content += f'<div class="chart"><img src="data:image/png;base64,{img_data}" alt="{section.title}"></div>'
            
            html_content += '</div>\n'
        
        html_content += """
</body>
</html>
"""
        
        with open(html_file, "w") as f:
            f.write(html_content)
        
        return str(html_file)
    
    async def _export_pdf(self, file_name: str, template: ReportTemplate,
                         content: Dict[str, str], charts: Dict[str, str]) -> str:
        """Export report as PDF (simplified implementation)"""
        # In a real implementation, you would use libraries like reportlab or weasyprint
        # For now, we'll create a text-based PDF placeholder
        pdf_file = self.reports_dir / f"{file_name}.pdf"
        
        # Mock PDF creation - would use actual PDF library
        with open(pdf_file, "w") as f:
            f.write(f"PDF Report: {template.name}\n")
            f.write(f"Generated: {datetime.now()}\n\n")
            
            for section in sorted(template.sections, key=lambda s: s.order):
                f.write(f"{section.title}\n")
                f.write("=" * len(section.title) + "\n\n")
                
                if section.section_id in content:
                    # Strip HTML tags for text output
                    import re
                    clean_content = re.sub('<[^<]+?>', '', content[section.section_id])
                    f.write(clean_content + "\n\n")
        
        return str(pdf_file)
    
    async def _export_json(self, file_name: str, template: ReportTemplate,
                         content: Dict[str, str], charts: Dict[str, str]) -> str:
        """Export report as JSON"""
        json_file = self.reports_dir / f"{file_name}.json"
        
        report_json = {
            "template": asdict(template),
            "content": content,
            "charts": charts,
            "generated_date": datetime.now().isoformat()
        }
        
        with open(json_file, "w") as f:
            json.dump(report_json, f, indent=2, default=str)
        
        return str(json_file)
    
    def _estimate_page_count(self, content: Dict[str, str]) -> int:
        """Estimate page count based on content"""
        total_chars = sum(len(text) for text in content.values())
        # Rough estimate: 2500 characters per page
        return max(1, total_chars // 2500)
    
    async def _generate_ai_insights(self, report_data: Dict[str, Any], 
                                  audience: ReportAudience) -> str:
        """Generate AI-powered insights for the report"""
        try:
            context = "executive_insights" if audience == ReportAudience.EXECUTIVE else "technical_insights"
            
            prompt = f"""
Analyze this security assessment data and provide key insights for {audience.value} audience:

{json.dumps(report_data, indent=2, default=str)[:2000]}...

Provide 3-5 key insights that highlight:
1. Most critical security issues
2. Business impact assessment
3. Recommended priorities
4. Overall security posture evaluation

Keep insights concise and actionable.
"""
            
            response = await self.ai_service.analyze(
                prompt,
                context=context,
                provider_preference=["openai", "claude"]
            )
            
            return response.get("analysis", "AI insights unavailable")
        
        except Exception as e:
            logger.error(f"Failed to generate AI insights: {e}")
            return "AI insights generation failed"
    
    async def _extract_key_findings(self, report_data: Dict[str, Any]) -> List[str]:
        """Extract key findings from report data"""
        findings = []
        
        metrics = report_data.get("security_metrics", {})
        vuln_data = report_data.get("vulnerability_data", [])
        
        if metrics.get("critical_vulnerabilities", 0) > 0:
            findings.append(f"{metrics['critical_vulnerabilities']} critical vulnerabilities identified")
        
        if metrics.get("security_score", 0) < 70:
            findings.append(f"Security score below acceptable threshold: {metrics['security_score']}/100")
        
        critical_vulns = [v for v in vuln_data if v.get("severity") == "critical"]
        if critical_vulns:
            findings.append(f"Critical SQL injection vulnerability in login system (CVSS: {critical_vulns[0].get('cvss_score', 0)})")
        
        if len(findings) == 0:
            findings.append("No critical security issues identified")
        
        return findings[:5]  # Top 5 findings
    
    async def _generate_recommendations(self, report_data: Dict[str, Any], 
                                      report_type: ReportType) -> List[str]:
        """Generate recommendations based on report data"""
        recommendations = []
        
        metrics = report_data.get("security_metrics", {})
        
        if metrics.get("critical_vulnerabilities", 0) > 0:
            recommendations.append("Immediately patch critical vulnerabilities")
        
        if metrics.get("security_score", 0) < 80:
            recommendations.append("Implement comprehensive security awareness training")
        
        if metrics.get("mean_time_to_remediation", 0) > 30:
            recommendations.append("Improve vulnerability remediation processes")
        
        recommendations.append("Regular security assessments and continuous monitoring")
        recommendations.append("Update incident response procedures")
        
        return recommendations[:5]
    
    def get_available_templates(self) -> List[Dict[str, Any]]:
        """Get list of available report templates"""
        templates = []
        
        for template in self.report_templates.values():
            if template.is_active:
                templates.append({
                    "template_id": template.template_id,
                    "name": template.name,
                    "description": template.description,
                    "report_type": template.report_type.value,
                    "target_audience": template.target_audience.value,
                    "section_count": len(template.sections),
                    "created_date": template.created_date.isoformat()
                })
        
        return templates
    
    def get_generated_reports(self, requester_id: Optional[str] = None) -> List[Dict[str, Any]]:
        """Get list of generated reports"""
        reports = []
        
        for report in self.generated_reports.values():
            if requester_id is None or report.generated_by == requester_id:
                reports.append({
                    "report_id": report.report_id,
                    "title": report.title,
                    "generated_date": report.generated_date.isoformat(),
                    "status": report.status,
                    "formats": list(report.file_paths.keys()),
                    "size_mb": round(report.size_bytes / 1024 / 1024, 2),
                    "page_count": report.page_count
                })
        
        return sorted(reports, key=lambda x: x["generated_date"], reverse=True)
    
    def get_reporting_metrics(self) -> Dict[str, Any]:
        """Get enterprise reporting system metrics"""
        total_reports = len(self.generated_reports)
        successful_reports = len([r for r in self.generated_reports.values() if r.status == "completed"])
        failed_reports = len([r for r in self.generated_reports.values() if r.status == "failed"])
        
        # Template usage
        template_usage = {}
        for report in self.generated_reports.values():
            template_id = report.template_id
            template_usage[template_id] = template_usage.get(template_id, 0) + 1
        
        return {
            "total_reports": total_reports,
            "successful_reports": successful_reports,
            "failed_reports": failed_reports,
            "success_rate": (successful_reports / total_reports * 100) if total_reports > 0 else 0,
            "active_templates": len([t for t in self.report_templates.values() if t.is_active]),
            "template_usage": template_usage,
            "total_file_size_mb": sum(r.size_bytes for r in self.generated_reports.values()) / 1024 / 1024,
            "average_generation_time": 45.2  # Would track actual generation times
        }