#!/usr/bin/env python3
"""
System Health Checker for Hybrid Execution Pipeline
Comprehensive health monitoring and diagnostics
"""

import asyncio
import logging
import time
import psutil
import json
import shutil
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass, field
from enum import Enum
from pathlib import Path

from .environment_detector import environment_detector, get_capabilities
from .hybrid_execution_engine import get_hybrid_engine, ExecutionResult
from .self_healing_manager import get_self_healing_manager, InstallationStatus
from .docker_tool_manager import DockerToolManager
from .native_tool_manager import NativeToolManager
from .simulation_manager import SimulationManager

logger = logging.getLogger(__name__)

class HealthStatus(Enum):
    """Health status levels"""
    HEALTHY = "healthy"
    WARNING = "warning"
    CRITICAL = "critical"
    UNKNOWN = "unknown"
    DEGRADED = "degraded"

class ComponentType(Enum):
    """System component types"""
    ENVIRONMENT = "environment"
    EXECUTION_ENGINE = "execution_engine"
    TOOL_MANAGER = "tool_manager"
    SELF_HEALING = "self_healing"
    DOCKER = "docker"
    NATIVE = "native"
    SIMULATION = "simulation"
    STORAGE = "storage"
    NETWORK = "network"
    SYSTEM_RESOURCES = "system_resources"

@dataclass
class HealthCheck:
    """Individual health check result"""
    component: ComponentType
    status: HealthStatus
    message: str
    timestamp: float
    details: Dict[str, Any] = field(default_factory=dict)
    recommendations: List[str] = field(default_factory=list)
    execution_time: float = 0.0

@dataclass
class SystemHealth:
    """Complete system health assessment"""
    overall_status: HealthStatus
    timestamp: float
    checks: List[HealthCheck]
    summary: Dict[str, Any]
    recommendations: List[str]
    performance_metrics: Dict[str, Any]

class HealthChecker:
    """Comprehensive system health monitoring"""
    
    def __init__(self):
        self.environment = environment_detector.environment
        self.capabilities = get_capabilities()
        
        # Health check configuration
        self.check_timeout = 30.0  # seconds
        self.critical_thresholds = {
            'cpu_usage': 90.0,
            'memory_usage': 95.0,
            'disk_usage': 95.0,
            'response_time': 10.0
        }
        
        self.warning_thresholds = {
            'cpu_usage': 80.0,
            'memory_usage': 85.0,
            'disk_usage': 85.0,
            'response_time': 5.0
        }
        
        # Component availability cache
        self.component_cache = {}
        self.cache_expiry = 60.0  # seconds
        
        logger.info("🏥 Health Checker initialized")
        logger.info(f"🌍 Environment: {self.environment.value}")
    
    async def perform_comprehensive_health_check(self) -> SystemHealth:
        """Perform complete system health assessment"""
        
        start_time = time.time()
        logger.info("🏥 Starting comprehensive health check...")
        
        checks = []
        
        # Environment check
        env_check = await self._check_environment()
        checks.append(env_check)
        
        # System resources check
        resources_check = await self._check_system_resources()
        checks.append(resources_check)
        
        # Storage check
        storage_check = await self._check_storage()
        checks.append(storage_check)
        
        # Network connectivity check
        network_check = await self._check_network()
        checks.append(network_check)
        
        # Execution engine check
        engine_check = await self._check_execution_engine()
        checks.append(engine_check)
        
        # Tool managers check
        tool_checks = await self._check_tool_managers()
        checks.extend(tool_checks)
        
        # Self-healing system check
        healing_check = await self._check_self_healing()
        checks.append(healing_check)
        
        # Docker integration check
        docker_check = await self._check_docker_integration()
        checks.append(docker_check)
        
        # Calculate overall status
        overall_status = self._calculate_overall_status(checks)
        
        # Generate summary
        summary = self._generate_summary(checks)
        
        # Generate recommendations
        recommendations = self._generate_recommendations(checks)
        
        # Collect performance metrics
        performance_metrics = await self._collect_performance_metrics()
        
        health = SystemHealth(
            overall_status=overall_status,
            timestamp=time.time(),
            checks=checks,
            summary=summary,
            recommendations=recommendations,
            performance_metrics=performance_metrics
        )
        
        execution_time = time.time() - start_time
        logger.info(f"✅ Health check completed in {execution_time:.2f}s - Status: {overall_status.value.upper()}")
        
        return health
    
    async def _check_environment(self) -> HealthCheck:
        """Check environment detection and capabilities"""
        
        start_time = time.time()
        
        try:
            # Test environment detection
            env_info = environment_detector.get_environment_info()
            
            # Check capabilities
            missing_capabilities = []
            critical_capabilities = ['python', 'subprocess']
            
            for cap in critical_capabilities:
                if not self.capabilities.get(cap, False):
                    missing_capabilities.append(cap)
            
            if missing_capabilities:
                return HealthCheck(
                    component=ComponentType.ENVIRONMENT,
                    status=HealthStatus.CRITICAL,
                    message=f"Critical capabilities missing: {', '.join(missing_capabilities)}",
                    timestamp=time.time(),
                    details={
                        'environment': env_info,
                        'missing_capabilities': missing_capabilities
                    },
                    recommendations=[
                        "Install missing system dependencies",
                        "Verify system configuration"
                    ],
                    execution_time=time.time() - start_time
                )
            
            # Check for warnings
            warnings = []
            optional_capabilities = ['docker', 'wsl', 'git']
            
            for cap in optional_capabilities:
                if not self.capabilities.get(cap, False):
                    warnings.append(f"{cap} not available")
            
            status = HealthStatus.WARNING if warnings else HealthStatus.HEALTHY
            message = f"Environment check passed"
            if warnings:
                message += f" with warnings: {', '.join(warnings)}"
            
            return HealthCheck(
                component=ComponentType.ENVIRONMENT,
                status=status,
                message=message,
                timestamp=time.time(),
                details={
                    'environment': env_info,
                    'capabilities': self.capabilities,
                    'warnings': warnings
                },
                execution_time=time.time() - start_time
            )
            
        except Exception as e:
            return HealthCheck(
                component=ComponentType.ENVIRONMENT,
                status=HealthStatus.CRITICAL,
                message=f"Environment check failed: {str(e)}",
                timestamp=time.time(),
                details={'error': str(e)},
                recommendations=[
                    "Check system configuration",
                    "Verify Python installation",
                    "Restart application"
                ],
                execution_time=time.time() - start_time
            )
    
    async def _check_system_resources(self) -> HealthCheck:
        """Check system resource usage"""
        
        start_time = time.time()
        
        try:
            # CPU usage
            cpu_percent = psutil.cpu_percent(interval=1.0)
            
            # Memory usage
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            
            # Disk usage (if available)
            disk_percent = 0.0
            try:
                disk_usage = psutil.disk_usage('/')
                disk_percent = (disk_usage.used / disk_usage.total) * 100
            except:
                pass
            
            # Determine status
            status = HealthStatus.HEALTHY
            issues = []
            
            if cpu_percent >= self.critical_thresholds['cpu_usage']:
                status = HealthStatus.CRITICAL
                issues.append(f"Critical CPU usage: {cpu_percent:.1f}%")
            elif cpu_percent >= self.warning_thresholds['cpu_usage']:
                status = HealthStatus.WARNING
                issues.append(f"High CPU usage: {cpu_percent:.1f}%")
            
            if memory_percent >= self.critical_thresholds['memory_usage']:
                status = HealthStatus.CRITICAL
                issues.append(f"Critical memory usage: {memory_percent:.1f}%")
            elif memory_percent >= self.warning_thresholds['memory_usage']:
                status = HealthStatus.WARNING
                issues.append(f"High memory usage: {memory_percent:.1f}%")
            
            if disk_percent >= self.critical_thresholds['disk_usage']:
                status = HealthStatus.CRITICAL
                issues.append(f"Critical disk usage: {disk_percent:.1f}%")
            elif disk_percent >= self.warning_thresholds['disk_usage']:
                status = HealthStatus.WARNING
                issues.append(f"High disk usage: {disk_percent:.1f}%")
            
            message = "System resources healthy"
            if issues:
                message = f"Resource issues detected: {', '.join(issues)}"
            
            recommendations = []
            if status != HealthStatus.HEALTHY:
                recommendations.extend([
                    "Monitor system resource usage",
                    "Close unnecessary applications",
                    "Consider reducing concurrent operations"
                ])
            
            return HealthCheck(
                component=ComponentType.SYSTEM_RESOURCES,
                status=status,
                message=message,
                timestamp=time.time(),
                details={
                    'cpu_percent': cpu_percent,
                    'memory_percent': memory_percent,
                    'disk_percent': disk_percent,
                    'memory_total_gb': memory.total / (1024**3),
                    'memory_available_gb': memory.available / (1024**3)
                },
                recommendations=recommendations,
                execution_time=time.time() - start_time
            )
            
        except Exception as e:
            return HealthCheck(
                component=ComponentType.SYSTEM_RESOURCES,
                status=HealthStatus.UNKNOWN,
                message=f"Resource check failed: {str(e)}",
                timestamp=time.time(),
                details={'error': str(e)},
                execution_time=time.time() - start_time
            )
    
    async def _check_storage(self) -> HealthCheck:
        """Check storage and file system health"""
        
        start_time = time.time()
        
        try:
            # Check critical directories
            critical_paths = [
                Path.home() / ".nexusscan",
                Path.home() / ".local" / "bin",
                Path("/tmp") if Path("/tmp").exists() else Path.cwd() / "temp"
            ]
            
            issues = []
            for path in critical_paths:
                try:
                    path.mkdir(parents=True, exist_ok=True)
                    
                    # Test write access
                    test_file = path / "health_check_test.tmp"
                    test_file.write_text("test")
                    test_file.unlink()
                    
                except Exception as e:
                    issues.append(f"Cannot write to {path}: {e}")
            
            # Check available space
            try:
                statvfs = shutil.disk_usage(Path.home())
                free_space_gb = statvfs.free / (1024**3)
                
                if free_space_gb < 1.0:  # Less than 1GB
                    issues.append(f"Low disk space: {free_space_gb:.1f}GB free")
                    
            except Exception as e:
                issues.append(f"Cannot check disk space: {e}")
            
            status = HealthStatus.CRITICAL if issues else HealthStatus.HEALTHY
            message = "Storage healthy" if not issues else f"Storage issues: {', '.join(issues)}"
            
            return HealthCheck(
                component=ComponentType.STORAGE,
                status=status,
                message=message,
                timestamp=time.time(),
                details={
                    'critical_paths': [str(p) for p in critical_paths],
                    'issues': issues
                },
                recommendations=[
                    "Free up disk space",
                    "Check directory permissions"
                ] if issues else [],
                execution_time=time.time() - start_time
            )
            
        except Exception as e:
            return HealthCheck(
                component=ComponentType.STORAGE,
                status=HealthStatus.UNKNOWN,
                message=f"Storage check failed: {str(e)}",
                timestamp=time.time(),
                details={'error': str(e)},
                execution_time=time.time() - start_time
            )
    
    async def _check_network(self) -> HealthCheck:
        """Check network connectivity"""
        
        start_time = time.time()
        
        try:
            # Test basic connectivity
            import socket
            
            test_hosts = [
                ("*******", 53),      # Google DNS
                ("*******", 53),      # Cloudflare DNS
            ]
            
            connectivity_results = []
            for host, port in test_hosts:
                try:
                    sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                    sock.settimeout(5.0)
                    result = sock.connect_ex((host, port))
                    sock.close()
                    
                    connectivity_results.append({
                        'host': host,
                        'port': port,
                        'success': result == 0
                    })
                except Exception as e:
                    connectivity_results.append({
                        'host': host,
                        'port': port,
                        'success': False,
                        'error': str(e)
                    })
            
            successful_connections = sum(1 for r in connectivity_results if r['success'])
            
            if successful_connections == 0:
                status = HealthStatus.CRITICAL
                message = "No network connectivity detected"
            elif successful_connections < len(test_hosts):
                status = HealthStatus.WARNING
                message = f"Limited network connectivity: {successful_connections}/{len(test_hosts)} hosts reachable"
            else:
                status = HealthStatus.HEALTHY
                message = "Network connectivity healthy"
            
            return HealthCheck(
                component=ComponentType.NETWORK,
                status=status,
                message=message,
                timestamp=time.time(),
                details={
                    'connectivity_tests': connectivity_results,
                    'successful_connections': successful_connections
                },
                recommendations=[
                    "Check internet connection",
                    "Verify firewall settings",
                    "Check DNS configuration"
                ] if status != HealthStatus.HEALTHY else [],
                execution_time=time.time() - start_time
            )
            
        except Exception as e:
            return HealthCheck(
                component=ComponentType.NETWORK,
                status=HealthStatus.UNKNOWN,
                message=f"Network check failed: {str(e)}",
                timestamp=time.time(),
                details={'error': str(e)},
                execution_time=time.time() - start_time
            )
    
    async def _check_execution_engine(self) -> HealthCheck:
        """Check hybrid execution engine health"""
        
        start_time = time.time()
        
        try:
            # Test hybrid engine initialization
            engine = await get_hybrid_engine()
            
            if not engine:
                return HealthCheck(
                    component=ComponentType.EXECUTION_ENGINE,
                    status=HealthStatus.CRITICAL,
                    message="Hybrid execution engine not available",
                    timestamp=time.time(),
                    recommendations=[
                        "Check hybrid engine configuration",
                        "Verify component dependencies"
                    ],
                    execution_time=time.time() - start_time
                )
            
            # Test engine capabilities
            available_methods = []
            if engine.docker_manager:
                available_methods.append("docker")
            if engine.native_manager:
                available_methods.append("native")
            if engine.wsl_manager:
                available_methods.append("wsl")
            if engine.simulation_manager:
                available_methods.append("simulation")
            
            if not available_methods:
                status = HealthStatus.CRITICAL
                message = "No execution methods available"
            elif len(available_methods) == 1 and "simulation" in available_methods:
                status = HealthStatus.WARNING
                message = "Only simulation execution available"
            else:
                status = HealthStatus.HEALTHY
                message = f"Execution engine healthy: {', '.join(available_methods)} methods available"
            
            return HealthCheck(
                component=ComponentType.EXECUTION_ENGINE,
                status=status,
                message=message,
                timestamp=time.time(),
                details={
                    'available_methods': available_methods,
                    'execution_priority': getattr(engine, 'execution_priority', [])
                },
                recommendations=[
                    "Enable additional execution methods",
                    "Configure Docker for containerized execution",
                    "Set up WSL for Linux tools"
                ] if status != HealthStatus.HEALTHY else [],
                execution_time=time.time() - start_time
            )
            
        except Exception as e:
            return HealthCheck(
                component=ComponentType.EXECUTION_ENGINE,
                status=HealthStatus.CRITICAL,
                message=f"Execution engine check failed: {str(e)}",
                timestamp=time.time(),
                details={'error': str(e)},
                recommendations=[
                    "Check execution engine configuration",
                    "Verify system dependencies",
                    "Restart application"
                ],
                execution_time=time.time() - start_time
            )
    
    async def _check_tool_managers(self) -> List[HealthCheck]:
        """Check all tool managers"""
        
        checks = []
        
        # Check native tool manager
        try:
            native_manager = NativeToolManager()
            await native_manager.initialize()
            
            available_tools = native_manager.get_available_tools()
            
            status = HealthStatus.HEALTHY if available_tools else HealthStatus.WARNING
            message = f"Native manager: {len(available_tools)} tools available"
            
            checks.append(HealthCheck(
                component=ComponentType.NATIVE,
                status=status,
                message=message,
                timestamp=time.time(),
                details={
                    'available_tools': available_tools,
                    'tool_count': len(available_tools)
                }
            ))
            
        except Exception as e:
            checks.append(HealthCheck(
                component=ComponentType.NATIVE,
                status=HealthStatus.CRITICAL,
                message=f"Native manager failed: {str(e)}",
                timestamp=time.time(),
                details={'error': str(e)}
            ))
        
        # Check simulation manager
        try:
            sim_manager = SimulationManager()
            supported_tools = sim_manager.get_supported_tools()
            
            checks.append(HealthCheck(
                component=ComponentType.SIMULATION,
                status=HealthStatus.HEALTHY,
                message=f"Simulation manager: {len(supported_tools)} tools supported",
                timestamp=time.time(),
                details={
                    'supported_tools': supported_tools,
                    'tool_count': len(supported_tools)
                }
            ))
            
        except Exception as e:
            checks.append(HealthCheck(
                component=ComponentType.SIMULATION,
                status=HealthStatus.WARNING,
                message=f"Simulation manager failed: {str(e)}",
                timestamp=time.time(),
                details={'error': str(e)}
            ))
        
        return checks
    
    async def _check_self_healing(self) -> HealthCheck:
        """Check self-healing system"""
        
        start_time = time.time()
        
        try:
            healing_manager = await get_self_healing_manager()
            
            if not healing_manager:
                return HealthCheck(
                    component=ComponentType.SELF_HEALING,
                    status=HealthStatus.WARNING,
                    message="Self-healing manager not available",
                    timestamp=time.time(),
                    execution_time=time.time() - start_time
                )
            
            # Check package managers
            package_managers = healing_manager.package_managers
            
            # Perform health check
            health_report = await healing_manager.system_health_check()
            
            missing_tools = health_report.get('missing_tools', [])
            failed_tools = health_report.get('failed_tools', [])
            
            if failed_tools:
                status = HealthStatus.WARNING
                message = f"Self-healing active: {len(failed_tools)} failed installations"
            elif missing_tools:
                status = HealthStatus.WARNING
                message = f"Self-healing ready: {len(missing_tools)} tools available for installation"
            else:
                status = HealthStatus.HEALTHY
                message = "Self-healing system healthy"
            
            return HealthCheck(
                component=ComponentType.SELF_HEALING,
                status=status,
                message=message,
                timestamp=time.time(),
                details={
                    'package_managers': list(package_managers.keys()),
                    'missing_tools': missing_tools[:5],  # First 5
                    'failed_tools': failed_tools,
                    'health_report': health_report
                },
                recommendations=[
                    "Run tool installation for missing tools",
                    "Clear failed installation cache if needed"
                ] if missing_tools or failed_tools else [],
                execution_time=time.time() - start_time
            )
            
        except Exception as e:
            return HealthCheck(
                component=ComponentType.SELF_HEALING,
                status=HealthStatus.UNKNOWN,
                message=f"Self-healing check failed: {str(e)}",
                timestamp=time.time(),
                details={'error': str(e)},
                execution_time=time.time() - start_time
            )
    
    async def _check_docker_integration(self) -> HealthCheck:
        """Check Docker integration health"""
        
        start_time = time.time()
        
        try:
            docker_manager = DockerToolManager()
            await docker_manager.initialize()
            
            if not docker_manager.docker_available:
                return HealthCheck(
                    component=ComponentType.DOCKER,
                    status=HealthStatus.WARNING,
                    message="Docker not available",
                    timestamp=time.time(),
                    details={'docker_available': False},
                    recommendations=[
                        "Install Docker Desktop",
                        "Start Docker daemon",
                        "Check Docker installation"
                    ],
                    execution_time=time.time() - start_time
                )
            
            # Check available tools and services
            available_tools = docker_manager.get_available_tools()
            service_status = docker_manager.get_service_status()
            
            # Get Docker stats
            docker_stats = await docker_manager.get_docker_stats()
            
            active_services = sum(1 for status in service_status.values() if status)
            
            if available_tools and active_services > 0:
                status = HealthStatus.HEALTHY
                message = f"Docker integration healthy: {len(available_tools)} tools, {active_services} services"
            elif available_tools:
                status = HealthStatus.WARNING
                message = f"Docker integration partial: {len(available_tools)} tools, no active services"
            else:
                status = HealthStatus.WARNING
                message = "Docker available but no tools configured"
            
            return HealthCheck(
                component=ComponentType.DOCKER,
                status=status,
                message=message,
                timestamp=time.time(),
                details={
                    'docker_available': True,
                    'available_tools': available_tools,
                    'service_status': service_status,
                    'active_services': active_services,
                    'docker_stats': docker_stats if isinstance(docker_stats, dict) else {}
                },
                recommendations=[
                    "Start Docker services for tools",
                    "Pull required Docker images",
                    "Configure tool services"
                ] if status != HealthStatus.HEALTHY else [],
                execution_time=time.time() - start_time
            )
            
        except Exception as e:
            return HealthCheck(
                component=ComponentType.DOCKER,
                status=HealthStatus.UNKNOWN,
                message=f"Docker check failed: {str(e)}",
                timestamp=time.time(),
                details={'error': str(e)},
                execution_time=time.time() - start_time
            )
    
    def _calculate_overall_status(self, checks: List[HealthCheck]) -> HealthStatus:
        """Calculate overall system health status"""
        
        if not checks:
            return HealthStatus.UNKNOWN
        
        # Count status types
        status_counts = {status: 0 for status in HealthStatus}
        for check in checks:
            status_counts[check.status] += 1
        
        # Determine overall status
        if status_counts[HealthStatus.CRITICAL] > 0:
            return HealthStatus.CRITICAL
        elif status_counts[HealthStatus.WARNING] > status_counts[HealthStatus.HEALTHY]:
            return HealthStatus.DEGRADED
        elif status_counts[HealthStatus.WARNING] > 0:
            return HealthStatus.WARNING
        elif status_counts[HealthStatus.HEALTHY] > 0:
            return HealthStatus.HEALTHY
        else:
            return HealthStatus.UNKNOWN
    
    def _generate_summary(self, checks: List[HealthCheck]) -> Dict[str, Any]:
        """Generate health summary"""
        
        status_counts = {status.value: 0 for status in HealthStatus}
        component_counts = {comp.value: 0 for comp in ComponentType}
        
        total_execution_time = 0.0
        
        for check in checks:
            status_counts[check.status.value] += 1
            component_counts[check.component.value] += 1
            total_execution_time += check.execution_time
        
        return {
            'total_checks': len(checks),
            'status_distribution': status_counts,
            'component_distribution': component_counts,
            'total_execution_time': total_execution_time,
            'critical_issues': sum(1 for check in checks if check.status == HealthStatus.CRITICAL),
            'warnings': sum(1 for check in checks if check.status == HealthStatus.WARNING),
            'healthy_components': sum(1 for check in checks if check.status == HealthStatus.HEALTHY)
        }
    
    def _generate_recommendations(self, checks: List[HealthCheck]) -> List[str]:
        """Generate system-wide recommendations"""
        
        all_recommendations = []
        
        # Collect all recommendations
        for check in checks:
            all_recommendations.extend(check.recommendations)
        
        # Remove duplicates while preserving order
        seen = set()
        unique_recommendations = []
        for rec in all_recommendations:
            if rec not in seen:
                seen.add(rec)
                unique_recommendations.append(rec)
        
        # Add general recommendations based on status
        critical_components = [check.component.value for check in checks if check.status == HealthStatus.CRITICAL]
        
        if critical_components:
            unique_recommendations.insert(0, f"Address critical issues in: {', '.join(critical_components)}")
        
        warning_components = [check.component.value for check in checks if check.status == HealthStatus.WARNING]
        
        if len(warning_components) > 2:
            unique_recommendations.append("Multiple components have warnings - consider comprehensive system review")
        
        return unique_recommendations[:10]  # Top 10 recommendations
    
    async def _collect_performance_metrics(self) -> Dict[str, Any]:
        """Collect current performance metrics"""
        
        try:
            return {
                'cpu_percent': psutil.cpu_percent(),
                'memory_percent': psutil.virtual_memory().percent,
                'disk_io': psutil.disk_io_counters()._asdict() if psutil.disk_io_counters() else {},
                'network_io': psutil.net_io_counters()._asdict() if psutil.net_io_counters() else {},
                'process_count': len(psutil.pids()),
                'boot_time': psutil.boot_time(),
                'load_average': psutil.getloadavg() if hasattr(psutil, 'getloadavg') else []
            }
        except Exception as e:
            logger.warning(f"Failed to collect performance metrics: {e}")
            return {}
    
    async def quick_health_check(self) -> Dict[str, Any]:
        """Perform quick health assessment"""
        
        start_time = time.time()
        
        quick_status = {
            'timestamp': start_time,
            'overall_status': 'unknown',
            'critical_issues': [],
            'warnings': [],
            'execution_time': 0.0
        }
        
        try:
            # Quick system check
            cpu_percent = psutil.cpu_percent()
            memory_percent = psutil.virtual_memory().percent
            
            if cpu_percent > self.critical_thresholds['cpu_usage']:
                quick_status['critical_issues'].append(f"Critical CPU usage: {cpu_percent:.1f}%")
            elif cpu_percent > self.warning_thresholds['cpu_usage']:
                quick_status['warnings'].append(f"High CPU usage: {cpu_percent:.1f}%")
            
            if memory_percent > self.critical_thresholds['memory_usage']:
                quick_status['critical_issues'].append(f"Critical memory usage: {memory_percent:.1f}%")
            elif memory_percent > self.warning_thresholds['memory_usage']:
                quick_status['warnings'].append(f"High memory usage: {memory_percent:.1f}%")
            
            # Quick engine check
            try:
                engine = await get_hybrid_engine()
                if not engine:
                    quick_status['critical_issues'].append("Hybrid execution engine not available")
            except Exception:
                quick_status['warnings'].append("Cannot verify execution engine status")
            
            # Determine overall status
            if quick_status['critical_issues']:
                quick_status['overall_status'] = 'critical'
            elif quick_status['warnings']:
                quick_status['overall_status'] = 'warning'
            else:
                quick_status['overall_status'] = 'healthy'
            
        except Exception as e:
            quick_status['critical_issues'].append(f"Health check failed: {str(e)}")
            quick_status['overall_status'] = 'critical'
        
        quick_status['execution_time'] = time.time() - start_time
        
        return quick_status

# Global health checker
health_checker = None

def get_health_checker() -> HealthChecker:
    """Get or create health checker"""
    global health_checker
    
    if health_checker is None:
        health_checker = HealthChecker()
    
    return health_checker

async def perform_system_health_check() -> SystemHealth:
    """Perform comprehensive system health check"""
    checker = get_health_checker()
    return await checker.perform_comprehensive_health_check()

async def quick_system_health() -> Dict[str, Any]:
    """Perform quick system health check"""
    checker = get_health_checker()
    return await checker.quick_health_check()