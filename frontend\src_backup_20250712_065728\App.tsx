import React, { useEffect, useState } from 'react';
import { Routes, Route, Navigate, useLocation } from 'react-router-dom';
import { ErrorBoundary } from 'react-error-boundary';
import { cn } from '@/lib/utils';

// Layout components
import { Sidebar } from '@/components/layout/Sidebar';
import { Header } from '@/components/layout/Header';
import { StatusBar } from '@/components/layout/StatusBar';
import { Terminal } from '@/components/layout/Terminal';

// Page components
import { DashboardPage } from '@/pages/DashboardPage';
import { CampaignsPage } from '@/pages/CampaignsPage';
import { ReportsPage } from '@/pages/ReportsPage';
import { SettingsPage } from '@/pages/SettingsPage';

// Tool pages
import { NetworkScanningPage } from '@/pages/tools/NetworkScanningPage';
import { WebTestingPage } from '@/pages/tools/WebTestingPage';
import { VulnerabilityAssessmentPage } from '@/pages/tools/VulnerabilityAssessmentPage';
import { PasswordToolsPage } from '@/pages/tools/PasswordToolsPage';
import { SSLTestingPage } from '@/pages/tools/SSLTestingPage';
import { ExploitationPage } from '@/pages/tools/ExploitationPage';

// Ferrari AI pages
import { FerrariDashboard } from '@/pages/ferrari/FerrariDashboard';
import { OrchestratorPage } from '@/pages/ferrari/OrchestratorPage';
import { CreativeExploitsPage } from '@/pages/ferrari/CreativeExploitsPage';
import { BehavioralAnalysisPage } from '@/pages/ferrari/BehavioralAnalysisPage';
import { AIProxyPage } from '@/pages/ferrari/AIProxyPage';

// Error components
import { ErrorFallback } from '@/components/ui/ErrorFallback';

// Stores
import { useAppStore } from '@/stores/app-store';
import { useBackendStore } from '@/stores/backend-store';
import { useWSLStore } from '@/stores/wsl-store';

// Services
import { electronService } from '@/services/electron-service';

/**
 * Main application component
 */
function App() {
  const location = useLocation();
  
  // App state
  const {
    isLoading,
    sidebarOpen,
    terminalOpen,
    theme,
    setLoading,
    toggleSidebar,
    toggleTerminal
  } = useAppStore();
  
  const { status: backendStatus, connect: connectBackend } = useBackendStore();
  const { status: wslStatus, initialize: initializeWSL } = useWSLStore();
  
  // Local state
  const [isInitialized, setIsInitialized] = useState(false);
  const [initError, setInitError] = useState<string | null>(null);
  
  /**
   * Initialize application
   */
  useEffect(() => {
    async function initializeApp() {
      try {
        setLoading(true);
        console.log('🚀 Initializing NexusScan Desktop App...');
        
        // Check if running in Electron
        if (!electronService.isElectron()) {
          console.warn('⚠️ Running in browser mode - some features may be limited');
        }
        
        // Initialize backend connection
        console.log('🔗 Connecting to backend...');
        const connected = await connectBackend();
        
        if (!connected) {
          console.warn('⚠️ Backend connection failed - will retry in background');
        }
        
        // Initialize WSL on Windows
        if (electronService.isElectron()) {
          const platform = await electronService.getPlatform();
          if (platform === 'win32') {
            console.log('🐧 Initializing WSL...');
            try {
              await initializeWSL();
            } catch (error) {
              console.warn('⚠️ WSL initialization failed:', error);
            }
          }
        }
        
        // Setup event listeners
        setupEventListeners();
        
        setIsInitialized(true);
        console.log('✅ NexusScan Desktop App initialized');
        
      } catch (error) {
        console.error('❌ App initialization failed:', error);
        setInitError(error instanceof Error ? error.message : 'Unknown initialization error');
      } finally {
        setLoading(false);
      }
    }
    
    initializeApp();
  }, [setLoading, connectBackend, initializeWSL]);
  
  /**
   * Setup IPC event listeners
   */
  function setupEventListeners() {
    if (!electronService.isElectron()) return;
    
    // Navigation events
    electronService.on('navigate', (path: string) => {
      window.history.pushState(null, '', path);
    });
    
    // UI toggle events
    electronService.on('toggle-sidebar', () => {
      toggleSidebar();
    });
    
    electronService.on('toggle-terminal', () => {
      toggleTerminal();
    });
    
    // Backend events
    electronService.on('backend-connected', () => {
      console.log('📡 Backend connected');
    });
    
    electronService.on('backend-disconnected', () => {
      console.log('📡 Backend disconnected');
    });
    
    electronService.on('backend-error', (error: any) => {
      console.error('📡 Backend error:', error);
    });
    
    // Tool events
    electronService.on('tool-output', (data: any) => {
      console.log('🔧 Tool output:', data);
    });
    
    electronService.on('tool-progress', (data: any) => {
      console.log('🔧 Tool progress:', data);
    });
    
    console.log('👂 Event listeners setup complete');
  }
  
  /**
   * Handle keyboard shortcuts
   */
  useEffect(() => {
    function handleKeyboard(event: KeyboardEvent) {
      // Only handle if not in input field
      if (event.target instanceof HTMLInputElement || 
          event.target instanceof HTMLTextAreaElement) {
        return;
      }
      
      const { ctrlKey, metaKey, shiftKey, key } = event;
      const modifier = ctrlKey || metaKey;
      
      if (modifier) {
        switch (key) {
          case 'b':
            event.preventDefault();
            toggleSidebar();
            break;
          case 't':
            event.preventDefault();
            toggleTerminal();
            break;
          case '1':
            event.preventDefault();
            window.history.pushState(null, '', '/dashboard');
            break;
          case '2':
            event.preventDefault();
            window.history.pushState(null, '', '/campaigns');
            break;
          case '3':
            event.preventDefault();
            window.history.pushState(null, '', '/reports');
            break;
        }
      }
    }
    
    window.addEventListener('keydown', handleKeyboard);
    return () => window.removeEventListener('keydown', handleKeyboard);
  }, [toggleSidebar, toggleTerminal]);
  
  /**
   * Show loading screen during initialization
   */
  if (isLoading || !isInitialized) {
    return (
      <div className="flex items-center justify-center h-screen bg-background">
        <div className="text-center space-y-4">
          <div className="loading-spinner mx-auto"></div>
          <div className="text-lg font-semibold">Initializing NexusScan Desktop...</div>
          <div className="text-sm text-muted-foreground">
            {backendStatus.connected ? 'Backend connected' : 'Connecting to backend...'}
          </div>
        </div>
      </div>
    );
  }
  
  /**
   * Show error screen if initialization failed
   */
  if (initError) {
    return (
      <div className="flex items-center justify-center h-screen bg-background p-8">
        <div className="text-center space-y-4 max-w-md">
          <div className="text-red-500 text-xl">⚠️</div>
          <div className="text-lg font-semibold">Initialization Failed</div>
          <div className="text-sm text-muted-foreground">{initError}</div>
          <button
            onClick={() => window.location.reload()}
            className="px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90"
          >
            Restart Application
          </button>
        </div>
      </div>
    );
  }
  
  return (
    <div className={cn('h-screen flex flex-col bg-background', theme)}>
      <ErrorBoundary
        FallbackComponent={ErrorFallback}
        onError={(error, errorInfo) => {
          console.error('🚨 React Error Boundary:', error, errorInfo);
        }}
        onReset={() => {
          window.location.reload();
        }}
      >
        {/* Header */}
        <Header />
        
        {/* Main content area */}
        <div className="flex flex-1 overflow-hidden">
          {/* Sidebar */}
          <Sidebar 
            isOpen={sidebarOpen}
            currentPath={location.pathname}
          />
          
          {/* Main content */}
          <main className="flex-1 flex flex-col overflow-hidden">
            {/* Page content */}
            <div className="flex-1 overflow-auto">
              <Routes>
                {/* Default redirect */}
                <Route path="/" element={<Navigate to="/dashboard" replace />} />
                
                {/* Core pages */}
                <Route path="/dashboard" element={<DashboardPage />} />
                <Route path="/campaigns/*" element={<CampaignsPage />} />
                <Route path="/reports/*" element={<ReportsPage />} />
                <Route path="/settings/*" element={<SettingsPage />} />
                
                {/* Tool pages */}
                <Route path="/tools/network-scanning/*" element={<NetworkScanningPage />} />
                <Route path="/tools/web-testing/*" element={<WebTestingPage />} />
                <Route path="/tools/vulnerability-assessment/*" element={<VulnerabilityAssessmentPage />} />
                <Route path="/tools/password-tools/*" element={<PasswordToolsPage />} />
                <Route path="/tools/ssl-testing/*" element={<SSLTestingPage />} />
                <Route path="/tools/exploitation/*" element={<ExploitationPage />} />
                
                {/* Ferrari AI pages */}
                <Route path="/ferrari" element={<FerrariDashboard />} />
                <Route path="/ferrari/orchestrator/*" element={<OrchestratorPage />} />
                <Route path="/ferrari/creative-exploits/*" element={<CreativeExploitsPage />} />
                <Route path="/ferrari/behavioral-analysis/*" element={<BehavioralAnalysisPage />} />
                <Route path="/ferrari/ai-proxy/*" element={<AIProxyPage />} />
                
                {/* Fallback for unknown routes */}
                <Route path="*" element={
                  <div className="flex items-center justify-center h-full">
                    <div className="text-center space-y-4">
                      <div className="text-4xl">🔍</div>
                      <div className="text-xl font-semibold">Page Not Found</div>
                      <div className="text-muted-foreground">
                        The page you're looking for doesn't exist.
                      </div>
                      <button
                        onClick={() => window.history.pushState(null, '', '/dashboard')}
                        className="px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90"
                      >
                        Go to Dashboard
                      </button>
                    </div>
                  </div>
                } />
              </Routes>
            </div>
            
            {/* Terminal */}
            {terminalOpen && (
              <Terminal 
                isOpen={terminalOpen}
                onToggle={toggleTerminal}
              />
            )}
          </main>
        </div>
        
        {/* Status bar */}
        <StatusBar 
          backendStatus={backendStatus}
          wslStatus={wslStatus}
        />
      </ErrorBoundary>
    </div>
  );
}

export default App;