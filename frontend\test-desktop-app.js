#!/usr/bin/env node

const { spawn } = require('child_process');
const path = require('path');

console.log('🚀 Testing NexusScan Desktop Application...\n');

// Function to test if Vite server is running
function testViteServer() {
  return new Promise((resolve) => {
    const http = require('http');
    const req = http.get('http://localhost:5180/', (res) => {
      console.log('✅ Vite dev server is responding (status:', res.statusCode, ')');
      resolve(true);
    });
    
    req.on('error', () => {
      console.log('❌ Vite dev server is not responding');
      resolve(false);
    });
    
    req.setTimeout(2000, () => {
      console.log('⏱️  Vite dev server timeout');
      req.destroy();
      resolve(false);
    });
  });
}

// Function to start Electron and capture output
function startElectron() {
  return new Promise((resolve) => {
    console.log('🖥️  Starting Electron application...');
    
    const electronProcess = spawn('./node_modules/.bin/electron.cmd', ['.'], {
      cwd: __dirname,
      env: { ...process.env, NODE_ENV: 'development' },
      stdio: ['pipe', 'pipe', 'pipe'],
      shell: true
    });

    let output = '';
    let hasStarted = false;
    let timeout;

    // Capture stdout
    electronProcess.stdout.on('data', (data) => {
      const text = data.toString();
      output += text;
      console.log('📝 STDOUT:', text.trim());
      
      // Check for successful startup indicators
      if (text.includes('Cache directory initialized') || 
          text.includes('NexusScan Desktop ready') ||
          text.includes('Application loaded from')) {
        hasStarted = true;
      }
    });

    // Capture stderr
    electronProcess.stderr.on('data', (data) => {
      const text = data.toString();
      output += text;
      console.log('⚠️  STDERR:', text.trim());
    });

    // Handle process events
    electronProcess.on('close', (code) => {
      clearTimeout(timeout);
      console.log(`\n🔄 Electron process exited with code: ${code}`);
      resolve({ success: hasStarted, output, code });
    });

    electronProcess.on('error', (error) => {
      clearTimeout(timeout);
      console.error('❌ Electron process error:', error.message);
      resolve({ success: false, output, error: error.message });
    });

    // Set timeout for test
    timeout = setTimeout(() => {
      console.log('\n⏱️  Test timeout (15s) - terminating Electron...');
      electronProcess.kill('SIGTERM');
      
      setTimeout(() => {
        if (!electronProcess.killed) {
          console.log('🔨 Force killing Electron process...');
          electronProcess.kill('SIGKILL');
        }
      }, 2000);
      
      resolve({ success: hasStarted, output, timeout: true });
    }, 15000);
  });
}

// Main test function
async function runTest() {
  try {
    // Test 1: Check Vite server
    console.log('📡 Testing Vite dev server...');
    const viteRunning = await testViteServer();
    
    if (!viteRunning) {
      console.log('\n❌ Vite server is not running. Please start it with: npm run dev:vite\n');
      return;
    }

    // Test 2: Start Electron
    const result = await startElectron();
    
    // Results
    console.log('\n' + '='.repeat(60));
    console.log('📊 TEST RESULTS:');
    console.log('='.repeat(60));
    
    if (result.success) {
      console.log('✅ Desktop application started successfully!');
      console.log('✅ Cache directory initialization worked');
      
      if (result.output.includes('Application loaded from')) {
        console.log('✅ React application loaded');
      }
      
      if (result.output.includes('NexusScan Desktop ready')) {
        console.log('✅ Backend services initialized');
      }
    } else {
      console.log('❌ Desktop application failed to start properly');
      
      if (result.timeout) {
        console.log('⏱️  Application may be stuck at loading screen');
      }
    }
    
    console.log('\n📋 Key Fixes Status:');
    console.log('• Cache directory fix:', result.output.includes('Cache directory initialized') ? '✅ Working' : '❌ Failed');
    console.log('• App ready event fix:', result.output.includes('Force dispatching appReady') ? '✅ Triggered' : 'ℹ️  Not triggered');
    console.log('• DevTools opening:', result.output.includes('DevTools') ? '✅ Opened' : 'ℹ️  Not mentioned');
    
    console.log('\n📝 Full Output:');
    console.log('-'.repeat(40));
    console.log(result.output || 'No output captured');
    
  } catch (error) {
    console.error('💥 Test failed with error:', error.message);
  }
}

// Run the test
runTest().then(() => {
  console.log('\n🏁 Test completed');
  process.exit(0);
});