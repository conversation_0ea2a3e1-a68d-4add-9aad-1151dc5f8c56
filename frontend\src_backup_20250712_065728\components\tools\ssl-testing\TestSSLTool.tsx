/**
 * TestSSL Tool Component
 * Comprehensive SSL/TLS configuration checker and vulnerability scanner
 */
import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { 
  Key,
  Play,
  Square,
  Download,
  Settings,
  Target,
  Globe,
  FileText,
  Terminal,
  Copy,
  RotateCcw,
  Clock,
  Zap,
  Filter,
  Eye,
  AlertTriangle,
  CheckCircle,
  TrendingUp,
  Activity,
  Lock,
  Search,
  List,
  BarChart3,
  Database,
  Server,
  Bug,
  Info,
  Code,
  Package,
  Layers,
  Shield,
  Crosshair,
  Cpu,
  Network,
  Gauge,
  Tag,
  Hash,
  Workflow,
  GitBranch,
  Book,
  Users,
  Table,
  FileX,
  Skull,
  Unlock,
  HardDrive,
  Flame,
  Binary,
  Braces,
  FileKey,
  History,
  Folder,
  Star,
  Crown,
  Award,
  Sparkles,
  Wifi,
  Router,
  Mail,
  CloudRain,
  Rss,
  UserCheck,
  UserX,
  Timer,
  Pause,
  FastForward,
  Rewind,
  SkipForward,
  Certificate,
  ShieldCheck,
  ShieldAlert,
  AlertCircle,
  XCircle,
  RefreshCw,
  ExternalLink,
  FileCheck,
  FileWarning,
  Fingerprint,
  Key as KeyIcon,
  LockOpen,
  ShieldOff,
  Verified,
  ScanLine
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useBackendStore } from '@/stores/backend-store';
import { apiClient, ToolExecution } from '@/services/api-client';
import { toast } from 'sonner';

/**
 * TestSSL configuration interface
 */
interface TestSSLConfig {
  target: {
    hostname: string;
    port: number;
    starttls: string;
    proxy: string;
    sni: string;
    targetFile: string;
  };
  checks: {
    protocols: boolean;
    ciphers: boolean;
    pfs: boolean;
    serverDefaults: boolean;
    serverPreferences: boolean;
    headers: boolean;
    vulnerabilities: boolean;
    clientSimulation: boolean;
    certificate: boolean;
    ocsp: boolean;
    crl: boolean;
    fallback: boolean;
  };
  output: {
    format: 'default' | 'json' | 'csv' | 'html' | 'log';
    severity: 'low' | 'medium' | 'high' | 'critical' | 'all';
    colorOutput: boolean;
    appendMode: boolean;
    logFile: string;
    jsonFile: string;
    csvFile: string;
    htmlFile: string;
  };
  scanning: {
    fast: boolean;
    wide: boolean;
    assumeHttp: boolean;
    debugLevel: number;
    parallel: boolean;
    warnings: boolean;
    hints: boolean;
    connectTimeout: number;
    openSSLTimeout: number;
  };
  advanced: {
    customCiphers: string;
    customProtocols: string;
    caaRecord: boolean;
    dnsRecord: boolean;
    clientCertificate: string;
    clientKey: string;
    maxCertLifetime: number;
    phoneHome: boolean;
  };
}

/**
 * SSL/TLS check categories
 */
const CHECK_CATEGORIES = {
  'protocols': {
    name: 'Protocols',
    description: 'SSL/TLS protocol versions (SSLv2, SSLv3, TLS 1.0-1.3)',
    icon: Network,
    severity: 'high'
  },
  'ciphers': {
    name: 'Cipher Suites',
    description: 'Supported cipher suites and their security strength',
    icon: Lock,
    severity: 'medium'
  },
  'pfs': {
    name: 'Perfect Forward Secrecy',
    description: 'Forward secrecy support and configuration',
    icon: ShieldCheck,
    severity: 'high'
  },
  'serverDefaults': {
    name: 'Server Defaults',
    description: 'Default server cipher order and preferences',
    icon: Server,
    severity: 'medium'
  },
  'serverPreferences': {
    name: 'Server Preferences',
    description: 'Cipher suite preferences and prioritization',
    icon: Settings,
    severity: 'medium'
  },
  'headers': {
    name: 'Security Headers',
    description: 'HTTP security headers (HSTS, HPKP, etc.)',
    icon: FileCheck,
    severity: 'medium'
  },
  'vulnerabilities': {
    name: 'Vulnerabilities',
    description: 'Known SSL/TLS vulnerabilities (BEAST, CRIME, etc.)',
    icon: ShieldAlert,
    severity: 'critical'
  },
  'clientSimulation': {
    name: 'Client Simulation',
    description: 'Simulate various client handshakes',
    icon: Users,
    severity: 'low'
  },
  'certificate': {
    name: 'Certificate',
    description: 'Certificate chain validation and properties',
    icon: Certificate,
    severity: 'high'
  },
  'ocsp': {
    name: 'OCSP Stapling',
    description: 'Online Certificate Status Protocol support',
    icon: Verified,
    severity: 'medium'
  },
  'crl': {
    name: 'CRL',
    description: 'Certificate Revocation List checking',
    icon: FileWarning,
    severity: 'medium'
  },
  'fallback': {
    name: 'TLS Fallback',
    description: 'TLS fallback SCSV protection',
    icon: ShieldOff,
    severity: 'high'
  }
};

/**
 * STARTTLS protocols
 */
const STARTTLS_PROTOCOLS = {
  '': { name: 'None (HTTPS)', description: 'Direct HTTPS connection' },
  'smtp': { name: 'SMTP', description: 'Email server (port 25/587)' },
  'pop3': { name: 'POP3', description: 'Email retrieval (port 110/995)' },
  'imap': { name: 'IMAP', description: 'Email access (port 143/993)' },
  'ftp': { name: 'FTP', description: 'File transfer (port 21)' },
  'ftps': { name: 'FTPS', description: 'Secure FTP (port 990)' },
  'ldap': { name: 'LDAP', description: 'Directory service (port 389/636)' },
  'nntp': { name: 'NNTP', description: 'Network news (port 119/563)' },
  'postgres': { name: 'PostgreSQL', description: 'Database (port 5432)' },
  'mysql': { name: 'MySQL', description: 'Database (port 3306)' },
  'xmpp': { name: 'XMPP', description: 'Messaging (port 5222)' },
  'xmpp-server': { name: 'XMPP Server', description: 'Server-to-server (port 5269)' },
  'irc': { name: 'IRC', description: 'Internet Relay Chat (port 6697)' }
};

/**
 * Output format options
 */
const OUTPUT_FORMATS = {
  'default': { name: 'Default', description: 'Human-readable colored output', extension: 'txt' },
  'json': { name: 'JSON', description: 'Machine-readable JSON format', extension: 'json' },
  'csv': { name: 'CSV', description: 'Comma-separated values', extension: 'csv' },
  'html': { name: 'HTML', description: 'HTML report with styling', extension: 'html' },
  'log': { name: 'Log', description: 'Detailed log format', extension: 'log' }
};

/**
 * SSL/TLS vulnerabilities
 */
const SSL_VULNERABILITIES = {
  'heartbleed': { name: 'Heartbleed', severity: 'critical', cve: 'CVE-2014-0160' },
  'ccs': { name: 'CCS Injection', severity: 'high', cve: 'CVE-2014-0224' },
  'ticketbleed': { name: 'Ticketbleed', severity: 'high', cve: 'CVE-2016-9244' },
  'robot': { name: 'ROBOT', severity: 'medium', cve: 'CVE-2017-13099' },
  'secure_renegotiation': { name: 'Secure Renegotiation', severity: 'medium', cve: 'CVE-2009-3555' },
  'secure_client_renegotiation': { name: 'Client Renegotiation', severity: 'medium', cve: 'CVE-2011-1473' },
  'crime': { name: 'CRIME', severity: 'medium', cve: 'CVE-2012-4929' },
  'breach': { name: 'BREACH', severity: 'medium', cve: 'CVE-2013-3587' },
  'poodle_ssl': { name: 'POODLE SSL', severity: 'high', cve: 'CVE-2014-3566' },
  'fallback_scsv': { name: 'TLS Fallback SCSV', severity: 'medium', cve: 'RFC 7507' },
  'sweet32': { name: 'SWEET32', severity: 'medium', cve: 'CVE-2016-2183' },
  'freak': { name: 'FREAK', severity: 'high', cve: 'CVE-2015-0204' },
  'logjam': { name: 'Logjam', severity: 'medium', cve: 'CVE-2015-4000' },
  'beast': { name: 'BEAST', severity: 'medium', cve: 'CVE-2011-3389' },
  'lucky13': { name: 'Lucky13', severity: 'medium', cve: 'CVE-2013-0169' },
  'rc4': { name: 'RC4', severity: 'medium', cve: 'RFC 7465' }
};

/**
 * Scanning presets
 */
const SCANNING_PRESETS = {
  'quick': {
    name: 'Quick Scan',
    description: 'Fast scan of essential checks',
    checks: {
      protocols: true,
      ciphers: false,
      pfs: true,
      serverDefaults: false,
      serverPreferences: false,
      headers: true,
      vulnerabilities: true,
      clientSimulation: false,
      certificate: true,
      ocsp: false,
      crl: false,
      fallback: true
    },
    fast: true
  },
  'standard': {
    name: 'Standard Scan',
    description: 'Comprehensive scan of all major checks',
    checks: {
      protocols: true,
      ciphers: true,
      pfs: true,
      serverDefaults: true,
      serverPreferences: true,
      headers: true,
      vulnerabilities: true,
      clientSimulation: false,
      certificate: true,
      ocsp: true,
      crl: false,
      fallback: true
    },
    fast: false
  },
  'comprehensive': {
    name: 'Comprehensive Scan',
    description: 'Full scan including client simulation',
    checks: {
      protocols: true,
      ciphers: true,
      pfs: true,
      serverDefaults: true,
      serverPreferences: true,
      headers: true,
      vulnerabilities: true,
      clientSimulation: true,
      certificate: true,
      ocsp: true,
      crl: true,
      fallback: true
    },
    fast: false
  },
  'vulnerability_only': {
    name: 'Vulnerability Focus',
    description: 'Focus on vulnerability detection only',
    checks: {
      protocols: false,
      ciphers: false,
      pfs: false,
      serverDefaults: false,
      serverPreferences: false,
      headers: false,
      vulnerabilities: true,
      clientSimulation: false,
      certificate: false,
      ocsp: false,
      crl: false,
      fallback: false
    },
    fast: true
  }
};

export default function TestSSLTool() {
  const { isConnected } = useBackendStore();
  const [config, setConfig] = React.useState<TestSSLConfig>({
    target: {
      hostname: '',
      port: 443,
      starttls: '',
      proxy: '',
      sni: '',
      targetFile: ''
    },
    checks: {
      protocols: true,
      ciphers: true,
      pfs: true,
      serverDefaults: true,
      serverPreferences: true,
      headers: true,
      vulnerabilities: true,
      clientSimulation: false,
      certificate: true,
      ocsp: true,
      crl: false,
      fallback: true
    },
    output: {
      format: 'default',
      severity: 'all',
      colorOutput: true,
      appendMode: false,
      logFile: '',
      jsonFile: '',
      csvFile: '',
      htmlFile: ''
    },
    scanning: {
      fast: false,
      wide: false,
      assumeHttp: false,
      debugLevel: 0,
      parallel: true,
      warnings: true,
      hints: true,
      connectTimeout: 10,
      openSSLTimeout: 10
    },
    advanced: {
      customCiphers: '',
      customProtocols: '',
      caaRecord: false,
      dnsRecord: false,
      clientCertificate: '',
      clientKey: '',
      maxCertLifetime: 30,
      phoneHome: false
    }
  });

  const [isRunning, setIsRunning] = React.useState(false);
  const [output, setOutput] = React.useState<string[]>([]);
  const [results, setResults] = React.useState<any[]>([]);
  const [currentExecution, setCurrentExecution] = React.useState<ToolExecution | null>(null);
  const [progress, setProgress] = React.useState(0);
  const [testResults, setTestResults] = React.useState<any>({
    grade: '',
    vulnerabilities: [],
    protocols: [],
    ciphers: [],
    certificate: null,
    headers: [],
    recommendations: []
  });

  /**
   * Apply scanning preset
   */
  const applyPreset = (preset: keyof typeof SCANNING_PRESETS) => {
    const presetConfig = SCANNING_PRESETS[preset];
    setConfig(prev => ({
      ...prev,
      checks: presetConfig.checks,
      scanning: {
        ...prev.scanning,
        fast: presetConfig.fast
      }
    }));
    toast.success(`Applied ${presetConfig.name} preset`);
  };

  /**
   * Update port based on STARTTLS protocol
   */
  const updateStartTLS = (protocol: string) => {
    const portMapping: Record<string, number> = {
      '': 443,
      'smtp': 25,
      'pop3': 110,
      'imap': 143,
      'ftp': 21,
      'ftps': 990,
      'ldap': 389,
      'nntp': 119,
      'postgres': 5432,
      'mysql': 3306,
      'xmpp': 5222,
      'xmpp-server': 5269,
      'irc': 6697
    };

    setConfig(prev => ({
      ...prev,
      target: {
        ...prev.target,
        starttls: protocol,
        port: portMapping[protocol] || prev.target.port
      }
    }));
  };

  /**
   * Generate TestSSL command
   */
  const generateCommand = (): string => {
    const parts = ['testssl.sh'];

    // Check options
    const checkArgs: string[] = [];
    if (config.checks.protocols) checkArgs.push('-p');
    if (config.checks.ciphers) checkArgs.push('-e');
    if (config.checks.pfs) checkArgs.push('-f');
    if (config.checks.serverDefaults) checkArgs.push('-S');
    if (config.checks.serverPreferences) checkArgs.push('-P');
    if (config.checks.headers) checkArgs.push('-h');
    if (config.checks.vulnerabilities) checkArgs.push('-U');
    if (config.checks.clientSimulation) checkArgs.push('-t');
    if (config.checks.certificate) checkArgs.push('-c');
    if (config.checks.ocsp) checkArgs.push('-O');
    if (config.checks.crl) checkArgs.push('-C');
    if (config.checks.fallback) checkArgs.push('-T');

    if (checkArgs.length === 0) {
      // If no specific checks, do everything
      checkArgs.push('--each-cipher');
    }

    parts.push(...checkArgs);

    // Output options
    if (config.output.format === 'json') {
      parts.push('--jsonfile', config.output.jsonFile || '/tmp/testssl.json');
    } else if (config.output.format === 'csv') {
      parts.push('--csvfile', config.output.csvFile || '/tmp/testssl.csv');
    } else if (config.output.format === 'html') {
      parts.push('--htmlfile', config.output.htmlFile || '/tmp/testssl.html');
    }

    if (config.output.logFile) {
      parts.push('--logfile', config.output.logFile);
    }

    if (config.output.appendMode) {
      parts.push('--append');
    }

    if (!config.output.colorOutput) {
      parts.push('--color', '0');
    }

    if (config.output.severity !== 'all') {
      parts.push('--severity', config.output.severity.toUpperCase());
    }

    // Scanning options
    if (config.scanning.fast) {
      parts.push('--fast');
    }

    if (config.scanning.wide) {
      parts.push('--wide');
    }

    if (config.scanning.assumeHttp) {
      parts.push('--assume-http');
    }

    if (config.scanning.debugLevel > 0) {
      parts.push('--debug', config.scanning.debugLevel.toString());
    }

    if (!config.scanning.parallel) {
      parts.push('--serial');
    }

    if (!config.scanning.warnings) {
      parts.push('--warnings', 'off');
    }

    if (!config.scanning.hints) {
      parts.push('--hints', 'off');
    }

    if (config.scanning.connectTimeout !== 10) {
      parts.push('--connect-timeout', config.scanning.connectTimeout.toString());
    }

    if (config.scanning.openSSLTimeout !== 10) {
      parts.push('--openssl-timeout', config.scanning.openSSLTimeout.toString());
    }

    // Advanced options
    if (config.advanced.customCiphers) {
      parts.push('--cipher', config.advanced.customCiphers);
    }

    if (config.advanced.customProtocols) {
      parts.push('--protocols', config.advanced.customProtocols);
    }

    if (config.advanced.caaRecord) {
      parts.push('--dnsreq');
    }

    if (config.advanced.clientCertificate && config.advanced.clientKey) {
      parts.push('--cert', config.advanced.clientCertificate);
      parts.push('--key', config.advanced.clientKey);
    }

    if (config.advanced.maxCertLifetime !== 30) {
      parts.push('--days2warn1', config.advanced.maxCertLifetime.toString());
    }

    if (!config.advanced.phoneHome) {
      parts.push('--phone-out');
    }

    // Target specification
    if (config.target.targetFile) {
      parts.push('--file', config.target.targetFile);
    } else {
      // Proxy
      if (config.target.proxy) {
        parts.push('--proxy', config.target.proxy);
      }

      // SNI
      if (config.target.sni) {
        parts.push('--sni', config.target.sni);
      }

      // STARTTLS
      if (config.target.starttls) {
        parts.push(`--starttls`, config.target.starttls);
      }

      // Target host and port
      const target = config.target.port !== 443 ? 
        `${config.target.hostname}:${config.target.port}` : 
        config.target.hostname;
      parts.push(target);
    }

    return parts.join(' ');
  };

  /**
   * Start TestSSL execution
   */
  const startExecution = async () => {
    if (!isConnected) {
      toast.error('Backend not connected');
      return;
    }

    // Validation
    if (!config.target.hostname && !config.target.targetFile) {
      toast.error('Please specify a hostname or target file');
      return;
    }

    try {
      setIsRunning(true);
      setOutput([]);
      setResults([]);
      setProgress(0);
      setTestResults({
        grade: '',
        vulnerabilities: [],
        protocols: [],
        ciphers: [],
        certificate: null,
        headers: [],
        recommendations: []
      });

      const execution = await apiClient.startTool('testssl', {
        ...config,
        command: generateCommand()
      });

      setCurrentExecution(execution);
      toast.success('TestSSL scan started');

      // Listen for progress updates
      const eventSource = new EventSource(`/api/tools/testssl/execution/${execution.id}/stream`);
      
      eventSource.onmessage = (event) => {
        const data = JSON.parse(event.data);
        
        if (data.type === 'output') {
          setOutput(prev => [...prev, data.content]);
        } else if (data.type === 'progress') {
          setProgress(data.progress);
        } else if (data.type === 'result') {
          setResults(prev => [...prev, data.result]);
        } else if (data.type === 'test_results') {
          setTestResults(data.results);
        } else if (data.type === 'complete') {
          setIsRunning(false);
          eventSource.close();
          toast.success('TestSSL scan completed');
        } else if (data.type === 'error') {
          setIsRunning(false);
          eventSource.close();
          toast.error(`Error: ${data.message}`);
        }
      };

      eventSource.onerror = () => {
        setIsRunning(false);
        eventSource.close();
        toast.error('Connection to execution stream lost');
      };

    } catch (error) {
      setIsRunning(false);
      console.error('Failed to start TestSSL:', error);
      toast.error('Failed to start TestSSL');
    }
  };

  /**
   * Stop execution
   */
  const stopExecution = async () => {
    if (!currentExecution) return;

    try {
      await apiClient.stopTool('testssl', currentExecution.id);
      setIsRunning(false);
      toast.success('TestSSL scan stopped');
    } catch (error) {
      console.error('Failed to stop TestSSL:', error);
      toast.error('Failed to stop TestSSL');
    }
  };

  /**
   * Export results
   */
  const exportResults = () => {
    const exportData = {
      config,
      results,
      testResults,
      command: generateCommand(),
      timestamp: new Date().toISOString()
    };

    const blob = new Blob([JSON.stringify(exportData, null, 2)], {
      type: 'application/json'
    });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `testssl-results-${Date.now()}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    
    toast.success('Results exported successfully');
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className="flex items-center justify-center w-10 h-10 bg-green-100 rounded-lg">
            <ShieldCheck className="w-5 h-5 text-green-600" />
          </div>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">TestSSL.sh</h1>
            <p className="text-sm text-gray-500">Comprehensive SSL/TLS configuration checker and vulnerability scanner</p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Badge variant={isConnected ? "default" : "secondary"}>
            {isConnected ? "Connected" : "Disconnected"}
          </Badge>
          <Badge variant="outline" className="font-mono">v3.0.8</Badge>
        </div>
      </div>

      {/* Quick Start Presets */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Zap className="w-5 h-5" />
            <span>Scanning Presets</span>
          </CardTitle>
          <CardDescription>
            Pre-configured scanning profiles for different use cases
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-3">
            {Object.entries(SCANNING_PRESETS).map(([key, preset]) => (
              <Button
                key={key}
                variant="outline"
                size="sm"
                onClick={() => applyPreset(key as keyof typeof SCANNING_PRESETS)}
                className="h-auto p-3 text-left justify-start"
              >
                <div>
                  <div className="font-medium text-sm">{preset.name}</div>
                  <div className="text-xs text-gray-500">{preset.description}</div>
                </div>
              </Button>
            ))}
          </div>
        </CardContent>
      </Card>

      <Tabs defaultValue="configure" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="configure" className="flex items-center space-x-2">
            <Settings className="w-4 h-4" />
            <span>Configure</span>
          </TabsTrigger>
          <TabsTrigger value="output" className="flex items-center space-x-2">
            <Terminal className="w-4 h-4" />
            <span>Output</span>
          </TabsTrigger>
          <TabsTrigger value="results" className="flex items-center space-x-2">
            <Target className="w-4 h-4" />
            <span>Results</span>
          </TabsTrigger>
          <TabsTrigger value="command" className="flex items-center space-x-2">
            <Code className="w-4 h-4" />
            <span>Command</span>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="configure" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Target Configuration */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Target className="w-5 h-5" />
                  <span>Target Configuration</span>
                </CardTitle>
                <CardDescription>
                  Specify the SSL/TLS endpoint to test
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium">Hostname</label>
                  <Input
                    placeholder="example.com"
                    value={config.target.hostname}
                    onChange={(e) => setConfig(prev => ({
                      ...prev,
                      target: { ...prev.target, hostname: e.target.value }
                    }))}
                  />
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">Port</label>
                  <Input
                    type="number"
                    min="1"
                    max="65535"
                    value={config.target.port}
                    onChange={(e) => setConfig(prev => ({
                      ...prev,
                      target: { ...prev.target, port: parseInt(e.target.value) || 443 }
                    }))}
                  />
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">STARTTLS Protocol</label>
                  <Select 
                    value={config.target.starttls} 
                    onValueChange={updateStartTLS}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select protocol..." />
                    </SelectTrigger>
                    <SelectContent>
                      {Object.entries(STARTTLS_PROTOCOLS).map(([key, protocol]) => (
                        <SelectItem key={key} value={key}>
                          <div>
                            <div className="font-medium">{protocol.name}</div>
                            <div className="text-xs text-gray-500">{protocol.description}</div>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">SNI (Server Name Indication)</label>
                  <Input
                    placeholder="sni.example.com"
                    value={config.target.sni}
                    onChange={(e) => setConfig(prev => ({
                      ...prev,
                      target: { ...prev.target, sni: e.target.value }
                    }))}
                  />
                  <p className="text-xs text-gray-500">Leave empty to use hostname</p>
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">Proxy</label>
                  <Input
                    placeholder="http://proxy.example.com:8080"
                    value={config.target.proxy}
                    onChange={(e) => setConfig(prev => ({
                      ...prev,
                      target: { ...prev.target, proxy: e.target.value }
                    }))}
                  />
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">Target File (Alternative)</label>
                  <Input
                    placeholder="/path/to/targets.txt"
                    value={config.target.targetFile}
                    onChange={(e) => setConfig(prev => ({
                      ...prev,
                      target: { ...prev.target, targetFile: e.target.value }
                    }))}
                  />
                  <p className="text-xs text-gray-500">File with multiple targets, one per line</p>
                </div>
              </CardContent>
            </Card>

            {/* Check Configuration */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <CheckCircle className="w-5 h-5" />
                  <span>SSL/TLS Checks</span>
                </CardTitle>
                <CardDescription>
                  Select which SSL/TLS aspects to test
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid gap-3">
                  {Object.entries(CHECK_CATEGORIES).map(([key, check]) => (
                    <div key={key} className="flex items-start space-x-3 p-3 border rounded-lg">
                      <input
                        type="checkbox"
                        id={`check-${key}`}
                        checked={config.checks[key as keyof typeof config.checks]}
                        onChange={(e) => setConfig(prev => ({
                          ...prev,
                          checks: {
                            ...prev.checks,
                            [key]: e.target.checked
                          }
                        }))}
                        className="rounded mt-1"
                      />
                      <div className="flex-1">
                        <div className="flex items-center space-x-2">
                          <check.icon className="w-4 h-4" />
                          <label htmlFor={`check-${key}`} className="font-medium text-sm cursor-pointer">
                            {check.name}
                          </label>
                          <Badge 
                            variant="outline" 
                            className={cn(
                              "text-xs",
                              check.severity === 'critical' && "border-red-200 text-red-700",
                              check.severity === 'high' && "border-orange-200 text-orange-700",
                              check.severity === 'medium' && "border-yellow-200 text-yellow-700",
                              check.severity === 'low' && "border-blue-200 text-blue-700"
                            )}
                          >
                            {check.severity}
                          </Badge>
                        </div>
                        <p className="text-xs text-gray-500 mt-1">{check.description}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Output Configuration */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <FileText className="w-5 h-5" />
                  <span>Output Configuration</span>
                </CardTitle>
                <CardDescription>
                  Configure output format and file options
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium">Output Format</label>
                  <Select 
                    value={config.output.format} 
                    onValueChange={(value: any) => setConfig(prev => ({
                      ...prev,
                      output: { ...prev.output, format: value }
                    }))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {Object.entries(OUTPUT_FORMATS).map(([key, format]) => (
                        <SelectItem key={key} value={key}>
                          <div>
                            <div className="font-medium">{format.name}</div>
                            <div className="text-xs text-gray-500">{format.description}</div>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">Severity Filter</label>
                  <Select 
                    value={config.output.severity} 
                    onValueChange={(value: any) => setConfig(prev => ({
                      ...prev,
                      output: { ...prev.output, severity: value }
                    }))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Severity Levels</SelectItem>
                      <SelectItem value="low">Low and Above</SelectItem>
                      <SelectItem value="medium">Medium and Above</SelectItem>
                      <SelectItem value="high">High and Above</SelectItem>
                      <SelectItem value="critical">Critical Only</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {config.output.format === 'json' && (
                  <div className="space-y-2">
                    <label className="text-sm font-medium">JSON Output File</label>
                    <Input
                      placeholder="/tmp/testssl.json"
                      value={config.output.jsonFile}
                      onChange={(e) => setConfig(prev => ({
                        ...prev,
                        output: { ...prev.output, jsonFile: e.target.value }
                      }))}
                    />
                  </div>
                )}

                {config.output.format === 'csv' && (
                  <div className="space-y-2">
                    <label className="text-sm font-medium">CSV Output File</label>
                    <Input
                      placeholder="/tmp/testssl.csv"
                      value={config.output.csvFile}
                      onChange={(e) => setConfig(prev => ({
                        ...prev,
                        output: { ...prev.output, csvFile: e.target.value }
                      }))}
                    />
                  </div>
                )}

                {config.output.format === 'html' && (
                  <div className="space-y-2">
                    <label className="text-sm font-medium">HTML Output File</label>
                    <Input
                      placeholder="/tmp/testssl.html"
                      value={config.output.htmlFile}
                      onChange={(e) => setConfig(prev => ({
                        ...prev,
                        output: { ...prev.output, htmlFile: e.target.value }
                      }))}
                    />
                  </div>
                )}

                <div className="space-y-2">
                  <label className="text-sm font-medium">Log File</label>
                  <Input
                    placeholder="/tmp/testssl.log"
                    value={config.output.logFile}
                    onChange={(e) => setConfig(prev => ({
                      ...prev,
                      output: { ...prev.output, logFile: e.target.value }
                    }))}
                  />
                </div>

                <div className="space-y-3">
                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="color-output"
                      checked={config.output.colorOutput}
                      onChange={(e) => setConfig(prev => ({
                        ...prev,
                        output: { ...prev.output, colorOutput: e.target.checked }
                      }))}
                      className="rounded"
                    />
                    <label htmlFor="color-output" className="text-sm font-medium">
                      Enable colored output
                    </label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="append-mode"
                      checked={config.output.appendMode}
                      onChange={(e) => setConfig(prev => ({
                        ...prev,
                        output: { ...prev.output, appendMode: e.target.checked }
                      }))}
                      className="rounded"
                    />
                    <label htmlFor="append-mode" className="text-sm font-medium">
                      Append to existing files
                    </label>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Scanning Configuration */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Gauge className="w-5 h-5" />
                  <span>Scanning Configuration</span>
                </CardTitle>
                <CardDescription>
                  Configure scanning behavior and performance
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="fast"
                      checked={config.scanning.fast}
                      onChange={(e) => setConfig(prev => ({
                        ...prev,
                        scanning: { ...prev.scanning, fast: e.target.checked }
                      }))}
                      className="rounded"
                    />
                    <label htmlFor="fast" className="text-sm font-medium">
                      Fast scan (skip time-consuming checks)
                    </label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="wide"
                      checked={config.scanning.wide}
                      onChange={(e) => setConfig(prev => ({
                        ...prev,
                        scanning: { ...prev.scanning, wide: e.target.checked }
                      }))}
                      className="rounded"
                    />
                    <label htmlFor="wide" className="text-sm font-medium">
                      Wide output format
                    </label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="assume-http"
                      checked={config.scanning.assumeHttp}
                      onChange={(e) => setConfig(prev => ({
                        ...prev,
                        scanning: { ...prev.scanning, assumeHttp: e.target.checked }
                      }))}
                      className="rounded"
                    />
                    <label htmlFor="assume-http" className="text-sm font-medium">
                      Assume HTTP service (don't check for SSL)
                    </label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="parallel"
                      checked={config.scanning.parallel}
                      onChange={(e) => setConfig(prev => ({
                        ...prev,
                        scanning: { ...prev.scanning, parallel: e.target.checked }
                      }))}
                      className="rounded"
                    />
                    <label htmlFor="parallel" className="text-sm font-medium">
                      Enable parallel testing
                    </label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="warnings"
                      checked={config.scanning.warnings}
                      onChange={(e) => setConfig(prev => ({
                        ...prev,
                        scanning: { ...prev.scanning, warnings: e.target.checked }
                      }))}
                      className="rounded"
                    />
                    <label htmlFor="warnings" className="text-sm font-medium">
                      Show warnings
                    </label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="hints"
                      checked={config.scanning.hints}
                      onChange={(e) => setConfig(prev => ({
                        ...prev,
                        scanning: { ...prev.scanning, hints: e.target.checked }
                      }))}
                      className="rounded"
                    />
                    <label htmlFor="hints" className="text-sm font-medium">
                      Show hints and recommendations
                    </label>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Connect Timeout (s)</label>
                    <Input
                      type="number"
                      min="1"
                      max="60"
                      value={config.scanning.connectTimeout}
                      onChange={(e) => setConfig(prev => ({
                        ...prev,
                        scanning: { ...prev.scanning, connectTimeout: parseInt(e.target.value) || 10 }
                      }))}
                    />
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium">OpenSSL Timeout (s)</label>
                    <Input
                      type="number"
                      min="1"
                      max="60"
                      value={config.scanning.openSSLTimeout}
                      onChange={(e) => setConfig(prev => ({
                        ...prev,
                        scanning: { ...prev.scanning, openSSLTimeout: parseInt(e.target.value) || 10 }
                      }))}
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">Debug Level</label>
                  <Select 
                    value={config.scanning.debugLevel.toString()} 
                    onValueChange={(value) => setConfig(prev => ({
                      ...prev,
                      scanning: { ...prev.scanning, debugLevel: parseInt(value) }
                    }))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="0">No Debug (0)</SelectItem>
                      <SelectItem value="1">Basic Debug (1)</SelectItem>
                      <SelectItem value="2">Verbose Debug (2)</SelectItem>
                      <SelectItem value="3">Very Verbose Debug (3)</SelectItem>
                      <SelectItem value="4">Ultra Verbose Debug (4)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Execution Controls */}
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <Button
                    onClick={startExecution}
                    disabled={!isConnected || isRunning}
                    size="lg"
                    className="bg-green-600 hover:bg-green-700"
                  >
                    <Play className="w-4 h-4 mr-2" />
                    Start SSL/TLS Scan
                  </Button>
                  
                  {isRunning && (
                    <Button
                      onClick={stopExecution}
                      variant="outline"
                      size="lg"
                    >
                      <Square className="w-4 h-4 mr-2" />
                      Stop
                    </Button>
                  )}
                </div>

                <div className="flex items-center space-x-2">
                  {isRunning && (
                    <div className="flex items-center space-x-2">
                      <ScanLine className="w-4 h-4 text-green-600 animate-pulse" />
                      <span className="text-sm text-green-600">Scanning...</span>
                    </div>
                  )}
                  <Badge variant="outline">
                    {testResults.grade && `Grade: ${testResults.grade}`}
                  </Badge>
                </div>
              </div>

              {isRunning && (
                <div className="mt-4">
                  <div className="flex items-center justify-between text-sm text-gray-600 mb-2">
                    <span>Scan Progress</span>
                    <span>{progress.toFixed(1)}%</span>
                  </div>
                  <Progress value={progress} className="h-2" />
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="output" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Terminal className="w-5 h-5" />
                <span>Real-time Output</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="bg-black rounded-lg p-4 h-96 overflow-y-auto font-mono text-sm">
                {output.length === 0 ? (
                  <div className="text-gray-500 italic">
                    No output yet. Start TestSSL scan to see results.
                  </div>
                ) : (
                  output.map((line, index) => (
                    <div key={index} className={cn(
                      "text-green-400",
                      line.includes('CRITICAL') && "text-red-400 font-bold",
                      line.includes('HIGH') && "text-orange-400",
                      line.includes('MEDIUM') && "text-yellow-400",
                      line.includes('LOW') && "text-blue-400",
                      line.includes('OK') && "text-green-400",
                      line.includes('INFO') && "text-cyan-400"
                    )}>
                      {line}
                    </div>
                  ))
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="results" className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold">SSL/TLS Test Results</h3>
            {(results.length > 0 || testResults.grade) && (
              <Button onClick={exportResults} variant="outline">
                <Download className="w-4 h-4 mr-2" />
                Export Results
              </Button>
            )}
          </div>

          {!testResults.grade && results.length === 0 ? (
            <Card>
              <CardContent className="pt-6">
                <div className="text-center py-8">
                  <ShieldCheck className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500">No test results yet</p>
                  <p className="text-sm text-gray-400">Start a scan to see SSL/TLS analysis</p>
                </div>
              </CardContent>
            </Card>
          ) : (
            <div className="space-y-6">
              {/* Overall Grade */}
              {testResults.grade && (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2">
                      <Award className="w-5 h-5" />
                      <span>Overall SSL/TLS Grade</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="flex items-center space-x-4">
                      <div className={cn(
                        "text-6xl font-bold flex items-center justify-center w-20 h-20 rounded-full",
                        testResults.grade === 'A+' && "bg-green-100 text-green-600",
                        testResults.grade === 'A' && "bg-green-100 text-green-600",
                        testResults.grade === 'B' && "bg-yellow-100 text-yellow-600",
                        testResults.grade === 'C' && "bg-orange-100 text-orange-600",
                        (testResults.grade === 'D' || testResults.grade === 'F') && "bg-red-100 text-red-600"
                      )}>
                        {testResults.grade}
                      </div>
                      <div>
                        <h3 className="text-xl font-semibold">SSL/TLS Configuration</h3>
                        <p className="text-gray-600">
                          {testResults.grade === 'A+' && "Exceptional - Best possible configuration"}
                          {testResults.grade === 'A' && "Excellent - Strong security"}
                          {testResults.grade === 'B' && "Good - Minor issues"}
                          {testResults.grade === 'C' && "Average - Some concerns"}
                          {testResults.grade === 'D' && "Poor - Major issues"}
                          {testResults.grade === 'F' && "Failed - Critical vulnerabilities"}
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Vulnerabilities */}
              {testResults.vulnerabilities && testResults.vulnerabilities.length > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2">
                      <ShieldAlert className="w-5 h-5" />
                      <span>Vulnerabilities Found</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {testResults.vulnerabilities.map((vuln: any, index: number) => (
                        <div key={index} className={cn(
                          "p-3 rounded-lg border-l-4",
                          vuln.severity === 'critical' && "border-red-500 bg-red-50",
                          vuln.severity === 'high' && "border-orange-500 bg-orange-50",
                          vuln.severity === 'medium' && "border-yellow-500 bg-yellow-50",
                          vuln.severity === 'low' && "border-blue-500 bg-blue-50"
                        )}>
                          <div className="flex items-center justify-between">
                            <div>
                              <h4 className="font-semibold">{vuln.name}</h4>
                              <p className="text-sm text-gray-600">{vuln.description}</p>
                              {vuln.cve && (
                                <p className="text-xs text-gray-500 mt-1">CVE: {vuln.cve}</p>
                              )}
                            </div>
                            <Badge variant={vuln.severity === 'critical' ? 'destructive' : 'outline'}>
                              {vuln.severity.toUpperCase()}
                            </Badge>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Certificate Information */}
              {testResults.certificate && (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2">
                      <Certificate className="w-5 h-5" />
                      <span>Certificate Information</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <h4 className="font-semibold mb-2">Certificate Details</h4>
                        <div className="space-y-1 text-sm">
                          <div><span className="text-gray-600">Subject:</span> {testResults.certificate.subject}</div>
                          <div><span className="text-gray-600">Issuer:</span> {testResults.certificate.issuer}</div>
                          <div><span className="text-gray-600">Valid From:</span> {testResults.certificate.validFrom}</div>
                          <div><span className="text-gray-600">Valid Until:</span> {testResults.certificate.validUntil}</div>
                          <div><span className="text-gray-600">Serial:</span> <code className="text-xs">{testResults.certificate.serial}</code></div>
                        </div>
                      </div>
                      <div>
                        <h4 className="font-semibold mb-2">Security Properties</h4>
                        <div className="space-y-1 text-sm">
                          <div><span className="text-gray-600">Key Size:</span> {testResults.certificate.keySize} bits</div>
                          <div><span className="text-gray-600">Signature:</span> {testResults.certificate.signature}</div>
                          <div><span className="text-gray-600">Trust:</span> 
                            <Badge variant={testResults.certificate.trusted ? "default" : "destructive"} className="ml-2">
                              {testResults.certificate.trusted ? "Trusted" : "Untrusted"}
                            </Badge>
                          </div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Individual Results */}
              {results.length > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2">
                      <List className="w-5 h-5" />
                      <span>Detailed Results</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      {results.map((result, index) => (
                        <div key={index} className="flex items-center justify-between p-2 border rounded">
                          <div className="flex items-center space-x-2">
                            {result.status === 'OK' && <CheckCircle className="w-4 h-4 text-green-600" />}
                            {result.status === 'WARN' && <AlertTriangle className="w-4 h-4 text-yellow-600" />}
                            {result.status === 'CRITICAL' && <XCircle className="w-4 h-4 text-red-600" />}
                            <span className="text-sm">{result.test}</span>
                          </div>
                          <div className="flex items-center space-x-2">
                            <span className="text-sm text-gray-600">{result.finding}</span>
                            <Badge variant="outline" className="text-xs">
                              {result.severity}
                            </Badge>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}
            </div>
          )}
        </TabsContent>

        <TabsContent value="command" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Code className="w-5 h-5" />
                <span>Generated Command</span>
              </CardTitle>
              <CardDescription>
                Review and copy the TestSSL command
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="bg-gray-900 rounded-lg p-4">
                  <code className="text-green-400 font-mono text-sm whitespace-pre-wrap">
                    {generateCommand()}
                  </code>
                </div>
                <div className="flex space-x-2">
                  <Button
                    variant="outline"
                    onClick={() => {
                      navigator.clipboard.writeText(generateCommand());
                      toast.success('Command copied to clipboard');
                    }}
                  >
                    <Copy className="w-4 h-4 mr-2" />
                    Copy Command
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* SSL/TLS Vulnerability Reference */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Book className="w-5 h-5" />
                <span>SSL/TLS Vulnerability Reference</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {Object.entries(SSL_VULNERABILITIES).map(([key, vuln]) => (
                  <div key={key} className="flex items-center justify-between p-2 border rounded">
                    <div>
                      <div className="font-semibold text-sm">{vuln.name}</div>
                      <div className="text-xs text-gray-500">{vuln.cve}</div>
                    </div>
                    <Badge variant={vuln.severity === 'critical' ? 'destructive' : 'outline'}>
                      {vuln.severity}
                    </Badge>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}