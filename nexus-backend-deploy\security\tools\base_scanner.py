#!/usr/bin/env python3
"""
Base Scanner Class with Railway Support
Provides common functionality for all security scanning tools
"""

import os
import logging
import asyncio
from typing import Dict, Any, Optional, List, Callable
from datetime import datetime
from abc import abstractmethod

from security.tools.tool_registry import SecurityTool, ToolMetadata, ScanOptions, ScanResult, ToolStatus
from security.tools.railway_adapter import railway_adapter

logger = logging.getLogger(__name__)


class BaseScanner(SecurityTool):
    """Base class for all security scanners with Railway support"""
    
    def __init__(self):
        """Initialize base scanner"""
        self.railway_mode = railway_adapter.environment.is_railway
        self.simulation_available = True
        super().__init__()
        
    def check_availability(self) -> bool:
        """Check if tool is available (always true on Railway with simulation)"""
        # On Railway, always return True if simulation is available
        if self.railway_mode and self.simulation_available:
            return True
            
        # Otherwise, check for actual tool
        return self.check_native_availability()
    
    @abstractmethod
    def check_native_availability(self) -> bool:
        """Check if the native tool is available (override in subclass)"""
        pass
    
    async def scan(self, options: ScanOptions, 
                   progress_callback: Optional[Callable[[float, str], None]] = None) -> ScanResult:
        """Execute scan with Railway support"""
        start_time = datetime.now().isoformat()
        
        try:
            # Report starting
            if progress_callback:
                await progress_callback(0.1, "Initializing scan...")
            
            # Validate options
            if not self.validate_options(options):
                return ScanResult(
                    tool_name=self.metadata.name,
                    target=options.target,
                    status=ToolStatus.FAILED,
                    start_time=start_time,
                    errors=["Invalid scan options"]
                )
            
            # Check if we should use simulation
            if self.railway_mode and railway_adapter.should_use_simulation(self.metadata.name):
                if progress_callback:
                    await progress_callback(0.3, "Running in simulation mode (Railway environment)...")
                
                # Execute with simulation
                result_data = await railway_adapter.execute_with_simulation(
                    self.metadata.name,
                    [],  # command would go here
                    options.target,
                    options.custom_options or {}
                )
                
                if progress_callback:
                    await progress_callback(0.9, "Processing simulated results...")
                
                # Convert to ScanResult
                return ScanResult(
                    tool_name=self.metadata.name,
                    target=options.target,
                    status=ToolStatus.COMPLETED,
                    start_time=start_time,
                    end_time=datetime.now().isoformat(),
                    duration_seconds=(datetime.now() - datetime.fromisoformat(start_time)).total_seconds(),
                    parsed_results=result_data,
                    metadata={"simulation_mode": True, "railway_environment": True}
                )
            else:
                # Execute native scan
                if progress_callback:
                    await progress_callback(0.3, "Executing native scan...")
                
                return await self.execute_native_scan(options, progress_callback)
                
        except Exception as e:
            logger.error(f"Scan failed for {self.metadata.name}: {e}")
            return ScanResult(
                tool_name=self.metadata.name,
                target=options.target,
                status=ToolStatus.FAILED,
                start_time=start_time,
                end_time=datetime.now().isoformat(),
                errors=[str(e)]
            )
    
    @abstractmethod
    async def execute_native_scan(self, options: ScanOptions, 
                                  progress_callback: Optional[Callable[[float, str], None]] = None) -> ScanResult:
        """Execute the actual native scan (override in subclass)"""
        pass
    
    def get_execution_mode(self) -> str:
        """Get current execution mode"""
        if self.railway_mode:
            return "railway_simulation"
        elif self.check_native_availability():
            return "native"
        else:
            return "unavailable"
    
    def get_status_info(self) -> Dict[str, Any]:
        """Get detailed status information"""
        return {
            "name": self.metadata.name,
            "available": self.is_available,
            "execution_mode": self.get_execution_mode(),
            "railway_environment": self.railway_mode,
            "simulation_available": self.simulation_available,
            "native_available": self.check_native_availability() if not self.railway_mode else False,
            "version": self.metadata.version,
            "category": self.metadata.category.value
        }