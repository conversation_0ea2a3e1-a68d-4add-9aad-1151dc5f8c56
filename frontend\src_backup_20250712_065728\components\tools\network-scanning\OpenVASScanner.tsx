/**
 * OpenVAS Scanner Component
 * Comprehensive vulnerability assessment and management framework
 */
import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { 
  Shield,
  Play,
  Square,
  Download,
  Settings,
  Target,
  AlertTriangle,
  CheckCircle,
  Clock,
  Terminal,
  FileText,
  Copy,
  RotateCcw,
  Database,
  BarChart3,
  Filter,
  Scan,
  Users,
  Lock,
  TrendingUp,
  Activity,
  Eye,
  Zap
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useBackendStore } from '@/stores/backend-store';
import { apiClient, ToolExecution } from '@/services/api-client';
import { toast } from 'sonner';

/**
 * OpenVAS scan configuration interface
 */
interface OpenVASConfig {
  targets: string;
  scanConfigId: string;
  scannerId: string;
  portList: string;
  credentials: {
    username: string;
    password: string;
    type: 'ssh' | 'smb' | 'snmp' | 'http';
  } | null;
  preferences: {
    maxChecks: number;
    maxHosts: number;
    sourceInterface: string;
    optimizeTest: boolean;
    reverseLookup: boolean;
    dropPrivileges: boolean;
  };
  scheduleEnabled: boolean;
  scheduleTime: string;
  alertsEnabled: boolean;
  reportFormat: 'pdf' | 'html' | 'xml' | 'csv' | 'txt';
  customArgs: string;
}

/**
 * OpenVAS scan configurations (predefined)
 */
const SCAN_CONFIGS = {
  'full-and-fast': {
    name: 'Full and Fast',
    description: 'Comprehensive scan with optimal speed/accuracy balance',
    estimatedTime: '2-4 hours',
    severity: 'medium'
  },
  'full-and-deep': {
    name: 'Full and Deep',
    description: 'Thorough scan with maximum vulnerability coverage',
    estimatedTime: '4-8 hours',
    severity: 'high'
  },
  'system-discovery': {
    name: 'System Discovery',
    description: 'Basic host and service discovery',
    estimatedTime: '30-60 minutes',
    severity: 'low'
  },
  'host-discovery': {
    name: 'Host Discovery',
    description: 'Simple host availability check',
    estimatedTime: '5-15 minutes',
    severity: 'low'
  },
  'full-and-very-deep': {
    name: 'Full and Very Deep',
    description: 'Maximum depth vulnerability assessment',
    estimatedTime: '8-24 hours',
    severity: 'very-high'
  },
  'full-and-very-deep-ultimate': {
    name: 'Full and Very Deep Ultimate',
    description: 'Ultimate comprehensive vulnerability scan',
    estimatedTime: '24+ hours',
    severity: 'extreme'
  }
};

/**
 * Port list configurations
 */
const PORT_LISTS = {
  'all-iana-assigned': 'All IANA assigned TCP and UDP ports (1-65535)',
  'all-tcp': 'All TCP ports (1-65535)',
  'all-tcp-nmap-top-1000': 'Top 1000 TCP ports by frequency',
  'all-privileged-tcp': 'All privileged TCP ports (1-1023)',
  'all-tcp-nmap-top-100': 'Top 100 TCP ports',
  'openvas-default': 'OpenVAS default port list'
};

/**
 * Vulnerability severity levels
 */
const SEVERITY_LEVELS = [
  { value: 'log', label: 'Log', color: 'gray', description: 'Informational entries' },
  { value: 'low', label: 'Low', color: 'blue', description: 'Minor security issues' },
  { value: 'medium', label: 'Medium', color: 'yellow', description: 'Moderate vulnerabilities' },
  { value: 'high', label: 'High', color: 'orange', description: 'Serious security flaws' },
  { value: 'critical', label: 'Critical', color: 'red', description: 'Critical vulnerabilities' }
];

/**
 * OpenVAS Scanner Component
 */
export function OpenVASScanner() {
  // State management
  const [config, setConfig] = React.useState<OpenVASConfig>({
    targets: '',
    scanConfigId: 'full-and-fast',
    scannerId: 'default',
    portList: 'all-tcp-nmap-top-1000',
    credentials: null,
    preferences: {
      maxChecks: 5,
      maxHosts: 20,
      sourceInterface: '',
      optimizeTest: true,
      reverseLookup: false,
      dropPrivileges: true
    },
    scheduleEnabled: false,
    scheduleTime: '',
    alertsEnabled: false,
    reportFormat: 'html',
    customArgs: ''
  });
  
  const [isScanning, setIsScanning] = React.useState(false);
  const [currentExecution, setCurrentExecution] = React.useState<ToolExecution | null>(null);
  const [progress, setProgress] = React.useState(0);
  const [output, setOutput] = React.useState<string[]>([]);
  const [results, setResults] = React.useState<any>(null);
  const [activeTab, setActiveTab] = React.useState('configure');
  const [useCredentials, setUseCredentials] = React.useState(false);
  const [vulnerabilityStats, setVulnerabilityStats] = React.useState<any>(null);
  
  // Backend connection
  const { status: backendStatus } = useBackendStore();
  
  // Update configuration
  const updateConfig = (updates: Partial<OpenVASConfig>) => {
    setConfig(prev => ({ ...prev, ...updates }));
  };
  
  // Update preferences
  const updatePreferences = (updates: Partial<OpenVASConfig['preferences']>) => {
    setConfig(prev => ({
      ...prev,
      preferences: { ...prev.preferences, ...updates }
    }));
  };
  
  // Update credentials
  const updateCredentials = (updates: Partial<NonNullable<OpenVASConfig['credentials']>>) => {
    setConfig(prev => ({
      ...prev,
      credentials: prev.credentials ? { ...prev.credentials, ...updates } : {
        username: '',
        password: '',
        type: 'ssh',
        ...updates
      }
    }));
  };
  
  // Apply scan configuration preset
  const applyConfigPreset = (configId: string) => {
    updateConfig({ scanConfigId: configId });
    const config = SCAN_CONFIGS[configId as keyof typeof SCAN_CONFIGS];
    if (config) {
      toast.success(`Applied ${config.name} configuration`);
    }
  };
  
  // Get estimated scan time
  const getEstimatedTime = (): string => {
    const scanConfig = SCAN_CONFIGS[config.scanConfigId as keyof typeof SCAN_CONFIGS];
    return scanConfig?.estimatedTime || 'Unknown';
  };
  
  // Get severity level styling
  const getSeverityColor = (severity: string) => {
    const level = SEVERITY_LEVELS.find(s => s.value === severity);
    return level?.color || 'gray';
  };
  
  // Generate OpenVAS command
  const generateCommand = (): string => {
    let command = 'gvmd --xml="<create_task>"';
    
    // Task name and comment
    command += `<name>NexusScan-${Date.now()}</name>`;
    command += `<comment>Automated vulnerability scan</comment>`;
    
    // Target
    command += `<target id="${config.targets}"/>`;
    
    // Scan configuration
    command += `<config id="${config.scanConfigId}"/>`;
    
    // Scanner
    command += `<scanner id="${config.scannerId}"/>`;
    
    // Preferences
    if (config.preferences.maxChecks !== 5) {
      command += `<preferences><preference><scanner_name>max_checks</scanner_name><value>${config.preferences.maxChecks}</value></preference></preferences>`;
    }
    
    // Schedule
    if (config.scheduleEnabled && config.scheduleTime) {
      command += `<schedule id="schedule-${config.scheduleTime}"/>`;
    }
    
    command += '"</create_task>"';
    
    // Custom arguments
    if (config.customArgs.trim()) {
      command += ` ${config.customArgs.trim()}`;
    }
    
    return command;
  };
  
  // Start scan
  const startScan = async () => {
    if (!config.targets.trim()) {
      toast.error('Please specify target hosts');
      return;
    }
    
    if (!backendStatus.connected) {
      toast.error('Backend not connected');
      return;
    }
    
    try {
      setIsScanning(true);
      setProgress(0);
      setOutput([]);
      setResults(null);
      setVulnerabilityStats(null);
      setActiveTab('output');
      
      const execution = await apiClient.executeTool('openvas', config, (progressUpdate) => {
        setCurrentExecution(progressUpdate);
        setProgress(progressUpdate.progress);
        if (progressUpdate.output) {
          setOutput(prev => [...prev, ...progressUpdate.output!]);
        }
        
        if (progressUpdate.status === 'completed') {
          setIsScanning(false);
          setResults(progressUpdate.results);
          
          // Parse vulnerability statistics
          if (progressUpdate.results?.vulnerabilities) {
            const stats = progressUpdate.results.vulnerabilities.reduce((acc: any, vuln: any) => {
              const severity = vuln.severity || 'low';
              acc[severity] = (acc[severity] || 0) + 1;
              return acc;
            }, {});
            setVulnerabilityStats(stats);
          }
          
          toast.success('OpenVAS scan completed');
        } else if (progressUpdate.status === 'failed') {
          setIsScanning(false);
          toast.error(`Scan failed: ${progressUpdate.error}`);
        }
      });
      
      setCurrentExecution(execution);
      toast.info('OpenVAS scan started');
    } catch (error) {
      setIsScanning(false);
      console.error('Failed to start OpenVAS scan:', error);
      toast.error('Failed to start scan');
    }
  };
  
  // Stop scan
  const stopScan = async () => {
    if (currentExecution) {
      try {
        await apiClient.cancelExecution(currentExecution.id);
        setIsScanning(false);
        setCurrentExecution(null);
        toast.info('Scan cancelled');
      } catch (error) {
        console.error('Failed to stop scan:', error);
        toast.error('Failed to stop scan');
      }
    }
  };
  
  // Copy command to clipboard
  const copyCommand = async () => {
    try {
      await navigator.clipboard.writeText(generateCommand());
      toast.success('Command copied to clipboard');
    } catch (error) {
      toast.error('Failed to copy command');
    }
  };
  
  // Export results
  const exportResults = () => {
    if (!results && output.length === 0) {
      toast.error('No results to export');
      return;
    }
    
    const data = results || output.join('\n');
    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `openvas-scan-${new Date().toISOString().slice(0, 19)}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    
    toast.success('Results exported');
  };
  
  return (
    <div className="container-desktop py-6 space-y-6">
      {/* Tool Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className="p-2 bg-green-100 dark:bg-green-900/20 rounded-lg">
            <Shield className="h-6 w-6 text-green-600 dark:text-green-400" />
          </div>
          <div>
            <h1 className="text-2xl font-bold">OpenVAS Scanner</h1>
            <p className="text-muted-foreground">
              Comprehensive vulnerability assessment and management framework
            </p>
          </div>
        </div>
        
        {/* Status indicators */}
        <div className="flex items-center gap-2">
          <Badge variant={backendStatus.connected ? 'default' : 'destructive'}>
            {backendStatus.connected ? 'Connected' : 'Offline'}
          </Badge>
          {isScanning && (
            <Badge variant="secondary" className="animate-pulse">
              Scanning...
            </Badge>
          )}
        </div>
      </div>
      
      {/* Main Interface */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="configure" className="flex items-center gap-2">
            <Settings className="h-4 w-4" />
            Configure
          </TabsTrigger>
          <TabsTrigger value="output" className="flex items-center gap-2">
            <Terminal className="h-4 w-4" />
            Output
          </TabsTrigger>
          <TabsTrigger value="results" className="flex items-center gap-2">
            <FileText className="h-4 w-4" />
            Results
          </TabsTrigger>
          <TabsTrigger value="command" className="flex items-center gap-2">
            <Target className="h-4 w-4" />
            Command
          </TabsTrigger>
        </TabsList>
        
        {/* Configuration Tab */}
        <TabsContent value="configure" className="space-y-6">
          <div className="grid gap-6 lg:grid-cols-2">
            {/* Target Configuration */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Target className="h-5 w-5" />
                  Target Configuration
                </CardTitle>
                <CardDescription>
                  Specify targets and scan scope for vulnerability assessment
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Targets */}
                <div className="space-y-2">
                  <label className="text-sm font-medium">Target Hosts</label>
                  <Textarea
                    placeholder="e.g., ***********, 10.0.0.0/24, example.com"
                    value={config.targets}
                    onChange={(e) => updateConfig({ targets: e.target.value })}
                    rows={3}
                  />
                  <p className="text-xs text-muted-foreground">
                    IP addresses, CIDR ranges, or hostnames (one per line)
                  </p>
                </div>
                
                {/* Scan Configuration */}
                <div className="space-y-2">
                  <label className="text-sm font-medium">Scan Configuration</label>
                  <Select 
                    value={config.scanConfigId} 
                    onValueChange={(value) => updateConfig({ scanConfigId: value })}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {Object.entries(SCAN_CONFIGS).map(([id, scanConfig]) => (
                        <SelectItem key={id} value={id}>
                          <div className="flex flex-col">
                            <span>{scanConfig.name}</span>
                            <span className="text-xs text-muted-foreground">
                              {scanConfig.description}
                            </span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <div className="flex items-center gap-2 text-xs text-muted-foreground">
                    <Clock className="h-3 w-3" />
                    <span>Estimated time: {getEstimatedTime()}</span>
                  </div>
                </div>
                
                {/* Port List */}
                <div className="space-y-2">
                  <label className="text-sm font-medium">Port List</label>
                  <Select 
                    value={config.portList} 
                    onValueChange={(value) => updateConfig({ portList: value })}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {Object.entries(PORT_LISTS).map(([id, description]) => (
                        <SelectItem key={id} value={id}>
                          <div className="flex flex-col">
                            <span className="capitalize">{id.replace(/-/g, ' ')}</span>
                            <span className="text-xs text-muted-foreground">
                              {description}
                            </span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                
                {/* Report Format */}
                <div className="space-y-2">
                  <label className="text-sm font-medium">Report Format</label>
                  <Select 
                    value={config.reportFormat} 
                    onValueChange={(value) => updateConfig({ reportFormat: value as any })}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="html">HTML Report</SelectItem>
                      <SelectItem value="pdf">PDF Report</SelectItem>
                      <SelectItem value="xml">XML Data</SelectItem>
                      <SelectItem value="csv">CSV Export</SelectItem>
                      <SelectItem value="txt">Text Summary</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </CardContent>
            </Card>
            
            {/* Advanced Options */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Database className="h-5 w-5" />
                  Scan Preferences
                </CardTitle>
                <CardDescription>
                  Advanced scanning options and performance tuning
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Performance Settings */}
                <div className="space-y-3">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Max Concurrent Checks</label>
                    <Input
                      type="number"
                      min="1"
                      max="10"
                      value={config.preferences.maxChecks}
                      onChange={(e) => updatePreferences({ maxChecks: parseInt(e.target.value) || 5 })}
                    />
                    <p className="text-xs text-muted-foreground">
                      Number of simultaneous vulnerability checks per host
                    </p>
                  </div>
                  
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Max Concurrent Hosts</label>
                    <Input
                      type="number"
                      min="1"
                      max="100"
                      value={config.preferences.maxHosts}
                      onChange={(e) => updatePreferences({ maxHosts: parseInt(e.target.value) || 20 })}
                    />
                    <p className="text-xs text-muted-foreground">
                      Number of hosts to scan simultaneously
                    </p>
                  </div>
                </div>
                
                {/* Scan Options */}
                <div className="space-y-3">
                  <label className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      checked={config.preferences.optimizeTest}
                      onChange={(e) => updatePreferences({ optimizeTest: e.target.checked })}
                      className="rounded border-gray-300"
                    />
                    <span className="text-sm">Optimize Test</span>
                  </label>
                  
                  <label className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      checked={config.preferences.reverseLookup}
                      onChange={(e) => updatePreferences({ reverseLookup: e.target.checked })}
                      className="rounded border-gray-300"
                    />
                    <span className="text-sm">Reverse DNS Lookup</span>
                  </label>
                  
                  <label className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      checked={config.preferences.dropPrivileges}
                      onChange={(e) => updatePreferences({ dropPrivileges: e.target.checked })}
                      className="rounded border-gray-300"
                    />
                    <span className="text-sm">Drop Privileges</span>
                  </label>
                </div>
                
                {/* Source Interface */}
                <div className="space-y-2">
                  <label className="text-sm font-medium">Source Interface (Optional)</label>
                  <Input
                    placeholder="e.g., eth0, wlan0"
                    value={config.preferences.sourceInterface}
                    onChange={(e) => updatePreferences({ sourceInterface: e.target.value })}
                  />
                </div>
              </CardContent>
            </Card>
          </div>
          
          {/* Authentication */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Lock className="h-5 w-5" />
                Authentication (Optional)
              </CardTitle>
              <CardDescription>
                Configure authenticated scanning for deeper vulnerability assessment
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={useCredentials}
                  onChange={(e) => {
                    setUseCredentials(e.target.checked);
                    if (!e.target.checked) {
                      updateConfig({ credentials: null });
                    } else {
                      updateConfig({ 
                        credentials: { username: '', password: '', type: 'ssh' }
                      });
                    }
                  }}
                  className="rounded border-gray-300"
                />
                <span className="text-sm font-medium">Enable Authenticated Scanning</span>
              </div>
              
              {useCredentials && (
                <div className="grid gap-4 md:grid-cols-2">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Authentication Type</label>
                    <Select 
                      value={config.credentials?.type || 'ssh'} 
                      onValueChange={(value) => updateCredentials({ type: value as any })}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="ssh">SSH</SelectItem>
                        <SelectItem value="smb">SMB/CIFS</SelectItem>
                        <SelectItem value="snmp">SNMP</SelectItem>
                        <SelectItem value="http">HTTP/HTTPS</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Username</label>
                    <Input
                      placeholder="Username"
                      value={config.credentials?.username || ''}
                      onChange={(e) => updateCredentials({ username: e.target.value })}
                    />
                  </div>
                  
                  <div className="space-y-2 md:col-span-2">
                    <label className="text-sm font-medium">Password</label>
                    <Input
                      type="password"
                      placeholder="Password"
                      value={config.credentials?.password || ''}
                      onChange={(e) => updateCredentials({ password: e.target.value })}
                    />
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
          
          {/* Quick Presets and Actions */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Zap className="h-5 w-5" />
                Quick Configurations & Actions
              </CardTitle>
              <CardDescription>
                Use predefined scan configurations or start your vulnerability assessment
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {/* Configuration presets */}
                <div>
                  <h4 className="text-sm font-medium mb-3">Scan Configurations</h4>
                  <div className="flex flex-wrap gap-2">
                    {Object.entries(SCAN_CONFIGS).map(([id, scanConfig]) => (
                      <Button
                        key={id}
                        variant={config.scanConfigId === id ? 'default' : 'outline'}
                        size="sm"
                        onClick={() => applyConfigPreset(id)}
                        className="flex items-center gap-2"
                      >
                        <div className={cn(
                          'w-2 h-2 rounded-full',
                          scanConfig.severity === 'low' && 'bg-blue-500',
                          scanConfig.severity === 'medium' && 'bg-yellow-500',
                          scanConfig.severity === 'high' && 'bg-orange-500',
                          scanConfig.severity === 'very-high' && 'bg-red-500',
                          scanConfig.severity === 'extreme' && 'bg-purple-500'
                        )} />
                        {scanConfig.name}
                      </Button>
                    ))}
                  </div>
                </div>
                
                {/* Action buttons */}
                <div className="flex items-center justify-between pt-4 border-t">
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={copyCommand}
                    >
                      <Copy className="h-4 w-4 mr-1" />
                      Copy Command
                    </Button>
                  </div>
                  
                  <div className="flex gap-2">
                    {isScanning ? (
                      <Button
                        variant="destructive"
                        onClick={stopScan}
                      >
                        <Square className="h-4 w-4 mr-2" />
                        Stop Scan
                      </Button>
                    ) : (
                      <Button
                        onClick={startScan}
                        disabled={!config.targets.trim() || !backendStatus.connected}
                      >
                        <Play className="h-4 w-4 mr-2" />
                        Start Vulnerability Scan
                      </Button>
                    )}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        {/* Output Tab */}
        <TabsContent value="output" className="space-y-6">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="flex items-center gap-2">
                    <Terminal className="h-5 w-5" />
                    Real-time Output
                  </CardTitle>
                  <CardDescription>
                    Live output from the OpenVAS scan process
                  </CardDescription>
                </div>
                
                {/* Progress and controls */}
                <div className="flex items-center gap-4">
                  {isScanning && (
                    <div className="flex items-center gap-2">
                      <Progress value={progress} className="w-32" />
                      <span className="text-sm text-muted-foreground">
                        {progress}%
                      </span>
                    </div>
                  )}
                  
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setOutput([])}
                    disabled={isScanning}
                  >
                    <RotateCcw className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="bg-gray-900 text-gray-100 p-4 rounded-lg h-96 overflow-y-auto font-mono text-sm">
                {output.length === 0 ? (
                  <div className="text-gray-500 italic">
                    {isScanning ? 'Waiting for output...' : 'No output yet. Start a scan to see results here.'}
                  </div>
                ) : (
                  output.map((line, index) => (
                    <div key={index} className="mb-1">
                      {line}
                    </div>
                  ))
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        {/* Results Tab */}
        <TabsContent value="results" className="space-y-6">
          {/* Vulnerability Statistics */}
          {vulnerabilityStats && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BarChart3 className="h-5 w-5" />
                  Vulnerability Summary
                </CardTitle>
                <CardDescription>
                  Overview of discovered vulnerabilities by severity
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid gap-4 md:grid-cols-5">
                  {SEVERITY_LEVELS.map((level) => (
                    <div key={level.value} className="text-center">
                      <div className={cn(
                        'text-2xl font-bold',
                        level.color === 'gray' && 'text-gray-600',
                        level.color === 'blue' && 'text-blue-600',
                        level.color === 'yellow' && 'text-yellow-600',
                        level.color === 'orange' && 'text-orange-600',
                        level.color === 'red' && 'text-red-600'
                      )}>
                        {vulnerabilityStats[level.value] || 0}
                      </div>
                      <div className="text-sm text-muted-foreground">{level.label}</div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
          
          {/* Detailed Results */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="flex items-center gap-2">
                    <FileText className="h-5 w-5" />
                    Detailed Results
                  </CardTitle>
                  <CardDescription>
                    Comprehensive vulnerability assessment results
                  </CardDescription>
                </div>
                
                <Button
                  variant="outline"
                  onClick={exportResults}
                  disabled={!results && output.length === 0}
                >
                  <Download className="h-4 w-4 mr-2" />
                  Export Results
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              {results ? (
                <div className="space-y-4">
                  <pre className="bg-muted p-4 rounded-lg overflow-auto text-sm max-h-96">
                    {JSON.stringify(results, null, 2)}
                  </pre>
                </div>
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  <Shield className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>No vulnerability results available yet.</p>
                  <p className="text-sm">Complete a scan to see detailed vulnerability assessment here.</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
        
        {/* Command Tab */}
        <TabsContent value="command" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Target className="h-5 w-5" />
                Generated Command
              </CardTitle>
              <CardDescription>
                The OpenVAS command that will be executed based on your configuration
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="bg-muted p-4 rounded-lg">
                  <code className="text-sm font-mono break-all">
                    {generateCommand()}
                  </code>
                </div>
                
                <div className="flex justify-between items-center">
                  <p className="text-sm text-muted-foreground">
                    This command will be executed on the backend server
                  </p>
                  
                  <Button variant="outline" onClick={copyCommand}>
                    <Copy className="h-4 w-4 mr-2" />
                    Copy Command
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}