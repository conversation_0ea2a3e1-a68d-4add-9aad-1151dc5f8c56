#!/usr/bin/env python3
"""
NexusScan Security Scanning Engine
Comprehensive security scanning with Nmap, Nuclei, and custom tools
"""

from .scan_orchestrator import (
    ScanOrchestrator, ScanRequest, ScanResult, ScanProgress,
    ScanStatus, ScanType
)

# Note: Nmap and Nuclei scanners are in security.tools.* modules
# from .nmap_scanner import (
#     NmapScanner, NmapScanRequest, NmapScanResult
# )

# from .nuclei_scanner import (
#     NucleiScanner, NucleiScanRequest, NucleiScanResult
# )

# Note: ToolManager functionality provided by security.unified_tool_manager
# from .tool_manager import (
#     ToolManager, ToolConfig, ToolResult, ToolStatus
# )

from .result_processor import (
    ResultProcessor, ProcessedResult, VulnerabilityFinding,
    ToolResult, ToolFinding, ToolConfig, ToolManager
)

from .progress_tracker import (
    ProgressTracker, ProgressUpdate, ProgressStatus
)

__version__ = "1.0.0"
__author__ = "NexusScan Development Team"

__all__ = [
    # Main orchestrator
    "ScanOrchestrator",
    "ScanRequest", 
    "ScanResult",
    "ScanProgress",
    "ScanStatus",
    "ScanType",
    
    # Tool integrations (available in security.tools.*)
    # "NmapScanner",
    # "NmapScanRequest", 
    # "NmapScanResult",
    # "NucleiScanner", 
    # "NucleiScanRequest",
    # "NucleiScanResult",
    
    # Core components
    "ToolManager",
    "ToolConfig", 
    "ToolResult",
    "ToolFinding",
    # "ToolStatus",
    "ResultProcessor",
    "ProcessedResult",
    "VulnerabilityFinding",
    "ProgressTracker",
    "ProgressUpdate",
    "ProgressStatus"
]