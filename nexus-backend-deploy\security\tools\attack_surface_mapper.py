"""
Attack Surface Mapper for NexusScan Desktop Application
AI-powered attack surface analysis and mapping.
"""

import json
import asyncio
import logging
from datetime import datetime
from typing import Dict, List, Optional, Callable, Any
from dataclasses import dataclass

from security.tools.tool_registry import (
    ToolMetadata, ToolCategory, ToolCapabilities,
    ScanOptions, ScanResult, ToolStatus, register_tool
)
from security.tools.base_scanner import BaseScanner
from ai.services import AIServiceManager, AIProvider

logger = logging.getLogger(__name__)


@dataclass
class AttackSurfaceOptions(ScanOptions):
    """Attack surface mapping options"""
    depth: str = "comprehensive"  # basic, comprehensive, deep
    include_subdomains: bool = True
    include_ports: bool = True
    include_technologies: bool = True
    include_vulnerabilities: bool = True
    ai_analysis: bool = True
    risk_scoring: bool = True
    
    def __post_init__(self):
        super().__post_init__()


@dataclass
class AttackSurfaceComponent:
    """Individual attack surface component"""
    component_type: str
    identifier: str
    description: str
    risk_level: str
    technologies: List[str]
    entry_points: List[str]
    potential_attacks: List[str]
    recommendations: List[str]


class AttackSurfaceMapper(BaseScanner):
    """AI-powered attack surface analysis and mapping"""
    
    def __init__(self):
        super().__init__()
        self.tool_name = "attack_surface_mapper"
        self.scan_types = ["basic", "comprehensive", "deep"]
        self.ai_manager = None
        
    def get_metadata(self) -> ToolMetadata:
        """Get tool metadata"""
        return ToolMetadata(
            name="Attack Surface Mapper",
            category=ToolCategory.AI_ANALYZER,
            description="AI-powered attack surface analysis and mapping",
            version="1.0.0",
            author="NexusScan AI Team",
            dependencies=["ai_services"],
            capabilities=ToolCapabilities(
                scan_types=self.scan_types,
                output_formats=["json", "html", "pdf"],
                ai_powered=True,
                real_time_analysis=True,
                risk_assessment=True
            )
        )
    
    def is_available(self) -> bool:
        """Check if AI services are available"""
        try:
            from ai.services import AIServiceManager
            return True
        except ImportError:
            return False
    
    async def _get_ai_manager(self) -> AIServiceManager:
        """Get AI service manager"""
        if self.ai_manager is None:
            from ai.services import AIServiceManager
            self.ai_manager = AIServiceManager()
        return self.ai_manager
    
    async def scan(self, 
                   target: str, 
                   options: Optional[AttackSurfaceOptions] = None,
                   progress_callback: Optional[Callable] = None) -> ScanResult:
        """Perform attack surface mapping"""
        if options is None:
            options = AttackSurfaceOptions(target=target)
        
        scan_id = f"attack_surface_{target.replace('://', '_').replace('/', '_')}"
    
    def check_native_availability(self) -> bool:
        """Check if native tool is available"""
        return self.is_available()"
    
    async def execute_native_scan(self, options: ScanOptions,
                                 progress_callback: Optional[Callable[[float, str], None]] = None) -> ScanResult:}"
        """Execute native scan""}"
        if not isinstance(options, AttackSurfaceOptions):
            options = AttackSurfaceOptions(target=options.target)
        
        return await self.scan(options.target, options, progress_callback)"
        start_time = datetime.now()
        
        logger.info(f"Starting attack surface mapping: {scan_id}")
        
        if progress_callback:
            await progress_callback(0.1, "Initializing", "Preparing attack surface analysis")
        
        try:
            ai_manager = await self._get_ai_manager()
            
            # Phase 1: Basic reconnaissance
            if progress_callback:
                await progress_callback(0.2, "Reconnaissance", "Gathering target information")
            
            basic_info = await self._gather_basic_info(target, options)
            
            # Phase 2: Technology detection
            if progress_callback:
                await progress_callback(0.4, "Technology Detection", "Identifying technologies and services")
            
            tech_stack = await self._detect_technologies(target, basic_info, ai_manager)
            
            # Phase 3: Entry point analysis
            if progress_callback:
                await progress_callback(0.6, "Entry Point Analysis", "Analyzing potential entry points")
            
            entry_points = await self._analyze_entry_points(target, tech_stack, ai_manager)
            
            # Phase 4: Risk assessment
            if progress_callback:
                await progress_callback(0.8, "Risk Assessment", "Performing AI-powered risk analysis")
            
            risk_analysis = await self._perform_risk_analysis(target, tech_stack, entry_points, ai_manager)
            
            # Phase 5: Generate comprehensive report
            if progress_callback:
                await progress_callback(0.9, "Report Generation", "Generating attack surface map")
            
            attack_surface_map = await self._generate_attack_surface_map(
                target, basic_info, tech_stack, entry_points, risk_analysis, ai_manager
            )
            
            if progress_callback:
                await progress_callback(1.0, "Complete", "Attack surface mapping completed")
            
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            return ScanResult(
                scan_id=scan_id,
                tool_name="attack_surface_mapper",
                target=target,
                status=ToolStatus.COMPLETED,
                start_time=start_time,
                end_time=end_time,
                duration_seconds=duration,
                command_executed=f"attack_surface_mapper --target {target} --depth {options.depth}",
                exit_code=0,
                raw_output=json.dumps(attack_surface_map, indent=2),
                error_output="",
                parsed_results=attack_surface_map,
                vulnerabilities=attack_surface_map.get("vulnerabilities", []),
                summary=attack_surface_map.get("summary", {}),
                metadata={
                    "analysis_depth": options.depth,
                    "ai_analysis_enabled": options.ai_analysis,
                    "components_analyzed": len(attack_surface_map.get("components", [])),
                    "risk_score": attack_surface_map.get("overall_risk_score", 0)
                }
            )
            
        except Exception as e:
            logger.error(f"Attack surface mapping failed: {e}")
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            return ScanResult(
                scan_id=scan_id,
                tool_name="attack_surface_mapper",
                target=target,
                status=ToolStatus.FAILED,
                start_time=start_time,
                end_time=end_time,
                duration_seconds=duration,
                command_executed="",
                exit_code=1,
                raw_output="",
                error_output=str(e),
                parsed_results={},
                vulnerabilities=[],
                summary={"error": str(e)},
                metadata={}
            )
    
    async def _gather_basic_info(self, target: str, options: AttackSurfaceOptions) -> Dict[str, Any]:
        """Gather basic information about the target"""
        basic_info = {
            "target": target,
            "domain": self._extract_domain(target),
            "ip_addresses": [],
            "subdomains": [],
            "ports": [],
            "headers": {},
            "certificates": []
        }
        
        # Simulate gathering basic information
        # In real implementation, this would use tools like nmap, dig, etc.
        if options.include_subdomains:
            basic_info["subdomains"] = [
                f"www.{basic_info['domain']}",
                f"mail.{basic_info['domain']}",
                f"api.{basic_info['domain']}"
            ]
        
        if options.include_ports:
            basic_info["ports"] = [
                {"port": 80, "protocol": "tcp", "service": "http", "state": "open"},
                {"port": 443, "protocol": "tcp", "service": "https", "state": "open"},
                {"port": 22, "protocol": "tcp", "service": "ssh", "state": "open"}
            ]
        
        return basic_info
    
    async def _detect_technologies(self, target: str, basic_info: Dict[str, Any], ai_manager: AIServiceManager) -> Dict[str, Any]:
        """Detect technologies and services"""
        tech_stack = {
            "web_servers": [],
            "frameworks": [],
            "databases": [],
            "programming_languages": [],
            "cms": [],
            "third_party_services": [],
            "security_technologies": []
        }
        
        # Use AI to analyze and identify technologies
        prompt = f"""
        Analyze the following target and identify potential technologies:
        Target: {target}
        Open ports: {basic_info.get('ports', [])}
        Subdomains: {basic_info.get('subdomains', [])}
        
        Please identify:
        1. Web servers (Apache, Nginx, IIS, etc.)
        2. Frameworks (React, Angular, Django, etc.)
        3. Databases (MySQL, PostgreSQL, MongoDB, etc.)
        4. Programming languages
        5. CMS platforms
        6. Third-party services
        7. Security technologies
        
        Provide a structured analysis with confidence levels.
        """
        
        try:
            ai_response = await ai_manager.generate_analysis(
                prompt=prompt,
                analysis_type="technology_detection",
                provider=AIProvider.OPENAI
            )
            
            # Parse AI response and populate tech_stack
            if ai_response and "analysis" in ai_response:
                tech_analysis = ai_response["analysis"]
                tech_stack["web_servers"] = tech_analysis.get("web_servers", ["Apache", "Nginx"])
                tech_stack["frameworks"] = tech_analysis.get("frameworks", ["React", "Bootstrap"])
                tech_stack["programming_languages"] = tech_analysis.get("languages", ["JavaScript", "PHP"])
                tech_stack["cms"] = tech_analysis.get("cms", [])
                tech_stack["databases"] = tech_analysis.get("databases", ["MySQL"])
                tech_stack["security_technologies"] = tech_analysis.get("security", ["SSL/TLS"])
        
        except Exception as e:
            logger.warning(f"AI technology detection failed: {e}")
            # Fallback to basic detection
            tech_stack["web_servers"] = ["Apache/Nginx"]
            tech_stack["frameworks"] = ["Unknown"]
            tech_stack["programming_languages"] = ["JavaScript", "HTML"]
        
        return tech_stack
    
    async def _analyze_entry_points(self, target: str, tech_stack: Dict[str, Any], ai_manager: AIServiceManager) -> List[Dict[str, Any]]:
        """Analyze potential entry points"""
        entry_points = []
        
        # Use AI to identify entry points based on technology stack
        prompt = f"""
        Based on the following technology stack, identify potential entry points for security testing:
        
        Target: {target}
        Web servers: {tech_stack.get('web_servers', [])}
        Frameworks: {tech_stack.get('frameworks', [])}
        Programming languages: {tech_stack.get('programming_languages', [])}
        CMS: {tech_stack.get('cms', [])}
        Databases: {tech_stack.get('databases', [])}
        
        For each entry point, provide:
        1. Entry point type
        2. Location/endpoint
        3. Risk level (low, medium, high, critical)
        4. Potential attack vectors
        5. Recommended security tests
        """
        
        try:
            ai_response = await ai_manager.generate_analysis(
                prompt=prompt,
                analysis_type="entry_point_analysis",
                provider=AIProvider.OPENAI
            )
            
            if ai_response and "analysis" in ai_response:
                entry_points_data = ai_response["analysis"]
                if isinstance(entry_points_data, list):
                    entry_points = entry_points_data
                else:
                    # Generate default entry points
                    entry_points = self._generate_default_entry_points(target, tech_stack)
        
        except Exception as e:
            logger.warning(f"AI entry point analysis failed: {e}")
            entry_points = self._generate_default_entry_points(target, tech_stack)
        
        return entry_points
    
    def _generate_default_entry_points(self, target: str, tech_stack: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Generate default entry points"""
        return [
            {
                "type": "web_interface",
                "location": f"{target}/",
                "risk_level": "medium",
                "attack_vectors": ["XSS", "CSRF", "SQL Injection"],
                "description": "Main web interface"
            },
            {
                "type": "admin_interface",
                "location": f"{target}/admin/",
                "risk_level": "high",
                "attack_vectors": ["Brute force", "Default credentials", "Privilege escalation"],
                "description": "Administrative interface"
            },
            {
                "type": "api_endpoints",
                "location": f"{target}/api/",
                "risk_level": "high",
                "attack_vectors": ["API abuse", "Authentication bypass", "Data exposure"],
                "description": "API endpoints"
            }
        ]
    
    async def _perform_risk_analysis(self, target: str, tech_stack: Dict[str, Any], 
                                   entry_points: List[Dict[str, Any]], ai_manager: AIServiceManager) -> Dict[str, Any]:
        """Perform comprehensive risk analysis"""
        risk_analysis = {
            "overall_risk_score": 0,
            "risk_factors": [],
            "critical_issues": [],
            "recommendations": [],
            "attack_scenarios": []
        }
        
        # Use AI for risk assessment
        prompt = f"""
        Perform a comprehensive risk analysis for the following target:
        
        Target: {target}
        Technologies: {tech_stack}
        Entry points: {entry_points}
        
        Provide:
        1. Overall risk score (0-100)
        2. Key risk factors
        3. Critical security issues
        4. Prioritized recommendations
        5. Potential attack scenarios
        
        Focus on real-world attack vectors and practical security concerns.
        """
        
        try:
            ai_response = await ai_manager.generate_analysis(
                prompt=prompt,
                analysis_type="risk_assessment",
                provider=AIProvider.OPENAI
            )
            
            if ai_response and "analysis" in ai_response:
                risk_data = ai_response["analysis"]
                risk_analysis["overall_risk_score"] = risk_data.get("risk_score", 65)
                risk_analysis["risk_factors"] = risk_data.get("risk_factors", [])
                risk_analysis["critical_issues"] = risk_data.get("critical_issues", [])
                risk_analysis["recommendations"] = risk_data.get("recommendations", [])
                risk_analysis["attack_scenarios"] = risk_data.get("attack_scenarios", [])
        
        except Exception as e:
            logger.warning(f"AI risk analysis failed: {e}")
            # Fallback risk analysis
            risk_analysis["overall_risk_score"] = 65
            risk_analysis["risk_factors"] = [
                "Multiple entry points identified",
                "Web application technologies present",
                "Potential admin interfaces"
            ]
            risk_analysis["recommendations"] = [
                "Implement proper authentication",
                "Regular security testing",
                "Keep software updated"
            ]
        
        return risk_analysis
    
    async def _generate_attack_surface_map(self, target: str, basic_info: Dict[str, Any], 
                                         tech_stack: Dict[str, Any], entry_points: List[Dict[str, Any]], 
                                         risk_analysis: Dict[str, Any], ai_manager: AIServiceManager) -> Dict[str, Any]:
        """Generate comprehensive attack surface map"""
        
        # Generate vulnerabilities from the analysis
        vulnerabilities = []
        for entry_point in entry_points:
            if entry_point.get("risk_level") in ["high", "critical"]:
                vulnerabilities.append({
                    "id": f"attack_surface_{len(vulnerabilities)}",
                    "type": "Attack Surface",
                    "severity": entry_point.get("risk_level", "medium"),
                    "location": entry_point.get("location", ""),
                    "description": f"High-risk entry point: {entry_point.get('description', '')}",
                    "attack_vectors": entry_point.get("attack_vectors", []),
                    "recommendation": "Implement proper security controls and monitoring"
                })
        
        attack_surface_map = {
            "target_info": basic_info,
            "technology_stack": tech_stack,
            "entry_points": entry_points,
            "risk_analysis": risk_analysis,
            "vulnerabilities": vulnerabilities,
            "components": self._generate_components(tech_stack, entry_points),
            "summary": {
                "total_entry_points": len(entry_points),
                "high_risk_points": len([ep for ep in entry_points if ep.get("risk_level") == "high"]),
                "critical_risk_points": len([ep for ep in entry_points if ep.get("risk_level") == "critical"]),
                "overall_risk_score": risk_analysis.get("overall_risk_score", 0),
                "technologies_identified": sum(len(v) for v in tech_stack.values() if isinstance(v, list)),
                "analysis_timestamp": datetime.now().isoformat()
            },
            "overall_risk_score": risk_analysis.get("overall_risk_score", 0)
        }
        
        return attack_surface_map
    
    def _generate_components(self, tech_stack: Dict[str, Any], entry_points: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Generate attack surface components"""
        components = []
        
        # Web server components
        for server in tech_stack.get("web_servers", []):
            components.append({
                "type": "web_server",
                "name": server,
                "risk_level": "medium",
                "entry_points": [ep for ep in entry_points if ep.get("type") == "web_interface"],
                "recommendations": ["Keep server updated", "Proper configuration", "Security headers"]
            })
        
        # Framework components
        for framework in tech_stack.get("frameworks", []):
            components.append({
                "type": "framework",
                "name": framework,
                "risk_level": "medium",
                "entry_points": [ep for ep in entry_points if ep.get("type") == "web_interface"],
                "recommendations": ["Security patches", "Secure coding practices", "Input validation"]
            })
        
        return components
    
    def _extract_domain(self, target: str) -> str:
        """Extract domain from target URL"""
        if "://" in target:
            domain = target.split("://")[1]
        else:
            domain = target
        
        # Remove path and port
        domain = domain.split("/")[0].split(":")[0]
        return domain
    
    def get_frontend_interface_data(self) -> Dict[str, Any]:
        """Get data for frontend interface"""
        return {
            "tool_info": {
                "name": "Attack Surface Mapper",
                "description": "AI-powered attack surface analysis and mapping",
                "version": "1.0.0",
                "category": "AI Analyzer",
                "status": "available" if self.is_available() else "unavailable"
            },
            "scan_options": {
                "target": {
                    "type": "text",
                    "required": True,
                    "placeholder": "https://example.com or example.com",
                    "validation": "url_or_domain"
                },
                "depth": {
                    "type": "select",
                    "options": ["basic", "comprehensive", "deep"],
                    "default": "comprehensive",
                    "label": "Analysis depth"
                },
                "include_subdomains": {
                    "type": "boolean",
                    "default": True,
                    "label": "Include subdomain analysis"
                },
                "include_ports": {
                    "type": "boolean",
                    "default": True,
                    "label": "Include port scanning"
                },
                "include_technologies": {
                    "type": "boolean",
                    "default": True,
                    "label": "Include technology detection"
                },
                "ai_analysis": {
                    "type": "boolean",
                    "default": True,
                    "label": "Enable AI-powered analysis"
                },
                "risk_scoring": {
                    "type": "boolean",
                    "default": True,
                    "label": "Enable risk scoring"
                }
            },
            "output_formats": ["json", "html", "pdf"],
            "capabilities": [
                "AI-powered analysis",
                "Technology detection",
                "Entry point identification",
                "Risk assessment",
                "Attack vector mapping",
                "Comprehensive reporting"
            ]
        }


# Register the tool
register_tool(AttackSurfaceMapper)