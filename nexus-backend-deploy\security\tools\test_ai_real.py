#!/usr/bin/env python3
"""
Test Real AI Integration
"""

import os
import sys

# Add src to path
sys.path.insert(0, 'src')

from core.config import Config
from ai.services import AIServiceManager

def test_ai_integration():
    print("🤖 Testing Real AI Integration")
    
    # Test environment variables
    print(f"OPENAI_API_KEY: {'✅ Set' if os.getenv('OPENAI_API_KEY') else '❌ Not set'}")
    print(f"DEEPSEEK_API_KEY: {'✅ Set' if os.getenv('DEEPSEEK_API_KEY') else '❌ Not set'}")
    print(f"ANTHROPIC_API_KEY: {'✅ Set' if os.getenv('ANTHROPIC_API_KEY') else '❌ Not set'}")
    
    # Test config loading
    config = Config()
    print(f"\nConfig AI Keys:")
    print(f"OpenAI: {'✅ Loaded' if config.ai.openai_api_key else '❌ Not loaded'}")
    print(f"DeepSeek: {'✅ Loaded' if config.ai.deepseek_api_key else '❌ Not loaded'}")
    print(f"Anthropic: {'✅ Loaded' if config.ai.anthropic_api_key else '❌ Not loaded'}")
    
    # Test AI service manager
    ai_manager = AIServiceManager(config)
    print(f"\nAI Service Providers: {len(ai_manager.providers)} initialized")
    
    for provider_name, provider in ai_manager.providers.items():
        print(f"  {provider_name}: ✅ Available")
    
    if len(ai_manager.providers) > 0:
        print("\n✅ AI Integration is working!")
        return True
    else:
        print("\n❌ No AI providers available")
        return False

if __name__ == "__main__":
    test_ai_integration()