@echo off
echo =====================================
echo NexusScan Electron Fix Runner
echo =====================================
echo.

REM Step 1: Kill any existing processes
echo Killing existing processes...
taskkill /F /IM electron.exe 2>nul
taskkill /F /IM node.exe 2>nul

REM Step 2: Build Electron files
echo Building Electron files...
call npm run build:electron

REM Step 3: Start Vite in background
echo Starting Vite dev server...
start /B npm run dev:vite

REM Step 4: Wait for Vite to start
echo Waiting for Vite to start...
:WAIT_VITE
timeout /t 2 /nobreak >nul
curl -s http://localhost:5182 >nul 2>&1
if %errorlevel% neq 0 goto WAIT_VITE

echo Vite is ready!

REM Step 5: Start Electron
echo Starting Electron app...
set NODE_ENV=development
set ELECTRON_ENABLE_LOGGING=1
npx electron .

pause