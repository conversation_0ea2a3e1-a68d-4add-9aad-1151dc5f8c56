/**
 * AI Proxy Page - Ferrari AI-powered proxy and traffic analysis
 */
import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { 
  Network,
  Filter,
  Activity,
  Shield,
  Brain,
  Play,
  Pause,
  Settings,
  Eye
} from 'lucide-react';

export function AIProxyPage() {
  const [proxyStats] = React.useState({
    requestsIntercepted: 1247,
    vulnerabilitiesFound: 23,
    payloadsModified: 89,
    activeConnections: 5
  });

  const [interceptedRequests] = React.useState([
    { id: '1', url: '/api/login', method: 'POST', status: 'modified', vulnerability: 'SQL Injection' },
    { id: '2', url: '/search?q=test', method: 'GET', status: 'analyzed', vulnerability: 'XSS' },
    { id: '3', url: '/api/users/123', method: 'PUT', status: 'passed', vulnerability: null }
  ]);

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Ferrari AI Proxy</h1>
          <p className="text-muted-foreground">Intelligent traffic interception and analysis</p>
        </div>
        <div className="flex items-center gap-3">
          <Badge variant="outline" className="text-green-500">
            <Activity className="mr-1 h-3 w-3" />
            Proxy Active
          </Badge>
          <Button variant="outline">
            <Pause className="mr-2 h-4 w-4" />
            Pause
          </Button>
        </div>
      </div>

      {/* Proxy Statistics */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Requests Intercepted</CardTitle>
            <Network className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{proxyStats.requestsIntercepted}</div>
            <p className="text-xs text-muted-foreground">Total this session</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Vulnerabilities Found</CardTitle>
            <Shield className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{proxyStats.vulnerabilitiesFound}</div>
            <p className="text-xs text-muted-foreground">Potential issues</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Payloads Modified</CardTitle>
            <Brain className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{proxyStats.payloadsModified}</div>
            <p className="text-xs text-muted-foreground">AI-enhanced requests</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Connections</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{proxyStats.activeConnections}</div>
            <p className="text-xs text-muted-foreground">Current connections</p>
          </CardContent>
        </Card>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <div className="flex items-center gap-3">
              <Network className="h-5 w-5" />
              <CardTitle>Proxy Configuration</CardTitle>
            </div>
            <CardDescription>Configure AI proxy settings</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <Input placeholder="Proxy listen address" defaultValue="127.0.0.1:8080" />
              <Input placeholder="Target host" defaultValue="example.com" />
              <div className="flex gap-2">
                <Button><Play className="mr-2 h-4 w-4" />Start Proxy</Button>
                <Button variant="outline"><Settings className="mr-2 h-4 w-4" />Advanced</Button>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <div className="flex items-center gap-3">
              <Filter className="h-5 w-5" />
              <CardTitle>AI Filter Rules</CardTitle>
            </div>
            <CardDescription>Configure AI analysis rules</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <label className="text-sm">Auto-detect vulnerabilities</label>
                <input type="checkbox" defaultChecked />
              </div>
              <div className="flex items-center justify-between">
                <label className="text-sm">Modify payloads</label>
                <input type="checkbox" defaultChecked />
              </div>
              <div className="flex items-center justify-between">
                <label className="text-sm">Block malicious requests</label>
                <input type="checkbox" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Intercepted Requests</CardTitle>
          <CardDescription>Real-time traffic analysis and modification</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {interceptedRequests.map((request) => (
              <div key={request.id} className="flex items-center gap-4 p-4 rounded-lg border">
                <div className="h-10 w-10 rounded-lg bg-primary/10 flex items-center justify-center">
                  <Network className="h-4 w-4" />
                </div>
                <div className="flex-1 space-y-1">
                  <div className="flex items-center gap-3">
                    <Badge variant="secondary">{request.method}</Badge>
                    <p className="text-sm font-medium font-mono">{request.url}</p>
                  </div>
                  {request.vulnerability && (
                    <p className="text-xs text-red-600">Vulnerability detected: {request.vulnerability}</p>
                  )}
                </div>
                <Badge variant="outline" className={
                  request.status === 'modified' ? 'text-yellow-500' :
                  request.status === 'analyzed' ? 'text-blue-500' : 'text-green-500'
                }>
                  {request.status}
                </Badge>
                <Button size="sm" variant="outline">
                  <Eye className="h-3 w-3" />
                </Button>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}