#!/usr/bin/env python3
"""
OpenAI Service Integration for NexusScan
Advanced AI-powered exploit generation and security analysis using OpenAI GPT-4 models
"""

import asyncio
import json
import logging
import time
from datetime import datetime
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass

import openai
from openai import AsyncOpenAI

logger = logging.getLogger(__name__)

@dataclass
class PayloadGenerationRequest:
    """Request for AI payload generation"""
    vulnerability_type: str
    target_context: Dict[str, Any]
    difficulty_level: str = "intermediate"  # beginner, intermediate, advanced, expert
    payload_style: str = "functional"       # functional, obfuscated, polymorphic
    target_environment: str = "web"         # web, network, mobile, api, cloud
    constraints: Dict[str, Any] = None
    custom_requirements: str = ""

    def __post_init__(self):
        if self.constraints is None:
            self.constraints = {}

@dataclass
class PayloadGenerationResponse:
    """Response from AI payload generation"""
    success: bool
    primary_payload: str
    explanation: str
    confidence_score: float
    alternative_payloads: List[str]
    execution_steps: List[str]
    preconditions: List[str]
    post_exploitation_options: List[str]
    risk_assessment: Dict[str, Any]
    remediation_advice: str
    detection_signatures: List[str]
    execution_time: float
    model_used: str
    errors: List[str] = None

    def __post_init__(self):
        if self.errors is None:
            self.errors = []

@dataclass
class SecurityAnalysisRequest:
    """Request for security analysis"""
    scan_data: Dict[str, Any]
    analysis_scope: str = "comprehensive"  # quick, comprehensive, targeted
    focus_areas: List[str] = None
    business_context: Dict[str, Any] = None
    compliance_frameworks: List[str] = None

    def __post_init__(self):
        if self.focus_areas is None:
            self.focus_areas = []
        if self.business_context is None:
            self.business_context = {}
        if self.compliance_frameworks is None:
            self.compliance_frameworks = []

@dataclass
class SecurityAnalysisResponse:
    """Response from security analysis"""
    success: bool
    executive_summary: str
    technical_analysis: str
    risk_score: float
    severity_breakdown: Dict[str, int]
    key_findings: List[Dict[str, Any]]
    attack_vectors: List[str]
    business_impact: str
    remediation_roadmap: List[Dict[str, Any]]
    compliance_status: Dict[str, str]
    recommendations: List[str]
    execution_time: float
    model_used: str
    errors: List[str] = None

    def __post_init__(self):
        if self.errors is None:
            self.errors = []

class OpenAIService:
    """OpenAI service implementation for advanced security AI capabilities"""
    
    def __init__(self, api_key: str, model: str = "gpt-4o"):
        """Initialize OpenAI service
        
        Args:
            api_key: OpenAI API key
            model: Model to use (default: gpt-4o)
        """
        self.api_key = api_key
        self.model = model
        self.client = AsyncOpenAI(api_key=api_key)
        self.request_timeout = 60  # seconds
        self.max_retries = 3
        
        # Performance tracking
        self.stats = {
            "total_requests": 0,
            "successful_requests": 0,
            "failed_requests": 0,
            "average_response_time": 0.0,
            "payload_generations": 0,
            "security_analyses": 0
        }
        
        logger.info(f"OpenAI service initialized with model: {model}")
    
    async def generate_payload(self, request: PayloadGenerationRequest) -> PayloadGenerationResponse:
        """Generate AI-powered security payload
        
        Args:
            request: Payload generation request
            
        Returns:
            PayloadGenerationResponse with generated payload and analysis
        """
        start_time = time.time()
        self.stats["total_requests"] += 1
        
        try:
            # Build comprehensive prompt for OpenAI model
            system_prompt = self._build_payload_system_prompt()
            user_prompt = self._build_payload_user_prompt(request)
            
            # Call OpenAI with advanced reasoning
            response = await self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                temperature=0.7,
                max_tokens=2000,
                timeout=self.request_timeout
            )
            
            # Parse structured response
            response_content = response.choices[0].message.content
            parsed_response = self._parse_payload_response(response_content)
            
            execution_time = time.time() - start_time
            self._update_stats(execution_time, success=True)
            
            return PayloadGenerationResponse(
                success=True,
                primary_payload=parsed_response.get("payload", ""),
                explanation=parsed_response.get("explanation", ""),
                confidence_score=parsed_response.get("confidence", 0.8),
                alternative_payloads=parsed_response.get("alternatives", []),
                execution_steps=parsed_response.get("steps", []),
                preconditions=parsed_response.get("preconditions", []),
                post_exploitation_options=parsed_response.get("post_exploitation", []),
                risk_assessment=parsed_response.get("risk_assessment", {}),
                remediation_advice=parsed_response.get("remediation", ""),
                detection_signatures=parsed_response.get("detection", []),
                execution_time=execution_time,
                model_used=self.model
            )
            
        except Exception as e:
            execution_time = time.time() - start_time
            self._update_stats(execution_time, success=False)
            
            logger.error(f"Payload generation failed: {e}")
            return PayloadGenerationResponse(
                success=False,
                primary_payload="",
                explanation="",
                confidence_score=0.0,
                alternative_payloads=[],
                execution_steps=[],
                preconditions=[],
                post_exploitation_options=[],
                risk_assessment={},
                remediation_advice="",
                detection_signatures=[],
                execution_time=execution_time,
                model_used=self.model,
                errors=[str(e)]
            )
    
    async def analyze_security_data(self, request: SecurityAnalysisRequest) -> SecurityAnalysisResponse:
        """Perform comprehensive security analysis using AI
        
        Args:
            request: Security analysis request
            
        Returns:
            SecurityAnalysisResponse with detailed analysis
        """
        start_time = time.time()
        self.stats["total_requests"] += 1
        
        try:
            # Build analysis prompt
            system_prompt = self._build_analysis_system_prompt()
            user_prompt = self._build_analysis_user_prompt(request)
            
            # Call OpenAI for advanced analysis
            response = await self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                temperature=0.3,  # Lower temperature for analysis
                max_tokens=2500,
                timeout=self.request_timeout
            )
            
            # Parse analysis response
            response_content = response.choices[0].message.content
            parsed_analysis = self._parse_analysis_response(response_content)
            
            execution_time = time.time() - start_time
            self._update_stats(execution_time, success=True)
            
            return SecurityAnalysisResponse(
                success=True,
                executive_summary=parsed_analysis.get("executive_summary", ""),
                technical_analysis=parsed_analysis.get("technical_analysis", ""),
                risk_score=parsed_analysis.get("risk_score", 0.0),
                severity_breakdown=parsed_analysis.get("severity_breakdown", {}),
                key_findings=parsed_analysis.get("key_findings", []),
                attack_vectors=parsed_analysis.get("attack_vectors", []),
                business_impact=parsed_analysis.get("business_impact", ""),
                remediation_roadmap=parsed_analysis.get("remediation_roadmap", []),
                compliance_status=parsed_analysis.get("compliance_status", {}),
                recommendations=parsed_analysis.get("recommendations", []),
                execution_time=execution_time,
                model_used=self.model
            )
            
        except Exception as e:
            execution_time = time.time() - start_time
            self._update_stats(execution_time, success=False)
            
            logger.error(f"Security analysis failed: {e}")
            return SecurityAnalysisResponse(
                success=False,
                executive_summary="",
                technical_analysis="",
                risk_score=0.0,
                severity_breakdown={},
                key_findings=[],
                attack_vectors=[],
                business_impact="",
                remediation_roadmap=[],
                compliance_status={},
                recommendations=[],
                execution_time=execution_time,
                model_used=self.model,
                errors=[str(e)]
            )
    
    async def generate_exploit_chain(self, vulnerabilities: List[Dict[str, Any]], target_info: Dict[str, Any]) -> Dict[str, Any]:
        """Generate multi-stage exploit chain using AI reasoning
        
        Args:
            vulnerabilities: List of discovered vulnerabilities
            target_info: Target environment information
            
        Returns:
            Dictionary containing exploit chain and analysis
        """
        start_time = time.time()
        self.stats["total_requests"] += 1
        
        try:
            # Build exploit chain prompt
            system_prompt = self._build_exploit_chain_system_prompt()
            user_prompt = self._build_exploit_chain_user_prompt(vulnerabilities, target_info)
            
            response = await self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                temperature=0.6,
                max_tokens=3000,
                timeout=self.request_timeout
            )
            
            response_content = response.choices[0].message.content
            parsed_chain = self._parse_exploit_chain_response(response_content)
            
            execution_time = time.time() - start_time
            self._update_stats(execution_time, success=True)
            
            return {
                "success": True,
                "exploit_chain": parsed_chain,
                "execution_time": execution_time,
                "model_used": self.model
            }
            
        except Exception as e:
            execution_time = time.time() - start_time
            self._update_stats(execution_time, success=False)
            
            logger.error(f"Exploit chain generation failed: {e}")
            return {
                "success": False,
                "exploit_chain": {},
                "execution_time": execution_time,
                "model_used": self.model,
                "errors": [str(e)]
            }
    
    def _build_payload_system_prompt(self) -> str:
        """Build system prompt for payload generation"""
        return """You are an expert cybersecurity researcher and penetration tester with deep knowledge of:
        - Advanced exploit development techniques
        - Web application security vulnerabilities  
        - Network protocol exploitation
        - Social engineering and phishing techniques
        - Post-exploitation and lateral movement
        - Defensive evasion techniques

        Your role is to generate educational security payloads for authorized penetration testing and security research purposes only. 

        CRITICAL REQUIREMENTS:
        1. All payloads must be for authorized, ethical security testing only
        2. Include comprehensive explanations of techniques used
        3. Provide detailed remediation and mitigation advice
        4. Assess realistic success probability and business impact
        5. Follow responsible disclosure principles
        6. Generate functional, technically accurate payloads
        7. Include detection signatures and defensive measures

        OUTPUT FORMAT:
        Always respond with valid JSON containing:
        {
            "payload": "functional exploit code/command",
            "explanation": "detailed technical explanation",
            "confidence": 0.85,
            "alternatives": ["alternative payload 1", "alternative payload 2"],
            "steps": ["step 1", "step 2", "step 3"],
            "preconditions": ["required condition 1", "required condition 2"],
            "post_exploitation": ["lateral movement option", "persistence option"],
            "risk_assessment": {
                "likelihood": "high/medium/low",
                "impact": "critical/high/medium/low",
                "detection_difficulty": "easy/medium/hard"
            },
            "remediation": "specific remediation steps",
            "detection": ["signature 1", "signature 2"]
        }"""
    
    def _build_payload_user_prompt(self, request: PayloadGenerationRequest) -> str:
        """Build user prompt for payload generation"""
        return f"""
        Generate a {request.difficulty_level} level {request.vulnerability_type} payload for authorized security testing:

        TARGET CONTEXT:
        - Environment: {request.target_environment}
        - Target Details: {json.dumps(request.target_context, indent=2)}
        - Payload Style: {request.payload_style}
        - Constraints: {json.dumps(request.constraints, indent=2)}
        
        SPECIFIC REQUIREMENTS:
        {request.custom_requirements}
        
        Generate a comprehensive payload with:
        1. Primary functional exploit code
        2. 2-3 alternative approaches
        3. Step-by-step execution guide
        4. Required preconditions
        5. Post-exploitation options
        6. Risk assessment matrix
        7. Complete remediation advice
        8. Detection signatures for defensive teams
        
        Ensure the payload is:
        - Technically accurate and functional
        - Appropriate for the specified difficulty level
        - Educational with detailed explanations
        - Ethically responsible with remediation guidance
        """
    
    def _build_analysis_system_prompt(self) -> str:
        """Build system prompt for security analysis"""
        return """You are an elite cybersecurity analyst with expertise in:
        - Vulnerability assessment and risk analysis
        - Threat modeling and attack surface analysis
        - Business impact assessment
        - Compliance and regulatory requirements
        - Security architecture and controls
        - Incident response and remediation

        Your role is to provide comprehensive security analysis of scan results and vulnerability data.

        ANALYSIS REQUIREMENTS:
        1. Provide executive-level summaries for business stakeholders
        2. Include detailed technical analysis for security teams
        3. Assess realistic business impact and risk scores
        4. Prioritize findings based on exploitability and impact
        5. Map findings to compliance frameworks when relevant
        6. Generate actionable remediation roadmaps
        7. Consider business context and operational constraints

        OUTPUT FORMAT:
        Always respond with valid JSON containing:
        {
            "executive_summary": "business-focused summary",
            "technical_analysis": "detailed technical assessment", 
            "risk_score": 0.85,
            "severity_breakdown": {"critical": 2, "high": 5, "medium": 10, "low": 3},
            "key_findings": [
                {
                    "title": "finding title",
                    "severity": "high",
                    "description": "detailed description",
                    "impact": "business impact",
                    "remediation": "specific remediation"
                }
            ],
            "attack_vectors": ["vector 1", "vector 2"],
            "business_impact": "overall business impact assessment",
            "remediation_roadmap": [
                {
                    "phase": "immediate",
                    "timeline": "24-48 hours",
                    "actions": ["action 1", "action 2"],
                    "priority": "critical"
                }
            ],
            "compliance_status": {"pci_dss": "non_compliant", "sox": "compliant"},
            "recommendations": ["recommendation 1", "recommendation 2"]
        }"""
    
    def _build_analysis_user_prompt(self, request: SecurityAnalysisRequest) -> str:
        """Build user prompt for security analysis"""
        return f"""
        Analyze the following security scan results with {request.analysis_scope} scope:

        SCAN DATA:
        {json.dumps(request.scan_data, indent=2)}

        ANALYSIS PARAMETERS:
        - Focus Areas: {', '.join(request.focus_areas) if request.focus_areas else 'All areas'}
        - Business Context: {json.dumps(request.business_context, indent=2)}
        - Compliance Frameworks: {', '.join(request.compliance_frameworks) if request.compliance_frameworks else 'General security'}

        Provide comprehensive analysis including:
        1. Executive summary for leadership
        2. Technical analysis for security teams
        3. Quantitative risk scoring (0.0-1.0 scale)
        4. Severity breakdown and key findings
        5. Potential attack vectors and threat scenarios
        6. Business impact assessment
        7. Phased remediation roadmap with timelines
        8. Compliance status for requested frameworks
        9. Strategic recommendations for security improvement

        Consider:
        - Exploitability vs. impact matrix
        - Business operational requirements
        - Resource constraints and feasibility
        - Regulatory and compliance obligations
        - Cost-benefit analysis of remediation
        """
    
    def _build_exploit_chain_system_prompt(self) -> str:
        """Build system prompt for exploit chain generation"""
        return """You are an advanced threat modeling expert specializing in:
        - Multi-stage attack planning and orchestration
        - Vulnerability chaining and exploitation sequences
        - Lateral movement and privilege escalation
        - Attack tree analysis and path optimization
        - Defensive evasion and persistence techniques

        Your role is to analyze multiple vulnerabilities and generate realistic attack chains for security assessment purposes.

        CHAIN GENERATION REQUIREMENTS:
        1. Identify logical vulnerability relationships and dependencies
        2. Plan multi-stage exploitation sequences
        3. Consider realistic attacker capabilities and motivations
        4. Assess detection likelihood at each stage
        5. Provide alternative paths and contingencies
        6. Include timing and sequencing considerations
        7. Generate educational content for defensive teams

        OUTPUT FORMAT:
        Always respond with valid JSON containing detailed attack chain analysis."""
    
    def _build_exploit_chain_user_prompt(self, vulnerabilities: List[Dict[str, Any]], target_info: Dict[str, Any]) -> str:
        """Build user prompt for exploit chain generation"""
        vulns_json = json.dumps(vulnerabilities, indent=2)
        target_json = json.dumps(target_info, indent=2)
        
        return f"""
        Generate a comprehensive multi-stage exploit chain using these vulnerabilities:

        DISCOVERED VULNERABILITIES:
        {vulns_json}

        TARGET ENVIRONMENT:
        {target_json}

        Generate an attack chain including:
        1. Initial access vectors and entry points
        2. Vulnerability chaining and escalation paths
        3. Lateral movement opportunities
        4. Persistence and stealth mechanisms
        5. Data exfiltration or impact scenarios
        6. Detection probability at each stage
        7. Alternative paths and contingencies
        8. Defensive countermeasures and detection points

        Consider realistic attacker:
        - Technical skill levels and resources
        - Time constraints and operational security
        - Detection avoidance and stealth requirements
        - Business objectives and impact goals
        """
    
    def _parse_payload_response(self, response_content: str) -> Dict[str, Any]:
        """Parse AI response for payload generation"""
        try:
            # Try to extract JSON from response
            if "```json" in response_content:
                json_start = response_content.find("```json") + 7
                json_end = response_content.find("```", json_start)
                json_content = response_content[json_start:json_end].strip()
            elif "{" in response_content and "}" in response_content:
                json_start = response_content.find("{")
                json_end = response_content.rfind("}") + 1
                json_content = response_content[json_start:json_end]
            else:
                # Fallback: treat as plain text response
                return {
                    "payload": response_content[:500],
                    "explanation": "AI response parsing failed, raw content provided",
                    "confidence": 0.5,
                    "alternatives": [],
                    "steps": [],
                    "preconditions": [],
                    "post_exploitation": [],
                    "risk_assessment": {},
                    "remediation": "Manual review required",
                    "detection": []
                }
            
            parsed = json.loads(json_content)
            return parsed
            
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse payload response: {e}")
            return {
                "payload": response_content[:500],
                "explanation": f"JSON parsing failed: {e}",
                "confidence": 0.3,
                "alternatives": [],
                "steps": [],
                "preconditions": [],
                "post_exploitation": [],
                "risk_assessment": {},
                "remediation": "Manual review required",
                "detection": []
            }
    
    def _parse_analysis_response(self, response_content: str) -> Dict[str, Any]:
        """Parse AI response for security analysis"""
        try:
            # Extract JSON from response
            if "```json" in response_content:
                json_start = response_content.find("```json") + 7
                json_end = response_content.find("```", json_start)
                json_content = response_content[json_start:json_end].strip()
            elif "{" in response_content and "}" in response_content:
                json_start = response_content.find("{")
                json_end = response_content.rfind("}") + 1
                json_content = response_content[json_start:json_end]
            else:
                # Fallback parsing
                return {
                    "executive_summary": response_content[:300],
                    "technical_analysis": response_content,
                    "risk_score": 0.5,
                    "severity_breakdown": {},
                    "key_findings": [],
                    "attack_vectors": [],
                    "business_impact": "Analysis parsing failed",
                    "remediation_roadmap": [],
                    "compliance_status": {},
                    "recommendations": []
                }
            
            return json.loads(json_content)
            
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse analysis response: {e}")
            return {
                "executive_summary": "Parsing failed",
                "technical_analysis": response_content,
                "risk_score": 0.0,
                "severity_breakdown": {},
                "key_findings": [],
                "attack_vectors": [],
                "business_impact": f"JSON parsing error: {e}",
                "remediation_roadmap": [],
                "compliance_status": {},
                "recommendations": []
            }
    
    def _parse_exploit_chain_response(self, response_content: str) -> Dict[str, Any]:
        """Parse AI response for exploit chain generation"""
        try:
            # Extract JSON from response
            if "```json" in response_content:
                json_start = response_content.find("```json") + 7
                json_end = response_content.find("```", json_start)
                json_content = response_content[json_start:json_end].strip()
                return json.loads(json_content)
            elif "{" in response_content:
                json_start = response_content.find("{")
                json_end = response_content.rfind("}") + 1
                json_content = response_content[json_start:json_end]
                return json.loads(json_content)
            else:
                return {"error": "No valid JSON found in response", "raw_content": response_content}
                
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse exploit chain response: {e}")
            return {"error": f"JSON parsing failed: {e}", "raw_content": response_content}
    
    def _update_stats(self, execution_time: float, success: bool):
        """Update service statistics"""
        if success:
            self.stats["successful_requests"] += 1
        else:
            self.stats["failed_requests"] += 1
        
        # Update average response time
        total_requests = self.stats["successful_requests"] + self.stats["failed_requests"]
        self.stats["average_response_time"] = (
            (self.stats["average_response_time"] * (total_requests - 1) + execution_time) / total_requests
        )
    
    def get_service_stats(self) -> Dict[str, Any]:
        """Get service performance statistics"""
        success_rate = 0.0
        if self.stats["total_requests"] > 0:
            success_rate = self.stats["successful_requests"] / self.stats["total_requests"]
        
        return {
            **self.stats,
            "success_rate": success_rate,
            "service_health": "healthy" if success_rate > 0.8 else "degraded" if success_rate > 0.5 else "unhealthy"
        }
    
    async def test_connection(self) -> Dict[str, Any]:
        """Test OpenAI service connection"""
        try:
            start_time = time.time()
            
            # Simple test request
            response = await self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "user", "content": "Respond with 'OpenAI service operational' if you can read this."}
                ],
                max_tokens=50,
                timeout=10
            )
            
            execution_time = time.time() - start_time
            
            return {
                "success": True,
                "model": self.model,
                "response_time": execution_time,
                "message": "OpenAI service connection successful",
                "api_response": response.choices[0].message.content
            }
            
        except Exception as e:
            return {
                "success": False,
                "model": self.model,
                "error": str(e),
                "message": "OpenAI service connection failed"
            }