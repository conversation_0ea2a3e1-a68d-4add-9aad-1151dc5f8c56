import { app, BrowserWindow } from 'electron';
import log from 'electron-log';
import * as path from 'path';

// Ultra-minimal Electron app for testing
class MinimalApp {
  private mainWindow: BrowserWindow | null = null;

  constructor() {
    log.info('🚀 Minimal app starting...');
    
    // Single event handler - just create window when ready
    app.on('ready', this.createWindow.bind(this));
    app.on('window-all-closed', () => {
      if (process.platform !== 'darwin') {
        app.quit();
      }
    });
    
    log.info('📍 Event handlers registered, waiting for ready...');
  }

  private async createWindow(): Promise<void> {
    log.info('🎉 Ready event fired! Creating window...');
    
    this.mainWindow = new BrowserWindow({
      width: 1200,
      height: 800,
      show: false,
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        sandbox: false // Temporarily disable for testing
      }
    });

    // Load the React app
    const isDev = process.env.NODE_ENV === 'development';
    const url = isDev 
      ? 'http://localhost:5180' 
      : `file://${path.join(__dirname, '../../../dist/index.html')}`;
    
    log.info(`📍 Loading URL: ${url}`);
    
    try {
      await this.mainWindow.loadURL(url);
      this.mainWindow.show();
      
      if (isDev) {
        this.mainWindow.webContents.openDevTools();
      }
      
      log.info('✅ Window created and loaded successfully!');
    } catch (error) {
      log.error('❌ Failed to load window:', error);
    }
  }
}

// Create the minimal app
const minimalApp = new MinimalApp();

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  log.error('Uncaught exception:', error);
});

process.on('unhandledRejection', (reason, promise) => {
  log.error('Unhandled rejection at:', promise, 'reason:', reason);
});