/**
 * Electron Service - Desktop API integration
 * Provides unified interface to Electron APIs in the renderer process
 */

// ===== TYPE DEFINITIONS =====

export interface WSLStatus {
  status: 'available' | 'unavailable' | 'unknown'
  version?: string
  error?: string
}

export interface WSLDistribution {
  name: string
  version: string
  isDefault: boolean
  status: string
}

export interface ElectronAPI {
  // App info
  getName: () => Promise<string>
  getVersion: () => Promise<string>
  getPlatform: () => Promise<string>
  getArch: () => Promise<string>
  
  // WSL operations
  wsl: {
    getStatus: () => Promise<WSLStatus>
    getDistributions: () => Promise<WSLDistribution[]>
    executeCommand: (command: string, distribution?: string) => Promise<{
      success: boolean
      output: string[]
      error?: string
    }>
    install?: () => Promise<boolean>
  }
  
  // File system operations
  fileSystem: {
    readFile: (path: string) => Promise<string>
    writeFile: (path: string, content: string) => Promise<void>
    exists: (path: string) => Promise<boolean>
    selectFile: (options?: any) => Promise<string | null>
    selectDirectory: (options?: any) => Promise<string | null>
    saveFile: (options?: any) => Promise<string | null>
  }
  
  // System operations
  system: {
    getSystemInfo: () => Promise<any>
    openExternal: (url: string) => Promise<void>
    showItemInFolder: (path: string) => Promise<void>
  }
  
  // Notifications
  notification: {
    show: (options: { title: string; body: string; icon?: string }) => Promise<void>
  }
  
  // IPC methods
  on: (channel: string, callback: (...args: any[]) => void) => void
  off: (channel: string, callback?: (...args: any[]) => void) => void
  once: (channel: string, callback: (...args: any[]) => void) => void
  invoke: (channel: string, ...args: any[]) => Promise<any>
  send: (channel: string, ...args: any[]) => void
}

declare global {
  interface Window {
    electronAPI?: ElectronAPI
  }
}

// ===== ELECTRON SERVICE CLASS =====

class ElectronService {
  private api: ElectronAPI | null = null
  
  constructor() {
    this.api = this.getAPI()
  }
  
  /**
   * Check if running in Electron environment
   */
  isElectron(): boolean {
    return typeof window !== 'undefined' && window.electronAPI !== undefined
  }
  
  /**
   * Get Electron API with type safety
   */
  private getAPI(): ElectronAPI | null {
    if (typeof window !== 'undefined' && window.electronAPI) {
      return window.electronAPI
    }
    return null
  }
  
  /**
   * Ensure API is available
   */
  private ensureAPI(): ElectronAPI {
    if (!this.api) {
      throw new Error('Electron API not available. Make sure you are running in Electron.')
    }
    return this.api
  }
  
  // ===== APP INFO =====
  
  async getName(): Promise<string> {
    const api = this.ensureAPI()
    return await api.getName()
  }
  
  async getVersion(): Promise<string> {
    const api = this.ensureAPI()
    return await api.getVersion()
  }
  
  async getPlatform(): Promise<string> {
    const api = this.ensureAPI()
    return await api.getPlatform()
  }
  
  async getArch(): Promise<string> {
    const api = this.ensureAPI()
    return await api.getArch()
  }
  
  // ===== WSL OPERATIONS =====
  
  get wsl() {
    const api = this.ensureAPI()
    return api.wsl
  }
  
  // ===== FILE SYSTEM =====
  
  async readFile(path: string): Promise<string> {
    const api = this.ensureAPI()
    return await api.fileSystem.readFile(path)
  }
  
  async writeFile(path: string, content: string): Promise<void> {
    const api = this.ensureAPI()
    await api.fileSystem.writeFile(path, content)
  }
  
  async fileExists(path: string): Promise<boolean> {
    const api = this.ensureAPI()
    return await api.fileSystem.exists(path)
  }
  
  async selectFile(options?: any): Promise<string | null> {
    const api = this.ensureAPI()
    return await api.fileSystem.selectFile(options)
  }
  
  async selectDirectory(options?: any): Promise<string | null> {
    const api = this.ensureAPI()
    return await api.fileSystem.selectDirectory(options)
  }
  
  async saveFile(options?: any): Promise<string | null> {
    const api = this.ensureAPI()
    return await api.fileSystem.saveFile(options)
  }
  
  // ===== SYSTEM =====
  
  async getSystemInfo(): Promise<any> {
    const api = this.ensureAPI()
    return await api.system.getSystemInfo()
  }
  
  async openExternal(url: string): Promise<void> {
    const api = this.ensureAPI()
    await api.system.openExternal(url)
  }
  
  async showItemInFolder(path: string): Promise<void> {
    const api = this.ensureAPI()
    await api.system.showItemInFolder(path)
  }
  
  // ===== NOTIFICATIONS =====
  
  async showNotification(options: {
    title: string
    body: string
    icon?: string
  }): Promise<void> {
    const api = this.ensureAPI()
    await api.notification.show(options)
  }
  
  // ===== IPC =====
  
  on(channel: string, callback: (...args: any[]) => void): void {
    const api = this.ensureAPI()
    api.on(channel, callback)
  }
  
  off(channel: string, callback?: (...args: any[]) => void): void {
    const api = this.ensureAPI()
    api.off(channel, callback)
  }
  
  once(channel: string, callback: (...args: any[]) => void): void {
    const api = this.ensureAPI()
    api.once(channel, callback)
  }
  
  async invoke(channel: string, ...args: any[]): Promise<any> {
    const api = this.ensureAPI()
    return await api.invoke(channel, ...args)
  }
  
  send(channel: string, ...args: any[]): void {
    const api = this.ensureAPI()
    api.send(channel, ...args)
  }
}

// ===== SINGLETON EXPORT =====

export const electronService = new ElectronService()

// ===== UTILITY EXPORTS =====

export function hasElectronAPI(): boolean {
  return electronService.isElectron()
}

export function getElectronAPI(): ElectronAPI | null {
  return typeof window !== 'undefined' ? window.electronAPI || null : null
}