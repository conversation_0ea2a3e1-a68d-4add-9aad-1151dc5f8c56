import { app, BrowserWindow } from 'electron';
import * as path from 'path';

console.log('🚀 Starting NexusScan Desktop (Fixed Version)...');

let mainWindow: BrowserWindow | null = null;

function createWindow() {
  console.log('📍 Creating main window...');
  
  mainWindow = new BrowserWindow({
    width: 1400,
    height: 900,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      sandbox: true,
      preload: path.join(__dirname, '../preload/api.js')
    }
  });

  // Always use dev server for now
  const url = 'http://localhost:5182';

  console.log('📍 Loading URL:', url);
  
  mainWindow.loadURL(url);
  mainWindow.webContents.openDevTools();

  mainWindow.on('closed', () => {
    mainWindow = null;
  });

  console.log('✅ Window created successfully');
}

// Register event handlers BEFORE app might be ready
app.on('ready', () => {
  console.log('🎉 Electron ready event fired!');
  createWindow();
});

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  if (mainWindow === null) {
    createWindow();
  }
});

// Check if app is already ready
if (app.isReady()) {
  console.log('⚡ App already ready, creating window immediately');
  createWindow();
} else {
  console.log('⏳ Waiting for app ready event...');
}