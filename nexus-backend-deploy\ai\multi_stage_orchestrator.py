#!/usr/bin/env python3
"""
Multi-Stage Attack Orchestration with AI Chaining for NexusScan Desktop
Intelligent orchestration of complex multi-stage attacks with AI-powered decision making and adaptive chaining.
"""

import asyncio
import logging
import json
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Set, Tuple, Union, Callable
from dataclasses import dataclass, asdict, field
from enum import Enum
import random
from collections import defaultdict, deque

from core.config import Config
from core.database import DatabaseManager
from ai.ai_service import AIServiceManager
from ai.services import AnalysisRequest
from ai.creative_exploit_engine import GeneratedExploit, VulnerabilityContext
from ai.adaptive_exploit_modifier import TargetProfile
from security.tools.custom.intelligence.mitre_attack_mapper import AttackTactic, MitreTechnique

logger = logging.getLogger(__name__)


class AttackStage(Enum):
    """Stages of a multi-stage attack"""
    RECONNAISSANCE = "reconnaissance"
    INITIAL_ACCESS = "initial_access"
    EXECUTION = "execution"
    PERSISTENCE = "persistence"
    PRIVILEGE_ESCALATION = "privilege_escalation"
    DEFENSE_EVASION = "defense_evasion"
    CREDENTIAL_ACCESS = "credential_access"
    DISCOVERY = "discovery"
    LATERAL_MOVEMENT = "lateral_movement"
    COLLECTION = "collection"
    COMMAND_CONTROL = "command_control"
    EXFILTRATION = "exfiltration"
    IMPACT = "impact"
    CLEANUP = "cleanup"


class StageStatus(Enum):
    """Status of attack stage execution"""
    PENDING = "pending"
    RUNNING = "running"
    SUCCESS = "success"
    PARTIAL_SUCCESS = "partial_success"
    FAILED = "failed"
    SKIPPED = "skipped"
    BLOCKED = "blocked"
    TIMEOUT = "timeout"


class DecisionType(Enum):
    """Types of AI decisions in attack orchestration"""
    STAGE_SELECTION = "stage_selection"
    EXPLOIT_SELECTION = "exploit_selection"
    TIMING_DECISION = "timing_decision"
    PIVOT_DECISION = "pivot_decision"
    ESCALATION_DECISION = "escalation_decision"
    EVASION_DECISION = "evasion_decision"
    CLEANUP_DECISION = "cleanup_decision"
    ABORT_DECISION = "abort_decision"


class ChainStrategy(Enum):
    """Strategies for chaining attack stages"""
    LINEAR = "linear"
    BRANCHING = "branching"
    ADAPTIVE = "adaptive"
    OPPORTUNISTIC = "opportunistic"
    STEALTH_FOCUSED = "stealth_focused"
    SPEED_FOCUSED = "speed_focused"
    COMPREHENSIVE = "comprehensive"


@dataclass
class AttackNode:
    """Individual node in attack chain"""
    node_id: str
    stage: AttackStage
    name: str
    description: str
    exploit: Optional[GeneratedExploit]
    tool_name: Optional[str]
    command: Optional[str]
    parameters: Dict[str, Any]
    prerequisites: List[str]
    success_conditions: List[str]
    failure_conditions: List[str]
    timeout_seconds: int
    retry_attempts: int
    cleanup_required: bool
    stealth_level: str
    risk_level: str
    expected_duration: timedelta
    mitre_techniques: List[str]
    dependencies: List[str] = field(default_factory=list)
    alternatives: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class StageExecution:
    """Execution record for an attack stage"""
    execution_id: str
    node_id: str
    stage: AttackStage
    status: StageStatus
    start_time: datetime
    end_time: Optional[datetime]
    duration: Optional[timedelta]
    success_rate: float
    error_message: Optional[str]
    output_data: Dict[str, Any]
    evidence_collected: List[str]
    side_effects: List[str]
    detection_indicators: List[str]
    pivot_opportunities: List[str]
    escalation_paths: List[str]
    cleanup_status: str = "pending"


@dataclass
class AIDecision:
    """AI-powered decision in attack orchestration"""
    decision_id: str
    decision_type: DecisionType
    context: Dict[str, Any]
    available_options: List[Dict[str, Any]]
    selected_option: Dict[str, Any]
    confidence_score: float
    reasoning: str
    alternative_options: List[Dict[str, Any]]
    decision_timestamp: datetime
    execution_impact: Dict[str, Any] = field(default_factory=dict)


@dataclass
class AttackChain:
    """Complete multi-stage attack chain"""
    chain_id: str
    name: str
    description: str
    target_profile: TargetProfile
    strategy: ChainStrategy
    attack_nodes: List[AttackNode]
    execution_flow: Dict[str, List[str]]  # node_id -> [next_node_ids]
    ai_decisions: List[AIDecision]
    stage_executions: List[StageExecution]
    chain_status: str
    success_probability: float
    stealth_score: float
    complexity_score: float
    estimated_duration: timedelta
    actual_duration: Optional[timedelta]
    objectives: List[str]
    constraints: Dict[str, Any]
    created_at: datetime
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class ChainTemplate:
    """Template for creating attack chains"""
    template_id: str
    name: str
    description: str
    target_environment: str
    default_strategy: ChainStrategy
    stage_sequence: List[AttackStage]
    node_templates: List[Dict[str, Any]]
    branching_rules: Dict[str, List[str]]
    success_criteria: Dict[str, Any]
    estimated_success_rate: float
    complexity_level: str
    stealth_rating: str


class MultiStageAttackOrchestrator:
    """AI-powered multi-stage attack orchestration system"""

    def __init__(self, config: Config, database: DatabaseManager, ai_service: AIServiceManager):
        """Initialize the multi-stage attack orchestrator"""
        self.config = config
        self.database = database
        self.ai_service = ai_service
        
        # Attack orchestration state
        self.active_chains: Dict[str, AttackChain] = {}
        self.chain_templates: Dict[str, ChainTemplate] = {}
        self.execution_queue: deque = deque()
        
        # Initialize templates
        asyncio.create_task(self._load_chain_templates())
    
    # Frontend Interface Methods for tools-based-frontend.md
    
    def get_frontend_interface_data(self) -> Dict[str, Any]:
        """Get comprehensive frontend interface data matching tools-based-frontend.md specs"""
        return {
            "header": {
                "title": "Multi-Stage Attack Orchestrator",
                "subtitle": "MITRE ATT&CK Framework Integration",
                "orchestrator_status": "Active" if self.active_chains else "Idle",
                "active_chains": len(self.active_chains),
                "total_stages": sum(len(chain.attack_nodes) for chain in self.active_chains.values()),
                "ai_orchestration_engine": "OpenAI GPT-4",
                "safety_mode": "Educational"
            },
            "chain_planning": {
                "attack_objectives": [
                    "Web Application Assessment", "Network Reconnaissance", 
                    "Privilege Escalation Testing", "Data Exfiltration Simulation",
                    "Defense Evasion Analysis", "Lateral Movement Assessment"
                ],
                "mitre_tactics": [
                    {"id": "TA0001", "name": "Initial Access", "techniques": 9, "enabled": True},
                    {"id": "TA0002", "name": "Execution", "techniques": 12, "enabled": True},
                    {"id": "TA0003", "name": "Persistence", "techniques": 19, "enabled": False},
                    {"id": "TA0004", "name": "Privilege Escalation", "techniques": 13, "enabled": True},
                    {"id": "TA0005", "name": "Defense Evasion", "techniques": 40, "enabled": True},
                    {"id": "TA0006", "name": "Credential Access", "techniques": 15, "enabled": False},
                    {"id": "TA0007", "name": "Discovery", "techniques": 29, "enabled": True},
                    {"id": "TA0008", "name": "Lateral Movement", "techniques": 9, "enabled": False}
                ],
                "chain_templates": [
                    {"id": "web_comprehensive", "name": "Web Application Assessment", "stages": 8, "complexity": "Advanced"},
                    {"id": "network_discovery", "name": "Network Reconnaissance", "stages": 5, "complexity": "Intermediate"},
                    {"id": "privilege_escalation", "name": "Privilege Testing", "stages": 6, "complexity": "Advanced"},
                    {"id": "evasion_focused", "name": "Defense Evasion", "stages": 7, "complexity": "Expert"}
                ],
                "risk_assessment": {
                    "environment_profiling": True,
                    "impact_analysis": True,
                    "detection_probability": True,
                    "mitigation_planning": True
                }
            },
            "execution_control": {
                "orchestration_modes": [
                    {"id": "automated", "name": "Fully Automated", "description": "AI-driven execution with minimal intervention"},
                    {"id": "guided", "name": "Guided Execution", "description": "AI suggestions with human approval"},
                    {"id": "manual", "name": "Manual Control", "description": "Step-by-step human control"}
                ],
                "safety_constraints": {
                    "detection_threshold": 0.3,
                    "impact_limitation": True,
                    "automated_cleanup": True,
                    "evidence_preservation": True
                },
                "execution_flow": {
                    "current_stage": None,
                    "progress_percentage": 0,
                    "estimated_completion": None,
                    "stage_dependencies": []
                }
            },
            "live_monitoring": {
                "real_time_progress": {
                    "stages_completed": 0,
                    "current_activity": "Idle",
                    "success_rate": 0.0,
                    "detection_events": 0
                },
                "ai_decision_log": [],
                "performance_metrics": {
                    "average_stage_duration": "0s",
                    "success_rate_trend": [],
                    "resource_utilization": 0
                },
                "security_events": []
            },
            "results_analysis": {
                "chain_outcomes": [],
                "mitre_coverage": {
                    "tactics_tested": 0,
                    "techniques_executed": 0,
                    "coverage_percentage": 0
                },
                "effectiveness_metrics": {
                    "objective_completion": 0,
                    "stealth_score": 0,
                    "time_efficiency": 0
                },
                "lessons_learned": [],
                "remediation_priorities": []
            },
            "export_capabilities": {
                "report_formats": ["MITRE Navigator", "Executive Summary", "Technical Report", "JSON Export"],
                "compliance_mappings": ["NIST", "ISO 27001", "PCI DSS"],
                "integration_apis": ["SIEM", "SOAR", "Threat Intelligence"]
            },
            "educational_content": {
                "mitre_explanations": True,
                "technique_descriptions": True,
                "defensive_countermeasures": True,
                "simulation_mode": True
            }
        }
        
        # AI decision making
        self.decision_history: List[AIDecision] = []
        self.learning_data: Dict[str, Any] = defaultdict(dict)
        
        # Orchestration rules and patterns
        self.stage_dependencies = self._load_stage_dependencies()
        self.branching_rules = self._load_branching_rules()
        self.escalation_patterns = self._load_escalation_patterns()
        
        # Performance tracking
        self.stage_success_rates: Dict[AttackStage, float] = defaultdict(float)
        self.chain_performance_history: List[Dict[str, Any]] = []
        
        # Safety and control
        self.safety_constraints = self._load_safety_constraints()
        self.emergency_stop_triggers: List[str] = []
        
        # Initialize default templates
        self._initialize_chain_templates()
        
        logger.info("Multi-Stage Attack Orchestrator initialized")

    def _load_stage_dependencies(self) -> Dict[AttackStage, List[AttackStage]]:
        """Load stage dependency mappings"""
        return {
            AttackStage.INITIAL_ACCESS: [AttackStage.RECONNAISSANCE],
            AttackStage.EXECUTION: [AttackStage.INITIAL_ACCESS],
            AttackStage.PERSISTENCE: [AttackStage.EXECUTION],
            AttackStage.PRIVILEGE_ESCALATION: [AttackStage.EXECUTION, AttackStage.PERSISTENCE],
            AttackStage.DEFENSE_EVASION: [AttackStage.EXECUTION],
            AttackStage.CREDENTIAL_ACCESS: [AttackStage.EXECUTION, AttackStage.PRIVILEGE_ESCALATION],
            AttackStage.DISCOVERY: [AttackStage.EXECUTION],
            AttackStage.LATERAL_MOVEMENT: [AttackStage.CREDENTIAL_ACCESS, AttackStage.DISCOVERY],
            AttackStage.COLLECTION: [AttackStage.DISCOVERY, AttackStage.LATERAL_MOVEMENT],
            AttackStage.COMMAND_CONTROL: [AttackStage.EXECUTION],
            AttackStage.EXFILTRATION: [AttackStage.COLLECTION, AttackStage.COMMAND_CONTROL],
            AttackStage.IMPACT: [AttackStage.PRIVILEGE_ESCALATION],
            AttackStage.CLEANUP: []  # Can be triggered at any stage
        }

    def _load_branching_rules(self) -> Dict[str, Dict[str, Any]]:
        """Load attack chain branching rules"""
        return {
            "privilege_escalation_success": {
                "trigger": "privilege_escalation_achieved",
                "options": [
                    {"stage": AttackStage.CREDENTIAL_ACCESS, "probability": 0.8},
                    {"stage": AttackStage.DISCOVERY, "probability": 0.9},
                    {"stage": AttackStage.LATERAL_MOVEMENT, "probability": 0.7}
                ]
            },
            "credential_access_success": {
                "trigger": "credentials_obtained",
                "options": [
                    {"stage": AttackStage.LATERAL_MOVEMENT, "probability": 0.9},
                    {"stage": AttackStage.DISCOVERY, "probability": 0.8},
                    {"stage": AttackStage.COLLECTION, "probability": 0.6}
                ]
            },
            "discovery_findings": {
                "trigger": "valuable_assets_discovered",
                "options": [
                    {"stage": AttackStage.COLLECTION, "probability": 0.8},
                    {"stage": AttackStage.LATERAL_MOVEMENT, "probability": 0.7},
                    {"stage": AttackStage.EXFILTRATION, "probability": 0.6}
                ]
            },
            "defense_detected": {
                "trigger": "security_controls_detected",
                "options": [
                    {"stage": AttackStage.DEFENSE_EVASION, "probability": 0.9},
                    {"stage": AttackStage.CLEANUP, "probability": 0.7},
                    {"action": "abort_chain", "probability": 0.3}
                ]
            },
            "lateral_movement_success": {
                "trigger": "new_systems_accessed",
                "options": [
                    {"stage": AttackStage.DISCOVERY, "probability": 0.8},
                    {"stage": AttackStage.COLLECTION, "probability": 0.7},
                    {"stage": AttackStage.PERSISTENCE, "probability": 0.6}
                ]
            }
        }

    def _load_escalation_patterns(self) -> Dict[str, Dict[str, Any]]:
        """Load attack escalation patterns"""
        return {
            "privilege_escalation_path": {
                "user_to_admin": [
                    "exploit_service_vulnerability",
                    "abuse_sudo_configuration",
                    "exploit_kernel_vulnerability",
                    "dll_hijacking",
                    "token_impersonation"
                ],
                "admin_to_system": [
                    "psexec_execution",
                    "wmi_execution",
                    "scheduled_task_creation",
                    "service_installation"
                ]
            },
            "lateral_movement_path": {
                "same_network": [
                    "credential_reuse",
                    "pass_the_hash",
                    "golden_ticket",
                    "rdp_hijacking"
                ],
                "different_network": [
                    "vpn_exploitation",
                    "trusted_relationship_abuse",
                    "supply_chain_attack"
                ]
            },
            "persistence_mechanisms": {
                "registry_persistence": [
                    "run_keys",
                    "winlogon_keys",
                    "service_keys",
                    "startup_folder"
                ],
                "scheduled_persistence": [
                    "scheduled_tasks",
                    "cron_jobs",
                    "systemd_timers"
                ],
                "service_persistence": [
                    "windows_services",
                    "linux_daemons",
                    "dll_search_order_hijacking"
                ]
            }
        }

    def _load_safety_constraints(self) -> Dict[str, Any]:
        """Load safety constraints for attack orchestration"""
        return {
            "time_limits": {
                "max_chain_duration": timedelta(hours=4),
                "max_stage_duration": timedelta(minutes=30),
                "inactivity_timeout": timedelta(minutes=10)
            },
            "impact_limits": {
                "no_data_destruction": True,
                "no_service_disruption": True,
                "no_unauthorized_data_access": False,  # Allowed for testing
                "no_privilege_abuse": False  # Allowed for testing
            },
            "detection_thresholds": {
                "max_failed_attempts": 3,
                "max_detection_score": 0.7,
                "abort_on_honeypot": True,
                "abort_on_sandbox": True
            },
            "scope_limitations": {
                "authorized_targets_only": True,
                "respect_network_boundaries": True,
                "no_external_communication": False,
                "log_all_activities": True
            }
        }

    def _initialize_chain_templates(self):
        """Initialize default attack chain templates"""
        
        # Web Application Attack Chain
        web_app_template = ChainTemplate(
            template_id="web_app_comprehensive",
            name="Comprehensive Web Application Attack",
            description="Full web application penetration testing chain",
            target_environment="web_application",
            default_strategy=ChainStrategy.COMPREHENSIVE,
            stage_sequence=[
                AttackStage.RECONNAISSANCE,
                AttackStage.INITIAL_ACCESS,
                AttackStage.EXECUTION,
                AttackStage.DISCOVERY,
                AttackStage.CREDENTIAL_ACCESS,
                AttackStage.COLLECTION,
                AttackStage.EXFILTRATION,
                AttackStage.CLEANUP
            ],
            node_templates=[
                {
                    "stage": AttackStage.RECONNAISSANCE,
                    "tools": ["nmap", "nuclei", "whatweb", "dirb"],
                    "techniques": ["T1590", "T1595"]
                },
                {
                    "stage": AttackStage.INITIAL_ACCESS,
                    "tools": ["sqlmap", "burpsuite", "nikto"],
                    "techniques": ["T1190", "T1133"]
                },
                {
                    "stage": AttackStage.EXECUTION,
                    "tools": ["metasploit", "custom_exploits"],
                    "techniques": ["T1059", "T1203"]
                }
            ],
            branching_rules={
                "sql_injection_found": ["credential_access", "discovery"],
                "admin_access_gained": ["collection", "lateral_movement"],
                "sensitive_data_found": ["exfiltration"]
            },
            success_criteria={
                "min_stages_completed": 5,
                "data_access_achieved": True,
                "stealth_maintained": True
            },
            estimated_success_rate=0.75,
            complexity_level="intermediate",
            stealth_rating="medium"
        )
        self.chain_templates[web_app_template.template_id] = web_app_template
        
        # Network Infrastructure Attack Chain
        network_template = ChainTemplate(
            template_id="network_infrastructure",
            name="Network Infrastructure Attack",
            description="Network-focused attack chain with lateral movement",
            target_environment="network_infrastructure",
            default_strategy=ChainStrategy.STEALTH_FOCUSED,
            stage_sequence=[
                AttackStage.RECONNAISSANCE,
                AttackStage.INITIAL_ACCESS,
                AttackStage.EXECUTION,
                AttackStage.PERSISTENCE,
                AttackStage.PRIVILEGE_ESCALATION,
                AttackStage.DISCOVERY,
                AttackStage.LATERAL_MOVEMENT,
                AttackStage.COLLECTION,
                AttackStage.CLEANUP
            ],
            node_templates=[
                {
                    "stage": AttackStage.RECONNAISSANCE,
                    "tools": ["nmap", "masscan", "enum4linux"],
                    "techniques": ["T1046", "T1018"]
                },
                {
                    "stage": AttackStage.LATERAL_MOVEMENT,
                    "tools": ["psexec", "wmiexec", "smbexec"],
                    "techniques": ["T1021", "T1047"]
                }
            ],
            branching_rules={
                "domain_admin_achieved": ["lateral_movement", "collection"],
                "multiple_systems_compromised": ["collection", "persistence"]
            },
            success_criteria={
                "lateral_movement_achieved": True,
                "domain_privilege_obtained": True,
                "multiple_systems_accessed": True
            },
            estimated_success_rate=0.65,
            complexity_level="advanced",
            stealth_rating="high"
        )
        self.chain_templates[network_template.template_id] = network_template

    async def create_attack_chain(self, target_profile: TargetProfile, objectives: List[str],
                                constraints: Dict[str, Any] = None) -> AttackChain:
        """Create an AI-optimized attack chain for target"""
        
        try:
            chain_id = f"chain_{int(time.time())}"
            
            # Select appropriate template
            template = await self._select_optimal_template(target_profile, objectives)
            
            # Get AI recommendations for chain strategy
            strategy = await self._ai_select_chain_strategy(target_profile, objectives, constraints)
            
            # Generate attack nodes
            attack_nodes = await self._generate_attack_nodes(target_profile, template, objectives)
            
            # Create execution flow
            execution_flow = await self._create_execution_flow(attack_nodes, strategy, template)
            
            # Calculate chain metrics
            success_probability = self._calculate_chain_success_probability(attack_nodes)
            stealth_score = self._calculate_chain_stealth_score(attack_nodes)
            complexity_score = self._calculate_chain_complexity(attack_nodes)
            estimated_duration = self._estimate_chain_duration(attack_nodes)
            
            # Create attack chain
            attack_chain = AttackChain(
                chain_id=chain_id,
                name=f"AI-Optimized Attack Chain - {target_profile.environment_type.value}",
                description=f"Multi-stage attack targeting {', '.join(objectives)}",
                target_profile=target_profile,
                strategy=strategy,
                attack_nodes=attack_nodes,
                execution_flow=execution_flow,
                ai_decisions=[],
                stage_executions=[],
                chain_status="created",
                success_probability=success_probability,
                stealth_score=stealth_score,
                complexity_score=complexity_score,
                estimated_duration=estimated_duration,
                actual_duration=None,
                objectives=objectives,
                constraints=constraints or {},
                created_at=datetime.now(),
                metadata={
                    "template_used": template.template_id if template else None,
                    "ai_optimized": True,
                    "target_environment": target_profile.environment_type.value
                }
            )
            
            # Store chain
            self.active_chains[chain_id] = attack_chain
            
            logger.info(f"Created attack chain {chain_id} with {len(attack_nodes)} nodes")
            return attack_chain
            
        except Exception as e:
            logger.error(f"Failed to create attack chain: {e}")
            raise

    async def execute_attack_chain(self, chain_id: str, execution_params: Dict[str, Any] = None) -> bool:
        """Execute attack chain with AI-powered orchestration"""
        
        try:
            if chain_id not in self.active_chains:
                raise ValueError(f"Attack chain {chain_id} not found")
            
            chain = self.active_chains[chain_id]
            chain.started_at = datetime.now()
            chain.chain_status = "executing"
            
            logger.info(f"Starting execution of attack chain {chain_id}")
            
            # Initialize execution state
            current_nodes = self._get_initial_nodes(chain)
            completed_nodes = set()
            failed_nodes = set()
            
            # Execute chain with AI orchestration
            while current_nodes and chain.chain_status == "executing":
                # AI decision for next actions
                ai_decision = await self._make_ai_orchestration_decision(
                    chain, current_nodes, completed_nodes, failed_nodes
                )
                
                if ai_decision:
                    chain.ai_decisions.append(ai_decision)
                    
                    # Check for abort decision
                    if ai_decision.selected_option.get("action") == "abort_chain":
                        logger.warning(f"AI decided to abort chain {chain_id}")
                        chain.chain_status = "aborted"
                        break
                
                # Execute current nodes in parallel
                node_results = await self._execute_nodes_parallel(chain, current_nodes, execution_params)
                
                # Process execution results
                next_nodes = set()
                for node_id, result in node_results.items():
                    if result.status == StageStatus.SUCCESS:
                        completed_nodes.add(node_id)
                        # Get next nodes based on execution flow
                        next_node_ids = chain.execution_flow.get(node_id, [])
                        next_nodes.update(next_node_ids)
                    elif result.status == StageStatus.FAILED:
                        failed_nodes.add(node_id)
                        # AI decision on failure handling
                        failure_decision = await self._handle_node_failure(chain, node_id, result)
                        if failure_decision:
                            chain.ai_decisions.append(failure_decision)
                    
                    chain.stage_executions.append(result)
                
                # Check safety constraints
                if await self._check_safety_violations(chain):
                    logger.warning(f"Safety violation detected, stopping chain {chain_id}")
                    chain.chain_status = "safety_stopped"
                    break
                
                # Update current nodes for next iteration
                current_nodes = next_nodes - completed_nodes - failed_nodes
                
                # Check completion criteria
                if await self._check_completion_criteria(chain, completed_nodes, failed_nodes):
                    break
            
            # Finalize chain execution
            chain.completed_at = datetime.now()
            chain.actual_duration = chain.completed_at - chain.started_at
            
            # Determine final status
            if chain.chain_status == "executing":
                if len(completed_nodes) >= len(chain.attack_nodes) * 0.7:  # 70% success threshold
                    chain.chain_status = "completed_success"
                else:
                    chain.chain_status = "completed_partial"
            
            # Execute cleanup if required
            await self._execute_cleanup_stage(chain)
            
            # Store execution results
            await self._store_chain_execution(chain)
            
            # Update learning data
            await self._update_learning_data(chain)
            
            success = chain.chain_status in ["completed_success", "completed_partial"]
            logger.info(f"Chain {chain_id} execution completed with status: {chain.chain_status}")
            
            return success
            
        except Exception as e:
            logger.error(f"Failed to execute attack chain {chain_id}: {e}")
            if chain_id in self.active_chains:
                self.active_chains[chain_id].chain_status = "error"
            return False

    async def _select_optimal_template(self, target_profile: TargetProfile, objectives: List[str]) -> Optional[ChainTemplate]:
        """Select optimal chain template based on target and objectives"""
        
        # Simple template selection based on target environment
        env_type = target_profile.environment_type.value
        
        for template in self.chain_templates.values():
            if template.target_environment == env_type:
                return template
        
        # Fallback to web application template
        return self.chain_templates.get("web_app_comprehensive")

    async def _ai_select_chain_strategy(self, target_profile: TargetProfile, objectives: List[str],
                                      constraints: Dict[str, Any]) -> ChainStrategy:
        """Use AI to select optimal chain strategy"""
        
        try:
            context = {
                "target_profile": asdict(target_profile),
                "objectives": objectives,
                "constraints": constraints,
                "available_strategies": [strategy.value for strategy in ChainStrategy],
                "task": "Select the optimal attack chain strategy based on target characteristics and objectives"
            }
            
            analysis_request = AnalysisRequest(
                analysis_type="chain_strategy_selection",
                target_info={"target_id": target_profile.target_id},
                context=context
            )
            
            ai_result = await self.ai_service.analyze(analysis_request)
            
            if ai_result and 'selected_strategy' in ai_result:
                try:
                    return ChainStrategy(ai_result['selected_strategy'])
                except ValueError:
                    pass
            
            # Fallback based on target characteristics
            if len(target_profile.security_controls) > 3:
                return ChainStrategy.STEALTH_FOCUSED
            elif "speed" in objectives:
                return ChainStrategy.SPEED_FOCUSED
            else:
                return ChainStrategy.ADAPTIVE
                
        except Exception as e:
            logger.error(f"AI strategy selection failed: {e}")
            return ChainStrategy.ADAPTIVE

    async def _generate_attack_nodes(self, target_profile: TargetProfile, template: Optional[ChainTemplate],
                                   objectives: List[str]) -> List[AttackNode]:
        """Generate attack nodes for the chain"""
        
        attack_nodes = []
        
        # Use template as base if available
        stage_sequence = template.stage_sequence if template else [
            AttackStage.RECONNAISSANCE,
            AttackStage.INITIAL_ACCESS,
            AttackStage.EXECUTION,
            AttackStage.DISCOVERY,
            AttackStage.COLLECTION
        ]
        
        for i, stage in enumerate(stage_sequence):
            node_id = f"node_{stage.value}_{i}"
            
            # Create attack node
            node = AttackNode(
                node_id=node_id,
                stage=stage,
                name=f"{stage.value.replace('_', ' ').title()} Stage",
                description=f"Execute {stage.value} tactics",
                exploit=None,  # Will be populated during execution
                tool_name=self._select_tool_for_stage(stage, target_profile),
                command=None,  # Will be generated during execution
                parameters={},
                prerequisites=self._get_stage_prerequisites(stage),
                success_conditions=self._get_stage_success_conditions(stage),
                failure_conditions=self._get_stage_failure_conditions(stage),
                timeout_seconds=self._get_stage_timeout(stage),
                retry_attempts=2,
                cleanup_required=stage in [AttackStage.PERSISTENCE, AttackStage.LATERAL_MOVEMENT],
                stealth_level=self._get_stage_stealth_level(stage, target_profile),
                risk_level=self._get_stage_risk_level(stage),
                expected_duration=self._get_stage_duration(stage),
                mitre_techniques=self._get_stage_mitre_techniques(stage),
                dependencies=[],
                alternatives=[],
                metadata={
                    "stage_index": i,
                    "template_derived": template is not None
                }
            )
            
            attack_nodes.append(node)
        
        return attack_nodes

    async def _create_execution_flow(self, attack_nodes: List[AttackNode], strategy: ChainStrategy,
                                   template: Optional[ChainTemplate]) -> Dict[str, List[str]]:
        """Create execution flow between attack nodes"""
        
        execution_flow = {}
        
        if strategy == ChainStrategy.LINEAR:
            # Simple linear flow
            for i, node in enumerate(attack_nodes[:-1]):
                execution_flow[node.node_id] = [attack_nodes[i + 1].node_id]
        
        elif strategy == ChainStrategy.BRANCHING:
            # Branching flow based on stage dependencies
            for node in attack_nodes:
                next_nodes = []
                for other_node in attack_nodes:
                    if (node.stage in self.stage_dependencies.get(other_node.stage, []) or
                        self._can_execute_parallel(node.stage, other_node.stage)):
                        next_nodes.append(other_node.node_id)
                execution_flow[node.node_id] = next_nodes
        
        else:  # ADAPTIVE, OPPORTUNISTIC, etc.
            # Dynamic flow that will be determined during execution
            for node in attack_nodes:
                # Initially, all nodes can potentially follow
                potential_next = [n.node_id for n in attack_nodes if n.node_id != node.node_id]
                execution_flow[node.node_id] = potential_next
        
        return execution_flow

    def _get_initial_nodes(self, chain: AttackChain) -> Set[str]:
        """Get initial nodes to execute"""
        
        # Start with reconnaissance stage
        initial_nodes = set()
        for node in chain.attack_nodes:
            if node.stage == AttackStage.RECONNAISSANCE:
                initial_nodes.add(node.node_id)
        
        # If no reconnaissance nodes, start with first available
        if not initial_nodes and chain.attack_nodes:
            initial_nodes.add(chain.attack_nodes[0].node_id)
        
        return initial_nodes

    async def _make_ai_orchestration_decision(self, chain: AttackChain, current_nodes: Set[str],
                                            completed_nodes: Set[str], failed_nodes: Set[str]) -> Optional[AIDecision]:
        """Make AI-powered orchestration decision"""
        
        try:
            # Prepare decision context
            context = {
                "chain_id": chain.chain_id,
                "current_stage": list(current_nodes),
                "completed_stages": list(completed_nodes),
                "failed_stages": list(failed_nodes),
                "chain_progress": len(completed_nodes) / len(chain.attack_nodes),
                "execution_time": (datetime.now() - chain.started_at).total_seconds() if chain.started_at else 0,
                "available_options": self._get_available_orchestration_options(chain, current_nodes),
                "task": "Make optimal orchestration decision for attack chain progression"
            }
            
            analysis_request = AnalysisRequest(
                analysis_type="attack_orchestration_decision",
                target_info={"chain_id": chain.chain_id},
                context=context
            )
            
            ai_result = await self.ai_service.analyze(analysis_request)
            
            if ai_result and 'decision' in ai_result:
                decision_data = ai_result['decision']
                
                decision = AIDecision(
                    decision_id=f"decision_{int(time.time())}",
                    decision_type=DecisionType.STAGE_SELECTION,
                    context=context,
                    available_options=context['available_options'],
                    selected_option=decision_data.get('selected_option', {}),
                    confidence_score=decision_data.get('confidence', 0.7),
                    reasoning=decision_data.get('reasoning', 'AI orchestration decision'),
                    alternative_options=decision_data.get('alternatives', []),
                    decision_timestamp=datetime.now()
                )
                
                return decision
            
            return None
            
        except Exception as e:
            logger.error(f"AI orchestration decision failed: {e}")
            return None

    def _get_available_orchestration_options(self, chain: AttackChain, current_nodes: Set[str]) -> List[Dict[str, Any]]:
        """Get available orchestration options"""
        
        options = []
        
        # Continue with current nodes
        options.append({
            "action": "continue_execution",
            "nodes": list(current_nodes),
            "description": "Continue executing current nodes"
        })
        
        # Skip to next stage
        if current_nodes:
            next_stages = set()
            for node_id in current_nodes:
                next_node_ids = chain.execution_flow.get(node_id, [])
                next_stages.update(next_node_ids)
            
            if next_stages:
                options.append({
                    "action": "skip_to_next",
                    "nodes": list(next_stages),
                    "description": "Skip current stage and move to next"
                })
        
        # Abort chain
        options.append({
            "action": "abort_chain",
            "nodes": [],
            "description": "Abort the entire attack chain"
        })
        
        return options

    async def _execute_nodes_parallel(self, chain: AttackChain, node_ids: Set[str],
                                    execution_params: Dict[str, Any]) -> Dict[str, StageExecution]:
        """Execute multiple nodes in parallel"""
        
        node_results = {}
        
        # Get nodes to execute
        nodes_to_execute = [node for node in chain.attack_nodes if node.node_id in node_ids]
        
        # Execute nodes concurrently
        tasks = []
        for node in nodes_to_execute:
            task = asyncio.create_task(self._execute_single_node(chain, node, execution_params))
            tasks.append((node.node_id, task))
        
        # Wait for all executions to complete
        for node_id, task in tasks:
            try:
                result = await task
                node_results[node_id] = result
            except Exception as e:
                logger.error(f"Node {node_id} execution failed: {e}")
                # Create failure result
                node_results[node_id] = StageExecution(
                    execution_id=f"exec_{node_id}_{int(time.time())}",
                    node_id=node_id,
                    stage=next(n.stage for n in nodes_to_execute if n.node_id == node_id),
                    status=StageStatus.FAILED,
                    start_time=datetime.now(),
                    end_time=datetime.now(),
                    duration=timedelta(seconds=0),
                    success_rate=0.0,
                    error_message=str(e),
                    output_data={},
                    evidence_collected=[],
                    side_effects=[],
                    detection_indicators=[],
                    pivot_opportunities=[],
                    escalation_paths=[]
                )
        
        return node_results

    async def _execute_single_node(self, chain: AttackChain, node: AttackNode,
                                 execution_params: Dict[str, Any]) -> StageExecution:
        """Execute a single attack node"""
        
        execution_id = f"exec_{node.node_id}_{int(time.time())}"
        start_time = datetime.now()
        
        try:
            logger.info(f"Executing node {node.node_id} - {node.stage.value}")
            
            # Simulate node execution (in real implementation, this would call actual tools)
            await asyncio.sleep(random.uniform(1, 5))  # Simulate execution time
            
            # Determine success based on node characteristics and random factors
            success_probability = 0.8  # Base success rate
            
            # Adjust based on stage complexity
            if node.stage in [AttackStage.PRIVILEGE_ESCALATION, AttackStage.LATERAL_MOVEMENT]:
                success_probability *= 0.7
            
            # Adjust based on target security controls
            security_control_count = len(chain.target_profile.security_controls)
            success_probability *= max(0.3, 1.0 - (security_control_count * 0.1))
            
            # Determine execution result
            success = random.random() < success_probability
            
            if success:
                status = StageStatus.SUCCESS
                success_rate = success_probability
                output_data = {
                    "execution_successful": True,
                    "stage_completed": node.stage.value,
                    "simulated_output": f"Successfully completed {node.stage.value}"
                }
                evidence_collected = [f"{node.stage.value}_evidence.log"]
                pivot_opportunities = self._identify_pivot_opportunities(node, chain.target_profile)
                escalation_paths = self._identify_escalation_paths(node)
            else:
                status = StageStatus.FAILED
                success_rate = 0.0
                output_data = {
                    "execution_successful": False,
                    "failure_reason": f"Failed to complete {node.stage.value}",
                    "error_details": "Simulated execution failure"
                }
                evidence_collected = []
                pivot_opportunities = []
                escalation_paths = []
            
            end_time = datetime.now()
            duration = end_time - start_time
            
            execution = StageExecution(
                execution_id=execution_id,
                node_id=node.node_id,
                stage=node.stage,
                status=status,
                start_time=start_time,
                end_time=end_time,
                duration=duration,
                success_rate=success_rate,
                error_message=output_data.get("failure_reason") if not success else None,
                output_data=output_data,
                evidence_collected=evidence_collected,
                side_effects=[],
                detection_indicators=[],
                pivot_opportunities=pivot_opportunities,
                escalation_paths=escalation_paths
            )
            
            return execution
            
        except Exception as e:
            logger.error(f"Node execution error: {e}")
            end_time = datetime.now()
            duration = end_time - start_time
            
            return StageExecution(
                execution_id=execution_id,
                node_id=node.node_id,
                stage=node.stage,
                status=StageStatus.FAILED,
                start_time=start_time,
                end_time=end_time,
                duration=duration,
                success_rate=0.0,
                error_message=str(e),
                output_data={},
                evidence_collected=[],
                side_effects=[],
                detection_indicators=[],
                pivot_opportunities=[],
                escalation_paths=[]
            )

    async def _handle_node_failure(self, chain: AttackChain, node_id: str, result: StageExecution) -> Optional[AIDecision]:
        """Handle node execution failure with AI decision making"""
        
        try:
            # Get the failed node
            failed_node = next(node for node in chain.attack_nodes if node.node_id == node_id)
            
            context = {
                "failed_node": asdict(failed_node),
                "failure_result": asdict(result),
                "chain_progress": len([e for e in chain.stage_executions if e.status == StageStatus.SUCCESS]),
                "available_alternatives": failed_node.alternatives,
                "retry_attempts_remaining": failed_node.retry_attempts,
                "task": "Decide how to handle node execution failure"
            }
            
            analysis_request = AnalysisRequest(
                analysis_type="failure_handling_decision",
                target_info={"node_id": node_id},
                context=context
            )
            
            ai_result = await self.ai_service.analyze(analysis_request)
            
            if ai_result and 'failure_handling' in ai_result:
                handling_data = ai_result['failure_handling']
                
                decision = AIDecision(
                    decision_id=f"failure_decision_{int(time.time())}",
                    decision_type=DecisionType.PIVOT_DECISION,
                    context=context,
                    available_options=[
                        {"action": "retry", "description": "Retry the failed node"},
                        {"action": "skip", "description": "Skip the failed node"},
                        {"action": "alternative", "description": "Use alternative approach"},
                        {"action": "abort", "description": "Abort the chain"}
                    ],
                    selected_option=handling_data.get('selected_action', {"action": "skip"}),
                    confidence_score=handling_data.get('confidence', 0.6),
                    reasoning=handling_data.get('reasoning', 'Failure handling decision'),
                    alternative_options=[],
                    decision_timestamp=datetime.now()
                )
                
                return decision
            
            return None
            
        except Exception as e:
            logger.error(f"Failure handling decision failed: {e}")
            return None

    async def _check_safety_violations(self, chain: AttackChain) -> bool:
        """Check for safety constraint violations"""
        
        # Check time limits
        if chain.started_at:
            elapsed = datetime.now() - chain.started_at
            if elapsed > self.safety_constraints["time_limits"]["max_chain_duration"]:
                return True
        
        # Check detection score
        recent_executions = [e for e in chain.stage_executions if e.end_time and 
                           (datetime.now() - e.end_time).total_seconds() < 300]  # Last 5 minutes
        
        if recent_executions:
            avg_detection_score = sum(len(e.detection_indicators) for e in recent_executions) / len(recent_executions)
            if avg_detection_score > self.safety_constraints["detection_thresholds"]["max_detection_score"]:
                return True
        
        # Check failure rate
        failed_count = len([e for e in chain.stage_executions if e.status == StageStatus.FAILED])
        if failed_count > self.safety_constraints["detection_thresholds"]["max_failed_attempts"]:
            return True
        
        return False

    async def _check_completion_criteria(self, chain: AttackChain, completed_nodes: Set[str],
                                       failed_nodes: Set[str]) -> bool:
        """Check if chain completion criteria are met"""
        
        total_nodes = len(chain.attack_nodes)
        completion_rate = len(completed_nodes) / total_nodes if total_nodes > 0 else 0
        
        # Check if minimum objectives are met
        if completion_rate >= 0.7:  # 70% completion threshold
            return True
        
        # Check if all critical objectives are completed
        critical_stages = [AttackStage.RECONNAISSANCE, AttackStage.INITIAL_ACCESS, AttackStage.EXECUTION]
        critical_completed = 0
        for node in chain.attack_nodes:
            if node.stage in critical_stages and node.node_id in completed_nodes:
                critical_completed += 1
        
        if critical_completed >= len(critical_stages):
            return True
        
        # Check if no more nodes can be executed
        remaining_nodes = set(node.node_id for node in chain.attack_nodes) - completed_nodes - failed_nodes
        if not remaining_nodes:
            return True
        
        return False

    async def _execute_cleanup_stage(self, chain: AttackChain):
        """Execute cleanup stage"""
        
        try:
            cleanup_nodes = [node for node in chain.attack_nodes if node.cleanup_required]
            
            for node in cleanup_nodes:
                logger.info(f"Executing cleanup for node {node.node_id}")
                # Simulate cleanup
                await asyncio.sleep(1)
        
        except Exception as e:
            logger.error(f"Cleanup execution failed: {e}")

    async def _store_chain_execution(self, chain: AttackChain):
        """Store chain execution results in database"""
        
        try:
            chain_data = {
                "chain_id": chain.chain_id,
                "name": chain.name,
                "target_profile_id": chain.target_profile.target_id,
                "strategy": chain.strategy.value,
                "chain_status": chain.chain_status,
                "success_probability": chain.success_probability,
                "actual_duration": chain.actual_duration.total_seconds() if chain.actual_duration else None,
                "nodes_executed": len(chain.stage_executions),
                "successful_nodes": len([e for e in chain.stage_executions if e.status == StageStatus.SUCCESS]),
                "created_at": chain.created_at,
                "metadata": json.dumps(asdict(chain), default=str)
            }
            
            # Store in campaigns table (reusing existing structure)
            self.database.cursor.execute("""
                INSERT INTO campaigns 
                (campaign_id, name, target, status, created_at, metadata)
                VALUES (?, ?, ?, ?, ?, ?)
            """, (
                chain.chain_id,
                chain.name,
                chain.target_profile.target_id,
                chain.chain_status,
                chain.created_at,
                json.dumps(chain_data, default=str)
            ))
            self.database.connection.commit()
            
        except Exception as e:
            logger.error(f"Failed to store chain execution: {e}")

    async def _update_learning_data(self, chain: AttackChain):
        """Update learning data based on chain execution"""
        
        try:
            # Update stage success rates
            for execution in chain.stage_executions:
                stage = execution.stage
                if stage not in self.stage_success_rates:
                    self.stage_success_rates[stage] = 0.5
                
                # Update with exponential moving average
                alpha = 0.1
                new_success = 1.0 if execution.status == StageStatus.SUCCESS else 0.0
                self.stage_success_rates[stage] = (alpha * new_success + 
                                                 (1 - alpha) * self.stage_success_rates[stage])
            
            # Store chain performance
            performance_data = {
                "chain_id": chain.chain_id,
                "success_rate": len([e for e in chain.stage_executions if e.status == StageStatus.SUCCESS]) / len(chain.stage_executions) if chain.stage_executions else 0,
                "execution_time": chain.actual_duration.total_seconds() if chain.actual_duration else 0,
                "strategy_used": chain.strategy.value,
                "target_environment": chain.target_profile.environment_type.value,
                "timestamp": datetime.now()
            }
            
            self.chain_performance_history.append(performance_data)
            
            # Limit history size
            if len(self.chain_performance_history) > 100:
                self.chain_performance_history = self.chain_performance_history[-100:]
                
        except Exception as e:
            logger.error(f"Failed to update learning data: {e}")

    # Helper methods for node generation
    def _select_tool_for_stage(self, stage: AttackStage, target_profile: TargetProfile) -> str:
        """Select appropriate tool for attack stage"""
        
        stage_tool_mapping = {
            AttackStage.RECONNAISSANCE: "nmap",
            AttackStage.INITIAL_ACCESS: "nuclei",
            AttackStage.EXECUTION: "metasploit",
            AttackStage.PERSISTENCE: "custom_persistence",
            AttackStage.PRIVILEGE_ESCALATION: "privilege_escalation_tools",
            AttackStage.DISCOVERY: "enum4linux",
            AttackStage.LATERAL_MOVEMENT: "psexec",
            AttackStage.COLLECTION: "data_collection_tools",
            AttackStage.EXFILTRATION: "exfiltration_tools",
            AttackStage.CLEANUP: "cleanup_tools"
        }
        
        return stage_tool_mapping.get(stage, "generic_tool")

    def _get_stage_prerequisites(self, stage: AttackStage) -> List[str]:
        """Get prerequisites for attack stage"""
        
        stage_prerequisites = {
            AttackStage.RECONNAISSANCE: [],
            AttackStage.INITIAL_ACCESS: ["target_identified"],
            AttackStage.EXECUTION: ["initial_access_achieved"],
            AttackStage.PERSISTENCE: ["code_execution_achieved"],
            AttackStage.PRIVILEGE_ESCALATION: ["user_access_achieved"],
            AttackStage.DISCOVERY: ["system_access_achieved"],
            AttackStage.LATERAL_MOVEMENT: ["network_access_achieved"],
            AttackStage.COLLECTION: ["target_systems_identified"],
            AttackStage.EXFILTRATION: ["data_collected"],
            AttackStage.CLEANUP: []
        }
        
        return stage_prerequisites.get(stage, [])

    def _get_stage_success_conditions(self, stage: AttackStage) -> List[str]:
        """Get success conditions for attack stage"""
        
        stage_success_conditions = {
            AttackStage.RECONNAISSANCE: ["target_information_gathered", "services_enumerated"],
            AttackStage.INITIAL_ACCESS: ["authentication_bypassed", "code_execution_achieved"],
            AttackStage.EXECUTION: ["payload_executed", "system_response_received"],
            AttackStage.PERSISTENCE: ["persistence_mechanism_installed"],
            AttackStage.PRIVILEGE_ESCALATION: ["elevated_privileges_obtained"],
            AttackStage.DISCOVERY: ["network_mapped", "assets_identified"],
            AttackStage.LATERAL_MOVEMENT: ["additional_systems_accessed"],
            AttackStage.COLLECTION: ["sensitive_data_identified"],
            AttackStage.EXFILTRATION: ["data_successfully_transferred"],
            AttackStage.CLEANUP: ["artifacts_removed", "logs_cleared"]
        }
        
        return stage_success_conditions.get(stage, ["stage_completed"])

    def _get_stage_failure_conditions(self, stage: AttackStage) -> List[str]:
        """Get failure conditions for attack stage"""
        
        return [
            "access_denied",
            "authentication_failed",
            "connection_timeout",
            "tool_execution_failed",
            "security_control_blocked",
            "detection_alert_triggered"
        ]

    def _get_stage_timeout(self, stage: AttackStage) -> int:
        """Get timeout for attack stage"""
        
        stage_timeouts = {
            AttackStage.RECONNAISSANCE: 300,  # 5 minutes
            AttackStage.INITIAL_ACCESS: 600,  # 10 minutes
            AttackStage.EXECUTION: 180,       # 3 minutes
            AttackStage.PERSISTENCE: 300,     # 5 minutes
            AttackStage.PRIVILEGE_ESCALATION: 600,  # 10 minutes
            AttackStage.DISCOVERY: 300,       # 5 minutes
            AttackStage.LATERAL_MOVEMENT: 900,  # 15 minutes
            AttackStage.COLLECTION: 600,      # 10 minutes
            AttackStage.EXFILTRATION: 1200,   # 20 minutes
            AttackStage.CLEANUP: 300          # 5 minutes
        }
        
        return stage_timeouts.get(stage, 300)

    def _get_stage_stealth_level(self, stage: AttackStage, target_profile: TargetProfile) -> str:
        """Get stealth level for attack stage"""
        
        # Adjust based on target security controls
        security_control_count = len(target_profile.security_controls)
        
        if security_control_count > 5:
            return "high"
        elif security_control_count > 2:
            return "medium"
        else:
            return "low"

    def _get_stage_risk_level(self, stage: AttackStage) -> str:
        """Get risk level for attack stage"""
        
        high_risk_stages = [
            AttackStage.PRIVILEGE_ESCALATION,
            AttackStage.LATERAL_MOVEMENT,
            AttackStage.IMPACT,
            AttackStage.EXFILTRATION
        ]
        
        if stage in high_risk_stages:
            return "high"
        elif stage in [AttackStage.EXECUTION, AttackStage.PERSISTENCE]:
            return "medium"
        else:
            return "low"

    def _get_stage_duration(self, stage: AttackStage) -> timedelta:
        """Get expected duration for attack stage"""
        
        stage_durations = {
            AttackStage.RECONNAISSANCE: timedelta(minutes=10),
            AttackStage.INITIAL_ACCESS: timedelta(minutes=15),
            AttackStage.EXECUTION: timedelta(minutes=5),
            AttackStage.PERSISTENCE: timedelta(minutes=8),
            AttackStage.PRIVILEGE_ESCALATION: timedelta(minutes=12),
            AttackStage.DISCOVERY: timedelta(minutes=10),
            AttackStage.LATERAL_MOVEMENT: timedelta(minutes=20),
            AttackStage.COLLECTION: timedelta(minutes=15),
            AttackStage.EXFILTRATION: timedelta(minutes=25),
            AttackStage.CLEANUP: timedelta(minutes=5)
        }
        
        return stage_durations.get(stage, timedelta(minutes=10))

    def _get_stage_mitre_techniques(self, stage: AttackStage) -> List[str]:
        """Get MITRE ATT&CK techniques for stage"""
        
        stage_techniques = {
            AttackStage.RECONNAISSANCE: ["T1590", "T1591", "T1592", "T1593", "T1594", "T1595"],
            AttackStage.INITIAL_ACCESS: ["T1189", "T1190", "T1133", "T1200", "T1566"],
            AttackStage.EXECUTION: ["T1059", "T1203", "T1204", "T1559", "T1569"],
            AttackStage.PERSISTENCE: ["T1053", "T1136", "T1137", "T1543", "T1547"],
            AttackStage.PRIVILEGE_ESCALATION: ["T1068", "T1134", "T1484", "T1546", "T1548"],
            AttackStage.DISCOVERY: ["T1007", "T1018", "T1033", "T1046", "T1049"],
            AttackStage.LATERAL_MOVEMENT: ["T1021", "T1047", "T1091", "T1210", "T1550"],
            AttackStage.COLLECTION: ["T1005", "T1039", "T1056", "T1074", "T1113"],
            AttackStage.EXFILTRATION: ["T1041", "T1048", "T1052", "T1567", "T1568"],
            AttackStage.CLEANUP: ["T1070", "T1485", "T1495", "T1565"]
        }
        
        return stage_techniques.get(stage, [])

    def _calculate_chain_success_probability(self, attack_nodes: List[AttackNode]) -> float:
        """Calculate overall chain success probability"""
        
        # Simple multiplication of stage success probabilities
        # In reality, this would be more sophisticated
        base_probability = 0.8
        
        for node in attack_nodes:
            # Adjust based on stage complexity
            if node.stage in [AttackStage.PRIVILEGE_ESCALATION, AttackStage.LATERAL_MOVEMENT]:
                base_probability *= 0.8
            elif node.stage in [AttackStage.RECONNAISSANCE, AttackStage.DISCOVERY]:
                base_probability *= 0.95
            else:
                base_probability *= 0.9
        
        return max(0.1, base_probability)

    def _calculate_chain_stealth_score(self, attack_nodes: List[AttackNode]) -> float:
        """Calculate overall chain stealth score"""
        
        stealth_scores = []
        for node in attack_nodes:
            if node.stealth_level == "high":
                stealth_scores.append(0.9)
            elif node.stealth_level == "medium":
                stealth_scores.append(0.6)
            else:
                stealth_scores.append(0.3)
        
        return sum(stealth_scores) / len(stealth_scores) if stealth_scores else 0.5

    def _calculate_chain_complexity(self, attack_nodes: List[AttackNode]) -> float:
        """Calculate overall chain complexity score"""
        
        complexity_factors = [
            len(attack_nodes) * 0.1,  # Number of stages
            len(set(node.stage for node in attack_nodes)) * 0.15,  # Unique stages
            sum(1 for node in attack_nodes if node.cleanup_required) * 0.2  # Cleanup requirements
        ]
        
        return min(1.0, sum(complexity_factors))

    def _estimate_chain_duration(self, attack_nodes: List[AttackNode]) -> timedelta:
        """Estimate total chain execution duration"""
        
        total_duration = timedelta()
        for node in attack_nodes:
            total_duration += node.expected_duration
        
        # Add overhead for decision making and transitions
        overhead = total_duration * 0.3
        return total_duration + overhead

    def _can_execute_parallel(self, stage1: AttackStage, stage2: AttackStage) -> bool:
        """Check if two stages can be executed in parallel"""
        
        parallel_compatible = {
            AttackStage.RECONNAISSANCE: [AttackStage.DISCOVERY],
            AttackStage.DISCOVERY: [AttackStage.COLLECTION],
            AttackStage.COLLECTION: [AttackStage.EXFILTRATION]
        }
        
        return stage2 in parallel_compatible.get(stage1, [])

    def _identify_pivot_opportunities(self, node: AttackNode, target_profile: TargetProfile) -> List[str]:
        """Identify pivot opportunities from successful node execution"""
        
        pivot_opportunities = []
        
        if node.stage == AttackStage.INITIAL_ACCESS:
            pivot_opportunities.extend(["credential_access", "privilege_escalation"])
        elif node.stage == AttackStage.DISCOVERY:
            pivot_opportunities.extend(["lateral_movement", "additional_targets"])
        elif node.stage == AttackStage.CREDENTIAL_ACCESS:
            pivot_opportunities.extend(["lateral_movement", "privilege_escalation"])
        
        return pivot_opportunities

    def _identify_escalation_paths(self, node: AttackNode) -> List[str]:
        """Identify escalation paths from successful node execution"""
        
        escalation_paths = []
        
        if node.stage == AttackStage.EXECUTION:
            escalation_paths.extend(["privilege_escalation", "persistence"])
        elif node.stage == AttackStage.PRIVILEGE_ESCALATION:
            escalation_paths.extend(["lateral_movement", "domain_admin"])
        elif node.stage == AttackStage.LATERAL_MOVEMENT:
            escalation_paths.extend(["additional_systems", "crown_jewels"])
        
        return escalation_paths

    def get_orchestration_statistics(self) -> Dict[str, Any]:
        """Get orchestration system statistics"""
        
        return {
            "active_chains": len(self.active_chains),
            "chain_templates": len(self.chain_templates),
            "total_decisions_made": len(self.decision_history),
            "stage_success_rates": dict(self.stage_success_rates),
            "average_chain_duration": sum(
                (perf["execution_time"] for perf in self.chain_performance_history),
                0
            ) / len(self.chain_performance_history) if self.chain_performance_history else 0,
            "orchestration_rules": len(self.branching_rules),
            "escalation_patterns": len(self.escalation_patterns),
            "safety_constraints_active": len(self.safety_constraints),
            "performance_history_size": len(self.chain_performance_history)
        }