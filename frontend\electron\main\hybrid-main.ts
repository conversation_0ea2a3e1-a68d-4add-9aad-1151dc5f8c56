import { app, BrowserWindow, Menu, shell, ipcMain, dialog, session } from 'electron';
import log from 'electron-log';
import Store from 'electron-store';
import * as path from 'path';
import * as fs from 'fs-extra';
import contextMenu from 'electron-context-menu';

// Hybrid version - gradually add complexity
class HybridApp {
  private mainWindow: BrowserWindow | null = null;
  private store: Store<any>;
  private isDevelopment: boolean;

  constructor() {
    log.info('🚀 Hybrid app starting...');
    
    this.isDevelopment = process.env.NODE_ENV === 'development';
    this.store = new Store({
      defaults: {
        windowBounds: { width: 1400, height: 900 }
      }
    });
    
    log.info('📍 Basic setup complete');
    
    // STEP 1: Basic cache setup (this worked in complex version)
    this.setupCache();
    
    // STEP 2: Basic app events (might be the culprit)
    app.on('ready', this.onReady.bind(this));
    app.on('window-all-closed', this.onWindowAllClosed.bind(this));
    
    log.info('📍 Basic events registered, waiting for ready...');
  }

  private async setupCache(): Promise<void> {
    const userData = app.getPath('userData');
    const cacheDir = path.join(userData, 'cache');
    
    try {
      await fs.ensureDir(cacheDir);
      app.setPath('userData', userData);
      log.info(`Cache directory initialized: ${cacheDir}`);
    } catch (error) {
      log.warn('Cache directory setup failed:', error);
    }
  }

  private async onReady(): Promise<void> {
    log.info('🎉 Ready event fired! Creating window...');
    
    this.mainWindow = new BrowserWindow({
      width: 1400,
      height: 900,
      show: false,
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        sandbox: true,
        preload: path.join(__dirname, '../preload/api.js')
      }
    });

    const url = this.isDevelopment 
      ? 'http://localhost:5180' 
      : `file://${path.join(__dirname, '../../../dist/index.html')}`;
    
    try {
      await this.mainWindow.loadURL(url);
      this.mainWindow.show();
      
      if (this.isDevelopment) {
        this.mainWindow.webContents.openDevTools();
      }
      
      // Setup essential IPC handlers
      this.setupIpcHandlers();
      
      // Context menu
      contextMenu({
        showLookUpSelection: false,
        showSearchWithGoogle: false,
        showCopyImage: false
      });
      
      log.info('✅ Window created and loaded successfully!');
    } catch (error) {
      log.error('❌ Failed to load window:', error);
    }
  }

  private setupIpcHandlers(): void {
    // App info handlers (needed by React app)
    ipcMain.handle('app:getVersion', () => app.getVersion());
    ipcMain.handle('app:getName', () => app.getName());
    ipcMain.handle('app:getPlatform', () => process.platform);
    ipcMain.handle('app:getArch', () => process.arch);

    // Store operations
    ipcMain.handle('store:get', (event, key) => {
      return this.store.get(key);
    });

    ipcMain.handle('store:set', (event, key, value) => {
      this.store.set(key, value);
    });

    // Window management
    ipcMain.handle('window:minimize', () => {
      this.mainWindow?.minimize();
    });

    ipcMain.handle('window:maximize', () => {
      if (this.mainWindow?.isMaximized()) {
        this.mainWindow.unmaximize();
      } else {
        this.mainWindow?.maximize();
      }
    });

    ipcMain.handle('window:close', () => {
      this.mainWindow?.close();
    });
  }

  private onWindowAllClosed(): void {
    if (process.platform !== 'darwin') {
      app.quit();
    }
  }
}

// Create the hybrid app
const hybridApp = new HybridApp();

process.on('uncaughtException', (error) => {
  log.error('Uncaught exception:', error);
});

process.on('unhandledRejection', (reason, promise) => {
  log.error('Unhandled rejection at:', promise, 'reason:', reason);
});