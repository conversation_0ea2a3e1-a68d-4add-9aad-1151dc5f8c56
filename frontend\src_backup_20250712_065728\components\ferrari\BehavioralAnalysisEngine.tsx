/**
 * Behavioral Analysis Engine Component
 * Zero-day style behavioral pattern recognition and anomaly detection
 */
import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { 
  Eye,
  Play,
  Square,
  Download,
  Settings,
  Target,
  Globe,
  FileText,
  Terminal,
  Copy,
  RotateCcw,
  Clock,
  Zap,
  Filter,
  AlertTriangle,
  CheckCircle,
  TrendingUp,
  Activity,
  Lock,
  Search,
  List,
  BarChart3,
  Database,
  Server,
  Bug,
  Info,
  Code,
  Package,
  Layers,
  Shield,
  Crosshair,
  Cpu,
  Network,
  Gauge,
  Tag,
  Hash,
  Workflow,
  GitBranch,
  Book,
  Users,
  Table,
  Binary,
  Braces,
  FileKey,
  History,
  Folder,
  Star,
  Crown,
  Award,
  Timer,
  FastForward,
  SkipForward,
  RefreshCw,
  ExternalLink,
  FileCheck,
  FileWarning,
  Fingerprint,
  Key as KeyIcon,
  LockOpen,
  ShieldOff,
  Verified,
  ScanLine,
  Scan,
  FileJson,
  FileCode,
  Brain,
  Lightbulb,
  TrendingDown,
  LineChart,
  PieChart,
  Radar,
  BarChart4,
  ScatterChart,
  Signal,
  Waves,
  Pulse,
  Microscope,
  FlaskConical,
  TestTube,
  Atom,
  Dna,
  Beaker,
  ChartLine,
  ChartBar,
  ChartPie,
  ChartSpline,
  ChartArea,
  ChartColumn,
  Maximize2,
  Minimize2,
  Plus,
  Minus,
  Upload,
  FileUp,
  FolderOpen,
  Play as StartIcon,
  Square as StopIcon,
  Pause as PauseIcon
} from 'lucide-react';

interface BehavioralPattern {
  id: string;
  name: string;
  description: string;
  type: 'network' | 'process' | 'file' | 'user' | 'system' | 'application';
  category: 'normal' | 'suspicious' | 'anomalous' | 'malicious';
  confidence: number;
  frequency: number;
  firstSeen: string;
  lastSeen: string;
  attributes: Record<string, any>;
  severity: 'low' | 'medium' | 'high' | 'critical';
  aiPrediction: string;
  recommendations: string[];
}

interface BaselineMetrics {
  id: string;
  name: string;
  type: 'cpu' | 'memory' | 'network' | 'disk' | 'process' | 'connection';
  baseline: number;
  current: number;
  threshold: number;
  deviation: number;
  trend: 'stable' | 'increasing' | 'decreasing' | 'volatile';
  timestamp: string;
}

interface AnomalyDetection {
  id: string;
  timestamp: string;
  type: 'statistical' | 'pattern_based' | 'ml_based' | 'rule_based';
  description: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  confidence: number;
  affectedSystems: string[];
  indicators: string[];
  possibleCauses: string[];
  recommendations: string[];
  false_positive_likelihood: number;
}

interface PredictiveInsight {
  id: string;
  type: 'vulnerability_location' | 'attack_vector' | 'system_weakness' | 'behavioral_drift';
  prediction: string;
  confidence: number;
  timeframe: string;
  risk_level: 'low' | 'medium' | 'high' | 'critical';
  supporting_evidence: string[];
  prevention_strategies: string[];
  ai_reasoning: string;
}

// Mock data for demonstration
const SAMPLE_PATTERNS: BehavioralPattern[] = [
  {
    id: '1',
    name: 'Unusual Network Traffic Spike',
    description: 'Detected 300% increase in outbound network traffic during off-hours',
    type: 'network',
    category: 'suspicious',
    confidence: 85,
    frequency: 12,
    firstSeen: '2025-07-10T14:30:00Z',
    lastSeen: '2025-07-11T02:15:00Z',
    attributes: { 
      source_ips: ['*********', '*********'], 
      destination_ports: [443, 80, 8080],
      data_volume: '2.3GB'
    },
    severity: 'high',
    aiPrediction: 'Potential data exfiltration or command & control communication',
    recommendations: ['Monitor destination IPs', 'Check for unauthorized applications', 'Review user activity']
  },
  {
    id: '2',
    name: 'Process Injection Behavior',
    description: 'Detected process hollowing patterns in system memory',
    type: 'process',
    category: 'malicious',
    confidence: 92,
    frequency: 3,
    firstSeen: '2025-07-11T09:45:00Z',
    lastSeen: '2025-07-11T10:30:00Z',
    attributes: { 
      target_processes: ['explorer.exe', 'svchost.exe'],
      injection_technique: 'Process Hollowing',
      memory_regions: 4
    },
    severity: 'critical',
    aiPrediction: 'Advanced persistent threat using steganographic techniques',
    recommendations: ['Isolate affected systems', 'Perform memory analysis', 'Update endpoint protection']
  }
];

const SAMPLE_BASELINES: BaselineMetrics[] = [
  {
    id: '1',
    name: 'CPU Usage',
    type: 'cpu',
    baseline: 35,
    current: 67,
    threshold: 80,
    deviation: 32,
    trend: 'increasing',
    timestamp: '2025-07-11T10:30:00Z'
  },
  {
    id: '2',
    name: 'Network Connections',
    type: 'network',
    baseline: 145,
    current: 298,
    threshold: 200,
    deviation: 153,
    trend: 'volatile',
    timestamp: '2025-07-11T10:30:00Z'
  },
  {
    id: '3',
    name: 'Memory Usage',
    type: 'memory',
    baseline: 4200,
    current: 4180,
    threshold: 6000,
    deviation: -20,
    trend: 'stable',
    timestamp: '2025-07-11T10:30:00Z'
  }
];

export const BehavioralAnalysisEngine: React.FC = () => {
  const [activeTab, setActiveTab] = useState('overview');
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [analysisProgress, setAnalysisProgress] = useState(0);
  const [patterns, setPatterns] = useState<BehavioralPattern[]>(SAMPLE_PATTERNS);
  const [baselines, setBaselines] = useState<BaselineMetrics[]>(SAMPLE_BASELINES);
  const [anomalies, setAnomalies] = useState<AnomalyDetection[]>([]);
  const [predictions, setPredictions] = useState<PredictiveInsight[]>([]);
  
  // Configuration state
  const [analysisConfig, setAnalysisConfig] = useState({
    duration: '24h',
    sensitivity: 75,
    dataSource: 'network_logs',
    aiModel: 'gpt-4',
    includeML: true,
    includeStatistical: true,
    includeRuleBased: true
  });

  // Real-time monitoring state
  const [isMonitoring, setIsMonitoring] = useState(false);
  const [monitoringMetrics, setMonitoringMetrics] = useState<any>({});

  useEffect(() => {
    if (isMonitoring) {
      const interval = setInterval(updateMetrics, 5000);
      return () => clearInterval(interval);
    }
  }, [isMonitoring]);

  const updateMetrics = () => {
    // Simulate real-time metric updates
    setBaselines(prev => prev.map(baseline => ({
      ...baseline,
      current: baseline.current + (Math.random() - 0.5) * 10,
      timestamp: new Date().toISOString()
    })));
  };

  const startBehavioralAnalysis = async () => {
    setIsAnalyzing(true);
    setAnalysisProgress(0);

    try {
      const response = await fetch('http://ec2-3-89-91-209.compute-1.amazonaws.com:8000/api/ai/behavioral-analysis/analyze', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          config: analysisConfig,
          baseline_period: '7d',
          analysis_period: analysisConfig.duration
        })
      });

      if (response.ok) {
        // Simulate progressive analysis
        const progressInterval = setInterval(() => {
          setAnalysisProgress(prev => {
            if (prev >= 100) {
              clearInterval(progressInterval);
              return 100;
            }
            return prev + 10;
          });
        }, 1000);

        // Simulate WebSocket updates
        setTimeout(() => {
          const mockAnomalies: AnomalyDetection[] = [
            {
              id: '1',
              timestamp: new Date().toISOString(),
              type: 'ml_based',
              description: 'Unusual user behavior pattern detected',
              severity: 'medium',
              confidence: 78,
              affectedSystems: ['workstation-42', 'server-web-01'],
              indicators: ['Login time anomaly', 'File access pattern change'],
              possibleCauses: ['Compromised credentials', 'Insider threat', 'Account sharing'],
              recommendations: ['Review user activity', 'Force password reset', 'Enable MFA'],
              false_positive_likelihood: 25
            }
          ];
          setAnomalies(mockAnomalies);

          const mockPredictions: PredictiveInsight[] = [
            {
              id: '1',
              type: 'vulnerability_location',
              prediction: 'Web application likely vulnerable to SQL injection in user authentication module',
              confidence: 82,
              timeframe: '7-14 days',
              risk_level: 'high',
              supporting_evidence: ['Increased error rates', 'Unusual database queries', 'Input validation gaps'],
              prevention_strategies: ['Implement parameterized queries', 'Add input validation', 'Deploy WAF rules'],
              ai_reasoning: 'Pattern analysis shows consistent anomalies in database interaction patterns typical of SQL injection attempts'
            }
          ];
          setPredictions(mockPredictions);
        }, 5000);
      }
    } catch (error) {
      console.error('Failed to start behavioral analysis:', error);
    } finally {
      setTimeout(() => {
        setIsAnalyzing(false);
        setAnalysisProgress(0);
      }, 10000);
    }
  };

  const establishBaseline = async () => {
    try {
      const response = await fetch('http://ec2-3-89-91-209.compute-1.amazonaws.com:8000/api/ai/behavioral-analysis/baseline', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          duration: '7d',
          metrics: ['cpu', 'memory', 'network', 'disk', 'process', 'connection'],
          learning_mode: 'adaptive'
        })
      });

      if (response.ok) {
        const newBaselines = await response.json();
        setBaselines(newBaselines.baselines || SAMPLE_BASELINES);
      }
    } catch (error) {
      console.error('Failed to establish baseline:', error);
    }
  };

  const startRealTimeMonitoring = () => {
    setIsMonitoring(!isMonitoring);
  };

  const exportAnalysisReport = () => {
    const report = {
      timestamp: new Date().toISOString(),
      configuration: analysisConfig,
      patterns: patterns,
      baselines: baselines,
      anomalies: anomalies,
      predictions: predictions,
      summary: {
        total_patterns: patterns.length,
        suspicious_patterns: patterns.filter(p => p.category === 'suspicious').length,
        malicious_patterns: patterns.filter(p => p.category === 'malicious').length,
        high_confidence_anomalies: anomalies.filter(a => a.confidence > 80).length
      }
    };

    const dataStr = JSON.stringify(report, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `behavioral_analysis_report_${new Date().toISOString().split('T')[0]}.json`;
    link.click();
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'low': return 'text-green-600 bg-green-50 border-green-200';
      case 'medium': return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'high': return 'text-orange-600 bg-orange-50 border-orange-200';
      case 'critical': return 'text-red-600 bg-red-50 border-red-200';
      default: return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'normal': return 'text-green-600 bg-green-50 border-green-200';
      case 'suspicious': return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'anomalous': return 'text-orange-600 bg-orange-50 border-orange-200';
      case 'malicious': return 'text-red-600 bg-red-50 border-red-200';
      default: return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'network': return Network;
      case 'process': return Cpu;
      case 'file': return FileText;
      case 'user': return Users;
      case 'system': return Server;
      case 'application': return Package;
      default: return Activity;
    }
  };

  return (
    <div className="space-y-6">
      {/* Header Actions */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button 
            onClick={startBehavioralAnalysis} 
            disabled={isAnalyzing}
            className="bg-blue-600 hover:bg-blue-700"
          >
            <Brain className="h-4 w-4 mr-2" />
            {isAnalyzing ? 'Analyzing...' : 'Start Analysis'}
          </Button>
          <Button onClick={establishBaseline} variant="outline">
            <BarChart3 className="h-4 w-4 mr-2" />
            Establish Baseline
          </Button>
          <Button 
            onClick={startRealTimeMonitoring} 
            variant={isMonitoring ? 'destructive' : 'default'}
          >
            {isMonitoring ? <StopIcon className="h-4 w-4 mr-2" /> : <StartIcon className="h-4 w-4 mr-2" />}
            {isMonitoring ? 'Stop Monitoring' : 'Start Monitoring'}
          </Button>
        </div>
        
        <div className="flex items-center space-x-3">
          {isAnalyzing && (
            <div className="flex items-center space-x-3">
              <div className="text-sm text-gray-600">Progress: {analysisProgress}%</div>
              <Progress value={analysisProgress} className="w-32" />
            </div>
          )}
          <Button onClick={exportAnalysisReport} variant="outline">
            <Download className="h-4 w-4 mr-2" />
            Export Report
          </Button>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-6">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="patterns">Patterns</TabsTrigger>
          <TabsTrigger value="anomalies">Anomalies</TabsTrigger>
          <TabsTrigger value="baselines">Baselines</TabsTrigger>
          <TabsTrigger value="predictions">Predictions</TabsTrigger>
          <TabsTrigger value="config">Configuration</TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-6">
          {/* Status Cards */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card className="p-4">
              <div className="flex items-center space-x-3">
                <Eye className="h-8 w-8 text-blue-600" />
                <div>
                  <p className="text-sm text-gray-600">Total Patterns</p>
                  <p className="text-2xl font-bold">{patterns.length}</p>
                </div>
              </div>
            </Card>
            <Card className="p-4">
              <div className="flex items-center space-x-3">
                <AlertTriangle className="h-8 w-8 text-orange-600" />
                <div>
                  <p className="text-sm text-gray-600">Anomalies</p>
                  <p className="text-2xl font-bold">{anomalies.length}</p>
                </div>
              </div>
            </Card>
            <Card className="p-4">
              <div className="flex items-center space-x-3">
                <TrendingUp className="h-8 w-8 text-green-600" />
                <div>
                  <p className="text-sm text-gray-600">Predictions</p>
                  <p className="text-2xl font-bold">{predictions.length}</p>
                </div>
              </div>
            </Card>
            <Card className="p-4">
              <div className="flex items-center space-x-3">
                <Activity className={`h-8 w-8 ${isMonitoring ? 'text-green-600' : 'text-gray-400'}`} />
                <div>
                  <p className="text-sm text-gray-600">Monitoring</p>
                  <p className="text-2xl font-bold">{isMonitoring ? 'Active' : 'Inactive'}</p>
                </div>
              </div>
            </Card>
          </div>

          {/* Real-time Metrics */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Pulse className="h-5 w-5" />
                <span>Real-time System Metrics</span>
              </CardTitle>
              <CardDescription>Current system behavior compared to established baselines</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {baselines.map((metric) => (
                  <Card key={metric.id} className="p-4">
                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <h4 className="font-semibold">{metric.name}</h4>
                        <Badge variant={metric.current > metric.threshold ? 'destructive' : 'default'}>
                          {metric.trend}
                        </Badge>
                      </div>
                      
                      <div className="space-y-2">
                        <div className="flex justify-between text-sm">
                          <span>Current: {metric.current.toFixed(0)}</span>
                          <span>Baseline: {metric.baseline.toFixed(0)}</span>
                        </div>
                        <Progress 
                          value={(metric.current / metric.threshold) * 100} 
                          className="h-2"
                        />
                        <div className="text-xs text-gray-500">
                          Threshold: {metric.threshold} | Deviation: {metric.deviation > 0 ? '+' : ''}{metric.deviation.toFixed(0)}
                        </div>
                      </div>
                    </div>
                  </Card>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Recent Patterns */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Radar className="h-5 w-5" />
                <span>Recent Behavioral Patterns</span>
              </CardTitle>
              <CardDescription>Latest detected behavioral patterns and anomalies</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {patterns.slice(0, 3).map((pattern) => {
                  const TypeIcon = getTypeIcon(pattern.type);
                  return (
                    <Card key={pattern.id} className="p-4">
                      <div className="flex items-start space-x-3">
                        <div className="p-2 bg-blue-100 rounded-lg">
                          <TypeIcon className="h-5 w-5 text-blue-600" />
                        </div>
                        <div className="flex-1">
                          <div className="flex items-start justify-between">
                            <div>
                              <h4 className="font-semibold">{pattern.name}</h4>
                              <p className="text-sm text-gray-600">{pattern.description}</p>
                              <p className="text-sm text-gray-500 mt-1">{pattern.aiPrediction}</p>
                            </div>
                            <div className="flex flex-col items-end space-y-2">
                              <Badge className={getSeverityColor(pattern.severity)} variant="outline">
                                {pattern.severity}
                              </Badge>
                              <Badge className={getCategoryColor(pattern.category)} variant="outline">
                                {pattern.category}
                              </Badge>
                              <span className="text-sm text-gray-500">{pattern.confidence}% confidence</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </Card>
                  );
                })}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Patterns Tab */}
        <TabsContent value="patterns" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Eye className="h-5 w-5" />
                <span>Behavioral Patterns</span>
              </CardTitle>
              <CardDescription>Detected behavioral patterns with AI analysis</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {patterns.map((pattern) => {
                  const TypeIcon = getTypeIcon(pattern.type);
                  return (
                    <Card key={pattern.id} className="p-4">
                      <div className="space-y-4">
                        <div className="flex items-start justify-between">
                          <div className="flex items-start space-x-3">
                            <div className="p-2 bg-blue-100 rounded-lg">
                              <TypeIcon className="h-5 w-5 text-blue-600" />
                            </div>
                            <div>
                              <h4 className="font-semibold">{pattern.name}</h4>
                              <p className="text-sm text-gray-600">{pattern.description}</p>
                              <div className="flex items-center space-x-4 mt-2 text-sm text-gray-500">
                                <span>Type: {pattern.type}</span>
                                <span>Frequency: {pattern.frequency}</span>
                                <span>First seen: {new Date(pattern.firstSeen).toLocaleDateString()}</span>
                              </div>
                            </div>
                          </div>
                          <div className="flex flex-col items-end space-y-2">
                            <Badge className={getSeverityColor(pattern.severity)} variant="outline">
                              {pattern.severity}
                            </Badge>
                            <Badge className={getCategoryColor(pattern.category)} variant="outline">
                              {pattern.category}
                            </Badge>
                            <span className="text-sm text-gray-500">{pattern.confidence}% confidence</span>
                          </div>
                        </div>

                        <div className="space-y-3">
                          <div>
                            <h5 className="font-medium text-sm mb-2">AI Prediction</h5>
                            <p className="text-sm text-blue-800 bg-blue-50 p-3 rounded-lg">
                              {pattern.aiPrediction}
                            </p>
                          </div>

                          <div>
                            <h5 className="font-medium text-sm mb-2">Attributes</h5>
                            <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                              {Object.entries(pattern.attributes).map(([key, value]) => (
                                <div key={key} className="text-sm">
                                  <span className="font-medium">{key}:</span>
                                  <span className="ml-1 text-gray-600">
                                    {Array.isArray(value) ? value.join(', ') : String(value)}
                                  </span>
                                </div>
                              ))}
                            </div>
                          </div>

                          <div>
                            <h5 className="font-medium text-sm mb-2">Recommendations</h5>
                            <div className="flex flex-wrap gap-2">
                              {pattern.recommendations.map((rec, index) => (
                                <Badge key={index} variant="outline" className="text-xs">
                                  {rec}
                                </Badge>
                              ))}
                            </div>
                          </div>
                        </div>
                      </div>
                    </Card>
                  );
                })}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Anomalies Tab */}
        <TabsContent value="anomalies" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <AlertTriangle className="h-5 w-5" />
                <span>Anomaly Detection</span>
              </CardTitle>
              <CardDescription>AI-powered anomaly detection and analysis</CardDescription>
            </CardHeader>
            <CardContent>
              {anomalies.length > 0 ? (
                <div className="space-y-4">
                  {anomalies.map((anomaly) => (
                    <Card key={anomaly.id} className="p-4">
                      <div className="space-y-4">
                        <div className="flex items-start justify-between">
                          <div>
                            <h4 className="font-semibold">{anomaly.description}</h4>
                            <div className="flex items-center space-x-4 mt-2 text-sm text-gray-500">
                              <span>Type: {anomaly.type.replace('_', ' ')}</span>
                              <span>Detected: {new Date(anomaly.timestamp).toLocaleString()}</span>
                              <span>Confidence: {anomaly.confidence}%</span>
                            </div>
                          </div>
                          <div className="flex flex-col items-end space-y-2">
                            <Badge className={getSeverityColor(anomaly.severity)} variant="outline">
                              {anomaly.severity}
                            </Badge>
                            <span className="text-xs text-gray-500">
                              FP Risk: {anomaly.false_positive_likelihood}%
                            </span>
                          </div>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div>
                            <h5 className="font-medium text-sm mb-2">Affected Systems</h5>
                            <div className="flex flex-wrap gap-1">
                              {anomaly.affectedSystems.map((system) => (
                                <Badge key={system} variant="outline" className="text-xs">
                                  {system}
                                </Badge>
                              ))}
                            </div>
                          </div>

                          <div>
                            <h5 className="font-medium text-sm mb-2">Indicators</h5>
                            <div className="flex flex-wrap gap-1">
                              {anomaly.indicators.map((indicator, index) => (
                                <Badge key={index} variant="secondary" className="text-xs">
                                  {indicator}
                                </Badge>
                              ))}
                            </div>
                          </div>

                          <div>
                            <h5 className="font-medium text-sm mb-2">Possible Causes</h5>
                            <ul className="text-sm text-gray-600">
                              {anomaly.possibleCauses.map((cause, index) => (
                                <li key={index} className="flex items-center space-x-2">
                                  <div className="w-1 h-1 bg-gray-400 rounded-full"></div>
                                  <span>{cause}</span>
                                </li>
                              ))}
                            </ul>
                          </div>

                          <div>
                            <h5 className="font-medium text-sm mb-2">Recommendations</h5>
                            <ul className="text-sm text-gray-600">
                              {anomaly.recommendations.map((rec, index) => (
                                <li key={index} className="flex items-center space-x-2">
                                  <div className="w-1 h-1 bg-green-500 rounded-full"></div>
                                  <span>{rec}</span>
                                </li>
                              ))}
                            </ul>
                          </div>
                        </div>
                      </div>
                    </Card>
                  ))}
                </div>
              ) : (
                <div className="text-center py-12">
                  <AlertTriangle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">No Anomalies Detected</h3>
                  <p className="text-gray-600 mb-4">Start behavioral analysis to detect anomalies</p>
                  <Button onClick={startBehavioralAnalysis} className="bg-blue-600 hover:bg-blue-700">
                    <Brain className="h-4 w-4 mr-2" />
                    Start Analysis
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Baselines Tab */}
        <TabsContent value="baselines" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <BarChart3 className="h-5 w-5" />
                <span>Behavioral Baselines</span>
              </CardTitle>
              <CardDescription>Established behavioral baselines and current deviations</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {baselines.map((baseline) => (
                  <Card key={baseline.id} className="p-4">
                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <h4 className="font-semibold">{baseline.name}</h4>
                        <Badge variant={baseline.current > baseline.threshold ? 'destructive' : 'default'}>
                          {baseline.trend}
                        </Badge>
                      </div>
                      
                      <div className="space-y-2">
                        <div className="text-2xl font-bold">
                          {baseline.current.toFixed(0)}
                          <span className="text-sm font-normal text-gray-500 ml-1">
                            {baseline.type === 'memory' ? 'MB' : baseline.type === 'cpu' ? '%' : ''}
                          </span>
                        </div>
                        
                        <div className="space-y-1">
                          <div className="flex justify-between text-sm">
                            <span>Baseline</span>
                            <span>{baseline.baseline.toFixed(0)}</span>
                          </div>
                          <Progress 
                            value={(baseline.current / baseline.threshold) * 100} 
                            className="h-2"
                          />
                          <div className="flex justify-between text-xs text-gray-500">
                            <span>Threshold: {baseline.threshold}</span>
                            <span className={baseline.deviation > 0 ? 'text-red-500' : 'text-green-500'}>
                              {baseline.deviation > 0 ? '+' : ''}{baseline.deviation.toFixed(0)}
                            </span>
                          </div>
                        </div>
                      </div>
                      
                      <div className="text-xs text-gray-500">
                        Updated: {new Date(baseline.timestamp).toLocaleString()}
                      </div>
                    </div>
                  </Card>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Predictions Tab */}
        <TabsContent value="predictions" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <TrendingUp className="h-5 w-5" />
                <span>Predictive Insights</span>
              </CardTitle>
              <CardDescription>AI-powered vulnerability and threat predictions</CardDescription>
            </CardHeader>
            <CardContent>
              {predictions.length > 0 ? (
                <div className="space-y-4">
                  {predictions.map((prediction) => (
                    <Card key={prediction.id} className="p-4">
                      <div className="space-y-4">
                        <div className="flex items-start justify-between">
                          <div>
                            <h4 className="font-semibold">{prediction.prediction}</h4>
                            <div className="flex items-center space-x-4 mt-2 text-sm text-gray-500">
                              <span>Type: {prediction.type.replace('_', ' ')}</span>
                              <span>Timeframe: {prediction.timeframe}</span>
                              <span>Confidence: {prediction.confidence}%</span>
                            </div>
                          </div>
                          <Badge className={getSeverityColor(prediction.risk_level)} variant="outline">
                            {prediction.risk_level} risk
                          </Badge>
                        </div>

                        <div className="space-y-3">
                          <div>
                            <h5 className="font-medium text-sm mb-2">AI Reasoning</h5>
                            <p className="text-sm text-blue-800 bg-blue-50 p-3 rounded-lg">
                              {prediction.ai_reasoning}
                            </p>
                          </div>

                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                              <h5 className="font-medium text-sm mb-2">Supporting Evidence</h5>
                              <ul className="text-sm text-gray-600">
                                {prediction.supporting_evidence.map((evidence, index) => (
                                  <li key={index} className="flex items-center space-x-2">
                                    <div className="w-1 h-1 bg-blue-500 rounded-full"></div>
                                    <span>{evidence}</span>
                                  </li>
                                ))}
                              </ul>
                            </div>

                            <div>
                              <h5 className="font-medium text-sm mb-2">Prevention Strategies</h5>
                              <ul className="text-sm text-gray-600">
                                {prediction.prevention_strategies.map((strategy, index) => (
                                  <li key={index} className="flex items-center space-x-2">
                                    <div className="w-1 h-1 bg-green-500 rounded-full"></div>
                                    <span>{strategy}</span>
                                  </li>
                                ))}
                              </ul>
                            </div>
                          </div>
                        </div>
                      </div>
                    </Card>
                  ))}
                </div>
              ) : (
                <div className="text-center py-12">
                  <TrendingUp className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">No Predictions Available</h3>
                  <p className="text-gray-600 mb-4">Run behavioral analysis to generate predictive insights</p>
                  <Button onClick={startBehavioralAnalysis} className="bg-blue-600 hover:bg-blue-700">
                    <Brain className="h-4 w-4 mr-2" />
                    Generate Predictions
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Configuration Tab */}
        <TabsContent value="config" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Settings className="h-5 w-5" />
                <span>Analysis Configuration</span>
              </CardTitle>
              <CardDescription>Configure behavioral analysis parameters and AI models</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Analysis Duration</label>
                    <Select value={analysisConfig.duration} onValueChange={(value) => 
                      setAnalysisConfig({...analysisConfig, duration: value})}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="1h">1 Hour</SelectItem>
                        <SelectItem value="6h">6 Hours</SelectItem>
                        <SelectItem value="24h">24 Hours</SelectItem>
                        <SelectItem value="7d">7 Days</SelectItem>
                        <SelectItem value="30d">30 Days</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <label className="text-sm font-medium">Data Source</label>
                    <Select value={analysisConfig.dataSource} onValueChange={(value) => 
                      setAnalysisConfig({...analysisConfig, dataSource: value})}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="network_logs">Network Logs</SelectItem>
                        <SelectItem value="system_logs">System Logs</SelectItem>
                        <SelectItem value="application_logs">Application Logs</SelectItem>
                        <SelectItem value="security_logs">Security Logs</SelectItem>
                        <SelectItem value="all_logs">All Sources</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <label className="text-sm font-medium">AI Model</label>
                    <Select value={analysisConfig.aiModel} onValueChange={(value) => 
                      setAnalysisConfig({...analysisConfig, aiModel: value})}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="gpt-4">GPT-4 (Advanced)</SelectItem>
                        <SelectItem value="deepseek-v3">DeepSeek-V3 (Fast)</SelectItem>
                        <SelectItem value="claude-4">Claude-4 (Balanced)</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <label className="text-sm font-medium">Sensitivity Level: {analysisConfig.sensitivity}%</label>
                    <input
                      type="range"
                      min="0"
                      max="100"
                      value={analysisConfig.sensitivity}
                      onChange={(e) => setAnalysisConfig({...analysisConfig, sensitivity: Number(e.target.value)})}
                      className="w-full"
                    />
                    <div className="flex justify-between text-xs text-gray-500">
                      <span>Low</span>
                      <span>Medium</span>
                      <span>High</span>
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <div className="space-y-3">
                    <label className="text-sm font-medium">Detection Methods</label>
                    <div className="space-y-2">
                      <div className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          checked={analysisConfig.includeML}
                          onChange={(e) => setAnalysisConfig({...analysisConfig, includeML: e.target.checked})}
                          className="rounded"
                        />
                        <label className="text-sm">Machine Learning Based</label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          checked={analysisConfig.includeStatistical}
                          onChange={(e) => setAnalysisConfig({...analysisConfig, includeStatistical: e.target.checked})}
                          className="rounded"
                        />
                        <label className="text-sm">Statistical Analysis</label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          checked={analysisConfig.includeRuleBased}
                          onChange={(e) => setAnalysisConfig({...analysisConfig, includeRuleBased: e.target.checked})}
                          className="rounded"
                        />
                        <label className="text-sm">Rule-Based Detection</label>
                      </div>
                    </div>
                  </div>

                  <Card className="p-4 bg-blue-50 border-blue-200">
                    <h4 className="font-semibold text-blue-800 mb-2">Analysis Preview</h4>
                    <div className="text-sm text-blue-700 space-y-1">
                      <p>Duration: {analysisConfig.duration}</p>
                      <p>Data Source: {analysisConfig.dataSource.replace('_', ' ')}</p>
                      <p>AI Model: {analysisConfig.aiModel}</p>
                      <p>Sensitivity: {analysisConfig.sensitivity}%</p>
                      <p>Methods: {[
                        analysisConfig.includeML && 'ML',
                        analysisConfig.includeStatistical && 'Statistical',
                        analysisConfig.includeRuleBased && 'Rule-based'
                      ].filter(Boolean).join(', ')}</p>
                    </div>
                  </Card>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};