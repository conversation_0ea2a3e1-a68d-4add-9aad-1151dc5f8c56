"""
AI Predictive Vulnerability Discovery Wrapper for NexusScan Desktop
Wraps the PredictiveVulnerabilityDiscovery as a registered security tool.
"""

import logging
from typing import Dict, List, Optional, Callable, Any
from datetime import datetime

from security.tools.tool_registry import (
    ToolMetadata, ToolCategory, ToolCapabilities,
    ScanOptions, ScanResult, ToolStatus, SecurityTool, register_tool
)
from ai.predictive_vulnerability_discovery import (
    PredictiveVulnerabilityDiscovery,
    PredictionRequest,
    PredictionModel,
    AnalysisScope
)
from ai.services import AIServiceManager
from core.config import Config

logger = logging.getLogger(__name__)


@register_tool
class AIVulnerabilityDiscoveryTool(SecurityTool):
    """AI-powered predictive vulnerability discovery tool wrapper"""
    
    def __init__(self):
        """Initialize AI vulnerability discovery tool"""
        super().__init__()
        self.discovery_engine = None
        
    def get_metadata(self) -> ToolMetadata:
        """Get tool metadata"""
        return ToolMetadata(
            name="ai_vulnerability_discovery",
            display_name="AI Vulnerability Discovery",
            description="Predictive vulnerability discovery using machine learning to identify potential zero-day vulnerabilities",
            version="1.0.0",
            category=ToolCategory.AI_ANALYZER,
            author="NexusScan AI Team",
            capabilities=ToolCapabilities(
                supports_async=True,
                supports_progress=True,
                supports_cancellation=False,
                requires_root=False,
                network_access_required=True,
                output_formats=["json", "text"],
                supported_targets=["url", "ip", "domain", "code"]
            )
        )
    
    def check_availability(self) -> bool:
        """Check if AI vulnerability discovery is available"""
        try:
            # Test initialization with AI service manager
            config = Config()
            ai_manager = AIServiceManager(config)
            self.discovery_engine = PredictiveVulnerabilityDiscovery(ai_manager)
            return True
        except Exception as e:
            logger.error(f"AI Vulnerability Discovery not available: {e}")
            return False
    
    async def scan(self, options: ScanOptions,
                   progress_callback: Optional[Callable[[float, str], None]] = None) -> ScanResult:
        """Execute predictive vulnerability discovery"""
        start_time = datetime.now()
        
        try:
            if not self.discovery_engine:
                config = Config()
                ai_manager = AIServiceManager(config)
                self.discovery_engine = PredictiveVulnerabilityDiscovery(ai_manager)
            
            # Extract analysis parameters from options
            target = options.target
            analysis_scope = options.custom_options.get("analysis_scope", AnalysisScope.COMPREHENSIVE)
            prediction_model = options.custom_options.get("prediction_model", PredictionModel.HYBRID)
            include_behavioral = options.custom_options.get("include_behavioral", True)
            include_pattern = options.custom_options.get("include_pattern", True)
            include_statistical = options.custom_options.get("include_statistical", True)
            
            # Create prediction request
            request = PredictionRequest(
                target=target,
                analysis_scope=analysis_scope,
                prediction_model=prediction_model,
                include_behavioral_analysis=include_behavioral,
                include_pattern_analysis=include_pattern,
                include_statistical_analysis=include_statistical,
                confidence_threshold=options.custom_options.get("confidence_threshold", 0.7),
                max_predictions=options.custom_options.get("max_predictions", 50)
            )
            
            if progress_callback:
                progress_callback(0.1, "Initializing predictive vulnerability discovery...")
            
            # Analyze target
            if progress_callback:
                progress_callback(0.2, "Collecting target information...")
            
            # Perform predictive analysis
            if progress_callback:
                progress_callback(0.4, "Running behavioral analysis models...")
            
            discovery_result = await self.discovery_engine.predict_vulnerabilities(request)
            
            if progress_callback:
                progress_callback(0.8, "Analyzing prediction results...")
            
            # Format results
            parsed_results = {
                "discovery_summary": {
                    "target": target,
                    "total_predictions": discovery_result.total_predictions,
                    "high_confidence_predictions": discovery_result.high_confidence_predictions,
                    "analysis_models_used": discovery_result.models_used,
                    "overall_risk_score": discovery_result.overall_risk_score
                },
                "predicted_vulnerabilities": [
                    {
                        "vulnerability_type": vuln.vulnerability_type,
                        "description": vuln.description,
                        "confidence_score": vuln.confidence_score,
                        "potential_impact": vuln.potential_impact,
                        "discovery_method": vuln.discovery_method,
                        "indicators": vuln.behavioral_indicators,
                        "recommended_tests": vuln.recommended_tests
                    }
                    for vuln in discovery_result.predicted_vulnerabilities
                ],
                "behavioral_anomalies": discovery_result.behavioral_anomalies,
                "pattern_matches": discovery_result.pattern_matches,
                "statistical_outliers": discovery_result.statistical_outliers,
                "recommendations": discovery_result.recommendations,
                "educational_insights": discovery_result.educational_insights
            }
            
            if progress_callback:
                progress_callback(1.0, "Vulnerability discovery complete")
            
            end_time = datetime.now()
            
            return ScanResult(
                tool_name=self.metadata.name,
                target=options.target,
                status=ToolStatus.COMPLETED,
                start_time=start_time.isoformat(),
                end_time=end_time.isoformat(),
                duration_seconds=(end_time - start_time).total_seconds(),
                parsed_results=parsed_results,
                vulnerabilities=[
                    {
                        "type": vuln.vulnerability_type,
                        "severity": self._calculate_severity(vuln.confidence_score, vuln.potential_impact),
                        "confidence": vuln.confidence_score,
                        "description": vuln.description
                    }
                    for vuln in discovery_result.predicted_vulnerabilities
                    if vuln.confidence_score >= 0.7
                ],
                metadata={
                    "discovery_id": discovery_result.discovery_id,
                    "analysis_timestamp": discovery_result.timestamp,
                    "models_used": discovery_result.models_used
                }
            )
            
        except Exception as e:
            logger.error(f"AI vulnerability discovery failed: {e}")
            end_time = datetime.now()
            
            return ScanResult(
                tool_name=self.metadata.name,
                target=options.target,
                status=ToolStatus.FAILED,
                start_time=start_time.isoformat(),
                end_time=end_time.isoformat(),
                duration_seconds=(end_time - start_time).total_seconds(),
                errors=[str(e)]
            )
    
    def _calculate_severity(self, confidence: float, impact: str) -> str:
        """Calculate vulnerability severity based on confidence and impact"""
        if impact == "critical" and confidence >= 0.8:
            return "critical"
        elif impact in ["critical", "high"] and confidence >= 0.7:
            return "high"
        elif confidence >= 0.6:
            return "medium"
        else:
            return "low"