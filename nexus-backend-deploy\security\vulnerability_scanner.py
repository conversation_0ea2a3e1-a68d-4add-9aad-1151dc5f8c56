"""
Vulnerability Scanner Interface for NexusScan Desktop
Provides a unified interface for vulnerability scanning operations.
"""

import asyncio
import logging
from typing import Dict, List, Optional, Any, Protocol
from abc import ABC, abstractmethod
from dataclasses import dataclass
from datetime import datetime

from security.unified_tool_manager import UnifiedToolManager
from security.tools.tool_registry import <PERSON><PERSON><PERSON><PERSON><PERSON>, ToolStatus

logger = logging.getLogger(__name__)


@dataclass
class VulnerabilityResult:
    """Standardized vulnerability result"""
    vulnerability_id: str
    title: str
    description: str
    severity: str  # critical, high, medium, low, informational
    confidence: float  # 0.0 - 1.0
    target: str
    port: Optional[int] = None
    service: Optional[str] = None
    evidence: Optional[str] = None
    remediation: Optional[str] = None
    references: List[str] = None
    cve_id: Optional[str] = None
    cwe_id: Optional[str] = None
    discovery_method: str = "automated"
    discovery_timestamp: Optional[datetime] = None

    def __post_init__(self):
        if self.references is None:
            self.references = []
        if self.discovery_timestamp is None:
            self.discovery_timestamp = datetime.now()


@dataclass
class ScanConfiguration:
    """Vulnerability scan configuration"""
    target: str
    scan_types: List[str]  # network, web, ssl, etc.
    timeout: int = 300
    threads: int = 5
    aggressive: bool = False
    include_informational: bool = True
    custom_options: Dict[str, Any] = None

    def __post_init__(self):
        if self.custom_options is None:
            self.custom_options = {}


class VulnerabilityScanner(ABC):
    """Abstract base class for vulnerability scanners"""

    @abstractmethod
    async def scan_target(self, config: ScanConfiguration) -> List[VulnerabilityResult]:
        """Scan target and return vulnerabilities"""
        pass

    @abstractmethod
    def get_supported_scan_types(self) -> List[str]:
        """Get list of supported scan types"""
        pass

    @abstractmethod
    def is_available(self) -> bool:
        """Check if scanner is available"""
        pass


class UnifiedVulnerabilityScanner(VulnerabilityScanner):
    """Unified vulnerability scanner using multiple tools"""

    def __init__(self, tool_manager: Optional[UnifiedToolManager] = None):
        """Initialize unified vulnerability scanner"""
        self.tool_manager = tool_manager or UnifiedToolManager()
        self.supported_scan_types = [
            "network",
            "web", 
            "ssl",
            "database",
            "service",
            "comprehensive"
        ]
        
        logger.info("UnifiedVulnerabilityScanner initialized")

    async def scan_target(self, config: ScanConfiguration) -> List[VulnerabilityResult]:
        """Scan target using multiple tools and return unified results"""
        all_vulnerabilities = []
        
        try:
            for scan_type in config.scan_types:
                if scan_type not in self.supported_scan_types:
                    logger.warning(f"Unsupported scan type: {scan_type}")
                    continue
                
                vulns = await self._execute_scan_type(scan_type, config)
                all_vulnerabilities.extend(vulns)
            
            # Deduplicate vulnerabilities
            deduplicated = self._deduplicate_vulnerabilities(all_vulnerabilities)
            
            logger.info(f"Vulnerability scan completed: {len(deduplicated)} unique vulnerabilities found")
            return deduplicated
            
        except Exception as e:
            logger.error(f"Vulnerability scan failed: {e}")
            return []

    async def _execute_scan_type(self, scan_type: str, config: ScanConfiguration) -> List[VulnerabilityResult]:
        """Execute specific scan type"""
        vulnerabilities = []
        
        try:
            if scan_type == "network":
                vulnerabilities.extend(await self._network_scan(config))
            elif scan_type == "web":
                vulnerabilities.extend(await self._web_scan(config))
            elif scan_type == "ssl":
                vulnerabilities.extend(await self._ssl_scan(config))
            elif scan_type == "service":
                vulnerabilities.extend(await self._service_scan(config))
            elif scan_type == "comprehensive":
                # Run all scan types
                for st in ["network", "web", "ssl", "service"]:
                    vulnerabilities.extend(await self._execute_scan_type(st, config))
            
        except Exception as e:
            logger.error(f"Scan type {scan_type} failed: {e}")
        
        return vulnerabilities

    async def _network_scan(self, config: ScanConfiguration) -> List[VulnerabilityResult]:
        """Perform network vulnerability scan"""
        vulnerabilities = []
        
        try:
            # Use Nmap for network discovery
            nmap_args = ["-sV", "-sC", "--script", "vuln", config.target]
            
            result = await self.tool_manager.run_tool("nmap", nmap_args)
            
            if result["success"]:
                vulnerabilities.extend(self._parse_nmap_vulnerabilities(result, config.target))
            
        except Exception as e:
            logger.error(f"Network scan failed: {e}")
        
        return vulnerabilities

    async def _web_scan(self, config: ScanConfiguration) -> List[VulnerabilityResult]:
        """Perform web application vulnerability scan"""
        vulnerabilities = []
        
        try:
            # Use Nuclei for web vulnerability scanning
            nuclei_args = ["-u", config.target, "-tags", "web", "-json"]
            
            result = await self.tool_manager.run_tool("nuclei", nuclei_args)
            
            if result["success"]:
                vulnerabilities.extend(self._parse_nuclei_vulnerabilities(result, config.target))
            
        except Exception as e:
            logger.error(f"Web scan failed: {e}")
        
        return vulnerabilities

    async def _ssl_scan(self, config: ScanConfiguration) -> List[VulnerabilityResult]:
        """Perform SSL/TLS vulnerability scan"""
        vulnerabilities = []
        
        try:
            # Use testssl for SSL scanning if available
            if "testssl" in self.tool_manager.get_available_tools():
                testssl_args = ["--json", config.target]
                result = await self.tool_manager.run_tool("testssl", testssl_args)
                
                if result["success"]:
                    vulnerabilities.extend(self._parse_testssl_vulnerabilities(result, config.target))
            else:
                # Fallback to basic SSL checks
                vulnerabilities.extend(await self._basic_ssl_check(config.target))
            
        except Exception as e:
            logger.error(f"SSL scan failed: {e}")
        
        return vulnerabilities

    async def _service_scan(self, config: ScanConfiguration) -> List[VulnerabilityResult]:
        """Perform service-specific vulnerability scan"""
        vulnerabilities = []
        
        try:
            # Use Nuclei with service-specific templates
            nuclei_args = ["-u", config.target, "-tags", "service,network", "-json"]
            
            result = await self.tool_manager.run_tool("nuclei", nuclei_args)
            
            if result["success"]:
                vulnerabilities.extend(self._parse_nuclei_vulnerabilities(result, config.target))
            
        except Exception as e:
            logger.error(f"Service scan failed: {e}")
        
        return vulnerabilities

    def _parse_nmap_vulnerabilities(self, result: Dict[str, Any], target: str) -> List[VulnerabilityResult]:
        """Parse Nmap scan results for vulnerabilities"""
        vulnerabilities = []
        
        try:
            output = result.get("output", "")
            
            # Look for vulnerability script results
            if "VULNERABLE" in output:
                lines = output.split('\n')
                for line in lines:
                    if "VULNERABLE" in line and "CVE" in line:
                        vuln = self._create_vulnerability_from_nmap_line(line, target)
                        if vuln:
                            vulnerabilities.append(vuln)
            
        except Exception as e:
            logger.error(f"Failed to parse Nmap vulnerabilities: {e}")
        
        return vulnerabilities

    def _parse_nuclei_vulnerabilities(self, result: Dict[str, Any], target: str) -> List[VulnerabilityResult]:
        """Parse Nuclei scan results for vulnerabilities"""
        vulnerabilities = []
        
        try:
            output = result.get("output", "")
            
            # Parse JSON output from Nuclei
            import json
            for line in output.split('\n'):
                if line.strip() and line.startswith('{'):
                    try:
                        nuclei_result = json.loads(line)
                        vuln = self._create_vulnerability_from_nuclei(nuclei_result, target)
                        if vuln:
                            vulnerabilities.append(vuln)
                    except json.JSONDecodeError:
                        continue
            
        except Exception as e:
            logger.error(f"Failed to parse Nuclei vulnerabilities: {e}")
        
        return vulnerabilities

    def _parse_testssl_vulnerabilities(self, result: Dict[str, Any], target: str) -> List[VulnerabilityResult]:
        """Parse testssl scan results for vulnerabilities"""
        vulnerabilities = []
        
        try:
            output = result.get("output", "")
            
            # Parse testssl JSON output
            import json
            testssl_data = json.loads(output)
            
            for check in testssl_data:
                if check.get("severity") in ["HIGH", "CRITICAL", "MEDIUM"]:
                    vuln = self._create_vulnerability_from_testssl(check, target)
                    if vuln:
                        vulnerabilities.append(vuln)
            
        except Exception as e:
            logger.error(f"Failed to parse testssl vulnerabilities: {e}")
        
        return vulnerabilities

    async def _basic_ssl_check(self, target: str) -> List[VulnerabilityResult]:
        """Perform basic SSL checks"""
        vulnerabilities = []
        
        try:
            import ssl
            import socket
            from urllib.parse import urlparse
            
            # Parse target to get hostname and port
            if target.startswith(('http://', 'https://')):
                parsed = urlparse(target)
                hostname = parsed.hostname
                port = parsed.port or (443 if parsed.scheme == 'https' else 80)
            else:
                hostname = target
                port = 443
            
            # Check SSL certificate
            context = ssl.create_default_context()
            
            with socket.create_connection((hostname, port), timeout=10) as sock:
                with context.wrap_socket(sock, server_hostname=hostname) as ssock:
                    cert = ssock.getpeercert()
                    
                    # Basic certificate checks
                    if cert:
                        # Check for self-signed
                        if cert.get('issuer') == cert.get('subject'):
                            vulnerabilities.append(VulnerabilityResult(
                                vulnerability_id=f"ssl_self_signed_{hostname}_{port}",
                                title="Self-Signed SSL Certificate",
                                description="SSL certificate is self-signed",
                                severity="medium",
                                confidence=0.9,
                                target=target,
                                port=port,
                                evidence="Self-signed certificate detected",
                                remediation="Replace with properly signed certificate"
                            ))
            
        except Exception as e:
            logger.warning(f"Basic SSL check failed for {target}: {e}")
        
        return vulnerabilities

    def _create_vulnerability_from_nmap_line(self, line: str, target: str) -> Optional[VulnerabilityResult]:
        """Create vulnerability from Nmap output line"""
        try:
            import re
            
            # Extract CVE if present
            cve_match = re.search(r'CVE-\d{4}-\d{4,7}', line)
            cve_id = cve_match.group(0) if cve_match else None
            
            return VulnerabilityResult(
                vulnerability_id=f"nmap_{hash(line)}",
                title="Network Vulnerability",
                description=line.strip(),
                severity="medium",
                confidence=0.7,
                target=target,
                evidence=line.strip(),
                cve_id=cve_id,
                discovery_method="nmap"
            )
        except Exception:
            return None

    def _create_vulnerability_from_nuclei(self, nuclei_result: Dict[str, Any], target: str) -> Optional[VulnerabilityResult]:
        """Create vulnerability from Nuclei result"""
        try:
            severity_map = {
                "critical": "critical",
                "high": "high", 
                "medium": "medium",
                "low": "low",
                "info": "informational"
            }
            
            return VulnerabilityResult(
                vulnerability_id=nuclei_result.get("template-id", f"nuclei_{hash(str(nuclei_result))}"),
                title=nuclei_result.get("info", {}).get("name", "Unknown Vulnerability"),
                description=nuclei_result.get("info", {}).get("description", ""),
                severity=severity_map.get(nuclei_result.get("info", {}).get("severity", "medium"), "medium"),
                confidence=0.8,
                target=target,
                evidence=nuclei_result.get("matched-at", ""),
                discovery_method="nuclei",
                references=nuclei_result.get("info", {}).get("reference", [])
            )
        except Exception:
            return None

    def _create_vulnerability_from_testssl(self, testssl_result: Dict[str, Any], target: str) -> Optional[VulnerabilityResult]:
        """Create vulnerability from testssl result"""
        try:
            severity_map = {
                "CRITICAL": "critical",
                "HIGH": "high",
                "MEDIUM": "medium",
                "LOW": "low"
            }
            
            return VulnerabilityResult(
                vulnerability_id=f"ssl_{testssl_result.get('id', hash(str(testssl_result)))}",
                title=f"SSL/TLS Issue: {testssl_result.get('finding', 'Unknown')}",
                description=testssl_result.get('finding', ''),
                severity=severity_map.get(testssl_result.get('severity', 'MEDIUM'), 'medium'),
                confidence=0.8,
                target=target,
                evidence=testssl_result.get('finding', ''),
                discovery_method="testssl"
            )
        except Exception:
            return None

    def _deduplicate_vulnerabilities(self, vulnerabilities: List[VulnerabilityResult]) -> List[VulnerabilityResult]:
        """Remove duplicate vulnerabilities"""
        seen = set()
        deduplicated = []
        
        for vuln in vulnerabilities:
            # Create a hash based on target, title, and severity
            vuln_key = f"{vuln.target}_{vuln.title}_{vuln.severity}"
            if vuln_key not in seen:
                seen.add(vuln_key)
                deduplicated.append(vuln)
        
        return deduplicated

    def get_supported_scan_types(self) -> List[str]:
        """Get list of supported scan types"""
        return self.supported_scan_types.copy()

    def is_available(self) -> bool:
        """Check if scanner is available"""
        try:
            # Check if tool manager has any available tools
            available_tools = self.tool_manager.get_available_tools()
            return len(available_tools) > 0
        except Exception:
            return False


# Backward compatibility alias
class BaseVulnerabilityScanner(UnifiedVulnerabilityScanner):
    """Backward compatibility alias"""
    pass