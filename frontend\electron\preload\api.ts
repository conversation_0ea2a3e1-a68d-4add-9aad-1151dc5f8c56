import { contextBridge, ipc<PERSON><PERSON><PERSON> } from 'electron';

// Define the API interface that will be exposed to the renderer
export interface ElectronAPI {
  // App information
  app: {
    getVersion: () => Promise<string>;
    getName: () => Promise<string>;
    getPlatform: () => Promise<string>;
    getArch: () => Promise<string>;
  };

  // Window management
  window: {
    minimize: () => Promise<void>;
    maximize: () => Promise<void>;
    close: () => Promise<void>;
    isMaximized: () => Promise<boolean>;
  };

  // File system operations
  fs: {
    showOpenDialog: (options: any) => Promise<any>;
    showSaveDialog: (options: any) => Promise<any>;
    readFile: (filePath: string) => Promise<{ success: boolean; content?: string; error?: string }>;
    writeFile: (filePath: string, content: string) => Promise<{ success: boolean; error?: string }>;
  };

  // Store operations (persistent storage)
  store: {
    get: (key: string) => Promise<any>;
    set: (key: string, value: any) => Promise<void>;
    delete: (key: string) => Promise<void>;
    clear: () => Promise<void>;
  };

  // Backend operations
  backend: {
    getStatus: () => Promise<any>;
    connect: () => Promise<boolean>;
    disconnect: () => Promise<void>;
    executeRequest: (method: string, endpoint: string, data?: any) => Promise<any>;
    getAvailableTools: () => Promise<any[]>;
    executeTool: (toolId: string, config: any) => Promise<any>;
    getFerrariCapabilities: () => Promise<any>;
  };

  // WSL operations (Windows only)
  wsl: {
    getStatus: () => Promise<any>;
    getDistributions: () => Promise<any[]>;
    installTools: () => Promise<any[]>;
    executeCommand: (command: string, distribution?: string) => Promise<string>;
    executeSecurityTool: (tool: string, args: string[], options?: any) => Promise<any>;
  };

  // Notifications
  notification: {
    show: (options: { title: string; body: string; icon?: string }) => Promise<void>;
  };

  // Shell operations
  shell: {
    openExternal: (url: string) => Promise<void>;
    showItemInFolder: (path: string) => Promise<void>;
  };

  // Event listeners
  on: (channel: string, callback: (...args: any[]) => void) => void;
  off: (channel: string, callback: (...args: any[]) => void) => void;
  once: (channel: string, callback: (...args: any[]) => void) => void;

  // Remove listeners
  removeAllListeners: (channel: string) => void;
}

// Create the API object
const electronAPI: ElectronAPI = {
  // App information
  app: {
    getVersion: () => ipcRenderer.invoke('app:getVersion'),
    getName: () => ipcRenderer.invoke('app:getName'),
    getPlatform: () => ipcRenderer.invoke('app:getPlatform'),
    getArch: () => ipcRenderer.invoke('app:getArch')
  },

  // Window management
  window: {
    minimize: () => ipcRenderer.invoke('window:minimize'),
    maximize: () => ipcRenderer.invoke('window:maximize'),
    close: () => ipcRenderer.invoke('window:close'),
    isMaximized: () => ipcRenderer.invoke('window:isMaximized')
  },

  // File system operations
  fs: {
    showOpenDialog: (options) => ipcRenderer.invoke('fs:showOpenDialog', options),
    showSaveDialog: (options) => ipcRenderer.invoke('fs:showSaveDialog', options),
    readFile: (filePath) => ipcRenderer.invoke('fs:readFile', filePath),
    writeFile: (filePath, content) => ipcRenderer.invoke('fs:writeFile', filePath, content)
  },

  // Store operations
  store: {
    get: (key) => ipcRenderer.invoke('store:get', key),
    set: (key, value) => ipcRenderer.invoke('store:set', key, value),
    delete: (key) => ipcRenderer.invoke('store:delete', key),
    clear: () => ipcRenderer.invoke('store:clear')
  },

  // Backend operations
  backend: {
    getStatus: () => ipcRenderer.invoke('backend:getStatus'),
    connect: () => ipcRenderer.invoke('backend:connect'),
    disconnect: () => ipcRenderer.invoke('backend:disconnect'),
    executeRequest: (method, endpoint, data) => 
      ipcRenderer.invoke('backend:executeRequest', method, endpoint, data),
    getAvailableTools: () => ipcRenderer.invoke('backend:getAvailableTools'),
    executeTool: (toolId, config) => ipcRenderer.invoke('backend:executeTool', toolId, config),
    getFerrariCapabilities: () => ipcRenderer.invoke('backend:getFerrariCapabilities')
  },

  // WSL operations (Windows only)
  wsl: {
    getStatus: () => ipcRenderer.invoke('wsl:getStatus'),
    getDistributions: () => ipcRenderer.invoke('wsl:getDistributions'),
    installTools: () => ipcRenderer.invoke('wsl:installTools'),
    executeCommand: (command, distribution) => 
      ipcRenderer.invoke('wsl:executeCommand', command, distribution),
    executeSecurityTool: (tool, args, options) => 
      ipcRenderer.invoke('wsl:executeSecurityTool', tool, args, options)
  },

  // Notifications
  notification: {
    show: (options) => ipcRenderer.invoke('notification:show', options)
  },

  // Shell operations
  shell: {
    openExternal: (url) => ipcRenderer.invoke('shell:openExternal', url),
    showItemInFolder: (path) => ipcRenderer.invoke('shell:showItemInFolder', path)
  },

  // Event listeners
  on: (channel: string, callback: (...args: any[]) => void) => {
    // Validate channel names for security
    const validChannels = [
      'navigate',
      'open-campaign',
      'import-config',
      'export-config',
      'export-report',
      'open-search',
      'toggle-terminal',
      'toggle-sidebar',
      'show-ai-status',
      'cascade-windows',
      'tile-windows',
      'show-shortcuts',
      'check-updates',
      'backend-status-changed',
      'backend-connected',
      'backend-disconnected',
      'backend-error',
      'websocket-message',
      'wsl-status-changed',
      'tool-output',
      'tool-progress',
      'scan-completed',
      'ai-response'
    ];

    if (validChannels.includes(channel)) {
      ipcRenderer.on(channel, callback);
    } else {
      console.warn(`Invalid IPC channel: ${channel}`);
    }
  },

  off: (channel: string, callback: (...args: any[]) => void) => {
    ipcRenderer.off(channel, callback);
  },

  once: (channel: string, callback: (...args: any[]) => void) => {
    const validChannels = [
      'navigate',
      'open-campaign',
      'import-config',
      'export-config',
      'export-report',
      'open-search',
      'toggle-terminal',
      'toggle-sidebar',
      'show-ai-status',
      'cascade-windows',
      'tile-windows',
      'show-shortcuts',
      'check-updates',
      'backend-status-changed',
      'backend-connected',
      'backend-disconnected',
      'backend-error',
      'websocket-message',
      'wsl-status-changed',
      'tool-output',
      'tool-progress',
      'scan-completed',
      'ai-response'
    ];

    if (validChannels.includes(channel)) {
      ipcRenderer.once(channel, callback);
    } else {
      console.warn(`Invalid IPC channel: ${channel}`);
    }
  },

  removeAllListeners: (channel: string) => {
    ipcRenderer.removeAllListeners(channel);
  }
};

// Expose the API to the renderer process
try {
  contextBridge.exposeInMainWorld('electronAPI', electronAPI);
  console.log('✅ NexusScan Desktop API exposed successfully');
} catch (error) {
  console.error('❌ Failed to expose NexusScan Desktop API:', error);
}

// Add additional security validations
process.once('loaded', () => {
  // Ensure no Node.js APIs are available in the renderer
  const win = globalThis as any;
  if (win.process || win.require || win.module || win.Buffer) {
    console.error('🚨 Security violation: Node.js APIs detected in renderer!');
  }

  // Log successful preload
  console.log('🔒 NexusScan Desktop preload script loaded securely');
});