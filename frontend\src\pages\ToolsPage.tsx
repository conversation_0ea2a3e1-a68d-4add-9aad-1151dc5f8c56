/**
 * Tools Page - Security tools management and execution
 */
import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Tabs, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Search,
  Play,
  Settings,
  Shield,
  Network,
  Database,
  Globe,
  Code,
  Lock,
  AlertTriangle,
  CheckCircle,
  Clock
} from 'lucide-react';

interface SecurityTool {
  id: string;
  name: string;
  description: string;
  category: 'network' | 'web' | 'database' | 'ai' | 'reconnaissance' | 'exploitation';
  status: 'available' | 'running' | 'error' | 'offline';
  version: string;
  lastUsed?: string;
  popularity: number;
  documentation?: string;
}

export function ToolsPage() {
  const [searchTerm, setSearchTerm] = React.useState('');
  const [selectedCategory, setSelectedCategory] = React.useState('all');
  
  const [tools, setTools] = React.useState<SecurityTool[]>([
    {
      id: 'nmap',
      name: 'Nmap',
      description: 'Network discovery and security auditing',
      category: 'network',
      status: 'available',
      version: '7.94',
      lastUsed: '2024-01-20',
      popularity: 95
    },
    {
      id: 'nuclei',
      name: 'Nuclei',
      description: 'Fast and customizable vulnerability scanner',
      category: 'web',
      status: 'available',
      version: '3.1.5',
      lastUsed: '2024-01-19',
      popularity: 90
    },
    {
      id: 'sqlmap',
      name: 'SQLMap',
      description: 'Automatic SQL injection detection and exploitation',
      category: 'database',
      status: 'available',
      version: '1.7.12',
      lastUsed: '2024-01-18',
      popularity: 85
    },
    {
      id: 'dirb',
      name: 'Dirb',
      description: 'Web content scanner for hidden files and directories',
      category: 'web',
      status: 'available',
      version: '2.22',
      lastUsed: '2024-01-17',
      popularity: 80
    },
    {
      id: 'nikto',
      name: 'Nikto',
      description: 'Web server scanner for vulnerabilities',
      category: 'web',
      status: 'running',
      version: '2.5.0',
      lastUsed: '2024-01-20',
      popularity: 75
    },
    {
      id: 'gobuster',
      name: 'Gobuster',
      description: 'Directory and file brute-forcing tool',
      category: 'reconnaissance',
      status: 'available',
      version: '3.6',
      lastUsed: '2024-01-16',
      popularity: 70
    }
  ]);

  const categories = [
    { id: 'all', name: 'All Tools', icon: Shield },
    { id: 'network', name: 'Network', icon: Network },
    { id: 'web', name: 'Web', icon: Globe },
    { id: 'database', name: 'Database', icon: Database },
    { id: 'ai', name: 'AI Tools', icon: Code },
    { id: 'reconnaissance', name: 'Recon', icon: Search },
    { id: 'exploitation', name: 'Exploitation', icon: Lock }
  ];

  const getStatusColor = (status: SecurityTool['status']) => {
    switch (status) {
      case 'available': return 'text-green-500';
      case 'running': return 'text-blue-500';
      case 'error': return 'text-red-500';
      case 'offline': return 'text-gray-500';
      default: return 'text-gray-500';
    }
  };

  const getStatusIcon = (status: SecurityTool['status']) => {
    switch (status) {
      case 'available': return <CheckCircle className="h-3 w-3" />;
      case 'running': return <Clock className="h-3 w-3" />;
      case 'error': return <AlertTriangle className="h-3 w-3" />;
      case 'offline': return <AlertTriangle className="h-3 w-3" />;
      default: return <AlertTriangle className="h-3 w-3" />;
    }
  };

  const getCategoryIcon = (category: SecurityTool['category']) => {
    switch (category) {
      case 'network': return <Network className="h-4 w-4" />;
      case 'web': return <Globe className="h-4 w-4" />;
      case 'database': return <Database className="h-4 w-4" />;
      case 'ai': return <Code className="h-4 w-4" />;
      case 'reconnaissance': return <Search className="h-4 w-4" />;
      case 'exploitation': return <Lock className="h-4 w-4" />;
      default: return <Shield className="h-4 w-4" />;
    }
  };

  const filteredTools = tools.filter(tool => {
    const matchesSearch = tool.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         tool.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = selectedCategory === 'all' || tool.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  const handleRunTool = (toolId: string) => {
    console.log(`Running tool: ${toolId}`);
    // Update tool status to running
    setTools(prev => prev.map(tool => 
      tool.id === toolId ? { ...tool, status: 'running' as const } : tool
    ));
  };

  const handleConfigureTool = (toolId: string) => {
    console.log(`Configuring tool: ${toolId}`);
  };

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Security Tools</h1>
          <p className="text-muted-foreground">
            Manage and execute security testing tools
          </p>
        </div>
        <div className="flex items-center gap-3">
          <Badge variant="outline">
            {tools.filter(t => t.status === 'available').length} Available
          </Badge>
          <Badge variant="outline">
            {tools.filter(t => t.status === 'running').length} Running
          </Badge>
        </div>
      </div>

      {/* Search and Filters */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex items-center gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search tools..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Category Tabs */}
      <Tabs value={selectedCategory} onValueChange={setSelectedCategory}>
        <TabsList className="grid w-full grid-cols-7">
          {categories.map((category) => {
            const Icon = category.icon;
            return (
              <TabsTrigger key={category.id} value={category.id}>
                <Icon className="mr-2 h-4 w-4" />
                {category.name}
              </TabsTrigger>
            );
          })}
        </TabsList>

        <TabsContent value={selectedCategory} className="mt-6">
          {/* Tools Grid */}
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {filteredTools.map((tool) => (
              <Card key={tool.id} className="hover:shadow-lg transition-shadow">
                <CardHeader>
                  <div className="flex items-start justify-between">
                    <div className="flex items-center gap-3">
                      {getCategoryIcon(tool.category)}
                      <div>
                        <CardTitle className="text-lg">{tool.name}</CardTitle>
                        <CardDescription>v{tool.version}</CardDescription>
                      </div>
                    </div>
                    <Badge variant="outline" className={getStatusColor(tool.status)}>
                      {getStatusIcon(tool.status)}
                      {tool.status}
                    </Badge>
                  </div>
                </CardHeader>
                
                <CardContent className="space-y-4">
                  <p className="text-sm text-muted-foreground">
                    {tool.description}
                  </p>

                  {/* Popularity Bar */}
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-xs font-medium">Popularity</span>
                      <span className="text-xs text-muted-foreground">{tool.popularity}%</span>
                    </div>
                    <div className="w-full bg-secondary rounded-full h-1">
                      <div 
                        className="bg-primary h-1 rounded-full transition-all" 
                        style={{ width: `${tool.popularity}%` }}
                      />
                    </div>
                  </div>

                  {/* Last Used */}
                  {tool.lastUsed && (
                    <div className="flex items-center gap-2 text-xs text-muted-foreground">
                      <Clock className="h-3 w-3" />
                      Last used: {tool.lastUsed}
                    </div>
                  )}

                  {/* Category Badge */}
                  <Badge variant="secondary" className="capitalize">
                    {tool.category}
                  </Badge>

                  {/* Actions */}
                  <div className="flex gap-2 pt-2">
                    <Button 
                      size="sm"
                      onClick={() => handleRunTool(tool.id)}
                      disabled={tool.status === 'running' || tool.status === 'offline'}
                    >
                      <Play className="mr-1 h-3 w-3" />
                      Run
                    </Button>
                    <Button 
                      size="sm" 
                      variant="outline"
                      onClick={() => handleConfigureTool(tool.id)}
                    >
                      <Settings className="mr-1 h-3 w-3" />
                      Configure
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Empty State */}
          {filteredTools.length === 0 && (
            <Card>
              <CardContent className="pt-6">
                <div className="text-center py-8">
                  <Shield className="mx-auto h-12 w-12 text-muted-foreground" />
                  <h3 className="mt-2 text-sm font-semibold">No tools found</h3>
                  <p className="mt-1 text-sm text-muted-foreground">
                    {searchTerm ? 'Try adjusting your search terms.' : 'No tools available in this category.'}
                  </p>
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}