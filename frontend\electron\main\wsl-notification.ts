import { Notification, dialog } from 'electron';
import log from 'electron-log';

export class WSLNotificationManager {
  static showWSLInitializing() {
    if (Notification.isSupported()) {
      const notification = new Notification({
        title: 'NexusScan Desktop',
        body: 'Initializing Windows Subsystem for Linux...',
        icon: 'path/to/icon.png'
      });
      notification.show();
    }
  }

  static showWSLReady() {
    if (Notification.isSupported()) {
      const notification = new Notification({
        title: 'NexusScan Desktop',
        body: 'Security tools are ready! WSL initialized successfully.',
        icon: 'path/to/icon.png'
      });
      notification.show();
    }
  }

  static showWSLError(error: string) {
    dialog.showMessageBox({
      type: 'error',
      title: 'WSL Initialization Failed',
      message: 'Failed to initialize Windows Subsystem for Linux',
      detail: error + '\n\nSome security tools may not be available.',
      buttons: ['Continue Anyway', 'Exit']
    }).then(result => {
      if (result.response === 1) {
        process.exit(1);
      }
    });
  }

  static showWSLInstalling() {
    dialog.showMessageBox({
      type: 'info',
      title: 'Installing WSL',
      message: 'Windows Subsystem for Linux is being installed',
      detail: 'This is a one-time setup that may take a few minutes. NexusScan will restart automatically when complete.',
      buttons: ['OK']
    });
  }
}