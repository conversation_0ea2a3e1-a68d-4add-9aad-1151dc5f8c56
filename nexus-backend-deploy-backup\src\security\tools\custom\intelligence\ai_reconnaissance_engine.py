#!/usr/bin/env python3
"""
AI-Guided Reconnaissance Engine for NexusScan Desktop
Automated OSINT collection, subdomain enumeration, and intelligent attack surface mapping.
"""

import asyncio
import logging
import json
import re
import time
import hashlib
import socket
import ssl
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Optional, Any, Set, Tuple
from dataclasses import dataclass, asdict
from enum import Enum
import aiohttp
import dns.resolver
import dns.exception
from urllib.parse import urlparse, urljoin, quote
import base64
import whois
from pathlib import Path

from core.config import Config
from core.database import DatabaseManager
from ai.ai_service import AIServiceManager
from ai.services import AnalysisRequest

logger = logging.getLogger(__name__)


class ReconCategory(Enum):
    """Reconnaissance categories"""
    SUBDOMAIN_ENUMERATION = "subdomain_enumeration"
    OSINT_COLLECTION = "osint_collection"
    TECHNOLOGY_FINGERPRINTING = "technology_fingerprinting"
    ATTACK_SURFACE_MAPPING = "attack_surface_mapping"
    SOCIAL_ENGINEERING = "social_engineering"
    DNS_ENUMERATION = "dns_enumeration"
    PORT_DISCOVERY = "port_discovery"
    WEB_ENUMERATION = "web_enumeration"


class ConfidenceLevel(Enum):
    """Confidence levels for reconnaissance results"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    VERIFIED = "verified"


@dataclass
class ReconTarget:
    """Reconnaissance target"""
    id: str
    domain: str
    company_name: str
    ip_addresses: List[str]
    scope: List[str]  # Scope definitions
    exclusions: List[str]  # Out of scope items
    created_at: datetime


@dataclass
class SubdomainResult:
    """Subdomain enumeration result"""
    subdomain: str
    ip_addresses: List[str]
    status_code: int
    technologies: List[str]
    cdn_provider: str
    ssl_info: Dict[str, Any]
    response_headers: Dict[str, str]
    confidence: ConfidenceLevel
    discovery_method: str
    discovered_at: datetime


@dataclass
class OSINTData:
    """OSINT data collection result"""
    target: str
    data_type: str
    source: str
    content: Dict[str, Any]
    confidence: ConfidenceLevel
    relevance_score: float
    collected_at: datetime


@dataclass
class TechnologyFingerprint:
    """Technology fingerprinting result"""
    technology: str
    version: str
    category: str
    confidence: ConfidenceLevel
    evidence: List[str]
    cve_count: int
    risk_score: float
    detected_at: datetime


@dataclass
class AttackSurface:
    """Attack surface mapping result"""
    asset_type: str
    asset_value: str
    services: List[Dict[str, Any]]
    vulnerabilities: List[str]
    risk_level: str
    attack_vectors: List[str]
    priority_score: float
    mapped_at: datetime


class AIReconnaissanceEngine:
    """AI-guided reconnaissance and OSINT collection engine"""

    def __init__(self, config: Config, ai_service: Optional[AIServiceManager] = None, 
                 database: Optional[DatabaseManager] = None):
        """Initialize AI reconnaissance engine"""
        self.config = config
        self.ai_service = ai_service
        self.database = database
        
        # Reconnaissance components
        self.subdomain_enumerator = SubdomainEnumerator()
        self.osint_collector = OSINTCollector()
        self.technology_detector = TechnologyDetector()
        self.attack_surface_mapper = AttackSurfaceMapper()
        
        # Data storage
        self.results_storage = Path("data/reconnaissance")
        self.results_storage.mkdir(parents=True, exist_ok=True)
        
        # Current target
        self.current_target: Optional[ReconTarget] = None
        
        # Results cache
        self.subdomain_results: Dict[str, SubdomainResult] = {}
        self.osint_data: List[OSINTData] = []
        self.technology_fingerprints: Dict[str, List[TechnologyFingerprint]] = {}
        self.attack_surfaces: List[AttackSurface] = []
        
        # Configuration
        self.max_concurrent_requests = 50
        self.request_delay = 0.1  # seconds
        self.timeout = 30  # seconds
        
        logger.info("AI Reconnaissance Engine initialized")

    async def enumerate_subdomains_intelligently(self, domain: str) -> Dict[str, List[SubdomainResult]]:
        """AI-guided intelligent subdomain discovery"""
        
        try:
            logger.info(f"Starting intelligent subdomain enumeration for {domain}")
            
            results = {}
            
            # Multiple enumeration techniques
            techniques = [
                ("dns_bruteforce", self.subdomain_enumerator.dns_bruteforce),
                ("certificate_transparency", self.subdomain_enumerator.certificate_transparency),
                ("search_engines", self.subdomain_enumerator.search_engine_discovery),
                ("dns_zone_transfer", self.subdomain_enumerator.dns_zone_transfer),
                ("api_sources", self.subdomain_enumerator.api_sources),
                ("ai_prediction", self._ai_subdomain_prediction)
            ]
            
            # Execute techniques concurrently
            tasks = []
            for technique_name, technique_func in techniques:
                task = asyncio.create_task(
                    self._execute_subdomain_technique(technique_name, technique_func, domain)
                )
                tasks.append(task)
            
            # Collect results
            technique_results = await asyncio.gather(*tasks, return_exceptions=True)
            
            for i, result in enumerate(technique_results):
                technique_name = techniques[i][0]
                if isinstance(result, Exception):
                    logger.error(f"Technique {technique_name} failed: {result}")
                    results[technique_name] = []
                else:
                    results[technique_name] = result
            
            # Consolidate and verify results
            all_subdomains = self._consolidate_subdomains(results)
            verified_results = await self._verify_subdomains(all_subdomains, domain)
            
            # AI analysis for additional insights
            if self.ai_service:
                ai_insights = await self._analyze_subdomains_with_ai(verified_results)
                results["ai_insights"] = ai_insights
            
            # Store results
            await self._store_subdomain_results(domain, verified_results)
            
            return {
                "techniques": results,
                "verified_subdomains": verified_results
            }
            
        except Exception as e:
            logger.error(f"Subdomain enumeration failed: {e}")
            return {}

    async def _execute_subdomain_technique(self, technique_name: str, technique_func, domain: str) -> List[SubdomainResult]:
        """Execute a single subdomain enumeration technique"""
        try:
            logger.debug(f"Executing {technique_name} for {domain}")
            results = await technique_func(domain)
            logger.info(f"{technique_name} found {len(results)} subdomains")
            return results
        except Exception as e:
            logger.error(f"Technique {technique_name} failed: {e}")
            return []

    async def _ai_subdomain_prediction(self, domain: str) -> List[SubdomainResult]:
        """Use AI to predict likely subdomains"""
        try:
            if not self.ai_service:
                return []
            
            # Create AI analysis request
            analysis_request = AnalysisRequest(
                scan_results={
                    "domain": domain,
                    "task": "subdomain_prediction",
                    "context": "Generate likely subdomain patterns for penetration testing"
                },
                analysis_type="reconnaissance",
                include_remediation=False,
                include_risk_scoring=False,
                custom_context={
                    "output_format": "subdomain_list",
                    "prediction_type": "intelligent_enumeration"
                }
            )
            
            response = await self.ai_service.analyze_vulnerabilities(analysis_request)
            
            if response.success:
                # Parse AI response for subdomain predictions
                predictions = self._parse_ai_subdomain_predictions(response.executive_summary, domain)
                return predictions
            
        except Exception as e:
            logger.error(f"AI subdomain prediction failed: {e}")
        
        return []

    def _parse_ai_subdomain_predictions(self, ai_response: str, domain: str) -> List[SubdomainResult]:
        """Parse AI response for subdomain predictions"""
        predictions = []
        
        try:
            # Extract potential subdomains from AI response
            subdomain_patterns = [
                r'\b([a-zA-Z0-9-]+)\.' + re.escape(domain),
                r'\b([a-zA-Z0-9-]+)\.example\.com',  # AI might use examples
                r'([a-zA-Z0-9-]+)\s*[-:]\s*subdomain',
                r'([a-zA-Z0-9-]+)\s*[-:]\s*service'
            ]
            
            found_subdomains = set()
            
            for pattern in subdomain_patterns:
                matches = re.findall(pattern, ai_response, re.IGNORECASE)
                for match in matches:
                    if isinstance(match, tuple):
                        subdomain = match[0]
                    else:
                        subdomain = match
                    
                    if subdomain and len(subdomain) > 1 and subdomain not in found_subdomains:
                        full_subdomain = f"{subdomain}.{domain}"
                        found_subdomains.add(subdomain)
                        
                        prediction = SubdomainResult(
                            subdomain=full_subdomain,
                            ip_addresses=[],
                            status_code=0,
                            technologies=[],
                            cdn_provider="",
                            ssl_info={},
                            response_headers={},
                            confidence=ConfidenceLevel.LOW,
                            discovery_method="ai_prediction",
                            discovered_at=datetime.now()
                        )
                        predictions.append(prediction)
            
            logger.info(f"AI predicted {len(predictions)} potential subdomains")
            
        except Exception as e:
            logger.error(f"Failed to parse AI subdomain predictions: {e}")
        
        return predictions

    def _consolidate_subdomains(self, results: Dict[str, List[SubdomainResult]]) -> List[SubdomainResult]:
        """Consolidate subdomains from multiple techniques"""
        
        subdomain_map = {}
        
        for technique, subdomain_list in results.items():
            for subdomain_result in subdomain_list:
                subdomain = subdomain_result.subdomain.lower()
                
                if subdomain in subdomain_map:
                    # Merge results - prefer higher confidence
                    existing = subdomain_map[subdomain]
                    if subdomain_result.confidence.value > existing.confidence.value:
                        subdomain_map[subdomain] = subdomain_result
                    else:
                        # Merge discovery methods
                        existing.discovery_method += f", {subdomain_result.discovery_method}"
                else:
                    subdomain_map[subdomain] = subdomain_result
        
        return list(subdomain_map.values())

    async def _verify_subdomains(self, subdomains: List[SubdomainResult], domain: str) -> List[SubdomainResult]:
        """Verify subdomain existence and gather additional information"""
        
        verified_results = []
        
        # Create semaphore for concurrent requests
        semaphore = asyncio.Semaphore(self.max_concurrent_requests)
        
        async def verify_single_subdomain(subdomain_result: SubdomainResult):
            async with semaphore:
                try:
                    # DNS resolution
                    ip_addresses = await self._resolve_subdomain(subdomain_result.subdomain)
                    
                    if ip_addresses:
                        subdomain_result.ip_addresses = ip_addresses
                        subdomain_result.confidence = ConfidenceLevel.VERIFIED
                        
                        # HTTP probing
                        http_info = await self._probe_http_service(subdomain_result.subdomain)
                        subdomain_result.status_code = http_info.get("status_code", 0)
                        subdomain_result.response_headers = http_info.get("headers", {})
                        subdomain_result.technologies = http_info.get("technologies", [])
                        subdomain_result.ssl_info = http_info.get("ssl_info", {})
                        
                        verified_results.append(subdomain_result)
                        
                except Exception as e:
                    logger.debug(f"Failed to verify {subdomain_result.subdomain}: {e}")
                
                # Rate limiting
                await asyncio.sleep(self.request_delay)
        
        # Execute verification tasks
        tasks = [verify_single_subdomain(sub) for sub in subdomains]
        await asyncio.gather(*tasks, return_exceptions=True)
        
        logger.info(f"Verified {len(verified_results)} subdomains out of {len(subdomains)}")
        return verified_results

    async def _resolve_subdomain(self, subdomain: str) -> List[str]:
        """Resolve subdomain to IP addresses"""
        try:
            resolver = dns.resolver.Resolver()
            resolver.timeout = 5
            resolver.lifetime = 10
            
            answers = resolver.resolve(subdomain, 'A')
            return [str(answer) for answer in answers]
            
        except (dns.resolver.NXDOMAIN, dns.resolver.NoAnswer, dns.exception.Timeout):
            return []
        except Exception as e:
            logger.debug(f"DNS resolution failed for {subdomain}: {e}")
            return []

    async def _probe_http_service(self, subdomain: str) -> Dict[str, Any]:
        """Probe HTTP service for additional information"""
        
        result = {
            "status_code": 0,
            "headers": {},
            "technologies": [],
            "ssl_info": {}
        }
        
        try:
            # Try HTTPS first, then HTTP
            for scheme in ["https", "http"]:
                try:
                    url = f"{scheme}://{subdomain}"
                    
                    async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=10)) as session:
                        async with session.get(url, allow_redirects=False) as response:
                            result["status_code"] = response.status
                            result["headers"] = dict(response.headers)
                            
                            # Technology detection from headers
                            result["technologies"] = self.technology_detector.detect_from_headers(result["headers"])
                            
                            # SSL information for HTTPS
                            if scheme == "https":
                                result["ssl_info"] = await self._get_ssl_info(subdomain)
                            
                            break  # Success, don't try other scheme
                            
                except Exception:
                    continue  # Try next scheme
                    
        except Exception as e:
            logger.debug(f"HTTP probing failed for {subdomain}: {e}")
        
        return result

    async def _get_ssl_info(self, hostname: str) -> Dict[str, Any]:
        """Get SSL certificate information"""
        
        ssl_info = {}
        
        try:
            # Get SSL certificate
            context = ssl.create_default_context()
            
            with socket.create_connection((hostname, 443), timeout=10) as sock:
                with context.wrap_socket(sock, server_hostname=hostname) as ssock:
                    cert = ssock.getpeercert()
                    
                    ssl_info = {
                        "subject": dict(x[0] for x in cert.get("subject", [])),
                        "issuer": dict(x[0] for x in cert.get("issuer", [])),
                        "version": cert.get("version"),
                        "serial_number": cert.get("serialNumber"),
                        "not_before": cert.get("notBefore"),
                        "not_after": cert.get("notAfter"),
                        "subject_alt_names": [x[1] for x in cert.get("subjectAltName", []) if x[0] == "DNS"]
                    }
                    
        except Exception as e:
            logger.debug(f"SSL info gathering failed for {hostname}: {e}")
        
        return ssl_info

    async def gather_osint_data(self, target_company: str) -> Dict[str, List[OSINTData]]:
        """Gather OSINT data about target company"""
        
        try:
            logger.info(f"Starting OSINT collection for {target_company}")
            
            results = {}
            
            # OSINT collection techniques
            techniques = [
                ("social_media", self.osint_collector.social_media_intelligence),
                ("public_documents", self.osint_collector.public_documents),
                ("breach_data", self.osint_collector.breach_intelligence),
                ("employee_enumeration", self.osint_collector.employee_enumeration),
                ("github_intelligence", self.osint_collector.github_intelligence),
                ("job_postings", self.osint_collector.job_postings_analysis),
                ("whois_data", self.osint_collector.whois_intelligence),
                ("dns_records", self.osint_collector.dns_intelligence)
            ]
            
            # Execute OSINT techniques
            for technique_name, technique_func in techniques:
                try:
                    logger.debug(f"Executing OSINT technique: {technique_name}")
                    technique_results = await technique_func(target_company)
                    results[technique_name] = technique_results
                    logger.info(f"{technique_name} collected {len(technique_results)} data points")
                except Exception as e:
                    logger.error(f"OSINT technique {technique_name} failed: {e}")
                    results[technique_name] = []
            
            # AI correlation and analysis
            if self.ai_service:
                correlated_data = await self._correlate_osint_with_ai(results)
                results["ai_correlation"] = correlated_data
            
            # Store OSINT data
            await self._store_osint_data(target_company, results)
            
            return results
            
        except Exception as e:
            logger.error(f"OSINT data gathering failed: {e}")
            return {}

    async def _correlate_osint_with_ai(self, osint_results: Dict[str, List[OSINTData]]) -> List[OSINTData]:
        """Use AI to correlate and analyze OSINT data"""
        
        try:
            # Prepare OSINT data for AI analysis
            analysis_data = {}
            for category, data_list in osint_results.items():
                analysis_data[category] = [asdict(data) for data in data_list]
            
            # Create AI analysis request
            analysis_request = AnalysisRequest(
                scan_results=analysis_data,
                analysis_type="osint_correlation",
                include_remediation=False,
                include_risk_scoring=True,
                custom_context={
                    "task": "correlate_osint_data",
                    "output_format": "intelligence_summary"
                }
            )
            
            response = await self.ai_service.analyze_vulnerabilities(analysis_request)
            
            if response.success:
                # Parse AI correlation results
                correlations = self._parse_ai_osint_correlation(response)
                return correlations
            
        except Exception as e:
            logger.error(f"AI OSINT correlation failed: {e}")
        
        return []

    def _parse_ai_osint_correlation(self, ai_response) -> List[OSINTData]:
        """Parse AI OSINT correlation results"""
        
        correlations = []
        
        try:
            # Extract insights from AI response
            insights = [
                "High-value targets identified",
                "Technology stack patterns",
                "Employee behavior patterns",
                "Security exposure indicators"
            ]
            
            for insight in insights:
                correlation = OSINTData(
                    target="ai_correlation",
                    data_type="intelligence_summary",
                    source="ai_analysis",
                    content={"insight": insight, "details": ai_response.executive_summary},
                    confidence=ConfidenceLevel.MEDIUM,
                    relevance_score=ai_response.risk_score / 10.0,
                    collected_at=datetime.now()
                )
                correlations.append(correlation)
            
        except Exception as e:
            logger.error(f"Failed to parse AI OSINT correlation: {e}")
        
        return correlations

    async def fingerprint_technology_stack(self, web_responses: List[Dict[str, Any]]) -> Dict[str, List[TechnologyFingerprint]]:
        """AI-powered technology identification and version detection"""
        
        try:
            logger.info(f"Fingerprinting technology stack from {len(web_responses)} responses")
            
            results = {}
            
            for response_data in web_responses:
                url = response_data.get("url", "")
                headers = response_data.get("headers", {})
                body = response_data.get("body", "")
                
                # Multiple detection methods
                fingerprints = []
                
                # Header-based detection
                header_fingerprints = self.technology_detector.detect_from_headers(headers)
                fingerprints.extend(header_fingerprints)
                
                # Content-based detection
                content_fingerprints = self.technology_detector.detect_from_content(body)
                fingerprints.extend(content_fingerprints)
                
                # URL pattern detection
                url_fingerprints = self.technology_detector.detect_from_url(url)
                fingerprints.extend(url_fingerprints)
                
                # AI-enhanced detection
                if self.ai_service:
                    ai_fingerprints = await self._ai_technology_detection(response_data)
                    fingerprints.extend(ai_fingerprints)
                
                if fingerprints:
                    results[url] = fingerprints
            
            # Consolidate and verify results
            consolidated_results = self._consolidate_technology_fingerprints(results)
            
            return consolidated_results
            
        except Exception as e:
            logger.error(f"Technology fingerprinting failed: {e}")
            return {}

    async def _ai_technology_detection(self, response_data: Dict[str, Any]) -> List[TechnologyFingerprint]:
        """Use AI for advanced technology detection"""
        
        try:
            if not self.ai_service:
                return []
            
            # Create analysis request for technology detection
            analysis_request = AnalysisRequest(
                scan_results=response_data,
                analysis_type="technology_fingerprinting",
                include_remediation=False,
                include_risk_scoring=True,
                custom_context={
                    "detection_type": "web_technology",
                    "output_format": "technology_list"
                }
            )
            
            response = await self.ai_service.analyze_vulnerabilities(analysis_request)
            
            if response.success:
                return self._parse_ai_technology_detection(response)
            
        except Exception as e:
            logger.error(f"AI technology detection failed: {e}")
        
        return []

    def _parse_ai_technology_detection(self, ai_response) -> List[TechnologyFingerprint]:
        """Parse AI technology detection results"""
        
        fingerprints = []
        
        try:
            # Parse AI response for technology mentions
            technologies = [
                "apache", "nginx", "php", "python", "nodejs", "wordpress", "drupal",
                "mysql", "postgresql", "mongodb", "redis", "elasticsearch",
                "react", "angular", "vue", "jquery", "bootstrap"
            ]
            
            response_text = ai_response.executive_summary.lower()
            
            for tech in technologies:
                if tech in response_text:
                    # Extract version if mentioned
                    version_pattern = f"{tech}\\s*v?([0-9]+\\.[0-9]+(?:\\.[0-9]+)?)"
                    version_match = re.search(version_pattern, response_text, re.IGNORECASE)
                    version = version_match.group(1) if version_match else "unknown"
                    
                    fingerprint = TechnologyFingerprint(
                        technology=tech.title(),
                        version=version,
                        category="web_technology",
                        confidence=ConfidenceLevel.MEDIUM,
                        evidence=["ai_detection"],
                        cve_count=0,
                        risk_score=ai_response.risk_score / 10.0,
                        detected_at=datetime.now()
                    )
                    fingerprints.append(fingerprint)
            
        except Exception as e:
            logger.error(f"Failed to parse AI technology detection: {e}")
        
        return fingerprints

    def _consolidate_technology_fingerprints(self, results: Dict[str, List[TechnologyFingerprint]]) -> Dict[str, List[TechnologyFingerprint]]:
        """Consolidate technology fingerprints across URLs"""
        
        consolidated = {}
        tech_global_map = {}
        
        # Consolidate by technology
        for url, fingerprints in results.items():
            for fp in fingerprints:
                tech_key = f"{fp.technology}_{fp.version}".lower()
                
                if tech_key not in tech_global_map:
                    tech_global_map[tech_key] = fp
                else:
                    # Merge evidence
                    existing = tech_global_map[tech_key]
                    existing.evidence.extend(fp.evidence)
                    existing.evidence = list(set(existing.evidence))  # Remove duplicates
                    
                    # Increase confidence if found multiple times
                    if existing.confidence == ConfidenceLevel.LOW:
                        existing.confidence = ConfidenceLevel.MEDIUM
                    elif existing.confidence == ConfidenceLevel.MEDIUM:
                        existing.confidence = ConfidenceLevel.HIGH
        
        # Group by URL
        for url, fingerprints in results.items():
            consolidated[url] = list(tech_global_map.values())
        
        return consolidated

    async def map_attack_surface(self, reconnaissance_data: Dict[str, Any]) -> List[AttackSurface]:
        """AI analysis to identify optimal attack vectors"""
        
        try:
            logger.info("Mapping attack surface from reconnaissance data")
            
            attack_surfaces = []
            
            # Extract assets from reconnaissance data
            assets = self._extract_assets_from_recon(reconnaissance_data)
            
            for asset in assets:
                # Analyze each asset for attack potential
                attack_surface = await self._analyze_asset_attack_surface(asset)
                if attack_surface:
                    attack_surfaces.append(attack_surface)
            
            # AI-powered attack vector analysis
            if self.ai_service:
                enhanced_surfaces = await self._enhance_attack_surface_with_ai(attack_surfaces)
                attack_surfaces = enhanced_surfaces
            
            # Prioritize attack surfaces
            prioritized_surfaces = self._prioritize_attack_surfaces(attack_surfaces)
            
            return prioritized_surfaces
            
        except Exception as e:
            logger.error(f"Attack surface mapping failed: {e}")
            return []

    def _extract_assets_from_recon(self, recon_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Extract assets from reconnaissance data"""
        
        assets = []
        
        try:
            # Extract subdomains as web assets
            if "verified_subdomains" in recon_data:
                for subdomain_result in recon_data["verified_subdomains"]:
                    asset = {
                        "type": "web_application",
                        "value": subdomain_result.subdomain,
                        "ip_addresses": subdomain_result.ip_addresses,
                        "technologies": subdomain_result.technologies,
                        "ssl_info": subdomain_result.ssl_info,
                        "status_code": subdomain_result.status_code
                    }
                    assets.append(asset)
            
            # Extract technologies as potential targets
            if "technology_fingerprints" in recon_data:
                for url, fingerprints in recon_data["technology_fingerprints"].items():
                    for fp in fingerprints:
                        asset = {
                            "type": "technology",
                            "value": f"{fp.technology} {fp.version}",
                            "category": fp.category,
                            "risk_score": fp.risk_score,
                            "cve_count": fp.cve_count
                        }
                        assets.append(asset)
            
            # Extract OSINT data as social engineering targets
            if "osint_data" in recon_data:
                for category, data_list in recon_data["osint_data"].items():
                    for osint_data in data_list:
                        if osint_data.relevance_score > 0.5:  # Filter relevant data
                            asset = {
                                "type": "osint_target",
                                "value": osint_data.content,
                                "category": category,
                                "relevance": osint_data.relevance_score
                            }
                            assets.append(asset)
            
        except Exception as e:
            logger.error(f"Asset extraction failed: {e}")
        
        return assets

    async def _analyze_asset_attack_surface(self, asset: Dict[str, Any]) -> Optional[AttackSurface]:
        """Analyze individual asset for attack surface"""
        
        try:
            asset_type = asset.get("type", "unknown")
            asset_value = str(asset.get("value", ""))
            
            # Determine services and vulnerabilities
            services = []
            vulnerabilities = []
            attack_vectors = []
            risk_level = "low"
            
            if asset_type == "web_application":
                services = [{"type": "http", "port": 80}, {"type": "https", "port": 443}]
                
                # Check for common web vulnerabilities
                if asset.get("status_code") == 200:
                    attack_vectors.extend(["sql_injection", "xss", "csrf", "directory_traversal"])
                
                technologies = asset.get("technologies", [])
                for tech in technologies:
                    if "wordpress" in tech.lower():
                        vulnerabilities.append("wordpress_vulnerabilities")
                        attack_vectors.append("wordpress_exploit")
                    elif "php" in tech.lower():
                        vulnerabilities.append("php_vulnerabilities")
                        attack_vectors.append("php_injection")
                
                risk_level = "medium" if vulnerabilities else "low"
                
            elif asset_type == "technology":
                cve_count = asset.get("cve_count", 0)
                if cve_count > 0:
                    vulnerabilities.append(f"known_cves_{cve_count}")
                    risk_level = "high" if cve_count > 5 else "medium"
                    attack_vectors.append("cve_exploitation")
                
            elif asset_type == "osint_target":
                attack_vectors.append("social_engineering")
                risk_level = "low"
            
            # Calculate priority score
            priority_score = self._calculate_asset_priority(asset, len(vulnerabilities), len(attack_vectors))
            
            return AttackSurface(
                asset_type=asset_type,
                asset_value=asset_value,
                services=services,
                vulnerabilities=vulnerabilities,
                risk_level=risk_level,
                attack_vectors=attack_vectors,
                priority_score=priority_score,
                mapped_at=datetime.now()
            )
            
        except Exception as e:
            logger.error(f"Asset attack surface analysis failed: {e}")
            return None

    def _calculate_asset_priority(self, asset: Dict[str, Any], vuln_count: int, attack_vector_count: int) -> float:
        """Calculate asset priority score"""
        
        base_score = 0.0
        
        # Asset type scoring
        if asset.get("type") == "web_application":
            base_score += 0.3
        elif asset.get("type") == "technology":
            base_score += 0.2
        elif asset.get("type") == "osint_target":
            base_score += 0.1
        
        # Vulnerability scoring
        base_score += vuln_count * 0.2
        
        # Attack vector scoring
        base_score += attack_vector_count * 0.1
        
        # Technology-specific scoring
        technologies = asset.get("technologies", [])
        for tech in technologies:
            if any(keyword in tech.lower() for keyword in ["admin", "management", "control"]):
                base_score += 0.2
        
        # Risk scoring
        risk_score = asset.get("risk_score", 0.0)
        base_score += risk_score * 0.1
        
        return min(1.0, base_score)

    async def _enhance_attack_surface_with_ai(self, attack_surfaces: List[AttackSurface]) -> List[AttackSurface]:
        """Enhance attack surface analysis with AI insights"""
        
        try:
            if not self.ai_service:
                return attack_surfaces
            
            # Prepare data for AI analysis
            surface_data = [asdict(surface) for surface in attack_surfaces]
            
            analysis_request = AnalysisRequest(
                scan_results={"attack_surfaces": surface_data},
                analysis_type="attack_surface_analysis",
                include_remediation=True,
                include_risk_scoring=True,
                custom_context={
                    "analysis_focus": "attack_prioritization",
                    "output_format": "enhanced_attack_surface"
                }
            )
            
            response = await self.ai_service.analyze_vulnerabilities(analysis_request)
            
            if response.success:
                # Enhance attack surfaces with AI insights
                enhanced_surfaces = self._apply_ai_enhancements(attack_surfaces, response)
                return enhanced_surfaces
            
        except Exception as e:
            logger.error(f"AI attack surface enhancement failed: {e}")
        
        return attack_surfaces

    def _apply_ai_enhancements(self, attack_surfaces: List[AttackSurface], ai_response) -> List[AttackSurface]:
        """Apply AI enhancements to attack surfaces"""
        
        try:
            # Extract AI insights
            ai_insights = ai_response.recommendations
            
            for surface in attack_surfaces:
                # Enhance attack vectors based on AI recommendations
                for insight in ai_insights[:3]:  # Limit to top 3 insights
                    if any(keyword in insight.lower() for keyword in ["web", "application"]):
                        if surface.asset_type == "web_application":
                            surface.attack_vectors.append("ai_recommended_web_attack")
                            surface.priority_score += 0.1
                    
                    elif any(keyword in insight.lower() for keyword in ["social", "human"]):
                        if surface.asset_type == "osint_target":
                            surface.attack_vectors.append("ai_recommended_social_engineering")
                            surface.priority_score += 0.05
                
                # Ensure priority score doesn't exceed 1.0
                surface.priority_score = min(1.0, surface.priority_score)
            
        except Exception as e:
            logger.error(f"Failed to apply AI enhancements: {e}")
        
        return attack_surfaces

    def _prioritize_attack_surfaces(self, attack_surfaces: List[AttackSurface]) -> List[AttackSurface]:
        """Prioritize attack surfaces by potential impact and likelihood"""
        
        # Sort by priority score descending
        prioritized = sorted(attack_surfaces, key=lambda x: x.priority_score, reverse=True)
        
        logger.info(f"Prioritized {len(prioritized)} attack surfaces")
        
        return prioritized

    async def _store_subdomain_results(self, domain: str, results: List[SubdomainResult]):
        """Store subdomain enumeration results"""
        try:
            results_file = self.results_storage / f"subdomains_{domain}_{int(time.time())}.json"
            
            data = {
                "domain": domain,
                "timestamp": datetime.now().isoformat(),
                "results": [asdict(result) for result in results]
            }
            
            with open(results_file, 'w') as f:
                json.dump(data, f, indent=2, default=str)
            
            logger.info(f"Stored subdomain results: {results_file}")
            
        except Exception as e:
            logger.error(f"Failed to store subdomain results: {e}")

    async def _store_osint_data(self, target: str, results: Dict[str, List[OSINTData]]):
        """Store OSINT collection results"""
        try:
            results_file = self.results_storage / f"osint_{target}_{int(time.time())}.json"
            
            data = {
                "target": target,
                "timestamp": datetime.now().isoformat(),
                "results": {}
            }
            
            for category, data_list in results.items():
                data["results"][category] = [asdict(item) for item in data_list]
            
            with open(results_file, 'w') as f:
                json.dump(data, f, indent=2, default=str)
            
            logger.info(f"Stored OSINT results: {results_file}")
            
        except Exception as e:
            logger.error(f"Failed to store OSINT results: {e}")

    def get_reconnaissance_summary(self) -> Dict[str, Any]:
        """Get summary of reconnaissance activities"""
        return {
            "subdomains_found": len(self.subdomain_results),
            "osint_data_points": len(self.osint_data),
            "technologies_identified": sum(len(techs) for techs in self.technology_fingerprints.values()),
            "attack_surfaces": len(self.attack_surfaces),
            "current_target": self.current_target.domain if self.current_target else None,
            "last_activity": datetime.now().isoformat()
        }


# Supporting classes
class SubdomainEnumerator:
    """Subdomain enumeration techniques"""
    
    async def dns_bruteforce(self, domain: str) -> List[SubdomainResult]:
        """DNS bruteforce enumeration"""
        results = []
        
        # Common subdomain wordlist
        subdomains = [
            "www", "mail", "ftp", "admin", "api", "app", "blog", "dev", "test",
            "staging", "prod", "beta", "demo", "secure", "portal", "login",
            "auth", "sso", "cdn", "static", "assets", "img", "images",
            "docs", "help", "support", "wiki", "kb", "forum", "shop", "store"
        ]
        
        for subdomain in subdomains:
            full_subdomain = f"{subdomain}.{domain}"
            try:
                # Simple DNS resolution check
                resolver = dns.resolver.Resolver()
                resolver.timeout = 2
                answers = resolver.resolve(full_subdomain, 'A')
                
                if answers:
                    result = SubdomainResult(
                        subdomain=full_subdomain,
                        ip_addresses=[str(answer) for answer in answers],
                        status_code=0,
                        technologies=[],
                        cdn_provider="",
                        ssl_info={},
                        response_headers={},
                        confidence=ConfidenceLevel.HIGH,
                        discovery_method="dns_bruteforce",
                        discovered_at=datetime.now()
                    )
                    results.append(result)
                    
            except Exception:
                continue
        
        return results
    
    async def certificate_transparency(self, domain: str) -> List[SubdomainResult]:
        """Certificate transparency log search"""
        results = []
        
        try:
            # Query crt.sh for certificate transparency logs
            ct_url = f"https://crt.sh/?q=%25.{domain}&output=json"
            
            async with aiohttp.ClientSession() as session:
                async with session.get(ct_url, timeout=aiohttp.ClientTimeout(total=30)) as response:
                    if response.status == 200:
                        data = await response.json()
                        
                        subdomains = set()
                        for entry in data:
                            name_value = entry.get("name_value", "")
                            for name in name_value.split("\n"):
                                name = name.strip()
                                if name.endswith(f".{domain}") and "*" not in name:
                                    subdomains.add(name)
                        
                        for subdomain in subdomains:
                            result = SubdomainResult(
                                subdomain=subdomain,
                                ip_addresses=[],
                                status_code=0,
                                technologies=[],
                                cdn_provider="",
                                ssl_info={},
                                response_headers={},
                                confidence=ConfidenceLevel.HIGH,
                                discovery_method="certificate_transparency",
                                discovered_at=datetime.now()
                            )
                            results.append(result)
                            
        except Exception as e:
            logger.error(f"Certificate transparency search failed: {e}")
        
        return results
    
    async def search_engine_discovery(self, domain: str) -> List[SubdomainResult]:
        """Search engine subdomain discovery"""
        # Placeholder implementation
        return []
    
    async def dns_zone_transfer(self, domain: str) -> List[SubdomainResult]:
        """DNS zone transfer attempt"""
        # Placeholder implementation
        return []
    
    async def api_sources(self, domain: str) -> List[SubdomainResult]:
        """API-based subdomain discovery"""
        # Placeholder implementation
        return []


class OSINTCollector:
    """OSINT data collection techniques"""
    
    async def social_media_intelligence(self, target: str) -> List[OSINTData]:
        """Social media intelligence gathering"""
        # Placeholder implementation
        return []
    
    async def public_documents(self, target: str) -> List[OSINTData]:
        """Public document discovery"""
        # Placeholder implementation
        return []
    
    async def breach_intelligence(self, target: str) -> List[OSINTData]:
        """Breach data intelligence"""
        # Placeholder implementation
        return []
    
    async def employee_enumeration(self, target: str) -> List[OSINTData]:
        """Employee enumeration"""
        # Placeholder implementation
        return []
    
    async def github_intelligence(self, target: str) -> List[OSINTData]:
        """GitHub intelligence gathering"""
        # Placeholder implementation
        return []
    
    async def job_postings_analysis(self, target: str) -> List[OSINTData]:
        """Job postings analysis"""
        # Placeholder implementation
        return []
    
    async def whois_intelligence(self, target: str) -> List[OSINTData]:
        """WHOIS intelligence gathering"""
        results = []
        
        try:
            # WHOIS lookup
            whois_data = whois.whois(target)
            
            osint_data = OSINTData(
                target=target,
                data_type="whois",
                source="whois_server",
                content={
                    "registrar": whois_data.registrar,
                    "creation_date": str(whois_data.creation_date),
                    "expiration_date": str(whois_data.expiration_date),
                    "name_servers": whois_data.name_servers,
                    "status": whois_data.status
                },
                confidence=ConfidenceLevel.HIGH,
                relevance_score=0.8,
                collected_at=datetime.now()
            )
            results.append(osint_data)
            
        except Exception as e:
            logger.error(f"WHOIS intelligence failed: {e}")
        
        return results
    
    async def dns_intelligence(self, target: str) -> List[OSINTData]:
        """DNS intelligence gathering"""
        # Placeholder implementation
        return []


class TechnologyDetector:
    """Technology detection and fingerprinting"""
    
    def detect_from_headers(self, headers: Dict[str, str]) -> List[TechnologyFingerprint]:
        """Detect technologies from HTTP headers"""
        fingerprints = []
        
        header_signatures = {
            "server": {
                "apache": r"apache/(\d+\.\d+)",
                "nginx": r"nginx/(\d+\.\d+)",
                "iis": r"microsoft-iis/(\d+\.\d+)"
            },
            "x-powered-by": {
                "php": r"php/(\d+\.\d+)",
                "asp.net": r"asp\.net",
                "express": r"express"
            }
        }
        
        for header_name, header_value in headers.items():
            header_lower = header_name.lower()
            
            if header_lower in header_signatures:
                for tech, pattern in header_signatures[header_lower].items():
                    match = re.search(pattern, header_value, re.IGNORECASE)
                    if match:
                        version = match.group(1) if match.groups() else "unknown"
                        
                        fingerprint = TechnologyFingerprint(
                            technology=tech.title(),
                            version=version,
                            category="web_server",
                            confidence=ConfidenceLevel.HIGH,
                            evidence=[f"header_{header_name}"],
                            cve_count=0,
                            risk_score=0.3,
                            detected_at=datetime.now()
                        )
                        fingerprints.append(fingerprint)
        
        return fingerprints
    
    def detect_from_content(self, content: str) -> List[TechnologyFingerprint]:
        """Detect technologies from page content"""
        fingerprints = []
        
        content_signatures = {
            "wordpress": r"wp-content|wp-includes|wordpress",
            "drupal": r"drupal|sites/default",
            "joomla": r"joomla|option=com_",
            "jquery": r"jquery[/-](\d+\.\d+)",
            "bootstrap": r"bootstrap[/-](\d+\.\d+)",
            "react": r"react[/-](\d+\.\d+)"
        }
        
        for tech, pattern in content_signatures.items():
            match = re.search(pattern, content, re.IGNORECASE)
            if match:
                version = match.group(1) if match.groups() else "unknown"
                
                fingerprint = TechnologyFingerprint(
                    technology=tech.title(),
                    version=version,
                    category="web_framework",
                    confidence=ConfidenceLevel.MEDIUM,
                    evidence=["content_analysis"],
                    cve_count=0,
                    risk_score=0.2,
                    detected_at=datetime.now()
                )
                fingerprints.append(fingerprint)
        
        return fingerprints
    
    def detect_from_url(self, url: str) -> List[TechnologyFingerprint]:
        """Detect technologies from URL patterns"""
        fingerprints = []
        
        url_patterns = {
            "php": r"\.php",
            "asp": r"\.asp",
            "jsp": r"\.jsp",
            "wordpress": r"wp-admin|wp-content"
        }
        
        for tech, pattern in url_patterns.items():
            if re.search(pattern, url, re.IGNORECASE):
                fingerprint = TechnologyFingerprint(
                    technology=tech.upper(),
                    version="unknown",
                    category="web_technology",
                    confidence=ConfidenceLevel.MEDIUM,
                    evidence=["url_pattern"],
                    cve_count=0,
                    risk_score=0.1,
                    detected_at=datetime.now()
                )
                fingerprints.append(fingerprint)
        
        return fingerprints


class AttackSurfaceMapper:
    """Attack surface mapping and analysis"""
    
    def map_web_assets(self, assets: List[Dict[str, Any]]) -> List[AttackSurface]:
        """Map web assets to attack surfaces"""
        # Implementation for web asset mapping
        return []
    
    def map_network_assets(self, assets: List[Dict[str, Any]]) -> List[AttackSurface]:
        """Map network assets to attack surfaces"""
        # Implementation for network asset mapping
        return []
    
    def analyze_attack_chains(self, surfaces: List[AttackSurface]) -> List[Dict[str, Any]]:
        """Analyze potential attack chains"""
        # Implementation for attack chain analysis
        return []