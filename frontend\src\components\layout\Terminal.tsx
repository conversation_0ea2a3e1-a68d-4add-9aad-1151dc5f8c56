/**
 * Integrated Terminal Component
 * Provides real-time command output and tool execution monitoring
 */
import React, { useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Terminal as TerminalIcon,
  X,
  Maximize2,
  Minimize2,
  Copy,
  Trash2,
  Download,
  Play,
  Square
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { webSocketService, ToolOutputMessage } from '@/services/websocket';
import { toast } from 'sonner';

/**
 * Terminal output line interface
 */
interface TerminalLine {
  id: string;
  timestamp: Date;
  content: string;
  type: 'info' | 'success' | 'warning' | 'error' | 'command' | 'output';
  executionId?: string;
  toolId?: string;
}

/**
 * Terminal component props
 */
interface TerminalProps {
  isOpen: boolean;
  onToggle: () => void;
  className?: string;
  height?: number;
}

/**
 * Terminal component for real-time command output
 */
export function Terminal({ isOpen, onToggle, className, height = 300 }: TerminalProps) {
  const [lines, setLines] = React.useState<TerminalLine[]>([]);
  const [isMaximized, setIsMaximized] = React.useState(false);
  const [isAutoScroll, setIsAutoScroll] = React.useState(true);
  const [filter, setFilter] = React.useState<string>('');
  const outputRef = useRef<HTMLDivElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  
  // Add initial welcome message
  React.useEffect(() => {
    addLine({
      content: 'NexusScan Desktop Terminal initialized. Ready for tool execution...',
      type: 'info'
    });
    
    // Subscribe to WebSocket tool output
    webSocketService.on('onToolOutput', handleToolOutput);
    
    return () => {
      webSocketService.off('onToolOutput');
    };
  }, []);
  
  // Auto-scroll to bottom when new lines are added
  useEffect(() => {
    if (isAutoScroll && outputRef.current) {
      outputRef.current.scrollTop = outputRef.current.scrollHeight;
    }
  }, [lines, isAutoScroll]);
  
  // Handle tool output from WebSocket
  const handleToolOutput = (message: ToolOutputMessage) => {
    addLine({
      content: message.data.output,
      type: message.data.stream === 'stderr' ? 'error' : 'output',
      executionId: message.data.executionId,
      toolId: message.data.toolId
    });
  };
  
  // Add a new line to the terminal
  const addLine = (lineData: Omit<TerminalLine, 'id' | 'timestamp'>) => {
    const newLine: TerminalLine = {
      id: crypto.randomUUID(),
      timestamp: new Date(),
      ...lineData
    };
    
    setLines(prev => {
      // Keep only the last 1000 lines for performance
      const newLines = [...prev, newLine];
      return newLines.slice(-1000);
    });
  };
  
  // Clear terminal
  const clearTerminal = () => {
    setLines([]);
    addLine({
      content: 'Terminal cleared',
      type: 'info'
    });
  };
  
  // Copy terminal content
  const copyTerminal = async () => {
    const content = filteredLines
      .map(line => `[${line.timestamp.toLocaleTimeString()}] ${line.content}`)
      .join('\n');
    
    try {
      await navigator.clipboard.writeText(content);
      toast.success('Terminal content copied to clipboard');
    } catch (error) {
      toast.error('Failed to copy terminal content');
    }
  };
  
  // Export terminal content
  const exportTerminal = () => {
    const content = filteredLines
      .map(line => `[${line.timestamp.toISOString()}] [${line.type.toUpperCase()}] ${line.content}`)
      .join('\n');
    
    const blob = new Blob([content], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `nexusscan-terminal-${new Date().toISOString().slice(0, 19)}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    
    toast.success('Terminal content exported');
  };
  
  // Toggle maximize
  const toggleMaximize = () => {
    setIsMaximized(!isMaximized);
  };
  
  // Handle scroll to detect if user scrolled up
  const handleScroll = () => {
    if (outputRef.current) {
      const { scrollTop, scrollHeight, clientHeight } = outputRef.current;
      const isAtBottom = scrollTop + clientHeight >= scrollHeight - 10;
      setIsAutoScroll(isAtBottom);
    }
  };
  
  // Filter lines based on search
  const filteredLines = React.useMemo(() => {
    if (!filter) return lines;
    return lines.filter(line => 
      line.content.toLowerCase().includes(filter.toLowerCase()) ||
      line.type.toLowerCase().includes(filter.toLowerCase()) ||
      (line.toolId && line.toolId.toLowerCase().includes(filter.toLowerCase()))
    );
  }, [lines, filter]);
  
  // Get line type styles
  const getLineStyle = (type: TerminalLine['type']) => {
    switch (type) {
      case 'info':
        return 'text-blue-400';
      case 'success':
        return 'text-green-400';
      case 'warning':
        return 'text-yellow-400';
      case 'error':
        return 'text-red-400';
      case 'command':
        return 'text-purple-400 font-medium';
      case 'output':
      default:
        return 'text-gray-300';
    }
  };
  
  const getLinePrefix = (type: TerminalLine['type']) => {
    switch (type) {
      case 'info':
        return '[INFO]';
      case 'success':
        return '[SUCCESS]';
      case 'warning':
        return '[WARN]';
      case 'error':
        return '[ERROR]';
      case 'command':
        return '[CMD]';
      case 'output':
      default:
        return '[OUT]';
    }
  };
  
  if (!isOpen) {
    return null;
  }
  
  const terminalHeight = isMaximized ? '60vh' : `${height}px`;
  
  return (
    <div 
      ref={containerRef}
      className={cn(
        'border-t border-border bg-card',
        'flex flex-col',
        className
      )}
      style={{ height: terminalHeight }}
    >
      {/* Terminal header */}
      <div className="flex items-center justify-between px-4 py-2 border-b border-border bg-muted/30">
        <div className="flex items-center gap-2">
          <TerminalIcon className="h-4 w-4" />
          <span className="font-medium text-sm">Terminal</span>
          <Badge variant="outline" className="text-xs">
            {filteredLines.length} lines
          </Badge>
        </div>
        
        <div className="flex items-center gap-1">
          {/* Filter input */}
          <input
            type="text"
            placeholder="Filter..."
            value={filter}
            onChange={(e) => setFilter(e.target.value)}
            className="px-2 py-1 text-xs bg-background border border-border rounded w-24 focus:w-32 transition-all focus:outline-none focus:ring-1 focus:ring-primary"
          />
          
          {/* Auto-scroll indicator */}
          {!isAutoScroll && (
            <Badge variant="secondary" className="text-xs cursor-pointer" 
                   onClick={() => setIsAutoScroll(true)}>
              Scroll paused
            </Badge>
          )}
          
          {/* Control buttons */}
          <Button
            variant="ghost"
            size="sm"
            onClick={copyTerminal}
            className="p-1 h-6 w-6"
            title="Copy all"
          >
            <Copy className="h-3 w-3" />
          </Button>
          
          <Button
            variant="ghost"
            size="sm"
            onClick={exportTerminal}
            className="p-1 h-6 w-6"
            title="Export to file"
          >
            <Download className="h-3 w-3" />
          </Button>
          
          <Button
            variant="ghost"
            size="sm"
            onClick={clearTerminal}
            className="p-1 h-6 w-6"
            title="Clear terminal"
          >
            <Trash2 className="h-3 w-3" />
          </Button>
          
          <Button
            variant="ghost"
            size="sm"
            onClick={toggleMaximize}
            className="p-1 h-6 w-6"
            title={isMaximized ? 'Minimize' : 'Maximize'}
          >
            {isMaximized ? (
              <Minimize2 className="h-3 w-3" />
            ) : (
              <Maximize2 className="h-3 w-3" />
            )}
          </Button>
          
          <Button
            variant="ghost"
            size="sm"
            onClick={onToggle}
            className="p-1 h-6 w-6"
            title="Close terminal"
          >
            <X className="h-3 w-3" />
          </Button>
        </div>
      </div>
      
      {/* Terminal output */}
      <div 
        ref={outputRef}
        className="flex-1 overflow-y-auto p-4 bg-gray-900 text-gray-100 font-mono text-sm leading-relaxed"
        onScroll={handleScroll}
      >
        {filteredLines.length === 0 ? (
          <div className="text-gray-500 italic">
            {filter ? 'No lines match the current filter' : 'No output yet...'}
          </div>
        ) : (
          filteredLines.map((line) => (
            <div key={line.id} className="flex gap-3 mb-1 hover:bg-gray-800/50 px-2 py-1 rounded">
              <span className="text-gray-500 text-xs shrink-0 w-20">
                {line.timestamp.toLocaleTimeString()}
              </span>
              <span className={cn('text-xs shrink-0 w-16', getLineStyle(line.type))}>
                {getLinePrefix(line.type)}
              </span>
              {line.toolId && (
                <span className="text-blue-300 text-xs shrink-0 w-20 truncate">
                  [{line.toolId}]
                </span>
              )}
              <span className={cn('flex-1 break-all', getLineStyle(line.type))}>
                {line.content}
              </span>
            </div>
          ))
        )}
        
        {/* Auto-scroll anchor */}
        <div id="terminal-bottom" />
      </div>
    </div>
  );
}

// Export utility function for adding lines from other components
export const addTerminalLine = (content: string, type: TerminalLine['type'] = 'info') => {
  // This would need to be implemented with a global state manager
  // For now, it's a placeholder for the pattern
  console.log(`Terminal: [${type.toUpperCase()}] ${content}`);
};