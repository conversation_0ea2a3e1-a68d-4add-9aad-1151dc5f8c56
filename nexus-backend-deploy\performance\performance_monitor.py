"""
Performance Monitoring and Optimization System for NexusScan Desktop
Real-time performance tracking, bottleneck detection, and optimization recommendations.
"""

import asyncio
import logging
import time
try:
    import psutil
    HAS_PSUTIL = True
except ImportError:
    HAS_PSUTIL = False
    print("⚠️ Warning: psutil not available - performance monitoring limited")
import threading
from typing import Dict, List, Optional, Any, Callable
from enum import Enum
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from pathlib import Path
import json
import statistics
from collections import deque, defaultdict
import tracemalloc
import gc
import weakref

from core.config import Config
from core.events import EventManager, EventTypes

logger = logging.getLogger(__name__)


class MetricType(Enum):
    """Types of performance metrics"""
    CPU_USAGE = "cpu_usage"
    MEMORY_USAGE = "memory_usage"
    DISK_IO = "disk_io"
    NETWORK_IO = "network_io"
    EXECUTION_TIME = "execution_time"
    THREAD_COUNT = "thread_count"
    DATABASE_QUERY = "database_query"
    API_RESPONSE = "api_response"
    SCAN_PERFORMANCE = "scan_performance"
    UI_RESPONSIVENESS = "ui_responsiveness"


class AlertLevel(Enum):
    """Performance alert levels"""
    INFO = "info"
    WARNING = "warning"
    CRITICAL = "critical"
    EMERGENCY = "emergency"


@dataclass
class PerformanceMetric:
    """Individual performance metric"""
    metric_id: str
    metric_type: MetricType
    value: float
    unit: str
    timestamp: datetime
    context: Dict[str, Any]
    threshold_exceeded: bool
    alert_level: Optional[AlertLevel]


@dataclass
class PerformanceProfile:
    """Performance profile for specific operations"""
    profile_id: str
    operation_name: str
    start_time: datetime
    end_time: Optional[datetime]
    duration_ms: Optional[float]
    cpu_usage: Optional[float]
    memory_usage: Optional[float]
    io_operations: Optional[int]
    thread_count: Optional[int]
    custom_metrics: Dict[str, Any]


@dataclass
class OptimizationRecommendation:
    """Performance optimization recommendation"""
    recommendation_id: str
    category: str
    priority: str  # low, medium, high, critical
    title: str
    description: str
    issue_detected: str
    potential_impact: str
    implementation_effort: str
    code_location: Optional[str]
    suggested_fix: str
    estimated_improvement: str


class PerformanceThresholds:
    """Performance threshold configurations"""
    
    def __init__(self):
        self.thresholds = {
            MetricType.CPU_USAGE: {
                "warning": 70.0,
                "critical": 85.0,
                "emergency": 95.0
            },
            MetricType.MEMORY_USAGE: {
                "warning": 70.0,
                "critical": 85.0,
                "emergency": 95.0
            },
            MetricType.EXECUTION_TIME: {
                "warning": 5000.0,  # 5 seconds
                "critical": 10000.0,  # 10 seconds
                "emergency": 30000.0  # 30 seconds
            },
            MetricType.DATABASE_QUERY: {
                "warning": 1000.0,  # 1 second
                "critical": 3000.0,  # 3 seconds
                "emergency": 10000.0  # 10 seconds
            },
            MetricType.API_RESPONSE: {
                "warning": 2000.0,  # 2 seconds
                "critical": 5000.0,  # 5 seconds
                "emergency": 15000.0  # 15 seconds
            }
        }
    
    def get_alert_level(self, metric_type: MetricType, value: float) -> Optional[AlertLevel]:
        """Get alert level for metric value"""
        thresholds = self.thresholds.get(metric_type, {})
        
        if value >= thresholds.get("emergency", float('inf')):
            return AlertLevel.EMERGENCY
        elif value >= thresholds.get("critical", float('inf')):
            return AlertLevel.CRITICAL
        elif value >= thresholds.get("warning", float('inf')):
            return AlertLevel.WARNING
        else:
            return None


class PerformanceMonitor:
    """Performance monitoring and optimization system"""
    
    def __init__(self, config: Config, event_manager: EventManager):
        """Initialize performance monitor"""
        self.config = config
        self.event_manager = event_manager
        
        # Performance data storage
        self.metrics_history = defaultdict(lambda: deque(maxlen=1000))
        self.active_profiles = {}
        self.completed_profiles = deque(maxlen=500)
        self.recommendations = {}
        
        # Monitoring configuration
        self.thresholds = PerformanceThresholds()
        self.monitoring_enabled = True
        self.sampling_interval = 5.0  # seconds
        self.profiling_enabled = True
        
        # System monitoring
        self.process = psutil.Process()
        self.system_baseline = self._establish_baseline()
        
        # Memory tracking
        self.memory_tracker_enabled = True
        if self.memory_tracker_enabled:
            tracemalloc.start()
        
        # Background monitoring task
        self.monitoring_task = None
        
        # Performance optimization cache
        self.optimization_cache = {}
        self.function_timings = defaultdict(list)
        
        logger.info("Performance monitoring system initialized")
    
    def _establish_baseline(self) -> Dict[str, float]:
        """Establish performance baseline"""
        return {
            "cpu_baseline": psutil.cpu_percent(interval=1),
            "memory_baseline": self.process.memory_info().rss / 1024 / 1024,  # MB
            "thread_baseline": threading.active_count(),
            "startup_time": time.time()
        }
    
    async def start_monitoring(self):
        """Start background performance monitoring"""
        if self.monitoring_task is None:
            self.monitoring_task = asyncio.create_task(self._monitoring_loop())
            logger.info("Performance monitoring started")
    
    async def stop_monitoring(self):
        """Stop background performance monitoring"""
        if self.monitoring_task:
            self.monitoring_task.cancel()
            try:
                await self.monitoring_task
            except asyncio.CancelledError:
                pass
            self.monitoring_task = None
            logger.info("Performance monitoring stopped")
    
    async def _monitoring_loop(self):
        """Main monitoring loop"""
        while self.monitoring_enabled:
            try:
                await self._collect_system_metrics()
                await self._check_thresholds()
                await self._generate_recommendations()
                await asyncio.sleep(self.sampling_interval)
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in monitoring loop: {e}")
                await asyncio.sleep(self.sampling_interval)
    
    async def _collect_system_metrics(self):
        """Collect system performance metrics"""
        try:
            timestamp = datetime.now()
            
            # CPU metrics
            cpu_percent = psutil.cpu_percent(interval=None)
            await self._record_metric(
                MetricType.CPU_USAGE,
                cpu_percent,
                "percent",
                timestamp,
                {"cores": psutil.cpu_count()}
            )
            
            # Memory metrics
            memory_info = self.process.memory_info()
            memory_percent = self.process.memory_percent()
            await self._record_metric(
                MetricType.MEMORY_USAGE,
                memory_percent,
                "percent",
                timestamp,
                {
                    "rss_mb": memory_info.rss / 1024 / 1024,
                    "vms_mb": memory_info.vms / 1024 / 1024
                }
            )
            
            # Thread count
            thread_count = threading.active_count()
            await self._record_metric(
                MetricType.THREAD_COUNT,
                thread_count,
                "count",
                timestamp,
                {"baseline": self.system_baseline["thread_baseline"]}
            )
            
            # Disk I/O
            disk_io = psutil.disk_io_counters()
            if disk_io:
                await self._record_metric(
                    MetricType.DISK_IO,
                    disk_io.read_bytes + disk_io.write_bytes,
                    "bytes",
                    timestamp,
                    {
                        "read_bytes": disk_io.read_bytes,
                        "write_bytes": disk_io.write_bytes,
                        "read_count": disk_io.read_count,
                        "write_count": disk_io.write_count
                    }
                )
            
            # Network I/O
            net_io = psutil.net_io_counters()
            if net_io:
                await self._record_metric(
                    MetricType.NETWORK_IO,
                    net_io.bytes_sent + net_io.bytes_recv,
                    "bytes",
                    timestamp,
                    {
                        "bytes_sent": net_io.bytes_sent,
                        "bytes_recv": net_io.bytes_recv,
                        "packets_sent": net_io.packets_sent,
                        "packets_recv": net_io.packets_recv
                    }
                )
        
        except Exception as e:
            logger.error(f"Failed to collect system metrics: {e}")
    
    async def _record_metric(self, metric_type: MetricType, value: float, 
                           unit: str, timestamp: datetime, context: Dict[str, Any]):
        """Record a performance metric"""
        # Check threshold
        alert_level = self.thresholds.get_alert_level(metric_type, value)
        threshold_exceeded = alert_level is not None
        
        # Create metric
        metric = PerformanceMetric(
            metric_id=f"{metric_type.value}_{int(timestamp.timestamp() * 1000)}",
            metric_type=metric_type,
            value=value,
            unit=unit,
            timestamp=timestamp,
            context=context,
            threshold_exceeded=threshold_exceeded,
            alert_level=alert_level
        )
        
        # Store metric
        self.metrics_history[metric_type].append(metric)
        
        # Emit alert if threshold exceeded
        if threshold_exceeded:
            await self._emit_performance_alert(metric)
    
    async def _emit_performance_alert(self, metric: PerformanceMetric):
        """Emit performance alert"""
        await self.event_manager.emit(
            EventTypes.PERFORMANCE_ALERT,
            {
                "metric_type": metric.metric_type.value,
                "value": metric.value,
                "unit": metric.unit,
                "alert_level": metric.alert_level.value,
                "context": metric.context
            },
            "performance_monitor"
        )
    
    async def _check_thresholds(self):
        """Check for performance threshold violations"""
        # Check for sustained high CPU usage
        cpu_metrics = list(self.metrics_history[MetricType.CPU_USAGE])
        if len(cpu_metrics) >= 5:
            recent_cpu = [m.value for m in cpu_metrics[-5:]]
            avg_cpu = statistics.mean(recent_cpu)
            
            if avg_cpu > 80:
                await self._create_recommendation(
                    "cpu_optimization",
                    "high",
                    "High CPU Usage Detected",
                    f"Average CPU usage over last 5 samples: {avg_cpu:.1f}%",
                    "Sustained high CPU usage affecting performance",
                    "Application responsiveness may be degraded",
                    "medium",
                    None,
                    "Consider implementing async operations and reducing CPU-intensive tasks",
                    "20-30% CPU usage reduction"
                )
        
        # Check for memory growth
        memory_metrics = list(self.metrics_history[MetricType.MEMORY_USAGE])
        if len(memory_metrics) >= 10:
            memory_values = [m.value for m in memory_metrics[-10:]]
            if len(set(memory_values)) > 1:  # Memory is changing
                slope = self._calculate_trend_slope(memory_values)
                if slope > 1.0:  # Memory increasing by >1% per sample
                    await self._create_recommendation(
                        "memory_leak",
                        "critical",
                        "Potential Memory Leak Detected",
                        f"Memory usage increasing at {slope:.2f}% per sample",
                        "Memory usage shows upward trend indicating potential leak",
                        "Application may crash due to out of memory",
                        "high",
                        None,
                        "Review object lifecycle and implement proper cleanup",
                        "Stabilize memory usage"
                    )
    
    def _calculate_trend_slope(self, values: List[float]) -> float:
        """Calculate trend slope for time series data"""
        n = len(values)
        if n < 2:
            return 0.0
        
        x_sum = sum(range(n))
        y_sum = sum(values)
        xy_sum = sum(i * values[i] for i in range(n))
        x2_sum = sum(i * i for i in range(n))
        
        slope = (n * xy_sum - x_sum * y_sum) / (n * x2_sum - x_sum * x_sum)
        return slope
    
    async def _create_recommendation(self, category: str, priority: str, title: str,
                                   description: str, issue_detected: str, potential_impact: str,
                                   implementation_effort: str, code_location: Optional[str],
                                   suggested_fix: str, estimated_improvement: str):
        """Create optimization recommendation"""
        recommendation_id = f"{category}_{int(time.time() * 1000)}"
        
        recommendation = OptimizationRecommendation(
            recommendation_id=recommendation_id,
            category=category,
            priority=priority,
            title=title,
            description=description,
            issue_detected=issue_detected,
            potential_impact=potential_impact,
            implementation_effort=implementation_effort,
            code_location=code_location,
            suggested_fix=suggested_fix,
            estimated_improvement=estimated_improvement
        )
        
        self.recommendations[recommendation_id] = recommendation
        
        # Emit recommendation event
        await self.event_manager.emit(
            EventTypes.OPTIMIZATION_RECOMMENDATION,
            {
                "recommendation_id": recommendation_id,
                "category": category,
                "priority": priority,
                "title": title
            },
            "performance_monitor"
        )
    
    async def _generate_recommendations(self):
        """Generate performance optimization recommendations"""
        # Analyze function timing patterns
        await self._analyze_function_timings()
        
        # Check for optimization opportunities
        await self._check_optimization_opportunities()
    
    async def _analyze_function_timings(self):
        """Analyze function execution timings for optimization opportunities"""
        for func_name, timings in self.function_timings.items():
            if len(timings) >= 10:
                avg_time = statistics.mean(timings)
                max_time = max(timings)
                std_dev = statistics.stdev(timings) if len(timings) > 1 else 0
                
                # Check for slow functions
                if avg_time > 1000:  # >1 second average
                    await self._create_recommendation(
                        "slow_function",
                        "medium",
                        f"Slow Function: {func_name}",
                        f"Average execution time: {avg_time:.0f}ms",
                        f"Function {func_name} executing slowly",
                        "User experience degradation",
                        "medium",
                        func_name,
                        "Optimize algorithm or implement caching",
                        f"Reduce execution time by 50-70%"
                    )
                
                # Check for inconsistent performance
                if std_dev > avg_time * 0.5:  # High variance
                    await self._create_recommendation(
                        "inconsistent_performance",
                        "low",
                        f"Inconsistent Performance: {func_name}",
                        f"High performance variance (σ={std_dev:.0f}ms)",
                        f"Function {func_name} has inconsistent execution times",
                        "Unpredictable user experience",
                        "low",
                        func_name,
                        "Investigate performance bottlenecks and optimize resource usage",
                        "Reduce performance variance by 60%"
                    )
    
    async def _check_optimization_opportunities(self):
        """Check for general optimization opportunities"""
        # Check thread pool efficiency
        thread_metrics = list(self.metrics_history[MetricType.THREAD_COUNT])
        if thread_metrics and len(thread_metrics) >= 5:
            recent_threads = [m.value for m in thread_metrics[-5:]]
            avg_threads = statistics.mean(recent_threads)
            baseline = self.system_baseline["thread_baseline"]
            
            if avg_threads > baseline * 3:  # More than 3x baseline
                await self._create_recommendation(
                    "thread_optimization",
                    "medium",
                    "Excessive Thread Usage",
                    f"Average threads: {avg_threads:.0f} (baseline: {baseline})",
                    "High thread count detected",
                    "Resource overhead and context switching",
                    "medium",
                    None,
                    "Implement thread pooling or async patterns",
                    "Reduce thread count by 40-60%"
                )
    
    def start_profile(self, operation_name: str, context: Dict[str, Any] = None) -> str:
        """Start performance profiling for an operation"""
        profile_id = f"profile_{int(time.time() * 1000000)}"
        
        profile = PerformanceProfile(
            profile_id=profile_id,
            operation_name=operation_name,
            start_time=datetime.now(),
            end_time=None,
            duration_ms=None,
            cpu_usage=psutil.cpu_percent(interval=None),
            memory_usage=self.process.memory_percent(),
            io_operations=None,
            thread_count=threading.active_count(),
            custom_metrics=context or {}
        )
        
        self.active_profiles[profile_id] = profile
        return profile_id
    
    def end_profile(self, profile_id: str) -> Optional[PerformanceProfile]:
        """End performance profiling for an operation"""
        if profile_id not in self.active_profiles:
            return None
        
        profile = self.active_profiles.pop(profile_id)
        profile.end_time = datetime.now()
        profile.duration_ms = (profile.end_time - profile.start_time).total_seconds() * 1000
        
        # Record timing for analysis
        self.function_timings[profile.operation_name].append(profile.duration_ms)
        
        # Keep only recent timings
        if len(self.function_timings[profile.operation_name]) > 100:
            self.function_timings[profile.operation_name] = \
                self.function_timings[profile.operation_name][-50:]
        
        self.completed_profiles.append(profile)
        
        # Check for performance issues
        asyncio.create_task(self._analyze_profile(profile))
        
        return profile
    
    async def _analyze_profile(self, profile: PerformanceProfile):
        """Analyze completed profile for issues"""
        # Check for slow operations
        if profile.duration_ms and profile.duration_ms > 5000:  # >5 seconds
            await self._create_recommendation(
                "slow_operation",
                "high",
                f"Slow Operation: {profile.operation_name}",
                f"Operation took {profile.duration_ms:.0f}ms to complete",
                f"Operation {profile.operation_name} executed slowly",
                "Poor user experience and potential timeouts",
                "medium",
                profile.operation_name,
                "Optimize operation logic or implement progress indicators",
                "Reduce execution time by 50-80%"
            )
    
    def profile_function(self, func_name: Optional[str] = None):
        """Decorator for automatic function profiling"""
        def decorator(func):
            name = func_name or f"{func.__module__}.{func.__name__}"
            
            if asyncio.iscoroutinefunction(func):
                async def async_wrapper(*args, **kwargs):
                    profile_id = self.start_profile(name)
                    try:
                        result = await func(*args, **kwargs)
                        return result
                    finally:
                        self.end_profile(profile_id)
                return async_wrapper
            else:
                def sync_wrapper(*args, **kwargs):
                    profile_id = self.start_profile(name)
                    try:
                        result = func(*args, **kwargs)
                        return result
                    finally:
                        self.end_profile(profile_id)
                return sync_wrapper
        
        return decorator
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """Get performance summary and statistics"""
        summary = {
            "monitoring_status": "active" if self.monitoring_enabled else "inactive",
            "metrics_collected": sum(len(metrics) for metrics in self.metrics_history.values()),
            "active_profiles": len(self.active_profiles),
            "completed_profiles": len(self.completed_profiles),
            "recommendations": len(self.recommendations),
            "system_baseline": self.system_baseline
        }
        
        # Current metrics
        current_metrics = {}
        for metric_type, metrics in self.metrics_history.items():
            if metrics:
                latest = metrics[-1]
                current_metrics[metric_type.value] = {
                    "value": latest.value,
                    "unit": latest.unit,
                    "timestamp": latest.timestamp.isoformat(),
                    "threshold_exceeded": latest.threshold_exceeded
                }
        
        summary["current_metrics"] = current_metrics
        
        # Performance trends
        trends = {}
        for metric_type, metrics in self.metrics_history.items():
            if len(metrics) >= 5:
                recent_values = [m.value for m in list(metrics)[-10:]]
                trends[metric_type.value] = {
                    "average": statistics.mean(recent_values),
                    "min": min(recent_values),
                    "max": max(recent_values),
                    "trend": "increasing" if self._calculate_trend_slope(recent_values) > 0 else "decreasing"
                }
        
        summary["trends"] = trends
        
        return summary
    
    def get_recommendations(self, category: Optional[str] = None, 
                          priority: Optional[str] = None) -> List[Dict[str, Any]]:
        """Get optimization recommendations"""
        recommendations = []
        
        for rec in self.recommendations.values():
            if category and rec.category != category:
                continue
            if priority and rec.priority != priority:
                continue
            
            recommendations.append(asdict(rec))
        
        # Sort by priority
        priority_order = {"critical": 0, "high": 1, "medium": 2, "low": 3}
        recommendations.sort(key=lambda x: priority_order.get(x["priority"], 999))
        
        return recommendations
    
    def get_function_performance(self, function_name: Optional[str] = None) -> Dict[str, Any]:
        """Get function performance statistics"""
        if function_name:
            timings = self.function_timings.get(function_name, [])
            if not timings:
                return {"error": "No timing data available"}
            
            return {
                "function": function_name,
                "call_count": len(timings),
                "average_ms": statistics.mean(timings),
                "min_ms": min(timings),
                "max_ms": max(timings),
                "std_dev_ms": statistics.stdev(timings) if len(timings) > 1 else 0,
                "percentile_95_ms": self._percentile(timings, 95)
            }
        else:
            # Return all function performance data
            performance_data = {}
            for func_name, timings in self.function_timings.items():
                if timings:
                    performance_data[func_name] = {
                        "call_count": len(timings),
                        "average_ms": statistics.mean(timings),
                        "max_ms": max(timings),
                        "percentile_95_ms": self._percentile(timings, 95)
                    }
            
            return performance_data
    
    def _percentile(self, data: List[float], percentile: float) -> float:
        """Calculate percentile of data"""
        if not data:
            return 0.0
        
        sorted_data = sorted(data)
        index = (percentile / 100) * (len(sorted_data) - 1)
        
        if index == int(index):
            return sorted_data[int(index)]
        else:
            lower_index = int(index)
            upper_index = lower_index + 1
            weight = index - lower_index
            return sorted_data[lower_index] * (1 - weight) + sorted_data[upper_index] * weight
    
    def clear_recommendations(self, category: Optional[str] = None):
        """Clear optimization recommendations"""
        if category:
            self.recommendations = {
                k: v for k, v in self.recommendations.items() 
                if v.category != category
            }
        else:
            self.recommendations.clear()
    
    def export_performance_data(self, file_path: str):
        """Export performance data to file"""
        export_data = {
            "summary": self.get_performance_summary(),
            "recommendations": self.get_recommendations(),
            "function_performance": self.get_function_performance(),
            "export_timestamp": datetime.now().isoformat()
        }
        
        with open(file_path, 'w') as f:
            json.dump(export_data, f, indent=2, default=str)
    
    def optimize_memory(self):
        """Trigger memory optimization"""
        # Force garbage collection
        collected = gc.collect()
        
        # Clear function timing cache
        for func_name in list(self.function_timings.keys()):
            if len(self.function_timings[func_name]) > 50:
                self.function_timings[func_name] = self.function_timings[func_name][-25:]
        
        # Clear old metrics
        for metric_type in self.metrics_history:
            if len(self.metrics_history[metric_type]) > 500:
                # Keep only recent 250 metrics
                old_deque = self.metrics_history[metric_type]
                new_deque = deque(list(old_deque)[-250:], maxlen=1000)
                self.metrics_history[metric_type] = new_deque
        
        logger.info(f"Memory optimization completed. Collected {collected} objects")
        return collected