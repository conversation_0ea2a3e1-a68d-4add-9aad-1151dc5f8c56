#!/usr/bin/env python3
"""
Native Tool Manager
Direct OS execution for Windows and Linux environments
"""

import asyncio
import subprocess
import logging
import shlex
import time
import os
import signal
from typing import Dict, Any, Optional, List, Tuple
from pathlib import Path

from .environment_detector import environment_detector, ExecutionEnvironment
from .hybrid_execution_engine import ToolExecutionResult, ExecutionResult

logger = logging.getLogger(__name__)

class NativeToolManager:
    """Native tool execution manager for direct OS execution"""
    
    def __init__(self):
        self.environment = environment_detector.environment
        self.capabilities = environment_detector.capabilities
        self.is_windows = self.capabilities.get('native_windows', False)
        self.is_linux = self.capabilities.get('native_linux', False)
        
        # Tool availability cache
        self.tool_cache = {}
        
        logger.info(f"🐧 Native Tool Manager initialized")
        logger.info(f"🏗️ Windows support: {self.is_windows}")
        logger.info(f"🐧 Linux support: {self.is_linux}")
    
    async def initialize(self):
        """Initialize native tool manager"""
        logger.info("🔍 Checking native tool availability...")
        
        # Common security tools to check
        tools_to_check = [
            'nmap', 'nuclei', 'sqlmap', 'gobuster', 'hashcat', 'john',
            'nikto', 'dirb', 'wpscan', 'ffuf', 'feroxbuster', 'whatweb',
            'enum4linux', 'smbclient', 'testssl.sh', 'sslyze',
            'msfconsole', 'searchsploit'
        ]
        
        for tool in tools_to_check:
            available = await self._check_tool_availability(tool)
            self.tool_cache[tool] = available
            if available:
                logger.info(f"✅ {tool} is available")
            else:
                logger.debug(f"❌ {tool} not found")
        
        available_count = sum(1 for available in self.tool_cache.values() if available)
        logger.info(f"📊 {available_count}/{len(tools_to_check)} native tools available")
    
    async def _check_tool_availability(self, tool_name: str) -> bool:
        """Check if a tool is available in the system"""
        try:
            # Check common executable extensions on Windows
            if self.is_windows:
                executables = [tool_name, f"{tool_name}.exe", f"{tool_name}.cmd", f"{tool_name}.bat"]
            else:
                executables = [tool_name]
            
            for executable in executables:
                try:
                    # Try 'which' on Unix-like systems, 'where' on Windows
                    if self.is_windows:
                        result = await asyncio.create_subprocess_exec(
                            "where", executable,
                            stdout=asyncio.subprocess.PIPE,
                            stderr=asyncio.subprocess.PIPE
                        )
                    else:
                        result = await asyncio.create_subprocess_exec(
                            "which", executable,
                            stdout=asyncio.subprocess.PIPE,
                            stderr=asyncio.subprocess.PIPE
                        )
                    
                    await result.wait()
                    if result.returncode == 0:
                        return True
                        
                except Exception:
                    continue
            
            return False
            
        except Exception as e:
            logger.debug(f"Tool availability check failed for {tool_name}: {e}")
            return False
    
    async def execute_command(
        self,
        command: str,
        target: str,
        options: Dict[str, Any],
        timeout: int = 300
    ) -> ToolExecutionResult:
        """Execute a command natively on the OS"""
        
        start_time = time.time()
        
        try:
            # Build full command with arguments
            full_command = self._build_command(command, target, options)
            
            logger.info(f"🚀 Executing native command: {full_command[:100]}...")
            
            # Execute command
            if self.is_windows:
                result = await self._execute_windows_command(full_command, timeout)
            else:
                result = await self._execute_linux_command(full_command, timeout)
            
            execution_time = time.time() - start_time
            
            if result['returncode'] == 0:
                return ToolExecutionResult(
                    status=ExecutionResult.SUCCESS,
                    method="native",
                    output=result['stdout'],
                    error=result['stderr'],
                    exit_code=result['returncode'],
                    execution_time=execution_time,
                    metadata={
                        'command': full_command,
                        'environment': self.environment.value
                    }
                )
            else:
                return ToolExecutionResult(
                    status=ExecutionResult.FAILED,
                    method="native",
                    output=result['stdout'],
                    error=result['stderr'],
                    exit_code=result['returncode'],
                    execution_time=execution_time,
                    metadata={
                        'command': full_command,
                        'environment': self.environment.value
                    }
                )
                
        except asyncio.TimeoutError:
            return ToolExecutionResult(
                status=ExecutionResult.TIMEOUT,
                method="native",
                error=f"Command timed out after {timeout} seconds",
                execution_time=time.time() - start_time
            )
        except Exception as e:
            return ToolExecutionResult(
                status=ExecutionResult.FAILED,
                method="native",
                error=f"Native execution failed: {str(e)}",
                execution_time=time.time() - start_time
            )
    
    def _build_command(self, command: str, target: str, options: Dict[str, Any]) -> str:
        """Build complete command with arguments"""
        
        # Start with base command
        cmd_parts = [command]
        
        # Add target
        if target:
            cmd_parts.append(target)
        
        # Add options based on tool
        tool_name = command.split()[0] if ' ' in command else command
        
        if tool_name == 'nmap':
            cmd_parts.extend(self._build_nmap_args(options))
        elif tool_name == 'nuclei':
            cmd_parts.extend(self._build_nuclei_args(options))
        elif tool_name == 'sqlmap':
            cmd_parts.extend(self._build_sqlmap_args(options))
        elif tool_name == 'gobuster':
            cmd_parts.extend(self._build_gobuster_args(options))
        elif tool_name == 'nikto':
            cmd_parts.extend(self._build_nikto_args(options))
        elif tool_name == 'hashcat':
            cmd_parts.extend(self._build_hashcat_args(options))
        elif tool_name == 'john':
            cmd_parts.extend(self._build_john_args(options))
        else:
            # Generic options handling
            cmd_parts.extend(self._build_generic_args(options))
        
        return ' '.join(cmd_parts)
    
    def _build_nmap_args(self, options: Dict[str, Any]) -> List[str]:
        """Build Nmap-specific arguments"""
        args = []
        
        # Scan type
        scan_type = options.get('scan_type', 'tcp')
        if scan_type == 'syn':
            args.append('-sS')
        elif scan_type == 'udp':
            args.append('-sU')
        elif scan_type == 'tcp':
            args.append('-sT')
        
        # Port specification
        ports = options.get('ports')
        if ports:
            args.extend(['-p', str(ports)])
        
        # Timing
        timing = options.get('timing', '4')
        args.extend(['-T', str(timing)])
        
        # Version detection
        if options.get('version_detection'):
            args.append('-sV')
        
        # OS detection
        if options.get('os_detection'):
            args.append('-O')
        
        # Scripts
        scripts = options.get('scripts')
        if scripts:
            args.extend(['--script', scripts])
        
        # Output format
        output_format = options.get('output_format', 'normal')
        if output_format == 'xml':
            args.append('-oX')
        elif output_format == 'grep':
            args.append('-oG')
        
        return args
    
    def _build_nuclei_args(self, options: Dict[str, Any]) -> List[str]:
        """Build Nuclei-specific arguments"""
        args = []
        
        # Templates
        templates = options.get('templates')
        if templates:
            args.extend(['-t', templates])
        
        # Severity
        severity = options.get('severity')
        if severity:
            args.extend(['-severity', severity])
        
        # Concurrency
        concurrency = options.get('concurrency', '25')
        args.extend(['-c', str(concurrency)])
        
        # Output format
        if options.get('json_output'):
            args.append('-json')
        
        # Silent mode
        if options.get('silent'):
            args.append('-silent')
        
        return args
    
    def _build_sqlmap_args(self, options: Dict[str, Any]) -> List[str]:
        """Build SQLMap-specific arguments"""
        args = []
        
        # URL or request file
        url = options.get('url')
        if url:
            args.extend(['-u', url])
        
        # Database type
        dbms = options.get('dbms')
        if dbms:
            args.extend(['--dbms', dbms])
        
        # Techniques
        technique = options.get('technique')
        if technique:
            args.extend(['--technique', technique])
        
        # Level and risk
        level = options.get('level', '1')
        args.extend(['--level', str(level)])
        
        risk = options.get('risk', '1')
        args.extend(['--risk', str(risk)])
        
        # Batch mode
        if options.get('batch'):
            args.append('--batch')
        
        return args
    
    def _build_gobuster_args(self, options: Dict[str, Any]) -> List[str]:
        """Build Gobuster-specific arguments"""
        args = []
        
        # Mode
        mode = options.get('mode', 'dir')
        args.append(mode)
        
        # Wordlist
        wordlist = options.get('wordlist', '/usr/share/wordlists/dirb/common.txt')
        args.extend(['-w', wordlist])
        
        # URL
        url = options.get('url')
        if url:
            args.extend(['-u', url])
        
        # Threads
        threads = options.get('threads', '10')
        args.extend(['-t', str(threads)])
        
        # Extensions
        extensions = options.get('extensions')
        if extensions:
            args.extend(['-x', extensions])
        
        return args
    
    def _build_nikto_args(self, options: Dict[str, Any]) -> List[str]:
        """Build Nikto-specific arguments"""
        args = []
        
        # Host
        host = options.get('host')
        if host:
            args.extend(['-h', host])
        
        # Port
        port = options.get('port')
        if port:
            args.extend(['-p', str(port)])
        
        # SSL
        if options.get('ssl'):
            args.append('-ssl')
        
        # Format
        format_type = options.get('format', 'txt')
        args.extend(['-Format', format_type])
        
        return args
    
    def _build_hashcat_args(self, options: Dict[str, Any]) -> List[str]:
        """Build Hashcat-specific arguments"""
        args = []
        
        # Attack mode
        attack_mode = options.get('attack_mode', '0')
        args.extend(['-a', str(attack_mode)])
        
        # Hash mode
        hash_mode = options.get('hash_mode')
        if hash_mode:
            args.extend(['-m', str(hash_mode)])
        
        # Wordlist
        wordlist = options.get('wordlist')
        if wordlist:
            args.append(wordlist)
        
        return args
    
    def _build_john_args(self, options: Dict[str, Any]) -> List[str]:
        """Build John the Ripper-specific arguments"""
        args = []
        
        # Wordlist
        wordlist = options.get('wordlist')
        if wordlist:
            args.extend(['--wordlist', wordlist])
        
        # Format
        format_type = options.get('format')
        if format_type:
            args.extend(['--format', format_type])
        
        # Rules
        rules = options.get('rules')
        if rules:
            args.extend(['--rules', rules])
        
        return args
    
    def _build_generic_args(self, options: Dict[str, Any]) -> List[str]:
        """Build generic arguments for unknown tools"""
        args = []
        
        # Add any additional arguments
        extra_args = options.get('args', [])
        if isinstance(extra_args, list):
            args.extend(extra_args)
        elif isinstance(extra_args, str):
            args.extend(shlex.split(extra_args))
        
        return args
    
    async def _execute_linux_command(self, command: str, timeout: int) -> Dict[str, Any]:
        """Execute command on Linux/Unix systems"""
        
        try:
            # Use shell=True for complex commands
            process = await asyncio.create_subprocess_shell(
                command,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                preexec_fn=os.setsid  # Create new process group
            )
            
            try:
                stdout, stderr = await asyncio.wait_for(
                    process.communicate(),
                    timeout=timeout
                )
                
                return {
                    'returncode': process.returncode,
                    'stdout': stdout.decode('utf-8', errors='replace'),
                    'stderr': stderr.decode('utf-8', errors='replace')
                }
                
            except asyncio.TimeoutError:
                # Kill the entire process group
                os.killpg(os.getpgid(process.pid), signal.SIGTERM)
                try:
                    await asyncio.wait_for(process.wait(), timeout=5)
                except asyncio.TimeoutError:
                    os.killpg(os.getpgid(process.pid), signal.SIGKILL)
                raise
                
        except Exception as e:
            logger.error(f"Linux command execution failed: {e}")
            raise
    
    async def _execute_windows_command(self, command: str, timeout: int) -> Dict[str, Any]:
        """Execute command on Windows systems"""
        
        try:
            # Use cmd.exe for Windows
            process = await asyncio.create_subprocess_shell(
                command,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                shell=True
            )
            
            try:
                stdout, stderr = await asyncio.wait_for(
                    process.communicate(),
                    timeout=timeout
                )
                
                return {
                    'returncode': process.returncode,
                    'stdout': stdout.decode('utf-8', errors='replace'),
                    'stderr': stderr.decode('utf-8', errors='replace')
                }
                
            except asyncio.TimeoutError:
                process.terminate()
                try:
                    await asyncio.wait_for(process.wait(), timeout=5)
                except asyncio.TimeoutError:
                    process.kill()
                raise
                
        except Exception as e:
            logger.error(f"Windows command execution failed: {e}")
            raise
    
    async def execute_shell_command(self, command: str, timeout: int = 300) -> ToolExecutionResult:
        """Execute a raw shell command"""
        
        start_time = time.time()
        
        try:
            logger.info(f"🔧 Executing shell command: {command[:100]}...")
            
            if self.is_windows:
                result = await self._execute_windows_command(command, timeout)
            else:
                result = await self._execute_linux_command(command, timeout)
            
            execution_time = time.time() - start_time
            
            return ToolExecutionResult(
                status=ExecutionResult.SUCCESS if result['returncode'] == 0 else ExecutionResult.FAILED,
                method="native_shell",
                output=result['stdout'],
                error=result['stderr'],
                exit_code=result['returncode'],
                execution_time=execution_time,
                metadata={'command': command}
            )
            
        except asyncio.TimeoutError:
            return ToolExecutionResult(
                status=ExecutionResult.TIMEOUT,
                method="native_shell",
                error=f"Shell command timed out after {timeout} seconds",
                execution_time=time.time() - start_time
            )
        except Exception as e:
            return ToolExecutionResult(
                status=ExecutionResult.FAILED,
                method="native_shell",
                error=f"Shell command failed: {str(e)}",
                execution_time=time.time() - start_time
            )
    
    def is_tool_available(self, tool_name: str) -> bool:
        """Check if a tool is available"""
        return self.tool_cache.get(tool_name, False)
    
    def get_available_tools(self) -> List[str]:
        """Get list of available tools"""
        return [tool for tool, available in self.tool_cache.items() if available]
    
    def get_tool_cache(self) -> Dict[str, bool]:
        """Get complete tool availability cache"""
        return self.tool_cache.copy()