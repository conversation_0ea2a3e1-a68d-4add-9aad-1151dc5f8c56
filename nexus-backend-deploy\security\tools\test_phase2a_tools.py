#!/usr/bin/env python3
"""
Test script for Phase 2A - External Tools Implementation
Verifies new tools are loaded and calculates completion percentage
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from security.tools.tool_loader import initialize_tools, get_tools_status
from security.tools.tool_registry import tool_registry

def test_phase2a_tools():
    """Test Phase 2A external tools implementation"""
    print("=" * 80)
    print("NexusScan Phase 2A - External Tools Test")
    print("=" * 80)
    
    # Initialize tools
    print("\n1. Loading all tools...")
    loading_report = initialize_tools()
    print(f"   - Total modules loaded: {loading_report['total_loaded']}")
    print(f"   - Available tools: {loading_report['available_tools']}")
    print(f"   - Failed imports: {len(loading_report['failed_imports'])}")
    
    if loading_report['failed_imports']:
        print("\n   Failed imports:")
        for failed in loading_report['failed_imports']:
            print(f"   - {failed['module']}: {failed['error']}")
    
    # Get all registered tools
    print("\n2. Registered Tools:")
    all_tools = tool_registry.list_all_tools()
    
    # Phase 1 tools (already implemented)
    phase1_tools = ["nmap", "nuclei", "sqlmap", "gobuster", "nikto"]
    
    # Phase 2A tools (new external wrappers)
    phase2a_tools = ["wpscan", "ffuf", "feroxbuster", "enum4linux", "sslyze", "metasploit", "searchsploit"]
    
    # Expected total tools from frontend
    expected_total_tools = 50
    
    # Count tools by category
    tools_by_category = {}
    for tool_name, tool_info in all_tools.items():
        category = tool_info.get("metadata", {}).get("category", "unknown")
        if category not in tools_by_category:
            tools_by_category[category] = []
        tools_by_category[category].append(tool_name)
    
    print(f"\n   Total tools registered: {len(all_tools)}")
    print("\n   Tools by category:")
    for category, tools in sorted(tools_by_category.items(), key=lambda x: str(x[0])):
        print(f"   - {category}: {len(tools)} tools")
        for tool in sorted(tools):
            status = "✓" if tool_registry.is_tool_available(tool) else "✗"
            phase = ""
            if tool in phase1_tools:
                phase = " [Phase 1]"
            elif tool in phase2a_tools:
                phase = " [Phase 2A - NEW]"
            print(f"     {status} {tool}{phase}")
    
    # Check Phase 2A tools specifically
    print("\n3. Phase 2A Tool Status:")
    phase2a_available = 0
    for tool in phase2a_tools:
        if tool in all_tools and tool_registry.is_tool_available(tool):
            phase2a_available += 1
            print(f"   ✓ {tool} - Successfully loaded")
        else:
            print(f"   ✗ {tool} - Not available")
    
    # Calculate completion percentage
    print("\n4. Completion Statistics:")
    total_tools = len(all_tools)
    completion_percentage = (total_tools / expected_total_tools) * 100
    
    print(f"   - Phase 1 tools: {len(phase1_tools)}")
    print(f"   - Phase 2A tools added: {phase2a_available}")
    print(f"   - Total tools available: {total_tools}")
    print(f"   - Expected tools: {expected_total_tools}")
    print(f"   - Completion: {completion_percentage:.1f}%")
    
    # Target verification
    print("\n5. Phase 2A Target Status:")
    target_completion = 30  # 30% target
    if completion_percentage >= target_completion:
        print(f"   ✓ TARGET ACHIEVED: {completion_percentage:.1f}% >= {target_completion}%")
    else:
        print(f"   ✗ Target not met: {completion_percentage:.1f}% < {target_completion}%")
    
    # Test a sample tool
    print("\n6. Testing Sample Tool (WPScan):")
    if "wpscan" in all_tools:
        wpscan = tool_registry.get_tool("wpscan")
        if wpscan:
            metadata = wpscan.get_metadata()
            print(f"   - Name: {metadata.name}")
            print(f"   - Display Name: {metadata.display_name}")
            print(f"   - Description: {metadata.description}")
            print(f"   - Category: {metadata.category}")
            print(f"   - Supports Async: {metadata.capabilities.supports_async}")
            print("   ✓ Tool loaded successfully!")
        else:
            print("   ✗ Failed to get tool instance")
    else:
        print("   ✗ WPScan not found in registry")
    
    # Summary
    print("\n" + "=" * 80)
    print("PHASE 2A SUMMARY:")
    print(f"- External tool wrappers implemented: {phase2a_available}/{len(phase2a_tools)}")
    print(f"- Total tools available: {total_tools}")
    print(f"- Overall completion: {completion_percentage:.1f}%")
    if completion_percentage >= 30:
        print("- Status: ✓ PHASE 2A COMPLETE - Ready for frontend development!")
    else:
        print("- Status: ✗ More tools needed to reach 30% target")
    print("=" * 80)

if __name__ == "__main__":
    test_phase2a_tools()