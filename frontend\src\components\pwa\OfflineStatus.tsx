/**
 * Offline Status Component
 * Shows network connectivity status and offline capabilities
 */
import React, { useState, useEffect } from 'react';
import { Badge } from '@/components/ui/badge';
import { Wifi, WifiOff, Cloud, CloudOff } from 'lucide-react';

export const OfflineStatus: React.FC = () => {
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [showOfflineMessage, setShowOfflineMessage] = useState(false);

  useEffect(() => {
    const handleOnline = () => {
      setIsOnline(true);
      setShowOfflineMessage(false);
    };

    const handleOffline = () => {
      setIsOnline(false);
      setShowOfflineMessage(true);
      
      // Hide offline message after 5 seconds
      setTimeout(() => setShowOfflineMessage(false), 5000);
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  if (isOnline && !showOfflineMessage) {
    return null;
  }

  return (
    <div className="fixed bottom-4 left-4 z-50">
      <Badge 
        variant={isOnline ? "default" : "destructive"}
        className="flex items-center gap-2 px-3 py-2 text-sm"
      >
        {isOnline ? (
          <>
            <Wifi className="h-4 w-4" />
            <span>Back Online</span>
          </>
        ) : (
          <>
            <WifiOff className="h-4 w-4" />
            <span>Offline Mode</span>
          </>
        )}
      </Badge>
    </div>
  );
};

export const BackendConnectionStatus: React.FC<{ isConnected: boolean }> = ({ isConnected }) => {
  return (
    <div className="flex items-center gap-2">
      {isConnected ? (
        <>
          <Cloud className="h-4 w-4 text-green-500" />
          <span className="text-sm text-green-600">Backend Connected</span>
        </>
      ) : (
        <>
          <CloudOff className="h-4 w-4 text-red-500" />
          <span className="text-sm text-red-600">Backend Offline</span>
        </>
      )}
    </div>
  );
};

export default OfflineStatus;
