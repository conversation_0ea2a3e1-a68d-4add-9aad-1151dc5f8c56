"""
Security Compliance Checking System for NexusScan Desktop
Comprehensive compliance assessment against multiple frameworks and standards.
"""

import asyncio
import logging
import json
import time
from typing import Dict, List, Optional, Any, Set, Tuple, Union
from enum import Enum
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from pathlib import Path
import re
from collections import defaultdict, Counter

from core.config import Config
from core.database import DatabaseManager
from core.events import EventManager, EventTypes
from ai.ai_service import AIServiceManager

logger = logging.getLogger(__name__)


class ComplianceFramework(Enum):
    """Supported compliance frameworks"""
    NIST_CSF = "nist_csf"
    ISO_27001 = "iso_27001"
    PCI_DSS = "pci_dss"
    HIPAA = "hipaa"
    SOX = "sox"
    GDPR = "gdpr"
    CIS_CONTROLS = "cis_controls"
    OWASP_TOP10 = "owasp_top10"
    NIST_800_53 = "nist_800_53"
    SOC2 = "soc2"
    COBIT = "cobit"
    FISMA = "fisma"


class ComplianceStatus(Enum):
    """Compliance status levels"""
    COMPLIANT = "compliant"
    PARTIALLY_COMPLIANT = "partially_compliant"
    NON_COMPLIANT = "non_compliant"
    NOT_APPLICABLE = "not_applicable"
    UNDER_REVIEW = "under_review"


class ControlCategory(Enum):
    """Security control categories"""
    ACCESS_CONTROL = "access_control"
    AUTHENTICATION = "authentication"
    AUTHORIZATION = "authorization"
    DATA_PROTECTION = "data_protection"
    ENCRYPTION = "encryption"
    NETWORK_SECURITY = "network_security"
    INCIDENT_RESPONSE = "incident_response"
    BUSINESS_CONTINUITY = "business_continuity"
    RISK_MANAGEMENT = "risk_management"
    AUDIT_LOGGING = "audit_logging"
    VULNERABILITY_MANAGEMENT = "vulnerability_management"
    CONFIGURATION_MANAGEMENT = "configuration_management"
    PHYSICAL_SECURITY = "physical_security"
    PERSONNEL_SECURITY = "personnel_security"
    SYSTEM_DEVELOPMENT = "system_development"


class SeverityLevel(Enum):
    """Compliance violation severity"""
    CRITICAL = "critical"
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"
    INFORMATIONAL = "informational"


@dataclass
class ComplianceControl:
    """Individual compliance control"""
    control_id: str
    framework: ComplianceFramework
    title: str
    description: str
    category: ControlCategory
    requirements: List[str]
    testing_procedures: List[str]
    implementation_guidance: List[str]
    related_controls: List[str]
    maturity_levels: Dict[str, str]  # Level -> Description
    automation_level: str  # manual, semi_automated, automated
    frequency: str  # continuous, quarterly, annually
    risk_rating: str  # low, medium, high, critical


@dataclass
class ComplianceEvidence:
    """Evidence for compliance assessment"""
    evidence_id: str
    control_id: str
    evidence_type: str  # document, configuration, log, screenshot
    description: str
    source: str
    collection_date: datetime
    validity_period: Optional[int]  # days
    evidence_data: Dict[str, Any]
    automated: bool
    confidence_score: float  # 0.0-1.0


@dataclass
class ComplianceFinding:
    """Compliance assessment finding"""
    finding_id: str
    control_id: str
    framework: ComplianceFramework
    status: ComplianceStatus
    severity: SeverityLevel
    title: str
    description: str
    impact: str
    likelihood: str
    risk_score: float
    evidence: List[str]  # Evidence IDs
    gaps: List[str]
    recommendations: List[str]
    remediation_timeline: str
    remediation_cost: str
    business_impact: str
    compensating_controls: List[str]
    last_assessed: datetime
    next_assessment: datetime


@dataclass
class ComplianceAssessment:
    """Complete compliance assessment"""
    assessment_id: str
    framework: ComplianceFramework
    scope: str
    assessment_date: datetime
    assessor: str
    target_environment: Dict[str, Any]
    controls_assessed: List[str]
    findings: List[ComplianceFinding]
    overall_status: ComplianceStatus
    compliance_percentage: float
    risk_score: float
    executive_summary: str
    recommendations: List[str]
    next_assessment_date: datetime
    certification_status: Optional[str]


class ComplianceChecker:
    """Comprehensive security compliance checking system"""
    
    def __init__(self, config: Config, db_manager: DatabaseManager,
                 event_manager: EventManager, ai_service: AIServiceManager):
        """Initialize compliance checker"""
        self.config = config
        self.db_manager = db_manager
        self.event_manager = event_manager
        self.ai_service = ai_service
        
        # Compliance state
        self.active_assessments: Dict[str, Dict[str, Any]] = {}
        self.compliance_controls: Dict[str, ComplianceControl] = {}
        self.evidence_repository: Dict[str, ComplianceEvidence] = {}
        self.assessment_history: List[ComplianceAssessment] = []
        
        # Load compliance frameworks
        self._load_compliance_frameworks()
        
        logger.info("Security compliance checker initialized")
    
    def _load_compliance_frameworks(self):
        """Load compliance framework controls and requirements"""
        # NIST Cybersecurity Framework
        self._load_nist_csf_controls()
        
        # PCI DSS
        self._load_pci_dss_controls()
        
        # ISO 27001
        self._load_iso_27001_controls()
        
        # OWASP Top 10
        self._load_owasp_top10_controls()
        
        # CIS Controls
        self._load_cis_controls()
        
        logger.info(f"Loaded {len(self.compliance_controls)} compliance controls")
    
    def _load_nist_csf_controls(self):
        """Load NIST Cybersecurity Framework controls"""
        nist_controls = [
            ComplianceControl(
                control_id="ID.AM-1",
                framework=ComplianceFramework.NIST_CSF,
                title="Asset Management",
                description="Physical devices and systems within the organization are inventoried",
                category=ControlCategory.CONFIGURATION_MANAGEMENT,
                requirements=[
                    "Maintain an inventory of physical devices and systems",
                    "Include device ownership, location, and criticality",
                    "Update inventory when changes occur"
                ],
                testing_procedures=[
                    "Review asset inventory documentation",
                    "Compare inventory to actual deployed systems",
                    "Verify inventory update procedures"
                ],
                implementation_guidance=[
                    "Use automated discovery tools",
                    "Implement change management processes",
                    "Regular inventory validation"
                ],
                related_controls=["ID.AM-2", "ID.AM-3"],
                maturity_levels={
                    "1": "Basic asset tracking",
                    "2": "Comprehensive inventory",
                    "3": "Automated discovery",
                    "4": "Real-time monitoring"
                },
                automation_level="semi_automated",
                frequency="quarterly",
                risk_rating="medium"
            ),
            ComplianceControl(
                control_id="PR.AC-1",
                framework=ComplianceFramework.NIST_CSF,
                title="Access Control Policy",
                description="Identities and credentials are issued, managed, verified, revoked, and audited",
                category=ControlCategory.ACCESS_CONTROL,
                requirements=[
                    "Implement identity and access management policies",
                    "Regular review of user access rights",
                    "Audit access control implementations"
                ],
                testing_procedures=[
                    "Review access control policies",
                    "Test user provisioning/deprovisioning",
                    "Verify access review processes"
                ],
                implementation_guidance=[
                    "Implement role-based access control",
                    "Use automated provisioning systems",
                    "Regular access certifications"
                ],
                related_controls=["PR.AC-2", "PR.AC-3"],
                maturity_levels={
                    "1": "Basic access controls",
                    "2": "Role-based access",
                    "3": "Automated provisioning",
                    "4": "Continuous monitoring"
                },
                automation_level="automated",
                frequency="quarterly",
                risk_rating="high"
            ),
            ComplianceControl(
                control_id="DE.CM-1",
                framework=ComplianceFramework.NIST_CSF,
                title="Security Monitoring",
                description="The network is monitored to detect potential cybersecurity events",
                category=ControlCategory.AUDIT_LOGGING,
                requirements=[
                    "Implement continuous network monitoring",
                    "Deploy security monitoring tools",
                    "Monitor for cybersecurity events"
                ],
                testing_procedures=[
                    "Review monitoring coverage",
                    "Test event detection capabilities",
                    "Verify alerting mechanisms"
                ],
                implementation_guidance=[
                    "Deploy SIEM solutions",
                    "Implement network monitoring tools",
                    "Establish monitoring procedures"
                ],
                related_controls=["DE.CM-2", "DE.AE-1"],
                maturity_levels={
                    "1": "Basic logging",
                    "2": "Centralized monitoring",
                    "3": "Automated analysis",
                    "4": "Predictive analytics"
                },
                automation_level="automated",
                frequency="continuous",
                risk_rating="high"
            )
        ]
        
        for control in nist_controls:
            self.compliance_controls[control.control_id] = control
    
    def _load_pci_dss_controls(self):
        """Load PCI DSS controls"""
        pci_controls = [
            ComplianceControl(
                control_id="PCI-1.1",
                framework=ComplianceFramework.PCI_DSS,
                title="Firewall Configuration Standards",
                description="Establish and implement firewall and router configuration standards",
                category=ControlCategory.NETWORK_SECURITY,
                requirements=[
                    "Document firewall and router configuration standards",
                    "Review firewall configurations at least every six months",
                    "Implement network segmentation"
                ],
                testing_procedures=[
                    "Review firewall configuration documentation",
                    "Test firewall rule effectiveness",
                    "Verify network segmentation"
                ],
                implementation_guidance=[
                    "Use standardized firewall configurations",
                    "Implement change management for firewall rules",
                    "Regular firewall audits"
                ],
                related_controls=["PCI-1.2", "PCI-1.3"],
                maturity_levels={
                    "1": "Basic firewall implementation",
                    "2": "Documented standards",
                    "3": "Automated management",
                    "4": "Continuous monitoring"
                },
                automation_level="semi_automated",
                frequency="quarterly",
                risk_rating="high"
            ),
            ComplianceControl(
                control_id="PCI-3.4",
                framework=ComplianceFramework.PCI_DSS,
                title="Primary Account Number Protection",
                description="Render PAN unreadable anywhere it is stored",
                category=ControlCategory.DATA_PROTECTION,
                requirements=[
                    "Encrypt or hash PANs when stored",
                    "Use strong cryptographic keys",
                    "Protect cryptographic keys"
                ],
                testing_procedures=[
                    "Review PAN storage locations",
                    "Test encryption implementations",
                    "Verify key management procedures"
                ],
                implementation_guidance=[
                    "Use AES encryption for PAN storage",
                    "Implement secure key management",
                    "Regular cryptographic assessments"
                ],
                related_controls=["PCI-3.1", "PCI-3.2"],
                maturity_levels={
                    "1": "Basic encryption",
                    "2": "Strong encryption",
                    "3": "Advanced key management",
                    "4": "Hardware security modules"
                },
                automation_level="automated",
                frequency="quarterly",
                risk_rating="critical"
            )
        ]
        
        for control in pci_controls:
            self.compliance_controls[control.control_id] = control
    
    def _load_iso_27001_controls(self):
        """Load ISO 27001 controls"""
        iso_controls = [
            ComplianceControl(
                control_id="A.9.1.1",
                framework=ComplianceFramework.ISO_27001,
                title="Access Control Policy",
                description="An access control policy shall be established, documented and reviewed",
                category=ControlCategory.ACCESS_CONTROL,
                requirements=[
                    "Establish access control policy",
                    "Document access control procedures",
                    "Regular policy reviews"
                ],
                testing_procedures=[
                    "Review access control policy documentation",
                    "Verify policy implementation",
                    "Check policy review records"
                ],
                implementation_guidance=[
                    "Align with business requirements",
                    "Include risk-based approach",
                    "Regular policy updates"
                ],
                related_controls=["A.9.1.2", "A.9.2.1"],
                maturity_levels={
                    "1": "Basic policy documentation",
                    "2": "Comprehensive policy",
                    "3": "Risk-based policy",
                    "4": "Continuous improvement"
                },
                automation_level="manual",
                frequency="annually",
                risk_rating="medium"
            ),
            ComplianceControl(
                control_id="A.12.6.1",
                framework=ComplianceFramework.ISO_27001,
                title="Management of Technical Vulnerabilities",
                description="Information about technical vulnerabilities shall be obtained in a timely fashion",
                category=ControlCategory.VULNERABILITY_MANAGEMENT,
                requirements=[
                    "Establish vulnerability management process",
                    "Monitor vulnerability sources",
                    "Assess and remediate vulnerabilities"
                ],
                testing_procedures=[
                    "Review vulnerability management procedures",
                    "Test vulnerability scanning coverage",
                    "Verify remediation processes"
                ],
                implementation_guidance=[
                    "Use automated vulnerability scanners",
                    "Establish remediation timelines",
                    "Track vulnerability metrics"
                ],
                related_controls=["A.12.6.2", "A.14.2.1"],
                maturity_levels={
                    "1": "Basic vulnerability scanning",
                    "2": "Regular assessments",
                    "3": "Automated scanning",
                    "4": "Continuous monitoring"
                },
                automation_level="automated",
                frequency="continuous",
                risk_rating="high"
            )
        ]
        
        for control in iso_controls:
            self.compliance_controls[control.control_id] = control
    
    def _load_owasp_top10_controls(self):
        """Load OWASP Top 10 controls"""
        owasp_controls = [
            ComplianceControl(
                control_id="OWASP-A01",
                framework=ComplianceFramework.OWASP_TOP10,
                title="Broken Access Control",
                description="Prevent unauthorized access to resources and functions",
                category=ControlCategory.ACCESS_CONTROL,
                requirements=[
                    "Implement proper access controls",
                    "Deny by default principle",
                    "Log access control failures"
                ],
                testing_procedures=[
                    "Test access control mechanisms",
                    "Verify authorization checks",
                    "Review access control logs"
                ],
                implementation_guidance=[
                    "Use framework security controls",
                    "Implement rate limiting",
                    "Secure API endpoints"
                ],
                related_controls=["OWASP-A02", "OWASP-A05"],
                maturity_levels={
                    "1": "Basic access controls",
                    "2": "Comprehensive authorization",
                    "3": "Advanced access patterns",
                    "4": "Zero-trust architecture"
                },
                automation_level="semi_automated",
                frequency="quarterly",
                risk_rating="high"
            ),
            ComplianceControl(
                control_id="OWASP-A03",
                framework=ComplianceFramework.OWASP_TOP10,
                title="Injection",
                description="Prevent injection attacks through input validation",
                category=ControlCategory.SYSTEM_DEVELOPMENT,
                requirements=[
                    "Validate all user inputs",
                    "Use parameterized queries",
                    "Implement output encoding"
                ],
                testing_procedures=[
                    "Test input validation mechanisms",
                    "Verify SQL injection protection",
                    "Check for injection vulnerabilities"
                ],
                implementation_guidance=[
                    "Use prepared statements",
                    "Implement input sanitization",
                    "Regular security testing"
                ],
                related_controls=["OWASP-A04", "OWASP-A06"],
                maturity_levels={
                    "1": "Basic input validation",
                    "2": "Comprehensive validation",
                    "3": "Advanced protection",
                    "4": "Runtime protection"
                },
                automation_level="automated",
                frequency="continuous",
                risk_rating="critical"
            )
        ]
        
        for control in owasp_controls:
            self.compliance_controls[control.control_id] = control
    
    def _load_cis_controls(self):
        """Load CIS Controls"""
        cis_controls = [
            ComplianceControl(
                control_id="CIS-1",
                framework=ComplianceFramework.CIS_CONTROLS,
                title="Inventory and Control of Hardware Assets",
                description="Actively manage all hardware devices on the network",
                category=ControlCategory.CONFIGURATION_MANAGEMENT,
                requirements=[
                    "Maintain accurate asset inventory",
                    "Deploy network discovery tools",
                    "Use DHCP logging for asset detection"
                ],
                testing_procedures=[
                    "Review asset inventory accuracy",
                    "Test discovery tool coverage",
                    "Verify unauthorized device detection"
                ],
                implementation_guidance=[
                    "Deploy automated discovery tools",
                    "Implement network access control",
                    "Regular inventory audits"
                ],
                related_controls=["CIS-2", "CIS-12"],
                maturity_levels={
                    "1": "Basic inventory",
                    "2": "Automated discovery",
                    "3": "Real-time monitoring",
                    "4": "Continuous compliance"
                },
                automation_level="automated",
                frequency="continuous",
                risk_rating="medium"
            ),
            ComplianceControl(
                control_id="CIS-5",
                framework=ComplianceFramework.CIS_CONTROLS,
                title="Secure Configuration Management",
                description="Establish and maintain secure configurations for all devices",
                category=ControlCategory.CONFIGURATION_MANAGEMENT,
                requirements=[
                    "Develop secure configuration standards",
                    "Deploy configuration management tools",
                    "Monitor for configuration changes"
                ],
                testing_procedures=[
                    "Review configuration standards",
                    "Test configuration management tools",
                    "Verify change detection capabilities"
                ],
                implementation_guidance=[
                    "Use configuration baselines",
                    "Implement automated deployment",
                    "Regular configuration audits"
                ],
                related_controls=["CIS-3", "CIS-11"],
                maturity_levels={
                    "1": "Manual configuration",
                    "2": "Standardized configurations",
                    "3": "Automated deployment",
                    "4": "Continuous monitoring"
                },
                automation_level="automated",
                frequency="continuous",
                risk_rating="high"
            )
        ]
        
        for control in cis_controls:
            self.compliance_controls[control.control_id] = control
    
    async def start_compliance_assessment(self, framework: ComplianceFramework,
                                        assessment_config: Dict[str, Any]) -> str:
        """Start comprehensive compliance assessment"""
        try:
            assessment_id = f"comp_{framework.value}_{int(time.time())}"
            
            # Get framework controls
            framework_controls = [
                control for control in self.compliance_controls.values()
                if control.framework == framework
            ]
            
            if not framework_controls:
                raise ValueError(f"No controls found for framework: {framework.value}")
            
            # Initialize assessment state
            assessment_state = {
                "assessment_id": assessment_id,
                "framework": framework,
                "config": assessment_config,
                "status": "running",
                "start_time": datetime.now(),
                "phase": "initialization",
                "controls_to_assess": [c.control_id for c in framework_controls],
                "controls_assessed": [],
                "findings": [],
                "evidence_collected": [],
                "ai_insights": []
            }
            
            self.active_assessments[assessment_id] = assessment_state
            
            # Emit assessment started event
            await self.event_manager.emit(
                EventTypes.COMPLIANCE_ASSESSMENT_STARTED,
                {
                    "assessment_id": assessment_id,
                    "framework": framework.value,
                    "controls_count": len(framework_controls),
                    "scope": assessment_config.get("scope", "full")
                },
                "compliance_checker"
            )
            
            logger.info(f"Started compliance assessment: {assessment_id}")
            return assessment_id
            
        except Exception as e:
            logger.error(f"Failed to start compliance assessment: {e}")
            raise
    
    async def execute_compliance_assessment(self, assessment_id: str) -> ComplianceAssessment:
        """Execute comprehensive compliance assessment"""
        try:
            if assessment_id not in self.active_assessments:
                raise ValueError(f"Unknown assessment: {assessment_id}")
            
            assessment_state = self.active_assessments[assessment_id]
            framework = assessment_state["framework"]
            
            logger.info(f"Executing compliance assessment: {assessment_id}")
            
            # Phase 1: Evidence Collection
            assessment_state["phase"] = "evidence_collection"
            await self._collect_compliance_evidence(assessment_state)
            
            # Phase 2: Control Assessment
            assessment_state["phase"] = "control_assessment"
            findings = await self._assess_compliance_controls(assessment_state)
            assessment_state["findings"] = findings
            
            # Phase 3: Gap Analysis
            assessment_state["phase"] = "gap_analysis"
            gap_analysis = await self._perform_gap_analysis(assessment_state)
            assessment_state["gap_analysis"] = gap_analysis
            
            # Phase 4: Risk Assessment
            assessment_state["phase"] = "risk_assessment"
            risk_assessment = await self._perform_compliance_risk_assessment(assessment_state)
            assessment_state["risk_assessment"] = risk_assessment
            
            # Phase 5: AI Analysis and Recommendations
            assessment_state["phase"] = "ai_analysis"
            ai_insights = await self._get_ai_compliance_analysis(assessment_state)
            assessment_state["ai_insights"] = ai_insights
            
            # Finalize assessment
            assessment_state["status"] = "completed"
            assessment_state["end_time"] = datetime.now()
            assessment_state["duration"] = (assessment_state["end_time"] - assessment_state["start_time"]).total_seconds()
            
            # Create final assessment report
            compliance_assessment = await self._create_compliance_assessment_report(assessment_state)
            
            # Store in history
            self.assessment_history.append(compliance_assessment)
            
            # Emit assessment completed event
            await self.event_manager.emit(
                EventTypes.COMPLIANCE_ASSESSMENT_COMPLETED,
                {
                    "assessment_id": assessment_id,
                    "framework": framework.value,
                    "overall_status": compliance_assessment.overall_status.value,
                    "compliance_percentage": compliance_assessment.compliance_percentage,
                    "findings_count": len(compliance_assessment.findings),
                    "duration": assessment_state["duration"]
                },
                "compliance_checker"
            )
            
            logger.info(f"Compliance assessment completed: {assessment_id}")
            return compliance_assessment
            
        except Exception as e:
            logger.error(f"Compliance assessment execution failed: {e}")
            if assessment_id in self.active_assessments:
                self.active_assessments[assessment_id]["status"] = "failed"
                self.active_assessments[assessment_id]["error"] = str(e)
            raise
    
    async def _collect_compliance_evidence(self, assessment_state: Dict[str, Any]):
        """Collect evidence for compliance assessment"""
        framework = assessment_state["framework"]
        controls = assessment_state["controls_to_assess"]
        
        evidence_collected = []
        
        for control_id in controls:
            control = self.compliance_controls.get(control_id)
            if not control:
                continue
            
            # Collect automated evidence
            automated_evidence = await self._collect_automated_evidence(control)
            evidence_collected.extend(automated_evidence)
            
            # Collect configuration evidence
            config_evidence = await self._collect_configuration_evidence(control)
            evidence_collected.extend(config_evidence)
            
            # Collect log evidence
            log_evidence = await self._collect_log_evidence(control)
            evidence_collected.extend(log_evidence)
        
        assessment_state["evidence_collected"] = evidence_collected
        logger.info(f"Collected {len(evidence_collected)} pieces of evidence")
    
    async def _collect_automated_evidence(self, control: ComplianceControl) -> List[ComplianceEvidence]:
        """Collect automated evidence for control"""
        evidence = []
        
        # Check if real evidence collection is enabled
        execution_mode = getattr(self, '_execution_mode', 'simulation')
        
        if execution_mode == "real":
            evidence = await self._collect_real_evidence(control)
        else:
            # Simulate automated evidence collection based on control category
            evidence = await self._collect_simulated_evidence(control)
        
        return evidence
    
    async def _collect_simulated_evidence(self, control: ComplianceControl) -> List[ComplianceEvidence]:
        """Collect simulated evidence for control"""
        evidence = []
        
        if control.category == ControlCategory.VULNERABILITY_MANAGEMENT:
            evidence.append(ComplianceEvidence(
                evidence_id=f"auto_{control.control_id}_{int(time.time())}",
                control_id=control.control_id,
                evidence_type="scan_report",
                description="Automated vulnerability scan results",
                source="vulnerability_scanner",
                collection_date=datetime.now(),
                validity_period=30,
                evidence_data={
                    "scan_date": datetime.now().isoformat(),
                    "vulnerabilities_found": 12,
                    "critical_count": 2,
                    "high_count": 5,
                    "medium_count": 5,
                    "scan_coverage": "95%"
                },
                automated=True,
                confidence_score=0.9
            ))
        
        elif control.category == ControlCategory.ACCESS_CONTROL:
            evidence.append(ComplianceEvidence(
                evidence_id=f"auto_{control.control_id}_{int(time.time())}",
                control_id=control.control_id,
                evidence_type="access_review",
                description="Automated access rights review",
                source="identity_management_system",
                collection_date=datetime.now(),
                validity_period=90,
                evidence_data={
                    "total_users": 150,
                    "privileged_users": 25,
                    "inactive_accounts": 8,
                    "orphaned_accounts": 3,
                    "policy_violations": 2
                },
                automated=True,
                confidence_score=0.85
            ))
        
        elif control.category == ControlCategory.NETWORK_SECURITY:
            evidence.append(ComplianceEvidence(
                evidence_id=f"auto_{control.control_id}_{int(time.time())}",
                control_id=control.control_id,
                evidence_type="network_scan",
                description="Network security configuration scan",
                source="network_scanner",
                collection_date=datetime.now(),
                validity_period=60,
                evidence_data={
                    "open_ports": ["22", "80", "443"],
                    "firewall_rules": 45,
                    "encryption_protocols": ["TLS 1.2", "TLS 1.3"],
                    "weak_configurations": 3
                },
                automated=True,
                confidence_score=0.88
            ))
        
        return evidence
    
    async def _collect_configuration_evidence(self, control: ComplianceControl) -> List[ComplianceEvidence]:
        """Collect configuration evidence for control"""
        evidence = []
        
        # Simulate configuration evidence collection
        if control.category == ControlCategory.CONFIGURATION_MANAGEMENT:
            evidence.append(ComplianceEvidence(
                evidence_id=f"config_{control.control_id}_{int(time.time())}",
                control_id=control.control_id,
                evidence_type="configuration",
                description="System configuration baseline",
                source="configuration_management_tool",
                collection_date=datetime.now(),
                validity_period=180,
                evidence_data={
                    "baseline_version": "v2.1",
                    "compliant_systems": 85,
                    "non_compliant_systems": 12,
                    "configuration_drift": "8%"
                },
                automated=True,
                confidence_score=0.92
            ))
        
        return evidence
    
    async def _collect_log_evidence(self, control: ComplianceControl) -> List[ComplianceEvidence]:
        """Collect log evidence for control"""
        evidence = []
        
        # Simulate log evidence collection
        if control.category == ControlCategory.AUDIT_LOGGING:
            evidence.append(ComplianceEvidence(
                evidence_id=f"log_{control.control_id}_{int(time.time())}",
                control_id=control.control_id,
                evidence_type="log_analysis",
                description="Security event log analysis",
                source="siem_system",
                collection_date=datetime.now(),
                validity_period=30,
                evidence_data={
                    "log_sources": 25,
                    "events_analyzed": 1500000,
                    "security_alerts": 45,
                    "false_positives": 12,
                    "incident_count": 3
                },
                automated=True,
                confidence_score=0.87
            ))
        
        return evidence
    
    async def _assess_compliance_controls(self, assessment_state: Dict[str, Any]) -> List[ComplianceFinding]:
        """Assess compliance controls against evidence"""
        findings = []
        controls = assessment_state["controls_to_assess"]
        evidence_list = assessment_state["evidence_collected"]
        
        # Group evidence by control
        control_evidence = defaultdict(list)
        for evidence in evidence_list:
            control_evidence[evidence.control_id].append(evidence)
        
        for control_id in controls:
            control = self.compliance_controls.get(control_id)
            if not control:
                continue
            
            # Assess control based on available evidence
            finding = await self._assess_individual_control(control, control_evidence.get(control_id, []))
            findings.append(finding)
            
            assessment_state["controls_assessed"].append(control_id)
        
        logger.info(f"Assessed {len(findings)} compliance controls")
        return findings
    
    async def _assess_individual_control(self, control: ComplianceControl, 
                                       evidence: List[ComplianceEvidence]) -> ComplianceFinding:
        """Assess individual compliance control"""
        # Determine compliance status based on evidence
        if not evidence:
            status = ComplianceStatus.NOT_APPLICABLE
            severity = SeverityLevel.MEDIUM
            risk_score = 5.0
        else:
            # Analyze evidence to determine compliance
            status, severity, risk_score = self._analyze_control_evidence(control, evidence)
        
        # Generate finding
        finding = ComplianceFinding(
            finding_id=f"finding_{control.control_id}_{int(time.time())}",
            control_id=control.control_id,
            framework=control.framework,
            status=status,
            severity=severity,
            title=f"Assessment of {control.title}",
            description=self._generate_finding_description(control, evidence, status),
            impact=self._assess_business_impact(control, status),
            likelihood=self._assess_likelihood(control, evidence),
            risk_score=risk_score,
            evidence=[e.evidence_id for e in evidence],
            gaps=self._identify_control_gaps(control, evidence),
            recommendations=self._generate_control_recommendations(control, status),
            remediation_timeline=self._estimate_remediation_timeline(control, status),
            remediation_cost=self._estimate_remediation_cost(control, status),
            business_impact=self._assess_business_impact(control, status),
            compensating_controls=self._identify_compensating_controls(control),
            last_assessed=datetime.now(),
            next_assessment=datetime.now() + timedelta(days=self._get_assessment_frequency(control))
        )
        
        return finding
    
    def _analyze_control_evidence(self, control: ComplianceControl, 
                                evidence: List[ComplianceEvidence]) -> Tuple[ComplianceStatus, SeverityLevel, float]:
        """Analyze evidence to determine control compliance status"""
        if not evidence:
            return ComplianceStatus.NOT_APPLICABLE, SeverityLevel.MEDIUM, 5.0
        
        # Calculate compliance score based on evidence
        total_confidence = sum(e.confidence_score for e in evidence)
        avg_confidence = total_confidence / len(evidence)
        
        # Analyze evidence data for compliance indicators
        compliance_indicators = 0
        total_indicators = len(control.requirements)
        
        for evidence_item in evidence:
            evidence_data = evidence_item.evidence_data
            
            # Control-specific analysis
            if control.category == ControlCategory.VULNERABILITY_MANAGEMENT:
                if evidence_data.get("scan_coverage", "0%") >= "90%":
                    compliance_indicators += 1
                if evidence_data.get("critical_count", 100) == 0:
                    compliance_indicators += 1
            
            elif control.category == ControlCategory.ACCESS_CONTROL:
                if evidence_data.get("orphaned_accounts", 100) <= 5:
                    compliance_indicators += 1
                if evidence_data.get("policy_violations", 100) <= 2:
                    compliance_indicators += 1
            
            elif control.category == ControlCategory.NETWORK_SECURITY:
                if evidence_data.get("weak_configurations", 100) <= 3:
                    compliance_indicators += 1
                if "TLS 1.2" in evidence_data.get("encryption_protocols", []):
                    compliance_indicators += 1
        
        # Determine status based on compliance percentage
        compliance_percentage = (compliance_indicators / max(total_indicators, 1)) * 100
        
        if compliance_percentage >= 90:
            status = ComplianceStatus.COMPLIANT
            severity = SeverityLevel.LOW
            risk_score = 2.0
        elif compliance_percentage >= 70:
            status = ComplianceStatus.PARTIALLY_COMPLIANT
            severity = SeverityLevel.MEDIUM
            risk_score = 5.0
        else:
            status = ComplianceStatus.NON_COMPLIANT
            severity = SeverityLevel.HIGH if control.risk_rating == "high" else SeverityLevel.MEDIUM
            risk_score = 8.0 if control.risk_rating == "high" else 6.0
        
        return status, severity, risk_score
    
    def _generate_finding_description(self, control: ComplianceControl, 
                                    evidence: List[ComplianceEvidence],
                                    status: ComplianceStatus) -> str:
        """Generate detailed finding description"""
        if status == ComplianceStatus.COMPLIANT:
            return f"Control {control.control_id} is compliant based on {len(evidence)} pieces of evidence."
        elif status == ComplianceStatus.PARTIALLY_COMPLIANT:
            return f"Control {control.control_id} is partially compliant. Some requirements need attention."
        elif status == ComplianceStatus.NON_COMPLIANT:
            return f"Control {control.control_id} is non-compliant. Immediate remediation required."
        else:
            return f"Control {control.control_id} assessment not applicable or evidence insufficient."
    
    def _assess_business_impact(self, control: ComplianceControl, status: ComplianceStatus) -> str:
        """Assess business impact of control status"""
        if status == ComplianceStatus.COMPLIANT:
            return "Low - Control is effectively implemented"
        elif status == ComplianceStatus.PARTIALLY_COMPLIANT:
            if control.risk_rating == "high":
                return "Medium - Partial gaps may lead to business disruption"
            else:
                return "Low - Minor gaps with limited business impact"
        else:
            if control.risk_rating in ["high", "critical"]:
                return "High - Significant business risk from control failure"
            else:
                return "Medium - Moderate business risk exposure"
    
    def _assess_likelihood(self, control: ComplianceControl, evidence: List[ComplianceEvidence]) -> str:
        """Assess likelihood of control failure"""
        if not evidence:
            return "Medium"
        
        avg_confidence = sum(e.confidence_score for e in evidence) / len(evidence)
        
        if avg_confidence >= 0.8:
            return "Low"
        elif avg_confidence >= 0.6:
            return "Medium"
        else:
            return "High"
    
    def _identify_control_gaps(self, control: ComplianceControl, 
                             evidence: List[ComplianceEvidence]) -> List[str]:
        """Identify gaps in control implementation"""
        gaps = []
        
        # Check if all requirements have supporting evidence
        requirements_covered = set()
        
        for evidence_item in evidence:
            evidence_data = evidence_item.evidence_data
            
            # Map evidence to requirements (simplified)
            if control.category == ControlCategory.VULNERABILITY_MANAGEMENT:
                if evidence_data.get("scan_coverage"):
                    requirements_covered.add("vulnerability_scanning")
                if evidence_data.get("critical_count") is not None:
                    requirements_covered.add("vulnerability_assessment")
            
            elif control.category == ControlCategory.ACCESS_CONTROL:
                if evidence_data.get("total_users"):
                    requirements_covered.add("access_management")
                if evidence_data.get("policy_violations") is not None:
                    requirements_covered.add("policy_enforcement")
        
        # Identify missing requirements
        total_requirements = len(control.requirements)
        covered_requirements = len(requirements_covered)
        
        if covered_requirements < total_requirements:
            gaps.append(f"Evidence missing for {total_requirements - covered_requirements} requirements")
        
        # Control-specific gap analysis
        for evidence_item in evidence:
            evidence_data = evidence_item.evidence_data
            
            if control.category == ControlCategory.VULNERABILITY_MANAGEMENT:
                if evidence_data.get("critical_count", 0) > 0:
                    gaps.append("Critical vulnerabilities requiring immediate remediation")
                if float(evidence_data.get("scan_coverage", "0%").rstrip("%")) < 90:
                    gaps.append("Insufficient vulnerability scan coverage")
            
            elif control.category == ControlCategory.ACCESS_CONTROL:
                if evidence_data.get("orphaned_accounts", 0) > 5:
                    gaps.append("Excessive orphaned accounts detected")
                if evidence_data.get("policy_violations", 0) > 2:
                    gaps.append("Access policy violations require attention")
        
        return gaps
    
    def _generate_control_recommendations(self, control: ComplianceControl, 
                                        status: ComplianceStatus) -> List[str]:
        """Generate recommendations for control improvement"""
        recommendations = []
        
        if status == ComplianceStatus.NON_COMPLIANT:
            recommendations.extend([
                f"Implement {control.title} according to framework requirements",
                "Establish formal procedures and documentation",
                "Assign responsible personnel and resources"
            ])
        elif status == ComplianceStatus.PARTIALLY_COMPLIANT:
            recommendations.extend([
                "Address identified gaps in control implementation",
                "Improve evidence collection and documentation",
                "Enhance monitoring and measurement capabilities"
            ])
        
        # Add control-specific recommendations
        recommendations.extend(control.implementation_guidance[:3])
        
        return recommendations[:5]  # Limit to top 5 recommendations
    
    def _estimate_remediation_timeline(self, control: ComplianceControl, status: ComplianceStatus) -> str:
        """Estimate remediation timeline"""
        if status == ComplianceStatus.COMPLIANT:
            return "N/A - Control is compliant"
        
        base_timeline = {
            "manual": 90,
            "semi_automated": 60,
            "automated": 30
        }
        
        days = base_timeline.get(control.automation_level, 60)
        
        if status == ComplianceStatus.NON_COMPLIANT:
            days *= 2  # Double for non-compliant controls
        
        if control.risk_rating in ["high", "critical"]:
            days = min(days, 30)  # Accelerate for high-risk controls
        
        return f"{days} days"
    
    def _estimate_remediation_cost(self, control: ComplianceControl, status: ComplianceStatus) -> str:
        """Estimate remediation cost"""
        if status == ComplianceStatus.COMPLIANT:
            return "Low - Maintenance only"
        
        cost_factors = {
            "manual": "High",
            "semi_automated": "Medium",
            "automated": "Low"
        }
        
        base_cost = cost_factors.get(control.automation_level, "Medium")
        
        if status == ComplianceStatus.NON_COMPLIANT:
            cost_mapping = {"Low": "Medium", "Medium": "High", "High": "Very High"}
            base_cost = cost_mapping.get(base_cost, "High")
        
        return base_cost
    
    def _identify_compensating_controls(self, control: ComplianceControl) -> List[str]:
        """Identify possible compensating controls"""
        compensating = []
        
        # Map to related controls that could provide compensation
        for related_control_id in control.related_controls:
            if related_control_id in self.compliance_controls:
                related_control = self.compliance_controls[related_control_id]
                compensating.append(f"{related_control_id}: {related_control.title}")
        
        return compensating[:3]  # Limit to top 3
    
    def _get_assessment_frequency(self, control: ComplianceControl) -> int:
        """Get assessment frequency in days"""
        frequency_mapping = {
            "continuous": 30,
            "quarterly": 90,
            "annually": 365
        }
        
        return frequency_mapping.get(control.frequency, 90)
    
    async def _perform_gap_analysis(self, assessment_state: Dict[str, Any]) -> Dict[str, Any]:
        """Perform comprehensive gap analysis"""
        findings = assessment_state["findings"]
        
        gap_analysis = {
            "total_controls": len(assessment_state["controls_to_assess"]),
            "compliant_controls": len([f for f in findings if f.status == ComplianceStatus.COMPLIANT]),
            "partially_compliant": len([f for f in findings if f.status == ComplianceStatus.PARTIALLY_COMPLIANT]),
            "non_compliant": len([f for f in findings if f.status == ComplianceStatus.NON_COMPLIANT]),
            "not_applicable": len([f for f in findings if f.status == ComplianceStatus.NOT_APPLICABLE]),
            "gap_categories": defaultdict(int),
            "priority_gaps": [],
            "remediation_summary": {}
        }
        
        # Analyze gaps by category
        for finding in findings:
            if finding.status != ComplianceStatus.COMPLIANT:
                control = self.compliance_controls[finding.control_id]
                gap_analysis["gap_categories"][control.category.value] += 1
        
        # Identify priority gaps (high-risk non-compliant controls)
        priority_gaps = [
            f for f in findings 
            if f.status == ComplianceStatus.NON_COMPLIANT and f.severity in [SeverityLevel.HIGH, SeverityLevel.CRITICAL]
        ]
        
        gap_analysis["priority_gaps"] = [
            {
                "control_id": f.control_id,
                "title": f.title,
                "severity": f.severity.value,
                "risk_score": f.risk_score,
                "remediation_timeline": f.remediation_timeline
            }
            for f in sorted(priority_gaps, key=lambda x: x.risk_score, reverse=True)[:10]
        ]
        
        return gap_analysis
    
    async def _perform_compliance_risk_assessment(self, assessment_state: Dict[str, Any]) -> Dict[str, Any]:
        """Perform compliance risk assessment"""
        findings = assessment_state["findings"]
        
        # Calculate overall risk metrics
        total_risk_score = sum(f.risk_score for f in findings)
        max_possible_score = len(findings) * 10.0  # Assuming max risk score of 10
        
        risk_percentage = (total_risk_score / max_possible_score) * 100 if max_possible_score > 0 else 0
        
        # Risk by category
        category_risks = defaultdict(list)
        for finding in findings:
            control = self.compliance_controls[finding.control_id]
            category_risks[control.category.value].append(finding.risk_score)
        
        category_risk_summary = {}
        for category, scores in category_risks.items():
            category_risk_summary[category] = {
                "average_risk": sum(scores) / len(scores),
                "max_risk": max(scores),
                "control_count": len(scores)
            }
        
        risk_assessment = {
            "overall_risk_score": total_risk_score,
            "risk_percentage": risk_percentage,
            "risk_level": self._determine_risk_level(risk_percentage),
            "category_risks": category_risk_summary,
            "high_risk_findings": len([f for f in findings if f.risk_score >= 7.0]),
            "medium_risk_findings": len([f for f in findings if 4.0 <= f.risk_score < 7.0]),
            "low_risk_findings": len([f for f in findings if f.risk_score < 4.0])
        }
        
        return risk_assessment
    
    def _determine_risk_level(self, risk_percentage: float) -> str:
        """Determine overall risk level"""
        if risk_percentage >= 80:
            return "Critical"
        elif risk_percentage >= 60:
            return "High"
        elif risk_percentage >= 40:
            return "Medium"
        else:
            return "Low"
    
    async def _get_ai_compliance_analysis(self, assessment_state: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Get AI analysis of compliance assessment"""
        ai_insights = []
        
        try:
            # Overall compliance analysis
            overall_analysis = await self._get_ai_overall_compliance_analysis(assessment_state)
            ai_insights.append({
                "type": "overall_analysis",
                "timestamp": datetime.now().isoformat(),
                "analysis": overall_analysis
            })
            
            # Risk prioritization recommendations
            risk_recommendations = await self._get_ai_risk_prioritization(assessment_state)
            ai_insights.append({
                "type": "risk_prioritization",
                "timestamp": datetime.now().isoformat(),
                "recommendations": risk_recommendations
            })
            
            # Remediation strategy
            remediation_strategy = await self._get_ai_remediation_strategy(assessment_state)
            ai_insights.append({
                "type": "remediation_strategy",
                "timestamp": datetime.now().isoformat(),
                "strategy": remediation_strategy
            })
            
            logger.info("AI compliance analysis completed")
            return ai_insights
            
        except Exception as e:
            logger.error(f"AI compliance analysis failed: {e}")
            return ai_insights
    
    async def _get_ai_overall_compliance_analysis(self, assessment_state: Dict[str, Any]) -> str:
        """Get AI overall compliance analysis"""
        try:
            framework = assessment_state["framework"]
            findings = assessment_state["findings"]
            gap_analysis = assessment_state.get("gap_analysis", {})
            risk_assessment = assessment_state.get("risk_assessment", {})
            
            compliant_count = gap_analysis.get("compliant_controls", 0)
            total_count = gap_analysis.get("total_controls", 0)
            compliance_percentage = (compliant_count / total_count * 100) if total_count > 0 else 0
            
            prompt = f"""
Analyze this comprehensive compliance assessment:

Framework: {framework.value}
Total Controls Assessed: {total_count}
Compliant Controls: {compliant_count}
Compliance Percentage: {compliance_percentage:.1f}%

Gap Analysis Summary:
- Partially Compliant: {gap_analysis.get("partially_compliant", 0)}
- Non-Compliant: {gap_analysis.get("non_compliant", 0)}
- Not Applicable: {gap_analysis.get("not_applicable", 0)}

Risk Assessment:
- Overall Risk Level: {risk_assessment.get("risk_level", "Unknown")}
- High Risk Findings: {risk_assessment.get("high_risk_findings", 0)}
- Medium Risk Findings: {risk_assessment.get("medium_risk_findings", 0)}

Top Gap Categories:
{dict(list(gap_analysis.get("gap_categories", {}).items())[:5])}

Provide comprehensive analysis covering:
1. Overall compliance posture assessment
2. Key compliance strengths and weaknesses
3. Regulatory and business risk implications
4. Certification readiness evaluation
5. Strategic improvement opportunities

Focus on executive-level insights and business impact.
"""
            
            response = await self.ai_service.analyze(
                prompt,
                context="compliance_analysis",
                provider_preference=["openai", "claude"]
            )
            
            return response.get("analysis", "No overall compliance analysis available")
            
        except Exception as e:
            logger.error(f"Failed to get AI overall compliance analysis: {e}")
            return "AI overall compliance analysis unavailable"
    
    async def _get_ai_risk_prioritization(self, assessment_state: Dict[str, Any]) -> str:
        """Get AI risk prioritization recommendations"""
        try:
            findings = assessment_state["findings"]
            
            # Get top risk findings
            high_risk_findings = [
                f for f in findings 
                if f.risk_score >= 7.0
            ]
            
            high_risk_findings.sort(key=lambda x: x.risk_score, reverse=True)
            
            prompt = f"""
Analyze these high-risk compliance findings for prioritization:

Total High-Risk Findings: {len(high_risk_findings)}

Top 5 High-Risk Findings:
{json.dumps([{
    "control_id": f.control_id,
    "title": f.title,
    "status": f.status.value,
    "severity": f.severity.value,
    "risk_score": f.risk_score,
    "business_impact": f.business_impact,
    "remediation_timeline": f.remediation_timeline
} for f in high_risk_findings[:5]], indent=2)}

Provide risk prioritization recommendations covering:
1. Immediate priority actions (next 30 days)
2. Short-term priorities (1-3 months)
3. Medium-term initiatives (3-6 months)
4. Resource allocation recommendations
5. Risk mitigation strategies

Focus on business risk reduction and regulatory compliance.
"""
            
            response = await self.ai_service.analyze(
                prompt,
                context="compliance_risk_prioritization",
                provider_preference=["openai", "claude"]
            )
            
            return response.get("analysis", "No risk prioritization available")
            
        except Exception as e:
            logger.error(f"Failed to get AI risk prioritization: {e}")
            return "AI risk prioritization unavailable"
    
    async def _get_ai_remediation_strategy(self, assessment_state: Dict[str, Any]) -> str:
        """Get AI remediation strategy recommendations"""
        try:
            framework = assessment_state["framework"]
            gap_analysis = assessment_state.get("gap_analysis", {})
            
            prompt = f"""
Develop a comprehensive remediation strategy for this compliance assessment:

Framework: {framework.value}
Priority Gaps: {len(gap_analysis.get("priority_gaps", []))}

Gap Distribution by Category:
{dict(gap_analysis.get("gap_categories", {}))}

Priority Gap Details:
{json.dumps(gap_analysis.get("priority_gaps", [])[:5], indent=2)}

Provide remediation strategy covering:
1. Phased remediation approach
2. Resource requirements and budget considerations
3. Quick wins and long-term initiatives
4. Technology and process improvements
5. Governance and oversight recommendations

Focus on practical, achievable remediation plans.
"""
            
            response = await self.ai_service.analyze(
                prompt,
                context="compliance_remediation_strategy",
                provider_preference=["openai", "claude"]
            )
            
            return response.get("analysis", "No remediation strategy available")
            
        except Exception as e:
            logger.error(f"Failed to get AI remediation strategy: {e}")
            return "AI remediation strategy unavailable"
    
    async def _create_compliance_assessment_report(self, assessment_state: Dict[str, Any]) -> ComplianceAssessment:
        """Create final compliance assessment report"""
        framework = assessment_state["framework"]
        findings = assessment_state["findings"]
        gap_analysis = assessment_state.get("gap_analysis", {})
        risk_assessment = assessment_state.get("risk_assessment", {})
        
        # Calculate compliance percentage
        compliant_count = gap_analysis.get("compliant_controls", 0)
        total_count = gap_analysis.get("total_controls", 0)
        compliance_percentage = (compliant_count / total_count * 100) if total_count > 0 else 0
        
        # Determine overall status
        if compliance_percentage >= 90:
            overall_status = ComplianceStatus.COMPLIANT
        elif compliance_percentage >= 70:
            overall_status = ComplianceStatus.PARTIALLY_COMPLIANT
        else:
            overall_status = ComplianceStatus.NON_COMPLIANT
        
        # Generate executive summary
        executive_summary = self._generate_executive_summary(
            framework, compliance_percentage, gap_analysis, risk_assessment
        )
        
        # Generate recommendations
        recommendations = self._generate_compliance_recommendations(findings, gap_analysis)
        
        assessment = ComplianceAssessment(
            assessment_id=assessment_state["assessment_id"],
            framework=framework,
            scope=assessment_state["config"].get("scope", "full"),
            assessment_date=assessment_state["start_time"],
            assessor="NexusScan Compliance Engine",
            target_environment=assessment_state["config"].get("target_environment", {}),
            controls_assessed=assessment_state["controls_assessed"],
            findings=findings,
            overall_status=overall_status,
            compliance_percentage=compliance_percentage,
            risk_score=risk_assessment.get("overall_risk_score", 0.0),
            executive_summary=executive_summary,
            recommendations=recommendations,
            next_assessment_date=datetime.now() + timedelta(days=365),  # Annual reassessment
            certification_status=None
        )
        
        return assessment
    
    def _generate_executive_summary(self, framework: ComplianceFramework, 
                                  compliance_percentage: float,
                                  gap_analysis: Dict[str, Any],
                                  risk_assessment: Dict[str, Any]) -> str:
        """Generate executive summary for compliance assessment"""
        summary_parts = [
            f"Compliance assessment for {framework.value} completed.",
            f"Overall compliance: {compliance_percentage:.1f}%",
            f"Risk level: {risk_assessment.get('risk_level', 'Unknown')}",
            f"Priority gaps identified: {len(gap_analysis.get('priority_gaps', []))}"
        ]
        
        if compliance_percentage >= 90:
            summary_parts.append("Organization demonstrates strong compliance posture.")
        elif compliance_percentage >= 70:
            summary_parts.append("Partial compliance achieved with opportunities for improvement.")
        else:
            summary_parts.append("Significant compliance gaps require immediate attention.")
        
        return " ".join(summary_parts)
    
    def _generate_compliance_recommendations(self, findings: List[ComplianceFinding],
                                           gap_analysis: Dict[str, Any]) -> List[str]:
        """Generate high-level compliance recommendations"""
        recommendations = []
        
        # Priority-based recommendations
        non_compliant_count = gap_analysis.get("non_compliant", 0)
        if non_compliant_count > 0:
            recommendations.append(f"Address {non_compliant_count} non-compliant controls as highest priority")
        
        partially_compliant_count = gap_analysis.get("partially_compliant", 0)
        if partially_compliant_count > 0:
            recommendations.append(f"Improve {partially_compliant_count} partially compliant controls")
        
        # Category-specific recommendations
        top_gap_categories = sorted(
            gap_analysis.get("gap_categories", {}).items(),
            key=lambda x: x[1],
            reverse=True
        )[:3]
        
        for category, count in top_gap_categories:
            recommendations.append(f"Focus improvement efforts on {category} controls ({count} gaps)")
        
        # General recommendations
        recommendations.extend([
            "Implement continuous compliance monitoring",
            "Establish regular compliance training programs",
            "Enhance documentation and evidence collection processes",
            "Consider third-party compliance assessment validation"
        ])
        
        return recommendations[:8]  # Top 8 recommendations
    
    def get_assessment_status(self, assessment_id: str) -> Optional[Dict[str, Any]]:
        """Get compliance assessment status"""
        if assessment_id not in self.active_assessments:
            return None
        
        assessment = self.active_assessments[assessment_id]
        
        progress_map = {
            "initialization": 0,
            "evidence_collection": 20,
            "control_assessment": 50,
            "gap_analysis": 70,
            "risk_assessment": 85,
            "ai_analysis": 95,
            "completed": 100
        }
        
        return {
            "assessment_id": assessment_id,
            "framework": assessment["framework"].value,
            "status": assessment["status"],
            "phase": assessment["phase"],
            "progress": progress_map.get(assessment["phase"], 0),
            "controls_assessed": len(assessment.get("controls_assessed", [])),
            "total_controls": len(assessment.get("controls_to_assess", [])),
            "findings_count": len(assessment.get("findings", [])),
            "elapsed_time": (datetime.now() - assessment["start_time"]).total_seconds()
        }
    
    def list_supported_frameworks(self) -> List[Dict[str, Any]]:
        """List all supported compliance frameworks"""
        frameworks = []
        
        framework_counts = defaultdict(int)
        for control in self.compliance_controls.values():
            framework_counts[control.framework] += 1
        
        for framework, count in framework_counts.items():
            frameworks.append({
                "framework": framework.value,
                "name": framework.value.replace("_", " ").title(),
                "controls_count": count,
                "categories": list(set(
                    control.category.value for control in self.compliance_controls.values()
                    if control.framework == framework
                ))
            })
        
        return frameworks
    
    def get_framework_controls(self, framework: ComplianceFramework) -> List[Dict[str, Any]]:
        """Get all controls for a specific framework"""
        controls = []
        
        for control in self.compliance_controls.values():
            if control.framework == framework:
                controls.append({
                    "control_id": control.control_id,
                    "title": control.title,
                    "category": control.category.value,
                    "risk_rating": control.risk_rating,
                    "automation_level": control.automation_level,
                    "frequency": control.frequency,
                    "requirements_count": len(control.requirements)
                })
        
        return sorted(controls, key=lambda x: x["control_id"])
    
    def get_assessment_history(self, framework: Optional[ComplianceFramework] = None,
                             limit: int = 20) -> List[Dict[str, Any]]:
        """Get compliance assessment history"""
        history = []
        
        assessments = self.assessment_history
        if framework:
            assessments = [a for a in assessments if a.framework == framework]
        
        recent_assessments = assessments[-limit:]
        
        for assessment in recent_assessments:
            history.append({
                "assessment_id": assessment.assessment_id,
                "framework": assessment.framework.value,
                "assessment_date": assessment.assessment_date.isoformat(),
                "overall_status": assessment.overall_status.value,
                "compliance_percentage": assessment.compliance_percentage,
                "risk_score": assessment.risk_score,
                "findings_count": len(assessment.findings),
                "scope": assessment.scope
            })
        
        return sorted(history, key=lambda x: x["assessment_date"], reverse=True)
    
    def set_execution_mode(self, mode: str):
        """Set execution mode for evidence collection"""
        self._execution_mode = mode
        logger.info(f"Compliance checker execution mode set to: {mode}")
    
    async def _collect_real_evidence(self, control: ComplianceControl) -> List[ComplianceEvidence]:
        """Collect real evidence for compliance control"""
        evidence = []
        
        try:
            logger.warning(f"REAL EXECUTION MODE: Collecting evidence for {control.control_id}")
            
            if control.category == ControlCategory.VULNERABILITY_MANAGEMENT:
                evidence.extend(await self._collect_real_vulnerability_evidence(control))
            
            elif control.category == ControlCategory.ACCESS_CONTROL:
                evidence.extend(await self._collect_real_access_evidence(control))
            
            elif control.category == ControlCategory.NETWORK_SECURITY:
                evidence.extend(await self._collect_real_network_evidence(control))
            
            elif control.category == ControlCategory.CONFIGURATION_MANAGEMENT:
                evidence.extend(await self._collect_real_config_evidence(control))
            
            elif control.category == ControlCategory.AUDIT_LOGGING:
                evidence.extend(await self._collect_real_logging_evidence(control))
            
            return evidence
            
        except Exception as e:
            logger.error(f"Real evidence collection failed for {control.control_id}: {e}")
            return [ComplianceEvidence(
                evidence_id=f"error_{control.control_id}_{int(time.time())}",
                control_id=control.control_id,
                evidence_type="error",
                description=f"Evidence collection failed: {str(e)}",
                source="compliance_checker",
                collection_date=datetime.now(),
                validity_period=None,
                evidence_data={"error": str(e)},
                automated=True,
                confidence_score=0.0
            )]
    
    async def _collect_real_vulnerability_evidence(self, control: ComplianceControl) -> List[ComplianceEvidence]:
        """Collect real vulnerability management evidence"""
        evidence = []
        
        try:
            # Run a quick vulnerability scan
            scan_command = "nmap --script vuln localhost"
            
            process = await asyncio.create_subprocess_shell(
                scan_command,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await asyncio.wait_for(process.communicate(), timeout=300)
            
            if process.returncode == 0:
                scan_output = stdout.decode('utf-8', errors='ignore')
                
                # Parse vulnerabilities from output
                import re
                cve_count = len(re.findall(r'CVE-\d{4}-\d{4,7}', scan_output))
                
                evidence.append(ComplianceEvidence(
                    evidence_id=f"vuln_scan_{control.control_id}_{int(time.time())}",
                    control_id=control.control_id,
                    evidence_type="vulnerability_scan",
                    description="Real vulnerability scan results",
                    source="nmap_vuln_scan",
                    collection_date=datetime.now(),
                    validity_period=7,  # Valid for 7 days
                    evidence_data={
                        "scan_output": scan_output[:1000],  # Truncate for storage
                        "cve_count": cve_count,
                        "scan_date": datetime.now().isoformat(),
                        "target": "localhost"
                    },
                    automated=True,
                    confidence_score=0.9
                ))
            
        except Exception as e:
            logger.error(f"Vulnerability evidence collection failed: {e}")
        
        return evidence
    
    async def _collect_real_access_evidence(self, control: ComplianceControl) -> List[ComplianceEvidence]:
        """Collect real access control evidence"""
        evidence = []
        
        try:
            # Collect user account information
            if os.name == 'posix':  # Linux/Unix
                # Get user accounts
                users_command = "cat /etc/passwd | wc -l"
                process = await asyncio.create_subprocess_shell(
                    users_command,
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )
                stdout, stderr = await asyncio.wait_for(process.communicate(), timeout=30)
                
                if process.returncode == 0:
                    user_count = int(stdout.decode('utf-8', errors='ignore').strip())
                    
                    evidence.append(ComplianceEvidence(
                        evidence_id=f"access_review_{control.control_id}_{int(time.time())}",
                        control_id=control.control_id,
                        evidence_type="access_review",
                        description="System user account audit",
                        source="system_audit",
                        collection_date=datetime.now(),
                        validity_period=30,
                        evidence_data={
                            "total_users": user_count,
                            "audit_date": datetime.now().isoformat(),
                            "system_type": "linux"
                        },
                        automated=True,
                        confidence_score=0.8
                    ))
            
        except Exception as e:
            logger.error(f"Access evidence collection failed: {e}")
        
        return evidence
    
    async def _collect_real_network_evidence(self, control: ComplianceControl) -> List[ComplianceEvidence]:
        """Collect real network security evidence"""
        evidence = []
        
        try:
            # Check network interfaces and open ports
            netstat_command = "netstat -tuln 2>/dev/null || ss -tuln"
            
            process = await asyncio.create_subprocess_shell(
                netstat_command,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await asyncio.wait_for(process.communicate(), timeout=30)
            
            if process.returncode == 0:
                netstat_output = stdout.decode('utf-8', errors='ignore')
                
                # Count listening ports
                listening_ports = []
                for line in netstat_output.split('\n'):
                    if 'LISTEN' in line or ':' in line:
                        parts = line.split()
                        if len(parts) >= 4:
                            addr_port = parts[3] if 'LISTEN' in line else parts[1]
                            if ':' in addr_port:
                                port = addr_port.split(':')[-1]
                                if port.isdigit():
                                    listening_ports.append(port)
                
                evidence.append(ComplianceEvidence(
                    evidence_id=f"network_scan_{control.control_id}_{int(time.time())}",
                    control_id=control.control_id,
                    evidence_type="network_configuration",
                    description="Network ports and services audit",
                    source="network_audit",
                    collection_date=datetime.now(),
                    validity_period=1,  # Network state changes frequently
                    evidence_data={
                        "listening_ports": list(set(listening_ports)),
                        "port_count": len(set(listening_ports)),
                        "audit_date": datetime.now().isoformat(),
                        "raw_output": netstat_output[:500]  # Truncated
                    },
                    automated=True,
                    confidence_score=0.85
                ))
            
        except Exception as e:
            logger.error(f"Network evidence collection failed: {e}")
        
        return evidence
    
    async def _collect_real_config_evidence(self, control: ComplianceControl) -> List[ComplianceEvidence]:
        """Collect real configuration management evidence"""
        evidence = []
        
        try:
            # Check system configuration files
            if os.name == 'posix':
                # Check SSH configuration
                ssh_config_command = "cat /etc/ssh/sshd_config 2>/dev/null | grep -E '(PermitRootLogin|PasswordAuthentication|Protocol)' | head -10"
                
                process = await asyncio.create_subprocess_shell(
                    ssh_config_command,
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )
                
                stdout, stderr = await asyncio.wait_for(process.communicate(), timeout=30)
                
                if process.returncode == 0:
                    ssh_config = stdout.decode('utf-8', errors='ignore')
                    
                    evidence.append(ComplianceEvidence(
                        evidence_id=f"config_audit_{control.control_id}_{int(time.time())}",
                        control_id=control.control_id,
                        evidence_type="configuration_file",
                        description="SSH daemon configuration audit",
                        source="sshd_config",
                        collection_date=datetime.now(),
                        validity_period=90,  # Config changes less frequently
                        evidence_data={
                            "ssh_config": ssh_config,
                            "audit_date": datetime.now().isoformat(),
                            "config_file": "/etc/ssh/sshd_config"
                        },
                        automated=True,
                        confidence_score=0.9
                    ))
            
        except Exception as e:
            logger.error(f"Configuration evidence collection failed: {e}")
        
        return evidence
    
    async def _collect_real_logging_evidence(self, control: ComplianceControl) -> List[ComplianceEvidence]:
        """Collect real audit logging evidence"""
        evidence = []
        
        try:
            # Check system logs
            if os.name == 'posix':
                # Count recent log entries
                log_command = "journalctl --since='1 day ago' | wc -l 2>/dev/null || echo '0'"
                
                process = await asyncio.create_subprocess_shell(
                    log_command,
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )
                
                stdout, stderr = await asyncio.wait_for(process.communicate(), timeout=30)
                
                if process.returncode == 0:
                    log_count = int(stdout.decode('utf-8', errors='ignore').strip() or '0')
                    
                    evidence.append(ComplianceEvidence(
                        evidence_id=f"log_audit_{control.control_id}_{int(time.time())}",
                        control_id=control.control_id,
                        evidence_type="log_analysis",
                        description="System log audit and analysis",
                        source="system_logs",
                        collection_date=datetime.now(),
                        validity_period=1,  # Logs change constantly
                        evidence_data={
                            "daily_log_entries": log_count,
                            "audit_date": datetime.now().isoformat(),
                            "log_source": "journalctl"
                        },
                        automated=True,
                        confidence_score=0.8
                    ))
            
        except Exception as e:
            logger.error(f"Logging evidence collection failed: {e}")
        
        return evidence