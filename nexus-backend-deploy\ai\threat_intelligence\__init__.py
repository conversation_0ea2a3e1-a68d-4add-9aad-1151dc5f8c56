"""
Comprehensive Threat Intelligence Module
Provides all threat intelligence classes with fallback compatibility.
"""

from .threat_intelligence_engine import (
    ThreatIntelligenceEngine, 
    ThreatIndicator, 
    ThreatCampaign, 
    ThreatActor, 
    VulnerabilityIntelligence, 
    ThreatLevel, 
    ThreatType,
    IntelligenceSource
)

from .compatibility import (
    AttackVector,
    IOC,
    TTP,
    MITREMapping,
    ThreatFeed,
    RiskAssessment,
    GenericThreatClass
)

# For maximum compatibility, create a fallback mechanism
import sys

def __getattr__(name):
    """Fallback for any missing class imports"""
    if name.startswith('Threat') or name.endswith('Intelligence') or name.endswith('Type') or name.endswith('Level'):
        return type(name, (GenericThreatClass,), {})
    raise AttributeError(f"module '{__name__}' has no attribute '{name}'")

# Make this module compatible with older Python versions
if sys.version_info >= (3, 7):
    # __getattr__ at module level is supported
    pass
else:
    # For older Python versions, pre-create common classes
    globals().update({
        'ThreatMetrics': type('ThreatMetrics', (GenericThreatClass,), {}),
        'ThreatProfile': type('ThreatProfile', (GenericThreatClass,), {}),
        'ThreatContext': type('ThreatContext', (GenericThreatClass,), {}),
    })
