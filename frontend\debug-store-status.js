/**
 * Store Status Debug - Check what's actually in the Zustand stores
 * 
 * Run this in the browser console when the app is running.
 * This script directly checks store state to find the disconnect.
 */

console.log('🔍 Checking Zustand Store Status...');

// Try to access React DevTools to get store state
if (window.__REACT_DEVTOOLS_GLOBAL_HOOK__) {
  console.log('✅ React DevTools available - checking stores...');
} else {
  console.log('⚠️ React DevTools not available - using window helpers...');
}

// Check if our debug helpers are available
if (window.nexusScan) {
  console.log('🛠️ NexusScan debug helpers available');
  
  // Test backend connection through helper
  window.nexusScan.getBackendStatus()
    .then(status => {
      console.log('📡 Backend Status (Helper):', status);
    })
    .catch(error => {
      console.error('❌ Backend Status Error:', error);
    });
  
  // Test WSL status through helper  
  window.nexusScan.getWSLStatus()
    .then(status => {
      console.log('🐧 WSL Status (Helper):', status);
    })
    .catch(error => {
      console.error('❌ WSL Status Error:', error);
    });
    
  // Test tools through helper
  window.nexusScan.getAvailableTools()
    .then(tools => {
      console.log(`🔧 Available Tools (Helper): ${tools.length} total`);
      console.log('🔧 First 5 tools:', tools.slice(0, 5));
    })
    .catch(error => {
      console.error('❌ Tools Error:', error);
    });
} else {
  console.log('❌ NexusScan debug helpers not available');
}

// Try to access the stores directly if possible
setTimeout(() => {
  console.log('🏪 Attempting direct store access...');
  
  // Check if we can find the store instances
  const reactFiberNode = document.querySelector('#root')?._reactInternalInstance ||
                         document.querySelector('#root')?._reactInternals;
  
  if (reactFiberNode) {
    console.log('⚛️ React Fiber found - stores might be accessible');
  } else {
    console.log('⚠️ Could not access React Fiber - stores not directly accessible');
  }
  
  console.log('✅ Store debug check complete - see results above');
}, 1000);