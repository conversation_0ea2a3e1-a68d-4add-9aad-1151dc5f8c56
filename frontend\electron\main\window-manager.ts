import { BrowserWindow, screen } from 'electron';
import log from 'electron-log';

export interface WindowInfo {
  id: string;
  window: BrowserWindow;
  type: 'main' | 'tool' | 'report' | 'settings';
  created: Date;
}

export class WindowManager {
  private windows: Map<string, WindowInfo> = new Map();
  private nextWindowId = 1;

  /**
   * Register a window with the manager
   */
  registerWindow(id: string, window: BrowserWindow, type: WindowInfo['type'] = 'main'): void {
    const windowInfo: WindowInfo = {
      id,
      window,
      type,
      created: new Date()
    };

    this.windows.set(id, windowInfo);

    // Setup window event handlers
    window.on('closed', () => {
      this.windows.delete(id);
      log.info(`Window closed: ${id}`);
    });

    log.info(`Window registered: ${id} (${type})`);
  }

  /**
   * Create a new tool window for specific security tools
   */
  createToolWindow(toolName: string, config?: any): BrowserWindow {
    const windowId = `tool-${toolName}-${this.nextWindowId++}`;
    
    // Get current display
    const primaryDisplay = screen.getPrimaryDisplay();
    const { width, height } = primaryDisplay.workAreaSize;

    // Create tool window
    const toolWindow = new BrowserWindow({
      width: Math.min(1200, width * 0.8),
      height: Math.min(800, height * 0.8),
      minWidth: 800,
      minHeight: 600,
      show: false,
      title: `NexusScan - ${toolName}`,
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        sandbox: true,
        webSecurity: true,
        preload: require('path').join(__dirname, '../preload/api.js')
      }
    });

    // Load tool-specific page
    const baseUrl = this.getBaseUrl();
    const toolUrl = `${baseUrl}#/tools/${toolName.toLowerCase()}`;
    
    toolWindow.loadURL(toolUrl);

    toolWindow.on('ready-to-show', () => {
      toolWindow.show();
    });

    // Register window
    this.registerWindow(windowId, toolWindow, 'tool');

    log.info(`Tool window created: ${toolName} (${windowId})`);
    return toolWindow;
  }

  /**
   * Create a report window
   */
  createReportWindow(reportId: string): BrowserWindow {
    const windowId = `report-${reportId}-${this.nextWindowId++}`;
    
    const reportWindow = new BrowserWindow({
      width: 1000,
      height: 800,
      minWidth: 800,
      minHeight: 600,
      show: false,
      title: `NexusScan - Report ${reportId}`,
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        sandbox: true,
        webSecurity: true,
        preload: require('path').join(__dirname, '../preload/api.js')
      }
    });

    // Load report page
    const baseUrl = this.getBaseUrl();
    const reportUrl = `${baseUrl}#/reports/${reportId}`;
    
    reportWindow.loadURL(reportUrl);

    reportWindow.on('ready-to-show', () => {
      reportWindow.show();
    });

    // Register window
    this.registerWindow(windowId, reportWindow, 'report');

    log.info(`Report window created: ${reportId} (${windowId})`);
    return reportWindow;
  }

  /**
   * Get window by ID
   */
  getWindow(id: string): BrowserWindow | null {
    const windowInfo = this.windows.get(id);
    return windowInfo ? windowInfo.window : null;
  }

  /**
   * Get all windows of a specific type
   */
  getWindowsByType(type: WindowInfo['type']): BrowserWindow[] {
    return Array.from(this.windows.values())
      .filter(info => info.type === type)
      .map(info => info.window);
  }

  /**
   * Close window by ID
   */
  closeWindow(id: string): boolean {
    const windowInfo = this.windows.get(id);
    if (windowInfo) {
      windowInfo.window.close();
      return true;
    }
    return false;
  }

  /**
   * Close all windows of a specific type
   */
  closeWindowsByType(type: WindowInfo['type']): number {
    const windows = this.getWindowsByType(type);
    windows.forEach(window => window.close());
    return windows.length;
  }

  /**
   * Get window statistics
   */
  getStats(): {
    total: number;
    byType: Record<WindowInfo['type'], number>;
  } {
    const stats = {
      total: this.windows.size,
      byType: {
        main: 0,
        tool: 0,
        report: 0,
        settings: 0
      } as Record<WindowInfo['type'], number>
    };

    for (const windowInfo of this.windows.values()) {
      stats.byType[windowInfo.type]++;
    }

    return stats;
  }

  /**
   * Focus window by ID
   */
  focusWindow(id: string): boolean {
    const windowInfo = this.windows.get(id);
    if (windowInfo && !windowInfo.window.isDestroyed()) {
      if (windowInfo.window.isMinimized()) {
        windowInfo.window.restore();
      }
      windowInfo.window.focus();
      return true;
    }
    return false;
  }

  /**
   * Get main window
   */
  getMainWindow(): BrowserWindow | null {
    const mainWindows = this.getWindowsByType('main');
    return mainWindows.length > 0 ? mainWindows[0] : null;
  }

  /**
   * Cascade windows for better organization
   */
  cascadeWindows(): void {
    const windows = Array.from(this.windows.values())
      .filter(info => !info.window.isDestroyed() && info.type !== 'main');

    let offsetX = 30;
    let offsetY = 30;

    windows.forEach((windowInfo, index) => {
      const window = windowInfo.window;
      const [x, y] = window.getPosition();
      
      window.setPosition(x + offsetX * index, y + offsetY * index);
    });
  }

  /**
   * Tile windows horizontally
   */
  tileWindowsHorizontally(): void {
    const windows = this.getWindowsByType('tool');
    if (windows.length === 0) return;

    const primaryDisplay = screen.getPrimaryDisplay();
    const { width, height } = primaryDisplay.workAreaSize;
    
    const windowWidth = Math.floor(width / windows.length);
    const windowHeight = height;

    windows.forEach((window, index) => {
      window.setBounds({
        x: windowWidth * index,
        y: 0,
        width: windowWidth,
        height: windowHeight
      });
    });
  }

  /**
   * Get base URL for loading pages
   */
  private getBaseUrl(): string {
    const isDevelopment = process.env.NODE_ENV === 'development';
    return isDevelopment ? 'http://localhost:5173' : 
           `file://${require('path').join(__dirname, '../../../index.html')}`;
  }

  /**
   * Cleanup all windows
   */
  cleanup(): void {
    log.info('Cleaning up window manager...');
    
    // Close all non-main windows
    const nonMainWindows = Array.from(this.windows.values())
      .filter(info => info.type !== 'main');

    nonMainWindows.forEach(windowInfo => {
      if (!windowInfo.window.isDestroyed()) {
        windowInfo.window.close();
      }
    });

    this.windows.clear();
    log.info('Window manager cleanup complete');
  }
}