#!/usr/bin/env python3
"""
WPScan Wrapper for NexusScan Desktop
WordPress vulnerability scanner integration
"""

import asyncio
import logging
from typing import Dict, List, Optional, Callable, Any
from datetime import datetime

from security.tools.tool_registry import (
    ToolMetadata, ToolCategory, ToolCapabilities,
    ScanOptions, ScanResult, ToolStatus, register_tool
)
from security.tools.external_tool_wrapper import ExternalToolWrapper

logger = logging.getLogger(__name__)


@register_tool
class WPScanWrapper(ExternalToolWrapper):
    """WPScan WordPress vulnerability scanner wrapper"""
    
    def __init__(self):
        super().__init__()
        self.tool_executable = "wpscan"
    
    def get_metadata(self) -> ToolMetadata:
        return ToolMetadata(
            name="wpscan",
            display_name="WPScan",
            description="WordPress vulnerability scanner",
            version="1.0.0",
            category=ToolCategory.WEB_SCANNER,
            capabilities=ToolCapabilities(
                supports_async=True,
                network_access_required=True,
                supported_targets=["url", "domain"],
                requires_root=False
            ),
            default_options={
                "enumerate": "vp,vt,u,m",  # vulnerable plugins, themes, users, media
                "detection_mode": "mixed",
                "force": False,
                "random_user_agent": True
            }
        )
    
    def check_native_availability(self) -> bool:
        """Check if WPScan is available"""
        try:
            import subprocess
            result = subprocess.run(
                ["wpscan", "--version"],
                capture_output=True,
                timeout=5
            )
            return result.returncode == 0
        except Exception:
            return False
    
    def get_tool_command(self, options: ScanOptions) -> List[str]:
        """Get WPScan command"""
        command = [
            "wpscan",
            "--url", options.target,
            "--format", "cli",  # CLI output format
            "--no-update"  # Don't update database
        ]
        
        # Add enumeration options
        enumerate = options.custom_options.get("enumerate", "vp,vt,u,m")
        if enumerate:
            command.extend(["--enumerate", enumerate])
        
        # Add detection mode
        detection_mode = options.custom_options.get("detection_mode", "mixed")
        command.extend(["--detection-mode", detection_mode])
        
        # Add random user agent
        if options.custom_options.get("random_user_agent", True):
            command.append("--random-user-agent")
        
        # Add force flag if needed
        if options.custom_options.get("force", False):
            command.append("--force")
        
        # Add API token if available
        api_token = options.custom_options.get("api_token")
        if api_token:
            command.extend(["--api-token", api_token])
        
        return command
    
    def parse_tool_output(self, output: str, target: str) -> Dict[str, Any]:
        """Parse WPScan output"""
        lines = output.strip().split('\n')
        vulnerabilities = []
        plugins = []
        themes = []
        users = []
        wordpress_version = None
        server_info = {}
        
        current_section = None
        
        for line in lines:
            # WordPress version detection
            if "WordPress version" in line and "identified" in line:
                parts = line.split()
                for i, part in enumerate(parts):
                    if part == "version" and i + 1 < len(parts):
                        wordpress_version = parts[i + 1]
                        break
            
            # Server information
            elif "Web Server :" in line:
                server_info['web_server'] = line.split(":", 1)[1].strip()
            elif "PHP Version :" in line:
                server_info['php_version'] = line.split(":", 1)[1].strip()
            
            # Section markers
            elif "[+] Enumerating Vulnerable Plugins" in line:
                current_section = "plugins"
            elif "[+] Enumerating Vulnerable Themes" in line:
                current_section = "themes"
            elif "[+] Enumerating Users" in line:
                current_section = "users"
            
            # Vulnerability detection
            elif line.strip().startswith("[!]") and "Title:" in line:
                vuln_title = line.split("Title:", 1)[1].strip()
                vulnerabilities.append({
                    "type": current_section or "general",
                    "title": vuln_title,
                    "severity": "high" if "[!]" in line else "medium"
                })
            
            # Plugin detection
            elif current_section == "plugins" and line.strip().startswith("[+]"):
                plugin_info = line.strip()[3:].strip()
                if plugin_info and "Location:" not in plugin_info:
                    plugins.append({
                        "name": plugin_info.split()[0] if plugin_info else "unknown",
                        "version": "unknown",
                        "vulnerable": "[!]" in line
                    })
            
            # Theme detection
            elif current_section == "themes" and line.strip().startswith("[+]"):
                theme_info = line.strip()[3:].strip()
                if theme_info and "Location:" not in theme_info:
                    themes.append({
                        "name": theme_info.split()[0] if theme_info else "unknown",
                        "version": "unknown",
                        "vulnerable": "[!]" in line
                    })
            
            # User enumeration
            elif current_section == "users" and line.strip().startswith("[+]"):
                user_info = line.strip()[3:].strip()
                if user_info and not user_info.startswith("Enumerating"):
                    users.append({
                        "username": user_info.split()[0] if user_info else "unknown",
                        "id": "unknown"
                    })
        
        # Create standardized vulnerabilities list
        standardized_vulns = []
        for vuln in vulnerabilities:
            standardized_vulns.append({
                "name": vuln["title"],
                "severity": vuln["severity"],
                "description": f"WordPress {vuln['type']} vulnerability: {vuln['title']}",
                "type": f"wordpress_{vuln['type']}",
                "cve": None,
                "remediation": "Update to the latest version or apply security patches"
            })
        
        return {
            "target": target,
            "wordpress_detected": wordpress_version is not None,
            "wordpress_version": wordpress_version,
            "server_info": server_info,
            "vulnerabilities": standardized_vulns,
            "plugins": plugins,
            "themes": themes,
            "users": users,
            "findings_summary": {
                "vulnerable_plugins": len([p for p in plugins if p.get("vulnerable", False)]),
                "vulnerable_themes": len([t for t in themes if t.get("vulnerable", False)]),
                "total_vulnerabilities": len(vulnerabilities),
                "users_found": len(users)
            }
        }
    
    async def execute_simulation(self, options: ScanOptions,
                                 progress_callback: Optional[Callable[[float, str], None]] = None) -> ScanResult:
        """Execute simulated scan for Railway/testing"""
        start_time = datetime.now().isoformat()
        
        if progress_callback:
            await progress_callback(0.1, "Starting WordPress detection...")
        
        await asyncio.sleep(1)  # Simulate scan time
        
        if progress_callback:
            await progress_callback(0.5, "Enumerating plugins and themes...")
        
        await asyncio.sleep(1)
        
        if progress_callback:
            await progress_callback(0.8, "Checking for vulnerabilities...")
        
        # Simulated results
        simulated_output = f"""[+] URL: {options.target}
[+] Started: {start_time}

[+] Interesting Finding(s):

[+] WordPress version 6.4.2 identified (Latest, released on 2023-12-06).

[+] WordPress theme in use: twentytwentyfour
 | Location: {options.target}/wp-content/themes/twentytwentyfour/
 | Latest Version: 1.0
 | Last Updated: 2023-11-07

[+] Enumerating Vulnerable Plugins (via Passive Methods)
[+] Checking Plugin Versions (via Passive and Aggressive Methods)

[i] Plugin(s) Identified:

[+] contact-form-7
 | Location: {options.target}/wp-content/plugins/contact-form-7/
 | Latest Version: 5.8.4
 | [!] The version is out of date, the latest version is 5.8.5

[+] Enumerating Users (via Passive and Aggressive Methods)

[i] User(s) Identified:

[+] admin
 | Found By: Author Posts - Display Name (Passive Detection)

[+] testuser
 | Found By: Wp Json Api (Aggressive Detection)"""
        
        parsed_results = self.parse_tool_output(simulated_output, options.target)
        
        if progress_callback:
            await progress_callback(1.0, "Scan complete")
        
        return ScanResult(
            tool_name=self.metadata.name,
            target=options.target,
            status=ToolStatus.COMPLETED,
            start_time=start_time,
            end_time=datetime.now().isoformat(),
            duration_seconds=(datetime.now() - datetime.fromisoformat(start_time)).total_seconds(),
            raw_output=simulated_output,
            parsed_results=parsed_results,
            vulnerabilities=parsed_results.get("vulnerabilities", []),
            metadata={
                "wordpress_version": parsed_results.get("wordpress_version"),
                "plugins_found": len(parsed_results.get("plugins", [])),
                "themes_found": len(parsed_results.get("themes", [])),
                "users_found": len(parsed_results.get("users", []))
            }
        )