/**
 * Settings Page - Application configuration and preferences
 */
import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Settings,
  Shield,
  Key,
  Server,
  Bell,
  Eye,
  EyeOff,
  CheckCircle,
  AlertTriangle,
  Cpu,
  Network,
  Database,
  Save,
  RotateCcw,
  Download,
  Upload
} from 'lucide-react';

interface APIKey {
  id: string;
  name: string;
  service: string;
  status: 'active' | 'inactive' | 'expired';
  lastUsed?: string;
  masked: string;
}

interface BackendConfig {
  url: string;
  timeout: number;
  retries: number;
  status: 'connected' | 'disconnected' | 'error';
}

export function SettingsPage() {
  const [activeTab, setActiveTab] = React.useState('general');
  const [showApiKeys, setShowApiKeys] = React.useState(false);

  // Load saved settings on mount
  React.useEffect(() => {
    try {
      const savedSettings = localStorage.getItem('nexusscan-settings');
      if (savedSettings) {
        const parsed = JSON.parse(savedSettings);

        if (parsed.general) {
          setGeneralSettings(parsed.general);
        }
        if (parsed.backend && isDevelopment) {
          setBackendConfig(parsed.backend);
        }
        if (parsed.security) {
          setSecuritySettings(parsed.security);
        }
        if (parsed.apiKeys) {
          setApiKeys(parsed.apiKeys);
        }

        console.log('📥 Settings loaded from storage');

        // Send execution mode to backend if loaded from storage
        if (parsed.security?.executionMode) {
          try {
            fetch('http://ec2-3-89-91-209.compute-1.amazonaws.com:8000/api/settings/execution-mode', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({ execution_mode: parsed.security.executionMode })
            }).then(response => {
              if (response.ok) {
                console.log('✅ Execution mode synced with backend:', parsed.security.executionMode);
              }
            }).catch(err => {
              console.warn('⚠️ Could not sync execution mode with backend:', err);
            });
          } catch (error) {
            console.warn('⚠️ Failed to sync execution mode:', error);
          }
        }
      }
    } catch (error) {
      console.error('❌ Failed to load settings:', error);
    }
  }, []);
  
  // General Settings
  const [generalSettings, setGeneralSettings] = React.useState({
    theme: 'dark',
    autoSave: true,
    notifications: true,
    autoUpdates: true,
    language: 'en'
  });

  // Backend Configuration (read-only in production)
  const [backendConfig, setBackendConfig] = React.useState<BackendConfig>({
    url: 'http://161.97.99.62:8090',
    timeout: 30000,
    retries: 3,
    status: 'connected'
  });

  // Development mode check
  const isDevelopment = process.env.NODE_ENV === 'development';

  // API Keys
  const [apiKeys, setApiKeys] = React.useState<APIKey[]>([
    {
      id: '1',
      name: 'OpenAI GPT-4',
      service: 'openai',
      status: 'active',
      lastUsed: '2024-01-20',
      masked: 'sk-...h4k2'
    },
    {
      id: '2',
      name: 'DeepSeek API',
      service: 'deepseek',
      status: 'active',
      lastUsed: '2024-01-19',
      masked: 'ds-...x7m9'
    },
    {
      id: '3',
      name: 'Anthropic Claude',
      service: 'anthropic',
      status: 'inactive',
      lastUsed: '2024-01-15',
      masked: 'sk-...p3n8'
    }
  ]);

  // Security Settings
  const [securitySettings, setSecuritySettings] = React.useState({
    executionMode: 'real', // Changed to 'real' for actual tool execution
    requireConfirmation: true,
    logLevel: 'info',
    encryptData: true,
    sessionTimeout: 60
  });

  // Tool Configuration
  const [toolSettings, setToolSettings] = React.useState({
    maxConcurrent: 5,
    defaultTimeout: 300,
    autoInstall: true,
    wslIntegration: true,
    toolPath: '/usr/bin'
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
      case 'connected': return 'text-green-500';
      case 'inactive':
      case 'disconnected': return 'text-yellow-500';
      case 'expired':
      case 'error': return 'text-red-500';
      default: return 'text-gray-500';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active':
      case 'connected': return <CheckCircle className="h-3 w-3" />;
      case 'inactive':
      case 'disconnected': return <AlertTriangle className="h-3 w-3" />;
      case 'expired':
      case 'error': return <AlertTriangle className="h-3 w-3" />;
      default: return <AlertTriangle className="h-3 w-3" />;
    }
  };

  const handleSaveSettings = async () => {
    console.log('💾 Saving settings...');

    // Save to localStorage for persistence
    const settingsToSave = {
      general: generalSettings,
      backend: isDevelopment ? backendConfig : undefined,
      security: securitySettings,
      apiKeys: apiKeys.map(key => ({ ...key, masked: key.masked })) // Don't save actual keys
    };

    try {
      localStorage.setItem('nexusscan-settings', JSON.stringify(settingsToSave));
      console.log('✅ Settings saved to localStorage');

      // Send execution mode to backend
      try {
        const response = await fetch('http://ec2-3-89-91-209.compute-1.amazonaws.com:8000/api/settings/execution-mode', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ execution_mode: securitySettings.executionMode })
        });

        if (response.ok) {
          console.log('✅ Execution mode updated on backend:', securitySettings.executionMode);
        } else {
          console.warn('⚠️ Failed to update backend execution mode');
        }
      } catch (backendError) {
        console.warn('⚠️ Backend not available for execution mode update:', backendError);
      }

      // Show success feedback
      alert('Settings saved successfully!');
    } catch (error) {
      console.error('❌ Failed to save settings:', error);
      alert('Failed to save settings. Please try again.');
    }
  };

  const handleResetSettings = () => {
    console.log('🔄 Resetting settings...');

    if (confirm('Are you sure you want to reset all settings to defaults? This cannot be undone.')) {
      // Reset to defaults
      setGeneralSettings({
        theme: 'dark',
        autoSave: true,
        notifications: true,
        autoUpdates: true,
        language: 'en'
      });

      setSecuritySettings({
        executionMode: 'real', // Default to real mode for functional tools
        requireConfirmation: true,
        logLevel: 'info',
        encryptData: true,
        auditLog: true
      });

      // Clear saved settings
      localStorage.removeItem('nexusscan-settings');
      console.log('✅ Settings reset to defaults');
      alert('Settings reset to defaults!');
    }
  };

  const handleTestConnection = () => {
    console.log('Testing backend connection...');
  };

  const handleAddApiKey = () => {
    console.log('Adding new API key...');
  };

  const handleExportSettings = () => {
    console.log('Exporting settings...');
  };

  const handleImportSettings = () => {
    console.log('Importing settings...');
  };

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Settings</h1>
          <p className="text-muted-foreground">
            Configure application preferences and integrations
          </p>
        </div>
        <div className="flex items-center gap-3">
          <Button variant="outline" onClick={handleExportSettings}>
            <Download className="mr-2 h-4 w-4" />
            Export
          </Button>
          <Button variant="outline" onClick={handleImportSettings}>
            <Upload className="mr-2 h-4 w-4" />
            Import
          </Button>
          <Button onClick={handleSaveSettings}>
            <Save className="mr-2 h-4 w-4" />
            Save Changes
          </Button>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className={`grid w-full ${isDevelopment ? 'grid-cols-5' : 'grid-cols-4'}`}>
          <TabsTrigger value="general">
            <Settings className="mr-2 h-4 w-4" />
            General
          </TabsTrigger>
          {isDevelopment && (
            <TabsTrigger value="backend">
              <Server className="mr-2 h-4 w-4" />
              Backend
            </TabsTrigger>
          )}
          <TabsTrigger value="security">
            <Shield className="mr-2 h-4 w-4" />
            Security
          </TabsTrigger>
          <TabsTrigger value="tools">
            <Cpu className="mr-2 h-4 w-4" />
            Tools
          </TabsTrigger>
          <TabsTrigger value="api-keys">
            <Key className="mr-2 h-4 w-4" />
            API Keys
          </TabsTrigger>
        </TabsList>

        {/* General Settings */}
        <TabsContent value="general" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Appearance</CardTitle>
              <CardDescription>Customize the application appearance</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-2">
                  <Label>Theme</Label>
                  <select className="w-full px-3 py-2 text-sm border rounded-md">
                    <option value="dark">Dark</option>
                    <option value="light">Light</option>
                    <option value="system">System</option>
                  </select>
                </div>
                <div className="space-y-2">
                  <Label>Language</Label>
                  <select className="w-full px-3 py-2 text-sm border rounded-md">
                    <option value="en">English</option>
                    <option value="es">Español</option>
                    <option value="fr">Français</option>
                  </select>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Preferences</CardTitle>
              <CardDescription>Application behavior settings</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <Label>Auto-save</Label>
                  <p className="text-sm text-muted-foreground">Automatically save changes</p>
                </div>
                <input type="checkbox" checked={generalSettings.autoSave} readOnly />
              </div>
              
              <div className="flex items-center justify-between">
                <div>
                  <Label>Notifications</Label>
                  <p className="text-sm text-muted-foreground">Show desktop notifications</p>
                </div>
                <input type="checkbox" checked={generalSettings.notifications} readOnly />
              </div>
              
              <div className="flex items-center justify-between">
                <div>
                  <Label>Auto-updates</Label>
                  <p className="text-sm text-muted-foreground">Automatically check for updates</p>
                </div>
                <input type="checkbox" checked={generalSettings.autoUpdates} readOnly />
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Backend Settings - Only show in development */}
        {isDevelopment && (
          <TabsContent value="backend" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Backend Connection</CardTitle>
              <CardDescription>Configure connection to NexusScan backend</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center gap-3 p-3 rounded-lg border">
                <div className="h-8 w-8 rounded-full bg-primary/10 flex items-center justify-center">
                  <Server className="h-4 w-4" />
                </div>
                <div className="flex-1">
                  <p className="text-sm font-medium">Backend Status</p>
                  <p className="text-xs text-muted-foreground">{backendConfig.url}</p>
                </div>
                <Badge variant="outline" className={getStatusColor(backendConfig.status)}>
                  {getStatusIcon(backendConfig.status)}
                  {backendConfig.status}
                </Badge>
                <Button size="sm" variant="outline" onClick={handleTestConnection}>
                  Test Connection
                </Button>
              </div>

              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-2">
                  <Label>Backend URL</Label>
                  <Input 
                    value={backendConfig.url}
                    onChange={(e) => setBackendConfig(prev => ({ ...prev, url: e.target.value }))}
                  />
                </div>
                <div className="space-y-2">
                  <Label>Timeout (ms)</Label>
                  <Input 
                    type="number"
                    value={backendConfig.timeout}
                    onChange={(e) => setBackendConfig(prev => ({ ...prev, timeout: parseInt(e.target.value) }))}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label>Connection Retries</Label>
                <Input 
                  type="number"
                  value={backendConfig.retries}
                  onChange={(e) => setBackendConfig(prev => ({ ...prev, retries: parseInt(e.target.value) }))}
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        )}

        {/* Security Settings */}
        <TabsContent value="security" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Execution Security</CardTitle>
              <CardDescription>Configure tool execution and safety settings</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Execution Mode - Now visible and functional */}
              <div className="space-y-2">
                <Label>Execution Mode</Label>
                <select
                  className="w-full px-3 py-2 text-sm border rounded-md"
                  value={securitySettings.executionMode}
                  onChange={(e) => setSecuritySettings(prev => ({ ...prev, executionMode: e.target.value }))}
                >
                  <option value="simulation">Simulation (Safe)</option>
                  <option value="real">Real Execution</option>
                </select>
                <p className="text-xs text-muted-foreground">
                  <strong>Real mode</strong>: Executes actual security tools with real results.
                  <strong>Simulation mode</strong>: Provides safe, educational outputs for learning.
                </p>
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <Label>Require Confirmation</Label>
                  <p className="text-sm text-muted-foreground">Confirm before executing potentially dangerous operations</p>
                </div>
                <input 
                  type="checkbox" 
                  checked={securitySettings.requireConfirmation}
                  onChange={(e) => setSecuritySettings(prev => ({ ...prev, requireConfirmation: e.target.checked }))}
                />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <Label>Encrypt Data</Label>
                  <p className="text-sm text-muted-foreground">Encrypt sensitive data at rest</p>
                </div>
                <input 
                  type="checkbox" 
                  checked={securitySettings.encryptData}
                  onChange={(e) => setSecuritySettings(prev => ({ ...prev, encryptData: e.target.checked }))}
                />
              </div>

              <div className="space-y-2">
                <Label>Session Timeout (minutes)</Label>
                <Input 
                  type="number"
                  value={securitySettings.sessionTimeout}
                  onChange={(e) => setSecuritySettings(prev => ({ ...prev, sessionTimeout: parseInt(e.target.value) }))}
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Tools Settings */}
        <TabsContent value="tools" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Tool Configuration</CardTitle>
              <CardDescription>Configure security tool execution settings</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-2">
                  <Label>Max Concurrent Tools</Label>
                  <Input 
                    type="number"
                    value={toolSettings.maxConcurrent}
                    onChange={(e) => setToolSettings(prev => ({ ...prev, maxConcurrent: parseInt(e.target.value) }))}
                  />
                </div>
                <div className="space-y-2">
                  <Label>Default Timeout (seconds)</Label>
                  <Input 
                    type="number"
                    value={toolSettings.defaultTimeout}
                    onChange={(e) => setToolSettings(prev => ({ ...prev, defaultTimeout: parseInt(e.target.value) }))}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label>Tool Installation Path</Label>
                <Input 
                  value={toolSettings.toolPath}
                  onChange={(e) => setToolSettings(prev => ({ ...prev, toolPath: e.target.value }))}
                />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <Label>Auto-install Tools</Label>
                  <p className="text-sm text-muted-foreground">Automatically install missing security tools</p>
                </div>
                <input 
                  type="checkbox" 
                  checked={toolSettings.autoInstall}
                  onChange={(e) => setToolSettings(prev => ({ ...prev, autoInstall: e.target.checked }))}
                />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <Label>WSL Integration</Label>
                  <p className="text-sm text-muted-foreground">Enable Windows Subsystem for Linux integration</p>
                </div>
                <input 
                  type="checkbox" 
                  checked={toolSettings.wslIntegration}
                  onChange={(e) => setToolSettings(prev => ({ ...prev, wslIntegration: e.target.checked }))}
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* API Keys */}
        <TabsContent value="api-keys" className="space-y-6">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>AI Service API Keys</CardTitle>
                  <CardDescription>Manage API keys for AI services</CardDescription>
                </div>
                <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setShowApiKeys(!showApiKeys)}
                  >
                    {showApiKeys ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                    {showApiKeys ? 'Hide' : 'Show'} Keys
                  </Button>
                  <Button size="sm" onClick={handleAddApiKey}>
                    <Key className="mr-2 h-4 w-4" />
                    Add Key
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {apiKeys.map((apiKey) => (
                  <div key={apiKey.id} className="flex items-center gap-4 p-4 rounded-lg border">
                    <div className="h-10 w-10 rounded-lg bg-primary/10 flex items-center justify-center">
                      <Key className="h-4 w-4" />
                    </div>
                    
                    <div className="flex-1 space-y-1">
                      <div className="flex items-center gap-3">
                        <p className="text-sm font-medium">{apiKey.name}</p>
                        <Badge variant="outline" className={getStatusColor(apiKey.status)}>
                          {getStatusIcon(apiKey.status)}
                          {apiKey.status}
                        </Badge>
                      </div>
                      <p className="text-xs text-muted-foreground">
                        Key: {showApiKeys ? 'sk-1234567890abcdef...' : apiKey.masked}
                        {apiKey.lastUsed && ` • Last used: ${apiKey.lastUsed}`}
                      </p>
                    </div>

                    <div className="flex items-center gap-2">
                      <Button size="sm" variant="outline">
                        Edit
                      </Button>
                      <Button size="sm" variant="outline">
                        Test
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Action Buttons */}
      <div className="flex items-center justify-end gap-3 pt-6 border-t">
        <Button variant="outline" onClick={handleResetSettings}>
          <RotateCcw className="mr-2 h-4 w-4" />
          Reset to Defaults
        </Button>
        <Button onClick={handleSaveSettings}>
          <Save className="mr-2 h-4 w-4" />
          Save All Changes
        </Button>
      </div>
    </div>
  );
}