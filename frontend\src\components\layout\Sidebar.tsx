/**
 * Sidebar Navigation Component - Clean Version
 * Navigation for NexusScan Desktop Application
 */
import React from 'react'
import { Link } from 'react-router-dom'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
  BarChart3,
  Briefcase,
  Network,
  Globe,
  Shield,
  Key,
  Lock,
  Zap,
  Brain,
  Target,
  Activity,
  FileText,
  Settings,
  ChevronDown,
  ChevronRight,
  Layers,
  Car,
  Bomb
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { useBackendStore } from '@/stores/backend-store'

interface NavItem {
  id: string
  label: string
  path: string
  icon: React.ReactNode
  badge?: string | number
  children?: NavItem[]
}

interface SidebarProps {
  isOpen: boolean
  currentPath: string
  className?: string
}

export function Sidebar({ isOpen, currentPath, className }: SidebarProps) {
  const { status: backendStatus } = useBackendStore()
  const [expandedSections, setExpandedSections] = React.useState<Set<string>>(new Set(['tools']))
  const navRef = React.useRef<HTMLElement>(null)
  
  const navigationItems: NavItem[] = [
    {
      id: 'dashboard',
      label: 'Dashboard',
      path: '/dashboard',
      icon: <BarChart3 className="h-4 w-4" />,
    },
    {
      id: 'campaigns',
      label: 'Campaigns',
      path: '/campaigns',
      icon: <Briefcase className="h-4 w-4" />,
    },
    {
      id: 'tools',
      label: 'Security Tools',
      path: '/tools',
      icon: <Layers className="h-4 w-4" />,
      badge: backendStatus.tools?.operational || 0,
      children: [
        {
          id: 'network-scanning',
          label: 'Network Scanning',
          path: '/tools/network-scanning',
          icon: <Network className="h-4 w-4" />,
        },
        {
          id: 'web-testing',
          label: 'Web Testing',
          path: '/tools/web-testing',
          icon: <Globe className="h-4 w-4" />,
        },
        {
          id: 'vulnerability-assessment',
          label: 'Vulnerability Assessment',
          path: '/tools/vulnerability-assessment',
          icon: <Shield className="h-4 w-4" />,
        },
        {
          id: 'password-tools',
          label: 'Password Tools',
          path: '/tools/password-tools',
          icon: <Key className="h-4 w-4" />,
        },
        {
          id: 'ssl-testing',
          label: 'SSL Testing',
          path: '/tools/ssl-testing',
          icon: <Lock className="h-4 w-4" />,
        },
        {
          id: 'exploitation',
          label: 'Exploitation',
          path: '/tools/exploitation',
          icon: <Bomb className="h-4 w-4" />,
        }
      ]
    },
    {
      id: 'ferrari',
      label: 'Ferrari AI',
      path: '/ferrari',
      icon: <Car className="h-4 w-4" />,
      children: [
        {
          id: 'orchestrator',
          label: 'Multi-Stage Orchestrator',
          path: '/ferrari/orchestrator',
          icon: <Brain className="h-4 w-4" />,
        },
        {
          id: 'creative-exploits',
          label: 'Creative Exploits',
          path: '/ferrari/creative-exploits',
          icon: <Target className="h-4 w-4" />,
        },
        {
          id: 'behavioral-analysis',
          label: 'Behavioral Analysis',
          path: '/ferrari/behavioral-analysis',
          icon: <Activity className="h-4 w-4" />,
        },
        {
          id: 'ai-proxy',
          label: 'AI Proxy',
          path: '/ferrari/ai-proxy',
          icon: <Network className="h-4 w-4" />,
        }
      ]
    },
    {
      id: 'reports',
      label: 'Reports',
      path: '/reports',
      icon: <FileText className="h-4 w-4" />,
    },
    {
      id: 'settings',
      label: 'Settings',
      path: '/settings',
      icon: <Settings className="h-4 w-4" />,
    }
  ]
  
  const toggleSection = (sectionId: string) => {
    const newExpanded = new Set(expandedSections)
    if (newExpanded.has(sectionId)) {
      newExpanded.delete(sectionId)
    } else {
      newExpanded.add(sectionId)
    }
    setExpandedSections(newExpanded)
  }

  // Keyboard navigation support
  React.useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (!navRef.current || !isOpen) return

      const focusableElements = navRef.current.querySelectorAll(
        'button, a[href]'
      ) as NodeListOf<HTMLElement>

      const currentIndex = Array.from(focusableElements).indexOf(
        document.activeElement as HTMLElement
      )

      switch (event.key) {
        case 'ArrowDown':
          event.preventDefault()
          const nextIndex = (currentIndex + 1) % focusableElements.length
          focusableElements[nextIndex]?.focus()
          break
        case 'ArrowUp':
          event.preventDefault()
          const prevIndex = currentIndex <= 0 ? focusableElements.length - 1 : currentIndex - 1
          focusableElements[prevIndex]?.focus()
          break
        case 'Home':
          event.preventDefault()
          focusableElements[0]?.focus()
          break
        case 'End':
          event.preventDefault()
          focusableElements[focusableElements.length - 1]?.focus()
          break
      }
    }

    if (isOpen) {
      document.addEventListener('keydown', handleKeyDown)
      return () => document.removeEventListener('keydown', handleKeyDown)
    }
  }, [isOpen, expandedSections])
  
  const isItemActive = (item: NavItem): boolean => {
    return currentPath.startsWith(item.path) && item.path !== '/'
  }
  
  const renderNavItem = (item: NavItem, level: number = 0) => {
    const isActive = isItemActive(item)
    const hasChildren = item.children && item.children.length > 0
    const isExpanded = expandedSections.has(item.id)
    const indentClass = level > 0 ? 'ml-6' : ''

    return (
      <div key={item.id} className={cn('space-y-1', indentClass)}>
        {hasChildren ? (
          <div className="flex items-center w-full group">
            {/* Main navigation button - navigates to the main page */}
            <Link to={item.path} className="flex-1">
              <Button
                variant="ghost"
                className={cn(
                  'w-full justify-start rounded-r-none transition-all duration-200',
                  'hover:bg-accent/80 hover:text-accent-foreground',
                  'focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2',
                  isActive ? 'bg-accent text-accent-foreground border-l-2 border-l-primary' : '',
                  level > 0 ? 'text-sm' : ''
                )}
              >
                <div className="flex items-center gap-3 w-full">
                  <span className={cn(
                    'transition-colors duration-200',
                    isActive ? 'text-primary' : 'text-muted-foreground group-hover:text-foreground'
                  )}>
                    {item.icon}
                  </span>
                  <span className="flex-1 text-left font-medium">{item.label}</span>
                  {item.badge && (
                    <Badge
                      variant="secondary"
                      className={cn(
                        'ml-auto transition-colors duration-200',
                        isActive ? 'bg-primary/10 text-primary' : ''
                      )}
                    >
                      {item.badge}
                    </Badge>
                  )}
                </div>
              </Button>
            </Link>
            {/* Dropdown toggle button */}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => toggleSection(item.id)}
              className={cn(
                'px-2 rounded-l-none border-l border-border/50 transition-all duration-200',
                'hover:bg-accent/80 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring',
                isActive ? 'bg-accent' : ''
              )}
              aria-label={`${isExpanded ? 'Collapse' : 'Expand'} ${item.label} section`}
              aria-expanded={isExpanded}
            >
              <span className={cn(
                'transition-transform duration-200',
                isExpanded ? 'rotate-0' : 'rotate-0'
              )}>
                {isExpanded ? (
                  <ChevronDown className="h-4 w-4" />
                ) : (
                  <ChevronRight className="h-4 w-4" />
                )}
              </span>
            </Button>
          </div>
        ) : (
          <Link to={item.path}>
            <Button
              variant="ghost"
              className={cn(
                'w-full justify-start transition-all duration-200',
                'hover:bg-accent/80 hover:text-accent-foreground',
                'focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2',
                isActive ? 'bg-accent text-accent-foreground border-l-2 border-l-primary' : '',
                level > 0 ? 'text-sm' : ''
              )}
            >
              <div className="flex items-center gap-3 w-full">
                <span className={cn(
                  'transition-colors duration-200',
                  isActive ? 'text-primary' : 'text-muted-foreground hover:text-foreground'
                )}>
                  {item.icon}
                </span>
                <span className="flex-1 text-left font-medium">{item.label}</span>
                {item.badge && (
                  <Badge
                    variant="secondary"
                    className={cn(
                      'ml-auto transition-colors duration-200',
                      isActive ? 'bg-primary/10 text-primary' : ''
                    )}
                  >
                    {item.badge}
                  </Badge>
                )}
              </div>
            </Button>
          </Link>
        )}

        {hasChildren && (
          <div
            className={cn(
              'ml-4 space-y-1 overflow-hidden transition-all duration-300 ease-in-out',
              isExpanded ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0'
            )}
          >
            {item.children?.map(child => renderNavItem(child, level + 1))}
          </div>
        )}
      </div>
    )
  }
  
  if (!isOpen) {
    return null
  }
  
  return (
    <aside className={cn(
      'w-64 flex-shrink-0 border-r border-border bg-card',
      'flex flex-col h-full',
      className
    )}>
      {/* Logo */}
      <div className="p-4 border-b border-border">
        <div className="flex items-center gap-3">
          <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
            <span className="text-white font-bold text-sm">N</span>
          </div>
          <div>
            <h2 className="font-semibold text-foreground">NexusScan</h2>
            <p className="text-xs text-muted-foreground">Desktop</p>
          </div>
        </div>
      </div>
      
      {/* Navigation */}
      <nav
        ref={navRef}
        className="flex-1 p-4 space-y-2 overflow-y-auto"
        role="navigation"
        aria-label="Main navigation"
      >
        {navigationItems.map(item => renderNavItem(item))}
      </nav>
      
      {/* Status */}
      <div className="p-4 border-t border-border">
        <div className="space-y-2">
          <div className="flex items-center justify-between text-xs">
            <span className="text-muted-foreground">Backend</span>
            <Badge 
              variant={backendStatus.connected ? 'default' : 'destructive'}
              className="text-xs"
            >
              {backendStatus.connected ? 'Online' : 'Offline'}
            </Badge>
          </div>
          
          {backendStatus.tools && (
            <div className="flex items-center justify-between text-xs">
              <span className="text-muted-foreground">Tools</span>
              <Badge variant="outline" className="text-xs">
                {backendStatus.tools.operational}/{backendStatus.tools.total}
              </Badge>
            </div>
          )}
        </div>
      </div>
    </aside>
  )
}