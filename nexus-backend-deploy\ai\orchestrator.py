#!/usr/bin/env python3
"""
Multi-Model AI Orchestrator
Advanced AI service orchestration with intelligent routing, load balancing,
failover, and performance optimization across multiple AI providers
"""

import asyncio
import time
import logging
import json
import hashlib
from typing import Any, Dict, List, Optional, Tuple, Union, Callable, AsyncGenerator
from dataclasses import dataclass, field, asdict
from datetime import datetime, timedelta
from enum import Enum
from abc import ABC, abstractmethod
import aiohttp
import random
from concurrent.futures import ThreadPoolExecutor

logger = logging.getLogger(__name__)

class AIProvider(Enum):
    """AI service providers"""
    OPENAI = "openai"
    DEEPSEEK = "deepseek"
    ANTHROPIC = "anthropic"
    COHERE = "cohere"
    HUGGINGFACE = "huggingface"
    LOCAL_MODEL = "local"

class TaskType(Enum):
    """AI task types"""
    VULNERABILITY_ANALYSIS = "vulnerability_analysis"
    EXPLOIT_GENERATION = "exploit_generation"
    CODE_ANALYSIS = "code_analysis"
    THREAT_INTELLIGENCE = "threat_intelligence"
    REPORT_GENERATION = "report_generation"
    REMEDIATION_ADVICE = "remediation_advice"
    PAYLOAD_OPTIMIZATION = "payload_optimization"
    BEHAVIORAL_ANALYSIS = "behavioral_analysis"

class RoutingStrategy(Enum):
    """AI routing strategies"""
    ROUND_ROBIN = "round_robin"
    LOAD_BASED = "load_based"
    PERFORMANCE_BASED = "performance_based"
    COST_OPTIMIZED = "cost_optimized"
    QUALITY_OPTIMIZED = "quality_optimized"
    CAPABILITY_BASED = "capability_based"

@dataclass
class AICapability:
    """AI provider capability definition"""
    provider: AIProvider
    task_types: List[TaskType]
    max_tokens: int
    supports_streaming: bool = False
    supports_function_calling: bool = False
    supports_vision: bool = False
    supports_code_execution: bool = False
    cost_per_1k_tokens: float = 0.0
    quality_score: float = 0.8
    reliability_score: float = 0.9

@dataclass
class AIRequest:
    """AI service request"""
    task_type: TaskType
    prompt: str
    context: Optional[Dict[str, Any]] = None
    max_tokens: int = 1000
    temperature: float = 0.7
    streaming: bool = False
    priority: int = 1  # 1=low, 2=medium, 3=high
    deadline: Optional[datetime] = None
    quality_threshold: float = 0.7
    max_cost: Optional[float] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return asdict(self)

@dataclass
class AIResponse:
    """AI service response"""
    request_id: str
    provider: AIProvider
    task_type: TaskType
    content: str
    metadata: Dict[str, Any] = field(default_factory=dict)
    tokens_used: int = 0
    cost: float = 0.0
    latency: float = 0.0
    quality_score: float = 0.0
    confidence: float = 0.0
    timestamp: datetime = field(default_factory=datetime.now)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        result = asdict(self)
        result['timestamp'] = self.timestamp.isoformat()
        return result

@dataclass
class ProviderMetrics:
    """Provider performance metrics"""
    provider: AIProvider
    total_requests: int = 0
    successful_requests: int = 0
    failed_requests: int = 0
    total_latency: float = 0.0
    total_cost: float = 0.0
    total_tokens: int = 0
    last_request: Optional[datetime] = None
    current_load: int = 0
    error_rate_window: List[bool] = field(default_factory=list)
    
    @property
    def success_rate(self) -> float:
        """Calculate success rate"""
        if self.total_requests == 0:
            return 1.0
        return self.successful_requests / self.total_requests
    
    @property
    def avg_latency(self) -> float:
        """Calculate average latency"""
        if self.successful_requests == 0:
            return 0.0
        return self.total_latency / self.successful_requests
    
    @property
    def avg_cost_per_request(self) -> float:
        """Calculate average cost per request"""
        if self.successful_requests == 0:
            return 0.0
        return self.total_cost / self.successful_requests
    
    @property
    def recent_error_rate(self) -> float:
        """Calculate recent error rate from sliding window"""
        if not self.error_rate_window:
            return 0.0
        failures = sum(1 for failed in self.error_rate_window if failed)
        return failures / len(self.error_rate_window)

class BaseAIProvider(ABC):
    """Base class for AI providers"""
    
    def __init__(self, provider: AIProvider, config: Dict[str, Any]):
        self.provider = provider
        self.config = config
        self.capabilities = self._define_capabilities()
        self.metrics = ProviderMetrics(provider)
    
    @abstractmethod
    def _define_capabilities(self) -> AICapability:
        """Define provider capabilities"""
        pass
    
    @abstractmethod
    async def process_request(self, request: AIRequest) -> AIResponse:
        """Process AI request"""
        pass
    
    @abstractmethod
    async def health_check(self) -> bool:
        """Check provider health"""
        pass
    
    def can_handle_request(self, request: AIRequest) -> bool:
        """Check if provider can handle request"""
        return (
            request.task_type in self.capabilities.task_types and
            request.max_tokens <= self.capabilities.max_tokens and
            (not request.streaming or self.capabilities.supports_streaming) and
            (request.max_cost is None or 
             self.capabilities.cost_per_1k_tokens * request.max_tokens / 1000 <= request.max_cost)
        )
    
    def update_metrics(self, success: bool, latency: float, tokens: int = 0, cost: float = 0.0):
        """Update provider metrics"""
        self.metrics.total_requests += 1
        self.metrics.last_request = datetime.now()
        
        if success:
            self.metrics.successful_requests += 1
            self.metrics.total_latency += latency
            self.metrics.total_tokens += tokens
            self.metrics.total_cost += cost
        else:
            self.metrics.failed_requests += 1
        
        # Update sliding window for error rate
        self.metrics.error_rate_window.append(not success)
        if len(self.metrics.error_rate_window) > 100:  # Keep last 100 requests
            self.metrics.error_rate_window.pop(0)

class OpenAIProvider(BaseAIProvider):
    """OpenAI GPT provider"""
    
    def _define_capabilities(self) -> AICapability:
        return AICapability(
            provider=AIProvider.OPENAI,
            task_types=[
                TaskType.VULNERABILITY_ANALYSIS,
                TaskType.EXPLOIT_GENERATION,
                TaskType.CODE_ANALYSIS,
                TaskType.THREAT_INTELLIGENCE,
                TaskType.REPORT_GENERATION,
                TaskType.REMEDIATION_ADVICE,
                TaskType.PAYLOAD_OPTIMIZATION
            ],
            max_tokens=4096,
            supports_streaming=True,
            supports_function_calling=True,
            supports_vision=True,
            cost_per_1k_tokens=0.03,
            quality_score=0.95,
            reliability_score=0.98
        )
    
    async def process_request(self, request: AIRequest) -> AIResponse:
        """Process request with OpenAI"""
        start_time = time.time()
        request_id = hashlib.md5(f"{request.prompt}{time.time()}".encode()).hexdigest()
        
        try:
            # Simulate OpenAI API call
            await asyncio.sleep(random.uniform(0.5, 2.0))  # Simulate network latency
            
            # Mock response generation
            response_content = self._generate_mock_response(request)
            tokens_used = len(response_content.split()) * 1.3  # Rough token estimation
            cost = (tokens_used / 1000) * self.capabilities.cost_per_1k_tokens
            
            latency = time.time() - start_time
            self.update_metrics(True, latency, int(tokens_used), cost)
            
            return AIResponse(
                request_id=request_id,
                provider=self.provider,
                task_type=request.task_type,
                content=response_content,
                tokens_used=int(tokens_used),
                cost=cost,
                latency=latency,
                quality_score=self.capabilities.quality_score,
                confidence=random.uniform(0.8, 0.95),
                metadata={
                    "model": "gpt-4",
                    "temperature": request.temperature,
                    "max_tokens": request.max_tokens
                }
            )
            
        except Exception as e:
            latency = time.time() - start_time
            self.update_metrics(False, latency)
            logger.error(f"OpenAI request failed: {e}")
            raise
    
    async def health_check(self) -> bool:
        """Check OpenAI health"""
        try:
            # Simulate health check
            await asyncio.sleep(0.1)
            return self.metrics.recent_error_rate < 0.5
        except:
            return False
    
    def _generate_mock_response(self, request: AIRequest) -> str:
        """Generate mock response for testing"""
        if request.task_type == TaskType.VULNERABILITY_ANALYSIS:
            return """
            Based on the security analysis, I've identified the following vulnerabilities:
            
            1. SQL Injection vulnerability in the login form
               - Severity: High
               - Impact: Database compromise possible
               - Recommendation: Use parameterized queries
            
            2. Cross-Site Scripting (XSS) in user input fields
               - Severity: Medium
               - Impact: Session hijacking possible
               - Recommendation: Implement input sanitization
            """
        elif request.task_type == TaskType.EXPLOIT_GENERATION:
            return """
            # SQL Injection Exploit Payload
            
            ## Basic Union-based SQL Injection:
            ' UNION SELECT 1,2,3,4,username,password,7,8 FROM users--
            
            ## Time-based Blind SQL Injection:
            '; IF (1=1) WAITFOR DELAY '0:0:5'--
            
            ## Boolean-based Blind SQL Injection:
            ' AND (SELECT COUNT(*) FROM users WHERE username='admin')>0--
            
            Note: These payloads are for authorized penetration testing only.
            """
        else:
            return f"Generated response for {request.task_type.value} using OpenAI GPT-4"

class DeepSeekProvider(BaseAIProvider):
    """DeepSeek provider"""
    
    def _define_capabilities(self) -> AICapability:
        return AICapability(
            provider=AIProvider.DEEPSEEK,
            task_types=[
                TaskType.CODE_ANALYSIS,
                TaskType.VULNERABILITY_ANALYSIS,
                TaskType.EXPLOIT_GENERATION,
                TaskType.PAYLOAD_OPTIMIZATION,
                TaskType.BEHAVIORAL_ANALYSIS
            ],
            max_tokens=8192,
            supports_streaming=True,
            supports_function_calling=False,
            cost_per_1k_tokens=0.014,
            quality_score=0.88,
            reliability_score=0.92
        )
    
    async def process_request(self, request: AIRequest) -> AIResponse:
        """Process request with DeepSeek"""
        start_time = time.time()
        request_id = hashlib.md5(f"deepseek_{request.prompt}{time.time()}".encode()).hexdigest()
        
        try:
            # Simulate DeepSeek API call
            await asyncio.sleep(random.uniform(0.8, 2.5))
            
            response_content = self._generate_mock_response(request)
            tokens_used = len(response_content.split()) * 1.2
            cost = (tokens_used / 1000) * self.capabilities.cost_per_1k_tokens
            
            latency = time.time() - start_time
            self.update_metrics(True, latency, int(tokens_used), cost)
            
            return AIResponse(
                request_id=request_id,
                provider=self.provider,
                task_type=request.task_type,
                content=response_content,
                tokens_used=int(tokens_used),
                cost=cost,
                latency=latency,
                quality_score=self.capabilities.quality_score,
                confidence=random.uniform(0.75, 0.9),
                metadata={
                    "model": "deepseek-coder",
                    "temperature": request.temperature
                }
            )
            
        except Exception as e:
            latency = time.time() - start_time
            self.update_metrics(False, latency)
            logger.error(f"DeepSeek request failed: {e}")
            raise
    
    async def health_check(self) -> bool:
        """Check DeepSeek health"""
        try:
            await asyncio.sleep(0.1)
            return self.metrics.recent_error_rate < 0.3
        except:
            return False
    
    def _generate_mock_response(self, request: AIRequest) -> str:
        """Generate mock response for testing"""
        if request.task_type == TaskType.CODE_ANALYSIS:
            return """
            # Code Security Analysis
            
            ## Identified Issues:
            
            1. **Buffer Overflow Risk** (Line 45)
               - Function: `strcpy()` without bounds checking
               - Risk Level: Critical
               - Fix: Use `strncpy()` or `strlcpy()`
            
            2. **Use After Free** (Line 78)
               - Variable: `user_data` pointer
               - Risk Level: High
               - Fix: Set pointer to NULL after free()
            
            3. **Integer Overflow** (Line 102)
               - Expression: `size * count`
               - Risk Level: Medium
               - Fix: Check for overflow before multiplication
            """
        else:
            return f"Generated response for {request.task_type.value} using DeepSeek"

class AnthropicProvider(BaseAIProvider):
    """Anthropic Claude provider"""
    
    def _define_capabilities(self) -> AICapability:
        return AICapability(
            provider=AIProvider.ANTHROPIC,
            task_types=[
                TaskType.VULNERABILITY_ANALYSIS,
                TaskType.REPORT_GENERATION,
                TaskType.REMEDIATION_ADVICE,
                TaskType.THREAT_INTELLIGENCE,
                TaskType.BEHAVIORAL_ANALYSIS
            ],
            max_tokens=4096,
            supports_streaming=True,
            supports_function_calling=False,
            cost_per_1k_tokens=0.025,
            quality_score=0.92,
            reliability_score=0.95
        )
    
    async def process_request(self, request: AIRequest) -> AIResponse:
        """Process request with Anthropic Claude"""
        start_time = time.time()
        request_id = hashlib.md5(f"claude_{request.prompt}{time.time()}".encode()).hexdigest()
        
        try:
            # Simulate Claude API call
            await asyncio.sleep(random.uniform(0.6, 2.2))
            
            response_content = self._generate_mock_response(request)
            tokens_used = len(response_content.split()) * 1.1
            cost = (tokens_used / 1000) * self.capabilities.cost_per_1k_tokens
            
            latency = time.time() - start_time
            self.update_metrics(True, latency, int(tokens_used), cost)
            
            return AIResponse(
                request_id=request_id,
                provider=self.provider,
                task_type=request.task_type,
                content=response_content,
                tokens_used=int(tokens_used),
                cost=cost,
                latency=latency,
                quality_score=self.capabilities.quality_score,
                confidence=random.uniform(0.82, 0.94),
                metadata={
                    "model": "claude-3-sonnet",
                    "temperature": request.temperature
                }
            )
            
        except Exception as e:
            latency = time.time() - start_time
            self.update_metrics(False, latency)
            logger.error(f"Anthropic request failed: {e}")
            raise
    
    async def health_check(self) -> bool:
        """Check Anthropic health"""
        try:
            await asyncio.sleep(0.1)
            return self.metrics.recent_error_rate < 0.2
        except:
            return False
    
    def _generate_mock_response(self, request: AIRequest) -> str:
        """Generate mock response for testing"""
        if request.task_type == TaskType.REMEDIATION_ADVICE:
            return """
            # Security Remediation Recommendations
            
            ## Immediate Actions (Critical Priority)
            
            1. **Patch SQL Injection Vulnerability**
               - Timeline: Within 24 hours
               - Action: Update database query methods to use parameterized queries
               - Testing: Verify with automated security scans
            
            2. **Implement Input Validation**
               - Timeline: Within 48 hours
               - Action: Add server-side input sanitization
               - Validation: Whitelist approach for all user inputs
            
            ## Medium-term Improvements (1-2 weeks)
            
            1. **Security Headers Implementation**
               - Add Content Security Policy (CSP)
               - Implement HTTP Strict Transport Security (HSTS)
               - Set proper X-Frame-Options
            
            2. **Access Control Review**
               - Audit user permissions
               - Implement principle of least privilege
               - Add multi-factor authentication
            """
        else:
            return f"Generated response for {request.task_type.value} using Claude"

class AIOrchestrator:
    """Multi-model AI orchestrator with intelligent routing"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        self.config = config or {}
        self.providers: Dict[AIProvider, BaseAIProvider] = {}
        self.routing_strategy = RoutingStrategy(
            self.config.get('routing_strategy', 'performance_based')
        )
        
        # Request queue and management
        self.request_queue = asyncio.Queue()
        self.active_requests: Dict[str, AIRequest] = {}
        self.response_cache: Dict[str, AIResponse] = {}
        
        # Circuit breaker settings
        self.circuit_breaker_threshold = self.config.get('circuit_breaker_threshold', 0.7)
        self.circuit_breaker_timeout = self.config.get('circuit_breaker_timeout', 300)
        self.provider_circuit_breakers: Dict[AIProvider, datetime] = {}
        
        # Load balancing
        self.round_robin_counter = 0
        
        # Initialize providers
        self._initialize_providers()
        
        # Start background tasks
        asyncio.create_task(self._process_requests())
        asyncio.create_task(self._health_monitor())
        asyncio.create_task(self._cache_cleanup())
    
    def _initialize_providers(self):
        """Initialize AI providers"""
        provider_configs = self.config.get('providers', {})
        
        # Initialize OpenAI
        if provider_configs.get('openai', {}).get('enabled', True):
            self.providers[AIProvider.OPENAI] = OpenAIProvider(
                AIProvider.OPENAI, 
                provider_configs.get('openai', {})
            )
        
        # Initialize DeepSeek
        if provider_configs.get('deepseek', {}).get('enabled', True):
            self.providers[AIProvider.DEEPSEEK] = DeepSeekProvider(
                AIProvider.DEEPSEEK,
                provider_configs.get('deepseek', {})
            )
        
        # Initialize Anthropic
        if provider_configs.get('anthropic', {}).get('enabled', True):
            self.providers[AIProvider.ANTHROPIC] = AnthropicProvider(
                AIProvider.ANTHROPIC,
                provider_configs.get('anthropic', {})
            )
        
        logger.info(f"Initialized {len(self.providers)} AI providers")
    
    async def process_request(self, request: AIRequest) -> AIResponse:
        """Process AI request with intelligent routing"""
        # Check cache first
        cache_key = self._create_cache_key(request)
        if cache_key in self.response_cache:
            cached_response = self.response_cache[cache_key]
            # Check if cache is still fresh (1 hour)
            if datetime.now() - cached_response.timestamp < timedelta(hours=1):
                logger.debug(f"Cache hit for request: {request.task_type.value}")
                return cached_response
        
        # Route request to best provider
        provider = await self._route_request(request)
        if not provider:
            raise RuntimeError("No available provider for request")
        
        # Process request
        try:
            response = await provider.process_request(request)
            
            # Cache response
            self.response_cache[cache_key] = response
            
            return response
            
        except Exception as e:
            logger.error(f"Request failed with {provider.provider.value}: {e}")
            
            # Try fallback provider
            fallback_provider = await self._get_fallback_provider(request, provider.provider)
            if fallback_provider:
                logger.info(f"Falling back to {fallback_provider.provider.value}")
                try:
                    response = await fallback_provider.process_request(request)
                    self.response_cache[cache_key] = response
                    return response
                except Exception as fallback_error:
                    logger.error(f"Fallback also failed: {fallback_error}")
            
            raise
    
    async def _route_request(self, request: AIRequest) -> Optional[BaseAIProvider]:
        """Route request to optimal provider"""
        available_providers = self._get_available_providers(request)
        
        if not available_providers:
            return None
        
        if self.routing_strategy == RoutingStrategy.ROUND_ROBIN:
            return self._route_round_robin(available_providers)
        elif self.routing_strategy == RoutingStrategy.LOAD_BASED:
            return self._route_load_based(available_providers)
        elif self.routing_strategy == RoutingStrategy.PERFORMANCE_BASED:
            return self._route_performance_based(available_providers)
        elif self.routing_strategy == RoutingStrategy.COST_OPTIMIZED:
            return self._route_cost_optimized(available_providers, request)
        elif self.routing_strategy == RoutingStrategy.QUALITY_OPTIMIZED:
            return self._route_quality_optimized(available_providers)
        elif self.routing_strategy == RoutingStrategy.CAPABILITY_BASED:
            return self._route_capability_based(available_providers, request)
        else:
            return available_providers[0]  # Fallback to first available
    
    def _get_available_providers(self, request: AIRequest) -> List[BaseAIProvider]:
        """Get providers that can handle the request"""
        available = []
        
        for provider in self.providers.values():
            # Check circuit breaker
            if provider.provider in self.provider_circuit_breakers:
                if datetime.now() - self.provider_circuit_breakers[provider.provider] < timedelta(seconds=self.circuit_breaker_timeout):
                    continue
                else:
                    # Reset circuit breaker
                    del self.provider_circuit_breakers[provider.provider]
            
            # Check if provider can handle request
            if provider.can_handle_request(request):
                # Check recent error rate
                if provider.metrics.recent_error_rate < self.circuit_breaker_threshold:
                    available.append(provider)
                else:
                    # Trip circuit breaker
                    self.provider_circuit_breakers[provider.provider] = datetime.now()
                    logger.warning(f"Circuit breaker tripped for {provider.provider.value}")
        
        return available
    
    def _route_round_robin(self, providers: List[BaseAIProvider]) -> BaseAIProvider:
        """Round-robin routing"""
        provider = providers[self.round_robin_counter % len(providers)]
        self.round_robin_counter += 1
        return provider
    
    def _route_load_based(self, providers: List[BaseAIProvider]) -> BaseAIProvider:
        """Route to provider with lowest current load"""
        return min(providers, key=lambda p: p.metrics.current_load)
    
    def _route_performance_based(self, providers: List[BaseAIProvider]) -> BaseAIProvider:
        """Route to provider with best performance"""
        def performance_score(provider: BaseAIProvider) -> float:
            # Combine success rate and latency
            success_weight = 0.6
            latency_weight = 0.4
            
            success_score = provider.metrics.success_rate
            # Normalize latency (lower is better)
            latency_score = 1.0 / (1.0 + provider.metrics.avg_latency)
            
            return success_score * success_weight + latency_score * latency_weight
        
        return max(providers, key=performance_score)
    
    def _route_cost_optimized(self, providers: List[BaseAIProvider], request: AIRequest) -> BaseAIProvider:
        """Route to most cost-effective provider"""
        def estimated_cost(provider: BaseAIProvider) -> float:
            return (request.max_tokens / 1000) * provider.capabilities.cost_per_1k_tokens
        
        return min(providers, key=estimated_cost)
    
    def _route_quality_optimized(self, providers: List[BaseAIProvider]) -> BaseAIProvider:
        """Route to highest quality provider"""
        return max(providers, key=lambda p: p.capabilities.quality_score)
    
    def _route_capability_based(self, providers: List[BaseAIProvider], request: AIRequest) -> BaseAIProvider:
        """Route based on specific capabilities for the request"""
        # Score providers based on how well they match request requirements
        def capability_score(provider: BaseAIProvider) -> float:
            score = 0.0
            
            # Task-specific scoring
            if request.task_type in provider.capabilities.task_types:
                score += 1.0
            
            # Streaming support
            if request.streaming and provider.capabilities.supports_streaming:
                score += 0.3
            
            # Quality threshold
            if provider.capabilities.quality_score >= request.quality_threshold:
                score += 0.5
            
            # Cost consideration
            if request.max_cost:
                estimated_cost = (request.max_tokens / 1000) * provider.capabilities.cost_per_1k_tokens
                if estimated_cost <= request.max_cost:
                    score += 0.2
            
            return score
        
        return max(providers, key=capability_score)
    
    async def _get_fallback_provider(self, request: AIRequest, failed_provider: AIProvider) -> Optional[BaseAIProvider]:
        """Get fallback provider for failed request"""
        available_providers = self._get_available_providers(request)
        
        # Remove the failed provider
        available_providers = [p for p in available_providers if p.provider != failed_provider]
        
        if not available_providers:
            return None
        
        # Return the best available provider
        return self._route_quality_optimized(available_providers)
    
    def _create_cache_key(self, request: AIRequest) -> str:
        """Create cache key for request"""
        key_data = {
            "task_type": request.task_type.value,
            "prompt": request.prompt,
            "context": request.context,
            "temperature": request.temperature,
            "max_tokens": request.max_tokens
        }
        key_str = json.dumps(key_data, sort_keys=True)
        return hashlib.md5(key_str.encode()).hexdigest()
    
    async def _process_requests(self):
        """Background task to process request queue"""
        while True:
            try:
                await asyncio.sleep(0.1)
                # Process any queued requests if needed
                # For now, we process requests synchronously
            except Exception as e:
                logger.error(f"Request processing error: {e}")
    
    async def _health_monitor(self):
        """Background health monitoring"""
        while True:
            try:
                await asyncio.sleep(60)  # Check every minute
                
                for provider in self.providers.values():
                    try:
                        healthy = await provider.health_check()
                        if not healthy:
                            logger.warning(f"Provider {provider.provider.value} failed health check")
                    except Exception as e:
                        logger.error(f"Health check failed for {provider.provider.value}: {e}")
                        
            except Exception as e:
                logger.error(f"Health monitoring error: {e}")
    
    async def _cache_cleanup(self):
        """Background cache cleanup"""
        while True:
            try:
                await asyncio.sleep(3600)  # Cleanup every hour
                
                current_time = datetime.now()
                expired_keys = []
                
                for key, response in self.response_cache.items():
                    if current_time - response.timestamp > timedelta(hours=2):
                        expired_keys.append(key)
                
                for key in expired_keys:
                    del self.response_cache[key]
                
                if expired_keys:
                    logger.info(f"Cleaned up {len(expired_keys)} expired cache entries")
                    
            except Exception as e:
                logger.error(f"Cache cleanup error: {e}")
    
    async def batch_process(self, requests: List[AIRequest]) -> List[AIResponse]:
        """Process multiple requests in parallel"""
        tasks = [self.process_request(request) for request in requests]
        return await asyncio.gather(*tasks, return_exceptions=True)
    
    async def stream_process(self, request: AIRequest) -> AsyncGenerator[str, None]:
        """Process request with streaming response"""
        # Route to provider that supports streaming
        available_providers = [
            p for p in self._get_available_providers(request)
            if p.capabilities.supports_streaming
        ]
        
        if not available_providers:
            raise RuntimeError("No streaming-capable provider available")
        
        provider = self._route_performance_based(available_providers)
        
        # Simulate streaming response
        response = await provider.process_request(request)
        words = response.content.split()
        
        for i, word in enumerate(words):
            yield word + " "
            if i % 5 == 0:  # Yield every 5 words
                await asyncio.sleep(0.1)
    
    def get_provider_stats(self) -> Dict[str, Any]:
        """Get comprehensive provider statistics"""
        stats = {
            "total_providers": len(self.providers),
            "routing_strategy": self.routing_strategy.value,
            "cache_size": len(self.response_cache),
            "circuit_breakers": len(self.provider_circuit_breakers),
            "providers": {}
        }
        
        for provider in self.providers.values():
            provider_stats = {
                "total_requests": provider.metrics.total_requests,
                "success_rate": provider.metrics.success_rate,
                "avg_latency": provider.metrics.avg_latency,
                "avg_cost": provider.metrics.avg_cost_per_request,
                "current_load": provider.metrics.current_load,
                "recent_error_rate": provider.metrics.recent_error_rate,
                "capabilities": asdict(provider.capabilities)
            }
            stats["providers"][provider.provider.value] = provider_stats
        
        return stats
    
    def get_optimization_recommendations(self) -> List[Dict[str, Any]]:
        """Get AI orchestration optimization recommendations"""
        recommendations = []
        
        # Analyze provider performance
        for provider in self.providers.values():
            metrics = provider.metrics
            
            if metrics.recent_error_rate > 0.3:
                recommendations.append({
                    "type": "provider_reliability",
                    "priority": "high",
                    "provider": provider.provider.value,
                    "message": f"High error rate ({metrics.recent_error_rate:.1%}) detected",
                    "suggestion": "Consider reducing load or checking provider status"
                })
            
            if metrics.avg_latency > 5.0:
                recommendations.append({
                    "type": "performance",
                    "priority": "medium",
                    "provider": provider.provider.value,
                    "message": f"High latency ({metrics.avg_latency:.2f}s) detected",
                    "suggestion": "Consider optimizing requests or switching providers"
                })
        
        # Analyze routing strategy
        if len(self.providers) > 1:
            cost_variance = max(p.capabilities.cost_per_1k_tokens for p in self.providers.values()) - \
                           min(p.capabilities.cost_per_1k_tokens for p in self.providers.values())
            
            if cost_variance > 0.02 and self.routing_strategy != RoutingStrategy.COST_OPTIMIZED:
                recommendations.append({
                    "type": "cost_optimization",
                    "priority": "low",
                    "message": f"Significant cost variance ({cost_variance:.3f}) between providers",
                    "suggestion": "Consider using cost-optimized routing strategy"
                })
        
        return recommendations

# Global AI orchestrator instance
ai_orchestrator: Optional[AIOrchestrator] = None

def get_ai_orchestrator() -> AIOrchestrator:
    """Get global AI orchestrator instance"""
    global ai_orchestrator
    
    if ai_orchestrator is None:
        ai_orchestrator = AIOrchestrator()
    
    return ai_orchestrator

def close_ai_orchestrator():
    """Close global AI orchestrator"""
    global ai_orchestrator
    ai_orchestrator = None