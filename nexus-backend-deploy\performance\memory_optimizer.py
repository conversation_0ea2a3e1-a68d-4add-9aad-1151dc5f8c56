"""
Memory Optimization System for NexusScan Desktop
Advanced memory management with leak detection, garbage collection optimization, and resource monitoring.
"""

import asyncio
import logging
import gc
import sys
import time
import threading
import weakref
import tracemalloc
import psutil
from typing import Dict, List, Optional, Any, Callable, Set, Type, Union
from enum import Enum
from datetime import datetime, timed<PERSON>ta
from dataclasses import dataclass, asdict
from pathlib import Path
import json
from collections import defaultdict, deque
import functools
import inspect
from concurrent.futures import ThreadPoolExecutor

logger = logging.getLogger(__name__)


class MemoryIssueType(Enum):
    """Types of memory issues"""
    MEMORY_LEAK = "memory_leak"
    HIGH_USAGE = "high_usage"
    FRAGMENTATION = "fragmentation"
    EXCESSIVE_OBJECTS = "excessive_objects"
    CIRCULAR_REFERENCE = "circular_reference"
    LARGE_ALLOCATION = "large_allocation"
    SLOW_DEALLOCATION = "slow_deallocation"


class OptimizationStrategy(Enum):
    """Memory optimization strategies"""
    GARBAGE_COLLECTION = "garbage_collection"
    OBJECT_POOLING = "object_pooling"
    WEAK_REFERENCES = "weak_references"
    LAZY_LOADING = "lazy_loading"
    CACHE_CLEANUP = "cache_cleanup"
    RESOURCE_COMPRESSION = "resource_compression"
    BATCH_PROCESSING = "batch_processing"


@dataclass
class MemorySnapshot:
    """Memory usage snapshot"""
    timestamp: datetime
    total_memory_mb: float
    available_memory_mb: float
    used_memory_mb: float
    memory_percent: float
    gc_objects: int
    gc_collections: Dict[int, int]
    top_objects: Dict[str, int]
    stack_trace: Optional[str]


@dataclass
class MemoryIssue:
    """Memory issue detection"""
    issue_id: str
    issue_type: MemoryIssueType
    severity: str  # low, medium, high, critical
    description: str
    detected_at: datetime
    memory_usage_mb: float
    affected_objects: List[str]
    suggested_strategies: List[OptimizationStrategy]
    evidence: Dict[str, Any]
    resolved: bool
    resolution_time: Optional[datetime]


@dataclass
class OptimizationResult:
    """Memory optimization result"""
    strategy: OptimizationStrategy
    memory_before_mb: float
    memory_after_mb: float
    memory_saved_mb: float
    objects_cleaned: int
    execution_time_ms: float
    success: bool
    details: str


class ObjectTracker:
    """Track object allocations and references"""
    
    def __init__(self):
        self.tracked_objects: Dict[int, Dict[str, Any]] = {}
        self.object_counts: Dict[str, int] = defaultdict(int)
        self.large_objects: List[Dict[str, Any]] = []
        self.weak_refs: Set[weakref.ref] = set()
        self.allocation_traces: Dict[int, str] = {}
        
    def track_object(self, obj: Any, context: str = ""):
        """Track an object for memory monitoring"""
        obj_id = id(obj)
        obj_type = type(obj).__name__
        obj_size = sys.getsizeof(obj)
        
        self.tracked_objects[obj_id] = {
            "type": obj_type,
            "size": obj_size,
            "context": context,
            "created_at": datetime.now(),
            "reference_count": sys.getrefcount(obj)
        }
        
        self.object_counts[obj_type] += 1
        
        # Track large objects separately
        if obj_size > 1024 * 1024:  # 1MB
            self.large_objects.append({
                "id": obj_id,
                "type": obj_type,
                "size": obj_size,
                "context": context
            })
        
        # Store allocation trace if available
        if tracemalloc.is_tracing():
            trace = tracemalloc.get_object_traceback(obj)
            if trace:
                self.allocation_traces[obj_id] = "\n".join(trace.format())
    
    def untrack_object(self, obj_id: int):
        """Remove object from tracking"""
        if obj_id in self.tracked_objects:
            obj_info = self.tracked_objects[obj_id]
            self.object_counts[obj_info["type"]] -= 1
            del self.tracked_objects[obj_id]
            
            # Remove from large objects if present
            self.large_objects = [
                lo for lo in self.large_objects if lo["id"] != obj_id
            ]
            
            # Remove allocation trace
            self.allocation_traces.pop(obj_id, None)
    
    def get_object_summary(self) -> Dict[str, Any]:
        """Get summary of tracked objects"""
        total_objects = len(self.tracked_objects)
        total_size = sum(obj["size"] for obj in self.tracked_objects.values())
        
        # Top object types by count
        top_by_count = sorted(
            self.object_counts.items(),
            key=lambda x: x[1],
            reverse=True
        )[:10]
        
        # Top object types by size
        size_by_type = defaultdict(int)
        for obj in self.tracked_objects.values():
            size_by_type[obj["type"]] += obj["size"]
        
        top_by_size = sorted(
            size_by_type.items(),
            key=lambda x: x[1],
            reverse=True
        )[:10]
        
        return {
            "total_objects": total_objects,
            "total_size_mb": total_size / 1024 / 1024,
            "large_objects_count": len(self.large_objects),
            "top_by_count": top_by_count,
            "top_by_size": [(t, s/1024/1024) for t, s in top_by_size],
            "object_types": len(self.object_counts)
        }


class MemoryMonitor:
    """Real-time memory monitoring"""
    
    def __init__(self):
        self.process = psutil.Process()
        self.memory_history: deque = deque(maxlen=1000)
        self.monitoring_enabled = True
        self.monitoring_interval = 5.0  # seconds
        self.baseline_memory = 0.0
        self.peak_memory = 0.0
        
        # Memory thresholds
        self.warning_threshold = 512  # MB
        self.critical_threshold = 1024  # MB
        self.leak_detection_window = 600  # 10 minutes
        
    async def start_monitoring(self):
        """Start memory monitoring"""
        self.baseline_memory = self.get_current_memory_usage()
        
        while self.monitoring_enabled:
            try:
                snapshot = self.take_memory_snapshot()
                self.memory_history.append(snapshot)
                
                # Update peak memory
                if snapshot.used_memory_mb > self.peak_memory:
                    self.peak_memory = snapshot.used_memory_mb
                
                # Check for issues
                await self._check_memory_issues(snapshot)
                
                await asyncio.sleep(self.monitoring_interval)
                
            except Exception as e:
                logger.error(f"Memory monitoring error: {e}")
                await asyncio.sleep(self.monitoring_interval)
    
    def stop_monitoring(self):
        """Stop memory monitoring"""
        self.monitoring_enabled = False
    
    def take_memory_snapshot(self) -> MemorySnapshot:
        """Take a memory usage snapshot"""
        memory_info = self.process.memory_info()
        memory_percent = self.process.memory_percent()
        
        # System memory info
        system_memory = psutil.virtual_memory()
        
        # Garbage collection info
        gc_stats = {}
        for i in range(3):  # GC generations 0, 1, 2
            gc_stats[i] = gc.get_count()[i]
        
        # Top object types
        top_objects = {}
        for obj_type in gc.get_objects():
            type_name = type(obj_type).__name__
            top_objects[type_name] = top_objects.get(type_name, 0) + 1
        
        # Sort and limit to top 10
        top_objects = dict(sorted(top_objects.items(), key=lambda x: x[1], reverse=True)[:10])
        
        return MemorySnapshot(
            timestamp=datetime.now(),
            total_memory_mb=system_memory.total / 1024 / 1024,
            available_memory_mb=system_memory.available / 1024 / 1024,
            used_memory_mb=memory_info.rss / 1024 / 1024,
            memory_percent=memory_percent,
            gc_objects=len(gc.get_objects()),
            gc_collections=gc_stats,
            top_objects=top_objects,
            stack_trace=None
        )
    
    def get_current_memory_usage(self) -> float:
        """Get current memory usage in MB"""
        return self.process.memory_info().rss / 1024 / 1024
    
    async def _check_memory_issues(self, snapshot: MemorySnapshot):
        """Check for memory issues in snapshot"""
        # High memory usage
        if snapshot.used_memory_mb > self.critical_threshold:
            logger.warning(f"Critical memory usage: {snapshot.used_memory_mb:.1f}MB")
        elif snapshot.used_memory_mb > self.warning_threshold:
            logger.info(f"High memory usage: {snapshot.used_memory_mb:.1f}MB")
        
        # Memory leak detection
        if len(self.memory_history) >= 10:
            await self._check_memory_leak()
    
    async def _check_memory_leak(self):
        """Check for memory leaks using trend analysis"""
        if len(self.memory_history) < 10:
            return
        
        # Get recent memory values
        recent_snapshots = list(self.memory_history)[-10:]
        memory_values = [s.used_memory_mb for s in recent_snapshots]
        
        # Simple trend analysis
        start_memory = memory_values[0]
        end_memory = memory_values[-1]
        memory_growth = end_memory - start_memory
        
        # Check if memory is consistently growing
        if memory_growth > 50:  # 50MB growth
            growth_rate = memory_growth / len(memory_values)
            if growth_rate > 5:  # 5MB per sample
                logger.warning(f"Potential memory leak detected: {growth_rate:.1f}MB growth per sample")
    
    def get_memory_statistics(self) -> Dict[str, Any]:
        """Get memory usage statistics"""
        if not self.memory_history:
            return {}
        
        recent_snapshots = list(self.memory_history)
        memory_values = [s.used_memory_mb for s in recent_snapshots]
        
        return {
            "current_memory_mb": memory_values[-1] if memory_values else 0,
            "baseline_memory_mb": self.baseline_memory,
            "peak_memory_mb": self.peak_memory,
            "average_memory_mb": sum(memory_values) / len(memory_values),
            "memory_growth_mb": memory_values[-1] - memory_values[0] if len(memory_values) > 1 else 0,
            "samples_collected": len(self.memory_history),
            "monitoring_enabled": self.monitoring_enabled
        }


class MemoryOptimizer:
    """Advanced memory optimization system"""
    
    def __init__(self):
        self.monitor = MemoryMonitor()
        self.object_tracker = ObjectTracker()
        self.memory_issues: Dict[str, MemoryIssue] = {}
        self.optimization_results: List[OptimizationResult] = []
        
        # Object pools for reuse
        self.object_pools: Dict[str, List[Any]] = defaultdict(list)
        self.pool_limits: Dict[str, int] = {}
        
        # Cache management
        self.caches: Dict[str, Dict[Any, Any]] = {}
        self.cache_limits: Dict[str, int] = {}
        self.cache_access_times: Dict[str, Dict[Any, datetime]] = defaultdict(dict)
        
        # Weak reference tracking
        self.weak_refs: Set[weakref.ref] = set()
        
        # Enable memory tracing
        if not tracemalloc.is_tracing():
            tracemalloc.start(25)  # Keep 25 frames
        
        logger.info("Memory optimizer initialized")
    
    async def start_optimization(self):
        """Start memory optimization system"""
        # Start monitoring
        asyncio.create_task(self.monitor.start_monitoring())
        
        # Start optimization tasks
        asyncio.create_task(self._periodic_optimization())
        asyncio.create_task(self._periodic_cache_cleanup())
        asyncio.create_task(self._periodic_gc_optimization())
    
    def stop_optimization(self):
        """Stop memory optimization"""
        self.monitor.stop_monitoring()
    
    async def _periodic_optimization(self):
        """Periodic memory optimization"""
        while self.monitor.monitoring_enabled:
            try:
                await asyncio.sleep(60)  # Run every minute
                await self.optimize_memory()
            except Exception as e:
                logger.error(f"Periodic optimization error: {e}")
    
    async def _periodic_cache_cleanup(self):
        """Periodic cache cleanup"""
        while self.monitor.monitoring_enabled:
            try:
                await asyncio.sleep(300)  # Run every 5 minutes
                await self.cleanup_caches()
            except Exception as e:
                logger.error(f"Cache cleanup error: {e}")
    
    async def _periodic_gc_optimization(self):
        """Periodic garbage collection optimization"""
        while self.monitor.monitoring_enabled:
            try:
                await asyncio.sleep(30)  # Run every 30 seconds
                await self.optimize_garbage_collection()
            except Exception as e:
                logger.error(f"GC optimization error: {e}")
    
    async def optimize_memory(self) -> Dict[str, OptimizationResult]:
        """Run comprehensive memory optimization"""
        results = {}
        
        # Get current memory usage
        memory_before = self.monitor.get_current_memory_usage()
        
        logger.info(f"Starting memory optimization (current usage: {memory_before:.1f}MB)")
        
        # Strategy 1: Garbage Collection
        gc_result = await self.optimize_garbage_collection()
        results["garbage_collection"] = gc_result
        
        # Strategy 2: Cache Cleanup
        cache_result = await self.cleanup_caches()
        results["cache_cleanup"] = cache_result
        
        # Strategy 3: Object Pool Cleanup
        pool_result = await self.cleanup_object_pools()
        results["object_pools"] = pool_result
        
        # Strategy 4: Weak Reference Cleanup
        weakref_result = await self.cleanup_weak_references()
        results["weak_references"] = weakref_result
        
        # Strategy 5: Large Object Cleanup
        large_obj_result = await self.cleanup_large_objects()
        results["large_objects"] = large_obj_result
        
        # Calculate total savings
        memory_after = self.monitor.get_current_memory_usage()
        total_saved = memory_before - memory_after
        
        logger.info(f"Memory optimization completed. Saved: {total_saved:.1f}MB")
        
        return results
    
    async def optimize_garbage_collection(self) -> OptimizationResult:
        """Optimize garbage collection"""
        start_time = time.time()
        memory_before = self.monitor.get_current_memory_usage()
        
        try:
            # Force garbage collection
            collected_counts = []
            for generation in range(3):
                collected = gc.collect(generation)
                collected_counts.append(collected)
            
            total_collected = sum(collected_counts)
            
            # Optimize GC thresholds for better performance
            if total_collected > 1000:
                # Increase thresholds to reduce GC frequency
                current_thresholds = gc.get_threshold()
                new_thresholds = (
                    int(current_thresholds[0] * 1.2),
                    int(current_thresholds[1] * 1.2),
                    int(current_thresholds[2] * 1.2)
                )
                gc.set_threshold(*new_thresholds)
            
            memory_after = self.monitor.get_current_memory_usage()
            execution_time = (time.time() - start_time) * 1000
            
            result = OptimizationResult(
                strategy=OptimizationStrategy.GARBAGE_COLLECTION,
                memory_before_mb=memory_before,
                memory_after_mb=memory_after,
                memory_saved_mb=memory_before - memory_after,
                objects_cleaned=total_collected,
                execution_time_ms=execution_time,
                success=True,
                details=f"Collected {total_collected} objects across {len(collected_counts)} generations"
            )
            
            self.optimization_results.append(result)
            return result
            
        except Exception as e:
            execution_time = (time.time() - start_time) * 1000
            result = OptimizationResult(
                strategy=OptimizationStrategy.GARBAGE_COLLECTION,
                memory_before_mb=memory_before,
                memory_after_mb=memory_before,
                memory_saved_mb=0,
                objects_cleaned=0,
                execution_time_ms=execution_time,
                success=False,
                details=f"GC optimization failed: {str(e)}"
            )
            
            self.optimization_results.append(result)
            return result
    
    async def cleanup_caches(self) -> OptimizationResult:
        """Cleanup expired cache entries"""
        start_time = time.time()
        memory_before = self.monitor.get_current_memory_usage()
        
        try:
            cleaned_items = 0
            cutoff_time = datetime.now() - timedelta(minutes=30)  # 30 minute TTL
            
            for cache_name, cache in self.caches.items():
                access_times = self.cache_access_times[cache_name]
                
                # Find expired items
                expired_keys = [
                    key for key, access_time in access_times.items()
                    if access_time < cutoff_time
                ]
                
                # Remove expired items
                for key in expired_keys:
                    if key in cache:
                        del cache[key]
                        del access_times[key]
                        cleaned_items += 1
                
                # Apply cache size limits
                cache_limit = self.cache_limits.get(cache_name, 1000)
                if len(cache) > cache_limit:
                    # Remove oldest items
                    sorted_items = sorted(
                        access_times.items(),
                        key=lambda x: x[1]
                    )
                    
                    items_to_remove = len(cache) - cache_limit
                    for key, _ in sorted_items[:items_to_remove]:
                        if key in cache:
                            del cache[key]
                            del access_times[key]
                            cleaned_items += 1
            
            memory_after = self.monitor.get_current_memory_usage()
            execution_time = (time.time() - start_time) * 1000
            
            result = OptimizationResult(
                strategy=OptimizationStrategy.CACHE_CLEANUP,
                memory_before_mb=memory_before,
                memory_after_mb=memory_after,
                memory_saved_mb=memory_before - memory_after,
                objects_cleaned=cleaned_items,
                execution_time_ms=execution_time,
                success=True,
                details=f"Cleaned {cleaned_items} cache entries from {len(self.caches)} caches"
            )
            
            self.optimization_results.append(result)
            return result
            
        except Exception as e:
            execution_time = (time.time() - start_time) * 1000
            result = OptimizationResult(
                strategy=OptimizationStrategy.CACHE_CLEANUP,
                memory_before_mb=memory_before,
                memory_after_mb=memory_before,
                memory_saved_mb=0,
                objects_cleaned=0,
                execution_time_ms=execution_time,
                success=False,
                details=f"Cache cleanup failed: {str(e)}"
            )
            
            self.optimization_results.append(result)
            return result
    
    async def cleanup_object_pools(self) -> OptimizationResult:
        """Cleanup object pools"""
        start_time = time.time()
        memory_before = self.monitor.get_current_memory_usage()
        
        try:
            cleaned_objects = 0
            
            for pool_name, pool in self.object_pools.items():
                pool_limit = self.pool_limits.get(pool_name, 100)
                
                # Remove excess objects from pool
                if len(pool) > pool_limit:
                    excess_count = len(pool) - pool_limit
                    del pool[:excess_count]
                    cleaned_objects += excess_count
            
            memory_after = self.monitor.get_current_memory_usage()
            execution_time = (time.time() - start_time) * 1000
            
            result = OptimizationResult(
                strategy=OptimizationStrategy.OBJECT_POOLING,
                memory_before_mb=memory_before,
                memory_after_mb=memory_after,
                memory_saved_mb=memory_before - memory_after,
                objects_cleaned=cleaned_objects,
                execution_time_ms=execution_time,
                success=True,
                details=f"Cleaned {cleaned_objects} objects from {len(self.object_pools)} pools"
            )
            
            self.optimization_results.append(result)
            return result
            
        except Exception as e:
            execution_time = (time.time() - start_time) * 1000
            result = OptimizationResult(
                strategy=OptimizationStrategy.OBJECT_POOLING,
                memory_before_mb=memory_before,
                memory_after_mb=memory_before,
                memory_saved_mb=0,
                objects_cleaned=0,
                execution_time_ms=execution_time,
                success=False,
                details=f"Object pool cleanup failed: {str(e)}"
            )
            
            self.optimization_results.append(result)
            return result
    
    async def cleanup_weak_references(self) -> OptimizationResult:
        """Cleanup dead weak references"""
        start_time = time.time()
        memory_before = self.monitor.get_current_memory_usage()
        
        try:
            # Remove dead weak references
            dead_refs = [ref for ref in self.weak_refs if ref() is None]
            for ref in dead_refs:
                self.weak_refs.discard(ref)
            
            memory_after = self.monitor.get_current_memory_usage()
            execution_time = (time.time() - start_time) * 1000
            
            result = OptimizationResult(
                strategy=OptimizationStrategy.WEAK_REFERENCES,
                memory_before_mb=memory_before,
                memory_after_mb=memory_after,
                memory_saved_mb=memory_before - memory_after,
                objects_cleaned=len(dead_refs),
                execution_time_ms=execution_time,
                success=True,
                details=f"Cleaned {len(dead_refs)} dead weak references"
            )
            
            self.optimization_results.append(result)
            return result
            
        except Exception as e:
            execution_time = (time.time() - start_time) * 1000
            result = OptimizationResult(
                strategy=OptimizationStrategy.WEAK_REFERENCES,
                memory_before_mb=memory_before,
                memory_after_mb=memory_before,
                memory_saved_mb=0,
                objects_cleaned=0,
                execution_time_ms=execution_time,
                success=False,
                details=f"Weak reference cleanup failed: {str(e)}"
            )
            
            self.optimization_results.append(result)
            return result
    
    async def cleanup_large_objects(self) -> OptimizationResult:
        """Cleanup large objects that are no longer needed"""
        start_time = time.time()
        memory_before = self.monitor.get_current_memory_usage()
        
        try:
            cleaned_objects = 0
            
            # Clean up large objects in tracker
            current_time = datetime.now()
            large_objects_copy = self.object_tracker.large_objects.copy()
            
            for large_obj in large_objects_copy:
                obj_id = large_obj["id"]
                
                # Check if object still exists
                try:
                    # Try to find the object by ID
                    found = False
                    for obj in gc.get_objects():
                        if id(obj) == obj_id:
                            found = True
                            break
                    
                    if not found:
                        # Object no longer exists, remove from tracking
                        self.object_tracker.large_objects.remove(large_obj)
                        cleaned_objects += 1
                        
                except Exception:
                    # Error accessing object, assume it's been cleaned up
                    try:
                        self.object_tracker.large_objects.remove(large_obj)
                        cleaned_objects += 1
                    except ValueError:
                        pass  # Already removed
            
            memory_after = self.monitor.get_current_memory_usage()
            execution_time = (time.time() - start_time) * 1000
            
            result = OptimizationResult(
                strategy=OptimizationStrategy.RESOURCE_COMPRESSION,
                memory_before_mb=memory_before,
                memory_after_mb=memory_after,
                memory_saved_mb=memory_before - memory_after,
                objects_cleaned=cleaned_objects,
                execution_time_ms=execution_time,
                success=True,
                details=f"Cleaned {cleaned_objects} large object references"
            )
            
            self.optimization_results.append(result)
            return result
            
        except Exception as e:
            execution_time = (time.time() - start_time) * 1000
            result = OptimizationResult(
                strategy=OptimizationStrategy.RESOURCE_COMPRESSION,
                memory_before_mb=memory_before,
                memory_after_mb=memory_before,
                memory_saved_mb=0,
                objects_cleaned=0,
                execution_time_ms=execution_time,
                success=False,
                details=f"Large object cleanup failed: {str(e)}"
            )
            
            self.optimization_results.append(result)
            return result
    
    def create_object_pool(self, pool_name: str, factory_function: Callable, 
                          pool_size: int = 100) -> None:
        """Create an object pool for reusing objects"""
        self.object_pools[pool_name] = []
        self.pool_limits[pool_name] = pool_size
        
        # Pre-populate pool
        for _ in range(min(10, pool_size)):  # Start with 10 objects
            try:
                obj = factory_function()
                self.object_pools[pool_name].append(obj)
            except Exception as e:
                logger.error(f"Failed to create object for pool {pool_name}: {e}")
                break
    
    def get_pooled_object(self, pool_name: str, factory_function: Callable) -> Any:
        """Get object from pool or create new one"""
        if pool_name in self.object_pools and self.object_pools[pool_name]:
            return self.object_pools[pool_name].pop()
        else:
            return factory_function()
    
    def return_to_pool(self, pool_name: str, obj: Any) -> None:
        """Return object to pool for reuse"""
        if pool_name in self.object_pools:
            pool_limit = self.pool_limits.get(pool_name, 100)
            if len(self.object_pools[pool_name]) < pool_limit:
                # Reset object state if it has a reset method
                if hasattr(obj, 'reset'):
                    try:
                        obj.reset()
                    except Exception:
                        return  # Don't pool if reset fails
                
                self.object_pools[pool_name].append(obj)
    
    def create_cache(self, cache_name: str, max_size: int = 1000) -> None:
        """Create a managed cache"""
        self.caches[cache_name] = {}
        self.cache_limits[cache_name] = max_size
        self.cache_access_times[cache_name] = {}
    
    def cache_get(self, cache_name: str, key: Any) -> Optional[Any]:
        """Get item from cache"""
        if cache_name in self.caches and key in self.caches[cache_name]:
            self.cache_access_times[cache_name][key] = datetime.now()
            return self.caches[cache_name][key]
        return None
    
    def cache_set(self, cache_name: str, key: Any, value: Any) -> None:
        """Set item in cache"""
        if cache_name in self.caches:
            self.caches[cache_name][key] = value
            self.cache_access_times[cache_name][key] = datetime.now()
    
    def get_memory_report(self) -> Dict[str, Any]:
        """Get comprehensive memory report"""
        memory_stats = self.monitor.get_memory_statistics()
        object_summary = self.object_tracker.get_object_summary()
        
        # Optimization results summary
        recent_optimizations = self.optimization_results[-10:]  # Last 10 optimizations
        total_memory_saved = sum(r.memory_saved_mb for r in recent_optimizations)
        
        # Cache statistics
        cache_stats = {}
        for cache_name, cache in self.caches.items():
            cache_stats[cache_name] = {
                "size": len(cache),
                "limit": self.cache_limits.get(cache_name, 0),
                "utilization": len(cache) / self.cache_limits.get(cache_name, 1) * 100
            }
        
        # Pool statistics
        pool_stats = {}
        for pool_name, pool in self.object_pools.items():
            pool_stats[pool_name] = {
                "size": len(pool),
                "limit": self.pool_limits.get(pool_name, 0),
                "utilization": len(pool) / self.pool_limits.get(pool_name, 1) * 100
            }
        
        return {
            "memory_statistics": memory_stats,
            "object_summary": object_summary,
            "optimization_summary": {
                "total_optimizations": len(self.optimization_results),
                "recent_memory_saved_mb": total_memory_saved,
                "successful_optimizations": len([r for r in recent_optimizations if r.success])
            },
            "cache_statistics": cache_stats,
            "pool_statistics": pool_stats,
            "memory_issues": len(self.memory_issues),
            "weak_references": len(self.weak_refs)
        }
    
    def export_memory_report(self, file_path: str):
        """Export memory report to file"""
        report = self.get_memory_report()
        
        with open(file_path, 'w') as f:
            json.dump(report, f, indent=2, default=str)


def memory_tracked(pool_name: Optional[str] = None):
    """Decorator for automatic memory tracking"""
    def decorator(func):
        @functools.wraps(func)
        async def async_wrapper(*args, **kwargs):
            optimizer = global_memory_optimizer
            
            # Track function execution
            start_memory = optimizer.monitor.get_current_memory_usage()
            
            try:
                result = await func(*args, **kwargs)
                
                # Track result object if it's significant
                if result and sys.getsizeof(result) > 1024:  # > 1KB
                    optimizer.object_tracker.track_object(
                        result, 
                        f"{func.__module__}.{func.__name__}"
                    )
                
                return result
                
            finally:
                end_memory = optimizer.monitor.get_current_memory_usage()
                memory_used = end_memory - start_memory
                
                if memory_used > 10:  # Log if > 10MB used
                    logger.info(f"Function {func.__name__} used {memory_used:.1f}MB")
        
        @functools.wraps(func)
        def sync_wrapper(*args, **kwargs):
            optimizer = global_memory_optimizer
            
            start_memory = optimizer.monitor.get_current_memory_usage()
            
            try:
                result = func(*args, **kwargs)
                
                # Track result object if it's significant
                if result and sys.getsizeof(result) > 1024:
                    optimizer.object_tracker.track_object(
                        result,
                        f"{func.__module__}.{func.__name__}"
                    )
                
                return result
                
            finally:
                end_memory = optimizer.monitor.get_current_memory_usage()
                memory_used = end_memory - start_memory
                
                if memory_used > 10:
                    logger.info(f"Function {func.__name__} used {memory_used:.1f}MB")
        
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
    
    return decorator


# Global memory optimizer instance
global_memory_optimizer = MemoryOptimizer()