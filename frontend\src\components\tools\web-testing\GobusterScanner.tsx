/**
 * Gobuster Scanner Component
 * Directory and file bruteforcing tool for web application testing
 */
import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { 
  FolderSearch,
  Play,
  Square,
  Download,
  Settings,
  Target,
  Globe,
  FileText,
  Terminal,
  Copy,
  RotateCcw,
  Clock,
  Zap,
  Filter,
  Eye,
  AlertTriangle,
  CheckCircle,
  TrendingUp,
  Activity,
  Lock,
  Search,
  List,
  BarChart3
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useBackendStore } from '@/stores/backend-store';
import { apiClient, ToolExecution } from '@/services/api-client';
import { toast } from 'sonner';

/**
 * Gobuster configuration interface
 */
interface GobusterConfig {
  url: string;
  mode: 'dir' | 'dns' | 'vhost' | 'fuzz';
  wordlist: string;
  customWordlist: string;
  extensions: string[];
  threads: number;
  timeout: number;
  delay: string;
  userAgent: string;
  cookies: string;
  headers: { [key: string]: string };
  authentication: {
    enabled: boolean;
    username: string;
    password: string;
    type: 'basic' | 'digest';
  };
  statusCodes: string[];
  excludeStatusCodes: string[];
  excludeLength: string;
  followRedirects: boolean;
  noTlsValidation: boolean;
  proxy: string;
  outputFormat: 'standard' | 'json';
  verbose: boolean;
  noProgress: boolean;
  customArgs: string;
}

/**
 * Gobuster scan modes
 */
const SCAN_MODES = {
  'dir': {
    name: 'Directory/File Scanning',
    description: 'Discover hidden directories and files',
    icon: <FolderSearch className="h-4 w-4" />,
    usesExtensions: true
  },
  'dns': {
    name: 'DNS Subdomain Scanning',
    description: 'Enumerate DNS subdomains',
    icon: <Globe className="h-4 w-4" />,
    usesExtensions: false
  },
  'vhost': {
    name: 'Virtual Host Scanning',
    description: 'Discover virtual hosts',
    icon: <Target className="h-4 w-4" />,
    usesExtensions: false
  },
  'fuzz': {
    name: 'FUZZ Parameter Testing',
    description: 'Fuzz parameters and endpoints',
    icon: <Zap className="h-4 w-4" />,
    usesExtensions: false
  }
};

/**
 * Common wordlists
 */
const WORDLISTS = {
  'common': '/usr/share/wordlists/dirb/common.txt',
  'big': '/usr/share/wordlists/dirb/big.txt',
  'directory-list-2.3-medium': '/usr/share/wordlists/dirbuster/directory-list-2.3-medium.txt',
  'directory-list-2.3-small': '/usr/share/wordlists/dirbuster/directory-list-2.3-small.txt',
  'raft-medium-directories': '/usr/share/wordlists/raft/raft-medium-directories.txt',
  'raft-large-directories': '/usr/share/wordlists/raft/raft-large-directories.txt',
  'seclist-common': '/usr/share/seclists/Discovery/Web-Content/common.txt',
  'seclist-directory-list': '/usr/share/seclists/Discovery/Web-Content/directory-list-2.3-medium.txt'
};

/**
 * Common file extensions for different technologies
 */
const EXTENSION_PRESETS = {
  'web-common': ['php', 'asp', 'aspx', 'jsp', 'html', 'htm', 'js', 'css', 'txt'],
  'php': ['php', 'php3', 'php4', 'php5', 'phtml', 'inc'],
  'asp': ['asp', 'aspx', 'ascx', 'ashx', 'asmx'],
  'jsp': ['jsp', 'jspx', 'jsw', 'jsv', 'jspf'],
  'python': ['py', 'pyc', 'pyo', 'pyw'],
  'backup': ['bak', 'backup', 'old', 'orig', 'save', 'tmp'],
  'config': ['conf', 'config', 'cfg', 'ini', 'xml', 'json', 'yaml', 'yml'],
  'logs': ['log', 'logs', 'access', 'error', 'debug']
};

/**
 * Common HTTP status codes
 */
const STATUS_CODES = [
  { code: '200', description: 'OK - Success' },
  { code: '204', description: 'No Content' },
  { code: '301', description: 'Moved Permanently' },
  { code: '302', description: 'Found (Redirect)' },
  { code: '307', description: 'Temporary Redirect' },
  { code: '403', description: 'Forbidden' },
  { code: '500', description: 'Internal Server Error' },
  { code: '503', description: 'Service Unavailable' }
];

/**
 * Gobuster Scanner Component
 */
export function GobusterScanner() {
  // State management
  const [config, setConfig] = React.useState<GobusterConfig>({
    url: '',
    mode: 'dir',
    wordlist: 'common',
    customWordlist: '',
    extensions: ['php', 'html', 'txt'],
    threads: 10,
    timeout: 10,
    delay: '',
    userAgent: '',
    cookies: '',
    headers: {},
    authentication: {
      enabled: false,
      username: '',
      password: '',
      type: 'basic'
    },
    statusCodes: ['200', '204', '301', '302', '307', '403'],
    excludeStatusCodes: [],
    excludeLength: '',
    followRedirects: false,
    noTlsValidation: false,
    proxy: '',
    outputFormat: 'standard',
    verbose: false,
    noProgress: false,
    customArgs: ''
  });
  
  const [isScanning, setIsScanning] = React.useState(false);
  const [currentExecution, setCurrentExecution] = React.useState<ToolExecution | null>(null);
  const [progress, setProgress] = React.useState(0);
  const [output, setOutput] = React.useState<string[]>([]);
  const [results, setResults] = React.useState<any>(null);
  const [activeTab, setActiveTab] = React.useState('configure');
  const [scanStats, setScanStats] = React.useState<any>(null);
  const [newHeaderKey, setNewHeaderKey] = React.useState('');
  const [newHeaderValue, setNewHeaderValue] = React.useState('');
  
  // Backend connection
  const { status: backendStatus } = useBackendStore();
  
  // Update configuration
  const updateConfig = (updates: Partial<GobusterConfig>) => {
    setConfig(prev => ({ ...prev, ...updates }));
  };
  
  // Update authentication
  const updateAuth = (updates: Partial<GobusterConfig['authentication']>) => {
    setConfig(prev => ({
      ...prev,
      authentication: { ...prev.authentication, ...updates }
    }));
  };
  
  // Apply extension preset
  const applyExtensionPreset = (presetKey: string) => {
    const preset = EXTENSION_PRESETS[presetKey as keyof typeof EXTENSION_PRESETS];
    if (preset) {
      updateConfig({ extensions: preset });
      toast.success(`Applied ${presetKey} extensions`);
    }
  };
  
  // Add custom header
  const addHeader = () => {
    if (newHeaderKey.trim() && newHeaderValue.trim()) {
      updateConfig({
        headers: {
          ...config.headers,
          [newHeaderKey.trim()]: newHeaderValue.trim()
        }
      });
      setNewHeaderKey('');
      setNewHeaderValue('');
      toast.success('Header added');
    }
  };
  
  // Remove header
  const removeHeader = (key: string) => {
    const { [key]: removed, ...remainingHeaders } = config.headers;
    updateConfig({ headers: remainingHeaders });
    toast.success('Header removed');
  };
  
  // Generate Gobuster command
  const generateCommand = (): string => {
    let command = `gobuster ${config.mode}`;
    
    // URL/Target
    if (config.mode === 'dns') {
      command += ` -d ${config.url.replace(/^https?:\/\//, '')}`;
    } else {
      command += ` -u ${config.url}`;
    }
    
    // Wordlist
    const wordlistPath = config.customWordlist.trim() || WORDLISTS[config.wordlist as keyof typeof WORDLISTS];
    command += ` -w ${wordlistPath}`;
    
    // Extensions (for dir mode)
    if (config.mode === 'dir' && config.extensions.length > 0) {
      command += ` -x ${config.extensions.join(',')}`;
    }
    
    // Threads
    command += ` -t ${config.threads}`;
    
    // Timeout
    if (config.timeout !== 10) {
      command += ` --timeout ${config.timeout}s`;
    }
    
    // Delay
    if (config.delay.trim()) {
      command += ` --delay ${config.delay}`;
    }
    
    // User Agent
    if (config.userAgent.trim()) {
      command += ` -a "${config.userAgent}"`;
    }
    
    // Cookies
    if (config.cookies.trim()) {
      command += ` -c "${config.cookies}"`;
    }
    
    // Headers
    Object.entries(config.headers).forEach(([key, value]) => {
      command += ` -H "${key}: ${value}"`;
    });
    
    // Authentication
    if (config.authentication.enabled && config.authentication.username) {
      command += ` -U ${config.authentication.username}`;
      if (config.authentication.password) {
        command += ` -P ${config.authentication.password}`;
      }
      if (config.authentication.type === 'digest') {
        command += ` --auth-type digest`;
      }
    }
    
    // Status codes
    if (config.statusCodes.length > 0) {
      command += ` -s ${config.statusCodes.join(',')}`;
    }
    
    // Exclude status codes
    if (config.excludeStatusCodes.length > 0) {
      command += ` -b ${config.excludeStatusCodes.join(',')}`;
    }
    
    // Exclude length
    if (config.excludeLength.trim()) {
      command += ` --exclude-length ${config.excludeLength}`;
    }
    
    // Follow redirects
    if (config.followRedirects) {
      command += ` -r`;
    }
    
    // No TLS validation
    if (config.noTlsValidation) {
      command += ` -k`;
    }
    
    // Proxy
    if (config.proxy.trim()) {
      command += ` --proxy ${config.proxy}`;
    }
    
    // Output format
    if (config.outputFormat === 'json') {
      command += ` -o gobuster-results.json`;
    }
    
    // Verbose
    if (config.verbose) {
      command += ` -v`;
    }
    
    // No progress
    if (config.noProgress) {
      command += ` --no-progress`;
    }
    
    // Custom arguments
    if (config.customArgs.trim()) {
      command += ` ${config.customArgs.trim()}`;
    }
    
    return command;
  };
  
  // Start scan
  const startScan = async () => {
    if (!config.url.trim()) {
      toast.error('Please specify a target URL');
      return;
    }
    
    if (!backendStatus.connected) {
      toast.error('Backend not connected');
      return;
    }
    
    try {
      setIsScanning(true);
      setProgress(0);
      setOutput([]);
      setResults(null);
      setScanStats(null);
      setActiveTab('output');
      
      const execution = await apiClient.executeTool('gobuster', config, (progressUpdate) => {
        setCurrentExecution(progressUpdate);
        setProgress(progressUpdate.progress);
        if (progressUpdate.output) {
          setOutput(prev => [...prev, ...progressUpdate.output!]);
        }
        
        if (progressUpdate.status === 'completed') {
          setIsScanning(false);
          setResults(progressUpdate.results);
          
          // Parse scan statistics
          if (progressUpdate.results?.findings) {
            const stats = {
              totalFound: progressUpdate.results.findings.length,
              statusCodes: progressUpdate.results.findings.reduce((acc: any, finding: any) => {
                const code = finding.status || '200';
                acc[code] = (acc[code] || 0) + 1;
                return acc;
              }, {}),
              avgResponseTime: progressUpdate.results.avgResponseTime || 0
            };
            setScanStats(stats);
          }
          
          toast.success('Gobuster scan completed');
        } else if (progressUpdate.status === 'failed') {
          setIsScanning(false);
          toast.error(`Scan failed: ${progressUpdate.error}`);
        }
      });
      
      setCurrentExecution(execution);
      toast.info('Gobuster scan started');
    } catch (error) {
      setIsScanning(false);
      console.error('Failed to start Gobuster scan:', error);
      toast.error('Failed to start scan');
    }
  };
  
  // Stop scan
  const stopScan = async () => {
    if (currentExecution) {
      try {
        await apiClient.cancelExecution(currentExecution.id);
        setIsScanning(false);
        setCurrentExecution(null);
        toast.info('Scan cancelled');
      } catch (error) {
        console.error('Failed to stop scan:', error);
        toast.error('Failed to stop scan');
      }
    }
  };
  
  // Copy command to clipboard
  const copyCommand = async () => {
    try {
      await navigator.clipboard.writeText(generateCommand());
      toast.success('Command copied to clipboard');
    } catch (error) {
      toast.error('Failed to copy command');
    }
  };
  
  // Export results
  const exportResults = () => {
    if (!results && output.length === 0) {
      toast.error('No results to export');
      return;
    }
    
    const data = results || output.join('
');
    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `gobuster-${config.mode}-${new Date().toISOString().slice(0, 19)}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    
    toast.success('Results exported');
  };
  
  return (
    <div className="container-desktop py-6 space-y-6">
      {/* Tool Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className="p-2 bg-red-100 dark:bg-red-900/20 rounded-lg">
            <FolderSearch className="h-6 w-6 text-red-600 dark:text-red-400" />
          </div>
          <div>
            <h1 className="text-2xl font-bold">Gobuster Scanner</h1>
            <p className="text-muted-foreground">
              Directory and file bruteforcing tool for web application testing
            </p>
          </div>
        </div>
        
        {/* Status indicators */}
        <div className="flex items-center gap-2">
          <Badge variant={backendStatus.connected ? 'default' : 'destructive'}>
            {backendStatus.connected ? 'Connected' : 'Offline'}
          </Badge>
          {isScanning && (
            <Badge variant="secondary" className="animate-pulse">
              Scanning...
            </Badge>
          )}
        </div>
      </div>
      
      {/* Main Interface */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="configure" className="flex items-center gap-2">
            <Settings className="h-4 w-4" />
            Configure
          </TabsTrigger>
          <TabsTrigger value="output" className="flex items-center gap-2">
            <Terminal className="h-4 w-4" />
            Output
          </TabsTrigger>
          <TabsTrigger value="results" className="flex items-center gap-2">
            <FileText className="h-4 w-4" />
            Results
          </TabsTrigger>
          <TabsTrigger value="command" className="flex items-center gap-2">
            <Target className="h-4 w-4" />
            Command
          </TabsTrigger>
        </TabsList>
        
        {/* Configuration Tab */}
        <TabsContent value="configure" className="space-y-6">
          <div className="grid gap-6 lg:grid-cols-2">
            {/* Target Configuration */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Target className="h-5 w-5" />
                  Target & Mode Configuration
                </CardTitle>
                <CardDescription>
                  Specify the target and scanning mode for enumeration
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* URL/Domain */}
                <div className="space-y-2">
                  <label className="text-sm font-medium">
                    {config.mode === 'dns' ? 'Domain' : 'Target URL'}
                  </label>
                  <Input
                    placeholder={config.mode === 'dns' ? 'e.g., example.com' : 'e.g., https://example.com'}
                    value={config.url}
                    onChange={(e) => updateConfig({ url: e.target.value })}
                  />
                  <p className="text-xs text-muted-foreground">
                    {config.mode === 'dns' 
                      ? 'Domain name for subdomain enumeration'
                      : 'Full URL including protocol (http/https)'
                    }
                  </p>
                </div>
                
                {/* Scan Mode */}
                <div className="space-y-2">
                  <label className="text-sm font-medium">Scan Mode</label>
                  <Select 
                    value={config.mode} 
                    onValueChange={(value) => updateConfig({ mode: value as any })}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {Object.entries(SCAN_MODES).map(([key, mode]) => (
                        <SelectItem key={key} value={key}>
                          <div className="flex items-center gap-2">
                            {mode.icon}
                            <div className="flex flex-col">
                              <span>{mode.name}</span>
                              <span className="text-xs text-muted-foreground">
                                {mode.description}
                              </span>
                            </div>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                
                {/* Wordlist */}
                <div className="space-y-2">
                  <label className="text-sm font-medium">Wordlist</label>
                  <Select 
                    value={config.wordlist} 
                    onValueChange={(value) => updateConfig({ wordlist: value })}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {Object.entries(WORDLISTS).map(([key, path]) => (
                        <SelectItem key={key} value={key}>
                          <div className="flex flex-col">
                            <span className="capitalize">{key.replace(/-/g, ' ')}</span>
                            <span className="text-xs text-muted-foreground">
                              {path}
                            </span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                
                {/* Custom Wordlist */}
                <div className="space-y-2">
                  <label className="text-sm font-medium">Custom Wordlist (Optional)</label>
                  <Input
                    placeholder="/path/to/custom/wordlist.txt"
                    value={config.customWordlist}
                    onChange={(e) => updateConfig({ customWordlist: e.target.value })}
                  />
                  <p className="text-xs text-muted-foreground">
                    Override default wordlist with custom file path
                  </p>
                </div>
                
                {/* Extensions (for dir mode) */}
                {SCAN_MODES[config.mode].usesExtensions && (
                  <div className="space-y-2">
                    <label className="text-sm font-medium">File Extensions</label>
                    <div className="space-y-2">
                      <Textarea
                        placeholder="php,html,txt,js"
                        value={config.extensions.join(',')}
                        onChange={(e) => updateConfig({ 
                          extensions: e.target.value.split(',').map(ext => ext.trim()).filter(Boolean)
                        })}
                        rows={2}
                      />
                      <div className="flex flex-wrap gap-2">
                        {Object.keys(EXTENSION_PRESETS).map((preset) => (
                          <Button
                            key={preset}
                            variant="outline"
                            size="sm"
                            onClick={() => applyExtensionPreset(preset)}
                          >
                            {preset}
                          </Button>
                        ))}
                      </div>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
            
            {/* Performance Options */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <TrendingUp className="h-5 w-5" />
                  Performance & Timing
                </CardTitle>
                <CardDescription>
                  Configure scanning performance and request timing
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid gap-4 md:grid-cols-2">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Threads</label>
                    <Input
                      type="number"
                      min="1"
                      max="100"
                      value={config.threads}
                      onChange={(e) => updateConfig({ threads: parseInt(e.target.value) || 10 })}
                    />
                    <p className="text-xs text-muted-foreground">
                      Number of concurrent threads
                    </p>
                  </div>
                  
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Timeout (seconds)</label>
                    <Input
                      type="number"
                      min="1"
                      max="300"
                      value={config.timeout}
                      onChange={(e) => updateConfig({ timeout: parseInt(e.target.value) || 10 })}
                    />
                  </div>
                </div>
                
                <div className="space-y-2">
                  <label className="text-sm font-medium">Delay Between Requests</label>
                  <Input
                    placeholder="e.g., 100ms, 1s (optional)"
                    value={config.delay}
                    onChange={(e) => updateConfig({ delay: e.target.value })}
                  />
                  <p className="text-xs text-muted-foreground">
                    Add delay between requests to avoid rate limiting
                  </p>
                </div>
                
                <div className="space-y-3">
                  <label className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      checked={config.followRedirects}
                      onChange={(e) => updateConfig({ followRedirects: e.target.checked })}
                      className="rounded border-gray-300"
                    />
                    <span className="text-sm">Follow Redirects</span>
                  </label>
                  
                  <label className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      checked={config.noTlsValidation}
                      onChange={(e) => updateConfig({ noTlsValidation: e.target.checked })}
                      className="rounded border-gray-300"
                    />
                    <span className="text-sm">Skip TLS Certificate Validation</span>
                  </label>
                  
                  <label className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      checked={config.verbose}
                      onChange={(e) => updateConfig({ verbose: e.target.checked })}
                      className="rounded border-gray-300"
                    />
                    <span className="text-sm">Verbose Output</span>
                  </label>
                </div>
              </CardContent>
            </Card>
          </div>
          
          {/* HTTP Configuration */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Globe className="h-5 w-5" />
                HTTP Configuration
              </CardTitle>
              <CardDescription>
                Configure HTTP headers, authentication, and request parameters
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-2">
                  <label className="text-sm font-medium">User Agent</label>
                  <Input
                    placeholder="Custom User-Agent string"
                    value={config.userAgent}
                    onChange={(e) => updateConfig({ userAgent: e.target.value })}
                  />
                </div>
                
                <div className="space-y-2">
                  <label className="text-sm font-medium">Cookies</label>
                  <Input
                    placeholder="cookie1=value1; cookie2=value2"
                    value={config.cookies}
                    onChange={(e) => updateConfig({ cookies: e.target.value })}
                  />
                </div>
              </div>
              
              {/* Custom Headers */}
              <div className="space-y-2">
                <label className="text-sm font-medium">Custom Headers</label>
                <div className="space-y-2">
                  {Object.entries(config.headers).map(([key, value]) => (
                    <div key={key} className="flex items-center gap-2 p-2 border rounded">
                      <span className="text-sm font-mono">{key}:</span>
                      <span className="text-sm flex-1">{value}</span>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => removeHeader(key)}
                      >
                        ×
                      </Button>
                    </div>
                  ))}
                  
                  <div className="flex gap-2">
                    <Input
                      placeholder="Header name"
                      value={newHeaderKey}
                      onChange={(e) => setNewHeaderKey(e.target.value)}
                    />
                    <Input
                      placeholder="Header value"
                      value={newHeaderValue}
                      onChange={(e) => setNewHeaderValue(e.target.value)}
                    />
                    <Button onClick={addHeader} disabled={!newHeaderKey.trim() || !newHeaderValue.trim()}>
                      Add
                    </Button>
                  </div>
                </div>
              </div>
              
              {/* Proxy */}
              <div className="space-y-2">
                <label className="text-sm font-medium">Proxy (Optional)</label>
                <Input
                  placeholder="http://proxy:8080"
                  value={config.proxy}
                  onChange={(e) => updateConfig({ proxy: e.target.value })}
                />
              </div>
            </CardContent>
          </Card>
          
          {/* Authentication */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Lock className="h-5 w-5" />
                Authentication
              </CardTitle>
              <CardDescription>
                Configure HTTP authentication if required
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={config.authentication.enabled}
                  onChange={(e) => updateAuth({ enabled: e.target.checked })}
                  className="rounded border-gray-300"
                />
                <span className="text-sm font-medium">Enable Authentication</span>
              </div>
              
              {config.authentication.enabled && (
                <div className="grid gap-4 md:grid-cols-3">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Type</label>
                    <Select 
                      value={config.authentication.type} 
                      onValueChange={(value) => updateAuth({ type: value as any })}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="basic">Basic Auth</SelectItem>
                        <SelectItem value="digest">Digest Auth</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Username</label>
                    <Input
                      placeholder="Username"
                      value={config.authentication.username}
                      onChange={(e) => updateAuth({ username: e.target.value })}
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Password</label>
                    <Input
                      type="password"
                      placeholder="Password"
                      value={config.authentication.password}
                      onChange={(e) => updateAuth({ password: e.target.value })}
                    />
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
          
          {/* Response Filtering */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Filter className="h-5 w-5" />
                Response Filtering
              </CardTitle>
              <CardDescription>
                Filter results based on HTTP status codes and response characteristics
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-2">
                  <label className="text-sm font-medium">Include Status Codes</label>
                  <div className="space-y-2">
                    <div className="flex flex-wrap gap-2">
                      {STATUS_CODES.map((status) => (
                        <Button
                          key={status.code}
                          variant={config.statusCodes.includes(status.code) ? 'default' : 'outline'}
                          size="sm"
                          onClick={() => {
                            if (config.statusCodes.includes(status.code)) {
                              updateConfig({
                                statusCodes: config.statusCodes.filter(c => c !== status.code)
                              });
                            } else {
                              updateConfig({
                                statusCodes: [...config.statusCodes, status.code]
                              });
                            }
                          }}
                        >
                          {status.code}
                        </Button>
                      ))}
                    </div>
                    <Input
                      placeholder="Custom status codes (comma-separated)"
                      value={config.statusCodes.join(',')}
                      onChange={(e) => updateConfig({
                        statusCodes: e.target.value.split(',').map(c => c.trim()).filter(Boolean)
                      })}
                    />
                  </div>
                </div>
                
                <div className="space-y-2">
                  <label className="text-sm font-medium">Exclude Status Codes</label>
                  <Input
                    placeholder="e.g., 404,503"
                    value={config.excludeStatusCodes.join(',')}
                    onChange={(e) => updateConfig({
                      excludeStatusCodes: e.target.value.split(',').map(c => c.trim()).filter(Boolean)
                    })}
                  />
                </div>
              </div>
              
              <div className="space-y-2">
                <label className="text-sm font-medium">Exclude Response Length</label>
                <Input
                  placeholder="e.g., 1234 or 1000-2000"
                  value={config.excludeLength}
                  onChange={(e) => updateConfig({ excludeLength: e.target.value })}
                />
                <p className="text-xs text-muted-foreground">
                  Filter out responses with specific content lengths
                </p>
              </div>
            </CardContent>
          </Card>
          
          {/* Actions */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Activity className="h-5 w-5" />
                Start Scan
              </CardTitle>
              <CardDescription>
                Execute the Gobuster scan with your configured parameters
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between">
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={copyCommand}
                  >
                    <Copy className="h-4 w-4 mr-1" />
                    Copy Command
                  </Button>
                </div>
                
                <div className="flex gap-2">
                  {isScanning ? (
                    <Button
                      variant="destructive"
                      onClick={stopScan}
                    >
                      <Square className="h-4 w-4 mr-2" />
                      Stop Scan
                    </Button>
                  ) : (
                    <Button
                      onClick={startScan}
                      disabled={!config.url.trim() || !backendStatus.connected}
                    >
                      <Play className="h-4 w-4 mr-2" />
                      Start Scan
                    </Button>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        {/* Output Tab */}
        <TabsContent value="output" className="space-y-6">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="flex items-center gap-2">
                    <Terminal className="h-5 w-5" />
                    Real-time Output
                  </CardTitle>
                  <CardDescription>
                    Live output from the Gobuster scan process
                  </CardDescription>
                </div>
                
                {/* Progress and controls */}
                <div className="flex items-center gap-4">
                  {isScanning && (
                    <div className="flex items-center gap-2">
                      <Progress value={progress} className="w-32" />
                      <span className="text-sm text-muted-foreground">
                        {progress}%
                      </span>
                    </div>
                  )}
                  
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setOutput([])}
                    disabled={isScanning}
                  >
                    <RotateCcw className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="bg-gray-900 text-gray-100 p-4 rounded-lg h-96 overflow-y-auto font-mono text-sm">
                {output.length === 0 ? (
                  <div className="text-gray-500 italic">
                    {isScanning ? 'Waiting for output...' : 'No output yet. Start a scan to see results here.'}
                  </div>
                ) : (
                  output.map((line, index) => (
                    <div key={index} className="mb-1">
                      {line}
                    </div>
                  ))
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        {/* Results Tab */}
        <TabsContent value="results" className="space-y-6">
          {/* Scan Statistics */}
          {scanStats && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BarChart3 className="h-5 w-5" />
                  Scan Statistics
                </CardTitle>
                <CardDescription>
                  Overview of discovered paths and response analysis
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid gap-4 md:grid-cols-3">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-600">{scanStats.totalFound}</div>
                    <div className="text-sm text-muted-foreground">Paths Found</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-600">{Object.keys(scanStats.statusCodes || {}).length}</div>
                    <div className="text-sm text-muted-foreground">Status Codes</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-purple-600">{scanStats.avgResponseTime}ms</div>
                    <div className="text-sm text-muted-foreground">Avg Response Time</div>
                  </div>
                </div>
                
                {scanStats.statusCodes && (
                  <div className="mt-4">
                    <h4 className="text-sm font-medium mb-2">Status Code Distribution</h4>
                    <div className="flex flex-wrap gap-2">
                      {Object.entries(scanStats.statusCodes).map(([code, count]) => (
                        <Badge key={code} variant="outline">
                          {code}: {count as number}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          )}
          
          {/* Detailed Results */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="flex items-center gap-2">
                    <FileText className="h-5 w-5" />
                    Scan Results
                  </CardTitle>
                  <CardDescription>
                    Detailed findings from the Gobuster scan
                  </CardDescription>
                </div>
                
                <Button
                  variant="outline"
                  onClick={exportResults}
                  disabled={!results && output.length === 0}
                >
                  <Download className="h-4 w-4 mr-2" />
                  Export Results
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              {results ? (
                <div className="space-y-4">
                  <pre className="bg-muted p-4 rounded-lg overflow-auto text-sm max-h-96">
                    {JSON.stringify(results, null, 2)}
                  </pre>
                </div>
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  <FolderSearch className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>No scan results available yet.</p>
                  <p className="text-sm">Complete a scan to see discovered paths and files here.</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
        
        {/* Command Tab */}
        <TabsContent value="command" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Target className="h-5 w-5" />
                Generated Command
              </CardTitle>
              <CardDescription>
                The Gobuster command that will be executed based on your configuration
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="bg-muted p-4 rounded-lg">
                  <code className="text-sm font-mono break-all">
                    {generateCommand()}
                  </code>
                </div>
                
                <div className="flex justify-between items-center">
                  <p className="text-sm text-muted-foreground">
                    This command will be executed on the backend server
                  </p>
                  
                  <Button variant="outline" onClick={copyCommand}>
                    <Copy className="h-4 w-4 mr-2" />
                    Copy Command
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}