"""
Predictive Vulnerability Discovery Module for NexusScan Desktop
AI-powered system that predicts likely vulnerabilities before active scanning using pattern recognition and threat intelligence.
"""

import json
import asyncio
import logging
import hashlib
from typing import Dict, List, Optional, Any, Union, Tuple
from dataclasses import dataclass, asdict
from enum import Enum
from datetime import datetime, timedelta
import numpy as np

from .services import AIServiceManager, AIProvider, AICapability
from .threat_intelligence_engine import ThreatIntelligenceEngine
from .behavioral_analysis_engine import BehavioralAnalysisEngine
from .vulnerability_agent import VulnerabilityAgent

logger = logging.getLogger(__name__)


class VulnerabilityPredictionConfidence(Enum):
    """Confidence levels for vulnerability predictions"""
    VERY_HIGH = "very_high"      # 90%+ confidence
    HIGH = "high"                # 75-89% confidence
    MEDIUM = "medium"            # 50-74% confidence
    LOW = "low"                  # 25-49% confidence
    VERY_LOW = "very_low"        # <25% confidence


class PredictionCategory(Enum):
    """Categories of vulnerability predictions"""
    WEB_APPLICATION = "web_application"
    NETWORK_INFRASTRUCTURE = "network_infrastructure"
    AUTHENTICATION_AUTHORIZATION = "authentication_authorization"
    DATA_EXPOSURE = "data_exposure"
    CONFIGURATION_MANAGEMENT = "configuration_management"
    BUSINESS_LOGIC = "business_logic"
    API_SECURITY = "api_security"
    CRYPTOGRAPHIC = "cryptographic"


class VulnerabilityIndicator(Enum):
    """Indicators that suggest potential vulnerabilities"""
    OUTDATED_SOFTWARE = "outdated_software"
    WEAK_CONFIGURATION = "weak_configuration"
    SUSPICIOUS_PATTERNS = "suspicious_patterns"
    KNOWN_ATTACK_VECTORS = "known_attack_vectors"
    BEHAVIORAL_ANOMALIES = "behavioral_anomalies"
    THREAT_INTELLIGENCE_MATCH = "threat_intelligence_match"
    HISTORICAL_VULNERABILITIES = "historical_vulnerabilities"
    CODE_PATTERN_ANALYSIS = "code_pattern_analysis"


@dataclass
class VulnerabilityPrediction:
    """Predicted vulnerability with confidence and reasoning"""
    vulnerability_type: str
    category: PredictionCategory
    confidence: VulnerabilityPredictionConfidence
    likelihood_score: float  # 0.0 - 1.0
    severity_prediction: str  # CVSS severity level
    indicators: List[VulnerabilityIndicator]
    reasoning: str
    recommended_tests: List[str]
    potential_impact: str
    mitigation_preview: str
    metadata: Dict[str, Any]


@dataclass
class PredictionRequest:
    """Request for vulnerability prediction"""
    target_url: Optional[str] = None
    target_ip: Optional[str] = None
    application_info: Optional[Dict[str, Any]] = None
    network_info: Optional[Dict[str, Any]] = None
    historical_data: Optional[Dict[str, Any]] = None
    threat_context: Optional[Dict[str, Any]] = None
    scan_scope: Optional[List[str]] = None


@dataclass
class PredictionResult:
    """Result of vulnerability prediction analysis"""
    predictions: List[VulnerabilityPrediction]
    overall_risk_score: float
    priority_predictions: List[VulnerabilityPrediction]
    recommended_scan_strategy: Dict[str, Any]
    confidence_summary: Dict[str, int]
    metadata: Dict[str, Any]


class PredictiveVulnerabilityDiscovery:
    """
    AI-powered predictive vulnerability discovery system that analyzes targets
    and predicts likely vulnerabilities before active scanning.
    """
    
    def __init__(self, ai_service_manager: AIServiceManager):
        self.ai_service_manager = ai_service_manager
        self.threat_intelligence = ThreatIntelligenceEngine(ai_service_manager)
        self.behavioral_analyzer = BehavioralAnalysisEngine(ai_service_manager)
        self.vulnerability_agent = VulnerabilityAgent(ai_service_manager)
        
        # Prediction models and patterns
        self.vulnerability_patterns = self._load_vulnerability_patterns()
        self.prediction_models = {}
        self.historical_predictions = []
        
        # Learning and accuracy tracking
        self.prediction_accuracy = {
            'total_predictions': 0,
            'correct_predictions': 0,
            'accuracy_rate': 0.0,
            'category_accuracy': {},
            'confidence_calibration': {}
        }
    
    async def predict_vulnerabilities(self, request: PredictionRequest) -> PredictionResult:
        """
        Predict likely vulnerabilities for a target before active scanning.
        
        Args:
            request: Prediction request with target information
            
        Returns:
            PredictionResult with vulnerability predictions and recommendations
        """
        try:
            logger.info("Starting predictive vulnerability discovery")
            
            # Step 1: Gather and analyze target information
            target_analysis = await self._analyze_target_information(request)
            
            # Step 2: Apply threat intelligence correlation
            threat_context = await self._correlate_threat_intelligence(
                request, target_analysis
            )
            
            # Step 3: Perform behavioral pattern analysis
            behavioral_indicators = await self._analyze_behavioral_patterns(
                request, target_analysis
            )
            
            # Step 4: Generate vulnerability predictions using AI
            predictions = await self._generate_vulnerability_predictions(
                request, target_analysis, threat_context, behavioral_indicators
            )
            
            # Step 5: Calculate confidence scores and prioritize predictions
            scored_predictions = await self._score_and_prioritize_predictions(
                predictions, target_analysis
            )
            
            # Step 6: Generate recommended scan strategy
            scan_strategy = await self._generate_scan_strategy(
                scored_predictions, request
            )
            
            # Step 7: Create final prediction result
            result = self._create_prediction_result(
                scored_predictions, scan_strategy, target_analysis
            )
            
            # Update learning and accuracy tracking
            await self._update_prediction_tracking(request, result)
            
            logger.info(f"Generated {len(result.predictions)} vulnerability predictions")
            return result
            
        except Exception as e:
            logger.error(f"Predictive vulnerability discovery failed: {str(e)}")
            raise
    
    async def _analyze_target_information(self, request: PredictionRequest) -> Dict[str, Any]:
        """Analyze target information for prediction context"""
        
        analysis_prompt = f"""
        Analyze target information for vulnerability prediction:
        
        Target URL: {request.target_url}
        Target IP: {request.target_ip}
        Application Info: {json.dumps(request.application_info, indent=2) if request.application_info else 'None'}
        Network Info: {json.dumps(request.network_info, indent=2) if request.network_info else 'None'}
        
        Provide comprehensive analysis including:
        1. Technology stack identification
        2. Architecture patterns recognition
        3. Security posture assessment
        4. Common vulnerability patterns for identified technologies
        5. Risk factors and attack surface analysis
        6. Historical vulnerability trends for similar systems
        
        Return as structured JSON with vulnerability prediction insights.
        """
        
        try:
            response = await self.ai_service_manager.analyze_vulnerabilities([{
                'target_url': request.target_url,
                'target_ip': request.target_ip,
                'application_info': request.application_info,
                'analysis_context': analysis_prompt
            }])
            
            # Parse AI response
            if isinstance(response, list) and response:
                analysis = response[0]
            else:
                analysis = response
            
            # Enhance with passive reconnaissance if URL provided
            if request.target_url:
                passive_analysis = await self._perform_passive_reconnaissance(
                    request.target_url
                )
                analysis['passive_reconnaissance'] = passive_analysis
            
            return analysis
            
        except Exception as e:
            logger.warning(f"Target analysis failed, using fallback: {str(e)}")
            return self._get_fallback_target_analysis(request)
    
    async def _correlate_threat_intelligence(self, request: PredictionRequest,
                                           target_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Correlate with threat intelligence for prediction enhancement"""
        
        try:
            # Get threat intelligence for target technologies
            technologies = target_analysis.get('technologies', [])
            threat_context = {}
            
            for tech in technologies:
                tech_threats = await self.threat_intelligence.get_threat_intelligence(
                    indicators=[tech],
                    threat_types=['vulnerability', 'exploit', 'malware']
                )
                threat_context[tech] = tech_threats
            
            # Correlate with recent attack patterns
            recent_attacks = await self.threat_intelligence.correlate_threats(
                target_analysis, time_window_days=30
            )
            threat_context['recent_attack_patterns'] = recent_attacks
            
            return threat_context
            
        except Exception as e:
            logger.warning(f"Threat intelligence correlation failed: {str(e)}")
            return {}
    
    async def _analyze_behavioral_patterns(self, request: PredictionRequest,
                                         target_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze behavioral patterns for anomaly-based prediction"""
        
        behavioral_indicators = {}
        
        try:
            if request.target_url:
                # Perform behavioral analysis
                behavior_analysis = await self.behavioral_analyzer.analyze_application_behavior(
                    request.target_url
                )
                
                # Detect anomalies that might indicate vulnerabilities
                anomalies = await self.behavioral_analyzer.detect_application_anomalies(
                    request.target_url, behavior_analysis
                )
                
                behavioral_indicators = {
                    'behavior_analysis': behavior_analysis,
                    'detected_anomalies': anomalies,
                    'risk_indicators': self._extract_risk_indicators(behavior_analysis)
                }
            
        except Exception as e:
            logger.warning(f"Behavioral pattern analysis failed: {str(e)}")
        
        return behavioral_indicators
    
    async def _generate_vulnerability_predictions(self, request: PredictionRequest,
                                                target_analysis: Dict[str, Any],
                                                threat_context: Dict[str, Any],
                                                behavioral_indicators: Dict[str, Any]) -> List[VulnerabilityPrediction]:
        """Generate vulnerability predictions using AI analysis"""
        
        prediction_prompt = f"""
        Generate vulnerability predictions based on comprehensive analysis:
        
        TARGET ANALYSIS:
        {json.dumps(target_analysis, indent=2)}
        
        THREAT INTELLIGENCE:
        {json.dumps(threat_context, indent=2)}
        
        BEHAVIORAL INDICATORS:
        {json.dumps(behavioral_indicators, indent=2)}
        
        Predict likely vulnerabilities including:
        1. Web application vulnerabilities (SQL injection, XSS, CSRF, etc.)
        2. Authentication and authorization flaws
        3. Configuration and deployment issues
        4. Business logic vulnerabilities
        5. API security weaknesses
        6. Data exposure risks
        7. Infrastructure vulnerabilities
        
        For each prediction provide:
        - Vulnerability type and category
        - Confidence level and likelihood score
        - Supporting indicators and reasoning
        - Recommended testing approaches
        - Potential impact assessment
        - Mitigation strategies
        
        Focus on educational context and defensive security perspective.
        """
        
        try:
            response = await self.ai_service_manager.analyze_vulnerabilities([{
                'prediction_context': {
                    'target_analysis': target_analysis,
                    'threat_context': threat_context,
                    'behavioral_indicators': behavioral_indicators
                },
                'analysis_type': 'vulnerability_prediction',
                'additional_context': prediction_prompt
            }])
            
            # Parse AI response into structured predictions
            predictions = self._parse_ai_predictions(response, target_analysis)
            
            # Enhance predictions with pattern matching
            enhanced_predictions = await self._enhance_predictions_with_patterns(
                predictions, target_analysis
            )
            
            return enhanced_predictions
            
        except Exception as e:
            logger.error(f"AI vulnerability prediction failed: {str(e)}")
            return self._generate_fallback_predictions(target_analysis)
    
    def _parse_ai_predictions(self, ai_response: Any, target_analysis: Dict[str, Any]) -> List[VulnerabilityPrediction]:
        """Parse AI response into structured vulnerability predictions"""
        
        predictions = []
        
        try:
            if isinstance(ai_response, list) and ai_response:
                response_data = ai_response[0]
            else:
                response_data = ai_response
            
            # Extract predictions from response
            prediction_data = response_data.get('predictions', [])
            if not prediction_data and 'vulnerabilities' in response_data:
                prediction_data = response_data['vulnerabilities']
            
            for pred_item in prediction_data:
                prediction = VulnerabilityPrediction(
                    vulnerability_type=pred_item.get('type', 'Unknown'),
                    category=self._map_to_prediction_category(pred_item.get('category', '')),
                    confidence=self._map_to_confidence_level(pred_item.get('confidence', 0.5)),
                    likelihood_score=float(pred_item.get('likelihood', 0.5)),
                    severity_prediction=pred_item.get('severity', 'Medium'),
                    indicators=self._extract_indicators(pred_item.get('indicators', [])),
                    reasoning=pred_item.get('reasoning', 'AI-generated prediction'),
                    recommended_tests=pred_item.get('recommended_tests', []),
                    potential_impact=pred_item.get('impact', 'Potential security risk'),
                    mitigation_preview=pred_item.get('mitigation', 'Standard security measures'),
                    metadata={
                        'prediction_timestamp': datetime.now().isoformat(),
                        'confidence_raw': pred_item.get('confidence', 0.5),
                        'ai_reasoning': pred_item.get('detailed_reasoning', ''),
                        'target_context': target_analysis.get('technologies', [])
                    }
                )
                predictions.append(prediction)
        
        except Exception as e:
            logger.warning(f"Error parsing AI predictions: {str(e)}")
        
        return predictions
    
    async def _enhance_predictions_with_patterns(self, predictions: List[VulnerabilityPrediction],
                                               target_analysis: Dict[str, Any]) -> List[VulnerabilityPrediction]:
        """Enhance predictions using vulnerability patterns and historical data"""
        
        enhanced_predictions = []
        
        for prediction in predictions:
            # Apply pattern-based enhancements
            pattern_enhancement = self._apply_vulnerability_patterns(
                prediction, target_analysis
            )
            
            # Adjust confidence based on historical accuracy
            historical_adjustment = self._apply_historical_accuracy_adjustment(
                prediction
            )
            
            # Create enhanced prediction
            enhanced_prediction = VulnerabilityPrediction(
                vulnerability_type=prediction.vulnerability_type,
                category=prediction.category,
                confidence=self._adjust_confidence_level(
                    prediction.confidence, pattern_enhancement, historical_adjustment
                ),
                likelihood_score=min(1.0, prediction.likelihood_score + pattern_enhancement),
                severity_prediction=prediction.severity_prediction,
                indicators=prediction.indicators + self._extract_pattern_indicators(
                    prediction, target_analysis
                ),
                reasoning=f"{prediction.reasoning}\n\nPattern Analysis: {pattern_enhancement:.2f} confidence boost from historical patterns.",
                recommended_tests=prediction.recommended_tests,
                potential_impact=prediction.potential_impact,
                mitigation_preview=prediction.mitigation_preview,
                metadata={
                    **prediction.metadata,
                    'pattern_enhancement': pattern_enhancement,
                    'historical_adjustment': historical_adjustment,
                    'enhancement_timestamp': datetime.now().isoformat()
                }
            )
            
            enhanced_predictions.append(enhanced_prediction)
        
        return enhanced_predictions
    
    async def _score_and_prioritize_predictions(self, predictions: List[VulnerabilityPrediction],
                                              target_analysis: Dict[str, Any]) -> List[VulnerabilityPrediction]:
        """Score and prioritize vulnerability predictions"""
        
        # Calculate composite scores for prioritization
        for prediction in predictions:
            composite_score = self._calculate_prediction_priority_score(
                prediction, target_analysis
            )
            prediction.metadata['priority_score'] = composite_score
        
        # Sort by priority score (highest first)
        prioritized_predictions = sorted(
            predictions, 
            key=lambda p: p.metadata.get('priority_score', 0), 
            reverse=True
        )
        
        return prioritized_predictions
    
    def _calculate_prediction_priority_score(self, prediction: VulnerabilityPrediction,
                                           target_analysis: Dict[str, Any]) -> float:
        """Calculate priority score for vulnerability prediction"""
        
        # Base score from likelihood and confidence
        base_score = prediction.likelihood_score * 0.4
        
        # Confidence weighting
        confidence_weights = {
            VulnerabilityPredictionConfidence.VERY_HIGH: 1.0,
            VulnerabilityPredictionConfidence.HIGH: 0.8,
            VulnerabilityPredictionConfidence.MEDIUM: 0.6,
            VulnerabilityPredictionConfidence.LOW: 0.4,
            VulnerabilityPredictionConfidence.VERY_LOW: 0.2
        }
        confidence_score = confidence_weights.get(prediction.confidence, 0.5) * 0.3
        
        # Severity weighting
        severity_weights = {
            'Critical': 1.0,
            'High': 0.8,
            'Medium': 0.6,
            'Low': 0.4,
            'Informational': 0.2
        }
        severity_score = severity_weights.get(prediction.severity_prediction, 0.5) * 0.2
        
        # Category priority weighting
        category_weights = {
            PredictionCategory.AUTHENTICATION_AUTHORIZATION: 0.9,
            PredictionCategory.DATA_EXPOSURE: 0.85,
            PredictionCategory.WEB_APPLICATION: 0.8,
            PredictionCategory.API_SECURITY: 0.75,
            PredictionCategory.BUSINESS_LOGIC: 0.7,
            PredictionCategory.NETWORK_INFRASTRUCTURE: 0.65,
            PredictionCategory.CONFIGURATION_MANAGEMENT: 0.6,
            PredictionCategory.CRYPTOGRAPHIC: 0.8
        }
        category_score = category_weights.get(prediction.category, 0.5) * 0.1
        
        total_score = base_score + confidence_score + severity_score + category_score
        return min(1.0, total_score)
    
    async def _generate_scan_strategy(self, predictions: List[VulnerabilityPrediction],
                                    request: PredictionRequest) -> Dict[str, Any]:
        """Generate recommended scan strategy based on predictions"""
        
        strategy_prompt = f"""
        Generate optimal scan strategy based on vulnerability predictions:
        
        PREDICTIONS SUMMARY:
        {self._create_predictions_summary(predictions)}
        
        TARGET SCOPE:
        {request.scan_scope if request.scan_scope else 'Full scope'}
        
        Generate strategy including:
        1. Prioritized scanning order
        2. Recommended tools and techniques
        3. Time and resource allocation
        4. Risk-based testing approach
        5. Verification strategies for predictions
        
        Focus on efficiency and comprehensive coverage.
        """
        
        try:
            response = await self.ai_service_manager.analyze_vulnerabilities([{
                'strategy_context': {
                    'predictions': [asdict(p) for p in predictions[:10]],  # Top 10 predictions
                    'scan_scope': request.scan_scope
                },
                'analysis_type': 'scan_strategy_generation',
                'additional_context': strategy_prompt
            }])
            
            # Parse strategy response
            if isinstance(response, list) and response:
                strategy_data = response[0]
            else:
                strategy_data = response
            
            return {
                'scanning_order': strategy_data.get('scanning_order', []),
                'recommended_tools': strategy_data.get('recommended_tools', []),
                'time_allocation': strategy_data.get('time_allocation', {}),
                'risk_priorities': strategy_data.get('risk_priorities', []),
                'verification_strategy': strategy_data.get('verification_strategy', {}),
                'efficiency_optimizations': strategy_data.get('efficiency_optimizations', [])
            }
            
        except Exception as e:
            logger.warning(f"Scan strategy generation failed: {str(e)}")
            return self._generate_fallback_scan_strategy(predictions)
    
    def _create_prediction_result(self, predictions: List[VulnerabilityPrediction],
                                scan_strategy: Dict[str, Any],
                                target_analysis: Dict[str, Any]) -> PredictionResult:
        """Create final prediction result"""
        
        # Calculate overall risk score
        overall_risk_score = self._calculate_overall_risk_score(predictions)
        
        # Identify priority predictions (top 25% by priority score)
        priority_count = max(1, len(predictions) // 4)
        priority_predictions = predictions[:priority_count]
        
        # Create confidence summary
        confidence_summary = {
            confidence.value: len([p for p in predictions if p.confidence == confidence])
            for confidence in VulnerabilityPredictionConfidence
        }
        
        return PredictionResult(
            predictions=predictions,
            overall_risk_score=overall_risk_score,
            priority_predictions=priority_predictions,
            recommended_scan_strategy=scan_strategy,
            confidence_summary=confidence_summary,
            metadata={
                'prediction_timestamp': datetime.now().isoformat(),
                'total_predictions': len(predictions),
                'target_analysis': target_analysis,
                'prediction_engine_version': '1.0.0'
            }
        )
    
    def _calculate_overall_risk_score(self, predictions: List[VulnerabilityPrediction]) -> float:
        """Calculate overall risk score based on all predictions"""
        
        if not predictions:
            return 0.0
        
        # Weight predictions by confidence and severity
        weighted_scores = []
        
        for prediction in predictions:
            confidence_weight = {
                VulnerabilityPredictionConfidence.VERY_HIGH: 1.0,
                VulnerabilityPredictionConfidence.HIGH: 0.8,
                VulnerabilityPredictionConfidence.MEDIUM: 0.6,
                VulnerabilityPredictionConfidence.LOW: 0.4,
                VulnerabilityPredictionConfidence.VERY_LOW: 0.2
            }.get(prediction.confidence, 0.5)
            
            severity_weight = {
                'Critical': 1.0,
                'High': 0.8,
                'Medium': 0.6,
                'Low': 0.4,
                'Informational': 0.2
            }.get(prediction.severity_prediction, 0.5)
            
            weighted_score = prediction.likelihood_score * confidence_weight * severity_weight
            weighted_scores.append(weighted_score)
        
        # Calculate average with diminishing returns for many predictions
        average_score = sum(weighted_scores) / len(weighted_scores)
        prediction_count_factor = min(1.0, len(predictions) / 10)  # Cap at 10 predictions
        
        overall_score = average_score * (0.7 + 0.3 * prediction_count_factor)
        return min(1.0, overall_score)
    
    async def _update_prediction_tracking(self, request: PredictionRequest,
                                        result: PredictionResult) -> None:
        """Update prediction tracking for learning and accuracy measurement"""
        
        self.prediction_accuracy['total_predictions'] += len(result.predictions)
        
        # Store prediction for future accuracy validation
        prediction_record = {
            'timestamp': datetime.now().isoformat(),
            'request': asdict(request),
            'predictions': [asdict(p) for p in result.predictions],
            'overall_risk_score': result.overall_risk_score,
            'metadata': result.metadata
        }
        
        self.historical_predictions.append(prediction_record)
        
        # Limit history size
        if len(self.historical_predictions) > 1000:
            self.historical_predictions = self.historical_predictions[-1000:]
    
    async def validate_prediction_accuracy(self, scan_results: Dict[str, Any],
                                         original_predictions: List[VulnerabilityPrediction]) -> Dict[str, Any]:
        """Validate prediction accuracy against actual scan results"""
        
        validation_results = {
            'total_predictions': len(original_predictions),
            'correct_predictions': 0,
            'false_positives': 0,
            'false_negatives': 0,
            'accuracy_by_category': {},
            'accuracy_by_confidence': {}
        }
        
        # Compare predictions with actual findings
        actual_vulnerabilities = scan_results.get('vulnerabilities', [])
        
        for prediction in original_predictions:
            # Check if prediction was correct
            is_correct = self._is_prediction_correct(prediction, actual_vulnerabilities)
            
            if is_correct:
                validation_results['correct_predictions'] += 1
            else:
                validation_results['false_positives'] += 1
            
            # Update category accuracy
            category = prediction.category.value
            if category not in validation_results['accuracy_by_category']:
                validation_results['accuracy_by_category'][category] = {'correct': 0, 'total': 0}
            
            validation_results['accuracy_by_category'][category]['total'] += 1
            if is_correct:
                validation_results['accuracy_by_category'][category]['correct'] += 1
            
            # Update confidence accuracy
            confidence = prediction.confidence.value
            if confidence not in validation_results['accuracy_by_confidence']:
                validation_results['accuracy_by_confidence'][confidence] = {'correct': 0, 'total': 0}
            
            validation_results['accuracy_by_confidence'][confidence]['total'] += 1
            if is_correct:
                validation_results['accuracy_by_confidence'][confidence]['correct'] += 1
        
        # Calculate overall accuracy
        if validation_results['total_predictions'] > 0:
            accuracy_rate = validation_results['correct_predictions'] / validation_results['total_predictions']
            validation_results['accuracy_rate'] = accuracy_rate
            
            # Update global accuracy tracking
            self.prediction_accuracy['correct_predictions'] += validation_results['correct_predictions']
            self.prediction_accuracy['accuracy_rate'] = (
                self.prediction_accuracy['correct_predictions'] / 
                self.prediction_accuracy['total_predictions']
            )
        
        return validation_results
    
    def _is_prediction_correct(self, prediction: VulnerabilityPrediction,
                              actual_vulnerabilities: List[Dict[str, Any]]) -> bool:
        """Check if a prediction matches actual scan results"""
        
        prediction_type = prediction.vulnerability_type.lower()
        
        for vuln in actual_vulnerabilities:
            vuln_type = vuln.get('type', '').lower()
            vuln_name = vuln.get('name', '').lower()
            
            # Check for type match or similar vulnerability
            if (prediction_type in vuln_type or 
                vuln_type in prediction_type or
                prediction_type in vuln_name):
                return True
        
        return False
    
    # Helper methods for various prediction aspects
    
    def _load_vulnerability_patterns(self) -> Dict[str, Any]:
        """Load vulnerability patterns for prediction enhancement"""
        
        return {
            'web_application': {
                'sql_injection': {
                    'indicators': ['database_errors', 'parameter_injection_points', 'dynamic_queries'],
                    'technologies': ['mysql', 'postgresql', 'mssql', 'oracle'],
                    'confidence_boost': 0.15
                },
                'xss': {
                    'indicators': ['user_input_reflection', 'insufficient_encoding', 'dom_manipulation'],
                    'technologies': ['javascript', 'html', 'ajax'],
                    'confidence_boost': 0.12
                },
                'csrf': {
                    'indicators': ['missing_csrf_tokens', 'state_changing_operations', 'cookie_authentication'],
                    'technologies': ['web_forms', 'session_management'],
                    'confidence_boost': 0.10
                }
            },
            'authentication': {
                'weak_passwords': {
                    'indicators': ['no_password_policy', 'default_credentials', 'weak_complexity'],
                    'confidence_boost': 0.20
                },
                'session_fixation': {
                    'indicators': ['predictable_session_ids', 'session_reuse', 'no_regeneration'],
                    'confidence_boost': 0.15
                }
            },
            'configuration': {
                'default_configurations': {
                    'indicators': ['default_passwords', 'unnecessary_services', 'verbose_errors'],
                    'confidence_boost': 0.18
                },
                'information_disclosure': {
                    'indicators': ['debug_information', 'version_disclosure', 'directory_listing'],
                    'confidence_boost': 0.14
                }
            }
        }
    
    def _map_to_prediction_category(self, category_text: str) -> PredictionCategory:
        """Map text to prediction category enum"""
        
        category_mapping = {
            'web': PredictionCategory.WEB_APPLICATION,
            'network': PredictionCategory.NETWORK_INFRASTRUCTURE,
            'auth': PredictionCategory.AUTHENTICATION_AUTHORIZATION,
            'data': PredictionCategory.DATA_EXPOSURE,
            'config': PredictionCategory.CONFIGURATION_MANAGEMENT,
            'business': PredictionCategory.BUSINESS_LOGIC,
            'api': PredictionCategory.API_SECURITY,
            'crypto': PredictionCategory.CRYPTOGRAPHIC
        }
        
        category_lower = category_text.lower()
        for key, category in category_mapping.items():
            if key in category_lower:
                return category
        
        return PredictionCategory.WEB_APPLICATION  # Default
    
    def _map_to_confidence_level(self, confidence_score: float) -> VulnerabilityPredictionConfidence:
        """Map confidence score to confidence level enum"""
        
        if confidence_score >= 0.9:
            return VulnerabilityPredictionConfidence.VERY_HIGH
        elif confidence_score >= 0.75:
            return VulnerabilityPredictionConfidence.HIGH
        elif confidence_score >= 0.5:
            return VulnerabilityPredictionConfidence.MEDIUM
        elif confidence_score >= 0.25:
            return VulnerabilityPredictionConfidence.LOW
        else:
            return VulnerabilityPredictionConfidence.VERY_LOW
    
    def _extract_indicators(self, indicators_data: List[Any]) -> List[VulnerabilityIndicator]:
        """Extract vulnerability indicators from data"""
        
        indicators = []
        indicator_mapping = {
            'outdated': VulnerabilityIndicator.OUTDATED_SOFTWARE,
            'configuration': VulnerabilityIndicator.WEAK_CONFIGURATION,
            'pattern': VulnerabilityIndicator.SUSPICIOUS_PATTERNS,
            'attack': VulnerabilityIndicator.KNOWN_ATTACK_VECTORS,
            'behavior': VulnerabilityIndicator.BEHAVIORAL_ANOMALIES,
            'threat': VulnerabilityIndicator.THREAT_INTELLIGENCE_MATCH,
            'historical': VulnerabilityIndicator.HISTORICAL_VULNERABILITIES,
            'code': VulnerabilityIndicator.CODE_PATTERN_ANALYSIS
        }
        
        for indicator_item in indicators_data:
            indicator_text = str(indicator_item).lower()
            for key, indicator_enum in indicator_mapping.items():
                if key in indicator_text:
                    indicators.append(indicator_enum)
                    break
        
        return indicators
    
    async def _perform_passive_reconnaissance(self, target_url: str) -> Dict[str, Any]:
        """Perform passive reconnaissance for additional context"""
        
        passive_data = {
            'headers_analysis': {},
            'technology_detection': [],
            'certificate_analysis': {},
            'dns_information': {},
            'subdomain_enumeration': []
        }
        
        try:
            # This would integrate with actual passive reconnaissance tools
            # For now, providing structure for future implementation
            logger.info(f"Passive reconnaissance for {target_url} - structure prepared")
            
        except Exception as e:
            logger.warning(f"Passive reconnaissance failed: {str(e)}")
        
        return passive_data
    
    def _extract_risk_indicators(self, behavior_analysis: Dict[str, Any]) -> List[str]:
        """Extract risk indicators from behavioral analysis"""
        
        risk_indicators = []
        
        # Analyze behavioral patterns for risk indicators
        if behavior_analysis.get('anomalies'):
            risk_indicators.append('behavioral_anomalies_detected')
        
        if behavior_analysis.get('suspicious_patterns'):
            risk_indicators.append('suspicious_behavior_patterns')
        
        if behavior_analysis.get('error_patterns'):
            risk_indicators.append('error_handling_issues')
        
        return risk_indicators
    
    def _apply_vulnerability_patterns(self, prediction: VulnerabilityPrediction,
                                    target_analysis: Dict[str, Any]) -> float:
        """Apply vulnerability patterns to enhance prediction confidence"""
        
        pattern_boost = 0.0
        
        try:
            # Get patterns for prediction category
            category_patterns = self.vulnerability_patterns.get(
                prediction.category.value.split('_')[0], {}
            )
            
            # Look for matching patterns
            prediction_type = prediction.vulnerability_type.lower()
            
            for pattern_name, pattern_data in category_patterns.items():
                if pattern_name in prediction_type or any(
                    indicator in prediction_type 
                    for indicator in pattern_data.get('indicators', [])
                ):
                    pattern_boost += pattern_data.get('confidence_boost', 0.0)
                    break
        
        except Exception as e:
            logger.warning(f"Pattern application failed: {str(e)}")
        
        return min(0.3, pattern_boost)  # Cap boost at 0.3
    
    def _apply_historical_accuracy_adjustment(self, prediction: VulnerabilityPrediction) -> float:
        """Apply historical accuracy adjustment to prediction"""
        
        category_accuracy = self.prediction_accuracy.get('category_accuracy', {})
        category_stats = category_accuracy.get(prediction.category.value, {})
        
        if category_stats.get('total', 0) > 10:  # Enough data for adjustment
            accuracy_rate = category_stats['correct'] / category_stats['total']
            # Adjust based on historical accuracy (±0.1)
            return (accuracy_rate - 0.5) * 0.2
        
        return 0.0  # No adjustment if insufficient data
    
    def _adjust_confidence_level(self, original_confidence: VulnerabilityPredictionConfidence,
                               pattern_enhancement: float,
                               historical_adjustment: float) -> VulnerabilityPredictionConfidence:
        """Adjust confidence level based on enhancements"""
        
        # Convert to numeric for calculation
        confidence_values = {
            VulnerabilityPredictionConfidence.VERY_LOW: 0.15,
            VulnerabilityPredictionConfidence.LOW: 0.35,
            VulnerabilityPredictionConfidence.MEDIUM: 0.60,
            VulnerabilityPredictionConfidence.HIGH: 0.80,
            VulnerabilityPredictionConfidence.VERY_HIGH: 0.95
        }
        
        original_value = confidence_values[original_confidence]
        adjusted_value = original_value + pattern_enhancement + historical_adjustment
        adjusted_value = max(0.0, min(1.0, adjusted_value))
        
        # Convert back to enum
        return self._map_to_confidence_level(adjusted_value)
    
    def _extract_pattern_indicators(self, prediction: VulnerabilityPrediction,
                                   target_analysis: Dict[str, Any]) -> List[VulnerabilityIndicator]:
        """Extract additional indicators from pattern analysis"""
        
        additional_indicators = []
        
        # Check for pattern-based indicators
        technologies = target_analysis.get('technologies', [])
        
        if any(tech in ['mysql', 'postgresql', 'mssql'] for tech in technologies):
            if 'sql' in prediction.vulnerability_type.lower():
                additional_indicators.append(VulnerabilityIndicator.CODE_PATTERN_ANALYSIS)
        
        if 'javascript' in technologies:
            if 'xss' in prediction.vulnerability_type.lower():
                additional_indicators.append(VulnerabilityIndicator.SUSPICIOUS_PATTERNS)
        
        return additional_indicators
    
    def _create_predictions_summary(self, predictions: List[VulnerabilityPrediction]) -> str:
        """Create summary of predictions for strategy generation"""
        
        summary_lines = []
        
        # Group by category
        category_counts = {}
        for prediction in predictions:
            category = prediction.category.value
            if category not in category_counts:
                category_counts[category] = []
            category_counts[category].append(prediction)
        
        for category, category_predictions in category_counts.items():
            high_confidence = [p for p in category_predictions 
                             if p.confidence in [VulnerabilityPredictionConfidence.HIGH, 
                                               VulnerabilityPredictionConfidence.VERY_HIGH]]
            summary_lines.append(f"{category}: {len(category_predictions)} predictions ({len(high_confidence)} high confidence)")
        
        return '\n'.join(summary_lines)
    
    def _generate_fallback_predictions(self, target_analysis: Dict[str, Any]) -> List[VulnerabilityPrediction]:
        """Generate fallback predictions when AI analysis fails"""
        
        fallback_predictions = [
            VulnerabilityPrediction(
                vulnerability_type="SQL Injection",
                category=PredictionCategory.WEB_APPLICATION,
                confidence=VulnerabilityPredictionConfidence.MEDIUM,
                likelihood_score=0.6,
                severity_prediction="High",
                indicators=[VulnerabilityIndicator.SUSPICIOUS_PATTERNS],
                reasoning="Common web application vulnerability - requires verification",
                recommended_tests=["SQLMap scanning", "Manual parameter testing"],
                potential_impact="Database compromise, data extraction",
                mitigation_preview="Input validation, parameterized queries",
                metadata={'fallback_prediction': True}
            ),
            VulnerabilityPrediction(
                vulnerability_type="Cross-Site Scripting (XSS)",
                category=PredictionCategory.WEB_APPLICATION,
                confidence=VulnerabilityPredictionConfidence.MEDIUM,
                likelihood_score=0.5,
                severity_prediction="Medium",
                indicators=[VulnerabilityIndicator.SUSPICIOUS_PATTERNS],
                reasoning="Common web application vulnerability - requires verification",
                recommended_tests=["XSS payload testing", "Input reflection analysis"],
                potential_impact="Session hijacking, user impersonation",
                mitigation_preview="Input encoding, Content Security Policy",
                metadata={'fallback_prediction': True}
            )
        ]
        
        return fallback_predictions
    
    def _generate_fallback_scan_strategy(self, predictions: List[VulnerabilityPrediction]) -> Dict[str, Any]:
        """Generate fallback scan strategy"""
        
        return {
            'scanning_order': ['Web Application Testing', 'Network Discovery', 'Authentication Testing'],
            'recommended_tools': ['Nmap', 'Nuclei', 'SQLMap'],
            'time_allocation': {'web_testing': '40%', 'network_scanning': '30%', 'auth_testing': '30%'},
            'risk_priorities': ['High', 'Medium', 'Low'],
            'verification_strategy': {'manual_verification': True, 'automated_confirmation': True},
            'efficiency_optimizations': ['Prioritize high-confidence predictions', 'Parallel scanning where possible']
        }
    
    def _get_fallback_target_analysis(self, request: PredictionRequest) -> Dict[str, Any]:
        """Provide fallback target analysis when AI analysis fails"""
        
        return {
            'technologies': ['web_application', 'http_server'],
            'architecture_patterns': ['standard_web_app'],
            'security_posture': 'unknown',
            'risk_factors': ['standard_web_risks'],
            'attack_surface': 'web_interface',
            'confidence': 0.3
        }