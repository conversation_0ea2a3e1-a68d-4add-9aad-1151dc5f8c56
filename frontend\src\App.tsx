import React, { useEffect, useState } from 'react';
import { Routes, Route, Navigate, useLocation } from 'react-router-dom';
import { ErrorBoundary } from 'react-error-boundary';
import { cn } from '@/lib/utils';

// Layout components
import { Sidebar } from '@/components/layout/Sidebar';
import { Header } from '@/components/layout/Header';
import { StatusBar } from '@/components/layout/StatusBar';
import { Terminal } from '@/components/layout/Terminal';

// PWA components
import PWAInstallPrompt from '@/components/pwa/PWAInstallPrompt';
import PWAUpdatePrompt from '@/components/pwa/PWAUpdatePrompt';
import OfflineStatus from '@/components/pwa/OfflineStatus';

// Page components
import { DashboardPage } from '@/pages/DashboardPage';
import { CampaignsPage } from '@/pages/CampaignsPage';
import { ReportsPage } from '@/pages/ReportsPage';
import { SettingsPage } from '@/pages/SettingsPage';

// Tool pages
import { NetworkScanningPage } from '@/pages/tools/NetworkScanningPage';
import { WebTestingPage } from '@/pages/tools/WebTestingPage';
import { VulnerabilityAssessmentPage } from '@/pages/tools/VulnerabilityAssessmentPage';
import { PasswordToolsPage } from '@/pages/tools/PasswordToolsPage';
import { SSLTestingPage } from '@/pages/tools/SSLTestingPage';
import { ExploitationPage } from '@/pages/tools/ExploitationPage';

// Ferrari AI pages
import { FerrariDashboard } from '@/pages/ferrari/FerrariDashboard';
import { OrchestratorPage } from '@/pages/ferrari/OrchestratorPage';
import { CreativeExploitsPage } from '@/pages/ferrari/CreativeExploitsPage';
import { BehavioralAnalysisPage } from '@/pages/ferrari/BehavioralAnalysisPage';
import { AIProxyPage } from '@/pages/ferrari/AIProxyPage';

// Error components
import { ErrorFallback } from '@/components/ui/ErrorFallback';

// Debug components removed - backend now fully functional

// Stores
import { useAppStore } from '@/stores/app-store';
import { useBackendStore } from '@/stores/backend-store';

// Services - Web only (no Electron services needed)

/**
 * Main application component
 */
function App() {
  const location = useLocation();
  
  // App state
  const {
    isLoading,
    sidebarOpen,
    terminalOpen,
    theme,
    setLoading,
    toggleSidebar,
    toggleTerminal
  } = useAppStore();
  
  const { status: backendStatus, connect: connectBackend, refreshTools } = useBackendStore();
  
  // Debug logging for remote backend status
  React.useEffect(() => {
    console.log('📊 App.tsx - Remote Backend Status:', {
      connected: backendStatus.connected,
      error: backendStatus.error,
      url: backendStatus.url,
      tools: backendStatus.tools,
      hasTools: !!backendStatus.tools,
      toolsType: typeof backendStatus.tools
    });

    // Log what we're passing to StatusBar
    console.log('🎯 App.tsx - Passing to StatusBar (Remote Mode):', {
      backendStatus: backendStatus
    });
  }, [backendStatus]);
  
  // Local state
  const [isInitialized, setIsInitialized] = useState(false);
  const [initError, setInitError] = useState<string | null>(null);
  
  /**
   * Initialize application
   */
  useEffect(() => {
    async function initializeApp() {
      try {
        setLoading(true);
        console.log('🚀 Initializing NexusScan Desktop App...');
        
        // Running in web mode
        console.log('🌐 Running in web mode');
        
        // Connect to backend (with timeout)
        console.log('🔗 Connecting to backend...');
        
        const backendTimeout = new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Backend connection timeout')), 15000)
        );
        
        try {
          const connected = await Promise.race([connectBackend(), backendTimeout]);
          console.log('✅ Backend connection completed, status:', connected);
          
          // Force refresh tools after connection
          if (connected) {
            console.log('🔧 Force refreshing tools after connection...');
            await refreshTools();
            console.log('🔧 Tools refresh completed');
          }
        } catch (error) {
          console.warn('⚠️ Backend connection failed or timed out:', error);
          // Continue initialization even if backend fails
        }
        
        // WSL initialization removed - using remote backend exclusively
        console.log('🌐 Remote backend mode - WSL not required');
        
        // Setup event listeners
        try {
          setupEventListeners();
          console.log('✅ Event listeners setup complete');
        } catch (error) {
          console.warn('⚠️ Event listeners setup failed:', error);
        }
        
        // Always complete initialization
        setIsInitialized(true);
        console.log('✅ NexusScan Desktop App initialized successfully');
        
      } catch (error) {
        console.error('❌ App initialization failed:', error);
        // Don't show error screen for initialization failures - just continue
        setIsInitialized(true);
        console.log('✅ NexusScan Desktop App initialized (with warnings)');
      } finally {
        setLoading(false);
      }
    }
    
    initializeApp();
  }, [setLoading, connectBackend]);
  
  /**
   * Setup event listeners (Web mode - no IPC needed)
   */
  function setupEventListeners() {
    // Web mode - no Electron IPC events needed
    console.log('👂 Web mode - no IPC event listeners needed');
  }
  
  /**
   * Handle keyboard shortcuts
   */
  useEffect(() => {
    function handleKeyboard(event: KeyboardEvent) {
      // Only handle if not in input field
      if (event.target instanceof HTMLInputElement || 
          event.target instanceof HTMLTextAreaElement) {
        return;
      }
      
      const { ctrlKey, metaKey, shiftKey, key } = event;
      const modifier = ctrlKey || metaKey;
      
      if (modifier) {
        switch (key) {
          case 'b':
            event.preventDefault();
            toggleSidebar();
            break;
          case 't':
            event.preventDefault();
            toggleTerminal();
            break;
          case '1':
            event.preventDefault();
            window.history.pushState(null, '', '/dashboard');
            break;
          case '2':
            event.preventDefault();
            window.history.pushState(null, '', '/campaigns');
            break;
          case '3':
            event.preventDefault();
            window.history.pushState(null, '', '/reports');
            break;
        }
      }
    }
    
    window.addEventListener('keydown', handleKeyboard);
    return () => window.removeEventListener('keydown', handleKeyboard);
  }, [toggleSidebar, toggleTerminal]);
  
  /**
   * Show loading screen during initialization
   */
  if (isLoading || !isInitialized) {
    return (
      <div className="flex items-center justify-center h-screen bg-background">
        <div className="text-center space-y-4">
          <div className="loading-spinner mx-auto"></div>
          <div className="text-lg font-semibold">Initializing NexusScan Desktop...</div>
          <div className="text-sm text-muted-foreground">
            {backendStatus.connected ? 'Backend connected' : 'Connecting to backend...'}
          </div>
        </div>
      </div>
    );
  }
  
  /**
   * Show error screen if initialization failed
   */
  if (initError) {
    return (
      <div className="flex items-center justify-center h-screen bg-background p-8">
        <div className="text-center space-y-4 max-w-md">
          <div className="text-red-500 text-xl">⚠️</div>
          <div className="text-lg font-semibold">Initialization Failed</div>
          <div className="text-sm text-muted-foreground">{initError}</div>
          <button
            onClick={() => window.location.reload()}
            className="px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90"
          >
            Restart Application
          </button>
        </div>
      </div>
    );
  }
  
  return (
    <div className={cn('h-screen flex flex-col bg-background', theme)}>
      {/* Debug panel removed - backend now fully functional */}

      <ErrorBoundary
        FallbackComponent={ErrorFallback}
        onError={(error, errorInfo) => {
          console.error('🚨 React Error Boundary:', error, errorInfo);
        }}
        onReset={() => {
          window.location.reload();
        }}
      >
        {/* Header */}
        <Header />
        
        {/* Main content area - Mobile responsive */}
        <div className="flex flex-1 overflow-hidden">
          {/* Desktop Sidebar - Hidden on mobile */}
          <div className="hidden md:block">
            <Sidebar
              isOpen={sidebarOpen}
              currentPath={location.pathname}
            />
          </div>

          {/* Main content */}
          <main className="flex-1 flex flex-col overflow-hidden min-w-0">
            {/* Page content - Mobile responsive */}
            <div className="flex-1 overflow-auto px-4 py-6 md:px-6">{/* Mobile padding added */}
              <Routes>
                {/* Default redirect */}
                <Route path="/" element={<Navigate to="/dashboard" replace />} />
                
                {/* Core pages */}
                <Route path="/dashboard" element={<DashboardPage />} />
                <Route path="/campaigns/*" element={<CampaignsPage />} />
                <Route path="/reports/*" element={<ReportsPage />} />
                <Route path="/settings/*" element={<SettingsPage />} />
                
                {/* Tool pages */}
                <Route path="/tools/network-scanning/*" element={<NetworkScanningPage />} />
                <Route path="/tools/web-testing/*" element={<WebTestingPage />} />
                <Route path="/tools/vulnerability-assessment/*" element={<VulnerabilityAssessmentPage />} />
                <Route path="/tools/password-tools/*" element={<PasswordToolsPage />} />
                <Route path="/tools/ssl-testing/*" element={<SSLTestingPage />} />
                <Route path="/tools/exploitation/*" element={<ExploitationPage />} />
                
                {/* Ferrari AI pages */}
                <Route path="/ferrari" element={<FerrariDashboard />} />
                <Route path="/ferrari/orchestrator/*" element={<OrchestratorPage />} />
                <Route path="/ferrari/creative-exploits/*" element={<CreativeExploitsPage />} />
                <Route path="/ferrari/behavioral-analysis/*" element={<BehavioralAnalysisPage />} />
                <Route path="/ferrari/ai-proxy/*" element={<AIProxyPage />} />
                
                {/* Fallback for unknown routes */}
                <Route path="*" element={
                  <div className="flex items-center justify-center h-full">
                    <div className="text-center space-y-4">
                      <div className="text-4xl">🔍</div>
                      <div className="text-xl font-semibold">Page Not Found</div>
                      <div className="text-muted-foreground">
                        The page you're looking for doesn't exist.
                      </div>
                      <button
                        onClick={() => window.history.pushState(null, '', '/dashboard')}
                        className="px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90"
                      >
                        Go to Dashboard
                      </button>
                    </div>
                  </div>
                } />
              </Routes>
            </div>
            
            {/* Terminal - Hidden on mobile by default */}
            {terminalOpen && (
              <div className="hidden md:block">
                <Terminal
                  isOpen={terminalOpen}
                  onToggle={toggleTerminal}
                />
              </div>
            )}
          </main>
        </div>
        
        {/* Status bar - Remote Backend Mode */}
        <StatusBar
          backendStatus={backendStatus}
        />

        {/* PWA Components */}
        <PWAInstallPrompt />
        <PWAUpdatePrompt />
        <OfflineStatus />
      </ErrorBoundary>
    </div>
  );
}

export default App;