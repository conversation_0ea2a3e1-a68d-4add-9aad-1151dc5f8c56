/**
 * Simple Real-World Backend Test
 * Tests that frontend can access all 23 tools from Contabo backend
 */

// Test the API client directly against real backend
async function testRealBackend() {
  console.log('🚀 Testing real Contabo backend connection...')

  const backendUrl = 'http://161.97.99.62:8090'
  
  try {
    // Test 1: Health check
    console.log('1️⃣ Testing health endpoint...')
    const healthResponse = await fetch(`${backendUrl}/api/health`)
    const healthData = await healthResponse.json()
    console.log('✅ Health response:', healthData)
    
    // Test 2: Get all tools
    console.log('2️⃣ Testing tools endpoint...')
    const toolsResponse = await fetch(`${backendUrl}/api/tools/available`)
    const toolsData = await toolsResponse.json()
    
    // Handle the response format
    const tools = toolsData.data || toolsData.tools || toolsData || []
    
    console.log(`📊 Found ${tools.length} tools`)
    console.log('📋 Tools list:')
    tools.forEach((tool, index) => {
      console.log(`   ${index + 1}. ${tool.name} (${tool.id}) - ${tool.status}`)
    })
    
    // Test 3: Verify we have 20+ tools (should be 23)
    if (tools.length >= 20) {
      console.log(`🎉 SUCCESS: Found ${tools.length} tools (expected 23)`)
      return true
    } else {
      console.log(`❌ FAILED: Only found ${tools.length} tools, expected 23`)
      return false
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error)
    return false
  }
}

// Run the test
testRealBackend().then(success => {
  if (success) {
    console.log('✅ Real backend test PASSED')
  } else {
    console.log('❌ Real backend test FAILED')
  }
})