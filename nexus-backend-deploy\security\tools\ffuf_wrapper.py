#!/usr/bin/env python3
"""
FFuf Wrapper for NexusScan Desktop
Fast web fuzzer integration
"""

import asyncio
import logging
from typing import Dict, List, Optional, Callable, Any
from datetime import datetime

from security.tools.tool_registry import (
    ToolMetadata, ToolCategory, ToolCapabilities,
    ScanOptions, ScanResult, ToolStatus, register_tool
)
from security.tools.external_tool_wrapper import ExternalToolWrapper

logger = logging.getLogger(__name__)


@register_tool
class FFufWrapper(ExternalToolWrapper):
    """FFuf fast web fuzzer wrapper"""
    
    def __init__(self):
        super().__init__()
        self.tool_executable = "ffuf"
    
    def get_metadata(self) -> ToolMetadata:
        return ToolMetadata(
            name="ffuf",
            display_name="FFuf",
            description="Fast web fuzzer written in Go",
            version="1.0.0",
            category=ToolCategory.WEB_SCANNER,
            capabilities=ToolCapabilities(
                supports_async=True,
                network_access_required=True,
                supported_targets=["url"],
                requires_root=False
            ),
            default_options={
                "wordlist": "/usr/share/wordlists/dirb/common.txt",
                "mode": "dir",  # dir, dns, vhost
                "threads": 40,
                "rate": 0,  # requests per second, 0 = unlimited
                "extensions": "",  # file extensions to fuzz
                "follow_redirects": False,
                "timeout": 10,
                "matchers": {
                    "status": "200,204,301,302,307,401,403",
                    "size": None,
                    "words": None,
                    "lines": None
                },
                "filters": {
                    "status": None,
                    "size": None,
                    "words": None,
                    "lines": None
                }
            }
        )
    
    def get_tool_command(self, options: ScanOptions) -> List[str]:
        """Get FFuf command"""
        wordlist = options.custom_options.get("wordlist", "/usr/share/wordlists/dirb/common.txt")
        mode = options.custom_options.get("mode", "dir")
        threads = options.custom_options.get("threads", 40)
        rate = options.custom_options.get("rate", 0)
        timeout = options.custom_options.get("timeout", 10)
        
        command = [
            "ffuf",
            "-u", options.target if "FUZZ" in options.target else f"{options.target}/FUZZ",
            "-w", wordlist,
            "-t", str(threads),
            "-timeout", str(timeout),
            "-v"  # Verbose output
        ]
        
        # Add rate limiting if specified
        if rate > 0:
            command.extend(["-rate", str(rate)])
        
        # Add extensions if specified
        extensions = options.custom_options.get("extensions", "")
        if extensions:
            command.extend(["-e", extensions])
        
        # Add follow redirects
        if options.custom_options.get("follow_redirects", False):
            command.append("-r")
        
        # Add matchers
        matchers = options.custom_options.get("matchers", {})
        if matchers.get("status"):
            command.extend(["-mc", matchers["status"]])
        if matchers.get("size"):
            command.extend(["-ms", str(matchers["size"])])
        if matchers.get("words"):
            command.extend(["-mw", str(matchers["words"])])
        if matchers.get("lines"):
            command.extend(["-ml", str(matchers["lines"])])
        
        # Add filters
        filters = options.custom_options.get("filters", {})
        if filters.get("status"):
            command.extend(["-fc", filters["status"]])
        if filters.get("size"):
            command.extend(["-fs", str(filters["size"])])
        if filters.get("words"):
            command.extend(["-fw", str(filters["words"])])
        if filters.get("lines"):
            command.extend(["-fl", str(filters["lines"])])
        
        # Mode-specific options
        if mode == "vhost":
            command.extend(["-H", "Host: FUZZ"])
        elif mode == "dns":
            # For DNS mode, target should be domain
            command[2] = f"FUZZ.{options.target}"
        
        return command
    
    def parse_tool_output(self, output: str, target: str) -> Dict[str, Any]:
        """Parse FFuf output"""
        lines = output.strip().split('\n')
        found_items = []
        stats = {
            "requests_total": 0,
            "requests_per_sec": 0,
            "duration": 0
        }
        
        for line in lines:
            # Parse found items
            if " [Status: " in line and not line.startswith("::"):
                parts = line.split()
                if len(parts) >= 5:
                    # Extract URL/path
                    url_path = parts[0]
                    
                    # Extract status code
                    status = None
                    for i, part in enumerate(parts):
                        if part == "[Status:" and i + 1 < len(parts):
                            status = parts[i + 1].rstrip(",]")
                            break
                    
                    # Extract size
                    size = None
                    for i, part in enumerate(parts):
                        if part == "Size:" and i + 1 < len(parts):
                            size = parts[i + 1].rstrip(",]")
                            break
                    
                    # Extract words
                    words = None
                    for i, part in enumerate(parts):
                        if part == "Words:" and i + 1 < len(parts):
                            words = parts[i + 1].rstrip(",]")
                            break
                    
                    found_items.append({
                        "path": url_path,
                        "url": f"{target.rstrip('/')}/{url_path.lstrip('/')}",
                        "status": int(status) if status else 0,
                        "size": int(size) if size else 0,
                        "words": int(words) if words else 0,
                        "type": "directory" if url_path.endswith('/') else "file"
                    })
            
            # Parse statistics
            elif ":: Progress:" in line:
                if "Req/sec" in line:
                    parts = line.split()
                    for i, part in enumerate(parts):
                        if part == "Req/sec:" and i + 1 < len(parts):
                            try:
                                stats["requests_per_sec"] = float(parts[i + 1])
                            except:
                                pass
            elif line.startswith("Requests:"):
                parts = line.split()
                if len(parts) >= 2:
                    try:
                        stats["requests_total"] = int(parts[1])
                    except:
                        pass
            elif line.startswith("Duration:"):
                parts = line.split()
                if len(parts) >= 2:
                    try:
                        duration_str = parts[1]
                        # Parse duration (could be in format like "1m30s")
                        stats["duration"] = duration_str
                    except:
                        pass
        
        # Sort items by status code and path
        found_items.sort(key=lambda x: (x["status"], x["path"]))
        
        # Create vulnerability findings
        vulnerabilities = []
        for item in found_items:
            if item["status"] in [200, 301, 302, 401, 403]:
                severity = "info"
                if item["status"] == 200:
                    severity = "low"
                elif item["status"] in [401, 403]:
                    severity = "medium"
                
                vulnerabilities.append({
                    "name": f"Exposed {item['type']}: {item['path']}",
                    "severity": severity,
                    "description": f"Found {item['type']} at {item['url']} with status {item['status']}",
                    "type": "information_disclosure",
                    "cve": None,
                    "remediation": "Review if this resource should be publicly accessible"
                })
        
        return {
            "target": target,
            "items_found": found_items,
            "total_found": len(found_items),
            "statistics": stats,
            "vulnerabilities": vulnerabilities,
            "summary": {
                "directories": len([i for i in found_items if i["type"] == "directory"]),
                "files": len([i for i in found_items if i["type"] == "file"]),
                "status_200": len([i for i in found_items if i["status"] == 200]),
                "status_301_302": len([i for i in found_items if i["status"] in [301, 302]]),
                "status_401_403": len([i for i in found_items if i["status"] in [401, 403]])
            }
        }
    
    async def execute_simulation(self, options: ScanOptions,
                                 progress_callback: Optional[Callable[[float, str], None]] = None) -> ScanResult:
        """Execute simulated scan for Railway/testing"""
        start_time = datetime.now().isoformat()
        
        if progress_callback:
            await progress_callback(0.1, "Starting fuzzing operation...")
        
        await asyncio.sleep(1)
        
        if progress_callback:
            await progress_callback(0.5, "Fuzzing directories and files...")
        
        await asyncio.sleep(1)
        
        # Simulated output
        simulated_output = f"""
        /'___\\  /'___\\           /'___\\       
       /\\ \\__/ /\\ \\__/  __  __  /\\ \\__/       
       \\ \\ ,__\\\\ \\ ,__\\/\\ \\/\\ \\ \\ \\ ,__\\      
        \\ \\ \\_/ \\ \\ \\_/\\ \\ \\_\\ \\ \\ \\ \\_/      
         \\ \\_\\   \\ \\_\\  \\ \\____/  \\ \\_\\       
          \\/_/    \\/_/   \\/___/    \\/_/       

       v2.1.0
________________________________________________

 :: Method           : GET
 :: URL              : {options.target}/FUZZ
 :: Wordlist         : FUZZ: /usr/share/wordlists/dirb/common.txt
 :: Follow redirects : false
 :: Calibration      : false
 :: Timeout          : 10
 :: Threads          : 40
 :: Matcher          : Response status: 200,204,301,302,307,401,403
________________________________________________

.htaccess               [Status: 403, Size: 276, Words: 20, Lines: 10]
.htpasswd               [Status: 403, Size: 276, Words: 20, Lines: 10]
admin                   [Status: 301, Size: 234, Words: 14, Lines: 8]
api                     [Status: 301, Size: 234, Words: 14, Lines: 8]
assets                  [Status: 301, Size: 234, Words: 14, Lines: 8]
backup                  [Status: 403, Size: 276, Words: 20, Lines: 10]
config                  [Status: 403, Size: 276, Words: 20, Lines: 10]
css                     [Status: 301, Size: 234, Words: 14, Lines: 8]
images                  [Status: 301, Size: 234, Words: 14, Lines: 8]
index.html              [Status: 200, Size: 1256, Words: 85, Lines: 32]
js                      [Status: 301, Size: 234, Words: 14, Lines: 8]
login                   [Status: 200, Size: 3456, Words: 123, Lines: 45]
robots.txt              [Status: 200, Size: 48, Words: 4, Lines: 2]
uploads                 [Status: 301, Size: 234, Words: 14, Lines: 8]
:: Progress: [4614/4614] :: Job [1/1] :: 2834 req/sec :: Duration: [0:00:02] :: Errors: 0 ::"""
        
        parsed_results = self.parse_tool_output(simulated_output, options.target)
        
        if progress_callback:
            await progress_callback(1.0, "Fuzzing complete")
        
        return ScanResult(
            tool_name=self.metadata.name,
            target=options.target,
            status=ToolStatus.COMPLETED,
            start_time=start_time,
            end_time=datetime.now().isoformat(),
            duration_seconds=(datetime.now() - datetime.fromisoformat(start_time)).total_seconds(),
            raw_output=simulated_output,
            parsed_results=parsed_results,
            vulnerabilities=parsed_results.get("vulnerabilities", []),
            metadata={
                "wordlist_used": options.custom_options.get("wordlist", "/usr/share/wordlists/dirb/common.txt"),
                "items_found": parsed_results.get("total_found", 0),
                "requests_made": parsed_results.get("statistics", {}).get("requests_total", 0)
            }
        )