import{a as T,g as P,R as U}from"./react-D5Q8GhuV.js";const F={},W=e=>{let n;const s=new Set,a=(o,r)=>{const t=typeof o=="function"?o(n):o;if(!Object.is(t,n)){const u=n;n=r??(typeof t!="object"||t===null)?t:Object.assign({},n,t),s.forEach(c=>c(n,u))}},d=()=>n,S={setState:a,getState:d,getInitialState:()=>f,subscribe:o=>(s.add(o),()=>s.delete(o)),destroy:()=>{(F?"production":void 0)!=="production"&&console.warn("[DEPRECATED] The `destroy` method will be unsupported in a future version. Instead use unsubscribe function returned by subscribe. Everything will be garbage-collected if store is garbage-collected."),s.clear()}},f=n=e(a,d,S);return S},M=e=>e?W(e):W;var _={exports:{}},g={},w={exports:{}},R={};/**
 * @license React
 * use-sync-external-store-shim.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var V;function $(){if(V)return R;V=1;var e=T();function n(r,t){return r===t&&(r!==0||1/r===1/t)||r!==r&&t!==t}var s=typeof Object.is=="function"?Object.is:n,a=e.useState,d=e.useEffect,p=e.useLayoutEffect,b=e.useDebugValue;function E(r,t){var u=t(),c=a({inst:{value:u,getSnapshot:t}}),i=c[0].inst,v=c[1];return p(function(){i.value=u,i.getSnapshot=t,S(i)&&v({inst:i})},[r,u,t]),d(function(){return S(i)&&v({inst:i}),r(function(){S(i)&&v({inst:i})})},[r]),b(u),u}function S(r){var t=r.getSnapshot;r=r.value;try{var u=t();return!s(r,u)}catch{return!0}}function f(r,t){return t()}var o=typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"?f:E;return R.useSyncExternalStore=e.useSyncExternalStore!==void 0?e.useSyncExternalStore:o,R}var q;function G(){return q||(q=1,w.exports=$()),w.exports}/**
 * @license React
 * use-sync-external-store-shim/with-selector.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var O;function L(){if(O)return g;O=1;var e=T(),n=G();function s(f,o){return f===o&&(f!==0||1/f===1/o)||f!==f&&o!==o}var a=typeof Object.is=="function"?Object.is:s,d=n.useSyncExternalStore,p=e.useRef,b=e.useEffect,E=e.useMemo,S=e.useDebugValue;return g.useSyncExternalStoreWithSelector=function(f,o,r,t,u){var c=p(null);if(c.current===null){var i={hasValue:!1,value:null};c.current=i}else i=c.current;c=E(function(){function D(l){if(!j){if(j=!0,h=l,l=t(l),u!==void 0&&i.hasValue){var m=i.value;if(u(m,l))return y=m}return y=l}if(m=y,a(h,l))return m;var I=t(l);return u!==void 0&&u(m,I)?(h=l,m):(h=l,y=I)}var j=!1,h,y,x=r===void 0?null:r;return[function(){return D(o())},x===null?void 0:function(){return D(x())}]},[o,r,t,u]);var v=d(f,c[0],c[1]);return b(function(){i.hasValue=!0,i.value=v},[v]),S(v),v},g}var z;function k(){return z||(z=1,_.exports=L()),_.exports}var B=k();const H=P(B),A={},{useDebugValue:J}=U,{useSyncExternalStoreWithSelector:K}=H;let C=!1;const N=e=>e;function Q(e,n=N,s){(A?"production":void 0)!=="production"&&s&&!C&&(console.warn("[DEPRECATED] Use `createWithEqualityFn` instead of `create` or use `useStoreWithEqualityFn` instead of `useStore`. They can be imported from 'zustand/traditional'. https://github.com/pmndrs/zustand/discussions/1937"),C=!0);const a=K(e.subscribe,e.getState,e.getServerState||e.getInitialState,n,s);return J(a),a}const X=e=>{(A?"production":void 0)!=="production"&&typeof e!="function"&&console.warn("[DEPRECATED] Passing a vanilla store will be unsupported in a future version. Instead use `import { useStore } from 'zustand'`.");const n=typeof e=="function"?M(e):e,s=(a,d)=>Q(n,a,d);return Object.assign(s,n),s},Z=e=>X;export{Z as c};
//# sourceMappingURL=state-fRPoTWzM.js.map
