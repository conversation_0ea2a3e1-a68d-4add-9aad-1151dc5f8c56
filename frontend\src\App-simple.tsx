import React, { useEffect, useState } from 'react';

function AppSimple() {
  const [status, setStatus] = useState({
    backend: 'checking...',
    wsl: 'checking...',
    tools: []
  });

  useEffect(() => {
    // Test backend connection directly
    async function testBackend() {
      try {
        const response = await fetch('http://161.97.99.62:8090/api/health');
        const data = await response.json();
        setStatus(prev => ({ ...prev, backend: 'Connected ✅' }));

        // Get tools
        const toolsResponse = await fetch('http://161.97.99.62:8090/api/tools/available');
        const toolsData = await toolsResponse.json();
        if (toolsData.success && toolsData.data) {
          setStatus(prev => ({ ...prev, tools: toolsData.data.slice(0, 5) }));
        }
      } catch (error) {
        setStatus(prev => ({ ...prev, backend: `Failed ❌: ${error.message}` }));
      }
    }

    // Test WSL if in Electron
    async function testWSL() {
      if (window.electronAPI?.wsl) {
        try {
          const wslStatus = await window.electronAPI.wsl.getStatus();
          setStatus(prev => ({ ...prev, wsl: `Available ✅: ${wslStatus.version || 'Unknown version'}` }));
        } catch (error) {
          setStatus(prev => ({ ...prev, wsl: `Failed ❌: ${error.message}` }));
        }
      } else {
        setStatus(prev => ({ ...prev, wsl: 'Not in Electron environment' }));
      }
    }

    testBackend();
    testWSL();
  }, []);

  const runNmapTest = async () => {
    try {
      const response = await fetch('http://ec2-3-89-91-209.compute-1.amazonaws.com:8000/api/simple-tools/nmap/scan', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          target: 'scanme.nmap.org',
          port_range: '80,443',
          scan_type: 'tcp'
        })
      });
      const data = await response.json();
      alert('Nmap scan result: ' + JSON.stringify(data, null, 2));
    } catch (error) {
      alert('Nmap scan failed: ' + error.message);
    }
  };

  return (
    <div style={{ padding: '20px', fontFamily: 'monospace', background: '#1a1a1a', color: '#fff', minHeight: '100vh' }}>
      <h1>NexusScan Desktop - Simple Test</h1>
      
      <div style={{ marginBottom: '20px' }}>
        <h2>System Status:</h2>
        <p>Backend: {status.backend}</p>
        <p>WSL: {status.wsl}</p>
      </div>

      <div style={{ marginBottom: '20px' }}>
        <h2>Available Tools ({status.tools.length}):</h2>
        {status.tools.map((tool: any) => (
          <div key={tool.id} style={{ marginLeft: '20px' }}>
            • {tool.name} ({tool.id}) - {tool.status}
          </div>
        ))}
      </div>

      <div>
        <h2>Test Tool Execution:</h2>
        <button 
          onClick={runNmapTest}
          style={{ 
            padding: '10px 20px', 
            background: '#3b82f6', 
            color: 'white', 
            border: 'none', 
            borderRadius: '5px',
            cursor: 'pointer'
          }}
        >
          Run Nmap Test Scan
        </button>
      </div>
    </div>
  );
}

export default AppSimple;