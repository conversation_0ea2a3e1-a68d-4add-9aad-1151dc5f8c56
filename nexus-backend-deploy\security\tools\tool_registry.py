"""
Tool Registry System for NexusScan Desktop Application
Dynamic loading and management of security scanning tools.
"""

import logging
import inspect
from typing import Dict, List, Optional, Type, Any, Callable
from dataclasses import dataclass, asdict
from abc import ABC, abstractmethod
from enum import Enum
import asyncio

logger = logging.getLogger(__name__)


class ToolCategory(Enum):
    """Tool categories for organization"""
    NETWORK_SCANNER = "network_scanner"
    WEB_SCANNER = "web_scanner"
    VULNERABILITY_SCANNER = "vulnerability_scanner"
    PAYLOAD_GENERATOR = "payload_generator"
    CUSTOM_ANALYZER = "custom_analyzer"
    AI_ANALYZER = "ai_analyzer"
    COMPLIANCE_CHECKER = "compliance_checker"
    EXPLOITATION = "exploitation"
    INTELLIGENCE = "intelligence"


class ToolStatus(Enum):
    """Tool execution status"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


@dataclass
class ToolCapabilities:
    """Tool capabilities and features"""
    supports_async: bool = True
    supports_progress: bool = False
    supports_cancellation: bool = False
    requires_root: bool = False
    network_access_required: bool = True
    output_formats: List[str] = None
    supported_targets: List[str] = None  # e.g., ["ip", "url", "domain"]

    def __post_init__(self):
        if self.output_formats is None:
            self.output_formats = ["json"]
        if self.supported_targets is None:
            self.supported_targets = ["ip", "url"]


@dataclass
class ToolMetadata:
    """Tool metadata and configuration"""
    name: str
    display_name: str
    description: str
    version: str
    category: ToolCategory
    author: str = "NexusScan Team"
    website: str = ""
    documentation: str = ""
    capabilities: ToolCapabilities = None
    default_options: Dict[str, Any] = None
    required_dependencies: List[str] = None

    def __post_init__(self):
        if self.capabilities is None:
            self.capabilities = ToolCapabilities()
        if self.default_options is None:
            self.default_options = {}
        if self.required_dependencies is None:
            self.required_dependencies = []


@dataclass
class ScanOptions:
    """Generic scan options that can be extended by specific tools"""
    target: str
    timeout: int = 300
    threads: int = 1
    output_format: str = "json"
    custom_options: Dict[str, Any] = None

    def __post_init__(self):
        if self.custom_options is None:
            self.custom_options = {}


@dataclass
class ScanResult:
    """Standardized scan result format"""
    tool_name: str
    target: str
    status: ToolStatus
    start_time: str
    end_time: Optional[str] = None
    duration_seconds: Optional[float] = None
    raw_output: Optional[str] = None
    parsed_results: Optional[Dict[str, Any]] = None
    errors: List[str] = None
    warnings: List[str] = None
    vulnerabilities: List[Dict[str, Any]] = None
    metadata: Optional[Dict[str, Any]] = None

    def __post_init__(self):
        if self.errors is None:
            self.errors = []
        if self.warnings is None:
            self.warnings = []
        if self.vulnerabilities is None:
            self.vulnerabilities = []
        if self.metadata is None:
            self.metadata = {}

    def to_dict(self) -> Dict[str, Any]:
        """Convert ScanResult to JSON-serializable dictionary"""
        return {
            "tool_name": self.tool_name,
            "target": self.target,
            "status": self.status.value if isinstance(self.status, ToolStatus) else str(self.status),
            "start_time": self.start_time,
            "end_time": self.end_time,
            "duration_seconds": self.duration_seconds,
            "raw_output": self.raw_output,
            "parsed_results": self.parsed_results,
            "errors": self.errors,
            "warnings": self.warnings,
            "vulnerabilities": self.vulnerabilities,
            "metadata": self.metadata
        }


class SecurityTool(ABC):
    """Abstract base class for all security tools"""

    def __init__(self):
        """Initialize tool"""
        self.metadata = self.get_metadata()
        self.is_available = self.check_availability()

    @abstractmethod
    def get_metadata(self) -> ToolMetadata:
        """Get tool metadata"""
        pass

    @abstractmethod
    async def scan(self, options: ScanOptions, 
                   progress_callback: Optional[Callable[[float, str], None]] = None) -> ScanResult:
        """Execute scan with given options"""
        pass

    @abstractmethod
    def check_availability(self) -> bool:
        """Check if tool is available and properly configured"""
        pass

    def validate_options(self, options: ScanOptions) -> bool:
        """Validate scan options"""
        # Basic validation
        if not options.target:
            return False
        
        # Check if target type is supported
        target_type = self._detect_target_type(options.target)
        if target_type not in self.metadata.capabilities.supported_targets:
            return False
        
        return True

    def _detect_target_type(self, target: str) -> str:
        """Detect target type (ip, url, domain, etc.)"""
        import re
        
        # IP address pattern
        ip_pattern = r'^(\d{1,3}\.){3}\d{1,3}$'
        if re.match(ip_pattern, target):
            return "ip"
        
        # URL pattern
        if target.startswith(('http://', 'https://')):
            return "url"
        
        # Domain pattern (basic)
        domain_pattern = r'^[a-zA-Z0-9][a-zA-Z0-9\-\.]*[a-zA-Z0-9]$'
        if re.match(domain_pattern, target):
            return "domain"
        
        return "unknown"

    async def cancel_scan(self):
        """Cancel ongoing scan (override if tool supports cancellation)"""
        if self.metadata.capabilities.supports_cancellation:
            # Implementation depends on specific tool
            pass
        else:
            logger.warning(f"Tool {self.metadata.name} does not support cancellation")


class ToolRegistry:
    """Central registry for all security tools"""

    def __init__(self):
        """Initialize tool registry"""
        self._tools: Dict[str, Type[SecurityTool]] = {}
        self._instances: Dict[str, SecurityTool] = {}
        self._categories: Dict[ToolCategory, List[str]] = {}
        
        logger.info("Tool registry initialized")

    def register_tool(self, tool_class: Type[SecurityTool]):
        """Register a security tool"""
        if not issubclass(tool_class, SecurityTool):
            raise ValueError("Tool must inherit from SecurityTool")
        
        # Create temporary instance to get metadata
        temp_instance = tool_class()
        tool_name = temp_instance.metadata.name
        
        if tool_name in self._tools:
            logger.warning(f"Tool {tool_name} is already registered, overwriting")
        
        self._tools[tool_name] = tool_class
        
        # Add to category mapping
        category = temp_instance.metadata.category
        if category not in self._categories:
            self._categories[category] = []
        
        if tool_name not in self._categories[category]:
            self._categories[category].append(tool_name)
        
        logger.info(f"Registered tool: {tool_name} ({category.value})")

    def get_tool(self, tool_name: str) -> Optional[SecurityTool]:
        """Get tool instance by name"""
        if tool_name not in self._tools:
            logger.error(f"Tool {tool_name} not found in registry")
            return None
        
        # Create instance if not exists
        if tool_name not in self._instances:
            try:
                self._instances[tool_name] = self._tools[tool_name]()
            except Exception as e:
                logger.error(f"Failed to create instance of {tool_name}: {e}")
                return None
        
        return self._instances[tool_name]

    def get_available_tools(self) -> List[str]:
        """Get list of available tools"""
        available = []
        for tool_name in self._tools:
            tool = self.get_tool(tool_name)
            if tool and tool.is_available:
                available.append(tool_name)
        return available

    def get_tools_by_category(self, category: ToolCategory) -> List[str]:
        """Get tools by category"""
        return self._categories.get(category, [])

    def get_tool_metadata(self, tool_name: str) -> Optional[ToolMetadata]:
        """Get tool metadata"""
        tool = self.get_tool(tool_name)
        return tool.metadata if tool else None

    def list_all_tools(self) -> Dict[str, Dict[str, Any]]:
        """List all registered tools with their metadata"""
        tools_info = {}
        for tool_name in self._tools:
            tool = self.get_tool(tool_name)
            if tool:
                tools_info[tool_name] = {
                    "metadata": asdict(tool.metadata),
                    "available": tool.is_available
                }
        return tools_info
    
    def list_tools(self) -> List[str]:
        """Get list of all registered tool names"""
        return list(self._tools.keys())
    
    def is_tool_available(self, tool_name: str) -> bool:
        """Check if a tool is available"""
        tool = self.get_tool(tool_name)
        return tool is not None and tool.is_available
    
    def get_tool_info(self, tool_name: str) -> Dict[str, Any]:
        """Get tool information"""
        tool = self.get_tool(tool_name)
        if tool:
            return {
                "name": tool.metadata.name,
                "description": tool.metadata.description,
                "category": tool.metadata.category.value,
                "version": tool.metadata.version,
                "available": tool.is_available
            }
        return {}

    def validate_dependencies(self, tool_name: str) -> Dict[str, bool]:
        """Validate tool dependencies"""
        tool = self.get_tool(tool_name)
        if not tool:
            return {}
        
        dependency_status = {}
        for dep in tool.metadata.required_dependencies:
            try:
                __import__(dep)
                dependency_status[dep] = True
            except ImportError:
                dependency_status[dep] = False
        
        return dependency_status

    async def execute_scan(self, tool_name: str, options: ScanOptions,
                          progress_callback: Optional[Callable[[float, str], None]] = None) -> Optional[ScanResult]:
        """Execute scan using specified tool"""
        tool = self.get_tool(tool_name)
        if not tool:
            return None
        
        if not tool.is_available:
            logger.error(f"Tool {tool_name} is not available")
            return None
        
        if not tool.validate_options(options):
            logger.error(f"Invalid options for tool {tool_name}")
            return None
        
        try:
            result = await tool.scan(options, progress_callback)
            logger.info(f"Scan completed: {tool_name} -> {options.target}")
            return result
        except Exception as e:
            logger.error(f"Scan failed for {tool_name}: {e}")
            return ScanResult(
                tool_name=tool_name,
                target=options.target,
                status=ToolStatus.FAILED,
                start_time="",
                errors=[str(e)]
            )


# Global tool registry instance
tool_registry = ToolRegistry()


def register_tool(tool_class: Type[SecurityTool]):
    """Decorator for registering tools"""
    tool_registry.register_tool(tool_class)
    return tool_class