/**
 * SQLMap Scanner Component
 * Automatic SQL injection and database takeover tool
 */
import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { 
  Database,
  Play,
  Square,
  Download,
  Settings,
  Target,
  Globe,
  FileText,
  Terminal,
  Copy,
  RotateCcw,
  Clock,
  Zap,
  Filter,
  Eye,
  AlertTriangle,
  CheckCircle,
  TrendingUp,
  Activity,
  Lock,
  Search,
  List,
  BarChart3,
  Server,
  Bug,
  Info,
  Code,
  Package,
  Layers,
  Shield,
  Crosshair,
  Cpu,
  Network,
  Gauge,
  Tag,
  Hash,
  Workflow,
  GitBranch,
  Book,
  Key,
  Users,
  Table,
  FileX,
  Skull
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useBackendStore } from '@/stores/backend-store';
import { apiClient, ToolExecution } from '@/services/api-client';
import { toast } from 'sonner';

/**
 * SQLMap configuration interface
 */
interface SQLMapConfig {
  target: {
    url: string;
    method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH' | 'HEAD';
    data: string;
    cookies: string;
    headers: { [key: string]: string };
    userAgent: string;
    referer: string;
    requestFile: string;
    bulkFile: string;
  };
  injection: {
    parameters: string[];
    skipParameters: string[];
    testParameter: string;
    dbms: string;
    os: string;
    techniques: {
      boolean: boolean;     // B
      error: boolean;       // E
      union: boolean;       // U
      stacked: boolean;     // S
      time: boolean;        // T
      inline: boolean;      // Q
    };
    tamper: string[];
    prefix: string;
    suffix: string;
    level: 1 | 2 | 3 | 4 | 5;
    risk: 1 | 2 | 3;
  };
  enumeration: {
    currentUser: boolean;
    currentDb: boolean;
    isDba: boolean;
    users: boolean;
    passwords: boolean;
    privileges: boolean;
    roles: boolean;
    databases: boolean;
    tables: boolean;
    columns: boolean;
    count: boolean;
    dump: boolean;
    dumpAll: boolean;
    search: boolean;
    comments: boolean;
    statements: boolean;
    excludeColumns: string[];
    limitStart: number;
    limitStop: number;
    firstChar: number;
    lastChar: number;
    query: string;
    sqlShell: boolean;
    osShell: boolean;
  };
  advanced: {
    threads: number;
    delay: number;
    timeout: number;
    retries: number;
    randomize: boolean;
    safeFreq: number;
    skipWaf: boolean;
    hpp: boolean;
    chunked: boolean;
    parseErrors: boolean;
    keepAlive: boolean;
    nullConnection: boolean;
    predictOutput: boolean;
    testFilter: string;
    testSkip: string;
  };
  output: {
    verbose: number;
    dumpTable: string;
    dumpWhere: string;
    dumpFile: string;
    csvDel: string;
    outputDir: string;
    dumpFormat: 'CSV' | 'HTML' | 'SQLITE';
    logFile: string;
    trafficFile: string;
    answers: string;
    batch: boolean;
    binaryFields: string;
    charset: string;
    encoding: string;
    forms: boolean;
    fresh: boolean;
    hexdump: boolean;
    parseErrors: boolean;
    repair: boolean;
    saveConfig: string;
  };
  brute: {
    commonTables: boolean;
    commonColumns: boolean;
    bruteForce: boolean;
    commonFiles: boolean;
    udfInject: boolean;
    sharedLib: string;
  };
  filesystem: {
    readFile: string;
    writeFile: string;
    destFile: string;
  };
  takeover: {
    osCmd: string;
    osShell: boolean;
    osPwn: boolean;
    osSmb: boolean;
    osBof: boolean;
    priv: boolean;
    regRead: boolean;
    regAdd: boolean;
    regDel: boolean;
    regKey: string;
    regVal: string;
    regData: string;
    regType: string;
  };
  authentication: {
    authType: 'Basic' | 'Digest' | 'NTLM' | 'PKI';
    authCred: string;
    authFile: string;
  };
  proxy: {
    enabled: boolean;
    url: string;
    credentials: string;
    proxyCred: string;
    proxyFile: string;
    ignoreProxy: boolean;
  };
  evasion: {
    randomAgent: boolean;
    mobile: boolean;
    pageRank: boolean;
    titles: boolean;
    checkWaf: boolean;
    identifyWaf: boolean;
    skipHeuristics: boolean;
    smart: boolean;
    textOnly: boolean;
    tor: boolean;
    torPort: number;
    torType: string;
    checkTor: boolean;
  };
  customArgs: string;
}

/**
 * SQL injection techniques
 */
const INJECTION_TECHNIQUES = {
  'boolean': { name: 'Boolean-based Blind', code: 'B', description: 'Boolean-based blind SQL injection' },
  'error': { name: 'Error-based', code: 'E', description: 'Error-based SQL injection' },
  'union': { name: 'UNION Query-based', code: 'U', description: 'UNION query-based SQL injection' },
  'stacked': { name: 'Stacked Queries', code: 'S', description: 'Stacked queries SQL injection' },
  'time': { name: 'Time-based Blind', code: 'T', description: 'Time-based blind SQL injection' },
  'inline': { name: 'Inline Queries', code: 'Q', description: 'Inline query SQL injection' }
};

/**
 * Database Management Systems
 */
const DBMS_OPTIONS = [
  'MySQL', 'Oracle', 'PostgreSQL', 'Microsoft SQL Server', 'SQLite', 'MongoDB',
  'Microsoft Access', 'Firebird', 'Sybase', 'SAP MaxDB', 'IBM DB2', 'HSQLDB',
  'Informix', 'MariaDB', 'MemSQL', 'TiDB', 'CockroachDB', 'H2', 'MonetDB'
];

/**
 * Operating Systems
 */
const OS_OPTIONS = [
  'Linux', 'Windows', 'FreeBSD', 'NetBSD', 'OpenBSD', 'Mac', 'Solaris'
];

/**
 * Tamper scripts
 */
const TAMPER_SCRIPTS = [
  'apostrophemask', 'apostrophenullencode', 'appendnullbyte', 'base64encode',
  'between', 'bluecoat', 'chardoubleencode', 'charencode', 'charunicodeencode',
  'concat2concatws', 'equaltolike', 'greatest', 'halfversionedmorekeywords',
  'ifnull2ifisnull', 'modsecurityversioned', 'modsecurityzeroversioned',
  'multiplespaces', 'percentage', 'randomcase', 'randomcomments', 'securesphere',
  'space2comment', 'space2dash', 'space2hash', 'space2morehash', 'space2mssqlblank',
  'space2mssqlhash', 'space2mysqlblank', 'space2mysqldash', 'space2plus',
  'space2randomblank', 'unionalltounion', 'unmagicquotes', 'versionedkeywords',
  'versionedmorekeywords', 'xforwardedfor'
];

/**
 * Output dump formats
 */
const DUMP_FORMATS = {
  'CSV': { name: 'CSV', description: 'Comma-separated values' },
  'HTML': { name: 'HTML', description: 'HTML table format' },
  'SQLITE': { name: 'SQLite', description: 'SQLite database format' }
};

export default function SQLMapScanner() {
  const { connectionStatus } = useBackendStore();
  const [config, setConfig] = React.useState<SQLMapConfig>({
    target: {
      url: '',
      method: 'GET',
      data: '',
      cookies: '',
      headers: {},
      userAgent: '',
      referer: '',
      requestFile: '',
      bulkFile: ''
    },
    injection: {
      parameters: [],
      skipParameters: [],
      testParameter: '',
      dbms: '',
      os: '',
      techniques: {
        boolean: true,
        error: true,
        union: true,
        stacked: false,
        time: false,
        inline: false
      },
      tamper: [],
      prefix: '',
      suffix: '',
      level: 1,
      risk: 1
    },
    enumeration: {
      currentUser: false,
      currentDb: false,
      isDba: false,
      users: false,
      passwords: false,
      privileges: false,
      roles: false,
      databases: false,
      tables: false,
      columns: false,
      count: false,
      dump: false,
      dumpAll: false,
      search: false,
      comments: false,
      statements: false,
      excludeColumns: [],
      limitStart: 0,
      limitStop: 0,
      firstChar: 1,
      lastChar: 0,
      query: '',
      sqlShell: false,
      osShell: false
    },
    advanced: {
      threads: 1,
      delay: 0,
      timeout: 30,
      retries: 3,
      randomize: false,
      safeFreq: 0,
      skipWaf: false,
      hpp: false,
      chunked: false,
      parseErrors: false,
      keepAlive: false,
      nullConnection: false,
      predictOutput: false,
      testFilter: '',
      testSkip: ''
    },
    output: {
      verbose: 1,
      dumpTable: '',
      dumpWhere: '',
      dumpFile: '',
      csvDel: ',',
      outputDir: '',
      dumpFormat: 'CSV',
      logFile: '',
      trafficFile: '',
      answers: '',
      batch: false,
      binaryFields: '',
      charset: '',
      encoding: '',
      forms: false,
      fresh: false,
      hexdump: false,
      parseErrors: false,
      repair: false,
      saveConfig: ''
    },
    brute: {
      commonTables: false,
      commonColumns: false,
      bruteForce: false,
      commonFiles: false,
      udfInject: false,
      sharedLib: ''
    },
    filesystem: {
      readFile: '',
      writeFile: '',
      destFile: ''
    },
    takeover: {
      osCmd: '',
      osShell: false,
      osPwn: false,
      osSmb: false,
      osBof: false,
      priv: false,
      regRead: false,
      regAdd: false,
      regDel: false,
      regKey: '',
      regVal: '',
      regData: '',
      regType: ''
    },
    authentication: {
      authType: 'Basic',
      authCred: '',
      authFile: ''
    },
    proxy: {
      enabled: false,
      url: '',
      credentials: '',
      proxyCred: '',
      proxyFile: '',
      ignoreProxy: false
    },
    evasion: {
      randomAgent: false,
      mobile: false,
      pageRank: false,
      titles: false,
      checkWaf: false,
      identifyWaf: false,
      skipHeuristics: false,
      smart: false,
      textOnly: false,
      tor: false,
      torPort: 9050,
      torType: 'HTTP',
      checkTor: false
    },
    customArgs: ''
  });

  const [execution, setExecution] = React.useState<ToolExecution | null>(null);
  const [activeTab, setActiveTab] = React.useState('configure');

  /**
   * Handle configuration changes
   */
  const updateConfig = (path: string, value: any) => {
    setConfig(prev => {
      const keys = path.split('.');
      const newConfig = { ...prev };
      let current = newConfig;
      
      for (let i = 0; i < keys.length - 1; i++) {
        current[keys[i]] = { ...current[keys[i]] };
        current = current[keys[i]];
      }
      
      current[keys[keys.length - 1]] = value;
      return newConfig;
    });
  };

  /**
   * Handle preset configurations
   */
  const applyPreset = (preset: string) => {
    const presets = {
      'basic_injection': {
        injection: {
          ...config.injection,
          techniques: { boolean: true, error: true, union: true, stacked: false, time: false, inline: false },
          level: 1,
          risk: 1
        },
        enumeration: {
          ...config.enumeration,
          currentUser: true,
          currentDb: true,
          isDba: true
        }
      },
      'comprehensive_enum': {
        injection: {
          ...config.injection,
          techniques: { boolean: true, error: true, union: true, stacked: true, time: true, inline: false },
          level: 3,
          risk: 2
        },
        enumeration: {
          ...config.enumeration,
          currentUser: true,
          currentDb: true,
          isDba: true,
          users: true,
          passwords: true,
          privileges: true,
          databases: true,
          tables: true,
          columns: true
        }
      },
      'database_takeover': {
        injection: {
          ...config.injection,
          techniques: { boolean: true, error: true, union: true, stacked: true, time: true, inline: true },
          level: 5,
          risk: 3
        },
        enumeration: {
          ...config.enumeration,
          currentUser: true,
          currentDb: true,
          isDba: true,
          users: true,
          passwords: true,
          privileges: true,
          databases: true,
          tables: true,
          columns: true,
          dump: true
        },
        takeover: {
          ...config.takeover,
          osShell: true,
          priv: true
        }
      },
      'waf_bypass': {
        injection: {
          ...config.injection,
          tamper: ['space2comment', 'randomcase', 'charencode'],
          level: 3,
          risk: 2
        },
        evasion: {
          ...config.evasion,
          randomAgent: true,
          checkWaf: true,
          identifyWaf: true
        },
        advanced: {
          ...config.advanced,
          delay: 1,
          randomize: true
        }
      },
      'time_based_blind': {
        injection: {
          ...config.injection,
          techniques: { boolean: false, error: false, union: false, stacked: false, time: true, inline: false },
          level: 2,
          risk: 1
        },
        advanced: {
          ...config.advanced,
          timeout: 60,
          delay: 2
        }
      },
      'stealth_scan': {
        injection: {
          ...config.injection,
          level: 1,
          risk: 1,
          tamper: ['space2comment', 'randomcase']
        },
        evasion: {
          ...config.evasion,
          randomAgent: true,
          smart: true
        },
        advanced: {
          ...config.advanced,
          threads: 1,
          delay: 3,
          randomize: true
        }
      }
    };

    if (presets[preset]) {
      setConfig(prev => ({ ...prev, ...presets[preset] }));
    }
  };

  /**
   * Toggle injection technique
   */
  const toggleTechnique = (technique: string) => {
    updateConfig(`injection.techniques.${technique}`, !config.injection.techniques[technique]);
  };

  /**
   * Start SQLMap scan
   */
  const startScan = async () => {
    if (!config.target.url.trim() && !config.target.requestFile && !config.target.bulkFile) {
      toast.error('Please provide a target URL, request file, or bulk file');
      return;
    }

    // Check if any injection techniques are selected
    const anyTechniqueSelected = Object.values(config.injection.techniques).some(t => t);
    if (!anyTechniqueSelected) {
      toast.error('Please select at least one SQL injection technique');
      return;
    }

    try {
      const execution = await apiClient.executeSecurityTool('sqlmap', config);
      setExecution(execution);
      setActiveTab('output');
      toast.success('SQLMap scan started successfully');
    } catch (error) {
      toast.error(`Failed to start SQLMap scan: ${error.message}`);
    }
  };

  /**
   * Stop scan
   */
  const stopScan = async () => {
    if (execution) {
      try {
        await apiClient.stopToolExecution(execution.id);
        toast.success('SQLMap scan stopped');
      } catch (error) {
        toast.error(`Failed to stop scan: ${error.message}`);
      }
    }
  };

  /**
   * Generate command line
   */
  const generateCommand = () => {
    let cmd = 'sqlmap';
    
    // Target
    if (config.target.url) {
      cmd += ` -u "${config.target.url}"`;
    }
    
    if (config.target.requestFile) {
      cmd += ` -r "${config.target.requestFile}"`;
    }
    
    if (config.target.bulkFile) {
      cmd += ` -m "${config.target.bulkFile}"`;
    }
    
    // HTTP method and data
    if (config.target.method !== 'GET') {
      cmd += ` --method=${config.target.method}`;
    }
    
    if (config.target.data) {
      cmd += ` --data="${config.target.data}"`;
    }
    
    // Headers and cookies
    if (config.target.cookies) {
      cmd += ` --cookie="${config.target.cookies}"`;
    }
    
    if (config.target.userAgent) {
      cmd += ` --user-agent="${config.target.userAgent}"`;
    }
    
    if (config.target.referer) {
      cmd += ` --referer="${config.target.referer}"`;
    }
    
    // Custom headers
    Object.entries(config.target.headers).forEach(([key, value]) => {
      cmd += ` --header="${key}: ${value}"`;
    });
    
    // Injection techniques
    const techniques = Object.entries(config.injection.techniques)
      .filter(([_, enabled]) => enabled)
      .map(([tech, _]) => INJECTION_TECHNIQUES[tech].code)
      .join('');
    
    if (techniques) {
      cmd += ` --technique=${techniques}`;
    }
    
    // Test parameters
    if (config.injection.testParameter) {
      cmd += ` -p "${config.injection.testParameter}"`;
    }
    
    if (config.injection.skipParameters.length > 0) {
      cmd += ` --skip="${config.injection.skipParameters.join(',')}"`;
    }
    
    // DBMS and OS
    if (config.injection.dbms) {
      cmd += ` --dbms="${config.injection.dbms}"`;
    }
    
    if (config.injection.os) {
      cmd += ` --os="${config.injection.os}"`;
    }
    
    // Level and Risk
    if (config.injection.level !== 1) {
      cmd += ` --level=${config.injection.level}`;
    }
    
    if (config.injection.risk !== 1) {
      cmd += ` --risk=${config.injection.risk}`;
    }
    
    // Tamper scripts
    if (config.injection.tamper.length > 0) {
      cmd += ` --tamper="${config.injection.tamper.join(',')}"`;
    }
    
    // Prefix and suffix
    if (config.injection.prefix) {
      cmd += ` --prefix="${config.injection.prefix}"`;
    }
    
    if (config.injection.suffix) {
      cmd += ` --suffix="${config.injection.suffix}"`;
    }
    
    // Enumeration options
    if (config.enumeration.currentUser) cmd += ' --current-user';
    if (config.enumeration.currentDb) cmd += ' --current-db';
    if (config.enumeration.isDba) cmd += ' --is-dba';
    if (config.enumeration.users) cmd += ' --users';
    if (config.enumeration.passwords) cmd += ' --passwords';
    if (config.enumeration.privileges) cmd += ' --privileges';
    if (config.enumeration.roles) cmd += ' --roles';
    if (config.enumeration.databases) cmd += ' --dbs';
    if (config.enumeration.tables) cmd += ' --tables';
    if (config.enumeration.columns) cmd += ' --columns';
    if (config.enumeration.count) cmd += ' --count';
    if (config.enumeration.dump) cmd += ' --dump';
    if (config.enumeration.dumpAll) cmd += ' --dump-all';
    if (config.enumeration.search) cmd += ' --search';
    if (config.enumeration.comments) cmd += ' --comments';
    if (config.enumeration.statements) cmd += ' --statements';
    
    // SQL and OS shell
    if (config.enumeration.sqlShell) cmd += ' --sql-shell';
    if (config.enumeration.osShell) cmd += ' --os-shell';
    
    // Custom query
    if (config.enumeration.query) {
      cmd += ` --sql-query="${config.enumeration.query}"`;
    }
    
    // Dump options
    if (config.output.dumpTable) {
      cmd += ` -T "${config.output.dumpTable}"`;
    }
    
    if (config.output.dumpWhere) {
      cmd += ` --where="${config.output.dumpWhere}"`;
    }
    
    if (config.enumeration.limitStart > 0 || config.enumeration.limitStop > 0) {
      cmd += ` --start=${config.enumeration.limitStart} --stop=${config.enumeration.limitStop}`;
    }
    
    // Advanced options
    if (config.advanced.threads > 1) {
      cmd += ` --threads=${config.advanced.threads}`;
    }
    
    if (config.advanced.delay > 0) {
      cmd += ` --delay=${config.advanced.delay}`;
    }
    
    if (config.advanced.timeout !== 30) {
      cmd += ` --timeout=${config.advanced.timeout}`;
    }
    
    if (config.advanced.retries !== 3) {
      cmd += ` --retries=${config.advanced.retries}`;
    }
    
    if (config.advanced.randomize) cmd += ' --randomize';
    if (config.advanced.skipWaf) cmd += ' --skip-waf';
    if (config.advanced.hpp) cmd += ' --hpp';
    if (config.advanced.chunked) cmd += ' --chunked';
    if (config.advanced.parseErrors) cmd += ' --parse-errors';
    if (config.advanced.keepAlive) cmd += ' --keep-alive';
    if (config.advanced.nullConnection) cmd += ' --null-connection';
    
    // Output options
    if (config.output.verbose > 1) {
      cmd += ` -v ${config.output.verbose}`;
    }
    
    if (config.output.outputDir) {
      cmd += ` --output-dir="${config.output.outputDir}"`;
    }
    
    if (config.output.dumpFormat !== 'CSV') {
      cmd += ` --dump-format=${config.output.dumpFormat}`;
    }
    
    if (config.output.batch) cmd += ' --batch';
    if (config.output.forms) cmd += ' --forms';
    if (config.output.fresh) cmd += ' --fresh-queries';
    
    // Authentication
    if (config.authentication.authCred) {
      cmd += ` --auth-type=${config.authentication.authType} --auth-cred="${config.authentication.authCred}"`;
    }
    
    if (config.authentication.authFile) {
      cmd += ` --auth-file="${config.authentication.authFile}"`;
    }
    
    // Proxy
    if (config.proxy.enabled && config.proxy.url) {
      cmd += ` --proxy="${config.proxy.url}"`;
    }
    
    if (config.proxy.proxyCred) {
      cmd += ` --proxy-cred="${config.proxy.proxyCred}"`;
    }
    
    // Evasion options
    if (config.evasion.randomAgent) cmd += ' --random-agent';
    if (config.evasion.mobile) cmd += ' --mobile';
    if (config.evasion.checkWaf) cmd += ' --check-waf';
    if (config.evasion.identifyWaf) cmd += ' --identify-waf';
    if (config.evasion.skipHeuristics) cmd += ' --skip-heuristics';
    if (config.evasion.smart) cmd += ' --smart';
    if (config.evasion.textOnly) cmd += ' --text-only';
    if (config.evasion.tor) cmd += ' --tor';
    
    if (config.evasion.torPort !== 9050) {
      cmd += ` --tor-port=${config.evasion.torPort}`;
    }
    
    if (config.evasion.checkTor) cmd += ' --check-tor';
    
    // File operations
    if (config.filesystem.readFile) {
      cmd += ` --file-read="${config.filesystem.readFile}"`;
    }
    
    if (config.filesystem.writeFile) {
      cmd += ` --file-write="${config.filesystem.writeFile}"`;
    }
    
    if (config.filesystem.destFile) {
      cmd += ` --file-dest="${config.filesystem.destFile}"`;
    }
    
    // OS takeover
    if (config.takeover.osCmd) {
      cmd += ` --os-cmd="${config.takeover.osCmd}"`;
    }
    
    if (config.takeover.osPwn) cmd += ' --os-pwn';
    if (config.takeover.osSmb) cmd += ' --os-smbrelay';
    if (config.takeover.osBof) cmd += ' --os-bof';
    if (config.takeover.priv) cmd += ' --priv-esc';
    
    // Registry operations
    if (config.takeover.regRead) cmd += ' --reg-read';
    if (config.takeover.regAdd) cmd += ' --reg-add';
    if (config.takeover.regDel) cmd += ' --reg-del';
    
    if (config.takeover.regKey) {
      cmd += ` --reg-key="${config.takeover.regKey}"`;
    }
    
    // Brute force
    if (config.brute.commonTables) cmd += ' --common-tables';
    if (config.brute.commonColumns) cmd += ' --common-columns';
    if (config.brute.bruteForce) cmd += ' --brute-force';
    
    // Custom arguments
    if (config.customArgs) {
      cmd += ` ${config.customArgs}`;
    }
    
    return cmd;
  };

  const isScanning = execution?.status === 'running';
  const canStart = connectionStatus === 'connected' && 
    (config.target.url.trim() || config.target.requestFile || config.target.bulkFile) && 
    Object.values(config.injection.techniques).some(t => t) && !isScanning;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center gap-3">
        <div className="flex items-center justify-center w-10 h-10 rounded-lg bg-gradient-to-br from-red-500 to-pink-600">
          <Database className="h-5 w-5 text-white" />
        </div>
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100">SQLMap Scanner</h2>
          <p className="text-gray-600 dark:text-gray-400">Automatic SQL injection and database takeover tool</p>
        </div>
      </div>

      {/* Warning Notice */}
      <Card className="border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-900/10">
        <CardContent className="pt-6">
          <div className="flex items-start gap-3">
            <Skull className="h-5 w-5 text-red-600 mt-0.5" />
            <div>
              <p className="text-sm font-medium text-red-800 dark:text-red-200">
                ⚠️ AUTHORIZED TESTING ONLY
              </p>
              <p className="text-sm text-red-700 dark:text-red-300 mt-1">
                SQLMap is a powerful penetration testing tool. Only use on systems you own or have explicit authorization to test. 
                Unauthorized access to computer systems is illegal.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Status Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Status</p>
                <p className="text-2xl font-bold">
                  {isScanning ? 'Testing' : 'Ready'}
                </p>
              </div>
              <Activity className={cn("h-8 w-8", isScanning ? "text-green-600" : "text-gray-400")} />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Techniques</p>
                <p className="text-2xl font-bold">{Object.values(config.injection.techniques).filter(t => t).length}</p>
              </div>
              <Code className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Risk Level</p>
                <p className="text-2xl font-bold">{config.injection.risk}</p>
              </div>
              <Gauge className={cn("h-8 w-8", 
                config.injection.risk === 1 ? "text-green-600" :
                config.injection.risk === 2 ? "text-yellow-600" : "text-red-600"
              )} />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Progress</p>
                <p className="text-2xl font-bold">{execution ? `${execution.progress}%` : '0%'}</p>
              </div>
              <BarChart3 className="h-8 w-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Interface */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Database className="h-5 w-5" />
                SQLMap Configuration
              </CardTitle>
              <CardDescription>
                Configure SQL injection testing parameters and database enumeration options
              </CardDescription>
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => navigator.clipboard.writeText(generateCommand())}
              >
                <Copy className="h-4 w-4 mr-2" />
                Copy Command
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setConfig(prev => ({ 
                  ...prev, 
                  target: { ...prev.target, url: '', data: '', cookies: '', headers: {} },
                  injection: { ...prev.injection, parameters: [], tamper: [] },
                  enumeration: { ...prev.enumeration, query: '' },
                  customArgs: ''
                }))}
              >
                <RotateCcw className="h-4 w-4 mr-2" />
                Reset
              </Button>
              {isScanning ? (
                <Button variant="destructive" size="sm" onClick={stopScan}>
                  <Square className="h-4 w-4 mr-2" />
                  Stop Scan
                </Button>
              ) : (
                <Button 
                  size="sm" 
                  onClick={startScan}
                  disabled={!canStart}
                >
                  <Play className="h-4 w-4 mr-2" />
                  Start Test
                </Button>
              )}
            </div>
          </div>
        </CardHeader>

        <CardContent>
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="configure">Configure</TabsTrigger>
              <TabsTrigger value="output">Output</TabsTrigger>
              <TabsTrigger value="results">Results</TabsTrigger>
              <TabsTrigger value="command">Command</TabsTrigger>
            </TabsList>

            <TabsContent value="configure" className="space-y-6">
              {/* Quick Presets */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Quick Presets</h3>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                  <Button variant="outline" onClick={() => applyPreset('basic_injection')}>
                    <Zap className="h-4 w-4 mr-2" />
                    Basic Injection
                  </Button>
                  <Button variant="outline" onClick={() => applyPreset('comprehensive_enum')}>
                    <Search className="h-4 w-4 mr-2" />
                    Comprehensive
                  </Button>
                  <Button variant="outline" onClick={() => applyPreset('database_takeover')}>
                    <Skull className="h-4 w-4 mr-2" />
                    DB Takeover
                  </Button>
                  <Button variant="outline" onClick={() => applyPreset('waf_bypass')}>
                    <Shield className="h-4 w-4 mr-2" />
                    WAF Bypass
                  </Button>
                  <Button variant="outline" onClick={() => applyPreset('time_based_blind')}>
                    <Clock className="h-4 w-4 mr-2" />
                    Time-based
                  </Button>
                  <Button variant="outline" onClick={() => applyPreset('stealth_scan')}>
                    <Eye className="h-4 w-4 mr-2" />
                    Stealth Mode
                  </Button>
                </div>
              </div>

              {/* Target Configuration */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Target Configuration</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Target URL</label>
                    <Input
                      value={config.target.url}
                      onChange={(e) => updateConfig('target.url', e.target.value)}
                      placeholder="https://example.com/page.php?id=1"
                    />
                  </div>

                  <div className="space-y-2">
                    <label className="text-sm font-medium">HTTP Method</label>
                    <Select value={config.target.method} onValueChange={(value) => updateConfig('target.method', value)}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="GET">GET</SelectItem>
                        <SelectItem value="POST">POST</SelectItem>
                        <SelectItem value="PUT">PUT</SelectItem>
                        <SelectItem value="DELETE">DELETE</SelectItem>
                        <SelectItem value="PATCH">PATCH</SelectItem>
                        <SelectItem value="HEAD">HEAD</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                {config.target.method !== 'GET' && (
                  <div className="space-y-2">
                    <label className="text-sm font-medium">POST Data</label>
                    <Textarea
                      value={config.target.data}
                      onChange={(e) => updateConfig('target.data', e.target.value)}
                      placeholder="username=admin&password=test&id=1"
                      className="min-h-[80px]"
                    />
                  </div>
                )}

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Cookies</label>
                    <Input
                      value={config.target.cookies}
                      onChange={(e) => updateConfig('target.cookies', e.target.value)}
                      placeholder="PHPSESSID=abc123; token=xyz789"
                    />
                  </div>

                  <div className="space-y-2">
                    <label className="text-sm font-medium">User Agent</label>
                    <Input
                      value={config.target.userAgent}
                      onChange={(e) => updateConfig('target.userAgent', e.target.value)}
                      placeholder="Mozilla/5.0 (Windows NT 10.0; Win64; x64)"
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">Custom Headers</label>
                  <Textarea
                    value={Object.entries(config.target.headers).map(([k, v]) => `${k}: ${v}`).join('\n')}
                    onChange={(e) => {
                      const headers = {};
                      e.target.value.split('\n').forEach(line => {
                        const [key, ...valueParts] = line.split(':');
                        if (key && valueParts.length > 0) {
                          headers[key.trim()] = valueParts.join(':').trim();
                        }
                      });
                      updateConfig('target.headers', headers);
                    }}
                    placeholder="Authorization: Bearer token&#10;X-Custom-Header: value"
                    className="min-h-[80px]"
                  />
                </div>
              </div>

              {/* Injection Techniques */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">SQL Injection Techniques</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  {Object.entries(INJECTION_TECHNIQUES).map(([key, technique]) => (
                    <Card 
                      key={key}
                      className={cn(
                        "cursor-pointer transition-colors",
                        config.injection.techniques[key] ? "border-red-500 bg-red-50 dark:bg-red-950" : ""
                      )}
                      onClick={() => toggleTechnique(key)}
                    >
                      <CardContent className="pt-6">
                        <div className="flex items-center justify-between">
                          <div className="flex-1">
                            <div className="font-medium">{technique.name}</div>
                            <div className="text-sm text-gray-500">{technique.description}</div>
                          </div>
                          <Badge variant="outline">{technique.code}</Badge>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>

              {/* Injection Parameters */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Injection Parameters</h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Level (1-5)</label>
                    <Select value={config.injection.level.toString()} onValueChange={(value) => updateConfig('injection.level', parseInt(value))}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="1">1 - Basic</SelectItem>
                        <SelectItem value="2">2 - Cookie testing</SelectItem>
                        <SelectItem value="3">3 - User-Agent testing</SelectItem>
                        <SelectItem value="4">4 - Referer testing</SelectItem>
                        <SelectItem value="5">5 - Host header testing</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <label className="text-sm font-medium">Risk (1-3)</label>
                    <Select value={config.injection.risk.toString()} onValueChange={(value) => updateConfig('injection.risk', parseInt(value))}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="1">1 - Safe</SelectItem>
                        <SelectItem value="2">2 - Heavy queries</SelectItem>
                        <SelectItem value="3">3 - OR-based queries</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <label className="text-sm font-medium">Test Parameter</label>
                    <Input
                      value={config.injection.testParameter}
                      onChange={(e) => updateConfig('injection.testParameter', e.target.value)}
                      placeholder="id,username,search"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">DBMS</label>
                    <Select value={config.injection.dbms} onValueChange={(value) => updateConfig('injection.dbms', value)}>
                      <SelectTrigger>
                        <SelectValue placeholder="Auto-detect" />
                      </SelectTrigger>
                      <SelectContent>
                        {DBMS_OPTIONS.map(dbms => (
                          <SelectItem key={dbms} value={dbms}>{dbms}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <label className="text-sm font-medium">Operating System</label>
                    <Select value={config.injection.os} onValueChange={(value) => updateConfig('injection.os', value)}>
                      <SelectTrigger>
                        <SelectValue placeholder="Auto-detect" />
                      </SelectTrigger>
                      <SelectContent>
                        {OS_OPTIONS.map(os => (
                          <SelectItem key={os} value={os}>{os}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">Tamper Scripts</label>
                  <div className="flex flex-wrap gap-1 mb-2">
                    {config.injection.tamper.map(script => (
                      <Badge
                        key={script}
                        variant="secondary"
                        className="cursor-pointer"
                        onClick={() => updateConfig('injection.tamper', config.injection.tamper.filter(s => s !== script))}
                      >
                        {script} ×
                      </Badge>
                    ))}
                  </div>
                  <Select onValueChange={(value) => {
                    if (value && !config.injection.tamper.includes(value)) {
                      updateConfig('injection.tamper', [...config.injection.tamper, value]);
                    }
                  }}>
                    <SelectTrigger>
                      <SelectValue placeholder="Add tamper script" />
                    </SelectTrigger>
                    <SelectContent>
                      {TAMPER_SCRIPTS.map(script => (
                        <SelectItem key={script} value={script}>{script}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* Enumeration Options */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Database Enumeration</h3>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="currentUser"
                      checked={config.enumeration.currentUser}
                      onChange={(e) => updateConfig('enumeration.currentUser', e.target.checked)}
                      className="rounded"
                    />
                    <label htmlFor="currentUser" className="text-sm font-medium">Current User</label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="currentDb"
                      checked={config.enumeration.currentDb}
                      onChange={(e) => updateConfig('enumeration.currentDb', e.target.checked)}
                      className="rounded"
                    />
                    <label htmlFor="currentDb" className="text-sm font-medium">Current DB</label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="isDba"
                      checked={config.enumeration.isDba}
                      onChange={(e) => updateConfig('enumeration.isDba', e.target.checked)}
                      className="rounded"
                    />
                    <label htmlFor="isDba" className="text-sm font-medium">Is DBA</label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="users"
                      checked={config.enumeration.users}
                      onChange={(e) => updateConfig('enumeration.users', e.target.checked)}
                      className="rounded"
                    />
                    <label htmlFor="users" className="text-sm font-medium">Users</label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="passwords"
                      checked={config.enumeration.passwords}
                      onChange={(e) => updateConfig('enumeration.passwords', e.target.checked)}
                      className="rounded"
                    />
                    <label htmlFor="passwords" className="text-sm font-medium">Passwords</label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="privileges"
                      checked={config.enumeration.privileges}
                      onChange={(e) => updateConfig('enumeration.privileges', e.target.checked)}
                      className="rounded"
                    />
                    <label htmlFor="privileges" className="text-sm font-medium">Privileges</label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="databases"
                      checked={config.enumeration.databases}
                      onChange={(e) => updateConfig('enumeration.databases', e.target.checked)}
                      className="rounded"
                    />
                    <label htmlFor="databases" className="text-sm font-medium">Databases</label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="tables"
                      checked={config.enumeration.tables}
                      onChange={(e) => updateConfig('enumeration.tables', e.target.checked)}
                      className="rounded"
                    />
                    <label htmlFor="tables" className="text-sm font-medium">Tables</label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="columns"
                      checked={config.enumeration.columns}
                      onChange={(e) => updateConfig('enumeration.columns', e.target.checked)}
                      className="rounded"
                    />
                    <label htmlFor="columns" className="text-sm font-medium">Columns</label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="dump"
                      checked={config.enumeration.dump}
                      onChange={(e) => updateConfig('enumeration.dump', e.target.checked)}
                      className="rounded"
                    />
                    <label htmlFor="dump" className="text-sm font-medium">Dump Data</label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="sqlShell"
                      checked={config.enumeration.sqlShell}
                      onChange={(e) => updateConfig('enumeration.sqlShell', e.target.checked)}
                      className="rounded"
                    />
                    <label htmlFor="sqlShell" className="text-sm font-medium">SQL Shell</label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="osShell"
                      checked={config.enumeration.osShell}
                      onChange={(e) => updateConfig('enumeration.osShell', e.target.checked)}
                      className="rounded"
                    />
                    <label htmlFor="osShell" className="text-sm font-medium">OS Shell</label>
                  </div>
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">Custom SQL Query</label>
                  <Textarea
                    value={config.enumeration.query}
                    onChange={(e) => updateConfig('enumeration.query', e.target.value)}
                    placeholder="SELECT * FROM users WHERE id=1"
                    className="min-h-[80px]"
                  />
                </div>
              </div>

              {/* Advanced Options */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Advanced Options</h3>
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Threads</label>
                    <Input
                      type="number"
                      value={config.advanced.threads}
                      onChange={(e) => updateConfig('advanced.threads', parseInt(e.target.value) || 1)}
                      min="1"
                      max="10"
                    />
                  </div>

                  <div className="space-y-2">
                    <label className="text-sm font-medium">Delay (s)</label>
                    <Input
                      type="number"
                      value={config.advanced.delay}
                      onChange={(e) => updateConfig('advanced.delay', parseInt(e.target.value) || 0)}
                      min="0"
                      max="30"
                    />
                  </div>

                  <div className="space-y-2">
                    <label className="text-sm font-medium">Timeout (s)</label>
                    <Input
                      type="number"
                      value={config.advanced.timeout}
                      onChange={(e) => updateConfig('advanced.timeout', parseInt(e.target.value) || 30)}
                      min="1"
                      max="300"
                    />
                  </div>

                  <div className="space-y-2">
                    <label className="text-sm font-medium">Retries</label>
                    <Input
                      type="number"
                      value={config.advanced.retries}
                      onChange={(e) => updateConfig('advanced.retries', parseInt(e.target.value) || 3)}
                      min="0"
                      max="10"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="randomize"
                      checked={config.advanced.randomize}
                      onChange={(e) => updateConfig('advanced.randomize', e.target.checked)}
                      className="rounded"
                    />
                    <label htmlFor="randomize" className="text-sm font-medium">Randomize</label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="skipWaf"
                      checked={config.advanced.skipWaf}
                      onChange={(e) => updateConfig('advanced.skipWaf', e.target.checked)}
                      className="rounded"
                    />
                    <label htmlFor="skipWaf" className="text-sm font-medium">Skip WAF</label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="randomAgent"
                      checked={config.evasion.randomAgent}
                      onChange={(e) => updateConfig('evasion.randomAgent', e.target.checked)}
                      className="rounded"
                    />
                    <label htmlFor="randomAgent" className="text-sm font-medium">Random Agent</label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="batch"
                      checked={config.output.batch}
                      onChange={(e) => updateConfig('output.batch', e.target.checked)}
                      className="rounded"
                    />
                    <label htmlFor="batch" className="text-sm font-medium">Batch Mode</label>
                  </div>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="output" className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold">Real-time Output</h3>
                <div className="flex items-center gap-2">
                  {isScanning && (
                    <Badge variant="secondary" className="animate-pulse">
                      <Activity className="h-3 w-3 mr-1" />
                      Testing in progress...
                    </Badge>
                  )}
                  <Button variant="outline" size="sm">
                    <Download className="h-4 w-4 mr-2" />
                    Export Output
                  </Button>
                </div>
              </div>
              
              {execution?.progress !== undefined && (
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Progress</span>
                    <span>{execution.progress}%</span>
                  </div>
                  <Progress value={execution.progress} className="w-full" />
                </div>
              )}

              <Card>
                <CardContent className="p-4">
                  <div className="bg-gray-900 dark:bg-gray-800 rounded-lg p-4 min-h-[400px] font-mono text-sm overflow-auto">
                    <div className="text-green-400">
                      {execution?.output || 'SQLMap output will appear here when testing starts...'}
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="results" className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold">Injection Results</h3>
                <div className="flex items-center gap-2">
                  <Button variant="outline" size="sm">
                    <Download className="h-4 w-4 mr-2" />
                    Export Results
                  </Button>
                  <Button variant="outline" size="sm">
                    <Filter className="h-4 w-4 mr-2" />
                    Filter Results
                  </Button>
                </div>
              </div>

              {execution?.results ? (
                <div className="space-y-4">
                  {/* Results Summary */}
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <Card>
                      <CardContent className="pt-6">
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Vulnerabilities</p>
                            <p className="text-2xl font-bold text-red-600">{execution.results.vulnerabilities || 0}</p>
                          </div>
                          <Bug className="h-8 w-8 text-red-600" />
                        </div>
                      </CardContent>
                    </Card>

                    <Card>
                      <CardContent className="pt-6">
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Databases</p>
                            <p className="text-2xl font-bold">{execution.results.databases || 0}</p>
                          </div>
                          <Database className="h-8 w-8 text-blue-600" />
                        </div>
                      </CardContent>
                    </Card>

                    <Card>
                      <CardContent className="pt-6">
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Tables</p>
                            <p className="text-2xl font-bold">{execution.results.tables || 0}</p>
                          </div>
                          <Table className="h-8 w-8 text-green-600" />
                        </div>
                      </CardContent>
                    </Card>

                    <Card>
                      <CardContent className="pt-6">
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Extracted</p>
                            <p className="text-2xl font-bold">{execution.results.extracted || 0}</p>
                          </div>
                          <FileX className="h-8 w-8 text-purple-600" />
                        </div>
                      </CardContent>
                    </Card>
                  </div>

                  {/* Injection Results Table */}
                  <Card>
                    <CardContent className="p-0">
                      <div className="overflow-x-auto">
                        <table className="w-full">
                          <thead className="border-b">
                            <tr className="text-left">
                              <th className="p-4 font-medium">Parameter</th>
                              <th className="p-4 font-medium">Technique</th>
                              <th className="p-4 font-medium">Payload</th>
                              <th className="p-4 font-medium">DBMS</th>
                              <th className="p-4 font-medium">Status</th>
                            </tr>
                          </thead>
                          <tbody>
                            {execution.results.findings?.map((finding, index) => (
                              <tr key={index} className="border-b hover:bg-gray-50 dark:hover:bg-gray-800">
                                <td className="p-4 font-medium">{finding.parameter}</td>
                                <td className="p-4">
                                  <Badge variant="outline">
                                    {finding.technique}
                                  </Badge>
                                </td>
                                <td className="p-4 font-mono text-sm max-w-xs truncate">{finding.payload}</td>
                                <td className="p-4">{finding.dbms}</td>
                                <td className="p-4">
                                  <Badge 
                                    variant={finding.vulnerable ? 'destructive' : 'secondary'}
                                  >
                                    {finding.vulnerable ? 'Vulnerable' : 'Safe'}
                                  </Badge>
                                </td>
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              ) : (
                <Card>
                  <CardContent className="p-8 text-center">
                    <Database className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">No Results Yet</h3>
                    <p className="text-gray-600 dark:text-gray-400">
                      Start a SQL injection test to see results here. SQLMap will identify injection points and extract database information.
                    </p>
                  </CardContent>
                </Card>
              )}
            </TabsContent>

            <TabsContent value="command" className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold">Generated Command</h3>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => navigator.clipboard.writeText(generateCommand())}
                >
                  <Copy className="h-4 w-4 mr-2" />
                  Copy Command
                </Button>
              </div>

              <Card>
                <CardContent className="p-4">
                  <div className="bg-gray-900 dark:bg-gray-800 rounded-lg p-4 overflow-x-auto">
                    <code className="text-green-400 whitespace-pre-wrap break-all">
                      {generateCommand()}
                    </code>
                  </div>
                </CardContent>
              </Card>

              <div className="space-y-4">
                <h4 className="font-semibold">Command Explanation</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                  <div>
                    <h5 className="font-medium mb-2">Target Options</h5>
                    <ul className="space-y-1 text-gray-600 dark:text-gray-400">
                      <li>• <code>-u</code>: Target URL</li>
                      <li>• <code>--data</code>: POST data</li>
                      <li>• <code>--cookie</code>: HTTP cookies</li>
                      <li>• <code>--user-agent</code>: User agent string</li>
                    </ul>
                  </div>
                  <div>
                    <h5 className="font-medium mb-2">Injection Control</h5>
                    <ul className="space-y-1 text-gray-600 dark:text-gray-400">
                      <li>• <code>--technique</code>: SQL injection techniques</li>
                      <li>• <code>--level</code>: Tests to perform (1-5)</li>
                      <li>• <code>--risk</code>: Risk of tests (1-3)</li>
                      <li>• <code>-p</code>: Testable parameters</li>
                    </ul>
                  </div>
                  <div>
                    <h5 className="font-medium mb-2">Enumeration</h5>
                    <ul className="space-y-1 text-gray-600 dark:text-gray-400">
                      <li>• <code>--current-user</code>: Current database user</li>
                      <li>• <code>--dbs</code>: Enumerate databases</li>
                      <li>• <code>--tables</code>: Enumerate tables</li>
                      <li>• <code>--dump</code>: Dump table data</li>
                    </ul>
                  </div>
                  <div>
                    <h5 className="font-medium mb-2">Advanced Features</h5>
                    <ul className="space-y-1 text-gray-600 dark:text-gray-400">
                      <li>• <code>--os-shell</code>: OS command shell</li>
                      <li>• <code>--sql-shell</code>: SQL command shell</li>
                      <li>• <code>--tamper</code>: Tamper injection payloads</li>
                      <li>• <code>--batch</code>: Never ask for user input</li>
                    </ul>
                  </div>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}