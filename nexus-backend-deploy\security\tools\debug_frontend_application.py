#!/usr/bin/env python3
"""
⚛️ Frontend Application Deep Debugger ⚛️
Comprehensive debugging for React+Tauri desktop application

This script performs detailed analysis of:
- React application build and dependencies
- Tauri desktop integration and configuration
- TypeScript compilation and type checking
- UI component functionality and performance
- State management (Zustand) integrity
- API client and backend communication
- Authentication and security integration
- Performance metrics and optimization
- Cross-platform compatibility
"""

import json
import logging
import subprocess
import sys
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any
import os

class FrontendDebugger:
    """⚛️ Deep frontend debugging and analysis"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.frontend_dir = self.project_root / 'frontend'
        self.results = {}
        
        print("⚛️ FRONTEND APPLICATION DEEP DEBUGGER")
        print("=" * 50)

    def debug_all_frontend_components(self):
        """Execute comprehensive frontend debugging"""
        
        # Project Structure Analysis
        self._debug_project_structure()
        self._debug_package_dependencies()
        
        # Build System Analysis
        self._debug_build_configuration()
        self._debug_typescript_setup()
        
        # React Application Analysis
        self._debug_react_components()
        self._debug_state_management()
        self._debug_routing_system()
        
        # Tauri Integration Analysis
        self._debug_tauri_configuration()
        self._debug_rust_backend()
        self._debug_desktop_integration()
        
        # Development Tools Analysis
        self._debug_development_tools()
        self._debug_testing_framework()
        
        # Performance Analysis
        self._debug_bundle_analysis()
        self._debug_performance_metrics()
        
        return self._generate_frontend_report()

    def _debug_project_structure(self):
        """📁 Debug project structure and file organization"""
        print("\n📁 Project Structure Analysis")
        print("-" * 30)
        
        required_files = [
            'package.json',
            'vite.config.ts',
            'tsconfig.json',
            'tailwind.config.js',
            'src/main.tsx',
            'src/App.tsx',
            'src-tauri/tauri.conf.json',
            'src-tauri/Cargo.toml'
        ]
        
        structure_results = {}
        
        for file_path in required_files:
            full_path = self.frontend_dir / file_path
            if full_path.exists():
                print(f"✅ {file_path}")
                structure_results[file_path] = {'exists': True, 'size': full_path.stat().st_size}
            else:
                print(f"❌ {file_path} - MISSING")
                structure_results[file_path] = {'exists': False}
        
        # Check critical directories
        required_dirs = [
            'src/components',
            'src/pages', 
            'src/services',
            'src/stores',
            'src/utils',
            'src-tauri/src'
        ]
        
        for dir_path in required_dirs:
            full_path = self.frontend_dir / dir_path
            if full_path.exists() and full_path.is_dir():
                file_count = len(list(full_path.rglob('*')))
                print(f"✅ {dir_path}/ ({file_count} files)")
                structure_results[dir_path] = {'exists': True, 'file_count': file_count}
            else:
                print(f"❌ {dir_path}/ - MISSING")
                structure_results[dir_path] = {'exists': False}
        
        self.results['project_structure'] = structure_results

    def _debug_package_dependencies(self):
        """📦 Debug package.json and dependencies"""
        print("\n📦 Package Dependencies Analysis")
        print("-" * 30)
        
        package_json = self.frontend_dir / 'package.json'
        
        if not package_json.exists():
            print("❌ package.json not found")
            self.results['dependencies'] = {'error': 'package.json not found'}
            return
        
        try:
            with open(package_json, 'r') as f:
                package_data = json.load(f)
            
            print(f"✅ Package: {package_data.get('name', 'Unknown')}")
            print(f"✅ Version: {package_data.get('version', 'Unknown')}")
            
            # Check critical dependencies
            dependencies = package_data.get('dependencies', {})
            dev_dependencies = package_data.get('devDependencies', {})
            
            critical_deps = [
                'react', 'react-dom', 'typescript', 'vite',
                '@tauri-apps/api', 'zustand', 'react-router-dom',
                'tailwindcss', '@radix-ui/react-dialog'
            ]
            
            missing_deps = []
            found_deps = {}
            
            for dep in critical_deps:
                if dep in dependencies:
                    version = dependencies[dep]
                    print(f"✅ {dep}: {version}")
                    found_deps[dep] = version
                elif dep in dev_dependencies:
                    version = dev_dependencies[dep]
                    print(f"✅ {dep} (dev): {version}")
                    found_deps[dep] = version
                else:
                    print(f"❌ {dep}: MISSING")
                    missing_deps.append(dep)
            
            # Check node_modules
            node_modules = self.frontend_dir / 'node_modules'
            if node_modules.exists():
                print(f"✅ node_modules directory exists")
                deps_installed = True
            else:
                print(f"❌ node_modules directory missing - run 'pnpm install'")
                deps_installed = False
            
            self.results['dependencies'] = {
                'package_name': package_data.get('name'),
                'version': package_data.get('version'),
                'found_deps': found_deps,
                'missing_deps': missing_deps,
                'deps_installed': deps_installed,
                'total_deps': len(dependencies),
                'total_dev_deps': len(dev_dependencies)
            }
            
        except Exception as e:
            print(f"❌ Error reading package.json: {str(e)}")
            self.results['dependencies'] = {'error': str(e)}

    def _debug_build_configuration(self):
        """🔧 Debug build configuration"""
        print("\n🔧 Build Configuration Analysis")
        print("-" * 30)
        
        # Check Vite configuration
        vite_config = self.frontend_dir / 'vite.config.ts'
        if vite_config.exists():
            print("✅ Vite configuration found")
            
            try:
                with open(vite_config, 'r') as f:
                    config_content = f.read()
                
                # Check for key configurations
                config_checks = {
                    'Tauri plugin': '@tauri-apps/vite-plugin' in config_content,
                    'React plugin': '@vitejs/plugin-react' in config_content,
                    'Path aliases': 'alias:' in config_content or '@/' in config_content,
                    'Build target': 'target:' in config_content
                }
                
                for check, found in config_checks.items():
                    status = "✅" if found else "⚠️"
                    print(f"  {status} {check}")
                
                self.results['build_config'] = {
                    'vite_config_exists': True,
                    'config_checks': config_checks
                }
                
            except Exception as e:
                print(f"❌ Error reading vite.config.ts: {str(e)}")
                self.results['build_config'] = {'error': str(e)}
        else:
            print("❌ vite.config.ts not found")
            self.results['build_config'] = {'vite_config_exists': False}

    def _debug_typescript_setup(self):
        """📝 Debug TypeScript configuration"""
        print("\n📝 TypeScript Configuration")
        print("-" * 30)
        
        tsconfig = self.frontend_dir / 'tsconfig.json'
        if tsconfig.exists():
            print("✅ TypeScript configuration found")
            
            try:
                # Run TypeScript compiler check
                result = subprocess.run(
                    ['npx', 'tsc', '--noEmit', '--project', str(tsconfig)],
                    cwd=self.frontend_dir,
                    capture_output=True,
                    text=True,
                    timeout=30
                )
                
                if result.returncode == 0:
                    print("✅ TypeScript compilation successful")
                    self.results['typescript'] = {'compilation': 'success'}
                else:
                    print(f"❌ TypeScript compilation errors:")
                    print(result.stdout)
                    print(result.stderr)
                    self.results['typescript'] = {
                        'compilation': 'error',
                        'errors': result.stderr
                    }
                    
            except subprocess.TimeoutExpired:
                print("⚠️ TypeScript check timed out")
                self.results['typescript'] = {'compilation': 'timeout'}
            except Exception as e:
                print(f"❌ TypeScript check failed: {str(e)}")
                self.results['typescript'] = {'compilation': 'failed', 'error': str(e)}
        else:
            print("❌ tsconfig.json not found")
            self.results['typescript'] = {'config_exists': False}

    def _debug_react_components(self):
        """⚛️ Debug React components"""
        print("\n⚛️ React Components Analysis")
        print("-" * 30)
        
        components_dir = self.frontend_dir / 'src/components'
        pages_dir = self.frontend_dir / 'src/pages'
        
        component_analysis = {}
        
        # Analyze components directory
        if components_dir.exists():
            component_files = list(components_dir.rglob('*.tsx'))
            print(f"✅ Components found: {len(component_files)}")
            
            # Check for key component categories
            categories = {
                'ui': 'ui/',
                'auth': 'auth/', 
                'security': 'security/',
                'ai': 'ai/',
                'layout': 'layout/'
            }
            
            found_categories = {}
            for category, path in categories.items():
                category_path = components_dir / path
                if category_path.exists():
                    category_files = list(category_path.glob('*.tsx'))
                    found_categories[category] = len(category_files)
                    print(f"  ✅ {category}: {len(category_files)} components")
                else:
                    found_categories[category] = 0
                    print(f"  ❌ {category}: Missing")
            
            component_analysis['components'] = {
                'total_files': len(component_files),
                'categories': found_categories
            }
        else:
            print("❌ Components directory not found")
            component_analysis['components'] = {'error': 'directory not found'}
        
        # Analyze pages directory  
        if pages_dir.exists():
            page_files = list(pages_dir.rglob('*.tsx'))
            print(f"✅ Pages found: {len(page_files)}")
            
            expected_pages = [
                'dashboard', 'campaigns', 'scans', 'tools', 
                'ai', 'reports', 'settings', 'vulnerabilities'
            ]
            
            found_pages = {}
            for page in expected_pages:
                page_path = pages_dir / page / 'index.tsx'
                if page_path.exists():
                    found_pages[page] = True
                    print(f"  ✅ {page}")
                else:
                    found_pages[page] = False
                    print(f"  ❌ {page}")
            
            component_analysis['pages'] = {
                'total_files': len(page_files),
                'expected_pages': found_pages
            }
        else:
            print("❌ Pages directory not found")
            component_analysis['pages'] = {'error': 'directory not found'}
        
        self.results['react_components'] = component_analysis

    def _debug_state_management(self):
        """🏪 Debug Zustand state management"""
        print("\n🏪 State Management Analysis")
        print("-" * 30)
        
        stores_dir = self.frontend_dir / 'src/stores'
        
        if stores_dir.exists():
            store_files = list(stores_dir.glob('*.ts'))
            print(f"✅ Store files found: {len(store_files)}")
            
            # Check for main app store
            app_store = stores_dir / 'app-store.ts'
            if app_store.exists():
                print("✅ Main app store found")
                
                # Basic content analysis
                try:
                    with open(app_store, 'r') as f:
                        store_content = f.read()
                    
                    # Check for key store features
                    store_checks = {
                        'Zustand import': 'zustand' in store_content,
                        'Authentication state': 'isAuthenticated' in store_content,
                        'Loading states': 'isLoading' in store_content,
                        'Error handling': 'handleError' in store_content,
                        'API integration': 'api' in store_content.lower()
                    }
                    
                    for check, found in store_checks.items():
                        status = "✅" if found else "⚠️"
                        print(f"  {status} {check}")
                    
                    self.results['state_management'] = {
                        'app_store_exists': True,
                        'store_checks': store_checks
                    }
                    
                except Exception as e:
                    print(f"❌ Error analyzing app store: {str(e)}")
                    self.results['state_management'] = {'error': str(e)}
            else:
                print("❌ Main app store not found")
                self.results['state_management'] = {'app_store_exists': False}
        else:
            print("❌ Stores directory not found")
            self.results['state_management'] = {'stores_dir_exists': False}

    def _debug_routing_system(self):
        """🛣️ Debug React Router setup"""
        print("\n🛣️ Routing System Analysis")
        print("-" * 30)
        
        # Check main App.tsx for routing setup
        app_tsx = self.frontend_dir / 'src/App.tsx'
        
        if app_tsx.exists():
            try:
                with open(app_tsx, 'r') as f:
                    app_content = f.read()
                
                routing_checks = {
                    'React Router import': 'react-router-dom' in app_content,
                    'Router component': 'Router' in app_content or 'BrowserRouter' in app_content,
                    'Routes component': 'Routes' in app_content,
                    'Route components': 'Route' in app_content
                }
                
                for check, found in routing_checks.items():
                    status = "✅" if found else "⚠️"
                    print(f"  {status} {check}")
                
                self.results['routing'] = {
                    'app_tsx_exists': True,
                    'routing_checks': routing_checks
                }
                
            except Exception as e:
                print(f"❌ Error analyzing App.tsx: {str(e)}")
                self.results['routing'] = {'error': str(e)}
        else:
            print("❌ App.tsx not found")
            self.results['routing'] = {'app_tsx_exists': False}

    def _debug_tauri_configuration(self):
        """🖥️ Debug Tauri configuration"""
        print("\n🖥️ Tauri Configuration Analysis")
        print("-" * 30)
        
        tauri_config = self.frontend_dir / 'src-tauri/tauri.conf.json'
        
        if tauri_config.exists():
            print("✅ Tauri configuration found")
            
            try:
                with open(tauri_config, 'r') as f:
                    config_data = json.load(f)
                
                # Check key configuration sections
                app_config = config_data.get('app', {})
                build_config = config_data.get('build', {})
                tauri_section = config_data.get('tauri', {})
                
                print(f"✅ App name: {app_config.get('productName', 'Unknown')}")
                print(f"✅ App version: {app_config.get('version', 'Unknown')}")
                print(f"✅ Build dist dir: {build_config.get('distDir', 'Unknown')}")
                
                # Check security settings
                security = tauri_section.get('security', {})
                csp = security.get('csp')
                
                if csp:
                    print("✅ Content Security Policy configured")
                else:
                    print("⚠️ Content Security Policy not configured")
                
                self.results['tauri_config'] = {
                    'config_exists': True,
                    'app_name': app_config.get('productName'),
                    'version': app_config.get('version'),
                    'dist_dir': build_config.get('distDir'),
                    'csp_configured': bool(csp)
                }
                
            except Exception as e:
                print(f"❌ Error reading tauri.conf.json: {str(e)}")
                self.results['tauri_config'] = {'error': str(e)}
        else:
            print("❌ tauri.conf.json not found")
            self.results['tauri_config'] = {'config_exists': False}

    def _debug_rust_backend(self):
        """🦀 Debug Rust backend for Tauri"""
        print("\n🦀 Rust Backend Analysis")
        print("-" * 30)
        
        # Check Cargo.toml
        cargo_toml = self.frontend_dir / 'src-tauri/Cargo.toml'
        main_rs = self.frontend_dir / 'src-tauri/src/main.rs'
        
        if cargo_toml.exists():
            print("✅ Cargo.toml found")
            
            try:
                with open(cargo_toml, 'r') as f:
                    cargo_content = f.read()
                
                # Check for Tauri dependencies
                tauri_checks = {
                    'Tauri dependency': 'tauri =' in cargo_content,
                    'Tauri build': 'tauri-build' in cargo_content,
                    'Serde support': 'serde' in cargo_content
                }
                
                for check, found in tauri_checks.items():
                    status = "✅" if found else "⚠️"
                    print(f"  {status} {check}")
                
                rust_analysis = {'cargo_toml_exists': True, 'tauri_checks': tauri_checks}
                
            except Exception as e:
                print(f"❌ Error reading Cargo.toml: {str(e)}")
                rust_analysis = {'error': str(e)}
        else:
            print("❌ Cargo.toml not found")
            rust_analysis = {'cargo_toml_exists': False}
        
        if main_rs.exists():
            print("✅ main.rs found")
            rust_analysis['main_rs_exists'] = True
        else:
            print("❌ main.rs not found")
            rust_analysis['main_rs_exists'] = False
        
        self.results['rust_backend'] = rust_analysis

    def _debug_desktop_integration(self):
        """🖥️ Debug desktop integration features"""
        print("\n🖥️ Desktop Integration Analysis")
        print("-" * 30)
        
        # Check for Tauri API usage in frontend
        tauri_api_file = self.frontend_dir / 'src/services/tauri-api.ts'
        
        if tauri_api_file.exists():
            print("✅ Tauri API service found")
            
            try:
                with open(tauri_api_file, 'r') as f:
                    api_content = f.read()
                
                # Check for key Tauri features
                tauri_features = {
                    'File system access': 'fs' in api_content,
                    'HTTP client': 'http' in api_content,
                    'Shell commands': 'shell' in api_content,
                    'Notifications': 'notification' in api_content,
                    'Window management': 'window' in api_content
                }
                
                for feature, found in tauri_features.items():
                    status = "✅" if found else "⚠️"
                    print(f"  {status} {feature}")
                
                self.results['desktop_integration'] = {
                    'tauri_api_exists': True,
                    'features': tauri_features
                }
                
            except Exception as e:
                print(f"❌ Error analyzing Tauri API: {str(e)}")
                self.results['desktop_integration'] = {'error': str(e)}
        else:
            print("❌ Tauri API service not found")
            self.results['desktop_integration'] = {'tauri_api_exists': False}

    def _debug_development_tools(self):
        """🛠️ Debug development tools"""
        print("\n🛠️ Development Tools Analysis")
        print("-" * 30)
        
        # Check for ESLint
        eslint_config = None
        for config_file in ['.eslintrc.js', '.eslintrc.json', 'eslint.config.js']:
            if (self.frontend_dir / config_file).exists():
                eslint_config = config_file
                break
        
        if eslint_config:
            print(f"✅ ESLint configuration: {eslint_config}")
        else:
            print("⚠️ ESLint configuration not found")
        
        # Check for Prettier
        prettier_config = None
        for config_file in ['.prettierrc', '.prettierrc.json', 'prettier.config.js']:
            if (self.frontend_dir / config_file).exists():
                prettier_config = config_file
                break
        
        if prettier_config:
            print(f"✅ Prettier configuration: {prettier_config}")
        else:
            print("⚠️ Prettier configuration not found")
        
        # Check for Tailwind CSS
        tailwind_config = self.frontend_dir / 'tailwind.config.js'
        if tailwind_config.exists():
            print("✅ Tailwind CSS configuration found")
        else:
            print("⚠️ Tailwind CSS configuration not found")
        
        self.results['development_tools'] = {
            'eslint_config': eslint_config,
            'prettier_config': prettier_config,
            'tailwind_config': tailwind_config.exists()
        }

    def _debug_testing_framework(self):
        """🧪 Debug testing framework"""
        print("\n🧪 Testing Framework Analysis")
        print("-" * 30)
        
        # Check for Vitest
        vitest_config = self.frontend_dir / 'vitest.config.ts'
        if vitest_config.exists():
            print("✅ Vitest configuration found")
        else:
            print("⚠️ Vitest configuration not found")
        
        # Check for Playwright
        playwright_config = self.frontend_dir / 'playwright.config.ts'
        if playwright_config.exists():
            print("✅ Playwright configuration found")
        else:
            print("⚠️ Playwright configuration not found")
        
        # Check test directories
        test_dirs = {
            'Unit tests': self.frontend_dir / 'src/test',
            'E2E tests': self.frontend_dir / 'src/e2e',
            'Component tests': self.frontend_dir / 'src/components/__tests__'
        }
        
        test_analysis = {}
        for test_type, test_dir in test_dirs.items():
            if test_dir.exists():
                test_files = list(test_dir.rglob('*.test.*')) + list(test_dir.rglob('*.spec.*'))
                print(f"✅ {test_type}: {len(test_files)} test files")
                test_analysis[test_type] = len(test_files)
            else:
                print(f"⚠️ {test_type}: Directory not found")
                test_analysis[test_type] = 0
        
        self.results['testing'] = {
            'vitest_config': vitest_config.exists(),
            'playwright_config': playwright_config.exists(),
            'test_counts': test_analysis
        }

    def _debug_bundle_analysis(self):
        """📦 Debug bundle size and analysis"""
        print("\n📦 Bundle Analysis")
        print("-" * 30)
        
        # Check if build exists
        dist_dir = self.frontend_dir / 'dist'
        
        if dist_dir.exists():
            # Analyze build size
            total_size = 0
            file_count = 0
            
            for file_path in dist_dir.rglob('*'):
                if file_path.is_file():
                    total_size += file_path.stat().st_size
                    file_count += 1
            
            total_size_mb = total_size / 1024 / 1024
            print(f"✅ Build directory exists: {file_count} files, {total_size_mb:.2f}MB")
            
            # Check for key files
            key_files = ['index.html', 'assets/']
            for key_file in key_files:
                if (dist_dir / key_file).exists():
                    print(f"✅ {key_file} found")
                else:
                    print(f"❌ {key_file} missing")
            
            self.results['bundle'] = {
                'build_exists': True,
                'file_count': file_count,
                'total_size_mb': total_size_mb
            }
        else:
            print("⚠️ Build directory not found - run 'pnpm build'")
            self.results['bundle'] = {'build_exists': False}

    def _debug_performance_metrics(self):
        """⚡ Debug performance metrics"""
        print("\n⚡ Performance Metrics")
        print("-" * 30)
        
        # This would require running the actual application
        # For now, we'll check for performance-related configurations
        
        # Check for performance optimizations in vite.config
        vite_config = self.frontend_dir / 'vite.config.ts'
        performance_optimizations = {}
        
        if vite_config.exists():
            try:
                with open(vite_config, 'r') as f:
                    config_content = f.read()
                
                optimizations = {
                    'Code splitting': 'rollupOptions' in config_content,
                    'Tree shaking': 'treeshake' in config_content,
                    'Minification': 'minify' in config_content,
                    'Source maps': 'sourcemap' in config_content
                }
                
                for opt, found in optimizations.items():
                    status = "✅" if found else "⚠️"
                    print(f"  {status} {opt}")
                    performance_optimizations[opt] = found
                    
            except Exception as e:
                print(f"❌ Error analyzing performance config: {str(e)}")
        
        self.results['performance'] = {
            'optimizations': performance_optimizations
        }

    def _generate_frontend_report(self):
        """Generate comprehensive frontend debugging report"""
        print("\n" + "="*50)
        print("⚛️ FRONTEND DEBUGGING REPORT")
        print("="*50)
        
        # Save results to file
        report_file = self.project_root / f"frontend_debug_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w') as f:
            json.dump(self.results, f, indent=2, default=str)
        
        print(f"\n📋 Detailed report saved to: {report_file}")
        
        # Summary
        issues = []
        
        if not self.results.get('dependencies', {}).get('deps_installed', True):
            issues.append("Dependencies not installed")
        
        if self.results.get('typescript', {}).get('compilation') == 'error':
            issues.append("TypeScript compilation errors")
        
        if not self.results.get('tauri_config', {}).get('config_exists', True):
            issues.append("Tauri configuration missing")
        
        if issues:
            print(f"\n⚠️ Issues found: {', '.join(issues)}")
            print("\n🔧 Recommended actions:")
            if "Dependencies not installed" in issues:
                print("  - Run: cd frontend && pnpm install")
            if "TypeScript compilation errors" in issues:
                print("  - Run: cd frontend && pnpm type-check")
            if "Tauri configuration missing" in issues:
                print("  - Check src-tauri/tauri.conf.json")
        else:
            print("\n✅ Frontend application appears healthy")
        
        return self.results

def main():
    """Main entry point"""
    debugger = FrontendDebugger()
    results = debugger.debug_all_frontend_components()
    return results

if __name__ == "__main__":
    main()