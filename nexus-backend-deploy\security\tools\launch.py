#!/usr/bin/env python3
"""
NexusScan Launcher - Simple launcher that handles dependency issues
This script provides a simple way to get NexusScan running with minimal setup.
"""

import sys
import os
import subprocess

def check_python_version():
    """Check if Python version is compatible"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print(f"❌ Python {version.major}.{version.minor} is not supported")
        print("Please use Python 3.8 or later")
        return False
    
    print(f"✅ Python {version.major}.{version.minor}.{version.micro} is compatible")
    return True

def install_essential_only():
    """Install only the most essential packages"""
    essential = [
        "flet>=0.28.3",
        "requests>=2.28.0", 
        "sqlalchemy>=2.0.0"
    ]
    
    print("📦 Installing essential packages only...")
    
    for package in essential:
        try:
            print(f"  Installing {package}...")
            subprocess.check_call([
                sys.executable, '-m', 'pip', 'install', package, '--quiet'
            ])
        except subprocess.CalledProcessError:
            print(f"  ⚠️ Failed to install {package} - continuing anyway")
    
    print("✅ Essential packages installation completed")

def setup_minimal_environment():
    """Set up minimal environment for running NexusScan"""
    
    # Add src to Python path
    src_path = os.path.join(os.path.dirname(__file__), 'src')
    if src_path not in sys.path:
        sys.path.insert(0, src_path)
    
    # Set environment variable for subprocess calls
    env = os.environ.copy()
    pythonpath = env.get('PYTHONPATH', '')
    if pythonpath:
        env['PYTHONPATH'] = f"{src_path}{os.pathsep}{pythonpath}"
    else:
        env['PYTHONPATH'] = src_path
    
    return env

def run_basic_test():
    """Run basic functionality test"""
    print("🧪 Running basic test...")
    
    try:
        env = setup_minimal_environment()
        result = subprocess.run([
            sys.executable, 'test_basic.py'
        ], capture_output=True, text=True, env=env)
        
        if result.returncode == 0:
            print("✅ Basic test passed")
            return True
        else:
            print("⚠️ Basic test had issues, but continuing...")
            print(result.stdout)
            return True  # Continue anyway
    except Exception as e:
        print(f"⚠️ Could not run basic test: {e}")
        return True  # Continue anyway

def initialize_database():
    """Initialize database"""
    print("🗄️ Initializing database...")
    
    try:
        env = setup_minimal_environment()
        subprocess.check_call([
            sys.executable, 'scripts/init_database.py'
        ], env=env)
        print("✅ Database initialized")
        return True
    except Exception as e:
        print(f"⚠️ Database initialization failed: {e}")
        return False

def launch_application():
    """Launch the main application"""
    print("🚀 Launching NexusScan...")
    
    try:
        env = setup_minimal_environment()
        subprocess.run([
            sys.executable, 'src/main.py'
        ], env=env)
    except KeyboardInterrupt:
        print("\n👋 NexusScan stopped by user")
    except Exception as e:
        print(f"❌ Failed to launch application: {e}")
        return False
    
    return True

def main():
    """Main launcher function"""
    print("🛡️ NexusScan Desktop Launcher")
    print("=" * 40)
    
    # Check Python version
    if not check_python_version():
        return 1
    
    # Install essential packages
    install_essential_only()
    
    # Initialize database
    if not initialize_database():
        print("⚠️ Database initialization failed, but continuing...")
    
    # Run basic test
    run_basic_test()
    
    # Launch application
    print("\n🎯 Starting NexusScan Desktop Application...")
    print("Close this window or press Ctrl+C to stop the application")
    print("-" * 40)
    
    launch_application()
    
    return 0

if __name__ == "__main__":
    sys.exit(main())