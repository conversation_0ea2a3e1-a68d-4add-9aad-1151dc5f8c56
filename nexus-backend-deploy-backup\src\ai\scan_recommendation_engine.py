#!/usr/bin/env python3
"""
Scan Recommendation Engine for NexusScan
AI-powered intelligent scan strategy recommendations based on target analysis, 
historical data, and threat intelligence
"""

import asyncio
import logging
import json
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Optional, Any, Union, <PERSON><PERSON>
from dataclasses import dataclass, field
from enum import Enum
import ipaddress
import socket

from .ai_service import (
    AIServiceProvider, AIServiceConfig, AIAnalysisRequest, AIAnalysisResult,
    AICapability, AIModelType, AnalysisType, create_ai_config_from_regular_config
)

logger = logging.getLogger(__name__)


class ScanStrategy(Enum):
    """Scan strategy types"""
    RECONNAISSANCE = "reconnaissance"
    COMPREHENSIVE = "comprehensive"
    TARGETED = "targeted"
    STEALTH = "stealth"
    AGGRESSIVE = "aggressive"
    COMPLIANCE = "compliance"
    MAINTENANCE = "maintenance"
    EMERGENCY = "emergency"


class TargetProfile(Enum):
    """Target profile types"""
    WEB_APPLICATION = "web_application"
    NETWORK_INFRASTRUCTURE = "network_infrastructure"
    DATABASE_SERVER = "database_server"
    CLOUD_INFRASTRUCTURE = "cloud_infrastructure"
    IOT_DEVICE = "iot_device"
    MOBILE_APPLICATION = "mobile_application"
    API_ENDPOINT = "api_endpoint"
    INDUSTRIAL_CONTROL = "industrial_control"
    UNKNOWN = "unknown"


class ScanPriority(Enum):
    """Scan priority levels"""
    CRITICAL = "critical"
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"
    INFORMATIONAL = "informational"


@dataclass
class TargetContext:
    """Context information about scan targets"""
    target_id: str
    targets: List[str]
    target_type: TargetProfile = TargetProfile.UNKNOWN
    organization: str = ""
    environment: str = "production"  # production, staging, development
    business_criticality: str = "medium"  # critical, high, medium, low
    previous_scans: List[Dict[str, Any]] = field(default_factory=list)
    known_technologies: List[str] = field(default_factory=list)
    network_context: Dict[str, Any] = field(default_factory=dict)
    compliance_requirements: List[str] = field(default_factory=list)
    time_constraints: Dict[str, Any] = field(default_factory=dict)
    stealth_requirements: bool = False
    authentication_info: Dict[str, Any] = field(default_factory=dict)
    custom_requirements: Dict[str, Any] = field(default_factory=dict)


@dataclass
class ScanRecommendation:
    """Scan recommendation with detailed configuration"""
    recommendation_id: str
    strategy: ScanStrategy
    priority: ScanPriority
    confidence: float
    scan_phases: List[Dict[str, Any]]
    tools_recommended: List[str]
    estimated_duration: timedelta
    resource_requirements: Dict[str, Any]
    risk_assessment: Dict[str, Any]
    expected_coverage: float
    stealth_level: str
    justification: str
    prerequisites: List[str] = field(default_factory=list)
    post_scan_actions: List[str] = field(default_factory=list)
    alternative_approaches: List[str] = field(default_factory=list)
    compliance_coverage: List[str] = field(default_factory=list)
    cost_estimate: Dict[str, Any] = field(default_factory=dict)
    success_criteria: List[str] = field(default_factory=list)


@dataclass
class ScanPhase:
    """Individual scan phase configuration"""
    phase_id: str
    phase_name: str
    tools: List[str]
    configuration: Dict[str, Any]
    dependencies: List[str] = field(default_factory=list)
    estimated_time: timedelta = timedelta(minutes=30)
    parallel_execution: bool = True
    success_criteria: List[str] = field(default_factory=list)
    failure_handling: str = "continue"


@dataclass
class ThreatIntelligence:
    """Threat intelligence data for recommendations"""
    active_campaigns: List[str]
    trending_vulnerabilities: List[str]
    threat_actors: List[str]
    attack_patterns: List[str]
    geography_risks: Dict[str, float]
    industry_threats: List[str]
    seasonal_trends: Dict[str, Any]
    intelligence_sources: List[str] = field(default_factory=list)
    confidence_level: float = 0.0
    last_updated: datetime = field(default_factory=datetime.now)


class ScanRecommendationEngine(AIServiceProvider):
    """AI-powered scan recommendation engine"""

    def __init__(self, config):
        ai_config = create_ai_config_from_regular_config(
            config, 
            "scan_recommendation_engine", 
            [AICapability.SCAN_RECOMMENDATION]
        )
        super().__init__(ai_config)
        
        # Knowledge bases
        self.target_profiles = self._load_target_profiles()
        self.scan_templates = self._load_scan_templates()
        self.tool_capabilities = self._load_tool_capabilities()
        self.compliance_mappings = self._load_compliance_mappings()
        
        # Historical data
        self.scan_history: List[Dict[str, Any]] = []
        self.performance_metrics: Dict[str, Any] = {}
        self.success_patterns: Dict[str, List[str]] = {}
        
        # Machine learning models (placeholder)
        self.recommendation_model = None
        self.optimization_model = None
        
        # Statistics
        self.recommendation_stats = {
            "total_recommendations": 0,
            "successful_scans": 0,
            "recommendations_accepted": 0,
            "average_accuracy": 0.0,
            "strategies_used": {},
            "target_profiles_analyzed": {}
        }

    async def analyze(self, request: AIAnalysisRequest) -> AIAnalysisResult:
        """Generate scan recommendations
        
        Args:
            request: Analysis request with target context
            
        Returns:
            Analysis result with scan recommendations
        """
        start_time = datetime.now()
        
        try:
            if request.capability != AICapability.SCAN_RECOMMENDATION:
                raise ValueError(f"Unsupported capability: {request.capability}")
            
            # Extract target context
            if isinstance(request.data, TargetContext):
                context = request.data
            else:
                context = TargetContext(**request.context.get("target_context", {}))
            
            # Analyze target profile
            target_analysis = await self._analyze_target_profile(context)
            
            # Get threat intelligence
            threat_intel = await self._get_threat_intelligence(context)
            
            # Generate recommendations
            recommendations = await self._generate_recommendations(context, target_analysis, threat_intel)
            
            # Optimize recommendations
            optimized_recommendations = await self._optimize_recommendations(recommendations, context)
            
            # Calculate confidence
            confidence = self._calculate_recommendation_confidence(optimized_recommendations, context)
            
            # Generate insights
            insights = self._generate_recommendation_insights(optimized_recommendations, target_analysis)
            
            result = AIAnalysisResult(
                request_id=request.request_id,
                analysis_type=request.analysis_type,
                capability=request.capability,
                success=True,
                confidence=confidence,
                result={
                    "recommendations": [rec.__dict__ for rec in optimized_recommendations],
                    "target_analysis": target_analysis,
                    "threat_intelligence": threat_intel.__dict__,
                    "optimization_notes": "Recommendations optimized for efficiency and coverage"
                },
                recommendations=[rec.justification for rec in optimized_recommendations[:3]],
                insights=insights,
                processing_time=(datetime.now() - start_time).total_seconds(),
                model_used=self.config.service_name
            )
            
            # Update statistics
            self._update_recommendation_statistics(optimized_recommendations, context)
            self._update_statistics(result)
            
            return result
            
        except Exception as e:
            self.logger.error(f"Scan recommendation failed: {e}")
            return AIAnalysisResult(
                request_id=request.request_id,
                analysis_type=request.analysis_type,
                capability=request.capability,
                success=False,
                confidence=0.0,
                result={},
                error_message=str(e),
                processing_time=(datetime.now() - start_time).total_seconds(),
                model_used=self.config.service_name
            )

    async def recommend_quick_scan(self, targets: List[str]) -> ScanRecommendation:
        """Generate quick scan recommendation
        
        Args:
            targets: List of targets to scan
            
        Returns:
            Quick scan recommendation
        """
        context = TargetContext(
            target_id=f"quick_scan_{datetime.now().isoformat()}",
            targets=targets,
            time_constraints={"max_duration": "30m"}
        )
        
        recommendation = ScanRecommendation(
            recommendation_id=f"quick_{context.target_id}",
            strategy=ScanStrategy.RECONNAISSANCE,
            priority=ScanPriority.MEDIUM,
            confidence=0.8,
            scan_phases=[
                {
                    "phase": "discovery",
                    "tools": ["nmap"],
                    "config": {"scan_type": "syn", "ports": "top-1000", "timing": "aggressive"}
                }
            ],
            tools_recommended=["nmap"],
            estimated_duration=timedelta(minutes=15),
            resource_requirements={"cpu": "low", "network": "medium"},
            risk_assessment={"detection_risk": "low", "impact_risk": "minimal"},
            expected_coverage=0.6,
            stealth_level="medium",
            justification="Quick reconnaissance scan for rapid assessment"
        )
        
        return recommendation

    async def recommend_comprehensive_scan(self, context: TargetContext) -> ScanRecommendation:
        """Generate comprehensive scan recommendation
        
        Args:
            context: Target context
            
        Returns:
            Comprehensive scan recommendation
        """
        phases = [
            {
                "phase": "discovery",
                "tools": ["nmap"],
                "config": {"scan_type": "syn", "service_detection": True, "os_detection": True}
            },
            {
                "phase": "vulnerability_scanning",
                "tools": ["nuclei", "nmap"],
                "config": {"templates": "all", "intensity": "comprehensive"}
            },
            {
                "phase": "web_application_testing",
                "tools": ["nuclei"],
                "config": {"web_templates": True, "custom_payloads": True}
            }
        ]
        
        recommendation = ScanRecommendation(
            recommendation_id=f"comprehensive_{context.target_id}",
            strategy=ScanStrategy.COMPREHENSIVE,
            priority=ScanPriority.HIGH,
            confidence=0.9,
            scan_phases=phases,
            tools_recommended=["nmap", "nuclei"],
            estimated_duration=timedelta(hours=2),
            resource_requirements={"cpu": "high", "network": "high", "storage": "medium"},
            risk_assessment={"detection_risk": "medium", "impact_risk": "low"},
            expected_coverage=0.95,
            stealth_level="low",
            justification="Comprehensive scan for thorough security assessment"
        )
        
        return recommendation

    async def recommend_compliance_scan(self, context: TargetContext, framework: str) -> ScanRecommendation:
        """Generate compliance-focused scan recommendation
        
        Args:
            context: Target context
            framework: Compliance framework (e.g., "PCI-DSS", "NIST", "SOX")
            
        Returns:
            Compliance scan recommendation
        """
        compliance_configs = {
            "PCI-DSS": {
                "focus_areas": ["network_segmentation", "encryption", "access_controls"],
                "required_tools": ["nmap", "nuclei"],
                "specific_checks": ["ssl_configuration", "default_passwords", "open_ports"]
            },
            "NIST": {
                "focus_areas": ["asset_inventory", "vulnerability_management", "configuration_management"],
                "required_tools": ["nmap", "nuclei"],
                "specific_checks": ["service_enumeration", "patch_levels", "configurations"]
            }
        }
        
        config = compliance_configs.get(framework, compliance_configs["NIST"])
        
        recommendation = ScanRecommendation(
            recommendation_id=f"compliance_{framework}_{context.target_id}",
            strategy=ScanStrategy.COMPLIANCE,
            priority=ScanPriority.HIGH,
            confidence=0.85,
            scan_phases=[
                {
                    "phase": "compliance_discovery",
                    "tools": config["required_tools"],
                    "config": {"focus": config["focus_areas"], "checks": config["specific_checks"]}
                }
            ],
            tools_recommended=config["required_tools"],
            estimated_duration=timedelta(hours=1),
            resource_requirements={"cpu": "medium", "network": "medium"},
            risk_assessment={"detection_risk": "low", "impact_risk": "minimal"},
            expected_coverage=0.8,
            stealth_level="high",
            justification=f"Compliance-focused scan for {framework} requirements",
            compliance_coverage=[framework]
        )
        
        return recommendation

    async def optimize_scan_schedule(self, recommendations: List[ScanRecommendation], 
                                   constraints: Dict[str, Any]) -> List[ScanRecommendation]:
        """Optimize scan schedule based on constraints
        
        Args:
            recommendations: List of scan recommendations
            constraints: Scheduling constraints
            
        Returns:
            Optimized scan recommendations
        """
        optimized = []
        
        # Sort by priority and resource requirements
        sorted_recs = sorted(recommendations, 
                           key=lambda x: (x.priority.value, x.estimated_duration.total_seconds()))
        
        total_time = timedelta()
        max_duration = constraints.get("max_total_duration", timedelta(hours=8))
        
        for rec in sorted_recs:
            if total_time + rec.estimated_duration <= max_duration:
                optimized.append(rec)
                total_time += rec.estimated_duration
            else:
                # Try to create a reduced version
                reduced_rec = await self._create_reduced_recommendation(rec, max_duration - total_time)
                if reduced_rec:
                    optimized.append(reduced_rec)
                break
        
        return optimized

    async def is_available(self) -> bool:
        """Check if recommendation engine is available"""
        try:
            # Quick test recommendation
            test_context = TargetContext(
                target_id="test",
                targets=["127.0.0.1"]
            )
            
            start_time = datetime.now()
            await self._analyze_target_profile(test_context)
            response_time = (datetime.now() - start_time).total_seconds()
            
            return response_time < 3.0
            
        except Exception as e:
            self.logger.error(f"Availability check failed: {e}")
            return False

    async def health_check(self) -> Dict[str, Any]:
        """Perform health check"""
        health_info = {
            "status": "healthy",
            "response_time": 0.0,
            "profiles_loaded": len(self.target_profiles),
            "templates_loaded": len(self.scan_templates),
            "recommendations_generated": self.recommendation_stats["total_recommendations"],
            "success_rate": 0.0,
            "error": None
        }
        
        try:
            start_time = datetime.now()
            available = await self.is_available()
            health_info["response_time"] = (datetime.now() - start_time).total_seconds()
            
            # Calculate success rate
            total = self.recommendation_stats["total_recommendations"]
            if total > 0:
                health_info["success_rate"] = self.recommendation_stats["successful_scans"] / total
            
            if not available:
                health_info["status"] = "unhealthy"
                health_info["error"] = "Engine not responding properly"
                
        except Exception as e:
            health_info["status"] = "unhealthy"
            health_info["error"] = str(e)
        
        return health_info

    # Private methods

    async def _analyze_target_profile(self, context: TargetContext) -> Dict[str, Any]:
        """Analyze target profile and characteristics"""
        analysis = {
            "profile_type": TargetProfile.UNKNOWN,
            "technologies_detected": [],
            "network_characteristics": {},
            "security_posture": "unknown",
            "attack_surface": {},
            "recommendations": []
        }
        
        try:
            # Analyze target types
            for target in context.targets:
                target_analysis = await self._analyze_single_target(target)
                
                # Update overall analysis
                if target_analysis["profile_type"] != TargetProfile.UNKNOWN:
                    analysis["profile_type"] = target_analysis["profile_type"]
                
                analysis["technologies_detected"].extend(target_analysis.get("technologies", []))
                analysis["attack_surface"].update(target_analysis.get("attack_surface", {}))
            
            # Remove duplicates
            analysis["technologies_detected"] = list(set(analysis["technologies_detected"]))
            
            # Determine security posture based on previous scans
            if context.previous_scans:
                analysis["security_posture"] = self._assess_security_posture(context.previous_scans)
            
            # Generate profile-specific recommendations
            analysis["recommendations"] = self._get_profile_recommendations(analysis["profile_type"])
            
        except Exception as e:
            self.logger.error(f"Target profile analysis failed: {e}")
        
        return analysis

    async def _analyze_single_target(self, target: str) -> Dict[str, Any]:
        """Analyze a single target"""
        analysis = {
            "profile_type": TargetProfile.UNKNOWN,
            "technologies": [],
            "attack_surface": {},
            "ports_likely": []
        }
        
        try:
            # Check if it's an IP address or hostname
            try:
                ip_addr = ipaddress.ip_address(target)
                analysis["attack_surface"]["ip_type"] = "ipv4" if ip_addr.version == 4 else "ipv6"
            except ipaddress.AddressValueError:
                # It's a hostname, try to resolve and analyze
                try:
                    socket.gethostbyname(target)
                    analysis["attack_surface"]["hostname"] = target
                    
                    # Analyze hostname patterns
                    if any(term in target.lower() for term in ["api", "rest", "graphql"]):
                        analysis["profile_type"] = TargetProfile.API_ENDPOINT
                        analysis["technologies"].append("API")
                    elif target.startswith("www.") or any(term in target for term in [".com", ".org", ".net"]):
                        analysis["profile_type"] = TargetProfile.WEB_APPLICATION
                        analysis["ports_likely"] = [80, 443]
                        
                except socket.gaierror:
                    self.logger.warning(f"Could not resolve hostname: {target}")
            
            # URL-based analysis
            if target.startswith(("http://", "https://")):
                analysis["profile_type"] = TargetProfile.WEB_APPLICATION
                analysis["technologies"].append("HTTP")
                if target.startswith("https://"):
                    analysis["technologies"].append("SSL/TLS")
            
        except Exception as e:
            self.logger.error(f"Single target analysis failed for {target}: {e}")
        
        return analysis

    async def _get_threat_intelligence(self, context: TargetContext) -> ThreatIntelligence:
        """Get relevant threat intelligence"""
        # In a real implementation, this would query threat intelligence feeds
        return ThreatIntelligence(
            active_campaigns=["Ransomware campaigns", "APT activities"],
            trending_vulnerabilities=["Log4j", "Spring4Shell", "ProxyShell"],
            threat_actors=["APT groups", "Ransomware gangs"],
            attack_patterns=["Initial access via web apps", "Lateral movement", "Data exfiltration"],
            geography_risks={"global": 1.0},
            industry_threats=["Supply chain attacks", "Cloud misconfigurations"],
            seasonal_trends={"Q4": "Increased activity during holidays"},
            intelligence_sources=["Internal feeds", "Commercial threat intel"],
            confidence_level=0.7
        )

    async def _generate_recommendations(self, context: TargetContext, 
                                      target_analysis: Dict[str, Any],
                                      threat_intel: ThreatIntelligence) -> List[ScanRecommendation]:
        """Generate scan recommendations based on analysis"""
        recommendations = []
        
        try:
            # Base recommendation based on target profile
            profile_type = target_analysis.get("profile_type", TargetProfile.UNKNOWN)
            base_strategy = self._get_strategy_for_profile(profile_type)
            
            # Generate primary recommendation
            primary_rec = await self._create_primary_recommendation(
                context, base_strategy, target_analysis, threat_intel
            )
            recommendations.append(primary_rec)
            
            # Generate alternative recommendations
            if context.time_constraints.get("max_duration"):
                quick_rec = await self._create_time_constrained_recommendation(context, target_analysis)
                recommendations.append(quick_rec)
            
            if context.stealth_requirements:
                stealth_rec = await self._create_stealth_recommendation(context, target_analysis)
                recommendations.append(stealth_rec)
            
            if context.compliance_requirements:
                for framework in context.compliance_requirements:
                    compliance_rec = await self.recommend_compliance_scan(context, framework)
                    recommendations.append(compliance_rec)
            
            # Generate threat-intelligence based recommendation
            if threat_intel.trending_vulnerabilities:
                threat_rec = await self._create_threat_focused_recommendation(
                    context, target_analysis, threat_intel
                )
                recommendations.append(threat_rec)
                
        except Exception as e:
            self.logger.error(f"Recommendation generation failed: {e}")
        
        return recommendations

    async def _optimize_recommendations(self, recommendations: List[ScanRecommendation],
                                      context: TargetContext) -> List[ScanRecommendation]:
        """Optimize recommendations for efficiency and effectiveness"""
        optimized = []
        
        # Remove duplicates and merge similar recommendations
        unique_recs = self._deduplicate_recommendations(recommendations)
        
        # Sort by priority and confidence
        sorted_recs = sorted(unique_recs, 
                           key=lambda x: (x.priority.value, -x.confidence))
        
        # Apply optimization strategies
        for rec in sorted_recs:
            optimized_rec = await self._optimize_single_recommendation(rec, context)
            optimized.append(optimized_rec)
        
        # Limit number of recommendations
        max_recommendations = 5
        return optimized[:max_recommendations]

    async def _create_primary_recommendation(self, context: TargetContext,
                                           strategy: ScanStrategy,
                                           target_analysis: Dict[str, Any],
                                           threat_intel: ThreatIntelligence) -> ScanRecommendation:
        """Create primary scan recommendation"""
        
        # Select tools based on target profile
        profile_type = target_analysis.get("profile_type", TargetProfile.UNKNOWN)
        tools = self._select_tools_for_profile(profile_type)
        
        # Create scan phases
        phases = self._create_scan_phases(strategy, tools, target_analysis)
        
        # Estimate duration and resources
        duration = self._estimate_scan_duration(phases, len(context.targets))
        resources = self._estimate_resource_requirements(phases, len(context.targets))
        
        # Calculate priority based on business criticality and threat level
        priority = self._calculate_scan_priority(context, threat_intel)
        
        recommendation = ScanRecommendation(
            recommendation_id=f"primary_{context.target_id}_{int(datetime.now().timestamp())}",
            strategy=strategy,
            priority=priority,
            confidence=0.85,
            scan_phases=phases,
            tools_recommended=tools,
            estimated_duration=duration,
            resource_requirements=resources,
            risk_assessment=self._assess_scan_risks(strategy, context),
            expected_coverage=self._calculate_expected_coverage(strategy, tools),
            stealth_level=self._determine_stealth_level(strategy, context),
            justification=self._generate_justification(strategy, target_analysis, threat_intel),
            prerequisites=self._identify_prerequisites(strategy, context),
            success_criteria=self._define_success_criteria(strategy, context)
        )
        
        return recommendation

    # Utility methods for recommendation generation

    def _get_strategy_for_profile(self, profile_type: TargetProfile) -> ScanStrategy:
        """Get recommended strategy for target profile"""
        strategy_map = {
            TargetProfile.WEB_APPLICATION: ScanStrategy.TARGETED,
            TargetProfile.NETWORK_INFRASTRUCTURE: ScanStrategy.COMPREHENSIVE,
            TargetProfile.DATABASE_SERVER: ScanStrategy.TARGETED,
            TargetProfile.CLOUD_INFRASTRUCTURE: ScanStrategy.COMPREHENSIVE,
            TargetProfile.API_ENDPOINT: ScanStrategy.TARGETED,
            TargetProfile.IOT_DEVICE: ScanStrategy.STEALTH,
            TargetProfile.UNKNOWN: ScanStrategy.RECONNAISSANCE
        }
        
        return strategy_map.get(profile_type, ScanStrategy.RECONNAISSANCE)

    def _select_tools_for_profile(self, profile_type: TargetProfile) -> List[str]:
        """Select appropriate tools for target profile"""
        tool_map = {
            TargetProfile.WEB_APPLICATION: ["nmap", "nuclei"],
            TargetProfile.NETWORK_INFRASTRUCTURE: ["nmap", "nuclei"],
            TargetProfile.DATABASE_SERVER: ["nmap", "nuclei"],
            TargetProfile.API_ENDPOINT: ["nuclei"],
            TargetProfile.CLOUD_INFRASTRUCTURE: ["nmap", "nuclei"],
            TargetProfile.IOT_DEVICE: ["nmap"],
            TargetProfile.UNKNOWN: ["nmap"]
        }
        
        return tool_map.get(profile_type, ["nmap"])

    def _create_scan_phases(self, strategy: ScanStrategy, tools: List[str],
                           target_analysis: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Create scan phases based on strategy and tools"""
        phases = []
        
        # Always start with discovery phase
        if "nmap" in tools:
            discovery_phase = {
                "phase_id": "discovery",
                "phase_name": "Network Discovery",
                "tools": ["nmap"],
                "configuration": {
                    "scan_type": "syn",
                    "port_range": "1-65535" if strategy == ScanStrategy.COMPREHENSIVE else "top-1000",
                    "service_detection": True,
                    "os_detection": strategy in [ScanStrategy.COMPREHENSIVE, ScanStrategy.TARGETED],
                    "timing": "normal" if strategy != ScanStrategy.STEALTH else "slow"
                },
                "estimated_time": timedelta(minutes=30),
                "parallel_execution": True
            }
            phases.append(discovery_phase)
        
        # Add vulnerability scanning phase
        if "nuclei" in tools:
            vuln_phase = {
                "phase_id": "vulnerability_scanning",
                "phase_name": "Vulnerability Assessment",
                "tools": ["nuclei"],
                "configuration": {
                    "templates": "comprehensive" if strategy == ScanStrategy.COMPREHENSIVE else "focused",
                    "rate_limit": 50 if strategy == ScanStrategy.STEALTH else 150,
                    "severity_filter": ["high", "critical"] if strategy == ScanStrategy.TARGETED else None
                },
                "dependencies": ["discovery"],
                "estimated_time": timedelta(hours=1),
                "parallel_execution": True
            }
            phases.append(vuln_phase)
        
        return phases

    def _estimate_scan_duration(self, phases: List[Dict[str, Any]], target_count: int) -> timedelta:
        """Estimate total scan duration"""
        total_duration = timedelta()
        
        for phase in phases:
            phase_duration = phase.get("estimated_time", timedelta(minutes=30))
            
            # Adjust for target count
            if target_count > 1:
                if phase.get("parallel_execution", True):
                    # Parallel execution scales logarithmically
                    scaling_factor = 1 + (target_count - 1) * 0.1
                else:
                    # Sequential execution scales linearly
                    scaling_factor = target_count
                
                phase_duration = timedelta(seconds=phase_duration.total_seconds() * scaling_factor)
            
            total_duration += phase_duration
        
        return total_duration

    def _estimate_resource_requirements(self, phases: List[Dict[str, Any]], 
                                      target_count: int) -> Dict[str, str]:
        """Estimate resource requirements"""
        cpu_usage = "low"
        memory_usage = "low"
        network_usage = "medium"
        
        # Adjust based on phases and target count
        if len(phases) > 2 or target_count > 10:
            cpu_usage = "medium"
            memory_usage = "medium"
        
        if target_count > 50:
            cpu_usage = "high"
            memory_usage = "high"
            network_usage = "high"
        
        return {
            "cpu": cpu_usage,
            "memory": memory_usage,
            "network": network_usage,
            "storage": "low"
        }

    def _calculate_scan_priority(self, context: TargetContext, 
                               threat_intel: ThreatIntelligence) -> ScanPriority:
        """Calculate scan priority"""
        priority_score = 0
        
        # Business criticality factor
        criticality_scores = {
            "critical": 4,
            "high": 3,
            "medium": 2,
            "low": 1
        }
        priority_score += criticality_scores.get(context.business_criticality, 2)
        
        # Environment factor
        if context.environment == "production":
            priority_score += 2
        elif context.environment == "staging":
            priority_score += 1
        
        # Threat intelligence factor
        if threat_intel.confidence_level > 0.8:
            priority_score += 2
        
        # Map score to priority
        if priority_score >= 7:
            return ScanPriority.CRITICAL
        elif priority_score >= 5:
            return ScanPriority.HIGH
        elif priority_score >= 3:
            return ScanPriority.MEDIUM
        else:
            return ScanPriority.LOW

    def _assess_scan_risks(self, strategy: ScanStrategy, context: TargetContext) -> Dict[str, str]:
        """Assess risks associated with scanning"""
        risks = {
            "detection_risk": "medium",
            "impact_risk": "low",
            "false_positive_risk": "medium"
        }
        
        # Adjust based on strategy
        if strategy == ScanStrategy.STEALTH:
            risks["detection_risk"] = "low"
        elif strategy == ScanStrategy.AGGRESSIVE:
            risks["detection_risk"] = "high"
            risks["impact_risk"] = "medium"
        
        # Adjust based on environment
        if context.environment == "production":
            risks["impact_risk"] = "medium"
        
        return risks

    def _calculate_expected_coverage(self, strategy: ScanStrategy, tools: List[str]) -> float:
        """Calculate expected vulnerability coverage"""
        base_coverage = {
            ScanStrategy.RECONNAISSANCE: 0.3,
            ScanStrategy.TARGETED: 0.7,
            ScanStrategy.COMPREHENSIVE: 0.9,
            ScanStrategy.STEALTH: 0.5,
            ScanStrategy.AGGRESSIVE: 0.8,
            ScanStrategy.COMPLIANCE: 0.6
        }
        
        coverage = base_coverage.get(strategy, 0.5)
        
        # Adjust based on tools
        if len(tools) > 1:
            coverage += 0.1
        if "nuclei" in tools:
            coverage += 0.1
        
        return min(coverage, 1.0)

    def _determine_stealth_level(self, strategy: ScanStrategy, context: TargetContext) -> str:
        """Determine stealth level for the scan"""
        if context.stealth_requirements or strategy == ScanStrategy.STEALTH:
            return "high"
        elif strategy == ScanStrategy.AGGRESSIVE:
            return "low"
        else:
            return "medium"

    def _generate_justification(self, strategy: ScanStrategy,
                              target_analysis: Dict[str, Any],
                              threat_intel: ThreatIntelligence) -> str:
        """Generate justification for the recommendation"""
        profile_type = target_analysis.get("profile_type", TargetProfile.UNKNOWN)
        
        justifications = {
            ScanStrategy.RECONNAISSANCE: "Initial reconnaissance to understand target attack surface",
            ScanStrategy.COMPREHENSIVE: f"Comprehensive assessment recommended for {profile_type.value} targets",
            ScanStrategy.TARGETED: f"Focused assessment targeting {profile_type.value} specific vulnerabilities",
            ScanStrategy.STEALTH: "Low-profile scanning to avoid detection while gathering intelligence",
            ScanStrategy.COMPLIANCE: "Compliance-focused scanning for regulatory requirements"
        }
        
        base_justification = justifications.get(strategy, "Security assessment recommendation")
        
        # Add threat intelligence context
        if threat_intel.trending_vulnerabilities:
            base_justification += f". Current threat landscape shows activity in: {', '.join(threat_intel.trending_vulnerabilities[:2])}"
        
        return base_justification

    def _identify_prerequisites(self, strategy: ScanStrategy, context: TargetContext) -> List[str]:
        """Identify prerequisites for the scan"""
        prerequisites = ["Network connectivity to targets"]
        
        if strategy in [ScanStrategy.COMPREHENSIVE, ScanStrategy.TARGETED]:
            prerequisites.append("Sufficient scanning time allocation")
        
        if context.authentication_info:
            prerequisites.append("Valid authentication credentials")
        
        if context.stealth_requirements:
            prerequisites.append("Approved stealth scanning window")
        
        return prerequisites

    def _define_success_criteria(self, strategy: ScanStrategy, context: TargetContext) -> List[str]:
        """Define success criteria for the scan"""
        criteria = ["Complete target enumeration", "Vulnerability identification"]
        
        if strategy == ScanStrategy.COMPREHENSIVE:
            criteria.extend([
                "Service version detection",
                "Complete port scanning",
                "Security configuration assessment"
            ])
        
        if context.compliance_requirements:
            criteria.append("Compliance requirement coverage")
        
        return criteria

    # Additional utility methods

    def _assess_security_posture(self, previous_scans: List[Dict[str, Any]]) -> str:
        """Assess security posture based on previous scans"""
        if not previous_scans:
            return "unknown"
        
        # Analyze trends in vulnerability counts
        recent_scan = previous_scans[-1]
        vuln_count = recent_scan.get("vulnerability_count", 0)
        
        if vuln_count == 0:
            return "strong"
        elif vuln_count < 5:
            return "good"
        elif vuln_count < 15:
            return "fair"
        else:
            return "weak"

    def _get_profile_recommendations(self, profile_type: TargetProfile) -> List[str]:
        """Get profile-specific recommendations"""
        recommendations = {
            TargetProfile.WEB_APPLICATION: [
                "Focus on web application vulnerabilities",
                "Include OWASP Top 10 testing",
                "Test for authentication bypass"
            ],
            TargetProfile.NETWORK_INFRASTRUCTURE: [
                "Comprehensive port scanning",
                "Service enumeration",
                "Network protocol testing"
            ],
            TargetProfile.API_ENDPOINT: [
                "API-specific vulnerability testing",
                "Authentication mechanism testing",
                "Rate limiting assessment"
            ]
        }
        
        return recommendations.get(profile_type, ["Standard vulnerability assessment"])

    async def _create_time_constrained_recommendation(self, context: TargetContext,
                                                    target_analysis: Dict[str, Any]) -> ScanRecommendation:
        """Create recommendation optimized for time constraints"""
        max_duration_str = context.time_constraints.get("max_duration", "1h")
        
        # Parse duration string (simplified)
        if "m" in max_duration_str:
            max_minutes = int(max_duration_str.replace("m", ""))
            max_duration = timedelta(minutes=max_minutes)
        else:
            max_hours = int(max_duration_str.replace("h", ""))
            max_duration = timedelta(hours=max_hours)
        
        # Create optimized phases
        quick_phases = [
            {
                "phase_id": "quick_discovery",
                "phase_name": "Rapid Discovery",
                "tools": ["nmap"],
                "configuration": {
                    "scan_type": "syn",
                    "port_range": "top-100",
                    "timing": "aggressive"
                },
                "estimated_time": max_duration * 0.7
            }
        ]
        
        if max_duration > timedelta(minutes=30):
            quick_phases.append({
                "phase_id": "critical_vulns",
                "phase_name": "Critical Vulnerability Check",
                "tools": ["nuclei"],
                "configuration": {
                    "severity_filter": ["critical"],
                    "rate_limit": 200
                },
                "estimated_time": max_duration * 0.3
            })
        
        return ScanRecommendation(
            recommendation_id=f"time_constrained_{context.target_id}",
            strategy=ScanStrategy.RECONNAISSANCE,
            priority=ScanPriority.HIGH,
            confidence=0.7,
            scan_phases=quick_phases,
            tools_recommended=["nmap", "nuclei"],
            estimated_duration=max_duration,
            resource_requirements={"cpu": "high", "network": "high"},
            risk_assessment={"detection_risk": "medium", "impact_risk": "low"},
            expected_coverage=0.4,
            stealth_level="low",
            justification=f"Time-optimized scan within {max_duration_str} constraint"
        )

    async def _create_stealth_recommendation(self, context: TargetContext,
                                           target_analysis: Dict[str, Any]) -> ScanRecommendation:
        """Create stealth-optimized recommendation"""
        stealth_phases = [
            {
                "phase_id": "stealth_discovery",
                "phase_name": "Stealthy Discovery",
                "tools": ["nmap"],
                "configuration": {
                    "scan_type": "syn",
                    "timing": "slow",
                    "source_port": "random",
                    "decoy_scan": True,
                    "fragment_packets": True
                },
                "estimated_time": timedelta(hours=2)
            }
        ]
        
        return ScanRecommendation(
            recommendation_id=f"stealth_{context.target_id}",
            strategy=ScanStrategy.STEALTH,
            priority=ScanPriority.MEDIUM,
            confidence=0.8,
            scan_phases=stealth_phases,
            tools_recommended=["nmap"],
            estimated_duration=timedelta(hours=3),
            resource_requirements={"cpu": "low", "network": "low"},
            risk_assessment={"detection_risk": "very_low", "impact_risk": "minimal"},
            expected_coverage=0.5,
            stealth_level="very_high",
            justification="Stealth-optimized scan to minimize detection risk"
        )

    async def _create_threat_focused_recommendation(self, context: TargetContext,
                                                  target_analysis: Dict[str, Any],
                                                  threat_intel: ThreatIntelligence) -> ScanRecommendation:
        """Create threat intelligence focused recommendation"""
        threat_phases = [
            {
                "phase_id": "threat_focused_scan",
                "phase_name": "Threat Intelligence Focused Scan",
                "tools": ["nuclei"],
                "configuration": {
                    "threat_templates": threat_intel.trending_vulnerabilities,
                    "priority_cves": True,
                    "attack_simulation": True
                },
                "estimated_time": timedelta(minutes=45)
            }
        ]
        
        return ScanRecommendation(
            recommendation_id=f"threat_focused_{context.target_id}",
            strategy=ScanStrategy.TARGETED,
            priority=ScanPriority.HIGH,
            confidence=0.9,
            scan_phases=threat_phases,
            tools_recommended=["nuclei"],
            estimated_duration=timedelta(hours=1),
            resource_requirements={"cpu": "medium", "network": "medium"},
            risk_assessment={"detection_risk": "medium", "impact_risk": "low"},
            expected_coverage=0.8,
            stealth_level="medium",
            justification=f"Threat-focused scan targeting current threats: {', '.join(threat_intel.trending_vulnerabilities[:3])}"
        )

    def _deduplicate_recommendations(self, recommendations: List[ScanRecommendation]) -> List[ScanRecommendation]:
        """Remove duplicate recommendations"""
        unique_recs = []
        seen_strategies = set()
        
        for rec in recommendations:
            if rec.strategy not in seen_strategies:
                unique_recs.append(rec)
                seen_strategies.add(rec.strategy)
        
        return unique_recs

    async def _optimize_single_recommendation(self, recommendation: ScanRecommendation,
                                            context: TargetContext) -> ScanRecommendation:
        """Optimize a single recommendation"""
        # Optimize based on historical performance
        if recommendation.strategy in self.success_patterns:
            patterns = self.success_patterns[recommendation.strategy]
            if patterns:
                # Apply successful patterns
                recommendation.confidence *= 1.1
        
        # Optimize resource allocation
        if context.environment == "production":
            # Reduce resource usage in production
            recommendation.resource_requirements = {
                k: "low" if v == "high" else v 
                for k, v in recommendation.resource_requirements.items()
            }
        
        return recommendation

    async def _create_reduced_recommendation(self, original: ScanRecommendation,
                                           available_time: timedelta) -> Optional[ScanRecommendation]:
        """Create reduced version of recommendation that fits time constraint"""
        if available_time < timedelta(minutes=10):
            return None
        
        # Create simplified version
        reduced_phases = []
        time_budget = available_time
        
        for phase in original.scan_phases:
            phase_time = phase.get("estimated_time", timedelta(minutes=30))
            if time_budget >= phase_time:
                reduced_phases.append(phase)
                time_budget -= phase_time
            else:
                # Try to create a shortened version of this phase
                if time_budget >= timedelta(minutes=10):
                    shortened_phase = phase.copy()
                    shortened_phase["estimated_time"] = time_budget
                    shortened_phase["configuration"]["reduced"] = True
                    reduced_phases.append(shortened_phase)
                break
        
        if not reduced_phases:
            return None
        
        # Create reduced recommendation
        reduced = ScanRecommendation(
            recommendation_id=f"reduced_{original.recommendation_id}",
            strategy=original.strategy,
            priority=original.priority,
            confidence=original.confidence * 0.8,  # Lower confidence for reduced scan
            scan_phases=reduced_phases,
            tools_recommended=original.tools_recommended,
            estimated_duration=available_time,
            resource_requirements=original.resource_requirements,
            risk_assessment=original.risk_assessment,
            expected_coverage=original.expected_coverage * 0.6,  # Lower coverage
            stealth_level=original.stealth_level,
            justification=f"Reduced version: {original.justification}"
        )
        
        return reduced

    def _calculate_recommendation_confidence(self, recommendations: List[ScanRecommendation],
                                           context: TargetContext) -> float:
        """Calculate overall confidence in recommendations"""
        if not recommendations:
            return 0.0
        
        # Average confidence of all recommendations
        avg_confidence = sum(rec.confidence for rec in recommendations) / len(recommendations)
        
        # Adjust based on context completeness
        context_completeness = 0.0
        if context.target_type != TargetProfile.UNKNOWN:
            context_completeness += 0.2
        if context.previous_scans:
            context_completeness += 0.2
        if context.known_technologies:
            context_completeness += 0.1
        
        return min(avg_confidence + context_completeness, 1.0)

    def _generate_recommendation_insights(self, recommendations: List[ScanRecommendation],
                                        target_analysis: Dict[str, Any]) -> List[str]:
        """Generate insights about the recommendations"""
        insights = []
        
        if len(recommendations) > 1:
            insights.append(f"Generated {len(recommendations)} scan strategies for comprehensive coverage")
        
        # Strategy distribution insights
        strategies = [rec.strategy for rec in recommendations]
        if ScanStrategy.STEALTH in strategies:
            insights.append("Stealth scanning recommended due to sensitivity requirements")
        
        if ScanStrategy.COMPREHENSIVE in strategies:
            insights.append("Comprehensive scanning will provide thorough security assessment")
        
        # Target-specific insights
        profile_type = target_analysis.get("profile_type", TargetProfile.UNKNOWN)
        if profile_type != TargetProfile.UNKNOWN:
            insights.append(f"Scan strategies optimized for {profile_type.value} targets")
        
        # Time and resource insights
        total_time = sum(rec.estimated_duration.total_seconds() for rec in recommendations)
        if total_time > 3600:  # More than 1 hour
            insights.append("Extended scan duration recommended for thorough assessment")
        
        return insights

    def _update_recommendation_statistics(self, recommendations: List[ScanRecommendation],
                                        context: TargetContext):
        """Update recommendation statistics"""
        self.recommendation_stats["total_recommendations"] += len(recommendations)
        
        # Track strategy usage
        for rec in recommendations:
            strategy = rec.strategy.value
            self.recommendation_stats["strategies_used"][strategy] = \
                self.recommendation_stats["strategies_used"].get(strategy, 0) + 1
        
        # Track target profile analysis
        profile = context.target_type.value
        self.recommendation_stats["target_profiles_analyzed"][profile] = \
            self.recommendation_stats["target_profiles_analyzed"].get(profile, 0) + 1

    # Knowledge base loading methods

    def _load_target_profiles(self) -> Dict[str, Any]:
        """Load target profile definitions"""
        return {
            "web_application": {
                "common_ports": [80, 443, 8080, 8443],
                "technologies": ["HTTP", "HTTPS", "HTML", "JavaScript"],
                "vulnerability_categories": ["OWASP Top 10", "Web application"]
            },
            "network_infrastructure": {
                "common_ports": [22, 23, 53, 161, 443],
                "technologies": ["SSH", "SNMP", "DNS"],
                "vulnerability_categories": ["Network", "Protocol", "Service"]
            }
        }

    def _load_scan_templates(self) -> Dict[str, Any]:
        """Load scan template configurations"""
        return {
            "web_focused": {
                "tools": ["nmap", "nuclei"],
                "phases": ["discovery", "web_vulnerability_scan"],
                "estimated_duration": "1h"
            },
            "network_focused": {
                "tools": ["nmap"],
                "phases": ["discovery", "service_enumeration", "vulnerability_scan"],
                "estimated_duration": "2h"
            }
        }

    def _load_tool_capabilities(self) -> Dict[str, Any]:
        """Load tool capability definitions"""
        return {
            "nmap": {
                "capabilities": ["port_scanning", "service_detection", "os_detection", "script_scanning"],
                "target_types": ["network", "host"],
                "stealth_options": True
            },
            "nuclei": {
                "capabilities": ["vulnerability_scanning", "web_testing", "api_testing"],
                "target_types": ["web", "api", "network"],
                "template_based": True
            }
        }

    def _load_compliance_mappings(self) -> Dict[str, Any]:
        """Load compliance framework mappings"""
        return {
            "PCI-DSS": {
                "requirements": ["network_segmentation", "encryption", "access_control"],
                "scan_focus": ["ports", "ssl", "authentication"],
                "tools": ["nmap", "nuclei"]
            },
            "NIST": {
                "requirements": ["asset_inventory", "vulnerability_management"],
                "scan_focus": ["services", "configurations", "patches"],
                "tools": ["nmap", "nuclei"]
            }
        }