import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen, fireEvent, waitFor } from '@/test/utils'
import userEvent from '@testing-library/user-event'
import { NmapScanner } from './NmapScanner'
import { createMockElectronAPI, createMockWebSocket } from '@/test/utils'

// Mock the stores
vi.mock('@/stores/backend-store', () => ({
  useBackendStore: () => ({
    isConnected: true,
    executeTool: vi.fn().mockResolvedValue({ executionId: 'exec_nmap_1' }),
    getToolStatus: vi.fn().mockResolvedValue({ status: 'available' })
  })
}))

vi.mock('@/stores/app-store', () => ({
  useAppStore: () => ({
    addNotification: vi.fn()
  })
}))

describe('NmapScanner', () => {
  const user = userEvent.setup()
  
  beforeEach(() => {
    global.electronAPI = createMockElectronAPI()
    global.WebSocket = vi.fn(() => createMockWebSocket())
  })

  describe('Component Rendering', () => {
    it('renders the Nmap scanner interface', () => {
      render(<NmapScanner />)
      
      expect(screen.getByText('Nmap Scanner')).toBeInTheDocument()
      expect(screen.getByText('Network Discovery and Security Auditing')).toBeInTheDocument()
      expect(screen.getByRole('tab', { name: 'Configure' })).toBeInTheDocument()
      expect(screen.getByRole('tab', { name: 'Output' })).toBeInTheDocument()
      expect(screen.getByRole('tab', { name: 'Results' })).toBeInTheDocument()
      expect(screen.getByRole('tab', { name: 'Command' })).toBeInTheDocument()
    })

    it('displays the configuration form by default', () => {
      render(<NmapScanner />)
      
      expect(screen.getByLabelText(/Target/)).toBeInTheDocument()
      expect(screen.getByLabelText(/Scan Type/)).toBeInTheDocument()
      expect(screen.getByLabelText(/Port Range/)).toBeInTheDocument()
      expect(screen.getByLabelText(/Timing Template/)).toBeInTheDocument()
    })

    it('shows all scan type options', async () => {
      render(<NmapScanner />)
      
      const scanTypeSelect = screen.getByLabelText(/Scan Type/)
      await user.click(scanTypeSelect)
      
      expect(screen.getByText('TCP Connect Scan')).toBeInTheDocument()
      expect(screen.getByText('TCP SYN Scan')).toBeInTheDocument()
      expect(screen.getByText('UDP Scan')).toBeInTheDocument()
      expect(screen.getByText('Comprehensive Scan')).toBeInTheDocument()
    })

    it('shows timing template options', async () => {
      render(<NmapScanner />)
      
      const timingSelect = screen.getByLabelText(/Timing Template/)
      await user.click(timingSelect)
      
      expect(screen.getByText('Paranoid (T0)')).toBeInTheDocument()
      expect(screen.getByText('Sneaky (T1)')).toBeInTheDocument()
      expect(screen.getByText('Polite (T2)')).toBeInTheDocument()
      expect(screen.getByText('Normal (T3)')).toBeInTheDocument()
      expect(screen.getByText('Aggressive (T4)')).toBeInTheDocument()
      expect(screen.getByText('Insane (T5)')).toBeInTheDocument()
    })
  })

  describe('Form Validation', () => {
    it('requires a target to be specified', async () => {
      render(<NmapScanner />)
      
      const startButton = screen.getByRole('button', { name: /Start Scan/ })
      await user.click(startButton)
      
      await waitFor(() => {
        expect(screen.getByText(/Target is required/)).toBeInTheDocument()
      })
    })

    it('validates IP address format', async () => {
      render(<NmapScanner />)
      
      const targetInput = screen.getByLabelText(/Target/)
      await user.type(targetInput, 'invalid-ip')
      
      const startButton = screen.getByRole('button', { name: /Start Scan/ })
      await user.click(startButton)
      
      await waitFor(() => {
        expect(screen.getByText(/Invalid target format/)).toBeInTheDocument()
      })
    })

    it('accepts valid IP addresses', async () => {
      render(<NmapScanner />)
      
      const targetInput = screen.getByLabelText(/Target/)
      await user.type(targetInput, '***********')
      
      expect(screen.queryByText(/Invalid target format/)).not.toBeInTheDocument()
    })

    it('accepts CIDR notation', async () => {
      render(<NmapScanner />)
      
      const targetInput = screen.getByLabelText(/Target/)
      await user.type(targetInput, '***********/24')
      
      expect(screen.queryByText(/Invalid target format/)).not.toBeInTheDocument()
    })

    it('accepts hostname format', async () => {
      render(<NmapScanner />)
      
      const targetInput = screen.getByLabelText(/Target/)
      await user.type(targetInput, 'example.com')
      
      expect(screen.queryByText(/Invalid target format/)).not.toBeInTheDocument()
    })

    it('validates port range format', async () => {
      render(<NmapScanner />)
      
      const portInput = screen.getByLabelText(/Port Range/)
      await user.clear(portInput)
      await user.type(portInput, 'invalid-ports')
      
      const startButton = screen.getByRole('button', { name: /Start Scan/ })
      await user.click(startButton)
      
      await waitFor(() => {
        expect(screen.getByText(/Invalid port range format/)).toBeInTheDocument()
      })
    })
  })

  describe('Tool Execution', () => {
    it('starts a scan with valid configuration', async () => {
      const mockExecuteTool = vi.fn().mockResolvedValue({ executionId: 'exec_nmap_1' })
      
      vi.mocked(useBackendStore).mockReturnValue({
        isConnected: true,
        executeTool: mockExecuteTool,
        getToolStatus: vi.fn().mockResolvedValue({ status: 'available' })
      })
      
      render(<NmapScanner />)
      
      // Fill in the form
      const targetInput = screen.getByLabelText(/Target/)
      await user.type(targetInput, '***********')
      
      const scanTypeSelect = screen.getByLabelText(/Scan Type/)
      await user.click(scanTypeSelect)
      await user.click(screen.getByText('TCP SYN Scan'))
      
      // Start the scan
      const startButton = screen.getByRole('button', { name: /Start Scan/ })
      await user.click(startButton)
      
      await waitFor(() => {
        expect(mockExecuteTool).toHaveBeenCalledWith('nmap', {
          target: '***********',
          scanType: 'syn',
          portRange: '1-65535',
          timing: 3,
          scripts: [],
          outputFormat: 'normal',
          osDetection: false,
          serviceDetection: false,
          aggressiveScan: false,
          noResolve: false,
          privileged: false
        })
      })
    })

    it('shows execution progress', async () => {
      render(<NmapScanner />)
      
      const targetInput = screen.getByLabelText(/Target/)
      await user.type(targetInput, '***********')
      
      const startButton = screen.getByRole('button', { name: /Start Scan/ })
      await user.click(startButton)
      
      await waitFor(() => {
        expect(screen.getByText(/Starting scan.../)).toBeInTheDocument()
      })
    })

    it('switches to output tab when scan starts', async () => {
      render(<NmapScanner />)
      
      const targetInput = screen.getByLabelText(/Target/)
      await user.type(targetInput, '***********')
      
      const startButton = screen.getByRole('button', { name: /Start Scan/ })
      await user.click(startButton)
      
      await waitFor(() => {
        expect(screen.getByRole('tab', { name: 'Output' })).toHaveAttribute('aria-selected', 'true')
      })
    })

    it('allows stopping a running scan', async () => {
      render(<NmapScanner />)
      
      // Start a scan
      const targetInput = screen.getByLabelText(/Target/)
      await user.type(targetInput, '***********')
      
      const startButton = screen.getByRole('button', { name: /Start Scan/ })
      await user.click(startButton)
      
      // Stop the scan
      await waitFor(() => {
        const stopButton = screen.getByRole('button', { name: /Stop Scan/ })
        expect(stopButton).toBeInTheDocument()
      })
      
      const stopButton = screen.getByRole('button', { name: /Stop Scan/ })
      await user.click(stopButton)
      
      await waitFor(() => {
        expect(screen.getByRole('button', { name: /Start Scan/ })).toBeInTheDocument()
      })
    })
  })

  describe('Real-time Output', () => {
    it('displays real-time output in the output tab', async () => {
      render(<NmapScanner />)
      
      // Start a scan to get to output tab
      const targetInput = screen.getByLabelText(/Target/)
      await user.type(targetInput, '***********')
      
      const startButton = screen.getByRole('button', { name: /Start Scan/ })
      await user.click(startButton)
      
      // Simulate output updates
      await waitFor(() => {
        expect(screen.getByText(/Terminal Output/)).toBeInTheDocument()
      })
    })

    it('shows progress indicator during scan', async () => {
      render(<NmapScanner />)
      
      const targetInput = screen.getByLabelText(/Target/)
      await user.type(targetInput, '***********')
      
      const startButton = screen.getByRole('button', { name: /Start Scan/ })
      await user.click(startButton)
      
      await waitFor(() => {
        expect(screen.getByRole('progressbar')).toBeInTheDocument()
      })
    })
  })

  describe('Export Functionality', () => {
    it('shows export options when scan is complete', async () => {
      render(<NmapScanner />)
      
      // Start and complete a scan
      const targetInput = screen.getByLabelText(/Target/)
      await user.type(targetInput, '***********')
      
      const startButton = screen.getByRole('button', { name: /Start Scan/ })
      await user.click(startButton)
      
      // Switch to results tab
      const resultsTab = screen.getByRole('tab', { name: 'Results' })
      await user.click(resultsTab)
      
      await waitFor(() => {
        expect(screen.getByRole('button', { name: /Export/ })).toBeInTheDocument()
      })
    })

    it('exports results in different formats', async () => {
      const mockWriteFile = vi.fn().mockResolvedValue(undefined)
      global.electronAPI.writeFile = mockWriteFile
      
      render(<NmapScanner />)
      
      // Complete a scan and go to results
      const targetInput = screen.getByLabelText(/Target/)
      await user.type(targetInput, '***********')
      
      const startButton = screen.getByRole('button', { name: /Start Scan/ })
      await user.click(startButton)
      
      const resultsTab = screen.getByRole('tab', { name: 'Results' })
      await user.click(resultsTab)
      
      // Export results
      const exportButton = screen.getByRole('button', { name: /Export/ })
      await user.click(exportButton)
      
      const jsonOption = screen.getByText('JSON')
      await user.click(jsonOption)
      
      await waitFor(() => {
        expect(mockWriteFile).toHaveBeenCalled()
      })
    })
  })

  describe('Command Generation', () => {
    it('generates correct nmap command', async () => {
      render(<NmapScanner />)
      
      // Configure the scan
      const targetInput = screen.getByLabelText(/Target/)
      await user.type(targetInput, '***********')
      
      const scanTypeSelect = screen.getByLabelText(/Scan Type/)
      await user.click(scanTypeSelect)
      await user.click(screen.getByText('TCP SYN Scan'))
      
      const timingSelect = screen.getByLabelText(/Timing Template/)
      await user.click(timingSelect)
      await user.click(screen.getByText('Aggressive (T4)'))
      
      // Switch to command tab
      const commandTab = screen.getByRole('tab', { name: 'Command' })
      await user.click(commandTab)
      
      await waitFor(() => {
        expect(screen.getByText(/nmap -sS -T4 -p 1-65535 ***********/)).toBeInTheDocument()
      })
    })

    it('includes scripts in command when selected', async () => {
      render(<NmapScanner />)
      
      const targetInput = screen.getByLabelText(/Target/)
      await user.type(targetInput, '***********')
      
      // Select some scripts
      const scriptsSelect = screen.getByLabelText(/NSE Scripts/)
      await user.click(scriptsSelect)
      await user.click(screen.getByText('vuln'))
      
      const commandTab = screen.getByRole('tab', { name: 'Command' })
      await user.click(commandTab)
      
      await waitFor(() => {
        expect(screen.getByText(/--script=vuln/)).toBeInTheDocument()
      })
    })

    it('allows copying command to clipboard', async () => {
      const mockWriteText = vi.fn()
      Object.assign(navigator, {
        clipboard: {
          writeText: mockWriteText
        }
      })
      
      render(<NmapScanner />)
      
      const targetInput = screen.getByLabelText(/Target/)
      await user.type(targetInput, '***********')
      
      const commandTab = screen.getByRole('tab', { name: 'Command' })
      await user.click(commandTab)
      
      const copyButton = screen.getByRole('button', { name: /Copy Command/ })
      await user.click(copyButton)
      
      await waitFor(() => {
        expect(mockWriteText).toHaveBeenCalled()
      })
    })
  })

  describe('Advanced Options', () => {
    it('shows advanced options when expanded', async () => {
      render(<NmapScanner />)
      
      const advancedButton = screen.getByRole('button', { name: /Advanced Options/ })
      await user.click(advancedButton)
      
      await waitFor(() => {
        expect(screen.getByLabelText(/OS Detection/)).toBeInTheDocument()
        expect(screen.getByLabelText(/Service Detection/)).toBeInTheDocument()
        expect(screen.getByLabelText(/Aggressive Scan/)).toBeInTheDocument()
      })
    })

    it('includes advanced options in configuration', async () => {
      const mockExecuteTool = vi.fn().mockResolvedValue({ executionId: 'exec_nmap_1' })
      
      vi.mocked(useBackendStore).mockReturnValue({
        isConnected: true,
        executeTool: mockExecuteTool,
        getToolStatus: vi.fn().mockResolvedValue({ status: 'available' })
      })
      
      render(<NmapScanner />)
      
      const targetInput = screen.getByLabelText(/Target/)
      await user.type(targetInput, '***********')
      
      // Enable advanced options
      const advancedButton = screen.getByRole('button', { name: /Advanced Options/ })
      await user.click(advancedButton)
      
      const osDetection = screen.getByLabelText(/OS Detection/)
      await user.click(osDetection)
      
      const serviceDetection = screen.getByLabelText(/Service Detection/)
      await user.click(serviceDetection)
      
      const startButton = screen.getByRole('button', { name: /Start Scan/ })
      await user.click(startButton)
      
      await waitFor(() => {
        expect(mockExecuteTool).toHaveBeenCalledWith('nmap', expect.objectContaining({
          osDetection: true,
          serviceDetection: true
        }))
      })
    })
  })

  describe('Error Handling', () => {
    it('shows error message when tool execution fails', async () => {
      const mockExecuteTool = vi.fn().mockRejectedValue(new Error('Tool execution failed'))
      
      vi.mocked(useBackendStore).mockReturnValue({
        isConnected: true,
        executeTool: mockExecuteTool,
        getToolStatus: vi.fn().mockResolvedValue({ status: 'available' })
      })
      
      render(<NmapScanner />)
      
      const targetInput = screen.getByLabelText(/Target/)
      await user.type(targetInput, '***********')
      
      const startButton = screen.getByRole('button', { name: /Start Scan/ })
      await user.click(startButton)
      
      await waitFor(() => {
        expect(screen.getByText(/Tool execution failed/)).toBeInTheDocument()
      })
    })

    it('shows warning when backend is disconnected', () => {
      vi.mocked(useBackendStore).mockReturnValue({
        isConnected: false,
        executeTool: vi.fn(),
        getToolStatus: vi.fn()
      })
      
      render(<NmapScanner />)
      
      expect(screen.getByText(/Backend disconnected/)).toBeInTheDocument()
    })

    it('disables start button when tool is unavailable', () => {
      vi.mocked(useBackendStore).mockReturnValue({
        isConnected: true,
        executeTool: vi.fn(),
        getToolStatus: vi.fn().mockResolvedValue({ status: 'unavailable' })
      })
      
      render(<NmapScanner />)
      
      const startButton = screen.getByRole('button', { name: /Start Scan/ })
      expect(startButton).toBeDisabled()
    })
  })
})