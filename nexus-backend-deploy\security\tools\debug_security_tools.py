#!/usr/bin/env python3
"""
🛡️ Security Tools Arsenal Debugger 🛡️
Comprehensive debugging for all security tools and frameworks

This script performs detailed analysis of:
- Security tool availability and versions
- Tool execution modes (simulation vs real)
- WSL integration and cross-platform compatibility
- Custom security modules and extensions
- AI-enhanced security capabilities
- MITRE ATT&CK framework integration
- Compliance testing frameworks
- Vulnerability databases and updates
- Network tool configurations
- Exploit frameworks and payload generation
"""

import asyncio
import json
import logging
import subprocess
import sys
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional
import os
import platform
import requests

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root / 'src'))

class SecurityToolsDebugger:
    """🛡️ Comprehensive security tools debugging"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.backend_url = "http://localhost:8000"
        self.results = {}
        self.execution_mode = os.getenv('EXECUTION_MODE', 'simulation')
        
        print("🛡️ SECURITY TOOLS ARSENAL DEBUGGER")
        print("=" * 50)
        print(f"Execution Mode: {self.execution_mode.upper()}")
        print("=" * 50)

    async def debug_all_security_tools(self):
        """Execute comprehensive security tools debugging"""
        
        # Core Tool Availability
        await self._debug_core_security_tools()
        await self._debug_tool_versions()
        
        # Execution Environment
        await self._debug_execution_environment()
        await self._debug_wsl_integration()
        
        # Tool Manager System
        await self._debug_unified_tool_manager()
        await self._debug_custom_tools()
        
        # AI-Enhanced Security
        await self._debug_ai_security_integration()
        await self._debug_exploit_generation()
        
        # Specialized Tools
        await self._debug_web_security_tools()
        await self._debug_network_security_tools()
        await self._debug_compliance_tools()
        
        # Integration & Performance
        await self._debug_tool_orchestration()
        await self._debug_performance_optimization()
        
        return self._generate_security_report()

    async def _debug_core_security_tools(self):
        """🔧 Debug core security tools availability"""
        print("\n🔧 Core Security Tools Analysis")
        print("-" * 30)
        
        # Define core security tools
        core_tools = {
            'nmap': {
                'description': 'Network discovery and port scanning',
                'test_command': ['nmap', '--version'],
                'category': 'network'
            },
            'nuclei': {
                'description': 'Fast vulnerability scanner with templates',
                'test_command': ['nuclei', '-version'],
                'category': 'vulnerability'
            },
            'sqlmap': {
                'description': 'SQL injection testing tool',
                'test_command': ['sqlmap', '--version'],
                'category': 'web'
            },
            'gobuster': {
                'description': 'Directory/file bruteforcer',
                'test_command': ['gobuster', 'version'],
                'category': 'web'
            },
            'nikto': {
                'description': 'Web server vulnerability scanner',
                'test_command': ['nikto', '-Version'],
                'category': 'web'
            },
            'dirb': {
                'description': 'Web content scanner',
                'test_command': ['dirb'],
                'category': 'web'
            },
            'masscan': {
                'description': 'Fast port scanner',
                'test_command': ['masscan', '--version'],
                'category': 'network'
            },
            'ffuf': {
                'description': 'Fast web fuzzer',
                'test_command': ['ffuf', '-V'],
                'category': 'web'
            }
        }
        
        tool_results = {}
        
        for tool_name, tool_info in core_tools.items():
            try:
                # Test tool availability
                result = subprocess.run(
                    tool_info['test_command'],
                    capture_output=True,
                    text=True,
                    timeout=10
                )
                
                if result.returncode == 0:
                    # Extract version if possible
                    output = result.stdout + result.stderr
                    version_info = self._extract_version(output, tool_name)
                    
                    print(f"✅ {tool_name:.<15} {tool_info['description']}")
                    if version_info:
                        print(f"   Version: {version_info}")
                    
                    tool_results[tool_name] = {
                        'status': 'available',
                        'version': version_info,
                        'category': tool_info['category'],
                        'description': tool_info['description']
                    }
                else:
                    print(f"❌ {tool_name:.<15} Not responding properly")
                    tool_results[tool_name] = {
                        'status': 'not_responding',
                        'error': result.stderr[:200] if result.stderr else 'Unknown error'
                    }
                    
            except FileNotFoundError:
                print(f"⚠️ {tool_name:.<15} Not installed")
                tool_results[tool_name] = {
                    'status': 'not_installed',
                    'category': tool_info['category'],
                    'description': tool_info['description']
                }
            except subprocess.TimeoutExpired:
                print(f"⏱️ {tool_name:.<15} Timeout")
                tool_results[tool_name] = {
                    'status': 'timeout'
                }
            except Exception as e:
                print(f"❌ {tool_name:.<15} Error: {str(e)}")
                tool_results[tool_name] = {
                    'status': 'error',
                    'error': str(e)
                }
        
        self.results['core_tools'] = tool_results
        
        # Summary
        available = len([t for t in tool_results.values() if t['status'] == 'available'])
        total = len(tool_results)
        print(f"\n📊 Tool Availability: {available}/{total} ({available/total*100:.1f}%)")

    def _extract_version(self, output: str, tool_name: str) -> Optional[str]:
        """Extract version information from tool output"""
        import re
        
        # Common version patterns
        patterns = [
            r'(?i)version\s+(\d+\.\d+\.\d+)',
            r'(?i)v(\d+\.\d+\.\d+)',
            r'(\d+\.\d+\.\d+)',
            r'(?i)' + tool_name + r'\s+(\d+\.\d+)'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, output)
            if match:
                return match.group(1)
        
        return None

    async def _debug_tool_versions(self):
        """📋 Debug tool versions and compatibility"""
        print("\n📋 Tool Version Analysis")
        print("-" * 30)
        
        # Check Python tools
        python_tools = ['requests', 'sqlite3', 'asyncio', 'json']
        
        for tool in python_tools:
            try:
                module = __import__(tool)
                version = getattr(module, '__version__', 'Unknown')
                print(f"✅ Python {tool}: {version}")
            except ImportError:
                print(f"❌ Python {tool}: Not available")
        
        # Check system information
        system_info = {
            'OS': platform.system(),
            'Release': platform.release(),
            'Architecture': platform.machine(),
            'Python': sys.version.split()[0]
        }
        
        print(f"\n🖥️ System Information:")
        for key, value in system_info.items():
            print(f"   {key}: {value}")
        
        self.results['system_info'] = system_info

    async def _debug_execution_environment(self):
        """⚙️ Debug execution environment"""
        print("\n⚙️ Execution Environment Analysis")
        print("-" * 30)
        
        # Check execution mode
        execution_mode = os.getenv('EXECUTION_MODE', 'simulation')
        require_confirmation = os.getenv('REQUIRE_REAL_MODE_CONFIRMATION', 'true')
        
        print(f"🔧 Execution Mode: {execution_mode}")
        print(f"🔐 Require Confirmation: {require_confirmation}")
        
        # Check environment variables
        env_vars = {
            'EXECUTION_MODE': execution_mode,
            'REQUIRE_REAL_MODE_CONFIRMATION': require_confirmation,
            'PATH': len(os.getenv('PATH', '').split(os.pathsep)),
            'PYTHONPATH': os.getenv('PYTHONPATH', 'Not set')
        }
        
        # Check if WSL is available
        wsl_available = False
        if platform.system() == 'Windows':
            try:
                result = subprocess.run(['wsl', '--status'], 
                                      capture_output=True, text=True, timeout=5)
                wsl_available = result.returncode == 0
            except Exception:
                wsl_available = False
        
        # Check Docker availability
        docker_available = False
        try:
            result = subprocess.run(['docker', '--version'], 
                                  capture_output=True, text=True, timeout=5)
            docker_available = result.returncode == 0
        except Exception:
            docker_available = False
        
        environment_analysis = {
            'execution_mode': execution_mode,
            'require_confirmation': require_confirmation,
            'wsl_available': wsl_available,
            'docker_available': docker_available,
            'environment_vars': env_vars
        }
        
        print(f"🐧 WSL Available: {'✅' if wsl_available else '❌'}")
        print(f"🐳 Docker Available: {'✅' if docker_available else '❌'}")
        
        if execution_mode == 'real':
            print("⚠️ REAL MODE ACTIVE - Tools will execute actual security scans")
        else:
            print("🎓 SIMULATION MODE - Educational mode active")
        
        self.results['execution_environment'] = environment_analysis

    async def _debug_wsl_integration(self):
        """🐧 Debug WSL integration"""
        print("\n🐧 WSL Integration Analysis")
        print("-" * 30)
        
        # Check if running in WSL
        in_wsl = os.path.exists('/proc/version')
        if in_wsl:
            try:
                with open('/proc/version', 'r') as f:
                    proc_version = f.read()
                in_wsl = 'microsoft' in proc_version.lower()
            except:
                in_wsl = False
        
        print(f"🐧 Running in WSL: {'✅' if in_wsl else '❌'}")
        
        wsl_analysis = {'running_in_wsl': in_wsl}
        
        if in_wsl:
            # Check WSL-specific tools
            wsl_tools = ['apt', 'apt-get', 'dpkg', 'grep', 'awk', 'sed']
            
            wsl_tool_status = {}
            for tool in wsl_tools:
                try:
                    result = subprocess.run(['which', tool], 
                                          capture_output=True, text=True, timeout=5)
                    available = result.returncode == 0
                    print(f"   {'✅' if available else '❌'} {tool}")
                    wsl_tool_status[tool] = available
                except:
                    wsl_tool_status[tool] = False
            
            wsl_analysis['wsl_tools'] = wsl_tool_status
        
        self.results['wsl_integration'] = wsl_analysis

    async def _debug_unified_tool_manager(self):
        """🎯 Debug unified tool manager"""
        print("\n🎯 Unified Tool Manager Analysis")
        print("-" * 30)
        
        try:
            # Check tool manager via API
            response = requests.get(f"{self.backend_url}/api/tools/status", timeout=10)
            
            if response.status_code == 200:
                tools_status = response.json()
                print(f"✅ Tool Manager Status: {tools_status.get('status', 'Unknown')}")
                
                # Analyze tool categories
                tools = tools_status.get('tools', {})
                categories = {}
                
                for tool_name, tool_info in tools.items():
                    if isinstance(tool_info, dict):
                        category = tool_info.get('category', 'unknown')
                        if category not in categories:
                            categories[category] = []
                        categories[category].append(tool_name)
                
                print(f"📊 Tool Categories:")
                for category, tool_list in categories.items():
                    print(f"   {category}: {len(tool_list)} tools")
                
                self.results['unified_tool_manager'] = {
                    'status': 'healthy',
                    'total_tools': len(tools),
                    'categories': categories,
                    'execution_mode': tools_status.get('execution_mode', 'unknown')
                }
                
            else:
                print(f"❌ Tool Manager API returned: {response.status_code}")
                self.results['unified_tool_manager'] = {
                    'status': 'api_error',
                    'error': f"HTTP {response.status_code}"
                }
                
        except requests.exceptions.ConnectionError:
            print("❌ Tool Manager API not accessible - check if backend is running")
            self.results['unified_tool_manager'] = {
                'status': 'not_accessible',
                'error': 'Backend not running'
            }
        except Exception as e:
            print(f"❌ Tool Manager error: {str(e)}")
            self.results['unified_tool_manager'] = {
                'status': 'error',
                'error': str(e)
            }

    async def _debug_custom_tools(self):
        """🛠️ Debug custom security tools"""
        print("\n🛠️ Custom Security Tools Analysis")
        print("-" * 30)
        
        # Check custom tools directory
        custom_tools_dir = self.project_root / 'src/security/tools/custom'
        
        if custom_tools_dir.exists():
            # Analyze custom tool categories
            categories = {
                'ai_analyzers': 'AI-powered analysis tools',
                'compliance': 'Compliance testing tools', 
                'intelligence': 'Threat intelligence tools',
                'scanners': 'Custom scanners',
                'specialized': 'Specialized security tools',
                'utilities': 'Utility tools'
            }
            
            custom_tool_analysis = {}
            
            for category, description in categories.items():
                category_path = custom_tools_dir / category
                if category_path.exists():
                    py_files = list(category_path.glob('*.py'))
                    init_files = list(category_path.glob('__init__.py'))
                    
                    tool_count = len(py_files) - len(init_files)  # Exclude __init__.py
                    print(f"✅ {category:.<20} {tool_count} tools")
                    
                    custom_tool_analysis[category] = {
                        'exists': True,
                        'tool_count': tool_count,
                        'description': description
                    }
                else:
                    print(f"❌ {category:.<20} Missing")
                    custom_tool_analysis[category] = {
                        'exists': False,
                        'description': description
                    }
            
            # Check for specific advanced tools
            advanced_tools = {
                'automated_compliance_tester.py': 'Automated compliance testing',
                'ai_reconnaissance_engine.py': 'AI-powered reconnaissance',
                'mitre_attack_mapper.py': 'MITRE ATT&CK mapping',
                'universal_parser.py': 'Universal output parser'
            }
            
            print(f"\n🔬 Advanced Tools:")
            for tool_file, description in advanced_tools.items():
                tool_path = custom_tools_dir / 'compliance' / tool_file
                if not tool_path.exists():
                    tool_path = custom_tools_dir / 'intelligence' / tool_file
                if not tool_path.exists():
                    tool_path = custom_tools_dir / 'utilities' / tool_file
                
                if tool_path.exists():
                    print(f"   ✅ {tool_file}")
                else:
                    print(f"   ❌ {tool_file}")
            
            self.results['custom_tools'] = custom_tool_analysis
        else:
            print("❌ Custom tools directory not found")
            self.results['custom_tools'] = {'error': 'directory not found'}

    async def _debug_ai_security_integration(self):
        """🤖 Debug AI-enhanced security capabilities"""
        print("\n🤖 AI Security Integration Analysis")
        print("-" * 30)
        
        # Check AI security modules
        ai_security_dir = self.project_root / 'src/ai'
        
        if ai_security_dir.exists():
            ai_modules = {
                'creative_exploit_engine.py': 'Creative exploit generation',
                'adaptive_exploit_modifier.py': 'Adaptive exploit modification',
                'evasion_technique_generator.py': 'Evasion technique generation',
                'behavioral_analysis_engine.py': 'Behavioral analysis',
                'multi_stage_orchestrator.py': 'Multi-stage attack orchestration',
                'vulnerability_agent.py': 'AI vulnerability agent'
            }
            
            ai_analysis = {}
            
            for module_file, description in ai_modules.items():
                module_path = ai_security_dir / module_file
                if module_path.exists():
                    print(f"✅ {module_file:.<35} {description}")
                    ai_analysis[module_file] = {
                        'exists': True,
                        'description': description
                    }
                else:
                    print(f"❌ {module_file:.<35} Missing")
                    ai_analysis[module_file] = {
                        'exists': False,
                        'description': description
                    }
            
            self.results['ai_security'] = ai_analysis
        else:
            print("❌ AI security directory not found")
            self.results['ai_security'] = {'error': 'directory not found'}

    async def _debug_exploit_generation(self):
        """💥 Debug exploit generation capabilities"""
        print("\n💥 Exploit Generation Analysis")
        print("-" * 30)
        
        # This would require testing actual AI services
        # For now, check configuration and availability
        
        api_keys = {
            'OpenAI': os.getenv('OPENAI_API_KEY'),
            'DeepSeek': os.getenv('DEEPSEEK_API_KEY'),
            'Anthropic': os.getenv('ANTHROPIC_API_KEY')
        }
        
        exploit_capabilities = {}
        
        for service, key in api_keys.items():
            if key and len(key) > 20:
                print(f"✅ {service} API configured for exploit generation")
                exploit_capabilities[service] = 'configured'
            else:
                print(f"⚠️ {service} API not configured")
                exploit_capabilities[service] = 'not_configured'
        
        # Check if in real mode for actual exploit generation
        if self.execution_mode == 'real':
            print("✅ Real mode enabled - exploit generation capabilities active")
            exploit_capabilities['mode'] = 'real'
        else:
            print("🎓 Simulation mode - exploit generation in educational mode")
            exploit_capabilities['mode'] = 'simulation'
        
        self.results['exploit_generation'] = exploit_capabilities

    async def _debug_web_security_tools(self):
        """🌐 Debug web application security tools"""
        print("\n🌐 Web Security Tools Analysis")
        print("-" * 30)
        
        web_tools = ['sqlmap', 'gobuster', 'nikto', 'dirb', 'ffuf', 'wpscan']
        
        web_tool_status = {}
        for tool in web_tools:
            if tool in self.results.get('core_tools', {}):
                status = self.results['core_tools'][tool]['status']
                web_tool_status[tool] = status
                print(f"{'✅' if status == 'available' else '❌'} {tool}")
            else:
                web_tool_status[tool] = 'unknown'
                print(f"❓ {tool}")
        
        available_count = len([t for t in web_tool_status.values() if t == 'available'])
        total_count = len(web_tool_status)
        
        print(f"\n📊 Web Tools Available: {available_count}/{total_count}")
        
        self.results['web_security_tools'] = {
            'tools': web_tool_status,
            'availability_percentage': (available_count / total_count) * 100
        }

    async def _debug_network_security_tools(self):
        """🌐 Debug network security tools"""
        print("\n🌐 Network Security Tools Analysis")
        print("-" * 30)
        
        network_tools = ['nmap', 'masscan', 'netcat', 'ping', 'traceroute', 'whois']
        
        network_tool_status = {}
        for tool in network_tools:
            try:
                result = subprocess.run([tool, '--version'], 
                                      capture_output=True, text=True, timeout=5)
                if result.returncode == 0:
                    network_tool_status[tool] = 'available'
                    print(f"✅ {tool}")
                else:
                    # Some tools don't support --version, try alternative
                    result = subprocess.run([tool, '-h'], 
                                          capture_output=True, text=True, timeout=5)
                    if result.returncode == 0 or 'usage' in result.stderr.lower():
                        network_tool_status[tool] = 'available'
                        print(f"✅ {tool}")
                    else:
                        network_tool_status[tool] = 'not_responding'
                        print(f"❌ {tool}")
            except FileNotFoundError:
                network_tool_status[tool] = 'not_installed'
                print(f"⚠️ {tool} (not installed)")
            except Exception:
                network_tool_status[tool] = 'error'
                print(f"❌ {tool} (error)")
        
        self.results['network_security_tools'] = network_tool_status

    async def _debug_compliance_tools(self):
        """📋 Debug compliance testing tools"""
        print("\n📋 Compliance Testing Analysis")
        print("-" * 30)
        
        # Check for compliance testing framework
        compliance_dir = self.project_root / 'src/security/tools/custom/compliance'
        
        if compliance_dir.exists():
            compliance_files = list(compliance_dir.glob('*.py'))
            print(f"✅ Compliance tools directory: {len(compliance_files)} tools")
            
            # Check for specific compliance frameworks
            frameworks = {
                'SOC2': 'SOC 2 compliance testing',
                'PCI-DSS': 'PCI DSS compliance testing',
                'HIPAA': 'HIPAA compliance testing',
                'GDPR': 'GDPR compliance testing',
                'ISO27001': 'ISO 27001 compliance testing'
            }
            
            compliance_analysis = {}
            
            for framework, description in frameworks.items():
                # Check if framework is mentioned in any compliance files
                framework_found = False
                for file_path in compliance_files:
                    try:
                        with open(file_path, 'r') as f:
                            content = f.read()
                            if framework.lower() in content.lower():
                                framework_found = True
                                break
                    except:
                        continue
                
                compliance_analysis[framework] = {
                    'supported': framework_found,
                    'description': description
                }
                
                print(f"{'✅' if framework_found else '⚠️'} {framework}: {description}")
            
            self.results['compliance_tools'] = compliance_analysis
        else:
            print("❌ Compliance tools directory not found")
            self.results['compliance_tools'] = {'error': 'directory not found'}

    async def _debug_tool_orchestration(self):
        """🎭 Debug tool orchestration"""
        print("\n🎭 Tool Orchestration Analysis")
        print("-" * 30)
        
        # Check orchestration components
        orchestration_files = {
            'unified_tool_manager.py': 'Unified tool management',
            'master_orchestrator.py': 'Master orchestration',
            'hybrid_execution_engine.py': 'Hybrid execution engine',
            'wsl_tool_runner.py': 'WSL tool integration'
        }
        
        security_dir = self.project_root / 'src/security'
        tools_dir = security_dir / 'tools'
        
        orchestration_analysis = {}
        
        for file_name, description in orchestration_files.items():
            file_path = security_dir / file_name
            if not file_path.exists():
                file_path = tools_dir / file_name
            
            if file_path.exists():
                print(f"✅ {file_name:.<30} {description}")
                orchestration_analysis[file_name] = {
                    'exists': True,
                    'description': description
                }
            else:
                print(f"❌ {file_name:.<30} Missing")
                orchestration_analysis[file_name] = {
                    'exists': False,
                    'description': description
                }
        
        self.results['tool_orchestration'] = orchestration_analysis

    async def _debug_performance_optimization(self):
        """⚡ Debug performance optimization"""
        print("\n⚡ Performance Optimization Analysis")
        print("-" * 30)
        
        # Check performance-related components
        performance_files = {
            'performance_optimizer.py': 'Performance optimization',
            'cache_strategies.py': 'Caching strategies',
            'resource_optimizer.py': 'Resource optimization',
            'performance_monitor.py': 'Performance monitoring'
        }
        
        security_dir = self.project_root / 'src/security'
        tools_dir = security_dir / 'tools'
        
        performance_analysis = {}
        
        for file_name, description in performance_files.items():
            file_path = security_dir / file_name
            if not file_path.exists():
                file_path = tools_dir / file_name
            
            if file_path.exists():
                print(f"✅ {file_name:.<30} {description}")
                performance_analysis[file_name] = {
                    'exists': True,
                    'description': description
                }
            else:
                print(f"❌ {file_name:.<30} Missing")
                performance_analysis[file_name] = {
                    'exists': False,
                    'description': description
                }
        
        self.results['performance_optimization'] = performance_analysis

    def _generate_security_report(self):
        """Generate comprehensive security tools report"""
        print("\n" + "="*50)
        print("🛡️ SECURITY TOOLS DEBUGGING REPORT")
        print("="*50)
        
        # Calculate overall security readiness
        core_tools = self.results.get('core_tools', {})
        available_tools = len([t for t in core_tools.values() if t.get('status') == 'available'])
        total_tools = len(core_tools)
        
        readiness_score = (available_tools / total_tools * 100) if total_tools > 0 else 0
        
        print(f"\n🛡️ Security Readiness Score: {readiness_score:.1f}%")
        print(f"🔧 Available Tools: {available_tools}/{total_tools}")
        print(f"⚙️ Execution Mode: {self.execution_mode.upper()}")
        
        # Identify critical missing tools
        missing_critical = []
        critical_tools = ['nmap', 'nuclei', 'sqlmap']
        
        for tool in critical_tools:
            if tool in core_tools and core_tools[tool].get('status') != 'available':
                missing_critical.append(tool)
        
        if missing_critical:
            print(f"\n⚠️ Critical tools missing: {', '.join(missing_critical)}")
            print("🔧 Recommended actions:")
            for tool in missing_critical:
                print(f"  - Install {tool}")
        
        # Check execution mode warnings
        if self.execution_mode == 'real':
            print(f"\n🚨 REAL MODE ACTIVE")
            print("   Security tools will execute actual scans")
            print("   Ensure you have authorization for target systems")
        else:
            print(f"\n🎓 SIMULATION MODE ACTIVE")
            print("   Tools running in educational mode")
            print("   Set EXECUTION_MODE=real for actual scanning")
        
        # Save detailed report
        report_file = self.project_root / f"security_tools_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w') as f:
            json.dump(self.results, f, indent=2, default=str)
        
        print(f"\n📋 Detailed report saved to: {report_file}")
        
        return self.results

async def main():
    """Main entry point"""
    debugger = SecurityToolsDebugger()
    results = await debugger.debug_all_security_tools()
    return results

if __name__ == "__main__":
    asyncio.run(main())