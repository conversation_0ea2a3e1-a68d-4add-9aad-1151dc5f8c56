/**
 * Vulnerability Assessment Page - Comprehensive vulnerability scanning
 */
import React from 'react';
import { Routes, Route, useNavigate } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import {
  AlertTriangle,
  Shield,
  Search,
  Target,
  CheckCircle,
  Play,
  Settings
} from 'lucide-react';

// Import tool components
import NucleiScanner from '@/components/tools/vulnerability-assessment/NucleiScanner';
import SQLMapScanner from '@/components/tools/vulnerability-assessment/SQLMapScanner';

function VulnerabilityAssessmentOverview() {
  const navigate = useNavigate();

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Vulnerability Assessment</h1>
          <p className="text-muted-foreground">Comprehensive vulnerability scanning and analysis</p>
        </div>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <div className="flex items-center gap-3">
              <AlertTriangle className="h-5 w-5" />
              <CardTitle>Nuclei Scanner</CardTitle>
            </div>
            <CardDescription>Fast vulnerability scanner with custom templates</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <Input placeholder="Target URL or IP" />
              <div className="flex gap-2">
                <Button onClick={() => {
                  console.log('🚀 Scan button clicked - navigating to Nuclei');
                  navigate('nuclei');
                }}>
                  <Play className="mr-2 h-4 w-4" />Scan
                </Button>
                <Button
                  variant="outline"
                  onClick={() => {
                    console.log('🔧 Configure button clicked - navigating to Nuclei');
                    navigate('nuclei');
                  }}
                >
                  <Settings className="mr-2 h-4 w-4" />Configure
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <div className="flex items-center gap-3">
              <Search className="h-5 w-5" />
              <CardTitle>Custom Vulnerability Scan</CardTitle>
            </div>
            <CardDescription>Targeted vulnerability assessment</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <Input placeholder="Target system" />
              <div className="flex gap-2">
                <Button onClick={() => navigate('/tools/vulnerability-assessment/nuclei')}>
                  <Play className="mr-2 h-4 w-4" />Start Assessment
                </Button>
                <Button
                  variant="outline"
                  onClick={() => navigate('/tools/vulnerability-assessment/nuclei')}
                >
                  <Settings className="mr-2 h-4 w-4" />Configure
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

export function VulnerabilityAssessmentPage() {
  return (
    <Routes>
      <Route index element={<VulnerabilityAssessmentOverview />} />
      <Route path="nuclei/*" element={<NucleiScanner />} />
      <Route path="sqlmap/*" element={<SQLMapScanner />} />
    </Routes>
  );
}