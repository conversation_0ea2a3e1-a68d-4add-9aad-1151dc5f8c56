/**
 * Status Debug Test - Test WSL and Backend Status Display
 * 
 * This script tests the status bar fixes for WSL and backend tool counts.
 * Run this in the browser console when the app is running.
 */

console.log('🔍 Starting Status Debug Test...');

// Test 1: Check if electronAPI is available
if (window.electronAPI) {
  console.log('✅ Electron API is available');
  
  // Test 2: Check WSL status
  window.electronAPI.wsl.getStatus()
    .then(status => {
      console.log('🐧 WSL Status from Electron:', status);
    })
    .catch(error => {
      console.error('❌ WSL Status Error:', error);
    });
  
  // Test 3: Check backend tools
  window.electronAPI.backend.getAvailableTools()
    .then(tools => {
      console.log(`🔧 Backend Tools Count: ${tools.length}`);
      console.log('🔧 First 5 tools:', tools.slice(0, 5));
    })
    .catch(error => {
      console.error('❌ Backend Tools Error:', error);
    });
  
  // Test 4: Check backend status
  window.electronAPI.backend.getStatus()
    .then(status => {
      console.log('📡 Backend Status from Electron:', status);
    })
    .catch(error => {
      console.error('❌ Backend Status Error:', error);
    });
    
  console.log('📊 Test commands sent - check console for results');
} else {
  console.log('❌ Electron API not available - running in browser mode');
}

// Test 5: Check Zustand stores
if (window.nexusScan) {
  console.log('🏪 Checking Zustand stores via nexusScan helper...');
  
  window.nexusScan.getBackendStatus()
    .then(status => {
      console.log('📡 Backend Status via Helper:', status);
    })
    .catch(error => {
      console.error('❌ Backend Status Helper Error:', error);
    });
    
  window.nexusScan.getWSLStatus()
    .then(status => {
      console.log('🐧 WSL Status via Helper:', status);
    })
    .catch(error => {
      console.error('❌ WSL Status Helper Error:', error);
    });
    
  window.nexusScan.getAvailableTools()
    .then(tools => {
      console.log(`🔧 Tools via Helper: ${tools.length} tools`);
    })
    .catch(error => {
      console.error('❌ Tools Helper Error:', error);
    });
} else {
  console.log('⚠️ nexusScan helper not available - not in development mode');
}

console.log('✅ Status Debug Test Complete - check above for results');