"""
Nmap Scanner Integration for NexusScan Desktop Application
Network discovery and port scanning using python-nmap.
"""

import nmap
import json
import asyncio
import logging
import subprocess
from datetime import datetime
from typing import Dict, List, Optional, Callable, Any
from dataclasses import dataclass

from security.tools.tool_registry import (
    ToolMetadata, ToolCategory, ToolCapabilities,
    ScanOptions, ScanResult, ToolStatus, register_tool
)
from security.tools.sudo_executor import get_sudo_executor, requires_sudo
from security.tools.base_scanner import BaseScanner

logger = logging.getLogger(__name__)


@dataclass
class NmapScanOptions(ScanOptions):
    """Nmap-specific scan options"""
    scan_type: str = "-sT"  # TCP connect scan (doesn't require root)
    port_range: str = "80,443,8080,8443"  # Common web ports for testing
    service_detection: bool = True
    os_detection: bool = False
    script_scan: bool = False
    aggressive_scan: bool = False
    stealth_scan: bool = False
    timing_template: str = "T4"  # T0-T5 (paranoid to insane)
    custom_scripts: List[str] = None
    exclude_ports: str = ""
    fragment_packets: bool = False
    decoy_scan: bool = False
    source_port: Optional[int] = None

    def __post_init__(self):
        super().__post_init__()
        if self.custom_scripts is None:
            self.custom_scripts = []


@dataclass
class NmapHost:
    """Nmap host information"""
    ip: str
    hostname: str = ""
    status: str = "unknown"
    reason: str = ""
    open_ports: List[Dict[str, Any]] = None
    os_matches: List[Dict[str, Any]] = None
    scripts_results: Dict[str, str] = None

    def __post_init__(self):
        if self.open_ports is None:
            self.open_ports = []
        if self.os_matches is None:
            self.os_matches = []
        if self.scripts_results is None:
            self.scripts_results = {}


@dataclass
class NmapPortInfo:
    """Nmap port information"""
    port: int
    protocol: str
    state: str
    reason: str
    service: str = ""
    version: str = ""
    product: str = ""
    extra_info: str = ""
    confidence: int = 0
    scripts: Dict[str, str] = None

    def __post_init__(self):
        if self.scripts is None:
            self.scripts = {}


@register_tool
class NmapScanner(BaseScanner):
    """Nmap network scanner implementation"""

    def __init__(self):
        """Initialize Nmap scanner"""
        self.nm = None
        super().__init__()

    def get_metadata(self) -> ToolMetadata:
        """Get Nmap tool metadata"""
        return ToolMetadata(
            name="nmap",
            display_name="Nmap Network Scanner",
            description="Network discovery and security auditing tool for port scanning and service detection",
            version="1.0.0",
            category=ToolCategory.NETWORK_SCANNER,
            author="NexusScan Team",
            website="https://nmap.org",
            documentation="https://nmap.org/docs.html",
            capabilities=ToolCapabilities(
                supports_async=True,
                supports_progress=True,
                supports_cancellation=False,
                requires_root=False,  # Depends on scan type
                network_access_required=True,
                output_formats=["json", "xml", "txt"],
                supported_targets=["ip", "domain", "cidr"]
            ),
            default_options={
                "scan_type": "-sS",
                "port_range": "1-1000",
                "timing_template": "T4"
            },
            required_dependencies=["python-nmap", "nmap"]
        )

    def check_native_availability(self) -> bool:
        """Check if native Nmap is available"""
        try:
            self.nm = nmap.PortScanner()
            # Test basic functionality
            self.nm.nmap_version()
            return True
        except Exception as e:
            logger.error(f"Native Nmap not available: {e}")
            return False

    async def execute_native_scan(self, options: ScanOptions,
                                  progress_callback: Optional[Callable[[float, str], None]] = None) -> ScanResult:
        """Execute native Nmap scan"""
        if not isinstance(options, NmapScanOptions):
            # Convert generic options to Nmap options
            nmap_options = NmapScanOptions(
                target=options.target,
                timeout=options.timeout,
                threads=options.threads,
                output_format=options.output_format
            )
        else:
            nmap_options = options

        start_time = datetime.now()
        
        if progress_callback:
            progress_callback(0.0, "Initializing Nmap scan...")

        try:
            # Build Nmap arguments
            nmap_args = self._build_nmap_arguments(nmap_options)
            
            if progress_callback:
                progress_callback(0.1, "Starting network scan...")

            # Execute scan in thread pool to avoid blocking
            try:
                loop = asyncio.get_running_loop()
            except RuntimeError:
                # No running loop, create one
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
            
            scan_data = await loop.run_in_executor(
                None, self._execute_nmap_scan, nmap_options.target, 
                nmap_options.port_range, nmap_args, progress_callback
            )

            if progress_callback:
                progress_callback(0.8, "Parsing scan results...")

            # Parse results
            parsed_results = self._parse_scan_results(scan_data)
            
            if progress_callback:
                progress_callback(0.9, "Extracting vulnerabilities...")

            # Extract vulnerabilities
            vulnerabilities = self._extract_vulnerabilities(parsed_results)

            if progress_callback:
                progress_callback(1.0, "Scan completed")

            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()

            return ScanResult(
                tool_name="nmap",
                target=nmap_options.target,
                status=ToolStatus.COMPLETED,
                start_time=start_time.isoformat(),
                end_time=end_time.isoformat(),
                duration_seconds=duration,
                raw_output=json.dumps(scan_data, indent=2),
                parsed_results=parsed_results,
                vulnerabilities=vulnerabilities,
                metadata={
                    "nmap_version": self.nm.nmap_version(),
                    "scan_arguments": nmap_args,
                    "hosts_scanned": len(parsed_results.get("hosts", []))
                }
            )

        except Exception as e:
            logger.error(f"Nmap scan failed: {e}")
            return ScanResult(
                tool_name="nmap",
                target=nmap_options.target,
                status=ToolStatus.FAILED,
                start_time=start_time.isoformat(),
                errors=[str(e)]
            )

    def _build_nmap_arguments(self, options: NmapScanOptions) -> str:
        """Build Nmap command arguments"""
        args = []

        # Scan type
        args.append(options.scan_type)

        # Timing template
        args.append(f"-{options.timing_template}")

        # Service detection
        if options.service_detection:
            args.append("-sV")

        # OS detection
        if options.os_detection:
            args.append("-O")

        # Script scanning
        if options.script_scan:
            if options.custom_scripts:
                args.append(f"--script={','.join(options.custom_scripts)}")
            else:
                args.append("-sC")

        # Aggressive scan
        if options.aggressive_scan:
            args.append("-A")

        # Stealth options
        if options.stealth_scan:
            args.append("-f")  # Fragment packets
            args.append("-D RND:10")  # Decoy scan

        # Fragment packets
        if options.fragment_packets:
            args.append("-f")

        # Source port
        if options.source_port:
            args.append(f"--source-port {options.source_port}")

        # Exclude ports
        if options.exclude_ports:
            args.append(f"--exclude-ports {options.exclude_ports}")

        return " ".join(args)

    def _execute_nmap_scan(self, target: str, port_range: str, args: str,
                          progress_callback: Optional[Callable[[float, str], None]] = None) -> Dict:
        """Execute the actual Nmap scan"""
        try:
            if progress_callback:
                progress_callback(0.2, f"Scanning {target}...")

            # Check if sudo is required for the scan
            scan_options = {'scan_type': args.split()[0] if args else '-sT'}
            needs_sudo = requires_sudo('nmap', scan_options)
            
            if needs_sudo:
                logger.info("Nmap scan requires sudo privileges - executing with sudo")
                return self._execute_nmap_with_sudo(target, port_range, args, progress_callback)
            else:
                logger.info("Executing Nmap scan without sudo")
                # Execute scan normally
                self.nm.scan(target, port_range, args)

            if progress_callback:
                progress_callback(0.7, "Scan execution completed")

            # Convert to serializable format
            scan_data = {}
            for host in self.nm.all_hosts():
                scan_data[host] = dict(self.nm[host])

            return scan_data

        except Exception as e:
            logger.error(f"Nmap execution failed: {e}")
            # If scan failed and it was a privileged scan, try with sudo
            if "requires root privileges" in str(e).lower():
                logger.info("Retrying Nmap scan with sudo privileges")
                return self._execute_nmap_with_sudo(target, port_range, args, progress_callback)
            raise
    
    def _execute_nmap_with_sudo(self, target: str, port_range: str, args: str,
                               progress_callback: Optional[Callable[[float, str], None]] = None) -> Dict:
        """Execute Nmap scan with sudo privileges"""
        try:
            sudo_executor = get_sudo_executor()
            
            # Build nmap command
            nmap_cmd = ['nmap']
            if args:
                nmap_cmd.extend(args.split())
            nmap_cmd.extend(['-p', port_range, target])
            
            if progress_callback:
                progress_callback(0.3, "Executing privileged scan...")
            
            # Execute with sudo
            result = sudo_executor.execute_with_sudo(nmap_cmd, timeout=300)
            
            if result.returncode != 0:
                raise Exception(f"Nmap sudo execution failed: {result.stderr}")
            
            if progress_callback:
                progress_callback(0.6, "Parsing sudo scan results...")
            
            # Parse XML output if available, otherwise parse text output
            return self._parse_nmap_output(result.stdout, target)
            
        except Exception as e:
            logger.error(f"Nmap sudo execution failed: {e}")
            raise
    
    def _parse_nmap_output(self, output: str, target: str) -> Dict:
        """Parse nmap text output into structured format"""
        scan_data = {}
        current_host = None
        
        lines = output.split('\n')
        for line in lines:
            line = line.strip()
            
            # Parse host information
            if 'Nmap scan report for' in line:
                if '(' in line and ')' in line:
                    # Extract IP from "hostname (IP)"
                    current_host = line.split('(')[1].split(')')[0]
                else:
                    # Extract IP directly
                    current_host = line.split()[-1]
                
                scan_data[current_host] = {
                    'status': {'state': 'up', 'reason': 'detected'},
                    'tcp': {},
                    'udp': {},
                    'hostnames': []
                }
            
            # Parse port information
            elif '/tcp' in line or '/udp' in line:
                if current_host:
                    parts = line.split()
                    if len(parts) >= 3:
                        port_info = parts[0]
                        state = parts[1]
                        service = parts[2] if len(parts) > 2 else 'unknown'
                        
                        port_num = int(port_info.split('/')[0])
                        protocol = port_info.split('/')[1]
                        
                        scan_data[current_host][protocol][port_num] = {
                            'state': state,
                            'reason': 'syn-ack' if state == 'open' else 'reset',
                            'name': service,
                            'product': '',
                            'version': '',
                            'extrainfo': '',
                            'conf': 10
                        }
        
        return scan_data

    def _parse_scan_results(self, scan_data: Dict) -> Dict[str, Any]:
        """Parse Nmap scan results into frontend-ready structured format"""
        hosts = []
        services_matrix = []
        network_topology = {"nodes": [], "edges": []}
        
        for ip, host_data in scan_data.items():
            host_info = self._parse_host_data(ip, host_data)
            hosts.append(host_info)
            
            # Build network topology data for frontend visualization
            node = {
                "id": host_info.get("ip", "unknown"),
                "ip": host_info.get("ip", "unknown"),
                "hostname": host_info.get("hostname", ""),
                "os": self._get_primary_os(host_info.get("os_matches", [])),
                "status": host_info.get("status", "unknown"),
                "port_count": len(host_info.get("open_ports", [])),
                "services": [p.get("service", "") for p in host_info.get("open_ports", [])],
                "risk_level": self._calculate_risk_level(host_info),
                "service_summary": self._create_service_summary(host_info.get("open_ports", []))
            }
            network_topology["nodes"].append(node)
            
            # Build services matrix for frontend table display
            services_row = {
                "host": host_info.get("ip", "unknown"),
                "hostname": host_info.get("hostname", ""),
                "services": self._extract_services_for_matrix(host_info),
                "os": self._get_primary_os(host_info.get("os_matches", [])),
                "status": host_info.get("status", "unknown"),
                "port_summary": self._create_port_summary(host_info.get("open_ports", [])),
                "risk_indicators": self._get_risk_indicators(host_info)
            }
            services_matrix.append(services_row)
        
        # Create scan statistics
        scan_stats = self._create_scan_statistics(hosts)
        
        # Frontend-ready data structure matching tools-based-frontend.md specs
        return {
            "scan_info": {
                "scanner": "nmap",
                "targets_scanned": len(scan_data),
                "scan_type": "network_discovery",
                "timestamp": datetime.now().isoformat(),
                "scan_profiles": ["Quick", "Comprehensive", "Stealth", "Aggressive"],
                "advanced_options": self._get_available_scan_options()
            },
            "hosts": hosts,
            "total_hosts": len(hosts),
            
            # Frontend visualization data (matching tools-based-frontend.md interface)
            "network_topology": network_topology,
            "services_matrix": services_matrix,
            "host_list": self._create_frontend_host_list(hosts),
            
            # Export options data
            "export_formats": {
                "json": self._prepare_json_export(hosts),
                "xml": self._prepare_xml_export(hosts),
                "csv": self._prepare_csv_export(hosts),
                "pdf_data": self._prepare_pdf_export_data(hosts)
            },
            
            # Real-time progress data
            "progress_data": {
                "hosts_completed": len(hosts),
                "total_estimated": len(scan_data),
                "percentage": 100,  # Scan is complete at this point
                "current_phase": "Analysis Complete",
                "estimated_time_remaining": "0s"
            },
            
            # Security analysis for frontend
            "security_summary": self._create_security_summary(hosts),
            "vulnerability_indicators": self._identify_vulnerability_indicators(hosts),
            "scan_statistics": scan_stats,
            
            # Comparison data for frontend diff functionality
            "comparison_baseline": self._create_comparison_baseline(hosts)
        }

    def _parse_host_data(self, ip: str, host_data: Dict) -> Dict[str, Any]:
        """Parse individual host data"""
        host = {
            "ip": ip,
            "hostname": "",
            "status": host_data.get("status", {}).get("state", "unknown"),
            "reason": host_data.get("status", {}).get("reason", ""),
            "open_ports": [],
            "services": [],
            "os_matches": []
        }

        # Extract hostnames
        hostnames = host_data.get("hostnames", [])
        if hostnames:
            host["hostname"] = hostnames[0].get("name", "")

        # Extract open ports and services
        if "tcp" in host_data:
            for port, port_data in host_data["tcp"].items():
                if port_data["state"] == "open":
                    port_info = {
                        "port": port,
                        "protocol": "tcp",
                        "state": port_data["state"],
                        "reason": port_data.get("reason", ""),
                        "service": port_data.get("name", "unknown"),
                        "version": port_data.get("version", ""),
                        "product": port_data.get("product", ""),
                        "extra_info": port_data.get("extrainfo", ""),
                        "confidence": port_data.get("conf", 0)
                    }
                    host["open_ports"].append(port_info)
                    host["services"].append(port_info)

        # Extract UDP ports if present
        if "udp" in host_data:
            for port, port_data in host_data["udp"].items():
                if port_data["state"] in ["open", "open|filtered"]:
                    port_info = {
                        "port": port,
                        "protocol": "udp",
                        "state": port_data["state"],
                        "reason": port_data.get("reason", ""),
                        "service": port_data.get("name", "unknown"),
                        "version": port_data.get("version", ""),
                        "product": port_data.get("product", ""),
                        "extra_info": port_data.get("extrainfo", ""),
                        "confidence": port_data.get("conf", 0)
                    }
                    host["open_ports"].append(port_info)
                    host["services"].append(port_info)

        # Extract OS information
        if "osmatch" in host_data:
            for os_match in host_data["osmatch"]:
                host["os_matches"].append({
                    "name": os_match.get("name", ""),
                    "accuracy": os_match.get("accuracy", 0),
                    "line": os_match.get("line", 0)
                })

        return host

    def _extract_vulnerabilities(self, parsed_results: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Extract potential vulnerabilities from scan results"""
        vulnerabilities = []

        for host in parsed_results.get("hosts", []):
            host_ip = host["ip"]
            
            # Check for common vulnerable services
            for service in host.get("services", []):
                vuln = self._check_service_vulnerabilities(host_ip, service)
                if vuln:
                    vulnerabilities.extend(vuln)

        return vulnerabilities

    def _check_service_vulnerabilities(self, host: str, service: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Check for known vulnerabilities in detected services"""
        vulnerabilities = []
        
        service_name = service.get("service", "").lower()
        port = service.get("port")
        version = service.get("version", "")

        # Common vulnerable services detection
        vulnerable_services = {
            "ftp": {
                "ports": [21],
                "severity": "medium",
                "description": "FTP service detected - check for anonymous access and secure configuration"
            },
            "ssh": {
                "ports": [22],
                "severity": "low",
                "description": "SSH service detected - ensure strong authentication and updated version"
            },
            "telnet": {
                "ports": [23],
                "severity": "high", 
                "description": "Telnet service detected - unencrypted protocol, consider SSH alternative"
            },
            "smtp": {
                "ports": [25, 587],
                "severity": "medium",
                "description": "SMTP service detected - check for open relay and secure configuration"
            },
            "http": {
                "ports": [80, 8080],
                "severity": "medium",
                "description": "HTTP service detected - check for HTTPS availability and security headers"
            },
            "https": {
                "ports": [443, 8443],
                "severity": "low",
                "description": "HTTPS service detected - verify SSL/TLS configuration and certificate validity"
            },
            "mysql": {
                "ports": [3306],
                "severity": "high",
                "description": "MySQL database service detected - ensure secure configuration and access controls"
            },
            "rdp": {
                "ports": [3389],
                "severity": "high",
                "description": "RDP service detected - ensure strong authentication and network-level authentication"
            },
            "vnc": {
                "ports": [5900, 5901],
                "severity": "high",
                "description": "VNC service detected - ensure password protection and encryption"
            }
        }

        # Check if service matches vulnerable patterns
        if service_name in vulnerable_services:
            vuln_info = vulnerable_services[service_name]
            if port in vuln_info["ports"]:
                vulnerability = {
                    "id": f"nmap_service_{service_name}_{host}_{port}",
                    "type": "service_detection",
                    "severity": vuln_info["severity"],
                    "title": f"{service_name.upper()} Service Detected",
                    "description": vuln_info["description"],
                    "target": host,
                    "port": port,
                    "service": service_name,
                    "version": version,
                    "evidence": {
                        "service_info": service,
                        "detection_method": "nmap_service_scan"
                    },
                    "remediation": f"Review {service_name} service configuration and security settings",
                    "references": []
                }
                vulnerabilities.append(vulnerability)

        return vulnerabilities

    def get_scan_presets(self) -> Dict[str, NmapScanOptions]:
        """Get predefined scan presets"""
        return {
            "quick_scan": NmapScanOptions(
                target="",
                scan_type="-sS",
                port_range="1-1000",
                timing_template="T4",
                service_detection=True
            ),
            "comprehensive_scan": NmapScanOptions(
                target="",
                scan_type="-sS",
                port_range="1-65535",
                timing_template="T4",
                service_detection=True,
                os_detection=True,
                script_scan=True
            ),
            "stealth_scan": NmapScanOptions(
                target="",
                scan_type="-sS",
                port_range="1-1000",
                timing_template="T2",
                service_detection=False,
                stealth_scan=True,
                fragment_packets=True
            ),
            "aggressive_scan": NmapScanOptions(
                target="",
                scan_type="-sS",
                port_range="1-65535",
                timing_template="T4",
                aggressive_scan=True,
                service_detection=True,
                os_detection=True,
                script_scan=True
            )
        }

    # Frontend Helper Methods for tools-based-frontend.md Interface

    def _get_primary_os(self, os_matches: List[Dict]) -> str:
        """Get primary OS from matches"""
        if not os_matches:
            return "Unknown"
        return os_matches[0].get("name", "Unknown")

    def _calculate_risk_level(self, host_info: Dict) -> str:
        """Calculate risk level for frontend visualization"""
        open_ports = host_info.get("open_ports", [])
        services = [p.get("service", "") for p in open_ports]
        
        high_risk_services = ["ftp", "telnet", "mysql", "rdp", "vnc", "ssh"]
        medium_risk_services = ["http", "smtp", "pop3", "imap"]
        
        high_risk_count = sum(1 for s in services if s.lower() in high_risk_services)
        medium_risk_count = sum(1 for s in services if s.lower() in medium_risk_services)
        
        if high_risk_count >= 2 or len(open_ports) > 10:
            return "High"
        elif high_risk_count >= 1 or medium_risk_count >= 3:
            return "Medium"
        elif len(open_ports) > 0:
            return "Low"
        else:
            return "Minimal"

    def _create_service_summary(self, open_ports: List[Dict]) -> Dict:
        """Create service summary for frontend"""
        services = {}
        for port in open_ports:
            service = port.get("service", "unknown")
            if service not in services:
                services[service] = []
            services[service].append(port.get("port"))
        
        return {
            "total_services": len(services),
            "services_by_type": services,
            "critical_services": [s for s in services.keys() if s.lower() in ["ftp", "telnet", "mysql", "rdp"]]
        }

    def _extract_services_for_matrix(self, host_info: Dict) -> List[Dict]:
        """Extract services for frontend matrix display"""
        services = []
        for port in host_info.get("open_ports", []):
            service = {
                "port": port.get("port"),
                "protocol": port.get("protocol"),
                "service": port.get("service", "unknown"),
                "version": port.get("version", ""),
                "state": port.get("state"),
                "risk": self._assess_service_risk(port.get("service", ""))
            }
            services.append(service)
        return services

    def _assess_service_risk(self, service: str) -> str:
        """Assess individual service risk"""
        high_risk = ["ftp", "telnet", "mysql", "rdp", "vnc"]
        medium_risk = ["http", "smtp", "pop3", "imap", "ssh"]
        
        service_lower = service.lower()
        if service_lower in high_risk:
            return "High"
        elif service_lower in medium_risk:
            return "Medium"
        else:
            return "Low"

    def _create_port_summary(self, open_ports: List[Dict]) -> Dict:
        """Create port summary for frontend"""
        tcp_ports = [p for p in open_ports if p.get("protocol") == "tcp"]
        udp_ports = [p for p in open_ports if p.get("protocol") == "udp"]
        
        return {
            "total": len(open_ports),
            "tcp": len(tcp_ports),
            "udp": len(udp_ports),
            "common_ports": [p.get("port") for p in open_ports if p.get("port") in [21, 22, 23, 25, 53, 80, 110, 143, 443, 993, 995]],
            "high_ports": [p.get("port") for p in open_ports if p.get("port", 0) > 1024]
        }

    def _get_risk_indicators(self, host_info: Dict) -> List[str]:
        """Get risk indicators for frontend display"""
        indicators = []
        open_ports = host_info.get("open_ports", [])
        
        # Check for dangerous services
        dangerous_services = ["ftp", "telnet", "mysql", "rdp", "vnc"]
        for port in open_ports:
            if port.get("service", "").lower() in dangerous_services:
                indicators.append(f"Dangerous service: {port.get('service')} on port {port.get('port')}")
        
        # Check for too many open ports
        if len(open_ports) > 15:
            indicators.append(f"Many open ports detected: {len(open_ports)}")
        
        # Check for unencrypted web services
        http_ports = [p for p in open_ports if p.get("service", "").lower() == "http"]
        if http_ports:
            indicators.append("Unencrypted HTTP service detected")
        
        return indicators

    def _create_frontend_host_list(self, hosts: List[Dict]) -> List[Dict]:
        """Create frontend-ready host list"""
        host_list = []
        for host in hosts:
            host_summary = {
                "ip": host.get("ip"),
                "hostname": host.get("hostname", ""),
                "status": host.get("status"),
                "os": self._get_primary_os(host.get("os_matches", [])),
                "open_ports_count": len(host.get("open_ports", [])),
                "services_summary": ", ".join(list(set([p.get("service", "") for p in host.get("open_ports", [])]))),
                "risk_level": self._calculate_risk_level(host),
                "last_seen": datetime.now().isoformat()
            }
            host_list.append(host_summary)
        return host_list

    def _prepare_json_export(self, hosts: List[Dict]) -> Dict:
        """Prepare JSON export data"""
        return {
            "scan_timestamp": datetime.now().isoformat(),
            "scanner": "nmap",
            "total_hosts": len(hosts),
            "hosts": hosts
        }

    def _prepare_xml_export(self, hosts: List[Dict]) -> str:
        """Prepare XML export data"""
        # This would generate Nmap-compatible XML
        return f"<!-- Nmap XML export - {len(hosts)} hosts scanned -->"

    def _prepare_csv_export(self, hosts: List[Dict]) -> str:
        """Prepare CSV export data"""
        csv_lines = ["IP,Hostname,Status,OS,Open Ports,Services"]
        for host in hosts:
            ports = ",".join([str(p.get("port")) for p in host.get("open_ports", [])])
            services = ",".join([p.get("service", "") for p in host.get("open_ports", [])])
            csv_lines.append(f"{host.get('ip')},{host.get('hostname')},{host.get('status')},{self._get_primary_os(host.get('os_matches', []))},{ports},{services}")
        return "\n".join(csv_lines)

    def _prepare_pdf_export_data(self, hosts: List[Dict]) -> Dict:
        """Prepare PDF export data structure"""
        return {
            "title": "Nmap Network Scan Report",
            "timestamp": datetime.now().isoformat(),
            "summary": {
                "total_hosts": len(hosts),
                "hosts_up": len([h for h in hosts if h.get("status") == "up"]),
                "total_services": sum(len(h.get("open_ports", [])) for h in hosts)
            },
            "hosts": hosts
        }

    def _create_security_summary(self, hosts: List[Dict]) -> Dict:
        """Create security summary for frontend dashboard"""
        total_hosts = len(hosts)
        hosts_up = len([h for h in hosts if h.get("status") == "up"])
        total_ports = sum(len(h.get("open_ports", [])) for h in hosts)
        
        # Risk distribution
        risk_levels = [self._calculate_risk_level(h) for h in hosts]
        risk_distribution = {
            "High": risk_levels.count("High"),
            "Medium": risk_levels.count("Medium"),
            "Low": risk_levels.count("Low"),
            "Minimal": risk_levels.count("Minimal")
        }
        
        # Service distribution
        all_services = []
        for host in hosts:
            all_services.extend([p.get("service", "") for p in host.get("open_ports", [])])
        
        service_counts = {}
        for service in all_services:
            service_counts[service] = service_counts.get(service, 0) + 1
        
        return {
            "total_hosts": total_hosts,
            "hosts_up": hosts_up,
            "total_services": total_ports,
            "risk_distribution": risk_distribution,
            "top_services": dict(sorted(service_counts.items(), key=lambda x: x[1], reverse=True)[:10]),
            "scan_coverage": (hosts_up / max(1, total_hosts)) * 100
        }

    def _identify_vulnerability_indicators(self, hosts: List[Dict]) -> List[Dict]:
        """Identify vulnerability indicators for frontend"""
        indicators = []
        
        for host in hosts:
            host_indicators = self._get_risk_indicators(host)
            for indicator in host_indicators:
                indicators.append({
                    "host": host.get("ip"),
                    "type": "risk_indicator",
                    "message": indicator,
                    "severity": "medium"
                })
        
        return indicators

    def _create_scan_statistics(self, hosts: List[Dict]) -> Dict:
        """Create detailed scan statistics"""
        return {
            "hosts_discovered": len(hosts),
            "hosts_responsive": len([h for h in hosts if h.get("status") == "up"]),
            "total_open_ports": sum(len(h.get("open_ports", [])) for h in hosts),
            "unique_services": len(set([p.get("service", "") for h in hosts for p in h.get("open_ports", [])])),
            "os_distribution": self._get_os_distribution(hosts),
            "port_distribution": self._get_port_distribution(hosts)
        }

    def _get_os_distribution(self, hosts: List[Dict]) -> Dict:
        """Get OS distribution for statistics"""
        os_counts = {}
        for host in hosts:
            os_name = self._get_primary_os(host.get("os_matches", []))
            os_counts[os_name] = os_counts.get(os_name, 0) + 1
        return os_counts

    def _get_port_distribution(self, hosts: List[Dict]) -> Dict:
        """Get port distribution for statistics"""
        port_counts = {}
        for host in hosts:
            for port in host.get("open_ports", []):
                port_num = port.get("port")
                port_counts[port_num] = port_counts.get(port_num, 0) + 1
        return dict(sorted(port_counts.items(), key=lambda x: x[1], reverse=True)[:20])

    def _create_comparison_baseline(self, hosts: List[Dict]) -> Dict:
        """Create baseline data for scan comparison"""
        return {
            "timestamp": datetime.now().isoformat(),
            "host_count": len(hosts),
            "service_fingerprint": self._create_service_fingerprint(hosts),
            "network_fingerprint": self._create_network_fingerprint(hosts)
        }

    def _create_service_fingerprint(self, hosts: List[Dict]) -> str:
        """Create service fingerprint for comparison"""
        services = []
        for host in hosts:
            for port in host.get("open_ports", []):
                services.append(f"{host.get('ip')}:{port.get('port')}/{port.get('service', 'unknown')}")
        return "|".join(sorted(services))

    def _create_network_fingerprint(self, hosts: List[Dict]) -> str:
        """Create network fingerprint for comparison"""
        host_signatures = []
        for host in hosts:
            ports = sorted([p.get("port") for p in host.get("open_ports", [])])
            host_signatures.append(f"{host.get('ip')}:{','.join(map(str, ports))}")
        return "|".join(sorted(host_signatures))

    def _get_available_scan_options(self) -> Dict:
        """Get available scan options for frontend configuration"""
        return {
            "scan_types": {
                "-sS": "TCP SYN Stealth Scan",
                "-sT": "TCP Connect Scan", 
                "-sU": "UDP Scan",
                "-sA": "TCP ACK Scan",
                "-sF": "TCP FIN Scan"
            },
            "timing_templates": {
                "T0": "Paranoid (very slow)",
                "T1": "Sneaky (slow)",
                "T2": "Polite (slow)",
                "T3": "Normal (default)",
                "T4": "Aggressive (fast)",
                "T5": "Insane (very fast)"
            },
            "common_port_ranges": {
                "top_100": "1-100",
                "top_1000": "1-1000", 
                "common_services": "21,22,23,25,53,80,110,143,443,993,995",
                "web_services": "80,443,8080,8443",
                "all_ports": "1-65535"
            }
        }