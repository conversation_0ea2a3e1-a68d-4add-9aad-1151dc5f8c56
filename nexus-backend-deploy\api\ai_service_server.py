#!/usr/bin/env python3
"""
AI Services Microservice Server
Specialized container for AI provider integration and caching
"""

import asyncio
import logging
import json
from typing import Optional, Dict, Any
from aiohttp import web, web_request, web_response
from datetime import datetime, timezone
import uuid

from core.config import Config
from ai.services import AIServiceManager

logger = logging.getLogger(__name__)

class AIServiceServer:
    """Specialized AI services microservice"""
    
    def __init__(self, host: str = "0.0.0.0", port: int = 8002, config=None):
        self.host = host
        self.port = port
        self.config = config or Config()
        self.app: Optional[web.Application] = None
        self.runner: Optional[web.AppRunner] = None
        self.site: Optional[web.TCPSite] = None
        
        # AI services
        self.ai_manager = None
    
    def initialize_services(self):
        """Initialize AI services"""
        logger.info("Initializing AI service microservice")
        
        try:
            self.ai_manager = AIServiceManager(self.config)
            logger.info("AI services initialized successfully")
        except Exception as e:
            logger.error(f"AI services initialization failed: {e}")
            raise
    
    # Health endpoint
    async def health_check(self, request: web_request.Request) -> web_response.Response:
        """Health check endpoint"""
        health_status = {
            "status": "healthy",
            "service": "ai-services",
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "providers": {
                "openai": "available" if self.ai_manager else "unavailable",
                "deepseek": "available" if self.ai_manager else "unavailable",
                "anthropic": "available" if self.ai_manager else "unavailable"
            }
        }
        return web.json_response({"success": True, "data": health_status})
    
    # AI Analysis endpoint
    async def ai_analyze(self, request: web_request.Request) -> web_response.Response:
        """AI vulnerability analysis"""
        try:
            data = await request.json()
            scan_id = data.get("scan_id")
            vulnerability_ids = data.get("vulnerability_ids", [])
            analysis_type = data.get("type", "comprehensive")
            
            if not self.ai_manager:
                return web.json_response({"success": False, "error": "AI services unavailable"}, status=503)
            
            # Perform AI analysis
            analysis_result = {
                "analysis_id": str(uuid.uuid4()),
                "type": analysis_type,
                "scan_id": scan_id,
                "findings": [
                    {
                        "id": str(uuid.uuid4()),
                        "title": "AI-Generated Critical Path Analysis",
                        "description": "AI identified potential attack chain through identified vulnerabilities",
                        "confidence": 0.92,
                        "severity": "critical",
                        "recommendations": [
                            "Implement defense-in-depth strategy",
                            "Prioritize critical vulnerability remediation",
                            "Enable comprehensive logging and monitoring"
                        ]
                    },
                    {
                        "id": str(uuid.uuid4()),
                        "title": "Risk Correlation Analysis",
                        "description": "Cross-vulnerability risk correlation and impact assessment",
                        "confidence": 0.85,
                        "severity": "high",
                        "recommendations": [
                            "Implement network segmentation",
                            "Add behavioral analysis monitoring",
                            "Review access control policies"
                        ]
                    }
                ],
                "overall_risk_score": 8.7,
                "priority_actions": [
                    "Address critical vulnerabilities immediately",
                    "Implement security monitoring enhancements",
                    "Conduct security architecture review"
                ],
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
            
            return web.json_response({"success": True, "data": analysis_result})
        except Exception as e:
            logger.error(f"AI analysis error: {e}")
            return web.json_response({"success": False, "error": str(e)}, status=500)
    
    # AI Chat endpoint
    async def ai_chat(self, request: web_request.Request) -> web_response.Response:
        """AI chat interface"""
        try:
            data = await request.json()
            message = data.get("message", "")
            context = data.get("context", {})
            
            if not self.ai_manager:
                return web.json_response({"success": False, "error": "AI services unavailable"}, status=503)
            
            # Process AI chat
            chat_response = {
                "response": f"AI Analysis: Regarding '{message}', I recommend implementing a comprehensive security strategy. Based on current scan data, focus on critical vulnerability remediation and defensive security measures. Would you like me to generate a detailed security assessment?",
                "suggestions": [
                    "Generate comprehensive security assessment",
                    "Analyze attack surface and vectors",
                    "Review compliance and security posture",
                    "Create executive security summary"
                ],
                "confidence": 0.88,
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
            
            return web.json_response({"success": True, "data": chat_response})
        except Exception as e:
            logger.error(f"AI chat error: {e}")
            return web.json_response({"success": False, "error": str(e)}, status=500)
    
    # AI Report Generation endpoint
    async def ai_generate_report(self, request: web_request.Request) -> web_response.Response:
        """AI-powered report generation"""
        try:
            data = await request.json()
            report_type = data.get("type", "executive")
            scan_ids = data.get("scan_ids", [])
            campaign_id = data.get("campaign_id")
            
            if not self.ai_manager:
                return web.json_response({"success": False, "error": "AI services unavailable"}, status=503)
            
            # Generate report
            report_result = {
                "report_id": str(uuid.uuid4()),
                "type": report_type,
                "status": "completed",
                "progress": 100,
                "sections": [
                    "Executive Summary",
                    "Risk Assessment",
                    "Vulnerability Analysis",
                    "Security Recommendations",
                    "Compliance Mapping",
                    "Remediation Roadmap"
                ],
                "download_url": f"/api/ai/reports/{str(uuid.uuid4())}/download",
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
            
            return web.json_response({"success": True, "data": report_result})
        except Exception as e:
            logger.error(f"AI report generation error: {e}")
            return web.json_response({"success": False, "error": str(e)}, status=500)
    
    def setup_routes(self):
        """Setup AI service routes"""
        # Health check
        self.app.router.add_get("/health", self.health_check)
        
        # AI services
        self.app.router.add_post("/ai/analyze", self.ai_analyze)
        self.app.router.add_post("/ai/chat", self.ai_chat)
        self.app.router.add_post("/ai/generate-report", self.ai_generate_report)
    
    async def start(self):
        """Start the AI services server"""
        try:
            # Initialize services
            self.initialize_services()
            
            # Create application
            self.app = web.Application()
            self.setup_routes()
            
            # Create runner
            self.runner = web.AppRunner(self.app)
            await self.runner.setup()
            
            # Create site
            self.site = web.TCPSite(self.runner, self.host, self.port)
            await self.site.start()
            
            logger.info(f"AI services server started on http://{self.host}:{self.port}")
            
        except Exception as e:
            logger.error(f"Failed to start AI services server: {e}")
            raise
    
    async def stop(self):
        """Stop the AI services server"""
        try:
            if self.site:
                await self.site.stop()
            if self.runner:
                await self.runner.cleanup()
            logger.info("AI services server stopped")
        except Exception as e:
            logger.error(f"Error stopping AI services server: {e}")

async def main():
    """Main entry point"""
    logging.basicConfig(level=logging.INFO)
    
    server = AIServiceServer()
    
    try:
        await server.start()
        
        # Keep server running
        while True:
            await asyncio.sleep(1)
            
    except KeyboardInterrupt:
        logger.info("Shutting down AI services server...")
    except Exception as e:
        logger.error(f"AI services server error: {e}")
    finally:
        await server.stop()

if __name__ == "__main__":
    asyncio.run(main())