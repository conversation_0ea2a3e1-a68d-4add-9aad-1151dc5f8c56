/**
 * Ferrari AI Page - Advanced AI Capabilities Hub
 * Central interface for all Ferrari AI features and capabilities
 */
import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Brain,
  Zap,
  Target,
  Shield,
  Activity,
  Cpu,
  Network,
  Eye,
  AlertTriangle,
  CheckCircle,
  Gauge,
  BarChart3,
  TrendingUp,
  Sparkles,
  Workflow,
  GitBranch,
  Lock,
  Unlock,
  Search,
  Filter,
  Settings,
  Play,
  Square,
  RefreshCw,
  Download,
  FileText,
  Terminal,
  Globe,
  Server,
  Database,
  Code,
  Binary,
  Hash,
  Key,
  Crown,
  Award,
  Star
} from 'lucide-react';

// Import Ferrari AI components
import { MultiStageOrchestrator } from '@/components/ferrari/MultiStageOrchestrator';
import { CreativeExploitEngine } from '@/components/ferrari/CreativeExploitEngine';
import { BehavioralAnalysisEngine } from '@/components/ferrari/BehavioralAnalysisEngine';
import { AIProxyManager } from '@/components/ferrari/AIProxyManager';
import { EvasionTechniquesGenerator } from '@/components/ferrari/EvasionTechniquesGenerator';
import { AdaptiveExploitModifier } from '@/components/ferrari/AdaptiveExploitModifier';

// Import stores
import { useBackendStore } from '@/stores/backend-store';
import { useAppStore } from '@/stores/app-store';

interface FerrariCapability {
  id: string;
  name: string;
  description: string;
  icon: React.ComponentType;
  status: 'operational' | 'degraded' | 'offline';
  confidence: number;
  lastUpdate: string;
  aiModels: string[];
  features: string[];
  category: 'orchestration' | 'generation' | 'analysis' | 'evasion' | 'modification' | 'proxy';
}

const FERRARI_CAPABILITIES: FerrariCapability[] = [
  {
    id: 'orchestrator',
    name: 'Multi-Stage Attack Orchestrator',
    description: 'AI-powered attack chain orchestration with MITRE ATT&CK integration',
    icon: Workflow,
    status: 'operational',
    confidence: 95,
    lastUpdate: '2025-07-11 10:30:00',
    aiModels: ['GPT-4', 'DeepSeek-V3', 'Claude-4'],
    features: ['MITRE ATT&CK Matrix', 'Real-time Adaptation', 'Safety Constraints', 'Chain Templates'],
    category: 'orchestration'
  },
  {
    id: 'creative-exploits',
    name: 'Creative Exploit Engine',
    description: 'AI-generated novel attack vectors and polyglot payload construction',
    icon: Sparkles,
    status: 'operational',
    confidence: 92,
    lastUpdate: '2025-07-11 10:25:00',
    aiModels: ['GPT-4', 'DeepSeek-V3'],
    features: ['Polyglot Payloads', 'Mutation Engine', 'Novelty Scoring', 'Confidence Analysis'],
    category: 'generation'
  },
  {
    id: 'behavioral-analysis',
    name: 'Behavioral Analysis Engine',
    description: 'Zero-day style behavioral pattern recognition and anomaly detection',
    icon: Eye,
    status: 'operational',
    confidence: 88,
    lastUpdate: '2025-07-11 10:20:00',
    aiModels: ['GPT-4', 'Claude-4'],
    features: ['Anomaly Detection', 'Pattern Recognition', 'Baseline Establishment', 'Predictive Analysis'],
    category: 'analysis'
  },
  {
    id: 'ai-proxy',
    name: 'AI-Powered Proxy Manager',
    description: 'Intelligent proxy rotation and traffic analysis for detection avoidance',
    icon: Network,
    status: 'operational',
    confidence: 90,
    lastUpdate: '2025-07-11 10:15:00',
    aiModels: ['GPT-4', 'DeepSeek-V3'],
    features: ['Intelligent Rotation', 'Traffic Analysis', 'Detection Avoidance', 'Performance Optimization'],
    category: 'proxy'
  },
  {
    id: 'evasion-techniques',
    name: 'Evasion Techniques Generator',
    description: 'Advanced WAF, IPS, and security control bypass generation',
    icon: Shield,
    status: 'operational',
    confidence: 86,
    lastUpdate: '2025-07-11 10:10:00',
    aiModels: ['GPT-4', 'DeepSeek-V3', 'Claude-4'],
    features: ['WAF Bypass', 'IPS Evasion', 'Signature Breaking', 'Vendor-Specific Intelligence'],
    category: 'evasion'
  },
  {
    id: 'adaptive-modifier',
    name: 'Adaptive Exploit Modifier',
    description: 'Real-time exploit adaptation based on target environment analysis',
    icon: Target,
    status: 'operational',
    confidence: 84,
    lastUpdate: '2025-07-11 10:05:00',
    aiModels: ['GPT-4', 'Claude-4'],
    features: ['Environment Detection', 'Real-time Adaptation', 'Learning System', 'Effectiveness Scoring'],
    category: 'modification'
  }
];

export const FerrariPage: React.FC = () => {
  const { isConnected, backendStatus } = useBackendStore();
  const { theme } = useAppStore();
  const [selectedCapability, setSelectedCapability] = useState<string>('orchestrator');
  const [aiStatus, setAiStatus] = useState<any>(null);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (isConnected) {
      fetchAIStatus();
    }
  }, [isConnected]);

  const fetchAIStatus = async () => {
    setLoading(true);
    try {
      // Fetch AI capabilities status from backend
      const response = await fetch('http://ec2-3-89-91-209.compute-1.amazonaws.com:8000/api/ai/capabilities/status');
      if (response.ok) {
        const data = await response.json();
        setAiStatus(data);
      }
    } catch (error) {
      console.error('Failed to fetch AI status:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'operational': return 'text-green-600 bg-green-50 border-green-200';
      case 'degraded': return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'offline': return 'text-red-600 bg-red-50 border-red-200';
      default: return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'orchestration': return Workflow;
      case 'generation': return Sparkles;
      case 'analysis': return Eye;
      case 'evasion': return Shield;
      case 'modification': return Target;
      case 'proxy': return Network;
      default: return Brain;
    }
  };

  const renderCapabilityComponent = () => {
    switch (selectedCapability) {
      case 'orchestrator':
        return <MultiStageOrchestrator />;
      case 'creative-exploits':
        return <CreativeExploitEngine />;
      case 'behavioral-analysis':
        return <BehavioralAnalysisEngine />;
      case 'ai-proxy':
        return <AIProxyManager />;
      case 'evasion-techniques':
        return <EvasionTechniquesGenerator />;
      case 'adaptive-modifier':
        return <AdaptiveExploitModifier />;
      default:
        return <MultiStageOrchestrator />;
    }
  };

  const selectedCapabilityData = FERRARI_CAPABILITIES.find(cap => cap.id === selectedCapability);

  return (
    <div className="flex flex-col space-y-6 p-6">
      {/* Header Section */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className="p-3 bg-purple-100 rounded-lg">
            <Brain className="h-8 w-8 text-purple-600" />
          </div>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Ferrari AI Capabilities</h1>
            <p className="text-gray-600 mt-1">Advanced AI-powered penetration testing and security analysis</p>
          </div>
        </div>
        <div className="flex items-center space-x-3">
          <Badge variant={isConnected ? 'default' : 'destructive'} className="px-3 py-1">
            <Activity className="h-4 w-4 mr-1" />
            {isConnected ? 'AI Online' : 'AI Offline'}
          </Badge>
          <Button onClick={fetchAIStatus} disabled={loading} variant="outline" size="sm">
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Refresh Status
          </Button>
        </div>
      </div>

      {/* Connection Warning */}
      {!isConnected && (
        <Card className="border-yellow-200 bg-yellow-50">
          <CardContent className="pt-6">
            <div className="flex items-center space-x-3">
              <AlertTriangle className="h-5 w-5 text-yellow-600" />
              <div>
                <p className="font-medium text-yellow-800">Backend Connection Required</p>
                <p className="text-sm text-yellow-600 mt-1">
                  Connect to the AWS backend to access Ferrari AI capabilities.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* AI Capabilities Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {FERRARI_CAPABILITIES.map((capability) => {
          const IconComponent = capability.icon;
          const CategoryIcon = getCategoryIcon(capability.category);
          
          return (
            <Card 
              key={capability.id}
              className={`cursor-pointer transition-all hover:shadow-md ${
                selectedCapability === capability.id ? 'ring-2 ring-purple-500 shadow-md' : ''
              }`}
              onClick={() => setSelectedCapability(capability.id)}
            >
              <CardHeader className="pb-4">
                <div className="flex items-start justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="p-2 bg-purple-100 rounded-lg">
                      <IconComponent className="h-6 w-6 text-purple-600" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <CardTitle className="text-lg font-semibold truncate">
                        {capability.name}
                      </CardTitle>
                      <div className="flex items-center space-x-2 mt-1">
                        <CategoryIcon className="h-4 w-4 text-gray-500" />
                        <span className="text-sm text-gray-500 capitalize">{capability.category}</span>
                      </div>
                    </div>
                  </div>
                  <Badge className={getStatusColor(capability.status)} variant="outline">
                    {capability.status}
                  </Badge>
                </div>
                <CardDescription className="mt-2 text-sm">
                  {capability.description}
                </CardDescription>
              </CardHeader>
              <CardContent className="pt-0">
                {/* Confidence Score */}
                <div className="space-y-2 mb-4">
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-600">AI Confidence</span>
                    <span className="font-medium">{capability.confidence}%</span>
                  </div>
                  <Progress value={capability.confidence} className="h-2" />
                </div>

                {/* AI Models */}
                <div className="space-y-2 mb-4">
                  <p className="text-sm font-medium text-gray-700">AI Models</p>
                  <div className="flex flex-wrap gap-1">
                    {capability.aiModels.map((model) => (
                      <Badge key={model} variant="secondary" className="text-xs">
                        {model}
                      </Badge>
                    ))}
                  </div>
                </div>

                {/* Key Features */}
                <div className="space-y-2">
                  <p className="text-sm font-medium text-gray-700">Key Features</p>
                  <div className="flex flex-wrap gap-1">
                    {capability.features.slice(0, 2).map((feature) => (
                      <Badge key={feature} variant="outline" className="text-xs">
                        {feature}
                      </Badge>
                    ))}
                    {capability.features.length > 2 && (
                      <Badge variant="outline" className="text-xs">
                        +{capability.features.length - 2} more
                      </Badge>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Selected Capability Interface */}
      {selectedCapabilityData && (
        <Card className="mt-6">
          <CardHeader>
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="p-3 bg-purple-100 rounded-lg">
                  <selectedCapabilityData.icon className="h-6 w-6 text-purple-600" />
                </div>
                <div>
                  <CardTitle className="text-xl">{selectedCapabilityData.name}</CardTitle>
                  <CardDescription>{selectedCapabilityData.description}</CardDescription>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <Badge className={getStatusColor(selectedCapabilityData.status)} variant="outline">
                  {selectedCapabilityData.status}
                </Badge>
                <Badge variant="secondary">
                  {selectedCapabilityData.confidence}% confidence
                </Badge>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            {/* Capability-specific interface */}
            {renderCapabilityComponent()}
          </CardContent>
        </Card>
      )}

      {/* AI System Status */}
      {aiStatus && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Cpu className="h-5 w-5" />
              <span>AI System Status</span>
            </CardTitle>
            <CardDescription>Real-time status of AI services and models</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">OpenAI GPT-4</span>
                  <Badge variant={aiStatus.openai?.status === 'online' ? 'default' : 'destructive'}>
                    {aiStatus.openai?.status || 'unknown'}
                  </Badge>
                </div>
                <Progress value={aiStatus.openai?.health || 0} className="h-2" />
              </div>
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">DeepSeek-V3</span>
                  <Badge variant={aiStatus.deepseek?.status === 'online' ? 'default' : 'destructive'}>
                    {aiStatus.deepseek?.status || 'unknown'}
                  </Badge>
                </div>
                <Progress value={aiStatus.deepseek?.health || 0} className="h-2" />
              </div>
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Claude-4</span>
                  <Badge variant={aiStatus.claude?.status === 'online' ? 'default' : 'destructive'}>
                    {aiStatus.claude?.status || 'unknown'}
                  </Badge>
                </div>
                <Progress value={aiStatus.claude?.health || 0} className="h-2" />
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default FerrariPage;