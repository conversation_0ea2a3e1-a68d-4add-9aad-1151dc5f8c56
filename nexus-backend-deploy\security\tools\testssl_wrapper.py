"""
TestSSL Scanner Wrapper for NexusScan Desktop
Comprehensive SSL/TLS security testing tool wrapper.
"""

import logging
from typing import Dict, List, Optional, Callable, Any
from datetime import datetime
import json

from security.tools.tool_registry import (
    ToolMetadata, ToolCategory, ToolCapabilities,
    ScanOptions, ScanResult, ToolStatus, register_tool
)
from security.tools.external_tool_wrapper import ExternalToolWrapper

logger = logging.getLogger(__name__)


@register_tool
class TestSSLScanner(ExternalToolWrapper):
    """TestSSL comprehensive SSL/TLS scanner wrapper"""
    
    def get_metadata(self) -> ToolMetadata:
        """Get tool metadata"""
        return ToolMetadata(
            name="testssl",
            display_name="TestSSL Scanner",
            description="Comprehensive SSL/TLS security testing including ciphers, protocols, and vulnerabilities",
            version="3.2.0",
            category=ToolCategory.VULNERABILITY_SCANNER,
            author="<PERSON>",
            website="https://testssl.sh/",
            capabilities=ToolCapabilities(
                supports_async=True,
                supports_progress=True,
                supports_cancellation=True,
                requires_root=False,
                network_access_required=True,
                output_formats=["json", "text", "html"],
                supported_targets=["url", "ip", "domain"]
            ),
            default_options={
                "severity": "all",  # all, high, medium, low
                "protocols": True,
                "server_defaults": True,
                "server_preference": True,
                "ciphers": True,
                "pfs": True,  # Perfect Forward Secrecy
                "vulnerabilities": True,
                "client_simulation": False,  # Time-consuming
                "fast": False
            }
        )
    
    def get_command_path(self) -> str:
        """Get TestSSL command path"""
        return "testssl.sh"
    
    def build_command(self, options: ScanOptions) -> List[str]:
        """Build TestSSL command"""
        cmd = [self.get_command_path()]
        
        # Output format
        if options.output_format == "json":
            cmd.extend(["--jsonfile", "-"])
        elif options.output_format == "html":
            cmd.extend(["--htmlfile", "-"])
        
        # Fast mode
        if options.custom_options.get("fast", False):
            cmd.append("--fast")
        
        # Severity level
        severity = options.custom_options.get("severity", "all")
        if severity != "all":
            cmd.extend(["--severity", severity.upper()])
        
        # Test selections
        if not options.custom_options.get("protocols", True):
            cmd.append("--protocols")
        
        if not options.custom_options.get("server_defaults", True):
            cmd.append("--server-defaults")
        
        if not options.custom_options.get("server_preference", True):
            cmd.append("--server-preference")
        
        if not options.custom_options.get("ciphers", True):
            cmd.append("--std")  # Standard ciphers only
        
        if not options.custom_options.get("pfs", True):
            cmd.append("--pfs")
        
        if not options.custom_options.get("vulnerabilities", True):
            cmd.append("--vulnerabilities")
        
        if options.custom_options.get("client_simulation", False):
            cmd.append("--client-simulation")
        
        # Timeout
        if options.timeout:
            cmd.extend(["--openssl-timeout", str(options.timeout)])
        
        # Proxy
        if options.custom_options.get("proxy"):
            cmd.extend(["--proxy", options.custom_options["proxy"]])
        
        # Target (must be last)
        cmd.append(options.target)
        
        return cmd
    
    def parse_output(self, output: str, options: ScanOptions) -> Dict[str, Any]:
        """Parse TestSSL output"""
        parsed_results = {
            "target": options.target,
            "scan_time": datetime.now().isoformat(),
            "protocols": {},
            "ciphers": [],
            "vulnerabilities": [],
            "server_defaults": {},
            "certificate_info": {},
            "summary": {}
        }
        
        try:
            if options.output_format == "json":
                # Parse JSON output
                data = json.loads(output)
                
                # Extract scan results
                for finding in data.get("scanResult", []):
                    finding_id = finding.get("id", "")
                    severity = finding.get("severity", "").upper()
                    finding_str = finding.get("finding", "")
                    
                    # Categorize findings
                    if "protocol" in finding_id.lower():
                        parsed_results["protocols"][finding_id] = {
                            "supported": "not offered" not in finding_str.lower(),
                            "severity": severity,
                            "details": finding_str
                        }
                    
                    elif "cipher" in finding_id.lower():
                        parsed_results["ciphers"].append({
                            "cipher": finding_id,
                            "severity": severity,
                            "details": finding_str
                        })
                    
                    elif any(vuln in finding_id.lower() for vuln in 
                            ["heartbleed", "ccs", "ticketbleed", "robot", "secure_renego",
                             "crime", "breach", "poodle", "beast", "lucky13", "freak", "logjam"]):
                        parsed_results["vulnerabilities"].append({
                            "name": finding_id,
                            "severity": severity,
                            "vulnerable": "not vulnerable" not in finding_str.lower(),
                            "details": finding_str
                        })
                    
                    elif "cert" in finding_id.lower():
                        parsed_results["certificate_info"][finding_id] = finding_str
                    
                    else:
                        parsed_results["server_defaults"][finding_id] = finding_str
                
            else:
                # Parse text output
                lines = output.strip().split('\n')
                current_section = None
                
                for line in lines:
                    line = line.strip()
                    if not line:
                        continue
                    
                    # Detect sections
                    if "Testing protocols" in line:
                        current_section = "protocols"
                    elif "Testing cipher categories" in line:
                        current_section = "ciphers"
                    elif "Testing vulnerabilities" in line:
                        current_section = "vulnerabilities"
                    elif "Testing server defaults" in line:
                        current_section = "server_defaults"
                    
                    # Parse based on section
                    elif current_section and ":" in line:
                        if current_section == "vulnerabilities":
                            vuln_name = line.split(':')[0].strip()
                            vuln_status = line.split(':')[1].strip()
                            parsed_results["vulnerabilities"].append({
                                "name": vuln_name,
                                "vulnerable": "not vulnerable" not in vuln_status.lower(),
                                "details": vuln_status
                            })
            
            # Generate summary
            parsed_results["summary"] = {
                "protocols_tested": len(parsed_results["protocols"]),
                "weak_protocols": sum(1 for p in parsed_results["protocols"].values() 
                                    if p.get("supported") and p.get("severity") in ["HIGH", "CRITICAL"]),
                "total_ciphers": len(parsed_results["ciphers"]),
                "weak_ciphers": sum(1 for c in parsed_results["ciphers"] 
                                  if c.get("severity") in ["HIGH", "CRITICAL"]),
                "vulnerabilities_found": sum(1 for v in parsed_results["vulnerabilities"] 
                                           if v.get("vulnerable")),
                "overall_grade": self._calculate_ssl_grade(parsed_results)
            }
            
        except Exception as e:
            logger.error(f"Failed to parse TestSSL output: {e}")
            parsed_results["parse_error"] = str(e)
            parsed_results["raw_output"] = output[:1000]  # First 1000 chars
        
        return parsed_results
    
    def _calculate_ssl_grade(self, results: Dict[str, Any]) -> str:
        """Calculate overall SSL/TLS grade based on findings"""
        score = 100
        
        # Deduct for weak protocols
        weak_protocols = results["summary"].get("weak_protocols", 0)
        score -= weak_protocols * 10
        
        # Deduct for weak ciphers
        weak_ciphers = results["summary"].get("weak_ciphers", 0)
        score -= min(weak_ciphers * 5, 20)
        
        # Deduct for vulnerabilities
        vulns = results["summary"].get("vulnerabilities_found", 0)
        score -= vulns * 15
        
        # Grade calculation
        if score >= 90:
            return "A+"
        elif score >= 80:
            return "A"
        elif score >= 70:
            return "B"
        elif score >= 60:
            return "C"
        elif score >= 50:
            return "D"
        else:
            return "F"
    
    def get_tool_command(self, options: ScanOptions) -> List[str]:
        """Get tool command - required by base class"""
        return self.build_command(options)
    
    def parse_tool_output(self, output: str, options: ScanOptions) -> Dict[str, Any]:
        """Parse tool output - required by base class"""
        return self.parse_output(output, options)