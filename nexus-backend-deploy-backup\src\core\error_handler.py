"""
Comprehensive Error Handling System for NexusScan Desktop
Advanced error handling with logging, recovery, and user-friendly error reporting.
"""

import asyncio
import logging
import traceback
import sys
import time
import json
import uuid
from typing import Dict, List, Optional, Any, Callable, Type, Union
from enum import Enum
from datetime import datetime, timed<PERSON>ta
from dataclasses import dataclass, asdict
from pathlib import Path
import functools
import threading
from concurrent.futures import ThreadPoolExecutor
import inspect

logger = logging.getLogger(__name__)


class ErrorSeverity(Enum):
    """Error severity levels"""
    CRITICAL = "critical"      # System cannot continue
    HIGH = "high"             # Major functionality affected
    MEDIUM = "medium"         # Minor functionality affected
    LOW = "low"              # Minimal impact
    INFO = "info"            # Informational only


class ErrorCategory(Enum):
    """Error categories for classification"""
    AUTHENTICATION = "authentication"
    AUTHORIZATION = "authorization"
    VALIDATION = "validation"
    DATABASE = "database"
    NETWORK = "network"
    FILE_SYSTEM = "file_system"
    SCANNING = "scanning"
    AI_SERVICE = "ai_service"
    CONFIGURATION = "configuration"
    PERFORMANCE = "performance"
    SECURITY = "security"
    UI = "ui"
    API = "api"
    SYSTEM = "system"
    UNKNOWN = "unknown"


class RecoveryStrategy(Enum):
    """Error recovery strategies"""
    RETRY = "retry"                    # Retry the operation
    FALLBACK = "fallback"             # Use alternative method
    GRACEFUL_DEGRADATION = "graceful_degradation"  # Continue with reduced functionality
    USER_INTERVENTION = "user_intervention"        # Require user action
    SYSTEM_RESTART = "system_restart"             # Restart component/system
    ABORT = "abort"                               # Stop operation
    IGNORE = "ignore"                             # Log and continue


@dataclass
class ErrorContext:
    """Error context information"""
    function_name: str
    module_name: str
    line_number: int
    arguments: Dict[str, Any]
    local_variables: Dict[str, Any]
    stack_trace: str
    timestamp: datetime
    thread_id: str
    process_id: int


@dataclass
class ErrorRecord:
    """Comprehensive error record"""
    error_id: str
    error_type: str
    error_message: str
    severity: ErrorSeverity
    category: ErrorCategory
    context: ErrorContext
    recovery_strategy: RecoveryStrategy
    recovery_attempts: int
    resolved: bool
    resolution_time: Optional[datetime]
    user_message: str
    technical_details: str
    suggestions: List[str]
    related_errors: List[str]
    metadata: Dict[str, Any]


@dataclass
class RecoveryAction:
    """Recovery action configuration"""
    strategy: RecoveryStrategy
    max_attempts: int
    delay_seconds: float
    fallback_function: Optional[Callable]
    condition_check: Optional[Callable]
    custom_handler: Optional[Callable]


class ErrorRecoveryManager:
    """Manages error recovery strategies"""
    
    def __init__(self):
        self.recovery_configs: Dict[Type[Exception], RecoveryAction] = {}
        self.recovery_history: Dict[str, List[datetime]] = {}
        self.circuit_breaker_states: Dict[str, bool] = {}
        
        # Default recovery configurations
        self._setup_default_recovery_configs()
    
    def _setup_default_recovery_configs(self):
        """Setup default recovery configurations"""
        # Network errors - retry with exponential backoff
        self.recovery_configs[ConnectionError] = RecoveryAction(
            strategy=RecoveryStrategy.RETRY,
            max_attempts=3,
            delay_seconds=1.0,
            fallback_function=None,
            condition_check=None,
            custom_handler=None
        )
        
        # Timeout errors - retry with longer timeout
        self.recovery_configs[TimeoutError] = RecoveryAction(
            strategy=RecoveryStrategy.RETRY,
            max_attempts=2,
            delay_seconds=2.0,
            fallback_function=None,
            condition_check=None,
            custom_handler=None
        )
        
        # Permission errors - user intervention required
        self.recovery_configs[PermissionError] = RecoveryAction(
            strategy=RecoveryStrategy.USER_INTERVENTION,
            max_attempts=1,
            delay_seconds=0.0,
            fallback_function=None,
            condition_check=None,
            custom_handler=None
        )
        
        # File not found - graceful degradation
        self.recovery_configs[FileNotFoundError] = RecoveryAction(
            strategy=RecoveryStrategy.GRACEFUL_DEGRADATION,
            max_attempts=1,
            delay_seconds=0.0,
            fallback_function=None,
            condition_check=None,
            custom_handler=None
        )
        
        # Memory errors - system restart
        self.recovery_configs[MemoryError] = RecoveryAction(
            strategy=RecoveryStrategy.SYSTEM_RESTART,
            max_attempts=1,
            delay_seconds=0.0,
            fallback_function=None,
            condition_check=None,
            custom_handler=None
        )
    
    def register_recovery_config(self, exception_type: Type[Exception], 
                                config: RecoveryAction):
        """Register custom recovery configuration"""
        self.recovery_configs[exception_type] = config
    
    def get_recovery_strategy(self, exception: Exception) -> RecoveryAction:
        """Get recovery strategy for exception"""
        exception_type = type(exception)
        
        # Check for exact match
        if exception_type in self.recovery_configs:
            return self.recovery_configs[exception_type]
        
        # Check for parent class matches
        for exc_type, config in self.recovery_configs.items():
            if isinstance(exception, exc_type):
                return config
        
        # Default strategy
        return RecoveryAction(
            strategy=RecoveryStrategy.ABORT,
            max_attempts=1,
            delay_seconds=0.0,
            fallback_function=None,
            condition_check=None,
            custom_handler=None
        )
    
    async def execute_recovery(self, error_record: ErrorRecord, 
                             original_function: Callable, 
                             args: tuple, kwargs: dict) -> Any:
        """Execute recovery strategy"""
        recovery_config = self.get_recovery_strategy(
            Exception(error_record.error_message)
        )
        
        if recovery_config.strategy == RecoveryStrategy.RETRY:
            return await self._execute_retry(
                error_record, original_function, args, kwargs, recovery_config
            )
        elif recovery_config.strategy == RecoveryStrategy.FALLBACK:
            return await self._execute_fallback(
                error_record, recovery_config, args, kwargs
            )
        elif recovery_config.strategy == RecoveryStrategy.GRACEFUL_DEGRADATION:
            return await self._execute_graceful_degradation(error_record)
        elif recovery_config.strategy == RecoveryStrategy.USER_INTERVENTION:
            return await self._request_user_intervention(error_record)
        else:
            # Abort or other strategies
            raise Exception(f"Recovery failed: {error_record.error_message}")
    
    async def _execute_retry(self, error_record: ErrorRecord, 
                           original_function: Callable, args: tuple, 
                           kwargs: dict, config: RecoveryAction) -> Any:
        """Execute retry recovery strategy"""
        for attempt in range(config.max_attempts):
            try:
                # Exponential backoff
                delay = config.delay_seconds * (2 ** attempt)
                if delay > 0:
                    await asyncio.sleep(delay)
                
                # Check circuit breaker
                function_key = f"{original_function.__module__}.{original_function.__name__}"
                if self.circuit_breaker_states.get(function_key, False):
                    raise Exception("Circuit breaker is open")
                
                # Retry the function
                if asyncio.iscoroutinefunction(original_function):
                    result = await original_function(*args, **kwargs)
                else:
                    result = original_function(*args, **kwargs)
                
                # Success - reset circuit breaker
                self.circuit_breaker_states[function_key] = False
                error_record.recovery_attempts = attempt + 1
                error_record.resolved = True
                error_record.resolution_time = datetime.now()
                
                return result
                
            except Exception as e:
                error_record.recovery_attempts = attempt + 1
                
                if attempt == config.max_attempts - 1:
                    # Final attempt failed - open circuit breaker
                    function_key = f"{original_function.__module__}.{original_function.__name__}"
                    self.circuit_breaker_states[function_key] = True
                    raise e
        
        raise Exception("All retry attempts failed")
    
    async def _execute_fallback(self, error_record: ErrorRecord, 
                              config: RecoveryAction, args: tuple, kwargs: dict) -> Any:
        """Execute fallback recovery strategy"""
        if config.fallback_function:
            try:
                if asyncio.iscoroutinefunction(config.fallback_function):
                    result = await config.fallback_function(*args, **kwargs)
                else:
                    result = config.fallback_function(*args, **kwargs)
                
                error_record.resolved = True
                error_record.resolution_time = datetime.now()
                return result
                
            except Exception as e:
                raise Exception(f"Fallback function also failed: {str(e)}")
        else:
            raise Exception("No fallback function configured")
    
    async def _execute_graceful_degradation(self, error_record: ErrorRecord) -> Any:
        """Execute graceful degradation strategy"""
        error_record.resolved = True
        error_record.resolution_time = datetime.now()
        
        # Return safe default or partial result
        return {
            "status": "degraded",
            "message": "Operating with reduced functionality",
            "error": error_record.error_message
        }
    
    async def _request_user_intervention(self, error_record: ErrorRecord) -> Any:
        """Request user intervention for error resolution"""
        # In a real implementation, this would show UI dialog or notification
        logger.warning(f"User intervention required: {error_record.user_message}")
        
        error_record.resolved = False
        
        return {
            "status": "user_intervention_required",
            "message": error_record.user_message,
            "error_id": error_record.error_id
        }


class ErrorHandler:
    """Comprehensive error handling system"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """Initialize error handler"""
        self.config = config or {}
        self.error_records: Dict[str, ErrorRecord] = {}
        self.recovery_manager = ErrorRecoveryManager()
        
        # Error handling configuration
        self.log_errors = self.config.get("log_errors", True)
        self.enable_recovery = self.config.get("enable_recovery", True)
        self.max_error_records = self.config.get("max_error_records", 1000)
        self.error_reporting_enabled = self.config.get("error_reporting", True)
        
        # Error aggregation for pattern detection
        self.error_patterns: Dict[str, List[datetime]] = {}
        self.pattern_threshold = 5  # Number of similar errors to trigger pattern alert
        self.pattern_window = timedelta(minutes=10)  # Time window for pattern detection
        
        logger.info("Error handling system initialized")
    
    def handle_error(self, exception: Exception, context: Optional[Dict[str, Any]] = None) -> ErrorRecord:
        """Handle an error and create error record"""
        error_id = str(uuid.uuid4())
        
        # Extract error context
        error_context = self._extract_error_context(exception, context)
        
        # Classify error
        severity = self._classify_error_severity(exception)
        category = self._classify_error_category(exception)
        
        # Generate user-friendly message
        user_message = self._generate_user_message(exception, severity, category)
        
        # Generate technical details
        technical_details = self._generate_technical_details(exception, error_context)
        
        # Generate suggestions
        suggestions = self._generate_suggestions(exception, category)
        
        # Create error record
        error_record = ErrorRecord(
            error_id=error_id,
            error_type=type(exception).__name__,
            error_message=str(exception),
            severity=severity,
            category=category,
            context=error_context,
            recovery_strategy=self.recovery_manager.get_recovery_strategy(exception).strategy,
            recovery_attempts=0,
            resolved=False,
            resolution_time=None,
            user_message=user_message,
            technical_details=technical_details,
            suggestions=suggestions,
            related_errors=[],
            metadata={}
        )
        
        # Store error record
        self.error_records[error_id] = error_record
        
        # Cleanup old records if needed
        if len(self.error_records) > self.max_error_records:
            self._cleanup_old_records()
        
        # Log error
        if self.log_errors:
            self._log_error(error_record)
        
        # Check for error patterns
        self._check_error_patterns(error_record)
        
        # Find related errors
        error_record.related_errors = self._find_related_errors(error_record)
        
        return error_record
    
    def _extract_error_context(self, exception: Exception, 
                             additional_context: Optional[Dict[str, Any]]) -> ErrorContext:
        """Extract comprehensive error context"""
        # Get current frame information
        frame = inspect.currentframe()
        try:
            # Go up the stack to find the frame where the error occurred
            while frame and frame.f_code.co_filename == __file__:
                frame = frame.f_back
            
            if frame:
                function_name = frame.f_code.co_name
                module_name = frame.f_globals.get('__name__', 'unknown')
                line_number = frame.f_lineno
                
                # Extract arguments and local variables (safely)
                arguments = {}
                local_variables = {}
                
                try:
                    # Get function arguments
                    arg_info = inspect.getargvalues(frame)
                    for arg in arg_info.args:
                        if arg in frame.f_locals:
                            value = frame.f_locals[arg]
                            # Convert complex objects to string representation
                            if isinstance(value, (str, int, float, bool, type(None))):
                                arguments[arg] = value
                            else:
                                arguments[arg] = str(type(value))
                    
                    # Get relevant local variables
                    for var_name, var_value in frame.f_locals.items():
                        if not var_name.startswith('_') and len(local_variables) < 10:
                            if isinstance(var_value, (str, int, float, bool, type(None))):
                                local_variables[var_name] = var_value
                            else:
                                local_variables[var_name] = str(type(var_value))
                
                except Exception:
                    # If we can't extract variables safely, continue without them
                    pass
            else:
                function_name = "unknown"
                module_name = "unknown"
                line_number = 0
                arguments = {}
                local_variables = {}
        
        finally:
            del frame  # Avoid reference cycles
        
        # Get stack trace
        stack_trace = traceback.format_exc()
        
        # Add additional context if provided
        if additional_context:
            local_variables.update(additional_context)
        
        return ErrorContext(
            function_name=function_name,
            module_name=module_name,
            line_number=line_number,
            arguments=arguments,
            local_variables=local_variables,
            stack_trace=stack_trace,
            timestamp=datetime.now(),
            thread_id=str(threading.current_thread().ident),
            process_id=os.getpid() if 'os' in globals() else 0
        )
    
    def _classify_error_severity(self, exception: Exception) -> ErrorSeverity:
        """Classify error severity"""
        # Critical errors that prevent system operation
        critical_errors = (SystemExit, KeyboardInterrupt, MemoryError, SystemError)
        if isinstance(exception, critical_errors):
            return ErrorSeverity.CRITICAL
        
        # High severity errors affecting major functionality
        high_errors = (ConnectionError, TimeoutError, PermissionError, 
                      ImportError, AttributeError, TypeError)
        if isinstance(exception, high_errors):
            return ErrorSeverity.HIGH
        
        # Medium severity errors affecting some functionality
        medium_errors = (ValueError, KeyError, IndexError, FileNotFoundError)
        if isinstance(exception, medium_errors):
            return ErrorSeverity.MEDIUM
        
        # Default to low severity
        return ErrorSeverity.LOW
    
    def _classify_error_category(self, exception: Exception) -> ErrorCategory:
        """Classify error category"""
        error_message = str(exception).lower()
        exception_type = type(exception).__name__.lower()
        
        # Check by exception type
        if isinstance(exception, (ConnectionError, TimeoutError)):
            return ErrorCategory.NETWORK
        elif isinstance(exception, (FileNotFoundError, PermissionError, OSError)):
            return ErrorCategory.FILE_SYSTEM
        elif isinstance(exception, (ValueError, TypeError, AttributeError)):
            return ErrorCategory.VALIDATION
        elif isinstance(exception, (ImportError, ModuleNotFoundError)):
            return ErrorCategory.SYSTEM
        
        # Check by error message content
        if any(keyword in error_message for keyword in ['database', 'sql', 'connection']):
            return ErrorCategory.DATABASE
        elif any(keyword in error_message for keyword in ['auth', 'login', 'password']):
            return ErrorCategory.AUTHENTICATION
        elif any(keyword in error_message for keyword in ['permission', 'access', 'forbidden']):
            return ErrorCategory.AUTHORIZATION
        elif any(keyword in error_message for keyword in ['scan', 'vulnerability', 'target']):
            return ErrorCategory.SCANNING
        elif any(keyword in error_message for keyword in ['ai', 'openai', 'model', 'api']):
            return ErrorCategory.AI_SERVICE
        elif any(keyword in error_message for keyword in ['config', 'setting', 'parameter']):
            return ErrorCategory.CONFIGURATION
        elif any(keyword in error_message for keyword in ['performance', 'timeout', 'memory']):
            return ErrorCategory.PERFORMANCE
        elif any(keyword in error_message for keyword in ['security', 'certificate', 'ssl']):
            return ErrorCategory.SECURITY
        elif any(keyword in error_message for keyword in ['ui', 'interface', 'display']):
            return ErrorCategory.UI
        elif any(keyword in error_message for keyword in ['api', 'endpoint', 'request']):
            return ErrorCategory.API
        
        return ErrorCategory.UNKNOWN
    
    def _generate_user_message(self, exception: Exception, severity: ErrorSeverity, 
                             category: ErrorCategory) -> str:
        """Generate user-friendly error message"""
        error_type = type(exception).__name__
        
        # Category-specific messages
        if category == ErrorCategory.NETWORK:
            return "Network connection issue. Please check your internet connection and try again."
        elif category == ErrorCategory.FILE_SYSTEM:
            return "File access issue. Please check file permissions and path."
        elif category == ErrorCategory.DATABASE:
            return "Database connection issue. Please check database configuration."
        elif category == ErrorCategory.AUTHENTICATION:
            return "Authentication failed. Please check your credentials."
        elif category == ErrorCategory.AUTHORIZATION:
            return "Access denied. You don't have permission to perform this action."
        elif category == ErrorCategory.SCANNING:
            return "Scanning operation failed. Please check target configuration and try again."
        elif category == ErrorCategory.AI_SERVICE:
            return "AI service is temporarily unavailable. Please try again later."
        elif category == ErrorCategory.CONFIGURATION:
            return "Configuration error. Please check application settings."
        elif category == ErrorCategory.PERFORMANCE:
            return "Performance issue detected. The operation may take longer than expected."
        elif category == ErrorCategory.SECURITY:
            return "Security validation failed. Please check security settings."
        elif category == ErrorCategory.UI:
            return "Interface error. Please refresh the application."
        elif category == ErrorCategory.API:
            return "API service error. Please try again later."
        
        # Severity-based fallback messages
        if severity == ErrorSeverity.CRITICAL:
            return "Critical system error. Please restart the application."
        elif severity == ErrorSeverity.HIGH:
            return "A serious error occurred. Some features may not work properly."
        elif severity == ErrorSeverity.MEDIUM:
            return "An error occurred. The operation could not be completed."
        else:
            return "A minor issue was encountered."
    
    def _generate_technical_details(self, exception: Exception, context: ErrorContext) -> str:
        """Generate technical error details"""
        details = []
        
        details.append(f"Error Type: {type(exception).__name__}")
        details.append(f"Error Message: {str(exception)}")
        details.append(f"Module: {context.module_name}")
        details.append(f"Function: {context.function_name}")
        details.append(f"Line: {context.line_number}")
        details.append(f"Timestamp: {context.timestamp.isoformat()}")
        details.append(f"Thread ID: {context.thread_id}")
        
        if context.arguments:
            details.append(f"Arguments: {json.dumps(context.arguments, default=str)}")
        
        details.append(f"Stack Trace:\n{context.stack_trace}")
        
        return "\n".join(details)
    
    def _generate_suggestions(self, exception: Exception, category: ErrorCategory) -> List[str]:
        """Generate suggestions for error resolution"""
        suggestions = []
        
        if category == ErrorCategory.NETWORK:
            suggestions.extend([
                "Check your internet connection",
                "Verify firewall settings",
                "Try again in a few moments",
                "Check if the service is available"
            ])
        elif category == ErrorCategory.FILE_SYSTEM:
            suggestions.extend([
                "Check file permissions",
                "Verify the file path exists",
                "Ensure sufficient disk space",
                "Check for file locks"
            ])
        elif category == ErrorCategory.DATABASE:
            suggestions.extend([
                "Check database connection settings",
                "Verify database service is running",
                "Check database credentials",
                "Ensure database is accessible"
            ])
        elif category == ErrorCategory.AUTHENTICATION:
            suggestions.extend([
                "Verify username and password",
                "Check if account is locked",
                "Ensure account has required permissions",
                "Try password reset if available"
            ])
        elif category == ErrorCategory.SCANNING:
            suggestions.extend([
                "Verify target is accessible",
                "Check scanning permissions",
                "Ensure target configuration is correct",
                "Try with different scan parameters"
            ])
        elif category == ErrorCategory.AI_SERVICE:
            suggestions.extend([
                "Check API key configuration",
                "Verify AI service is available",
                "Check rate limits",
                "Try again later"
            ])
        else:
            suggestions.extend([
                "Try the operation again",
                "Check application logs for more details",
                "Restart the application if the problem persists",
                "Contact support if the issue continues"
            ])
        
        return suggestions
    
    def _log_error(self, error_record: ErrorRecord):
        """Log error with appropriate level"""
        log_message = f"[{error_record.error_id}] {error_record.error_type}: {error_record.error_message}"
        
        if error_record.severity == ErrorSeverity.CRITICAL:
            logger.critical(log_message)
        elif error_record.severity == ErrorSeverity.HIGH:
            logger.error(log_message)
        elif error_record.severity == ErrorSeverity.MEDIUM:
            logger.warning(log_message)
        else:
            logger.info(log_message)
    
    def _check_error_patterns(self, error_record: ErrorRecord):
        """Check for error patterns that might indicate systemic issues"""
        pattern_key = f"{error_record.error_type}:{error_record.category.value}"
        
        if pattern_key not in self.error_patterns:
            self.error_patterns[pattern_key] = []
        
        # Add current error timestamp
        self.error_patterns[pattern_key].append(error_record.context.timestamp)
        
        # Remove old entries outside the pattern window
        cutoff_time = datetime.now() - self.pattern_window
        self.error_patterns[pattern_key] = [
            ts for ts in self.error_patterns[pattern_key] if ts > cutoff_time
        ]
        
        # Check if pattern threshold is exceeded
        if len(self.error_patterns[pattern_key]) >= self.pattern_threshold:
            logger.warning(
                f"Error pattern detected: {pattern_key} occurred {len(self.error_patterns[pattern_key])} times "
                f"in the last {self.pattern_window.total_seconds()/60:.1f} minutes"
            )
    
    def _find_related_errors(self, error_record: ErrorRecord) -> List[str]:
        """Find related errors that might be connected"""
        related = []
        
        for other_id, other_record in self.error_records.items():
            if other_id == error_record.error_id:
                continue
            
            # Check if errors are similar
            similarity_score = 0
            
            # Same error type
            if other_record.error_type == error_record.error_type:
                similarity_score += 3
            
            # Same category
            if other_record.category == error_record.category:
                similarity_score += 2
            
            # Same function
            if other_record.context.function_name == error_record.context.function_name:
                similarity_score += 2
            
            # Recent timestamp (within 5 minutes)
            time_diff = abs((other_record.context.timestamp - error_record.context.timestamp).total_seconds())
            if time_diff < 300:  # 5 minutes
                similarity_score += 1
            
            # If similarity is high enough, consider it related
            if similarity_score >= 4:
                related.append(other_id)
        
        return related[:5]  # Limit to 5 related errors
    
    def _cleanup_old_records(self):
        """Cleanup old error records to prevent memory growth"""
        # Sort by timestamp and keep only the most recent records
        sorted_records = sorted(
            self.error_records.items(),
            key=lambda x: x[1].context.timestamp,
            reverse=True
        )
        
        # Keep only the most recent records
        keep_count = self.max_error_records // 2
        records_to_keep = dict(sorted_records[:keep_count])
        
        self.error_records = records_to_keep
    
    async def attempt_recovery(self, error_record: ErrorRecord, 
                             original_function: Callable, 
                             args: tuple, kwargs: dict) -> Any:
        """Attempt to recover from error"""
        if not self.enable_recovery:
            raise Exception(f"Recovery disabled: {error_record.error_message}")
        
        try:
            result = await self.recovery_manager.execute_recovery(
                error_record, original_function, args, kwargs
            )
            return result
        except Exception as e:
            logger.error(f"Recovery failed for error {error_record.error_id}: {str(e)}")
            raise
    
    def get_error_statistics(self) -> Dict[str, Any]:
        """Get error statistics and insights"""
        if not self.error_records:
            return {"total_errors": 0}
        
        # Count by severity
        severity_counts = {}
        for severity in ErrorSeverity:
            severity_counts[severity.value] = len([
                r for r in self.error_records.values() 
                if r.severity == severity
            ])
        
        # Count by category
        category_counts = {}
        for category in ErrorCategory:
            category_counts[category.value] = len([
                r for r in self.error_records.values() 
                if r.category == category
            ])
        
        # Resolution statistics
        total_errors = len(self.error_records)
        resolved_errors = len([r for r in self.error_records.values() if r.resolved])
        resolution_rate = (resolved_errors / total_errors * 100) if total_errors > 0 else 0
        
        # Most common errors
        error_type_counts = {}
        for record in self.error_records.values():
            error_type_counts[record.error_type] = error_type_counts.get(record.error_type, 0) + 1
        
        most_common_errors = sorted(
            error_type_counts.items(), 
            key=lambda x: x[1], 
            reverse=True
        )[:5]
        
        return {
            "total_errors": total_errors,
            "resolved_errors": resolved_errors,
            "resolution_rate": resolution_rate,
            "severity_breakdown": severity_counts,
            "category_breakdown": category_counts,
            "most_common_errors": most_common_errors,
            "error_patterns": len(self.error_patterns),
            "recovery_enabled": self.enable_recovery
        }
    
    def export_error_report(self, file_path: str, include_resolved: bool = True):
        """Export error report to file"""
        errors_to_export = self.error_records.values()
        
        if not include_resolved:
            errors_to_export = [r for r in errors_to_export if not r.resolved]
        
        report_data = {
            "export_timestamp": datetime.now().isoformat(),
            "total_errors": len(errors_to_export),
            "statistics": self.get_error_statistics(),
            "errors": [asdict(error) for error in errors_to_export]
        }
        
        with open(file_path, 'w') as f:
            json.dump(report_data, f, indent=2, default=str)


def error_handler(category: ErrorCategory = ErrorCategory.UNKNOWN,
                 severity: Optional[ErrorSeverity] = None,
                 enable_recovery: bool = True,
                 custom_message: Optional[str] = None):
    """Decorator for automatic error handling"""
    def decorator(func):
        @functools.wraps(func)
        async def async_wrapper(*args, **kwargs):
            handler = ErrorHandler()
            
            try:
                return await func(*args, **kwargs)
            except Exception as e:
                # Create error record
                error_record = handler.handle_error(e, {
                    "function": func.__name__,
                    "args": str(args)[:200],  # Limit length
                    "kwargs": str(kwargs)[:200]
                })
                
                # Override category and severity if specified
                if category != ErrorCategory.UNKNOWN:
                    error_record.category = category
                if severity:
                    error_record.severity = severity
                if custom_message:
                    error_record.user_message = custom_message
                
                # Attempt recovery if enabled
                if enable_recovery:
                    try:
                        return await handler.attempt_recovery(
                            error_record, func, args, kwargs
                        )
                    except Exception:
                        pass  # Recovery failed, re-raise original exception
                
                # Re-raise with enhanced error information
                raise Exception(error_record.user_message) from e
        
        @functools.wraps(func)
        def sync_wrapper(*args, **kwargs):
            handler = ErrorHandler()
            
            try:
                return func(*args, **kwargs)
            except Exception as e:
                # Create error record
                error_record = handler.handle_error(e, {
                    "function": func.__name__,
                    "args": str(args)[:200],
                    "kwargs": str(kwargs)[:200]
                })
                
                # Override category and severity if specified
                if category != ErrorCategory.UNKNOWN:
                    error_record.category = category
                if severity:
                    error_record.severity = severity
                if custom_message:
                    error_record.user_message = custom_message
                
                # Re-raise with enhanced error information
                raise Exception(error_record.user_message) from e
        
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
    
    return decorator


# Global error handler instance
global_error_handler = ErrorHandler()

import os