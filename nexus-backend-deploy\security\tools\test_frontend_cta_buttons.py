#!/usr/bin/env python3
"""
🔘 Frontend CTA Button Functionality Test
Test what actually happens when users click Execute, Configure, and Info buttons
"""

import requests
import json
import time
from datetime import datetime
from typing import Dict, List, Any

class FrontendCTAButtonTester:
    def __init__(self):
        self.backend_url = "http://localhost:8000"
        self.frontend_url = "http://localhost:1420"
        
    def test_execute_button_flow(self, tool_id: str, tool_name: str) -> Dict[str, Any]:
        """Test the complete execute button flow"""
        print(f"\n▶️ TESTING EXECUTE BUTTON: {tool_name}")
        print("-" * 50)
        
        results = {
            'button_enabled': False,
            'api_call_works': False,
            'scan_creation': False,
            'scan_tracking': False,
            'result_retrieval': False,
            'error_handling': False,
            'user_experience': 'Unknown',
            'issues': []
        }
        
        # 1. Check if execute button should be enabled
        try:
            response = requests.get(f"{self.backend_url}/api/tools/status", timeout=5)
            if response.status_code == 200:
                status_data = response.json()
                tool_status = status_data.get('data', {}).get(tool_id, {})
                if tool_status.get('status') == 'available':
                    results['button_enabled'] = True
                    print(f"✅ Execute button should be enabled (tool available)")
                else:
                    print(f"❌ Execute button should be disabled (tool unavailable)")
                    results['issues'].append("Tool not available for execution")
        except Exception as e:
            print(f"❌ Status check failed: {e}")
            results['issues'].append(f"Status check error: {e}")
        
        # 2. Test basic execute API call
        if results['button_enabled']:
            try:
                execution_config = {
                    "tool_name": tool_id,
                    "target": "127.0.0.1",
                    "scan_type": "basic",
                    "options": {"timeout": 30}
                }
                
                response = requests.post(
                    f"{self.backend_url}/api/tools/execute", 
                    json=execution_config,
                    timeout=10
                )
                
                if response.status_code == 200:
                    execution_data = response.json()
                    results['api_call_works'] = True
                    scan_id = execution_data.get('data', {}).get('scan_id')
                    print(f"✅ Execute API call successful")
                    print(f"   Scan ID: {scan_id}")
                    
                    # 3. Test scan creation and tracking
                    if scan_id:
                        results['scan_creation'] = True
                        results['scan_tracking'] = self._test_scan_tracking(scan_id)
                        results['result_retrieval'] = self._test_result_retrieval(scan_id)
                    
                elif response.status_code == 400:
                    print(f"⚠️ Execute API returned validation error")
                    print(f"   Response: {response.text}")
                    results['error_handling'] = True
                    results['issues'].append("Validation error in execution")
                else:
                    print(f"❌ Execute API failed: {response.status_code}")
                    print(f"   Response: {response.text}")
                    results['issues'].append(f"Execute API failed: {response.status_code}")
                    
            except Exception as e:
                print(f"❌ Execute API error: {e}")
                results['issues'].append(f"Execute API error: {e}")
        
        # 4. Determine user experience
        if results['api_call_works'] and results['scan_creation']:
            if results['scan_tracking'] and results['result_retrieval']:
                results['user_experience'] = 'Excellent - Full workflow works'
            elif results['scan_tracking']:
                results['user_experience'] = 'Good - Can track progress'
            else:
                results['user_experience'] = 'Fair - Basic execution works'
        elif results['error_handling']:
            results['user_experience'] = 'Poor - Shows validation errors'
        else:
            results['user_experience'] = 'Broken - Execute fails'
        
        print(f"🎯 User Experience: {results['user_experience']}")
        return results
    
    def _test_scan_tracking(self, scan_id: str) -> bool:
        """Test if we can track scan progress"""
        try:
            # Try different scan tracking endpoints
            tracking_endpoints = [
                f'/api/scans/{scan_id}',
                f'/api/scans/{scan_id}/status', 
                f'/api/scans/{scan_id}/progress'
            ]
            
            for endpoint in tracking_endpoints:
                response = requests.get(f"{self.backend_url}{endpoint}", timeout=5)
                if response.status_code == 200:
                    print(f"   ✅ Scan tracking works: {endpoint}")
                    return True
                elif response.status_code == 404:
                    print(f"   ⚠️ Tracking endpoint not found: {endpoint}")
                else:
                    print(f"   ❌ Tracking failed: {endpoint} ({response.status_code})")
            
            return False
        except Exception as e:
            print(f"   ❌ Scan tracking error: {e}")
            return False
    
    def _test_result_retrieval(self, scan_id: str) -> bool:
        """Test if we can retrieve scan results"""
        try:
            result_endpoints = [
                f'/api/scans/{scan_id}/results',
                f'/api/scans/{scan_id}/logs',
                f'/api/scans/{scan_id}/report'
            ]
            
            for endpoint in result_endpoints:
                response = requests.get(f"{self.backend_url}{endpoint}", timeout=5)
                if response.status_code == 200:
                    print(f"   ✅ Result retrieval works: {endpoint}")
                    return True
                elif response.status_code == 404:
                    print(f"   ⚠️ Result endpoint not found: {endpoint}")
                else:
                    print(f"   ❌ Result retrieval failed: {endpoint} ({response.status_code})")
            
            return False
        except Exception as e:
            print(f"   ❌ Result retrieval error: {e}")
            return False
    
    def test_configure_button_flow(self, tool_id: str, tool_name: str) -> Dict[str, Any]:
        """Test the configure button functionality"""
        print(f"\n⚙️ TESTING CONFIGURE BUTTON: {tool_name}")
        print("-" * 50)
        
        results = {
            'config_api_exists': False,
            'options_available': False,
            'presets_available': False,
            'configuration_save': False,
            'configuration_load': False,
            'user_experience': 'Unknown',
            'available_options': [],
            'issues': []
        }
        
        # Test configuration endpoints
        config_endpoints = {
            'config': f'/api/tools/{tool_id}/config',
            'options': f'/api/tools/{tool_id}/options', 
            'presets': f'/api/tools/{tool_id}/presets'
        }
        
        for endpoint_type, endpoint in config_endpoints.items():
            try:
                response = requests.get(f"{self.backend_url}{endpoint}", timeout=5)
                if response.status_code == 200:
                    config_data = response.json()
                    print(f"✅ {endpoint_type.capitalize()} endpoint works")
                    
                    if endpoint_type == 'config':
                        results['config_api_exists'] = True
                    elif endpoint_type == 'options':
                        results['options_available'] = True
                        results['available_options'] = list(config_data.get('data', {}).keys())
                        print(f"   Available options: {results['available_options']}")
                    elif endpoint_type == 'presets':
                        results['presets_available'] = True
                        preset_count = len(config_data.get('data', []))
                        print(f"   Available presets: {preset_count}")
                        
                elif response.status_code == 404:
                    print(f"⚠️ {endpoint_type.capitalize()} endpoint not implemented")
                    results['issues'].append(f"{endpoint_type} endpoint missing")
                else:
                    print(f"❌ {endpoint_type.capitalize()} endpoint failed: {response.status_code}")
                    results['issues'].append(f"{endpoint_type} endpoint failed")
                    
            except Exception as e:
                print(f"❌ {endpoint_type.capitalize()} endpoint error: {e}")
                results['issues'].append(f"{endpoint_type} endpoint error")
        
        # Test configuration save/load if config API exists
        if results['config_api_exists']:
            results['configuration_save'] = self._test_config_save(tool_id)
            results['configuration_load'] = self._test_config_load(tool_id)
        
        # Determine user experience
        if results['config_api_exists'] and results['options_available']:
            if results['presets_available']:
                results['user_experience'] = 'Excellent - Full configuration support'
            else:
                results['user_experience'] = 'Good - Custom configuration works'
        elif any([results['config_api_exists'], results['options_available'], results['presets_available']]):
            results['user_experience'] = 'Fair - Partial configuration support'
        else:
            results['user_experience'] = 'Broken - No configuration support'
        
        print(f"🎯 User Experience: {results['user_experience']}")
        return results
    
    def _test_config_save(self, tool_id: str) -> bool:
        """Test configuration saving"""
        try:
            config_data = {
                "scan_type": "tcp_connect",
                "timeout": 30,
                "rate_limit": 100
            }
            
            response = requests.post(
                f"{self.backend_url}/api/tools/{tool_id}/config",
                json=config_data,
                timeout=5
            )
            
            if response.status_code in [200, 201]:
                print(f"   ✅ Configuration save works")
                return True
            else:
                print(f"   ❌ Configuration save failed: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"   ❌ Configuration save error: {e}")
            return False
    
    def _test_config_load(self, tool_id: str) -> bool:
        """Test configuration loading"""
        try:
            response = requests.get(f"{self.backend_url}/api/tools/{tool_id}/config", timeout=5)
            if response.status_code == 200:
                config_data = response.json()
                if config_data.get('data'):
                    print(f"   ✅ Configuration load works")
                    return True
            
            print(f"   ❌ Configuration load failed")
            return False
            
        except Exception as e:
            print(f"   ❌ Configuration load error: {e}")
            return False
    
    def test_info_button_flow(self, tool_id: str, tool_name: str) -> Dict[str, Any]:
        """Test the info button functionality"""
        print(f"\n ℹ️ TESTING INFO BUTTON: {tool_name}")
        print("-" * 50)
        
        results = {
            'info_api_exists': False,
            'tool_description': None,
            'capabilities_listed': False,
            'usage_examples': False,
            'help_documentation': False,
            'user_experience': 'Unknown',
            'info_quality': 'Unknown',
            'issues': []
        }
        
        # Test info API
        try:
            response = requests.get(f"{self.backend_url}/api/tools/{tool_id}/info", timeout=5)
            if response.status_code == 200:
                info_data = response.json()
                results['info_api_exists'] = True
                tool_info = info_data.get('data', {})
                
                print(f"✅ Info API works")
                
                # Check info quality
                description = tool_info.get('description', '')
                if description:
                    results['tool_description'] = description
                    print(f"   Description: {description[:100]}...")
                
                capabilities = tool_info.get('capabilities', [])
                if capabilities:
                    results['capabilities_listed'] = True
                    print(f"   Capabilities: {len(capabilities)} listed")
                
                examples = tool_info.get('examples', [])
                if examples:
                    results['usage_examples'] = True
                    print(f"   Usage examples: {len(examples)} provided")
                
                help_url = tool_info.get('help_url', '')
                if help_url:
                    results['help_documentation'] = True
                    print(f"   Help documentation: {help_url}")
                
                # Determine info quality
                quality_score = sum([
                    bool(description),
                    results['capabilities_listed'],
                    results['usage_examples'],
                    results['help_documentation']
                ])
                
                if quality_score >= 3:
                    results['info_quality'] = 'Excellent - Comprehensive information'
                elif quality_score >= 2:
                    results['info_quality'] = 'Good - Adequate information'
                elif quality_score >= 1:
                    results['info_quality'] = 'Fair - Basic information'
                else:
                    results['info_quality'] = 'Poor - Minimal information'
                    
            elif response.status_code == 404:
                print(f"❌ Info API not implemented")
                results['issues'].append("Info API not implemented")
            else:
                print(f"❌ Info API failed: {response.status_code}")
                results['issues'].append(f"Info API failed: {response.status_code}")
                
        except Exception as e:
            print(f"❌ Info API error: {e}")
            results['issues'].append(f"Info API error: {e}")
        
        # Determine user experience
        if results['info_api_exists']:
            results['user_experience'] = f"Working - {results['info_quality']}"
        else:
            results['user_experience'] = 'Broken - No information available'
        
        print(f"🎯 User Experience: {results['user_experience']}")
        print(f"📊 Info Quality: {results['info_quality']}")
        return results
    
    def test_frontend_ui_behavior(self) -> Dict[str, Any]:
        """Test frontend UI behavior and error handling"""
        print(f"\n🖥️ TESTING FRONTEND UI BEHAVIOR")
        print("-" * 50)
        
        results = {
            'loading_states': False,
            'error_handling': False,
            'button_states': False,
            'user_feedback': False,
            'navigation': False,
            'issues': []
        }
        
        # Test if frontend properly handles different backend states
        
        # 1. Test loading states (check if skeleton loaders work)
        print("1. Testing loading states...")
        try:
            # Simulate slow backend by checking if frontend handles delays
            response = requests.get(f"{self.backend_url}/api/tools/status", timeout=1)
            if response.status_code == 200:
                print("   ✅ Backend responds quickly - loading states may be brief")
                results['loading_states'] = True
        except requests.Timeout:
            print("   ⚠️ Backend slow - frontend should show loading states")
        except Exception as e:
            print(f"   ❌ Backend error - frontend should handle gracefully")
        
        # 2. Test error handling (simulate backend errors)
        print("2. Testing error handling...")
        try:
            # Test with invalid API call
            response = requests.get(f"{self.backend_url}/api/invalid/endpoint", timeout=5)
            if response.status_code == 404:
                print("   ✅ Backend returns proper 404 errors")
                results['error_handling'] = True
        except Exception as e:
            print(f"   ❌ Error handling test failed: {e}")
        
        # 3. Test button states
        print("3. Testing button states...")
        try:
            response = requests.get(f"{self.backend_url}/api/tools/status", timeout=5)
            if response.status_code == 200:
                status_data = response.json()
                tools_data = status_data.get('data', {})
                
                available_count = sum(1 for tool in tools_data.values() if tool.get('status') == 'available')
                total_count = len(tools_data)
                
                print(f"   Tools available: {available_count}/{total_count}")
                if available_count > 0:
                    print("   ✅ Some execute buttons should be enabled")
                    results['button_states'] = True
                else:
                    print("   ⚠️ All execute buttons should be disabled")
                    
        except Exception as e:
            print(f"   ❌ Button state test failed: {e}")
        
        return results
    
    def run_comprehensive_cta_test(self):
        """Run comprehensive CTA button testing"""
        start_time = datetime.now()
        
        print("🔘 COMPREHENSIVE CTA BUTTON TESTING")
        print("=" * 80)
        print(f"Test started: {start_time.strftime('%Y-%m-%d %H:%M:%S')}")
        
        # Get available tools
        try:
            response = requests.get(f"{self.backend_url}/api/tools/available", timeout=5)
            if response.status_code == 200:
                tools_data = response.json()
                available_tools = tools_data.get('data', [])
                print(f"\n📋 Testing CTA buttons for {len(available_tools)} tools")
            else:
                print(f"❌ Failed to get available tools: {response.status_code}")
                return
        except Exception as e:
            print(f"❌ Error getting available tools: {e}")
            return
        
        # Test each tool's CTA buttons
        tool_results = {}
        for tool in available_tools:
            tool_id = tool.get('id')
            tool_name = tool.get('name')
            if tool_id and tool_name:
                tool_results[tool_id] = {
                    'execute': self.test_execute_button_flow(tool_id, tool_name),
                    'configure': self.test_configure_button_flow(tool_id, tool_name),
                    'info': self.test_info_button_flow(tool_id, tool_name)
                }
        
        # Test frontend UI behavior
        ui_results = self.test_frontend_ui_behavior()
        
        # Generate comprehensive report
        self._generate_cta_report(tool_results, ui_results, start_time)
        
        return {
            'tools': tool_results,
            'ui_behavior': ui_results
        }
    
    def _generate_cta_report(self, tool_results, ui_results, start_time):
        """Generate comprehensive CTA button report"""
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        print(f"\n\n🔘 COMPREHENSIVE CTA BUTTON REPORT")
        print("=" * 80)
        
        # Summary statistics
        total_tools = len(tool_results)
        execute_working = sum(1 for r in tool_results.values() if r['execute']['api_call_works'])
        configure_working = sum(1 for r in tool_results.values() if r['configure']['config_api_exists'])
        info_working = sum(1 for r in tool_results.values() if r['info']['info_api_exists'])
        
        print(f"Test Duration: {duration:.2f} seconds")
        print(f"Tools Tested: {total_tools}")
        print(f"Execute Buttons Working: {execute_working}/{total_tools} ({execute_working/total_tools*100:.1f}%)")
        print(f"Configure Buttons Working: {configure_working}/{total_tools} ({configure_working/total_tools*100:.1f}%)")
        print(f"Info Buttons Working: {info_working}/{total_tools} ({info_working/total_tools*100:.1f}%)")
        
        # Detailed button analysis
        print(f"\n🔘 DETAILED BUTTON ANALYSIS")
        print("-" * 60)
        
        for tool_id, results in tool_results.items():
            print(f"\n🔧 {tool_id.upper()}")
            
            # Execute button
            execute_status = "✅" if results['execute']['api_call_works'] else "❌"
            print(f"   Execute: {execute_status} {results['execute']['user_experience']}")
            
            # Configure button  
            config_status = "✅" if results['configure']['config_api_exists'] else "❌"
            print(f"   Configure: {config_status} {results['configure']['user_experience']}")
            
            # Info button
            info_status = "✅" if results['info']['info_api_exists'] else "❌"
            print(f"   Info: {info_status} {results['info']['user_experience']}")
        
        # What should work vs what actually works
        print(f"\n🎯 EXPECTED VS ACTUAL BEHAVIOR")
        print("-" * 60)
        
        print(f"EXECUTE BUTTONS:")
        print(f"  Expected: All 5 tools should be executable with progress tracking")
        print(f"  Actual: {execute_working}/5 basic execution works, limited tracking")
        
        print(f"CONFIGURE BUTTONS:")
        print(f"  Expected: Rich configuration UI with presets and custom options")
        print(f"  Actual: {configure_working}/5 configuration endpoints exist")
        
        print(f"INFO BUTTONS:")
        print(f"  Expected: Comprehensive tool information and help")
        print(f"  Actual: {info_working}/5 info endpoints implemented")
        
        # User experience assessment
        excellent_ux = sum(1 for r in tool_results.values() 
                          if 'Excellent' in r['execute']['user_experience'])
        good_ux = sum(1 for r in tool_results.values() 
                     if 'Good' in r['execute']['user_experience'])
        
        print(f"\n📊 USER EXPERIENCE SUMMARY")
        print("-" * 60)
        print(f"Excellent UX: {excellent_ux}/{total_tools} tools")
        print(f"Good UX: {good_ux}/{total_tools} tools")
        print(f"Poor/Broken UX: {total_tools - excellent_ux - good_ux}/{total_tools} tools")
        
        # Critical issues
        print(f"\n🚨 CRITICAL CTA ISSUES")
        print("-" * 60)
        
        if configure_working == 0:
            print("• NO CONFIGURE BUTTONS WORK - Users cannot customize tool settings")
        if info_working < total_tools:
            print(f"• {total_tools - info_working} INFO BUTTONS DON'T WORK - Users lack tool guidance")
        if execute_working < total_tools:
            print(f"• {total_tools - execute_working} EXECUTE BUTTONS FAIL - Core functionality broken")
        
        # Recommendations
        print(f"\n💡 IMMEDIATE FIXES NEEDED")
        print("-" * 60)
        print("1. Implement configuration endpoints for all tools")
        print("2. Add info endpoints for sqlmap, masscan, gobuster")
        print("3. Implement scan tracking and result retrieval")
        print("4. Add progress indicators and status updates")
        print("5. Improve error handling and user feedback")

def main():
    """Main test execution"""
    tester = FrontendCTAButtonTester()
    results = tester.run_comprehensive_cta_test()
    return results

if __name__ == "__main__":
    main()