/**
 * Sidebar Navigation Component - Clean Version
 * Navigation for NexusScan Desktop Application
 */
import React from 'react'
import { Link } from 'react-router-dom'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
  BarChart3,
  Briefcase,
  Network,
  Globe,
  Shield,
  Key,
  Lock,
  Zap,
  Brain,
  Target,
  Activity,
  FileText,
  Settings,
  ChevronDown,
  ChevronRight,
  Layers,
  Car
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { useBackendStore } from '@/stores/backend-store'

interface NavItem {
  id: string
  label: string
  path: string
  icon: React.ReactNode
  badge?: string | number
  children?: NavItem[]
}

interface SidebarProps {
  isOpen: boolean
  currentPath: string
  className?: string
}

export function Sidebar({ isOpen, currentPath, className }: SidebarProps) {
  const { status: backendStatus } = useBackendStore()
  const [expandedSections, setExpandedSections] = React.useState<Set<string>>(new Set(['tools']))
  
  const navigationItems: NavItem[] = [
    {
      id: 'dashboard',
      label: 'Dashboard',
      path: '/dashboard',
      icon: <BarChart3 className="h-4 w-4" />,
    },
    {
      id: 'campaigns',
      label: 'Campaigns',
      path: '/campaigns',
      icon: <Briefcase className="h-4 w-4" />,
    },
    {
      id: 'tools',
      label: 'Security Tools',
      path: '/tools',
      icon: <Layers className="h-4 w-4" />,
      badge: backendStatus.tools?.operational || 0,
      children: [
        {
          id: 'network-scanning',
          label: 'Network Scanning',
          path: '/tools/network-scanning',
          icon: <Network className="h-4 w-4" />,
        },
        {
          id: 'web-testing',
          label: 'Web Testing',
          path: '/tools/web-testing',
          icon: <Globe className="h-4 w-4" />,
        },
        {
          id: 'vulnerability-assessment',
          label: 'Vulnerability Assessment',
          path: '/tools/vulnerability-assessment',
          icon: <Shield className="h-4 w-4" />,
        }
      ]
    },
    {
      id: 'ferrari',
      label: 'Ferrari AI',
      path: '/ferrari',
      icon: <Car className="h-4 w-4" />,
      children: [
        {
          id: 'orchestrator',
          label: 'Multi-Stage Orchestrator',
          path: '/ferrari/orchestrator',
          icon: <Brain className="h-4 w-4" />,
        },
        {
          id: 'creative-exploits',
          label: 'Creative Exploits',
          path: '/ferrari/creative-exploits',
          icon: <Target className="h-4 w-4" />,
        }
      ]
    },
    {
      id: 'reports',
      label: 'Reports',
      path: '/reports',
      icon: <FileText className="h-4 w-4" />,
    },
    {
      id: 'settings',
      label: 'Settings',
      path: '/settings',
      icon: <Settings className="h-4 w-4" />,
    }
  ]
  
  const toggleSection = (sectionId: string) => {
    const newExpanded = new Set(expandedSections)
    if (newExpanded.has(sectionId)) {
      newExpanded.delete(sectionId)
    } else {
      newExpanded.add(sectionId)
    }
    setExpandedSections(newExpanded)
  }
  
  const isItemActive = (item: NavItem): boolean => {
    return currentPath.startsWith(item.path) && item.path !== '/'
  }
  
  const renderNavItem = (item: NavItem, level: number = 0) => {
    const isActive = isItemActive(item)
    const hasChildren = item.children && item.children.length > 0
    const isExpanded = expandedSections.has(item.id)
    const indentClass = level > 0 ? 'ml-6' : ''
    
    return (
      <div key={item.id} className={cn('space-y-1', indentClass)}>
        {hasChildren ? (
          <Button
            variant="ghost"
            onClick={() => toggleSection(item.id)}
            className={cn(
              'w-full justify-start',
              isActive ? 'bg-accent' : ''
            )}
          >
            <div className="flex items-center gap-3 flex-1">
              {item.icon}
              <span className="flex-1 text-left">{item.label}</span>
              {item.badge && (
                <Badge variant="secondary" className="ml-auto">
                  {item.badge}
                </Badge>
              )}
            </div>
            {isExpanded ? (
              <ChevronDown className="h-4 w-4 ml-2" />
            ) : (
              <ChevronRight className="h-4 w-4 ml-2" />
            )}
          </Button>
        ) : (
          <Link to={item.path}>
            <Button
              variant="ghost"
              className={cn(
                'w-full justify-start',
                isActive ? 'bg-accent' : ''
              )}
            >
              <div className="flex items-center gap-3 w-full">
                {item.icon}
                <span className="flex-1 text-left">{item.label}</span>
                {item.badge && (
                  <Badge variant="secondary" className="ml-auto">
                    {item.badge}
                  </Badge>
                )}
              </div>
            </Button>
          </Link>
        )}
        
        {hasChildren && isExpanded && (
          <div className="ml-4 space-y-1">
            {item.children?.map(child => renderNavItem(child, level + 1))}
          </div>
        )}
      </div>
    )
  }
  
  if (!isOpen) {
    return null
  }
  
  return (
    <aside className={cn(
      'w-64 flex-shrink-0 border-r border-border bg-card',
      'flex flex-col h-full',
      className
    )}>
      {/* Logo */}
      <div className="p-4 border-b border-border">
        <div className="flex items-center gap-3">
          <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
            <span className="text-white font-bold text-sm">N</span>
          </div>
          <div>
            <h2 className="font-semibold text-foreground">NexusScan</h2>
            <p className="text-xs text-muted-foreground">Desktop</p>
          </div>
        </div>
      </div>
      
      {/* Navigation */}
      <nav className="flex-1 p-4 space-y-2 overflow-y-auto">
        {navigationItems.map(item => renderNavItem(item))}
      </nav>
      
      {/* Status */}
      <div className="p-4 border-t border-border">
        <div className="space-y-2">
          <div className="flex items-center justify-between text-xs">
            <span className="text-muted-foreground">Backend</span>
            <Badge 
              variant={backendStatus.connected ? 'default' : 'destructive'}
              className="text-xs"
            >
              {backendStatus.connected ? 'Online' : 'Offline'}
            </Badge>
          </div>
          
          {backendStatus.tools && (
            <div className="flex items-center justify-between text-xs">
              <span className="text-muted-foreground">Tools</span>
              <Badge variant="outline" className="text-xs">
                {backendStatus.tools.operational}/{backendStatus.tools.total}
              </Badge>
            </div>
          )}
        </div>
      </div>
    </aside>
  )
}