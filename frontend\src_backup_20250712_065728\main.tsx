import React from 'react';
import ReactD<PERSON> from 'react-dom/client';
import { <PERSON><PERSON>er<PERSON>outer } from 'react-router-dom';
import { Toaster } from 'sonner';
import App from './App';
import './index.css';

// Import Electron API types
import type { ElectronAPI } from '@types/electron';

// Declare global Electron API
declare global {
  interface Window {
    electronAPI: ElectronAPI;
  }
}

/**
 * Initialize NexusScan Desktop Application
 */
async function initializeApp() {
  try {
    console.log('🚀 Initializing NexusScan Desktop...');
    
    // Check if we're running in Electron
    const isElectron = window.electronAPI !== undefined;
    console.log(`📱 Runtime Environment: ${isElectron ? 'Electron Desktop' : 'Browser'}`);
    
    if (isElectron) {
      // Get app information
      const [version, platform, arch] = await Promise.all([
        window.electronAPI.app.getVersion(),
        window.electronAPI.app.getPlatform(),
        window.electronAPI.app.getArch()
      ]);
      
      console.log(`📋 App Version: ${version}`);
      console.log(`💻 Platform: ${platform} (${arch})`);
      
      // Initialize backend connection
      console.log('🔗 Connecting to backend...');
      try {
        const backendStatus = await window.electronAPI.backend.getStatus();
        console.log('📡 Backend Status:', backendStatus);
        
        if (!backendStatus.connected) {
          console.log('🔄 Attempting backend connection...');
          const connected = await window.electronAPI.backend.connect();
          console.log(`🔗 Backend Connection: ${connected ? 'Success' : 'Failed'}`);
        }
      } catch (error) {
        console.warn('⚠️ Backend connection error:', error);
      }
      
      // Check WSL status on Windows
      if (platform === 'win32') {
        try {
          const wslStatus = await window.electronAPI.wsl.getStatus();
          console.log('🐧 WSL Status:', wslStatus);
        } catch (error) {
          console.warn('⚠️ WSL check error:', error);
        }
      }
    }
    
    // Mark app as ready
    console.log('✅ NexusScan Desktop initialized successfully');
    
    // Dispatch app ready event
    window.dispatchEvent(new CustomEvent('appReady'));
    
  } catch (error) {
    console.error('❌ Failed to initialize NexusScan Desktop:', error);
    
    // Still dispatch app ready to show the app even if initialization fails
    window.dispatchEvent(new CustomEvent('appReady'));
  }
}

/**
 * Render the React application
 */
function renderApp() {
  const root = ReactDOM.createRoot(
    document.getElementById('root') as HTMLElement
  );
  
  root.render(
    <React.StrictMode>
      <BrowserRouter>
        <App />
        <Toaster 
          position="top-right"
          toastOptions={{
            duration: 4000,
            style: {
              background: 'hsl(var(--card))',
              color: 'hsl(var(--card-foreground))',
              border: '1px solid hsl(var(--border))',
            },
          }}
        />
      </BrowserRouter>
    </React.StrictMode>
  );
}

/**
 * Handle unhandled errors
 */
function setupErrorHandling() {
  // Handle uncaught JavaScript errors
  window.addEventListener('error', (event) => {
    console.error('🚨 Uncaught error:', event.error);
    
    // Report to Electron main process if available
    if (window.electronAPI) {
      window.electronAPI.notification.show({
        title: 'Application Error',
        body: 'An unexpected error occurred. Check the console for details.'
      }).catch(() => {
        // Fallback if notification fails
        console.error('Failed to show error notification');
      });
    }
  });
  
  // Handle unhandled promise rejections
  window.addEventListener('unhandledrejection', (event) => {
    console.error('🚨 Unhandled promise rejection:', event.reason);
    
    // Prevent the default browser behavior
    event.preventDefault();
  });
  
  // Handle React errors (will be caught by Error Boundaries)
  console.log('🛡️ Error handling configured');
}

/**
 * Setup development mode helpers
 */
function setupDevelopmentMode() {
  if (process.env.NODE_ENV === 'development') {
    // Enable React DevTools
    if (window.__REACT_DEVTOOLS_GLOBAL_HOOK__) {
      console.log('🔧 React DevTools available');
    }
    
    // Add global helpers for debugging
    (window as any).nexusScan = {
      electronAPI: window.electronAPI,
      version: __APP_VERSION__,
      backend: __BACKEND_URL__,
      
      // Helper functions
      async getBackendStatus() {
        if (window.electronAPI) {
          return await window.electronAPI.backend.getStatus();
        }
        return null;
      },
      
      async getAvailableTools() {
        if (window.electronAPI) {
          return await window.electronAPI.backend.getAvailableTools();
        }
        return [];
      },
      
      async getWSLStatus() {
        if (window.electronAPI) {
          return await window.electronAPI.wsl.getStatus();
        }
        return null;
      }
    };
    
    console.log('🛠️ Development mode helpers available at window.nexusScan');
  }
}

/**
 * Main application bootstrap
 */
async function bootstrap() {
  try {
    // Setup error handling first
    setupErrorHandling();
    
    // Setup development mode helpers
    setupDevelopmentMode();
    
    // Render the React app
    renderApp();
    
    // Initialize app services
    await initializeApp();
    
    console.log('🎉 NexusScan Desktop ready!');
    
  } catch (error) {
    console.error('💥 Bootstrap failed:', error);
    
    // Try to render the app anyway
    try {
      renderApp();
    } catch (renderError) {
      console.error('💥 Failed to render app:', renderError);
      
      // Last resort: show error message
      document.body.innerHTML = `
        <div style="
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          height: 100vh;
          background: #0f172a;
          color: #e2e8f0;
          font-family: system-ui, sans-serif;
          text-align: center;
          padding: 2rem;
        ">
          <h1 style="color: #ef4444; margin-bottom: 1rem;">Application Error</h1>
          <p style="margin-bottom: 2rem;">Failed to start NexusScan Desktop</p>
          <details style="max-width: 600px;">
            <summary style="cursor: pointer; margin-bottom: 1rem;">Error Details</summary>
            <pre style="
              background: #1e293b;
              padding: 1rem;
              border-radius: 0.5rem;
              text-align: left;
              overflow: auto;
              font-size: 0.875rem;
            ">${error.message}\n\n${error.stack}</pre>
          </details>
          <button onclick="window.location.reload()" style="
            background: #3b82f6;
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 0.5rem;
            cursor: pointer;
            margin-top: 2rem;
          ">Restart Application</button>
        </div>
      `;
    }
  }
}

// Start the application
bootstrap();