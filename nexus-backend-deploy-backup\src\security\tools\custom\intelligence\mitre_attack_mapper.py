#!/usr/bin/env python3
"""
MITRE ATT&CK Integration and Technique Mapping for NexusScan Desktop
Comprehensive MITRE ATT&CK framework integration with technique mapping and threat modeling.
"""

import asyncio
import logging
import json
import requests
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Set, Tuple
from dataclasses import dataclass, asdict, field
from enum import Enum
from pathlib import Path

from core.config import Config
from core.database import DatabaseManager
from ai.ai_service import AIServiceManager
from ai.services import AnalysisRequest

logger = logging.getLogger(__name__)


class AttackTactic(Enum):
    """MITRE ATT&CK Tactics"""
    RECONNAISSANCE = "reconnaissance"
    RESOURCE_DEVELOPMENT = "resource-development"
    INITIAL_ACCESS = "initial-access"
    EXECUTION = "execution"
    PERSISTENCE = "persistence"
    PRIVILEGE_ESCALATION = "privilege-escalation"
    DEFENSE_EVASION = "defense-evasion"
    CREDENTIAL_ACCESS = "credential-access"
    DISCOVERY = "discovery"
    LATERAL_MOVEMENT = "lateral-movement"
    COLLECTION = "collection"
    COMMAND_AND_CONTROL = "command-and-control"
    EXFILTRATION = "exfiltration"
    IMPACT = "impact"


class DetectionDifficulty(Enum):
    """Detection difficulty levels"""
    EASY = "easy"
    MODERATE = "moderate"
    HARD = "hard"
    VERY_HARD = "very_hard"


class TechniqueStatus(Enum):
    """Status of technique in current assessment"""
    NOT_ATTEMPTED = "not_attempted"
    IN_PROGRESS = "in_progress"
    SUCCESSFUL = "successful"
    FAILED = "failed"
    BLOCKED = "blocked"
    DETECTED = "detected"


@dataclass
class MitreSubTechnique:
    """MITRE ATT&CK Sub-technique"""
    sub_technique_id: str
    name: str
    description: str
    platforms: List[str]
    permissions_required: List[str]
    data_sources: List[str]
    defense_bypassed: List[str]
    supports_remote: bool
    system_requirements: List[str]


@dataclass
class MitreTechnique:
    """MITRE ATT&CK Technique"""
    technique_id: str
    name: str
    description: str
    tactics: List[AttackTactic]
    platforms: List[str]
    permissions_required: List[str]
    data_sources: List[str]
    defense_bypassed: List[str]
    supports_remote: bool
    system_requirements: List[str]
    network_requirements: List[str]
    detection_difficulty: DetectionDifficulty
    sub_techniques: List[MitreSubTechnique] = field(default_factory=list)
    kill_chain_phases: List[str] = field(default_factory=list)
    mitigation_techniques: List[str] = field(default_factory=list)
    related_techniques: List[str] = field(default_factory=list)
    capec_mappings: List[str] = field(default_factory=list)


@dataclass
class TechniqueMappingResult:
    """Result of mapping techniques to tools/actions"""
    technique: MitreTechnique
    mapped_tools: List[str]
    mapped_actions: List[str]
    confidence_score: float
    status: TechniqueStatus
    execution_timestamp: Optional[datetime]
    execution_results: Dict[str, Any] = field(default_factory=dict)
    detection_signatures: List[str] = field(default_factory=list)
    evidence_collected: List[str] = field(default_factory=list)


@dataclass
class AttackPath:
    """Attack path through MITRE ATT&CK techniques"""
    path_id: str
    name: str
    description: str
    target_environment: str
    techniques_sequence: List[str]  # Technique IDs in order
    estimated_duration: timedelta
    difficulty_level: str
    detection_probability: float
    success_probability: float
    prerequisites: List[str]
    objectives: List[str]
    alternative_paths: List[str] = field(default_factory=list)


@dataclass
class ThreatActorProfile:
    """Threat actor profile with MITRE ATT&CK techniques"""
    actor_id: str
    name: str
    aliases: List[str]
    description: str
    associated_groups: List[str]
    target_sectors: List[str]
    target_countries: List[str]
    first_seen: datetime
    last_seen: datetime
    sophistication_level: str
    primary_motivation: str
    common_techniques: List[str]  # Technique IDs
    preferred_platforms: List[str]
    attribution_confidence: float


class MitreAttackMapper:
    """Comprehensive MITRE ATT&CK integration and technique mapping system"""

    def __init__(self, config: Config, database: DatabaseManager, ai_service: AIServiceManager):
        """Initialize the MITRE ATT&CK mapper"""
        self.config = config
        self.database = database
        self.ai_service = ai_service
        
        # MITRE ATT&CK data storage
        self.techniques_db: Dict[str, MitreTechnique] = {}
        self.tactics_mapping: Dict[AttackTactic, List[str]] = {}
        self.platform_techniques: Dict[str, List[str]] = {}
        
        # Mapping results
        self.technique_mappings: Dict[str, TechniqueMappingResult] = {}
        self.attack_paths: Dict[str, AttackPath] = {}
        self.threat_actors: Dict[str, ThreatActorProfile] = {}
        
        # Tool to technique mappings
        self.tool_technique_map = self._load_tool_technique_mappings()
        
        # Load MITRE ATT&CK data
        self.mitre_data_path = Path(self.config.get('mitre_data_path', './data/mitre_attack'))
        self.mitre_data_path.mkdir(parents=True, exist_ok=True)
        
        logger.info("MITRE ATT&CK Mapper initialized")

    def _load_tool_technique_mappings(self) -> Dict[str, List[str]]:
        """Load mappings between security tools and MITRE techniques"""
        return {
            # Network scanning and reconnaissance
            "nmap": ["T1046", "T1040", "T1018", "T1083"],  # Network Service Scanning, Network Sniffing, Remote System Discovery, File and Directory Discovery
            "nuclei": ["T1046", "T1190", "T1595.002"],  # Network Service Scanning, Exploit Public-Facing Application, Vulnerability Scanning
            "sqlmap": ["T1190", "T1071.001", "T1133"],  # Exploit Public-Facing Application, Web Protocols, External Remote Services
            
            # Web application testing
            "burpsuite": ["T1190", "T1071.001", "T1110.001", "T1558.003"],  # Exploit Public-Facing App, Web Protocols, Password Guessing, Kerberoasting
            "dirb": ["T1083", "T1087", "T1595.002"],  # File and Directory Discovery, Account Discovery, Vulnerability Scanning
            "gobuster": ["T1083", "T1087", "T1595.002"],  # File and Directory Discovery, Account Discovery, Vulnerability Scanning
            
            # Exploitation frameworks
            "metasploit": ["T1190", "T1068", "T1055", "T1059", "T1071"],  # Exploit Public-Facing App, Exploitation for Privilege Escalation, Process Injection, Command and Scripting Interpreter, Application Layer Protocol
            
            # Password attacks
            "hydra": ["T1110.001", "T1110.003", "T1021"],  # Password Guessing, Password Spraying, Remote Services
            "john": ["T1110.002", "T1555"],  # Password Cracking, Credentials from Password Stores
            "hashcat": ["T1110.002", "T1555"],  # Password Cracking, Credentials from Password Stores
            
            # Network analysis
            "wireshark": ["T1040", "T1557", "T1041"],  # Network Sniffing, Adversary-in-the-Middle, Exfiltration Over C2 Channel
            "tcpdump": ["T1040", "T1557"],  # Network Sniffing, Adversary-in-the-Middle
            
            # System reconnaissance
            "enum4linux": ["T1087.002", "T1135", "T1069.002"],  # Domain Account Discovery, Network Share Discovery, Domain Groups
            "smbclient": ["T1021.002", "T1135", "T1083"],  # SMB/Windows Admin Shares, Network Share Discovery, File and Directory Discovery
            
            # Vulnerability scanning
            "nikto": ["T1595.002", "T1190"],  # Vulnerability Scanning, Exploit Public-Facing Application
            "wpscan": ["T1595.002", "T1190", "T1083"],  # Vulnerability Scanning, Exploit Public-Facing App, File and Directory Discovery
            
            # DNS reconnaissance
            "dnsrecon": ["T1590.002", "T1087.002"],  # DNS, Domain Account Discovery
            "dig": ["T1590.002"],  # DNS
            "nslookup": ["T1590.002"],  # DNS
            
            # SSL/TLS analysis
            "sslscan": ["T1590.001", "T1595.002"],  # Hardware, Vulnerability Scanning
            "sslyze": ["T1590.001", "T1595.002"],  # Hardware, Vulnerability Scanning
            
            # Web crawling and discovery
            "whatweb": ["T1592.002", "T1518.001"],  # Software, Security Software Discovery
            "wapiti": ["T1595.002", "T1190"],  # Vulnerability Scanning, Exploit Public-Facing Application
            
            # Custom NexusScan tools
            "ai_reconnaissance_engine": ["T1590", "T1591", "T1594", "T1595"],  # Gather Victim Host/Network/Organization Information, Search Victim-Owned Websites, Search Victim-Owned Websites, Active Scanning
            "wordlist_manager": ["T1110.001", "T1110.003"],  # Password Guessing, Password Spraying
            "proxy_engine": ["T1040", "T1557", "T1071.001"],  # Network Sniffing, Adversary-in-the-Middle, Web Protocols
            "parameter_fuzzer": ["T1190", "T1595.002"],  # Exploit Public-Facing Application, Vulnerability Scanning
            "tech_fingerprinter": ["T1592.002", "T1518.001"],  # Software, Security Software Discovery
        }

    async def load_mitre_attack_data(self, force_update: bool = False) -> bool:
        """Load MITRE ATT&CK data from official sources"""
        
        try:
            mitre_file = self.mitre_data_path / "enterprise-attack.json"
            
            # Check if we need to download/update data
            if force_update or not mitre_file.exists() or self._data_is_stale(mitre_file):
                logger.info("Downloading latest MITRE ATT&CK data...")
                await self._download_mitre_data()
            
            # Load techniques from file
            with open(mitre_file, 'r', encoding='utf-8') as f:
                mitre_data = json.load(f)
            
            # Parse and store techniques
            for obj in mitre_data.get('objects', []):
                if obj.get('type') == 'attack-pattern':
                    technique = self._parse_mitre_technique(obj)
                    if technique:
                        self.techniques_db[technique.technique_id] = technique
                        
                        # Update tactics mapping
                        for tactic in technique.tactics:
                            if tactic not in self.tactics_mapping:
                                self.tactics_mapping[tactic] = []
                            self.tactics_mapping[tactic].append(technique.technique_id)
                        
                        # Update platform mapping
                        for platform in technique.platforms:
                            if platform not in self.platform_techniques:
                                self.platform_techniques[platform] = []
                            self.platform_techniques[platform].append(technique.technique_id)
            
            logger.info(f"Loaded {len(self.techniques_db)} MITRE ATT&CK techniques")
            return True
            
        except Exception as e:
            logger.error(f"Failed to load MITRE ATT&CK data: {e}")
            return False

    async def _download_mitre_data(self):
        """Download MITRE ATT&CK data from official repository"""
        
        url = "https://raw.githubusercontent.com/mitre/cti/master/enterprise-attack/enterprise-attack.json"
        
        try:
            response = requests.get(url, timeout=30)
            response.raise_for_status()
            
            mitre_file = self.mitre_data_path / "enterprise-attack.json"
            with open(mitre_file, 'w', encoding='utf-8') as f:
                f.write(response.text)
                
            logger.info("MITRE ATT&CK data downloaded successfully")
            
        except Exception as e:
            logger.error(f"Failed to download MITRE ATT&CK data: {e}")
            # Fall back to embedded data if available
            await self._load_embedded_mitre_data()

    async def _load_embedded_mitre_data(self):
        """Load embedded/cached MITRE ATT&CK data"""
        
        # This would load a subset of key techniques if download fails
        embedded_techniques = {
            "T1190": {
                "name": "Exploit Public-Facing Application",
                "description": "Adversaries may attempt to take advantage of a weakness in an Internet-facing computer or program using software, data, or commands in order to cause unintended or unanticipated behavior.",
                "tactics": ["initial-access"],
                "platforms": ["Linux", "Windows", "macOS", "Network"],
                "data_sources": ["Application Log: Application Log Content", "Web Credential: Web Credential Usage"]
            },
            "T1046": {
                "name": "Network Service Scanning",
                "description": "Adversaries may attempt to get a listing of services running on remote hosts, including those that may be vulnerable to remote software exploitation.",
                "tactics": ["discovery"],
                "platforms": ["Linux", "macOS", "Windows"],
                "data_sources": ["Cloud Service: Cloud Service Enumeration", "Network Traffic: Network Traffic Flow"]
            },
            "T1110.001": {
                "name": "Password Guessing",
                "description": "Adversaries with no prior knowledge of legitimate credentials within the system or environment may guess passwords to attempt access to accounts.",
                "tactics": ["credential-access"],
                "platforms": ["Azure AD", "Office 365", "SaaS", "IaaS", "Linux", "macOS", "Windows", "Network"],
                "data_sources": ["Application Log: Application Log Content", "User Account: User Account Authentication"]
            }
        }
        
        for tech_id, tech_data in embedded_techniques.items():
            technique = MitreTechnique(
                technique_id=tech_id,
                name=tech_data["name"],
                description=tech_data["description"],
                tactics=[AttackTactic(t) for t in tech_data["tactics"]],
                platforms=tech_data["platforms"],
                permissions_required=[],
                data_sources=tech_data.get("data_sources", []),
                defense_bypassed=[],
                supports_remote=True,
                system_requirements=[],
                network_requirements=[],
                detection_difficulty=DetectionDifficulty.MODERATE
            )
            self.techniques_db[tech_id] = technique

    def _parse_mitre_technique(self, mitre_obj: Dict[str, Any]) -> Optional[MitreTechnique]:
        """Parse MITRE ATT&CK technique object"""
        
        try:
            # Extract technique ID
            external_refs = mitre_obj.get('external_references', [])
            technique_id = None
            for ref in external_refs:
                if ref.get('source_name') == 'mitre-attack':
                    technique_id = ref.get('external_id')
                    break
            
            if not technique_id:
                return None
            
            # Extract tactics
            tactics = []
            kill_chain_phases = mitre_obj.get('kill_chain_phases', [])
            for phase in kill_chain_phases:
                if phase.get('kill_chain_name') == 'mitre-attack':
                    phase_name = phase.get('phase_name', '').replace('-', '_').upper()
                    try:
                        tactic = AttackTactic(phase.get('phase_name'))
                        tactics.append(tactic)
                    except ValueError:
                        logger.warning(f"Unknown tactic: {phase.get('phase_name')}")
            
            # Parse platforms
            platforms = mitre_obj.get('x_mitre_platforms', [])
            
            # Parse permissions required
            permissions = mitre_obj.get('x_mitre_permissions_required', [])
            
            # Parse data sources
            data_sources = []
            if 'x_mitre_data_sources' in mitre_obj:
                data_sources = mitre_obj['x_mitre_data_sources']
            
            # Parse defense bypassed
            defense_bypassed = mitre_obj.get('x_mitre_defense_bypassed', [])
            
            # Determine if supports remote execution
            supports_remote = 'Network' in platforms or any('remote' in str(p).lower() for p in platforms)
            
            # Parse system requirements
            system_requirements = mitre_obj.get('x_mitre_system_requirements', [])
            
            # Determine detection difficulty based on various factors
            detection_difficulty = self._determine_detection_difficulty(mitre_obj)
            
            technique = MitreTechnique(
                technique_id=technique_id,
                name=mitre_obj.get('name', ''),
                description=mitre_obj.get('description', ''),
                tactics=tactics,
                platforms=platforms,
                permissions_required=permissions,
                data_sources=data_sources,
                defense_bypassed=defense_bypassed,
                supports_remote=supports_remote,
                system_requirements=system_requirements,
                network_requirements=[],
                detection_difficulty=detection_difficulty
            )
            
            return technique
            
        except Exception as e:
            logger.error(f"Failed to parse MITRE technique: {e}")
            return None

    def _determine_detection_difficulty(self, mitre_obj: Dict[str, Any]) -> DetectionDifficulty:
        """Determine detection difficulty based on technique characteristics"""
        
        # Simple heuristic based on defense bypassed and data sources
        defense_bypassed = mitre_obj.get('x_mitre_defense_bypassed', [])
        data_sources = mitre_obj.get('x_mitre_data_sources', [])
        
        if len(defense_bypassed) > 3:
            return DetectionDifficulty.VERY_HARD
        elif len(defense_bypassed) > 1:
            return DetectionDifficulty.HARD
        elif len(data_sources) < 2:
            return DetectionDifficulty.HARD
        elif len(data_sources) > 4:
            return DetectionDifficulty.EASY
        else:
            return DetectionDifficulty.MODERATE

    def _data_is_stale(self, file_path: Path) -> bool:
        """Check if MITRE data file is stale (older than 7 days)"""
        
        if not file_path.exists():
            return True
            
        file_age = datetime.now() - datetime.fromtimestamp(file_path.stat().st_mtime)
        return file_age > timedelta(days=7)

    async def map_tool_to_techniques(self, tool_name: str, tool_action: str = None) -> List[TechniqueMappingResult]:
        """Map security tool usage to MITRE ATT&CK techniques"""
        
        try:
            mapped_techniques = []
            
            # Get base techniques for the tool
            base_techniques = self.tool_technique_map.get(tool_name, [])
            
            for technique_id in base_techniques:
                if technique_id in self.techniques_db:
                    technique = self.techniques_db[technique_id]
                    
                    # Calculate confidence based on tool and action specificity
                    confidence = self._calculate_mapping_confidence(tool_name, tool_action, technique)
                    
                    mapping_result = TechniqueMappingResult(
                        technique=technique,
                        mapped_tools=[tool_name],
                        mapped_actions=[tool_action] if tool_action else [],
                        confidence_score=confidence,
                        status=TechniqueStatus.NOT_ATTEMPTED,
                        execution_timestamp=None
                    )
                    
                    mapped_techniques.append(mapping_result)
                    self.technique_mappings[technique_id] = mapping_result
            
            # Use AI to suggest additional techniques
            ai_suggestions = await self._get_ai_technique_suggestions(tool_name, tool_action)
            for suggestion in ai_suggestions:
                if suggestion['technique_id'] not in [m.technique.technique_id for m in mapped_techniques]:
                    if suggestion['technique_id'] in self.techniques_db:
                        technique = self.techniques_db[suggestion['technique_id']]
                        
                        mapping_result = TechniqueMappingResult(
                            technique=technique,
                            mapped_tools=[tool_name],
                            mapped_actions=[tool_action] if tool_action else [],
                            confidence_score=suggestion['confidence'],
                            status=TechniqueStatus.NOT_ATTEMPTED,
                            execution_timestamp=None
                        )
                        
                        mapped_techniques.append(mapping_result)
            
            logger.info(f"Mapped {tool_name} to {len(mapped_techniques)} techniques")
            return mapped_techniques
            
        except Exception as e:
            logger.error(f"Failed to map tool to techniques: {e}")
            return []

    async def _get_ai_technique_suggestions(self, tool_name: str, tool_action: str = None) -> List[Dict[str, Any]]:
        """Get AI suggestions for additional technique mappings"""
        
        try:
            context = {
                "tool_name": tool_name,
                "tool_action": tool_action,
                "available_techniques": list(self.techniques_db.keys()),
                "task": f"Suggest additional MITRE ATT&CK techniques that could be associated with using {tool_name}"
            }
            
            analysis_request = AnalysisRequest(
                analysis_type="mitre_technique_mapping",
                target_info={"tool": tool_name, "action": tool_action},
                context=context
            )
            
            ai_result = await self.ai_service.analyze(analysis_request)
            
            if ai_result and 'suggested_techniques' in ai_result:
                return ai_result['suggested_techniques']
            else:
                return []
                
        except Exception as e:
            logger.error(f"AI technique suggestion failed: {e}")
            return []

    def _calculate_mapping_confidence(self, tool_name: str, tool_action: str, technique: MitreTechnique) -> float:
        """Calculate confidence score for tool-technique mapping"""
        
        base_confidence = 0.7  # Base confidence for predefined mappings
        
        # Adjust based on specificity
        if tool_action:
            base_confidence += 0.1
        
        # Adjust based on technique characteristics
        if technique.supports_remote and 'remote' in tool_name.lower():
            base_confidence += 0.1
        
        # Adjust based on platform compatibility
        common_platforms = ['Linux', 'Windows', 'macOS']
        if any(platform in technique.platforms for platform in common_platforms):
            base_confidence += 0.05
        
        return min(base_confidence, 1.0)

    async def generate_attack_path(self, target_platform: str, attack_objectives: List[str]) -> AttackPath:
        """Generate an attack path using MITRE ATT&CK techniques"""
        
        try:
            path_id = f"path_{int(datetime.now().timestamp())}"
            
            # Filter techniques by platform
            available_techniques = [
                tech for tech in self.techniques_db.values()
                if target_platform in tech.platforms or 'Network' in tech.platforms
            ]
            
            # Build attack sequence by tactic order
            tactic_order = [
                AttackTactic.RECONNAISSANCE,
                AttackTactic.INITIAL_ACCESS,
                AttackTactic.EXECUTION,
                AttackTactic.PERSISTENCE,
                AttackTactic.PRIVILEGE_ESCALATION,
                AttackTactic.DEFENSE_EVASION,
                AttackTactic.CREDENTIAL_ACCESS,
                AttackTactic.DISCOVERY,
                AttackTactic.LATERAL_MOVEMENT,
                AttackTactic.COLLECTION,
                AttackTactic.EXFILTRATION,
                AttackTactic.IMPACT
            ]
            
            techniques_sequence = []
            for tactic in tactic_order:
                # Select most suitable technique for this tactic
                tactic_techniques = [
                    tech for tech in available_techniques
                    if tactic in tech.tactics
                ]
                
                if tactic_techniques:
                    # Select technique with lowest detection difficulty
                    selected_technique = min(
                        tactic_techniques,
                        key=lambda t: list(DetectionDifficulty).index(t.detection_difficulty)
                    )
                    techniques_sequence.append(selected_technique.technique_id)
            
            # Calculate path characteristics
            estimated_duration = timedelta(hours=len(techniques_sequence) * 2)  # 2 hours per technique
            difficulty_level = self._calculate_path_difficulty(techniques_sequence)
            detection_probability = self._calculate_detection_probability(techniques_sequence)
            success_probability = self._calculate_success_probability(techniques_sequence)
            
            attack_path = AttackPath(
                path_id=path_id,
                name=f"Attack Path - {target_platform}",
                description=f"Generated attack path for {target_platform} targeting: {', '.join(attack_objectives)}",
                target_environment=target_platform,
                techniques_sequence=techniques_sequence,
                estimated_duration=estimated_duration,
                difficulty_level=difficulty_level,
                detection_probability=detection_probability,
                success_probability=success_probability,
                prerequisites=[f"Access to {target_platform} environment"],
                objectives=attack_objectives
            )
            
            self.attack_paths[path_id] = attack_path
            
            logger.info(f"Generated attack path with {len(techniques_sequence)} techniques")
            return attack_path
            
        except Exception as e:
            logger.error(f"Failed to generate attack path: {e}")
            raise

    def _calculate_path_difficulty(self, technique_ids: List[str]) -> str:
        """Calculate overall difficulty of attack path"""
        
        difficulties = []
        for tech_id in technique_ids:
            if tech_id in self.techniques_db:
                tech = self.techniques_db[tech_id]
                difficulties.append(list(DetectionDifficulty).index(tech.detection_difficulty))
        
        if not difficulties:
            return "unknown"
        
        avg_difficulty = sum(difficulties) / len(difficulties)
        
        if avg_difficulty < 1:
            return "easy"
        elif avg_difficulty < 2:
            return "moderate"
        elif avg_difficulty < 3:
            return "hard"
        else:
            return "very_hard"

    def _calculate_detection_probability(self, technique_ids: List[str]) -> float:
        """Calculate probability of attack path detection"""
        
        detection_probabilities = []
        for tech_id in technique_ids:
            if tech_id in self.techniques_db:
                tech = self.techniques_db[tech_id]
                # Simple heuristic: more data sources = higher detection probability
                detection_prob = min(0.9, len(tech.data_sources) * 0.15)
                detection_probabilities.append(detection_prob)
        
        if not detection_probabilities:
            return 0.5
        
        # Calculate compound detection probability
        no_detection_prob = 1.0
        for prob in detection_probabilities:
            no_detection_prob *= (1.0 - prob)
        
        return 1.0 - no_detection_prob

    def _calculate_success_probability(self, technique_ids: List[str]) -> float:
        """Calculate probability of attack path success"""
        
        success_probabilities = []
        for tech_id in technique_ids:
            if tech_id in self.techniques_db:
                tech = self.techniques_db[tech_id]
                # Simple heuristic: fewer requirements = higher success probability
                success_prob = max(0.3, 0.9 - len(tech.system_requirements) * 0.1)
                success_probabilities.append(success_prob)
        
        if not success_probabilities:
            return 0.5
        
        # Calculate compound success probability
        overall_success = 1.0
        for prob in success_probabilities:
            overall_success *= prob
        
        return overall_success

    async def update_technique_status(self, technique_id: str, status: TechniqueStatus, 
                                    execution_results: Dict[str, Any] = None):
        """Update the status of a technique execution"""
        
        if technique_id in self.technique_mappings:
            mapping = self.technique_mappings[technique_id]
            mapping.status = status
            mapping.execution_timestamp = datetime.now()
            
            if execution_results:
                mapping.execution_results = execution_results
                
                # Extract evidence if successful
                if status == TechniqueStatus.SUCCESSFUL:
                    evidence = execution_results.get('evidence', [])
                    mapping.evidence_collected.extend(evidence)
            
            logger.info(f"Updated technique {technique_id} status to {status.value}")

    def get_techniques_by_tactic(self, tactic: AttackTactic) -> List[MitreTechnique]:
        """Get all techniques for a specific tactic"""
        
        technique_ids = self.tactics_mapping.get(tactic, [])
        return [self.techniques_db[tid] for tid in technique_ids if tid in self.techniques_db]

    def get_techniques_by_platform(self, platform: str) -> List[MitreTechnique]:
        """Get all techniques applicable to a platform"""
        
        technique_ids = self.platform_techniques.get(platform, [])
        return [self.techniques_db[tid] for tid in technique_ids if tid in self.techniques_db]

    def search_techniques(self, query: str) -> List[MitreTechnique]:
        """Search techniques by name or description"""
        
        query_lower = query.lower()
        matches = []
        
        for technique in self.techniques_db.values():
            if (query_lower in technique.name.lower() or 
                query_lower in technique.description.lower() or
                query_lower in technique.technique_id.lower()):
                matches.append(technique)
        
        return matches

    def get_technique_coverage_report(self) -> Dict[str, Any]:
        """Generate technique coverage report"""
        
        total_techniques = len(self.techniques_db)
        mapped_techniques = len(self.technique_mappings)
        
        # Count by tactic
        tactic_coverage = {}
        for tactic in AttackTactic:
            tactic_techniques = self.tactics_mapping.get(tactic, [])
            mapped_for_tactic = len([t for t in tactic_techniques if t in self.technique_mappings])
            tactic_coverage[tactic.value] = {
                "total": len(tactic_techniques),
                "mapped": mapped_for_tactic,
                "coverage_percentage": (mapped_for_tactic / len(tactic_techniques) * 100) if tactic_techniques else 0
            }
        
        # Count by status
        status_counts = {}
        for status in TechniqueStatus:
            count = len([m for m in self.technique_mappings.values() if m.status == status])
            status_counts[status.value] = count
        
        return {
            "total_techniques_available": total_techniques,
            "total_techniques_mapped": mapped_techniques,
            "overall_coverage_percentage": (mapped_techniques / total_techniques * 100) if total_techniques else 0,
            "tactic_coverage": tactic_coverage,
            "technique_status_distribution": status_counts,
            "attack_paths_generated": len(self.attack_paths),
            "threat_actors_profiled": len(self.threat_actors)
        }

    def export_mitre_mapping(self, output_path: Path) -> bool:
        """Export MITRE ATT&CK mapping to file"""
        
        try:
            export_data = {
                "export_timestamp": datetime.now().isoformat(),
                "nexusscan_version": "1.0",
                "mitre_attack_version": "14.1",
                "technique_mappings": {
                    tid: {
                        "technique": asdict(mapping.technique),
                        "mapped_tools": mapping.mapped_tools,
                        "mapped_actions": mapping.mapped_actions,
                        "confidence_score": mapping.confidence_score,
                        "status": mapping.status.value,
                        "execution_timestamp": mapping.execution_timestamp.isoformat() if mapping.execution_timestamp else None,
                        "execution_results": mapping.execution_results,
                        "evidence_collected": mapping.evidence_collected
                    }
                    for tid, mapping in self.technique_mappings.items()
                },
                "attack_paths": {
                    pid: asdict(path) for pid, path in self.attack_paths.items()
                },
                "coverage_report": self.get_technique_coverage_report()
            }
            
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, indent=2, default=str)
            
            logger.info(f"MITRE mapping exported to {output_path}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to export MITRE mapping: {e}")
            return False

    def get_mitre_statistics(self) -> Dict[str, Any]:
        """Get MITRE ATT&CK integration statistics"""
        
        return {
            "techniques_loaded": len(self.techniques_db),
            "tactics_mapped": len(self.tactics_mapping),
            "platforms_supported": len(self.platform_techniques),
            "tools_mapped": len(self.tool_technique_map),
            "technique_mappings": len(self.technique_mappings),
            "attack_paths_generated": len(self.attack_paths),
            "threat_actors_profiled": len(self.threat_actors),
            "data_last_updated": datetime.now().isoformat()
        }