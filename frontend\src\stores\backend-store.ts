/**
 * Backend Store - Real-world production backend integration
 * Connects to AWS EC2 backend with 23 operational security tools
 */
import { create } from 'zustand'
import { subscribeWithSelector } from 'zustand/middleware'
import { immer } from 'zustand/middleware/immer'
import { apiClient, type BackendStatus, type ToolInfo, type FerrariCapabilities, type AIStatus } from '@/services/api-client'
import { webSocketService } from '@/services/websocket'
import { toast } from 'sonner'
import { WorkflowExecution } from '@/services/assessment-workflows'

// ===== TYPE DEFINITIONS =====

export interface ConnectionHistory {
  timestamp: Date
  success: boolean
  error?: string
}

export interface BackendState {
  // Connection state
  status: BackendStatus
  isConnecting: boolean
  lastConnectionAttempt?: Date
  connectionHistory: ConnectionHistory[]
  
  // Tools state
  tools: ToolInfo[]
  toolsLoading: boolean
  toolsLastUpdated?: Date
  
  // Ferrari AI state
  ferrariCapabilities: FerrariCapabilities | null
  ferrariLoading: boolean
  
  // AI status
  aiStatus: AIStatus | null
  aiLoading: boolean
  
  // WebSocket state
  websocketConnected: boolean
  websocketReconnectAttempts: number
  
  // Workflow execution state
  activeWorkflows: Map<string, WorkflowExecution>
  workflowHistory: WorkflowExecution[]
}

export interface BackendActions {
  // Connection actions
  connect: () => Promise<boolean>
  disconnect: () => void
  checkHealth: () => Promise<boolean>
  
  // Tools actions
  refreshTools: () => Promise<void>
  getToolDetails: (toolId: string) => Promise<ToolInfo | null>
  
  // Ferrari AI actions
  refreshFerrariCapabilities: () => Promise<void>
  refreshAIStatus: () => Promise<void>
  
  // WebSocket actions
  connectWebSocket: () => Promise<boolean>
  disconnectWebSocket: () => void
  
  // Workflow execution actions
  updateWorkflowExecution: (execution: WorkflowExecution) => void
  getActiveWorkflows: () => WorkflowExecution[]
  getWorkflowById: (id: string) => WorkflowExecution | undefined
  cancelWorkflow: (id: string) => void
  clearWorkflowHistory: () => void
  
  // Utility actions
  setConnectionStatus: (status: Partial<BackendStatus>) => void
  reset: () => void
}

export type BackendStore = BackendState & BackendActions

// ===== INITIAL STATE =====

const initialState: BackendState = {
  status: {
    connected: false,
    url: apiClient.getBaseURL(),
  },
  isConnecting: false,
  connectionHistory: [],
  
  tools: [],
  toolsLoading: false,
  
  ferrariCapabilities: null,
  ferrariLoading: false,
  
  aiStatus: null,
  aiLoading: false,
  
  websocketConnected: false,
  websocketReconnectAttempts: 0,
  
  activeWorkflows: new Map(),
  workflowHistory: [],
}

// ===== STORE IMPLEMENTATION =====

export const useBackendStore = create<BackendStore>()(
  subscribeWithSelector(
    immer((set, get) => ({
      ...initialState,
      
      // ===== CONNECTION MANAGEMENT =====
      
      connect: async (): Promise<boolean> => {
        console.log('🔗 Connecting to backend...')
        
        set((draft) => {
          draft.isConnecting = true
          draft.lastConnectionAttempt = new Date()
        })
        
        try {
          console.log('🔗 Attempting to connect to backend...')

          // Check backend health - use Electron Backend Manager if available, otherwise API client
          let healthStatus: BackendStatus

          console.log('🔗 Using API client for connection...')
          healthStatus = await apiClient.checkHealth()
          
          set((draft) => {
            draft.status = healthStatus
            draft.isConnecting = false
            draft.connectionHistory.unshift({
              timestamp: new Date(),
              success: healthStatus.connected,
              error: healthStatus.error,
            })
            
            // Keep only last 50 connection attempts
            if (draft.connectionHistory.length > 50) {
              draft.connectionHistory = draft.connectionHistory.slice(0, 50)
            }
          })
          
          if (healthStatus.connected) {
            console.log('✅ Backend connected successfully')
            toast.success('Connected to backend')
            
            // Refresh tools and capabilities
            console.log('🔧 Starting tools refresh...')
            await get().refreshTools()
            console.log('🏎️ Starting Ferrari capabilities refresh...')
            get().refreshFerrariCapabilities()
            console.log('🤖 Starting AI status refresh...')
            get().refreshAIStatus()
            
            // Connect WebSocket
            get().connectWebSocket()
            
            return true
          } else {
            console.error('❌ Backend connection failed:', healthStatus.error)
            toast.error(`Backend connection failed: ${healthStatus.error}`)
            return false
          }
        } catch (error) {
          console.error('❌ Backend connection error:', error)
          
          set((draft) => {
            draft.status = {
              connected: false,
              url: apiClient.getBaseURL(),
              error: error instanceof Error ? error.message : 'Unknown error',
            }
            draft.isConnecting = false
            draft.connectionHistory.unshift({
              timestamp: new Date(),
              success: false,
              error: error instanceof Error ? error.message : 'Unknown error',
            })
          })
          
          toast.error('Failed to connect to backend')
          return false
        }
      },
      
      disconnect: () => {
        console.log('🔌 Disconnecting from backend...')
        
        set((draft) => {
          draft.status = {
            connected: false,
            url: apiClient.getBaseURL(),
          }
          draft.tools = []
          draft.ferrariCapabilities = null
          draft.aiStatus = null
        })
        
        // Disconnect WebSocket
        get().disconnectWebSocket()
        
        toast.info('Disconnected from backend')
      },
      
      checkHealth: async (): Promise<boolean> => {
        try {
          // Use API client for health check
          const healthStatus = await apiClient.checkHealth()

          set((draft) => {
            draft.status = healthStatus
          })

          return healthStatus.connected
        } catch (error) {
          console.error('❌ Health check failed:', error)

          set((draft) => {
            draft.status = {
              connected: false,
              url: window.electronAPI ? 'Electron Backend Manager' : apiClient.getBaseURL(),
              error: error instanceof Error ? error.message : 'Unknown error',
            }
          })

          return false
        }
      },
      
      // ===== TOOLS MANAGEMENT =====
      
      refreshTools: async (): Promise<void> => {
        const state = get()
        
        if (!state.status.connected) {
          console.warn('⚠️ Cannot refresh tools - backend not connected')
          return
        }
        
        set((draft) => {
          draft.toolsLoading = true
        })
        
        try {
          console.log('🔧 Refreshing tools list...')
          
          const tools = await apiClient.getAvailableTools()
          
          set((draft) => {
            draft.tools = tools
            draft.toolsLoading = false
            draft.toolsLastUpdated = new Date()
            
            // Update tools count in status
            const operational = tools.filter(tool => tool.status === 'available').length
            const failed = tools.filter(tool => tool.status === 'error').length
            
            draft.status.tools = {
              total: tools.length,
              operational: operational,
              failed: failed,
            }
            
            console.log('🔧 Backend Store - Updated status.tools:', {
              total: tools.length,
              operational: operational,
              failed: failed
            })
          })
          
          const operational = tools.filter(tool => tool.status === 'available').length
          const failed = tools.filter(tool => tool.status === 'error').length
          console.log(`✅ Loaded ${tools.length} tools (${operational} operational, ${failed} failed)`)
        } catch (error) {
          console.error('❌ Failed to refresh tools:', error)
          
          set((draft) => {
            draft.toolsLoading = false
          })
          
          toast.error('Failed to load security tools')
        }
      },
      
      getToolDetails: async (toolId: string): Promise<ToolInfo | null> => {
        try {
          return await apiClient.getToolDetails(toolId)
        } catch (error) {
          console.error(`❌ Failed to get tool details for ${toolId}:`, error)
          return null
        }
      },
      
      // ===== FERRARI AI MANAGEMENT =====
      
      refreshFerrariCapabilities: async (): Promise<void> => {
        const state = get()
        
        if (!state.status.connected) {
          console.warn('⚠️ Cannot refresh Ferrari capabilities - backend not connected')
          return
        }
        
        set((draft) => {
          draft.ferrariLoading = true
        })
        
        try {
          console.log('🏎️ Refreshing Ferrari AI capabilities...')
          
          const capabilities = await apiClient.getFerrariCapabilities()
          
          set((draft) => {
            draft.ferrariCapabilities = capabilities
            draft.ferrariLoading = false
          })
          
          console.log('✅ Ferrari AI capabilities loaded')
        } catch (error) {
          console.error('❌ Failed to refresh Ferrari capabilities:', error)
          
          set((draft) => {
            draft.ferrariLoading = false
          })
        }
      },
      
      refreshAIStatus: async (): Promise<void> => {
        const state = get()
        
        if (!state.status.connected) {
          console.warn('⚠️ Cannot refresh AI status - backend not connected')
          return
        }
        
        set((draft) => {
          draft.aiLoading = true
        })
        
        try {
          console.log('🤖 Refreshing AI status...')
          
          const aiStatus = await apiClient.getAIStatus()
          
          set((draft) => {
            draft.aiStatus = aiStatus
            draft.aiLoading = false
          })
          
          console.log('✅ AI status loaded')
        } catch (error) {
          console.error('❌ Failed to refresh AI status:', error)
          
          set((draft) => {
            draft.aiLoading = false
          })
        }
      },
      
      // ===== WEBSOCKET MANAGEMENT =====
      
      connectWebSocket: async (): Promise<boolean> => {
        try {
          console.log('🔌 Connecting to WebSocket...')
          
          const connected = await webSocketService.connect()
          
          set((draft) => {
            draft.websocketConnected = connected
            if (connected) {
              draft.websocketReconnectAttempts = 0
            }
          })
          
          if (connected) {
            // Set up event listeners
            webSocketService.on('onDisconnect', () => {
              set((draft) => {
                draft.websocketConnected = false
              })
            })
            
            webSocketService.on('onConnect', () => {
              set((draft) => {
                draft.websocketConnected = true
                draft.websocketReconnectAttempts = 0
              })
            })
            
            // Subscribe to system events
            webSocketService.subscribeToSystemEvents()
            webSocketService.subscribeToAIUpdates()
          }
          
          return connected
        } catch (error) {
          console.error('❌ WebSocket connection failed:', error)
          
          set((draft) => {
            draft.websocketConnected = false
            draft.websocketReconnectAttempts++
          })
          
          return false
        }
      },
      
      disconnectWebSocket: () => {
        console.log('🔌 Disconnecting WebSocket...')
        
        webSocketService.disconnect()
        
        set((draft) => {
          draft.websocketConnected = false
          draft.websocketReconnectAttempts = 0
        })
      },
      
      // ===== WORKFLOW EXECUTION ACTIONS =====
      
      updateWorkflowExecution: (execution: WorkflowExecution) => {
        set((draft) => {
          draft.activeWorkflows.set(execution.id, execution)
          
          // If workflow is completed, move to history
          if (execution.status === 'completed' || execution.status === 'failed' || execution.status === 'cancelled') {
            const historyExecution = { ...execution }
            draft.workflowHistory.unshift(historyExecution)
            draft.activeWorkflows.delete(execution.id)
            
            // Keep only last 100 executions in history
            if (draft.workflowHistory.length > 100) {
              draft.workflowHistory = draft.workflowHistory.slice(0, 100)
            }
          }
        })
      },
      
      getActiveWorkflows: (): WorkflowExecution[] => {
        const { activeWorkflows } = get()
        return Array.from(activeWorkflows.values())
      },
      
      getWorkflowById: (id: string): WorkflowExecution | undefined => {
        const { activeWorkflows, workflowHistory } = get()
        return activeWorkflows.get(id) || workflowHistory.find(w => w.id === id)
      },
      
      cancelWorkflow: (id: string) => {
        set((draft) => {
          const workflow = draft.activeWorkflows.get(id)
          if (workflow && workflow.status === 'running') {
            workflow.status = 'cancelled'
            workflow.completedAt = new Date()
            
            // Move to history
            draft.workflowHistory.unshift({ ...workflow })
            draft.activeWorkflows.delete(id)
          }
        })
      },
      
      clearWorkflowHistory: () => {
        set((draft) => {
          draft.workflowHistory = []
        })
      },
      
      // ===== UTILITY ACTIONS =====
      
      setConnectionStatus: (status: Partial<BackendStatus>) => {
        set((draft) => {
          Object.assign(draft.status, status)
        })
      },
      
      reset: () => {
        set(() => ({ ...initialState }))
      },
    }))
  )
)

// ===== STORE UTILITIES =====

/**
 * Auto-connect to backend on app initialization
 */
export const initializeBackendStore = async () => {
  try {
    console.log('🚀 Initializing backend store...')
    
    const { connect } = useBackendStore.getState()
    await connect()
    
    console.log('✅ Backend store initialized')
  } catch (error) {
    console.error('❌ Failed to initialize backend store:', error)
  }
}

/**
 * Periodic health check
 */
export const startBackendHealthCheck = () => {
  const checkHealth = async () => {
    const { checkHealth, status } = useBackendStore.getState()
    
    if (status.connected) {
      await checkHealth()
    }
  }
  
  // Check health every 30 seconds
  setInterval(checkHealth, 30000)
  
  console.log('🏥 Backend health check started')
}

/**
 * Get tool by ID utility
 */
export const useToolById = (toolId: string) => {
  return useBackendStore((state) => 
    state.tools.find(tool => tool.id === toolId)
  )
}

/**
 * Get tools by category utility
 */
export const useToolsByCategory = (category: ToolInfo['category']) => {
  return useBackendStore((state) => 
    state.tools.filter(tool => tool.category === category)
  )
}