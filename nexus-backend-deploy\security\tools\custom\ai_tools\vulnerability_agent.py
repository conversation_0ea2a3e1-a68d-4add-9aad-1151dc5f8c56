#!/usr/bin/env python3
"""
AI Vulnerability Agent for NexusScan Desktop
AI-powered vulnerability assessment with CVSS v3.1 scoring and remediation guidance
"""

import asyncio
import logging
import json
from datetime import datetime
from typing import Dict, List, Optional, Callable, Any
from dataclasses import dataclass

from security.tools.tool_registry import (
    ToolMetadata, ToolCategory, ToolCapabilities,
    ScanOptions, ScanResult, ToolStatus, register_tool
)
from security.tools.base_scanner import BaseScanner
from ai.services import ai_service_manager

logger = logging.getLogger(__name__)


@dataclass
class VulnerabilityAssessmentOptions(ScanOptions):
    """Vulnerability Agent specific options"""
    assessment_type: str = "comprehensive"  # comprehensive, targeted, quick
    severity_filter: List[str] = None  # ["critical", "high", "medium", "low", "info"]
    include_remediation: bool = True
    include_cvss_scoring: bool = True
    include_exploit_analysis: bool = True
    context_data: Dict[str, Any] = None  # Additional context for AI analysis
    
    def __post_init__(self):
        super().__post_init__()
        if self.severity_filter is None:
            self.severity_filter = ["critical", "high", "medium"]
        if self.context_data is None:
            self.context_data = {}


@register_tool
class VulnerabilityAgent(BaseScanner):
    """AI-powered vulnerability assessment agent"""
    
    def __init__(self):
        """Initialize Vulnerability Agent"""
        super().__init__()
        self.simulation_available = True
    
    def get_metadata(self) -> ToolMetadata:
        """Get Vulnerability Agent metadata"""
        return ToolMetadata(
            name="vulnerability_agent",
            display_name="AI Vulnerability Agent",
            description="AI-powered vulnerability assessment with CVSS v3.1 scoring and remediation guidance",
            version="1.0.0",
            category=ToolCategory.AI_ANALYZER,
            capabilities=ToolCapabilities(
                supports_async=True,
                supports_progress=True,
                supports_cancellation=False,
                requires_root=False,
                network_access_required=False,
                output_formats=["json", "html"],
                supported_targets=["ip", "url", "domain", "scan_results"]
            ),
            default_options={
                "assessment_type": "comprehensive",
                "include_remediation": True,
                "include_cvss_scoring": True
            },
            required_dependencies=["ai_service"]
        )
    
    def check_native_availability(self) -> bool:
        """Check if AI service is available"""
        try:
            # Check if we have any available providers
            import os
            has_openai = bool(os.getenv('OPENAI_API_KEY'))
            has_deepseek = bool(os.getenv('DEEPSEEK_API_KEY'))
            has_anthropic = bool(os.getenv('ANTHROPIC_API_KEY'))
            return has_openai or has_deepseek or has_anthropic
        except Exception as e:
            logger.error(f"AI service check failed: {e}")
            return False
    
    async def execute_native_scan(self, options: ScanOptions,
                                  progress_callback: Optional[Callable[[float, str], None]] = None) -> ScanResult:
        """Execute AI vulnerability assessment"""
        start_time = datetime.now().isoformat()
        
        if not isinstance(options, VulnerabilityAssessmentOptions):
            # Convert generic options to VulnerabilityAssessmentOptions
            vuln_options = VulnerabilityAssessmentOptions(
                target=options.target,
                timeout=options.timeout,
                output_format=options.output_format,
                custom_options=options.custom_options or {}
            )
        else:
            vuln_options = options
        
        try:
            if progress_callback:
                await progress_callback(0.1, "Initializing AI vulnerability assessment...")
            
            # Gather target information
            if progress_callback:
                await progress_callback(0.2, "Gathering target information...")
            
            target_info = await self._gather_target_info(vuln_options.target)
            
            # Perform AI vulnerability analysis
            if progress_callback:
                await progress_callback(0.4, "Performing AI vulnerability analysis...")
            
            vulnerabilities = await self._analyze_vulnerabilities(
                vuln_options.target, 
                target_info, 
                vuln_options
            )
            
            # Generate CVSS scores if requested
            if vuln_options.include_cvss_scoring:
                if progress_callback:
                    await progress_callback(0.6, "Calculating CVSS v3.1 scores...")
                
                vulnerabilities = await self._calculate_cvss_scores(vulnerabilities)
            
            # Generate remediation guidance if requested
            if vuln_options.include_remediation:
                if progress_callback:
                    await progress_callback(0.8, "Generating remediation guidance...")
                
                vulnerabilities = await self._generate_remediation(vulnerabilities)
            
            # Analyze exploit potential if requested
            if vuln_options.include_exploit_analysis:
                if progress_callback:
                    await progress_callback(0.9, "Analyzing exploit potential...")
                
                vulnerabilities = await self._analyze_exploit_potential(vulnerabilities)
            
            if progress_callback:
                await progress_callback(1.0, "Assessment complete")
            
            # Generate final report
            assessment_results = {
                "target": vuln_options.target,
                "assessment_type": vuln_options.assessment_type,
                "vulnerabilities": vulnerabilities,
                "summary": self._generate_summary(vulnerabilities),
                "recommendations": await self._generate_recommendations(vulnerabilities),
                "metadata": {
                    "ai_confidence": self._calculate_confidence(vulnerabilities),
                    "assessment_timestamp": start_time,
                    "agent_version": "1.0.0"
                }
            }
            
            return ScanResult(
                tool_name=self.metadata.name,
                target=vuln_options.target,
                status=ToolStatus.COMPLETED,
                start_time=start_time,
                end_time=datetime.now().isoformat(),
                duration_seconds=(datetime.now() - datetime.fromisoformat(start_time)).total_seconds(),
                parsed_results=assessment_results,
                vulnerabilities=vulnerabilities,
                metadata={
                    "ai_powered": True,
                    "assessment_type": vuln_options.assessment_type,
                    "vulnerabilities_found": len(vulnerabilities)
                }
            )
            
        except Exception as e:
            logger.error(f"Vulnerability assessment failed: {e}")
            return ScanResult(
                tool_name=self.metadata.name,
                target=vuln_options.target,
                status=ToolStatus.FAILED,
                start_time=start_time,
                end_time=datetime.now().isoformat(),
                errors=[str(e)]
            )
    
    async def _gather_target_info(self, target: str) -> Dict[str, Any]:
        """Gather information about the target"""
        target_info = {
            "target": target,
            "target_type": self._detect_target_type(target),
            "timestamp": datetime.now().isoformat()
        }
        
        # Add basic target analysis
        if target_info["target_type"] == "url":
            from urllib.parse import urlparse
            parsed = urlparse(target)
            target_info.update({
                "domain": parsed.netloc,
                "scheme": parsed.scheme,
                "path": parsed.path
            })
        elif target_info["target_type"] == "ip":
            target_info["ip_address"] = target
        
        return target_info
    
    async def _analyze_vulnerabilities(self, target: str, target_info: Dict[str, Any],
                                     options: VulnerabilityAssessmentOptions) -> List[Dict[str, Any]]:
        """Use AI to analyze potential vulnerabilities"""
        
        # Create AI analysis prompt
        analysis_prompt = f"""
        Analyze the target {target} for potential security vulnerabilities.
        
        Target Information:
        {json.dumps(target_info, indent=2)}
        
        Assessment Type: {options.assessment_type}
        Severity Filter: {', '.join(options.severity_filter)}
        
        Please provide a comprehensive vulnerability assessment including:
        1. Potential vulnerabilities based on the target type and information
        2. Severity levels (Critical, High, Medium, Low, Info)
        3. Detailed descriptions of each vulnerability
        4. Potential impact and attack vectors
        5. Confidence levels for each finding
        
        Return the analysis in JSON format with the following structure:
        {{
            "vulnerabilities": [
                {{
                    "id": "unique_vulnerability_id",
                    "title": "Vulnerability Title",
                    "description": "Detailed description",
                    "severity": "Critical|High|Medium|Low|Info",
                    "category": "vulnerability_category",
                    "cwe_id": "CWE-XXX",
                    "attack_vector": "description_of_attack_vector",
                    "impact": "potential_impact_description",
                    "confidence": "high|medium|low",
                    "evidence": "supporting_evidence"
                }}
            ]
        }}
        """
        
        try:
            # Use AI service for analysis
            ai_response = await ai_service_manager.analyze(
                prompt=analysis_prompt,
                analysis_type="vulnerability_assessment",
                context={"target": target, "options": options.custom_options}
            )
            
            # Parse AI response
            if isinstance(ai_response, str):
                ai_data = json.loads(ai_response)
            else:
                ai_data = ai_response
            
            vulnerabilities = ai_data.get("vulnerabilities", [])
            
            # Filter by severity if specified
            if options.severity_filter:
                vulnerabilities = [
                    vuln for vuln in vulnerabilities 
                    if vuln.get("severity", "").lower() in [s.lower() for s in options.severity_filter]
                ]
            
            return vulnerabilities
            
        except Exception as e:
            logger.error(f"AI vulnerability analysis failed: {e}")
            # Return sample vulnerabilities for demonstration
            return self._get_sample_vulnerabilities(target, options)
    
    async def _calculate_cvss_scores(self, vulnerabilities: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Calculate CVSS v3.1 scores for vulnerabilities"""
        
        for vuln in vulnerabilities:
            try:
                # Create CVSS calculation prompt
                cvss_prompt = f"""
                Calculate the CVSS v3.1 score for the following vulnerability:
                
                Title: {vuln.get('title', 'Unknown')}
                Description: {vuln.get('description', 'No description')}
                Category: {vuln.get('category', 'Unknown')}
                Attack Vector: {vuln.get('attack_vector', 'Unknown')}
                Impact: {vuln.get('impact', 'Unknown')}
                
                Provide CVSS v3.1 metrics and calculate the base score.
                Return JSON format:
                {{
                    "cvss_version": "3.1",
                    "vector_string": "CVSS:3.1/AV:X/AC:X/PR:X/UI:X/S:X/C:X/I:X/A:X",
                    "base_score": 0.0,
                    "base_severity": "Critical|High|Medium|Low|None",
                    "metrics": {{
                        "attack_vector": "Network|Adjacent|Local|Physical",
                        "attack_complexity": "Low|High",
                        "privileges_required": "None|Low|High",
                        "user_interaction": "None|Required",
                        "scope": "Unchanged|Changed",
                        "confidentiality": "None|Low|High",
                        "integrity": "None|Low|High",
                        "availability": "None|Low|High"
                    }}
                }}
                """
                
                cvss_response = await ai_service_manager.analyze(
                    prompt=cvss_prompt,
                    analysis_type="cvss_calculation",
                    context={"vulnerability": vuln}
                )
                
                if isinstance(cvss_response, str):
                    cvss_data = json.loads(cvss_response)
                else:
                    cvss_data = cvss_response
                
                vuln["cvss"] = cvss_data
                
            except Exception as e:
                logger.warning(f"CVSS calculation failed for {vuln.get('id', 'unknown')}: {e}")
                # Provide default CVSS based on severity
                vuln["cvss"] = self._get_default_cvss(vuln.get("severity", "Medium"))
        
        return vulnerabilities
    
    async def _generate_remediation(self, vulnerabilities: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Generate remediation guidance for vulnerabilities"""
        
        for vuln in vulnerabilities:
            try:
                remediation_prompt = f"""
                Generate detailed remediation guidance for the following vulnerability:
                
                Title: {vuln.get('title', 'Unknown')}
                Description: {vuln.get('description', 'No description')}
                Severity: {vuln.get('severity', 'Unknown')}
                Category: {vuln.get('category', 'Unknown')}
                CWE: {vuln.get('cwe_id', 'Unknown')}
                
                Provide comprehensive remediation guidance including:
                1. Immediate steps to mitigate the vulnerability
                2. Long-term fixes and best practices
                3. Prevention strategies
                4. Testing and validation steps
                
                Return JSON format:
                {{
                    "immediate_steps": ["step1", "step2", ...],
                    "long_term_fixes": ["fix1", "fix2", ...],
                    "prevention": ["strategy1", "strategy2", ...],
                    "validation": ["test1", "test2", ...],
                    "references": ["url1", "url2", ...],
                    "effort_level": "Low|Medium|High",
                    "timeline": "Immediate|Days|Weeks|Months"
                }}
                """
                
                remediation_response = await ai_service_manager.analyze(
                    prompt=remediation_prompt,
                    analysis_type="remediation_guidance",
                    context={"vulnerability": vuln}
                )
                
                if isinstance(remediation_response, str):
                    remediation_data = json.loads(remediation_response)
                else:
                    remediation_data = remediation_response
                
                vuln["remediation"] = remediation_data
                
            except Exception as e:
                logger.warning(f"Remediation generation failed for {vuln.get('id', 'unknown')}: {e}")
                vuln["remediation"] = self._get_default_remediation(vuln)
        
        return vulnerabilities
    
    async def _analyze_exploit_potential(self, vulnerabilities: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Analyze exploit potential for vulnerabilities"""
        
        for vuln in vulnerabilities:
            try:
                exploit_prompt = f"""
                Analyze the exploit potential for the following vulnerability:
                
                Title: {vuln.get('title', 'Unknown')}
                Description: {vuln.get('description', 'No description')}
                Severity: {vuln.get('severity', 'Unknown')}
                Attack Vector: {vuln.get('attack_vector', 'Unknown')}
                
                Analyze:
                1. Likelihood of exploitation
                2. Available exploit tools or methods
                3. Difficulty level for attackers
                4. Real-world exploitation examples
                5. Current threat landscape
                
                Return JSON format:
                {{
                    "exploit_likelihood": "Very High|High|Medium|Low|Very Low",
                    "exploit_difficulty": "Trivial|Easy|Moderate|Hard|Very Hard",
                    "public_exploits": true|false,
                    "weaponization": "In the Wild|Proof of Concept|Theoretical",
                    "attack_complexity": "description",
                    "prerequisites": ["req1", "req2", ...],
                    "threat_actors": ["actor_type1", "actor_type2", ...],
                    "detection_difficulty": "Easy|Moderate|Hard"
                }}
                """
                
                exploit_response = await ai_service_manager.analyze(
                    prompt=exploit_prompt,
                    analysis_type="exploit_analysis",
                    context={"vulnerability": vuln}
                )
                
                if isinstance(exploit_response, str):
                    exploit_data = json.loads(exploit_response)
                else:
                    exploit_data = exploit_response
                
                vuln["exploit_analysis"] = exploit_data
                
            except Exception as e:
                logger.warning(f"Exploit analysis failed for {vuln.get('id', 'unknown')}: {e}")
                vuln["exploit_analysis"] = self._get_default_exploit_analysis(vuln)
        
        return vulnerabilities
    
    def _generate_summary(self, vulnerabilities: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Generate assessment summary"""
        severity_counts = {"Critical": 0, "High": 0, "Medium": 0, "Low": 0, "Info": 0}
        
        for vuln in vulnerabilities:
            severity = vuln.get("severity", "Unknown")
            if severity in severity_counts:
                severity_counts[severity] += 1
        
        total_vulns = len(vulnerabilities)
        critical_high = severity_counts["Critical"] + severity_counts["High"]
        
        return {
            "total_vulnerabilities": total_vulns,
            "severity_breakdown": severity_counts,
            "critical_high_count": critical_high,
            "risk_level": "Critical" if severity_counts["Critical"] > 0 else 
                         "High" if severity_counts["High"] > 0 else
                         "Medium" if severity_counts["Medium"] > 0 else "Low",
            "immediate_action_required": critical_high > 0
        }
    
    async def _generate_recommendations(self, vulnerabilities: List[Dict[str, Any]]) -> List[str]:
        """Generate high-level recommendations"""
        
        if not vulnerabilities:
            return ["No vulnerabilities identified. Continue regular security monitoring."]
        
        recommendations = []
        severity_counts = {"Critical": 0, "High": 0, "Medium": 0, "Low": 0}
        
        for vuln in vulnerabilities:
            severity = vuln.get("severity", "Unknown")
            if severity in severity_counts:
                severity_counts[severity] += 1
        
        # Priority recommendations based on findings
        if severity_counts["Critical"] > 0:
            recommendations.append(f"URGENT: Address {severity_counts['Critical']} critical vulnerabilities immediately")
        
        if severity_counts["High"] > 0:
            recommendations.append(f"High Priority: Remediate {severity_counts['High']} high-severity vulnerabilities within 7 days")
        
        if severity_counts["Medium"] > 0:
            recommendations.append(f"Medium Priority: Plan remediation for {severity_counts['Medium']} medium-severity issues within 30 days")
        
        # General recommendations
        recommendations.extend([
            "Implement regular vulnerability scanning and monitoring",
            "Establish a vulnerability management process",
            "Consider penetration testing to validate findings",
            "Review and update security policies and procedures"
        ])
        
        return recommendations
    
    def _calculate_confidence(self, vulnerabilities: List[Dict[str, Any]]) -> float:
        """Calculate overall AI confidence score"""
        if not vulnerabilities:
            return 0.0
        
        confidence_map = {"high": 0.9, "medium": 0.7, "low": 0.5}
        total_confidence = 0.0
        
        for vuln in vulnerabilities:
            confidence = vuln.get("confidence", "medium").lower()
            total_confidence += confidence_map.get(confidence, 0.5)
        
        return round(total_confidence / len(vulnerabilities), 2)
    
    def _get_sample_vulnerabilities(self, target: str, options: VulnerabilityAssessmentOptions) -> List[Dict[str, Any]]:
        """Generate sample vulnerabilities for demonstration"""
        return [
            {
                "id": "vuln_001",
                "title": "Missing Security Headers",
                "description": "The target lacks important security headers that protect against common web attacks",
                "severity": "Medium",
                "category": "Web Application Security",
                "cwe_id": "CWE-693",
                "attack_vector": "Client-side attacks via missing security headers",
                "impact": "Increased susceptibility to XSS, clickjacking, and MIME-type attacks",
                "confidence": "high",
                "evidence": "Analysis of HTTP response headers"
            },
            {
                "id": "vuln_002", 
                "title": "Outdated Software Components",
                "description": "The target appears to use outdated software components with known vulnerabilities",
                "severity": "High",
                "category": "Software Vulnerability",
                "cwe_id": "CWE-1104",
                "attack_vector": "Exploitation of known vulnerabilities in outdated components",
                "impact": "Potential remote code execution or data compromise",
                "confidence": "medium",
                "evidence": "Version detection and vulnerability database correlation"
            }
        ]
    
    def _get_default_cvss(self, severity: str) -> Dict[str, Any]:
        """Get default CVSS score based on severity"""
        cvss_defaults = {
            "Critical": {"base_score": 9.0, "base_severity": "Critical"},
            "High": {"base_score": 7.5, "base_severity": "High"},
            "Medium": {"base_score": 5.0, "base_severity": "Medium"},
            "Low": {"base_score": 2.5, "base_severity": "Low"},
            "Info": {"base_score": 0.0, "base_severity": "None"}
        }
        
        default = cvss_defaults.get(severity, cvss_defaults["Medium"])
        return {
            "cvss_version": "3.1",
            "base_score": default["base_score"],
            "base_severity": default["base_severity"],
            "vector_string": f"CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:L/I:L/A:L",
            "estimated": True
        }
    
    def _get_default_remediation(self, vuln: Dict[str, Any]) -> Dict[str, Any]:
        """Get default remediation guidance"""
        return {
            "immediate_steps": ["Review vulnerability details", "Assess impact on systems"],
            "long_term_fixes": ["Implement appropriate security controls", "Update affected components"],
            "prevention": ["Regular security assessments", "Security awareness training"],
            "validation": ["Verify fix implementation", "Re-test affected areas"],
            "effort_level": "Medium",
            "timeline": "Days"
        }
    
    def _get_default_exploit_analysis(self, vuln: Dict[str, Any]) -> Dict[str, Any]:
        """Get default exploit analysis"""
        return {
            "exploit_likelihood": "Medium",
            "exploit_difficulty": "Moderate",
            "public_exploits": False,
            "weaponization": "Proof of Concept",
            "attack_complexity": "Requires specific conditions",
            "prerequisites": ["Network access", "Basic technical knowledge"],
            "threat_actors": ["Opportunistic attackers"],
            "detection_difficulty": "Moderate"
        }