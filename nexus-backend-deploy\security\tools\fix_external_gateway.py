#!/usr/bin/env python3
"""
Fix the external_tool_gateway.py file that has escaped newlines
"""

def fix_external_gateway():
    """Fix the external tool gateway file"""
    gateway_path = "src/security/tools/external_tool_gateway.py"
    
    try:
        # Read the file
        with open(gateway_path, 'r') as f:
            content = f.read()
        
        # Replace escaped newlines with actual newlines
        fixed_content = content.replace('\\n', '\n')
        
        # Write back the fixed content
        with open(gateway_path, 'w') as f:
            f.write(fixed_content)
        
        print(f"✅ Fixed escaped newlines in {gateway_path}")
        print(f"   File now has {len(fixed_content.split('\\n'))} lines")
        
    except Exception as e:
        print(f"❌ Error fixing {gateway_path}: {e}")

if __name__ == "__main__":
    fix_external_gateway()