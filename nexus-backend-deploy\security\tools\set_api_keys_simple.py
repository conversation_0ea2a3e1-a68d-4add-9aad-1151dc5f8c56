#!/usr/bin/env python3
"""
Simple API Key Setup for NexusScan
Alternative method for setting API keys via arguments or environment variables
"""

import sys
import os
import argparse
from pathlib import Path

# Add src to Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from core.config import Config


def set_api_keys(openai_key=None, deepseek_key=None):
    """Set API keys programmatically"""
    print("🔑 NexusScan API Key Configuration")
    print("=" * 40)
    
    try:
        config = Config()
        print("✅ Configuration system loaded")
        
        # Check current status
        current_openai = config.get_api_key("openai")
        current_deepseek = config.get_api_key("deepseek")
        
        print(f"\n📊 Current Status:")
        print(f"   OpenAI: {'✅ Set' if current_openai else '❌ Not set'}")
        print(f"   DeepSeek: {'✅ Set' if current_deepseek else '❌ Not set'}")
        
        # Set OpenAI key
        if openai_key:
            config.set_api_key("openai", openai_key)
            print(f"✅ OpenAI API key updated")
        elif not current_openai:
            print(f"ℹ️  No OpenAI key provided")
        
        # Set DeepSeek key
        if deepseek_key:
            config.set_api_key("deepseek", deepseek_key)
            print(f"✅ DeepSeek API key updated")
        elif not current_deepseek:
            print(f"ℹ️  No DeepSeek key provided")
        
        # Final status
        config = Config()  # Reload to verify
        final_openai = config.get_api_key("openai")
        final_deepseek = config.get_api_key("deepseek")
        
        print(f"\n🎯 Final Configuration:")
        print(f"   OpenAI: {'✅ Configured' if final_openai else '❌ Not set'}")
        print(f"   DeepSeek: {'✅ Configured' if final_deepseek else '❌ Not set'}")
        
        if final_openai:
            print(f"   OpenAI preview: {final_openai[:8]}...{final_openai[-4:]}")
        if final_deepseek:
            print(f"   DeepSeek preview: {final_deepseek[:8]}...{final_deepseek[-4:]}")
        
        if final_openai or final_deepseek:
            print(f"\n🔗 Next Steps:")
            print(f"   1. Test AI services: python3 test_ai_services_real.py")
            print(f"   2. Run application: python3 src/main.py")
            
            return True
        else:
            print(f"\n❌ No API keys configured")
            return False
            
    except Exception as e:
        print(f"❌ Failed to configure API keys: {e}")
        return False


def show_usage():
    """Show usage examples"""
    print("🔑 NexusScan API Key Setup")
    print("=" * 30)
    print()
    print("📖 Usage Options:")
    print()
    print("1️⃣ Set keys via command line:")
    print("   python3 set_api_keys_simple.py --openai sk-your-openai-key")
    print("   python3 set_api_keys_simple.py --deepseek sk-your-deepseek-key")
    print("   python3 set_api_keys_simple.py --openai sk-key1 --deepseek sk-key2")
    print()
    print("2️⃣ Set keys via environment variables:")
    print("   export OPENAI_API_KEY='sk-your-openai-key'")
    print("   export DEEPSEEK_API_KEY='sk-your-deepseek-key'")
    print("   python3 set_api_keys_simple.py --from-env")
    print()
    print("3️⃣ Show current configuration:")
    print("   python3 set_api_keys_simple.py --show")
    print()
    print("🔗 Get your API keys:")
    print("   OpenAI: https://platform.openai.com/api-keys")
    print("   DeepSeek: https://platform.deepseek.com/api_keys")


def show_config():
    """Show current configuration"""
    print("📊 Current NexusScan Configuration")
    print("=" * 35)
    
    try:
        config = Config()
        
        openai_key = config.get_api_key("openai")
        deepseek_key = config.get_api_key("deepseek")
        
        print(f"\n🤖 AI Configuration:")
        print(f"   Primary provider: {config.ai.preferred_provider}")
        print(f"   OpenAI model: {config.ai.openai_model}")
        print(f"   DeepSeek model: {config.ai.deepseek_model}")
        print(f"   Temperature: {config.ai.temperature}")
        print(f"   Max tokens: {config.ai.max_tokens}")
        
        print(f"\n🔑 API Keys:")
        print(f"   OpenAI: {'✅ Set' if openai_key else '❌ Not set'}")
        print(f"   DeepSeek: {'✅ Set' if deepseek_key else '❌ Not set'}")
        
        if openai_key:
            print(f"   OpenAI preview: {openai_key[:8]}...{openai_key[-4:]}")
        if deepseek_key:
            print(f"   DeepSeek preview: {deepseek_key[:8]}...{deepseek_key[-4:]}")
        
        print(f"\n🔐 Storage:")
        print(f"   Config directory: {config.config_dir}")
        print(f"   Secure config: {config.secure_config_file}")
        encryption_status = "✅ Enabled" if hasattr(config.security, 'encryption_key') and config.security.encryption_key else "❌ Disabled"
        print(f"   Encryption: {encryption_status}")
        
    except Exception as e:
        print(f"❌ Failed to load configuration: {e}")


def main():
    parser = argparse.ArgumentParser(description="NexusScan API Key Setup")
    parser.add_argument("--openai", help="OpenAI API key")
    parser.add_argument("--deepseek", help="DeepSeek API key")
    parser.add_argument("--from-env", action="store_true", help="Load keys from environment variables")
    parser.add_argument("--show", action="store_true", help="Show current configuration")
    parser.add_argument("--help-usage", action="store_true", help="Show detailed usage examples")
    
    # If no arguments provided, show usage
    if len(sys.argv) == 1:
        show_usage()
        return
    
    args = parser.parse_args()
    
    if args.help_usage:
        show_usage()
        return
    
    if args.show:
        show_config()
        return
    
    # Load from environment if requested
    openai_key = args.openai
    deepseek_key = args.deepseek
    
    if args.from_env:
        openai_key = openai_key or os.getenv("OPENAI_API_KEY")
        deepseek_key = deepseek_key or os.getenv("DEEPSEEK_API_KEY")
        
        if openai_key:
            print(f"✅ Found OpenAI key in environment")
        if deepseek_key:
            print(f"✅ Found DeepSeek key in environment")
    
    if not openai_key and not deepseek_key:
        print("❌ No API keys provided")
        print("Use --help for usage information")
        return
    
    # Set the keys
    success = set_api_keys(openai_key, deepseek_key)
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()