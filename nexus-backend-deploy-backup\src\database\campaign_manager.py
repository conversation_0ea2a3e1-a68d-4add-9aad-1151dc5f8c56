#!/usr/bin/env python3
"""
Campaign Management System for NexusScan Desktop Application
Handles creation, management, and lifecycle of security testing campaigns
"""

import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, desc, asc, func

from .models import (
    Campaign, Scan, Vulnerability, AIAnalysis, ScanTemplate,
    CampaignStatus, ScanStatus, VulnerabilitySeverity, VulnerabilityStatus,
    DatabaseManager, DatabaseQueries
)

logger = logging.getLogger(__name__)


@dataclass
class CampaignCreateRequest:
    """Request structure for creating a new campaign"""
    name: str
    description: str
    target_scope: List[str]
    owner: str = "default"
    tags: List[str] = None
    settings: Dict[str, Any] = None

    def __post_init__(self):
        if self.tags is None:
            self.tags = []
        if self.settings is None:
            self.settings = {}


@dataclass
class CampaignUpdateRequest:
    """Request structure for updating an existing campaign"""
    name: Optional[str] = None
    description: Optional[str] = None
    target_scope: Optional[List[str]] = None
    status: Optional[str] = None
    tags: Optional[List[str]] = None
    settings: Optional[Dict[str, Any]] = None


@dataclass
class CampaignStats:
    """Campaign statistics"""
    total_scans: int
    completed_scans: int
    total_vulnerabilities: int
    critical_vulnerabilities: int
    high_vulnerabilities: int
    medium_vulnerabilities: int
    low_vulnerabilities: int
    info_vulnerabilities: int
    average_cvss_score: float
    scan_success_rate: float
    estimated_completion: Optional[datetime]


@dataclass
class CampaignSummary:
    """Campaign summary information"""
    id: int
    name: str
    description: str
    status: str
    created_at: datetime
    updated_at: datetime
    owner: str
    target_count: int
    stats: CampaignStats


class CampaignManager:
    """Manages security testing campaigns"""

    def __init__(self, db_manager: DatabaseManager):
        """Initialize campaign manager
        
        Args:
            db_manager: Database manager instance
        """
        self.db_manager = db_manager
        self.logger = logging.getLogger(__name__)

    def create_campaign(self, request: CampaignCreateRequest) -> Campaign:
        """Create a new security testing campaign
        
        Args:
            request: Campaign creation request
            
        Returns:
            Created campaign object
            
        Raises:
            ValueError: If campaign name already exists
        """
        session = self.db_manager.get_session()
        try:
            # Check if campaign name already exists
            existing = session.query(Campaign).filter_by(name=request.name).first()
            if existing:
                raise ValueError(f"Campaign with name '{request.name}' already exists")
            
            # Create campaign
            campaign = Campaign(
                name=request.name,
                description=request.description,
                target_scope=request.target_scope,
                owner=request.owner,
                tags=request.tags,
                settings=request.settings,
                status=CampaignStatus.DRAFT.value
            )
            
            session.add(campaign)
            session.commit()
            session.refresh(campaign)
            
            self.logger.info(f"Created campaign: {campaign.name} (ID: {campaign.id})")
            return campaign
            
        except Exception as e:
            session.rollback()
            self.logger.error(f"Failed to create campaign: {e}")
            raise
        finally:
            session.close()

    def get_campaign(self, campaign_id: int) -> Optional[Campaign]:
        """Get campaign by ID
        
        Args:
            campaign_id: Campaign ID
            
        Returns:
            Campaign object or None if not found
        """
        session = self.db_manager.get_session()
        try:
            return session.query(Campaign).filter_by(id=campaign_id).first()
        finally:
            session.close()

    def get_campaign_by_name(self, name: str) -> Optional[Campaign]:
        """Get campaign by name
        
        Args:
            name: Campaign name
            
        Returns:
            Campaign object or None if not found
        """
        session = self.db_manager.get_session()
        try:
            return session.query(Campaign).filter_by(name=name).first()
        finally:
            session.close()

    def update_campaign(self, campaign_id: int, request: CampaignUpdateRequest) -> Optional[Campaign]:
        """Update existing campaign
        
        Args:
            campaign_id: Campaign ID to update
            request: Update request with new values
            
        Returns:
            Updated campaign object or None if not found
        """
        session = self.db_manager.get_session()
        try:
            campaign = session.query(Campaign).filter_by(id=campaign_id).first()
            if not campaign:
                return None
            
            # Update provided fields
            if request.name is not None:
                # Check name uniqueness
                existing = session.query(Campaign).filter(
                    and_(Campaign.name == request.name, Campaign.id != campaign_id)
                ).first()
                if existing:
                    raise ValueError(f"Campaign name '{request.name}' already exists")
                campaign.name = request.name
            
            if request.description is not None:
                campaign.description = request.description
            
            if request.target_scope is not None:
                campaign.target_scope = request.target_scope
            
            if request.status is not None:
                campaign.status = request.status
            
            if request.tags is not None:
                campaign.tags = request.tags
            
            if request.settings is not None:
                campaign.settings = request.settings
            
            campaign.updated_at = datetime.utcnow()
            session.commit()
            session.refresh(campaign)
            
            self.logger.info(f"Updated campaign: {campaign.name} (ID: {campaign.id})")
            return campaign
            
        except Exception as e:
            session.rollback()
            self.logger.error(f"Failed to update campaign {campaign_id}: {e}")
            raise
        finally:
            session.close()

    def delete_campaign(self, campaign_id: int) -> bool:
        """Delete campaign and all associated data
        
        Args:
            campaign_id: Campaign ID to delete
            
        Returns:
            True if deleted successfully, False if not found
        """
        session = self.db_manager.get_session()
        try:
            campaign = session.query(Campaign).filter_by(id=campaign_id).first()
            if not campaign:
                return False
            
            # Delete campaign (cascades to all related data)
            session.delete(campaign)
            session.commit()
            
            self.logger.info(f"Deleted campaign: {campaign.name} (ID: {campaign_id})")
            return True
            
        except Exception as e:
            session.rollback()
            self.logger.error(f"Failed to delete campaign {campaign_id}: {e}")
            raise
        finally:
            session.close()

    def list_campaigns(self, 
                      owner: Optional[str] = None,
                      status: Optional[str] = None,
                      tags: Optional[List[str]] = None,
                      limit: int = 100,
                      offset: int = 0,
                      sort_by: str = "updated_at",
                      sort_desc: bool = True) -> List[Campaign]:
        """List campaigns with filtering and pagination
        
        Args:
            owner: Filter by owner
            status: Filter by status
            tags: Filter by tags (must contain all tags)
            limit: Maximum number of results
            offset: Pagination offset
            sort_by: Sort field
            sort_desc: Sort descending if True
            
        Returns:
            List of campaign objects
        """
        session = self.db_manager.get_session()
        try:
            query = session.query(Campaign)
            
            # Apply filters
            if owner:
                query = query.filter(Campaign.owner == owner)
            
            if status:
                query = query.filter(Campaign.status == status)
            
            if tags:
                # Filter campaigns that contain all specified tags
                for tag in tags:
                    query = query.filter(Campaign.tags.contains([tag]))
            
            # Apply sorting
            sort_column = getattr(Campaign, sort_by, Campaign.updated_at)
            if sort_desc:
                query = query.order_by(desc(sort_column))
            else:
                query = query.order_by(asc(sort_column))
            
            # Apply pagination
            query = query.offset(offset).limit(limit)
            
            return query.all()
            
        finally:
            session.close()

    def get_campaign_summary(self, campaign_id: int) -> Optional[CampaignSummary]:
        """Get comprehensive campaign summary with statistics
        
        Args:
            campaign_id: Campaign ID
            
        Returns:
            Campaign summary or None if not found
        """
        session = self.db_manager.get_session()
        try:
            campaign = session.query(Campaign).filter_by(id=campaign_id).first()
            if not campaign:
                return None
            
            # Calculate statistics
            stats = self._calculate_campaign_stats(session, campaign_id)
            
            return CampaignSummary(
                id=campaign.id,
                name=campaign.name,
                description=campaign.description,
                status=campaign.status,
                created_at=campaign.created_at,
                updated_at=campaign.updated_at,
                owner=campaign.owner,
                target_count=len(campaign.target_scope) if campaign.target_scope else 0,
                stats=stats
            )
            
        finally:
            session.close()

    def start_campaign(self, campaign_id: int) -> bool:
        """Start a campaign (change status from draft to running)
        
        Args:
            campaign_id: Campaign ID
            
        Returns:
            True if started successfully, False if not found or invalid state
        """
        session = self.db_manager.get_session()
        try:
            campaign = session.query(Campaign).filter_by(id=campaign_id).first()
            if not campaign:
                return False
            
            if campaign.status != CampaignStatus.DRAFT.value:
                self.logger.warning(f"Cannot start campaign {campaign_id}: status is {campaign.status}")
                return False
            
            campaign.status = CampaignStatus.RUNNING.value
            campaign.updated_at = datetime.utcnow()
            session.commit()
            
            self.logger.info(f"Started campaign: {campaign.name} (ID: {campaign_id})")
            return True
            
        except Exception as e:
            session.rollback()
            self.logger.error(f"Failed to start campaign {campaign_id}: {e}")
            raise
        finally:
            session.close()

    def pause_campaign(self, campaign_id: int) -> bool:
        """Pause a running campaign
        
        Args:
            campaign_id: Campaign ID
            
        Returns:
            True if paused successfully, False if not found or invalid state
        """
        session = self.db_manager.get_session()
        try:
            campaign = session.query(Campaign).filter_by(id=campaign_id).first()
            if not campaign:
                return False
            
            if campaign.status != CampaignStatus.RUNNING.value:
                self.logger.warning(f"Cannot pause campaign {campaign_id}: status is {campaign.status}")
                return False
            
            campaign.status = CampaignStatus.PAUSED.value
            campaign.updated_at = datetime.utcnow()
            session.commit()
            
            self.logger.info(f"Paused campaign: {campaign.name} (ID: {campaign_id})")
            return True
            
        except Exception as e:
            session.rollback()
            self.logger.error(f"Failed to pause campaign {campaign_id}: {e}")
            raise
        finally:
            session.close()

    def complete_campaign(self, campaign_id: int) -> bool:
        """Mark a campaign as completed
        
        Args:
            campaign_id: Campaign ID
            
        Returns:
            True if completed successfully, False if not found
        """
        session = self.db_manager.get_session()
        try:
            campaign = session.query(Campaign).filter_by(id=campaign_id).first()
            if not campaign:
                return False
            
            campaign.status = CampaignStatus.COMPLETED.value
            campaign.updated_at = datetime.utcnow()
            session.commit()
            
            self.logger.info(f"Completed campaign: {campaign.name} (ID: {campaign_id})")
            return True
            
        except Exception as e:
            session.rollback()
            self.logger.error(f"Failed to complete campaign {campaign_id}: {e}")
            raise
        finally:
            session.close()

    def get_campaign_scans(self, campaign_id: int, 
                          status: Optional[str] = None,
                          scan_type: Optional[str] = None,
                          limit: int = 100) -> List[Scan]:
        """Get scans for a campaign
        
        Args:
            campaign_id: Campaign ID
            status: Filter by scan status
            scan_type: Filter by scan type
            limit: Maximum number of results
            
        Returns:
            List of scan objects
        """
        session = self.db_manager.get_session()
        try:
            query = session.query(Scan).filter(Scan.campaign_id == campaign_id)
            
            if status:
                query = query.filter(Scan.status == status)
            
            if scan_type:
                query = query.filter(Scan.scan_type == scan_type)
            
            query = query.order_by(desc(Scan.started_at)).limit(limit)
            return query.all()
            
        finally:
            session.close()

    def get_campaign_vulnerabilities(self, campaign_id: int,
                                   severity: Optional[str] = None,
                                   status: Optional[str] = None,
                                   vuln_type: Optional[str] = None,
                                   limit: int = 100) -> List[Vulnerability]:
        """Get vulnerabilities for a campaign
        
        Args:
            campaign_id: Campaign ID
            severity: Filter by severity
            status: Filter by vulnerability status
            vuln_type: Filter by vulnerability type
            limit: Maximum number of results
            
        Returns:
            List of vulnerability objects
        """
        session = self.db_manager.get_session()
        try:
            query = session.query(Vulnerability).filter(Vulnerability.campaign_id == campaign_id)
            
            if severity:
                query = query.filter(Vulnerability.severity == severity)
            
            if status:
                query = query.filter(Vulnerability.status == status)
            
            if vuln_type:
                query = query.filter(Vulnerability.vuln_type == vuln_type)
            
            query = query.order_by(desc(Vulnerability.discovered_at)).limit(limit)
            return query.all()
            
        finally:
            session.close()

    def clone_campaign(self, campaign_id: int, new_name: str) -> Optional[Campaign]:
        """Clone an existing campaign
        
        Args:
            campaign_id: Campaign ID to clone
            new_name: Name for the new campaign
            
        Returns:
            New campaign object or None if original not found
        """
        session = self.db_manager.get_session()
        try:
            original = session.query(Campaign).filter_by(id=campaign_id).first()
            if not original:
                return None
            
            # Check new name uniqueness
            existing = session.query(Campaign).filter_by(name=new_name).first()
            if existing:
                raise ValueError(f"Campaign with name '{new_name}' already exists")
            
            # Create cloned campaign
            cloned = Campaign(
                name=new_name,
                description=f"Cloned from: {original.description}" if original.description else f"Cloned from {original.name}",
                target_scope=original.target_scope.copy() if original.target_scope else [],
                owner=original.owner,
                tags=original.tags.copy() if original.tags else [],
                settings=original.settings.copy() if original.settings else {},
                status=CampaignStatus.DRAFT.value
            )
            
            session.add(cloned)
            session.commit()
            session.refresh(cloned)
            
            self.logger.info(f"Cloned campaign {original.name} -> {new_name} (ID: {cloned.id})")
            return cloned
            
        except Exception as e:
            session.rollback()
            self.logger.error(f"Failed to clone campaign {campaign_id}: {e}")
            raise
        finally:
            session.close()

    def _calculate_campaign_stats(self, session: Session, campaign_id: int) -> CampaignStats:
        """Calculate comprehensive campaign statistics
        
        Args:
            session: Database session
            campaign_id: Campaign ID
            
        Returns:
            Campaign statistics
        """
        # Scan statistics
        total_scans = session.query(Scan).filter_by(campaign_id=campaign_id).count()
        completed_scans = session.query(Scan).filter(
            and_(Scan.campaign_id == campaign_id, Scan.status == ScanStatus.COMPLETED.value)
        ).count()
        
        scan_success_rate = (completed_scans / total_scans * 100) if total_scans > 0 else 0.0
        
        # Vulnerability statistics
        total_vulns = session.query(Vulnerability).filter_by(campaign_id=campaign_id).count()
        
        # Count vulnerabilities by severity
        critical_vulns = session.query(Vulnerability).filter(
            and_(Vulnerability.campaign_id == campaign_id, 
                 Vulnerability.severity == VulnerabilitySeverity.CRITICAL.value)
        ).count()
        
        high_vulns = session.query(Vulnerability).filter(
            and_(Vulnerability.campaign_id == campaign_id, 
                 Vulnerability.severity == VulnerabilitySeverity.HIGH.value)
        ).count()
        
        medium_vulns = session.query(Vulnerability).filter(
            and_(Vulnerability.campaign_id == campaign_id, 
                 Vulnerability.severity == VulnerabilitySeverity.MEDIUM.value)
        ).count()
        
        low_vulns = session.query(Vulnerability).filter(
            and_(Vulnerability.campaign_id == campaign_id, 
                 Vulnerability.severity == VulnerabilitySeverity.LOW.value)
        ).count()
        
        info_vulns = session.query(Vulnerability).filter(
            and_(Vulnerability.campaign_id == campaign_id, 
                 Vulnerability.severity == VulnerabilitySeverity.INFO.value)
        ).count()
        
        # Average CVSS score
        avg_cvss = session.query(func.avg(Vulnerability.cvss_score)).filter(
            Vulnerability.campaign_id == campaign_id
        ).scalar() or 0.0
        
        # Estimate completion time (basic implementation)
        estimated_completion = None
        running_scans = session.query(Scan).filter(
            and_(Scan.campaign_id == campaign_id, Scan.status == ScanStatus.RUNNING.value)
        ).count()
        
        if running_scans > 0:
            # Estimate based on average scan duration
            avg_duration = session.query(func.avg(Scan.duration_seconds)).filter(
                and_(Scan.campaign_id == campaign_id, Scan.status == ScanStatus.COMPLETED.value)
            ).scalar()
            
            if avg_duration:
                estimated_completion = datetime.utcnow() + timedelta(seconds=avg_duration * running_scans)
        
        return CampaignStats(
            total_scans=total_scans,
            completed_scans=completed_scans,
            total_vulnerabilities=total_vulns,
            critical_vulnerabilities=critical_vulns,
            high_vulnerabilities=high_vulns,
            medium_vulnerabilities=medium_vulns,
            low_vulnerabilities=low_vulns,
            info_vulnerabilities=info_vulns,
            average_cvss_score=float(avg_cvss),
            scan_success_rate=scan_success_rate,
            estimated_completion=estimated_completion
        )

    def export_campaign_data(self, campaign_id: int, format: str = "json") -> Dict[str, Any]:
        """Export campaign data in specified format
        
        Args:
            campaign_id: Campaign ID
            format: Export format (json, csv, xml)
            
        Returns:
            Exported data dictionary
        """
        session = self.db_manager.get_session()
        try:
            campaign = session.query(Campaign).filter_by(id=campaign_id).first()
            if not campaign:
                raise ValueError(f"Campaign {campaign_id} not found")
            
            # Get all related data
            scans = session.query(Scan).filter_by(campaign_id=campaign_id).all()
            vulnerabilities = session.query(Vulnerability).filter_by(campaign_id=campaign_id).all()
            ai_analyses = session.query(AIAnalysis).filter_by(campaign_id=campaign_id).all()
            
            export_data = {
                "campaign": {
                    "id": campaign.id,
                    "name": campaign.name,
                    "description": campaign.description,
                    "target_scope": campaign.target_scope,
                    "status": campaign.status,
                    "owner": campaign.owner,
                    "tags": campaign.tags,
                    "settings": campaign.settings,
                    "created_at": campaign.created_at.isoformat(),
                    "updated_at": campaign.updated_at.isoformat()
                },
                "scans": [
                    {
                        "id": scan.id,
                        "name": scan.name,
                        "scan_type": scan.scan_type,
                        "target": scan.target,
                        "status": scan.status,
                        "started_at": scan.started_at.isoformat(),
                        "completed_at": scan.completed_at.isoformat() if scan.completed_at else None,
                        "duration_seconds": scan.duration_seconds,
                        "tools_used": scan.tools_used,
                        "summary": scan.summary
                    }
                    for scan in scans
                ],
                "vulnerabilities": [
                    {
                        "id": vuln.id,
                        "vuln_type": vuln.vuln_type,
                        "severity": vuln.severity,
                        "title": vuln.title,
                        "description": vuln.description,
                        "affected_url": vuln.affected_url,
                        "affected_parameter": vuln.affected_parameter,
                        "cvss_score": float(vuln.cvss_score) if vuln.cvss_score else 0.0,
                        "cve_id": vuln.cve_id,
                        "status": vuln.status,
                        "verified": vuln.verified,
                        "exploitable": vuln.exploitable,
                        "discovered_at": vuln.discovered_at.isoformat(),
                        "evidence": vuln.evidence,
                        "remediation": vuln.remediation
                    }
                    for vuln in vulnerabilities
                ],
                "ai_analyses": [
                    {
                        "id": analysis.id,
                        "analysis_type": analysis.analysis_type,
                        "ai_provider": analysis.ai_provider,
                        "model_used": analysis.model_used,
                        "confidence_score": float(analysis.confidence_score) if analysis.confidence_score else 0.0,
                        "execution_time_ms": analysis.execution_time_ms,
                        "created_at": analysis.created_at.isoformat()
                    }
                    for analysis in ai_analyses
                ],
                "export_metadata": {
                    "exported_at": datetime.utcnow().isoformat(),
                    "format": format,
                    "version": "1.0"
                }
            }
            
            return export_data
            
        finally:
            session.close()

    def get_all_campaigns(self) -> List[Campaign]:
        """Get all campaigns (alias for list_campaigns with no filters)
        
        Returns:
            List of all campaigns
        """
        return self.list_campaigns()

    def search_campaigns(self, search_term: str, limit: int = 50) -> List[Campaign]:
        """Search campaigns by name, description, or tags
        
        Args:
            search_term: Search term
            limit: Maximum number of results
            
        Returns:
            List of matching campaigns
        """
        session = self.db_manager.get_session()
        try:
            query = session.query(Campaign).filter(
                or_(
                    Campaign.name.contains(search_term),
                    Campaign.description.contains(search_term),
                    Campaign.tags.contains([search_term])
                )
            ).order_by(desc(Campaign.updated_at)).limit(limit)
            
            return query.all()
            
        finally:
            session.close()