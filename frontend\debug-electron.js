/**
 * Debug Electron startup issues
 */
const { app } = require('electron');

console.log('🔍 Debugging Electron startup...');
console.log('App path:', app.getAppPath());
console.log('User data:', app.getPath('userData'));
console.log('Is ready:', app.isReady());

// Force quit after 10 seconds if not ready
setTimeout(() => {
  if (!app.isReady()) {
    console.error('❌ App failed to become ready after 10 seconds');
    process.exit(1);
  }
}, 10000);

// Listen to all app events
const events = ['ready', 'window-all-closed', 'before-quit', 'will-quit', 'quit'];
events.forEach(event => {
  app.on(event, () => {
    console.log(`📍 App event: ${event}`);
  });
});

// Try loading the main app
try {
  require('./dist/electron/main/main.js');
} catch (error) {
  console.error('❌ Failed to load main.js:', error);
  process.exit(1);
}