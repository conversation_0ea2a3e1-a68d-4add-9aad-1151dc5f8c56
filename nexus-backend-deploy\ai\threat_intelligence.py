#!/usr/bin/env python3
"""
Threat Intelligence Integration for NexusScan
AI-powered threat intelligence aggregation, analysis, and correlation
with vulnerability findings and security assessments
"""

import asyncio
import logging
import json
import re
from datetime import datetime, timed<PERSON><PERSON>
from typing import Dict, List, Optional, Any, Union, <PERSON><PERSON>
from dataclasses import dataclass, field
from enum import Enum
import hashlib
import aiohttp

from .ai_service import (
    AIServiceProvider, AIServiceConfig, AIAnalysisRequest, AIAnalysisResult,
    AICapability, AIModelType, AnalysisType
)

logger = logging.getLogger(__name__)


class ThreatType(Enum):
    """Types of threats"""
    MALWARE = "malware"
    APT = "apt"
    RANSOMWARE = "ransomware"
    BOTNET = "botnet"
    PHISHING = "phishing"
    EXPLOIT_KIT = "exploit_kit"
    INSIDER_THREAT = "insider_threat"
    SUPPLY_CHAIN = "supply_chain"
    ZERO_DAY = "zero_day"
    CAMPAIGN = "campaign"


class ThreatSeverity(Enum):
    """Threat severity levels"""
    CRITICAL = "critical"
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"
    INFORMATIONAL = "informational"


class IOCType(Enum):
    """Indicator of Compromise types"""
    IP_ADDRESS = "ip_address"
    DOMAIN = "domain"
    URL = "url"
    FILE_HASH = "file_hash"
    EMAIL = "email"
    USER_AGENT = "user_agent"
    CERTIFICATE = "certificate"
    REGISTRY_KEY = "registry_key"
    MUTEX = "mutex"
    YARA_RULE = "yara_rule"


class IntelligenceSource(Enum):
    """Threat intelligence sources"""
    COMMERCIAL_FEED = "commercial_feed"
    OPEN_SOURCE = "open_source"
    GOVERNMENT = "government"
    INDUSTRY_SHARING = "industry_sharing"
    INTERNAL_RESEARCH = "internal_research"
    HONEYPOT = "honeypot"
    SANDBOX = "sandbox"
    COMMUNITY = "community"


@dataclass
class ThreatIndicator:
    """Individual threat indicator"""
    indicator_id: str
    type: IOCType
    value: str
    threat_types: List[ThreatType]
    severity: ThreatSeverity
    confidence: float
    first_seen: datetime
    last_seen: datetime
    source: IntelligenceSource
    description: str = ""
    tags: List[str] = field(default_factory=list)
    kill_chain_phases: List[str] = field(default_factory=list)
    ttps: List[str] = field(default_factory=list)  # Tactics, Techniques, and Procedures
    related_campaigns: List[str] = field(default_factory=list)
    geographic_regions: List[str] = field(default_factory=list)
    target_industries: List[str] = field(default_factory=list)
    malware_families: List[str] = field(default_factory=list)
    references: List[str] = field(default_factory=list)
    false_positive_likelihood: float = 0.0
    expiration_date: Optional[datetime] = None
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class ThreatCampaign:
    """Threat campaign information"""
    campaign_id: str
    name: str
    threat_actors: List[str]
    objectives: List[str]
    targets: List[str]
    start_date: datetime
    end_date: Optional[datetime]
    active: bool
    techniques_used: List[str]
    indicators: List[str]  # References to ThreatIndicator IDs
    severity: ThreatSeverity
    confidence: float
    description: str = ""
    attribution: str = ""
    motivation: List[str] = field(default_factory=list)
    sophistication_level: str = "medium"
    geographic_scope: List[str] = field(default_factory=list)
    industry_targeting: List[str] = field(default_factory=list)
    attack_vectors: List[str] = field(default_factory=list)
    impact_assessment: str = ""
    countermeasures: List[str] = field(default_factory=list)
    references: List[str] = field(default_factory=list)


@dataclass
class VulnerabilityIntelligence:
    """Intelligence about vulnerability exploitation"""
    cve_id: str
    exploitation_status: str  # "not_exploited", "poc_available", "actively_exploited"
    exploit_kits: List[str]
    threat_actors: List[str]
    campaigns_using: List[str]
    first_exploitation_date: Optional[datetime]
    exploitation_trend: str  # "increasing", "stable", "decreasing"
    difficulty_level: str  # "trivial", "easy", "medium", "hard", "expert"
    target_prevalence: float  # Percentage of vulnerable systems being targeted
    patch_adoption_rate: float
    weaponization_timeline: Optional[timedelta]
    underground_mentions: int
    social_media_mentions: int
    security_vendor_reports: List[str]
    exploit_price: Optional[float]  # Price in underground markets
    mitigation_effectiveness: Dict[str, float]


@dataclass
class ThreatLandscape:
    """Current threat landscape analysis"""
    analysis_date: datetime
    active_campaigns: List[ThreatCampaign]
    trending_threats: List[ThreatType]
    hot_vulnerabilities: List[str]  # CVE IDs
    emerging_techniques: List[str]
    geographic_hotspots: Dict[str, float]
    industry_risk_levels: Dict[str, str]
    seasonal_patterns: Dict[str, Any]
    threat_actor_activity: Dict[str, str]
    exploit_kit_activity: Dict[str, str]
    malware_trends: Dict[str, int]
    attack_vector_trends: Dict[str, float]
    defensive_recommendations: List[str]
    intelligence_gaps: List[str]
    confidence_level: float


@dataclass
class ThreatMatch:
    """Match between scan findings and threat intelligence"""
    match_id: str
    scan_finding_id: str
    threat_indicator_id: str
    match_type: str  # "exact", "partial", "behavioral", "contextual"
    confidence: float
    risk_score: float
    description: str
    implications: List[str]
    recommended_actions: List[str]
    false_positive_likelihood: float
    context: Dict[str, Any] = field(default_factory=dict)
    metadata: Dict[str, Any] = field(default_factory=dict)


class ThreatIntelligenceEngine(AIServiceProvider):
    """AI-powered threat intelligence integration engine"""

    def __init__(self, config: AIServiceConfig):
        super().__init__(config)
        
        # Intelligence feeds and sources
        self.intelligence_feeds: Dict[str, Dict[str, Any]] = {}
        self.feed_configs: Dict[str, Dict[str, Any]] = {}
        
        # Data storage
        self.threat_indicators: Dict[str, ThreatIndicator] = {}
        self.threat_campaigns: Dict[str, ThreatCampaign] = {}
        self.vulnerability_intelligence: Dict[str, VulnerabilityIntelligence] = {}
        
        # Analysis engines
        self.correlation_engine = None
        self.prediction_models: Dict[str, Any] = {}
        
        # Caching and performance
        self.analysis_cache: Dict[str, Any] = {}
        self.cache_ttl = timedelta(hours=6)
        
        # Statistics
        self.intelligence_stats = {
            "indicators_processed": 0,
            "campaigns_tracked": 0,
            "correlations_found": 0,
            "false_positives_filtered": 0,
            "feeds_active": 0,
            "last_update": None,
            "coverage_percentage": 0.0
        }

    async def analyze(self, request: AIAnalysisRequest) -> AIAnalysisResult:
        """Perform threat intelligence analysis
        
        Args:
            request: Analysis request
            
        Returns:
            Analysis result with threat intelligence
        """
        start_time = datetime.now()
        
        try:
            if request.capability != AICapability.THREAT_INTELLIGENCE:
                raise ValueError(f"Unsupported capability: {request.capability}")
            
            # Extract context
            context = request.context
            analysis_type = request.analysis_type
            
            if analysis_type == AnalysisType.THREAT_ASSESSMENT:
                result_data = await self._assess_threats(context)
            elif analysis_type == AnalysisType.VULNERABILITY_ANALYSIS:
                result_data = await self._analyze_vulnerability_intelligence(context)
            elif analysis_type == AnalysisType.BEHAVIORAL_PATTERN:
                result_data = await self._analyze_behavioral_patterns(context)
            else:
                result_data = await self._general_threat_analysis(context)
            
            # Generate threat landscape
            threat_landscape = await self._generate_threat_landscape()
            
            # Find correlations with scan data
            if "scan_results" in context:
                correlations = await self._correlate_with_scan_results(context["scan_results"])
                result_data["correlations"] = correlations
            
            # Generate actionable intelligence
            actionable_intel = self._generate_actionable_intelligence(result_data, threat_landscape)
            
            result = AIAnalysisResult(
                request_id=request.request_id,
                analysis_type=request.analysis_type,
                capability=request.capability,
                success=True,
                confidence=0.8,
                result={
                    "threat_analysis": result_data,
                    "threat_landscape": threat_landscape.__dict__,
                    "actionable_intelligence": actionable_intel
                },
                recommendations=actionable_intel.get("recommendations", []),
                insights=actionable_intel.get("insights", []),
                processing_time=(datetime.now() - start_time).total_seconds(),
                model_used=self.config.service_name
            )
            
            self._update_statistics(result)
            return result
            
        except Exception as e:
            self.logger.error(f"Threat intelligence analysis failed: {e}")
            return AIAnalysisResult(
                request_id=request.request_id,
                analysis_type=request.analysis_type,
                capability=request.capability,
                success=False,
                confidence=0.0,
                result={},
                error_message=str(e),
                processing_time=(datetime.now() - start_time).total_seconds(),
                model_used=self.config.service_name
            )

    async def ingest_threat_feed(self, feed_name: str, feed_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Ingest threat intelligence feed data
        
        Args:
            feed_name: Name of the threat feed
            feed_data: Raw feed data
            
        Returns:
            Ingestion results
        """
        ingestion_results = {
            "feed_name": feed_name,
            "indicators_processed": 0,
            "indicators_added": 0,
            "indicators_updated": 0,
            "campaigns_identified": 0,
            "processing_errors": [],
            "processing_time": 0.0
        }
        
        start_time = datetime.now()
        
        try:
            for item in feed_data:
                try:
                    # Parse threat indicator
                    indicator = self._parse_threat_indicator(item, feed_name)
                    if indicator:
                        existing = self.threat_indicators.get(indicator.indicator_id)
                        if existing:
                            # Update existing indicator
                            self._update_threat_indicator(existing, indicator)
                            ingestion_results["indicators_updated"] += 1
                        else:
                            # Add new indicator
                            self.threat_indicators[indicator.indicator_id] = indicator
                            ingestion_results["indicators_added"] += 1
                        
                        ingestion_results["indicators_processed"] += 1
                    
                    # Parse campaign information if present
                    if "campaign" in item:
                        campaign = self._parse_threat_campaign(item["campaign"])
                        if campaign:
                            self.threat_campaigns[campaign.campaign_id] = campaign
                            ingestion_results["campaigns_identified"] += 1
                
                except Exception as e:
                    ingestion_results["processing_errors"].append(str(e))
            
            # Update feed statistics
            self.intelligence_stats["indicators_processed"] += ingestion_results["indicators_processed"]
            self.intelligence_stats["campaigns_tracked"] = len(self.threat_campaigns)
            self.intelligence_stats["last_update"] = datetime.now()
            
            ingestion_results["processing_time"] = (datetime.now() - start_time).total_seconds()
            
        except Exception as e:
            self.logger.error(f"Feed ingestion failed for {feed_name}: {e}")
            ingestion_results["processing_errors"].append(str(e))
        
        return ingestion_results

    async def correlate_scan_findings(self, scan_findings: List[Dict[str, Any]]) -> List[ThreatMatch]:
        """Correlate scan findings with threat intelligence
        
        Args:
            scan_findings: List of vulnerability findings from scans
            
        Returns:
            List of threat matches
        """
        matches = []
        
        for finding in scan_findings:
            try:
                # Extract IoCs from finding
                iocs = self._extract_iocs_from_finding(finding)
                
                # Find matches in threat intelligence
                for ioc in iocs:
                    threat_matches = await self._find_threat_matches(ioc, finding)
                    matches.extend(threat_matches)
                
                # Check for behavioral patterns
                behavioral_matches = await self._find_behavioral_matches(finding)
                matches.extend(behavioral_matches)
                
                # Check vulnerability intelligence
                if "cve_id" in finding:
                    vuln_intel = self.vulnerability_intelligence.get(finding["cve_id"])
                    if vuln_intel:
                        match = self._create_vulnerability_match(finding, vuln_intel)
                        matches.append(match)
            
            except Exception as e:
                self.logger.error(f"Error correlating finding {finding.get('id', 'unknown')}: {e}")
        
        # Deduplicate and rank matches
        unique_matches = self._deduplicate_matches(matches)
        ranked_matches = self._rank_matches(unique_matches)
        
        self.intelligence_stats["correlations_found"] += len(ranked_matches)
        
        return ranked_matches

    async def get_vulnerability_intelligence(self, cve_id: str) -> Optional[VulnerabilityIntelligence]:
        """Get intelligence about a specific vulnerability
        
        Args:
            cve_id: CVE identifier
            
        Returns:
            Vulnerability intelligence or None if not found
        """
        # Check cache first
        cache_key = f"vuln_intel_{cve_id}"
        cached = self._get_cached_result(cache_key)
        if cached:
            return cached
        
        # Get from intelligence database
        vuln_intel = self.vulnerability_intelligence.get(cve_id)
        
        if not vuln_intel:
            # Try to gather intelligence from feeds
            vuln_intel = await self._gather_vulnerability_intelligence(cve_id)
            if vuln_intel:
                self.vulnerability_intelligence[cve_id] = vuln_intel
        
        # Cache result
        if vuln_intel:
            self._cache_result(cache_key, vuln_intel)
        
        return vuln_intel

    async def predict_threat_trends(self, timeframe: timedelta = timedelta(days=30)) -> Dict[str, Any]:
        """Predict threat trends for the given timeframe
        
        Args:
            timeframe: Prediction timeframe
            
        Returns:
            Threat trend predictions
        """
        predictions = {
            "timeframe": timeframe.days,
            "threat_type_trends": {},
            "vulnerability_trends": {},
            "campaign_predictions": [],
            "geographic_trends": {},
            "confidence": 0.0
        }
        
        try:
            # Analyze historical data
            historical_data = self._analyze_historical_trends()
            
            # Predict threat type trends
            predictions["threat_type_trends"] = self._predict_threat_types(historical_data, timeframe)
            
            # Predict vulnerability exploitation trends
            predictions["vulnerability_trends"] = self._predict_vulnerability_trends(historical_data, timeframe)
            
            # Predict new campaigns
            predictions["campaign_predictions"] = self._predict_new_campaigns(historical_data, timeframe)
            
            # Geographic trend predictions
            predictions["geographic_trends"] = self._predict_geographic_trends(historical_data, timeframe)
            
            # Calculate overall confidence
            predictions["confidence"] = self._calculate_prediction_confidence(predictions)
            
        except Exception as e:
            self.logger.error(f"Threat trend prediction failed: {e}")
        
        return predictions

    async def is_available(self) -> bool:
        """Check if threat intelligence engine is available"""
        try:
            # Check if we have threat data
            if not self.threat_indicators:
                return False
            
            # Check if feeds are recent
            last_update = self.intelligence_stats.get("last_update")
            if last_update:
                age = datetime.now() - last_update
                return age < timedelta(days=1)
            
            return True
            
        except Exception as e:
            self.logger.error(f"Availability check failed: {e}")
            return False

    async def health_check(self) -> Dict[str, Any]:
        """Perform health check"""
        health_info = {
            "status": "healthy",
            "indicators_loaded": len(self.threat_indicators),
            "campaigns_tracked": len(self.threat_campaigns),
            "feeds_configured": len(self.intelligence_feeds),
            "last_update": self.intelligence_stats.get("last_update"),
            "coverage": self.intelligence_stats.get("coverage_percentage", 0.0),
            "error": None
        }
        
        try:
            available = await self.is_available()
            
            if not available:
                health_info["status"] = "unhealthy"
                health_info["error"] = "Insufficient or stale threat data"
            
            # Check data freshness
            last_update = self.intelligence_stats.get("last_update")
            if last_update:
                age = datetime.now() - last_update
                if age > timedelta(hours=12):
                    health_info["status"] = "degraded"
                    health_info["error"] = f"Data is {age.total_seconds()/3600:.1f} hours old"
                
        except Exception as e:
            health_info["status"] = "unhealthy"
            health_info["error"] = str(e)
        
        return health_info

    # Private methods

    async def _assess_threats(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Assess threats based on context"""
        assessment = {
            "threat_level": "medium",
            "active_threats": [],
            "threat_actors": [],
            "attack_vectors": [],
            "indicators_found": [],
            "risk_factors": []
        }
        
        # Analyze for active threats
        target_info = context.get("target_info", {})
        if target_info:
            # Check if target matches known campaign targets
            for campaign in self.threat_campaigns.values():
                if self._target_matches_campaign(target_info, campaign):
                    assessment["active_threats"].append(campaign.name)
                    assessment["threat_actors"].extend(campaign.threat_actors)
        
        # Check for relevant indicators
        if "network_indicators" in context:
            for indicator_value in context["network_indicators"]:
                matching_indicators = self._find_matching_indicators(indicator_value)
                assessment["indicators_found"].extend(matching_indicators)
        
        # Assess overall threat level
        assessment["threat_level"] = self._calculate_threat_level(assessment)
        
        return assessment

    async def _analyze_vulnerability_intelligence(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze vulnerability-specific intelligence"""
        analysis = {
            "exploitation_status": "unknown",
            "threat_actors_using": [],
            "exploit_availability": "unknown",
            "underground_activity": 0,
            "patch_urgency": "medium"
        }
        
        cve_id = context.get("cve_id")
        if cve_id and cve_id in self.vulnerability_intelligence:
            vuln_intel = self.vulnerability_intelligence[cve_id]
            analysis.update({
                "exploitation_status": vuln_intel.exploitation_status,
                "threat_actors_using": vuln_intel.threat_actors,
                "exploit_availability": "available" if vuln_intel.exploit_kits else "unknown",
                "underground_activity": vuln_intel.underground_mentions,
                "patch_urgency": self._calculate_patch_urgency(vuln_intel)
            })
        
        return analysis

    async def _analyze_behavioral_patterns(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze behavioral patterns in context data"""
        patterns = {
            "suspicious_patterns": [],
            "ttp_matches": [],
            "kill_chain_stage": "unknown",
            "campaign_indicators": []
        }
        
        # Analyze network behavior
        if "network_traffic" in context:
            suspicious = self._analyze_network_patterns(context["network_traffic"])
            patterns["suspicious_patterns"].extend(suspicious)
        
        # Match against known TTPs
        if "observed_techniques" in context:
            ttp_matches = self._match_ttps(context["observed_techniques"])
            patterns["ttp_matches"] = ttp_matches
        
        return patterns

    async def _general_threat_analysis(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Perform general threat analysis"""
        return {
            "threat_landscape": await self._generate_threat_landscape(),
            "relevant_campaigns": self._find_relevant_campaigns(context),
            "risk_assessment": self._assess_contextual_risk(context)
        }

    async def _generate_threat_landscape(self) -> ThreatLandscape:
        """Generate current threat landscape"""
        active_campaigns = [c for c in self.threat_campaigns.values() if c.active]
        
        # Analyze trending threats
        threat_counts = {}
        for indicator in self.threat_indicators.values():
            for threat_type in indicator.threat_types:
                threat_counts[threat_type] = threat_counts.get(threat_type, 0) + 1
        
        trending_threats = sorted(threat_counts.keys(), 
                                key=lambda x: threat_counts[x], reverse=True)[:5]
        
        # Find hot vulnerabilities
        hot_vulns = []
        for cve_id, vuln_intel in self.vulnerability_intelligence.items():
            if vuln_intel.exploitation_status == "actively_exploited":
                hot_vulns.append(cve_id)
        
        return ThreatLandscape(
            analysis_date=datetime.now(),
            active_campaigns=active_campaigns,
            trending_threats=trending_threats,
            hot_vulnerabilities=hot_vulns[:10],
            emerging_techniques=self._identify_emerging_techniques(),
            geographic_hotspots=self._calculate_geographic_risks(),
            industry_risk_levels=self._calculate_industry_risks(),
            threat_actor_activity=self._analyze_threat_actor_activity(),
            defensive_recommendations=self._generate_defensive_recommendations(),
            confidence_level=0.75
        )

    async def _correlate_with_scan_results(self, scan_results: Dict[str, Any]) -> List[ThreatMatch]:
        """Correlate threat intelligence with scan results"""
        correlations = []
        
        findings = scan_results.get("findings", [])
        for finding in findings:
            matches = await self._find_threat_matches_for_finding(finding)
            correlations.extend(matches)
        
        return correlations

    def _generate_actionable_intelligence(self, threat_data: Dict[str, Any], 
                                        landscape: ThreatLandscape) -> Dict[str, Any]:
        """Generate actionable intelligence"""
        actionable = {
            "immediate_actions": [],
            "strategic_recommendations": [],
            "monitoring_priorities": [],
            "insights": []
        }
        
        # Immediate actions based on active threats
        if landscape.active_campaigns:
            actionable["immediate_actions"].append(
                f"Monitor for indicators from {len(landscape.active_campaigns)} active campaigns"
            )
        
        # Strategic recommendations
        if landscape.hot_vulnerabilities:
            actionable["strategic_recommendations"].append(
                f"Prioritize patching for {len(landscape.hot_vulnerabilities)} actively exploited vulnerabilities"
            )
        
        # Monitoring priorities
        for threat_type in landscape.trending_threats[:3]:
            actionable["monitoring_priorities"].append(f"Increase monitoring for {threat_type.value} indicators")
        
        # Generate insights
        actionable["insights"] = self._generate_threat_insights(threat_data, landscape)
        
        return actionable

    # Threat indicator and campaign parsing

    def _parse_threat_indicator(self, data: Dict[str, Any], source: str) -> Optional[ThreatIndicator]:
        """Parse threat indicator from feed data"""
        try:
            # Extract indicator type and value
            ioc_type = self._determine_ioc_type(data.get("indicator", ""))
            if not ioc_type:
                return None
            
            indicator = ThreatIndicator(
                indicator_id=data.get("id", hashlib.md5(str(data).encode()).hexdigest()),
                type=ioc_type,
                value=data.get("indicator", ""),
                threat_types=[ThreatType(t) for t in data.get("threat_types", ["malware"])],
                severity=ThreatSeverity(data.get("severity", "medium")),
                confidence=float(data.get("confidence", 0.5)),
                first_seen=self._parse_datetime(data.get("first_seen")),
                last_seen=self._parse_datetime(data.get("last_seen")),
                source=IntelligenceSource(data.get("source", "open_source")),
                description=data.get("description", ""),
                tags=data.get("tags", []),
                ttps=data.get("ttps", []),
                malware_families=data.get("malware_families", []),
                references=data.get("references", [])
            )
            
            return indicator
            
        except Exception as e:
            self.logger.error(f"Failed to parse threat indicator: {e}")
            return None

    def _parse_threat_campaign(self, data: Dict[str, Any]) -> Optional[ThreatCampaign]:
        """Parse threat campaign from data"""
        try:
            campaign = ThreatCampaign(
                campaign_id=data.get("id", hashlib.md5(str(data).encode()).hexdigest()),
                name=data.get("name", "Unknown Campaign"),
                threat_actors=data.get("threat_actors", []),
                objectives=data.get("objectives", []),
                targets=data.get("targets", []),
                start_date=self._parse_datetime(data.get("start_date")),
                end_date=self._parse_datetime(data.get("end_date")),
                active=data.get("active", True),
                techniques_used=data.get("techniques", []),
                indicators=data.get("indicators", []),
                severity=ThreatSeverity(data.get("severity", "medium")),
                confidence=float(data.get("confidence", 0.5)),
                description=data.get("description", ""),
                attribution=data.get("attribution", ""),
                motivation=data.get("motivation", [])
            )
            
            return campaign
            
        except Exception as e:
            self.logger.error(f"Failed to parse threat campaign: {e}")
            return None

    # Correlation and matching methods

    async def _find_threat_matches(self, ioc: str, finding: Dict[str, Any]) -> List[ThreatMatch]:
        """Find threat matches for an IoC"""
        matches = []
        
        for indicator in self.threat_indicators.values():
            if self._indicators_match(ioc, indicator.value):
                match = ThreatMatch(
                    match_id=f"match_{finding.get('id', 'unknown')}_{indicator.indicator_id}",
                    scan_finding_id=finding.get("id", "unknown"),
                    threat_indicator_id=indicator.indicator_id,
                    match_type="exact" if ioc == indicator.value else "partial",
                    confidence=indicator.confidence,
                    risk_score=self._calculate_match_risk_score(indicator, finding),
                    description=f"Match found for {indicator.type.value}: {indicator.description}",
                    implications=self._generate_match_implications(indicator, finding),
                    recommended_actions=self._generate_match_actions(indicator, finding),
                    false_positive_likelihood=indicator.false_positive_likelihood
                )
                matches.append(match)
        
        return matches

    async def _find_behavioral_matches(self, finding: Dict[str, Any]) -> List[ThreatMatch]:
        """Find behavioral pattern matches"""
        matches = []
        
        # Analyze behavioral patterns in the finding
        behaviors = self._extract_behaviors_from_finding(finding)
        
        for campaign in self.threat_campaigns.values():
            if self._behaviors_match_campaign(behaviors, campaign):
                match = ThreatMatch(
                    match_id=f"behavioral_{finding.get('id', 'unknown')}_{campaign.campaign_id}",
                    scan_finding_id=finding.get("id", "unknown"),
                    threat_indicator_id=campaign.campaign_id,
                    match_type="behavioral",
                    confidence=campaign.confidence * 0.8,  # Lower confidence for behavioral matches
                    risk_score=self._calculate_campaign_risk_score(campaign, finding),
                    description=f"Behavioral match with campaign: {campaign.name}",
                    implications=[f"Possible involvement in {campaign.name} campaign"],
                    recommended_actions=["Investigate for additional campaign indicators"],
                    false_positive_likelihood=0.3
                )
                matches.append(match)
        
        return matches

    def _create_vulnerability_match(self, finding: Dict[str, Any], 
                                  vuln_intel: VulnerabilityIntelligence) -> ThreatMatch:
        """Create vulnerability intelligence match"""
        return ThreatMatch(
            match_id=f"vuln_{finding.get('id', 'unknown')}_{vuln_intel.cve_id}",
            scan_finding_id=finding.get("id", "unknown"),
            threat_indicator_id=vuln_intel.cve_id,
            match_type="vulnerability_intel",
            confidence=0.9,
            risk_score=self._calculate_vulnerability_risk_score(vuln_intel),
            description=f"Intelligence available for {vuln_intel.cve_id}",
            implications=self._generate_vulnerability_implications(vuln_intel),
            recommended_actions=self._generate_vulnerability_actions(vuln_intel),
            false_positive_likelihood=0.1
        )

    # Utility and helper methods

    def _determine_ioc_type(self, indicator_value: str) -> Optional[IOCType]:
        """Determine IoC type from indicator value"""
        if re.match(r'^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}$', indicator_value):
            return IOCType.IP_ADDRESS
        elif re.match(r'^[a-fA-F0-9]{32,64}$', indicator_value):
            return IOCType.FILE_HASH
        elif '.' in indicator_value and not indicator_value.startswith('http'):
            return IOCType.DOMAIN
        elif indicator_value.startswith(('http://', 'https://')):
            return IOCType.URL
        elif '@' in indicator_value:
            return IOCType.EMAIL
        else:
            return None

    def _parse_datetime(self, dt_str: Optional[str]) -> datetime:
        """Parse datetime string"""
        if not dt_str:
            return datetime.now()
        
        try:
            return datetime.fromisoformat(dt_str.replace('Z', '+00:00'))
        except:
            return datetime.now()

    def _indicators_match(self, ioc1: str, ioc2: str) -> bool:
        """Check if two indicators match"""
        return ioc1.lower() == ioc2.lower()

    def _extract_iocs_from_finding(self, finding: Dict[str, Any]) -> List[str]:
        """Extract IoCs from a scan finding"""
        iocs = []
        
        # Extract from various fields
        for field in ["target", "evidence", "location", "host"]:
            value = finding.get(field, "")
            if value:
                # Extract IP addresses
                ips = re.findall(r'\b\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}\b', value)
                iocs.extend(ips)
                
                # Extract domains
                domains = re.findall(r'\b[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}\b', value)
                iocs.extend(domains)
                
                # Extract URLs
                urls = re.findall(r'https?://[^\s<>"{}|\\^`\[\]]+', value)
                iocs.extend(urls)
        
        return list(set(iocs))  # Remove duplicates

    def _extract_behaviors_from_finding(self, finding: Dict[str, Any]) -> List[str]:
        """Extract behavioral indicators from finding"""
        behaviors = []
        
        evidence = finding.get("evidence", "").lower()
        
        # Common behavioral indicators
        if "lateral movement" in evidence:
            behaviors.append("lateral_movement")
        if "privilege escalation" in evidence:
            behaviors.append("privilege_escalation")
        if "data exfiltration" in evidence:
            behaviors.append("data_exfiltration")
        if "persistence" in evidence:
            behaviors.append("persistence")
        
        return behaviors

    def _calculate_match_risk_score(self, indicator: ThreatIndicator, finding: Dict[str, Any]) -> float:
        """Calculate risk score for a threat match"""
        base_score = 5.0
        
        # Adjust based on indicator severity
        severity_multipliers = {
            ThreatSeverity.CRITICAL: 2.0,
            ThreatSeverity.HIGH: 1.5,
            ThreatSeverity.MEDIUM: 1.0,
            ThreatSeverity.LOW: 0.7,
            ThreatSeverity.INFORMATIONAL: 0.3
        }
        
        base_score *= severity_multipliers.get(indicator.severity, 1.0)
        
        # Adjust based on confidence
        base_score *= indicator.confidence
        
        # Adjust based on recency
        age = datetime.now() - indicator.last_seen
        if age < timedelta(days=7):
            base_score *= 1.3
        elif age < timedelta(days=30):
            base_score *= 1.1
        
        return min(base_score, 10.0)

    def _generate_match_implications(self, indicator: ThreatIndicator, finding: Dict[str, Any]) -> List[str]:
        """Generate implications for a threat match"""
        implications = []
        
        for threat_type in indicator.threat_types:
            if threat_type == ThreatType.MALWARE:
                implications.append("Possible malware infection")
            elif threat_type == ThreatType.APT:
                implications.append("Potential Advanced Persistent Threat activity")
            elif threat_type == ThreatType.RANSOMWARE:
                implications.append("Ransomware threat detected")
        
        if indicator.malware_families:
            implications.append(f"Associated with malware families: {', '.join(indicator.malware_families)}")
        
        return implications

    def _generate_match_actions(self, indicator: ThreatIndicator, finding: Dict[str, Any]) -> List[str]:
        """Generate recommended actions for a threat match"""
        actions = []
        
        if indicator.severity in [ThreatSeverity.CRITICAL, ThreatSeverity.HIGH]:
            actions.append("Immediate isolation and investigation required")
        
        actions.append("Block associated indicators across security controls")
        actions.append("Search for additional indicators from the same threat")
        
        if indicator.related_campaigns:
            actions.append("Investigate for other campaign indicators")
        
        return actions

    def _deduplicate_matches(self, matches: List[ThreatMatch]) -> List[ThreatMatch]:
        """Remove duplicate threat matches"""
        unique_matches = {}
        
        for match in matches:
            key = f"{match.scan_finding_id}_{match.threat_indicator_id}"
            if key not in unique_matches or match.confidence > unique_matches[key].confidence:
                unique_matches[key] = match
        
        return list(unique_matches.values())

    def _rank_matches(self, matches: List[ThreatMatch]) -> List[ThreatMatch]:
        """Rank threat matches by importance"""
        return sorted(matches, key=lambda m: (m.risk_score, m.confidence), reverse=True)

    def _get_cached_result(self, cache_key: str) -> Any:
        """Get cached result if valid"""
        if cache_key in self.analysis_cache:
            cached_item = self.analysis_cache[cache_key]
            if datetime.now() - cached_item["timestamp"] < self.cache_ttl:
                return cached_item["data"]
            else:
                del self.analysis_cache[cache_key]
        return None

    def _cache_result(self, cache_key: str, data: Any):
        """Cache analysis result"""
        self.analysis_cache[cache_key] = {
            "data": data,
            "timestamp": datetime.now()
        }

    # Additional analysis methods

    def _identify_emerging_techniques(self) -> List[str]:
        """Identify emerging attack techniques"""
        # Analyze recent indicators for new TTPs
        recent_indicators = [i for i in self.threat_indicators.values() 
                           if datetime.now() - i.last_seen < timedelta(days=30)]
        
        technique_counts = {}
        for indicator in recent_indicators:
            for ttp in indicator.ttps:
                technique_counts[ttp] = technique_counts.get(ttp, 0) + 1
        
        # Return techniques that are relatively new but gaining traction
        emerging = []
        for technique, count in technique_counts.items():
            if count >= 3:  # Threshold for emerging technique
                emerging.append(technique)
        
        return emerging[:5]

    def _calculate_geographic_risks(self) -> Dict[str, float]:
        """Calculate geographic risk levels"""
        geo_risks = {}
        
        for indicator in self.threat_indicators.values():
            for region in indicator.geographic_regions:
                if region not in geo_risks:
                    geo_risks[region] = 0.0
                
                # Weight by severity and confidence
                weight = indicator.confidence
                if indicator.severity == ThreatSeverity.CRITICAL:
                    weight *= 2.0
                elif indicator.severity == ThreatSeverity.HIGH:
                    weight *= 1.5
                
                geo_risks[region] += weight
        
        # Normalize scores
        max_risk = max(geo_risks.values()) if geo_risks else 1.0
        return {region: risk/max_risk for region, risk in geo_risks.items()}

    def _calculate_industry_risks(self) -> Dict[str, str]:
        """Calculate industry-specific risk levels"""
        industry_risks = {}
        
        for campaign in self.threat_campaigns.values():
            if campaign.active:
                for industry in campaign.industry_targeting:
                    if industry not in industry_risks:
                        industry_risks[industry] = "low"
                    
                    # Upgrade risk level based on campaign severity
                    if campaign.severity == ThreatSeverity.CRITICAL:
                        industry_risks[industry] = "critical"
                    elif campaign.severity == ThreatSeverity.HIGH and industry_risks[industry] != "critical":
                        industry_risks[industry] = "high"
        
        return industry_risks

    def _analyze_threat_actor_activity(self) -> Dict[str, str]:
        """Analyze threat actor activity levels"""
        actor_activity = {}
        
        for campaign in self.threat_campaigns.values():
            for actor in campaign.threat_actors:
                if campaign.active:
                    actor_activity[actor] = "active"
                elif campaign.end_date and datetime.now() - campaign.end_date < timedelta(days=90):
                    actor_activity[actor] = "recent"
                else:
                    actor_activity[actor] = "dormant"
        
        return actor_activity

    def _generate_defensive_recommendations(self) -> List[str]:
        """Generate defensive recommendations based on threat landscape"""
        recommendations = []
        
        # Based on active campaigns
        active_campaigns = [c for c in self.threat_campaigns.values() if c.active]
        if active_campaigns:
            recommendations.append(f"Monitor for indicators from {len(active_campaigns)} active threat campaigns")
        
        # Based on trending threats
        malware_indicators = [i for i in self.threat_indicators.values() 
                            if ThreatType.MALWARE in i.threat_types]
        if len(malware_indicators) > 100:
            recommendations.append("Enhance endpoint detection and response capabilities")
        
        return recommendations

    def _generate_threat_insights(self, threat_data: Dict[str, Any], 
                                landscape: ThreatLandscape) -> List[str]:
        """Generate threat intelligence insights"""
        insights = []
        
        if len(landscape.active_campaigns) > 5:
            insights.append("High level of concurrent threat campaign activity detected")
        
        if landscape.hot_vulnerabilities:
            insights.append(f"Active exploitation observed for {len(landscape.hot_vulnerabilities)} vulnerabilities")
        
        # Seasonal analysis
        current_month = datetime.now().month
        if current_month in [11, 12]:  # Holiday season
            insights.append("Increased threat activity typical during holiday season")
        
        return insights

    async def _gather_vulnerability_intelligence(self, cve_id: str) -> Optional[VulnerabilityIntelligence]:
        """Gather intelligence about a vulnerability from feeds"""
        # This would query external sources for vulnerability intelligence
        # For now, return a placeholder
        return VulnerabilityIntelligence(
            cve_id=cve_id,
            exploitation_status="unknown",
            exploit_kits=[],
            threat_actors=[],
            campaigns_using=[],
            first_exploitation_date=None,
            exploitation_trend="stable",
            difficulty_level="medium",
            target_prevalence=0.0,
            patch_adoption_rate=0.0,
            weaponization_timeline=None,
            underground_mentions=0,
            social_media_mentions=0,
            security_vendor_reports=[],
            exploit_price=None,
            mitigation_effectiveness={}
        )