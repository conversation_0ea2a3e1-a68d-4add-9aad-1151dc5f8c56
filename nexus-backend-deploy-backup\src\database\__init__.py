#!/usr/bin/env python3
"""
NexusScan Database Package
Provides comprehensive database management for campaigns, scans, and vulnerabilities
"""

from .models import (
    # Base and engine
    Base, DatabaseManager, DatabaseConfig, DatabaseQueries,
    
    # Main models
    Campaign, Scan, Vulnerability, AIAnalysis,
    
    # Configuration models
    AppConfig, ScanTemplate,
    
    # Reporting models
    Report, ExportHistory,
    
    # Security models
    ActivityLog, FileAttachment, DatabaseVersion,
    
    # Enums
    CampaignStatus, ScanStatus, VulnerabilitySeverity, VulnerabilityStatus,
    
    # Types
    JSONType
)

from .campaign_manager import (
    CampaignManager, CampaignCreateRequest, CampaignUpdateRequest,
    CampaignStats, CampaignSummary
)

from .data_manager import (
    DataManager, ScanCreateRequest, VulnerabilityCreateRequest,
    AIAnalysisCreateRequest, SearchFilters
)

from .export_manager import (
    ExportManager, ExportRequest, ExportResult
)

from .config_manager import (
    ConfigManager, ConfigSection, SettingDefinition
)

__version__ = "1.0.0"
__author__ = "NexusScan Development Team"

__all__ = [
    # Core classes
    "DatabaseManager",
    "DatabaseConfig", 
    "DatabaseQueries",
    "CampaignManager",
    "DataManager",
    "ExportManager",
    "ConfigManager",
    
    # Models
    "Base",
    "Campaign",
    "Scan", 
    "Vulnerability",
    "AIAnalysis",
    "AppConfig",
    "ScanTemplate",
    "Report",
    "ExportHistory",
    "ActivityLog",
    "FileAttachment",
    "DatabaseVersion",
    
    # Request/Response objects
    "CampaignCreateRequest",
    "CampaignUpdateRequest", 
    "CampaignStats",
    "CampaignSummary",
    "ScanCreateRequest",
    "VulnerabilityCreateRequest",
    "AIAnalysisCreateRequest",
    "SearchFilters",
    "ExportRequest",
    "ExportResult",
    "ConfigSection",
    "SettingDefinition",
    
    # Enums
    "CampaignStatus",
    "ScanStatus",
    "VulnerabilitySeverity", 
    "VulnerabilityStatus",
    
    # Types
    "JSONType"
]


def initialize_database(database_url: str = "sqlite:///nexusscan.db", 
                       echo: bool = False) -> DatabaseManager:
    """Initialize database with default configuration
    
    Args:
        database_url: Database connection URL
        echo: Enable SQL query logging
        
    Returns:
        Configured DatabaseManager instance
    """
    config = DatabaseConfig(
        database_url=database_url,
        echo=echo
    )
    
    db_manager = DatabaseManager(config)
    db_manager.init_database()
    
    return db_manager


def create_managers(db_manager: DatabaseManager) -> tuple:
    """Create all manager instances
    
    Args:
        db_manager: Database manager instance
        
    Returns:
        Tuple of (campaign_manager, data_manager, export_manager, config_manager)
    """
    campaign_manager = CampaignManager(db_manager)
    data_manager = DataManager(db_manager)
    export_manager = ExportManager(db_manager, data_manager)
    config_manager = ConfigManager(db_manager)
    
    return campaign_manager, data_manager, export_manager, config_manager


def setup_database_environment(database_url: str = "sqlite:///nexusscan.db",
                              echo: bool = False,
                              initialize_defaults: bool = True) -> dict:
    """Set up complete database environment
    
    Args:
        database_url: Database connection URL
        echo: Enable SQL query logging
        initialize_defaults: Initialize default configuration
        
    Returns:
        Dictionary containing all manager instances
    """
    # Initialize database
    db_manager = initialize_database(database_url, echo)
    
    # Create managers
    campaign_manager, data_manager, export_manager, config_manager = create_managers(db_manager)
    
    # Initialize default configuration if requested
    if initialize_defaults:
        config_manager.initialize_default_config()
    
    return {
        "db_manager": db_manager,
        "campaign_manager": campaign_manager,
        "data_manager": data_manager,
        "export_manager": export_manager,
        "config_manager": config_manager
    }