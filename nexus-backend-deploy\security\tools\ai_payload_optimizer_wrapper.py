"""
AI Payload Optimizer Wrapper for NexusScan Desktop
Wraps the IntelligentPayloadOptimizer as a registered security tool.
"""

import logging
from typing import Dict, List, Optional, Callable, Any
from datetime import datetime

from security.tools.tool_registry import (
    ToolMetadata, ToolCategory, ToolCapabilities,
    ScanOptions, ScanResult, ToolStatus, SecurityTool, register_tool
)
from ai.intelligent_payload_optimizer import (
    IntelligentPayloadOptimizer, 
    PayloadOptimizationRequest,
    OptimizationStrategy,
    OptimizationMetric
)
from ai.services import AIServiceManager
from core.config import Config

logger = logging.getLogger(__name__)


@register_tool
class AIPayloadOptimizerTool(SecurityTool):
    """AI-powered payload optimization tool wrapper"""
    
    def __init__(self):
        """Initialize AI payload optimizer tool"""
        super().__init__()
        self.optimizer = None
        
    def get_metadata(self) -> ToolMetadata:
        """Get tool metadata"""
        return ToolMetadata(
            name="ai_payload_optimizer",
            display_name="AI Payload Optimizer",
            description="Intelligent payload optimization using AI to maximize effectiveness and minimize detection",
            version="1.0.0",
            category=ToolCategory.AI_ANALYZER,
            author="NexusScan AI Team",
            capabilities=ToolCapabilities(
                supports_async=True,
                supports_progress=True,
                supports_cancellation=False,
                requires_root=False,
                network_access_required=True,
                output_formats=["json", "text"],
                supported_targets=["payload", "exploit", "vulnerability"]
            )
        )
    
    def check_availability(self) -> bool:
        """Check if AI payload optimizer is available"""
        try:
            # Test initialization with AI service manager
            config = Config()
            ai_manager = AIServiceManager(config)
            self.optimizer = IntelligentPayloadOptimizer(ai_manager)
            return True
        except Exception as e:
            logger.error(f"AI Payload Optimizer not available: {e}")
            return False
    
    async def scan(self, options: ScanOptions,
                   progress_callback: Optional[Callable[[float, str], None]] = None) -> ScanResult:
        """Execute payload optimization"""
        start_time = datetime.now()
        
        try:
            if not self.optimizer:
                config = Config()
                ai_manager = AIServiceManager(config)
                self.optimizer = IntelligentPayloadOptimizer(ai_manager)
            
            # Extract payload and optimization parameters from options
            payload = options.custom_options.get("payload", options.target)
            vulnerability_type = options.custom_options.get("vulnerability_type", "generic")
            strategy = options.custom_options.get("strategy", OptimizationStrategy.EFFECTIVENESS_BASED)
            
            # Create optimization request
            request = PayloadOptimizationRequest(
                original_payload=payload,
                vulnerability_type=vulnerability_type,
                target_environment=options.custom_options.get("target_environment", {}),
                optimization_strategy=strategy,
                constraints=options.custom_options.get("constraints", {}),
                success_criteria=options.custom_options.get("success_criteria", {
                    OptimizationMetric.SUCCESS_RATE: 0.8,
                    OptimizationMetric.DETECTION_AVOIDANCE: 0.9
                })
            )
            
            if progress_callback:
                progress_callback(0.1, "Initializing AI payload optimizer...")
            
            # Optimize payload
            if progress_callback:
                progress_callback(0.3, "Analyzing payload and target environment...")
            
            optimization_result = await self.optimizer.optimize_payload(request)
            
            if progress_callback:
                progress_callback(0.9, "Optimization complete, preparing results...")
            
            # Format results
            parsed_results = {
                "optimization_summary": {
                    "original_payload": payload,
                    "optimized_payload": optimization_result.optimized_payload,
                    "strategy_used": optimization_result.strategy_used.value,
                    "improvement_score": optimization_result.improvement_score,
                    "confidence_score": optimization_result.confidence_score
                },
                "optimization_details": optimization_result.optimization_details,
                "performance_metrics": optimization_result.performance_metrics,
                "recommendations": optimization_result.recommendations,
                "educational_content": optimization_result.educational_content
            }
            
            end_time = datetime.now()
            
            return ScanResult(
                tool_name=self.metadata.name,
                target=options.target,
                status=ToolStatus.COMPLETED,
                start_time=start_time.isoformat(),
                end_time=end_time.isoformat(),
                duration_seconds=(end_time - start_time).total_seconds(),
                parsed_results=parsed_results,
                metadata={
                    "optimization_id": optimization_result.optimization_id,
                    "ai_provider": optimization_result.ai_provider,
                    "timestamp": optimization_result.timestamp
                }
            )
            
        except Exception as e:
            logger.error(f"AI payload optimization failed: {e}")
            end_time = datetime.now()
            
            return ScanResult(
                tool_name=self.metadata.name,
                target=options.target,
                status=ToolStatus.FAILED,
                start_time=start_time.isoformat(),
                end_time=end_time.isoformat(),
                duration_seconds=(end_time - start_time).total_seconds(),
                errors=[str(e)]
            )