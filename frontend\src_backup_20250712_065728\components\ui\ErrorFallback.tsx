/**
 * Error Fallback Component
 * Displays when React error boundaries catch errors
 */
import React from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { AlertTriangle, RefreshCw, Home, Bug } from 'lucide-react';
import { cn } from '@/lib/utils';

export interface ErrorFallbackProps {
  error: Error;
  resetErrorBoundary: () => void;
  className?: string;
}

export function ErrorFallback({ error, resetErrorBoundary, className }: ErrorFallbackProps) {
  const [showDetails, setShowDetails] = React.useState(false);
  
  const handleReload = () => {
    window.location.reload();
  };
  
  const handleGoHome = () => {
    window.location.href = '/dashboard';
  };
  
  const copyErrorToClipboard = async () => {
    const errorInfo = `
Error: ${error.message}
Stack: ${error.stack}
URL: ${window.location.href}
User Agent: ${navigator.userAgent}
Timestamp: ${new Date().toISOString()}
    `.trim();
    
    try {
      await navigator.clipboard.writeText(errorInfo);
      // Could show a toast here
    } catch (err) {
      console.error('Failed to copy error info:', err);
    }
  };
  
  return (
    <div className={cn(
      'flex items-center justify-center min-h-[400px] p-6',
      className
    )}>
      <Card className=\"w-full max-w-2xl\">
        <CardHeader className=\"text-center\">
          <div className=\"mx-auto mb-4 h-12 w-12 rounded-full bg-red-100 dark:bg-red-900/20 flex items-center justify-center\">
            <AlertTriangle className=\"h-6 w-6 text-red-600 dark:text-red-400\" />
          </div>
          <CardTitle className=\"text-xl font-semibold\">
            Something went wrong
          </CardTitle>
          <CardDescription>
            An unexpected error occurred in the application. This has been logged for investigation.
          </CardDescription>
        </CardHeader>
        
        <CardContent className=\"space-y-6\">
          {/* Error message */}
          <div className=\"rounded-lg bg-muted p-4\">
            <h4 className=\"font-medium text-sm text-muted-foreground mb-2\">Error Message:</h4>
            <p className=\"text-sm font-mono bg-background p-2 rounded border\">
              {error.message}
            </p>
          </div>
          
          {/* Action buttons */}
          <div className=\"flex flex-col sm:flex-row gap-3 justify-center\">
            <Button onClick={resetErrorBoundary} className=\"flex items-center gap-2\">
              <RefreshCw className=\"h-4 w-4\" />
              Try Again
            </Button>
            
            <Button variant=\"outline\" onClick={handleReload} className=\"flex items-center gap-2\">
              <RefreshCw className=\"h-4 w-4\" />
              Reload Page
            </Button>
            
            <Button variant=\"outline\" onClick={handleGoHome} className=\"flex items-center gap-2\">
              <Home className=\"h-4 w-4\" />
              Go to Dashboard
            </Button>
          </div>
          
          {/* Error details toggle */}\n          <div className=\"space-y-3\">\n            <Button\n              variant=\"ghost\"\n              size=\"sm\"\n              onClick={() => setShowDetails(!showDetails)}\n              className=\"w-full flex items-center gap-2 text-muted-foreground\"\n            >\n              <Bug className=\"h-4 w-4\" />\n              {showDetails ? 'Hide' : 'Show'} Technical Details\n            </Button>\n            \n            {showDetails && (\n              <div className=\"space-y-3\">\n                <div className=\"rounded-lg bg-muted p-4\">\n                  <h4 className=\"font-medium text-sm text-muted-foreground mb-2\">Stack Trace:</h4>\n                  <pre className=\"text-xs bg-background p-3 rounded border overflow-auto max-h-48 font-mono\">\n                    {error.stack}\n                  </pre>\n                </div>\n                \n                <div className=\"rounded-lg bg-muted p-4\">\n                  <h4 className=\"font-medium text-sm text-muted-foreground mb-2\">Environment:</h4>\n                  <div className=\"text-xs space-y-1 font-mono\">\n                    <div><strong>URL:</strong> {window.location.href}</div>\n                    <div><strong>User Agent:</strong> {navigator.userAgent}</div>\n                    <div><strong>Timestamp:</strong> {new Date().toISOString()}</div>\n                  </div>\n                </div>\n                \n                <Button\n                  variant=\"outline\"\n                  size=\"sm\"\n                  onClick={copyErrorToClipboard}\n                  className=\"w-full\"\n                >\n                  Copy Error Information\n                </Button>\n              </div>\n            )}\n          </div>\n          \n          {/* Help text */}\n          <div className=\"text-center text-sm text-muted-foreground\">\n            If this problem persists, please restart the application or contact support.\n          </div>\n        </CardContent>\n      </Card>\n    </div>\n  );\n}"