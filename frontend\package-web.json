{"name": "nexusscan-web", "version": "1.0.0", "description": "NexusScan Web - Professional Penetration Testing Platform", "type": "module", "homepage": "./", "author": {"name": "NexusScan Team", "email": "<EMAIL>"}, "license": "MIT", "private": true, "scripts": {"dev": "vite --config vite.config.web.ts", "build": "vite build --config vite.config.web.ts", "preview": "vite preview --config vite.config.web.ts", "deploy": "npm run build && npm run deploy:vercel", "deploy:vercel": "vercel --prod", "deploy:netlify": "netlify deploy --prod --dir=dist-web", "deploy:github": "gh-pages -d dist-web", "test": "vitest", "test:ui": "vitest --ui", "test:run": "vitest run", "test:coverage": "vitest run --coverage", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:e2e:headed": "playwright test --headed", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix", "format": "prettier --write \"src/**/*.{ts,tsx,json,css,md}\"", "type-check": "tsc --noEmit", "analyze": "npm run build && npx vite-bundle-analyzer dist-web/stats.html", "clean": "rimraf dist-web node_modules/.vite"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.8.0", "zustand": "^4.4.0", "axios": "^1.6.0", "@tanstack/react-query": "^5.0.0", "@hookform/resolvers": "^3.3.2", "@radix-ui/react-slot": "^1.2.3", "@xterm/addon-fit": "^0.10.0", "@xterm/addon-web-links": "^0.11.0", "@xterm/xterm": "^5.5.0", "lucide-react": "^0.263.0", "clsx": "^2.0.0", "tailwind-merge": "^1.14.0", "class-variance-authority": "^0.7.0", "monaco-editor": "^0.44.0", "recharts": "^2.8.0", "framer-motion": "^10.16.0", "react-hook-form": "^7.47.0", "zod": "^3.22.0", "sonner": "^1.0.0", "cmdk": "^0.2.0"}, "devDependencies": {"@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@types/node": "^20.0.0", "@vitejs/plugin-react": "^4.0.0", "vite": "^4.4.0", "vite-plugin-pwa": "^0.16.0", "vite-bundle-analyzer": "^0.7.0", "typescript": "^5.0.0", "tailwindcss": "^3.3.0", "autoprefixer": "^10.4.0", "postcss": "^8.4.0", "vitest": "^0.34.0", "@vitest/ui": "^0.34.0", "jsdom": "^22.0.0", "@testing-library/react": "^13.4.0", "@testing-library/jest-dom": "^6.0.0", "@testing-library/user-event": "^14.4.0", "playwright": "^1.38.0", "@playwright/test": "^1.38.0", "eslint": "^8.45.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint-plugin-react": "^7.33.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.0", "prettier": "^3.0.0", "rimraf": "^5.0.0", "concurrently": "^8.2.0", "cross-env": "^7.0.3", "gh-pages": "^6.0.0"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}}