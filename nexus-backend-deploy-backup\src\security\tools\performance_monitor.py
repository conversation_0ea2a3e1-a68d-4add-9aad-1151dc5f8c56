#!/usr/bin/env python3
"""
Performance Monitor for Hybrid Execution Pipeline
Enterprise-grade monitoring, metrics collection, and optimization
"""

import asyncio
import logging
import time
import psutil
import json
import statistics
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass, field
from enum import Enum
from pathlib import Path
from collections import defaultdict, deque
import threading

from .environment_detector import environment_detector
from .hybrid_execution_engine import ToolExecutionResult, ExecutionResult

logger = logging.getLogger(__name__)

class MetricType(Enum):
    """Performance metric types"""
    EXECUTION_TIME = "execution_time"
    MEMORY_USAGE = "memory_usage"
    CPU_USAGE = "cpu_usage"
    SUCCESS_RATE = "success_rate"
    THROUGHPUT = "throughput"
    LATENCY = "latency"
    ERROR_RATE = "error_rate"
    RESOURCE_EFFICIENCY = "resource_efficiency"

class AlertLevel(Enum):
    """Alert severity levels"""
    INFO = "info"
    WARNING = "warning"
    CRITICAL = "critical"
    ERROR = "error"

@dataclass
class PerformanceMetric:
    """Performance metric data point"""
    metric_type: MetricType
    value: float
    timestamp: float
    tool_name: str = ""
    execution_method: str = ""
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class SystemAlert:
    """System performance alert"""
    level: AlertLevel
    message: str
    timestamp: float
    metric_type: Optional[MetricType] = None
    threshold_value: Optional[float] = None
    actual_value: Optional[float] = None
    recommendations: List[str] = field(default_factory=list)

@dataclass
class PerformanceReport:
    """Comprehensive performance report"""
    report_id: str
    generated_at: float
    time_period: Tuple[float, float]
    system_overview: Dict[str, Any]
    tool_performance: Dict[str, Dict[str, Any]]
    execution_methods: Dict[str, Dict[str, Any]]
    alerts: List[SystemAlert]
    recommendations: List[str]
    trends: Dict[str, Any]

class PerformanceMonitor:
    """Enterprise-grade performance monitoring system"""
    
    def __init__(self):
        self.environment = environment_detector.environment
        
        # Metric storage
        self.metrics_buffer: Dict[MetricType, deque] = defaultdict(lambda: deque(maxlen=1000))
        self.tool_metrics: Dict[str, Dict[MetricType, deque]] = defaultdict(lambda: defaultdict(lambda: deque(maxlen=500)))
        self.method_metrics: Dict[str, Dict[MetricType, deque]] = defaultdict(lambda: defaultdict(lambda: deque(maxlen=500)))
        
        # Alert system
        self.alerts: deque = deque(maxlen=100)
        self.alert_thresholds = self._setup_alert_thresholds()
        
        # Monitoring configuration
        self.monitoring_enabled = True
        self.real_time_monitoring = True
        self.metrics_collection_interval = 5.0  # seconds
        
        # Performance baselines
        self.baselines: Dict[str, Dict[MetricType, float]] = {}
        self.performance_targets: Dict[MetricType, float] = {
            MetricType.EXECUTION_TIME: 300.0,  # 5 minutes max
            MetricType.SUCCESS_RATE: 0.95,     # 95% success rate
            MetricType.CPU_USAGE: 80.0,        # 80% max CPU
            MetricType.MEMORY_USAGE: 85.0,     # 85% max memory
            MetricType.ERROR_RATE: 0.05,       # 5% max error rate
            MetricType.THROUGHPUT: 1.0         # 1 tool per second min
        }
        
        # Background monitoring
        self._monitoring_task = None
        self._system_monitor_task = None
        self._shutdown_event = asyncio.Event()
        
        # Statistics
        self.monitoring_stats = {
            'metrics_collected': 0,
            'alerts_generated': 0,
            'reports_generated': 0,
            'monitoring_uptime': time.time(),
            'last_health_check': 0
        }
        
        logger.info("🔍 Performance Monitor initialized")
        logger.info(f"🌍 Environment: {self.environment.value}")
        logger.info(f"📊 Real-time monitoring: {'enabled' if self.real_time_monitoring else 'disabled'}")
    
    def _setup_alert_thresholds(self) -> Dict[MetricType, Dict[str, float]]:
        """Setup alert thresholds for different metrics"""
        
        return {
            MetricType.EXECUTION_TIME: {
                'warning': 180.0,   # 3 minutes
                'critical': 300.0   # 5 minutes
            },
            MetricType.CPU_USAGE: {
                'warning': 70.0,    # 70%
                'critical': 85.0    # 85%
            },
            MetricType.MEMORY_USAGE: {
                'warning': 75.0,    # 75%
                'critical': 90.0    # 90%
            },
            MetricType.SUCCESS_RATE: {
                'warning': 0.90,    # 90%
                'critical': 0.80    # 80%
            },
            MetricType.ERROR_RATE: {
                'warning': 0.10,    # 10%
                'critical': 0.20    # 20%
            },
            MetricType.THROUGHPUT: {
                'warning': 0.5,     # 0.5 tools/sec
                'critical': 0.25    # 0.25 tools/sec
            }
        }
    
    async def start_monitoring(self):
        """Start background monitoring tasks"""
        
        if not self.monitoring_enabled:
            logger.info("📊 Monitoring disabled, skipping start")
            return
        
        logger.info("🚀 Starting performance monitoring...")
        
        # Start system monitoring
        self._system_monitor_task = asyncio.create_task(self._system_monitor_loop())
        
        # Start metrics collection
        self._monitoring_task = asyncio.create_task(self._monitoring_loop())
        
        logger.info("✅ Performance monitoring started")
    
    async def stop_monitoring(self):
        """Stop background monitoring tasks"""
        
        logger.info("🛑 Stopping performance monitoring...")
        
        self._shutdown_event.set()
        
        # Cancel monitoring tasks
        if self._monitoring_task:
            self._monitoring_task.cancel()
            try:
                await self._monitoring_task
            except asyncio.CancelledError:
                pass
        
        if self._system_monitor_task:
            self._system_monitor_task.cancel()
            try:
                await self._system_monitor_task
            except asyncio.CancelledError:
                pass
        
        logger.info("✅ Performance monitoring stopped")
    
    async def _monitoring_loop(self):
        """Main monitoring loop"""
        
        try:
            while not self._shutdown_event.is_set():
                await self._collect_system_metrics()
                await self._check_alert_conditions()
                await asyncio.sleep(self.metrics_collection_interval)
                
        except asyncio.CancelledError:
            logger.info("Monitoring loop cancelled")
        except Exception as e:
            logger.error(f"Monitoring loop error: {e}")
    
    async def _system_monitor_loop(self):
        """System resource monitoring loop"""
        
        try:
            while not self._shutdown_event.is_set():
                await self._collect_system_resources()
                await asyncio.sleep(1.0)  # More frequent system monitoring
                
        except asyncio.CancelledError:
            logger.info("System monitor loop cancelled")
        except Exception as e:
            logger.error(f"System monitor loop error: {e}")
    
    async def _collect_system_metrics(self):
        """Collect comprehensive system metrics"""
        
        try:
            current_time = time.time()
            
            # Calculate derived metrics
            await self._calculate_success_rates()
            await self._calculate_throughput()
            await self._calculate_error_rates()
            
            self.monitoring_stats['metrics_collected'] += 1
            self.monitoring_stats['last_health_check'] = current_time
            
        except Exception as e:
            logger.error(f"Failed to collect system metrics: {e}")
    
    async def _collect_system_resources(self):
        """Collect system resource metrics"""
        
        try:
            current_time = time.time()
            
            # CPU usage
            cpu_percent = psutil.cpu_percent(interval=None)
            self._record_metric(MetricType.CPU_USAGE, cpu_percent, current_time)
            
            # Memory usage
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            self._record_metric(MetricType.MEMORY_USAGE, memory_percent, current_time)
            
        except Exception as e:
            logger.error(f"Failed to collect system resources: {e}")
    
    async def _calculate_success_rates(self):
        """Calculate success rates for tools and methods"""
        
        current_time = time.time()
        lookback_period = 300.0  # 5 minutes
        
        # Calculate overall success rate
        recent_executions = self._get_recent_executions(current_time - lookback_period)
        if recent_executions:
            success_count = sum(1 for result in recent_executions if result.status == ExecutionResult.SUCCESS)
            success_rate = success_count / len(recent_executions)
            self._record_metric(MetricType.SUCCESS_RATE, success_rate, current_time)
        
        # Calculate per-tool success rates
        for tool_name, tool_executions in self._group_executions_by_tool(recent_executions).items():
            if tool_executions:
                tool_success_count = sum(1 for result in tool_executions if result.status == ExecutionResult.SUCCESS)
                tool_success_rate = tool_success_count / len(tool_executions)
                self._record_tool_metric(tool_name, MetricType.SUCCESS_RATE, tool_success_rate, current_time)
    
    async def _calculate_throughput(self):
        """Calculate execution throughput"""
        
        current_time = time.time()
        lookback_period = 60.0  # 1 minute
        
        recent_executions = self._get_recent_executions(current_time - lookback_period)
        throughput = len(recent_executions) / lookback_period if recent_executions else 0.0
        
        self._record_metric(MetricType.THROUGHPUT, throughput, current_time)
    
    async def _calculate_error_rates(self):
        """Calculate error rates"""
        
        current_time = time.time()
        lookback_period = 300.0  # 5 minutes
        
        recent_executions = self._get_recent_executions(current_time - lookback_period)
        if recent_executions:
            error_count = sum(1 for result in recent_executions if result.status in [ExecutionResult.FAILED, ExecutionResult.TIMEOUT])
            error_rate = error_count / len(recent_executions)
            self._record_metric(MetricType.ERROR_RATE, error_rate, current_time)
    
    def _get_recent_executions(self, since_time: float) -> List[ToolExecutionResult]:
        """Get executions since specified time (placeholder - would integrate with execution history)"""
        # This would integrate with the actual execution history tracking
        # For now, return empty list
        return []
    
    def _group_executions_by_tool(self, executions: List[ToolExecutionResult]) -> Dict[str, List[ToolExecutionResult]]:
        """Group executions by tool name"""
        groups = defaultdict(list)
        for execution in executions:
            tool_name = execution.metadata.get('tool_name', 'unknown')
            groups[tool_name].append(execution)
        return groups
    
    def record_tool_execution(self, tool_name: str, result: ToolExecutionResult):
        """Record a tool execution for performance tracking"""
        
        if not self.monitoring_enabled:
            return
        
        current_time = time.time()
        
        # Record execution time
        if result.execution_time > 0:
            self._record_tool_metric(tool_name, MetricType.EXECUTION_TIME, result.execution_time, current_time)
            self._record_method_metric(result.method, MetricType.EXECUTION_TIME, result.execution_time, current_time)
        
        # Record latency (time from request to first response)
        if hasattr(result, 'latency') and result.latency > 0:
            self._record_tool_metric(tool_name, MetricType.LATENCY, result.latency, current_time)
        
        # Calculate resource efficiency (success/time ratio)
        if result.execution_time > 0:
            efficiency = 1.0 / result.execution_time if result.status == ExecutionResult.SUCCESS else 0.0
            self._record_tool_metric(tool_name, MetricType.RESOURCE_EFFICIENCY, efficiency, current_time)
        
        logger.debug(f"📊 Recorded metrics for {tool_name} execution via {result.method}")
    
    def _record_metric(self, metric_type: MetricType, value: float, timestamp: float):
        """Record a global metric"""
        
        metric = PerformanceMetric(
            metric_type=metric_type,
            value=value,
            timestamp=timestamp
        )
        
        self.metrics_buffer[metric_type].append(metric)
    
    def _record_tool_metric(self, tool_name: str, metric_type: MetricType, value: float, timestamp: float):
        """Record a tool-specific metric"""
        
        metric = PerformanceMetric(
            metric_type=metric_type,
            value=value,
            timestamp=timestamp,
            tool_name=tool_name
        )
        
        self.tool_metrics[tool_name][metric_type].append(metric)
    
    def _record_method_metric(self, method: str, metric_type: MetricType, value: float, timestamp: float):
        """Record a method-specific metric"""
        
        metric = PerformanceMetric(
            metric_type=metric_type,
            value=value,
            timestamp=timestamp,
            execution_method=method
        )
        
        self.method_metrics[method][metric_type].append(metric)
    
    async def _check_alert_conditions(self):
        """Check for alert conditions and generate alerts"""
        
        current_time = time.time()
        
        for metric_type, thresholds in self.alert_thresholds.items():
            recent_metrics = self._get_recent_metrics(metric_type, 60.0)  # Last minute
            
            if not recent_metrics:
                continue
            
            avg_value = statistics.mean(metric.value for metric in recent_metrics)
            
            # Check critical threshold
            if 'critical' in thresholds and self._check_threshold(avg_value, thresholds['critical'], metric_type):
                alert = SystemAlert(
                    level=AlertLevel.CRITICAL,
                    message=f"Critical {metric_type.value} threshold exceeded",
                    timestamp=current_time,
                    metric_type=metric_type,
                    threshold_value=thresholds['critical'],
                    actual_value=avg_value,
                    recommendations=self._get_performance_recommendations(metric_type, avg_value)
                )
                self._add_alert(alert)
            
            # Check warning threshold
            elif 'warning' in thresholds and self._check_threshold(avg_value, thresholds['warning'], metric_type):
                alert = SystemAlert(
                    level=AlertLevel.WARNING,
                    message=f"Warning {metric_type.value} threshold exceeded",
                    timestamp=current_time,
                    metric_type=metric_type,
                    threshold_value=thresholds['warning'],
                    actual_value=avg_value,
                    recommendations=self._get_performance_recommendations(metric_type, avg_value)
                )
                self._add_alert(alert)
    
    def _check_threshold(self, value: float, threshold: float, metric_type: MetricType) -> bool:
        """Check if a value exceeds threshold based on metric type"""
        
        # For success rate, alert if below threshold
        if metric_type == MetricType.SUCCESS_RATE:
            return value < threshold
        
        # For most other metrics, alert if above threshold
        return value > threshold
    
    def _get_recent_metrics(self, metric_type: MetricType, seconds: float) -> List[PerformanceMetric]:
        """Get recent metrics of a specific type"""
        
        cutoff_time = time.time() - seconds
        recent_metrics = []
        
        for metric in self.metrics_buffer[metric_type]:
            if metric.timestamp >= cutoff_time:
                recent_metrics.append(metric)
        
        return recent_metrics
    
    def _add_alert(self, alert: SystemAlert):
        """Add an alert to the system"""
        
        # Check for duplicate alerts (same type within 5 minutes)
        cutoff_time = alert.timestamp - 300.0
        
        for existing_alert in self.alerts:
            if (existing_alert.timestamp >= cutoff_time and 
                existing_alert.metric_type == alert.metric_type and
                existing_alert.level == alert.level):
                return  # Don't add duplicate alert
        
        self.alerts.append(alert)
        self.monitoring_stats['alerts_generated'] += 1
        
        logger.warning(f"🚨 {alert.level.value.upper()} ALERT: {alert.message}")
    
    def _get_performance_recommendations(self, metric_type: MetricType, value: float) -> List[str]:
        """Get performance recommendations based on metric"""
        
        recommendations = []
        
        if metric_type == MetricType.EXECUTION_TIME:
            recommendations.extend([
                "Consider enabling parallel execution for campaigns",
                "Check if Docker execution is available for faster tool execution",
                "Review tool timeout settings and adjust if necessary",
                "Monitor system resources during execution"
            ])
        
        elif metric_type == MetricType.CPU_USAGE:
            recommendations.extend([
                "Reduce concurrent tool executions",
                "Enable Docker execution to isolate resource usage",
                "Consider running tools sequentially instead of parallel",
                "Check for runaway processes"
            ])
        
        elif metric_type == MetricType.MEMORY_USAGE:
            recommendations.extend([
                "Limit concurrent tool executions",
                "Clear tool output caches more frequently",
                "Use simulation mode for testing large campaigns",
                "Restart the application if memory usage continues to grow"
            ])
        
        elif metric_type == MetricType.SUCCESS_RATE:
            recommendations.extend([
                "Check network connectivity to targets",
                "Verify tool installations and dependencies",
                "Review tool timeout settings",
                "Enable auto-installation for missing tools"
            ])
        
        elif metric_type == MetricType.ERROR_RATE:
            recommendations.extend([
                "Check system logs for error patterns",
                "Verify target accessibility",
                "Update tool definitions and configurations",
                "Enable fallback execution methods"
            ])
        
        return recommendations
    
    async def generate_performance_report(self, hours: float = 1.0) -> PerformanceReport:
        """Generate comprehensive performance report"""
        
        current_time = time.time()
        start_time = current_time - (hours * 3600)
        report_id = f"perf_report_{int(current_time)}"
        
        logger.info(f"📊 Generating performance report for last {hours} hours...")
        
        # System overview
        system_overview = await self._generate_system_overview(start_time, current_time)
        
        # Tool performance analysis
        tool_performance = await self._analyze_tool_performance(start_time, current_time)
        
        # Execution method analysis
        execution_methods = await self._analyze_execution_methods(start_time, current_time)
        
        # Recent alerts
        recent_alerts = [alert for alert in self.alerts if alert.timestamp >= start_time]
        
        # Generate recommendations
        recommendations = await self._generate_recommendations(system_overview, tool_performance)
        
        # Trend analysis
        trends = await self._analyze_trends(start_time, current_time)
        
        report = PerformanceReport(
            report_id=report_id,
            generated_at=current_time,
            time_period=(start_time, current_time),
            system_overview=system_overview,
            tool_performance=tool_performance,
            execution_methods=execution_methods,
            alerts=recent_alerts,
            recommendations=recommendations,
            trends=trends
        )
        
        self.monitoring_stats['reports_generated'] += 1
        
        logger.info(f"✅ Performance report generated: {report_id}")
        
        return report
    
    async def _generate_system_overview(self, start_time: float, end_time: float) -> Dict[str, Any]:
        """Generate system overview for report"""
        
        return {
            'monitoring_period': {
                'start': start_time,
                'end': end_time,
                'duration_hours': (end_time - start_time) / 3600
            },
            'environment': self.environment.value,
            'monitoring_stats': self.monitoring_stats.copy(),
            'current_system_resources': {
                'cpu_percent': psutil.cpu_percent(),
                'memory_percent': psutil.virtual_memory().percent,
                'disk_usage': psutil.disk_usage('/').percent if hasattr(psutil, 'disk_usage') else 0
            },
            'alert_summary': {
                'total_alerts': len([a for a in self.alerts if start_time <= a.timestamp <= end_time]),
                'critical_alerts': len([a for a in self.alerts if start_time <= a.timestamp <= end_time and a.level == AlertLevel.CRITICAL]),
                'warning_alerts': len([a for a in self.alerts if start_time <= a.timestamp <= end_time and a.level == AlertLevel.WARNING])
            }
        }
    
    async def _analyze_tool_performance(self, start_time: float, end_time: float) -> Dict[str, Dict[str, Any]]:
        """Analyze individual tool performance"""
        
        tool_analysis = {}
        
        for tool_name, tool_metrics in self.tool_metrics.items():
            tool_analysis[tool_name] = {
                'execution_count': 0,
                'avg_execution_time': 0.0,
                'success_rate': 0.0,
                'efficiency_score': 0.0,
                'performance_trend': 'stable'
            }
            
            # Analyze execution times
            execution_times = []
            for metric in tool_metrics.get(MetricType.EXECUTION_TIME, []):
                if start_time <= metric.timestamp <= end_time:
                    execution_times.append(metric.value)
            
            if execution_times:
                tool_analysis[tool_name]['execution_count'] = len(execution_times)
                tool_analysis[tool_name]['avg_execution_time'] = statistics.mean(execution_times)
                tool_analysis[tool_name]['min_execution_time'] = min(execution_times)
                tool_analysis[tool_name]['max_execution_time'] = max(execution_times)
            
            # Analyze success rates
            success_rates = []
            for metric in tool_metrics.get(MetricType.SUCCESS_RATE, []):
                if start_time <= metric.timestamp <= end_time:
                    success_rates.append(metric.value)
            
            if success_rates:
                tool_analysis[tool_name]['success_rate'] = statistics.mean(success_rates)
            
            # Calculate efficiency scores
            efficiency_scores = []
            for metric in tool_metrics.get(MetricType.RESOURCE_EFFICIENCY, []):
                if start_time <= metric.timestamp <= end_time:
                    efficiency_scores.append(metric.value)
            
            if efficiency_scores:
                tool_analysis[tool_name]['efficiency_score'] = statistics.mean(efficiency_scores)
        
        return tool_analysis
    
    async def _analyze_execution_methods(self, start_time: float, end_time: float) -> Dict[str, Dict[str, Any]]:
        """Analyze execution method performance"""
        
        method_analysis = {}
        
        for method_name, method_metrics in self.method_metrics.items():
            method_analysis[method_name] = {
                'usage_count': 0,
                'avg_execution_time': 0.0,
                'success_rate': 0.0,
                'reliability_score': 0.0
            }
            
            # Analyze execution times
            execution_times = []
            for metric in method_metrics.get(MetricType.EXECUTION_TIME, []):
                if start_time <= metric.timestamp <= end_time:
                    execution_times.append(metric.value)
            
            if execution_times:
                method_analysis[method_name]['usage_count'] = len(execution_times)
                method_analysis[method_name]['avg_execution_time'] = statistics.mean(execution_times)
        
        return method_analysis
    
    async def _generate_recommendations(self, system_overview: Dict[str, Any], tool_performance: Dict[str, Dict[str, Any]]) -> List[str]:
        """Generate system recommendations based on analysis"""
        
        recommendations = []
        
        # Analyze system resources
        if system_overview['current_system_resources']['cpu_percent'] > 80:
            recommendations.append("High CPU usage detected - consider reducing concurrent executions")
        
        if system_overview['current_system_resources']['memory_percent'] > 85:
            recommendations.append("High memory usage detected - restart application or reduce workload")
        
        # Analyze tool performance
        slow_tools = [tool for tool, data in tool_performance.items() 
                     if data.get('avg_execution_time', 0) > 180]
        
        if slow_tools:
            recommendations.append(f"Slow tool execution detected: {', '.join(slow_tools)} - consider timeout adjustments")
        
        # Analyze success rates
        unreliable_tools = [tool for tool, data in tool_performance.items() 
                           if data.get('success_rate', 1.0) < 0.8]
        
        if unreliable_tools:
            recommendations.append(f"Low success rate tools: {', '.join(unreliable_tools)} - check configurations")
        
        # Alert-based recommendations
        if system_overview['alert_summary']['critical_alerts'] > 0:
            recommendations.append("Critical alerts detected - immediate attention required")
        
        return recommendations
    
    async def _analyze_trends(self, start_time: float, end_time: float) -> Dict[str, Any]:
        """Analyze performance trends"""
        
        trends = {
            'execution_time_trend': 'stable',
            'success_rate_trend': 'stable',
            'resource_usage_trend': 'stable',
            'overall_health': 'good'
        }
        
        # This would analyze metrics over time to determine trends
        # Implementation would look at metrics slopes and patterns
        
        return trends
    
    def get_real_time_metrics(self) -> Dict[str, Any]:
        """Get current real-time metrics"""
        
        current_time = time.time()
        
        return {
            'timestamp': current_time,
            'system_resources': {
                'cpu_percent': psutil.cpu_percent(),
                'memory_percent': psutil.virtual_memory().percent,
                'uptime': current_time - self.monitoring_stats['monitoring_uptime']
            },
            'recent_metrics': {
                metric_type.value: [
                    {'value': m.value, 'timestamp': m.timestamp} 
                    for m in list(self.metrics_buffer[metric_type])[-10:]
                ]
                for metric_type in MetricType
            },
            'active_alerts': len([a for a in self.alerts if current_time - a.timestamp < 300]),
            'monitoring_status': 'active' if self.monitoring_enabled else 'inactive'
        }
    
    def get_monitoring_statistics(self) -> Dict[str, Any]:
        """Get monitoring system statistics"""
        
        return {
            'monitoring_stats': self.monitoring_stats.copy(),
            'metrics_buffer_sizes': {
                metric_type.value: len(metrics)
                for metric_type, metrics in self.metrics_buffer.items()
            },
            'tool_metrics_count': {
                tool_name: {
                    metric_type.value: len(metrics)
                    for metric_type, metrics in tool_metrics.items()
                }
                for tool_name, tool_metrics in self.tool_metrics.items()
            },
            'alert_statistics': {
                'total_alerts': len(self.alerts),
                'recent_alerts': len([a for a in self.alerts if time.time() - a.timestamp < 3600]),
                'alert_levels': {
                    level.value: len([a for a in self.alerts if a.level == level])
                    for level in AlertLevel
                }
            }
        }

# Global performance monitor
performance_monitor = None

async def get_performance_monitor() -> PerformanceMonitor:
    """Get or create performance monitor"""
    global performance_monitor
    
    if performance_monitor is None:
        performance_monitor = PerformanceMonitor()
        await performance_monitor.start_monitoring()
    
    return performance_monitor

async def start_performance_monitoring():
    """Start global performance monitoring"""
    monitor = await get_performance_monitor()
    logger.info("🔍 Global performance monitoring started")

async def stop_performance_monitoring():
    """Stop global performance monitoring"""
    global performance_monitor
    
    if performance_monitor:
        await performance_monitor.stop_monitoring()
        performance_monitor = None
    
    logger.info("🛑 Global performance monitoring stopped")