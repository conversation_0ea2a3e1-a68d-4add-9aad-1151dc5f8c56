/**
 * Multi-Stage Attack Orchestrator Component
 * AI-powered attack chain orchestration with MITRE ATT&CK integration
 */
import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { 
  Workflow,
  Play,
  Square,
  Pause,
  Download,
  Settings,
  Target,
  Globe,
  FileText,
  Terminal,
  Copy,
  RotateCcw,
  Clock,
  Zap,
  Filter,
  Eye,
  AlertTriangle,
  CheckCircle,
  TrendingUp,
  Activity,
  Lock,
  Search,
  List,
  BarChart3,
  Database,
  Server,
  Bug,
  Info,
  Code,
  Package,
  Layers,
  Shield,
  Crosshair,
  Cpu,
  Network,
  Gauge,
  Tag,
  Hash,
  GitBranch,
  Book,
  Users,
  Table,
  Binary,
  Braces,
  FileKey,
  History,
  Folder,
  Star,
  Crown,
  Award,
  Sparkles,
  Timer,
  FastForward,
  SkipForward,
  RefreshCw,
  ExternalLink,
  FileCheck,
  FileWarning,
  Fingerprint,
  Key as KeyIcon,
  LockOpen,
  ShieldOff,
  Verified,
  ScanLine,
  Scan,
  FileJson,
  FileCode,
  Brain,
  Lightbulb,
  TreeDeciduous,
  MapPin,
  Link,
  Plus,
  Minus,
  Edit,
  Trash2,
  Save,
  Upload,
  ArrowRight,
  ArrowDown,
  ChevronRight,
  ChevronDown,
  Maximize2,
  Minimize2,
  MoreHorizontal,
  Move,
  Shuffle,
  RotateCw,
  Repeat,
  PlayCircle,
  StopCircle,
  PauseCircle
} from 'lucide-react';

interface MitreAttackTactic {
  id: string;
  name: string;
  description: string;
  techniques: MitreAttackTechnique[];
}

interface MitreAttackTechnique {
  id: string;
  name: string;
  description: string;
  platforms: string[];
  tactics: string[];
  detection: string;
  mitigation: string;
}

interface AttackChainNode {
  id: string;
  type: 'initial_access' | 'execution' | 'persistence' | 'privilege_escalation' | 'defense_evasion' | 'credential_access' | 'discovery' | 'lateral_movement' | 'collection' | 'exfiltration' | 'impact';
  technique: string;
  techniqueId: string;
  description: string;
  prerequisites: string[];
  tools: string[];
  aiConfidence: number;
  estimatedTime: number;
  successProbability: number;
  position: { x: number; y: number };
  connections: string[];
  status: 'pending' | 'running' | 'success' | 'failed' | 'skipped';
  output?: string;
  timestamp?: string;
}

interface AttackChain {
  id: string;
  name: string;
  description: string;
  target: string;
  nodes: AttackChainNode[];
  createdAt: string;
  updatedAt: string;
  status: 'draft' | 'validated' | 'running' | 'completed' | 'failed';
  aiGenerated: boolean;
  totalSteps: number;
  completedSteps: number;
  estimatedDuration: number;
  actualDuration?: number;
}

interface SafetyConstraint {
  id: string;
  name: string;
  description: string;
  type: 'target_validation' | 'impact_limitation' | 'authorization_check' | 'abort_condition';
  enabled: boolean;
  parameters: Record<string, any>;
}

// MITRE ATT&CK Tactics and Techniques (subset for demonstration)
const MITRE_TACTICS: MitreAttackTactic[] = [
  {
    id: 'TA0001',
    name: 'Initial Access',
    description: 'The adversary is trying to get into your network',
    techniques: [
      {
        id: 'T1566',
        name: 'Phishing',
        description: 'Adversaries may send phishing messages to gain access to victim systems',
        platforms: ['Linux', 'macOS', 'Windows'],
        tactics: ['Initial Access'],
        detection: 'Monitor for suspicious email attachments and links',
        mitigation: 'User training and email security solutions'
      },
      {
        id: 'T1190',
        name: 'Exploit Public-Facing Application',
        description: 'Adversaries may attempt to take advantage of a weakness in an Internet-facing computer or program',
        platforms: ['Linux', 'Windows', 'macOS'],
        tactics: ['Initial Access'],
        detection: 'Monitor application logs for exploitation attempts',
        mitigation: 'Regular patching and vulnerability management'
      }
    ]
  },
  {
    id: 'TA0002',
    name: 'Execution',
    description: 'The adversary is trying to run malicious code',
    techniques: [
      {
        id: 'T1059',
        name: 'Command and Scripting Interpreter',
        description: 'Adversaries may abuse command and script interpreters to execute commands, scripts, or binaries',
        platforms: ['Linux', 'macOS', 'Windows'],
        tactics: ['Execution'],
        detection: 'Monitor process creation and command line arguments',
        mitigation: 'Application control and script blocking'
      },
      {
        id: 'T1569',
        name: 'System Services',
        description: 'Adversaries may abuse system services or daemons to execute commands or programs',
        platforms: ['Linux', 'macOS', 'Windows'],
        tactics: ['Execution', 'Persistence', 'Privilege Escalation'],
        detection: 'Monitor service creation and modification',
        mitigation: 'Limit service account privileges'
      }
    ]
  }
];

// Predefined attack chain templates
const ATTACK_CHAIN_TEMPLATES: Partial<AttackChain>[] = [
  {
    name: 'Web Application Penetration',
    description: 'Comprehensive web application security assessment',
    nodes: [
      {
        id: '1',
        type: 'initial_access',
        technique: 'Exploit Public-Facing Application',
        techniqueId: 'T1190',
        description: 'Identify and exploit web application vulnerabilities',
        prerequisites: ['Target web application URL'],
        tools: ['Nuclei', 'SQLMap', 'Burp Suite'],
        aiConfidence: 85,
        estimatedTime: 30,
        successProbability: 75,
        position: { x: 100, y: 100 },
        connections: ['2'],
        status: 'pending'
      },
      {
        id: '2',
        type: 'execution',
        technique: 'Command and Scripting Interpreter',
        techniqueId: 'T1059',
        description: 'Execute commands through web shell or injection',
        prerequisites: ['Successful initial exploitation'],
        tools: ['Custom payloads', 'Web shells'],
        aiConfidence: 80,
        estimatedTime: 15,
        successProbability: 70,
        position: { x: 300, y: 100 },
        connections: ['3'],
        status: 'pending'
      }
    ]
  },
  {
    name: 'Network Infrastructure Assessment',
    description: 'Network-focused penetration testing approach',
    nodes: [
      {
        id: '1',
        type: 'discovery',
        technique: 'Network Service Scanning',
        techniqueId: 'T1046',
        description: 'Discover network services and open ports',
        prerequisites: ['Target network range'],
        tools: ['Nmap', 'Masscan'],
        aiConfidence: 95,
        estimatedTime: 20,
        successProbability: 95,
        position: { x: 100, y: 100 },
        connections: ['2'],
        status: 'pending'
      }
    ]
  }
];

// Safety constraints
const DEFAULT_SAFETY_CONSTRAINTS: SafetyConstraint[] = [
  {
    id: 'target_validation',
    name: 'Target Validation',
    description: 'Verify target is authorized for testing',
    type: 'target_validation',
    enabled: true,
    parameters: { requireAuthorization: true, verificationMethod: 'manual' }
  },
  {
    id: 'impact_limitation',
    name: 'Impact Limitation',
    description: 'Limit potential damage to target systems',
    type: 'impact_limitation',
    enabled: true,
    parameters: { maxConcurrency: 5, rateLimiting: true, destructiveOperations: false }
  },
  {
    id: 'abort_conditions',
    name: 'Abort Conditions',
    description: 'Automatically abort under certain conditions',
    type: 'abort_condition',
    enabled: true,
    parameters: { maxFailures: 3, timeoutMinutes: 120, criticalErrors: ['connection_refused', 'authentication_failure'] }
  }
];

export const MultiStageOrchestrator: React.FC = () => {
  const [activeTab, setActiveTab] = useState('builder');
  const [selectedChain, setSelectedChain] = useState<AttackChain | null>(null);
  const [attackChains, setAttackChains] = useState<AttackChain[]>([]);
  const [safetyConstraints, setSafetyConstraints] = useState<SafetyConstraint[]>(DEFAULT_SAFETY_CONSTRAINTS);
  const [isRunning, setIsRunning] = useState(false);
  const [progress, setProgress] = useState(0);
  const [aiRecommendations, setAiRecommendations] = useState<string[]>([]);
  const [targetInfo, setTargetInfo] = useState({
    url: '',
    ipRange: '',
    platform: '',
    description: ''
  });

  // Chain builder state
  const [newChain, setNewChain] = useState<Partial<AttackChain>>({
    name: '',
    description: '',
    target: '',
    nodes: [],
    status: 'draft',
    aiGenerated: false,
    totalSteps: 0,
    completedSteps: 0,
    estimatedDuration: 0
  });

  useEffect(() => {
    loadAttackChains();
    fetchAIRecommendations();
  }, []);

  const loadAttackChains = () => {
    // Load existing attack chains from storage
    const stored = localStorage.getItem('nexusscan_attack_chains');
    if (stored) {
      setAttackChains(JSON.parse(stored));
    }
  };

  const saveAttackChains = (chains: AttackChain[]) => {
    localStorage.setItem('nexusscan_attack_chains', JSON.stringify(chains));
    setAttackChains(chains);
  };

  const fetchAIRecommendations = async () => {
    try {
      const response = await fetch('http://************:8090/api/orchestrator/analytics');
      if (response.ok) {
        const data = await response.json();
        setAiRecommendations(data.recommendations || []);
      }
    } catch (error) {
      console.error('Failed to fetch AI recommendations:', error);
    }
  };

  const generateAIChain = async () => {
    if (!targetInfo.url && !targetInfo.ipRange) {
      alert('Please specify a target URL or IP range');
      return;
    }

    try {
      const response = await fetch('http://************:8090/api/orchestrator/generate-chain', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          target: targetInfo,
          constraints: safetyConstraints.filter(c => c.enabled),
          preferences: {
            aggressiveness: 'moderate',
            includeExploitation: true,
            timeLimit: 120
          }
        })
      });

      if (response.ok) {
        const generatedChain = await response.json();
        const newChain: AttackChain = {
          ...generatedChain,
          id: Date.now().toString(),
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          status: 'validated',
          aiGenerated: true
        };
        
        const updatedChains = [...attackChains, newChain];
        saveAttackChains(updatedChains);
        setSelectedChain(newChain);
        setActiveTab('execution');
      }
    } catch (error) {
      console.error('Failed to generate AI chain:', error);
    }
  };

  const executeAttackChain = async (chain: AttackChain) => {
    if (!chain) return;

    setIsRunning(true);
    setProgress(0);

    try {
      const response = await fetch('http://************:8090/api/orchestrator/execute-chain', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          chainId: chain.id,
          chain: chain,
          constraints: safetyConstraints.filter(c => c.enabled)
        })
      });

      if (response.ok) {
        // Set up WebSocket for real-time updates
        const ws = new WebSocket('wss://************:8090/ws');
        
        ws.onmessage = (event) => {
          const data = JSON.parse(event.data);
          if (data.type === 'orchestrator_progress') {
            setProgress(data.progress);
            
            // Update chain status
            const updatedChain = { ...chain };
            updatedChain.nodes = updatedChain.nodes.map(node => {
              if (node.id === data.nodeId) {
                return { ...node, status: data.status, output: data.output, timestamp: data.timestamp };
              }
              return node;
            });
            
            setSelectedChain(updatedChain);
          }
        };

        ws.onclose = () => {
          setIsRunning(false);
        };
      }
    } catch (error) {
      console.error('Failed to execute attack chain:', error);
      setIsRunning(false);
    }
  };

  const stopExecution = () => {
    setIsRunning(false);
    // Send stop signal to backend
    fetch('http://************:8090/api/orchestrator/stop', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ chainId: selectedChain?.id })
    });
  };

  const createChainFromTemplate = (template: Partial<AttackChain>) => {
    const newChain: AttackChain = {
      id: Date.now().toString(),
      name: template.name || 'New Attack Chain',
      description: template.description || '',
      target: targetInfo.url || targetInfo.ipRange,
      nodes: template.nodes || [],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      status: 'draft',
      aiGenerated: false,
      totalSteps: template.nodes?.length || 0,
      completedSteps: 0,
      estimatedDuration: template.nodes?.reduce((sum, node) => sum + node.estimatedTime, 0) || 0
    };

    setNewChain(newChain);
    setActiveTab('builder');
  };

  const exportChain = (chain: AttackChain) => {
    const dataStr = JSON.stringify(chain, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `attack_chain_${chain.name.replace(/\s+/g, '_')}.json`;
    link.click();
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'text-gray-600 bg-gray-50 border-gray-200';
      case 'running': return 'text-blue-600 bg-blue-50 border-blue-200';
      case 'success': return 'text-green-600 bg-green-50 border-green-200';
      case 'failed': return 'text-red-600 bg-red-50 border-red-200';
      case 'skipped': return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      default: return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const getTacticIcon = (type: string) => {
    switch (type) {
      case 'initial_access': return Globe;
      case 'execution': return Terminal;
      case 'persistence': return Lock;
      case 'privilege_escalation': return TrendingUp;
      case 'defense_evasion': return Shield;
      case 'credential_access': return KeyIcon;
      case 'discovery': return Search;
      case 'lateral_movement': return Network;
      case 'collection': return Database;
      case 'exfiltration': return Upload;
      case 'impact': return Zap;
      default: return Target;
    }
  };

  return (
    <div className="space-y-6">
      {/* Header Actions */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button 
            onClick={generateAIChain} 
            disabled={isRunning}
            className="bg-purple-600 hover:bg-purple-700"
          >
            <Brain className="h-4 w-4 mr-2" />
            Generate AI Chain
          </Button>
          {selectedChain && (
            <div className="flex space-x-2">
              <Button 
                onClick={() => executeAttackChain(selectedChain)} 
                disabled={isRunning}
                variant="default"
              >
                <Play className="h-4 w-4 mr-2" />
                Execute Chain
              </Button>
              {isRunning && (
                <Button onClick={stopExecution} variant="destructive">
                  <Square className="h-4 w-4 mr-2" />
                  Stop
                </Button>
              )}
              <Button onClick={() => exportChain(selectedChain)} variant="outline">
                <Download className="h-4 w-4 mr-2" />
                Export
              </Button>
            </div>
          )}
        </div>
        
        {isRunning && (
          <div className="flex items-center space-x-3">
            <div className="text-sm text-gray-600">Progress: {progress}%</div>
            <Progress value={progress} className="w-32" />
          </div>
        )}
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="target">Target Setup</TabsTrigger>
          <TabsTrigger value="templates">Templates</TabsTrigger>
          <TabsTrigger value="builder">Chain Builder</TabsTrigger>
          <TabsTrigger value="execution">Execution</TabsTrigger>
          <TabsTrigger value="results">Results</TabsTrigger>
        </TabsList>

        {/* Target Setup Tab */}
        <TabsContent value="target" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Target className="h-5 w-5" />
                <span>Target Configuration</span>
              </CardTitle>
              <CardDescription>Define the target environment for attack chain generation</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium">Target URL</label>
                  <Input
                    placeholder="https://example.com"
                    value={targetInfo.url}
                    onChange={(e) => setTargetInfo({...targetInfo, url: e.target.value})}
                  />
                </div>
                <div className="space-y-2">
                  <label className="text-sm font-medium">IP Range (optional)</label>
                  <Input
                    placeholder="***********/24"
                    value={targetInfo.ipRange}
                    onChange={(e) => setTargetInfo({...targetInfo, ipRange: e.target.value})}
                  />
                </div>
                <div className="space-y-2">
                  <label className="text-sm font-medium">Platform</label>
                  <Select value={targetInfo.platform} onValueChange={(value) => setTargetInfo({...targetInfo, platform: value})}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select platform" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="windows">Windows</SelectItem>
                      <SelectItem value="linux">Linux</SelectItem>
                      <SelectItem value="macos">macOS</SelectItem>
                      <SelectItem value="web">Web Application</SelectItem>
                      <SelectItem value="network">Network Infrastructure</SelectItem>
                      <SelectItem value="cloud">Cloud Environment</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <label className="text-sm font-medium">Description</label>
                  <Textarea
                    placeholder="Additional target information..."
                    value={targetInfo.description}
                    onChange={(e) => setTargetInfo({...targetInfo, description: e.target.value})}
                    rows={3}
                  />
                </div>
              </div>

              {/* Safety Constraints */}
              <div className="space-y-4 pt-4 border-t">
                <h3 className="text-lg font-semibold flex items-center space-x-2">
                  <Shield className="h-5 w-5" />
                  <span>Safety Constraints</span>
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {safetyConstraints.map((constraint) => (
                    <Card key={constraint.id} className="p-4">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center space-x-2">
                            <input
                              type="checkbox"
                              checked={constraint.enabled}
                              onChange={(e) => {
                                const updated = safetyConstraints.map(c =>
                                  c.id === constraint.id ? {...c, enabled: e.target.checked} : c
                                );
                                setSafetyConstraints(updated);
                              }}
                              className="rounded"
                            />
                            <h4 className="font-medium">{constraint.name}</h4>
                          </div>
                          <p className="text-sm text-gray-600 mt-1">{constraint.description}</p>
                        </div>
                        <Badge variant={constraint.enabled ? 'default' : 'secondary'}>
                          {constraint.enabled ? 'Enabled' : 'Disabled'}
                        </Badge>
                      </div>
                    </Card>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Templates Tab */}
        <TabsContent value="templates" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Book className="h-5 w-5" />
                <span>Attack Chain Templates</span>
              </CardTitle>
              <CardDescription>Pre-built attack chains for common scenarios</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {ATTACK_CHAIN_TEMPLATES.map((template, index) => (
                  <Card key={index} className="p-4 cursor-pointer hover:shadow-md transition-shadow"
                        onClick={() => createChainFromTemplate(template)}>
                    <div className="space-y-3">
                      <div className="flex items-start justify-between">
                        <h3 className="font-semibold">{template.name}</h3>
                        <Badge variant="outline">{template.nodes?.length || 0} steps</Badge>
                      </div>
                      <p className="text-sm text-gray-600">{template.description}</p>
                      <div className="flex items-center space-x-4 text-sm text-gray-500">
                        <span className="flex items-center space-x-1">
                          <Clock className="h-4 w-4" />
                          <span>{template.nodes?.reduce((sum, node) => sum + node.estimatedTime, 0) || 0}m</span>
                        </span>
                        <span className="flex items-center space-x-1">
                          <Gauge className="h-4 w-4" />
                          <span>{template.nodes?.reduce((sum, node) => sum + node.aiConfidence, 0) / (template.nodes?.length || 1) || 0}% confidence</span>
                        </span>
                      </div>
                    </div>
                  </Card>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* AI Recommendations */}
          {aiRecommendations.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Lightbulb className="h-5 w-5" />
                  <span>AI Recommendations</span>
                </CardTitle>
                <CardDescription>AI-suggested improvements and techniques</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {aiRecommendations.map((recommendation, index) => (
                    <div key={index} className="flex items-start space-x-3 p-3 bg-blue-50 rounded-lg">
                      <Brain className="h-5 w-5 text-blue-600 mt-0.5" />
                      <p className="text-sm text-blue-800">{recommendation}</p>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        {/* Chain Builder Tab */}
        <TabsContent value="builder" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Workflow className="h-5 w-5" />
                <span>Attack Chain Builder</span>
              </CardTitle>
              <CardDescription>Build custom attack chains with MITRE ATT&CK techniques</CardDescription>
            </CardHeader>
            <CardContent>
              {/* Chain Metadata */}
              <div className="space-y-4 mb-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Chain Name</label>
                    <Input
                      placeholder="Enter attack chain name"
                      value={newChain.name}
                      onChange={(e) => setNewChain({...newChain, name: e.target.value})}
                    />
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Target</label>
                    <Input
                      placeholder="Target system or application"
                      value={newChain.target}
                      onChange={(e) => setNewChain({...newChain, target: e.target.value})}
                    />
                  </div>
                </div>
                <div className="space-y-2">
                  <label className="text-sm font-medium">Description</label>
                  <Textarea
                    placeholder="Describe the attack chain objectives..."
                    value={newChain.description}
                    onChange={(e) => setNewChain({...newChain, description: e.target.value})}
                    rows={3}
                  />
                </div>
              </div>

              {/* MITRE ATT&CK Matrix */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">MITRE ATT&CK Techniques</h3>
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {MITRE_TACTICS.map((tactic) => (
                    <Card key={tactic.id} className="p-4">
                      <h4 className="font-semibold text-lg mb-2">{tactic.name}</h4>
                      <p className="text-sm text-gray-600 mb-4">{tactic.description}</p>
                      <div className="space-y-2">
                        {tactic.techniques.map((technique) => (
                          <div key={technique.id} 
                               className="p-3 border rounded-lg hover:bg-gray-50 cursor-pointer">
                            <div className="flex items-start justify-between">
                              <div className="flex-1">
                                <h5 className="font-medium">{technique.name}</h5>
                                <p className="text-sm text-gray-600 mt-1">{technique.description}</p>
                                <div className="flex items-center space-x-2 mt-2">
                                  <Badge variant="outline" className="text-xs">{technique.id}</Badge>
                                  {technique.platforms.map(platform => (
                                    <Badge key={platform} variant="secondary" className="text-xs">
                                      {platform}
                                    </Badge>
                                  ))}
                                </div>
                              </div>
                              <Button size="sm" variant="outline">
                                <Plus className="h-4 w-4" />
                              </Button>
                            </div>
                          </div>
                        ))}
                      </div>
                    </Card>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Execution Tab */}
        <TabsContent value="execution" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <PlayCircle className="h-5 w-5" />
                <span>Attack Chain Execution</span>
              </CardTitle>
              <CardDescription>Monitor and control attack chain execution</CardDescription>
            </CardHeader>
            <CardContent>
              {selectedChain ? (
                <div className="space-y-6">
                  {/* Chain Overview */}
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <Card className="p-4">
                      <div className="flex items-center space-x-3">
                        <Target className="h-8 w-8 text-blue-600" />
                        <div>
                          <p className="text-sm text-gray-600">Target</p>
                          <p className="font-semibold">{selectedChain.target}</p>
                        </div>
                      </div>
                    </Card>
                    <Card className="p-4">
                      <div className="flex items-center space-x-3">
                        <List className="h-8 w-8 text-green-600" />
                        <div>
                          <p className="text-sm text-gray-600">Total Steps</p>
                          <p className="font-semibold">{selectedChain.totalSteps}</p>
                        </div>
                      </div>
                    </Card>
                    <Card className="p-4">
                      <div className="flex items-center space-x-3">
                        <CheckCircle className="h-8 w-8 text-purple-600" />
                        <div>
                          <p className="text-sm text-gray-600">Completed</p>
                          <p className="font-semibold">{selectedChain.completedSteps}</p>
                        </div>
                      </div>
                    </Card>
                    <Card className="p-4">
                      <div className="flex items-center space-x-3">
                        <Clock className="h-8 w-8 text-orange-600" />
                        <div>
                          <p className="text-sm text-gray-600">Duration</p>
                          <p className="font-semibold">{selectedChain.estimatedDuration}m</p>
                        </div>
                      </div>
                    </Card>
                  </div>

                  {/* Execution Timeline */}
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold">Execution Timeline</h3>
                    <div className="space-y-3">
                      {selectedChain.nodes.map((node, index) => {
                        const TacticIcon = getTacticIcon(node.type);
                        return (
                          <Card key={node.id} className="p-4">
                            <div className="flex items-center space-x-4">
                              <div className="flex items-center space-x-3">
                                <div className="text-sm font-medium text-gray-500">
                                  {index + 1}
                                </div>
                                <div className="p-2 bg-gray-100 rounded-lg">
                                  <TacticIcon className="h-5 w-5 text-gray-600" />
                                </div>
                              </div>
                              
                              <div className="flex-1">
                                <div className="flex items-start justify-between">
                                  <div>
                                    <h4 className="font-semibold">{node.technique}</h4>
                                    <p className="text-sm text-gray-600">{node.description}</p>
                                    <div className="flex items-center space-x-2 mt-2">
                                      <Badge variant="outline" className="text-xs">{node.techniqueId}</Badge>
                                      <Badge variant="secondary" className="text-xs capitalize">{node.type.replace('_', ' ')}</Badge>
                                      <span className="text-xs text-gray-500">
                                        {node.aiConfidence}% confidence
                                      </span>
                                    </div>
                                  </div>
                                  
                                  <div className="flex items-center space-x-2">
                                    <Badge className={getStatusColor(node.status)} variant="outline">
                                      {node.status}
                                    </Badge>
                                    {node.timestamp && (
                                      <span className="text-xs text-gray-500">{node.timestamp}</span>
                                    )}
                                  </div>
                                </div>
                                
                                {node.output && (
                                  <div className="mt-3 p-3 bg-gray-50 rounded-lg">
                                    <pre className="text-sm font-mono whitespace-pre-wrap">{node.output}</pre>
                                  </div>
                                )}
                              </div>
                            </div>
                          </Card>
                        );
                      })}
                    </div>
                  </div>
                </div>
              ) : (
                <div className="text-center py-12">
                  <Workflow className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">No Attack Chain Selected</h3>
                  <p className="text-gray-600 mb-4">Select an attack chain from the builder or generate one using AI</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Results Tab */}
        <TabsContent value="results" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <BarChart3 className="h-5 w-5" />
                <span>Execution Results</span>
              </CardTitle>
              <CardDescription>Analysis and reporting of attack chain results</CardDescription>
            </CardHeader>
            <CardContent>
              {selectedChain && selectedChain.status === 'completed' ? (
                <div className="space-y-6">
                  {/* Results Summary */}
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <Card className="p-4 bg-green-50 border-green-200">
                      <div className="flex items-center space-x-3">
                        <CheckCircle className="h-8 w-8 text-green-600" />
                        <div>
                          <p className="text-sm text-green-600">Successful Steps</p>
                          <p className="text-2xl font-bold text-green-700">
                            {selectedChain.nodes.filter(n => n.status === 'success').length}
                          </p>
                        </div>
                      </div>
                    </Card>
                    
                    <Card className="p-4 bg-red-50 border-red-200">
                      <div className="flex items-center space-x-3">
                        <AlertTriangle className="h-8 w-8 text-red-600" />
                        <div>
                          <p className="text-sm text-red-600">Failed Steps</p>
                          <p className="text-2xl font-bold text-red-700">
                            {selectedChain.nodes.filter(n => n.status === 'failed').length}
                          </p>
                        </div>
                      </div>
                    </Card>
                    
                    <Card className="p-4 bg-blue-50 border-blue-200">
                      <div className="flex items-center space-x-3">
                        <Clock className="h-8 w-8 text-blue-600" />
                        <div>
                          <p className="text-sm text-blue-600">Total Duration</p>
                          <p className="text-2xl font-bold text-blue-700">
                            {selectedChain.actualDuration || selectedChain.estimatedDuration}m
                          </p>
                        </div>
                      </div>
                    </Card>
                  </div>

                  {/* Export Options */}
                  <Card className="p-4">
                    <h3 className="font-semibold mb-4">Export Results</h3>
                    <div className="flex space-x-3">
                      <Button onClick={() => exportChain(selectedChain)} variant="outline">
                        <FileJson className="h-4 w-4 mr-2" />
                        JSON Report
                      </Button>
                      <Button variant="outline">
                        <FileText className="h-4 w-4 mr-2" />
                        PDF Report
                      </Button>
                      <Button variant="outline">
                        <FileCode className="h-4 w-4 mr-2" />
                        Command History
                      </Button>
                    </div>
                  </Card>
                </div>
              ) : (
                <div className="text-center py-12">
                  <BarChart3 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">No Results Available</h3>
                  <p className="text-gray-600">Execute an attack chain to view results and analysis</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};