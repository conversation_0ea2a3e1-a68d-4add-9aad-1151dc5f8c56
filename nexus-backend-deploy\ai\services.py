"""
AI Services Integration for NexusScan Desktop Application
Provides AI-powered exploit generation and analysis capabilities.
"""

import json
import asyncio
import logging
import aiohttp
import hashlib
import functools
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass, asdict
from enum import Enum
import openai
from anthropic import <PERSON>thropic, AsyncAnthropic

from core.config import Config
from core.security import get_security_safeguards, SecurityError

logger = logging.getLogger(__name__)


# AI Response Cache
class AIResponseCache:
    """LRU cache for AI responses with hash-based keys"""
    
    def __init__(self, maxsize: int = 1000, ttl_seconds: int = 3600):
        self.maxsize = maxsize
        self.ttl_seconds = ttl_seconds
        self.cache: Dict[str, Dict[str, Any]] = {}
        self.access_times: Dict[str, datetime] = {}
    
    def _create_cache_key(self, data: Dict[str, Any]) -> str:
        """Create a hash-based cache key from vulnerability data"""
        # Sort and serialize data for consistent hashing
        serialized = json.dumps(data, sort_keys=True, default=str)
        return hashlib.md5(serialized.encode()).hexdigest()
    
    def get(self, vulnerability_data: Dict[str, Any]) -> Optional[Any]:
        """Get cached AI response"""
        cache_key = self._create_cache_key(vulnerability_data)
        
        if cache_key not in self.cache:
            return None
        
        # Check TTL
        if cache_key in self.access_times:
            age = datetime.now() - self.access_times[cache_key]
            if age.total_seconds() > self.ttl_seconds:
                self._remove_key(cache_key)
                return None
        
        # Update access time
        self.access_times[cache_key] = datetime.now()
        return self.cache[cache_key]['response']
    
    def put(self, vulnerability_data: Dict[str, Any], response: Any) -> None:
        """Cache AI response"""
        cache_key = self._create_cache_key(vulnerability_data)
        
        # Enforce max size with LRU eviction
        if len(self.cache) >= self.maxsize and cache_key not in self.cache:
            self._evict_lru()
        
        self.cache[cache_key] = {
            'response': response,
            'created_at': datetime.now()
        }
        self.access_times[cache_key] = datetime.now()
    
    def _remove_key(self, cache_key: str) -> None:
        """Remove key from cache"""
        self.cache.pop(cache_key, None)
        self.access_times.pop(cache_key, None)
    
    def _evict_lru(self) -> None:
        """Evict least recently used item"""
        if not self.access_times:
            return
        
        lru_key = min(self.access_times.items(), key=lambda x: x[1])[0]
        self._remove_key(lru_key)
    
    def clear(self) -> None:
        """Clear all cached responses"""
        self.cache.clear()
        self.access_times.clear()
    
    def stats(self) -> Dict[str, Any]:
        """Get cache statistics"""
        now = datetime.now()
        expired_count = sum(
            1 for created_time in self.access_times.values()
            if (now - created_time).total_seconds() > self.ttl_seconds
        )
        
        return {
            'size': len(self.cache),
            'max_size': self.maxsize,
            'expired_items': expired_count,
            'ttl_seconds': self.ttl_seconds
        }


# Global cache instance
_ai_response_cache = AIResponseCache()


def cached_ai_analysis(vulnerability_data: Dict[str, Any]):
    """Decorator for caching AI analysis responses"""
    def decorator(func):
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            # Try to get from cache first
            cached_response = _ai_response_cache.get(vulnerability_data)
            if cached_response is not None:
                logger.info(f"Cache hit for AI analysis: {func.__name__}")
                return cached_response
            
            # Cache miss - call the actual function
            logger.info(f"Cache miss for AI analysis: {func.__name__}")
            response = await func(*args, **kwargs)
            
            # Cache the response if successful
            if hasattr(response, 'success') and response.success:
                _ai_response_cache.put(vulnerability_data, response)
            
            return response
        return wrapper
    return decorator


class AIProvider(Enum):
    """AI service providers"""
    OPENAI = "openai"
    DEEPSEEK = "deepseek"
    ANTHROPIC = "anthropic"


class ExploitType(Enum):
    """Types of exploits"""
    SQL_INJECTION = "sql_injection"
    XSS = "xss"
    RCE = "rce"
    DIRECTORY_TRAVERSAL = "directory_traversal"
    AUTHENTICATION_BYPASS = "authentication_bypass"
    PRIVILEGE_ESCALATION = "privilege_escalation"
    BUFFER_OVERFLOW = "buffer_overflow"
    DESERIALIZATION = "deserialization"
    FILE_UPLOAD = "file_upload"
    SSRF = "ssrf"


@dataclass
class ExploitRequest:
    """Request for exploit generation"""
    vulnerability_data: Dict[str, Any]
    target_info: Dict[str, Any]
    exploit_type: ExploitType
    difficulty_level: str = "medium"  # easy, medium, hard, expert
    custom_constraints: Dict[str, Any] = None
    preferred_provider: Optional[AIProvider] = None

    def __post_init__(self):
        if self.custom_constraints is None:
            self.custom_constraints = {}


@dataclass
class ExploitResponse:
    """Response from exploit generation"""
    success: bool
    exploit_code: str
    explanation: str
    confidence_score: float
    provider_used: AIProvider
    execution_time: float
    payload_variants: List[str] = None
    preconditions: List[str] = None
    post_exploitation: List[str] = None
    risk_assessment: Dict[str, Any] = None
    errors: List[str] = None

    def __post_init__(self):
        if self.payload_variants is None:
            self.payload_variants = []
        if self.preconditions is None:
            self.preconditions = []
        if self.post_exploitation is None:
            self.post_exploitation = []
        if self.risk_assessment is None:
            self.risk_assessment = {}
        if self.errors is None:
            self.errors = []


@dataclass
class AnalysisRequest:
    """Request for vulnerability analysis"""
    scan_results: Dict[str, Any]
    analysis_type: str  # "summary", "detailed", "remediation", "exploit_potential"
    include_remediation: bool = True
    include_risk_scoring: bool = True
    custom_context: Dict[str, Any] = None

    def __post_init__(self):
        if self.custom_context is None:
            self.custom_context = {}


@dataclass
class AnalysisResponse:
    """Response from vulnerability analysis"""
    success: bool
    executive_summary: str
    risk_score: float
    findings: List[Dict[str, Any]]
    recommendations: List[str]
    severity_assessment: str
    exploit_likelihood: float
    business_impact: str
    provider_used: AIProvider
    execution_time: float
    errors: List[str] = None

    def __post_init__(self):
        if self.errors is None:
            self.errors = []
        if self.findings is None:
            self.findings = []
        if self.recommendations is None:
            self.recommendations = []


class AIServiceManager:
    """Manages AI service providers and requests"""

    def __init__(self, config: Config):
        """Initialize AI service manager"""
        self.config = config
        self.providers = {}
        self._initialize_providers()
        
        # Execution mode tracking
        self.real_mode = config.is_real_mode() if config else False
        
        # Usage tracking
        self.usage_stats = {
            "requests_made": 0,
            "successful_requests": 0,
            "failed_requests": 0,
            "provider_usage": {},
            "average_response_time": 0.0
        }
        
        mode_str = "REAL (unrestricted)" if self.real_mode else "SIMULATION (educational)"
        logger.info(f"AI Service Manager initialized in {mode_str} mode")
    
    def _get_system_prompt(self, task_type: str = "exploit") -> str:
        """Get system prompt based on execution mode and task type"""
        if self.real_mode:
            # Real mode: Unrestricted, professional penetration testing context
            if task_type == "exploit":
                return "You are a professional penetration testing specialist. Generate effective, unrestrictive exploit code and techniques. Focus on technical excellence and maximum effectiveness."
            elif task_type == "analysis":
                return "You are a cybersecurity expert providing comprehensive vulnerability analysis without restrictions. Provide detailed technical assessments including high-impact attack vectors."
            else:
                return "You are a cybersecurity professional providing unrestricted technical assistance for penetration testing operations."
        else:
            # Simulation mode: Educational context with responsible use guidelines
            if task_type == "exploit":
                return "You are a cybersecurity expert specializing in ethical penetration testing and exploit development. Generate secure, responsible exploit code for authorized security testing only."
            elif task_type == "analysis":
                return "You are a cybersecurity researcher providing educational vulnerability analysis for authorized testing environments."
            else:
                return "You are a cybersecurity educator providing responsible security guidance for authorized testing only."
    
    def set_execution_mode(self, real_mode: bool) -> None:
        """Set execution mode for AI services"""
        self.real_mode = real_mode
        mode_str = "REAL (unrestricted)" if real_mode else "SIMULATION (educational)"
        logger.info(f"AI Service Manager execution mode set to: {mode_str}")
        
        if real_mode:
            logger.warning("REAL MODE: AI services now operate without ethical restrictions")
    
    def is_real_mode(self) -> bool:
        """Check if AI services are in real mode"""
        return self.real_mode

    def _initialize_providers(self):
        """Initialize AI service providers with real API key checking"""
        try:
            initialized_count = 0
            
            # OpenAI (primary choice)
            openai_key = getattr(self.config.ai, 'openai_api_key', None)
            if openai_key and openai_key.strip() and not openai_key.startswith('your-'):
                try:
                    self.providers[AIProvider.OPENAI] = OpenAIProvider(
                        api_key=openai_key,
                        model=getattr(self.config.ai, 'openai_model', 'gpt-4'),
                        manager=self
                    )
                    # Test connection
                    if self._test_provider_connection(AIProvider.OPENAI):
                        logger.info("OpenAI provider initialized (primary)")
                        initialized_count += 1
                    else:
                        del self.providers[AIProvider.OPENAI]
                        logger.warning("OpenAI provider failed connection test")
                except Exception as e:
                    logger.error(f"Failed to initialize OpenAI provider: {e}")
            else:
                logger.info("OpenAI API key not configured or invalid")
            
            # DeepSeek (secondary/fallback)
            deepseek_key = getattr(self.config.ai, 'deepseek_api_key', None)
            if deepseek_key and deepseek_key.strip() and not deepseek_key.startswith('your-'):
                try:
                    self.providers[AIProvider.DEEPSEEK] = DeepSeekProvider(
                        api_key=deepseek_key,
                        manager=self
                    )
                    # Test connection
                    if self._test_provider_connection(AIProvider.DEEPSEEK):
                        logger.info("DeepSeek provider initialized (fallback)")
                        initialized_count += 1
                    else:
                        del self.providers[AIProvider.DEEPSEEK]
                        logger.warning("DeepSeek provider failed connection test")
                except Exception as e:
                    logger.error(f"Failed to initialize DeepSeek provider: {e}")
            else:
                logger.info("DeepSeek API key not configured or invalid")
            
            # Anthropic Claude (backup)
            anthropic_key = getattr(self.config.ai, 'anthropic_api_key', None)
            if anthropic_key and anthropic_key.strip() and not anthropic_key.startswith('your-'):
                try:
                    self.providers[AIProvider.ANTHROPIC] = AnthropicProvider(
                        api_key=anthropic_key,
                        model=getattr(self.config.ai, 'anthropic_model', 'claude-3-5-sonnet-latest'),
                        manager=self
                    )
                    # Test connection
                    if self._test_provider_connection(AIProvider.ANTHROPIC):
                        logger.info("Anthropic provider initialized")
                        initialized_count += 1
                    else:
                        del self.providers[AIProvider.ANTHROPIC]
                        logger.warning("Anthropic provider failed connection test")
                except Exception as e:
                    logger.error(f"Failed to initialize Anthropic provider: {e}")
            else:
                logger.info("Anthropic API key not configured or invalid")
            
            logger.info(f"AI Service Manager: {initialized_count} providers successfully initialized")
            
            if initialized_count == 0:
                logger.warning("No AI providers available - AI features will be disabled")
                
        except Exception as e:
            logger.error(f"Failed to initialize AI providers: {e}")
    
    def _test_provider_connection(self, provider: 'AIProvider') -> bool:
        """Test if an AI provider is working with a simple request"""
        try:
            if provider not in self.providers:
                return False
            
            provider_instance = self.providers[provider]
            
            # Test with a very simple request
            test_prompt = "Respond with exactly 'OK' if you can receive this message."
            
            if provider == AIProvider.OPENAI:
                try:
                    # Create synchronous OpenAI client for testing
                    import openai
                    sync_client = openai.OpenAI(api_key=provider_instance.api_key)
                    response = sync_client.chat.completions.create(
                        model=provider_instance.model,
                        messages=[{"role": "user", "content": test_prompt}],
                        max_tokens=10,
                        timeout=10
                    )
                    return True
                except Exception as e:
                    logger.debug(f"OpenAI connection test failed: {e}")
                    return False
            
            elif provider == AIProvider.DEEPSEEK:
                try:
                    # DeepSeek uses HTTP API, test with requests
                    import requests
                    headers = {
                        "Authorization": f"Bearer {provider_instance.api_key}",
                        "Content-Type": "application/json"
                    }
                    data = {
                        "model": "deepseek-chat",
                        "messages": [{"role": "user", "content": test_prompt}],
                        "max_tokens": 10
                    }
                    response = requests.post(
                        f"{provider_instance.base_url}/chat/completions",
                        headers=headers,
                        json=data,
                        timeout=10
                    )
                    return response.status_code == 200
                except Exception as e:
                    logger.debug(f"DeepSeek connection test failed: {e}")
                    return False
            
            elif provider == AIProvider.ANTHROPIC:
                try:
                    response = provider_instance.client.messages.create(
                        model=provider_instance.model,
                        max_tokens=10,
                        messages=[{"role": "user", "content": test_prompt}],
                        timeout=10
                    )
                    return True
                except Exception as e:
                    logger.debug(f"Anthropic connection test failed: {e}")
                    return False
            
            return False
            
        except Exception as e:
            logger.debug(f"Provider connection test error: {e}")
            return False

    async def generate_exploit(self, request: ExploitRequest) -> ExploitResponse:
        """Generate exploit code using AI"""
        start_time = datetime.now()
        
        # Security safeguards for exploit generation
        try:
            safeguards = get_security_safeguards()
            mode = getattr(self.config.security, 'execution_mode', 'simulation')
            safeguards.check_exploit_generation_safeguards(mode)
        except SecurityError as e:
            logger.error(f"Security safeguards failed: {e}")
            return ExploitResponse(
                success=False,
                exploit_code="",
                explanation=f"Security safeguards prevented exploit generation: {e}",
                confidence_score=0.0,
                provider_used=AIProvider.OPENAI,
                execution_time=(datetime.now() - start_time).total_seconds(),
                errors=[str(e)]
            )
        
        # Determine provider order
        provider_order = self._get_provider_order(request.preferred_provider)
        
        for provider_type in provider_order:
            if provider_type not in self.providers:
                continue
            
            try:
                provider = self.providers[provider_type]
                
                logger.info(f"Attempting exploit generation with {provider_type.value}")
                
                response = await provider.generate_exploit(request)
                response.provider_used = provider_type
                response.execution_time = (datetime.now() - start_time).total_seconds()
                
                # Update usage stats
                self._update_usage_stats(provider_type, True, response.execution_time)
                
                return response
                
            except Exception as e:
                logger.error(f"Exploit generation failed with {provider_type.value}: {e}")
                continue
        
        # All providers failed
        execution_time = (datetime.now() - start_time).total_seconds()
        self._update_usage_stats(None, False, execution_time)
        
        return ExploitResponse(
            success=False,
            exploit_code="",
            explanation="All AI providers failed to generate exploit",
            confidence_score=0.0,
            provider_used=AIProvider.OPENAI,  # Default
            execution_time=execution_time,
            errors=["All configured AI providers failed"]
        )

    async def analyze_vulnerabilities(self, request: AnalysisRequest) -> AnalysisResponse:
        """Analyze vulnerabilities using AI with caching"""
        start_time = datetime.now()
        
        # Check cache first
        cache_key_data = {
            'scan_results': request.scan_results,
            'analysis_type': request.analysis_type,
            'include_remediation': request.include_remediation,
            'include_risk_scoring': request.include_risk_scoring
        }
        
        cached_response = _ai_response_cache.get(cache_key_data)
        if cached_response is not None:
            logger.info("Cache hit for vulnerability analysis")
            # Update execution time to reflect cache hit
            cached_response.execution_time = (datetime.now() - start_time).total_seconds()
            return cached_response
        
        logger.info("Cache miss for vulnerability analysis")
        
        # Use first available provider for analysis
        provider_order = self._get_provider_order()
        
        for provider_type in provider_order:
            if provider_type not in self.providers:
                continue
            
            try:
                provider = self.providers[provider_type]
                
                logger.info(f"Attempting vulnerability analysis with {provider_type.value}")
                
                response = await provider.analyze_vulnerabilities(request)
                response.provider_used = provider_type
                response.execution_time = (datetime.now() - start_time).total_seconds()
                
                # Cache successful response
                if response.success:
                    _ai_response_cache.put(cache_key_data, response)
                
                # Update usage stats
                self._update_usage_stats(provider_type, True, response.execution_time)
                
                return response
                
            except Exception as e:
                logger.error(f"Analysis failed with {provider_type.value}: {e}")
                continue
        
        # All providers failed
        execution_time = (datetime.now() - start_time).total_seconds()
        self._update_usage_stats(None, False, execution_time)
        
        return AnalysisResponse(
            success=False,
            executive_summary="Failed to analyze vulnerabilities",
            risk_score=0.0,
            severity_assessment="unknown",
            remediation_steps=[],
            exploit_likelihood=0.0,
            business_impact="unknown",
            findings=[],
            recommendations=[],
            provider_used=AIProvider.OPENAI,  # Default
            execution_time=execution_time,
            errors=["All configured AI providers failed"]
        )

    def _get_provider_order(self, preferred: Optional[AIProvider] = None) -> List[AIProvider]:
        """Get provider order for fallback"""
        if preferred and preferred in self.providers:
            order = [preferred]
            # Add others as fallback
            for provider in [AIProvider.OPENAI, AIProvider.DEEPSEEK, AIProvider.ANTHROPIC]:
                if provider != preferred and provider in self.providers:
                    order.append(provider)
            return order
        
        # Default order: OpenAI (primary) -> DeepSeek -> Anthropic
        return [p for p in [AIProvider.OPENAI, AIProvider.DEEPSEEK, AIProvider.ANTHROPIC] 
                if p in self.providers]

    def _update_usage_stats(self, provider: Optional[AIProvider], success: bool, execution_time: float):
        """Update usage statistics"""
        self.usage_stats["requests_made"] += 1
        
        if success:
            self.usage_stats["successful_requests"] += 1
        else:
            self.usage_stats["failed_requests"] += 1
        
        if provider:
            if provider.value not in self.usage_stats["provider_usage"]:
                self.usage_stats["provider_usage"][provider.value] = {"requests": 0, "successes": 0}
            
            self.usage_stats["provider_usage"][provider.value]["requests"] += 1
            if success:
                self.usage_stats["provider_usage"][provider.value]["successes"] += 1
        
        # Update average response time
        total_requests = self.usage_stats["requests_made"]
        current_avg = self.usage_stats["average_response_time"]
        self.usage_stats["average_response_time"] = ((current_avg * (total_requests - 1)) + execution_time) / total_requests

    def get_usage_stats(self) -> Dict[str, Any]:
        """Get usage statistics"""
        return self.usage_stats.copy()

    def get_available_providers(self) -> List[str]:
        """Get list of available providers"""
        return [p.value for p in self.providers.keys()]
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """Get AI response cache statistics"""
        return _ai_response_cache.stats()
    
    def clear_cache(self) -> None:
        """Clear AI response cache"""
        _ai_response_cache.clear()
        logger.info("AI response cache cleared")


class BaseAIProvider:
    """Base class for AI providers"""
    
    def __init__(self, api_key: str, manager=None):
        self.api_key = api_key
        self.manager = manager
    
    async def generate_exploit(self, request: ExploitRequest) -> ExploitResponse:
        """Generate exploit code"""
        raise NotImplementedError
    
    async def analyze_vulnerabilities(self, request: AnalysisRequest) -> AnalysisResponse:
        """Analyze vulnerabilities"""
        raise NotImplementedError


class OpenAIProvider(BaseAIProvider):
    """OpenAI provider implementation"""
    
    def __init__(self, api_key: str, model: str = "gpt-4o", manager=None):
        super().__init__(api_key, manager)
        self.client = openai.AsyncOpenAI(api_key=api_key)
        self.model = model
    
    async def generate_exploit(self, request: ExploitRequest) -> ExploitResponse:
        """Generate exploit using OpenAI"""
        try:
            prompt = self._build_exploit_prompt(request)
            
            response = await self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": self.manager._get_system_prompt("exploit") if self.manager else "You are a cybersecurity expert."},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.7,
                max_tokens=2500
            )
            
            content = response.choices[0].message.content
            parsed_response = self._parse_exploit_response(content)
            
            return ExploitResponse(
                success=True,
                exploit_code=parsed_response.get("exploit_code", ""),
                explanation=parsed_response.get("explanation", ""),
                confidence_score=parsed_response.get("confidence_score", 0.8),
                provider_used=AIProvider.OPENAI,
                execution_time=0.0,  # Will be set by manager
                payload_variants=parsed_response.get("payload_variants", []),
                preconditions=parsed_response.get("preconditions", []),
                post_exploitation=parsed_response.get("post_exploitation", []),
                risk_assessment=parsed_response.get("risk_assessment", {})
            )
            
        except Exception as e:
            logger.error(f"OpenAI exploit generation failed: {e}")
            return ExploitResponse(
                success=False,
                exploit_code="",
                explanation="",
                confidence_score=0.0,
                provider_used=AIProvider.OPENAI,
                execution_time=0.0,
                errors=[str(e)]
            )
    
    async def analyze_vulnerabilities(self, request: AnalysisRequest) -> AnalysisResponse:
        """Analyze vulnerabilities using OpenAI"""
        try:
            prompt = self._build_analysis_prompt(request)
            
            response = await self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": "You are a cybersecurity analyst expert in vulnerability assessment and risk analysis. Provide thorough, accurate security analysis."},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.3,
                max_tokens=2000
            )
            
            content = response.choices[0].message.content
            parsed_response = self._parse_analysis_response(content)
            
            return AnalysisResponse(
                success=True,
                executive_summary=parsed_response.get("executive_summary", "Security analysis completed"),
                risk_score=parsed_response.get("risk_score", 5.0),
                findings=parsed_response.get("findings", []),
                recommendations=parsed_response.get("recommendations", []),
                severity_assessment=parsed_response.get("severity_assessment", "medium"),
                exploit_likelihood=parsed_response.get("exploit_likelihood", 0.5),
                business_impact=parsed_response.get("business_impact", "medium"),
                provider_used=AIProvider.OPENAI,
                execution_time=0.0
            )
            
        except Exception as e:
            logger.error(f"OpenAI analysis failed: {e}")
            return AnalysisResponse(
                success=False,
                executive_summary="Analysis failed",
                risk_score=0.0,
                findings=[],
                recommendations=[],
                severity_assessment="unknown",
                exploit_likelihood=0.0,
                business_impact="unknown",
                provider_used=AIProvider.OPENAI,
                execution_time=0.0,
                errors=[str(e)]
            )
    
    def _build_exploit_prompt(self, request: ExploitRequest) -> str:
        """Build prompt for exploit generation"""
        return f"""
Generate an exploit for the following vulnerability:

Vulnerability Details:
{json.dumps(request.vulnerability_data, indent=2)}

Target Information:
{json.dumps(request.target_info, indent=2)}

Exploit Type: {request.exploit_type.value}
Difficulty Level: {request.difficulty_level}
Constraints: {json.dumps(request.custom_constraints, indent=2)}

Please provide a structured response with:
1. Exploit code (commented)
2. Detailed explanation
3. Payload variants
4. Preconditions
5. Post-exploitation steps
6. Risk assessment
7. Confidence score (0.0-1.0)

Format as JSON for easy parsing.
"""
    
    def _build_analysis_prompt(self, request: AnalysisRequest) -> str:
        """Build prompt for vulnerability analysis"""
        return f"""
Analyze the following security scan results:

Scan Results:
{json.dumps(request.scan_results, indent=2)}

Analysis Type: {request.analysis_type}
Include Remediation: {request.include_remediation}
Include Risk Scoring: {request.include_risk_scoring}
Context: {json.dumps(request.custom_context, indent=2)}

Please provide a structured analysis with:
1. Executive summary
2. Risk score (0.0-10.0)
3. Key findings (list of vulnerability details)
4. Recommendations (list of remediation steps)
5. Severity assessment
6. Exploit likelihood (0.0-1.0)
7. Business impact assessment

Format as JSON for easy parsing.
"""
    
    def _parse_exploit_response(self, content: str) -> Dict[str, Any]:
        """Parse exploit generation response"""
        try:
            # Try to parse as JSON first
            return json.loads(content)
        except json.JSONDecodeError:
            # Fallback to text parsing
            return {
                "exploit_code": content,
                "explanation": "Generated exploit code",
                "confidence_score": 0.7,
                "payload_variants": [],
                "preconditions": [],
                "post_exploitation": [],
                "risk_assessment": {}
            }
    
    def _parse_analysis_response(self, content: str) -> Dict[str, Any]:
        """Parse analysis response"""
        try:
            return json.loads(content)
        except json.JSONDecodeError:
            return {
                "executive_summary": content,
                "risk_score": 5.0,
                "findings": [{"description": "Analysis completed", "severity": "medium"}],
                "recommendations": ["Review security configurations"],
                "severity_assessment": "medium",
                "exploit_likelihood": 0.5,
                "business_impact": "medium"
            }


class DeepSeekProvider(BaseAIProvider):
    """DeepSeek provider implementation"""
    
    def __init__(self, api_key: str, manager=None):
        super().__init__(api_key, manager)
        self.base_url = "https://api.deepseek.com/v1"
    
    async def generate_exploit(self, request: ExploitRequest) -> ExploitResponse:
        """Generate exploit using DeepSeek"""
        try:
            prompt = self._build_exploit_prompt(request)
            
            async with aiohttp.ClientSession() as session:
                headers = {
                    "Authorization": f"Bearer {self.api_key}",
                    "Content-Type": "application/json"
                }
                
                data = {
                    "model": "deepseek-chat",  # Points to DeepSeek-V3-0324 (latest)
                    "messages": [
                        {"role": "system", "content": self.manager._get_system_prompt("exploit") if self.manager else "You are a cybersecurity expert."},
                        {"role": "user", "content": prompt}
                    ],
                    "temperature": 0.7,
                    "max_tokens": 2500
                }
                
                async with session.post(f"{self.base_url}/chat/completions", 
                                      headers=headers, json=data) as response:
                    if response.status == 200:
                        result = await response.json()
                        content = result["choices"][0]["message"]["content"]
                        
                        return ExploitResponse(
                            success=True,
                            exploit_code=content,
                            explanation="Generated by DeepSeek",
                            confidence_score=0.8,
                            provider_used=AIProvider.DEEPSEEK,
                            execution_time=0.0
                        )
                    else:
                        raise Exception(f"DeepSeek API error: {response.status}")
                        
        except Exception as e:
            logger.error(f"DeepSeek exploit generation failed: {e}")
            return ExploitResponse(
                success=False,
                exploit_code="",
                explanation="",
                confidence_score=0.0,
                provider_used=AIProvider.DEEPSEEK,
                execution_time=0.0,
                errors=[str(e)]
            )
    
    async def analyze_vulnerabilities(self, request: AnalysisRequest) -> AnalysisResponse:
        """Analyze vulnerabilities using DeepSeek"""
        # Similar implementation to OpenAI but using DeepSeek API
        return AnalysisResponse(
            success=False,
            executive_summary="DeepSeek analysis not implemented yet",
            risk_score=0.0,
            severity_assessment="unknown",
            remediation_steps=[],
            exploit_likelihood=0.0,
            business_impact="unknown",
            findings=[],
            recommendations=[],
            provider_used=AIProvider.DEEPSEEK,
            execution_time=0.0,
            errors=["Not implemented"]
        )
    
    def _build_exploit_prompt(self, request: ExploitRequest) -> str:
        """Build DeepSeek-specific prompt"""
        return f"Generate exploit code for {request.exploit_type.value} vulnerability: {request.vulnerability_data}"


class AnthropicProvider(BaseAIProvider):
    """Anthropic Claude provider implementation"""
    
    def __init__(self, api_key: str, model: str = "claude-3-5-sonnet-latest", manager=None):
        super().__init__(api_key, manager)
        self.client = AsyncAnthropic(api_key=api_key)
        self.model = model
    
    async def generate_exploit(self, request: ExploitRequest) -> ExploitResponse:
        """Generate exploit using Anthropic Claude"""
        # Placeholder implementation - Claude has restrictions on exploit generation
        return ExploitResponse(
            success=False,
            exploit_code="",
            explanation="Anthropic Claude has restrictions on exploit generation",
            confidence_score=0.0,
            provider_used=AIProvider.ANTHROPIC,
            execution_time=0.0,
            errors=["Provider policy restrictions"]
        )
    
    async def analyze_vulnerabilities(self, request: AnalysisRequest) -> AnalysisResponse:
        """Analyze vulnerabilities using Anthropic Claude"""
        try:
            prompt = f"Analyze these security vulnerabilities: {json.dumps(request.scan_results, indent=2)}"
            
            response = await self.client.messages.create(
                model=self.model,
                max_tokens=2000,
                messages=[{"role": "user", "content": prompt}]
            )
            
            return AnalysisResponse(
                success=True,
                executive_summary=response.content[0].text,
                risk_score=5.0,
                severity_assessment="medium",
                remediation_steps=[],
                exploit_likelihood=0.5,
                business_impact="medium",
                findings=[],
                recommendations=[],
                provider_used=AIProvider.ANTHROPIC,
                execution_time=0.0
            )
            
        except Exception as e:
            logger.error(f"Anthropic analysis failed: {e}")
            return AnalysisResponse(
                success=False,
                analysis="Analysis failed",
                risk_score=0.0,
                severity_assessment="unknown",
                remediation_steps=[],
                exploit_likelihood=0.0,
                business_impact="unknown",
                provider_used=AIProvider.ANTHROPIC,
                execution_time=0.0,
                errors=[str(e)]
            )