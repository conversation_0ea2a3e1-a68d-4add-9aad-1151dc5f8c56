#!/usr/bin/env python3
"""
RBAC Middleware and Decorators
Easy integration of RBAC system with application components,
providing decorators and middleware for access control
"""

import asyncio
import functools
import logging
from typing import Any, Callable, Dict, List, Optional, Union
from datetime import datetime

from .rbac_system import RBACManager, PermissionType, get_rbac_manager

logger = logging.getLogger(__name__)

class RBACContext:
    """Context object for RBAC operations"""
    
    def __init__(self, user_id: str, username: str, organization_id: Optional[str] = None,
                 roles: Optional[List[str]] = None, ip_address: Optional[str] = None):
        self.user_id = user_id
        self.username = username
        self.organization_id = organization_id
        self.roles = roles or []
        self.ip_address = ip_address
        self.authenticated_at = datetime.now()

class RBACMiddleware:
    """RBAC middleware for request processing"""
    
    def __init__(self, rbac_manager: Optional[RBACManager] = None):
        self.rbac_manager = rbac_manager or get_rbac_manager()
    
    async def authenticate_request(self, token: str, ip_address: Optional[str] = None) -> Optional[RBACContext]:
        """Authenticate request and return RBAC context"""
        try:
            payload = self.rbac_manager.verify_token(token)
            if not payload:
                return None
            
            return RBACContext(
                user_id=payload['user_id'],
                username=payload['username'],
                organization_id=payload.get('organization_id'),
                roles=payload.get('roles', []),
                ip_address=ip_address
            )
            
        except Exception as e:
            logger.error(f"Authentication failed: {e}")
            return None
    
    async def check_permission(self, context: RBACContext, permission: PermissionType,
                             resource: str = "*", additional_context: Optional[Dict[str, Any]] = None) -> bool:
        """Check permission for authenticated user"""
        try:
            return await self.rbac_manager.check_permission(
                context.user_id, permission, resource, additional_context
            )
        except Exception as e:
            logger.error(f"Permission check failed: {e}")
            return False

# Global middleware instance
rbac_middleware = RBACMiddleware()

def require_permission(permission: PermissionType, resource: str = "*"):
    """Decorator to require specific permission for function access"""
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def async_wrapper(*args, **kwargs):
            # Extract RBAC context from kwargs
            context = kwargs.get('rbac_context') or kwargs.get('context')
            
            if not context or not isinstance(context, RBACContext):
                raise PermissionError("No valid RBAC context provided")
            
            # Check permission
            has_permission = await rbac_middleware.check_permission(
                context, permission, resource
            )
            
            if not has_permission:
                raise PermissionError(f"Access denied: {permission.value} on {resource}")
            
            return await func(*args, **kwargs)
        
        @functools.wraps(func)
        def sync_wrapper(*args, **kwargs):
            # For synchronous functions, create async wrapper
            return asyncio.run(async_wrapper(*args, **kwargs))
        
        # Return appropriate wrapper based on function type
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
    
    return decorator

def require_any_permission(*permissions: PermissionType, resource: str = "*"):
    """Decorator to require any of the specified permissions"""
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def async_wrapper(*args, **kwargs):
            context = kwargs.get('rbac_context') or kwargs.get('context')
            
            if not context or not isinstance(context, RBACContext):
                raise PermissionError("No valid RBAC context provided")
            
            # Check if user has any of the required permissions
            has_permission = False
            for permission in permissions:
                if await rbac_middleware.check_permission(context, permission, resource):
                    has_permission = True
                    break
            
            if not has_permission:
                perm_names = [p.value for p in permissions]
                raise PermissionError(f"Access denied: requires one of {perm_names} on {resource}")
            
            return await func(*args, **kwargs)
        
        @functools.wraps(func)
        def sync_wrapper(*args, **kwargs):
            return asyncio.run(async_wrapper(*args, **kwargs))
        
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
    
    return decorator

def require_role(role_name: str):
    """Decorator to require specific role"""
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def async_wrapper(*args, **kwargs):
            context = kwargs.get('rbac_context') or kwargs.get('context')
            
            if not context or not isinstance(context, RBACContext):
                raise PermissionError("No valid RBAC context provided")
            
            if role_name not in context.roles:
                raise PermissionError(f"Access denied: requires role '{role_name}'")
            
            return await func(*args, **kwargs)
        
        @functools.wraps(func)
        def sync_wrapper(*args, **kwargs):
            return asyncio.run(async_wrapper(*args, **kwargs))
        
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
    
    return decorator

def require_organization(org_id: Optional[str] = None):
    """Decorator to require user belongs to specific organization"""
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def async_wrapper(*args, **kwargs):
            context = kwargs.get('rbac_context') or kwargs.get('context')
            
            if not context or not isinstance(context, RBACContext):
                raise PermissionError("No valid RBAC context provided")
            
            # If org_id is None, just check that user has an organization
            if org_id is None:
                if not context.organization_id:
                    raise PermissionError("Access denied: user must belong to an organization")
            else:
                if context.organization_id != org_id:
                    raise PermissionError(f"Access denied: requires organization '{org_id}'")
            
            return await func(*args, **kwargs)
        
        @functools.wraps(func)
        def sync_wrapper(*args, **kwargs):
            return asyncio.run(async_wrapper(*args, **kwargs))
        
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
    
    return decorator

class RBACService:
    """High-level RBAC service for application integration"""
    
    def __init__(self, rbac_manager: Optional[RBACManager] = None):
        self.rbac_manager = rbac_manager or get_rbac_manager()
        self.middleware = RBACMiddleware(self.rbac_manager)
    
    async def create_admin_user(self, username: str, email: str, password: str,
                              organization_id: Optional[str] = None) -> str:
        """Create admin user with super admin role"""
        user = await self.rbac_manager.create_user(
            username, email, password, organization_id, ['super_admin']
        )
        return user.user_id
    
    async def create_analyst_user(self, username: str, email: str, password: str,
                                organization_id: Optional[str] = None) -> str:
        """Create security analyst user"""
        user = await self.rbac_manager.create_user(
            username, email, password, organization_id, ['security_analyst']
        )
        return user.user_id
    
    async def create_pentester_user(self, username: str, email: str, password: str,
                                  organization_id: Optional[str] = None) -> str:
        """Create penetration tester user"""
        user = await self.rbac_manager.create_user(
            username, email, password, organization_id, ['penetration_tester']
        )
        return user.user_id
    
    async def create_viewer_user(self, username: str, email: str, password: str,
                               organization_id: Optional[str] = None) -> str:
        """Create viewer user with read-only access"""
        user = await self.rbac_manager.create_user(
            username, email, password, organization_id, ['viewer']
        )
        return user.user_id
    
    async def authenticate_and_authorize(self, token: str, required_permission: PermissionType,
                                       resource: str = "*", ip_address: Optional[str] = None) -> Optional[RBACContext]:
        """Authenticate token and check permission in one call"""
        context = await self.middleware.authenticate_request(token, ip_address)
        if not context:
            return None
        
        has_permission = await self.middleware.check_permission(
            context, required_permission, resource
        )
        
        if not has_permission:
            return None
        
        return context
    
    async def get_user_dashboard_data(self, context: RBACContext) -> Dict[str, Any]:
        """Get dashboard data based on user permissions"""
        dashboard_data = {
            'user_info': {
                'username': context.username,
                'organization_id': context.organization_id,
                'roles': context.roles
            },
            'permissions': [],
            'allowed_actions': []
        }
        
        # Get user permissions
        permissions = self.rbac_manager.get_user_permissions(context.user_id)
        dashboard_data['permissions'] = permissions
        
        # Check specific action permissions
        actions_to_check = [
            ('create_campaign', PermissionType.CAMPAIGN_CREATE),
            ('execute_scan', PermissionType.SCAN_EXECUTE),
            ('view_reports', PermissionType.REPORT_READ),
            ('manage_users', PermissionType.USER_CREATE),
            ('system_admin', PermissionType.SYSTEM_ADMIN),
            ('use_ai', PermissionType.AI_USE),
            ('threat_intel', PermissionType.INTEL_READ)
        ]
        
        for action_name, permission_type in actions_to_check:
            has_permission = await self.middleware.check_permission(
                context, permission_type
            )
            if has_permission:
                dashboard_data['allowed_actions'].append(action_name)
        
        return dashboard_data
    
    def create_context_from_token(self, token: str, ip_address: Optional[str] = None) -> Optional[RBACContext]:
        """Create RBAC context from JWT token (synchronous)"""
        try:
            payload = self.rbac_manager.verify_token(token)
            if not payload:
                return None
            
            return RBACContext(
                user_id=payload['user_id'],
                username=payload['username'],
                organization_id=payload.get('organization_id'),
                roles=payload.get('roles', []),
                ip_address=ip_address
            )
        except Exception as e:
            logger.error(f"Failed to create context from token: {e}")
            return None

# Global RBAC service instance
rbac_service = RBACService()

# Convenience decorators for common permissions
require_admin = require_permission(PermissionType.SYSTEM_ADMIN)
require_scan_access = require_permission(PermissionType.SCAN_READ)
require_scan_execute = require_permission(PermissionType.SCAN_EXECUTE)
require_campaign_access = require_permission(PermissionType.CAMPAIGN_READ)
require_campaign_create = require_permission(PermissionType.CAMPAIGN_CREATE)
require_report_access = require_permission(PermissionType.REPORT_READ)
require_ai_access = require_permission(PermissionType.AI_USE)
require_intel_access = require_permission(PermissionType.INTEL_READ)

# Role-based decorators
require_super_admin = require_role('super_admin')
require_security_analyst = require_role('security_analyst')
require_penetration_tester = require_role('penetration_tester')

def with_rbac_context(func: Callable) -> Callable:
    """Decorator that automatically injects RBAC context from token"""
    @functools.wraps(func)
    async def async_wrapper(*args, **kwargs):
        # Look for token in kwargs
        token = kwargs.get('token') or kwargs.get('auth_token')
        ip_address = kwargs.get('ip_address')
        
        if not token:
            raise ValueError("No authentication token provided")
        
        # Create context
        context = rbac_service.create_context_from_token(token, ip_address)
        if not context:
            raise PermissionError("Invalid or expired token")
        
        # Inject context
        kwargs['rbac_context'] = context
        
        return await func(*args, **kwargs)
    
    @functools.wraps(func)
    def sync_wrapper(*args, **kwargs):
        return asyncio.run(async_wrapper(*args, **kwargs))
    
    if asyncio.iscoroutinefunction(func):
        return async_wrapper
    else:
        return sync_wrapper

def audit_action(action_description: str):
    """Decorator to automatically audit function calls"""
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def async_wrapper(*args, **kwargs):
            context = kwargs.get('rbac_context')
            
            if context:
                # Log the action (simplified - would integrate with audit system)
                logger.info(f"User {context.username} performed: {action_description}")
            
            return await func(*args, **kwargs)
        
        @functools.wraps(func)
        def sync_wrapper(*args, **kwargs):
            return asyncio.run(async_wrapper(*args, **kwargs))
        
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
    
    return decorator

# Example usage functions demonstrating the RBAC system
@require_admin
@audit_action("Created new user")
async def create_user_example(username: str, email: str, password: str, rbac_context: RBACContext):
    """Example function showing admin-only user creation"""
    # This function can only be called by users with SYSTEM_ADMIN permission
    return f"User {username} created by {rbac_context.username}"

@require_scan_execute
@audit_action("Executed security scan")
async def execute_scan_example(target: str, scan_type: str, rbac_context: RBACContext):
    """Example function showing scan execution with permission check"""
    # This function can only be called by users with SCAN_EXECUTE permission
    return f"Scan of {target} ({scan_type}) executed by {rbac_context.username}"

@require_any_permission(PermissionType.REPORT_READ, PermissionType.REPORT_ADMIN)
@audit_action("Accessed security report")
async def view_report_example(report_id: str, rbac_context: RBACContext):
    """Example function showing report access with multiple valid permissions"""
    return f"Report {report_id} accessed by {rbac_context.username}"

@with_rbac_context
@require_organization()
async def organization_specific_action(data: str, token: str, rbac_context: RBACContext):
    """Example function that requires organization membership and auto-injects context"""
    return f"Organization {rbac_context.organization_id} action by {rbac_context.username}"