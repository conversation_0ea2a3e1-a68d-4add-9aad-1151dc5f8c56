"""
Threat Model Generator for NexusScan Desktop Application
AI-powered threat modeling and attack scenario generation.
"""

import json
import asyncio
import logging
from datetime import datetime
from typing import Dict, List, Optional, Callable, Any
from dataclasses import dataclass

from security.tools.tool_registry import (
    ToolMetadata, ToolCategory, ToolCapabilities,
    ScanOptions, ScanResult, ToolStatus, register_tool
)
from security.tools.base_scanner import BaseScanner
from ai.services import AIServiceManager, AIProvider

logger = logging.getLogger(__name__)


@dataclass
class ThreatModelOptions(ScanOptions):
    """Threat modeling options"""
    analysis_depth: str = "comprehensive"  # basic, comprehensive, deep
    include_mitre_attack: bool = True
    include_kill_chain: bool = True
    include_threat_actors: bool = True
    include_attack_scenarios: bool = True
    business_context: str = ""
    asset_criticality: str = "medium"  # low, medium, high, critical
    compliance_frameworks: List[str] = None
    
    def __post_init__(self):
        super().__post_init__()
        if self.compliance_frameworks is None:
            self.compliance_frameworks = []


@dataclass
class ThreatActor:
    """Threat actor profile"""
    name: str
    type: str
    sophistication: str
    motivation: str
    capabilities: List[str]
    ttps: List[str]
    likelihood: str


@dataclass
class ThreatScenario:
    """Threat scenario"""
    id: str
    name: str
    description: str
    threat_actors: List[str]
    attack_vectors: List[str]
    impact: str
    likelihood: str
    risk_rating: str
    mitre_techniques: List[str]
    countermeasures: List[str]


class ThreatModelGenerator(BaseScanner):
    """AI-powered threat modeling and attack scenario generation"""
    
    def __init__(self):
        super().__init__()
        self.tool_name = "threat_model_generator"
        # Scan types moved to default_options
        self.ai_manager = None
        
    def get_metadata(self) -> ToolMetadata:
        """Get tool metadata"""
        return ToolMetadata(
            name="threat_model_generator",
            display_name="Threat Model Generator",
            category=ToolCategory.AI_ANALYZER,
            description="AI-powered threat modeling and attack scenario generation",
            version="1.0.0",
            author="NexusScan AI Team",
            capabilities=ToolCapabilities(
                output_formats=["json", "html", "pdf"],
                supports_async=True,
                supports_progress=True,
                requires_root=False,
                network_access_required=True
            )
        )
    
    def is_available(self) -> bool:
        """Check if AI services are available"""
        try:
            from ai.services import AIServiceManager
            return True
        except ImportError:
            return False
    
    async def _get_ai_manager(self) -> AIServiceManager:
        """Get AI service manager"""
        if self.ai_manager is None:
            from ai.services import AIServiceManager
            self.ai_manager = AIServiceManager()
        return self.ai_manager
    
    async def scan(self, 
                   target: str, 
                   options: Optional[ThreatModelOptions] = None,
                   progress_callback: Optional[Callable] = None) -> ScanResult:
        """Generate threat model"""
        if options is None:
            options = ThreatModelOptions(target=target)
        
        scan_id = f"threat_model_{target.replace('://', '_').replace('/', '_')}"
    
    def check_native_availability(self) -> bool:
        """Check if native tool is available"""
        return self.is_available()
    
    async def execute_native_scan(self, options: ScanOptions,
                                 progress_callback: Optional[Callable[[float, str], None]] = None) -> ScanResult:
        """Execute native scan"""
        if not isinstance(options, ThreatModelOptions):
            options = ThreatModelOptions(target=options.target)
        
        return await self.scan(options.target, options, progress_callback)
        start_time = datetime.now()
        
        logger.info(f"Starting threat modeling: {scan_id}")
        
        if progress_callback:
            await progress_callback(0.1, "Initializing", "Preparing threat model generation")
        
        try:
            ai_manager = await self._get_ai_manager()
            
            # Phase 1: Asset identification and classification
            if progress_callback:
                await progress_callback(0.2, "Asset Analysis", "Identifying and classifying assets")
            
            asset_analysis = await self._analyze_assets(target, options, ai_manager)
            
            # Phase 2: Threat actor identification
            if progress_callback:
                await progress_callback(0.3, "Threat Actors", "Identifying relevant threat actors")
            
            threat_actors = await self._identify_threat_actors(target, asset_analysis, ai_manager)
            
            # Phase 3: Attack vector analysis
            if progress_callback:
                await progress_callback(0.5, "Attack Vectors", "Analyzing potential attack vectors")
            
            attack_vectors = await self._analyze_attack_vectors(target, asset_analysis, threat_actors, ai_manager)
            
            # Phase 4: Threat scenario generation
            if progress_callback:
                await progress_callback(0.7, "Threat Scenarios", "Generating threat scenarios")
            
            threat_scenarios = await self._generate_threat_scenarios(target, asset_analysis, threat_actors, attack_vectors, ai_manager)
            
            # Phase 5: Risk assessment and prioritization
            if progress_callback:
                await progress_callback(0.9, "Risk Assessment", "Assessing and prioritizing risks")
            
            risk_assessment = await self._assess_risks(target, threat_scenarios, options, ai_manager)
            
            # Phase 6: Generate comprehensive threat model
            if progress_callback:
                await progress_callback(0.95, "Report Generation", "Generating threat model report")
            
            threat_model = await self._generate_threat_model(
                target, asset_analysis, threat_actors, attack_vectors, 
                threat_scenarios, risk_assessment, options, ai_manager
            )
            
            if progress_callback:
                await progress_callback(1.0, "Complete", "Threat model generation completed")
            
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            return ScanResult(
                scan_id=scan_id,
                tool_name="threat_model_generator",
                target=target,
                status=ToolStatus.COMPLETED,
                start_time=start_time,
                end_time=end_time,
                duration_seconds=duration,
                command_executed=f"threat_model_generator --target {target} --depth {options.analysis_depth}",
                exit_code=0,
                raw_output=json.dumps(threat_model, indent=2),
                error_output="",
                parsed_results=threat_model,
                vulnerabilities=threat_model.get("vulnerabilities", []),
                summary=threat_model.get("summary", {}),
                metadata={
                    "analysis_depth": options.analysis_depth,
                    "ai_analysis_enabled": True,
                    "threat_actors_identified": len(threat_actors),
                    "threat_scenarios_generated": len(threat_scenarios),
                    "overall_risk_rating": risk_assessment.get("overall_risk_rating", "medium")
                }
            )
            
        except Exception as e:
            logger.error(f"Threat model generation failed: {e}")
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            return ScanResult(
                scan_id=scan_id,
                tool_name="threat_model_generator",
                target=target,
                status=ToolStatus.FAILED,
                start_time=start_time,
                end_time=end_time,
                duration_seconds=duration,
                command_executed="",
                exit_code=1,
                raw_output="",
                error_output=str(e),
                parsed_results={},
                vulnerabilities=[],
                summary={"error": str(e)},
                metadata={}
            )
    
    async def _analyze_assets(self, target: str, options: ThreatModelOptions, ai_manager: AIServiceManager) -> Dict[str, Any]:
        """Analyze and classify assets"""
        prompt = f"""
        Analyze the following target and identify key assets for threat modeling:
        
        Target: {target}
        Business Context: {options.business_context}
        Asset Criticality: {options.asset_criticality}
        
        Please identify:
        1. Primary assets (data, systems, processes)
        2. Supporting assets (infrastructure, personnel, premises)
        3. Asset criticality levels
        4. Dependencies and relationships
        5. Business impact of compromise
        
        Provide a comprehensive asset inventory with classification.
        """
        
        try:
            ai_response = await ai_manager.generate_analysis(
                prompt=prompt,
                analysis_type="asset_analysis",
                provider=AIProvider.OPENAI
            )
            
            if ai_response and "analysis" in ai_response:
                return ai_response["analysis"]
            else:
                return self._generate_default_asset_analysis(target, options)
        
        except Exception as e:
            logger.warning(f"AI asset analysis failed: {e}")
            return self._generate_default_asset_analysis(target, options)
    
    def _generate_default_asset_analysis(self, target: str, options: ThreatModelOptions) -> Dict[str, Any]:
        """Generate default asset analysis"""
        return {
            "primary_assets": [
                {
                    "name": "Web Application",
                    "type": "application",
                    "criticality": options.asset_criticality,
                    "location": target,
                    "description": "Main web application"
                },
                {
                    "name": "Customer Data",
                    "type": "data",
                    "criticality": "high",
                    "location": "Database",
                    "description": "Personal and sensitive customer information"
                }
            ],
            "supporting_assets": [
                {
                    "name": "Database Server",
                    "type": "infrastructure",
                    "criticality": "high",
                    "description": "Backend database system"
                },
                {
                    "name": "Web Server",
                    "type": "infrastructure",
                    "criticality": "medium",
                    "description": "Frontend web server"
                }
            ],
            "asset_dependencies": {
                "web_application": ["database_server", "web_server"],
                "customer_data": ["database_server"]
            }
        }
    
    async def _identify_threat_actors(self, target: str, asset_analysis: Dict[str, Any], ai_manager: AIServiceManager) -> List[Dict[str, Any]]:
        """Identify relevant threat actors"""
        prompt = f"""
        Based on the following target and asset analysis, identify relevant threat actors:
        
        Target: {target}
        Assets: {asset_analysis}
        
        For each threat actor, provide:
        1. Actor type (insider, cybercriminal, nation-state, hacktivist, etc.)
        2. Motivation (financial, espionage, disruption, etc.)
        3. Sophistication level (low, medium, high, advanced)
        4. Typical capabilities and tools
        5. Likelihood of targeting this organization
        6. Common TTPs (Tactics, Techniques, Procedures)
        
        Focus on realistic threat actors for this type of target.
        """
        
        try:
            ai_response = await ai_manager.generate_analysis(
                prompt=prompt,
                analysis_type="threat_actor_identification",
                provider=AIProvider.OPENAI
            )
            
            if ai_response and "analysis" in ai_response:
                return ai_response["analysis"].get("threat_actors", [])
            else:
                return self._generate_default_threat_actors()
        
        except Exception as e:
            logger.warning(f"AI threat actor identification failed: {e}")
            return self._generate_default_threat_actors()
    
    def _generate_default_threat_actors(self) -> List[Dict[str, Any]]:
        """Generate default threat actors"""
        return [
            {
                "name": "Opportunistic Cybercriminals",
                "type": "cybercriminal",
                "sophistication": "medium",
                "motivation": "financial",
                "capabilities": ["web application attacks", "social engineering", "malware"],
                "ttps": ["SQL injection", "phishing", "credential stuffing"],
                "likelihood": "high"
            },
            {
                "name": "Insider Threat",
                "type": "insider",
                "sophistication": "low",
                "motivation": "financial/revenge",
                "capabilities": ["legitimate access", "data exfiltration"],
                "ttps": ["data theft", "privilege abuse", "system sabotage"],
                "likelihood": "medium"
            },
            {
                "name": "Script Kiddies",
                "type": "script_kiddie",
                "sophistication": "low",
                "motivation": "recognition",
                "capabilities": ["automated tools", "known exploits"],
                "ttps": ["vulnerability scanning", "exploit kits", "defacement"],
                "likelihood": "medium"
            }
        ]
    
    async def _analyze_attack_vectors(self, target: str, asset_analysis: Dict[str, Any], 
                                    threat_actors: List[Dict[str, Any]], ai_manager: AIServiceManager) -> List[Dict[str, Any]]:
        """Analyze potential attack vectors"""
        prompt = f"""
        Based on the target, assets, and threat actors, identify potential attack vectors:
        
        Target: {target}
        Assets: {asset_analysis}
        Threat Actors: {threat_actors}
        
        For each attack vector, provide:
        1. Attack vector name and description
        2. Entry points and methods
        3. Required capabilities and tools
        4. Potential impact
        5. Likelihood of success
        6. MITRE ATT&CK techniques involved
        7. Detection difficulty
        
        Focus on realistic attack vectors for the identified threat actors.
        """
        
        try:
            ai_response = await ai_manager.generate_analysis(
                prompt=prompt,
                analysis_type="attack_vector_analysis",
                provider=AIProvider.OPENAI
            )
            
            if ai_response and "analysis" in ai_response:
                return ai_response["analysis"].get("attack_vectors", [])
            else:
                return self._generate_default_attack_vectors()
        
        except Exception as e:
            logger.warning(f"AI attack vector analysis failed: {e}")
            return self._generate_default_attack_vectors()
    
    def _generate_default_attack_vectors(self) -> List[Dict[str, Any]]:
        """Generate default attack vectors"""
        return [
            {
                "name": "Web Application Exploitation",
                "description": "Exploiting web application vulnerabilities",
                "entry_points": ["web interface", "API endpoints"],
                "methods": ["SQL injection", "XSS", "CSRF"],
                "impact": "high",
                "likelihood": "medium",
                "mitre_techniques": ["T1190", "T1059"],
                "detection_difficulty": "medium"
            },
            {
                "name": "Credential-based Attacks",
                "description": "Attacks targeting user credentials",
                "entry_points": ["login forms", "authentication APIs"],
                "methods": ["brute force", "credential stuffing", "phishing"],
                "impact": "high",
                "likelihood": "high",
                "mitre_techniques": ["T1110", "T1566"],
                "detection_difficulty": "low"
            },
            {
                "name": "Social Engineering",
                "description": "Human-focused attacks",
                "entry_points": ["employees", "support staff"],
                "methods": ["phishing", "pretexting", "baiting"],
                "impact": "medium",
                "likelihood": "medium",
                "mitre_techniques": ["T1566", "T1204"],
                "detection_difficulty": "high"
            }
        ]
    
    async def _generate_threat_scenarios(self, target: str, asset_analysis: Dict[str, Any], 
                                       threat_actors: List[Dict[str, Any]], attack_vectors: List[Dict[str, Any]], 
                                       ai_manager: AIServiceManager) -> List[Dict[str, Any]]:
        """Generate threat scenarios"""
        prompt = f"""
        Create realistic threat scenarios combining the threat actors and attack vectors:
        
        Target: {target}
        Assets: {asset_analysis}
        Threat Actors: {threat_actors}
        Attack Vectors: {attack_vectors}
        
        For each scenario, provide:
        1. Scenario name and description
        2. Threat actor involved
        3. Attack sequence and steps
        4. Targeted assets
        5. Potential impact (CIA triad)
        6. Business impact
        7. Likelihood assessment
        8. Risk rating
        9. Kill chain mapping
        10. Recommended countermeasures
        
        Generate 5-7 realistic scenarios covering different risk levels.
        """
        
        try:
            ai_response = await ai_manager.generate_analysis(
                prompt=prompt,
                analysis_type="threat_scenario_generation",
                provider=AIProvider.OPENAI
            )
            
            if ai_response and "analysis" in ai_response:
                return ai_response["analysis"].get("threat_scenarios", [])
            else:
                return self._generate_default_threat_scenarios()
        
        except Exception as e:
            logger.warning(f"AI threat scenario generation failed: {e}")
            return self._generate_default_threat_scenarios()
    
    def _generate_default_threat_scenarios(self) -> List[Dict[str, Any]]:
        """Generate default threat scenarios"""
        return [
            {
                "id": "scenario_1",
                "name": "Data Breach via SQL Injection",
                "description": "Cybercriminal exploits SQL injection vulnerability to access database",
                "threat_actor": "Opportunistic Cybercriminals",
                "attack_sequence": [
                    "Reconnaissance and vulnerability scanning",
                    "SQL injection exploitation",
                    "Database access and data exfiltration",
                    "Data monetization"
                ],
                "targeted_assets": ["Customer Data", "Database Server"],
                "impact": "high",
                "likelihood": "medium",
                "risk_rating": "high",
                "countermeasures": ["Input validation", "WAF deployment", "Database monitoring"]
            },
            {
                "id": "scenario_2",
                "name": "Insider Data Theft",
                "description": "Malicious insider abuses legitimate access to steal sensitive data",
                "threat_actor": "Insider Threat",
                "attack_sequence": [
                    "Abuse of legitimate access",
                    "Data collection and consolidation",
                    "Covert data exfiltration",
                    "Data sale or misuse"
                ],
                "targeted_assets": ["Customer Data", "Business Systems"],
                "impact": "high",
                "likelihood": "low",
                "risk_rating": "medium",
                "countermeasures": ["Access controls", "Data loss prevention", "User monitoring"]
            }
        ]
    
    async def _assess_risks(self, target: str, threat_scenarios: List[Dict[str, Any]], 
                          options: ThreatModelOptions, ai_manager: AIServiceManager) -> Dict[str, Any]:
        """Assess and prioritize risks"""
        prompt = f"""
        Assess the risks from the following threat scenarios:
        
        Target: {target}
        Threat Scenarios: {threat_scenarios}
        Business Context: {options.business_context}
        Asset Criticality: {options.asset_criticality}
        
        Provide:
        1. Overall risk rating for the organization
        2. Risk prioritization matrix
        3. Key risk factors
        4. Risk treatment recommendations
        5. Residual risk assessment
        6. Compliance implications
        
        Use a quantitative approach where possible.
        """
        
        try:
            ai_response = await ai_manager.generate_analysis(
                prompt=prompt,
                analysis_type="risk_assessment",
                provider=AIProvider.OPENAI
            )
            
            if ai_response and "analysis" in ai_response:
                return ai_response["analysis"]
            else:
                return self._generate_default_risk_assessment(threat_scenarios)
        
        except Exception as e:
            logger.warning(f"AI risk assessment failed: {e}")
            return self._generate_default_risk_assessment(threat_scenarios)
    
    def _generate_default_risk_assessment(self, threat_scenarios: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Generate default risk assessment"""
        high_risk_count = len([s for s in threat_scenarios if s.get("risk_rating") == "high"])
        medium_risk_count = len([s for s in threat_scenarios if s.get("risk_rating") == "medium"])
        
        return {
            "overall_risk_rating": "medium" if high_risk_count < 2 else "high",
            "risk_factors": [
                "Web application vulnerabilities",
                "Insufficient access controls",
                "Limited monitoring capabilities"
            ],
            "risk_priorities": [
                {"scenario": "Data Breach via SQL Injection", "priority": 1},
                {"scenario": "Insider Data Theft", "priority": 2}
            ],
            "treatment_recommendations": [
                "Implement web application security controls",
                "Enhance monitoring and detection capabilities",
                "Improve access control mechanisms"
            ]
        }
    
    async def _generate_threat_model(self, target: str, asset_analysis: Dict[str, Any], 
                                   threat_actors: List[Dict[str, Any]], attack_vectors: List[Dict[str, Any]], 
                                   threat_scenarios: List[Dict[str, Any]], risk_assessment: Dict[str, Any], 
                                   options: ThreatModelOptions, ai_manager: AIServiceManager) -> Dict[str, Any]:
        """Generate comprehensive threat model"""
        
        # Generate vulnerabilities from threat scenarios
        vulnerabilities = []
        for scenario in threat_scenarios:
            if scenario.get("risk_rating") in ["high", "critical"]:
                vulnerabilities.append({
                    "id": f"threat_model_{scenario.get('id', len(vulnerabilities))}",
                    "type": "Threat Scenario",
                    "severity": scenario.get("risk_rating", "medium"),
                    "description": scenario.get("description", ""),
                    "threat_actor": scenario.get("threat_actor", "Unknown"),
                    "attack_vectors": scenario.get("attack_sequence", []),
                    "targeted_assets": scenario.get("targeted_assets", []),
                    "recommendation": f"Implement countermeasures: {', '.join(scenario.get('countermeasures', []))}"
                })
        
        threat_model = {
            "target_info": {
                "target": target,
                "business_context": options.business_context,
                "asset_criticality": options.asset_criticality,
                "analysis_depth": options.analysis_depth
            },
            "asset_analysis": asset_analysis,
            "threat_actors": threat_actors,
            "attack_vectors": attack_vectors,
            "threat_scenarios": threat_scenarios,
            "risk_assessment": risk_assessment,
            "vulnerabilities": vulnerabilities,
            "summary": {
                "total_assets": len(asset_analysis.get("primary_assets", [])) + len(asset_analysis.get("supporting_assets", [])),
                "threat_actors_identified": len(threat_actors),
                "attack_vectors_analyzed": len(attack_vectors),
                "threat_scenarios_generated": len(threat_scenarios),
                "high_risk_scenarios": len([s for s in threat_scenarios if s.get("risk_rating") == "high"]),
                "overall_risk_rating": risk_assessment.get("overall_risk_rating", "medium"),
                "analysis_timestamp": datetime.now().isoformat()
            }
        }
        
        return threat_model
    
    def get_frontend_interface_data(self) -> Dict[str, Any]:
        """Get data for frontend interface"""
        return {
            "tool_info": {
                "name": "Threat Model Generator",
                "description": "AI-powered threat modeling and attack scenario generation",
                "version": "1.0.0",
                "category": "AI Analyzer",
                "status": "available" if self.is_available() else "unavailable"
            },
            "scan_options": {
                "target": {
                    "type": "text",
                    "required": True,
                    "placeholder": "https://example.com or Company Name",
                    "validation": "url_or_text"
                },
                "analysis_depth": {
                    "type": "select",
                    "options": ["basic", "comprehensive", "deep"],
                    "default": "comprehensive",
                    "label": "Analysis depth"
                },
                "business_context": {
                    "type": "textarea",
                    "required": False,
                    "placeholder": "Describe the business context and environment",
                    "label": "Business context"
                },
                "asset_criticality": {
                    "type": "select",
                    "options": ["low", "medium", "high", "critical"],
                    "default": "medium",
                    "label": "Asset criticality level"
                },
                "include_mitre_attack": {
                    "type": "boolean",
                    "default": True,
                    "label": "Include MITRE ATT&CK mapping"
                },
                "include_threat_actors": {
                    "type": "boolean",
                    "default": True,
                    "label": "Include threat actor analysis"
                },
                "include_attack_scenarios": {
                    "type": "boolean",
                    "default": True,
                    "label": "Include attack scenario generation"
                }
            },
            "output_formats": ["json", "html", "pdf"],
            "capabilities": [
                "AI-powered threat modeling",
                "Asset identification and classification",
                "Threat actor profiling",
                "Attack vector analysis",
                "Threat scenario generation",
                "Risk assessment and prioritization",
                "MITRE ATT&CK mapping",
                "Comprehensive reporting"
            ]
        }


# Register the tool
register_tool(ThreatModelGenerator)