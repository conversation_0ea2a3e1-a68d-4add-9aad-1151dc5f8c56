/**
 * Nmap Scanner Component
 * Advanced network discovery and port scanning interface
 */
import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { MobileTabs as Tabs, MobileTabsContent as TabsContent, MobileTabsList as TabsList, MobileTabsTrigger as TabsTrigger } from '@/components/ui/mobile-tabs';
import { TouchSelect, TouchInput, TouchTextarea, TouchButton } from '@/components/ui/mobile-form';
import { MobileFormGrid, MobileFormCard, MobileFormField } from '@/components/ui/mobile-form';
import { MobileResultsGrid, MobileResultCard, MobileTable, MobileDataList, MobileActionBar } from '@/components/ui/mobile-results';
import { SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  Network,
  Play,
  Square,
  Download,
  Settings,
  Target,
  Shield,
  Zap,
  Clock,
  Terminal,
  FileText,
  AlertTriangle,
  CheckCircle,
  Copy,
  RotateCcw
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useBackendStore } from '@/stores/backend-store';
import { apiClient, ToolExecution } from '@/services/api-client';
import { toast } from 'sonner';

/**
 * Nmap scan configuration interface
 */
interface NmapConfig {
  target: string;
  scanType: 'syn' | 'tcp' | 'udp' | 'comprehensive' | 'stealth' | 'aggressive';
  portRange: string;
  timing: 0 | 1 | 2 | 3 | 4 | 5;
  detectOS: boolean;
  detectVersion: boolean;
  enableScripts: boolean;
  scriptCategories: string[];
  outputFormat: 'normal' | 'xml' | 'grepable' | 'json';
  customArgs: string;
}

/**
 * Scan preset configurations
 */
const SCAN_PRESETS: Record<string, Partial<NmapConfig>> = {
  'quick': {
    scanType: 'syn',
    portRange: '1-1000',
    timing: 4,
    detectOS: false,
    detectVersion: false,
    enableScripts: false
  },
  'comprehensive': {
    scanType: 'comprehensive',
    portRange: '1-65535',
    timing: 3,
    detectOS: true,
    detectVersion: true,
    enableScripts: true,
    scriptCategories: ['default', 'safe']
  },
  'stealth': {
    scanType: 'stealth',
    portRange: '1-1000',
    timing: 1,
    detectOS: false,
    detectVersion: false,
    enableScripts: false
  },
  'vulnerability': {
    scanType: 'comprehensive',
    portRange: '1-1000',
    timing: 3,
    detectOS: true,
    detectVersion: true,
    enableScripts: true,
    scriptCategories: ['vuln', 'safe', 'default']
  }
};

/**
 * Available NSE script categories
 */
const SCRIPT_CATEGORIES = [
  { value: 'auth', label: 'Authentication' },
  { value: 'broadcast', label: 'Broadcast' },
  { value: 'brute', label: 'Brute Force' },
  { value: 'default', label: 'Default' },
  { value: 'discovery', label: 'Discovery' },
  { value: 'dos', label: 'Denial of Service' },
  { value: 'exploit', label: 'Exploit' },
  { value: 'external', label: 'External' },
  { value: 'fuzzer', label: 'Fuzzer' },
  { value: 'intrusive', label: 'Intrusive' },
  { value: 'malware', label: 'Malware' },
  { value: 'safe', label: 'Safe' },
  { value: 'version', label: 'Version' },
  { value: 'vuln', label: 'Vulnerability' }
];

/**
 * Nmap Scanner Component
 */
export function NmapScanner() {
  // State management
  const [config, setConfig] = React.useState<NmapConfig>({
    target: '',
    scanType: 'syn',
    portRange: '1-1000',
    timing: 3,
    detectOS: false,
    detectVersion: false,
    enableScripts: false,
    scriptCategories: ['safe'],
    outputFormat: 'normal',
    customArgs: ''
  });
  
  const [isScanning, setIsScanning] = React.useState(false);
  const [currentExecution, setCurrentExecution] = React.useState<ToolExecution | null>(null);
  const [progress, setProgress] = React.useState(0);
  const [output, setOutput] = React.useState<string[]>([]);
  const [results, setResults] = React.useState<any>(null);
  const [activeTab, setActiveTab] = React.useState('configure');
  
  // Backend connection
  const { status: backendStatus } = useBackendStore();
  
  // Update configuration
  const updateConfig = (updates: Partial<NmapConfig>) => {
    setConfig(prev => ({ ...prev, ...updates }));
  };
  
  // Apply scan preset
  const applyPreset = (presetName: string) => {
    const preset = SCAN_PRESETS[presetName];
    if (preset) {
      updateConfig(preset);
      toast.success(`Applied ${presetName} scan preset`);
    }
  };
  
  // Generate Nmap command
  const generateCommand = (): string => {
    let command = 'nmap';
    
    // Scan type
    switch (config.scanType) {
      case 'syn':
        command += ' -sS';
        break;
      case 'tcp':
        command += ' -sT';
        break;
      case 'udp':
        command += ' -sU';
        break;
      case 'comprehensive':
        command += ' -sS -sU';
        break;
      case 'stealth':
        command += ' -sS -f';
        break;
      case 'aggressive':
        command += ' -A';
        break;
    }
    
    // Port range
    if (config.portRange && config.portRange !== '1-65535') {
      command += ` -p ${config.portRange}`;
    }
    
    // Timing
    command += ` -T${config.timing}`;
    
    // OS detection
    if (config.detectOS) {
      command += ' -O';
    }
    
    // Version detection
    if (config.detectVersion) {
      command += ' -sV';
    }
    
    // Scripts
    if (config.enableScripts && config.scriptCategories.length > 0) {
      command += ` --script=${config.scriptCategories.join(',')}`;
    }
    
    // Output format
    switch (config.outputFormat) {
      case 'xml':
        command += ' -oX -';
        break;
      case 'grepable':
        command += ' -oG -';
        break;
      case 'json':
        command += ' -oJ -';
        break;
    }
    
    // Custom arguments
    if (config.customArgs.trim()) {
      command += ` ${config.customArgs.trim()}`;
    }
    
    // Target
    command += ` ${config.target}`;
    
    return command;
  };
  
  // Start scan
  const startScan = async () => {
    if (!config.target.trim()) {
      toast.error('Please specify a target');
      return;
    }
    
    if (!backendStatus.connected) {
      toast.error('Backend not connected');
      return;
    }
    
    try {
      setIsScanning(true);
      setProgress(0);
      setOutput([]);
      setResults(null);
      setActiveTab('output');
      
      console.log('🚀 Starting Nmap scan with config:', config);

      const execution = await apiClient.executeTool('nmap', config, (progressUpdate) => {
        console.log('📊 Progress update:', progressUpdate);
        setCurrentExecution(progressUpdate);
        setProgress(progressUpdate.progress);
        if (progressUpdate.output) {
          setOutput(prev => [...prev, ...progressUpdate.output!]);
        }

        if (progressUpdate.status === 'completed') {
          setIsScanning(false);
          setResults(progressUpdate.results);
          console.log('✅ Scan completed with results:', progressUpdate.results);
          toast.success('Nmap scan completed');
        } else if (progressUpdate.status === 'failed') {
          setIsScanning(false);
          console.error('❌ Scan failed:', progressUpdate.error);
          toast.error(`Scan failed: ${progressUpdate.error}`);
        }
      });
      
      setCurrentExecution(execution);
      toast.info('Nmap scan started');
    } catch (error) {
      setIsScanning(false);
      console.error('Failed to start Nmap scan:', error);
      toast.error('Failed to start scan');
    }
  };
  
  // Stop scan
  const stopScan = async () => {
    if (currentExecution) {
      try {
        await apiClient.cancelExecution(currentExecution.id);
        setIsScanning(false);
        setCurrentExecution(null);
        toast.info('Scan cancelled');
      } catch (error) {
        console.error('Failed to stop scan:', error);
        toast.error('Failed to stop scan');
      }
    }
  };
  
  // Copy command to clipboard
  const copyCommand = async () => {
    try {
      await navigator.clipboard.writeText(generateCommand());
      toast.success('Command copied to clipboard');
    } catch (error) {
      toast.error('Failed to copy command');
    }
  };
  
  // Export results
  const exportResults = () => {
    if (!results && output.length === 0) {
      toast.error('No results to export');
      return;
    }
    
    const data = results || output.join('\n\n');
    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `nmap-scan-${new Date().toISOString().slice(0, 19)}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    
    toast.success('Results exported');
  };
  
  return (
    <div className="container-desktop py-6 space-y-6">
      {/* Tool Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className="p-2 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
            <Network className="h-6 w-6 text-blue-600 dark:text-blue-400" />
          </div>
          <div>
            <h1 className="text-2xl font-bold">Nmap Scanner</h1>
            <p className="text-muted-foreground">
              Network discovery and port scanning with OS and service detection
            </p>
          </div>
        </div>
        
        {/* Status indicators */}
        <div className="flex items-center gap-2">
          <Badge variant={backendStatus.connected ? 'default' : 'destructive'}>
            {backendStatus.connected ? 'Connected' : 'Offline'}
          </Badge>
          {isScanning && (
            <Badge variant="secondary" className="animate-pulse">
              Scanning...
            </Badge>
          )}
        </div>
      </div>
      
      {/* Main Interface - Mobile Responsive */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList>
          <TabsTrigger value="configure">
            <Settings className="h-4 w-4" />
            <span className="hidden sm:inline">Configure</span>
            <span className="sm:hidden">Config</span>
          </TabsTrigger>
          <TabsTrigger value="output">
            <Terminal className="h-4 w-4" />
            Output
          </TabsTrigger>
          <TabsTrigger value="results">
            <FileText className="h-4 w-4" />
            Results
          </TabsTrigger>
          <TabsTrigger value="command">
            <Target className="h-4 w-4" />
            <span className="hidden sm:inline">Command</span>
            <span className="sm:hidden">Cmd</span>
          </TabsTrigger>
        </TabsList>
        
        {/* Configuration Tab - Mobile Responsive */}
        <TabsContent value="configure">
          <MobileFormGrid>
            {/* Basic Configuration */}
            <MobileFormCard
              title="Target Configuration"
              description="Specify the target hosts and basic scan parameters"
              icon={<Target className="h-5 w-5" />}
            >
              {/* Target */}
              <TouchInput
                label="Target(s)"
                placeholder="e.g., ***********, 10.0.0.0/24, example.com"
                value={config.target}
                onChange={(e) => updateConfig({ target: e.target.value })}
                description="IP addresses, CIDR ranges, or hostnames"
              />

              {/* Scan Type */}
              <TouchSelect
                label="Scan Type"
                value={config.scanType}
                onValueChange={(value) => updateConfig({ scanType: value as any })}
              >
                <SelectItem value="syn">SYN Scan (-sS)</SelectItem>
                <SelectItem value="tcp">TCP Connect (-sT)</SelectItem>
                <SelectItem value="udp">UDP Scan (-sU)</SelectItem>
                <SelectItem value="comprehensive">Comprehensive (TCP+UDP)</SelectItem>
                <SelectItem value="stealth">Stealth (Fragmented)</SelectItem>
                <SelectItem value="aggressive">Aggressive (-A)</SelectItem>
              </TouchSelect>

              {/* Port Range */}
              <TouchInput
                label="Port Range"
                placeholder="e.g., 1-1000, 22,80,443, -"
                value={config.portRange}
                onChange={(e) => updateConfig({ portRange: e.target.value })}
                description="Specific ports, ranges, or '-' for all ports"
              />

              {/* Timing */}
              <TouchSelect
                label="Timing Template"
                value={config.timing.toString()}
                onValueChange={(value) => updateConfig({ timing: parseInt(value) as any })}
              >
                <SelectItem value="0">Paranoid (T0) - Very Slow</SelectItem>
                <SelectItem value="1">Sneaky (T1) - Slow</SelectItem>
                <SelectItem value="2">Polite (T2) - Normal</SelectItem>
                <SelectItem value="3">Normal (T3) - Default</SelectItem>
                <SelectItem value="4">Aggressive (T4) - Fast</SelectItem>
                <SelectItem value="5">Insane (T5) - Very Fast</SelectItem>
              </TouchSelect>
            </MobileFormCard>
            
            {/* Advanced Options */}
            <MobileFormCard
              title="Detection Options"
              description="Enable additional detection and enumeration features"
              icon={<Shield className="h-5 w-5" />}
            >
              {/* Detection Options */}
              <div className="space-y-4">
                <label className="flex items-center space-x-3 cursor-pointer">
                  <input
                    type="checkbox"
                    checked={config.detectOS}
                    onChange={(e) => updateConfig({ detectOS: e.target.checked })}
                    className="rounded border-gray-300 h-5 w-5"
                  />
                  <span className="text-sm font-medium">OS Detection (-O)</span>
                </label>

                <label className="flex items-center space-x-3 cursor-pointer">
                  <input
                    type="checkbox"
                    checked={config.detectVersion}
                    onChange={(e) => updateConfig({ detectVersion: e.target.checked })}
                    className="rounded border-gray-300 h-5 w-5"
                  />
                  <span className="text-sm font-medium">Version Detection (-sV)</span>
                </label>

                <label className="flex items-center space-x-3 cursor-pointer">
                  <input
                    type="checkbox"
                    checked={config.enableScripts}
                    onChange={(e) => updateConfig({ enableScripts: e.target.checked })}
                    className="rounded border-gray-300 h-5 w-5"
                  />
                  <span className="text-sm font-medium">Enable NSE Scripts</span>
                </label>
              </div>
                
              {/* Script Categories */}
              {config.enableScripts && (
                <MobileFormField label="Script Categories">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                    {SCRIPT_CATEGORIES.map((category) => (
                      <label key={category.value} className="flex items-center space-x-3 cursor-pointer">
                        <input
                          type="checkbox"
                          checked={config.scriptCategories.includes(category.value)}
                          onChange={(e) => {
                            if (e.target.checked) {
                              updateConfig({
                                scriptCategories: [...config.scriptCategories, category.value]
                              });
                            } else {
                              updateConfig({
                                scriptCategories: config.scriptCategories.filter(c => c !== category.value)
                              });
                            }
                          }}
                          className="rounded border-gray-300 h-4 w-4"
                        />
                        <span className="text-sm">{category.label}</span>
                      </label>
                    ))}
                  </div>
                </MobileFormField>
              )}

              {/* Output Format */}
              <TouchSelect
                label="Output Format"
                value={config.outputFormat}
                onValueChange={(value) => updateConfig({ outputFormat: value as any })}
              >
                <SelectItem value="normal">Normal Text</SelectItem>
                <SelectItem value="xml">XML Format</SelectItem>
                <SelectItem value="grepable">Grepable Format</SelectItem>
                <SelectItem value="json">JSON Format</SelectItem>
              </TouchSelect>

              {/* Custom Arguments */}
              <TouchInput
                label="Custom Arguments"
                placeholder="Additional nmap arguments"
                value={config.customArgs}
                onChange={(e) => updateConfig({ customArgs: e.target.value })}
              />
            </MobileFormCard>
          </MobileFormGrid>
          
          {/* Presets and Actions */}
          <MobileFormCard
            title="Quick Presets & Actions"
            description="Use predefined configurations or start your scan"
            icon={<Zap className="h-5 w-5" />}
          >
            <MobileActionBar>
              {/* Preset buttons */}
              <div className="flex flex-wrap gap-2">
                {Object.keys(SCAN_PRESETS).map((preset) => (
                  <TouchButton
                    key={preset}
                    variant="outline"
                    size="sm"
                    onClick={() => applyPreset(preset)}
                    className="capitalize"
                  >
                    {preset} Scan
                  </TouchButton>
                ))}
              </div>

              {/* Action buttons */}
              <div className="flex flex-col gap-2 md:flex-row">
                <TouchButton
                  variant="outline"
                  onClick={copyCommand}
                >
                  <Copy className="h-4 w-4 mr-2" />
                  Copy Command
                </TouchButton>

                {isScanning ? (
                  <TouchButton
                    variant="destructive"
                    onClick={stopScan}
                  >
                    <Square className="h-4 w-4 mr-2" />
                    Stop Scan
                  </TouchButton>
                ) : (
                  <TouchButton
                    onClick={startScan}
                    disabled={!config.target.trim() || !backendStatus.connected}
                  >
                    <Play className="h-4 w-4 mr-2" />
                    Start Scan
                  </TouchButton>
                )}
              </div>
            </MobileActionBar>
          </MobileFormCard>
        </TabsContent>
        
        {/* Output Tab - Mobile Responsive */}
        <TabsContent value="output">
          <MobileFormCard
            title="Real-time Output"
            description="Live output from the Nmap scan process"
            icon={<Terminal className="h-5 w-5" />}
          >
            <MobileActionBar>
              {/* Progress indicator */}
              <div className="flex items-center gap-2">
                {isScanning && (
                  <>
                    <Progress value={progress} className="w-24 md:w-32" />
                    <span className="text-xs md:text-sm text-muted-foreground">
                      {progress}%
                    </span>
                  </>
                )}
              </div>

              {/* Controls */}
              <TouchButton
                variant="outline"
                size="sm"
                onClick={() => setOutput([])}
                disabled={isScanning}
              >
                <RotateCcw className="h-4 w-4 mr-2" />
                Clear
              </TouchButton>
            </MobileActionBar>

            <div className="bg-gray-900 text-gray-100 p-3 md:p-4 rounded-lg h-64 md:h-96 overflow-y-auto font-mono text-xs md:text-sm">
              {output.length === 0 ? (
                <div className="text-gray-500 italic">
                  {isScanning ? 'Waiting for output...' : 'No output yet. Start a scan to see results here.'}
                </div>
              ) : (
                output.map((line, index) => (
                  <div key={index} className="mb-1">
                    {line}
                  </div>
                ))
              )}
            </div>
          </MobileFormCard>
        </TabsContent>
        
        {/* Results Tab - Mobile Responsive */}
        <TabsContent value="results">
          <MobileFormCard
            title="Scan Results"
            description="Structured results and analysis from the completed scan"
            icon={<FileText className="h-5 w-5" />}
          >
            <MobileActionBar>
              <div></div> {/* Spacer */}
              <TouchButton
                variant="outline"
                onClick={exportResults}
                disabled={!results && output.length === 0}
              >
                <Download className="h-4 w-4 mr-2" />
                Export
              </TouchButton>
            </MobileActionBar>

            {results ? (
              <div className="space-y-4">
                {/* Results will be displayed here */}
                <pre className="bg-muted p-3 md:p-4 rounded-lg overflow-auto text-xs md:text-sm">
                  {JSON.stringify(results, null, 2)}
                </pre>
              </div>
            ) : (
              <div className="text-center py-6 md:py-8 text-muted-foreground">
                <FileText className="h-8 w-8 md:h-12 md:w-12 mx-auto mb-3 md:mb-4 opacity-50" />
                <p className="text-sm md:text-base">No structured results available yet.</p>
                <p className="text-xs md:text-sm">Complete a scan to see parsed results here.</p>
              </div>
            )}
          </MobileFormCard>
        </TabsContent>
        
        {/* Command Tab - Mobile Responsive */}
        <TabsContent value="command">
          <MobileFormCard
            title="Generated Command"
            description="The Nmap command that will be executed based on your configuration"
            icon={<Target className="h-5 w-5" />}
          >
            <div className="space-y-4">
              <div className="bg-muted p-3 md:p-4 rounded-lg">
                <code className="text-xs md:text-sm font-mono break-all">
                  {generateCommand()}
                </code>
              </div>

              <MobileActionBar>
                <p className="text-xs md:text-sm text-muted-foreground">
                  This command will be executed on the backend server
                </p>

                <TouchButton variant="outline" onClick={copyCommand}>
                  <Copy className="h-4 w-4 mr-2" />
                  Copy
                </TouchButton>
              </MobileActionBar>
            </div>
          </MobileFormCard>
        </TabsContent>
      </Tabs>
    </div>
  );
}