#!/usr/bin/env python3
"""
Test Railway Deployment - Comprehensive Tool Verification
Verify all 50+ tools from tools-based-frontend.md actually work on Railway
"""

import asyncio
import aiohttp
import json
import time
from datetime import datetime
from typing import Dict, List, Any, Optional

# Railway backend URL
RAILWAY_URL = "https://web-production-48c02.up.railway.app"

# Expected tools from tools-based-frontend.md
EXPECTED_TOOLS = {
    "Core Security Scanners (3 tools)": [
        "nmap",
        "nuclei", 
        "sqlmap"
    ],
    
    "AI-Powered Tools (28+ tools)": [
        "creative_exploit_engine",
        "multi_stage_orchestrator", 
        "behavioral_analysis_engine",
        "adaptive_exploit_modifier",
        "evasion_technique_generator",
        "vulnerability_agent",
        "threat_intelligence_analyzer",
        "scan_recommendation_engine",
        "remediation_engine",
        "intelligent_payload_optimizer",
        "predictive_vulnerability_discovery",
        "automated_exploit_chaining",
        "realtime_threat_intelligence_correlator"
    ],
    
    "Custom Intelligence Tools (3 tools)": [
        "ai_reconnaissance_engine",
        "tech_fingerprinter", 
        "mitre_attack_mapper"
    ],
    
    "Compliance Testing (1 comprehensive)": [
        "automated_compliance_tester"
    ],
    
    "Utility Tools (7 tools)": [
        "universal_parser",
        "evidence_collector",
        "proxy_engine", 
        "metasploit_bridge",
        "credential_validator",
        "payload_encoder",
        "report_formatter"
    ],
    
    "Infrastructure Tools (8 tools)": [
        "performance_monitor",
        "resource_optimizer",
        "self_healing_manager",
        "environment_detector", 
        "execution_orchestrator",
        "hybrid_execution_engine",
        "tool_registry",
        "capability_mapper"
    ],
    
    "External Tool Integration (20+ tools)": [
        "gobuster",
        "dirb",
        "nikto", 
        "wpscan",
        "ffuf",
        "feroxbuster",
        "whatweb",
        "hashcat",
        "john",
        "enum4linux",
        "smbclient",
        "testssl",
        "sslyze",
        "metasploit",
        "searchsploit"
    ]
}


class RailwayDeploymentTester:
    """Test Railway deployment for all documented tools"""
    
    def __init__(self):
        self.base_url = RAILWAY_URL
        self.results = {
            "test_timestamp": datetime.now().isoformat(),
            "base_url": self.base_url,
            "backend_status": "unknown",
            "tools_availability": {},
            "tool_execution_tests": {},
            "summary": {
                "total_expected_tools": 0,
                "tools_found": 0,
                "tools_available": 0,
                "tools_working": 0,
                "missing_tools": [],
                "broken_tools": [],
                "success_rate": 0.0
            }
        }
        
        # Count total expected tools
        for category, tools in EXPECTED_TOOLS.items():
            self.results["summary"]["total_expected_tools"] += len(tools)
    
    async def run_comprehensive_test(self):
        """Run comprehensive test of Railway deployment"""
        print(f"\n🧪 Testing Railway Deployment at: {self.base_url}")
        print("=" * 80)
        print(f"🕐 Test started: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=30)) as session:
            # Test 1: Backend Health
            await self._test_backend_health(session)
            
            # Test 2: Tools Availability
            await self._test_tools_availability(session)
            
            # Test 3: Tool Execution
            await self._test_tool_execution(session)
            
            # Test 4: API Endpoints
            await self._test_api_endpoints(session)
        
        # Generate final report
        self._generate_final_report()
    
    async def _test_backend_health(self, session: aiohttp.ClientSession):
        """Test basic backend health"""
        print("\n🏥 Testing Backend Health...")
        print("-" * 60)
        
        try:
            # Test health endpoint
            async with session.get(f"{self.base_url}/api/health") as response:
                if response.status == 200:
                    health_data = await response.json()
                    print(f"✅ Health endpoint: {response.status}")
                    print(f"   Backend status: {health_data.get('status', 'unknown')}")
                    self.results["backend_status"] = "healthy"
                else:
                    print(f"❌ Health endpoint failed: {response.status}")
                    self.results["backend_status"] = "unhealthy"
        except Exception as e:
            print(f"❌ Health check failed: {e}")
            self.results["backend_status"] = "unreachable"
    
    async def _test_tools_availability(self, session: aiohttp.ClientSession):
        """Test tools availability endpoint"""
        print("\n🔧 Testing Tools Availability...")
        print("-" * 60)
        
        try:
            async with session.get(f"{self.base_url}/api/tools/available") as response:
                if response.status == 200:
                    tools_data = await response.json()
                    print(f"✅ Tools endpoint: {response.status}")
                    
                    if tools_data.get("success", False):
                        available_tools = tools_data.get("data", [])
                        metadata = tools_data.get("metadata", {})
                        
                        print(f"📊 Tools Summary:")
                        print(f"   Total tools: {metadata.get('total_tools', 0)}")
                        print(f"   Available tools: {metadata.get('available_tools', 0)}")
                        print(f"   Railway mode: {metadata.get('railway_mode', False)}")
                        
                        # Check each expected tool
                        found_tools = {tool["id"]: tool for tool in available_tools}
                        
                        for category, expected_tools in EXPECTED_TOOLS.items():
                            print(f"\n📂 {category}:")
                            for tool_name in expected_tools:
                                if tool_name in found_tools:
                                    tool_info = found_tools[tool_name]
                                    status = tool_info.get("status", "unknown")
                                    
                                    if status == "available":
                                        print(f"   ✅ {tool_name} - Available")
                                        self.results["summary"]["tools_available"] += 1
                                    else:
                                        print(f"   ⚠️  {tool_name} - Found but {status}")
                                    
                                    self.results["summary"]["tools_found"] += 1
                                    self.results["tools_availability"][tool_name] = {
                                        "found": True,
                                        "status": status,
                                        "details": tool_info
                                    }
                                else:
                                    print(f"   ❌ {tool_name} - Missing")
                                    self.results["summary"]["missing_tools"].append(tool_name)
                                    self.results["tools_availability"][tool_name] = {
                                        "found": False,
                                        "status": "missing"
                                    }
                        
                        # Check for unexpected tools
                        all_expected = []
                        for tools in EXPECTED_TOOLS.values():
                            all_expected.extend(tools)
                        
                        unexpected_tools = [tool["id"] for tool in available_tools if tool["id"] not in all_expected]
                        if unexpected_tools:
                            print(f"\n🆕 Unexpected tools found: {unexpected_tools}")
                    
                    else:
                        print(f"❌ Tools endpoint returned success=false")
                        print(f"   Error: {tools_data.get('error', 'Unknown error')}")
                
                else:
                    print(f"❌ Tools endpoint failed: {response.status}")
                    error_text = await response.text()
                    print(f"   Error: {error_text[:200]}...")
        
        except Exception as e:
            print(f"❌ Tools availability test failed: {e}")
    
    async def _test_tool_execution(self, session: aiohttp.ClientSession):
        """Test actual tool execution"""
        print("\n⚡ Testing Tool Execution...")
        print("-" * 60)
        
        # Test a few key tools from each category
        test_tools = [
            ("nmap", "Core Scanner"),
            ("nuclei", "Core Scanner"), 
            ("vulnerability_agent", "AI Tool"),
            ("gobuster", "External Tool")
        ]
        
        test_target = "example.com"
        
        for tool_name, category in test_tools:
            print(f"\n🔍 Testing {tool_name} ({category})...")
            
            try:
                # Prepare scan request
                scan_data = {
                    "target": test_target,
                    "options": {
                        "scan_type": "quick",
                        "timeout": 30
                    }
                }
                
                # Execute tool scan
                async with session.post(
                    f"{self.base_url}/api/tools/{tool_name}/scan",
                    json=scan_data,
                    headers={"Content-Type": "application/json"}
                ) as response:
                    
                    if response.status == 200:
                        result_data = await response.json()
                        
                        if result_data.get("success", False):
                            scan_result = result_data.get("data", {})
                            print(f"   ✅ {tool_name} execution successful")
                            print(f"      Status: {scan_result.get('status', 'unknown')}")
                            print(f"      Duration: {scan_result.get('duration_seconds', 0):.2f}s")
                            
                            # Check for results
                            if scan_result.get("results"):
                                print(f"      Results: Present ({len(str(scan_result['results']))} chars)")
                            
                            if scan_result.get("vulnerabilities"):
                                print(f"      Vulnerabilities: {len(scan_result['vulnerabilities'])}")
                            
                            self.results["summary"]["tools_working"] += 1
                            self.results["tool_execution_tests"][tool_name] = {
                                "status": "success",
                                "execution_time": scan_result.get("duration_seconds", 0),
                                "has_results": bool(scan_result.get("results"))
                            }
                        else:
                            print(f"   ❌ {tool_name} execution failed")
                            print(f"      Error: {result_data.get('error', 'Unknown error')}")
                            self.results["summary"]["broken_tools"].append(tool_name)
                            self.results["tool_execution_tests"][tool_name] = {
                                "status": "failed",
                                "error": result_data.get("error", "Unknown error")
                            }
                    
                    else:
                        print(f"   ❌ {tool_name} HTTP error: {response.status}")
                        error_text = await response.text()
                        print(f"      Error: {error_text[:100]}...")
                        self.results["summary"]["broken_tools"].append(tool_name)
                        self.results["tool_execution_tests"][tool_name] = {
                            "status": "http_error",
                            "status_code": response.status,
                            "error": error_text[:200]
                        }
            
            except Exception as e:
                print(f"   ❌ {tool_name} execution exception: {e}")
                self.results["summary"]["broken_tools"].append(tool_name)
                self.results["tool_execution_tests"][tool_name] = {
                    "status": "exception", 
                    "error": str(e)
                }
    
    async def _test_api_endpoints(self, session: aiohttp.ClientSession):
        """Test various API endpoints"""
        print("\n🌐 Testing API Endpoints...")
        print("-" * 60)
        
        endpoints_to_test = [
            ("GET", "/api/tools/health", "Tools health check"),
            ("GET", "/api/orchestrator/capabilities", "Multi-Stage Orchestrator"),
            ("GET", "/api/ai/capabilities/status", "AI capabilities status"),
            ("GET", "/api/proxy/configurations", "AI Proxy configurations"),
            ("GET", "/api/ai/creative-exploits/capabilities", "Creative Exploit Engine")
        ]
        
        for method, endpoint, description in endpoints_to_test:
            try:
                if method == "GET":
                    async with session.get(f"{self.base_url}{endpoint}") as response:
                        if response.status == 200:
                            print(f"   ✅ {description}: {response.status}")
                        else:
                            print(f"   ❌ {description}: {response.status}")
                
            except Exception as e:
                print(f"   ❌ {description}: Exception - {e}")
    
    def _generate_final_report(self):
        """Generate comprehensive final report"""
        print("\n" + "=" * 80)
        print("📊 RAILWAY DEPLOYMENT TEST RESULTS")
        print("=" * 80)
        
        # Calculate success rate
        total_expected = self.results["summary"]["total_expected_tools"]
        tools_working = self.results["summary"]["tools_working"]
        
        if total_expected > 0:
            success_rate = (self.results["summary"]["tools_available"] / total_expected) * 100
            self.results["summary"]["success_rate"] = success_rate
        
        # Print summary
        print(f"🎯 Test Summary:")
        print(f"   Backend Status: {self.results['backend_status']}")
        print(f"   Total Expected Tools: {total_expected}")
        print(f"   Tools Found: {self.results['summary']['tools_found']}")
        print(f"   Tools Available: {self.results['summary']['tools_available']}")
        print(f"   Tools Working: {self.results['summary']['tools_working']}")
        print(f"   Success Rate: {success_rate:.1f}%")
        
        # Missing tools
        missing_tools = self.results["summary"]["missing_tools"]
        if missing_tools:
            print(f"\n❌ Missing Tools ({len(missing_tools)}):")
            for tool in missing_tools[:10]:  # Show first 10
                print(f"   - {tool}")
            if len(missing_tools) > 10:
                print(f"   ... and {len(missing_tools) - 10} more")
        
        # Broken tools
        broken_tools = self.results["summary"]["broken_tools"]
        if broken_tools:
            print(f"\n⚠️  Broken Tools ({len(broken_tools)}):")
            for tool in broken_tools:
                print(f"   - {tool}")
        
        # Overall status
        print(f"\n🏆 Overall Status:")
        if success_rate >= 80:
            print("   ✅ EXCELLENT - Ready for frontend development")
        elif success_rate >= 60:
            print("   ⚠️  GOOD - Most tools working, some fixes needed")
        elif success_rate >= 40:
            print("   ❌ NEEDS WORK - Many tools missing or broken")
        else:
            print("   🚨 CRITICAL - Major issues, backend needs fixing")
        
        # Recommendations
        print(f"\n📝 Recommendations:")
        if success_rate >= 80:
            print("   • Proceed with frontend development")
            print("   • All major tools are functional")
            print("   • Consider adding missing tools as needed")
        else:
            print("   • Fix missing/broken tools before frontend development")
            print("   • Check Railway deployment logs for errors")
            print("   • Verify tool registration and availability logic")
        
        # Save detailed results
        with open("railway_deployment_test_results.json", "w") as f:
            json.dump(self.results, f, indent=2)
        
        print(f"\n💾 Detailed results saved to: railway_deployment_test_results.json")


async def main():
    """Main test function"""
    print("🚀 Railway Deployment Comprehensive Test")
    print(f"Testing all tools from tools-based-frontend.md")
    print(f"Target: {RAILWAY_URL}")
    
    tester = RailwayDeploymentTester()
    await tester.run_comprehensive_test()


if __name__ == "__main__":
    asyncio.run(main())