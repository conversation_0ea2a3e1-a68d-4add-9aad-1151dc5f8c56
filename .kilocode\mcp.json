{"mcpServers": {"context7": {"command": "npx", "args": ["-y", "@upstash/context7-mcp"], "env": {"DEFAULT_MINIMUM_TOKENS": ""}}, "github": {"command": "cmd", "args": ["/c", "npx", "-y", "@modelcontextprotocol/server-github"], "env": {"GITHUB_PERSONAL_ACCESS_TOKEN": "*********************************************************************************************"}}, "Playwright": {"type": "stdio", "command": "cmd", "args": ["/c", "npx", "-y", "@playwright/mcp@latest"], "env": {}}}}