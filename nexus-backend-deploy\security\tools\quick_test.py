#!/usr/bin/env python3
# Quick NexusScan Test Script

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_core():
    try:
        from core.config import Config
        from core.database import DatabaseManager
        config = Config()
        db = DatabaseManager(config.database.database_path)
        print("✅ Core systems working")
        return True
    except Exception as e:
        print(f"❌ Core systems failed: {e}")
        return False

def test_tools():
    working_tools = []
    tools = [
        ("security.tools.nmap_scanner", "Nmap"),
        ("security.tools.nuclei_scanner", "Nuclei"), 
        ("security.tools.sqlmap_scanner", "SQLMap")
    ]
    
    for module, name in tools:
        try:
            mod = __import__(module, fromlist=[name + "Scanner"])
            scanner_class = getattr(mod, name + "Scanner")
            scanner = scanner_class()
            available = scanner.check_availability()
            status = "✅" if available else "⚠️"
            print(f"{status} {name}: {'Available' if available else 'Not installed'}")
            if available:
                working_tools.append(name)
        except Exception as e:
            print(f"❌ {name}: {e}")
    
    return working_tools

if __name__ == "__main__":
    print("🛡️ NexusScan Quick Test")
    print("=" * 30)
    
    core_ok = test_core()
    working_tools = test_tools()
    
    print(f"\n📊 Status:")
    print(f"Core Framework: {'✅' if core_ok else '❌'}")
    print(f"Working Tools: {len(working_tools)}/3")
    print(f"Tools Available: {', '.join(working_tools) if working_tools else 'None'}")
    
    if core_ok and len(working_tools) > 0:
        print("\n🎉 NexusScan is partially working!")
        print("Install remaining tools for full functionality.")
    elif core_ok:
        print("\n⚠️ Core framework works but no security tools installed.")
        print("See INSTALLATION_GUIDE.md for tool installation.")
    else:
        print("\n❌ Core framework needs attention.")
