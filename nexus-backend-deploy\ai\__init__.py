"""
AI Services Package for NexusScan Desktop Application
Provides comprehensive AI-powered security analysis, vulnerability assessment,
threat intelligence, remediation, and intelligent reporting capabilities.
"""

# Core AI service infrastructure
from .ai_service import (
    AIServiceManager, AIServiceProvider, AIServiceConfig, AIAnalysisRequest, AIAnalysisResult,
    AICapability, AIModelType, AnalysisType, VulnerabilityContext, ThreatContext,
    ai_service_manager
)

# Vulnerability assessment AI agent
from .vulnerability_agent import (
    VulnerabilityAgent, VulnerabilityAssessment, VulnerabilityCategory,
    ExploitComplexity, AttackVector, AttackPath, ThreatLandscape
)

# Scan recommendation engine
from .scan_recommendation_engine import (
    ScanRecommendationEngine, ScanRecommendation, ScanStrategy, TargetProfile,
    ScanPriority, TargetContext, ScanPhase, ThreatIntelligence
)

# Remediation engine
from .remediation_engine import (
    RemediationEngine, RemediationPlan, RemediationStep, RemediationType,
    RemediationPriority, RemediationComplexity, PatchInformation,
    ConfigurationRecommendation
)

# Threat intelligence integration
from .threat_intelligence import (
    ThreatIntelligenceEngine, ThreatIndicator, ThreatCampaign, ThreatActor,
    VulnerabilityIntelligence, ThreatLevel, ThreatType, IntelligenceSource,
    ConfidenceLevel
)

# AI-powered insights and reporting
from .insights_engine import (
    InsightsEngine, SecurityMetrics, TrendAnalysis, SecurityInsight,
    ExecutiveInsight, IntelligentReport, InsightType, ReportType,
    AnalyticsTimeframe, PredictiveModel
)

# Legacy compatibility
from .services import (
    AIServiceManager as LegacyAIServiceManager,
    AIProvider,
    ExploitType,
    ExploitRequest,
    ExploitResponse,
    AnalysisRequest,
    AnalysisResponse
)

__all__ = [
    # Core AI infrastructure
    "AIServiceManager",
    "AIServiceProvider", 
    "AIServiceConfig",
    "AIAnalysisRequest",
    "AIAnalysisResult",
    "AICapability",
    "AIModelType",
    "AnalysisType",
    "VulnerabilityContext",
    "ThreatContext",
    "ai_service_manager",
    
    # Vulnerability assessment
    "VulnerabilityAgent",
    "VulnerabilityAssessment",
    "VulnerabilityCategory",
    "ExploitComplexity",
    "AttackVector",
    "AttackPath",
    "ThreatLandscape",
    
    # Scan recommendations
    "ScanRecommendationEngine",
    "ScanRecommendation",
    "ScanStrategy",
    "TargetProfile",
    "ScanPriority",
    "TargetContext",
    "ScanPhase",
    "ThreatIntelligence",
    
    # Remediation
    "RemediationEngine",
    "RemediationPlan",
    "RemediationStep",
    "RemediationType",
    "RemediationPriority",
    "RemediationComplexity",
    "PatchInformation",
    "ConfigurationRecommendation",
    
    # Threat intelligence
    "ThreatIntelligenceEngine",
    "ThreatIndicator",
    "ThreatCampaign",
    "ThreatActor",
    "VulnerabilityIntelligence",
    "ThreatLevel",
    "ThreatType",
    "IntelligenceSource",
    "ConfidenceLevel",
    
    # Insights and reporting
    "InsightsEngine",
    "SecurityMetrics",
    "TrendAnalysis",
    "SecurityInsight",
    "ExecutiveInsight",
    "IntelligentReport",
    "InsightType",
    "ReportType",
    "AnalyticsTimeframe",
    "PredictiveModel",
    
    # Legacy compatibility
    "LegacyAIServiceManager",
    "AIProvider",
    "ExploitType",
    "ExploitRequest",
    "ExploitResponse",
    "AnalysisRequest",
    "AnalysisResponse"
]