/**
 * Burp Suite Interface Component
 * Professional web application security testing platform integration
 */
import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { 
  Shield,
  Play,
  Square,
  Download,
  Settings,
  Target,
  Globe,
  FileText,
  Terminal,
  Copy,
  RotateCcw,
  Clock,
  Zap,
  Filter,
  Eye,
  AlertTriangle,
  CheckCircle,
  TrendingUp,
  Activity,
  Lock,
  Search,
  List,
  BarChart3,
  Database,
  Server,
  Bug,
  Info,
  Layers,
  Network,
  Code,
  Package,
  Gauge,
  <PERSON>,
  <PERSON>hair,
  Scan<PERSON>ine,
  <PERSON>,
  Key,
  Hash,
  <PERSON>,
  Crown,
  Cpu
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useBackendStore } from '@/stores/backend-store';
import { apiClient, ToolExecution } from '@/services/api-client';
import { toast } from 'sonner';

/**
 * Burp Suite configuration interface
 */
interface BurpSuiteConfig {
  target: {
    url: string;
    scope: string[];
    includeSubdomains: boolean;
    protocol: 'http' | 'https' | 'both';
  };
  scanning: {
    mode: 'crawl_and_audit' | 'crawl_only' | 'audit_only' | 'passive_audit';
    scanSpeed: 'thorough' | 'normal' | 'fast';
    auditAccuracy: 'minimize_false_positives' | 'normal' | 'minimize_false_negatives';
    scanTypes: {
      sql_injection: boolean;
      xss: boolean;
      command_injection: boolean;
      path_traversal: boolean;
      file_inclusion: boolean;
      xxe: boolean;
      ssrf: boolean;
      deserialization: boolean;
      expression_language: boolean;
      ldap_injection: boolean;
      header_injection: boolean;
      host_header: boolean;
      cache_poisoning: boolean;
      csrf: boolean;
      clickjacking: boolean;
      dom_xss: boolean;
      websocket: boolean;
      cors: boolean;
      tls_issues: boolean;
      insecure_cookies: boolean;
    };
  };
  crawler: {
    maxCrawlTime: number;
    maxUniqueLocations: number;
    maxDepth: number;
    maxParameters: number;
    handleApplicationErrors: boolean;
    useAdvancedJS: boolean;
    submitForms: boolean;
    followRedirects: boolean;
    parseRobotsTxt: boolean;
    parseSitemapXml: boolean;
  };
  authentication: {
    enabled: boolean;
    type: 'form' | 'http_basic' | 'http_digest' | 'ntlm' | 'custom';
    loginUrl: string;
    username: string;
    password: string;
    usernameField: string;
    passwordField: string;
    submitButton: string;
    sessionHandling: boolean;
    macros: string[];
  };
  proxy: {
    enabled: boolean;
    host: string;
    port: number;
    authentication: {
      enabled: boolean;
      username: string;
      password: string;
    };
  };
  headers: {
    userAgent: string;
    customHeaders: { [key: string]: string };
    cookies: string;
  };
  advanced: {
    maxConcurrentRequests: number;
    requestTimeout: number;
    retryOnNetworkFailure: boolean;
    throttleRequests: number;
    resourcePool: 'normal' | 'fast' | 'thorough';
    javaHeapSize: string;
    extensionsEnabled: boolean;
    collaboratorServer: string;
  };
  reporting: {
    format: 'html' | 'xml' | 'json';
    includeRequestResponse: boolean;
    reportConfidence: 'certain' | 'firm' | 'tentative';
    reportSeverity: 'high' | 'medium' | 'low' | 'info';
    falsePositiveThreshold: number;
  };
  customArgs: string;
}

/**
 * Burp Suite scan modes
 */
const SCAN_MODES = {
  'crawl_and_audit': {
    name: 'Crawl and Audit',
    description: 'Complete discovery and security testing',
    icon: <Search className="h-4 w-4" />,
    duration: 'Long'
  },
  'crawl_only': {
    name: 'Crawl Only',
    description: 'Discovery and mapping only',
    icon: <Spider className="h-4 w-4" />,
    duration: 'Medium'
  },
  'audit_only': {
    name: 'Audit Only',
    description: 'Security testing of known URLs',
    icon: <Shield className="h-4 w-4" />,
    duration: 'Medium'
  },
  'passive_audit': {
    name: 'Passive Audit',
    description: 'Passive security analysis',
    icon: <Eye className="h-4 w-4" />,
    duration: 'Short'
  }
};

/**
 * Scan speeds
 */
const SCAN_SPEEDS = {
  'thorough': { name: 'Thorough', description: 'Comprehensive but slow', time: '4-8 hours' },
  'normal': { name: 'Normal', description: 'Balanced speed and coverage', time: '1-3 hours' },
  'fast': { name: 'Fast', description: 'Quick scan with basic coverage', time: '15-60 minutes' }
};

/**
 * Audit accuracy levels
 */
const AUDIT_ACCURACY = {
  'minimize_false_positives': { name: 'Minimize False Positives', description: 'Higher confidence, fewer findings' },
  'normal': { name: 'Normal', description: 'Balanced accuracy and coverage' },
  'minimize_false_negatives': { name: 'Minimize False Negatives', description: 'More comprehensive, may include false positives' }
};

/**
 * Vulnerability categories
 */
const VULNERABILITY_CATEGORIES = {
  'injection': ['sql_injection', 'command_injection', 'ldap_injection', 'expression_language'],
  'xss': ['xss', 'dom_xss'],
  'file_access': ['path_traversal', 'file_inclusion'],
  'xml_issues': ['xxe'],
  'server_issues': ['ssrf', 'deserialization'],
  'client_issues': ['csrf', 'clickjacking', 'cors'],
  'infrastructure': ['tls_issues', 'header_injection', 'host_header', 'cache_poisoning'],
  'session': ['insecure_cookies', 'websocket']
};

export default function BurpSuiteInterface() {
  const { connectionStatus } = useBackendStore();
  const [config, setConfig] = React.useState<BurpSuiteConfig>({
    target: {
      url: '',
      scope: [],
      includeSubdomains: true,
      protocol: 'both'
    },
    scanning: {
      mode: 'crawl_and_audit',
      scanSpeed: 'normal',
      auditAccuracy: 'normal',
      scanTypes: {
        sql_injection: true,
        xss: true,
        command_injection: true,
        path_traversal: true,
        file_inclusion: true,
        xxe: true,
        ssrf: true,
        deserialization: false,
        expression_language: false,
        ldap_injection: false,
        header_injection: true,
        host_header: false,
        cache_poisoning: false,
        csrf: true,
        clickjacking: true,
        dom_xss: true,
        websocket: false,
        cors: true,
        tls_issues: true,
        insecure_cookies: true
      }
    },
    crawler: {
      maxCrawlTime: 60,
      maxUniqueLocations: 10000,
      maxDepth: 10,
      maxParameters: 65,
      handleApplicationErrors: true,
      useAdvancedJS: true,
      submitForms: true,
      followRedirects: true,
      parseRobotsTxt: true,
      parseSitemapXml: true
    },
    authentication: {
      enabled: false,
      type: 'form',
      loginUrl: '',
      username: '',
      password: '',
      usernameField: 'username',
      passwordField: 'password',
      submitButton: '',
      sessionHandling: false,
      macros: []
    },
    proxy: {
      enabled: false,
      host: '',
      port: 8080,
      authentication: {
        enabled: false,
        username: '',
        password: ''
      }
    },
    headers: {
      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
      customHeaders: {},
      cookies: ''
    },
    advanced: {
      maxConcurrentRequests: 10,
      requestTimeout: 120,
      retryOnNetworkFailure: true,
      throttleRequests: 0,
      resourcePool: 'normal',
      javaHeapSize: '2g',
      extensionsEnabled: true,
      collaboratorServer: ''
    },
    reporting: {
      format: 'html',
      includeRequestResponse: true,
      reportConfidence: 'firm',
      reportSeverity: 'medium',
      falsePositiveThreshold: 20
    },
    customArgs: ''
  });

  const [execution, setExecution] = React.useState<ToolExecution | null>(null);
  const [activeTab, setActiveTab] = React.useState('configure');

  /**
   * Handle configuration changes
   */
  const updateConfig = (path: string, value: any) => {
    setConfig(prev => {
      const keys = path.split('.');
      const newConfig = { ...prev };
      let current = newConfig;
      
      for (let i = 0; i < keys.length - 1; i++) {
        current[keys[i]] = { ...current[keys[i]] };
        current = current[keys[i]];
      }
      
      current[keys[keys.length - 1]] = value;
      return newConfig;
    });
  };

  /**
   * Handle preset configurations
   */
  const applyPreset = (preset: string) => {
    const presets = {
      'quick_scan': {
        scanning: {
          ...config.scanning,
          mode: 'crawl_and_audit',
          scanSpeed: 'fast',
          scanTypes: {
            ...config.scanning.scanTypes,
            sql_injection: true,
            xss: true,
            csrf: true,
            clickjacking: true,
            insecure_cookies: true
          }
        },
        crawler: { ...config.crawler, maxCrawlTime: 15, maxUniqueLocations: 1000, maxDepth: 5 }
      },
      'comprehensive_audit': {
        scanning: {
          ...config.scanning,
          mode: 'crawl_and_audit',
          scanSpeed: 'thorough',
          auditAccuracy: 'minimize_false_negatives',
          scanTypes: Object.keys(config.scanning.scanTypes).reduce((acc, key) => {
            acc[key] = true;
            return acc;
          }, {})
        },
        crawler: { ...config.crawler, maxCrawlTime: 120, maxUniqueLocations: 50000, maxDepth: 15 }
      },
      'owasp_top_10': {
        scanning: {
          ...config.scanning,
          mode: 'crawl_and_audit',
          scanSpeed: 'normal',
          scanTypes: {
            sql_injection: true,
            xss: true,
            command_injection: true,
            path_traversal: true,
            file_inclusion: true,
            xxe: true,
            ssrf: true,
            deserialization: true,
            csrf: true,
            insecure_cookies: true,
            // Reset others
            expression_language: false,
            ldap_injection: false,
            header_injection: false,
            host_header: false,
            cache_poisoning: false,
            clickjacking: false,
            dom_xss: false,
            websocket: false,
            cors: false,
            tls_issues: false
          }
        }
      },
      'authenticated_scan': {
        authentication: {
          enabled: true,
          type: 'form',
          sessionHandling: true
        },
        scanning: {
          ...config.scanning,
          mode: 'crawl_and_audit',
          scanSpeed: 'normal'
        },
        crawler: { ...config.crawler, submitForms: true, handleApplicationErrors: true }
      },
      'api_testing': {
        scanning: {
          ...config.scanning,
          mode: 'audit_only',
          scanSpeed: 'normal',
          scanTypes: {
            sql_injection: true,
            command_injection: true,
            xxe: true,
            ssrf: true,
            deserialization: true,
            header_injection: true,
            cors: true,
            // Reset others for API focus
            xss: false,
            path_traversal: false,
            file_inclusion: false,
            expression_language: false,
            ldap_injection: false,
            host_header: false,
            cache_poisoning: false,
            csrf: false,
            clickjacking: false,
            dom_xss: false,
            websocket: true,
            tls_issues: true,
            insecure_cookies: false
          }
        },
        headers: { ...config.headers, userAgent: 'BurpSuite/API-Scanner' }
      },
      'stealth_scan': {
        scanning: { ...config.scanning, scanSpeed: 'thorough' },
        advanced: {
          ...config.advanced,
          maxConcurrentRequests: 2,
          throttleRequests: 2000,
          resourcePool: 'normal'
        },
        crawler: { ...config.crawler, maxCrawlTime: 30 }
      }
    };

    if (presets[preset]) {
      setConfig(prev => ({ ...prev, ...presets[preset] }));
    }
  };

  /**
   * Toggle vulnerability category
   */
  const toggleVulnerabilityCategory = (category: string) => {
    const vulnerabilities = VULNERABILITY_CATEGORIES[category];
    const allEnabled = vulnerabilities.every(vuln => config.scanning.scanTypes[vuln]);
    
    const updates = {};
    vulnerabilities.forEach(vuln => {
      updates[`scanning.scanTypes.${vuln}`] = !allEnabled;
    });
    
    Object.entries(updates).forEach(([path, value]) => {
      updateConfig(path, value);
    });
  };

  /**
   * Start Burp Suite scan
   */
  const startScan = async () => {
    if (!config.target.url.trim()) {
      toast.error('Please provide a target URL');
      return;
    }

    // Validation for authentication
    if (config.authentication.enabled && !config.authentication.username) {
      toast.error('Username is required when authentication is enabled');
      return;
    }

    try {
      const execution = await apiClient.executeSecurityTool('burp-suite', config);
      setExecution(execution);
      setActiveTab('output');
      toast.success('Burp Suite scan started successfully');
    } catch (error) {
      toast.error(`Failed to start Burp Suite scan: ${error.message}`);
    }
  };

  /**
   * Stop scan
   */
  const stopScan = async () => {
    if (execution) {
      try {
        await apiClient.stopToolExecution(execution.id);
        toast.success('Burp Suite scan stopped');
      } catch (error) {
        toast.error(`Failed to stop scan: ${error.message}`);
      }
    }
  };

  /**
   * Generate command line
   */
  const generateCommand = () => {
    let cmd = 'java -jar';
    
    // Java heap size
    if (config.advanced.javaHeapSize) {
      cmd += ` -Xmx${config.advanced.javaHeapSize}`;
    }
    
    cmd += ' burpsuite_pro.jar';
    
    // Headless mode for scanning
    cmd += ' --project-file=temp_project.burp';
    cmd += ' --config-file=scan_config.json';
    
    // Target URL
    cmd += ` --target="${config.target.url}"`;
    
    // Scan mode
    if (config.scanning.mode === 'crawl_only') {
      cmd += ' --crawler-only';
    } else if (config.scanning.mode === 'audit_only') {
      cmd += ' --audit-only';
    } else if (config.scanning.mode === 'passive_audit') {
      cmd += ' --passive-audit';
    }
    
    // Report output
    cmd += ` --report-format=${config.reporting.format}`;
    cmd += ' --report-output=burp_report';
    
    // Custom arguments
    if (config.customArgs) {
      cmd += ` ${config.customArgs}`;
    }
    
    return cmd;
  };

  const isScanning = execution?.status === 'running';
  const canStart = connectionStatus === 'connected' && config.target.url.trim() && !isScanning;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center gap-3">
        <div className="flex items-center justify-center w-10 h-10 rounded-lg bg-gradient-to-br from-red-500 to-orange-600">
          <Crown className="h-5 w-5 text-white" />
        </div>
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100">Burp Suite Professional</h2>
          <p className="text-gray-600 dark:text-gray-400">Industry-leading web application security testing platform</p>
        </div>
      </div>

      {/* License Notice */}
      <Card className="border-yellow-200 bg-yellow-50 dark:border-yellow-800 dark:bg-yellow-900/10">
        <CardContent className="pt-6">
          <div className="flex items-start gap-3">
            <AlertTriangle className="h-5 w-5 text-yellow-600 mt-0.5" />
            <div>
              <p className="text-sm font-medium text-yellow-800 dark:text-yellow-200">
                Professional License Required
              </p>
              <p className="text-sm text-yellow-700 dark:text-yellow-300 mt-1">
                Burp Suite Professional license is required for automated scanning. Community Edition supports manual testing only.
                This interface configures automated scans for Pro users.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Status Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Status</p>
                <p className="text-2xl font-bold">
                  {isScanning ? 'Scanning' : 'Ready'}
                </p>
              </div>
              <Activity className={cn("h-8 w-8", isScanning ? "text-green-600" : "text-gray-400")} />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Mode</p>
                <p className="text-2xl font-bold">{SCAN_MODES[config.scanning.mode].name}</p>
              </div>
              {SCAN_MODES[config.scanning.mode].icon}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Speed</p>
                <p className="text-2xl font-bold">{SCAN_SPEEDS[config.scanning.scanSpeed].name}</p>
              </div>
              <Gauge className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Progress</p>
                <p className="text-2xl font-bold">{execution ? `${execution.progress}%` : '0%'}</p>
              </div>
              <BarChart3 className="h-8 w-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Interface */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Shield className="h-5 w-5" />
                Burp Suite Configuration
              </CardTitle>
              <CardDescription>
                Configure comprehensive web application security testing parameters
              </CardDescription>
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => navigator.clipboard.writeText(generateCommand())}
              >
                <Copy className="h-4 w-4 mr-2" />
                Copy Command
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setConfig(prev => ({ 
                  ...prev, 
                  target: { ...prev.target, url: '', scope: [] },
                  headers: { ...prev.headers, customHeaders: {}, cookies: '' },
                  authentication: { ...prev.authentication, enabled: false, username: '', password: '' },
                  customArgs: ''
                }))}
              >
                <RotateCcw className="h-4 w-4 mr-2" />
                Reset
              </Button>
              {isScanning ? (
                <Button variant="destructive" size="sm" onClick={stopScan}>
                  <Square className="h-4 w-4 mr-2" />
                  Stop Scan
                </Button>
              ) : (
                <Button 
                  size="sm" 
                  onClick={startScan}
                  disabled={!canStart}
                >
                  <Play className="h-4 w-4 mr-2" />
                  Start Scan
                </Button>
              )}
            </div>
          </div>
        </CardHeader>

        <CardContent>
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="configure">Configure</TabsTrigger>
              <TabsTrigger value="output">Output</TabsTrigger>
              <TabsTrigger value="results">Results</TabsTrigger>
              <TabsTrigger value="command">Command</TabsTrigger>
            </TabsList>

            <TabsContent value="configure" className="space-y-6">
              {/* Quick Presets */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Quick Presets</h3>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                  <Button variant="outline" onClick={() => applyPreset('quick_scan')}>
                    <Zap className="h-4 w-4 mr-2" />
                    Quick Scan
                  </Button>
                  <Button variant="outline" onClick={() => applyPreset('comprehensive_audit')}>
                    <Search className="h-4 w-4 mr-2" />
                    Comprehensive
                  </Button>
                  <Button variant="outline" onClick={() => applyPreset('owasp_top_10')}>
                    <Shield className="h-4 w-4 mr-2" />
                    OWASP Top 10
                  </Button>
                  <Button variant="outline" onClick={() => applyPreset('authenticated_scan')}>
                    <Users className="h-4 w-4 mr-2" />
                    Authenticated
                  </Button>
                  <Button variant="outline" onClick={() => applyPreset('api_testing')}>
                    <Code className="h-4 w-4 mr-2" />
                    API Testing
                  </Button>
                  <Button variant="outline" onClick={() => applyPreset('stealth_scan')}>
                    <Eye className="h-4 w-4 mr-2" />
                    Stealth Scan
                  </Button>
                </div>
              </div>

              {/* Target Configuration */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Target Configuration</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Target URL</label>
                    <Input
                      value={config.target.url}
                      onChange={(e) => updateConfig('target.url', e.target.value)}
                      placeholder="https://example.com"
                    />
                  </div>

                  <div className="space-y-2">
                    <label className="text-sm font-medium">Protocol</label>
                    <Select value={config.target.protocol} onValueChange={(value) => updateConfig('target.protocol', value)}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="both">HTTP and HTTPS</SelectItem>
                        <SelectItem value="http">HTTP Only</SelectItem>
                        <SelectItem value="https">HTTPS Only</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">Additional Scope URLs</label>
                  <Textarea
                    value={config.target.scope.join('\n')}
                    onChange={(e) => updateConfig('target.scope', e.target.value.split('\n').filter(url => url.trim()))}
                    placeholder="https://api.example.com&#10;https://admin.example.com"
                    className="min-h-[80px]"
                  />
                </div>

                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="includeSubdomains"
                    checked={config.target.includeSubdomains}
                    onChange={(e) => updateConfig('target.includeSubdomains', e.target.checked)}
                    className="rounded"
                  />
                  <label htmlFor="includeSubdomains" className="text-sm font-medium">Include Subdomains</label>
                </div>
              </div>

              {/* Scan Configuration */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Scan Configuration</h3>
                
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Scan Mode</label>
                    <Select value={config.scanning.mode} onValueChange={(value) => updateConfig('scanning.mode', value)}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {Object.entries(SCAN_MODES).map(([key, mode]) => (
                          <SelectItem key={key} value={key}>
                            <div className="flex items-center gap-2">
                              {mode.icon}
                              <div>
                                <div className="font-medium">{mode.name}</div>
                                <div className="text-xs text-gray-500">{mode.description}</div>
                              </div>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <label className="text-sm font-medium">Scan Speed</label>
                    <Select value={config.scanning.scanSpeed} onValueChange={(value) => updateConfig('scanning.scanSpeed', value)}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {Object.entries(SCAN_SPEEDS).map(([key, speed]) => (
                          <SelectItem key={key} value={key}>
                            <div>
                              <div className="font-medium">{speed.name}</div>
                              <div className="text-xs text-gray-500">{speed.description} • {speed.time}</div>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <label className="text-sm font-medium">Audit Accuracy</label>
                    <Select value={config.scanning.auditAccuracy} onValueChange={(value) => updateConfig('scanning.auditAccuracy', value)}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {Object.entries(AUDIT_ACCURACY).map(([key, accuracy]) => (
                          <SelectItem key={key} value={key}>
                            <div>
                              <div className="font-medium">{accuracy.name}</div>
                              <div className="text-xs text-gray-500">{accuracy.description}</div>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>

              {/* Vulnerability Types */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Vulnerability Types</h3>
                
                <div className="space-y-3">
                  {Object.entries(VULNERABILITY_CATEGORIES).map(([category, vulnerabilities]) => {
                    const allEnabled = vulnerabilities.every(vuln => config.scanning.scanTypes[vuln]);
                    return (
                      <div key={category} className="space-y-2">
                        <Button
                          variant={allEnabled ? "default" : "outline"}
                          size="sm"
                          onClick={() => toggleVulnerabilityCategory(category)}
                          className="w-full justify-start"
                        >
                          {category.replace('_', ' ').toUpperCase()} ({vulnerabilities.length} tests)
                        </Button>
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-2 ml-4">
                          {vulnerabilities.map(vuln => (
                            <div key={vuln} className="flex items-center space-x-2">
                              <input
                                type="checkbox"
                                id={vuln}
                                checked={config.scanning.scanTypes[vuln]}
                                onChange={(e) => updateConfig(`scanning.scanTypes.${vuln}`, e.target.checked)}
                                className="rounded"
                              />
                              <label htmlFor={vuln} className="text-xs font-medium">
                                {vuln.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                              </label>
                            </div>
                          ))}
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>

              {/* Authentication Configuration */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Authentication Configuration</h3>
                
                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="authEnabled"
                    checked={config.authentication.enabled}
                    onChange={(e) => updateConfig('authentication.enabled', e.target.checked)}
                    className="rounded"
                  />
                  <label htmlFor="authEnabled" className="text-sm font-medium">Enable Authentication</label>
                </div>

                {config.authentication.enabled && (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <label className="text-sm font-medium">Authentication Type</label>
                      <Select value={config.authentication.type} onValueChange={(value) => updateConfig('authentication.type', value)}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="form">Form-based</SelectItem>
                          <SelectItem value="http_basic">HTTP Basic</SelectItem>
                          <SelectItem value="http_digest">HTTP Digest</SelectItem>
                          <SelectItem value="ntlm">NTLM</SelectItem>
                          <SelectItem value="custom">Custom</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <label className="text-sm font-medium">Login URL</label>
                      <Input
                        value={config.authentication.loginUrl}
                        onChange={(e) => updateConfig('authentication.loginUrl', e.target.value)}
                        placeholder="https://example.com/login"
                      />
                    </div>

                    <div className="space-y-2">
                      <label className="text-sm font-medium">Username</label>
                      <Input
                        value={config.authentication.username}
                        onChange={(e) => updateConfig('authentication.username', e.target.value)}
                        placeholder="testuser"
                      />
                    </div>

                    <div className="space-y-2">
                      <label className="text-sm font-medium">Password</label>
                      <Input
                        type="password"
                        value={config.authentication.password}
                        onChange={(e) => updateConfig('authentication.password', e.target.value)}
                        placeholder="password"
                      />
                    </div>

                    {config.authentication.type === 'form' && (
                      <>
                        <div className="space-y-2">
                          <label className="text-sm font-medium">Username Field</label>
                          <Input
                            value={config.authentication.usernameField}
                            onChange={(e) => updateConfig('authentication.usernameField', e.target.value)}
                            placeholder="username"
                          />
                        </div>

                        <div className="space-y-2">
                          <label className="text-sm font-medium">Password Field</label>
                          <Input
                            value={config.authentication.passwordField}
                            onChange={(e) => updateConfig('authentication.passwordField', e.target.value)}
                            placeholder="password"
                          />
                        </div>
                      </>
                    )}

                    <div className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        id="sessionHandling"
                        checked={config.authentication.sessionHandling}
                        onChange={(e) => updateConfig('authentication.sessionHandling', e.target.checked)}
                        className="rounded"
                      />
                      <label htmlFor="sessionHandling" className="text-sm font-medium">Advanced Session Handling</label>
                    </div>
                  </div>
                )}
              </div>

              {/* Crawler Configuration */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Crawler Configuration</h3>
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Max Crawl Time (min)</label>
                    <Input
                      type="number"
                      value={config.crawler.maxCrawlTime}
                      onChange={(e) => updateConfig('crawler.maxCrawlTime', parseInt(e.target.value) || 60)}
                      min="1"
                      max="480"
                    />
                  </div>

                  <div className="space-y-2">
                    <label className="text-sm font-medium">Max Unique Locations</label>
                    <Input
                      type="number"
                      value={config.crawler.maxUniqueLocations}
                      onChange={(e) => updateConfig('crawler.maxUniqueLocations', parseInt(e.target.value) || 10000)}
                      min="100"
                      max="100000"
                    />
                  </div>

                  <div className="space-y-2">
                    <label className="text-sm font-medium">Max Depth</label>
                    <Input
                      type="number"
                      value={config.crawler.maxDepth}
                      onChange={(e) => updateConfig('crawler.maxDepth', parseInt(e.target.value) || 10)}
                      min="1"
                      max="20"
                    />
                  </div>

                  <div className="space-y-2">
                    <label className="text-sm font-medium">Max Parameters</label>
                    <Input
                      type="number"
                      value={config.crawler.maxParameters}
                      onChange={(e) => updateConfig('crawler.maxParameters', parseInt(e.target.value) || 65)}
                      min="1"
                      max="1000"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="handleApplicationErrors"
                      checked={config.crawler.handleApplicationErrors}
                      onChange={(e) => updateConfig('crawler.handleApplicationErrors', e.target.checked)}
                      className="rounded"
                    />
                    <label htmlFor="handleApplicationErrors" className="text-sm font-medium">Handle App Errors</label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="useAdvancedJS"
                      checked={config.crawler.useAdvancedJS}
                      onChange={(e) => updateConfig('crawler.useAdvancedJS', e.target.checked)}
                      className="rounded"
                    />
                    <label htmlFor="useAdvancedJS" className="text-sm font-medium">Advanced JS</label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="submitForms"
                      checked={config.crawler.submitForms}
                      onChange={(e) => updateConfig('crawler.submitForms', e.target.checked)}
                      className="rounded"
                    />
                    <label htmlFor="submitForms" className="text-sm font-medium">Submit Forms</label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="parseRobotsTxt"
                      checked={config.crawler.parseRobotsTxt}
                      onChange={(e) => updateConfig('crawler.parseRobotsTxt', e.target.checked)}
                      className="rounded"
                    />
                    <label htmlFor="parseRobotsTxt" className="text-sm font-medium">Parse robots.txt</label>
                  </div>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="output" className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold">Real-time Output</h3>
                <div className="flex items-center gap-2">
                  {isScanning && (
                    <Badge variant="secondary" className="animate-pulse">
                      <Activity className="h-3 w-3 mr-1" />
                      Scanning in progress...
                    </Badge>
                  )}
                  <Button variant="outline" size="sm">
                    <Download className="h-4 w-4 mr-2" />
                    Export Output
                  </Button>
                </div>
              </div>
              
              {execution?.progress !== undefined && (
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Progress</span>
                    <span>{execution.progress}%</span>
                  </div>
                  <Progress value={execution.progress} className="w-full" />
                </div>
              )}

              <Card>
                <CardContent className="p-4">
                  <div className="bg-gray-900 dark:bg-gray-800 rounded-lg p-4 min-h-[400px] font-mono text-sm overflow-auto">
                    <div className="text-green-400">
                      {execution?.output || 'Burp Suite output will appear here when scanning starts...'}
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="results" className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold">Security Assessment Results</h3>
                <div className="flex items-center gap-2">
                  <Button variant="outline" size="sm">
                    <Download className="h-4 w-4 mr-2" />
                    Export Report
                  </Button>
                  <Button variant="outline" size="sm">
                    <Filter className="h-4 w-4 mr-2" />
                    Filter Results
                  </Button>
                </div>
              </div>

              {execution?.results ? (
                <div className="space-y-4">
                  {/* Results Summary */}
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <Card>
                      <CardContent className="pt-6">
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="text-sm font-medium text-gray-600 dark:text-gray-400">High Severity</p>
                            <p className="text-2xl font-bold text-red-600">{execution.results.high_severity || 0}</p>
                          </div>
                          <AlertTriangle className="h-8 w-8 text-red-600" />
                        </div>
                      </CardContent>
                    </Card>

                    <Card>
                      <CardContent className="pt-6">
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Medium Severity</p>
                            <p className="text-2xl font-bold text-yellow-600">{execution.results.medium_severity || 0}</p>
                          </div>
                          <AlertTriangle className="h-8 w-8 text-yellow-600" />
                        </div>
                      </CardContent>
                    </Card>

                    <Card>
                      <CardContent className="pt-6">
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Low Severity</p>
                            <p className="text-2xl font-bold text-blue-600">{execution.results.low_severity || 0}</p>
                          </div>
                          <Info className="h-8 w-8 text-blue-600" />
                        </div>
                      </CardContent>
                    </Card>

                    <Card>
                      <CardContent className="pt-6">
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Pages Crawled</p>
                            <p className="text-2xl font-bold">{execution.results.pages_crawled || 0}</p>
                          </div>
                          <Spider className="h-8 w-8 text-green-600" />
                        </div>
                      </CardContent>
                    </Card>
                  </div>

                  {/* Vulnerability Results Table */}
                  <Card>
                    <CardContent className="p-0">
                      <div className="overflow-x-auto">
                        <table className="w-full">
                          <thead className="border-b">
                            <tr className="text-left">
                              <th className="p-4 font-medium">Vulnerability</th>
                              <th className="p-4 font-medium">Severity</th>
                              <th className="p-4 font-medium">Confidence</th>
                              <th className="p-4 font-medium">Location</th>
                              <th className="p-4 font-medium">Type</th>
                            </tr>
                          </thead>
                          <tbody>
                            {execution.results.vulnerabilities?.map((vuln, index) => (
                              <tr key={index} className="border-b hover:bg-gray-50 dark:hover:bg-gray-800">
                                <td className="p-4 font-medium">{vuln.name}</td>
                                <td className="p-4">
                                  <Badge 
                                    variant={vuln.severity === 'High' ? 'destructive' : 
                                            vuln.severity === 'Medium' ? 'default' : 'secondary'}
                                  >
                                    {vuln.severity}
                                  </Badge>
                                </td>
                                <td className="p-4">
                                  <Badge variant="outline">
                                    {vuln.confidence}
                                  </Badge>
                                </td>
                                <td className="p-4 font-mono text-sm">{vuln.url}</td>
                                <td className="p-4 text-sm">{vuln.type}</td>
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              ) : (
                <Card>
                  <CardContent className="p-8 text-center">
                    <Shield className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">No Results Yet</h3>
                    <p className="text-gray-600 dark:text-gray-400">
                      Start a security assessment to see vulnerability findings here. Burp Suite will provide detailed vulnerability analysis and remediation guidance.
                    </p>
                  </CardContent>
                </Card>
              )}
            </TabsContent>

            <TabsContent value="command" className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold">Generated Command</h3>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => navigator.clipboard.writeText(generateCommand())}
                >
                  <Copy className="h-4 w-4 mr-2" />
                  Copy Command
                </Button>
              </div>

              <Card>
                <CardContent className="p-4">
                  <div className="bg-gray-900 dark:bg-gray-800 rounded-lg p-4 overflow-x-auto">
                    <code className="text-green-400 whitespace-pre-wrap break-all">
                      {generateCommand()}
                    </code>
                  </div>
                </CardContent>
              </Card>

              <div className="space-y-4">
                <h4 className="font-semibold">Command Explanation</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                  <div>
                    <h5 className="font-medium mb-2">Java Options</h5>
                    <ul className="space-y-1 text-gray-600 dark:text-gray-400">
                      <li>• <code>-Xmx</code>: Maximum heap size for Java</li>
                      <li>• <code>-jar</code>: Execute JAR file</li>
                      <li>• <code>burpsuite_pro.jar</code>: Burp Suite Professional</li>
                      <li>• <code>--project-file</code>: Project file location</li>
                    </ul>
                  </div>
                  <div>
                    <h5 className="font-medium mb-2">Scan Options</h5>
                    <ul className="space-y-1 text-gray-600 dark:text-gray-400">
                      <li>• <code>--target</code>: Target URL to scan</li>
                      <li>• <code>--config-file</code>: Scan configuration</li>
                      <li>• <code>--crawler-only</code>: Discovery mode only</li>
                      <li>• <code>--audit-only</code>: Security testing only</li>
                    </ul>
                  </div>
                  <div>
                    <h5 className="font-medium mb-2">Reporting</h5>
                    <ul className="space-y-1 text-gray-600 dark:text-gray-400">
                      <li>• <code>--report-format</code>: Output format (HTML/XML/JSON)</li>
                      <li>• <code>--report-output</code>: Report file name</li>
                      <li>• Includes vulnerability details and remediation</li>
                      <li>• Professional-grade security assessment</li>
                    </ul>
                  </div>
                  <div>
                    <h5 className="font-medium mb-2">Professional Features</h5>
                    <ul className="space-y-1 text-gray-600 dark:text-gray-400">
                      <li>• Advanced crawler with JavaScript support</li>
                      <li>• Comprehensive vulnerability scanner</li>
                      <li>• Session handling and authentication</li>
                      <li>• Enterprise-grade reporting</li>
                    </ul>
                  </div>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}