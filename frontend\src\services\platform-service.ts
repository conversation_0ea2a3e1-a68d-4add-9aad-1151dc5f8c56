/**
 * Platform Service - Unified interface for Electron and Web environments
 * Provides seamless switching between desktop and web APIs
 */

import { electronService } from './electron-service';
import { webService } from './web-service';

// ===== TYPE DEFINITIONS =====

export interface PlatformServiceAPI {
  // Environment detection
  isElectron(): boolean;
  isWeb(): boolean;
  
  // App info
  getName(): string;
  getVersion(): string;
  getPlatform(): string;
  getArch(): string;
  
  // Notifications
  showNotification(title: string, options?: NotificationOptions): Promise<void>;
  
  // Storage
  getItem(key: string): string | null;
  setItem(key: string, value: string): void;
  removeItem(key: string): void;
  clear(): void;
  
  // File operations
  downloadFile(data: Blob, filename: string): void;
  downloadText(text: string, filename: string, mimeType?: string): void;
  uploadFile(accept?: string): Promise<File | null>;
  
  // Clipboard operations
  copyToClipboard(text: string): Promise<boolean>;
  
  // URL operations
  openExternal(url: string): void;
  
  // Feature detection
  supportsFeature(feature: string): boolean;
  
  // Platform-specific capabilities
  getCapabilities(): {
    hasWindowControls: boolean;
    hasFileSystem: boolean;
    hasNotifications: boolean;
    hasClipboard: boolean;
    isMobile: boolean;
    isDesktop: boolean;
  };
}

// ===== PLATFORM SERVICE IMPLEMENTATION =====

class PlatformService implements PlatformServiceAPI {
  private _isElectron: boolean = false;
  private _isWeb: boolean = true;
  private _isMobile: boolean;

  constructor() {
    this._isMobile = this.detectMobile();

    console.log('🔧 Platform Service initialized:', {
      isElectron: this._isElectron,
      isWeb: this._isWeb,
      isMobile: this._isMobile,
      platform: this.getPlatform()
    });
  }

  // ===== ENVIRONMENT DETECTION =====

  isElectron(): boolean {
    return false;
  }

  isWeb(): boolean {
    return true;
  }

  private detectMobile(): boolean {
    if (typeof window === 'undefined') return false;
    
    // Check for touch capability and screen size
    const hasTouchScreen = 'ontouchstart' in window || navigator.maxTouchPoints > 0;
    const isSmallScreen = window.innerWidth <= 768;
    const isMobileUserAgent = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    
    return hasTouchScreen && (isSmallScreen || isMobileUserAgent);
  }

  // ===== SERVICE SELECTION =====

  private getService() {
    return this._isElectron ? electronService : webService;
  }

  // ===== UNIFIED API =====

  getName(): string {
    return this.getService().getName();
  }

  getVersion(): string {
    return this.getService().getVersion();
  }

  getPlatform(): string {
    return this.getService().getPlatform();
  }

  getArch(): string {
    return this.getService().getArch();
  }

  // ===== NOTIFICATIONS =====

  async showNotification(title: string, options?: NotificationOptions): Promise<void> {
    try {
      if (this._isElectron) {
        // Electron notifications
        electronService.showNotification(title, options);
      } else {
        // Web notifications
        await webService.showNotification(title, options);
      }
    } catch (error) {
      console.error('Failed to show notification:', error);
    }
  }

  // ===== STORAGE =====

  getItem(key: string): string | null {
    return this.getService().getItem(key);
  }

  setItem(key: string, value: string): void {
    this.getService().setItem(key, value);
  }

  removeItem(key: string): void {
    this.getService().removeItem(key);
  }

  clear(): void {
    if (this._isElectron) {
      // Electron store clear (if available)
      console.warn('Electron store clear not implemented');
    } else {
      webService.clear();
    }
  }

  // ===== FILE OPERATIONS =====

  downloadFile(data: Blob, filename: string): void {
    if (this._isElectron) {
      // Use Electron file save dialog
      console.warn('Electron file download not implemented, falling back to web');
      webService.downloadFile(data, filename);
    } else {
      webService.downloadFile(data, filename);
    }
  }

  downloadText(text: string, filename: string, mimeType: string = 'text/plain'): void {
    if (this._isElectron) {
      // Use Electron file save dialog
      console.warn('Electron text download not implemented, falling back to web');
      webService.downloadText(text, filename, mimeType);
    } else {
      webService.downloadText(text, filename, mimeType);
    }
  }

  async uploadFile(accept?: string): Promise<File | null> {
    if (this._isElectron) {
      // Use Electron file open dialog
      console.warn('Electron file upload not implemented, falling back to web');
      return webService.uploadFile(accept);
    } else {
      return webService.uploadFile(accept);
    }
  }

  // ===== CLIPBOARD OPERATIONS =====

  async copyToClipboard(text: string): Promise<boolean> {
    if (this._isElectron) {
      // Use Electron clipboard
      console.warn('Electron clipboard not implemented, falling back to web');
      return webService.copyToClipboard(text);
    } else {
      return webService.copyToClipboard(text);
    }
  }

  // ===== URL OPERATIONS =====

  openExternal(url: string): void {
    if (this._isElectron) {
      electronService.openExternal(url);
    } else {
      webService.openExternal(url);
    }
  }

  // ===== FEATURE DETECTION =====

  supportsFeature(feature: string): boolean {
    if (this._isElectron) {
      // Electron generally supports most features
      switch (feature) {
        case 'windowControls':
          return true;
        case 'fileSystem':
          return true;
        case 'notifications':
          return true;
        case 'clipboard':
          return true;
        default:
          return true;
      }
    } else {
      return webService.supportsFeature(feature);
    }
  }

  // ===== PLATFORM CAPABILITIES =====

  getCapabilities() {
    return {
      hasWindowControls: this._isElectron,
      hasFileSystem: this._isElectron,
      hasNotifications: this.supportsFeature('notifications'),
      hasClipboard: this.supportsFeature('clipboard'),
      isMobile: this._isMobile,
      isDesktop: !this._isMobile
    };
  }

  // ===== UTILITY METHODS =====

  /**
   * Get environment info for debugging
   */
  getEnvironmentInfo() {
    const capabilities = this.getCapabilities();
    
    return {
      platform: this.getPlatform(),
      arch: this.getArch(),
      isElectron: this._isElectron,
      isWeb: this._isWeb,
      isMobile: this._isMobile,
      capabilities,
      userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : 'Unknown',
      screenSize: typeof window !== 'undefined' ? {
        width: window.innerWidth,
        height: window.innerHeight
      } : null
    };
  }

  /**
   * Log environment info for debugging
   */
  logEnvironmentInfo() {
    console.log('🔧 Platform Service Environment:', this.getEnvironmentInfo());
  }

  /**
   * Check if running in development mode
   */
  isDevelopment(): boolean {
    return process.env.NODE_ENV === 'development';
  }

  /**
   * Check if running in production mode
   */
  isProduction(): boolean {
    return process.env.NODE_ENV === 'production';
  }

  /**
   * Get backend URL based on environment
   */
  getBackendUrl(): string {
    return __BACKEND_URL__ || 'http://************:8090';
  }

  /**
   * Check if backend is remote
   */
  isRemoteBackend(): boolean {
    const backendUrl = this.getBackendUrl();
    return !backendUrl.includes('localhost') && !backendUrl.includes('127.0.0.1');
  }
}

// ===== EXPORT =====

export const platformService = new PlatformService();
export default platformService;

// ===== GLOBAL DEBUG HELPER =====

if (typeof window !== 'undefined' && platformService.isDevelopment()) {
  (window as any).platformService = platformService;
  console.log('🛠️ Platform Service available at window.platformService');
}
