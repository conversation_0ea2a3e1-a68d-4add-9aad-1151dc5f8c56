#!/usr/bin/env python3
"""
Production Readiness Test - Real Security Tool Integration
This script performs HONEST, comprehensive testing of actual security capabilities
"""

import requests
import subprocess
import json
import time
import os
import sys
from datetime import datetime
from pathlib import Path

class ProductionReadinessTest:
    def __init__(self):
        self.base_url = "http://localhost:8000/api"
        self.results = {}
        self.real_vulnerabilities_found = []
        
    def log(self, message, level="INFO"):
        timestamp = datetime.now().strftime("%H:%M:%S")
        print(f"[{timestamp}] {level}: {message}")
        
    def test_real_tool_availability(self):
        """Test if security tools are actually installed and functional"""
        self.log("🔧 TESTING REAL SECURITY TOOL AVAILABILITY", "TEST")
        
        tools_to_test = {
            "nmap": ["nmap", "--version"],
            "nuclei": ["nuclei", "-version"],
            "sqlmap": ["sqlmap", "--version"],
            "nikto": ["nikto", "-Version"],
            "dirb": ["dirb"],
            "gobuster": ["gobuster", "version"],
            "ffuf": ["ffuf", "-V"],
            "hydra": ["hydra", "-h"],
            "john": ["john", "--test=0"],
            "hashcat": ["hashcat", "--version"]
        }
        
        real_tools = {}
        for tool_name, cmd in tools_to_test.items():
            try:
                result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
                if result.returncode == 0 or "version" in result.stdout.lower() or "help" in result.stderr.lower():
                    real_tools[tool_name] = {
                        "available": True,
                        "version": result.stdout.strip()[:100] if result.stdout else result.stderr.strip()[:100]
                    }
                    self.log(f"✅ {tool_name}: Available")
                else:
                    real_tools[tool_name] = {"available": False, "error": "Command failed"}
                    self.log(f"❌ {tool_name}: Command failed")
            except subprocess.TimeoutExpired:
                real_tools[tool_name] = {"available": False, "error": "Timeout"}
                self.log(f"❌ {tool_name}: Timeout")
            except FileNotFoundError:
                real_tools[tool_name] = {"available": False, "error": "Not installed"}
                self.log(f"❌ {tool_name}: Not installed")
            except Exception as e:
                real_tools[tool_name] = {"available": False, "error": str(e)}
                self.log(f"❌ {tool_name}: {str(e)}")
        
        available_count = sum(1 for tool in real_tools.values() if tool["available"])
        total_count = len(real_tools)
        
        self.results["tool_availability"] = {
            "tools": real_tools,
            "available_count": available_count,
            "total_count": total_count,
            "percentage": (available_count / total_count) * 100 if total_count > 0 else 0
        }
        
        self.log(f"📊 Tool Availability: {available_count}/{total_count} ({self.results['tool_availability']['percentage']:.1f}%)")
        return real_tools
    
    def test_real_nmap_scan(self):
        """Perform actual nmap scan to test real functionality"""
        self.log("🎯 TESTING REAL NMAP EXECUTION", "TEST")
        
        try:
            # Test against localhost (safe target)
            cmd = ["nmap", "-sS", "-F", "--top-ports", "100", "127.0.0.1"]
            self.log(f"Executing: {' '.join(cmd)}")
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
            
            if result.returncode == 0:
                # Parse nmap output for open ports
                open_ports = []
                for line in result.stdout.split('\n'):
                    if '/tcp' in line and 'open' in line:
                        port = line.split('/')[0].strip()
                        service = line.split()[-1] if len(line.split()) > 2 else "unknown"
                        open_ports.append({"port": port, "service": service})
                
                self.results["nmap_scan"] = {
                    "success": True,
                    "target": "127.0.0.1",
                    "open_ports": open_ports,
                    "raw_output": result.stdout,
                    "execution_time": "< 60s"
                }
                
                self.log(f"✅ Nmap scan successful: {len(open_ports)} open ports found")
                for port in open_ports:
                    self.log(f"   🔓 Port {port['port']}: {port['service']}")
                    
                return True
            else:
                self.results["nmap_scan"] = {
                    "success": False,
                    "error": result.stderr,
                    "stdout": result.stdout
                }
                self.log(f"❌ Nmap scan failed: {result.stderr}")
                return False
                
        except subprocess.TimeoutExpired:
            self.results["nmap_scan"] = {"success": False, "error": "Timeout after 60s"}
            self.log("❌ Nmap scan timeout")
            return False
        except Exception as e:
            self.results["nmap_scan"] = {"success": False, "error": str(e)}
            self.log(f"❌ Nmap scan error: {str(e)}")
            return False
    
    def test_real_nuclei_scan(self):
        """Perform actual nuclei vulnerability scan"""
        self.log("🔍 TESTING REAL NUCLEI EXECUTION", "TEST")
        
        try:
            # Test against a controlled target (httpbin.org is safe for testing)
            cmd = ["nuclei", "-u", "https://httpbin.org", "-t", "exposures/", "-silent", "-jsonl"]
            self.log(f"Executing: {' '.join(cmd)}")
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=120)
            
            vulnerabilities = []
            if result.stdout:
                for line in result.stdout.strip().split('\n'):
                    if line.strip():
                        try:
                            vuln = json.loads(line)
                            vulnerabilities.append({
                                "template_id": vuln.get("template-id"),
                                "name": vuln.get("info", {}).get("name"),
                                "severity": vuln.get("info", {}).get("severity"),
                                "matched_at": vuln.get("matched-at")
                            })
                        except json.JSONDecodeError:
                            continue
            
            self.results["nuclei_scan"] = {
                "success": True,
                "target": "https://httpbin.org",
                "vulnerabilities_found": len(vulnerabilities),
                "vulnerabilities": vulnerabilities,
                "raw_output": result.stdout,
                "execution_time": "< 120s"
            }
            
            self.log(f"✅ Nuclei scan successful: {len(vulnerabilities)} potential issues found")
            for vuln in vulnerabilities[:3]:  # Show first 3
                self.log(f"   🚨 {vuln['name']} ({vuln['severity']})")
                
            return True
            
        except subprocess.TimeoutExpired:
            self.results["nuclei_scan"] = {"success": False, "error": "Timeout after 120s"}
            self.log("❌ Nuclei scan timeout")
            return False
        except Exception as e:
            self.results["nuclei_scan"] = {"success": False, "error": str(e)}
            self.log(f"❌ Nuclei scan error: {str(e)}")
            return False
    
    def test_backend_tool_integration(self):
        """Test if backend can actually execute tools through our API"""
        self.log("🔗 TESTING BACKEND TOOL INTEGRATION", "TEST")
        
        try:
            # Check if backend reports real tool availability
            response = requests.get(f"{self.base_url}/tools", timeout=10)
            if response.status_code == 200:
                tools_data = response.json()
                
                # Compare with actual tool availability
                real_tools = self.results.get("tool_availability", {}).get("tools", {})
                backend_tools = tools_data.get("tools", [])
                
                accuracy_count = 0
                for backend_tool in backend_tools:
                    tool_name = backend_tool.get("name", "").lower()
                    backend_available = backend_tool.get("available", False)
                    real_available = real_tools.get(tool_name, {}).get("available", False)
                    
                    if backend_available == real_available:
                        accuracy_count += 1
                        self.log(f"✅ {tool_name}: Backend accuracy correct")
                    else:
                        self.log(f"❌ {tool_name}: Backend says {backend_available}, reality is {real_available}")
                
                accuracy = (accuracy_count / len(backend_tools)) * 100 if backend_tools else 0
                
                self.results["backend_tool_integration"] = {
                    "success": True,
                    "accuracy_percentage": accuracy,
                    "accurate_tools": accuracy_count,
                    "total_tools": len(backend_tools),
                    "backend_tools": backend_tools
                }
                
                self.log(f"📊 Backend Tool Accuracy: {accuracy:.1f}% ({accuracy_count}/{len(backend_tools)})")
                return accuracy > 80  # Consider good if >80% accurate
            else:
                self.results["backend_tool_integration"] = {
                    "success": False,
                    "error": f"HTTP {response.status_code}"
                }
                return False
                
        except Exception as e:
            self.results["backend_tool_integration"] = {"success": False, "error": str(e)}
            self.log(f"❌ Backend tool integration error: {str(e)}")
            return False
    
    def test_real_ai_integration(self):
        """Test actual AI service connectivity and functionality"""
        self.log("🤖 TESTING REAL AI INTEGRATION", "TEST")
        
        ai_test_results = {}
        
        # Test OpenAI
        if os.getenv('OPENAI_API_KEY'):
            try:
                import openai
                client = openai.OpenAI()
                
                response = client.chat.completions.create(
                    model="gpt-3.5-turbo",
                    messages=[{"role": "user", "content": "What is SQL injection? Respond in exactly 10 words."}],
                    max_tokens=20
                )
                
                ai_test_results["openai"] = {
                    "available": True,
                    "response": response.choices[0].message.content,
                    "model": "gpt-3.5-turbo"
                }
                self.log("✅ OpenAI: Real connectivity confirmed")
            except Exception as e:
                ai_test_results["openai"] = {"available": False, "error": str(e)}
                self.log(f"❌ OpenAI: {str(e)}")
        else:
            ai_test_results["openai"] = {"available": False, "error": "No API key"}
            self.log("❌ OpenAI: No API key configured")
        
        # Test through backend API
        try:
            response = requests.get(f"{self.base_url}/ai/providers", timeout=10)
            if response.status_code == 200:
                providers_data = response.json()
                backend_providers = providers_data.get("providers", [])
                
                # Compare backend claims with reality
                accurate_providers = 0
                for provider in backend_providers:
                    name = provider.get("name", "").lower()
                    backend_available = provider.get("available", False)
                    real_available = ai_test_results.get(name, {}).get("available", False)
                    
                    if backend_available == real_available:
                        accurate_providers += 1
                
                accuracy = (accurate_providers / len(backend_providers)) * 100 if backend_providers else 0
                
                self.results["ai_integration"] = {
                    "real_providers": ai_test_results,
                    "backend_accuracy": accuracy,
                    "backend_providers": backend_providers
                }
                
                self.log(f"📊 AI Provider Accuracy: {accuracy:.1f}%")
                return accuracy > 50  # At least some AI working
            else:
                self.results["ai_integration"] = {"success": False, "error": "Backend API failed"}
                return False
                
        except Exception as e:
            self.results["ai_integration"] = {"success": False, "error": str(e)}
            self.log(f"❌ AI integration test error: {str(e)}")
            return False
    
    def test_complete_real_workflow(self):
        """Test complete scan workflow with real vulnerability detection"""
        self.log("🎯 TESTING COMPLETE REAL WORKFLOW", "TEST")
        
        try:
            # Create campaign
            campaign_data = {
                "name": f"Production Test {datetime.now().strftime('%Y%m%d_%H%M%S')}",
                "description": "Real production readiness validation with actual security testing",
                "targets": ["127.0.0.1", "httpbin.org"],
                "scan_types": ["port_scan", "vulnerability"],
                "priority": "high"
            }
            
            response = requests.post(f"{self.base_url}/campaigns", json=campaign_data, timeout=10)
            if response.status_code not in [200, 201]:
                raise Exception(f"Campaign creation failed: {response.status_code}")
            
            campaign = response.json()['data']
            campaign_id = campaign['id']
            self.log(f"✅ Real campaign created: {campaign_id}")
            
            # Create scan that should use real tools
            scan_data = {
                "campaign_id": campaign_id,
                "target": "127.0.0.1",
                "scan_type": "port_scan",
                "configuration": {
                    "tools": ["nmap", "nuclei"],
                    "ports": "1-1000",
                    "intensity": "normal",
                    "real_execution": True  # Flag to request real execution
                }
            }
            
            response = requests.post(f"{self.base_url}/scans", json=scan_data, timeout=10)
            if response.status_code not in [200, 201]:
                raise Exception(f"Scan creation failed: {response.status_code}")
            
            scan = response.json()['data']
            scan_id = scan['id']
            self.log(f"✅ Real scan created: {scan_id}")
            
            # Start scan and monitor for REAL results
            response = requests.post(f"{self.base_url}/scans/{scan_id}/start", timeout=10)
            if response.status_code not in [200, 201]:
                raise Exception(f"Scan start failed: {response.status_code}")
            
            self.log("⏳ Monitoring real scan execution...")
            
            # Monitor scan progress for actual completion
            for i in range(30):  # Monitor for up to 5 minutes
                time.sleep(10)
                response = requests.get(f"{self.base_url}/scans/{scan_id}", timeout=5)
                if response.status_code == 200:
                    scan_status = response.json()['data']
                    status = scan_status.get('status')
                    progress = scan_status.get('progress', 0)
                    
                    self.log(f"   📊 Scan progress: {status} ({progress}%)")
                    
                    if status == 'completed':
                        # Check if real vulnerabilities were found
                        vulnerabilities = scan_status.get('vulnerabilities', [])
                        results = scan_status.get('results', {})
                        
                        self.results["real_workflow"] = {
                            "success": True,
                            "campaign_id": campaign_id,
                            "scan_id": scan_id,
                            "vulnerabilities_found": len(vulnerabilities),
                            "real_execution": bool(results.get('raw_output')),
                            "execution_time": scan_status.get('duration_seconds', 0),
                            "results": results
                        }
                        
                        self.log(f"✅ Real workflow completed: {len(vulnerabilities)} vulnerabilities found")
                        return True
                    elif status == 'failed':
                        self.log("❌ Real scan failed")
                        break
                else:
                    self.log(f"❌ Failed to get scan status: {response.status_code}")
                    break
            
            # If we get here, scan didn't complete in time
            self.results["real_workflow"] = {
                "success": False,
                "error": "Scan timeout or incomplete",
                "campaign_id": campaign_id,
                "scan_id": scan_id
            }
            self.log("❌ Real workflow timeout")
            return False
            
        except Exception as e:
            self.results["real_workflow"] = {"success": False, "error": str(e)}
            self.log(f"❌ Real workflow error: {str(e)}")
            return False
    
    def generate_honest_assessment(self):
        """Generate brutally honest production readiness assessment"""
        self.log("📋 GENERATING HONEST PRODUCTION READINESS ASSESSMENT", "FINAL")
        
        print("\n" + "="*80)
        print("🔍 HONEST PRODUCTION READINESS ASSESSMENT")
        print("="*80)
        
        # Tool Availability Assessment
        tool_results = self.results.get("tool_availability", {})
        tool_percentage = tool_results.get("percentage", 0)
        
        print(f"\n🔧 SECURITY TOOLS REALITY CHECK:")
        print(f"   Available Tools: {tool_results.get('available_count', 0)}/{tool_results.get('total_count', 0)} ({tool_percentage:.1f}%)")
        
        if tool_percentage >= 80:
            print("   ✅ EXCELLENT: Most security tools are properly installed")
        elif tool_percentage >= 60:
            print("   🟡 GOOD: Many tools available but some missing")
        elif tool_percentage >= 40:
            print("   🟠 FAIR: Basic tools available but significant gaps")
        else:
            print("   🔴 POOR: Critical security tools missing - not production ready")
        
        # Real Scan Capability
        nmap_success = self.results.get("nmap_scan", {}).get("success", False)
        nuclei_success = self.results.get("nuclei_scan", {}).get("success", False)
        
        print(f"\n🎯 REAL SCANNING CAPABILITY:")
        print(f"   Nmap Execution: {'✅ Working' if nmap_success else '❌ Failed'}")
        print(f"   Nuclei Execution: {'✅ Working' if nuclei_success else '❌ Failed'}")
        
        if nmap_success and nuclei_success:
            print("   ✅ EXCELLENT: Core scanning tools work correctly")
        elif nmap_success or nuclei_success:
            print("   🟡 PARTIAL: Some scanning capability available")
        else:
            print("   🔴 CRITICAL: No real scanning capability - SIMULATION ONLY")
        
        # AI Integration Reality
        ai_results = self.results.get("ai_integration", {})
        ai_accuracy = ai_results.get("backend_accuracy", 0)
        
        print(f"\n🤖 AI INTEGRATION REALITY:")
        print(f"   Backend Accuracy: {ai_accuracy:.1f}%")
        
        real_ai_count = sum(1 for provider in ai_results.get("real_providers", {}).values() if provider.get("available", False))
        print(f"   Real AI Providers: {real_ai_count}")
        
        if real_ai_count >= 2 and ai_accuracy >= 80:
            print("   ✅ EXCELLENT: Multiple AI providers working correctly")
        elif real_ai_count >= 1:
            print("   🟡 BASIC: Limited AI capability available")
        else:
            print("   🔴 NONE: No real AI integration - claims are false")
        
        # Backend Integration Accuracy
        backend_accuracy = self.results.get("backend_tool_integration", {}).get("accuracy_percentage", 0)
        
        print(f"\n🔗 BACKEND INTEGRATION HONESTY:")
        print(f"   Tool Reporting Accuracy: {backend_accuracy:.1f}%")
        
        if backend_accuracy >= 90:
            print("   ✅ HONEST: Backend accurately reports tool capabilities")
        elif backend_accuracy >= 70:
            print("   🟡 MOSTLY HONEST: Some discrepancies in tool reporting")
        else:
            print("   🔴 DISHONEST: Backend claims don't match reality")
        
        # Overall Production Readiness
        workflow_success = self.results.get("real_workflow", {}).get("success", False)
        
        print(f"\n🎯 OVERALL PRODUCTION READINESS:")
        
        scores = [
            tool_percentage,
            (100 if nmap_success else 0),
            (100 if nuclei_success else 0),
            ai_accuracy,
            backend_accuracy,
            (100 if workflow_success else 0)
        ]
        
        overall_score = sum(scores) / len(scores)
        
        print(f"   Overall Score: {overall_score:.1f}/100")
        
        if overall_score >= 85:
            verdict = "🟢 PRODUCTION READY"
            details = "System demonstrates real security capabilities with honest reporting"
        elif overall_score >= 70:
            verdict = "🟡 MOSTLY READY"
            details = "Core functionality works but some gaps exist"
        elif overall_score >= 50:
            verdict = "🟠 DEVELOPMENT STAGE"
            details = "Basic infrastructure but significant capabilities missing"
        else:
            verdict = "🔴 NOT PRODUCTION READY"
            details = "Critical gaps in functionality - mostly simulation"
        
        print(f"   Verdict: {verdict}")
        print(f"   Assessment: {details}")
        
        # Critical Issues
        print(f"\n⚠️  CRITICAL ISSUES TO ADDRESS:")
        issues = []
        
        if tool_percentage < 80:
            issues.append("Install missing security tools for real scanning capability")
        
        if not (nmap_success and nuclei_success):
            issues.append("Fix core scanning tools - currently non-functional")
        
        if real_ai_count == 0:
            issues.append("Configure real AI API keys for actual AI-powered analysis")
        
        if backend_accuracy < 70:
            issues.append("Fix backend tool detection to accurately report capabilities")
        
        if not workflow_success:
            issues.append("Complete end-to-end workflow integration for real vulnerability detection")
        
        if not issues:
            print("   ✅ No critical issues found - system appears production ready")
        else:
            for i, issue in enumerate(issues, 1):
                print(f"   {i}. {issue}")
        
        # Honest Recommendations
        print(f"\n💡 HONEST RECOMMENDATIONS:")
        
        if overall_score >= 85:
            print("   • System is ready for production security testing")
            print("   • Focus on performance optimization and user experience")
            print("   • Consider advanced feature development")
        elif overall_score >= 70:
            print("   • Address remaining tool installation issues")
            print("   • Complete AI integration configuration")
            print("   • Conduct additional integration testing")
        else:
            print("   • Focus on core functionality implementation")
            print("   • Install and configure essential security tools")
            print("   • Develop real scanning capabilities beyond simulation")
            print("   • This is currently a demonstration platform, not a working security tool")
        
        return overall_score, verdict, details
    
    def run_all_tests(self):
        """Run complete production readiness test suite"""
        self.log("🚀 STARTING COMPREHENSIVE PRODUCTION READINESS TEST", "START")
        self.log(f"📅 Test Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # Test 1: Real tool availability
        self.test_real_tool_availability()
        
        # Test 2: Real nmap execution
        self.test_real_nmap_scan()
        
        # Test 3: Real nuclei execution
        self.test_real_nuclei_scan()
        
        # Test 4: Backend integration accuracy
        self.test_backend_tool_integration()
        
        # Test 5: Real AI integration
        self.test_real_ai_integration()
        
        # Test 6: Complete workflow test
        self.test_complete_real_workflow()
        
        # Generate final assessment
        score, verdict, details = self.generate_honest_assessment()
        
        # Save detailed results
        results_file = f"production-readiness-results-{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(results_file, 'w') as f:
            json.dump(self.results, f, indent=2, default=str)
        
        self.log(f"📄 Detailed results saved to: {results_file}")
        
        return score >= 70  # Consider passing if score >= 70%

if __name__ == "__main__":
    test = ProductionReadinessTest()
    success = test.run_all_tests()
    
    print(f"\n{'✅ TESTS PASSED' if success else '❌ TESTS FAILED'}")
    sys.exit(0 if success else 1)