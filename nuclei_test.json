{"success": true, "data": {"scan_id": "b5f054e7-dbbf-4131-bbce-5ac73fcfed08", "tool": "nuclei", "target": "http://scanme.nmap.org", "tool_name": "nuclei", "status": "completed", "start_time": "2025-07-13T06:15:09.811146", "end_time": "2025-07-13T06:15:10.867702", "duration_seconds": 1.056556, "raw_output": "flag provided but not defined: -json\n", "parsed_results": {"scan_info": {"scanner": "nuclei", "scan_type": "vulnerability_scan", "timestamp": "2025-07-13T06:15:09.911544", "template_count": 4000, "last_update": "2025-07-13T06:15:10.125155", "status": "completed", "scan_configuration": {"default_rate_limit": 150, "max_concurrency": 25, "available_severities": ["critical", "high", "medium", "low", "info"], "template_categories": ["cves", "exposures", "technologies", "takeovers", "vulnerabilities", "workflows", "file", "network", "dns", "headless"], "supported_export_formats": ["JSON", "SARIF", "CSV", "HTML"]}}, "vulnerabilities": [], "detailed_findings": [], "vulnerability_grid": [], "severity_chart_data": {"labels": ["Critical", "High", "Medium", "Low", "Info"], "datasets": [{"data": [0, 0, 0, 0, 0], "backgroundColor": ["#ef4444", "#f97316", "#eab308", "#16a34a", "#3b82f6"], "borderWidth": 2, "borderColor": "#1f2937"}], "total": 0, "distribution": {"critical_percentage": 0.0, "high_percentage": 0.0, "medium_percentage": 0.0, "low_percentage": 0.0, "info_percentage": 0.0}}, "template_coverage": {"categories": ["cve", "exposures", "technologies", "takeovers", "vulnerabilities", "workflows"], "coverage_data": [{"category": "cve", "count": 0, "intensity": 0.0, "color_intensity": "rgba(59, 130, 246, 0.0)"}, {"category": "exposures", "count": 0, "intensity": 0.0, "color_intensity": "rgba(59, 130, 246, 0.0)"}, {"category": "technologies", "count": 0, "intensity": 0.0, "color_intensity": "rgba(59, 130, 246, 0.0)"}, {"category": "takeovers", "count": 0, "intensity": 0.0, "color_intensity": "rgba(59, 130, 246, 0.0)"}, {"category": "vulnerabilities", "count": 0, "intensity": 0.0, "color_intensity": "rgba(59, 130, 246, 0.0)"}, {"category": "workflows", "count": 0, "intensity": 0.0, "color_intensity": "rgba(59, 130, 246, 0.0)"}], "total_categories_tested": 0, "most_active_category": ["none", 0]}, "live_results": [], "statistics": {"total_findings": 0, "severity_counts": {"critical": 0, "high": 0, "medium": 0, "low": 0, "info": 0}, "template_stats": {"templates_executed": 0, "template_usage": {}, "category_distribution": {}, "most_active_templates": {}}, "scan_coverage": {"templates_used": 0, "estimated_total_templates": 4000, "coverage_percentage": 0.0, "tags_covered": 0, "categories_tested": 0}}, "progress_data": {"templates_run": 0, "total_templates": 4000, "vulnerabilities_found": 0, "completion_percentage": 100, "current_phase": "Analysis Complete"}, "export_formats": {"json": {"export_format": "nuclei_json", "scan_timestamp": "2025-07-13T06:15:10.769916", "scanner": "nuclei", "total_findings": 0, "findings": [], "export_metadata": {"exported_by": "nexusscan", "export_version": "1.0"}}, "sarif": {"version": "2.1.0", "runs": [{"tool": {"driver": {"name": "nuclei", "version": ""}}, "results": []}]}, "csv": "Template ID,Template Name,Severity,Target,Matched At,Tags,Description", "html": {"title": "Nuclei Vulnerability Scan Report", "timestamp": "2025-07-13T06:15:10.867532", "summary": {"total_findings": 0, "severity_distribution": {"critical_percentage": 0.0, "high_percentage": 0.0, "medium_percentage": 0.0, "low_percentage": 0.0, "info_percentage": 0.0}, "templates_used": 0}, "findings": [], "template_coverage": {"categories": ["cve", "exposures", "technologies", "takeovers", "vulnerabilities", "workflows"], "coverage_data": [{"category": "cve", "count": 0, "intensity": 0.0, "color_intensity": "rgba(59, 130, 246, 0.0)"}, {"category": "exposures", "count": 0, "intensity": 0.0, "color_intensity": "rgba(59, 130, 246, 0.0)"}, {"category": "technologies", "count": 0, "intensity": 0.0, "color_intensity": "rgba(59, 130, 246, 0.0)"}, {"category": "takeovers", "count": 0, "intensity": 0.0, "color_intensity": "rgba(59, 130, 246, 0.0)"}, {"category": "vulnerabilities", "count": 0, "intensity": 0.0, "color_intensity": "rgba(59, 130, 246, 0.0)"}, {"category": "workflows", "count": 0, "intensity": 0.0, "color_intensity": "rgba(59, 130, 246, 0.0)"}], "total_categories_tested": 0, "most_active_category": ["none", 0]}}}, "template_filters": {"severity": ["critical", "high", "medium", "low", "info"], "tags": ["cve", "exposure", "config", "rce", "sqli", "xss", "lfi", "rfi", "ssrf", "auth-bypass", "default-login", "misconfig", "file", "network", "dns", "takeover", "tech", "cms", "ecommerce", "iot", "intrusive", "dos", "redirect", "plugin"], "categories": ["cve", "exposures", "technologies", "takeovers", "vulnerabilities"]}, "scan_options": {"rate_limit": 150, "concurrency": 25, "timeout": 300, "custom_headers": {}, "bulk_scan": false}}, "errors": [], "warnings": [], "vulnerabilities": [], "metadata": {"nuclei_version": "", "templates_used": "default", "vulnerabilities_found": 0}}}