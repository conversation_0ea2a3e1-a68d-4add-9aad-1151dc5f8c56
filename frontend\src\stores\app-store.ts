/**
 * Main Application Store
 * Manages global app state including UI preferences and settings
 */
import React from 'react';
import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';

// ===== TYPE DEFINITIONS =====

export interface AppState {
  // UI State
  isLoading: boolean;
  sidebarOpen: boolean;
  terminalOpen: boolean;
  theme: 'light' | 'dark' | 'system';
  
  // App Information
  version: string;
  platform: string;
  arch: string;
  
  // User Preferences
  preferences: {
    autoSave: boolean;
    notifications: boolean;
    soundEnabled: boolean;
    compactMode: boolean;
    defaultTimeout: number;
    maxConcurrentScans: number;
    retryAttempts: number;
  };
  
  // Window State
  windowState: {
    width: number;
    height: number;
    x: number;
    y: number;
    isMaximized: boolean;
    isFullscreen: boolean;
  };
  
  // Recent Activity
  recentActivity: Array<{
    id: string;
    type: 'tool_execution' | 'campaign_created' | 'report_generated' | 'error';
    message: string;
    timestamp: Date;
    data?: any;
  }>;
}

export interface AppActions {
  // Loading State
  setLoading: (loading: boolean) => void;
  
  // UI Toggles
  toggleSidebar: () => void;
  setSidebarOpen: (open: boolean) => void;
  toggleTerminal: () => void;
  setTerminalOpen: (open: boolean) => void;
  
  // Theme Management
  setTheme: (theme: AppState['theme']) => void;
  toggleTheme: () => void;
  
  // App Information
  setAppInfo: (info: Pick<AppState, 'version' | 'platform' | 'arch'>) => void;
  
  // Preferences
  updatePreferences: (preferences: Partial<AppState['preferences']>) => void;
  resetPreferences: () => void;
  
  // Window State
  updateWindowState: (state: Partial<AppState['windowState']>) => void;
  
  // Activity Tracking
  addActivity: (activity: Omit<AppState['recentActivity'][0], 'id' | 'timestamp'>) => void;
  clearActivity: () => void;
  
  // Utility Actions
  reset: () => void;
}

export type AppStore = AppState & AppActions;

// ===== DEFAULT STATE =====

const defaultPreferences: AppState['preferences'] = {
  autoSave: true,
  notifications: true,
  soundEnabled: false,
  compactMode: false,
  defaultTimeout: 300, // 5 minutes
  maxConcurrentScans: 3,
  retryAttempts: 3,
};

const defaultWindowState: AppState['windowState'] = {
  width: 1200,
  height: 800,
  x: 100,
  y: 100,
  isMaximized: false,
  isFullscreen: false,
};

const initialState: AppState = {
  // UI State
  isLoading: false,
  sidebarOpen: true,
  terminalOpen: false,
  theme: 'system',
  
  // App Information
  version: '1.0.0',
  platform: 'unknown',
  arch: 'unknown',
  
  // User Preferences
  preferences: defaultPreferences,
  
  // Window State
  windowState: defaultWindowState,
  
  // Recent Activity
  recentActivity: [],
};

// ===== ZUSTAND STORE =====

export const useAppStore = create<AppStore>()(
  persist(
    immer((set, get) => ({
      ...initialState,
      
      // ===== LOADING STATE =====
      
      setLoading: (loading: boolean) => 
        set((state) => {
          state.isLoading = loading;
        }),
      
      // ===== UI TOGGLES =====
      
      toggleSidebar: () => 
        set((state) => {
          state.sidebarOpen = !state.sidebarOpen;
        }),
      
      setSidebarOpen: (open: boolean) => 
        set((state) => {
          state.sidebarOpen = open;
        }),
      
      toggleTerminal: () => 
        set((state) => {
          state.terminalOpen = !state.terminalOpen;
        }),
      
      setTerminalOpen: (open: boolean) => 
        set((state) => {
          state.terminalOpen = open;
        }),
      
      // ===== THEME MANAGEMENT =====
      
      setTheme: (theme: AppState['theme']) => 
        set((state) => {
          state.theme = theme;
          
          // Apply theme to document
          const root = document.documentElement;
          if (theme === 'dark') {
            root.classList.add('dark');
          } else if (theme === 'light') {
            root.classList.remove('dark');
          } else {
            // System theme
            const systemPrefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
            if (systemPrefersDark) {
              root.classList.add('dark');
            } else {
              root.classList.remove('dark');
            }
          }
        }),
      
      toggleTheme: () => {
        const currentTheme = get().theme;
        const nextTheme = currentTheme === 'light' ? 'dark' : 
                         currentTheme === 'dark' ? 'system' : 'light';
        get().setTheme(nextTheme);
      },
      
      // ===== APP INFORMATION =====
      
      setAppInfo: (info: Pick<AppState, 'version' | 'platform' | 'arch'>) => 
        set((state) => {
          state.version = info.version;
          state.platform = info.platform;
          state.arch = info.arch;
        }),
      
      // ===== PREFERENCES =====
      
      updatePreferences: (preferences: Partial<AppState['preferences']>) => 
        set((state) => {
          Object.assign(state.preferences, preferences);
        }),
      
      resetPreferences: () => 
        set((state) => {
          state.preferences = { ...defaultPreferences };
        }),
      
      // ===== WINDOW STATE =====
      
      updateWindowState: (windowState: Partial<AppState['windowState']>) => 
        set((state) => {
          Object.assign(state.windowState, windowState);
        }),
      
      // ===== ACTIVITY TRACKING =====
      
      addActivity: (activity: Omit<AppState['recentActivity'][0], 'id' | 'timestamp'>) => 
        set((state) => {
          const newActivity = {
            ...activity,
            id: crypto.randomUUID(),
            timestamp: new Date(),
          };
          
          state.recentActivity.unshift(newActivity);
          
          // Keep only the last 100 activities
          if (state.recentActivity.length > 100) {
            state.recentActivity = state.recentActivity.slice(0, 100);
          }
        }),
      
      clearActivity: () => 
        set((state) => {
          state.recentActivity = [];
        }),
      
      // ===== UTILITY ACTIONS =====
      
      reset: () => 
        set(() => ({ ...initialState })),
    })),
    {
      name: 'nexusscan-app-state',
      version: 1,
      
      // Only persist certain parts of the state
      partialize: (state) => ({
        sidebarOpen: state.sidebarOpen,
        terminalOpen: state.terminalOpen,
        theme: state.theme,
        preferences: state.preferences,
        windowState: state.windowState,
      }),
      
      // Handle migrations when store version changes
      migrate: (persistedState: any, version: number) => {
        if (version === 0) {
          // Migration from version 0 to 1
          return {
            ...persistedState,
            preferences: {
              ...defaultPreferences,
              ...persistedState.preferences,
            },
          };
        }
        return persistedState;
      },
    }
  )
);

// ===== STORE UTILITIES =====

/**
 * Initialize the app store with system information
 */
export const initializeAppStore = async () => {
  try {
    // Get app information if running in Electron
    if (window.electronAPI) {
      const [version, platform, arch] = await Promise.all([
        window.electronAPI.app.getVersion(),
        window.electronAPI.app.getPlatform(),
        window.electronAPI.app.getArch(),
      ]);
      
      useAppStore.getState().setAppInfo({ version, platform, arch });
    }
    
    // Apply initial theme
    const theme = useAppStore.getState().theme;
    useAppStore.getState().setTheme(theme);
    
    // Listen for system theme changes
    if (window.matchMedia) {
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
      mediaQuery.addEventListener('change', () => {
        if (useAppStore.getState().theme === 'system') {
          useAppStore.getState().setTheme('system');
        }
      });
    }
    
    console.log('✅ App store initialized');
  } catch (error) {
    console.error('❌ Failed to initialize app store:', error);
  }
};

/**
 * Utility hook for theme detection
 */
export const useThemeDetection = () => {
  const { theme, setTheme } = useAppStore();
  
  React.useEffect(() => {
    if (theme === 'system' && window.matchMedia) {
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
      const handleChange = () => setTheme('system');
      
      mediaQuery.addEventListener('change', handleChange);
      return () => mediaQuery.removeEventListener('change', handleChange);
    }
  }, [theme, setTheme]);
  
  return theme;
};