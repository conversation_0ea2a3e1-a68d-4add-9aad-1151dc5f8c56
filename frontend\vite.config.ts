import { defineConfig } from 'vitest/config';
import react from '@vitejs/plugin-react';
import path from 'path';
import { VitePWA } from 'vite-plugin-pwa';

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    react(),
    VitePWA({
      registerType: 'autoUpdate',
      workbox: {
        globPatterns: ['**/*.{js,css,html,ico,png,svg,woff2}']
      },
      includeAssets: ['favicon.ico', 'icons/*.svg', 'icons/*.png'],
      devOptions: {
        enabled: true
      }
    })
  ],
  
  // Development server configuration
  server: {
    port: 5182,
    host: 'localhost',
    strictPort: false,
    cors: true
  },

  // Build configuration
  build: {
    outDir: 'dist',
    emptyOutDir: true,
    sourcemap: true,
    target: 'chrome138', // Match Electron 28's Chromium version
    rollupOptions: {
      output: {
        manualChunks: {
          // Vendor chunks for better caching
          react: ['react', 'react-dom', 'react-router-dom'],
          ui: ['lucide-react', 'clsx', 'tailwind-merge'],
          editor: ['monaco-editor'],
          terminal: ['@xterm/xterm', '@xterm/addon-fit', '@xterm/addon-web-links'],
          charts: ['recharts'],
          virtualized: ['react-virtualized']
        }
      }
    },
    // Increase chunk size warning limit for desktop app
    chunkSizeWarningLimit: 1000
  },

  // Path resolution
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
      '@components': path.resolve(__dirname, './src/components'),
      '@pages': path.resolve(__dirname, './src/pages'),
      '@services': path.resolve(__dirname, './src/services'),
      '@stores': path.resolve(__dirname, './src/stores'),
      '@types': path.resolve(__dirname, './src/types'),
      '@utils': path.resolve(__dirname, './src/utils'),
      '@hooks': path.resolve(__dirname, './src/hooks'),
      '@assets': path.resolve(__dirname, './assets')
    }
  },

  // CSS configuration
  css: {
    postcss: './postcss.config.js'
  },

  // Define configuration for different environments
  define: {
    __APP_VERSION__: JSON.stringify(process.env.npm_package_version || '1.0.0'),
    __APP_NAME__: JSON.stringify('NexusScan Desktop'),
    __IS_ELECTRON__: JSON.stringify(true),
    __BACKEND_URL__: JSON.stringify(
      process.env.NODE_ENV === 'development'
        ? 'http://************:8090'
        : 'http://************:8090'
    )
  },

  // Optimization for desktop app
  optimizeDeps: {
    include: [
      'react',
      'react-dom',
      'react-router-dom',
      'zustand',
      'axios',
      'lucide-react',
      'clsx',
      'tailwind-merge',
      'recharts',
      'framer-motion'
    ],
    exclude: [
      'electron'
    ]
  },

  // Base path configuration
  base: process.env.NODE_ENV === 'production' ? './' : '/',

  // Preview configuration for testing builds
  preview: {
    port: 4173,
    host: 'localhost',
    strictPort: true
  },

  // Test configuration
  test: {
    environment: 'jsdom',
    setupFiles: ['./src/test/setup.ts'],
    globals: true,
    css: true,
    clearMocks: true,
    mockReset: true,
    restoreMocks: true
  }
});