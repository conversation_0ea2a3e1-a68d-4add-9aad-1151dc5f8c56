#!/usr/bin/env python3
"""
AI Performance Monitor
Real-time AI service performance monitoring, optimization, and analytics
"""

import asyncio
import time
import json
import logging
from typing import Any, Dict, List, Optional, Tuple
from dataclasses import dataclass, field, asdict
from datetime import datetime, timedelta
from enum import Enum
import statistics

logger = logging.getLogger(__name__)

class PerformanceMetric(Enum):
    """AI performance metrics"""
    RESPONSE_TIME = "response_time"
    QUALITY_SCORE = "quality_score"
    COST_PER_REQUEST = "cost_per_request"
    SUCCESS_RATE = "success_rate"
    CACHE_HIT_RATE = "cache_hit_rate"
    PROVIDER_UTILIZATION = "provider_utilization"
    THROUGHPUT = "throughput"
    ERROR_RATE = "error_rate"

class AlertLevel(Enum):
    """Performance alert levels"""
    INFO = "info"
    WARNING = "warning"
    CRITICAL = "critical"

@dataclass
class PerformanceSnapshot:
    """Point-in-time performance snapshot"""
    timestamp: datetime
    response_time: float
    quality_score: float
    cost: float
    provider: str
    task_type: str
    cache_hit: bool
    success: bool
    tokens_used: int = 0
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        result = asdict(self)
        result['timestamp'] = self.timestamp.isoformat()
        return result

@dataclass
class PerformanceAlert:
    """Performance monitoring alert"""
    level: AlertLevel
    metric: PerformanceMetric
    message: str
    current_value: float
    threshold: float
    timestamp: datetime = field(default_factory=datetime.now)
    recommendations: List[str] = field(default_factory=list)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        result = asdict(self)
        result['level'] = self.level.value
        result['metric'] = self.metric.value
        result['timestamp'] = self.timestamp.isoformat()
        return result

@dataclass
class PerformanceTrend:
    """Performance trend analysis"""
    metric: PerformanceMetric
    direction: str  # "improving", "degrading", "stable"
    change_percentage: float
    current_value: float
    previous_value: float
    time_period: str
    confidence: float

class AIPerformanceMonitor:
    """Real-time AI performance monitoring and optimization"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        self.config = config or {}
        self.snapshots: List[PerformanceSnapshot] = []
        self.alerts: List[PerformanceAlert] = []
        
        # Performance thresholds
        self.thresholds = {
            PerformanceMetric.RESPONSE_TIME: 2.0,  # 2 seconds
            PerformanceMetric.QUALITY_SCORE: 0.8,  # 80%
            PerformanceMetric.SUCCESS_RATE: 0.95,  # 95%
            PerformanceMetric.CACHE_HIT_RATE: 0.7,  # 70%
            PerformanceMetric.ERROR_RATE: 0.05,    # 5%
            PerformanceMetric.COST_PER_REQUEST: 0.10  # $0.10
        }
        
        # Rolling window for metrics (keep last 1000 snapshots)
        self.max_snapshots = 1000
        
        # Background monitoring
        self.monitoring_active = True
        self.monitoring_interval = 60  # 1 minute
        
        # Performance optimization settings
        self.auto_optimization = self.config.get('auto_optimization', True)
        self.optimization_cooldown = 300  # 5 minutes between optimizations
        self.last_optimization = {}
        
        # Start background monitoring
        asyncio.create_task(self._performance_monitoring_loop())
    
    async def record_performance(self, 
                               response_time: float,
                               quality_score: float,
                               cost: float,
                               provider: str,
                               task_type: str,
                               cache_hit: bool,
                               success: bool,
                               tokens_used: int = 0):
        """Record AI performance snapshot"""
        
        snapshot = PerformanceSnapshot(
            timestamp=datetime.now(),
            response_time=response_time,
            quality_score=quality_score,
            cost=cost,
            provider=provider,
            task_type=task_type,
            cache_hit=cache_hit,
            success=success,
            tokens_used=tokens_used
        )
        
        self.snapshots.append(snapshot)
        
        # Maintain rolling window
        if len(self.snapshots) > self.max_snapshots:
            self.snapshots = self.snapshots[-self.max_snapshots:]
        
        # Check for performance alerts
        await self._check_performance_alerts(snapshot)
        
        # Trigger optimization if needed
        if self.auto_optimization:
            await self._trigger_optimization_if_needed()
    
    async def _check_performance_alerts(self, snapshot: PerformanceSnapshot):
        """Check for performance alerts based on current snapshot"""
        
        # Response time alert
        if snapshot.response_time > self.thresholds[PerformanceMetric.RESPONSE_TIME]:
            alert = PerformanceAlert(
                level=AlertLevel.WARNING if snapshot.response_time < 5.0 else AlertLevel.CRITICAL,
                metric=PerformanceMetric.RESPONSE_TIME,
                message=f"High response time: {snapshot.response_time:.2f}s",
                current_value=snapshot.response_time,
                threshold=self.thresholds[PerformanceMetric.RESPONSE_TIME],
                recommendations=[
                    "Check provider status and load",
                    "Consider switching to faster provider",
                    "Enable more aggressive caching"
                ]
            )
            self.alerts.append(alert)
            logger.warning(f"Performance Alert: {alert.message}")
        
        # Quality score alert
        if snapshot.quality_score < self.thresholds[PerformanceMetric.QUALITY_SCORE]:
            alert = PerformanceAlert(
                level=AlertLevel.WARNING,
                metric=PerformanceMetric.QUALITY_SCORE,
                message=f"Low quality score: {snapshot.quality_score:.2f}",
                current_value=snapshot.quality_score,
                threshold=self.thresholds[PerformanceMetric.QUALITY_SCORE],
                recommendations=[
                    "Switch to higher quality provider",
                    "Adjust prompt template",
                    "Increase model temperature for creativity"
                ]
            )
            self.alerts.append(alert)
        
        # Cost alert
        if snapshot.cost > self.thresholds[PerformanceMetric.COST_PER_REQUEST]:
            alert = PerformanceAlert(
                level=AlertLevel.INFO,
                metric=PerformanceMetric.COST_PER_REQUEST,
                message=f"High cost per request: ${snapshot.cost:.4f}",
                current_value=snapshot.cost,
                threshold=self.thresholds[PerformanceMetric.COST_PER_REQUEST],
                recommendations=[
                    "Switch to more cost-effective provider",
                    "Reduce max_tokens if possible",
                    "Implement more aggressive caching"
                ]
            )
            self.alerts.append(alert)
        
        # Keep only recent alerts (last 24 hours)
        cutoff_time = datetime.now() - timedelta(hours=24)
        self.alerts = [alert for alert in self.alerts if alert.timestamp > cutoff_time]
    
    async def _trigger_optimization_if_needed(self):
        """Trigger automatic optimization based on performance patterns"""
        
        if len(self.snapshots) < 10:  # Need minimum data
            return
        
        recent_snapshots = self.snapshots[-10:]  # Last 10 requests
        
        # Check if optimization cooldown has passed
        now = datetime.now()
        for metric, last_time in self.last_optimization.items():
            if (now - last_time).total_seconds() < self.optimization_cooldown:
                return
        
        # Analyze recent performance
        avg_response_time = statistics.mean(s.response_time for s in recent_snapshots)
        avg_quality = statistics.mean(s.quality_score for s in recent_snapshots)
        success_rate = sum(1 for s in recent_snapshots if s.success) / len(recent_snapshots)
        
        optimizations_needed = []
        
        # High response time optimization
        if avg_response_time > self.thresholds[PerformanceMetric.RESPONSE_TIME]:
            optimizations_needed.append("response_time")
        
        # Low quality optimization
        if avg_quality < self.thresholds[PerformanceMetric.QUALITY_SCORE]:
            optimizations_needed.append("quality")
        
        # Low success rate optimization
        if success_rate < self.thresholds[PerformanceMetric.SUCCESS_RATE]:
            optimizations_needed.append("reliability")
        
        # Apply optimizations
        for optimization in optimizations_needed:
            await self._apply_optimization(optimization)
            self.last_optimization[optimization] = now
    
    async def _apply_optimization(self, optimization_type: str):
        """Apply specific optimization based on type"""
        
        logger.info(f"Applying automatic optimization: {optimization_type}")
        
        if optimization_type == "response_time":
            # Optimize for faster response times
            logger.info("Optimization: Switching to performance-based routing")
            # This would integrate with the orchestrator to change routing strategy
            
        elif optimization_type == "quality":
            # Optimize for higher quality responses
            logger.info("Optimization: Switching to quality-optimized routing")
            # This would adjust provider selection for quality
            
        elif optimization_type == "reliability":
            # Optimize for better reliability
            logger.info("Optimization: Enabling more aggressive circuit breakers")
            # This would adjust circuit breaker thresholds
    
    async def _performance_monitoring_loop(self):
        """Background performance monitoring loop"""
        
        while self.monitoring_active:
            try:
                await asyncio.sleep(self.monitoring_interval)
                
                # Generate performance summary
                await self._generate_performance_summary()
                
                # Clean up old data
                await self._cleanup_old_data()
                
            except Exception as e:
                logger.error(f"Performance monitoring error: {e}")
    
    async def _generate_performance_summary(self):
        """Generate periodic performance summary"""
        
        if len(self.snapshots) < 5:
            return
        
        recent_snapshots = self.snapshots[-50:]  # Last 50 requests
        
        # Calculate summary metrics
        avg_response_time = statistics.mean(s.response_time for s in recent_snapshots)
        avg_quality = statistics.mean(s.quality_score for s in recent_snapshots)
        avg_cost = statistics.mean(s.cost for s in recent_snapshots)
        success_rate = sum(1 for s in recent_snapshots if s.success) / len(recent_snapshots)
        cache_hit_rate = sum(1 for s in recent_snapshots if s.cache_hit) / len(recent_snapshots)
        
        # Provider utilization
        provider_counts = {}
        for snapshot in recent_snapshots:
            provider_counts[snapshot.provider] = provider_counts.get(snapshot.provider, 0) + 1
        
        summary = {
            'timestamp': datetime.now().isoformat(),
            'metrics': {
                'avg_response_time': avg_response_time,
                'avg_quality_score': avg_quality,
                'avg_cost_per_request': avg_cost,
                'success_rate': success_rate,
                'cache_hit_rate': cache_hit_rate,
                'total_requests': len(recent_snapshots)
            },
            'provider_utilization': provider_counts,
            'active_alerts': len([a for a in self.alerts 
                                if a.timestamp > datetime.now() - timedelta(hours=1)])
        }
        
        logger.info(f"Performance Summary: {json.dumps(summary, indent=2)}")
    
    async def _cleanup_old_data(self):
        """Clean up old performance data"""
        
        # Remove snapshots older than 24 hours
        cutoff_time = datetime.now() - timedelta(hours=24)
        self.snapshots = [s for s in self.snapshots if s.timestamp > cutoff_time]
        
        # Remove alerts older than 24 hours
        self.alerts = [a for a in self.alerts if a.timestamp > cutoff_time]
    
    def get_performance_metrics(self, time_window_hours: int = 1) -> Dict[str, Any]:
        """Get performance metrics for specified time window"""
        
        cutoff_time = datetime.now() - timedelta(hours=time_window_hours)
        recent_snapshots = [s for s in self.snapshots if s.timestamp > cutoff_time]
        
        if not recent_snapshots:
            return {"error": "No data available for specified time window"}
        
        # Calculate metrics
        response_times = [s.response_time for s in recent_snapshots]
        quality_scores = [s.quality_score for s in recent_snapshots]
        costs = [s.cost for s in recent_snapshots]
        
        # Provider breakdown
        provider_stats = {}
        for snapshot in recent_snapshots:
            provider = snapshot.provider
            if provider not in provider_stats:
                provider_stats[provider] = {
                    'requests': 0,
                    'avg_response_time': 0,
                    'avg_quality': 0,
                    'avg_cost': 0,
                    'success_rate': 0
                }
            
            stats = provider_stats[provider]
            stats['requests'] += 1
            stats['avg_response_time'] += snapshot.response_time
            stats['avg_quality'] += snapshot.quality_score
            stats['avg_cost'] += snapshot.cost
            if snapshot.success:
                stats['success_rate'] += 1
        
        # Average out provider stats
        for provider, stats in provider_stats.items():
            count = stats['requests']
            stats['avg_response_time'] /= count
            stats['avg_quality'] /= count
            stats['avg_cost'] /= count
            stats['success_rate'] /= count
        
        return {
            'time_window_hours': time_window_hours,
            'total_requests': len(recent_snapshots),
            'overall_metrics': {
                'avg_response_time': statistics.mean(response_times),
                'min_response_time': min(response_times),
                'max_response_time': max(response_times),
                'median_response_time': statistics.median(response_times),
                'avg_quality_score': statistics.mean(quality_scores),
                'avg_cost_per_request': statistics.mean(costs),
                'total_cost': sum(costs),
                'success_rate': sum(1 for s in recent_snapshots if s.success) / len(recent_snapshots),
                'cache_hit_rate': sum(1 for s in recent_snapshots if s.cache_hit) / len(recent_snapshots)
            },
            'provider_breakdown': provider_stats,
            'recent_alerts': [alert.to_dict() for alert in self.alerts[-10:]]
        }
    
    def get_performance_trends(self, hours: int = 24) -> List[PerformanceTrend]:
        """Analyze performance trends over time"""
        
        cutoff_time = datetime.now() - timedelta(hours=hours)
        recent_snapshots = [s for s in self.snapshots if s.timestamp > cutoff_time]
        
        if len(recent_snapshots) < 20:  # Need minimum data for trend analysis
            return []
        
        # Split data into two halves for comparison
        mid_point = len(recent_snapshots) // 2
        first_half = recent_snapshots[:mid_point]
        second_half = recent_snapshots[mid_point:]
        
        trends = []
        
        # Analyze response time trend
        first_avg_time = statistics.mean(s.response_time for s in first_half)
        second_avg_time = statistics.mean(s.response_time for s in second_half)
        time_change = ((second_avg_time - first_avg_time) / first_avg_time) * 100
        
        trends.append(PerformanceTrend(
            metric=PerformanceMetric.RESPONSE_TIME,
            direction="improving" if time_change < -5 else "degrading" if time_change > 5 else "stable",
            change_percentage=time_change,
            current_value=second_avg_time,
            previous_value=first_avg_time,
            time_period=f"{hours}h",
            confidence=0.8 if len(recent_snapshots) > 50 else 0.6
        ))
        
        # Analyze quality trend
        first_avg_quality = statistics.mean(s.quality_score for s in first_half)
        second_avg_quality = statistics.mean(s.quality_score for s in second_half)
        quality_change = ((second_avg_quality - first_avg_quality) / first_avg_quality) * 100
        
        trends.append(PerformanceTrend(
            metric=PerformanceMetric.QUALITY_SCORE,
            direction="improving" if quality_change > 2 else "degrading" if quality_change < -2 else "stable",
            change_percentage=quality_change,
            current_value=second_avg_quality,
            previous_value=first_avg_quality,
            time_period=f"{hours}h",
            confidence=0.8 if len(recent_snapshots) > 50 else 0.6
        ))
        
        # Analyze cost trend
        first_avg_cost = statistics.mean(s.cost for s in first_half)
        second_avg_cost = statistics.mean(s.cost for s in second_half)
        cost_change = ((second_avg_cost - first_avg_cost) / first_avg_cost) * 100
        
        trends.append(PerformanceTrend(
            metric=PerformanceMetric.COST_PER_REQUEST,
            direction="improving" if cost_change < -5 else "degrading" if cost_change > 5 else "stable",
            change_percentage=cost_change,
            current_value=second_avg_cost,
            previous_value=first_avg_cost,
            time_period=f"{hours}h",
            confidence=0.8 if len(recent_snapshots) > 50 else 0.6
        ))
        
        return trends
    
    def get_optimization_recommendations(self) -> List[Dict[str, Any]]:
        """Get AI performance optimization recommendations"""
        
        recommendations = []
        
        if len(self.snapshots) < 10:
            return [{"type": "info", "message": "Insufficient data for recommendations"}]
        
        recent_snapshots = self.snapshots[-50:]
        
        # Response time recommendations
        avg_response_time = statistics.mean(s.response_time for s in recent_snapshots)
        if avg_response_time > 2.0:
            recommendations.append({
                "type": "performance",
                "priority": "high",
                "metric": "response_time",
                "current_value": avg_response_time,
                "recommendation": "High response times detected",
                "actions": [
                    "Switch to performance-based routing",
                    "Increase cache TTL for common requests",
                    "Consider upgrading to faster AI provider tiers"
                ]
            })
        
        # Quality recommendations
        avg_quality = statistics.mean(s.quality_score for s in recent_snapshots)
        if avg_quality < 0.8:
            recommendations.append({
                "type": "quality",
                "priority": "medium",
                "metric": "quality_score",
                "current_value": avg_quality,
                "recommendation": "AI response quality below target",
                "actions": [
                    "Switch to quality-optimized routing",
                    "Review and optimize prompt templates",
                    "Consider using higher-tier AI models"
                ]
            })
        
        # Cost optimization recommendations
        total_cost = sum(s.cost for s in recent_snapshots)
        avg_cost = total_cost / len(recent_snapshots)
        if avg_cost > 0.05:  # More than 5 cents per request
            recommendations.append({
                "type": "cost",
                "priority": "low",
                "metric": "cost_per_request",
                "current_value": avg_cost,
                "recommendation": "Cost optimization opportunities available",
                "actions": [
                    "Switch to cost-optimized routing",
                    "Implement more aggressive caching",
                    "Review token usage and optimize prompts"
                ]
            })
        
        # Cache optimization
        cache_hit_rate = sum(1 for s in recent_snapshots if s.cache_hit) / len(recent_snapshots)
        if cache_hit_rate < 0.5:
            recommendations.append({
                "type": "cache",
                "priority": "medium",
                "metric": "cache_hit_rate",
                "current_value": cache_hit_rate,
                "recommendation": "Low cache hit rate reducing performance",
                "actions": [
                    "Implement predictive cache warming",
                    "Increase cache TTL for stable responses",
                    "Optimize cache key generation for better hits"
                ]
            })
        
        return recommendations
    
    def stop_monitoring(self):
        """Stop background monitoring"""
        self.monitoring_active = False

# Global performance monitor instance
ai_performance_monitor: Optional[AIPerformanceMonitor] = None

def get_ai_performance_monitor() -> AIPerformanceMonitor:
    """Get global AI performance monitor instance"""
    global ai_performance_monitor
    
    if ai_performance_monitor is None:
        ai_performance_monitor = AIPerformanceMonitor()
    
    return ai_performance_monitor

def close_ai_performance_monitor():
    """Close global AI performance monitor"""
    global ai_performance_monitor
    if ai_performance_monitor:
        ai_performance_monitor.stop_monitoring()
        ai_performance_monitor = None