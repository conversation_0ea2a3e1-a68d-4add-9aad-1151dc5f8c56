import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { APIClient } from './api-client'
import { server } from '@/test/mocks/server'
import { rest } from 'msw'

const API_BASE = 'http://ec2-3-89-91-209.compute-1.amazonaws.com:8000'

describe('APIClient', () => {
  let apiClient: APIClient

  beforeEach(() => {
    apiClient = new APIClient()
  })

  afterEach(() => {
    server.resetHandlers()
  })

  describe('Health Check', () => {
    it('successfully checks backend health', async () => {
      const health = await apiClient.checkHealth()
      
      expect(health).toEqual({
        status: 'healthy',
        timestamp: expect.any(String),
        version: '1.0.0',
        services: {
          database: 'operational',
          tools: 'operational',
          ai: 'operational'
        }
      })
    })

    it('handles health check failure', async () => {
      server.use(
        rest.get(`${API_BASE}/api/health`, (req, res, ctx) => {
          return res(ctx.status(500))
        })
      )

      await expect(apiClient.checkHealth()).rejects.toThrow()
    })

    it('retries failed health checks', async () => {
      let attempts = 0
      server.use(
        rest.get(`${API_BASE}/api/health`, (req, res, ctx) => {
          attempts++
          if (attempts < 3) {
            return res(ctx.status(500))
          }
          return res(
            ctx.json({
              status: 'healthy',
              timestamp: new Date().toISOString(),
              version: '1.0.0',
              services: {
                database: 'operational',
                tools: 'operational',
                ai: 'operational'
              }
            })
          )
        })
      )

      const health = await apiClient.checkHealth()
      expect(health.status).toBe('healthy')
      expect(attempts).toBe(3)
    })
  })

  describe('Tools Management', () => {
    it('fetches available tools', async () => {
      const tools = await apiClient.getAvailableTools()
      
      expect(tools).toHaveProperty('tools')
      expect(tools).toHaveProperty('total', 22)
      expect(tools).toHaveProperty('available', 22)
      expect(tools).toHaveProperty('categories')
      
      expect(tools.tools).toBeInstanceOf(Array)
      expect(tools.tools.length).toBeGreaterThan(0)
      
      const nmapTool = tools.tools.find(tool => tool.id === 'nmap')
      expect(nmapTool).toBeDefined()
      expect(nmapTool).toEqual({
        id: 'nmap',
        name: 'Nmap',
        category: 'network-scanning',
        version: '7.94',
        status: 'available',
        description: 'Network discovery and security auditing'
      })
    })

    it('executes tool with configuration', async () => {
      const toolConfig = {
        target: '***********',
        scanType: 'syn',
        portRange: '1-1000',
        timing: 3
      }

      const execution = await apiClient.executeTool('nmap', toolConfig)
      
      expect(execution).toEqual({
        executionId: expect.stringMatching(/^exec_\d+$/),
        toolId: 'nmap',
        status: 'started',
        message: 'nmap execution started',
        timestamp: expect.any(String)
      })
    })

    it('gets tool execution status', async () => {
      const executionId = 'exec_test_1234'
      
      const status = await apiClient.getToolStatus(executionId)
      
      expect(status).toEqual({
        executionId,
        status: 'running',
        progress: 45,
        output: 'Scanning target...\nFound 3 open ports...',
        startTime: expect.any(String),
        estimatedCompletion: expect.any(String)
      })
    })

    it('handles tool execution errors', async () => {
      server.use(
        http.post(`${API_BASE}/api/tools/:toolId/execute`, () => {
          return HttpResponse.json(
            { error: 'Tool not found' },
            { status: 404 }
          )
        })
      )

      await expect(
        apiClient.executeTool('invalid-tool', {})
      ).rejects.toThrow('Tool not found')
    })

    it('validates tool configuration before execution', async () => {
      const invalidConfig = {
        // Missing required target field
        scanType: 'syn'
      }

      server.use(
        http.post(`${API_BASE}/api/tools/nmap/execute`, () => {
          return HttpResponse.json(
            { error: 'Invalid configuration: target is required' },
            { status: 400 }
          )
        })
      )

      await expect(
        apiClient.executeTool('nmap', invalidConfig)
      ).rejects.toThrow('Invalid configuration')
    })
  })

  describe('Ferrari AI Integration', () => {
    it('fetches orchestrator capabilities', async () => {
      const capabilities = await apiClient.getOrchestratorCapabilities()
      
      expect(capabilities).toEqual({
        status: 'operational',
        capabilities: {
          attackChains: true,
          mitreAttack: true,
          realTimeAdaptation: true,
          safetyConstraints: true
        },
        templates: ['reconnaissance', 'exploitation', 'post-exploitation'],
        analytics: {
          chainsExecuted: 42,
          successRate: 0.95,
          avgExecutionTime: 120
        }
      })
    })

    it('fetches creative exploit capabilities', async () => {
      const capabilities = await apiClient.getCreativeExploitCapabilities()
      
      expect(capabilities).toEqual({
        status: 'operational',
        engines: ['gpt-4', 'deepseek-v3', 'claude-4'],
        capabilities: {
          payloadGeneration: true,
          mutationEngine: true,
          confidenceScoring: true,
          noveltyDetection: true
        },
        statistics: {
          payloadsGenerated: 1250,
          uniquePayloads: 890,
          avgConfidenceScore: 0.87
        }
      })
    })

    it('executes AI attack chain', async () => {
      const attackChain = {
        target: '***********',
        objective: 'reconnaissance',
        techniques: ['T1595', 'T1590'],
        safetyConstraints: {
          simulationMode: true,
          maxExecutionTime: 3600
        }
      }

      server.use(
        http.post(`${API_BASE}/api/orchestrator/execute`, () => {
          return HttpResponse.json({
            executionId: 'exec_orchestrator_1',
            status: 'started',
            chainId: 'chain_123',
            message: 'Attack chain execution started',
            timestamp: new Date().toISOString()
          })
        })
      )

      const execution = await apiClient.executeAttackChain(attackChain)
      
      expect(execution).toEqual({
        executionId: 'exec_orchestrator_1',
        status: 'started',
        chainId: 'chain_123',
        message: 'Attack chain execution started',
        timestamp: expect.any(String)
      })
    })

    it('generates creative exploits', async () => {
      const targetInfo = {
        platform: 'Linux',
        services: ['HTTP', 'SSH'],
        vulnerabilities: ['CVE-2023-1234']
      }

      server.use(
        http.post(`${API_BASE}/api/ai/creative-exploits/generate`, () => {
          return HttpResponse.json({
            payloadId: 'payload_123',
            payload: 'curl -X POST -d "exploit_data" http://target/vulnerable',
            confidence: 0.92,
            noveltyScore: 0.78,
            riskLevel: 'medium',
            mitigations: ['Input validation', 'WAF rules'],
            timestamp: new Date().toISOString()
          })
        })
      )

      const exploit = await apiClient.generateCreativeExploit(targetInfo)
      
      expect(exploit).toEqual({
        payloadId: 'payload_123',
        payload: expect.any(String),
        confidence: expect.any(Number),
        noveltyScore: expect.any(Number),
        riskLevel: 'medium',
        mitigations: expect.any(Array),
        timestamp: expect.any(String)
      })
    })

    it('handles AI service errors gracefully', async () => {
      server.use(
        http.get(`${API_BASE}/api/orchestrator/capabilities`, () => {
          return HttpResponse.json(
            { error: 'AI service temporarily unavailable' },
            { status: 503 }
          )
        })
      )

      await expect(
        apiClient.getOrchestratorCapabilities()
      ).rejects.toThrow('AI service temporarily unavailable')
    })
  })

  describe('Campaign Management', () => {
    it('fetches campaigns list', async () => {
      const campaigns = await apiClient.getCampaigns()
      
      expect(campaigns).toHaveProperty('campaigns')
      expect(campaigns).toHaveProperty('total', 1)
      
      expect(campaigns.campaigns[0]).toEqual({
        id: 'camp_1',
        name: 'Test Campaign',
        description: 'Testing campaign',
        status: 'active',
        createdAt: expect.any(String),
        targets: ['***********/24'],
        tools: ['nmap', 'nuclei']
      })
    })

    it('creates new campaign', async () => {
      const campaignData = {
        name: 'New Campaign',
        description: 'New testing campaign',
        targets: ['***********'],
        tools: ['nmap']
      }

      server.use(
        http.post(`${API_BASE}/api/campaigns`, () => {
          return HttpResponse.json({
            id: 'camp_new',
            ...campaignData,
            status: 'created',
            createdAt: new Date().toISOString()
          })
        })
      )

      const campaign = await apiClient.createCampaign(campaignData)
      
      expect(campaign).toEqual({
        id: 'camp_new',
        name: 'New Campaign',
        description: 'New testing campaign',
        targets: ['***********'],
        tools: ['nmap'],
        status: 'created',
        createdAt: expect.any(String)
      })
    })

    it('updates existing campaign', async () => {
      const campaignId = 'camp_1'
      const updateData = {
        name: 'Updated Campaign',
        description: 'Updated description'
      }

      server.use(
        http.patch(`${API_BASE}/api/campaigns/${campaignId}`, () => {
          return HttpResponse.json({
            id: campaignId,
            name: 'Updated Campaign',
            description: 'Updated description',
            status: 'active',
            createdAt: new Date().toISOString(),
            targets: ['***********/24'],
            tools: ['nmap', 'nuclei']
          })
        })
      )

      const campaign = await apiClient.updateCampaign(campaignId, updateData)
      
      expect(campaign.name).toBe('Updated Campaign')
      expect(campaign.description).toBe('Updated description')
    })

    it('deletes campaign', async () => {
      const campaignId = 'camp_1'

      server.use(
        http.delete(`${API_BASE}/api/campaigns/${campaignId}`, () => {
          return new HttpResponse(null, { status: 204 })
        })
      )

      await expect(
        apiClient.deleteCampaign(campaignId)
      ).resolves.toBeUndefined()
    })
  })

  describe('Error Handling and Retry Logic', () => {
    it('retries on network errors', async () => {
      let attempts = 0
      server.use(
        http.get(`${API_BASE}/api/health`, () => {
          attempts++
          if (attempts < 3) {
            return new HttpResponse('Network Error', { status: 500 })
          }
          return HttpResponse.json({
            status: 'healthy',
            timestamp: new Date().toISOString(),
            version: '1.0.0',
            services: { database: 'operational', tools: 'operational', ai: 'operational' }
          })
        })
      )

      const health = await apiClient.checkHealth()
      expect(health.status).toBe('healthy')
      expect(attempts).toBe(3)
    })

    it('respects maximum retry attempts', async () => {
      server.use(
        http.get(`${API_BASE}/api/health`, () => {
          return new HttpResponse('Persistent Error', { status: 500 })
        })
      )

      await expect(apiClient.checkHealth()).rejects.toThrow()
    })

    it('handles timeout errors', async () => {
      server.use(
        http.get(`${API_BASE}/api/health`, async () => {
          await new Promise(resolve => setTimeout(resolve, 10000)) // 10 second delay
          return HttpResponse.json({ status: 'healthy' })
        })
      )

      // Set a short timeout for testing
      apiClient.setTimeout(1000) // 1 second timeout

      await expect(apiClient.checkHealth()).rejects.toThrow(/timeout/i)
    })

    it('handles malformed JSON responses', async () => {
      server.use(
        http.get(`${API_BASE}/api/health`, () => {
          return new HttpResponse('{ invalid json }', {
            headers: { 'Content-Type': 'application/json' }
          })
        })
      )

      await expect(apiClient.checkHealth()).rejects.toThrow()
    })

    it('handles authentication errors', async () => {
      server.use(
        http.get(`${API_BASE}/api/health`, () => {
          return HttpResponse.json(
            { error: 'Unauthorized' },
            { status: 401 }
          )
        })
      )

      await expect(apiClient.checkHealth()).rejects.toThrow('Unauthorized')
    })
  })

  describe('Request/Response Validation', () => {
    it('validates request parameters', async () => {
      // Test with null/undefined parameters
      await expect(
        apiClient.executeTool(null as any, {})
      ).rejects.toThrow('Tool ID is required')

      await expect(
        apiClient.executeTool('', {})
      ).rejects.toThrow('Tool ID is required')
    })

    it('validates response structure', async () => {
      server.use(
        http.get(`${API_BASE}/api/tools/available`, () => {
          return HttpResponse.json({
            // Missing required fields
            partial: 'response'
          })
        })
      )

      await expect(apiClient.getAvailableTools()).rejects.toThrow()
    })

    it('handles unexpected response formats', async () => {
      server.use(
        http.get(`${API_BASE}/api/health`, () => {
          return new HttpResponse('Plain text response')
        })
      )

      await expect(apiClient.checkHealth()).rejects.toThrow()
    })
  })

  describe('Performance and Caching', () => {
    it('caches tool list responses', async () => {
      let requestCount = 0
      server.use(
        http.get(`${API_BASE}/api/tools/available`, () => {
          requestCount++
          return HttpResponse.json({
            tools: [],
            total: 0,
            available: 0,
            categories: {}
          })
        })
      )

      // Make multiple requests
      await apiClient.getAvailableTools()
      await apiClient.getAvailableTools()
      await apiClient.getAvailableTools()

      // Should only make one actual request due to caching
      expect(requestCount).toBe(1)
    })

    it('invalidates cache when appropriate', async () => {
      let requestCount = 0
      server.use(
        http.get(`${API_BASE}/api/tools/available`, () => {
          requestCount++
          return HttpResponse.json({
            tools: [],
            total: 0,
            available: 0,
            categories: {}
          })
        })
      )

      await apiClient.getAvailableTools()
      
      // Force cache invalidation
      apiClient.invalidateCache()
      
      await apiClient.getAvailableTools()

      expect(requestCount).toBe(2)
    })

    it('handles concurrent requests efficiently', async () => {
      const requests = Array(10).fill(null).map(() => 
        apiClient.checkHealth()
      )

      const responses = await Promise.all(requests)
      
      responses.forEach(response => {
        expect(response.status).toBe('healthy')
      })
    })
  })

  describe('WebSocket Integration', () => {
    it('connects to WebSocket for real-time updates', async () => {
      const wsConnection = apiClient.connectWebSocket()
      
      expect(wsConnection).toBeDefined()
      expect(wsConnection.readyState).toBe(WebSocket.CONNECTING)
    })

    it('handles WebSocket connection errors', async () => {
      const mockWebSocket = {
        addEventListener: vi.fn(),
        removeEventListener: vi.fn(),
        close: vi.fn(),
        send: vi.fn(),
        readyState: WebSocket.CLOSED
      }

      global.WebSocket = vi.fn(() => mockWebSocket)

      const wsConnection = apiClient.connectWebSocket()
      
      // Simulate connection error
      const errorCallback = mockWebSocket.addEventListener.mock.calls
        .find(call => call[0] === 'error')?.[1]
      
      if (errorCallback) {
        errorCallback(new Event('error'))
      }

      expect(mockWebSocket.close).toHaveBeenCalled()
    })

    it('reconnects WebSocket on connection loss', async () => {
      let connectionAttempts = 0
      
      global.WebSocket = vi.fn(() => {
        connectionAttempts++
        return {
          addEventListener: vi.fn(),
          removeEventListener: vi.fn(),
          close: vi.fn(),
          send: vi.fn(),
          readyState: connectionAttempts === 1 ? WebSocket.CLOSED : WebSocket.OPEN
        }
      })

      const wsConnection = apiClient.connectWebSocket()
      
      // Simulate connection loss and reconnection
      await new Promise(resolve => setTimeout(resolve, 100))
      
      expect(connectionAttempts).toBeGreaterThan(1)
    })
  })
})