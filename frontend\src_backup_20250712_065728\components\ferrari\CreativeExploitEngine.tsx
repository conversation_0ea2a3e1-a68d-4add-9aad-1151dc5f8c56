/**
 * Creative Exploit Engine Component
 * AI-generated novel attack vectors and polyglot payload construction
 */
import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { 
  Sparkles,
  Play,
  Square,
  Download,
  Settings,
  Target,
  Globe,
  FileText,
  Terminal,
  Copy,
  RotateCcw,
  Clock,
  Zap,
  Filter,
  Eye,
  AlertTriangle,
  CheckCircle,
  TrendingUp,
  Activity,
  Lock,
  Search,
  List,
  BarChart3,
  Database,
  Server,
  Bug,
  Info,
  Code,
  Package,
  Layers,
  Shield,
  Crosshair,
  Cpu,
  Network,
  Gauge,
  Tag,
  Hash,
  Workflow,
  GitBranch,
  Book,
  Users,
  Table,
  Binary,
  Braces,
  FileKey,
  History,
  Folder,
  Star,
  Crown,
  Award,
  Timer,
  FastForward,
  SkipForward,
  RefreshCw,
  ExternalLink,
  FileCheck,
  FileWarning,
  Fingerprint,
  Key as KeyIcon,
  LockOpen,
  ShieldOff,
  Verified,
  ScanLine,
  Scan,
  FileJson,
  FileCode,
  Brain,
  Lightbulb,
  Shuffle,
  RotateCw,
  Repeat,
  PlayCircle,
  StopCircle,
  PauseCircle,
  Plus,
  Minus,
  Edit,
  Trash2,
  Save,
  Upload,
  ArrowRight,
  ArrowDown,
  ChevronRight,
  ChevronDown,
  Maximize2,
  Minimize2,
  MoreHorizontal,
  Move,
  Beaker,
  Dna,
  Atom,
  Zap as Lightning,
  FlaskConical,
  TestTube,
  Microscope,
  Rocket,
  Wand2
} from 'lucide-react';

interface ExploitPayload {
  id: string;
  name: string;
  type: 'sql_injection' | 'xss' | 'command_injection' | 'rce' | 'lfi' | 'polyglot' | 'custom';
  payload: string;
  description: string;
  targetPlatform: string[];
  vectors: string[];
  confidence: number;
  noveltyScore: number;
  complexity: 'low' | 'medium' | 'high' | 'extreme';
  bypassTechniques: string[];
  encodings: string[];
  aiModel: string;
  createdAt: string;
  testResults?: TestResult[];
  mitigation: string;
  educational: string;
}

interface TestResult {
  testType: string;
  success: boolean;
  response: string;
  detectionRate: number;
  bypassedControls: string[];
}

interface PayloadTemplate {
  id: string;
  name: string;
  category: string;
  basePayload: string;
  description: string;
  parameters: TemplateParameter[];
}

interface TemplateParameter {
  name: string;
  type: 'string' | 'number' | 'boolean' | 'select';
  options?: string[];
  default: any;
  description: string;
}

interface MutationRule {
  id: string;
  name: string;
  description: string;
  pattern: string;
  replacement: string;
  category: 'encoding' | 'obfuscation' | 'evasion' | 'polyglot';
}

// Predefined payload templates
const PAYLOAD_TEMPLATES: PayloadTemplate[] = [
  {
    id: 'sql_union',
    name: 'SQL UNION Injection',
    category: 'SQL Injection',
    basePayload: "' UNION SELECT 1,2,3,database(),user(),version()-- -",
    description: 'Basic UNION-based SQL injection payload',
    parameters: [
      { name: 'columns', type: 'number', default: 3, description: 'Number of columns in SELECT' },
      { name: 'position', type: 'number', default: 2, description: 'Position for data extraction' },
      { name: 'comment', type: 'select', options: ['-- -', '#', '/**/', ';--'], default: '-- -', description: 'SQL comment style' }
    ]
  },
  {
    id: 'xss_dom',
    name: 'DOM XSS Payload',
    category: 'Cross-Site Scripting',
    basePayload: '<svg onload=alert(1)>',
    description: 'Simple DOM-based XSS payload',
    parameters: [
      { name: 'event', type: 'select', options: ['onload', 'onerror', 'onmouseover', 'onfocus'], default: 'onload', description: 'Event handler' },
      { name: 'function', type: 'string', default: 'alert(1)', description: 'JavaScript function to execute' },
      { name: 'tag', type: 'select', options: ['svg', 'img', 'script', 'iframe'], default: 'svg', description: 'HTML tag' }
    ]
  },
  {
    id: 'cmd_injection',
    name: 'Command Injection',
    category: 'Command Injection',
    basePayload: '; whoami #',
    description: 'Basic command injection payload',
    parameters: [
      { name: 'separator', type: 'select', options: [';', '&&', '||', '|', '`'], default: ';', description: 'Command separator' },
      { name: 'command', type: 'string', default: 'whoami', description: 'Command to execute' },
      { name: 'terminator', type: 'select', options: ['#', '//', '--', ''], default: '#', description: 'Command terminator' }
    ]
  },
  {
    id: 'polyglot_multi',
    name: 'Multi-Vector Polyglot',
    category: 'Polyglot',
    basePayload: "';alert(String.fromCharCode(88,83,83))//';alert(String.fromCharCode(88,83,83))//\";alert(String.fromCharCode(88,83,83))//\";alert(String.fromCharCode(88,83,83))//--></SCRIPT>\">'>alert(String.fromCharCode(88,83,83))</SCRIPT>",
    description: 'Multi-context polyglot payload for XSS',
    parameters: [
      { name: 'encoding', type: 'select', options: ['none', 'url', 'html', 'unicode'], default: 'none', description: 'Encoding method' },
      { name: 'payload_type', type: 'select', options: ['alert', 'console', 'document.write'], default: 'alert', description: 'JavaScript execution method' }
    ]
  }
];

// Mutation rules for payload enhancement
const MUTATION_RULES: MutationRule[] = [
  {
    id: 'url_encode',
    name: 'URL Encoding',
    description: 'Apply URL encoding to special characters',
    pattern: '([<>"\'&])',
    replacement: (match: string) => encodeURIComponent(match),
    category: 'encoding'
  },
  {
    id: 'html_entities',
    name: 'HTML Entity Encoding',
    description: 'Convert characters to HTML entities',
    pattern: '([<>"\'&])',
    replacement: (match: string) => `&#${match.charCodeAt(0)};`,
    category: 'encoding'
  },
  {
    id: 'unicode_escape',
    name: 'Unicode Escaping',
    description: 'Use Unicode escape sequences',
    pattern: '([a-zA-Z])',
    replacement: (match: string) => `\\u00${match.charCodeAt(0).toString(16).padStart(2, '0')}`,
    category: 'encoding'
  },
  {
    id: 'case_variation',
    name: 'Case Variation',
    description: 'Randomize character case',
    pattern: '([a-zA-Z])',
    replacement: (match: string) => Math.random() > 0.5 ? match.toUpperCase() : match.toLowerCase(),
    category: 'obfuscation'
  }
];

// Bypass techniques
const BYPASS_TECHNIQUES = [
  { id: 'waf_evasion', name: 'WAF Evasion', description: 'Techniques to bypass Web Application Firewalls' },
  { id: 'filter_bypass', name: 'Filter Bypass', description: 'Methods to circumvent input filters' },
  { id: 'encoding_chains', name: 'Encoding Chains', description: 'Multiple encoding layers' },
  { id: 'context_switching', name: 'Context Switching', description: 'Switching between execution contexts' },
  { id: 'protocol_smuggling', name: 'Protocol Smuggling', description: 'HTTP request smuggling techniques' },
  { id: 'polyglot_construction', name: 'Polyglot Construction', description: 'Multi-language payload construction' }
];

export const CreativeExploitEngine: React.FC = () => {
  const [activeTab, setActiveTab] = useState('generator');
  const [generatedPayloads, setGeneratedPayloads] = useState<ExploitPayload[]>([]);
  const [selectedPayload, setSelectedPayload] = useState<ExploitPayload | null>(null);
  const [isGenerating, setIsGenerating] = useState(false);
  const [generationProgress, setGenerationProgress] = useState(0);
  
  // Generator configuration
  const [targetType, setTargetType] = useState('web_application');
  const [payloadType, setPayloadType] = useState('');
  const [targetDescription, setTargetDescription] = useState('');
  const [creativityLevel, setCreativityLevel] = useState(75);
  const [bypassFocus, setBypassFocus] = useState<string[]>([]);
  const [aiModel, setAiModel] = useState('gpt-4');

  // Mutation engine state
  const [basePayload, setBasePayload] = useState('');
  const [mutatedPayloads, setMutatedPayloads] = useState<ExploitPayload[]>([]);
  const [selectedMutations, setSelectedMutations] = useState<string[]>([]);

  useEffect(() => {
    loadSavedPayloads();
  }, []);

  const loadSavedPayloads = () => {
    const saved = localStorage.getItem('nexusscan_creative_payloads');
    if (saved) {
      setGeneratedPayloads(JSON.parse(saved));
    }
  };

  const savePayloads = (payloads: ExploitPayload[]) => {
    localStorage.setItem('nexusscan_creative_payloads', JSON.stringify(payloads));
    setGeneratedPayloads(payloads);
  };

  const generateCreativePayloads = async () => {
    if (!targetDescription) {
      alert('Please provide a target description');
      return;
    }

    setIsGenerating(true);
    setGenerationProgress(0);

    try {
      const response = await fetch('http://ec2-3-89-91-209.compute-1.amazonaws.com:8000/api/ai/creative-exploits/generate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          targetType,
          payloadType: payloadType || 'auto',
          targetDescription,
          creativityLevel: creativityLevel / 100,
          bypassFocus,
          aiModel,
          count: 5,
          includePolyglots: true,
          includeEducational: true
        })
      });

      if (response.ok) {
        const data = await response.json();
        const newPayloads: ExploitPayload[] = data.payloads.map((p: any, index: number) => ({
          id: Date.now() + index,
          name: p.name || `Generated Payload ${index + 1}`,
          type: p.type,
          payload: p.payload,
          description: p.description,
          targetPlatform: p.targetPlatform || [targetType],
          vectors: p.vectors || [],
          confidence: p.confidence || 0,
          noveltyScore: p.noveltyScore || 0,
          complexity: p.complexity || 'medium',
          bypassTechniques: p.bypassTechniques || [],
          encodings: p.encodings || [],
          aiModel: aiModel,
          createdAt: new Date().toISOString(),
          mitigation: p.mitigation || '',
          educational: p.educational || ''
        }));

        const updatedPayloads = [...generatedPayloads, ...newPayloads];
        savePayloads(updatedPayloads);
        
        if (newPayloads.length > 0) {
          setSelectedPayload(newPayloads[0]);
        }
      }
    } catch (error) {
      console.error('Failed to generate payloads:', error);
    } finally {
      setIsGenerating(false);
      setGenerationProgress(0);
    }
  };

  const mutatePayload = (payload: string, mutations: string[]) => {
    let mutated = payload;
    
    mutations.forEach(mutationId => {
      const rule = MUTATION_RULES.find(r => r.id === mutationId);
      if (rule) {
        mutated = mutated.replace(new RegExp(rule.pattern, 'g'), rule.replacement as any);
      }
    });

    return mutated;
  };

  const generateMutations = () => {
    if (!basePayload) {
      alert('Please enter a base payload');
      return;
    }

    const mutations: ExploitPayload[] = [];
    
    // Generate single mutations
    MUTATION_RULES.forEach(rule => {
      const mutated = mutatePayload(basePayload, [rule.id]);
      mutations.push({
        id: `mutation_${rule.id}_${Date.now()}`,
        name: `${rule.name} Variant`,
        type: 'custom',
        payload: mutated,
        description: `Applied ${rule.description}`,
        targetPlatform: ['web'],
        vectors: [rule.category],
        confidence: 80,
        noveltyScore: 60,
        complexity: 'medium',
        bypassTechniques: [rule.name],
        encodings: [rule.category],
        aiModel: 'mutation_engine',
        createdAt: new Date().toISOString(),
        mitigation: '',
        educational: ''
      });
    });

    // Generate combination mutations
    const combinationMutations = [
      ['url_encode', 'case_variation'],
      ['html_entities', 'unicode_escape'],
      ['url_encode', 'html_entities', 'case_variation']
    ];

    combinationMutations.forEach((combo, index) => {
      const mutated = mutatePayload(basePayload, combo);
      mutations.push({
        id: `combo_${index}_${Date.now()}`,
        name: `Combined Mutation ${index + 1}`,
        type: 'custom',
        payload: mutated,
        description: `Applied: ${combo.map(id => MUTATION_RULES.find(r => r.id === id)?.name).join(', ')}`,
        targetPlatform: ['web'],
        vectors: ['multi'],
        confidence: 75,
        noveltyScore: 80,
        complexity: 'high',
        bypassTechniques: combo.map(id => MUTATION_RULES.find(r => r.id === id)?.name || id),
        encodings: combo,
        aiModel: 'mutation_engine',
        createdAt: new Date().toISOString(),
        mitigation: '',
        educational: ''
      });
    });

    setMutatedPayloads(mutations);
  };

  const testPayload = async (payload: ExploitPayload) => {
    try {
      const response = await fetch('http://ec2-3-89-91-209.compute-1.amazonaws.com:8000/api/ai/creative-exploits/test', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          payload: payload.payload,
          type: payload.type,
          testTypes: ['waf_detection', 'filter_bypass', 'context_validity']
        })
      });

      if (response.ok) {
        const testResults = await response.json();
        const updatedPayload = { ...payload, testResults: testResults.results };
        
        const updatedPayloads = generatedPayloads.map(p => 
          p.id === payload.id ? updatedPayload : p
        );
        savePayloads(updatedPayloads);
        setSelectedPayload(updatedPayload);
      }
    } catch (error) {
      console.error('Failed to test payload:', error);
    }
  };

  const exportPayload = (payload: ExploitPayload) => {
    const exportData = {
      payload: payload.payload,
      description: payload.description,
      type: payload.type,
      confidence: payload.confidence,
      noveltyScore: payload.noveltyScore,
      bypassTechniques: payload.bypassTechniques,
      mitigation: payload.mitigation,
      educational: payload.educational,
      createdAt: payload.createdAt
    };

    const dataStr = JSON.stringify(exportData, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `payload_${payload.name.replace(/\s+/g, '_')}.json`;
    link.click();
  };

  const getComplexityColor = (complexity: string) => {
    switch (complexity) {
      case 'low': return 'text-green-600 bg-green-50 border-green-200';
      case 'medium': return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'high': return 'text-orange-600 bg-orange-50 border-orange-200';
      case 'extreme': return 'text-red-600 bg-red-50 border-red-200';
      default: return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'sql_injection': return Database;
      case 'xss': return Code;
      case 'command_injection': return Terminal;
      case 'rce': return Zap;
      case 'lfi': return FileText;
      case 'polyglot': return Layers;
      default: return Bug;
    }
  };

  return (
    <div className="space-y-6">
      {/* Header Actions */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button 
            onClick={generateCreativePayloads} 
            disabled={isGenerating}
            className="bg-purple-600 hover:bg-purple-700"
          >
            <Sparkles className="h-4 w-4 mr-2" />
            {isGenerating ? 'Generating...' : 'Generate Payloads'}
          </Button>
          <Button onClick={generateMutations} variant="outline">
            <Dna className="h-4 w-4 mr-2" />
            Generate Mutations
          </Button>
        </div>
        
        {isGenerating && (
          <div className="flex items-center space-x-3">
            <div className="text-sm text-gray-600">Generating: {generationProgress}%</div>
            <Progress value={generationProgress} className="w-32" />
          </div>
        )}
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="generator">AI Generator</TabsTrigger>
          <TabsTrigger value="mutations">Mutation Engine</TabsTrigger>
          <TabsTrigger value="library">Payload Library</TabsTrigger>
          <TabsTrigger value="testing">Testing Lab</TabsTrigger>
          <TabsTrigger value="education">Education</TabsTrigger>
        </TabsList>

        {/* AI Generator Tab */}
        <TabsContent value="generator" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Brain className="h-5 w-5" />
                <span>AI Payload Generator</span>
              </CardTitle>
              <CardDescription>Generate novel exploit payloads using advanced AI models</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Configuration */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Target Type</label>
                    <Select value={targetType} onValueChange={setTargetType}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="web_application">Web Application</SelectItem>
                        <SelectItem value="api_endpoint">API Endpoint</SelectItem>
                        <SelectItem value="database">Database</SelectItem>
                        <SelectItem value="command_line">Command Line</SelectItem>
                        <SelectItem value="mobile_app">Mobile Application</SelectItem>
                        <SelectItem value="iot_device">IoT Device</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <label className="text-sm font-medium">Payload Type (optional)</label>
                    <Select value={payloadType} onValueChange={setPayloadType}>
                      <SelectTrigger>
                        <SelectValue placeholder="Auto-detect" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="">Auto-detect</SelectItem>
                        <SelectItem value="sql_injection">SQL Injection</SelectItem>
                        <SelectItem value="xss">Cross-Site Scripting</SelectItem>
                        <SelectItem value="command_injection">Command Injection</SelectItem>
                        <SelectItem value="rce">Remote Code Execution</SelectItem>
                        <SelectItem value="lfi">Local File Inclusion</SelectItem>
                        <SelectItem value="polyglot">Polyglot Payload</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <label className="text-sm font-medium">AI Model</label>
                    <Select value={aiModel} onValueChange={setAiModel}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="gpt-4">GPT-4 (Creative)</SelectItem>
                        <SelectItem value="deepseek-v3">DeepSeek-V3 (Analytical)</SelectItem>
                        <SelectItem value="claude-4">Claude-4 (Balanced)</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <label className="text-sm font-medium">Creativity Level: {creativityLevel}%</label>
                    <input
                      type="range"
                      min="0"
                      max="100"
                      value={creativityLevel}
                      onChange={(e) => setCreativityLevel(Number(e.target.value))}
                      className="w-full"
                    />
                    <div className="flex justify-between text-xs text-gray-500">
                      <span>Conservative</span>
                      <span>Balanced</span>
                      <span>Highly Creative</span>
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Target Description</label>
                    <Textarea
                      placeholder="Describe the target system, technology stack, security controls, or specific vulnerability context..."
                      value={targetDescription}
                      onChange={(e) => setTargetDescription(e.target.value)}
                      rows={6}
                    />
                  </div>

                  <div className="space-y-2">
                    <label className="text-sm font-medium">Bypass Focus</label>
                    <div className="grid grid-cols-2 gap-2">
                      {BYPASS_TECHNIQUES.map((technique) => (
                        <div key={technique.id} className="flex items-center space-x-2">
                          <input
                            type="checkbox"
                            id={technique.id}
                            checked={bypassFocus.includes(technique.id)}
                            onChange={(e) => {
                              if (e.target.checked) {
                                setBypassFocus([...bypassFocus, technique.id]);
                              } else {
                                setBypassFocus(bypassFocus.filter(id => id !== technique.id));
                              }
                            }}
                            className="rounded"
                          />
                          <label htmlFor={technique.id} className="text-sm text-gray-700">
                            {technique.name}
                          </label>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Mutation Engine Tab */}
        <TabsContent value="mutations" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Dna className="h-5 w-5" />
                <span>Payload Mutation Engine</span>
              </CardTitle>
              <CardDescription>Apply advanced mutations and encoding to existing payloads</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Base Payload Input */}
                <div className="space-y-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Base Payload</label>
                    <Textarea
                      placeholder="Enter the base payload to mutate..."
                      value={basePayload}
                      onChange={(e) => setBasePayload(e.target.value)}
                      rows={6}
                      className="font-mono"
                    />
                  </div>

                  <div className="space-y-2">
                    <label className="text-sm font-medium">Quick Templates</label>
                    <div className="space-y-2">
                      {PAYLOAD_TEMPLATES.map((template) => (
                        <Button
                          key={template.id}
                          variant="outline"
                          size="sm"
                          onClick={() => setBasePayload(template.basePayload)}
                          className="w-full justify-start"
                        >
                          <Code className="h-4 w-4 mr-2" />
                          {template.name}
                        </Button>
                      ))}
                    </div>
                  </div>
                </div>

                {/* Mutation Rules */}
                <div className="space-y-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Mutation Rules</label>
                    <div className="space-y-2 max-h-64 overflow-y-auto">
                      {MUTATION_RULES.map((rule) => (
                        <Card key={rule.id} className="p-3">
                          <div className="flex items-start space-x-3">
                            <input
                              type="checkbox"
                              checked={selectedMutations.includes(rule.id)}
                              onChange={(e) => {
                                if (e.target.checked) {
                                  setSelectedMutations([...selectedMutations, rule.id]);
                                } else {
                                  setSelectedMutations(selectedMutations.filter(id => id !== rule.id));
                                }
                              }}
                              className="mt-1 rounded"
                            />
                            <div className="flex-1">
                              <h4 className="font-medium">{rule.name}</h4>
                              <p className="text-sm text-gray-600">{rule.description}</p>
                              <Badge variant="outline" className="mt-1 text-xs">
                                {rule.category}
                              </Badge>
                            </div>
                          </div>
                        </Card>
                      ))}
                    </div>
                  </div>
                </div>
              </div>

              {/* Generated Mutations */}
              {mutatedPayloads.length > 0 && (
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold">Generated Mutations</h3>
                  <div className="space-y-3">
                    {mutatedPayloads.map((payload) => (
                      <Card key={payload.id} className="p-4">
                        <div className="space-y-3">
                          <div className="flex items-start justify-between">
                            <div>
                              <h4 className="font-semibold">{payload.name}</h4>
                              <p className="text-sm text-gray-600">{payload.description}</p>
                            </div>
                            <div className="flex items-center space-x-2">
                              <Badge className={getComplexityColor(payload.complexity)} variant="outline">
                                {payload.complexity}
                              </Badge>
                              <Badge variant="secondary">{payload.noveltyScore}% novel</Badge>
                            </div>
                          </div>
                          
                          <div className="p-3 bg-gray-50 rounded-lg font-mono text-sm break-all">
                            {payload.payload}
                          </div>
                          
                          <div className="flex items-center justify-between">
                            <div className="flex flex-wrap gap-1">
                              {payload.bypassTechniques.map((technique) => (
                                <Badge key={technique} variant="outline" className="text-xs">
                                  {technique}
                                </Badge>
                              ))}
                            </div>
                            <div className="flex space-x-2">
                              <Button size="sm" variant="outline" onClick={() => navigator.clipboard.writeText(payload.payload)}>
                                <Copy className="h-4 w-4" />
                              </Button>
                              <Button size="sm" variant="outline" onClick={() => exportPayload(payload)}>
                                <Download className="h-4 w-4" />
                              </Button>
                            </div>
                          </div>
                        </div>
                      </Card>
                    ))}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Payload Library Tab */}
        <TabsContent value="library" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Database className="h-5 w-5" />
                <span>Payload Library</span>
              </CardTitle>
              <CardDescription>Collection of generated and curated exploit payloads</CardDescription>
            </CardHeader>
            <CardContent>
              {generatedPayloads.length > 0 ? (
                <div className="space-y-4">
                  {/* Library Stats */}
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                    <Card className="p-4">
                      <div className="flex items-center space-x-3">
                        <Package className="h-8 w-8 text-blue-600" />
                        <div>
                          <p className="text-sm text-gray-600">Total Payloads</p>
                          <p className="text-2xl font-bold">{generatedPayloads.length}</p>
                        </div>
                      </div>
                    </Card>
                    <Card className="p-4">
                      <div className="flex items-center space-x-3">
                        <Sparkles className="h-8 w-8 text-purple-600" />
                        <div>
                          <p className="text-sm text-gray-600">AI Generated</p>
                          <p className="text-2xl font-bold">
                            {generatedPayloads.filter(p => p.aiModel !== 'mutation_engine').length}
                          </p>
                        </div>
                      </div>
                    </Card>
                    <Card className="p-4">
                      <div className="flex items-center space-x-3">
                        <Gauge className="h-8 w-8 text-green-600" />
                        <div>
                          <p className="text-sm text-gray-600">Avg Confidence</p>
                          <p className="text-2xl font-bold">
                            {Math.round(generatedPayloads.reduce((sum, p) => sum + p.confidence, 0) / generatedPayloads.length)}%
                          </p>
                        </div>
                      </div>
                    </Card>
                    <Card className="p-4">
                      <div className="flex items-center space-x-3">
                        <Star className="h-8 w-8 text-yellow-600" />
                        <div>
                          <p className="text-sm text-gray-600">Avg Novelty</p>
                          <p className="text-2xl font-bold">
                            {Math.round(generatedPayloads.reduce((sum, p) => sum + p.noveltyScore, 0) / generatedPayloads.length)}%
                          </p>
                        </div>
                      </div>
                    </Card>
                  </div>

                  {/* Payload Grid */}
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                    {generatedPayloads.map((payload) => {
                      const TypeIcon = getTypeIcon(payload.type);
                      return (
                        <Card key={payload.id} 
                              className={`p-4 cursor-pointer transition-all hover:shadow-md ${
                                selectedPayload?.id === payload.id ? 'ring-2 ring-purple-500' : ''
                              }`}
                              onClick={() => setSelectedPayload(payload)}>
                          <div className="space-y-3">
                            <div className="flex items-start justify-between">
                              <div className="flex items-center space-x-3">
                                <div className="p-2 bg-purple-100 rounded-lg">
                                  <TypeIcon className="h-5 w-5 text-purple-600" />
                                </div>
                                <div>
                                  <h4 className="font-semibold">{payload.name}</h4>
                                  <p className="text-sm text-gray-600 capitalize">{payload.type.replace('_', ' ')}</p>
                                </div>
                              </div>
                              <Badge className={getComplexityColor(payload.complexity)} variant="outline">
                                {payload.complexity}
                              </Badge>
                            </div>
                            
                            <p className="text-sm text-gray-600">{payload.description}</p>
                            
                            <div className="flex items-center justify-between">
                              <div className="flex space-x-4 text-sm text-gray-500">
                                <span>Confidence: {payload.confidence}%</span>
                                <span>Novelty: {payload.noveltyScore}%</span>
                              </div>
                              <div className="flex space-x-1">
                                <Button size="sm" variant="outline" onClick={(e) => {
                                  e.stopPropagation();
                                  testPayload(payload);
                                }}>
                                  <TestTube className="h-4 w-4" />
                                </Button>
                                <Button size="sm" variant="outline" onClick={(e) => {
                                  e.stopPropagation();
                                  navigator.clipboard.writeText(payload.payload);
                                }}>
                                  <Copy className="h-4 w-4" />
                                </Button>
                                <Button size="sm" variant="outline" onClick={(e) => {
                                  e.stopPropagation();
                                  exportPayload(payload);
                                }}>
                                  <Download className="h-4 w-4" />
                                </Button>
                              </div>
                            </div>
                          </div>
                        </Card>
                      );
                    })}
                  </div>
                </div>
              ) : (
                <div className="text-center py-12">
                  <Package className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">No Payloads Generated</h3>
                  <p className="text-gray-600 mb-4">Generate your first AI-powered exploit payloads</p>
                  <Button onClick={() => setActiveTab('generator')} className="bg-purple-600 hover:bg-purple-700">
                    <Sparkles className="h-4 w-4 mr-2" />
                    Generate Payloads
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Testing Lab Tab */}
        <TabsContent value="testing" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <FlaskConical className="h-5 w-5" />
                <span>Payload Testing Lab</span>
              </CardTitle>
              <CardDescription>Test payload effectiveness and detection rates</CardDescription>
            </CardHeader>
            <CardContent>
              {selectedPayload ? (
                <div className="space-y-6">
                  {/* Payload Details */}
                  <Card className="p-4 bg-blue-50 border-blue-200">
                    <h3 className="font-semibold mb-2">{selectedPayload.name}</h3>
                    <div className="p-3 bg-white rounded border font-mono text-sm break-all">
                      {selectedPayload.payload}
                    </div>
                    <div className="flex items-center justify-between mt-3">
                      <div className="flex space-x-4 text-sm text-gray-600">
                        <span>Type: {selectedPayload.type.replace('_', ' ')}</span>
                        <span>Confidence: {selectedPayload.confidence}%</span>
                        <span>Novelty: {selectedPayload.noveltyScore}%</span>
                      </div>
                      <Button onClick={() => testPayload(selectedPayload)} variant="default">
                        <TestTube className="h-4 w-4 mr-2" />
                        Run Tests
                      </Button>
                    </div>
                  </Card>

                  {/* Test Results */}
                  {selectedPayload.testResults && (
                    <div className="space-y-4">
                      <h3 className="text-lg font-semibold">Test Results</h3>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        {selectedPayload.testResults.map((result, index) => (
                          <Card key={index} className="p-4">
                            <div className="flex items-center justify-between mb-2">
                              <h4 className="font-semibold capitalize">{result.testType.replace('_', ' ')}</h4>
                              <Badge variant={result.success ? 'default' : 'destructive'}>
                                {result.success ? 'Passed' : 'Failed'}
                              </Badge>
                            </div>
                            <p className="text-sm text-gray-600 mb-2">{result.response}</p>
                            <div className="space-y-1">
                              <div className="text-sm">Detection Rate: {result.detectionRate}%</div>
                              <Progress value={result.detectionRate} className="h-2" />
                            </div>
                            {result.bypassedControls.length > 0 && (
                              <div className="mt-2">
                                <p className="text-xs text-gray-500 mb-1">Bypassed Controls:</p>
                                <div className="flex flex-wrap gap-1">
                                  {result.bypassedControls.map((control) => (
                                    <Badge key={control} variant="outline" className="text-xs">
                                      {control}
                                    </Badge>
                                  ))}
                                </div>
                              </div>
                            )}
                          </Card>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              ) : (
                <div className="text-center py-12">
                  <TestTube className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">No Payload Selected</h3>
                  <p className="text-gray-600 mb-4">Select a payload from the library to run tests</p>
                  <Button onClick={() => setActiveTab('library')} variant="outline">
                    <Database className="h-4 w-4 mr-2" />
                    View Library
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Education Tab */}
        <TabsContent value="education" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Book className="h-5 w-5" />
                <span>Educational Resources</span>
              </CardTitle>
              <CardDescription>Learn about exploit development and defensive measures</CardDescription>
            </CardHeader>
            <CardContent>
              {selectedPayload ? (
                <div className="space-y-6">
                  {/* Educational Content */}
                  <Card className="p-4 bg-green-50 border-green-200">
                    <h3 className="font-semibold mb-2 flex items-center space-x-2">
                      <Lightbulb className="h-5 w-5 text-green-600" />
                      <span>Educational Explanation</span>
                    </h3>
                    <p className="text-sm text-green-800">
                      {selectedPayload.educational || 'This payload demonstrates advanced exploit techniques for educational purposes. Always ensure you have proper authorization before testing.'}
                    </p>
                  </Card>

                  {/* Mitigation Strategies */}
                  <Card className="p-4 bg-blue-50 border-blue-200">
                    <h3 className="font-semibold mb-2 flex items-center space-x-2">
                      <Shield className="h-5 w-5 text-blue-600" />
                      <span>Mitigation Strategies</span>
                    </h3>
                    <p className="text-sm text-blue-800">
                      {selectedPayload.mitigation || 'Implement proper input validation, output encoding, and security controls to prevent this type of attack.'}
                    </p>
                  </Card>

                  {/* Bypass Techniques Explanation */}
                  <Card className="p-4">
                    <h3 className="font-semibold mb-3">Bypass Techniques Used</h3>
                    <div className="space-y-2">
                      {selectedPayload.bypassTechniques.map((technique) => (
                        <div key={technique} className="flex items-start space-x-3">
                          <div className="p-1 bg-purple-100 rounded">
                            <Zap className="h-4 w-4 text-purple-600" />
                          </div>
                          <div>
                            <h4 className="font-medium">{technique}</h4>
                            <p className="text-sm text-gray-600">
                              {BYPASS_TECHNIQUES.find(t => t.name === technique)?.description || 
                               'Advanced technique for bypassing security controls'}
                            </p>
                          </div>
                        </div>
                      ))}
                    </div>
                  </Card>
                </div>
              ) : (
                <div className="space-y-6">
                  <Card className="p-6 bg-amber-50 border-amber-200">
                    <div className="flex items-start space-x-3">
                      <AlertTriangle className="h-6 w-6 text-amber-600 mt-1" />
                      <div>
                        <h3 className="font-semibold text-amber-800 mb-2">Educational Use Only</h3>
                        <p className="text-sm text-amber-700">
                          The Creative Exploit Engine is designed for educational purposes and authorized penetration testing only. 
                          All generated payloads include educational explanations and mitigation strategies to promote responsible security research.
                        </p>
                      </div>
                    </div>
                  </Card>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <Card className="p-4">
                      <h3 className="font-semibold mb-3 flex items-center space-x-2">
                        <Target className="h-5 w-5" />
                        <span>Responsible Testing</span>
                      </h3>
                      <ul className="text-sm text-gray-600 space-y-1">
                        <li>• Always obtain proper authorization</li>
                        <li>• Test only on your own systems or authorized targets</li>
                        <li>• Follow responsible disclosure practices</li>
                        <li>• Document findings for defensive improvements</li>
                      </ul>
                    </Card>

                    <Card className="p-4">
                      <h3 className="font-semibold mb-3 flex items-center space-x-2">
                        <Shield className="h-5 w-5" />
                        <span>Defensive Mindset</span>
                      </h3>
                      <ul className="text-sm text-gray-600 space-y-1">
                        <li>• Understand attack vectors to defend better</li>
                        <li>• Implement defense in depth</li>
                        <li>• Regular security assessments</li>
                        <li>• Keep systems updated and patched</li>
                      </ul>
                    </Card>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};