#!/usr/bin/env python3
"""
Automated Evidence Collection Framework for NexusScan Desktop
Comprehensive evidence automation with legal-standard documentation and chain of custody.
"""

import asyncio
import logging
import json
import os
import shutil
import hashlib
import zipfile
import base64
import time
import subprocess
from datetime import datetime, timed<PERSON><PERSON>
from typing import Dict, List, Optional, Any, Set, Tuple, Union
from dataclasses import dataclass, asdict, field
from enum import Enum
from pathlib import Path
import tempfile
import sqlite3

from core.config import Config
from core.database import DatabaseManager
from ai.ai_service import AIServiceManager, AnalysisRequest

logger = logging.getLogger(__name__)


class EvidenceType(Enum):
    """Types of evidence that can be collected"""
    SCREENSHOT = "screenshot"
    COMMAND_OUTPUT = "command_output"
    FILE_CONTENT = "file_content"
    NETWORK_TRAFFIC = "network_traffic"
    SYSTEM_STATE = "system_state"
    EXPLOIT_PROOF = "exploit_proof"
    VULNERABILITY_SCAN = "vulnerability_scan"
    LOG_FILE = "log_file"
    CONFIGURATION = "configuration"
    TIMELINE_EVENT = "timeline_event"


class ComplianceStandard(Enum):
    """Compliance standards for evidence formatting"""
    SOC2_TYPE_II = "soc2_type_ii"
    HIPAA = "hipaa"
    PCI_DSS = "pci_dss"
    ISO_27001 = "iso_27001"
    NIST_CSF = "nist_csf"
    LEGAL_DISCOVERY = "legal_discovery"
    FORENSIC_ANALYSIS = "forensic_analysis"
    INCIDENT_RESPONSE = "incident_response"


class EvidenceClassification(Enum):
    """Classification levels for evidence"""
    PUBLIC = "public"
    INTERNAL = "internal"
    CONFIDENTIAL = "confidential"
    RESTRICTED = "restricted"
    TOP_SECRET = "top_secret"


@dataclass
class EvidenceMetadata:
    """Metadata for evidence items"""
    evidence_id: str
    evidence_type: EvidenceType
    classification: EvidenceClassification
    title: str
    description: str
    source_system: str
    source_tool: str
    collection_method: str
    collector_user: str
    collection_timestamp: datetime
    file_path: Optional[str]
    file_size: int
    file_hash_md5: Optional[str]
    file_hash_sha256: Optional[str]
    related_vulnerability: Optional[str]
    related_target: Optional[str]
    compliance_tags: List[ComplianceStandard]
    legal_hold: bool = False
    retention_period: int = 365  # days
    chain_of_custody: List[Dict[str, Any]] = field(default_factory=list)
    verification_status: str = "pending"
    notes: str = ""


@dataclass
class EvidencePackage:
    """Complete evidence package for a security operation"""
    package_id: str
    campaign_id: str
    package_name: str
    description: str
    created_by: str
    created_at: datetime
    evidence_items: List[EvidenceMetadata]
    compliance_standards: List[ComplianceStandard]
    classification_level: EvidenceClassification
    package_hash: Optional[str]
    package_file_path: Optional[str]
    legal_disclaimer: str
    chain_of_custody_log: List[Dict[str, Any]] = field(default_factory=list)
    verification_signatures: List[Dict[str, Any]] = field(default_factory=list)
    export_formats: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)


class AutomatedEvidenceCollector:
    """Comprehensive evidence collection automation system"""

    def __init__(self, config: Config, database: DatabaseManager, ai_service: AIServiceManager):
        """Initialize the evidence collector"""
        self.config = config
        self.database = database
        self.ai_service = ai_service
        
        # Evidence storage configuration
        self.evidence_base_path = Path(self.config.get('evidence_storage_path', './evidence'))
        self.evidence_base_path.mkdir(parents=True, exist_ok=True)
        
        # Temporary working directory
        self.temp_dir = Path(tempfile.mkdtemp(prefix='nexusscan_evidence_'))
        
        # Evidence tracking
        self.active_collections: Dict[str, EvidencePackage] = {}
        self.collection_queue: List[Dict[str, Any]] = []
        
        # Legal templates
        self.legal_templates = self._load_legal_templates()
        
        # File type handlers
        self.file_handlers = {
            '.txt': self._handle_text_file,
            '.log': self._handle_log_file,
            '.json': self._handle_json_file,
            '.xml': self._handle_xml_file,
            '.csv': self._handle_csv_file,
            '.png': self._handle_image_file,
            '.jpg': self._handle_image_file,
            '.jpeg': self._handle_image_file,
            '.pcap': self._handle_pcap_file,
            '.pcapng': self._handle_pcap_file
        }
        
        logger.info(f"Evidence Collector initialized - Storage: {self.evidence_base_path}")

    def _load_legal_templates(self) -> Dict[str, str]:
        """Load legal disclaimer templates"""
        return {
            "penetration_testing": """
PENETRATION TESTING EVIDENCE DISCLAIMER

This evidence package contains materials collected during authorized penetration testing activities.
The collection and analysis of this evidence was conducted under proper legal authorization.

AUTHORIZATION DETAILS:
• Written authorization obtained from target system owner
• Testing scope and limitations clearly defined
• All activities conducted within authorized timeframe
• Evidence collected only from authorized systems

LEGAL COMPLIANCE:
• Evidence collection follows applicable data protection laws
• Chain of custody maintained throughout collection process
• Evidence integrity verified through cryptographic hashing
• Access restricted to authorized personnel only

This evidence is intended solely for security assessment and improvement purposes.
            """,
            
            "compliance_audit": """
COMPLIANCE AUDIT EVIDENCE DISCLAIMER

This evidence package contains materials collected during compliance assessment activities.
All evidence collection was conducted in accordance with applicable regulatory requirements.

COMPLIANCE FRAMEWORKS:
• SOC 2 Type II - Security and Availability
• HIPAA - Health Information Protection
• PCI DSS - Payment Card Industry Standards
• ISO 27001 - Information Security Management

EVIDENCE HANDLING:
• Collected under proper audit protocols
• Chain of custody maintained per regulatory requirements
• Evidence integrity verified and documented
• Retention period aligned with regulatory standards

This evidence supports compliance validation and risk assessment activities.
            """
        }

    async def capture_exploitation_proof(self, exploit_results: Dict[str, Any]) -> EvidenceMetadata:
        """Automatically capture proof of successful exploitation"""
        
        try:
            evidence_id = f"exploit_proof_{int(time.time())}"
            
            # Create evidence metadata
            evidence = EvidenceMetadata(
                evidence_id=evidence_id,
                evidence_type=EvidenceType.EXPLOIT_PROOF,
                classification=EvidenceClassification.CONFIDENTIAL,
                title=f"Exploitation Proof - {exploit_results.get('vulnerability_type', 'Unknown')}",
                description=f"Automated capture of successful exploitation: {exploit_results.get('description', 'No description')}",
                source_system=exploit_results.get('target', 'Unknown'),
                source_tool=exploit_results.get('tool_name', 'Unknown'),
                collection_method="automated_exploitation_capture",
                collector_user="nexusscan_system",
                collection_timestamp=datetime.now(),
                file_path=None,
                file_size=0,
                file_hash_md5=None,
                file_hash_sha256=None,
                related_vulnerability=exploit_results.get('vulnerability_id'),
                related_target=exploit_results.get('target'),
                compliance_tags=[ComplianceStandard.INCIDENT_RESPONSE, ComplianceStandard.FORENSIC_ANALYSIS],
                legal_hold=True
            )
            
            # Create evidence file
            evidence_data = {
                "exploit_summary": {
                    "vulnerability_type": exploit_results.get('vulnerability_type'),
                    "severity": exploit_results.get('severity'),
                    "target_system": exploit_results.get('target'),
                    "exploit_timestamp": exploit_results.get('timestamp', datetime.now().isoformat()),
                    "success_indicators": exploit_results.get('success_indicators', []),
                    "impact_assessment": exploit_results.get('impact_assessment', {})
                },
                "technical_details": {
                    "command_executed": exploit_results.get('command'),
                    "response_received": exploit_results.get('response'),
                    "payload_used": exploit_results.get('payload'),
                    "tool_output": exploit_results.get('tool_output'),
                    "network_traffic": exploit_results.get('network_traffic', [])
                },
                "verification_data": {
                    "screenshot_captured": exploit_results.get('screenshot_path') is not None,
                    "command_output_saved": exploit_results.get('command_output') is not None,
                    "file_access_verified": exploit_results.get('file_access', False),
                    "persistence_established": exploit_results.get('persistence', False)
                },
                "remediation_info": {
                    "recommended_fixes": exploit_results.get('recommended_fixes', []),
                    "patch_information": exploit_results.get('patch_info', {}),
                    "temporary_mitigations": exploit_results.get('mitigations', [])
                }
            }
            
            # Save evidence file
            evidence_file = self.evidence_base_path / f"{evidence_id}.json"
            with open(evidence_file, 'w') as f:
                json.dump(evidence_data, f, indent=2, default=str)
            
            # Update metadata with file information
            evidence.file_path = str(evidence_file)
            evidence.file_size = evidence_file.stat().st_size
            evidence.file_hash_md5 = self._calculate_file_hash(evidence_file, 'md5')
            evidence.file_hash_sha256 = self._calculate_file_hash(evidence_file, 'sha256')
            
            # Add to chain of custody
            self._add_chain_of_custody_entry(evidence, "Evidence created and secured")
            
            # Store in database
            await self._store_evidence_metadata(evidence)
            
            logger.info(f"Exploitation proof captured: {evidence_id}")
            
            return evidence
            
        except Exception as e:
            logger.error(f"Failed to capture exploitation proof: {e}")
            raise

    async def generate_timeline_evidence(self, attack_chain_execution: Dict[str, Any]) -> EvidenceMetadata:
        """Create detailed timeline of attack progression with timestamps"""
        
        try:
            evidence_id = f"timeline_{int(time.time())}"
            
            # Create timeline evidence
            timeline_data = {
                "attack_chain_summary": {
                    "campaign_id": attack_chain_execution.get('campaign_id'),
                    "attack_name": attack_chain_execution.get('attack_name'),
                    "target_system": attack_chain_execution.get('target'),
                    "start_time": attack_chain_execution.get('start_time'),
                    "end_time": attack_chain_execution.get('end_time'),
                    "total_duration": attack_chain_execution.get('duration'),
                    "attack_phases": attack_chain_execution.get('phases', [])
                },
                "detailed_timeline": [],
                "phase_analysis": {},
                "success_metrics": {
                    "total_steps": 0,
                    "successful_steps": 0,
                    "failed_steps": 0,
                    "critical_findings": []
                }
            }
            
            # Process execution events
            events = attack_chain_execution.get('execution_events', [])
            for event in events:
                timeline_entry = {
                    "timestamp": event.get('timestamp'),
                    "phase": event.get('phase'),
                    "step_number": event.get('step_number'),
                    "action_type": event.get('action_type'),
                    "tool_used": event.get('tool'),
                    "command": event.get('command'),
                    "target": event.get('target'),
                    "status": event.get('status'),
                    "output_summary": event.get('output_summary'),
                    "vulnerabilities_found": event.get('vulnerabilities', []),
                    "evidence_collected": event.get('evidence_files', []),
                    "duration_seconds": event.get('duration'),
                    "success_indicators": event.get('success_indicators', []),
                    "failure_reasons": event.get('failure_reasons', [])
                }
                timeline_data["detailed_timeline"].append(timeline_entry)
                
                # Update metrics
                timeline_data["success_metrics"]["total_steps"] += 1
                if event.get('status') == 'success':
                    timeline_data["success_metrics"]["successful_steps"] += 1
                else:
                    timeline_data["success_metrics"]["failed_steps"] += 1
            
            # Generate phase analysis
            phases = set(event.get('phase') for event in events if event.get('phase'))
            for phase in phases:
                phase_events = [e for e in events if e.get('phase') == phase]
                phase_analysis = {
                    "phase_name": phase,
                    "start_time": min(e.get('timestamp') for e in phase_events),
                    "end_time": max(e.get('timestamp') for e in phase_events),
                    "total_steps": len(phase_events),
                    "successful_steps": len([e for e in phase_events if e.get('status') == 'success']),
                    "key_findings": [],
                    "tools_used": list(set(e.get('tool') for e in phase_events if e.get('tool'))),
                    "vulnerabilities_discovered": []
                }
                
                # Collect vulnerabilities for this phase
                for event in phase_events:
                    if event.get('vulnerabilities'):
                        phase_analysis["vulnerabilities_discovered"].extend(event.get('vulnerabilities'))
                
                timeline_data["phase_analysis"][phase] = phase_analysis
            
            # Create evidence metadata
            evidence = EvidenceMetadata(
                evidence_id=evidence_id,
                evidence_type=EvidenceType.TIMELINE_EVENT,
                classification=EvidenceClassification.CONFIDENTIAL,
                title=f"Attack Timeline - {attack_chain_execution.get('attack_name', 'Unknown')}",
                description=f"Comprehensive timeline of attack execution with {len(events)} events",
                source_system=attack_chain_execution.get('target', 'Unknown'),
                source_tool="nexusscan_orchestrator",
                collection_method="automated_timeline_generation",
                collector_user="nexusscan_system",
                collection_timestamp=datetime.now(),
                file_path=None,
                file_size=0,
                file_hash_md5=None,
                file_hash_sha256=None,
                related_vulnerability=None,
                related_target=attack_chain_execution.get('target'),
                compliance_tags=[ComplianceStandard.INCIDENT_RESPONSE, ComplianceStandard.FORENSIC_ANALYSIS],
                legal_hold=True
            )
            
            # Save timeline file
            timeline_file = self.evidence_base_path / f"{evidence_id}.json"
            with open(timeline_file, 'w') as f:
                json.dump(timeline_data, f, indent=2, default=str)
            
            # Update metadata
            evidence.file_path = str(timeline_file)
            evidence.file_size = timeline_file.stat().st_size
            evidence.file_hash_md5 = self._calculate_file_hash(timeline_file, 'md5')
            evidence.file_hash_sha256 = self._calculate_file_hash(timeline_file, 'sha256')
            
            # Chain of custody
            self._add_chain_of_custody_entry(evidence, "Timeline evidence generated and secured")
            
            # Store in database
            await self._store_evidence_metadata(evidence)
            
            logger.info(f"Timeline evidence generated: {evidence_id}")
            
            return evidence
            
        except Exception as e:
            logger.error(f"Failed to generate timeline evidence: {e}")
            raise

    async def package_compliance_evidence(self, scan_results: Dict[str, Any], 
                                        compliance_type: ComplianceStandard) -> EvidencePackage:
        """Format evidence for specific compliance requirements"""
        
        try:
            package_id = f"compliance_{compliance_type.value}_{int(time.time())}"
            
            # Create evidence package
            evidence_package = EvidencePackage(
                package_id=package_id,
                campaign_id=scan_results.get('campaign_id', 'unknown'),
                package_name=f"Compliance Evidence - {compliance_type.value.upper()}",
                description=f"Evidence package formatted for {compliance_type.value} compliance requirements",
                created_by="nexusscan_system",
                created_at=datetime.now(),
                evidence_items=[],
                compliance_standards=[compliance_type],
                classification_level=EvidenceClassification.CONFIDENTIAL,
                package_hash=None,
                package_file_path=None,
                legal_disclaimer=self.legal_templates.get("compliance_audit", "")
            )
            
            # Generate compliance-specific evidence based on standard
            if compliance_type == ComplianceStandard.SOC2_TYPE_II:
                await self._generate_soc2_evidence(evidence_package, scan_results)
            elif compliance_type == ComplianceStandard.PCI_DSS:
                await self._generate_pci_evidence(evidence_package, scan_results)
            elif compliance_type == ComplianceStandard.HIPAA:
                await self._generate_hipaa_evidence(evidence_package, scan_results)
            elif compliance_type == ComplianceStandard.ISO_27001:
                await self._generate_iso27001_evidence(evidence_package, scan_results)
            else:
                await self._generate_generic_compliance_evidence(evidence_package, scan_results)
            
            # Create package file
            package_file = await self._create_evidence_package_file(evidence_package)
            evidence_package.package_file_path = str(package_file)
            evidence_package.package_hash = self._calculate_file_hash(package_file, 'sha256')
            
            # Add to chain of custody
            self._add_package_chain_of_custody_entry(evidence_package, "Compliance evidence package created")
            
            # Store package in database
            await self._store_evidence_package(evidence_package)
            
            logger.info(f"Compliance evidence package created: {package_id}")
            
            return evidence_package
            
        except Exception as e:
            logger.error(f"Failed to create compliance evidence package: {e}")
            raise

    async def create_court_ready_documentation(self, evidence_package: EvidencePackage) -> Path:
        """Legal-standard evidence packaging with chain of custody"""
        
        try:
            # Create court-ready package directory
            court_package_dir = self.evidence_base_path / f"court_ready_{evidence_package.package_id}"
            court_package_dir.mkdir(exist_ok=True)
            
            # Generate comprehensive documentation
            documentation = {
                "evidence_package_summary": {
                    "package_id": evidence_package.package_id,
                    "creation_date": evidence_package.created_at.isoformat(),
                    "creator": evidence_package.created_by,
                    "total_evidence_items": len(evidence_package.evidence_items),
                    "classification": evidence_package.classification_level.value,
                    "integrity_hash": evidence_package.package_hash
                },
                "legal_certification": {
                    "chain_of_custody_verified": True,
                    "evidence_integrity_verified": True,
                    "collection_authorization_documented": True,
                    "retention_policy_applied": True,
                    "access_controls_implemented": True
                },
                "evidence_inventory": [],
                "chain_of_custody_log": evidence_package.chain_of_custody_log,
                "verification_signatures": evidence_package.verification_signatures,
                "technical_specifications": {
                    "collection_tools": [],
                    "forensic_procedures": [],
                    "verification_methods": [],
                    "storage_methods": []
                },
                "legal_disclaimers": evidence_package.legal_disclaimer
            }
            
            # Document each evidence item
            for evidence in evidence_package.evidence_items:
                evidence_doc = {
                    "evidence_id": evidence.evidence_id,
                    "type": evidence.evidence_type.value,
                    "title": evidence.title,
                    "collection_timestamp": evidence.collection_timestamp.isoformat(),
                    "collector": evidence.collector_user,
                    "source_system": evidence.source_system,
                    "file_hash_sha256": evidence.file_hash_sha256,
                    "file_size_bytes": evidence.file_size,
                    "chain_of_custody": evidence.chain_of_custody,
                    "verification_status": evidence.verification_status,
                    "legal_hold_status": evidence.legal_hold
                }
                documentation["evidence_inventory"].append(evidence_doc)
            
            # Save documentation
            doc_file = court_package_dir / "evidence_documentation.json"
            with open(doc_file, 'w') as f:
                json.dump(documentation, f, indent=2, default=str)
            
            # Copy all evidence files
            evidence_files_dir = court_package_dir / "evidence_files"
            evidence_files_dir.mkdir(exist_ok=True)
            
            for evidence in evidence_package.evidence_items:
                if evidence.file_path and Path(evidence.file_path).exists():
                    shutil.copy2(evidence.file_path, evidence_files_dir / f"{evidence.evidence_id}_{Path(evidence.file_path).name}")
            
            # Generate integrity verification file
            integrity_file = court_package_dir / "integrity_verification.txt"
            with open(integrity_file, 'w') as f:
                f.write("EVIDENCE PACKAGE INTEGRITY VERIFICATION\n")
                f.write("=" * 50 + "\n\n")
                f.write(f"Package ID: {evidence_package.package_id}\n")
                f.write(f"Creation Date: {evidence_package.created_at}\n")
                f.write(f"Package Hash: {evidence_package.package_hash}\n\n")
                
                f.write("INDIVIDUAL FILE VERIFICATION:\n")
                for evidence in evidence_package.evidence_items:
                    f.write(f"File: {evidence.evidence_id}\n")
                    f.write(f"  SHA256: {evidence.file_hash_sha256}\n")
                    f.write(f"  MD5: {evidence.file_hash_md5}\n")
                    f.write(f"  Size: {evidence.file_size} bytes\n\n")
            
            # Create final court-ready ZIP package
            court_package_zip = self.evidence_base_path / f"court_ready_{evidence_package.package_id}.zip"
            with zipfile.ZipFile(court_package_zip, 'w', zipfile.ZIP_DEFLATED) as zipf:
                for file_path in court_package_dir.rglob('*'):
                    if file_path.is_file():
                        arcname = file_path.relative_to(court_package_dir)
                        zipf.write(file_path, arcname)
            
            logger.info(f"Court-ready documentation created: {court_package_zip}")
            
            return court_package_zip
            
        except Exception as e:
            logger.error(f"Failed to create court-ready documentation: {e}")
            raise

    async def _generate_soc2_evidence(self, package: EvidencePackage, scan_results: Dict[str, Any]):
        """Generate SOC 2 Type II specific evidence"""
        
        # Security controls evidence
        security_controls = {
            "access_controls": scan_results.get('access_control_findings', []),
            "network_security": scan_results.get('network_security_findings', []),
            "vulnerability_management": scan_results.get('vulnerability_findings', []),
            "incident_response": scan_results.get('incident_response_findings', []),
            "monitoring_logging": scan_results.get('monitoring_findings', [])
        }
        
        evidence_id = f"soc2_controls_{int(time.time())}"
        evidence = await self._create_evidence_from_data(
            evidence_id,
            EvidenceType.CONFIGURATION,
            "SOC 2 Security Controls Assessment",
            security_controls,
            [ComplianceStandard.SOC2_TYPE_II]
        )
        package.evidence_items.append(evidence)

    async def _generate_pci_evidence(self, package: EvidencePackage, scan_results: Dict[str, Any]):
        """Generate PCI DSS specific evidence"""
        
        pci_requirements = {
            "network_security": scan_results.get('network_findings', []),
            "encryption_protocols": scan_results.get('encryption_findings', []),
            "access_controls": scan_results.get('access_findings', []),
            "monitoring_systems": scan_results.get('monitoring_findings', []),
            "vulnerability_scans": scan_results.get('vulnerability_findings', [])
        }
        
        evidence_id = f"pci_assessment_{int(time.time())}"
        evidence = await self._create_evidence_from_data(
            evidence_id,
            EvidenceType.VULNERABILITY_SCAN,
            "PCI DSS Compliance Assessment",
            pci_requirements,
            [ComplianceStandard.PCI_DSS]
        )
        package.evidence_items.append(evidence)

    async def _generate_hipaa_evidence(self, package: EvidencePackage, scan_results: Dict[str, Any]):
        """Generate HIPAA specific evidence"""
        
        hipaa_safeguards = {
            "administrative_safeguards": scan_results.get('admin_findings', []),
            "physical_safeguards": scan_results.get('physical_findings', []),
            "technical_safeguards": scan_results.get('technical_findings', []),
            "breach_risk_assessment": scan_results.get('breach_risks', [])
        }
        
        evidence_id = f"hipaa_safeguards_{int(time.time())}"
        evidence = await self._create_evidence_from_data(
            evidence_id,
            EvidenceType.SYSTEM_STATE,
            "HIPAA Security Safeguards Assessment",
            hipaa_safeguards,
            [ComplianceStandard.HIPAA]
        )
        package.evidence_items.append(evidence)

    async def _generate_iso27001_evidence(self, package: EvidencePackage, scan_results: Dict[str, Any]):
        """Generate ISO 27001 specific evidence"""
        
        iso_controls = {
            "information_security_policies": scan_results.get('policy_findings', []),
            "asset_management": scan_results.get('asset_findings', []),
            "access_control": scan_results.get('access_findings', []),
            "cryptography": scan_results.get('crypto_findings', []),
            "operations_security": scan_results.get('ops_findings', []),
            "incident_management": scan_results.get('incident_findings', [])
        }
        
        evidence_id = f"iso27001_controls_{int(time.time())}"
        evidence = await self._create_evidence_from_data(
            evidence_id,
            EvidenceType.CONFIGURATION,
            "ISO 27001 Information Security Controls",
            iso_controls,
            [ComplianceStandard.ISO_27001]
        )
        package.evidence_items.append(evidence)

    async def _generate_generic_compliance_evidence(self, package: EvidencePackage, scan_results: Dict[str, Any]):
        """Generate generic compliance evidence"""
        
        generic_evidence = {
            "security_findings": scan_results.get('security_findings', []),
            "vulnerability_assessment": scan_results.get('vulnerabilities', []),
            "configuration_review": scan_results.get('configurations', []),
            "risk_assessment": scan_results.get('risks', [])
        }
        
        evidence_id = f"generic_compliance_{int(time.time())}"
        evidence = await self._create_evidence_from_data(
            evidence_id,
            EvidenceType.VULNERABILITY_SCAN,
            "Generic Compliance Assessment",
            generic_evidence,
            []
        )
        package.evidence_items.append(evidence)

    async def _create_evidence_from_data(self, evidence_id: str, evidence_type: EvidenceType,
                                       title: str, data: Dict[str, Any], 
                                       compliance_tags: List[ComplianceStandard]) -> EvidenceMetadata:
        """Create evidence metadata and file from data"""
        
        # Create evidence metadata
        evidence = EvidenceMetadata(
            evidence_id=evidence_id,
            evidence_type=evidence_type,
            classification=EvidenceClassification.CONFIDENTIAL,
            title=title,
            description=f"Automated collection: {title}",
            source_system="nexusscan_system",
            source_tool="evidence_collector",
            collection_method="automated_data_collection",
            collector_user="nexusscan_system",
            collection_timestamp=datetime.now(),
            file_path=None,
            file_size=0,
            file_hash_md5=None,
            file_hash_sha256=None,
            related_vulnerability=None,
            related_target=None,
            compliance_tags=compliance_tags,
            legal_hold=True
        )
        
        # Save data file
        evidence_file = self.evidence_base_path / f"{evidence_id}.json"
        with open(evidence_file, 'w') as f:
            json.dump(data, f, indent=2, default=str)
        
        # Update metadata
        evidence.file_path = str(evidence_file)
        evidence.file_size = evidence_file.stat().st_size
        evidence.file_hash_md5 = self._calculate_file_hash(evidence_file, 'md5')
        evidence.file_hash_sha256 = self._calculate_file_hash(evidence_file, 'sha256')
        
        # Chain of custody
        self._add_chain_of_custody_entry(evidence, "Evidence data collected and secured")
        
        # Store in database
        await self._store_evidence_metadata(evidence)
        
        return evidence

    def _calculate_file_hash(self, file_path: Path, algorithm: str) -> str:
        """Calculate file hash for integrity verification"""
        
        if algorithm == 'md5':
            hash_obj = hashlib.md5()
        elif algorithm == 'sha256':
            hash_obj = hashlib.sha256()
        else:
            raise ValueError(f"Unsupported hash algorithm: {algorithm}")
        
        with open(file_path, 'rb') as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_obj.update(chunk)
        
        return hash_obj.hexdigest()

    def _add_chain_of_custody_entry(self, evidence: EvidenceMetadata, action: str):
        """Add entry to chain of custody log"""
        
        entry = {
            "timestamp": datetime.now().isoformat(),
            "action": action,
            "user": "nexusscan_system",
            "location": "evidence_collector",
            "integrity_verified": True,
            "access_method": "automated_system"
        }
        evidence.chain_of_custody.append(entry)

    def _add_package_chain_of_custody_entry(self, package: EvidencePackage, action: str):
        """Add entry to package chain of custody log"""
        
        entry = {
            "timestamp": datetime.now().isoformat(),
            "action": action,
            "user": "nexusscan_system",
            "location": "evidence_collector",
            "package_integrity_verified": True,
            "evidence_count": len(package.evidence_items)
        }
        package.chain_of_custody_log.append(entry)

    async def _create_evidence_package_file(self, package: EvidencePackage) -> Path:
        """Create compressed evidence package file"""
        
        package_file = self.evidence_base_path / f"{package.package_id}.zip"
        
        with zipfile.ZipFile(package_file, 'w', zipfile.ZIP_DEFLATED) as zipf:
            # Add package metadata
            package_metadata = asdict(package)
            zipf.writestr("package_metadata.json", json.dumps(package_metadata, indent=2, default=str))
            
            # Add evidence files
            for evidence in package.evidence_items:
                if evidence.file_path and Path(evidence.file_path).exists():
                    zipf.write(evidence.file_path, f"evidence/{evidence.evidence_id}_{Path(evidence.file_path).name}")
            
            # Add legal disclaimer
            zipf.writestr("legal_disclaimer.txt", package.legal_disclaimer)
        
        return package_file

    async def _store_evidence_metadata(self, evidence: EvidenceMetadata):
        """Store evidence metadata in database"""
        
        try:
            evidence_data = {
                "evidence_id": evidence.evidence_id,
                "evidence_type": evidence.evidence_type.value,
                "title": evidence.title,
                "description": evidence.description,
                "file_path": evidence.file_path,
                "file_hash": evidence.file_hash_sha256,
                "collection_timestamp": evidence.collection_timestamp.isoformat(),
                "metadata": json.dumps(asdict(evidence), default=str)
            }
            
            # Store in evidence_packages table (reusing existing table structure)
            self.database.cursor.execute("""
                INSERT INTO evidence_packages 
                (evidence_id, campaign_id, evidence_type, file_path, metadata, timestamp)
                VALUES (?, ?, ?, ?, ?, ?)
            """, (
                evidence.evidence_id,
                evidence.related_target or "unknown",
                evidence.evidence_type.value,
                evidence.file_path,
                json.dumps(asdict(evidence), default=str),
                evidence.collection_timestamp
            ))
            self.database.connection.commit()
            
        except Exception as e:
            logger.error(f"Failed to store evidence metadata: {e}")

    async def _store_evidence_package(self, package: EvidencePackage):
        """Store evidence package in database"""
        
        try:
            package_data = {
                "package_id": package.package_id,
                "campaign_id": package.campaign_id,
                "package_name": package.package_name,
                "file_path": package.package_file_path,
                "metadata": json.dumps(asdict(package), default=str),
                "timestamp": package.created_at
            }
            
            self.database.cursor.execute("""
                INSERT INTO evidence_packages 
                (evidence_id, campaign_id, evidence_type, file_path, metadata, timestamp)
                VALUES (?, ?, ?, ?, ?, ?)
            """, (
                package.package_id,
                package.campaign_id,
                "evidence_package",
                package.package_file_path,
                json.dumps(asdict(package), default=str),
                package.created_at
            ))
            self.database.connection.commit()
            
        except Exception as e:
            logger.error(f"Failed to store evidence package: {e}")

    # File handlers for different evidence types
    async def _handle_text_file(self, file_path: Path) -> Dict[str, Any]:
        """Handle text file evidence"""
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
        return {"type": "text", "content": content, "line_count": len(content.splitlines())}

    async def _handle_log_file(self, file_path: Path) -> Dict[str, Any]:
        """Handle log file evidence"""
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            lines = f.readlines()
        return {"type": "log", "line_count": len(lines), "sample_lines": lines[:10]}

    async def _handle_json_file(self, file_path: Path) -> Dict[str, Any]:
        """Handle JSON file evidence"""
        with open(file_path, 'r') as f:
            data = json.load(f)
        return {"type": "json", "structure": type(data).__name__, "keys": list(data.keys()) if isinstance(data, dict) else None}

    async def _handle_xml_file(self, file_path: Path) -> Dict[str, Any]:
        """Handle XML file evidence"""
        return {"type": "xml", "format": "xml_document"}

    async def _handle_csv_file(self, file_path: Path) -> Dict[str, Any]:
        """Handle CSV file evidence"""
        with open(file_path, 'r') as f:
            lines = f.readlines()
        return {"type": "csv", "row_count": len(lines), "estimated_columns": len(lines[0].split(',')) if lines else 0}

    async def _handle_image_file(self, file_path: Path) -> Dict[str, Any]:
        """Handle image file evidence"""
        return {"type": "image", "format": file_path.suffix, "file_size": file_path.stat().st_size}

    async def _handle_pcap_file(self, file_path: Path) -> Dict[str, Any]:
        """Handle PCAP file evidence"""
        return {"type": "network_capture", "format": file_path.suffix, "file_size": file_path.stat().st_size}

    def get_evidence_statistics(self) -> Dict[str, Any]:
        """Get evidence collection statistics"""
        
        return {
            "active_collections": len(self.active_collections),
            "evidence_storage_path": str(self.evidence_base_path),
            "temp_directory": str(self.temp_dir),
            "supported_file_types": list(self.file_handlers.keys()),
            "compliance_standards": [std.value for std in ComplianceStandard],
            "evidence_types": [etype.value for etype in EvidenceType]
        }

    def cleanup(self):
        """Cleanup temporary files and resources"""
        try:
            if self.temp_dir.exists():
                shutil.rmtree(self.temp_dir)
            logger.info("Evidence collector cleanup completed")
        except Exception as e:
            logger.error(f"Cleanup failed: {e}")