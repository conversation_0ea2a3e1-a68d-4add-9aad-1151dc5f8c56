"""
AI Automated Exploit Chaining Wrapper for NexusScan Desktop
Wraps the AutomatedExploitChaining as a registered security tool.
"""

import logging
from typing import Dict, List, Optional, Callable, Any
from datetime import datetime

from security.tools.tool_registry import (
    ToolMetadata, ToolCategory, ToolCapabilities,
    ScanOptions, ScanResult, ToolStatus, SecurityTool, register_tool
)
from ai.automated_exploit_chaining import (
    AutomatedExploitChaining,
    ExploitChainRequest,
    ChainStrategy,
    ExploitGoal
)
from ai.services import AIServiceManager
from core.config import Config

logger = logging.getLogger(__name__)


@register_tool
class AIExploitChainingTool(SecurityTool):
    """AI-powered automated exploit chaining tool wrapper"""
    
    def __init__(self):
        """Initialize AI exploit chaining tool"""
        super().__init__()
        self.chaining_engine = None
        
    def get_metadata(self) -> ToolMetadata:
        """Get tool metadata"""
        return ToolMetadata(
            name="ai_exploit_chaining",
            display_name="AI Exploit Chaining",
            description="Automated exploit chain generation using AI to combine multiple vulnerabilities for maximum impact",
            version="1.0.0",
            category=ToolCategory.AI_ANALYZER,
            author="NexusScan AI Team",
            capabilities=ToolCapabilities(
                supports_async=True,
                supports_progress=True,
                supports_cancellation=True,
                requires_root=False,
                network_access_required=True,
                output_formats=["json", "text", "graph"],
                supported_targets=["url", "ip", "domain", "vulnerability_list"]
            )
        )
    
    def check_availability(self) -> bool:
        """Check if AI exploit chaining is available"""
        try:
            # Test initialization with AI service manager
            config = Config()
            ai_manager = AIServiceManager(config)
            self.chaining_engine = AutomatedExploitChaining(ai_manager)
            return True
        except Exception as e:
            logger.error(f"AI Exploit Chaining not available: {e}")
            return False
    
    async def scan(self, options: ScanOptions,
                   progress_callback: Optional[Callable[[float, str], None]] = None) -> ScanResult:
        """Execute automated exploit chaining"""
        start_time = datetime.now()
        
        try:
            if not self.chaining_engine:
                config = Config()
                ai_manager = AIServiceManager(config)
                self.chaining_engine = AutomatedExploitChaining(ai_manager)
            
            # Extract chaining parameters from options
            target = options.target
            vulnerabilities = options.custom_options.get("vulnerabilities", [])
            chain_strategy = options.custom_options.get("strategy", ChainStrategy.OPTIMAL_PATH)
            exploit_goal = options.custom_options.get("goal", ExploitGoal.MAXIMUM_IMPACT)
            max_chain_length = options.custom_options.get("max_chain_length", 5)
            include_mitre_mapping = options.custom_options.get("include_mitre_mapping", True)
            
            # Create chain request
            request = ExploitChainRequest(
                target=target,
                known_vulnerabilities=vulnerabilities,
                chain_strategy=chain_strategy,
                exploit_goal=exploit_goal,
                max_chain_length=max_chain_length,
                include_mitre_mapping=include_mitre_mapping,
                constraints=options.custom_options.get("constraints", {}),
                safety_level=options.custom_options.get("safety_level", "high")
            )
            
            if progress_callback:
                progress_callback(0.1, "Initializing exploit chaining engine...")
            
            # Analyze vulnerabilities
            if progress_callback:
                progress_callback(0.2, "Analyzing vulnerability relationships...")
            
            # Generate exploit chains
            if progress_callback:
                progress_callback(0.4, "Generating potential exploit chains...")
            
            chaining_result = await self.chaining_engine.generate_exploit_chains(request)
            
            if progress_callback:
                progress_callback(0.7, "Evaluating chain effectiveness...")
            
            # Format results
            parsed_results = {
                "chaining_summary": {
                    "target": target,
                    "total_chains_generated": chaining_result.total_chains,
                    "viable_chains": chaining_result.viable_chains,
                    "optimal_chain": chaining_result.optimal_chain,
                    "strategy_used": chaining_result.strategy_used.value,
                    "average_chain_length": chaining_result.average_chain_length
                },
                "exploit_chains": [
                    {
                        "chain_id": chain.chain_id,
                        "vulnerabilities": chain.vulnerabilities,
                        "exploit_sequence": chain.exploit_sequence,
                        "success_probability": chain.success_probability,
                        "impact_score": chain.impact_score,
                        "complexity_score": chain.complexity_score,
                        "mitre_techniques": chain.mitre_techniques if include_mitre_mapping else [],
                        "execution_steps": chain.execution_steps,
                        "prerequisites": chain.prerequisites
                    }
                    for chain in chaining_result.exploit_chains
                ],
                "chain_visualization": chaining_result.chain_visualization,
                "effectiveness_analysis": chaining_result.effectiveness_analysis,
                "defense_recommendations": chaining_result.defense_recommendations,
                "educational_breakdown": chaining_result.educational_breakdown
            }
            
            if progress_callback:
                progress_callback(0.9, "Finalizing chain analysis...")
            
            # Extract vulnerabilities for standard format
            vulnerabilities = []
            if chaining_result.optimal_chain:
                for vuln in chaining_result.optimal_chain.vulnerabilities:
                    vulnerabilities.append({
                        "type": vuln["type"],
                        "severity": vuln.get("severity", "medium"),
                        "description": f"Part of exploit chain: {vuln.get('description', 'No description')}"
                    })
            
            if progress_callback:
                progress_callback(1.0, "Exploit chaining complete")
            
            end_time = datetime.now()
            
            return ScanResult(
                tool_name=self.metadata.name,
                target=options.target,
                status=ToolStatus.COMPLETED,
                start_time=start_time.isoformat(),
                end_time=end_time.isoformat(),
                duration_seconds=(end_time - start_time).total_seconds(),
                parsed_results=parsed_results,
                vulnerabilities=vulnerabilities,
                metadata={
                    "chaining_id": chaining_result.chaining_id,
                    "analysis_timestamp": chaining_result.timestamp,
                    "ai_provider": chaining_result.ai_provider
                }
            )
            
        except Exception as e:
            logger.error(f"AI exploit chaining failed: {e}")
            end_time = datetime.now()
            
            return ScanResult(
                tool_name=self.metadata.name,
                target=options.target,
                status=ToolStatus.FAILED,
                start_time=start_time.isoformat(),
                end_time=end_time.isoformat(),
                duration_seconds=(end_time - start_time).total_seconds(),
                errors=[str(e)]
            )