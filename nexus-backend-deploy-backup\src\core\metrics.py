#!/usr/bin/env python3
"""
Metrics Collection System for NexusScan Platform
Prometheus-compatible metrics for monitoring and alerting
"""

import time
import threading
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, field
from collections import defaultdict, deque
from datetime import datetime, timedelta

@dataclass
class MetricPoint:
    timestamp: float
    value: float
    labels: Dict[str, str] = field(default_factory=dict)

class MetricsCollector:
    """Thread-safe metrics collection with Prometheus export"""
    
    def __init__(self):
        self._counters: Dict[str, float] = defaultdict(float)
        self._gauges: Dict[str, float] = defaultdict(float)
        self._histograms: Dict[str, List[float]] = defaultdict(list)
        self._timeseries: Dict[str, deque] = defaultdict(lambda: deque(maxlen=1000))
        self._lock = threading.Lock()
        
    def increment_counter(self, name: str, value: float = 1.0, labels: Dict[str, str] = None):
        """Increment a counter metric"""
        with self._lock:
            key = self._build_key(name, labels)
            self._counters[key] += value
            self._record_timeseries(key, self._counters[key])
    
    def set_gauge(self, name: str, value: float, labels: Dict[str, str] = None):
        """Set a gauge metric value"""
        with self._lock:
            key = self._build_key(name, labels)
            self._gauges[key] = value
            self._record_timeseries(key, value)
    
    def record_histogram(self, name: str, value: float, labels: Dict[str, str] = None):
        """Record a histogram value"""
        with self._lock:
            key = self._build_key(name, labels)
            self._histograms[key].append(value)
            # Keep only last 1000 values for memory efficiency
            if len(self._histograms[key]) > 1000:
                self._histograms[key] = self._histograms[key][-1000:]
    
    def _build_key(self, name: str, labels: Dict[str, str] = None) -> str:
        """Build metric key with labels"""
        if not labels:
            return name
        
        label_str = ",".join(f'{k}="{v}"' for k, v in sorted(labels.items()))
        return f"{name}{{{label_str}}}"
    
    def _record_timeseries(self, key: str, value: float):
        """Record value in timeseries for trending"""
        self._timeseries[key].append(MetricPoint(time.time(), value))
    
    def get_prometheus_format(self) -> str:
        """Export metrics in Prometheus format"""
        lines = []
        
        # Export counters
        for key, value in self._counters.items():
            lines.append(f"# TYPE {key.split('{')[0]} counter")
            lines.append(f"{key} {value}")
        
        # Export gauges  
        for key, value in self._gauges.items():
            lines.append(f"# TYPE {key.split('{')[0]} gauge")
            lines.append(f"{key} {value}")
        
        # Export histogram summaries
        for key, values in self._histograms.items():
            if values:
                base_name = key.split('{')[0]
                lines.append(f"# TYPE {base_name} histogram")
                lines.append(f"{key}_count {len(values)}")
                lines.append(f"{key}_sum {sum(values)}")
                if values:
                    sorted_values = sorted(values)
                    lines.append(f"{key}_bucket{{le=\"0.5\"}} {len([v for v in sorted_values if v <= 0.5])}")
                    lines.append(f"{key}_bucket{{le=\"0.95\"}} {len([v for v in sorted_values if v <= 0.95])}")
                    lines.append(f"{key}_bucket{{le=\"+Inf\"}} {len(sorted_values)}")
        
        return "\n".join(lines)
    
    def get_stats(self) -> Dict[str, Any]:
        """Get collection statistics"""
        with self._lock:
            return {
                "counters_count": len(self._counters),
                "gauges_count": len(self._gauges),
                "histograms_count": len(self._histograms),
                "timeseries_points": sum(len(ts) for ts in self._timeseries.values()),
                "memory_usage_bytes": self._estimate_memory_usage()
            }
    
    def _estimate_memory_usage(self) -> int:
        """Estimate memory usage of metrics"""
        # Rough estimation
        return (len(self._counters) * 64 + 
                len(self._gauges) * 64 + 
                sum(len(hist) * 8 for hist in self._histograms.values()) +
                sum(len(ts) * 24 for ts in self._timeseries.values()))

# Global metrics instance
metrics = MetricsCollector()

# Convenience functions for common NexusScan metrics
def record_scan_duration(duration_seconds: float, scan_type: str, success: bool = True):
    """Record scan execution duration"""
    metrics.record_histogram("nexusscan_scan_duration_seconds", duration_seconds, {
        "type": scan_type,
        "success": str(success).lower()
    })

def increment_vulnerability_count(severity: str, vuln_type: str = "unknown"):
    """Increment vulnerability counter"""
    metrics.increment_counter("nexusscan_vulnerabilities_total", labels={
        "severity": severity,
        "type": vuln_type
    })

def record_ai_request_duration(duration_seconds: float, provider: str, success: bool = True):
    """Record AI service request duration"""
    metrics.record_histogram("nexusscan_ai_request_duration_seconds", duration_seconds, {
        "provider": provider,
        "success": str(success).lower()
    })

def set_active_scans(count: int):
    """Set number of currently active scans"""
    metrics.set_gauge("nexusscan_active_scans", count)

def increment_tool_usage(tool_name: str, platform: str = "unknown"):
    """Increment tool usage counter"""
    metrics.increment_counter("nexusscan_tool_usage_total", labels={
        "tool": tool_name,
        "platform": platform
    })

def record_database_query_duration(duration_seconds: float, operation: str):
    """Record database query duration"""
    metrics.record_histogram("nexusscan_database_query_duration_seconds", duration_seconds, {
        "operation": operation
    })

def set_system_resource_usage(cpu_percent: float, memory_percent: float, disk_percent: float):
    """Set system resource usage"""
    metrics.set_gauge("nexusscan_cpu_usage_percent", cpu_percent)
    metrics.set_gauge("nexusscan_memory_usage_percent", memory_percent)
    metrics.set_gauge("nexusscan_disk_usage_percent", disk_percent)