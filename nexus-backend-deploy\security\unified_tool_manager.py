#!/usr/bin/env python3
"""
Unified Tool Manager for NexusScan Desktop
Seamlessly combines Windows native tools with WSL Linux tools
"""

import asyncio
import logging
from typing import Dict, List, Optional, Any, Union
from pathlib import Path
import json

from .embedded_tool_manager import EmbeddedTool<PERSON>anager
from .tools.wsl_tool_runner import WSL<PERSON>oolRunner, ExecutionMode
from core.config import Config

logger = logging.getLogger(__name__)


class UnifiedToolManager:
    """Unified manager for all security tools (Windows + WSL)"""
    
    def __init__(self, config: Optional[Config] = None):
        """Initialize unified tool manager"""
        self.config = config or Config()
        
        # Initialize managers with execution mode from global config
        execution_mode = ExecutionMode.REAL if self.config.is_real_mode() else ExecutionMode.SIMULATION
        require_confirmation = self.config.security.require_real_mode_confirmation
        
        self.windows_manager = EmbeddedToolManager()
        self.wsl_manager = WSLToolRunner(
            execution_mode=execution_mode,
            require_confirmation=require_confirmation
        )
        
        # Set real mode confirmation status from global config
        if self.config.security.real_mode_confirmed:
            self.wsl_manager.real_mode_confirmed = True
        
        self.tool_preferences = self._setup_tool_preferences()
        self.tool_catalog = {}
        self._build_tool_catalog()
        
        logger.info(f"UnifiedToolManager initialized in {execution_mode.value.upper()} mode")
    
    def _setup_tool_preferences(self) -> Dict[str, Dict[str, Any]]:
        """Setup tool preferences and fallback strategies"""
        return {
            # Network Scanning
            "nmap": {
                "primary": "windows",
                "fallback": None,
                "description": "Network discovery and port scanning"
            },
            
            # Vulnerability Scanning
            "nuclei": {
                "primary": "windows", 
                "fallback": None,
                "description": "Fast vulnerability scanner with templates"
            },
            
            # Web Application Testing
            "sqlmap": {
                "primary": "windows",
                "fallback": None,
                "description": "SQL injection testing tool"
            },
            "gobuster": {
                "primary": "windows",
                "fallback": "dirb",
                "description": "Directory/file bruteforcer"
            },
            "nikto": {
                "primary": "wsl",
                "fallback": "nuclei",
                "description": "Web server vulnerability scanner"
            },
            "dirb": {
                "primary": "wsl",
                "fallback": "gobuster", 
                "description": "Web content scanner"
            },
            "wpscan": {
                "primary": "wsl",
                "fallback": "nuclei",
                "description": "WordPress vulnerability scanner"
            },
            "ffuf": {
                "primary": "wsl",
                "fallback": "gobuster",
                "description": "Fast web fuzzer"
            },
            "feroxbuster": {
                "primary": "wsl",
                "fallback": "gobuster",
                "description": "Fast content discovery tool"
            },
            "whatweb": {
                "primary": "wsl",
                "fallback": None,
                "description": "Web technology identifier"
            },
            
            # Password & Cryptography
            "hashcat": {
                "primary": "windows",
                "fallback": None,
                "description": "Advanced password recovery"
            },
            "john": {
                "primary": "windows",
                "fallback": None,
                "description": "Password cracker"
            },
            
            # Network Enumeration
            "enum4linux-ng": {
                "primary": "wsl",
                "fallback": None,
                "description": "Modern SMB enumeration tool (next generation)"
            },
            "smbclient": {
                "primary": "wsl", 
                "fallback": None,
                "description": "SMB/CIFS client"
            },
            
            # SSL/TLS Testing
            "testssl": {
                "primary": "wsl",
                "fallback": None,
                "description": "SSL/TLS security scanner"
            },
            "sslyze": {
                "primary": "wsl",
                "fallback": None,
                "description": "SSL/TLS scanning library"
            },
            
            # Exploitation
            "metasploit": {
                "primary": "wsl",
                "fallback": None,
                "description": "Penetration testing framework"
            },
            "searchsploit": {
                "primary": "wsl",
                "fallback": None,
                "description": "Exploit database search"
            }
        }
    
    def _build_tool_catalog(self):
        """Build comprehensive tool catalog"""
        # Get Windows tools
        windows_tools = self.windows_manager.get_available_tools()
        wsl_tools = self.wsl_manager.get_available_tools()
        
        for tool_name, preferences in self.tool_preferences.items():
            self.tool_catalog[tool_name] = {
                "description": preferences["description"],
                "primary_platform": preferences["primary"],
                "fallback_tool": preferences.get("fallback"),
                "available_on": [],
                "preferred_manager": None,
                "fallback_manager": None
            }
            
            # Check availability
            if tool_name in windows_tools:
                self.tool_catalog[tool_name]["available_on"].append("windows")
                if preferences["primary"] == "windows":
                    self.tool_catalog[tool_name]["preferred_manager"] = "windows"
            
            if tool_name in wsl_tools:
                self.tool_catalog[tool_name]["available_on"].append("wsl")
                if preferences["primary"] == "wsl":
                    self.tool_catalog[tool_name]["preferred_manager"] = "wsl"
            
            # Setup fallback manager
            if not self.tool_catalog[tool_name]["preferred_manager"]:
                if "windows" in self.tool_catalog[tool_name]["available_on"]:
                    self.tool_catalog[tool_name]["preferred_manager"] = "windows"
                elif "wsl" in self.tool_catalog[tool_name]["available_on"]:
                    self.tool_catalog[tool_name]["preferred_manager"] = "wsl"
    
    async def setup_environment(self) -> Dict[str, Any]:
        """Setup complete tool environment"""
        setup_result = {
            "success": False,
            "windows_tools": 0,
            "wsl_tools": 0,
            "total_tools": 0,
            "wsl_setup": False,
            "errors": []
        }
        
        try:
            # Setup WSL environment if available
            if self.wsl_manager.wsl_available:
                logger.info("🐧 Setting up WSL environment...")
                wsl_setup = await self.wsl_manager.setup_wsl_environment()
                setup_result["wsl_setup"] = wsl_setup["success"]
                
                if not wsl_setup["success"]:
                    setup_result["errors"].append(f"WSL setup failed: {wsl_setup.get('error', 'Unknown error')}")
            
            # Rebuild tool catalog after setup
            self._build_tool_catalog()
            
            # Count available tools
            windows_status = self.windows_manager.get_tool_status_report()
            wsl_status = await self.wsl_manager.check_tool_status() if self.wsl_manager.wsl_available else {}
            
            setup_result["windows_tools"] = windows_status["total_tools"]
            setup_result["wsl_tools"] = len([t for t in wsl_status.values() if t])
            setup_result["total_tools"] = setup_result["windows_tools"] + setup_result["wsl_tools"]
            
            setup_result["success"] = setup_result["total_tools"] > 0
            
            logger.info(f"✅ Tool setup complete: {setup_result['total_tools']} tools available")
            
        except Exception as e:
            logger.error(f"❌ Environment setup failed: {e}")
            setup_result["errors"].append(str(e))
        
        return setup_result
    
    def get_available_tools(self) -> List[str]:
        """Get list of all available tools"""
        available = []
        for tool_name, info in self.tool_catalog.items():
            if info["preferred_manager"]:
                available.append(tool_name)
        return sorted(available)
    
    def get_tool_info(self, tool_name: str) -> Optional[Dict[str, Any]]:
        """Get detailed information about a tool"""
        if tool_name not in self.tool_catalog:
            return None
        
        info = self.tool_catalog[tool_name].copy()
        
        # Add runtime information
        if info["preferred_manager"] == "windows":
            windows_info = self.windows_manager.get_tool_info(tool_name)
            if windows_info:
                info.update({
                    "executable_path": windows_info["path"],
                    "embedded": windows_info["embedded"],
                    "working_directory": windows_info["working_dir"]
                })
        elif info["preferred_manager"] == "wsl":
            info.update({
                "executable_path": f"wsl:{tool_name}",
                "embedded": False,
                "working_directory": "/tmp"
            })
        
        return info
    
    async def run_tool(self, tool_name: str, arguments: List[str], 
                      options: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Run a tool using the best available method"""
        if tool_name not in self.tool_catalog:
            return {
                "success": False,
                "error": f"Tool {tool_name} not recognized",
                "output": "",
                "stderr": ""
            }
        
        tool_info = self.tool_catalog[tool_name]
        options = options or {}
        
        # Try preferred platform first
        preferred_manager = tool_info["preferred_manager"]
        
        # Use Hybrid Execution Engine if available
        try:
            from .tools.hybrid_execution_engine import get_hybrid_engine, ExecutionResult
            
            # Initialize hybrid engine
            hybrid_engine = await get_hybrid_engine()
            
            # Convert arguments to target and options
            target = arguments.get('target', '')
            options = {k: v for k, v in arguments.items() if k != 'target'}
            
            # Execute with hybrid engine
            hybrid_result = await hybrid_engine.execute_tool(tool_name, target, options)
            
            # Convert hybrid result to legacy format
            if hybrid_result.status == ExecutionResult.SUCCESS:
                result = {
                    "success": True,
                    "output": hybrid_result.output,
                    "stderr": hybrid_result.error,
                    "execution_time": hybrid_result.execution_time,
                    "method": hybrid_result.method,
                    "metadata": hybrid_result.metadata
                }
            else:
                result = {
                    "success": False,
                    "error": hybrid_result.error,
                    "output": hybrid_result.output,
                    "stderr": hybrid_result.error,
                    "method": hybrid_result.method
                }
                
        except Exception as e:
            logger.warning(f"Hybrid execution failed, falling back to legacy: {e}")
            
            # Fallback to legacy execution
            if preferred_manager == "windows":
                result = await self.windows_manager.run_tool(tool_name, arguments)
            elif preferred_manager == "wsl":
                result = await self.wsl_manager.run_tool(tool_name, arguments)
            else:
                return {
                    "success": False,
                    "error": f"Tool {tool_name} not available on any platform",
                    "output": "",
                    "stderr": ""
                }
        
        # Try fallback if primary failed
        if not result["success"] and tool_info["fallback_tool"]:
            fallback_tool = tool_info["fallback_tool"]
            logger.warning(f"Primary tool {tool_name} failed, trying fallback {fallback_tool}")
            
            if fallback_tool in self.tool_catalog:
                fallback_result = await self.run_tool(fallback_tool, arguments, options)
                if fallback_result["success"]:
                    fallback_result["fallback_used"] = fallback_tool
                    fallback_result["original_tool"] = tool_name
                    return fallback_result
        
        # Enhance result with tool information
        result["tool_info"] = {
            "name": tool_name,
            "platform": preferred_manager,
            "description": tool_info["description"]
        }
        
        return result
    
    async def execute_scan(self, tool_id: str, target: str, scan_type: str, timeout: int = 3600) -> Dict[str, Any]:
        """Execute a single tool scan with specified parameters
        
        Args:
            tool_id: Tool identifier (e.g., 'nmap', 'nuclei', 'sqlmap')
            target: Target to scan
            scan_type: Type of scan to perform
            timeout: Timeout in seconds
            
        Returns:
            Scan result dictionary
        """
        try:
            logger.info(f"Executing {tool_id} scan on {target} with type {scan_type}")
            
            # Map scan types to tool-specific options
            scan_options = self._get_tool_scan_options(tool_id, scan_type)
            
            # Execute the tool
            result = await self.run_tool(tool_id, target, scan_options)
            
            # Enhance result with scan metadata
            result.update({
                "tool_id": tool_id,
                "target": target,
                "scan_type": scan_type,
                "executed_at": asyncio.get_event_loop().time(),
                "timeout": timeout
            })
            
            return result
            
        except Exception as e:
            logger.error(f"Failed to execute {tool_id} scan: {e}")
            return {
                "success": False,
                "error": str(e),
                "tool_id": tool_id,
                "target": target,
                "scan_type": scan_type
            }
    
    def _get_tool_scan_options(self, tool_id: str, scan_type: str) -> Dict[str, Any]:
        """Get tool-specific scan options based on scan type
        
        Args:
            tool_id: Tool identifier
            scan_type: Scan type
            
        Returns:
            Tool-specific options dictionary
        """
        tool_scan_mappings = {
            "nmap": {
                "quick": {"ports": "1-1000", "flags": "-T4"},
                "comprehensive": {"ports": "1-65535", "flags": "-sV -sC -A"},
                "stealth": {"ports": "1-1000", "flags": "-sS -T2"},
                "aggressive": {"ports": "1-65535", "flags": "-A -T4"}
            },
            "nuclei": {
                "critical": {"severity": "critical"},
                "high": {"severity": "high,critical"},
                "medium": {"severity": "medium,high,critical"},
                "low": {"severity": "low,medium,high,critical"},
                "info": {"severity": "info,low,medium,high,critical"}
            },
            "sqlmap": {
                "detection": {"risk": "1", "level": "1"},
                "enumeration": {"risk": "2", "level": "3"},
                "exploitation": {"risk": "3", "level": "5"}
            },
            "masscan": {
                "fast": {"rate": "1000", "ports": "1-1000"},
                "full": {"rate": "500", "ports": "1-65535"},
                "custom": {"rate": "100", "ports": "22,80,443,8080"}
            },
            "gobuster": {
                "dir": {"wordlist": "common.txt", "threads": "10"},
                "vhost": {"wordlist": "subdomains.txt", "threads": "10"},
                "dns": {"wordlist": "dns.txt", "threads": "10"}
            }
        }
        
        return tool_scan_mappings.get(tool_id, {}).get(scan_type, {})

    async def run_scan_campaign(self, targets: List[str], 
                               scan_config: Dict[str, Any]) -> Dict[str, Any]:
        """Run comprehensive scan campaign using all available tools"""
        campaign_result = {
            "targets": targets,
            "config": scan_config,
            "results": {},
            "summary": {
                "total_scans": 0,
                "successful_scans": 0,
                "failed_scans": 0,
                "tools_used": [],
                "vulnerabilities_found": 0
            }
        }
        
        for target in targets:
            target_results = {}
            
            # Network Discovery
            if scan_config.get("network_discovery", True):
                nmap_result = await self._run_nmap_scan(target, scan_config.get("nmap_options", {}))
                target_results["nmap"] = nmap_result
                campaign_result["summary"]["total_scans"] += 1
                if nmap_result["success"]:
                    campaign_result["summary"]["successful_scans"] += 1
                    if "nmap" not in campaign_result["summary"]["tools_used"]:
                        campaign_result["summary"]["tools_used"].append("nmap")
            
            # Vulnerability Scanning
            if scan_config.get("vulnerability_scan", True):
                nuclei_result = await self._run_nuclei_scan(target, scan_config.get("nuclei_options", {}))
                target_results["nuclei"] = nuclei_result
                campaign_result["summary"]["total_scans"] += 1
                if nuclei_result["success"]:
                    campaign_result["summary"]["successful_scans"] += 1
                    if "nuclei" not in campaign_result["summary"]["tools_used"]:
                        campaign_result["summary"]["tools_used"].append("nuclei")
            
            # Web Application Testing
            if scan_config.get("web_testing", True):
                # Directory scanning
                if scan_config.get("directory_scan", True):
                    dir_result = await self._run_directory_scan(target, scan_config.get("directory_options", {}))
                    target_results["directory_scan"] = dir_result
                    campaign_result["summary"]["total_scans"] += 1
                    if dir_result["success"]:
                        campaign_result["summary"]["successful_scans"] += 1
                
                # Web vulnerability scanning
                if scan_config.get("web_vuln_scan", True):
                    web_vuln_result = await self._run_web_vulnerability_scan(target, scan_config.get("web_vuln_options", {}))
                    target_results["web_vulnerability"] = web_vuln_result
                    campaign_result["summary"]["total_scans"] += 1
                    if web_vuln_result["success"]:
                        campaign_result["summary"]["successful_scans"] += 1
            
            # SQL Injection Testing
            if scan_config.get("sql_injection", False):
                sqlmap_result = await self._run_sqlmap_scan(target, scan_config.get("sqlmap_options", {}))
                target_results["sqlmap"] = sqlmap_result
                campaign_result["summary"]["total_scans"] += 1
                if sqlmap_result["success"]:
                    campaign_result["summary"]["successful_scans"] += 1
                    if "sqlmap" not in campaign_result["summary"]["tools_used"]:
                        campaign_result["summary"]["tools_used"].append("sqlmap")
            
            campaign_result["results"][target] = target_results
        
        # Calculate failed scans
        campaign_result["summary"]["failed_scans"] = (
            campaign_result["summary"]["total_scans"] - 
            campaign_result["summary"]["successful_scans"]
        )
        
        return campaign_result
    
    async def _run_nmap_scan(self, target: str, options: Dict[str, Any]) -> Dict[str, Any]:
        """Run Nmap scan with options"""
        scan_type = options.get("scan_type", "basic")
        
        if scan_type == "basic":
            args = ["-sV", "-sC", target]
        elif scan_type == "fast":
            args = ["-F", target]
        elif scan_type == "comprehensive":
            args = ["-sS", "-sV", "-sC", "-A", "-O", target]
        elif scan_type == "stealth":
            args = ["-sS", "-T2", target]
        else:
            args = [target]
        
        return await self.run_tool("nmap", args)
    
    async def _run_nuclei_scan(self, target: str, options: Dict[str, Any]) -> Dict[str, Any]:
        """Run Nuclei vulnerability scan"""
        args = ["-u", target]
        
        if options.get("tags"):
            args.extend(["-tags", ",".join(options["tags"])])
        
        if options.get("severity"):
            args.extend(["-severity", options["severity"]])
        
        args.extend(["-json"])
        
        return await self.run_tool("nuclei", args)
    
    async def _run_directory_scan(self, target: str, options: Dict[str, Any]) -> Dict[str, Any]:
        """Run directory/file enumeration"""
        # Try gobuster first (Windows), then dirb (WSL)
        tool = "gobuster" if "gobuster" in self.get_available_tools() else "dirb"
        
        if tool == "gobuster":
            args = ["dir", "-u", target]
            if options.get("wordlist"):
                args.extend(["-w", options["wordlist"]])
        else:  # dirb
            args = [target]
            if options.get("wordlist"):
                args.append(options["wordlist"])
        
        return await self.run_tool(tool, args)
    
    async def _run_web_vulnerability_scan(self, target: str, options: Dict[str, Any]) -> Dict[str, Any]:
        """Run web vulnerability scan (Nikto or Nuclei web templates)"""
        if "nikto" in self.get_available_tools():
            args = ["-h", target]
            if options.get("format"):
                args.extend(["-Format", options["format"]])
            return await self.run_tool("nikto", args)
        else:
            # Use Nuclei with web templates
            args = ["-u", target, "-tags", "web", "-json"]
            return await self.run_tool("nuclei", args)
    
    async def _run_sqlmap_scan(self, target: str, options: Dict[str, Any]) -> Dict[str, Any]:
        """Run SQLMap scan"""
        args = ["-u", target]
        
        if options.get("risk"):
            args.extend(["--risk", str(options["risk"])])
        
        if options.get("level"):
            args.extend(["--level", str(options["level"])])
        
        args.append("--batch")  # Non-interactive mode
        
        return await self.run_tool("sqlmap", args)
    
    def get_comprehensive_status(self) -> Dict[str, Any]:
        """Get comprehensive status of all tools and platforms"""
        windows_status = self.windows_manager.get_tool_status_report()
        
        status = {
            "unified_manager": True,
            "platforms": {
                "windows": {
                    "available": True,
                    "tools_count": windows_status["total_tools"],
                    "embedded_tools": windows_status["embedded_tools"],
                    "system_tools": windows_status["system_tools"]
                },
                "wsl": {
                    "available": self.wsl_manager.wsl_available,
                    "tools_count": len(self.wsl_manager.linux_tools),
                    "setup_required": not self.wsl_manager.wsl_available
                }
            },
            "tool_catalog": self.tool_catalog,
            "total_available_tools": len(self.get_available_tools()),
            "recommended_setup": []
        }
        
        # Add setup recommendations
        if not self.wsl_manager.wsl_available:
            status["recommended_setup"].append({
                "action": "Enable WSL",
                "description": "Enable Windows Subsystem for Linux for additional security tools",
                "priority": "medium"
            })
        
        if windows_status["missing_core_tools"]:
            status["recommended_setup"].append({
                "action": "Install Windows tools",
                "description": f"Install missing tools: {', '.join(windows_status['missing_core_tools'])}",
                "priority": "high"
            })
        
        return status
    
    def set_execution_mode(self, mode: str) -> None:
        """Set execution mode for all managed tools"""
        # Update global config
        self.config.set_execution_mode(mode)
        
        # Update WSL tool runner
        execution_mode = ExecutionMode.REAL if mode.lower() == "real" else ExecutionMode.SIMULATION
        self.wsl_manager.set_execution_mode(execution_mode)
        
        logger.info(f"UnifiedToolManager execution mode set to: {mode.upper()}")
    
    def get_execution_mode(self) -> str:
        """Get current execution mode"""
        return "real" if self.config.is_real_mode() else "simulation"
    
    def confirm_real_mode(self) -> bool:
        """Confirm real mode execution across all tools"""
        if self.config.confirm_real_mode():
            self.wsl_manager.real_mode_confirmed = True
            logger.warning("REAL MODE CONFIRMED: All tools now operate without restrictions")
            return True
        return False
    
    def enable_real_mode_bypass(self, bypass: bool = True) -> None:
        """Enable/disable real mode confirmation requirement"""
        self.config.enable_real_mode_bypass(bypass)
        self.wsl_manager.require_confirmation = not bypass
        if bypass:
            self.wsl_manager.real_mode_confirmed = True
        logger.warning(f"Real mode bypass: {'ENABLED' if bypass else 'DISABLED'}")
    
    async def execute_arbitrary_command(self, command: str, platform: str = "wsl", timeout: Optional[int] = None) -> Dict[str, Any]:
        """Execute arbitrary command (real mode allows unrestricted execution)"""
        if platform.lower() == "wsl":
            return await self.wsl_manager.execute_command(command, timeout)
        else:
            # For Windows commands, we'd need to implement this in embedded_tool_manager
            logger.warning("Arbitrary Windows command execution not yet implemented")
            return {
                "success": False,
                "error": "Windows arbitrary command execution not implemented",
                "platform": platform
            }