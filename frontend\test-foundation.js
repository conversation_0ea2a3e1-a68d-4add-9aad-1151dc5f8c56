/**
 * Foundation Validation Test Script
 * Tests core functionality of the rebuilt NexusScan Desktop application
 */

const axios = require('axios');

// Test configuration
const BACKEND_URL = 'http://ec2-3-89-91-209.compute-1.amazonaws.com:8000';
const TIMEOUT = 10000;

// Test results tracking
const results = {
  passed: 0,
  failed: 0,
  tests: []
};

function logTest(name, passed, details = '') {
  const status = passed ? '✅ PASS' : '❌ FAIL';
  console.log(`${status} ${name}`);
  if (details) console.log(`   ${details}`);
  
  results.tests.push({ name, passed, details });
  if (passed) results.passed++;
  else results.failed++;
}

async function testBackendHealth() {
  try {
    const response = await axios.get(`${BACKEND_URL}/api/health`, { timeout: TIMEOUT });
    const data = response.data;
    
    if (data.success && data.data.status === 'healthy') {
      logTest('Backend Health Check', true, `Status: ${data.data.status}, Services available`);
      return true;
    } else {
      logTest('Backend Health Check', false, `Unexpected response: ${JSON.stringify(data)}`);
      return false;
    }
  } catch (error) {
    logTest('Backend Health Check', false, `Connection failed: ${error.message}`);
    return false;
  }
}

async function testToolsEnumeration() {
  try {
    const response = await axios.get(`${BACKEND_URL}/api/tools/available`, { timeout: TIMEOUT });
    const data = response.data;
    
    if (data.success && Array.isArray(data.data)) {
      const toolCount = data.data.length;
      const availableTools = data.data.filter(tool => tool.status === 'available').length;
      logTest('Tools Enumeration', true, `${availableTools}/${toolCount} tools available`);
      return true;
    } else {
      logTest('Tools Enumeration', false, `Invalid response format`);
      return false;
    }
  } catch (error) {
    logTest('Tools Enumeration', false, `Request failed: ${error.message}`);
    return false;
  }
}

async function testFerrariAI() {
  try {
    const response = await axios.get(`${BACKEND_URL}/api/ai/capabilities/status`, { timeout: TIMEOUT });
    const data = response.data;
    
    if (data.success && data.data.overall_status === 'online') {
      const providers = data.data.core_ai_services.active_providers.length;
      logTest('Ferrari AI Status', true, `${providers} AI providers online`);
      return true;
    } else {
      logTest('Ferrari AI Status', false, `AI services not fully operational`);
      return false;
    }
  } catch (error) {
    logTest('Ferrari AI Status', false, `Request failed: ${error.message}`);
    return false;
  }
}

async function testOrchestratorCapabilities() {
  try {
    const response = await axios.get(`${BACKEND_URL}/api/orchestrator/capabilities`, { timeout: 20000 });
    const data = response.data;
    
    if (data.success && data.data.available) {
      logTest('Ferrari Orchestrator', true, 'Orchestrator capabilities accessible');
      return true;
    } else {
      logTest('Ferrari Orchestrator', false, 'Orchestrator not responding correctly');
      return false;
    }
  } catch (error) {
    logTest('Ferrari Orchestrator', false, `Request failed: ${error.message}`);
    return false;
  }
}

async function testCreativeExploits() {
  try {
    const response = await axios.get(`${BACKEND_URL}/api/ai/creative-exploits/capabilities`, { timeout: 20000 });
    const data = response.data;
    
    if (data.success && data.data.available) {
      logTest('Creative Exploits Engine', true, 'Engine operational');
      return true;
    } else {
      logTest('Creative Exploits Engine', false, 'Engine not operational');
      return false;
    }
  } catch (error) {
    logTest('Creative Exploits Engine', false, `Request failed: ${error.message}`);
    return false;
  }
}

async function runFoundationTests() {
  console.log('🧪 NEXUSSCAN DESKTOP - FOUNDATION VALIDATION TESTING');
  console.log('===============================================\n');
  
  console.log('📡 Testing AWS Backend Connectivity...');
  await testBackendHealth();
  await testToolsEnumeration();
  
  console.log('\n🤖 Testing Ferrari AI Platform...');
  await testFerrariAI();
  await testOrchestratorCapabilities();
  await testCreativeExploits();
  
  console.log('\n📊 FOUNDATION VALIDATION RESULTS:');
  console.log('===============================================');
  console.log(`✅ Passed: ${results.passed}`);
  console.log(`❌ Failed: ${results.failed}`);
  console.log(`📈 Success Rate: ${((results.passed / (results.passed + results.failed)) * 100).toFixed(1)}%`);
  
  if (results.failed === 0) {
    console.log('\n🎉 FOUNDATION VALIDATION: SUCCESS!');
    console.log('✅ Backend connectivity confirmed');
    console.log('✅ All core services operational');
    console.log('✅ Ready for comprehensive testing');
    return true;
  } else {
    console.log('\n⚠️ FOUNDATION VALIDATION: ISSUES FOUND');
    console.log('❌ Some core services not functioning');
    console.log('🔧 Fix issues before proceeding to comprehensive testing');
    return false;
  }
}

// Execute foundation tests
runFoundationTests()
  .then(success => {
    process.exit(success ? 0 : 1);
  })
  .catch(error => {
    console.error('💥 Foundation testing failed:', error.message);
    process.exit(1);
  });