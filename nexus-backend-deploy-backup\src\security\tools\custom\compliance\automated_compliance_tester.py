#!/usr/bin/env python3
"""
Automated Compliance Testing Framework for NexusScan Desktop
Comprehensive compliance testing for SOC2, PCI-DSS, HIPAA and other security frameworks.
"""

import asyncio
import logging
import json
import re
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Set, Tuple, Union
from dataclasses import dataclass, asdict, field
from enum import Enum
from collections import defaultdict
import yaml
import xml.etree.ElementTree as ET

from core.config import Config
from core.database import DatabaseManager
from ai.ai_service import AIServiceManager
from ai.services import AnalysisRequest

logger = logging.getLogger(__name__)


class ComplianceFramework(Enum):
    """Supported compliance frameworks"""
    SOC2 = "soc2"
    PCI_DSS = "pci_dss"
    HIPAA = "hipaa"
    GDPR = "gdpr"
    ISO27001 = "iso27001"
    NIST_CSF = "nist_csf"
    CIS_CONTROLS = "cis_controls"
    OWASP_TOP10 = "owasp_top10"
    FISMA = "fisma"
    FedRAMP = "fedramp"


class ComplianceCategory(Enum):
    """Categories of compliance controls"""
    ACCESS_CONTROL = "access_control"
    AUTHENTICATION = "authentication"
    AUTHORIZATION = "authorization"
    DATA_PROTECTION = "data_protection"
    ENCRYPTION = "encryption"
    LOGGING_MONITORING = "logging_monitoring"
    INCIDENT_RESPONSE = "incident_response"
    VULNERABILITY_MANAGEMENT = "vulnerability_management"
    NETWORK_SECURITY = "network_security"
    PHYSICAL_SECURITY = "physical_security"
    BUSINESS_CONTINUITY = "business_continuity"
    RISK_MANAGEMENT = "risk_management"


class TestSeverity(Enum):
    """Severity levels for compliance test results"""
    CRITICAL = "critical"
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"
    INFO = "info"


class TestStatus(Enum):
    """Status of compliance test execution"""
    PENDING = "pending"
    RUNNING = "running"
    PASSED = "passed"
    FAILED = "failed"
    WARNING = "warning"
    NOT_APPLICABLE = "not_applicable"
    MANUAL_REVIEW = "manual_review"
    ERROR = "error"


@dataclass
class ComplianceControl:
    """Individual compliance control definition"""
    control_id: str
    framework: ComplianceFramework
    category: ComplianceCategory
    title: str
    description: str
    requirements: List[str]
    test_procedures: List[str]
    evidence_requirements: List[str]
    risk_rating: str
    automation_level: str
    remediation_guidance: List[str]
    references: List[str]
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class ComplianceTest:
    """Individual compliance test specification"""
    test_id: str
    control_id: str
    name: str
    description: str
    test_type: str
    automated: bool
    test_steps: List[Dict[str, Any]]
    expected_results: List[str]
    failure_criteria: List[str]
    tools_required: List[str]
    estimated_duration: timedelta
    prerequisites: List[str]
    cleanup_required: bool
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class TestResult:
    """Result of a compliance test execution"""
    result_id: str
    test_id: str
    control_id: str
    framework: ComplianceFramework
    status: TestStatus
    severity: TestSeverity
    score: float
    findings: List[Dict[str, Any]]
    evidence: List[Dict[str, Any]]
    recommendations: List[str]
    execution_time: timedelta
    timestamp: datetime
    tester: str
    environment: str
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class ComplianceReport:
    """Comprehensive compliance assessment report"""
    report_id: str
    framework: ComplianceFramework
    scope: str
    test_results: List[TestResult]
    overall_score: float
    compliance_percentage: float
    critical_findings: int
    high_findings: int
    medium_findings: int
    low_findings: int
    recommendations: List[str]
    executive_summary: str
    detailed_findings: List[Dict[str, Any]]
    remediation_plan: List[Dict[str, Any]]
    next_assessment_date: datetime
    assessor: str
    timestamp: datetime
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class ComplianceTestingRequest:
    """Request for compliance testing"""
    target: str
    frameworks: List[ComplianceFramework]
    scope: str = "comprehensive"
    include_evidence_collection: bool = True
    automated_remediation: bool = True
    depth_level: str = "comprehensive"
    compliance_year: int = 2025
    custom_options: Dict[str, Any] = field(default_factory=dict)


class AutomatedComplianceTester:
    """
    Automated compliance testing framework for multiple security frameworks.
    Provides comprehensive testing, reporting, and remediation guidance.
    """
    
    def __init__(self, config: Config, db_manager: DatabaseManager, ai_service: AIServiceManager):
        self.config = config
        self.db_manager = db_manager
        self.ai_service = ai_service
        
        # Testing configuration
        self.test_timeout = 300  # 5 minutes default
        self.max_concurrent_tests = 5
        self.retry_attempts = 3
        
        # Compliance control registry
        self.controls_registry = {}
        self.test_registry = {}
        
        # Initialize compliance frameworks
        self._initialize_compliance_frameworks()
        
        logger.info("Automated Compliance Tester initialized")
    
    async def _initialize_compliance_frameworks(self):
        """Initialize compliance framework definitions"""
        try:
            # Load SOC2 controls
            self._load_soc2_controls()
            
            # Load PCI-DSS controls
            self._load_pci_dss_controls()
            
            # Load HIPAA controls
            self._load_hipaa_controls()
            
            # Load additional frameworks
            self._load_additional_frameworks()
            
            logger.info("Compliance frameworks initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize compliance frameworks: {e}")
            raise
    
    def _load_soc2_controls(self):
        """Load SOC2 Type II controls"""
        soc2_controls = [
            ComplianceControl(
                control_id="CC1.1",
                framework=ComplianceFramework.SOC2,
                category=ComplianceCategory.ACCESS_CONTROL,
                title="Control Environment - Integrity and Ethical Values",
                description="The entity demonstrates a commitment to integrity and ethical values",
                requirements=[
                    "Management demonstrates integrity and ethical values",
                    "Board exercises oversight responsibility",
                    "Management establishes structure, authority, and responsibility"
                ],
                test_procedures=[
                    "Review organizational policies and procedures",
                    "Interview management and key personnel",
                    "Test implementation of ethical standards"
                ],
                evidence_requirements=[
                    "Code of conduct documentation",
                    "Training records",
                    "Disciplinary action records"
                ],
                risk_rating="Medium",
                automation_level="Semi-automated",
                remediation_guidance=[
                    "Establish formal code of conduct",
                    "Implement ethics training program",
                    "Create reporting mechanisms for violations"
                ],
                references=["SOC2 TSC CC1.1", "COSO Framework"]
            ),
            ComplianceControl(
                control_id="CC6.1",
                framework=ComplianceFramework.SOC2,
                category=ComplianceCategory.LOGGING_MONITORING,
                title="Logical and Physical Access Controls",
                description="The entity implements logical access security software, infrastructure, and architectures",
                requirements=[
                    "Logical access controls restrict access",
                    "Authentication mechanisms are implemented",
                    "Access rights are periodically reviewed"
                ],
                test_procedures=[
                    "Test access control configurations",
                    "Review user access reports",
                    "Test authentication mechanisms"
                ],
                evidence_requirements=[
                    "Access control matrix",
                    "User access reviews",
                    "Authentication logs"
                ],
                risk_rating="High",
                automation_level="Automated",
                remediation_guidance=[
                    "Implement role-based access control",
                    "Enable multi-factor authentication",
                    "Regular access reviews"
                ],
                references=["SOC2 TSC CC6.1"]
            )
        ]
        
        for control in soc2_controls:
            self.controls_registry[control.control_id] = control
    
    def _load_pci_dss_controls(self):
        """Load PCI-DSS controls"""
        pci_dss_controls = [
            ComplianceControl(
                control_id="PCI-1.1",
                framework=ComplianceFramework.PCI_DSS,
                category=ComplianceCategory.NETWORK_SECURITY,
                title="Establish and implement firewall and router configuration standards",
                description="Firewalls and routers are key components of the architecture that controls entry to and exit from the network",
                requirements=[
                    "Formal process for approving and testing network connections",
                    "Current network diagram with data flows",
                    "Requirements for firewall and router at each connection"
                ],
                test_procedures=[
                    "Review firewall configurations",
                    "Test network segmentation",
                    "Verify documentation currency"
                ],
                evidence_requirements=[
                    "Network diagrams",
                    "Firewall rule sets",
                    "Change management records"
                ],
                risk_rating="High",
                automation_level="Automated",
                remediation_guidance=[
                    "Document network architecture",
                    "Implement network segmentation",
                    "Regular firewall rule reviews"
                ],
                references=["PCI DSS v4.0 Requirement 1.1"]
            ),
            ComplianceControl(
                control_id="PCI-3.4",
                framework=ComplianceFramework.PCI_DSS,
                category=ComplianceCategory.DATA_PROTECTION,
                title="Render PAN unreadable anywhere it is stored",
                description="Primary Account Numbers must be rendered unreadable wherever stored",
                requirements=[
                    "PAN is masked when displayed",
                    "PAN is protected wherever stored",
                    "Cryptographic keys are protected"
                ],
                test_procedures=[
                    "Test data masking implementations",
                    "Review encryption implementations",
                    "Test key management procedures"
                ],
                evidence_requirements=[
                    "Encryption policies",
                    "Key management documentation",
                    "Data masking evidence"
                ],
                risk_rating="Critical",
                automation_level="Semi-automated",
                remediation_guidance=[
                    "Implement strong encryption",
                    "Establish key management",
                    "Data masking for displays"
                ],
                references=["PCI DSS v4.0 Requirement 3.4"]
            )
        ]
        
        for control in pci_dss_controls:
            self.controls_registry[control.control_id] = control
    
    def _load_hipaa_controls(self):
        """Load HIPAA controls"""
        hipaa_controls = [
            ComplianceControl(
                control_id="HIPAA-164.308a1i",
                framework=ComplianceFramework.HIPAA,
                category=ComplianceCategory.ACCESS_CONTROL,
                title="Assigned Security Responsibility",
                description="A covered entity must identify the security official responsible for developing and implementing security policies",
                requirements=[
                    "Designate security official",
                    "Security official has authority",
                    "Security responsibilities documented"
                ],
                test_procedures=[
                    "Review organizational chart",
                    "Interview security official",
                    "Review job descriptions"
                ],
                evidence_requirements=[
                    "Security official designation",
                    "Job descriptions",
                    "Organizational policies"
                ],
                risk_rating="Medium",
                automation_level="Manual",
                remediation_guidance=[
                    "Designate qualified security official",
                    "Document security responsibilities",
                    "Provide adequate authority"
                ],
                references=["45 CFR 164.308(a)(2)"]
            ),
            ComplianceControl(
                control_id="HIPAA-164.312a1",
                framework=ComplianceFramework.HIPAA,
                category=ComplianceCategory.ACCESS_CONTROL,
                title="Access Control",
                description="Implement technical policies and procedures for electronic information systems",
                requirements=[
                    "Unique user identification",
                    "Emergency access procedure",
                    "Automatic logoff",
                    "Encryption and decryption"
                ],
                test_procedures=[
                    "Test user access controls",
                    "Review emergency procedures",
                    "Test automatic logoff",
                    "Verify encryption implementation"
                ],
                evidence_requirements=[
                    "Access control matrix",
                    "Emergency access logs",
                    "Encryption documentation"
                ],
                risk_rating="High",
                automation_level="Automated",
                remediation_guidance=[
                    "Implement unique user IDs",
                    "Configure automatic logoff",
                    "Enable encryption at rest"
                ],
                references=["45 CFR 164.312(a)(1)"]
            )
        ]
        
        for control in hipaa_controls:
            self.controls_registry[control.control_id] = control
    
    def _load_additional_frameworks(self):
        """Load additional compliance frameworks"""
        # Additional frameworks can be loaded here
        # This is extensible for future compliance requirements
        pass
    
    async def create_compliance_test_suite(self, framework: ComplianceFramework, 
                                         scope: List[str] = None) -> List[ComplianceTest]:
        """Create comprehensive test suite for compliance framework"""
        try:
            test_suite = []
            
            # Filter controls by framework and scope
            relevant_controls = [
                control for control in self.controls_registry.values()
                if control.framework == framework and (
                    not scope or any(category in scope for category in [control.category.value])
                )
            ]
            
            for control in relevant_controls:
                # Generate automated tests
                automated_tests = await self._generate_automated_tests(control)
                test_suite.extend(automated_tests)
                
                # Generate manual tests
                manual_tests = await self._generate_manual_tests(control)
                test_suite.extend(manual_tests)
            
            logger.info(f"Created test suite with {len(test_suite)} tests for {framework.value}")
            return test_suite
            
        except Exception as e:
            logger.error(f"Failed to create compliance test suite: {e}")
            raise
    
    async def _generate_automated_tests(self, control: ComplianceControl) -> List[ComplianceTest]:
        """Generate automated tests for a compliance control"""
        automated_tests = []
        
        try:
            if control.category == ComplianceCategory.ACCESS_CONTROL:
                # Access control tests
                tests = [
                    ComplianceTest(
                        test_id=f"{control.control_id}_access_matrix",
                        control_id=control.control_id,
                        name="Access Control Matrix Validation",
                        description="Validate user access permissions match defined roles",
                        test_type="automated",
                        automated=True,
                        test_steps=[
                            {"step": "enumerate_users", "action": "list_all_system_users"},
                            {"step": "check_permissions", "action": "validate_user_permissions"},
                            {"step": "verify_roles", "action": "match_roles_to_matrix"}
                        ],
                        expected_results=["All users have appropriate permissions", "No unauthorized access"],
                        failure_criteria=["Excessive permissions found", "Unauthorized access detected"],
                        tools_required=["user_enumeration", "permission_scanner"],
                        estimated_duration=timedelta(minutes=15),
                        prerequisites=["Network access", "Authentication credentials"],
                        cleanup_required=False
                    )
                ]
                automated_tests.extend(tests)
            
            elif control.category == ComplianceCategory.ENCRYPTION:
                # Encryption tests
                tests = [
                    ComplianceTest(
                        test_id=f"{control.control_id}_encryption_validation",
                        control_id=control.control_id,
                        name="Encryption Implementation Test",
                        description="Validate encryption standards and implementation",
                        test_type="automated",
                        automated=True,
                        test_steps=[
                            {"step": "scan_encryption", "action": "identify_encryption_methods"},
                            {"step": "test_strength", "action": "validate_encryption_strength"},
                            {"step": "check_keys", "action": "verify_key_management"}
                        ],
                        expected_results=["Strong encryption implemented", "Proper key management"],
                        failure_criteria=["Weak encryption found", "Poor key management"],
                        tools_required=["ssl_scanner", "crypto_analyzer"],
                        estimated_duration=timedelta(minutes=20),
                        prerequisites=["Target system access"],
                        cleanup_required=False
                    )
                ]
                automated_tests.extend(tests)
            
            elif control.category == ComplianceCategory.LOGGING_MONITORING:
                # Logging and monitoring tests
                tests = [
                    ComplianceTest(
                        test_id=f"{control.control_id}_logging_validation",
                        control_id=control.control_id,
                        name="Logging and Monitoring Test",
                        description="Validate logging configuration and monitoring capabilities",
                        test_type="automated",
                        automated=True,
                        test_steps=[
                            {"step": "check_logging", "action": "verify_log_configuration"},
                            {"step": "test_monitoring", "action": "validate_monitoring_tools"},
                            {"step": "verify_retention", "action": "check_log_retention"}
                        ],
                        expected_results=["Comprehensive logging enabled", "Monitoring tools active"],
                        failure_criteria=["Insufficient logging", "No monitoring detected"],
                        tools_required=["log_analyzer", "monitoring_scanner"],
                        estimated_duration=timedelta(minutes=25),
                        prerequisites=["System access", "Log access"],
                        cleanup_required=False
                    )
                ]
                automated_tests.extend(tests)
            
            return automated_tests
            
        except Exception as e:
            logger.error(f"Failed to generate automated tests for {control.control_id}: {e}")
            return []
    
    async def _generate_manual_tests(self, control: ComplianceControl) -> List[ComplianceTest]:
        """Generate manual tests for a compliance control"""
        manual_tests = []
        
        try:
            # Generate manual review tests
            manual_test = ComplianceTest(
                test_id=f"{control.control_id}_manual_review",
                control_id=control.control_id,
                name=f"Manual Review - {control.title}",
                description=f"Manual review and validation of {control.title}",
                test_type="manual",
                automated=False,
                test_steps=[
                    {"step": "document_review", "action": "review_policies_procedures"},
                    {"step": "interview", "action": "interview_responsible_personnel"},
                    {"step": "evidence_review", "action": "review_supporting_evidence"}
                ],
                expected_results=control.requirements,
                failure_criteria=["Requirements not met", "Insufficient evidence"],
                tools_required=["document_review", "interview_guide"],
                estimated_duration=timedelta(hours=2),
                prerequisites=["Access to documentation", "Personnel availability"],
                cleanup_required=False
            )
            manual_tests.append(manual_test)
            
            return manual_tests
            
        except Exception as e:
            logger.error(f"Failed to generate manual tests for {control.control_id}: {e}")
            return []
    
    async def execute_compliance_test(self, test: ComplianceTest) -> TestResult:
        """Execute individual compliance test"""
        try:
            start_time = datetime.now()
            findings = []
            evidence = []
            status = TestStatus.PENDING
            severity = TestSeverity.INFO
            score = 0.0
            
            logger.info(f"Executing compliance test: {test.test_id}")
            
            if test.automated:
                # Execute automated test
                result = await self._execute_automated_test(test)
                status = result["status"]
                findings = result["findings"]
                evidence = result["evidence"]
                score = result["score"]
                
                # Determine severity based on findings
                if any(finding.get("severity") == "critical" for finding in findings):
                    severity = TestSeverity.CRITICAL
                elif any(finding.get("severity") == "high" for finding in findings):
                    severity = TestSeverity.HIGH
                elif any(finding.get("severity") == "medium" for finding in findings):
                    severity = TestSeverity.MEDIUM
                else:
                    severity = TestSeverity.LOW
            else:
                # Manual test requires human intervention
                status = TestStatus.MANUAL_REVIEW
                findings = [{"type": "manual_review", "description": "Manual review required"}]
            
            # Generate AI-powered recommendations
            recommendations = await self._generate_ai_recommendations(test, findings)
            
            execution_time = datetime.now() - start_time
            
            test_result = TestResult(
                result_id=f"result_{test.test_id}_{int(time.time())}",
                test_id=test.test_id,
                control_id=test.control_id,
                framework=self.controls_registry[test.control_id].framework,
                status=status,
                severity=severity,
                score=score,
                findings=findings,
                evidence=evidence,
                recommendations=recommendations,
                execution_time=execution_time,
                timestamp=datetime.now(),
                tester="NexusScan_Automated_Compliance_Tester",
                environment="production"
            )
            
            logger.info(f"Completed compliance test {test.test_id} with status {status.value}")
            return test_result
            
        except Exception as e:
            logger.error(f"Failed to execute compliance test {test.test_id}: {e}")
            # Return error result
            return TestResult(
                result_id=f"error_{test.test_id}_{int(time.time())}",
                test_id=test.test_id,
                control_id=test.control_id,
                framework=self.controls_registry[test.control_id].framework,
                status=TestStatus.ERROR,
                severity=TestSeverity.HIGH,
                score=0.0,
                findings=[{"type": "error", "description": str(e)}],
                evidence=[],
                recommendations=["Investigate test execution error"],
                execution_time=timedelta(seconds=0),
                timestamp=datetime.now(),
                tester="NexusScan_Automated_Compliance_Tester",
                environment="production"
            )
    
    async def _execute_automated_test(self, test: ComplianceTest) -> Dict[str, Any]:
        """Execute automated compliance test"""
        try:
            findings = []
            evidence = []
            score = 100.0  # Start with perfect score
            
            # Execute test steps
            for step in test.test_steps:
                step_result = await self._execute_test_step(step, test)
                
                if step_result["status"] == "failed":
                    findings.append({
                        "step": step["step"],
                        "finding": step_result["finding"],
                        "severity": step_result.get("severity", "medium"),
                        "evidence": step_result.get("evidence", [])
                    })
                    score -= 25  # Deduct points for failures
                
                evidence.extend(step_result.get("evidence", []))
            
            # Determine overall status
            if not findings:
                status = TestStatus.PASSED
            elif score >= 75:
                status = TestStatus.WARNING
            else:
                status = TestStatus.FAILED
            
            return {
                "status": status,
                "findings": findings,
                "evidence": evidence,
                "score": max(0.0, score)
            }
            
        except Exception as e:
            logger.error(f"Failed to execute automated test: {e}")
            return {
                "status": TestStatus.ERROR,
                "findings": [{"type": "error", "description": str(e)}],
                "evidence": [],
                "score": 0.0
            }
    
    async def _execute_test_step(self, step: Dict[str, Any], test: ComplianceTest) -> Dict[str, Any]:
        """Execute individual test step"""
        try:
            action = step["action"]
            step_name = step["step"]
            
            # Simulate test step execution
            # In real implementation, this would call actual security tools
            
            if action == "list_all_system_users":
                # Simulate user enumeration
                return {
                    "status": "passed",
                    "evidence": [{"type": "user_list", "data": "user enumeration completed"}]
                }
            
            elif action == "validate_user_permissions":
                # Simulate permission validation
                # This could find issues in real implementation
                return {
                    "status": "failed",
                    "finding": "Excessive permissions detected for user 'testuser'",
                    "severity": "medium",
                    "evidence": [{"type": "permission_report", "data": "permission analysis results"}]
                }
            
            elif action == "identify_encryption_methods":
                # Simulate encryption scanning
                return {
                    "status": "passed",
                    "evidence": [{"type": "encryption_scan", "data": "TLS 1.3 detected"}]
                }
            
            elif action == "verify_log_configuration":
                # Simulate logging verification
                return {
                    "status": "warning",
                    "finding": "Some security events not logged",
                    "severity": "low",
                    "evidence": [{"type": "log_config", "data": "log configuration analysis"}]
                }
            
            else:
                # Default case
                return {
                    "status": "passed",
                    "evidence": [{"type": "generic", "data": f"Step {step_name} completed"}]
                }
                
        except Exception as e:
            logger.error(f"Failed to execute test step {step}: {e}")
            return {
                "status": "error",
                "finding": f"Test step execution failed: {e}",
                "severity": "high",
                "evidence": []
            }
    
    async def _generate_ai_recommendations(self, test: ComplianceTest, 
                                         findings: List[Dict[str, Any]]) -> List[str]:
        """Generate AI-powered recommendations for test results"""
        try:
            if not findings:
                return ["Test passed - no recommendations needed"]
            
            # Prepare AI analysis request
            analysis_request = AnalysisRequest(
                request_type="compliance_recommendation",
                data={
                    "test_name": test.name,
                    "control_id": test.control_id,
                    "findings": findings,
                    "test_description": test.description
                },
                context={
                    "framework": self.controls_registry[test.control_id].framework.value,
                    "category": self.controls_registry[test.control_id].category.value
                }
            )
            
            # Get AI recommendations
            ai_response = await self.ai_service.analyze(analysis_request)
            
            if ai_response and "recommendations" in ai_response:
                return ai_response["recommendations"]
            else:
                # Fallback recommendations
                return self._generate_fallback_recommendations(test, findings)
                
        except Exception as e:
            logger.error(f"Failed to generate AI recommendations: {e}")
            return self._generate_fallback_recommendations(test, findings)
    
    def _generate_fallback_recommendations(self, test: ComplianceTest, 
                                         findings: List[Dict[str, Any]]) -> List[str]:
        """Generate fallback recommendations when AI is unavailable"""
        recommendations = []
        
        for finding in findings:
            if "permission" in finding.get("finding", "").lower():
                recommendations.append("Review and remediate excessive user permissions")
                recommendations.append("Implement principle of least privilege")
            
            if "encryption" in finding.get("finding", "").lower():
                recommendations.append("Upgrade to stronger encryption methods")
                recommendations.append("Implement proper key management")
            
            if "logging" in finding.get("finding", "").lower():
                recommendations.append("Enable comprehensive security logging")
                recommendations.append("Implement log monitoring and alerting")
        
        if not recommendations:
            recommendations.append("Review compliance control implementation")
            recommendations.append("Consult framework documentation for specific guidance")
        
        return recommendations
    
    async def execute_compliance_assessment(self, framework: ComplianceFramework,
                                          scope: List[str] = None) -> ComplianceReport:
        """Execute comprehensive compliance assessment"""
        try:
            logger.info(f"Starting compliance assessment for {framework.value}")
            
            # Create test suite
            test_suite = await self.create_compliance_test_suite(framework, scope)
            
            # Execute tests
            test_results = []
            for test in test_suite:
                result = await self.execute_compliance_test(test)
                test_results.append(result)
            
            # Generate compliance report
            report = await self._generate_compliance_report(framework, test_results)
            
            logger.info(f"Completed compliance assessment for {framework.value}")
            return report
            
        except Exception as e:
            logger.error(f"Failed to execute compliance assessment: {e}")
            raise
    
    async def _generate_compliance_report(self, framework: ComplianceFramework,
                                        test_results: List[TestResult]) -> ComplianceReport:
        """Generate comprehensive compliance report"""
        try:
            # Calculate metrics
            total_tests = len(test_results)
            passed_tests = len([r for r in test_results if r.status == TestStatus.PASSED])
            failed_tests = len([r for r in test_results if r.status == TestStatus.FAILED])
            
            critical_findings = len([r for r in test_results if r.severity == TestSeverity.CRITICAL])
            high_findings = len([r for r in test_results if r.severity == TestSeverity.HIGH])
            medium_findings = len([r for r in test_results if r.severity == TestSeverity.MEDIUM])
            low_findings = len([r for r in test_results if r.severity == TestSeverity.LOW])
            
            compliance_percentage = (passed_tests / total_tests * 100) if total_tests > 0 else 0
            overall_score = sum(r.score for r in test_results) / total_tests if total_tests > 0 else 0
            
            # Generate executive summary
            executive_summary = await self._generate_executive_summary(
                framework, test_results, compliance_percentage
            )
            
            # Compile detailed findings
            detailed_findings = []
            for result in test_results:
                if result.findings:
                    detailed_findings.append({
                        "control_id": result.control_id,
                        "test_id": result.test_id,
                        "status": result.status.value,
                        "severity": result.severity.value,
                        "findings": result.findings,
                        "recommendations": result.recommendations
                    })
            
            # Generate remediation plan
            remediation_plan = await self._generate_remediation_plan(test_results)
            
            # Compile overall recommendations
            all_recommendations = []
            for result in test_results:
                all_recommendations.extend(result.recommendations)
            
            # Remove duplicates while preserving order
            unique_recommendations = list(dict.fromkeys(all_recommendations))
            
            report = ComplianceReport(
                report_id=f"compliance_{framework.value}_{int(time.time())}",
                framework=framework,
                scope="Full Assessment",
                test_results=test_results,
                overall_score=overall_score,
                compliance_percentage=compliance_percentage,
                critical_findings=critical_findings,
                high_findings=high_findings,
                medium_findings=medium_findings,
                low_findings=low_findings,
                recommendations=unique_recommendations[:10],  # Top 10 recommendations
                executive_summary=executive_summary,
                detailed_findings=detailed_findings,
                remediation_plan=remediation_plan,
                next_assessment_date=datetime.now() + timedelta(days=365),
                assessor="NexusScan Automated Compliance Tester",
                timestamp=datetime.now()
            )
            
            return report
            
        except Exception as e:
            logger.error(f"Failed to generate compliance report: {e}")
            raise
    
    async def _generate_executive_summary(self, framework: ComplianceFramework,
                                        test_results: List[TestResult],
                                        compliance_percentage: float) -> str:
        """Generate executive summary for compliance report"""
        try:
            total_tests = len(test_results)
            failed_tests = len([r for r in test_results if r.status == TestStatus.FAILED])
            critical_findings = len([r for r in test_results if r.severity == TestSeverity.CRITICAL])
            
            summary = f"""
Executive Summary - {framework.value.upper()} Compliance Assessment

Assessment Overview:
- Total Controls Tested: {total_tests}
- Overall Compliance Percentage: {compliance_percentage:.1f}%
- Failed Controls: {failed_tests}
- Critical Findings: {critical_findings}

Key Findings:
"""
            
            if compliance_percentage >= 90:
                summary += "- Organization demonstrates strong compliance posture\n"
                summary += "- Minor remediation items identified\n"
            elif compliance_percentage >= 75:
                summary += "- Organization shows good compliance foundation\n"
                summary += "- Several areas require attention\n"
            elif compliance_percentage >= 50:
                summary += "- Significant compliance gaps identified\n"
                summary += "- Immediate remediation required\n"
            else:
                summary += "- Major compliance deficiencies found\n"
                summary += "- Comprehensive remediation program needed\n"
            
            if critical_findings > 0:
                summary += f"- {critical_findings} critical security issues require immediate attention\n"
            
            summary += "\nRecommendations:\n"
            summary += "- Prioritize critical and high-severity findings\n"
            summary += "- Implement systematic remediation approach\n"
            summary += "- Establish ongoing compliance monitoring\n"
            
            return summary.strip()
            
        except Exception as e:
            logger.error(f"Failed to generate executive summary: {e}")
            return "Executive summary generation failed"
    
    async def _generate_remediation_plan(self, test_results: List[TestResult]) -> List[Dict[str, Any]]:
        """Generate prioritized remediation plan"""
        try:
            remediation_items = []
            
            # Prioritize by severity
            critical_results = [r for r in test_results if r.severity == TestSeverity.CRITICAL]
            high_results = [r for r in test_results if r.severity == TestSeverity.HIGH]
            medium_results = [r for r in test_results if r.severity == TestSeverity.MEDIUM]
            
            priority = 1
            
            # Add critical items
            for result in critical_results:
                if result.recommendations:
                    remediation_items.append({
                        "priority": priority,
                        "control_id": result.control_id,
                        "severity": result.severity.value,
                        "timeline": "Immediate (0-30 days)",
                        "actions": result.recommendations,
                        "estimated_effort": "High",
                        "business_impact": "Critical"
                    })
                    priority += 1
            
            # Add high priority items
            for result in high_results:
                if result.recommendations:
                    remediation_items.append({
                        "priority": priority,
                        "control_id": result.control_id,
                        "severity": result.severity.value,
                        "timeline": "Short-term (30-90 days)",
                        "actions": result.recommendations,
                        "estimated_effort": "Medium",
                        "business_impact": "High"
                    })
                    priority += 1
            
            # Add medium priority items
            for result in medium_results:
                if result.recommendations:
                    remediation_items.append({
                        "priority": priority,
                        "control_id": result.control_id,
                        "severity": result.severity.value,
                        "timeline": "Medium-term (90-180 days)",
                        "actions": result.recommendations,
                        "estimated_effort": "Low",
                        "business_impact": "Medium"
                    })
                    priority += 1
            
            return remediation_items[:20]  # Top 20 remediation items
            
        except Exception as e:
            logger.error(f"Failed to generate remediation plan: {e}")
            return []
    
    async def generate_compliance_dashboard(self, reports: List[ComplianceReport]) -> Dict[str, Any]:
        """Generate compliance dashboard with multiple framework results"""
        try:
            dashboard = {
                "summary": {
                    "total_frameworks": len(reports),
                    "average_compliance": sum(r.compliance_percentage for r in reports) / len(reports),
                    "total_critical_findings": sum(r.critical_findings for r in reports),
                    "total_high_findings": sum(r.high_findings for r in reports)
                },
                "frameworks": {},
                "trending": {},
                "recommendations": []
            }
            
            # Framework-specific data
            for report in reports:
                dashboard["frameworks"][report.framework.value] = {
                    "compliance_percentage": report.compliance_percentage,
                    "overall_score": report.overall_score,
                    "critical_findings": report.critical_findings,
                    "high_findings": report.high_findings,
                    "status": "compliant" if report.compliance_percentage >= 80 else "non_compliant",
                    "last_assessment": report.timestamp.isoformat()
                }
            
            # Top recommendations across all frameworks
            all_recommendations = []
            for report in reports:
                all_recommendations.extend(report.recommendations)
            
            # Count recommendation frequency
            recommendation_counts = defaultdict(int)
            for rec in all_recommendations:
                recommendation_counts[rec] += 1
            
            # Get top recommendations
            top_recommendations = sorted(
                recommendation_counts.items(),
                key=lambda x: x[1],
                reverse=True
            )[:10]
            
            dashboard["recommendations"] = [rec[0] for rec in top_recommendations]
            
            return dashboard
            
        except Exception as e:
            logger.error(f"Failed to generate compliance dashboard: {e}")
            return {}


# Example usage and testing functions
async def main():
    """Example usage of the Automated Compliance Tester"""
    # Initialize components (these would be real instances in production)
    config = Config()
    db_manager = DatabaseManager(config)
    ai_service = AIServiceManager(config)
    
    # Initialize compliance tester
    compliance_tester = AutomatedComplianceTester(config, db_manager, ai_service)
    
    # Execute SOC2 assessment
    soc2_report = await compliance_tester.execute_compliance_assessment(
        ComplianceFramework.SOC2,
        scope=["access_control", "logging_monitoring"]
    )
    
    print(f"SOC2 Compliance: {soc2_report.compliance_percentage:.1f}%")
    print(f"Critical Findings: {soc2_report.critical_findings}")
    
    # Execute PCI-DSS assessment
    pci_report = await compliance_tester.execute_compliance_assessment(
        ComplianceFramework.PCI_DSS,
        scope=["network_security", "data_protection"]
    )
    
    print(f"PCI-DSS Compliance: {pci_report.compliance_percentage:.1f}%")
    print(f"Critical Findings: {pci_report.critical_findings}")
    
    # Generate dashboard
    dashboard = await compliance_tester.generate_compliance_dashboard([soc2_report, pci_report])
    print(f"Overall Compliance: {dashboard['summary']['average_compliance']:.1f}%")


if __name__ == "__main__":
    asyncio.run(main())