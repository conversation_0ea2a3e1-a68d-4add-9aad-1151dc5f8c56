/**
 * John the Ripper Tool Component
 * Community-enhanced version of the legendary password cracker
 */
import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { 
  Key,
  Play,
  Square,
  Download,
  Settings,
  Target,
  Globe,
  FileText,
  Terminal,
  Copy,
  RotateCcw,
  Clock,
  Zap,
  Filter,
  Eye,
  AlertTriangle,
  CheckCircle,
  TrendingUp,
  Activity,
  Lock,
  Search,
  List,
  BarChart3,
  Database,
  Server,
  Bug,
  Info,
  Code,
  Package,
  Layers,
  Shield,
  Crosshair,
  Cpu,
  Network,
  Gauge,
  Tag,
  Hash,
  Workflow,
  GitBranch,
  Book,
  Users,
  Table,
  FileX,
  Skull,
  Unlock,
  HardDrive,
  Flame,
  Binary,
  Braces,
  FileKey,
  History,
  Folder,
  Star,
  Crown,
  Award,
  Sparkles
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useBackendStore } from '@/stores/backend-store';
import { apiClient, ToolExecution } from '@/services/api-client';
import { toast } from 'sonner';

/**
 * John the Ripper configuration interface
 */
interface JohnConfig {
  target: {
    hashFile: string;
    hashString: string;
    format: string;
    username: boolean;
  };
  attack: {
    mode: 'wordlist' | 'incremental' | 'single' | 'external' | 'mask';
    wordlist: string;
    charsets: string;
    minLength: number;
    maxLength: number;
    mask: string;
    external: string;
  };
  rules: {
    enabled: boolean;
    ruleFile: string;
    builtinRules: string[];
  };
  session: {
    name: string;
    restore: boolean;
    fork: number;
    nodeMin: number;
    nodeMax: number;
    nodeCount: number;
  };
  output: {
    showProgress: boolean;
    verbosity: number;
    potFile: string;
    logFile: string;
    format: 'default' | 'csv' | 'json';
  };
  performance: {
    maxRunTime: number;
    idleTime: number;
    loopback: boolean;
    markov: boolean;
    markovLevel: number;
  };
}

/**
 * John the Ripper hash formats
 */
const HASH_FORMATS = {
  // Traditional formats
  'des': { name: 'Traditional DES', category: 'Traditional' },
  'bsdi': { name: 'BSDi Extended DES', category: 'Traditional' },
  'md5crypt': { name: 'MD5-based crypt(3)', category: 'Traditional' },
  'bcrypt': { name: 'bcrypt', category: 'Traditional' },
  'scrypt': { name: 'scrypt', category: 'Traditional' },
  
  // Raw hashes
  'raw-md5': { name: 'Raw MD5', category: 'Raw Hashes' },
  'raw-sha1': { name: 'Raw SHA-1', category: 'Raw Hashes' },
  'raw-sha256': { name: 'Raw SHA-256', category: 'Raw Hashes' },
  'raw-sha512': { name: 'Raw SHA-512', category: 'Raw Hashes' },
  
  // Windows
  'nt': { name: 'Windows NTLM', category: 'Windows' },
  'lm': { name: 'Windows LM', category: 'Windows' },
  'netlm': { name: 'Windows NetLM', category: 'Windows' },
  'netntlm': { name: 'Windows NetNTLM', category: 'Windows' },
  'mssql': { name: 'MS SQL Server', category: 'Windows' },
  
  // Applications
  'zip': { name: 'ZIP/WinZip/7-Zip', category: 'Applications' },
  'rar': { name: 'RAR3/RAR5', category: 'Applications' },
  'pdf': { name: 'PDF documents', category: 'Applications' },
  'office': { name: 'MS Office docs', category: 'Applications' },
  'keepass': { name: 'KeePass databases', category: 'Applications' },
  'bitcoin': { name: 'Bitcoin wallets', category: 'Applications' },
  
  // Database
  'mysql-sha1': { name: 'MySQL password()', category: 'Database' },
  'postgres': { name: 'PostgreSQL MD5', category: 'Database' },
  'oracle': { name: 'Oracle 10g/11g', category: 'Database' },
  
  // Network
  'krb5': { name: 'Kerberos 5', category: 'Network' },
  'ssh': { name: 'SSH private keys', category: 'Network' },
  'wpa': { name: 'WPA/WPA2 PSK', category: 'Network' }
};

/**
 * Attack modes for John the Ripper
 */
const ATTACK_MODES = {
  'wordlist': { 
    name: 'Wordlist Attack', 
    description: 'Dictionary-based attack using wordlists',
    icon: Book
  },
  'incremental': { 
    name: 'Incremental Mode', 
    description: 'Brute-force with character frequency analysis',
    icon: TrendingUp
  },
  'single': { 
    name: 'Single Crack Mode', 
    description: 'Uses login names and GECOS information',
    icon: Target
  },
  'external': { 
    name: 'External Mode', 
    description: 'Custom algorithm using external programs',
    icon: Code
  },
  'mask': { 
    name: 'Mask Attack', 
    description: 'Mask-based attack with custom patterns',
    icon: Filter
  }
};

/**
 * Built-in rule sets
 */
const BUILTIN_RULES = {
  'best64': { name: 'Best64', description: 'Best 64 rules for efficiency' },
  'rockyou-30000': { name: 'RockYou 30K', description: '30,000 most common passwords' },
  'dive': { name: 'DIVE', description: 'Extensive rule set' },
  'InsidePro': { name: 'InsidePro', description: 'Professional rule set' },
  'generated': { name: 'Generated', description: 'Auto-generated rules' },
  'specific': { name: 'Specific', description: 'Format-specific rules' },
  'extra': { name: 'Extra', description: 'Additional rule variations' }
};

/**
 * Performance presets
 */
const PERFORMANCE_PRESETS = {
  'stealth': {
    name: 'Stealth Mode',
    description: 'Minimal resource usage, slow but discrete',
    fork: 1,
    idleTime: 5,
    markov: false
  },
  'balanced': {
    name: 'Balanced',
    description: 'Good balance of speed and resource usage',
    fork: 2,
    idleTime: 2,
    markov: true
  },
  'aggressive': {
    name: 'Aggressive',
    description: 'High resource usage for maximum speed',
    fork: 4,
    idleTime: 0,
    markov: true
  },
  'maximum': {
    name: 'Maximum Performance',
    description: 'Use all available resources',
    fork: 8,
    idleTime: 0,
    markov: true
  }
};

/**
 * Incremental character sets
 */
const INCREMENTAL_CHARSETS = {
  'alpha': { name: 'Alpha', description: 'Alphabetic characters only', chars: 'abcdefghijklmnopqrstuvwxyz' },
  'digits': { name: 'Digits', description: 'Numeric characters only', chars: '0123456789' },
  'alnum': { name: 'Alphanumeric', description: 'Letters and numbers', chars: 'abcdefghijklmnopqrstuvwxyz0123456789' },
  'ascii': { name: 'ASCII Printable', description: 'All printable ASCII characters', chars: 'All printable ASCII' },
  'lanman': { name: 'LanMan', description: 'LM hash character set', chars: 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!"#$%&\'()*+,-./:;<=>?@[\\]^_`{|}~' },
  'nt': { name: 'NTLM', description: 'NTLM character set', chars: 'Full Unicode support' }
};

export default function JohnTheRipperTool() {
  const { isConnected } = useBackendStore();
  const [config, setConfig] = React.useState<JohnConfig>({
    target: {
      hashFile: '',
      hashString: '',
      format: 'raw-md5',
      username: false
    },
    attack: {
      mode: 'wordlist',
      wordlist: '/usr/share/john/password.lst',
      charsets: 'alnum',
      minLength: 1,
      maxLength: 8,
      mask: '?a?a?a?a?a?a?a?a',
      external: ''
    },
    rules: {
      enabled: true,
      ruleFile: '',
      builtinRules: ['best64']
    },
    session: {
      name: 'john_session',
      restore: false,
      fork: 1,
      nodeMin: 1,
      nodeMax: 1,
      nodeCount: 1
    },
    output: {
      showProgress: true,
      verbosity: 3,
      potFile: '',
      logFile: '',
      format: 'default'
    },
    performance: {
      maxRunTime: 3600,
      idleTime: 2,
      loopback: false,
      markov: true,
      markovLevel: 200
    }
  });

  const [isRunning, setIsRunning] = React.useState(false);
  const [output, setOutput] = React.useState<string[]>([]);
  const [results, setResults] = React.useState<any[]>([]);
  const [currentExecution, setCurrentExecution] = React.useState<ToolExecution | null>(null);
  const [progress, setProgress] = React.useState(0);

  /**
   * Apply performance preset
   */
  const applyPreset = (preset: keyof typeof PERFORMANCE_PRESETS) => {
    const presetConfig = PERFORMANCE_PRESETS[preset];
    setConfig(prev => ({
      ...prev,
      session: {
        ...prev.session,
        fork: presetConfig.fork
      },
      performance: {
        ...prev.performance,
        idleTime: presetConfig.idleTime,
        markov: presetConfig.markov
      }
    }));
    toast.success(`Applied ${presetConfig.name} preset`);
  };

  /**
   * Generate John the Ripper command
   */
  const generateCommand = (): string => {
    const parts = ['john'];

    // Target specification
    if (config.target.hashFile) {
      parts.push(config.target.hashFile);
    }

    // Hash format
    if (config.target.format) {
      parts.push(`--format=${config.target.format}`);
    }

    // Attack mode
    switch (config.attack.mode) {
      case 'wordlist':
        if (config.attack.wordlist) {
          parts.push(`--wordlist=${config.attack.wordlist}`);
        }
        break;
      case 'incremental':
        parts.push(`--incremental=${config.attack.charsets}`);
        if (config.attack.minLength > 1) {
          parts.push(`--min-length=${config.attack.minLength}`);
        }
        if (config.attack.maxLength < 20) {
          parts.push(`--max-length=${config.attack.maxLength}`);
        }
        break;
      case 'single':
        parts.push('--single');
        break;
      case 'external':
        if (config.attack.external) {
          parts.push(`--external=${config.attack.external}`);
        }
        break;
      case 'mask':
        if (config.attack.mask) {
          parts.push(`--mask=${config.attack.mask}`);
        }
        break;
    }

    // Rules
    if (config.rules.enabled) {
      if (config.rules.ruleFile) {
        parts.push(`--rules=${config.rules.ruleFile}`);
      } else if (config.rules.builtinRules.length > 0) {
        parts.push(`--rules=${config.rules.builtinRules.join(',')}`);
      }
    }

    // Session management
    if (config.session.name) {
      parts.push(`--session=${config.session.name}`);
    }
    if (config.session.restore) {
      parts.push('--restore');
    }
    if (config.session.fork > 1) {
      parts.push(`--fork=${config.session.fork}`);
    }

    // Output options
    if (config.output.showProgress) {
      parts.push('--progress-every=1');
    }
    if (config.output.verbosity !== 3) {
      parts.push(`--verbosity=${config.output.verbosity}`);
    }
    if (config.output.potFile) {
      parts.push(`--pot=${config.output.potFile}`);
    }
    if (config.output.logFile) {
      parts.push(`--log-stderr=${config.output.logFile}`);
    }

    // Performance options
    if (config.performance.maxRunTime > 0) {
      parts.push(`--max-run-time=${config.performance.maxRunTime}`);
    }
    if (config.performance.idleTime > 0) {
      parts.push(`--idle=${config.performance.idleTime}`);
    }
    if (config.performance.loopback) {
      parts.push('--loopback');
    }
    if (config.performance.markov) {
      parts.push(`--markov=${config.performance.markovLevel}`);
    }

    return parts.join(' ');
  };

  /**
   * Start John the Ripper execution
   */
  const startExecution = async () => {
    if (!isConnected) {
      toast.error('Backend not connected');
      return;
    }

    // Validation
    if (!config.target.hashFile && !config.target.hashString) {
      toast.error('Please specify a hash file or hash string');
      return;
    }

    try {
      setIsRunning(true);
      setOutput([]);
      setResults([]);
      setProgress(0);

      const execution = await apiClient.startTool('john', {
        ...config,
        command: generateCommand()
      });

      setCurrentExecution(execution);
      toast.success('John the Ripper started successfully');

      // Listen for progress updates
      const eventSource = new EventSource(`/api/tools/john/execution/${execution.id}/stream`);
      
      eventSource.onmessage = (event) => {
        const data = JSON.parse(event.data);
        
        if (data.type === 'output') {
          setOutput(prev => [...prev, data.content]);
        } else if (data.type === 'progress') {
          setProgress(data.progress);
        } else if (data.type === 'result') {
          setResults(prev => [...prev, data.result]);
        } else if (data.type === 'complete') {
          setIsRunning(false);
          eventSource.close();
          toast.success('John the Ripper completed');
        } else if (data.type === 'error') {
          setIsRunning(false);
          eventSource.close();
          toast.error(`Error: ${data.message}`);
        }
      };

      eventSource.onerror = () => {
        setIsRunning(false);
        eventSource.close();
        toast.error('Connection to execution stream lost');
      };

    } catch (error) {
      setIsRunning(false);
      console.error('Failed to start John the Ripper:', error);
      toast.error('Failed to start John the Ripper');
    }
  };

  /**
   * Stop execution
   */
  const stopExecution = async () => {
    if (!currentExecution) return;

    try {
      await apiClient.stopTool('john', currentExecution.id);
      setIsRunning(false);
      toast.success('John the Ripper stopped');
    } catch (error) {
      console.error('Failed to stop John the Ripper:', error);
      toast.error('Failed to stop John the Ripper');
    }
  };

  /**
   * Export results
   */
  const exportResults = () => {
    const exportData = {
      config,
      results,
      command: generateCommand(),
      timestamp: new Date().toISOString()
    };

    const blob = new Blob([JSON.stringify(exportData, null, 2)], {
      type: 'application/json'
    });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `john-results-${Date.now()}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    
    toast.success('Results exported successfully');
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className="flex items-center justify-center w-10 h-10 bg-red-100 rounded-lg">
            <Crown className="w-5 h-5 text-red-600" />
          </div>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">John the Ripper</h1>
            <p className="text-sm text-gray-500">Community-enhanced version of the legendary password cracker</p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Badge variant={isConnected ? "default" : "secondary"}>
            {isConnected ? "Connected" : "Disconnected"}
          </Badge>
          <Badge variant="outline" className="font-mono">v1.9.0-jumbo-1</Badge>
        </div>
      </div>

      {/* Warning Notice */}
      <Card className="border-yellow-200 bg-yellow-50">
        <CardContent className="pt-4">
          <div className="flex items-start space-x-2">
            <AlertTriangle className="w-5 h-5 text-yellow-600 mt-0.5" />
            <div className="space-y-1">
              <p className="text-sm font-medium text-yellow-800">
                Authorization Required
              </p>
              <p className="text-xs text-yellow-700">
                Only use John the Ripper on systems you own or have explicit permission to test. 
                Password cracking without authorization is illegal.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      <Tabs defaultValue="configure" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="configure" className="flex items-center space-x-2">
            <Settings className="w-4 h-4" />
            <span>Configure</span>
          </TabsTrigger>
          <TabsTrigger value="output" className="flex items-center space-x-2">
            <Terminal className="w-4 h-4" />
            <span>Output</span>
          </TabsTrigger>
          <TabsTrigger value="results" className="flex items-center space-x-2">
            <Target className="w-4 h-4" />
            <span>Results</span>
          </TabsTrigger>
          <TabsTrigger value="command" className="flex items-center space-x-2">
            <Code className="w-4 h-4" />
            <span>Command</span>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="configure" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Target Configuration */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Target className="w-5 h-5" />
                  <span>Target Configuration</span>
                </CardTitle>
                <CardDescription>
                  Specify the hash file or hash string to crack
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium">Hash File Path</label>
                  <Input
                    placeholder="/path/to/hashes.txt"
                    value={config.target.hashFile}
                    onChange={(e) => setConfig(prev => ({
                      ...prev,
                      target: { ...prev.target, hashFile: e.target.value }
                    }))}
                  />
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">Hash String (Alternative)</label>
                  <Textarea
                    placeholder="Enter hash directly (e.g., 5d41402abc4b2a76b9719d911017c592)"
                    value={config.target.hashString}
                    onChange={(e) => setConfig(prev => ({
                      ...prev,
                      target: { ...prev.target, hashString: e.target.value }
                    }))}
                    rows={3}
                  />
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">Hash Format</label>
                  <Select 
                    value={config.target.format} 
                    onValueChange={(value) => setConfig(prev => ({
                      ...prev,
                      target: { ...prev.target, format: value }
                    }))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {Object.entries(
                        Object.entries(HASH_FORMATS).reduce((acc, [key, value]) => {
                          if (!acc[value.category]) acc[value.category] = [];
                          acc[value.category].push({ key, ...value });
                          return acc;
                        }, {} as Record<string, any>)
                      ).map(([category, formats]) => (
                        <div key={category}>
                          <div className="px-2 py-1 text-xs font-semibold text-gray-500 uppercase">
                            {category}
                          </div>
                          {formats.map((format: any) => (
                            <SelectItem key={format.key} value={format.key}>
                              {format.name}
                            </SelectItem>
                          ))}
                        </div>
                      ))}
                    </SelectContent>
                  </Select>
                  <p className="text-xs text-gray-500">
                    {HASH_FORMATS[config.target.format as keyof typeof HASH_FORMATS]?.name}
                  </p>
                </div>

                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="username"
                    checked={config.target.username}
                    onChange={(e) => setConfig(prev => ({
                      ...prev,
                      target: { ...prev.target, username: e.target.checked }
                    }))}
                    className="rounded"
                  />
                  <label htmlFor="username" className="text-sm font-medium">
                    Hash file contains usernames
                  </label>
                </div>
              </CardContent>
            </Card>

            {/* Attack Configuration */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Zap className="w-5 h-5" />
                  <span>Attack Configuration</span>
                </CardTitle>
                <CardDescription>
                  Configure the attack mode and parameters
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium">Attack Mode</label>
                  <Select 
                    value={config.attack.mode} 
                    onValueChange={(value: any) => setConfig(prev => ({
                      ...prev,
                      attack: { ...prev.attack, mode: value }
                    }))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {Object.entries(ATTACK_MODES).map(([key, mode]) => (
                        <SelectItem key={key} value={key}>
                          <div className="flex items-center space-x-2">
                            <mode.icon className="w-4 h-4" />
                            <div>
                              <div className="font-medium">{mode.name}</div>
                              <div className="text-xs text-gray-500">{mode.description}</div>
                            </div>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {config.attack.mode === 'wordlist' && (
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Wordlist Path</label>
                    <Input
                      placeholder="/usr/share/john/password.lst"
                      value={config.attack.wordlist}
                      onChange={(e) => setConfig(prev => ({
                        ...prev,
                        attack: { ...prev.attack, wordlist: e.target.value }
                      }))}
                    />
                  </div>
                )}

                {config.attack.mode === 'incremental' && (
                  <>
                    <div className="space-y-2">
                      <label className="text-sm font-medium">Character Set</label>
                      <Select 
                        value={config.attack.charsets} 
                        onValueChange={(value) => setConfig(prev => ({
                          ...prev,
                          attack: { ...prev.attack, charsets: value }
                        }))}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {Object.entries(INCREMENTAL_CHARSETS).map(([key, charset]) => (
                            <SelectItem key={key} value={key}>
                              <div>
                                <div className="font-medium">{charset.name}</div>
                                <div className="text-xs text-gray-500">{charset.description}</div>
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <label className="text-sm font-medium">Min Length</label>
                        <Input
                          type="number"
                          min="1"
                          max="20"
                          value={config.attack.minLength}
                          onChange={(e) => setConfig(prev => ({
                            ...prev,
                            attack: { ...prev.attack, minLength: parseInt(e.target.value) || 1 }
                          }))}
                        />
                      </div>
                      <div className="space-y-2">
                        <label className="text-sm font-medium">Max Length</label>
                        <Input
                          type="number"
                          min="1"
                          max="20"
                          value={config.attack.maxLength}
                          onChange={(e) => setConfig(prev => ({
                            ...prev,
                            attack: { ...prev.attack, maxLength: parseInt(e.target.value) || 8 }
                          }))}
                        />
                      </div>
                    </div>
                  </>
                )}

                {config.attack.mode === 'mask' && (
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Mask Pattern</label>
                    <Input
                      placeholder="?a?a?a?a?a?a?a?a"
                      value={config.attack.mask}
                      onChange={(e) => setConfig(prev => ({
                        ...prev,
                        attack: { ...prev.attack, mask: e.target.value }
                      }))}
                    />
                    <p className="text-xs text-gray-500">
                      ?a = all printable ASCII, ?l = lowercase, ?u = uppercase, ?d = digits
                    </p>
                  </div>
                )}

                {config.attack.mode === 'external' && (
                  <div className="space-y-2">
                    <label className="text-sm font-medium">External Program</label>
                    <Input
                      placeholder="external_program"
                      value={config.attack.external}
                      onChange={(e) => setConfig(prev => ({
                        ...prev,
                        attack: { ...prev.attack, external: e.target.value }
                      }))}
                    />
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Rules Configuration */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Workflow className="w-5 h-5" />
                  <span>Rules Configuration</span>
                </CardTitle>
                <CardDescription>
                  Configure word mangling rules for enhanced attacks
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="rules-enabled"
                    checked={config.rules.enabled}
                    onChange={(e) => setConfig(prev => ({
                      ...prev,
                      rules: { ...prev.rules, enabled: e.target.checked }
                    }))}
                    className="rounded"
                  />
                  <label htmlFor="rules-enabled" className="text-sm font-medium">
                    Enable word mangling rules
                  </label>
                </div>

                {config.rules.enabled && (
                  <>
                    <div className="space-y-2">
                      <label className="text-sm font-medium">Custom Rule File</label>
                      <Input
                        placeholder="/path/to/custom.rule"
                        value={config.rules.ruleFile}
                        onChange={(e) => setConfig(prev => ({
                          ...prev,
                          rules: { ...prev.rules, ruleFile: e.target.value }
                        }))}
                      />
                    </div>

                    <div className="space-y-2">
                      <label className="text-sm font-medium">Built-in Rule Sets</label>
                      <div className="grid grid-cols-1 gap-2">
                        {Object.entries(BUILTIN_RULES).map(([key, rule]) => (
                          <div key={key} className="flex items-center space-x-2">
                            <input
                              type="checkbox"
                              id={`rule-${key}`}
                              checked={config.rules.builtinRules.includes(key)}
                              onChange={(e) => {
                                if (e.target.checked) {
                                  setConfig(prev => ({
                                    ...prev,
                                    rules: {
                                      ...prev.rules,
                                      builtinRules: [...prev.rules.builtinRules, key]
                                    }
                                  }));
                                } else {
                                  setConfig(prev => ({
                                    ...prev,
                                    rules: {
                                      ...prev.rules,
                                      builtinRules: prev.rules.builtinRules.filter(r => r !== key)
                                    }
                                  }));
                                }
                              }}
                              className="rounded"
                            />
                            <label htmlFor={`rule-${key}`} className="text-sm">
                              <span className="font-medium">{rule.name}</span>
                              <span className="text-gray-500 ml-2">{rule.description}</span>
                            </label>
                          </div>
                        ))}
                      </div>
                    </div>
                  </>
                )}
              </CardContent>
            </Card>

            {/* Performance Configuration */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Gauge className="w-5 h-5" />
                  <span>Performance Configuration</span>
                </CardTitle>
                <CardDescription>
                  Optimize performance and resource usage
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium">Performance Presets</label>
                  <div className="grid grid-cols-2 gap-2">
                    {Object.entries(PERFORMANCE_PRESETS).map(([key, preset]) => (
                      <Button
                        key={key}
                        variant="outline"
                        size="sm"
                        onClick={() => applyPreset(key as keyof typeof PERFORMANCE_PRESETS)}
                        className="h-auto p-3 text-left"
                      >
                        <div>
                          <div className="font-medium text-xs">{preset.name}</div>
                          <div className="text-xs text-gray-500">{preset.description}</div>
                        </div>
                      </Button>
                    ))}
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Fork Processes</label>
                    <Input
                      type="number"
                      min="1"
                      max="16"
                      value={config.session.fork}
                      onChange={(e) => setConfig(prev => ({
                        ...prev,
                        session: { ...prev.session, fork: parseInt(e.target.value) || 1 }
                      }))}
                    />
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Max Runtime (seconds)</label>
                    <Input
                      type="number"
                      min="0"
                      value={config.performance.maxRunTime}
                      onChange={(e) => setConfig(prev => ({
                        ...prev,
                        performance: { ...prev.performance, maxRunTime: parseInt(e.target.value) || 0 }
                      }))}
                    />
                  </div>
                </div>

                <div className="space-y-3">
                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="markov"
                      checked={config.performance.markov}
                      onChange={(e) => setConfig(prev => ({
                        ...prev,
                        performance: { ...prev.performance, markov: e.target.checked }
                      }))}
                      className="rounded"
                    />
                    <label htmlFor="markov" className="text-sm font-medium">
                      Enable Markov mode for better statistics
                    </label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="loopback"
                      checked={config.performance.loopback}
                      onChange={(e) => setConfig(prev => ({
                        ...prev,
                        performance: { ...prev.performance, loopback: e.target.checked }
                      }))}
                      className="rounded"
                    />
                    <label htmlFor="loopback" className="text-sm font-medium">
                      Use cracked passwords as wordlist
                    </label>
                  </div>
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">Session Name</label>
                  <Input
                    placeholder="john_session"
                    value={config.session.name}
                    onChange={(e) => setConfig(prev => ({
                      ...prev,
                      session: { ...prev.session, name: e.target.value }
                    }))}
                  />
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Execution Controls */}
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <Button
                    onClick={startExecution}
                    disabled={!isConnected || isRunning}
                    size="lg"
                    className="bg-red-600 hover:bg-red-700"
                  >
                    <Play className="w-4 h-4 mr-2" />
                    Start John the Ripper
                  </Button>
                  
                  {isRunning && (
                    <Button
                      onClick={stopExecution}
                      variant="outline"
                      size="lg"
                    >
                      <Square className="w-4 h-4 mr-2" />
                      Stop
                    </Button>
                  )}
                </div>

                <div className="flex items-center space-x-2">
                  {isRunning && (
                    <div className="flex items-center space-x-2">
                      <Activity className="w-4 h-4 text-green-600 animate-pulse" />
                      <span className="text-sm text-green-600">Running...</span>
                    </div>
                  )}
                  <Badge variant="outline">
                    {results.length} results
                  </Badge>
                </div>
              </div>

              {isRunning && (
                <div className="mt-4">
                  <div className="flex items-center justify-between text-sm text-gray-600 mb-2">
                    <span>Progress</span>
                    <span>{progress.toFixed(1)}%</span>
                  </div>
                  <Progress value={progress} className="h-2" />
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="output" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Terminal className="w-5 h-5" />
                <span>Real-time Output</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="bg-black rounded-lg p-4 h-96 overflow-y-auto font-mono text-sm">
                {output.length === 0 ? (
                  <div className="text-gray-500 italic">
                    No output yet. Start John the Ripper to see results.
                  </div>
                ) : (
                  output.map((line, index) => (
                    <div key={index} className="text-green-400">
                      {line}
                    </div>
                  ))
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="results" className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold">Cracked Passwords</h3>
            {results.length > 0 && (
              <Button onClick={exportResults} variant="outline">
                <Download className="w-4 h-4 mr-2" />
                Export Results
              </Button>
            )}
          </div>

          {results.length === 0 ? (
            <Card>
              <CardContent className="pt-6">
                <div className="text-center py-8">
                  <Key className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500">No passwords cracked yet</p>
                  <p className="text-sm text-gray-400">Results will appear here as passwords are found</p>
                </div>
              </CardContent>
            </Card>
          ) : (
            <div className="grid gap-4">
              {results.map((result, index) => (
                <Card key={index}>
                  <CardContent className="pt-4">
                    <div className="flex items-center justify-between">
                      <div className="space-y-1">
                        <div className="flex items-center space-x-2">
                          <CheckCircle className="w-4 h-4 text-green-600" />
                          <span className="font-mono text-sm">{result.username || 'Unknown'}</span>
                        </div>
                        <div className="font-mono text-lg font-semibold text-green-600">
                          {result.password}
                        </div>
                        <div className="text-xs text-gray-500">
                          Hash: {result.hash?.substring(0, 20)}...
                        </div>
                      </div>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          navigator.clipboard.writeText(result.password);
                          toast.success('Password copied to clipboard');
                        }}
                      >
                        <Copy className="w-4 h-4" />
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>

        <TabsContent value="command" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Code className="w-5 h-5" />
                <span>Generated Command</span>
              </CardTitle>
              <CardDescription>
                Review and copy the John the Ripper command
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="bg-gray-900 rounded-lg p-4">
                  <code className="text-green-400 font-mono text-sm whitespace-pre-wrap">
                    {generateCommand()}
                  </code>
                </div>
                <div className="flex space-x-2">
                  <Button
                    variant="outline"
                    onClick={() => {
                      navigator.clipboard.writeText(generateCommand());
                      toast.success('Command copied to clipboard');
                    }}
                  >
                    <Copy className="w-4 h-4 mr-2" />
                    Copy Command
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Command Explanation */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Book className="w-5 h-5" />
                <span>Command Explanation</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3 text-sm">
                <div>
                  <span className="font-semibold">john</span>
                  <span className="text-gray-600 ml-2">- John the Ripper password cracker</span>
                </div>
                
                {config.target.hashFile && (
                  <div>
                    <span className="font-semibold">{config.target.hashFile}</span>
                    <span className="text-gray-600 ml-2">- Target hash file</span>
                  </div>
                )}
                
                <div>
                  <span className="font-semibold">--format={config.target.format}</span>
                  <span className="text-gray-600 ml-2">- Hash format to crack</span>
                </div>
                
                {config.attack.mode === 'wordlist' && (
                  <div>
                    <span className="font-semibold">--wordlist={config.attack.wordlist}</span>
                    <span className="text-gray-600 ml-2">- Dictionary attack with wordlist</span>
                  </div>
                )}
                
                {config.attack.mode === 'incremental' && (
                  <div>
                    <span className="font-semibold">--incremental={config.attack.charsets}</span>
                    <span className="text-gray-600 ml-2">- Brute-force with character frequency analysis</span>
                  </div>
                )}
                
                {config.rules.enabled && (
                  <div>
                    <span className="font-semibold">--rules</span>
                    <span className="text-gray-600 ml-2">- Enable word mangling rules</span>
                  </div>
                )}
                
                {config.session.fork > 1 && (
                  <div>
                    <span className="font-semibold">--fork={config.session.fork}</span>
                    <span className="text-gray-600 ml-2">- Run multiple parallel processes</span>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}