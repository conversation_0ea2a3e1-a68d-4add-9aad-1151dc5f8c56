#!/usr/bin/env python3
"""
JSON Report Exporter for NexusScan
High-performance JSON report generation with advanced formatting
"""

import json
import logging
from datetime import datetime
from typing import Dict, Any
from pathlib import Path

from ..report_generator import ReportData

logger = logging.getLogger(__name__)

class JSONExporter:
    """Advanced JSON report exporter with structured data"""
    
    def __init__(self):
        """Initialize JSON exporter"""
        self.version = "3.0.0"
    
    async def export(self, report_data: ReportData, output_path: str) -> str:
        """Export report data to JSON format
        
        Args:
            report_data: Complete report data structure
            output_path: Path to save JSON file
            
        Returns:
            Path to generated JSON file
        """
        try:
            # Build comprehensive JSON structure
            json_report = {
                "nexusscan_report": {
                    "metadata": {
                        "format_version": self.version,
                        "generated_at": report_data.generated_at.isoformat(),
                        "generator": "NexusScan Advanced Reporting Engine",
                        "schema_version": "3.0",
                        "report_type": "security_assessment"
                    },
                    "campaign": {
                        "id": report_data.metadata.campaign_id,
                        "name": report_data.metadata.campaign_name,
                        "timeline": {
                            "scan_start": report_data.metadata.scan_start.isoformat(),
                            "scan_end": report_data.metadata.scan_end.isoformat(),
                            "duration_seconds": (report_data.metadata.scan_end - report_data.metadata.scan_start).total_seconds(),
                            "generated_at": report_data.generated_at.isoformat()
                        },
                        "scope": {
                            "targets": report_data.metadata.targets,
                            "total_targets": len(report_data.metadata.targets),
                            "scan_types": report_data.metadata.scan_types,
                            "tools_used": report_data.metadata.tools_used
                        },
                        "operator": report_data.metadata.operator
                    },
                    "summary": {
                        "total_vulnerabilities": report_data.metadata.total_vulnerabilities,
                        "severity_distribution": report_data.metadata.severity_breakdown,
                        "risk_score": self._calculate_risk_score(report_data.vulnerabilities),
                        "compliance_status": self._assess_compliance_status(report_data.vulnerabilities),
                        "executive_summary": report_data.executive_summary,
                        "technical_overview": report_data.technical_findings
                    },
                    "findings": {
                        "vulnerabilities": [
                            self._format_vulnerability(vuln) for vuln in report_data.vulnerabilities
                        ],
                        "by_severity": self._group_vulnerabilities_by_severity(report_data.vulnerabilities),
                        "by_target": self._group_vulnerabilities_by_target(report_data.vulnerabilities),
                        "by_tool": self._group_vulnerabilities_by_tool(report_data.vulnerabilities)
                    },
                    "recommendations": {
                        "immediate_actions": [rec for rec in report_data.recommendations if "URGENT" in rec or "critical" in rec.lower()],
                        "short_term": [rec for rec in report_data.recommendations if rec not in [r for r in report_data.recommendations if "URGENT" in r or "critical" in r.lower()]],
                        "long_term": [
                            "Establish ongoing security monitoring",
                            "Implement security awareness training",
                            "Regular penetration testing schedule"
                        ],
                        "all_recommendations": report_data.recommendations
                    },
                    "compliance": {
                        "notes": report_data.compliance_notes,
                        "frameworks": self._assess_compliance_frameworks(report_data.vulnerabilities),
                        "requirements": self._identify_compliance_requirements(report_data.vulnerabilities)
                    },
                    "statistics": {
                        "tools_effectiveness": self._analyze_tool_effectiveness(report_data.vulnerabilities),
                        "target_analysis": self._analyze_target_security(report_data.vulnerabilities),
                        "temporal_analysis": self._analyze_discovery_timeline(report_data.vulnerabilities),
                        "severity_trends": self._analyze_severity_trends(report_data.vulnerabilities)
                    }
                }
            }
            
            # Write JSON with proper formatting
            output_file = Path(output_path)
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(json_report, f, indent=2, ensure_ascii=False, default=self._json_serializer)
            
            logger.info(f"JSON report exported to: {output_file}")
            return str(output_file)
            
        except Exception as e:
            logger.error(f"JSON export failed: {e}")
            raise
    
    def _format_vulnerability(self, vuln) -> Dict[str, Any]:
        """Format vulnerability for JSON export"""
        return {
            "id": vuln.id,
            "metadata": {
                "title": vuln.title,
                "severity": vuln.severity,
                "discovered_at": vuln.discovered_at.isoformat() if vuln.discovered_at else None,
                "tool_name": vuln.tool_name
            },
            "technical_details": {
                "description": vuln.description,
                "target": vuln.target,
                "port": vuln.port,
                "protocol": vuln.protocol,
                "cvss_score": vuln.cvss_score
            },
            "threat_intelligence": {
                "cve_ids": vuln.cve_ids or [],
                "references": vuln.references or [],
                "threat_level": self._assess_threat_level(vuln),
                "exploitability": self._assess_exploitability(vuln)
            },
            "remediation": {
                "recommendation": vuln.remediation,
                "priority": self._get_remediation_priority(vuln.severity),
                "estimated_effort": self._estimate_remediation_effort(vuln),
                "business_impact": self._assess_business_impact(vuln)
            }
        }
    
    def _calculate_risk_score(self, vulnerabilities) -> float:
        """Calculate overall risk score based on vulnerabilities"""
        if not vulnerabilities:
            return 0.0
        
        severity_weights = {'critical': 10, 'high': 7, 'medium': 4, 'low': 2, 'info': 1}
        total_score = sum(severity_weights.get(v.severity.lower(), 1) for v in vulnerabilities)
        max_possible = len(vulnerabilities) * 10
        
        return round((total_score / max_possible) * 100, 2) if max_possible > 0 else 0.0
    
    def _assess_compliance_status(self, vulnerabilities) -> str:
        """Assess overall compliance status"""
        critical_count = sum(1 for v in vulnerabilities if v.severity.lower() == 'critical')
        high_count = sum(1 for v in vulnerabilities if v.severity.lower() == 'high')
        
        if critical_count > 0:
            return "NON_COMPLIANT"
        elif high_count > 5:
            return "NEEDS_ATTENTION"
        elif high_count > 0:
            return "MINOR_ISSUES"
        else:
            return "COMPLIANT"
    
    def _group_vulnerabilities_by_severity(self, vulnerabilities) -> Dict[str, list]:
        """Group vulnerabilities by severity level"""
        groups = {'critical': [], 'high': [], 'medium': [], 'low': [], 'info': []}
        for vuln in vulnerabilities:
            severity = vuln.severity.lower()
            if severity in groups:
                groups[severity].append(vuln.id)
        return groups
    
    def _group_vulnerabilities_by_target(self, vulnerabilities) -> Dict[str, list]:
        """Group vulnerabilities by target"""
        groups = {}
        for vuln in vulnerabilities:
            target = vuln.target
            if target not in groups:
                groups[target] = []
            groups[target].append(vuln.id)
        return groups
    
    def _group_vulnerabilities_by_tool(self, vulnerabilities) -> Dict[str, list]:
        """Group vulnerabilities by discovery tool"""
        groups = {}
        for vuln in vulnerabilities:
            tool = vuln.tool_name or 'unknown'
            if tool not in groups:
                groups[tool] = []
            groups[tool].append(vuln.id)
        return groups
    
    def _assess_compliance_frameworks(self, vulnerabilities) -> list:
        """Assess relevant compliance frameworks"""
        frameworks = []
        
        # Basic framework assessment based on vulnerability types
        if any(v.cve_ids for v in vulnerabilities):
            frameworks.append("ISO 27001")
            frameworks.append("NIST Cybersecurity Framework")
        
        if any("injection" in v.title.lower() or "xss" in v.title.lower() for v in vulnerabilities):
            frameworks.append("OWASP Top 10")
            frameworks.append("PCI DSS")
        
        if any(v.severity.lower() in ['critical', 'high'] for v in vulnerabilities):
            frameworks.append("SOC 2 Type II")
        
        return list(set(frameworks))
    
    def _identify_compliance_requirements(self, vulnerabilities) -> list:
        """Identify specific compliance requirements affected"""
        requirements = []
        
        critical_high = sum(1 for v in vulnerabilities if v.severity.lower() in ['critical', 'high'])
        if critical_high > 0:
            requirements.append(f"Address {critical_high} high/critical vulnerabilities for compliance")
        
        if any("encryption" in v.description.lower() for v in vulnerabilities):
            requirements.append("Implement proper encryption controls")
        
        if any("authentication" in v.description.lower() for v in vulnerabilities):
            requirements.append("Strengthen authentication mechanisms")
        
        return requirements
    
    def _analyze_tool_effectiveness(self, vulnerabilities) -> Dict[str, Any]:
        """Analyze effectiveness of different scanning tools"""
        tool_stats = {}
        for vuln in vulnerabilities:
            tool = vuln.tool_name or 'unknown'
            if tool not in tool_stats:
                tool_stats[tool] = {'total': 0, 'critical': 0, 'high': 0, 'medium': 0, 'low': 0, 'info': 0}
            tool_stats[tool]['total'] += 1
            tool_stats[tool][vuln.severity.lower()] += 1
        
        return tool_stats
    
    def _analyze_target_security(self, vulnerabilities) -> Dict[str, Any]:
        """Analyze security posture by target"""
        target_stats = {}
        for vuln in vulnerabilities:
            target = vuln.target
            if target not in target_stats:
                target_stats[target] = {'total': 0, 'risk_score': 0}
            target_stats[target]['total'] += 1
            # Simple risk scoring
            severity_score = {'critical': 10, 'high': 7, 'medium': 4, 'low': 2, 'info': 1}
            target_stats[target]['risk_score'] += severity_score.get(vuln.severity.lower(), 1)
        
        return target_stats
    
    def _analyze_discovery_timeline(self, vulnerabilities) -> Dict[str, Any]:
        """Analyze when vulnerabilities were discovered"""
        if not vulnerabilities:
            return {}
        
        discoveries = [v.discovered_at for v in vulnerabilities if v.discovered_at]
        if not discoveries:
            return {}
        
        return {
            "earliest_discovery": min(discoveries).isoformat(),
            "latest_discovery": max(discoveries).isoformat(),
            "total_discovery_period_hours": (max(discoveries) - min(discoveries)).total_seconds() / 3600
        }
    
    def _analyze_severity_trends(self, vulnerabilities) -> Dict[str, Any]:
        """Analyze severity distribution trends"""
        total = len(vulnerabilities)
        if total == 0:
            return {}
        
        severity_counts = {'critical': 0, 'high': 0, 'medium': 0, 'low': 0, 'info': 0}
        for vuln in vulnerabilities:
            severity_counts[vuln.severity.lower()] += 1
        
        return {
            "distribution_percentages": {
                severity: round((count / total) * 100, 2) 
                for severity, count in severity_counts.items()
            },
            "risk_concentration": {
                "critical_high_percentage": round(((severity_counts['critical'] + severity_counts['high']) / total) * 100, 2),
                "actionable_findings": severity_counts['critical'] + severity_counts['high'] + severity_counts['medium']
            }
        }
    
    def _assess_threat_level(self, vuln) -> str:
        """Assess threat level for individual vulnerability"""
        if vuln.severity.lower() == 'critical':
            return "IMMEDIATE"
        elif vuln.severity.lower() == 'high':
            return "HIGH"
        elif vuln.severity.lower() == 'medium':
            return "MODERATE"
        else:
            return "LOW"
    
    def _assess_exploitability(self, vuln) -> str:
        """Assess exploitability of vulnerability"""
        if vuln.cvss_score and vuln.cvss_score >= 9.0:
            return "CRITICAL"
        elif vuln.cvss_score and vuln.cvss_score >= 7.0:
            return "HIGH"
        elif vuln.cvss_score and vuln.cvss_score >= 4.0:
            return "MEDIUM"
        else:
            return "LOW"
    
    def _get_remediation_priority(self, severity: str) -> str:
        """Get remediation priority based on severity"""
        priority_map = {
            'critical': 'IMMEDIATE',
            'high': 'HIGH',
            'medium': 'MEDIUM',
            'low': 'LOW',
            'info': 'INFORMATIONAL'
        }
        return priority_map.get(severity.lower(), 'MEDIUM')
    
    def _estimate_remediation_effort(self, vuln) -> str:
        """Estimate effort required for remediation"""
        if "patch" in (vuln.remediation or "").lower():
            return "LOW"
        elif "configuration" in (vuln.remediation or "").lower():
            return "MEDIUM"
        elif "redesign" in (vuln.remediation or "").lower():
            return "HIGH"
        else:
            return "MEDIUM"
    
    def _assess_business_impact(self, vuln) -> str:
        """Assess potential business impact"""
        if vuln.severity.lower() in ['critical', 'high']:
            return "HIGH"
        elif vuln.severity.lower() == 'medium':
            return "MEDIUM"
        else:
            return "LOW"
    
    def _json_serializer(self, obj):
        """JSON serializer for datetime objects"""
        if isinstance(obj, datetime):
            return obj.isoformat()
        raise TypeError(f"Object of type {type(obj)} is not JSON serializable")