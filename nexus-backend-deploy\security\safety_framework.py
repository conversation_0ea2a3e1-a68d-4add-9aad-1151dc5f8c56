#!/usr/bin/env python3
"""
Smart Safety Framework for NexusScan Desktop
Provides comprehensive risk assessment and safety measures for advanced penetration testing operations.
"""

import asyncio
import logging
import json
import re
import hashlib
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Optional, Any, Tuple, Union
from dataclasses import dataclass, asdict
from enum import Enum
from pathlib import Path
import ipaddress
import socket

from core.config import Config
from ai.ai_service import AIServiceManager, AnalysisRequest

logger = logging.getLogger(__name__)


class RiskLevel(Enum):
    """Risk levels for operations"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"
    EXTREME = "extreme"


class AttackCategory(Enum):
    """Categories of attack operations"""
    RECONNAISSANCE = "reconnaissance"
    VULNERABILITY_SCANNING = "vulnerability_scanning"
    EXPLOITATION = "exploitation"
    POST_EXPLOITATION = "post_exploitation"
    PERSISTENCE = "persistence"
    LATERAL_MOVEMENT = "lateral_movement"
    DATA_EXFILTRATION = "data_exfiltration"
    DENIAL_OF_SERVICE = "denial_of_service"
    SYSTEM_MODIFICATION = "system_modification"


class LegalJurisdiction(Enum):
    """Legal jurisdictions for compliance"""
    US_FEDERAL = "us_federal"
    EU_GDPR = "eu_gdpr"
    UK_DATA_PROTECTION = "uk_data_protection"
    CANADA_PIPEDA = "canada_pipeda"
    AUSTRALIA_PRIVACY = "australia_privacy"
    GENERIC_INTERNATIONAL = "generic_international"


@dataclass
class SafetyAssessment:
    """Comprehensive safety assessment for operations"""
    command: str
    target_info: Dict[str, Any]
    risk_level: RiskLevel
    attack_category: AttackCategory
    safety_score: float  # 0.0 (very dangerous) to 1.0 (very safe)
    legal_implications: List[str]
    technical_risks: List[str]
    business_risks: List[str]
    mitigation_strategies: List[str]
    required_authorizations: List[str]
    estimated_impact: str
    reversibility: bool
    stealth_level: str  # noisy, moderate, stealthy
    detection_probability: float  # 0.0 (undetectable) to 1.0 (certain detection)
    assessment_timestamp: datetime
    ai_confidence: float = 0.0
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization"""
        return asdict(self)


@dataclass
class LegalDisclaimer:
    """Legal disclaimer for operations"""
    jurisdiction: LegalJurisdiction
    operation_type: str
    target_authorization: bool
    disclaimer_text: str
    acknowledgment_required: List[str]
    legal_contact_info: Dict[str, str]
    compliance_requirements: List[str]
    liability_limitations: List[str]
    generated_timestamp: datetime


@dataclass
class AuthorizationRequest:
    """Request for user authorization"""
    operation_id: str
    user_id: str
    safety_assessment: SafetyAssessment
    legal_disclaimer: LegalDisclaimer
    ai_recommendations: Dict[str, Any]
    required_confirmations: List[str]
    emergency_contacts: List[str]
    max_authorization_time: timedelta
    created_timestamp: datetime


class SmartSafetyFramework:
    """Intelligent safety framework for penetration testing operations"""

    def __init__(self, config: Config, ai_service: Optional[AIServiceManager] = None):
        """Initialize Smart Safety Framework"""
        self.config = config
        self.ai_service = ai_service
        
        # Safety configuration
        self.dangerous_commands = self._load_dangerous_commands()
        self.safe_target_patterns = self._load_safe_target_patterns()
        self.legal_jurisdictions = self._initialize_legal_jurisdictions()
        
        # Risk assessment cache
        self.assessment_cache: Dict[str, SafetyAssessment] = {}
        self.cache_ttl = timedelta(hours=1)
        
        logger.info("Smart Safety Framework initialized")

    def _load_dangerous_commands(self) -> Dict[str, Dict[str, Any]]:
        """Load dangerous command patterns and their risk assessments"""
        return {
            # Metasploit exploits
            "msfconsole": {
                "base_risk": RiskLevel.HIGH,
                "category": AttackCategory.EXPLOITATION,
                "patterns": [
                    r"exploit/.*",
                    r"use exploit",
                    r"payload/.*"
                ]
            },
            
            # System modification commands
            "rm -rf": {
                "base_risk": RiskLevel.CRITICAL,
                "category": AttackCategory.SYSTEM_MODIFICATION,
                "patterns": [r"rm\s+-rf\s+/", r"del\s+/s\s+/q"]
            },
            
            # Network attacks
            "nmap": {
                "base_risk": RiskLevel.MEDIUM,
                "category": AttackCategory.RECONNAISSANCE,
                "patterns": [
                    r"nmap.*-sS",  # SYN scan
                    r"nmap.*-O",   # OS detection
                    r"nmap.*-A"    # Aggressive scan
                ]
            },
            
            # SQL injection
            "sqlmap": {
                "base_risk": RiskLevel.HIGH,
                "category": AttackCategory.EXPLOITATION,
                "patterns": [
                    r"sqlmap.*--dump",
                    r"sqlmap.*--os-shell",
                    r"sqlmap.*--sql-shell"
                ]
            },
            
            # Web application attacks
            "hydra": {
                "base_risk": RiskLevel.HIGH,
                "category": AttackCategory.EXPLOITATION,
                "patterns": [
                    r"hydra.*ssh",
                    r"hydra.*ftp",
                    r"hydra.*http"
                ]
            },
            
            # Post-exploitation
            "meterpreter": {
                "base_risk": RiskLevel.CRITICAL,
                "category": AttackCategory.POST_EXPLOITATION,
                "patterns": [
                    r"meterpreter.*upload",
                    r"meterpreter.*download",
                    r"meterpreter.*shell"
                ]
            }
        }

    def _load_safe_target_patterns(self) -> List[str]:
        """Load patterns for safe testing targets"""
        return [
            r"127\.0\.0\.1",
            r"localhost",
            r".*\.test$",
            r".*\.local$", 
            r"testfire\.net",
            r"demo\.testfire\.net",
            r"vulnhub\.com",
            r"tryhackme\.com",
            r"hackthebox\.eu",
            r"10\.0\.0\..*",  # Private networks
            r"192\.168\..*",
            r"172\.(1[6-9]|2[0-9]|3[01])\..*"
        ]

    def _initialize_legal_jurisdictions(self) -> Dict[LegalJurisdiction, Dict[str, Any]]:
        """Initialize legal jurisdiction configurations"""
        return {
            LegalJurisdiction.US_FEDERAL: {
                "name": "United States Federal",
                "key_laws": ["Computer Fraud and Abuse Act (CFAA)", "Digital Millennium Copyright Act (DMCA)"],
                "authorization_requirements": ["Written consent from target system owner"],
                "liability_concerns": ["Unauthorized access penalties", "Data breach notification requirements"]
            },
            
            LegalJurisdiction.EU_GDPR: {
                "name": "European Union GDPR",
                "key_laws": ["General Data Protection Regulation (GDPR)", "Network and Information Security Directive"],
                "authorization_requirements": ["Data controller consent", "Data subject notification"],
                "liability_concerns": ["GDPR compliance", "Personal data protection", "Right to be forgotten"]
            },
            
            LegalJurisdiction.GENERIC_INTERNATIONAL: {
                "name": "Generic International",
                "key_laws": ["Local cybersecurity regulations", "International cybercrime conventions"],
                "authorization_requirements": ["Target system owner authorization", "Local legal compliance"],
                "liability_concerns": ["Cross-border data transfer", "Local cybercrime laws"]
            }
        }

    async def assess_command_risk(self, command: str, target_info: Dict[str, Any]) -> SafetyAssessment:
        """Perform comprehensive risk assessment for a command"""
        
        # Check cache first
        cache_key = hashlib.md5(f"{command}:{json.dumps(target_info, sort_keys=True)}".encode()).hexdigest()
        if cache_key in self.assessment_cache:
            cached_assessment = self.assessment_cache[cache_key]
            if datetime.now() - cached_assessment.assessment_timestamp < self.cache_ttl:
                return cached_assessment

        try:
            # Base risk assessment
            base_risk = self._assess_base_risk(command)
            attack_category = self._classify_attack_category(command)
            
            # Target safety assessment
            target_safety = self._assess_target_safety(target_info)
            
            # Command analysis
            technical_risks = self._analyze_technical_risks(command, target_info)
            business_risks = self._analyze_business_risks(command, target_info, attack_category)
            legal_implications = self._analyze_legal_implications(command, target_info)
            
            # Calculate safety score
            safety_score = self._calculate_safety_score(
                base_risk, target_safety, technical_risks, business_risks
            )
            
            # Determine final risk level
            final_risk_level = self._determine_final_risk_level(base_risk, safety_score)
            
            # Generate mitigation strategies
            mitigation_strategies = self._generate_mitigation_strategies(
                command, target_info, final_risk_level, attack_category
            )
            
            # Determine required authorizations
            required_authorizations = self._determine_required_authorizations(
                final_risk_level, attack_category, legal_implications
            )
            
            # Estimate impact and reversibility
            estimated_impact = self._estimate_impact(command, target_info, final_risk_level)
            reversibility = self._assess_reversibility(command, attack_category)
            
            # Stealth and detection analysis
            stealth_level, detection_probability = self._analyze_stealth_detection(command)
            
            # AI enhancement if available
            ai_confidence = 0.0
            if self.ai_service:
                ai_analysis = await self._get_ai_safety_analysis(command, target_info)
                ai_confidence = ai_analysis.get("confidence", 0.0)
                
                # Enhance assessment with AI insights
                if ai_analysis.get("success", False):
                    technical_risks.extend(ai_analysis.get("additional_risks", []))
                    mitigation_strategies.extend(ai_analysis.get("ai_mitigations", []))

            assessment = SafetyAssessment(
                command=command,
                target_info=target_info,
                risk_level=final_risk_level,
                attack_category=attack_category,
                safety_score=safety_score,
                legal_implications=legal_implications,
                technical_risks=technical_risks,
                business_risks=business_risks,
                mitigation_strategies=mitigation_strategies,
                required_authorizations=required_authorizations,
                estimated_impact=estimated_impact,
                reversibility=reversibility,
                stealth_level=stealth_level,
                detection_probability=detection_probability,
                assessment_timestamp=datetime.now(),
                ai_confidence=ai_confidence
            )
            
            # Cache assessment
            self.assessment_cache[cache_key] = assessment
            
            return assessment
            
        except Exception as e:
            logger.error(f"Risk assessment failed: {e}")
            # Return conservative assessment on failure
            return SafetyAssessment(
                command=command,
                target_info=target_info,
                risk_level=RiskLevel.CRITICAL,
                attack_category=AttackCategory.SYSTEM_MODIFICATION,
                safety_score=0.0,
                legal_implications=["Unable to assess legal implications"],
                technical_risks=["Risk assessment failed"],
                business_risks=["Unknown business impact"],
                mitigation_strategies=["Manual review required"],
                required_authorizations=["Full manual authorization required"],
                estimated_impact="Unknown - potentially severe",
                reversibility=False,
                stealth_level="unknown",
                detection_probability=1.0,
                assessment_timestamp=datetime.now()
            )

    def _assess_base_risk(self, command: str) -> RiskLevel:
        """Assess base risk level of command"""
        command_lower = command.lower()
        
        # Check against dangerous command patterns
        for cmd, info in self.dangerous_commands.items():
            if cmd in command_lower:
                for pattern in info["patterns"]:
                    if re.search(pattern, command_lower):
                        return info["base_risk"]
        
        # Default classification based on keywords
        if any(keyword in command_lower for keyword in ["exploit", "shell", "payload", "attack"]):
            return RiskLevel.HIGH
        elif any(keyword in command_lower for keyword in ["scan", "enum", "discover"]):
            return RiskLevel.MEDIUM
        elif any(keyword in command_lower for keyword in ["info", "help", "version"]):
            return RiskLevel.LOW
        
        return RiskLevel.MEDIUM  # Default

    def _classify_attack_category(self, command: str) -> AttackCategory:
        """Classify the attack category of command"""
        command_lower = command.lower()
        
        if any(keyword in command_lower for keyword in ["nmap", "scan", "enum", "discover"]):
            return AttackCategory.RECONNAISSANCE
        elif any(keyword in command_lower for keyword in ["exploit", "payload", "shell"]):
            return AttackCategory.EXPLOITATION
        elif any(keyword in command_lower for keyword in ["meterpreter", "session", "upload"]):
            return AttackCategory.POST_EXPLOITATION
        elif any(keyword in command_lower for keyword in ["persistence", "startup", "service"]):
            return AttackCategory.PERSISTENCE
        elif any(keyword in command_lower for keyword in ["lateral", "pivot", "route"]):
            return AttackCategory.LATERAL_MOVEMENT
        elif any(keyword in command_lower for keyword in ["download", "exfil", "copy"]):
            return AttackCategory.DATA_EXFILTRATION
        elif any(keyword in command_lower for keyword in ["dos", "flood", "overload"]):
            return AttackCategory.DENIAL_OF_SERVICE
        
        return AttackCategory.VULNERABILITY_SCANNING  # Default

    def _assess_target_safety(self, target_info: Dict[str, Any]) -> float:
        """Assess safety of target (1.0 = very safe, 0.0 = very dangerous)"""
        target_host = target_info.get("host", "")
        
        # Check against safe target patterns
        for pattern in self.safe_target_patterns:
            if re.match(pattern, target_host):
                return 0.9  # Very safe
        
        # Check if it's a private network
        try:
            ip = ipaddress.ip_address(target_host)
            if ip.is_private:
                return 0.7  # Moderately safe
            elif ip.is_loopback:
                return 0.95  # Very safe
        except ValueError:
            pass  # Not an IP address
        
        # Check for test/demo domains
        if any(keyword in target_host.lower() for keyword in ["test", "demo", "lab", "sandbox"]):
            return 0.8  # Safe
        
        # Production systems are risky
        if any(keyword in target_host.lower() for keyword in ["prod", "live", "www", "mail"]):
            return 0.2  # Dangerous
        
        return 0.5  # Unknown safety

    def _analyze_technical_risks(self, command: str, target_info: Dict[str, Any]) -> List[str]:
        """Analyze technical risks of the operation"""
        risks = []
        command_lower = command.lower()
        
        if "exploit" in command_lower:
            risks.append("System compromise and unauthorized access")
            risks.append("Potential system instability or crashes")
            
        if "payload" in command_lower:
            risks.append("Execution of arbitrary code on target system")
            risks.append("Potential antivirus detection and alerting")
            
        if "shell" in command_lower:
            risks.append("Interactive access to target system")
            risks.append("Potential for lateral movement")
            
        if "dos" in command_lower or "flood" in command_lower:
            risks.append("Service disruption and availability impact")
            risks.append("Network congestion and performance degradation")
            
        if "upload" in command_lower:
            risks.append("File system modification")
            risks.append("Potential malware installation")
            
        # Network-based risks
        if target_info.get("host"):
            risks.append("Network traffic generation and logging")
            risks.append("Firewall and IDS/IPS detection")
        
        return risks

    def _analyze_business_risks(self, command: str, target_info: Dict[str, Any], 
                              category: AttackCategory) -> List[str]:
        """Analyze business risks of the operation"""
        risks = []
        
        if category in [AttackCategory.EXPLOITATION, AttackCategory.POST_EXPLOITATION]:
            risks.append("Regulatory compliance violations")
            risks.append("Customer data exposure risk")
            risks.append("Business reputation damage")
            risks.append("Legal liability and lawsuits")
            
        if category == AttackCategory.DENIAL_OF_SERVICE:
            risks.append("Service availability impact")
            risks.append("Revenue loss from downtime")
            risks.append("Customer service disruption")
            
        if category == AttackCategory.DATA_EXFILTRATION:
            risks.append("Intellectual property theft")
            risks.append("Privacy law violations")
            risks.append("Competitive disadvantage")
            
        # Industry-specific risks
        target_info_str = str(target_info).lower()
        if any(keyword in target_info_str for keyword in ["healthcare", "medical", "hospital"]):
            risks.append("HIPAA compliance violations")
            risks.append("Patient safety and privacy risks")
            
        if any(keyword in target_info_str for keyword in ["financial", "bank", "payment"]):
            risks.append("PCI DSS compliance violations")
            risks.append("Financial data exposure")
            
        return risks

    def _analyze_legal_implications(self, command: str, target_info: Dict[str, Any]) -> List[str]:
        """Analyze legal implications of the operation"""
        implications = []
        
        # Unauthorized access laws
        if any(keyword in command.lower() for keyword in ["exploit", "shell", "access"]):
            implications.append("Computer Fraud and Abuse Act (CFAA) violations")
            implications.append("Unauthorized access to computer systems")
            implications.append("Potential criminal charges")
            
        # Data protection laws
        if any(keyword in command.lower() for keyword in ["download", "exfil", "dump"]):
            implications.append("Data protection law violations")
            implications.append("Privacy regulation non-compliance")
            implications.append("Data breach notification requirements")
            
        # International considerations
        target_host = target_info.get("host", "")
        if not self._is_domestic_target(target_host):
            implications.append("Cross-border cybercrime laws")
            implications.append("International legal jurisdiction issues")
            implications.append("Diplomatic and political complications")
            
        return implications

    def _is_domestic_target(self, target_host: str) -> bool:
        """Check if target appears to be domestic (simplified heuristic)"""
        # This is a simplified check - in reality would need geolocation
        return any(domain in target_host.lower() for domain in [".local", ".test", "localhost", "127.0.0.1"])

    def _calculate_safety_score(self, base_risk: RiskLevel, target_safety: float,
                              technical_risks: List[str], business_risks: List[str]) -> float:
        """Calculate overall safety score (0.0 = dangerous, 1.0 = safe)"""
        
        # Base risk scoring
        risk_scores = {
            RiskLevel.LOW: 0.8,
            RiskLevel.MEDIUM: 0.6,
            RiskLevel.HIGH: 0.3,
            RiskLevel.CRITICAL: 0.1,
            RiskLevel.EXTREME: 0.0
        }
        
        base_score = risk_scores[base_risk]
        
        # Adjust for target safety
        target_adjusted = base_score * target_safety
        
        # Penalty for risks
        risk_penalty = min(0.8, (len(technical_risks) + len(business_risks)) * 0.1)
        
        final_score = max(0.0, target_adjusted - risk_penalty)
        
        return round(final_score, 2)

    def _determine_final_risk_level(self, base_risk: RiskLevel, safety_score: float) -> RiskLevel:
        """Determine final risk level based on all factors"""
        
        if safety_score >= 0.8:
            return RiskLevel.LOW
        elif safety_score >= 0.6:
            return RiskLevel.MEDIUM
        elif safety_score >= 0.3:
            return RiskLevel.HIGH
        elif safety_score >= 0.1:
            return RiskLevel.CRITICAL
        else:
            return RiskLevel.EXTREME

    def _generate_mitigation_strategies(self, command: str, target_info: Dict[str, Any],
                                      risk_level: RiskLevel, category: AttackCategory) -> List[str]:
        """Generate mitigation strategies for the operation"""
        strategies = []
        
        # Universal strategies
        strategies.append("Obtain explicit written authorization from target system owner")
        strategies.append("Implement comprehensive logging and monitoring")
        strategies.append("Prepare incident response and rollback procedures")
        
        # Risk-level specific strategies
        if risk_level in [RiskLevel.HIGH, RiskLevel.CRITICAL, RiskLevel.EXTREME]:
            strategies.append("Implement additional approval workflows")
            strategies.append("Notify legal and compliance teams")
            strategies.append("Prepare emergency stop procedures")
            strategies.append("Schedule operation during maintenance windows")
            
        # Category-specific strategies
        if category == AttackCategory.EXPLOITATION:
            strategies.append("Use minimal viable payloads")
            strategies.append("Implement automatic cleanup procedures")
            strategies.append("Monitor for defensive responses")
            
        if category == AttackCategory.DATA_EXFILTRATION:
            strategies.append("Use encrypted data transfer channels")
            strategies.append("Limit data collection to necessary samples")
            strategies.append("Implement secure data destruction")
            
        return strategies

    def _determine_required_authorizations(self, risk_level: RiskLevel, 
                                         category: AttackCategory, legal_implications: List[str]) -> List[str]:
        """Determine required authorization levels"""
        authorizations = []
        
        # Base authorizations
        authorizations.append("Target system owner authorization")
        
        if risk_level in [RiskLevel.MEDIUM, RiskLevel.HIGH]:
            authorizations.append("Security team lead approval")
            
        if risk_level in [RiskLevel.HIGH, RiskLevel.CRITICAL, RiskLevel.EXTREME]:
            authorizations.append("Executive sponsor approval")
            authorizations.append("Legal team review")
            
        if risk_level == RiskLevel.EXTREME:
            authorizations.append("Board-level approval")
            authorizations.append("External legal counsel review")
            
        # Category-specific authorizations
        if category in [AttackCategory.EXPLOITATION, AttackCategory.POST_EXPLOITATION]:
            authorizations.append("Penetration testing authorization")
            
        if legal_implications:
            authorizations.append("Compliance officer approval")
            
        return authorizations

    def _estimate_impact(self, command: str, target_info: Dict[str, Any], risk_level: RiskLevel) -> str:
        """Estimate potential impact of operation"""
        
        if risk_level == RiskLevel.LOW:
            return "Minimal impact - information gathering only"
        elif risk_level == RiskLevel.MEDIUM:
            return "Limited impact - service discovery and enumeration"
        elif risk_level == RiskLevel.HIGH:
            return "Significant impact - potential system compromise"
        elif risk_level == RiskLevel.CRITICAL:
            return "Critical impact - system control and data access"
        else:  # EXTREME
            return "Extreme impact - complete system compromise and potential data destruction"

    def _assess_reversibility(self, command: str, category: AttackCategory) -> bool:
        """Assess if operation effects are reversible"""
        
        # Non-reversible categories
        if category in [AttackCategory.DATA_EXFILTRATION, AttackCategory.SYSTEM_MODIFICATION]:
            return False
            
        # Check for destructive commands
        if any(keyword in command.lower() for keyword in ["delete", "remove", "destroy", "wipe"]):
            return False
            
        # Most reconnaissance and scanning is reversible
        if category in [AttackCategory.RECONNAISSANCE, AttackCategory.VULNERABILITY_SCANNING]:
            return True
            
        return True  # Default to reversible

    def _analyze_stealth_detection(self, command: str) -> Tuple[str, float]:
        """Analyze stealth level and detection probability"""
        
        command_lower = command.lower()
        
        # Noisy operations
        if any(keyword in command_lower for keyword in ["dos", "flood", "scan -A", "exploit"]):
            return "noisy", 0.9
            
        # Moderate stealth
        if any(keyword in command_lower for keyword in ["scan", "enum", "probe"]):
            return "moderate", 0.6
            
        # Stealthy operations
        if any(keyword in command_lower for keyword in ["passive", "quiet", "-sS"]):
            return "stealthy", 0.3
            
        return "moderate", 0.5  # Default

    async def _get_ai_safety_analysis(self, command: str, target_info: Dict[str, Any]) -> Dict[str, Any]:
        """Get AI-powered safety analysis"""
        try:
            if not self.ai_service:
                return {"success": False, "error": "AI service not available"}
                
            analysis_request = AnalysisRequest(
                scan_results={
                    "command": command,
                    "target_info": target_info,
                    "analysis_type": "safety_assessment"
                },
                analysis_type="safety_assessment",
                include_remediation=True,
                include_risk_scoring=True,
                custom_context={"safety_framework": True}
            )
            
            response = await self.ai_service.analyze_vulnerabilities(analysis_request)
            
            if response.success:
                return {
                    "success": True,
                    "confidence": response.execution_time,  # Placeholder
                    "additional_risks": response.recommendations[:3],  # Repurpose recommendations as risks
                    "ai_mitigations": response.recommendations[3:] if len(response.recommendations) > 3 else []
                }
            else:
                return {"success": False, "error": "AI analysis failed"}
                
        except Exception as e:
            logger.error(f"AI safety analysis failed: {e}")
            return {"success": False, "error": str(e)}

    async def generate_safety_recommendations(self, planned_attack: Dict[str, Any]) -> List[str]:
        """Generate AI-powered safety recommendations for planned attack"""
        recommendations = []
        
        try:
            attack_type = planned_attack.get("type", "unknown")
            target_info = planned_attack.get("target_info", {})
            
            # Base recommendations
            recommendations.extend([
                "Verify explicit authorization documentation is current and comprehensive",
                "Implement real-time monitoring with automated alerting",
                "Prepare detailed rollback and cleanup procedures",
                "Establish communication protocols with target administrators"
            ])
            
            # Attack-specific recommendations
            if "exploit" in attack_type.lower():
                recommendations.extend([
                    "Test exploits in isolated environment first",
                    "Use least-privilege payload configurations",
                    "Implement session timeout mechanisms",
                    "Monitor for defensive countermeasures"
                ])
                
            if "network" in str(target_info).lower():
                recommendations.extend([
                    "Coordinate with network operations center",
                    "Implement traffic rate limiting",
                    "Monitor for network congestion",
                    "Prepare alternative communication channels"
                ])
                
            return recommendations
            
        except Exception as e:
            logger.error(f"Safety recommendations generation failed: {e}")
            return ["Manual safety review required due to analysis failure"]

    async def create_legal_disclaimer(self, attack_type: str, target: Dict[str, Any], 
                                    jurisdiction: LegalJurisdiction = LegalJurisdiction.GENERIC_INTERNATIONAL) -> LegalDisclaimer:
        """Create dynamic legal disclaimer based on attack context"""
        
        jurisdiction_info = self.legal_jurisdictions[jurisdiction]
        
        disclaimer_text = f"""
PENETRATION TESTING LEGAL DISCLAIMER AND AUTHORIZATION

Target System: {target.get('host', 'Not specified')}
Operation Type: {attack_type}
Jurisdiction: {jurisdiction_info['name']}
Generated: {datetime.now().isoformat()}

1. AUTHORIZATION REQUIREMENT
This penetration testing operation is conducted under explicit written authorization 
from the target system owner. Unauthorized access to computer systems is prohibited 
under applicable laws including: {', '.join(jurisdiction_info['key_laws'])}.

2. SCOPE LIMITATION
Testing activities are limited to the specifically authorized target systems and 
methods described in the signed authorization agreement.

3. LIABILITY LIMITATION
The penetration testing team operates under professional liability insurance and 
follows industry-standard methodologies to minimize risk.

4. DATA PROTECTION
All data accessed during testing will be handled according to applicable data 
protection regulations and will be securely destroyed upon completion.

5. INCIDENT RESPONSE
Emergency contact procedures are established for immediate cessation of activities 
if required by the target system owner.

By proceeding, the operator acknowledges understanding of legal requirements and 
confirms valid authorization is in place.
"""
        
        return LegalDisclaimer(
            jurisdiction=jurisdiction,
            operation_type=attack_type,
            target_authorization=True,  # Assumed for disclaimer generation
            disclaimer_text=disclaimer_text,
            acknowledgment_required=[
                "I have explicit written authorization to test the target system",
                "I understand the legal implications of unauthorized access",
                "I will immediately cease activities if requested by system owner",
                "I will protect any data accessed during testing"
            ],
            legal_contact_info={
                "emergency_contact": "<EMAIL>",
                "legal_counsel": "<EMAIL>"
            },
            compliance_requirements=jurisdiction_info["authorization_requirements"],
            liability_limitations=jurisdiction_info["liability_concerns"],
            generated_timestamp=datetime.now()
        )

    def clear_assessment_cache(self):
        """Clear the assessment cache"""
        self.assessment_cache.clear()
        logger.info("Safety assessment cache cleared")

    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics"""
        return {
            "cached_assessments": len(self.assessment_cache),
            "cache_ttl_hours": self.cache_ttl.total_seconds() / 3600
        }