import { app, BrowserWindow, Menu, shell, ipcMain, dialog, session, Notification } from 'electron';
import { autoUpdater } from 'electron-updater';
import windowStateKeeper from 'electron-window-state';
import contextMenu from 'electron-context-menu';
import log from 'electron-log';
import Store from 'electron-store';
import * as path from 'path';
import * as fs from 'fs-extra';
import { WindowManager } from './window-manager';
import { BackendManager } from './backend-manager';
import { MenuBuilder } from './menu';

// Configure logging
log.transports.file.level = 'info';
log.transports.console.level = 'debug';

// Configure auto-updater
autoUpdater.logger = log;

class NexusScanApp {
  private mainWindow: BrowserWindow | null = null;
  private windowManager: WindowManager;
  private backendManager: BackendManager;
  private store: Store<any>;
  private isDevelopment: boolean;

  constructor() {
    log.info('🚀 NexusScanApp constructor starting...');
    
    this.isDevelopment = process.env.NODE_ENV === 'development';
    log.info('📍 Environment setup complete');
    
    this.store = new Store({
      defaults: {
        windowBounds: { width: 1400, height: 900 },
        backend: {
          url: 'http://************:8090',
          autoConnect: true,
          timeout: 30000
        },
        ui: {
          theme: 'dark',
          showTerminal: true,
          showSidebar: true
        }
      }
    });
    log.info('📍 Store initialized');

    this.windowManager = new WindowManager();
    log.info('📍 WindowManager created');
    
    this.backendManager = new BackendManager();
    log.info('📍 BackendManager created');
    
    // Remote backend mode - WSL manager not needed
    log.info('📍 Remote backend mode - WSL not required');

    log.info('📍 Starting app initialization...');
    this.initializeApp();
  }

  private async initializeApp(): Promise<void> {
    log.info('📍 initializeApp() started');
    
    // ONLY do essential cache setup and app events in constructor
    // Move all complex setup to AFTER ready event fires
    
    // Fix cache permission issues on Windows
    const userData = app.getPath('userData');
    const cacheDir = path.join(userData, 'cache');
    
    try {
      await fs.ensureDir(cacheDir);
      app.setPath('userData', userData);
      log.info(`Cache directory initialized: ${cacheDir}`);
    } catch (error) {
      log.warn('Cache directory setup failed:', error);
    }
    log.info('📍 Cache setup complete');

    // ONLY register basic app event handlers - no complex security setup yet
    log.info('📍 Setting up basic app event handlers...');
    app.on('window-all-closed', this.onWindowAllClosed.bind(this));
    app.on('activate', this.onActivate.bind(this));
    app.on('before-quit', this.onBeforeQuit.bind(this));
    log.info('📍 Basic app event handlers complete');

    // Use app.whenReady() instead of ready event listener
    log.info('📍 Waiting for Electron ready event...');
    app.whenReady().then(() => {
      this.onReady().catch((error) => {
        log.error('❌ Failed to initialize app after ready:', error);
      });
    });

    log.info('✅ initializeApp() COMPLETE - ready handler set up');
  }

  private async onReady(): Promise<void> {
    log.info('🎉 ELECTRON READY EVENT FIRED - NexusScan Desktop starting...');

    // NOW setup all the complex security and IPC handlers after ready event
    log.info('📍 Setting up security handlers...');
    this.setupSecurityHandlers();
    log.info('📍 Security handlers complete');

    // Configure session security
    log.info('📍 Configuring session security...');
    await this.configureSession();
    log.info('📍 Session security complete');

    // Create main window
    log.info('📍 Creating main window...');
    await this.createMainWindow();
    log.info('📍 Main window created');

    // Setup IPC and context menu after window creation
    log.info('📍 Setting up IPC handlers...');
    this.setupIpcHandlers();
    log.info('📍 IPC handlers complete');

    // Context menu
    log.info('📍 Setting up context menu...');
    contextMenu({
      showLookUpSelection: false,
      showSearchWithGoogle: false,
      showCopyImage: false
    });
    log.info('📍 Context menu complete');

    // Setup auto-updater in production
    if (!this.isDevelopment) {
      log.info('📍 Setting up auto-updater...');
      this.setupAutoUpdater();
      log.info('📍 Auto-updater complete');
    }

    // Initialize backend connection (non-blocking)
    log.info('📍 Starting backend connection in background...');
    this.backendManager.initialize().then(() => {
      log.info('✅ Backend connection established');
      this.mainWindow?.webContents.send('backend-status-changed', { connected: true });
    }).catch((error) => {
      log.warn('⚠️ Backend connection failed:', error);
      this.mainWindow?.webContents.send('backend-status-changed', { connected: false, error: error.message });
    });

    // Initialize remote backend mode (WSL not required)
    log.info('📍 Starting remote backend mode...');
    this.initializeRemoteBackendMode();

    log.info('🎉 NexusScan Desktop UI ready! Background services initializing...');
  }

  /**
   * Remote backend mode - WSL initialization removed
   */
  private async initializeRemoteBackendMode(): Promise<void> {
    try {
      log.info('🌐 Remote backend mode - WSL not required');

      // Send remote backend status to UI
      if (this.mainWindow && !this.mainWindow.isDestroyed()) {
        this.mainWindow.webContents.send('remote-backend-status', {
          status: 'remote-mode',
          message: 'Using remote backend - WSL not required',
          backend_url: 'http://************:8090'
        });
      }
    } catch (error) {
      log.error('❌ Remote backend mode initialization failed:', error);
    }
  }

  private setupSecurityHandlers(): void {
    // Security: Prevent new window creation from renderer
    app.on('web-contents-created', (event, contents) => {
      contents.setWindowOpenHandler(({ url }) => {
        shell.openExternal(url);
        return { action: 'deny' };
      });
    });

    // Security: Prevent navigation to external URLs  
    app.on('web-contents-created', (event, contents) => {
      contents.on('will-navigate', (event, navigationUrl) => {
        const parsedUrl = new URL(navigationUrl);
        
        // Allow localhost in development
        if (this.isDevelopment && parsedUrl.hostname === 'localhost') {
          return;
        }

        // Prevent navigation to external URLs
        if (parsedUrl.origin !== this.getBaseUrl()) {
          event.preventDefault();
        }
      });
    });
  }

  private async configureSession(): Promise<void> {
    const electronSession = session.defaultSession;

    // Security headers
    electronSession.webRequest.onHeadersReceived((details: any, callback: any) => {
      callback({
        responseHeaders: {
          ...details.responseHeaders,
          'Content-Security-Policy': [
            "default-src 'self' 'unsafe-inline' data: blob:; " +
            "script-src 'self' 'unsafe-eval' 'unsafe-inline'; " +
            "connect-src 'self' ws: wss: http://************:8090 ws://************:8090; " +
            "img-src 'self' data: blob:; " +
            "style-src 'self' 'unsafe-inline'; " +
            "font-src 'self' data:;"
          ]
        }
      });
    });

    // Permissions
    electronSession.setPermissionRequestHandler((webContents: any, permission: any, callback: any) => {
      // Deny all permissions requests from renderer
      callback(false);
    });

    // Clear cache on startup in development
    if (this.isDevelopment) {
      await session.defaultSession.clearStorageData();
    }
  }

  private async createMainWindow(): Promise<void> {
    // Window state management
    const mainWindowState = windowStateKeeper({
      defaultWidth: 1400,
      defaultHeight: 900,
      maximize: false
    });

    // Create main window
    this.mainWindow = new BrowserWindow({
      ...mainWindowState,
      minWidth: 1200,
      minHeight: 800,
      show: true, // Show immediately for debugging
      icon: this.getIconPath(),
      titleBarStyle: process.platform === 'darwin' ? 'hiddenInset' : 'default',
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        sandbox: true,
        webSecurity: true,
        allowRunningInsecureContent: false,
        experimentalFeatures: false,
        preload: path.join(__dirname, '../preload/api.js')
      }
    });

    log.info('🎯 Main window created with show: true');

    // Manage window state
    mainWindowState.manage(this.mainWindow);

    // Window event handlers
    this.mainWindow.on('ready-to-show', () => {
      if (this.mainWindow) {
        log.info('🎯 Showing main window...');

        // Close DevTools if they're open
        if (this.mainWindow.webContents.isDevToolsOpened()) {
          this.mainWindow.webContents.closeDevTools();
          log.info('🔧 Closed DevTools to show main UI');
        }

        this.mainWindow.show();
        this.mainWindow.focus();
        this.mainWindow.moveTop();

        // Force window to front
        if (process.platform === 'win32') {
          this.mainWindow.setAlwaysOnTop(true);
          this.mainWindow.setAlwaysOnTop(false);
        }

        log.info('✅ Main window should now be visible');
        // DevTools can be opened manually with F12 if needed
      }
    });

    this.mainWindow.on('close', (event) => {
      log.info('🚨 Window close event triggered');
      // Prevent window from closing in development for debugging
      if (this.isDevelopment) {
        event.preventDefault();
        log.info('🚨 Window close prevented in development mode');
        return;
      }
    });

    this.mainWindow.on('closed', () => {
      log.info('🚨 Window closed event triggered');
      this.mainWindow = null;
    });

    // Security: Prevent new window creation
    this.mainWindow.webContents.setWindowOpenHandler(() => {
      return { action: 'deny' };
    });

    // Load the application
    await this.loadApp();

    // Build menu
    const menuBuilder = new MenuBuilder(this.mainWindow, this.isDevelopment);
    const menu = menuBuilder.buildMenu();
    Menu.setApplicationMenu(menu);

    // Register with window manager
    this.windowManager.registerWindow('main', this.mainWindow);
  }

  private async loadApp(): Promise<void> {
    if (!this.mainWindow) return;

    const url = this.getBaseUrl();
    
    try {
      await this.mainWindow.loadURL(url);
      log.info(`Application loaded from: ${url}`);
      
      // Force app ready event after load timeout (debug loading issues)
      setTimeout(() => {
        if (this.mainWindow && !this.mainWindow.isDestroyed()) {
          // Close DevTools if they're still open
          if (this.mainWindow.webContents.isDevToolsOpened()) {
            this.mainWindow.webContents.closeDevTools();
            log.info('🔧 Force closed DevTools after timeout');
          }

          this.mainWindow.webContents.executeJavaScript(`
            console.log('🔧 Force dispatching appReady event');
            window.dispatchEvent(new CustomEvent('appReady'));
            document.body.classList.add('app-ready');
          `);
        }
      }, 3000);
      
      // DevTools can be opened manually with F12 if needed for debugging
    } catch (error) {
      log.error('Failed to load application:', error);
      
      // Show error dialog
      dialog.showErrorBox(
        'Application Load Error',
        `Failed to load the application. Please restart NexusScan Desktop.\n\nError: ${error}`
      );
      
      app.quit();
    }
  }

  private getBaseUrl(): string {
    if (this.isDevelopment) {
      return 'http://localhost:5182';
    }
    return `file://${path.join(__dirname, '../../../dist/index.html')}`;
  }

  private getIconPath(): string {
    const iconName = process.platform === 'win32' ? 'icon.ico' : 
                     process.platform === 'darwin' ? 'icon.icns' : 'icon.png';
    return path.join(__dirname, '../../assets/icons', iconName);
  }

  private setupIpcHandlers(): void {
    // App info
    ipcMain.handle('app:getVersion', () => app.getVersion());
    ipcMain.handle('app:getName', () => app.getName());
    ipcMain.handle('app:getPlatform', () => process.platform);
    ipcMain.handle('app:getArch', () => process.arch);

    // Window management
    ipcMain.handle('window-minimize', () => {
      this.mainWindow?.minimize();
    });

    ipcMain.handle('window-maximize', () => {
      this.mainWindow?.maximize();
    });

    ipcMain.handle('window-toggle-maximize', () => {
      if (this.mainWindow?.isMaximized()) {
        this.mainWindow.unmaximize();
      } else {
        this.mainWindow?.maximize();
      }
    });

    ipcMain.handle('window-close', () => {
      this.mainWindow?.close();
    });

    ipcMain.handle('window-is-maximized', () => {
      return this.mainWindow?.isMaximized() || false;
    });

    // File system operations
    ipcMain.handle('fs:showOpenDialog', async (event, options) => {
      if (!this.mainWindow) return { canceled: true };
      
      const result = await dialog.showOpenDialog(this.mainWindow, options);
      return result;
    });

    ipcMain.handle('fs:showSaveDialog', async (event, options) => {
      if (!this.mainWindow) return { canceled: true };
      
      const result = await dialog.showSaveDialog(this.mainWindow, options);
      return result;
    });

    ipcMain.handle('fs:readFile', async (event, filePath) => {
      try {
        const content = await fs.readFile(filePath, 'utf-8');
        return { success: true, content };
      } catch (error: any) {
        return { success: false, error: error.message };
      }
    });

    ipcMain.handle('fs:writeFile', async (event, filePath, content) => {
      try {
        await fs.writeFile(filePath, content, 'utf-8');
        return { success: true };
      } catch (error: any) {
        return { success: false, error: error.message };
      }
    });

    // Store operations
    ipcMain.handle('store:get', (event, key) => {
      return this.store.get(key);
    });

    ipcMain.handle('store:set', (event, key, value) => {
      this.store.set(key, value);
    });

    ipcMain.handle('store:delete', (event, key) => {
      this.store.delete(key);
    });

    ipcMain.handle('store:clear', () => {
      this.store.clear();
    });

    // Backend operations
    ipcMain.handle('backend:getStatus', () => {
      return this.backendManager.getStatus();
    });

    ipcMain.handle('backend:connect', () => {
      return this.backendManager.connect();
    });

    ipcMain.handle('backend:disconnect', () => {
      return this.backendManager.disconnect();
    });

    ipcMain.handle('backend:getAvailableTools', () => {
      return this.backendManager.getAvailableTools();
    });

    ipcMain.handle('backend:executeRequest', (event, method, endpoint, data) => {
      return this.backendManager.executeRequest(method, endpoint, data);
    });

    ipcMain.handle('backend:executeTool', (event, toolId, config) => {
      return this.backendManager.executeTool(toolId, config);
    });

    ipcMain.handle('backend:getFerrariCapabilities', () => {
      return this.backendManager.getFerrariCapabilities();
    });

    // WSL operations removed - using remote backend exclusively
    log.info('🌐 Remote backend mode - WSL IPC handlers not needed');

    // Notifications
    ipcMain.handle('notification:show', (event, options) => {
      const notification = new Notification(options);
      notification.show();
    });

    // Shell operations
    ipcMain.handle('shell:openExternal', (event, url) => {
      return shell.openExternal(url);
    });

    ipcMain.handle('shell:showItemInFolder', (event, path) => {
      return shell.showItemInFolder(path);
    });
  }

  private setupAutoUpdater(): void {
    autoUpdater.checkForUpdatesAndNotify();

    autoUpdater.on('checking-for-update', () => {
      log.info('Checking for updates...');
    });

    autoUpdater.on('update-available', (info) => {
      log.info('Update available:', info);
    });

    autoUpdater.on('update-not-available', (info) => {
      log.info('Update not available:', info);
    });

    autoUpdater.on('error', (err) => {
      log.error('Auto-updater error:', err);
    });

    autoUpdater.on('download-progress', (progress) => {
      log.info(`Download progress: ${progress.percent}%`);
    });

    autoUpdater.on('update-downloaded', (info) => {
      log.info('Update downloaded:', info);
      
      dialog.showMessageBox(this.mainWindow!, {
        type: 'info',
        title: 'Update Ready',
        message: 'Update downloaded and ready to install.',
        detail: 'NexusScan Desktop will restart to apply the update.',
        buttons: ['Install Now', 'Install Later']
      }).then((result) => {
        if (result.response === 0) {
          autoUpdater.quitAndInstall();
        }
      });
    });
  }

  private onWindowAllClosed(): void {
    log.info('🚨 All windows closed event triggered');
    // On macOS, keep app running even when all windows are closed
    if (process.platform !== 'darwin') {
      log.info('🚨 Quitting app due to all windows closed');
      app.quit();
    }
  }

  private onActivate(): void {
    // On macOS, re-create window when dock icon is clicked
    if (this.mainWindow === null) {
      this.createMainWindow();
    }
  }

  private async onBeforeQuit(): Promise<void> {
    log.info('NexusScan Desktop shutting down...');
    
    // Cleanup
    await this.backendManager.cleanup();
    
    log.info('NexusScan Desktop shutdown complete');
  }
}

// Create and start the application
const nexusScanApp = new NexusScanApp();

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  log.error('Uncaught exception:', error);
});

process.on('unhandledRejection', (reason, promise) => {
  log.error('Unhandled rejection at:', promise, 'reason:', reason);
});