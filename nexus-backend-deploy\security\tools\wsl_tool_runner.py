#!/usr/bin/env python3
"""
WSL Tool Runner for NexusScan Desktop
Executes Linux security tools through WSL from Windows application
"""

import subprocess
import json
import asyncio
import re
import shlex
from typing import Dict, List, Optional, Any
from pathlib import Path
from enum import Enum
import logging

logger = logging.getLogger(__name__)


class ExecutionMode(Enum):
    """Execution modes for security tools"""
    SIMULATION = "simulation"
    REAL = "real"


class SecurityError(Exception):
    """Security-related errors"""
    pass


class WSLToolRunner:
    """Execute Linux security tools through WSL"""
    
    def __init__(self, execution_mode: ExecutionMode = ExecutionMode.SIMULATION, 
                 require_confirmation: bool = True):
        """Initialize WSL tool runner
        
        Args:
            execution_mode: SIMULATION (safe, restricted) or REAL (unrestricted)
            require_confirmation: Whether real mode requires user confirmation
            
        Security Model:
            - SIMULATION mode: Command validation, whitelist-only execution
            - REAL mode: No command restrictions, only timeout protection
            - Both modes: Timeout protection to prevent runaway processes
        """
        self.execution_mode = execution_mode
        self.require_confirmation = require_confirmation
        self.real_mode_confirmed = False
        self.wsl_available = self._check_wsl_availability()
        
        # Security settings - timeout protection for both modes
        self.default_timeout = 300  # 5 minutes
        self.max_timeout = 1800     # 30 minutes
        self.allowed_tools = set()
        self._initialize_allowed_tools()
        self.linux_tools = {
            "dirb": {
                "command": "dirb",
                "install_cmd": "sudo apt update && sudo apt install -y dirb",
                "test_cmd": "dirb",
                "description": "Web content scanner - directory/file enumeration",
                "category": "web_scanning"
            },
            "nikto": {
                "command": "nikto",
                "install_cmd": "sudo apt update && sudo apt install -y nikto", 
                "test_cmd": "nikto -Version",
                "description": "Web server scanner - vulnerability detection",
                "category": "web_scanning"
            },
            "wpscan": {
                "command": "wpscan",
                "install_cmd": "sudo apt update && sudo apt install -y wpscan",
                "test_cmd": "wpscan --version",
                "description": "WordPress vulnerability scanner",
                "category": "web_scanning"
            },
            "enum4linux-ng": {
                "command": "enum4linux-ng",
                "install_cmd": "cd /opt && sudo git clone https://github.com/cddmp/enum4linux-ng.git && sudo ln -sf /opt/enum4linux-ng/enum4linux-ng.py /usr/local/bin/enum4linux-ng",
                "test_cmd": "enum4linux-ng --help",
                "description": "Modern SMB enumeration tool (next generation)",
                "category": "network_enumeration"
            },
            "smbclient": {
                "command": "smbclient",
                "install_cmd": "sudo apt update && sudo apt install -y smbclient",
                "test_cmd": "smbclient --version",
                "description": "SMB/CIFS client for file sharing access",
                "category": "network_enumeration"
            },
            "metasploit": {
                "command": "msfconsole",
                "install_cmd": "curl https://raw.githubusercontent.com/rapid7/metasploit-omnibus/master/config/templates/metasploit-framework-wrappers/msfupdate.erb | sudo bash && sudo apt install -y metasploit-framework",
                "test_cmd": "msfconsole --version",
                "description": "Penetration testing framework",
                "category": "exploitation"
            },
            "searchsploit": {
                "command": "searchsploit",
                "install_cmd": "sudo apt update && sudo apt install -y exploitdb",
                "test_cmd": "searchsploit --version",
                "description": "Local exploit database search tool",
                "category": "exploitation"
            },
            "ffuf": {
                "command": "ffuf",
                "install_cmd": "sudo apt update && sudo apt install -y ffuf",
                "test_cmd": "ffuf -V",
                "description": "Fast web fuzzer written in Go",
                "category": "web_scanning"
            },
            "feroxbuster": {
                "command": "feroxbuster",
                "install_cmd": "sudo apt update && sudo apt install -y feroxbuster",
                "test_cmd": "feroxbuster --version",
                "description": "Fast, simple, recursive content discovery tool",
                "category": "web_scanning"
            },
            "whatweb": {
                "command": "whatweb",
                "install_cmd": "sudo apt update && sudo apt install -y whatweb",
                "test_cmd": "whatweb --version",
                "description": "Web technology identifier",
                "category": "web_scanning"
            },
            "testssl": {
                "command": "testssl.sh",
                "install_cmd": "sudo apt update && sudo apt install -y testssl.sh",
                "test_cmd": "testssl.sh --version",
                "description": "SSL/TLS security scanner",
                "category": "ssl_scanning"
            },
            "sslyze": {
                "command": "sslyze",
                "install_cmd": "sudo apt update && sudo apt install -y sslyze",
                "test_cmd": "sslyze --version",
                "description": "Fast and powerful SSL/TLS scanning library",
                "category": "ssl_scanning"
            }
        }
    
    def _initialize_allowed_tools(self) -> None:
        """Initialize list of allowed tools for security validation"""
        # Only allow known security tools - no arbitrary command execution
        self.allowed_tools = {
            "dirb", "nikto", "wpscan", "enum4linux", "smbclient", "smbmap",
            "gobuster", "dirbuster", "ffuf", "feroxbuster", "whatweb",
            "testssl.sh", "sslyze", "nmap", "masscan", "rustscan",
            # Add approved tools here
        }
    
    def _validate_command(self, command: str) -> bool:
        """Validate command for security before execution"""
        if not command or not command.strip():
            raise SecurityError("Empty command not allowed")
        
        # In REAL mode, skip validation restrictions (only timeout protection)
        if self.execution_mode == ExecutionMode.REAL:
            logger.warning(f"REAL MODE: Command validation bypassed for: {command}")
            return True
        
        # SIMULATION mode: Apply full validation restrictions
        logger.info(f"SIMULATION MODE: Applying validation restrictions")
        
        # Extract base command
        cmd_parts = shlex.split(command)
        if not cmd_parts:
            raise SecurityError("Invalid command format")
        
        base_cmd = cmd_parts[0]
        
        # Check if tool is in allowed list (simulation mode only)
        if base_cmd not in self.allowed_tools:
            raise SecurityError(f"Tool '{base_cmd}' not in allowed tools list (simulation mode)")
        
        # Block dangerous patterns (simulation mode only)
        dangerous_patterns = [
            r'[;&|]',  # Command chaining
            r'`.*`',   # Command substitution
            r'\$\(',   # Command substitution
            r'>\s*/dev/', # Device access
            r'rm\s+-rf', # Destructive operations
            r'sudo\s+(?!apt)', # Only allow sudo apt
            r'curl\s+.*\|\s*bash', # Pipe to shell
            r'wget\s+.*\|\s*bash', # Pipe to shell
        ]
        
        for pattern in dangerous_patterns:
            if re.search(pattern, command, re.IGNORECASE):
                raise SecurityError(f"Command contains dangerous pattern: {pattern} (simulation mode)")
        
        return True
    
    def _simulate_command_output(self, command: str) -> Dict[str, Any]:
        """Generate simulated output for testing without real execution"""
        logger.info(f"SIMULATION MODE: Would execute: {command}")
        
        # Generate realistic-looking simulated output based on tool
        cmd_parts = shlex.split(command)
        tool = cmd_parts[0] if cmd_parts else "unknown"
        
        simulated_outputs = {
            "nmap": "Starting Nmap simulation...\nPorts scanned: 1000\nOpen ports found: 3",
            "dirb": "DIRB simulation started\nFound directories: /admin, /backup, /test",
            "nikto": "Nikto web scanner simulation\nVulnerabilities found: 5 (simulated)",
            "wpscan": "WordPress scanner simulation\nPlugins scanned: 15\nVulnerabilities: 2 (simulated)",
        }
        
        output = simulated_outputs.get(tool, f"Simulated output for {tool}")
        
        return {
            "success": True,
            "returncode": 0,
            "output": output,
            "stderr": "",
            "command": command,
            "execution_mode": "simulation",
            "timestamp": asyncio.get_event_loop().time()
        }
    
    def confirm_real_mode(self) -> bool:
        """Confirm real mode execution (to be called by frontend/user)"""
        if not self.require_confirmation:
            self.real_mode_confirmed = True
            return True
        
        # In production, this would integrate with frontend confirmation dialog
        logger.warning("Real mode confirmation required but no frontend available")
        logger.warning("Set require_confirmation=False to bypass (development only)")
        return False
    
    def set_execution_mode(self, mode: ExecutionMode) -> None:
        """Set execution mode"""
        self.execution_mode = mode
        if mode == ExecutionMode.REAL:
            logger.warning("Switched to REAL execution mode - commands will execute on system")
        else:
            logger.info("Switched to SIMULATION mode - commands will be simulated")
    
    def _check_wsl_availability(self) -> bool:
        """Check if WSL is available on the system - hybrid environment aware"""
        try:
            # Import environment detector
            from .environment_detector import environment_detector, ExecutionEnvironment
            
            # Check execution environment
            current_env = environment_detector.environment
            
            # If we're already in WSL or Linux, we don't need WSL bridge
            if current_env in [ExecutionEnvironment.WSL_GUEST, ExecutionEnvironment.LINUX_NATIVE]:
                logger.info("🐧 Running in Linux environment - WSL bridge not needed")
                return False  # Don't use WSL wrapper when already in Linux
            
            # If we're in Windows, check for WSL availability
            if current_env == ExecutionEnvironment.WINDOWS:
                result = subprocess.run(
                    ["wsl", "--status"],
                    capture_output=True,
                    text=True,
                    timeout=10
                )
                return result.returncode == 0
            
            return False
        except Exception as e:
            logger.error(f"Failed to check WSL availability: {e}")
            return False
    
    async def run_tool_hybrid(
        self, 
        tool_name: str, 
        target: str, 
        options: Dict[str, Any],
        timeout: int = 300
    ) -> 'ToolExecutionResult':
        """Hybrid tool execution - adapted for environment"""
        try:
            # Import hybrid execution components
            from .environment_detector import environment_detector, ExecutionEnvironment
            from .hybrid_execution_engine import ToolExecutionResult, ExecutionResult
            
            current_env = environment_detector.environment
            
            # If we're in Linux/WSL guest, execute directly without WSL wrapper
            if current_env in [ExecutionEnvironment.WSL_GUEST, ExecutionEnvironment.LINUX_NATIVE]:
                return await self._execute_native_linux(tool_name, target, options, timeout)
            
            # If we're in Windows with WSL, use traditional WSL execution
            elif current_env == ExecutionEnvironment.WINDOWS and self.wsl_available:
                return await self.run_tool(tool_name, target, options)
            
            # Otherwise, fallback to simulation
            else:
                logger.warning(f"WSL not available for {tool_name}, using simulation")
                return ToolExecutionResult(
                    status=ExecutionResult.NOT_AVAILABLE,
                    method="wsl_hybrid",
                    error="WSL not available in current environment"
                )
                
        except Exception as e:
            from .hybrid_execution_engine import ToolExecutionResult, ExecutionResult
            return ToolExecutionResult(
                status=ExecutionResult.FAILED,
                method="wsl_hybrid",
                error=f"Hybrid execution failed: {str(e)}"
            )
    
    async def _execute_native_linux(
        self,
        tool_name: str,
        target: str, 
        options: Dict[str, Any],
        timeout: int = 300
    ) -> 'ToolExecutionResult':
        """Execute tool natively in Linux environment"""
        import time
        from .hybrid_execution_engine import ToolExecutionResult, ExecutionResult
        
        start_time = time.time()
        
        try:
            # Build command without WSL wrapper
            command_parts = [tool_name]
            
            # Add target
            if target:
                command_parts.append(target)
            
            # Add basic options (simplified)
            if options.get('args'):
                if isinstance(options['args'], list):
                    command_parts.extend(options['args'])
                else:
                    command_parts.extend(options['args'].split())
            
            command = ' '.join(command_parts)
            
            logger.info(f"🐧 Executing natively in Linux: {command}")
            
            # Execute directly with shell
            process = await asyncio.create_subprocess_shell(
                command,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            try:
                stdout, stderr = await asyncio.wait_for(
                    process.communicate(),
                    timeout=timeout
                )
                
                execution_time = time.time() - start_time
                output = stdout.decode('utf-8', errors='replace')
                error = stderr.decode('utf-8', errors='replace')
                
                if process.returncode == 0:
                    return ToolExecutionResult(
                        status=ExecutionResult.SUCCESS,
                        method="native_linux",
                        output=output,
                        error=error,
                        exit_code=process.returncode,
                        execution_time=execution_time
                    )
                else:
                    return ToolExecutionResult(
                        status=ExecutionResult.FAILED,
                        method="native_linux",
                        output=output,
                        error=error,
                        exit_code=process.returncode,
                        execution_time=execution_time
                    )
                    
            except asyncio.TimeoutError:
                process.kill()
                return ToolExecutionResult(
                    status=ExecutionResult.TIMEOUT,
                    method="native_linux",
                    error=f"Tool execution timed out after {timeout} seconds",
                    execution_time=time.time() - start_time
                )
                
        except Exception as e:
            return ToolExecutionResult(
                status=ExecutionResult.FAILED,
                method="native_linux",
                error=f"Native Linux execution failed: {str(e)}",
                execution_time=time.time() - start_time
            )
            
        except Exception as e:
            logger.warning(f"WSL availability check failed: {e}")
            return False
    
    async def setup_wsl_environment(self) -> Dict[str, Any]:
        """Setup complete WSL environment for security tools"""
        if not self.wsl_available:
            return {"success": False, "error": "WSL not available"}
        
        setup_result = {
            "success": False,
            "wsl_setup": False,
            "tools_installed": {},
            "environment_ready": False
        }
        
        try:
            # 1. Check WSL distribution
            logger.info("🔍 Checking WSL distribution...")
            wsl_check = await self._run_wsl_command("cat /etc/os-release")
            if not wsl_check["success"]:
                # Try to install Ubuntu if no distribution
                logger.info("📥 Installing Ubuntu WSL distribution...")
                install_result = await self._run_command_on_host("wsl --install -d Ubuntu")
                if not install_result["success"]:
                    return setup_result
            
            setup_result["wsl_setup"] = True
            
            # 2. Update system packages
            logger.info("🔄 Updating WSL system packages...")
            update_result = await self._run_wsl_command("sudo apt update && sudo apt upgrade -y")
            
            # 3. Install essential dependencies
            logger.info("📦 Installing essential dependencies...")
            deps_cmd = "sudo apt install -y curl wget git build-essential python3 python3-pip perl ruby"
            deps_result = await self._run_wsl_command(deps_cmd)
            
            # 4. Install security tools by category
            await self._install_tools_by_category()
            
            # 5. Setup tool environment
            await self._setup_tool_environment()
            
            setup_result["environment_ready"] = True
            setup_result["success"] = True
            
            logger.info("✅ WSL environment setup complete")
            
        except Exception as e:
            logger.error(f"❌ WSL setup failed: {e}")
            setup_result["error"] = str(e)
        
        return setup_result
    
    async def _install_tools_by_category(self):
        """Install tools organized by category for better success rate"""
        categories = {
            "web_scanning": ["nikto", "dirb", "wpscan", "ffuf", "feroxbuster", "whatweb"],
            "network_enumeration": ["enum4linux", "smbclient"],
            "ssl_scanning": ["testssl", "sslyze"],
            "exploitation": ["searchsploit"]  # Metasploit separately due to complexity
        }
        
        for category, tools in categories.items():
            logger.info(f"📦 Installing {category} tools...")
            
            for tool_name in tools:
                if tool_name in self.linux_tools:
                    await self._install_single_tool(tool_name)
        
        # Install Metasploit separately (more complex)
        logger.info("🚀 Installing Metasploit Framework...")
        await self._install_metasploit()
    
    async def _install_single_tool(self, tool_name: str) -> bool:
        """Install a single tool with retry logic"""
        tool_config = self.linux_tools[tool_name]
        max_retries = 3
        
        for attempt in range(max_retries):
            try:
                logger.info(f"  📥 Installing {tool_name} (attempt {attempt + 1}/{max_retries})")
                result = await self._run_wsl_command(tool_config["install_cmd"])
                
                if result["success"]:
                    # Verify installation
                    test_result = await self._run_wsl_command(tool_config["test_cmd"])
                    if test_result["success"]:
                        logger.info(f"  ✅ {tool_name} installed and verified")
                        return True
                    else:
                        logger.warning(f"  ⚠️ {tool_name} installed but verification failed")
                
                # Wait before retry
                if attempt < max_retries - 1:
                    await asyncio.sleep(2)
                    
            except Exception as e:
                logger.error(f"  ❌ Error installing {tool_name}: {e}")
        
        logger.error(f"  ❌ Failed to install {tool_name} after {max_retries} attempts")
        return False
    
    async def _install_metasploit(self) -> bool:
        """Install Metasploit with proper setup"""
        try:
            # Add Metasploit repository and install
            msf_commands = [
                "curl https://raw.githubusercontent.com/rapid7/metasploit-omnibus/master/config/templates/metasploit-framework-wrappers/msfupdate.erb > /tmp/msfinstall",
                "chmod 755 /tmp/msfinstall",
                "sudo /tmp/msfinstall",
                "sudo apt update",
                "sudo apt install -y metasploit-framework"
            ]
            
            for cmd in msf_commands:
                result = await self._run_wsl_command(cmd)
                if not result["success"]:
                    logger.warning(f"Metasploit setup step failed: {cmd}")
                    return False
            
            # Initialize Metasploit database
            init_result = await self._run_wsl_command("sudo msfdb init")
            
            logger.info("✅ Metasploit Framework installed")
            return True
            
        except Exception as e:
            logger.error(f"❌ Metasploit installation failed: {e}")
            return False
    
    async def _setup_tool_environment(self):
        """Setup environment variables and configurations"""
        env_commands = [
            # Setup wordlists directory
            "sudo mkdir -p /usr/share/wordlists",
            "sudo chown $USER:$USER /usr/share/wordlists",
            
            # Download common wordlists
            "cd /usr/share/wordlists && wget -q https://github.com/danielmiessler/SecLists/archive/master.zip -O seclists.zip",
            "cd /usr/share/wordlists && unzip -q seclists.zip && mv SecLists-master SecLists",
            
            # Setup tool configurations
            "mkdir -p ~/.config/nuclei",
            "mkdir -p ~/.config/ffuf",
            
            # Update locate database
            "sudo updatedb || true"
        ]
        
        for cmd in env_commands:
            await self._run_wsl_command(cmd)
    
    async def _run_command_on_host(self, command: str) -> Dict[str, Any]:
        """Run command on Windows host (not in WSL)"""
        try:
            process = await asyncio.create_subprocess_shell(
                command,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await process.communicate()
            
            return {
                "success": process.returncode == 0,
                "returncode": process.returncode,
                "output": stdout.decode('utf-8', errors='ignore'),
                "stderr": stderr.decode('utf-8', errors='ignore')
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "output": "",
                "stderr": str(e)
            }

    async def install_linux_tools(self) -> Dict[str, bool]:
        """Install Linux security tools in WSL"""
        if not self.wsl_available:
            return {}
        
        # Use the comprehensive setup method
        setup_result = await self.setup_wsl_environment()
        
        if setup_result["success"]:
            # Return status of individual tools
            status = await self.check_tool_status()
            return status
        else:
            return {}
    
    async def run_tool(self, tool_name: str, arguments: List[str], timeout: Optional[int] = None) -> Dict[str, Any]:
        """Run a Linux tool through WSL"""
        if not self.wsl_available:
            return {
                "success": False,
                "error": "WSL not available",
                "output": "",
                "stderr": ""
            }
        
        if tool_name not in self.linux_tools:
            return {
                "success": False,
                "error": f"Tool {tool_name} not supported",
                "output": "",
                "stderr": ""
            }
        
        tool_config = self.linux_tools[tool_name]
        
        # Check if tool is installed
        test_result = await self._run_wsl_command(tool_config["test_cmd"])
        if not test_result["success"]:
            # Try to install the tool
            logger.info(f"Tool {tool_name} not found, attempting installation...")
            install_result = await self._run_wsl_command(tool_config["install_cmd"])
            if not install_result["success"]:
                return {
                    "success": False,
                    "error": f"Failed to install {tool_name}",
                    "output": "",
                    "stderr": install_result["stderr"]
                }
        
        # Build command
        command = [tool_config["command"]] + arguments
        command_str = " ".join(command)
        
        # Execute tool with timeout
        return await self._run_wsl_command(command_str, timeout)
    
    async def execute_command(self, command: str, timeout: Optional[int] = None) -> Dict[str, Any]:
        """Execute arbitrary command directly (real mode allows any command)"""
        return await self._run_wsl_command(command, timeout)
    
    async def _run_wsl_command(self, command: str, timeout: Optional[int] = None) -> Dict[str, Any]:
        """Execute a command in WSL with security controls"""
        try:
            # Validate command before execution
            self._validate_command(command)
            
            # Check execution mode
            if self.execution_mode == ExecutionMode.SIMULATION:
                return self._simulate_command_output(command)
            
            # Real mode execution requires confirmation
            if self.execution_mode == ExecutionMode.REAL:
                if self.require_confirmation and not self.real_mode_confirmed:
                    if not self.confirm_real_mode():
                        raise SecurityError("Real mode requires explicit confirmation")
                
                logger.warning(f"REAL MODE: Executing command: {command}")
            
            # Set timeout (default or specified)
            execution_timeout = timeout or self.default_timeout
            if execution_timeout > self.max_timeout:
                execution_timeout = self.max_timeout
                logger.warning(f"Timeout reduced to maximum allowed: {self.max_timeout}s")
            
            # Execute with timeout and resource limits
            process = await asyncio.create_subprocess_exec(
                "wsl",
                "--exec",
                "bash", 
                "-c",
                command,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            try:
                stdout, stderr = await asyncio.wait_for(
                    process.communicate(), 
                    timeout=execution_timeout
                )
            except asyncio.TimeoutError:
                # Kill the process if it times out
                try:
                    process.kill()
                    await process.wait()
                except:
                    pass
                
                return {
                    "success": False,
                    "error": f"Command timed out after {execution_timeout} seconds",
                    "output": "",
                    "stderr": f"Execution timeout ({execution_timeout}s)",
                    "command": command,
                    "execution_mode": self.execution_mode.value,
                    "timeout": execution_timeout
                }
            
            return {
                "success": process.returncode == 0,
                "returncode": process.returncode,
                "output": stdout.decode('utf-8', errors='ignore'),
                "stderr": stderr.decode('utf-8', errors='ignore'),
                "command": command,
                "execution_mode": self.execution_mode.value,
                "timeout": execution_timeout
            }
            
        except SecurityError as e:
            logger.error(f"Security validation failed: {e}")
            return {
                "success": False,
                "error": f"Security validation failed: {str(e)}",
                "output": "",
                "stderr": str(e),
                "command": command,
                "execution_mode": self.execution_mode.value
            }
        except Exception as e:
            logger.error(f"Command execution failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "output": "",
                "stderr": str(e),
                "command": command,
                "execution_mode": self.execution_mode.value
            }
    
    def get_available_tools(self) -> List[str]:
        """Get list of available Linux tools"""
        if not self.wsl_available:
            return []
        return list(self.linux_tools.keys())
    
    async def check_tool_status(self) -> Dict[str, bool]:
        """Check which Linux tools are installed"""
        if not self.wsl_available:
            return {}
        
        status = {}
        for tool_name, tool_config in self.linux_tools.items():
            result = await self._run_wsl_command(tool_config["test_cmd"])
            status[tool_name] = result["success"]
        
        return status


# Example usage functions for specific tools

async def run_dirb_scan(target_url: str, wordlist: str = "/usr/share/dirb/wordlists/common.txt") -> Dict[str, Any]:
    """Run DIRB directory scan"""
    wsl_runner = WSLToolRunner()
    
    arguments = [target_url, wordlist]
    result = await wsl_runner.run_tool("dirb", arguments)
    
    if result["success"]:
        # Parse DIRB output
        directories_found = []
        for line in result["output"].split('\n'):
            if line.startswith('+ '):
                directories_found.append(line.strip())
        
        result["parsed_results"] = {
            "directories_found": directories_found,
            "target": target_url,
            "wordlist": wordlist
        }
    
    return result

async def run_nikto_scan(target_url: str) -> Dict[str, Any]:
    """Run Nikto web vulnerability scan"""
    wsl_runner = WSLToolRunner()
    
    arguments = ["-h", target_url, "-Format", "json"]
    result = await wsl_runner.run_tool("nikto", arguments)
    
    if result["success"]:
        # Parse Nikto JSON output
        try:
            nikto_data = json.loads(result["output"])
            result["parsed_results"] = nikto_data
        except json.JSONDecodeError:
            # Fallback to text parsing
            result["parsed_results"] = {
                "raw_output": result["output"],
                "target": target_url
            }
    
    return result

async def run_wpscan(target_url: str, api_token: Optional[str] = None) -> Dict[str, Any]:
    """Run WPScan WordPress vulnerability scan"""
    wsl_runner = WSLToolRunner()
    
    arguments = ["--url", target_url, "--format", "json"]
    if api_token:
        arguments.extend(["--api-token", api_token])
    
    result = await wsl_runner.run_tool("wpscan", arguments)
    
    if result["success"]:
        try:
            wpscan_data = json.loads(result["output"])
            result["parsed_results"] = wpscan_data
        except json.JSONDecodeError:
            result["parsed_results"] = {
                "raw_output": result["output"],
                "target": target_url
            }
    
    return result