#!/usr/bin/env python3
"""
Legal Disclaimer and Responsible Usage Framework for NexusScan Backend
Provides legal protection through informed consent and educational guidance.
Backend-only version without UI dependencies.
"""

import asyncio
import logging
import json
import hashlib
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass, asdict
from enum import Enum

from core.config import Config
from core.database import DatabaseManager

logger = logging.getLogger(__name__)


class DisclaimerType(Enum):
    """Types of legal disclaimers"""
    INITIAL_STARTUP = "initial_startup"
    SECURITY_TOOL = "security_tool"
    AI_PAYLOAD = "ai_payload"
    DATA_EXPORT = "data_export"
    HIGH_IMPACT = "high_impact"


class WarningLevel(Enum):
    """Warning severity levels"""
    INFO = "info"
    CAUTION = "caution"
    WARNING = "warning"
    CRITICAL = "critical"


@dataclass
class DisclaimerConfig:
    """Configuration for legal disclaimer"""
    disclaimer_type: DisclaimerType
    title: str
    message: str
    acknowledgment_text: str
    educational_content: List[str]
    legal_references: List[str]
    requires_explicit_consent: bool
    session_persistent: bool
    minimum_display_time: int  # seconds


@dataclass
class UserAcknowledgment:
    """Record of user acknowledgment"""
    acknowledgment_id: str
    user_id: str
    disclaimer_type: DisclaimerType
    timestamp: datetime
    ip_address: str
    user_agent: str
    consent_hash: str
    session_id: str
    metadata: Dict[str, Any]


class LegalDisclaimerManager:
    """
    Manages legal disclaimers and user acknowledgments for responsible security testing.
    Provides legal protection through informed consent without restricting capabilities.
    """
    
    def __init__(self, config: Config, db_manager: DatabaseManager):
        self.config = config
        self.db_manager = db_manager
        
        # Session tracking
        self.session_acknowledgments = {}
        self.disclaimer_shown = {}
        
        # Initialize disclaimers
        self.disclaimers = self._initialize_disclaimers()
        
        logger.info("Legal Disclaimer Manager initialized")
    
    def _initialize_disclaimers(self) -> Dict[DisclaimerType, DisclaimerConfig]:
        """Initialize all disclaimer configurations"""
        return {
            DisclaimerType.INITIAL_STARTUP: DisclaimerConfig(
                disclaimer_type=DisclaimerType.INITIAL_STARTUP,
                title="NexusScan Legal Notice & Terms of Use",
                message="""
⚠️ IMPORTANT LEGAL NOTICE ⚠️

NexusScan is a professional penetration testing platform designed exclusively for authorized security testing.

LEGAL REQUIREMENTS:
• You must have explicit written authorization to test all target systems
• You must comply with all applicable local, state, and federal laws
• Unauthorized security testing may constitute criminal activity
• You are solely responsible for ensuring proper authorization

INTENDED USE:
• Authorized penetration testing and security assessments
• Security research in controlled environments
• Educational purposes in approved laboratory settings
• Compliance testing with proper organizational approval

WARNING: Misuse of this software may result in:
• Criminal prosecution under computer crime laws
• Civil liability for damages
• Termination of employment or academic standing
• Violation of service provider terms of service

By proceeding, you acknowledge that you understand these requirements and accept full legal and ethical responsibility for all activities performed using this software.
                """.strip(),
                acknowledgment_text="I have proper authorization and accept full responsibility",
                educational_content=[
                    "Always obtain written authorization before testing",
                    "Document scope and limitations of testing authorization",
                    "Follow responsible disclosure practices",
                    "Respect privacy and confidentiality requirements",
                    "Maintain detailed logs of all testing activities"
                ],
                legal_references=[
                    "Computer Fraud and Abuse Act (CFAA) - 18 U.S.C. § 1030",
                    "Digital Millennium Copyright Act (DMCA)",
                    "General Data Protection Regulation (GDPR)",
                    "Local and state computer crime statutes"
                ],
                requires_explicit_consent=True,
                session_persistent=True,
                minimum_display_time=10
            ),
            
            DisclaimerType.SECURITY_TOOL: DisclaimerConfig(
                disclaimer_type=DisclaimerType.SECURITY_TOOL,
                title="Security Tool Execution Notice",
                message="""
🔧 SECURITY TOOL EXECUTION

You are about to execute security testing tools that may:
• Generate network traffic that could be detected
• Trigger security alerts on target systems
• Consume system resources during scanning
• Create log entries on target and intermediate systems

BEST PRACTICES:
• Coordinate with system administrators when possible
• Test during approved maintenance windows
• Use appropriate scanning intensity for the environment
• Monitor for system impact during testing
• Document all testing activities

Remember: You are responsible for ensuring all testing is authorized and conducted professionally.
                """.strip(),
                acknowledgment_text="I understand the impact and have proper authorization",
                educational_content=[
                    "Consider timing of security scans",
                    "Monitor system performance during testing",
                    "Use appropriate tool configurations",
                    "Maintain communication with stakeholders"
                ],
                legal_references=[],
                requires_explicit_consent=True,
                session_persistent=True,
                minimum_display_time=5
            ),
            
            DisclaimerType.AI_PAYLOAD: DisclaimerConfig(
                disclaimer_type=DisclaimerType.AI_PAYLOAD,
                title="AI-Generated Payload Notice",
                message="""
🤖 AI-POWERED SECURITY TESTING

This tool uses artificial intelligence to generate security testing payloads and attack vectors.

IMPORTANT CONSIDERATIONS:
• AI-generated payloads may be more sophisticated than traditional tests
• Novel attack vectors may trigger unexpected system responses
• Generated content is for authorized testing purposes only
• Payloads may contain techniques not seen by security controls

RESPONSIBLE USAGE:
• Review AI-generated content before execution
• Start with less aggressive testing approaches
• Monitor target systems for unexpected behavior
• Maintain detailed records of AI-assisted testing

Educational Purpose: AI enhancement is designed to improve security testing effectiveness while maintaining professional standards.
                """.strip(),
                acknowledgment_text="I will review AI content and use it responsibly",
                educational_content=[
                    "Review AI-generated payloads before use",
                    "Understand potential impact of novel techniques",
                    "Use AI capabilities to enhance, not replace, professional judgment",
                    "Document AI-assisted testing methodologies"
                ],
                legal_references=[],
                requires_explicit_consent=False,
                session_persistent=True,
                minimum_display_time=3
            ),
            
            DisclaimerType.HIGH_IMPACT: DisclaimerConfig(
                disclaimer_type=DisclaimerType.HIGH_IMPACT,
                title="High-Impact Operation Warning",
                message="""
⚠️ HIGH-IMPACT SECURITY OPERATION

The operation you are about to perform has been identified as potentially high-impact:

POTENTIAL EFFECTS:
• May cause service disruption or system instability
• Could generate significant network traffic
• May trigger security incident response procedures
• Could result in data extraction or system modification

PROFESSIONAL RECOMMENDATIONS:
• Verify explicit authorization for high-impact testing
• Coordinate with system owners and stakeholders
• Have incident response contacts readily available
• Consider testing during maintenance windows
• Ensure proper backup and recovery procedures are in place

This warning is provided for educational purposes to promote responsible security testing practices.
                """.strip(),
                acknowledgment_text="I have verified authorization for high-impact testing",
                educational_content=[
                    "High-impact operations require additional care",
                    "Coordinate with stakeholders before execution",
                    "Have rollback procedures ready",
                    "Monitor systems closely during testing"
                ],
                legal_references=[],
                requires_explicit_consent=True,
                session_persistent=False,
                minimum_display_time=7
            )
        }
    
    async def check_disclaimer_acknowledgment(self, disclaimer_type: DisclaimerType, 
                                            context: Dict[str, Any] = None) -> bool:
        """
        Check if disclaimer has been acknowledged (backend-only version).
        Returns True if acknowledged, False if needs acknowledgment.
        """
        try:
            # Check if already acknowledged in session
            if (disclaimer_type in self.session_acknowledgments and 
                self.disclaimers[disclaimer_type].session_persistent):
                return True
            
            # For backend-only mode, we log the disclaimer requirement
            # but don't block execution (frontend will handle actual display)
            disclaimer = self.disclaimers[disclaimer_type]
            
            logger.info(f"Disclaimer required: {disclaimer_type.value}")
            logger.info(f"Title: {disclaimer.title}")
            logger.info(f"Educational content: {len(disclaimer.educational_content)} items")
            
            # Record requirement for frontend display
            await self._record_disclaimer_requirement(disclaimer_type, context or {})
            
            # For backend mode, assume acknowledgment will be handled by frontend
            return True
            
        except Exception as e:
            logger.error(f"Failed to check disclaimer {disclaimer_type}: {e}")
            return True  # Don't block backend operations
    
    async def _record_disclaimer_requirement(self, disclaimer_type: DisclaimerType, 
                                           context: Dict[str, Any]):
        """Record disclaimer requirement for frontend display"""
        try:
            requirement = {
                "requirement_id": f"req_{disclaimer_type.value}_{int(datetime.now().timestamp())}",
                "disclaimer_type": disclaimer_type.value,
                "timestamp": datetime.now().isoformat(),
                "context": context,
                "status": "pending_frontend_display"
            }
            
            # Store requirement (could be in database or message queue for frontend)
            logger.info(f"Recorded disclaimer requirement: {disclaimer_type.value}")
            
        except Exception as e:
            logger.error(f"Failed to record disclaimer requirement: {e}")
    
    async def _record_acknowledgment(self, disclaimer_type: DisclaimerType, 
                                   context: Dict[str, Any]):
        """Record user acknowledgment in database"""
        try:
            acknowledgment = UserAcknowledgment(
                acknowledgment_id=f"ack_{disclaimer_type.value}_{int(datetime.now().timestamp())}",
                user_id=context.get("user_id", "anonymous"),
                disclaimer_type=disclaimer_type,
                timestamp=datetime.now(),
                ip_address=context.get("ip_address", "unknown"),
                user_agent=context.get("user_agent", "NexusScan Desktop"),
                consent_hash=hashlib.sha256(
                    f"{disclaimer_type.value}_{datetime.now().isoformat()}".encode()
                ).hexdigest(),
                session_id=context.get("session_id", "unknown"),
                metadata=context
            )
            
            # Store in database (implementation depends on actual schema)
            logger.info(f"Recorded acknowledgment for {disclaimer_type.value}")
            
        except Exception as e:
            logger.error(f"Failed to record acknowledgment: {e}")
    
    async def analyze_command_impact(self, command: str, tool_name: str, 
                                   risk_level: WarningLevel) -> Dict[str, Any]:
        """
        Analyze command for potential impact and provide educational guidance (backend-only).
        Returns analysis information for frontend display.
        """
        try:
            # Analyze command for educational guidance
            warning_info = self._analyze_command_impact(command, tool_name, risk_level)
            
            if risk_level == WarningLevel.INFO:
                logger.info(f"Command analysis: {command} - {tool_name} (low risk)")
            else:
                logger.warning(f"Command analysis: {command} - {tool_name} (risk level: {risk_level.value})")
                logger.info(f"Potential impacts: {warning_info['potential_impacts']}")
                logger.info(f"Best practices: {warning_info['best_practices']}")
            
            return warning_info
            
        except Exception as e:
            logger.error(f"Failed to analyze command: {e}")
            return {
                "command": command,
                "tool": tool_name,
                "risk_level": risk_level,
                "potential_impacts": [],
                "best_practices": [],
                "educational_notes": []
            }
    
    def _analyze_command_impact(self, command: str, tool_name: str, 
                              risk_level: WarningLevel) -> Dict[str, Any]:
        """Analyze command for potential impact and provide educational guidance"""
        impact_analysis = {
            "command": command,
            "tool": tool_name,
            "risk_level": risk_level,
            "potential_impacts": [],
            "best_practices": [],
            "educational_notes": []
        }
        
        # Command pattern analysis
        command_lower = command.lower()
        
        # High-impact patterns
        if any(pattern in command_lower for pattern in ["--dump-all", "--dump", "-a", "--all"]):
            impact_analysis["potential_impacts"].append("May extract large amounts of data")
            impact_analysis["best_practices"].append("Consider using targeted extraction instead")
        
        if any(pattern in command_lower for pattern in ["--threads", "-t"]):
            import re
            thread_match = re.search(r'(?:--threads|(?<!\w)-t)\s*[=\s]\s*(\d+)', command_lower)
            if thread_match and int(thread_match.group(1)) > 10:
                impact_analysis["potential_impacts"].append("High thread count may overwhelm target")
                impact_analysis["best_practices"].append("Consider reducing thread count for stability")
        
        if any(pattern in command_lower for pattern in ["--risk=3", "--level=5", "--aggressive"]):
            impact_analysis["potential_impacts"].append("Aggressive testing may trigger alerts")
            impact_analysis["best_practices"].append("Coordinate with security team before aggressive scans")
        
        if any(pattern in command_lower for pattern in ["--batch", "--non-interactive"]):
            impact_analysis["potential_impacts"].append("Automated execution without manual review")
            impact_analysis["best_practices"].append("Review results carefully after batch operations")
        
        # Network impact
        if any(pattern in command_lower for pattern in ["nmap", "masscan", "-p-", "--top-ports"]):
            impact_analysis["potential_impacts"].append("Network scanning may be detected")
            impact_analysis["best_practices"].append("Use appropriate timing and stealth options")
        
        # Data handling
        if any(pattern in command_lower for pattern in ["--output", "-o", "--export"]):
            impact_analysis["potential_impacts"].append("Data will be saved to local files")
            impact_analysis["best_practices"].append("Ensure secure handling of extracted data")
        
        # Educational content based on tool
        if tool_name.lower() == "sqlmap":
            impact_analysis["educational_notes"].extend([
                "SQLMap is designed for authorized database security testing",
                "Always verify database connectivity and permissions",
                "Consider database load during testing"
            ])
        elif tool_name.lower() == "nmap":
            impact_analysis["educational_notes"].extend([
                "Nmap scanning may be logged by network security devices",
                "Use appropriate timing options for network conditions",
                "Consider firewall and IDS detection capabilities"
            ])
        elif tool_name.lower() == "nuclei":
            impact_analysis["educational_notes"].extend([
                "Nuclei templates test for known vulnerabilities",
                "Template execution may trigger security controls",
                "Review template content before execution"
            ])
        
        return impact_analysis
    
    def get_disclaimer_content(self, disclaimer_type: DisclaimerType) -> Dict[str, Any]:
        """Get disclaimer content for frontend display (backend-only method)"""
        if disclaimer_type not in self.disclaimers:
            return {}
        
        disclaimer = self.disclaimers[disclaimer_type]
        return {
            "type": disclaimer_type.value,
            "title": disclaimer.title,
            "message": disclaimer.message,
            "acknowledgment_text": disclaimer.acknowledgment_text,
            "educational_content": disclaimer.educational_content,
            "legal_references": disclaimer.legal_references,
            "requires_explicit_consent": disclaimer.requires_explicit_consent,
            "minimum_display_time": disclaimer.minimum_display_time
        }
    
    def get_acknowledgment_status(self, disclaimer_type: DisclaimerType) -> bool:
        """Check if disclaimer has been acknowledged in current session"""
        return self.session_acknowledgments.get(disclaimer_type, False)
    
    def clear_session_acknowledgments(self):
        """Clear all session acknowledgments (e.g., on logout)"""
        self.session_acknowledgments.clear()
        self.disclaimer_shown.clear()
    
    def record_acknowledgment_sync(self, disclaimer_type: DisclaimerType, context: Dict[str, Any] = None):
        """Record acknowledgment synchronously (for backend use)"""
        try:
            acknowledgment = UserAcknowledgment(
                acknowledgment_id=f"ack_{disclaimer_type.value}_{int(datetime.now().timestamp())}",
                user_id=context.get("user_id", "anonymous") if context else "anonymous",
                disclaimer_type=disclaimer_type,
                timestamp=datetime.now(),
                ip_address=context.get("ip_address", "unknown") if context else "unknown",
                user_agent=context.get("user_agent", "NexusScan Backend") if context else "NexusScan Backend",
                consent_hash=hashlib.sha256(
                    f"{disclaimer_type.value}_{datetime.now().isoformat()}".encode()
                ).hexdigest(),
                session_id=context.get("session_id", "unknown") if context else "unknown",
                metadata=context or {}
            )
            
            # Store in session
            if self.disclaimers[disclaimer_type].session_persistent:
                self.session_acknowledgments[disclaimer_type] = True
            
            logger.info(f"Recorded acknowledgment for {disclaimer_type.value}")
            
        except Exception as e:
            logger.error(f"Failed to record acknowledgment: {e}")


# Backend-only factory function
def create_legal_disclaimer_manager(config: Config, db_manager: DatabaseManager) -> LegalDisclaimerManager:
    """Create legal disclaimer manager for backend use"""
    return LegalDisclaimerManager(config, db_manager)


if __name__ == "__main__":
    # Backend-only example
    print("Legal Disclaimer Manager - Backend Only Version")
    print("For frontend integration, use get_disclaimer_content() and analyze_command_impact() methods")