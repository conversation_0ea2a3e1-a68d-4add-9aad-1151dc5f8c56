#!/usr/bin/env python3
"""
Real Tool Detection System
Provides honest reporting of actually available security tools
"""

import subprocess
import logging
import os
import sys
from typing import Dict, List, Optional, Any
from pathlib import Path
import shutil

logger = logging.getLogger(__name__)

class RealToolDetector:
    """Detects and validates actual security tool availability"""
    
    def __init__(self):
        """Initialize real tool detector"""
        self.detected_tools = {}
        self.detection_cache = {}
        
    def detect_all_tools(self) -> Dict[str, Any]:
        """Detect all available security tools
        
        Returns:
            Dictionary of tool detection results
        """
        tools_to_detect = {
            # Network Scanners
            'nmap': {
                'commands': [['nmap', '--version']],
                'category': 'Network Scanners',
                'description': 'Network Mapper - Network discovery and security auditing'
            },
            'masscan': {
                'commands': [['masscan', '--version']],
                'category': 'Network Scanners', 
                'description': 'Fast port scanner'
            },
            'zmap': {
                'commands': [['zmap', '--version']],
                'category': 'Network Scanners',
                'description': 'Internet-wide network scanner'
            },
            
            # Vulnerability Scanners
            'nuclei': {
                'commands': [['nuclei', '-version']],
                'category': 'Vulnerability Scanners',
                'description': 'Fast vulnerability scanner'
            },
            'openvas': {
                'commands': [['openvas', '--version'], ['gvm-start', '--help']],
                'category': 'Vulnerability Scanners',
                'description': 'OpenVAS vulnerability scanner'
            },
            'nessus': {
                'commands': [['nessuscli', '--version'], ['/opt/nessus/sbin/nessuscli', '--version']],
                'category': 'Vulnerability Scanners',
                'description': 'Nessus vulnerability scanner'
            },
            
            # Web Scanners
            'sqlmap': {
                'commands': [['sqlmap', '--version']],
                'category': 'Web Scanners',
                'description': 'SQL injection testing tool'
            },
            'nikto': {
                'commands': [['nikto', '-Version']],
                'category': 'Web Scanners',
                'description': 'Web vulnerability scanner'
            },
            'dirb': {
                'commands': [['dirb']],
                'category': 'Web Scanners',
                'description': 'Web directory brute-forcer'
            },
            'gobuster': {
                'commands': [['gobuster', 'version']],
                'category': 'Web Scanners',
                'description': 'Directory/file brute-forcer'
            },
            'ffuf': {
                'commands': [['ffuf', '-V']],
                'category': 'Web Scanners',
                'description': 'Fast web fuzzer'
            },
            
            # SSL/TLS Tools
            'testssl': {
                'commands': [['testssl.sh', '--version'], ['testssl', '--version']],
                'category': 'SSL/TLS',
                'description': 'SSL/TLS testing tool'
            },
            'sslyze': {
                'commands': [['sslyze', '--version']],
                'category': 'SSL/TLS',
                'description': 'SSL configuration scanner'
            },
            
            # Additional Security Tools
            'whatweb': {
                'commands': [['whatweb', '--version']],
                'category': 'Fingerprinting',
                'description': 'Web technology identifier'
            },
            'smbclient': {
                'commands': [['smbclient', '--version']],
                'category': 'Network Enumeration',
                'description': 'SMB/CIFS client'
            },
            'feroxbuster': {
                'commands': [['feroxbuster', '--version']],
                'category': 'Fuzzer',
                'description': 'Fast content discovery tool'
            },
            'enum4linux': {
                'commands': [['enum4linux', '--help']],
                'category': 'Network Enumeration',
                'description': 'SMB enumeration tool'
            },
            'searchsploit': {
                'commands': [['searchsploit', '--version']],
                'category': 'Exploit Database',
                'description': 'Exploit database search'
            },
            
            # Framework Tools
            'metasploit': {
                'commands': [['msfconsole', '--version'], ['msfconsole', '-v']],
                'category': 'Framework',
                'description': 'Penetration testing framework'
            },
            'burp': {
                'commands': [['burpsuite', '--version'], ['java', '-jar', '/opt/burp/burpsuite.jar', '--version']],
                'category': 'Framework',
                'description': 'Web application security testing'
            },
            
            # Password Tools
            'hydra': {
                'commands': [['hydra', '-h']],
                'category': 'Password Tools',
                'description': 'Network logon cracker'
            },
            'john': {
                'commands': [['john', '--test=0']],
                'category': 'Password Tools',
                'description': 'John the Ripper password cracker'
            },
            'hashcat': {
                'commands': [['hashcat', '--version']],
                'category': 'Password Tools',
                'description': 'Advanced password recovery'
            },
            
            # Custom Tools (NexusScan specific)
            'wpscan': {
                'commands': [['wpscan', '--version']],
                'category': 'Web Scanners',
                'description': 'WordPress vulnerability scanner'
            }
        }
        
        results = {}
        
        for tool_name, tool_config in tools_to_detect.items():
            detection_result = self._detect_single_tool(tool_name, tool_config)
            results[tool_name] = detection_result
            
        self.detected_tools = results
        return results
    
    def _detect_single_tool(self, tool_name: str, tool_config: Dict[str, Any]) -> Dict[str, Any]:
        """Detect a single security tool
        
        Args:
            tool_name: Name of the tool
            tool_config: Tool configuration with detection commands
            
        Returns:
            Detection result dictionary
        """
        # Check cache first
        if tool_name in self.detection_cache:
            return self.detection_cache[tool_name]
        
        result = {
            'name': tool_name,
            'available': False,
            'version': None,
            'path': None,
            'category': tool_config.get('category', 'Unknown'),
            'description': tool_config.get('description', f'{tool_name} security tool'),
            'detection_method': 'command_execution',
            'error': None
        }
        
        # Try each detection command
        for command in tool_config.get('commands', []):
            try:
                # First check if command exists in PATH
                if command[0]:
                    tool_path = shutil.which(command[0])
                    if tool_path:
                        result['path'] = tool_path
                    else:
                        continue  # Tool not in PATH, try next command
                
                # Execute detection command
                proc_result = subprocess.run(
                    command,
                    capture_output=True,
                    text=True,
                    timeout=10
                )
                
                # Check if command succeeded or provided version info
                if (proc_result.returncode == 0 or 
                    'version' in proc_result.stdout.lower() or
                    'usage' in proc_result.stderr.lower() or
                    tool_name.lower() in proc_result.stdout.lower()):
                    
                    result['available'] = True
                    result['version'] = self._extract_version(proc_result.stdout + proc_result.stderr)
                    break
                    
            except subprocess.TimeoutExpired:
                result['error'] = 'Command timeout'
                continue
            except FileNotFoundError:
                result['error'] = 'Command not found'
                continue
            except Exception as e:
                result['error'] = str(e)
                continue
        
        # Cache result
        self.detection_cache[tool_name] = result
        
        # Log detection result
        if result['available']:
            logger.info(f"✅ Detected {tool_name}: {result['version']} at {result['path']}")
        else:
            logger.debug(f"❌ {tool_name} not available: {result['error']}")
        
        return result
    
    def _extract_version(self, output: str) -> str:
        """Extract version information from command output
        
        Args:
            output: Command output text
            
        Returns:
            Extracted version string
        """
        output = output.strip()
        
        # Common version patterns
        version_patterns = [
            r'version\s+(\d+\.\d+(?:\.\d+)?)',
            r'v(\d+\.\d+(?:\.\d+)?)',
            r'(\d+\.\d+(?:\.\d+)?)',
        ]
        
        import re
        for pattern in version_patterns:
            match = re.search(pattern, output, re.IGNORECASE)
            if match:
                return match.group(1)
        
        # Return first non-empty line if no version pattern found
        lines = [line.strip() for line in output.split('\n') if line.strip()]
        if lines:
            return lines[0][:100]  # Limit length
        
        return 'unknown'
    
    def get_available_tools(self) -> List[Dict[str, Any]]:
        """Get list of available tools only
        
        Returns:
            List of available tool dictionaries
        """
        if not self.detected_tools:
            self.detect_all_tools()
        
        return [tool for tool in self.detected_tools.values() if tool['available']]
    
    def get_tools_by_category(self) -> Dict[str, List[Dict[str, Any]]]:
        """Get tools grouped by category
        
        Returns:
            Dictionary of tools grouped by category
        """
        if not self.detected_tools:
            self.detect_all_tools()
        
        categorized = {}
        for tool in self.detected_tools.values():
            category = tool['category']
            if category not in categorized:
                categorized[category] = []
            categorized[category].append(tool)
        
        return categorized
    
    def is_tool_available(self, tool_name: str) -> bool:
        """Check if a specific tool is available
        
        Args:
            tool_name: Name of the tool to check
            
        Returns:
            True if tool is available
        """
        if not self.detected_tools:
            self.detect_all_tools()
        
        return self.detected_tools.get(tool_name, {}).get('available', False)
    
    def get_tool_info(self, tool_name: str) -> Optional[Dict[str, Any]]:
        """Get detailed information about a specific tool
        
        Args:
            tool_name: Name of the tool
            
        Returns:
            Tool information dictionary or None if not found
        """
        if not self.detected_tools:
            self.detect_all_tools()
        
        return self.detected_tools.get(tool_name)
    
    def refresh_detection(self, tool_name: str = None) -> None:
        """Refresh tool detection (clear cache)
        
        Args:
            tool_name: Specific tool to refresh, or None for all tools
        """
        if tool_name:
            self.detection_cache.pop(tool_name, None)
            if tool_name in self.detected_tools:
                del self.detected_tools[tool_name]
        else:
            self.detection_cache.clear()
            self.detected_tools.clear()
    
    def install_missing_tools(self, tools: List[str] = None) -> Dict[str, bool]:
        """Attempt to install missing tools
        
        Args:
            tools: List of tool names to install, or None for common tools
            
        Returns:
            Dictionary of installation results
        """
        if tools is None:
            tools = ['ffuf', 'dirb', 'masscan']  # Common missing tools
        
        installation_commands = {
            'ffuf': ['sudo', 'apt', 'install', '-y', 'ffuf'],
            'dirb': ['sudo', 'apt', 'install', '-y', 'dirb'],
            'masscan': ['sudo', 'apt', 'install', '-y', 'masscan'],
            'zmap': ['sudo', 'apt', 'install', '-y', 'zmap'],
            'testssl': ['git', 'clone', 'https://github.com/drwetter/testssl.sh.git', '/opt/testssl'],
            'sslyze': ['pip3', 'install', 'sslyze'],
            'wpscan': ['gem', 'install', 'wpscan']
        }
        
        results = {}
        
        for tool in tools:
            if tool in installation_commands:
                try:
                    logger.info(f"Installing {tool}...")
                    result = subprocess.run(
                        installation_commands[tool],
                        capture_output=True,
                        text=True,
                        timeout=300
                    )
                    
                    if result.returncode == 0:
                        logger.info(f"✅ Successfully installed {tool}")
                        results[tool] = True
                        # Refresh detection for this tool
                        self.refresh_detection(tool)
                    else:
                        logger.error(f"❌ Failed to install {tool}: {result.stderr}")
                        results[tool] = False
                        
                except Exception as e:
                    logger.error(f"❌ Error installing {tool}: {e}")
                    results[tool] = False
            else:
                logger.warning(f"No installation command available for {tool}")
                results[tool] = False
        
        return results

# Global tool detector instance
_real_tool_detector = None

def get_real_tool_detector() -> RealToolDetector:
    """Get global real tool detector instance
    
    Returns:
        RealToolDetector instance
    """
    global _real_tool_detector
    if _real_tool_detector is None:
        _real_tool_detector = RealToolDetector()
    return _real_tool_detector