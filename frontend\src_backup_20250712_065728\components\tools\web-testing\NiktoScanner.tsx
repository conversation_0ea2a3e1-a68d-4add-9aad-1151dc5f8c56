/**
 * Nikto Scanner Component
 * Web server vulnerability scanner for comprehensive security testing
 */
import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { 
  Shield,
  Play,
  Square,
  Download,
  Settings,
  Target,
  Globe,
  FileText,
  Terminal,
  Copy,
  RotateCcw,
  Clock,
  Zap,
  Filter,
  Eye,
  AlertTriangle,
  CheckCircle,
  TrendingUp,
  Activity,
  Lock,
  Search,
  List,
  BarChart3,
  Database,
  Server,
  Bug,
  Info
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useBackendStore } from '@/stores/backend-store';
import { apiClient, ToolExecution } from '@/services/api-client';
import { toast } from 'sonner';

/**
 * Nikto configuration interface
 */
interface NiktoConfig {
  host: string;
  port: number;
  ssl: boolean;
  scanTuning: string[];
  plugins: string[];
  evasion: string[];
  timeout: number;
  maxtime: number;
  userAgent: string;
  cookies: string;
  headers: { [key: string]: string };
  authentication: {
    enabled: boolean;
    username: string;
    password: string;
    realm: string;
    type: 'basic' | 'ntlm' | 'form';
  };
  proxy: string;
  output: {
    format: 'txt' | 'xml' | 'csv' | 'json' | 'htm';
    file: string;
  };
  checks: {
    cgi: boolean;
    headers: boolean;
    cookies: boolean;
    ssl: boolean;
    outdated: boolean;
    paths: boolean;
    mutations: boolean;
  };
  vhosts: string[];
  noInteractive: boolean;
  noDns: boolean;
  noSSLChecks: boolean;
  customArgs: string;
}

/**
 * Nikto scan tuning options
 */
const SCAN_TUNING = {
  '1': { name: 'Interesting File / Seen in logs', description: 'Files of interest' },
  '2': { name: 'Misconfiguration / Default File', description: 'Default and sample files' },
  '3': { name: 'Information Disclosure', description: 'Information leakage' },
  '4': { name: 'Injection (XSS/Script/HTML)', description: 'Injection flaws' },
  '5': { name: 'Remote File Retrieval - Inside Web Root', description: 'File inclusion' },
  '6': { name: 'Denial of Service', description: 'DoS conditions' },
  '7': { name: 'Remote File Retrieval - Server Wide', description: 'Remote file access' },
  '8': { name: 'Command Execution - Remote Shell', description: 'Command injection' },
  '9': { name: 'SQL Injection', description: 'SQL injection tests' },
  'a': { name: 'Authentication Bypass', description: 'Authentication issues' },
  'b': { name: 'Software Identification', description: 'Banner grabbing' },
  'c': { name: 'Remote Source Inclusion', description: 'Include vulnerabilities' },
  'x': { name: 'Reverse Tuning Options', description: 'Skip certain tests' }
};

/**
 * Nikto evasion techniques
 */
const EVASION_TECHNIQUES = {
  '1': { name: 'Random URI encoding', description: 'Encode URI with random hex' },
  '2': { name: 'Directory self-reference', description: 'Use ./ in paths' },
  '3': { name: 'Premature URL ending', description: 'Add fake parameters' },
  '4': { name: 'Prepend long random string', description: 'Long random URI prefix' },
  '5': { name: 'Fake parameter', description: 'Add fake CGI parameters' },
  '6': { name: 'TAB as request spacer', description: 'Use TAB instead of spaces' },
  '7': { name: 'Change the case', description: 'Random case changes' },
  '8': { name: 'Use Windows directory separator', description: 'Use backslashes' },
  '9': { name: 'Session splicing', description: 'Split requests across packets' },
  'A': { name: 'Use a carriage return', description: 'CR as line terminator' },
  'B': { name: 'Use binary value 0x0b as request spacer', description: 'Binary spacer' }
};

/**
 * Common plugins
 */
const COMMON_PLUGINS = [
  'apache_expect_xss',
  'auth',
  'content_search',
  'cookies',
  'embedded',
  'favicon',
  'headers',
  'httpoptions',
  'multiple_index',
  'origin_reflection',
  'parked',
  'paths',
  'put_del_test',
  'report_csv',
  'report_html',
  'report_nbe',
  'report_txt',
  'report_xml',
  'robots',
  'ssl',
  'subdomain',
  'tests'
];

/**
 * Vulnerability severity levels
 */
const SEVERITY_LEVELS = [
  { value: 'info', label: 'Info', color: 'blue', description: 'Informational findings' },
  { value: 'low', label: 'Low', color: 'green', description: 'Low risk issues' },
  { value: 'medium', label: 'Medium', color: 'yellow', description: 'Medium risk vulnerabilities' },
  { value: 'high', label: 'High', color: 'orange', description: 'High risk security flaws' },
  { value: 'critical', label: 'Critical', color: 'red', description: 'Critical vulnerabilities' }
];

/**
 * Nikto Scanner Component
 */
export function NiktoScanner() {
  // State management
  const [config, setConfig] = React.useState<NiktoConfig>({
    host: '',
    port: 80,
    ssl: false,
    scanTuning: ['1', '2', '3', '4', '5', '7', '8', '9', 'a', 'b'],
    plugins: ['tests', 'headers', 'cookies', 'paths'],
    evasion: [],
    timeout: 10,
    maxtime: 0,
    userAgent: '',
    cookies: '',
    headers: {},
    authentication: {
      enabled: false,
      username: '',
      password: '',
      realm: '',
      type: 'basic'
    },
    proxy: '',
    output: {
      format: 'txt',
      file: ''
    },
    checks: {
      cgi: true,
      headers: true,
      cookies: true,
      ssl: true,
      outdated: true,
      paths: true,
      mutations: true
    },
    vhosts: [],
    noInteractive: true,
    noDns: false,
    noSSLChecks: false,
    customArgs: ''
  });
  
  const [isScanning, setIsScanning] = React.useState(false);
  const [currentExecution, setCurrentExecution] = React.useState<ToolExecution | null>(null);
  const [progress, setProgress] = React.useState(0);
  const [output, setOutput] = React.useState<string[]>([]);
  const [results, setResults] = React.useState<any>(null);
  const [activeTab, setActiveTab] = React.useState('configure');
  const [vulnerabilityStats, setVulnerabilityStats] = React.useState<any>(null);
  const [newHeaderKey, setNewHeaderKey] = React.useState('');
  const [newHeaderValue, setNewHeaderValue] = React.useState('');
  const [newVhost, setNewVhost] = React.useState('');
  
  // Backend connection
  const { status: backendStatus } = useBackendStore();
  
  // Update configuration
  const updateConfig = (updates: Partial<NiktoConfig>) => {
    setConfig(prev => ({ ...prev, ...updates }));
  };
  
  // Update authentication
  const updateAuth = (updates: Partial<NiktoConfig['authentication']>) => {
    setConfig(prev => ({
      ...prev,
      authentication: { ...prev.authentication, ...updates }
    }));
  };
  
  // Update output settings
  const updateOutput = (updates: Partial<NiktoConfig['output']>) => {
    setConfig(prev => ({
      ...prev,
      output: { ...prev.output, ...updates }
    }));
  };
  
  // Update checks
  const updateChecks = (updates: Partial<NiktoConfig['checks']>) => {
    setConfig(prev => ({
      ...prev,
      checks: { ...prev.checks, ...updates }
    }));
  };
  
  // Toggle scan tuning option
  const toggleTuning = (option: string) => {
    if (config.scanTuning.includes(option)) {
      updateConfig({
        scanTuning: config.scanTuning.filter(t => t !== option)
      });
    } else {
      updateConfig({
        scanTuning: [...config.scanTuning, option]
      });
    }
  };
  
  // Toggle evasion technique
  const toggleEvasion = (technique: string) => {
    if (config.evasion.includes(technique)) {
      updateConfig({
        evasion: config.evasion.filter(e => e !== technique)
      });
    } else {
      updateConfig({
        evasion: [...config.evasion, technique]
      });
    }
  };
  
  // Toggle plugin
  const togglePlugin = (plugin: string) => {
    if (config.plugins.includes(plugin)) {
      updateConfig({
        plugins: config.plugins.filter(p => p !== plugin)
      });
    } else {
      updateConfig({
        plugins: [...config.plugins, plugin]
      });
    }
  };
  
  // Add custom header
  const addHeader = () => {
    if (newHeaderKey.trim() && newHeaderValue.trim()) {
      updateConfig({
        headers: {
          ...config.headers,
          [newHeaderKey.trim()]: newHeaderValue.trim()
        }
      });
      setNewHeaderKey('');
      setNewHeaderValue('');
      toast.success('Header added');
    }
  };
  
  // Remove header
  const removeHeader = (key: string) => {
    const { [key]: removed, ...remainingHeaders } = config.headers;
    updateConfig({ headers: remainingHeaders });
    toast.success('Header removed');
  };
  
  // Add virtual host
  const addVhost = () => {
    if (newVhost.trim() && !config.vhosts.includes(newVhost.trim())) {
      updateConfig({
        vhosts: [...config.vhosts, newVhost.trim()]
      });
      setNewVhost('');
      toast.success('Virtual host added');
    }
  };
  
  // Remove virtual host
  const removeVhost = (vhost: string) => {
    updateConfig({
      vhosts: config.vhosts.filter(v => v !== vhost)
    });
    toast.success('Virtual host removed');
  };
  
  // Update SSL mode based on port
  React.useEffect(() => {
    if (config.port === 443 && !config.ssl) {
      updateConfig({ ssl: true });
    } else if (config.port === 80 && config.ssl) {
      updateConfig({ ssl: false });
    }
  }, [config.port]);
  
  // Generate Nikto command
  const generateCommand = (): string => {
    let command = 'nikto';
    
    // Host and port
    command += ` -h ${config.host}`;
    if (config.port !== (config.ssl ? 443 : 80)) {
      command += `:${config.port}`;
    }
    
    // SSL
    if (config.ssl) {
      command += ` -ssl`;
    }
    
    // Scan tuning
    if (config.scanTuning.length > 0) {
      command += ` -Tuning ${config.scanTuning.join('')}`;
    }
    
    // Plugins
    if (config.plugins.length > 0) {
      command += ` -Plugins ${config.plugins.join(',')}`;
    }
    
    // Evasion
    if (config.evasion.length > 0) {
      command += ` -evasion ${config.evasion.join('')}`;
    }
    
    // Timeout
    if (config.timeout !== 10) {
      command += ` -timeout ${config.timeout}`;
    }
    
    // Max time
    if (config.maxtime > 0) {
      command += ` -maxtime ${config.maxtime}`;
    }
    
    // User Agent
    if (config.userAgent.trim()) {
      command += ` -useragent "${config.userAgent}"`;
    }
    
    // Cookies
    if (config.cookies.trim()) {
      command += ` -C "${config.cookies}"`;
    }
    
    // Headers
    Object.entries(config.headers).forEach(([key, value]) => {
      command += ` -H "${key}: ${value}"`;
    });
    
    // Authentication
    if (config.authentication.enabled && config.authentication.username) {
      command += ` -id ${config.authentication.username}:${config.authentication.password}`;
      if (config.authentication.realm) {
        command += `:${config.authentication.realm}`;
      }
      if (config.authentication.type === 'ntlm') {
        command += ` -authtype ntlm`;
      }
    }
    
    // Proxy
    if (config.proxy.trim()) {
      command += ` -useproxy ${config.proxy}`;
    }
    
    // Virtual hosts
    if (config.vhosts.length > 0) {
      config.vhosts.forEach(vhost => {
        command += ` -vhost ${vhost}`;
      });
    }
    
    // Output format
    if (config.output.format !== 'txt') {
      const outputFile = config.output.file || `nikto-output.${config.output.format}`;
      command += ` -Format ${config.output.format} -output ${outputFile}`;
    }
    
    // Options
    if (config.noInteractive) {
      command += ` -nointeractive`;
    }
    
    if (config.noDns) {
      command += ` -nolookup`;
    }
    
    if (config.noSSLChecks) {
      command += ` -nossl`;
    }
    
    // Custom arguments
    if (config.customArgs.trim()) {
      command += ` ${config.customArgs.trim()}`;
    }
    
    return command;
  };
  
  // Start scan
  const startScan = async () => {
    if (!config.host.trim()) {
      toast.error('Please specify a target host');
      return;
    }
    
    if (!backendStatus.connected) {
      toast.error('Backend not connected');
      return;
    }
    
    try {
      setIsScanning(true);
      setProgress(0);
      setOutput([]);
      setResults(null);
      setVulnerabilityStats(null);
      setActiveTab('output');
      
      const execution = await apiClient.executeTool('nikto', config, (progressUpdate) => {
        setCurrentExecution(progressUpdate);
        setProgress(progressUpdate.progress);
        if (progressUpdate.output) {
          setOutput(prev => [...prev, ...progressUpdate.output!]);
        }
        
        if (progressUpdate.status === 'completed') {
          setIsScanning(false);
          setResults(progressUpdate.results);
          
          // Parse vulnerability statistics
          if (progressUpdate.results?.vulnerabilities) {
            const stats = progressUpdate.results.vulnerabilities.reduce((acc: any, vuln: any) => {
              const severity = vuln.severity || 'info';
              acc[severity] = (acc[severity] || 0) + 1;
              return acc;
            }, {});
            stats.total = progressUpdate.results.vulnerabilities.length;
            setVulnerabilityStats(stats);
          }
          
          toast.success('Nikto scan completed');
        } else if (progressUpdate.status === 'failed') {
          setIsScanning(false);
          toast.error(`Scan failed: ${progressUpdate.error}`);
        }
      });
      
      setCurrentExecution(execution);
      toast.info('Nikto scan started');
    } catch (error) {
      setIsScanning(false);
      console.error('Failed to start Nikto scan:', error);
      toast.error('Failed to start scan');
    }
  };
  
  // Stop scan
  const stopScan = async () => {
    if (currentExecution) {
      try {
        await apiClient.cancelExecution(currentExecution.id);
        setIsScanning(false);
        setCurrentExecution(null);
        toast.info('Scan cancelled');
      } catch (error) {
        console.error('Failed to stop scan:', error);
        toast.error('Failed to stop scan');
      }
    }
  };
  
  // Copy command to clipboard
  const copyCommand = async () => {
    try {
      await navigator.clipboard.writeText(generateCommand());
      toast.success('Command copied to clipboard');
    } catch (error) {
      toast.error('Failed to copy command');
    }
  };
  
  // Export results
  const exportResults = () => {
    if (!results && output.length === 0) {
      toast.error('No results to export');
      return;
    }
    
    const data = results || output.join('\n');
    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `nikto-scan-${new Date().toISOString().slice(0, 19)}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    
    toast.success('Results exported');
  };
  
  // Get severity color
  const getSeverityColor = (severity: string) => {
    const level = SEVERITY_LEVELS.find(s => s.value === severity);
    return level?.color || 'gray';
  };
  
  return (
    <div className="container-desktop py-6 space-y-6">
      {/* Tool Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className="p-2 bg-orange-100 dark:bg-orange-900/20 rounded-lg">
            <Shield className="h-6 w-6 text-orange-600 dark:text-orange-400" />
          </div>
          <div>
            <h1 className="text-2xl font-bold">Nikto Scanner</h1>
            <p className="text-muted-foreground">
              Web server vulnerability scanner for comprehensive security testing
            </p>
          </div>
        </div>
        
        {/* Status indicators */}
        <div className="flex items-center gap-2">
          <Badge variant={backendStatus.connected ? 'default' : 'destructive'}>
            {backendStatus.connected ? 'Connected' : 'Offline'}
          </Badge>
          {isScanning && (
            <Badge variant="secondary" className="animate-pulse">
              Scanning...
            </Badge>
          )}
        </div>
      </div>
      
      {/* Main Interface */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="configure" className="flex items-center gap-2">
            <Settings className="h-4 w-4" />
            Configure
          </TabsTrigger>
          <TabsTrigger value="output" className="flex items-center gap-2">
            <Terminal className="h-4 w-4" />
            Output
          </TabsTrigger>
          <TabsTrigger value="results" className="flex items-center gap-2">
            <FileText className="h-4 w-4" />
            Results
          </TabsTrigger>
          <TabsTrigger value="command" className="flex items-center gap-2">
            <Target className="h-4 w-4" />
            Command
          </TabsTrigger>
        </TabsList>
        
        {/* Configuration Tab */}
        <TabsContent value="configure" className="space-y-6">
          <div className="grid gap-6 lg:grid-cols-2">
            {/* Target Configuration */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Target className="h-5 w-5" />
                  Target Configuration
                </CardTitle>
                <CardDescription>
                  Specify the target web server for vulnerability scanning
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Host */}
                <div className="space-y-2">
                  <label className="text-sm font-medium">Target Host</label>
                  <Input
                    placeholder="e.g., example.com, *************"
                    value={config.host}
                    onChange={(e) => updateConfig({ host: e.target.value })}
                  />
                  <p className="text-xs text-muted-foreground">
                    Hostname or IP address of the target web server
                  </p>
                </div>
                
                <div className="grid gap-4 md:grid-cols-2">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Port</label>
                    <Input
                      type="number"
                      min="1"
                      max="65535"
                      value={config.port}
                      onChange={(e) => updateConfig({ port: parseInt(e.target.value) || 80 })}
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Protocol</label>
                    <div className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        checked={config.ssl}
                        onChange={(e) => updateConfig({ ssl: e.target.checked })}
                        className="rounded border-gray-300"
                      />
                      <span className="text-sm">Use SSL/HTTPS</span>
                    </div>
                  </div>
                </div>
                
                {/* Virtual Hosts */}
                <div className="space-y-2">
                  <label className="text-sm font-medium">Virtual Hosts (Optional)</label>
                  <div className="space-y-2">
                    {config.vhosts.map((vhost, index) => (
                      <div key={index} className="flex items-center gap-2 p-2 border rounded">
                        <Globe className="h-4 w-4 text-muted-foreground" />
                        <span className="text-sm flex-1">{vhost}</span>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => removeVhost(vhost)}
                        >
                          ×
                        </Button>
                      </div>
                    ))}
                    
                    <div className="flex gap-2">
                      <Input
                        placeholder="Virtual host name"
                        value={newVhost}
                        onChange={(e) => setNewVhost(e.target.value)}
                      />
                      <Button onClick={addVhost} disabled={!newVhost.trim()}>
                        Add
                      </Button>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            {/* Performance Options */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <TrendingUp className="h-5 w-5" />
                  Performance & Timing
                </CardTitle>
                <CardDescription>
                  Configure scanning performance and timing options
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid gap-4 md:grid-cols-2">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Timeout (seconds)</label>
                    <Input
                      type="number"
                      min="1"
                      max="300"
                      value={config.timeout}
                      onChange={(e) => updateConfig({ timeout: parseInt(e.target.value) || 10 })}
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Max Time (seconds)</label>
                    <Input
                      type="number"
                      min="0"
                      max="3600"
                      value={config.maxtime}
                      onChange={(e) => updateConfig({ maxtime: parseInt(e.target.value) || 0 })}
                      placeholder="0 = unlimited"
                    />
                  </div>
                </div>
                
                <div className="space-y-3">
                  <label className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      checked={config.noInteractive}
                      onChange={(e) => updateConfig({ noInteractive: e.target.checked })}
                      className="rounded border-gray-300"
                    />
                    <span className="text-sm">No Interactive Mode</span>
                  </label>
                  
                  <label className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      checked={config.noDns}
                      onChange={(e) => updateConfig({ noDns: e.target.checked })}
                      className="rounded border-gray-300"
                    />
                    <span className="text-sm">Skip DNS Lookups</span>
                  </label>
                  
                  <label className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      checked={config.noSSLChecks}
                      onChange={(e) => updateConfig({ noSSLChecks: e.target.checked })}
                      className="rounded border-gray-300"
                    />
                    <span className="text-sm">Skip SSL Certificate Checks</span>
                  </label>
                </div>
              </CardContent>
            </Card>
          </div>
          
          {/* Scan Tuning */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Filter className="h-5 w-5" />
                Scan Tuning
              </CardTitle>
              <CardDescription>
                Select specific vulnerability test categories to run
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-3 md:grid-cols-2 lg:grid-cols-3">
                {Object.entries(SCAN_TUNING).map(([key, tuning]) => (
                  <div
                    key={key}
                    className={cn(
                      'p-3 border rounded-lg cursor-pointer transition-all',
                      config.scanTuning.includes(key)
                        ? 'border-primary bg-primary/5'
                        : 'border-border hover:border-primary/50'
                    )}
                    onClick={() => toggleTuning(key)}
                  >
                    <div className="flex items-start gap-3">
                      <div className="mt-1">
                        <input
                          type="checkbox"
                          checked={config.scanTuning.includes(key)}
                          onChange={() => toggleTuning(key)}
                          className="rounded border-gray-300"
                        />
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="text-sm font-medium">{key}: {tuning.name}</div>
                        <p className="text-xs text-muted-foreground mt-1">{tuning.description}</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
          
          {/* Plugins */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Database className="h-5 w-5" />
                Plugins
              </CardTitle>
              <CardDescription>
                Select specific Nikto plugins to run during the scan
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-2 md:grid-cols-3 lg:grid-cols-4">
                {COMMON_PLUGINS.map((plugin) => (
                  <label key={plugin} className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      checked={config.plugins.includes(plugin)}
                      onChange={() => togglePlugin(plugin)}
                      className="rounded border-gray-300"
                    />
                    <span className="text-sm">{plugin}</span>
                  </label>
                ))}
              </div>
            </CardContent>
          </Card>
          
          {/* Evasion Techniques */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Eye className="h-5 w-5" />
                Evasion Techniques
              </CardTitle>
              <CardDescription>
                Configure evasion techniques to bypass security controls
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-3 md:grid-cols-2">
                {Object.entries(EVASION_TECHNIQUES).map(([key, technique]) => (
                  <div
                    key={key}
                    className={cn(
                      'p-3 border rounded-lg cursor-pointer transition-all',
                      config.evasion.includes(key)
                        ? 'border-primary bg-primary/5'
                        : 'border-border hover:border-primary/50'
                    )}
                    onClick={() => toggleEvasion(key)}
                  >
                    <div className="flex items-start gap-3">
                      <div className="mt-1">
                        <input
                          type="checkbox"
                          checked={config.evasion.includes(key)}
                          onChange={() => toggleEvasion(key)}
                          className="rounded border-gray-300"
                        />
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="text-sm font-medium">{key}: {technique.name}</div>
                        <p className="text-xs text-muted-foreground mt-1">{technique.description}</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
          
          {/* HTTP Configuration */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Globe className="h-5 w-5" />
                HTTP Configuration
              </CardTitle>
              <CardDescription>
                Configure HTTP headers, authentication, and request parameters
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-2">
                  <label className="text-sm font-medium">User Agent</label>
                  <Input
                    placeholder="Custom User-Agent string"
                    value={config.userAgent}
                    onChange={(e) => updateConfig({ userAgent: e.target.value })}
                  />
                </div>
                
                <div className="space-y-2">
                  <label className="text-sm font-medium">Cookies</label>
                  <Input
                    placeholder="cookie1=value1; cookie2=value2"
                    value={config.cookies}
                    onChange={(e) => updateConfig({ cookies: e.target.value })}
                  />
                </div>
              </div>
              
              {/* Custom Headers */}
              <div className="space-y-2">
                <label className="text-sm font-medium">Custom Headers</label>
                <div className="space-y-2">
                  {Object.entries(config.headers).map(([key, value]) => (
                    <div key={key} className="flex items-center gap-2 p-2 border rounded">
                      <span className="text-sm font-mono">{key}:</span>
                      <span className="text-sm flex-1">{value}</span>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => removeHeader(key)}
                      >
                        ×
                      </Button>
                    </div>
                  ))}
                  
                  <div className="flex gap-2">
                    <Input
                      placeholder="Header name"
                      value={newHeaderKey}
                      onChange={(e) => setNewHeaderKey(e.target.value)}
                    />
                    <Input
                      placeholder="Header value"
                      value={newHeaderValue}
                      onChange={(e) => setNewHeaderValue(e.target.value)}
                    />
                    <Button onClick={addHeader} disabled={!newHeaderKey.trim() || !newHeaderValue.trim()}>
                      Add
                    </Button>
                  </div>
                </div>
              </div>
              
              <div className="space-y-2">
                <label className="text-sm font-medium">Proxy (Optional)</label>
                <Input
                  placeholder="http://proxy:8080"
                  value={config.proxy}
                  onChange={(e) => updateConfig({ proxy: e.target.value })}
                />
              </div>
            </CardContent>
          </Card>
          
          {/* Authentication */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Lock className="h-5 w-5" />
                Authentication
              </CardTitle>
              <CardDescription>
                Configure HTTP authentication if required
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={config.authentication.enabled}
                  onChange={(e) => updateAuth({ enabled: e.target.checked })}
                  className="rounded border-gray-300"
                />
                <span className="text-sm font-medium">Enable Authentication</span>
              </div>
              
              {config.authentication.enabled && (
                <div className="grid gap-4 md:grid-cols-2">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Type</label>
                    <Select 
                      value={config.authentication.type} 
                      onValueChange={(value) => updateAuth({ type: value as any })}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="basic">Basic Auth</SelectItem>
                        <SelectItem value="ntlm">NTLM Auth</SelectItem>
                        <SelectItem value="form">Form Auth</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Realm (Optional)</label>
                    <Input
                      placeholder="Authentication realm"
                      value={config.authentication.realm}
                      onChange={(e) => updateAuth({ realm: e.target.value })}
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Username</label>
                    <Input
                      placeholder="Username"
                      value={config.authentication.username}
                      onChange={(e) => updateAuth({ username: e.target.value })}
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Password</label>
                    <Input
                      type="password"
                      placeholder="Password"
                      value={config.authentication.password}
                      onChange={(e) => updateAuth({ password: e.target.value })}
                    />
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
          
          {/* Output Configuration */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Output Configuration
              </CardTitle>
              <CardDescription>
                Configure output format and file options
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-2">
                  <label className="text-sm font-medium">Output Format</label>
                  <Select 
                    value={config.output.format} 
                    onValueChange={(value) => updateOutput({ format: value as any })}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="txt">Text Format</SelectItem>
                      <SelectItem value="xml">XML Format</SelectItem>
                      <SelectItem value="csv">CSV Format</SelectItem>
                      <SelectItem value="json">JSON Format</SelectItem>
                      <SelectItem value="htm">HTML Format</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="space-y-2">
                  <label className="text-sm font-medium">Output File (Optional)</label>
                  <Input
                    placeholder="Custom output filename"
                    value={config.output.file}
                    onChange={(e) => updateOutput({ file: e.target.value })}
                  />
                </div>
              </div>
              
              <div className="space-y-2">
                <label className="text-sm font-medium">Custom Arguments</label>
                <Input
                  placeholder="Additional Nikto arguments"
                  value={config.customArgs}
                  onChange={(e) => updateConfig({ customArgs: e.target.value })}
                />
              </div>
            </CardContent>
          </Card>
          
          {/* Actions */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Activity className="h-5 w-5" />
                Start Scan
              </CardTitle>
              <CardDescription>
                Execute the Nikto vulnerability scan with your configured parameters
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between">
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={copyCommand}
                  >
                    <Copy className="h-4 w-4 mr-1" />
                    Copy Command
                  </Button>
                </div>
                
                <div className="flex gap-2">
                  {isScanning ? (
                    <Button
                      variant="destructive"
                      onClick={stopScan}
                    >
                      <Square className="h-4 w-4 mr-2" />
                      Stop Scan
                    </Button>
                  ) : (
                    <Button
                      onClick={startScan}
                      disabled={!config.host.trim() || !backendStatus.connected}
                    >
                      <Play className="h-4 w-4 mr-2" />
                      Start Vulnerability Scan
                    </Button>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        {/* Output Tab */}
        <TabsContent value="output" className="space-y-6">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="flex items-center gap-2">
                    <Terminal className="h-5 w-5" />
                    Real-time Output
                  </CardTitle>
                  <CardDescription>
                    Live output from the Nikto vulnerability scan
                  </CardDescription>
                </div>
                
                {/* Progress and controls */}
                <div className="flex items-center gap-4">
                  {isScanning && (
                    <div className="flex items-center gap-2">
                      <Progress value={progress} className="w-32" />
                      <span className="text-sm text-muted-foreground">
                        {progress}%
                      </span>
                    </div>
                  )}
                  
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setOutput([])}
                    disabled={isScanning}
                  >
                    <RotateCcw className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="bg-gray-900 text-gray-100 p-4 rounded-lg h-96 overflow-y-auto font-mono text-sm">
                {output.length === 0 ? (
                  <div className="text-gray-500 italic">
                    {isScanning ? 'Waiting for output...' : 'No output yet. Start a scan to see results here.'}
                  </div>
                ) : (
                  output.map((line, index) => (
                    <div key={index} className="mb-1">
                      {line}
                    </div>
                  ))
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        {/* Results Tab */}
        <TabsContent value="results" className="space-y-6">
          {/* Vulnerability Statistics */}
          {vulnerabilityStats && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BarChart3 className="h-5 w-5" />
                  Vulnerability Summary
                </CardTitle>
                <CardDescription>
                  Overview of discovered vulnerabilities by severity
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid gap-4 md:grid-cols-6">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-purple-600">{vulnerabilityStats.total || 0}</div>
                    <div className="text-sm text-muted-foreground">Total</div>
                  </div>
                  {SEVERITY_LEVELS.map((level) => (
                    <div key={level.value} className="text-center">
                      <div className={cn(
                        'text-2xl font-bold',
                        level.color === 'blue' && 'text-blue-600',
                        level.color === 'green' && 'text-green-600',
                        level.color === 'yellow' && 'text-yellow-600',
                        level.color === 'orange' && 'text-orange-600',
                        level.color === 'red' && 'text-red-600'
                      )}>
                        {vulnerabilityStats[level.value] || 0}
                      </div>
                      <div className="text-sm text-muted-foreground">{level.label}</div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
          
          {/* Detailed Results */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="flex items-center gap-2">
                    <FileText className="h-5 w-5" />
                    Scan Results
                  </CardTitle>
                  <CardDescription>
                    Detailed vulnerability findings from the Nikto scan
                  </CardDescription>
                </div>
                
                <Button
                  variant="outline"
                  onClick={exportResults}
                  disabled={!results && output.length === 0}
                >
                  <Download className="h-4 w-4 mr-2" />
                  Export Results
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              {results ? (
                <div className="space-y-4">
                  <pre className="bg-muted p-4 rounded-lg overflow-auto text-sm max-h-96">
                    {JSON.stringify(results, null, 2)}
                  </pre>
                </div>
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  <Shield className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>No vulnerability results available yet.</p>
                  <p className="text-sm">Complete a scan to see detailed vulnerability findings here.</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
        
        {/* Command Tab */}
        <TabsContent value="command" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Target className="h-5 w-5" />
                Generated Command
              </CardTitle>
              <CardDescription>
                The Nikto command that will be executed based on your configuration
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="bg-muted p-4 rounded-lg">
                  <code className="text-sm font-mono break-all">
                    {generateCommand()}
                  </code>
                </div>
                
                <div className="flex justify-between items-center">
                  <p className="text-sm text-muted-foreground">
                    This command will be executed on the backend server
                  </p>
                  
                  <Button variant="outline" onClick={copyCommand}>
                    <Copy className="h-4 w-4 mr-2" />
                    Copy Command
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}