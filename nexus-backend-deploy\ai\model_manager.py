#!/usr/bin/env python3
"""
AI Model Manager
Advanced model lifecycle management, version control, A/B testing,
and performance optimization for AI models
"""

import asyncio
import json
import logging
import time
import hashlib
import os
from typing import Any, Dict, List, Optional, Tuple, Callable, Union
from dataclasses import dataclass, field, asdict
from datetime import datetime, timed<PERSON><PERSON>
from enum import Enum
from pathlib import Path
import pickle
import threading
from abc import ABC, abstractmethod

logger = logging.getLogger(__name__)

class ModelStatus(Enum):
    """Model deployment status"""
    TRAINING = "training"
    VALIDATING = "validating"
    STAGING = "staging"
    PRODUCTION = "production"
    DEPRECATED = "deprecated"
    FAILED = "failed"

class ModelType(Enum):
    """AI model types"""
    LANGUAGE_MODEL = "language_model"
    CLASSIFICATION = "classification"
    REGRESSION = "regression"
    EMBEDDING = "embedding"
    FINE_TUNED = "fine_tuned"
    ENSEMBLE = "ensemble"

class DeploymentStrategy(Enum):
    """Model deployment strategies"""
    BLUE_GREEN = "blue_green"
    CANARY = "canary"
    ROLLING = "rolling"
    A_B_TEST = "a_b_test"
    SHADOW = "shadow"

@dataclass
class ModelMetrics:
    """Model performance metrics"""
    accuracy: float = 0.0
    precision: float = 0.0
    recall: float = 0.0
    f1_score: float = 0.0
    latency_ms: float = 0.0
    throughput_rps: float = 0.0
    memory_usage_mb: float = 0.0
    cpu_usage_percent: float = 0.0
    error_rate: float = 0.0
    cost_per_request: float = 0.0
    user_satisfaction: float = 0.0
    
    def to_dict(self) -> Dict[str, float]:
        """Convert to dictionary"""
        return asdict(self)

@dataclass
class ModelVersion:
    """Model version information"""
    model_id: str
    version: str
    model_type: ModelType
    status: ModelStatus
    created_at: datetime
    metrics: ModelMetrics
    metadata: Dict[str, Any] = field(default_factory=dict)
    config: Dict[str, Any] = field(default_factory=dict)
    training_data_hash: Optional[str] = None
    model_size_mb: float = 0.0
    deployment_count: int = 0
    last_used: Optional[datetime] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        result = asdict(self)
        result['created_at'] = self.created_at.isoformat()
        result['last_used'] = self.last_used.isoformat() if self.last_used else None
        result['status'] = self.status.value
        result['model_type'] = self.model_type.value
        return result

@dataclass
class ABTestConfig:
    """A/B testing configuration"""
    test_id: str
    model_a_version: str
    model_b_version: str
    traffic_split: float = 0.5  # Percentage to model B
    success_metric: str = "f1_score"
    confidence_threshold: float = 0.95
    min_samples: int = 1000
    max_duration_hours: int = 168  # 7 days
    started_at: Optional[datetime] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        result = asdict(self)
        result['started_at'] = self.started_at.isoformat() if self.started_at else None
        return result

@dataclass
class ABTestResult:
    """A/B test results"""
    test_id: str
    model_a_metrics: ModelMetrics
    model_b_metrics: ModelMetrics
    samples_a: int
    samples_b: int
    confidence_level: float
    p_value: float
    winner: Optional[str] = None
    recommendation: str = ""
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return asdict(self)

class BaseModel(ABC):
    """Base class for AI models"""
    
    def __init__(self, model_id: str, version: str, config: Dict[str, Any]):
        self.model_id = model_id
        self.version = version
        self.config = config
        self.metrics = ModelMetrics()
        self.is_loaded = False
        self.last_prediction_time = None
        self._lock = threading.RLock()
    
    @abstractmethod
    async def load(self) -> bool:
        """Load model into memory"""
        pass
    
    @abstractmethod
    async def unload(self) -> bool:
        """Unload model from memory"""
        pass
    
    @abstractmethod
    async def predict(self, input_data: Any) -> Any:
        """Make prediction"""
        pass
    
    @abstractmethod
    async def evaluate(self, test_data: Any) -> ModelMetrics:
        """Evaluate model performance"""
        pass
    
    def update_metrics(self, latency: float, success: bool):
        """Update model metrics"""
        with self._lock:
            # Update latency
            if self.metrics.latency_ms == 0:
                self.metrics.latency_ms = latency * 1000
            else:
                self.metrics.latency_ms = (self.metrics.latency_ms * 0.9 + latency * 1000 * 0.1)
            
            # Update error rate
            if not success:
                self.metrics.error_rate = (self.metrics.error_rate * 0.9 + 0.1)
            else:
                self.metrics.error_rate = self.metrics.error_rate * 0.9
            
            self.last_prediction_time = datetime.now()

class LanguageModel(BaseModel):
    """Language model implementation"""
    
    def __init__(self, model_id: str, version: str, config: Dict[str, Any]):
        super().__init__(model_id, version, config)
        self.model_data = None
    
    async def load(self) -> bool:
        """Load language model"""
        try:
            # Simulate model loading
            await asyncio.sleep(0.5)
            self.model_data = {"loaded": True, "model_size": "7B"}
            self.is_loaded = True
            self.metrics.memory_usage_mb = 8000.0  # Simulate 8GB model
            logger.info(f"Loaded language model {self.model_id} v{self.version}")
            return True
        except Exception as e:
            logger.error(f"Failed to load model {self.model_id}: {e}")
            return False
    
    async def unload(self) -> bool:
        """Unload language model"""
        try:
            self.model_data = None
            self.is_loaded = False
            self.metrics.memory_usage_mb = 0.0
            logger.info(f"Unloaded language model {self.model_id} v{self.version}")
            return True
        except Exception as e:
            logger.error(f"Failed to unload model {self.model_id}: {e}")
            return False
    
    async def predict(self, input_data: Any) -> Any:
        """Generate text prediction"""
        if not self.is_loaded:
            raise RuntimeError(f"Model {self.model_id} is not loaded")
        
        start_time = time.time()
        
        try:
            # Simulate text generation
            await asyncio.sleep(0.1)
            
            prompt = input_data.get("prompt", "")
            max_tokens = input_data.get("max_tokens", 100)
            
            # Mock response
            response = f"Generated response for: {prompt[:50]}... (max_tokens: {max_tokens})"
            
            latency = time.time() - start_time
            self.update_metrics(latency, True)
            
            return {
                "text": response,
                "tokens_generated": max_tokens,
                "latency": latency
            }
            
        except Exception as e:
            latency = time.time() - start_time
            self.update_metrics(latency, False)
            raise
    
    async def evaluate(self, test_data: Any) -> ModelMetrics:
        """Evaluate language model"""
        # Simulate evaluation
        await asyncio.sleep(1.0)
        
        # Mock evaluation metrics
        metrics = ModelMetrics(
            accuracy=0.85,
            precision=0.82,
            recall=0.88,
            f1_score=0.85,
            latency_ms=self.metrics.latency_ms,
            throughput_rps=10.0,
            memory_usage_mb=self.metrics.memory_usage_mb,
            error_rate=self.metrics.error_rate
        )
        
        return metrics

class ClassificationModel(BaseModel):
    """Classification model implementation"""
    
    async def load(self) -> bool:
        """Load classification model"""
        try:
            await asyncio.sleep(0.2)
            self.is_loaded = True
            self.metrics.memory_usage_mb = 500.0  # Smaller model
            logger.info(f"Loaded classification model {self.model_id} v{self.version}")
            return True
        except Exception as e:
            logger.error(f"Failed to load classification model {self.model_id}: {e}")
            return False
    
    async def unload(self) -> bool:
        """Unload classification model"""
        self.is_loaded = False
        self.metrics.memory_usage_mb = 0.0
        return True
    
    async def predict(self, input_data: Any) -> Any:
        """Make classification prediction"""
        if not self.is_loaded:
            raise RuntimeError(f"Model {self.model_id} is not loaded")
        
        start_time = time.time()
        
        try:
            # Simulate classification
            await asyncio.sleep(0.05)
            
            # Mock classification result
            import random
            classes = ["malware", "benign", "suspicious"]
            predicted_class = random.choice(classes)
            confidence = random.uniform(0.7, 0.95)
            
            latency = time.time() - start_time
            self.update_metrics(latency, True)
            
            return {
                "class": predicted_class,
                "confidence": confidence,
                "probabilities": {cls: random.uniform(0.1, 0.9) for cls in classes}
            }
            
        except Exception as e:
            latency = time.time() - start_time
            self.update_metrics(latency, False)
            raise
    
    async def evaluate(self, test_data: Any) -> ModelMetrics:
        """Evaluate classification model"""
        await asyncio.sleep(0.5)
        
        return ModelMetrics(
            accuracy=0.92,
            precision=0.89,
            recall=0.94,
            f1_score=0.91,
            latency_ms=self.metrics.latency_ms,
            throughput_rps=50.0,
            memory_usage_mb=self.metrics.memory_usage_mb,
            error_rate=self.metrics.error_rate
        )

class ModelManager:
    """AI model lifecycle manager"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        self.config = config or {}
        self.models: Dict[str, Dict[str, BaseModel]] = {}  # model_id -> {version -> model}
        self.model_versions: Dict[str, List[ModelVersion]] = {}  # model_id -> [versions]
        self.active_deployments: Dict[str, str] = {}  # model_id -> active_version
        self.ab_tests: Dict[str, ABTestConfig] = {}
        self.ab_test_results: Dict[str, ABTestResult] = {}
        
        # Model registry
        self.model_registry_path = Path(self.config.get('registry_path', 'models/registry'))
        self.model_registry_path.mkdir(parents=True, exist_ok=True)
        
        # Resource management
        self.max_loaded_models = self.config.get('max_loaded_models', 10)
        self.auto_unload_timeout = self.config.get('auto_unload_timeout', 3600)  # 1 hour
        
        # Background tasks
        self._lock = threading.RLock()
        asyncio.create_task(self._model_cleanup_task())
        asyncio.create_task(self._ab_test_monitor())
        
        # Load existing registry
        self._load_registry()
    
    def _load_registry(self):
        """Load model registry from disk"""
        registry_file = self.model_registry_path / "registry.json"
        
        if registry_file.exists():
            try:
                with open(registry_file, 'r') as f:
                    registry_data = json.load(f)
                
                for model_id, versions_data in registry_data.items():
                    self.model_versions[model_id] = []
                    for version_data in versions_data:
                        version = ModelVersion(
                            model_id=version_data['model_id'],
                            version=version_data['version'],
                            model_type=ModelType(version_data['model_type']),
                            status=ModelStatus(version_data['status']),
                            created_at=datetime.fromisoformat(version_data['created_at']),
                            metrics=ModelMetrics(**version_data['metrics']),
                            metadata=version_data.get('metadata', {}),
                            config=version_data.get('config', {}),
                            training_data_hash=version_data.get('training_data_hash'),
                            model_size_mb=version_data.get('model_size_mb', 0.0)
                        )
                        if version_data.get('last_used'):
                            version.last_used = datetime.fromisoformat(version_data['last_used'])
                        
                        self.model_versions[model_id].append(version)
                
                logger.info(f"Loaded registry with {len(self.model_versions)} models")
                
            except Exception as e:
                logger.error(f"Failed to load model registry: {e}")
    
    def _save_registry(self):
        """Save model registry to disk"""
        registry_file = self.model_registry_path / "registry.json"
        
        try:
            registry_data = {}
            for model_id, versions in self.model_versions.items():
                registry_data[model_id] = [version.to_dict() for version in versions]
            
            with open(registry_file, 'w') as f:
                json.dump(registry_data, f, indent=2)
                
        except Exception as e:
            logger.error(f"Failed to save model registry: {e}")
    
    async def register_model(self, 
                           model_id: str,
                           version: str,
                           model_type: ModelType,
                           config: Dict[str, Any],
                           metadata: Optional[Dict[str, Any]] = None) -> bool:
        """Register new model version"""
        
        with self._lock:
            # Create model version
            model_version = ModelVersion(
                model_id=model_id,
                version=version,
                model_type=model_type,
                status=ModelStatus.STAGING,
                created_at=datetime.now(),
                metrics=ModelMetrics(),
                metadata=metadata or {},
                config=config
            )
            
            # Add to registry
            if model_id not in self.model_versions:
                self.model_versions[model_id] = []
            
            # Check if version already exists
            existing_versions = [v.version for v in self.model_versions[model_id]]
            if version in existing_versions:
                logger.warning(f"Model version {model_id}:{version} already exists")
                return False
            
            self.model_versions[model_id].append(model_version)
            
            # Create model instance
            model_instance = self._create_model_instance(model_id, version, model_type, config)
            
            if model_id not in self.models:
                self.models[model_id] = {}
            
            self.models[model_id][version] = model_instance
            
            # Save registry
            self._save_registry()
            
            logger.info(f"Registered model {model_id}:{version}")
            return True
    
    def _create_model_instance(self, 
                             model_id: str, 
                             version: str, 
                             model_type: ModelType, 
                             config: Dict[str, Any]) -> BaseModel:
        """Create model instance based on type"""
        
        if model_type == ModelType.LANGUAGE_MODEL:
            return LanguageModel(model_id, version, config)
        elif model_type == ModelType.CLASSIFICATION:
            return ClassificationModel(model_id, version, config)
        else:
            # Default to base model
            return LanguageModel(model_id, version, config)
    
    async def deploy_model(self, 
                         model_id: str, 
                         version: str,
                         strategy: DeploymentStrategy = DeploymentStrategy.BLUE_GREEN) -> bool:
        """Deploy model version to production"""
        
        if model_id not in self.models or version not in self.models[model_id]:
            logger.error(f"Model {model_id}:{version} not found")
            return False
        
        model = self.models[model_id][version]
        
        # Load model if not already loaded
        if not model.is_loaded:
            success = await model.load()
            if not success:
                return False
        
        # Update model status
        with self._lock:
            for model_version in self.model_versions[model_id]:
                if model_version.version == version:
                    model_version.status = ModelStatus.PRODUCTION
                    model_version.deployment_count += 1
                    break
            
            # Set as active deployment
            old_version = self.active_deployments.get(model_id)
            self.active_deployments[model_id] = version
            
            # Handle deployment strategy
            if strategy == DeploymentStrategy.BLUE_GREEN and old_version:
                # Unload old version after successful deployment
                if old_version in self.models[model_id]:
                    await self.models[model_id][old_version].unload()
                    # Update old version status
                    for model_version in self.model_versions[model_id]:
                        if model_version.version == old_version:
                            model_version.status = ModelStatus.DEPRECATED
                            break
        
        self._save_registry()
        logger.info(f"Deployed model {model_id}:{version} using {strategy.value} strategy")
        return True
    
    async def predict(self, model_id: str, input_data: Any, version: Optional[str] = None) -> Any:
        """Make prediction using model"""
        
        # Use active version if not specified
        if version is None:
            version = self.active_deployments.get(model_id)
            if not version:
                raise ValueError(f"No active deployment for model {model_id}")
        
        if model_id not in self.models or version not in self.models[model_id]:
            raise ValueError(f"Model {model_id}:{version} not found")
        
        model = self.models[model_id][version]
        
        # Check if model is in A/B test
        ab_test = self._get_active_ab_test(model_id)
        if ab_test:
            # Route to A/B test
            return await self._ab_test_predict(ab_test, input_data)
        
        # Regular prediction
        result = await model.predict(input_data)
        
        # Update last used time
        with self._lock:
            for model_version in self.model_versions[model_id]:
                if model_version.version == version:
                    model_version.last_used = datetime.now()
                    break
        
        return result
    
    async def evaluate_model(self, model_id: str, version: str, test_data: Any) -> ModelMetrics:
        """Evaluate model performance"""
        
        if model_id not in self.models or version not in self.models[model_id]:
            raise ValueError(f"Model {model_id}:{version} not found")
        
        model = self.models[model_id][version]
        
        # Load model if needed
        if not model.is_loaded:
            await model.load()
        
        # Evaluate
        metrics = await model.evaluate(test_data)
        
        # Update model version metrics
        with self._lock:
            for model_version in self.model_versions[model_id]:
                if model_version.version == version:
                    model_version.metrics = metrics
                    break
        
        self._save_registry()
        return metrics
    
    async def start_ab_test(self, 
                          model_id: str,
                          version_a: str,
                          version_b: str,
                          traffic_split: float = 0.5,
                          success_metric: str = "f1_score",
                          duration_hours: int = 168) -> str:
        """Start A/B test between two model versions"""
        
        # Validate models exist
        if (model_id not in self.models or 
            version_a not in self.models[model_id] or 
            version_b not in self.models[model_id]):
            raise ValueError("Model versions not found")
        
        # Generate test ID
        test_id = hashlib.md5(f"{model_id}_{version_a}_{version_b}_{time.time()}".encode()).hexdigest()[:8]
        
        # Create test configuration
        ab_test = ABTestConfig(
            test_id=test_id,
            model_a_version=version_a,
            model_b_version=version_b,
            traffic_split=traffic_split,
            success_metric=success_metric,
            max_duration_hours=duration_hours,
            started_at=datetime.now()
        )
        
        self.ab_tests[model_id] = ab_test
        
        # Load both models
        await self.models[model_id][version_a].load()
        await self.models[model_id][version_b].load()
        
        logger.info(f"Started A/B test {test_id} for {model_id}: {version_a} vs {version_b}")
        return test_id
    
    async def _ab_test_predict(self, ab_test: ABTestConfig, input_data: Any) -> Any:
        """Route prediction through A/B test"""
        import random
        
        model_id = None
        for mid, test in self.ab_tests.items():
            if test.test_id == ab_test.test_id:
                model_id = mid
                break
        
        if not model_id:
            raise RuntimeError("A/B test model not found")
        
        # Route based on traffic split
        use_model_b = random.random() < ab_test.traffic_split
        version = ab_test.model_b_version if use_model_b else ab_test.model_a_version
        
        model = self.models[model_id][version]
        result = await model.predict(input_data)
        
        # Track A/B test metrics (simplified)
        result['ab_test_version'] = 'B' if use_model_b else 'A'
        result['ab_test_id'] = ab_test.test_id
        
        return result
    
    def _get_active_ab_test(self, model_id: str) -> Optional[ABTestConfig]:
        """Get active A/B test for model"""
        ab_test = self.ab_tests.get(model_id)
        
        if ab_test and ab_test.started_at:
            # Check if test is still active
            elapsed = datetime.now() - ab_test.started_at
            if elapsed.total_seconds() / 3600 < ab_test.max_duration_hours:
                return ab_test
            else:
                # Test expired, clean up
                del self.ab_tests[model_id]
        
        return None
    
    async def stop_ab_test(self, model_id: str) -> Optional[ABTestResult]:
        """Stop A/B test and return results"""
        
        ab_test = self.ab_tests.get(model_id)
        if not ab_test:
            return None
        
        # Analyze results (simplified)
        model_a = self.models[model_id][ab_test.model_a_version]
        model_b = self.models[model_id][ab_test.model_b_version]
        
        # Mock statistical analysis
        result = ABTestResult(
            test_id=ab_test.test_id,
            model_a_metrics=model_a.metrics,
            model_b_metrics=model_b.metrics,
            samples_a=1000,  # Mock sample sizes
            samples_b=1000,
            confidence_level=0.95,
            p_value=0.03,
            winner="B" if model_b.metrics.f1_score > model_a.metrics.f1_score else "A",
            recommendation="Deploy model B based on superior F1 score"
        )
        
        self.ab_test_results[ab_test.test_id] = result
        del self.ab_tests[model_id]
        
        logger.info(f"Completed A/B test {ab_test.test_id}, winner: {result.winner}")
        return result
    
    async def get_model_performance(self, model_id: str, version: Optional[str] = None) -> Dict[str, Any]:
        """Get model performance metrics"""
        
        if version is None:
            version = self.active_deployments.get(model_id)
            if not version:
                raise ValueError(f"No active deployment for model {model_id}")
        
        # Get metrics from registry
        model_version = None
        for mv in self.model_versions.get(model_id, []):
            if mv.version == version:
                model_version = mv
                break
        
        if not model_version:
            raise ValueError(f"Model version {model_id}:{version} not found")
        
        # Get runtime metrics if model is loaded
        runtime_metrics = {}
        if (model_id in self.models and 
            version in self.models[model_id] and 
            self.models[model_id][version].is_loaded):
            
            model = self.models[model_id][version]
            runtime_metrics = {
                "runtime_latency_ms": model.metrics.latency_ms,
                "runtime_error_rate": model.metrics.error_rate,
                "memory_usage_mb": model.metrics.memory_usage_mb,
                "last_prediction": model.last_prediction_time.isoformat() if model.last_prediction_time else None
            }
        
        return {
            "model_id": model_id,
            "version": version,
            "status": model_version.status.value,
            "metrics": model_version.metrics.to_dict(),
            "runtime_metrics": runtime_metrics,
            "deployment_count": model_version.deployment_count,
            "created_at": model_version.created_at.isoformat(),
            "last_used": model_version.last_used.isoformat() if model_version.last_used else None
        }
    
    async def list_models(self) -> List[Dict[str, Any]]:
        """List all registered models"""
        models_list = []
        
        for model_id, versions in self.model_versions.items():
            for version in versions:
                is_loaded = (model_id in self.models and 
                           version.version in self.models[model_id] and 
                           self.models[model_id][version.version].is_loaded)
                
                is_active = self.active_deployments.get(model_id) == version.version
                
                models_list.append({
                    "model_id": model_id,
                    "version": version.version,
                    "model_type": version.model_type.value,
                    "status": version.status.value,
                    "is_loaded": is_loaded,
                    "is_active": is_active,
                    "created_at": version.created_at.isoformat(),
                    "metrics": version.metrics.to_dict()
                })
        
        return models_list
    
    async def _model_cleanup_task(self):
        """Background task to cleanup unused models"""
        while True:
            try:
                await asyncio.sleep(300)  # Check every 5 minutes
                
                current_time = datetime.now()
                models_to_unload = []
                
                for model_id, versions in self.models.items():
                    for version, model in versions.items():
                        if (model.is_loaded and 
                            model.last_prediction_time and
                            (current_time - model.last_prediction_time).total_seconds() > self.auto_unload_timeout):
                            
                            # Don't unload active deployments
                            if self.active_deployments.get(model_id) != version:
                                models_to_unload.append((model_id, version))
                
                # Unload unused models
                for model_id, version in models_to_unload:
                    await self.models[model_id][version].unload()
                    logger.info(f"Auto-unloaded unused model {model_id}:{version}")
                
                # Enforce max loaded models limit
                loaded_models = []
                for model_id, versions in self.models.items():
                    for version, model in versions.items():
                        if model.is_loaded:
                            loaded_models.append((model_id, version, model.last_prediction_time or datetime.min))
                
                if len(loaded_models) > self.max_loaded_models:
                    # Sort by last usage time and unload oldest
                    loaded_models.sort(key=lambda x: x[2])
                    models_to_unload = loaded_models[:-self.max_loaded_models]
                    
                    for model_id, version, _ in models_to_unload:
                        # Don't unload active deployments
                        if self.active_deployments.get(model_id) != version:
                            await self.models[model_id][version].unload()
                            logger.info(f"Unloaded model {model_id}:{version} due to memory limits")
                
            except Exception as e:
                logger.error(f"Model cleanup task error: {e}")
    
    async def _ab_test_monitor(self):
        """Background task to monitor A/B tests"""
        while True:
            try:
                await asyncio.sleep(3600)  # Check every hour
                
                expired_tests = []
                for model_id, ab_test in self.ab_tests.items():
                    if ab_test.started_at:
                        elapsed = datetime.now() - ab_test.started_at
                        if elapsed.total_seconds() / 3600 >= ab_test.max_duration_hours:
                            expired_tests.append(model_id)
                
                # Stop expired tests
                for model_id in expired_tests:
                    result = await self.stop_ab_test(model_id)
                    if result:
                        logger.info(f"Auto-stopped expired A/B test for {model_id}")
                
            except Exception as e:
                logger.error(f"A/B test monitor error: {e}")
    
    def get_system_stats(self) -> Dict[str, Any]:
        """Get system-wide model management statistics"""
        total_models = sum(len(versions) for versions in self.model_versions.values())
        loaded_models = 0
        total_memory_mb = 0.0
        
        for versions in self.models.values():
            for model in versions.values():
                if model.is_loaded:
                    loaded_models += 1
                    total_memory_mb += model.metrics.memory_usage_mb
        
        return {
            "total_models": total_models,
            "loaded_models": loaded_models,
            "active_deployments": len(self.active_deployments),
            "active_ab_tests": len(self.ab_tests),
            "total_memory_usage_mb": total_memory_mb,
            "memory_limit_mb": self.max_loaded_models * 8000,  # Estimate
            "ab_test_results": len(self.ab_test_results)
        }

# Global model manager instance
model_manager: Optional[ModelManager] = None

def get_model_manager() -> ModelManager:
    """Get global model manager instance"""
    global model_manager
    
    if model_manager is None:
        model_manager = ModelManager()
    
    return model_manager

def close_model_manager():
    """Close global model manager"""
    global model_manager
    model_manager = None