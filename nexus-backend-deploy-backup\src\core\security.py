#!/usr/bin/env python3
"""
Security Module for NexusScan
Handles encryption, API key management, and security safeguards
"""

import os
import base64
import hashlib
import secrets
from pathlib import Path
from typing import Optional, Dict, Any, Tuple
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
import logging

logger = logging.getLogger(__name__)


class SecurityError(Exception):
    """Security-related exceptions"""
    pass


class EncryptionManager:
    """Handles encryption/decryption of sensitive data"""
    
    def __init__(self, master_key: Optional[str] = None):
        """Initialize encryption manager with master key"""
        self.master_key = master_key
        self._fernet_key = None
        
        if master_key:
            self._derive_encryption_key(master_key)
    
    def _derive_encryption_key(self, password: str, salt: Optional[bytes] = None) -> bytes:
        """Derive encryption key from password using PBKDF2"""
        if salt is None:
            salt = b'nexusscan_security_salt_2024'  # Static salt for consistency
        
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=100000,
        )
        key = base64.urlsafe_b64encode(kdf.derive(password.encode()))
        self._fernet_key = Fernet(key)
        return key
    
    def encrypt(self, data: str) -> str:
        """Encrypt string data"""
        if not self._fernet_key:
            raise SecurityError("Encryption key not initialized")
        
        encrypted_data = self._fernet_key.encrypt(data.encode())
        return base64.urlsafe_b64encode(encrypted_data).decode()
    
    def decrypt(self, encrypted_data: str) -> str:
        """Decrypt string data"""
        if not self._fernet_key:
            raise SecurityError("Encryption key not initialized")
        
        try:
            encrypted_bytes = base64.urlsafe_b64decode(encrypted_data.encode())
            decrypted_data = self._fernet_key.decrypt(encrypted_bytes)
            return decrypted_data.decode()
        except Exception as e:
            raise SecurityError(f"Decryption failed: {e}")
    
    @staticmethod
    def generate_master_key() -> str:
        """Generate a secure random master key"""
        return secrets.token_urlsafe(32)


class APIKeyManager:
    """Secure API key storage and management"""
    
    def __init__(self, config_dir: str = "~/.nexusscan"):
        """Initialize API key manager"""
        self.config_dir = Path(config_dir).expanduser()
        self.config_dir.mkdir(exist_ok=True, mode=0o700)
        
        self.keystore_path = self.config_dir / "keystore.enc"
        self.master_key_path = self.config_dir / "master.key"
        
        self.encryption_manager = None
        self._keystore: Dict[str, Any] = {}
        
        self._initialize_security()
    
    def _initialize_security(self):
        """Initialize security components"""
        try:
            # Load or create master key
            if self.master_key_path.exists():
                with open(self.master_key_path, 'r') as f:
                    master_key = f.read().strip()
            else:
                master_key = EncryptionManager.generate_master_key()
                with open(self.master_key_path, 'w') as f:
                    f.write(master_key)
                os.chmod(self.master_key_path, 0o600)
            
            self.encryption_manager = EncryptionManager(master_key)
            
            # Load existing keystore
            self._load_keystore()
            
        except Exception as e:
            logger.error(f"Failed to initialize security: {e}")
            raise SecurityError(f"Security initialization failed: {e}")
    
    def _load_keystore(self):
        """Load encrypted keystore"""
        if self.keystore_path.exists():
            try:
                with open(self.keystore_path, 'r') as f:
                    encrypted_data = f.read()
                
                if encrypted_data.strip():
                    decrypted_data = self.encryption_manager.decrypt(encrypted_data)
                    import json
                    self._keystore = json.loads(decrypted_data)
                else:
                    self._keystore = {}
                    
            except Exception as e:
                logger.warning(f"Failed to load keystore: {e}")
                self._keystore = {}
    
    def _save_keystore(self):
        """Save encrypted keystore"""
        try:
            import json
            keystore_json = json.dumps(self._keystore, indent=2)
            encrypted_data = self.encryption_manager.encrypt(keystore_json)
            
            with open(self.keystore_path, 'w') as f:
                f.write(encrypted_data)
            os.chmod(self.keystore_path, 0o600)
            
        except Exception as e:
            logger.error(f"Failed to save keystore: {e}")
            raise SecurityError(f"Keystore save failed: {e}")
    
    def store_api_key(self, provider: str, api_key: str, validate: bool = True) -> bool:
        """Store API key securely"""
        try:
            if validate and not self.validate_api_key(provider, api_key):
                raise SecurityError(f"Invalid API key format for {provider}")
            
            # Store with metadata
            self._keystore[provider] = {
                'api_key': api_key,
                'created_at': int(__import__('time').time()),
                'last_used': None,
                'rotation_count': self._keystore.get(provider, {}).get('rotation_count', 0)
            }
            
            self._save_keystore()
            logger.info(f"API key stored for provider: {provider}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to store API key for {provider}: {e}")
            return False
    
    def get_api_key(self, provider: str) -> Optional[str]:
        """Retrieve API key securely"""
        try:
            key_data = self._keystore.get(provider)
            if key_data:
                # Update last used timestamp
                key_data['last_used'] = int(__import__('time').time())
                self._save_keystore()
                return key_data['api_key']
            return None
            
        except Exception as e:
            logger.error(f"Failed to retrieve API key for {provider}: {e}")
            return None
    
    def rotate_api_key(self, provider: str, new_api_key: str) -> bool:
        """Rotate API key for provider"""
        try:
            if not self.validate_api_key(provider, new_api_key):
                raise SecurityError(f"Invalid new API key format for {provider}")
            
            old_data = self._keystore.get(provider, {})
            
            self._keystore[provider] = {
                'api_key': new_api_key,
                'created_at': int(__import__('time').time()),
                'last_used': None,
                'rotation_count': old_data.get('rotation_count', 0) + 1,
                'previous_key_hash': hashlib.sha256(
                    old_data.get('api_key', '').encode()
                ).hexdigest()[:16] if old_data.get('api_key') else None
            }
            
            self._save_keystore()
            logger.info(f"API key rotated for provider: {provider}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to rotate API key for {provider}: {e}")
            return False
    
    def delete_api_key(self, provider: str) -> bool:
        """Delete API key for provider"""
        try:
            if provider in self._keystore:
                del self._keystore[provider]
                self._save_keystore()
                logger.info(f"API key deleted for provider: {provider}")
                return True
            return False
            
        except Exception as e:
            logger.error(f"Failed to delete API key for {provider}: {e}")
            return False
    
    def list_providers(self) -> Dict[str, Dict[str, Any]]:
        """List all providers with metadata (without keys)"""
        result = {}
        for provider, data in self._keystore.items():
            result[provider] = {
                'created_at': data.get('created_at'),
                'last_used': data.get('last_used'),
                'rotation_count': data.get('rotation_count', 0),
                'has_key': bool(data.get('api_key'))
            }
        return result
    
    @staticmethod
    def validate_api_key(provider: str, api_key: str) -> bool:
        """Validate API key format"""
        if not api_key or len(api_key.strip()) < 10:
            return False
        
        # Provider-specific validation
        provider_lower = provider.lower()
        
        if provider_lower == 'openai':
            return api_key.startswith('sk-') and len(api_key) >= 48
        elif provider_lower == 'deepseek':
            return len(api_key) >= 32 and not api_key.startswith('sk-')
        elif provider_lower == 'anthropic':
            return api_key.startswith('sk-ant-') and len(api_key) >= 40
        else:
            # Generic validation for unknown providers
            return len(api_key) >= 20 and api_key.replace('-', '').replace('_', '').isalnum()


class SecuritySafeguards:
    """Security safeguards for exploit generation and real mode operations"""
    
    def __init__(self, config_manager=None):
        """Initialize security safeguards"""
        self.config_manager = config_manager
        self._legal_disclaimer_accepted = False
        self._authorized_environment = None
    
    def verify_authorized_testing_environment(self) -> bool:
        """Verify that the current environment is authorized for security testing"""
        # Check for common indicators of authorized testing environments
        indicators = [
            # Environment variables
            os.getenv('NEXUSSCAN_AUTHORIZED') == 'true',
            os.getenv('PENTEST_AUTHORIZED') == 'true',
            
            # Network indicators (localhost, private networks)
            self._is_safe_network_environment(),
            
            # File-based authorization
            Path('/.nexusscan_authorized').exists(),
            Path('./authorized_testing').exists(),
            
            # Development environment indicators
            os.getenv('ENVIRONMENT') in ['development', 'testing', 'pentest'],
        ]
        
        # Cache result to avoid repeated checks
        if self._authorized_environment is None:
            self._authorized_environment = any(indicators)
            
            if self._authorized_environment:
                logger.info("Authorized testing environment detected")
            else:
                logger.warning("No authorized testing environment indicators found")
        
        return self._authorized_environment
    
    def _is_safe_network_environment(self) -> bool:
        """Check if we're in a safe network environment"""
        try:
            import socket
            hostname = socket.gethostname()
            
            # Safe indicators
            safe_patterns = [
                'localhost', '127.0.0.1', 'pentest', 'test', 'lab', 
                'demo', 'training', 'dev', 'staging'
            ]
            
            return any(pattern in hostname.lower() for pattern in safe_patterns)
            
        except Exception:
            return False
    
    def legal_disclaimer_accepted(self) -> bool:
        """Check if legal disclaimer has been accepted"""
        if not self._legal_disclaimer_accepted:
            # Check persistent storage
            if self.config_manager:
                try:
                    from core.config import Config
                    config = Config()
                    self._legal_disclaimer_accepted = getattr(
                        config.security, 'legal_disclaimer_accepted', False
                    )
                except Exception:
                    pass
        
        return self._legal_disclaimer_accepted
    
    def accept_legal_disclaimer(self) -> bool:
        """Mark legal disclaimer as accepted"""
        try:
            if self.config_manager:
                # Store in persistent config
                from core.config import Config
                config = Config()
                config.security.legal_disclaimer_accepted = True
                # Save config if possible
            
            self._legal_disclaimer_accepted = True
            logger.info("Legal disclaimer accepted")
            return True
            
        except Exception as e:
            logger.error(f"Failed to store disclaimer acceptance: {e}")
            return False
    
    def check_exploit_generation_safeguards(self, mode: str = "simulation") -> None:
        """Check all safeguards before exploit generation"""
        # Legal disclaimer for real mode
        if mode == "real" and not self.legal_disclaimer_accepted():
            raise SecurityError(
                "Real mode requires legal disclaimer acceptance. "
                "Use accept_legal_disclaimer() or run in simulation mode."
            )
        
        logger.info(f"Security safeguards passed for {mode} mode")
    
    def override_environment_check(self, authorized: bool = True) -> None:
        """Override environment authorization (for testing)"""
        self._authorized_environment = authorized
        logger.warning(f"Environment authorization override: {authorized}")


# Convenience functions
def get_api_key_manager() -> APIKeyManager:
    """Get singleton API key manager instance"""
    if not hasattr(get_api_key_manager, '_instance'):
        get_api_key_manager._instance = APIKeyManager()
    return get_api_key_manager._instance


def get_security_safeguards() -> SecuritySafeguards:
    """Get singleton security safeguards instance"""
    if not hasattr(get_security_safeguards, '_instance'):
        get_security_safeguards._instance = SecuritySafeguards()
    return get_security_safeguards._instance


# Export key functions
__all__ = [
    'SecurityError',
    'EncryptionManager', 
    'APIKeyManager',
    'SecuritySafeguards',
    'get_api_key_manager',
    'get_security_safeguards'
]