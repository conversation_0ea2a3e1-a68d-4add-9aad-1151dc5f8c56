#!/usr/bin/env python3
"""
🔧 Backend Services Deep Debugger 🔧
Comprehensive debugging for all Python backend components

This script performs detailed analysis of:
- FastAPI REST server status and endpoints
- Database connections and query performance  
- AI service integrations and API connectivity
- Security tool managers and execution modes
- WebSocket real-time communication
- Event system and message propagation
- Memory usage and performance bottlenecks
"""

import asyncio
import json
import logging
import sys
import time
import traceback
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any
import requests
import sqlite3
import websockets
import subprocess

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root / 'src'))

class BackendDebugger:
    """🔧 Deep backend debugging and analysis"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.backend_url = "http://localhost:8000"
        self.metrics_url = "http://localhost:8001"
        self.ws_url = "ws://localhost:8000/ws"
        self.results = {}
        
        print("🔧 BACKEND SERVICES DEEP DEBUGGER")
        print("=" * 50)

    async def debug_all_backend_services(self):
        """Execute comprehensive backend debugging"""
        
        # Core Service Analysis
        await self._debug_fastapi_server()
        await self._debug_database_layer()
        await self._debug_api_endpoints()
        
        # AI Services Analysis  
        await self._debug_ai_services()
        await self._debug_ai_provider_connectivity()
        
        # Security Tools Analysis
        await self._debug_security_tools()
        await self._debug_tool_execution_modes()
        
        # Communication Analysis
        await self._debug_websocket_communication()
        await self._debug_event_system()
        
        # Performance Analysis
        await self._debug_memory_performance()
        await self._debug_response_times()
        
        return self._generate_backend_report()

    async def _debug_fastapi_server(self):
        """🚀 Debug FastAPI server status"""
        print("\n🚀 FastAPI Server Analysis")
        print("-" * 30)
        
        try:
            # Check server startup
            response = requests.get(f"{self.backend_url}/api/health", timeout=10)
            
            if response.status_code == 200:
                health_data = response.json()
                print(f"✅ Server Status: {health_data.get('status', 'Unknown')}")
                print(f"✅ Uptime: {health_data.get('uptime', 'Unknown')}")
                
                self.results['fastapi_server'] = {
                    'status': 'healthy',
                    'uptime': health_data.get('uptime'),
                    'response_time': response.elapsed.total_seconds()
                }
            else:
                print(f"❌ Server returned status: {response.status_code}")
                self.results['fastapi_server'] = {
                    'status': 'error',
                    'error': f"HTTP {response.status_code}"
                }
                
        except requests.exceptions.ConnectionError:
            print("❌ FastAPI server not responding - check if backend is running")
            self.results['fastapi_server'] = {
                'status': 'not_running',
                'error': 'Connection refused'
            }
        except Exception as e:
            print(f"❌ FastAPI server error: {str(e)}")
            self.results['fastapi_server'] = {
                'status': 'error',
                'error': str(e)
            }

    async def _debug_database_layer(self):
        """🗄️ Debug database connectivity and performance"""
        print("\n🗄️ Database Layer Analysis")
        print("-" * 30)
        
        # Find database file
        db_paths = [
            self.project_root / 'data/nexusscan.db',
            self.project_root / 'nexusscan.db',
            Path.home() / '.local/share/nexusscan/nexusscan.db'  # WSL path
        ]
        
        db_path = None
        for path in db_paths:
            if path.exists():
                db_path = path
                break
        
        if not db_path:
            print("❌ Database file not found in any expected location")
            self.results['database'] = {'status': 'not_found'}
            return
        
        print(f"✅ Database found: {db_path}")
        
        try:
            # Connect and analyze
            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()
            
            # Check schema
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [row[0] for row in cursor.fetchall()]
            print(f"📋 Tables found: {len(tables)} - {', '.join(tables)}")
            
            # Check data counts
            data_counts = {}
            for table in ['campaigns', 'scans', 'vulnerabilities']:
                if table in tables:
                    cursor.execute(f"SELECT COUNT(*) FROM {table}")
                    count = cursor.fetchone()[0]
                    data_counts[table] = count
                    print(f"📊 {table}: {count} records")
            
            # Performance test
            start_time = time.time()
            cursor.execute("SELECT 1")
            query_time = time.time() - start_time
            
            print(f"⏱️ Query performance: {query_time:.3f}s")
            
            self.results['database'] = {
                'status': 'healthy',
                'path': str(db_path),
                'tables': tables,
                'data_counts': data_counts,
                'query_time': query_time
            }
            
            conn.close()
            
        except Exception as e:
            print(f"❌ Database error: {str(e)}")
            self.results['database'] = {
                'status': 'error',
                'error': str(e)
            }

    async def _debug_api_endpoints(self):
        """🔌 Debug all API endpoints"""
        print("\n🔌 API Endpoints Analysis")
        print("-" * 30)
        
        endpoints = [
            ('/api/health', 'Health Check'),
            ('/api/campaigns', 'Campaigns'),
            ('/api/scans', 'Scans'),
            ('/api/dashboard/metrics', 'Dashboard Metrics'),
            ('/api/tools/status', 'Tools Status'),
            ('/api/ai/status', 'AI Services'),
            ('/api/config', 'Configuration')
        ]
        
        endpoint_results = {}
        
        for endpoint, name in endpoints:
            try:
                start_time = time.time()
                response = requests.get(f"{self.backend_url}{endpoint}", timeout=5)
                response_time = time.time() - start_time
                
                if response.status_code == 200:
                    status = "✅"
                    result = "OK"
                elif response.status_code == 401:
                    status = "🔐"
                    result = "Auth Required"
                elif response.status_code == 404:
                    status = "❓"
                    result = "Not Found"
                else:
                    status = "⚠️"
                    result = f"HTTP {response.status_code}"
                
                print(f"{status} {name:.<25} {result} ({response_time:.3f}s)")
                
                endpoint_results[endpoint] = {
                    'status_code': response.status_code,
                    'response_time': response_time,
                    'result': result
                }
                
            except Exception as e:
                print(f"❌ {name:.<25} Error: {str(e)}")
                endpoint_results[endpoint] = {
                    'error': str(e)
                }
        
        self.results['api_endpoints'] = endpoint_results

    async def _debug_ai_services(self):
        """🤖 Debug AI service integrations"""
        print("\n🤖 AI Services Analysis")
        print("-" * 30)
        
        # Check AI service status via API
        try:
            response = requests.get(f"{self.backend_url}/api/ai/status", timeout=10)
            if response.status_code == 200:
                ai_status = response.json()
                print(f"✅ AI Services: {ai_status.get('status', 'Unknown')}")
                
                providers = ai_status.get('providers', {})
                for provider, status in providers.items():
                    print(f"  🤖 {provider}: {status}")
                
                self.results['ai_services'] = ai_status
            else:
                print(f"❌ AI Services API returned: {response.status_code}")
                self.results['ai_services'] = {'error': f"HTTP {response.status_code}"}
                
        except Exception as e:
            print(f"❌ AI Services error: {str(e)}")
            self.results['ai_services'] = {'error': str(e)}

    async def _debug_ai_provider_connectivity(self):
        """🌐 Test AI provider API connectivity"""
        print("\n🌐 AI Provider Connectivity")
        print("-" * 30)
        
        import os
        
        providers = {
            'OpenAI': os.getenv('OPENAI_API_KEY'),
            'DeepSeek': os.getenv('DEEPSEEK_API_KEY'),
            'Anthropic': os.getenv('ANTHROPIC_API_KEY')
        }
        
        connectivity_results = {}
        
        for provider, api_key in providers.items():
            if not api_key:
                print(f"⚠️ {provider}: No API key configured")
                connectivity_results[provider] = {'status': 'no_key'}
                continue
            
            if len(api_key) < 20:
                print(f"⚠️ {provider}: API key appears invalid (too short)")
                connectivity_results[provider] = {'status': 'invalid_key'}
                continue
            
            print(f"✅ {provider}: API key configured ({api_key[:8]}...)")
            connectivity_results[provider] = {'status': 'configured'}
        
        self.results['ai_connectivity'] = connectivity_results

    async def _debug_security_tools(self):
        """🛡️ Debug security tools status"""
        print("\n🛡️ Security Tools Analysis")
        print("-" * 30)
        
        try:
            response = requests.get(f"{self.backend_url}/api/tools/status", timeout=10)
            if response.status_code == 200:
                tools_status = response.json()
                print(f"✅ Tools Manager: {tools_status.get('status', 'Unknown')}")
                
                tools = tools_status.get('tools', {})
                for tool, status in tools.items():
                    status_symbol = "✅" if status == "available" else "❌"
                    print(f"  {status_symbol} {tool}: {status}")
                
                self.results['security_tools'] = tools_status
            else:
                print(f"❌ Tools API returned: {response.status_code}")
                self.results['security_tools'] = {'error': f"HTTP {response.status_code}"}
                
        except Exception as e:
            print(f"❌ Security tools error: {str(e)}")
            self.results['security_tools'] = {'error': str(e)}

    async def _debug_tool_execution_modes(self):
        """⚙️ Debug tool execution modes"""
        print("\n⚙️ Tool Execution Modes")
        print("-" * 30)
        
        import os
        
        execution_mode = os.getenv('EXECUTION_MODE', 'simulation')
        require_confirmation = os.getenv('REQUIRE_REAL_MODE_CONFIRMATION', 'true')
        
        print(f"🔧 Execution Mode: {execution_mode}")
        print(f"🔐 Require Confirmation: {require_confirmation}")
        
        if execution_mode == 'real':
            print("✅ Real mode enabled - tools will execute actual scans")
        else:
            print("⚠️ Simulation mode - tools will run in educational mode")
        
        self.results['execution_modes'] = {
            'execution_mode': execution_mode,
            'require_confirmation': require_confirmation
        }

    async def _debug_websocket_communication(self):
        """🔌 Debug WebSocket connectivity"""
        print("\n🔌 WebSocket Communication")
        print("-" * 30)
        
        try:
            async with websockets.connect(self.ws_url, timeout=5) as websocket:
                print("✅ WebSocket connection established")
                
                # Send test message
                test_message = {"type": "ping", "timestamp": datetime.now().isoformat()}
                await websocket.send(json.dumps(test_message))
                
                # Wait for response
                try:
                    response = await asyncio.wait_for(websocket.recv(), timeout=3)
                    print(f"✅ WebSocket response received: {response[:100]}...")
                    self.results['websocket'] = {'status': 'healthy'}
                except asyncio.TimeoutError:
                    print("⚠️ WebSocket timeout - no response received")
                    self.results['websocket'] = {'status': 'timeout'}
                    
        except Exception as e:
            print(f"❌ WebSocket error: {str(e)}")
            self.results['websocket'] = {'status': 'error', 'error': str(e)}

    async def _debug_event_system(self):
        """📡 Debug event system"""
        print("\n📡 Event System Analysis")
        print("-" * 30)
        
        # This would require importing the actual event system
        # For now, we'll do a basic check
        print("📋 Event system check requires backend access")
        self.results['event_system'] = {'status': 'requires_backend_access'}

    async def _debug_memory_performance(self):
        """💾 Debug memory usage and performance"""
        print("\n💾 Memory & Performance Analysis")
        print("-" * 30)
        
        import psutil
        
        # Get process info if backend is running
        backend_processes = []
        for proc in psutil.process_iter(['pid', 'name', 'cmdline', 'memory_info', 'cpu_percent']):
            try:
                if 'python' in proc.info['name'].lower() and proc.info['cmdline']:
                    cmdline = ' '.join(proc.info['cmdline'])
                    if 'main.py' in cmdline or 'nexusscan' in cmdline.lower():
                        backend_processes.append(proc)
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        
        if backend_processes:
            for proc in backend_processes:
                memory_mb = proc.info['memory_info'].rss / 1024 / 1024
                print(f"🔍 Backend Process {proc.info['pid']}: {memory_mb:.1f}MB RAM")
                
            self.results['memory_performance'] = {
                'backend_processes': len(backend_processes),
                'total_memory_mb': sum(p.info['memory_info'].rss for p in backend_processes) / 1024 / 1024
            }
        else:
            print("❌ No backend processes found")
            self.results['memory_performance'] = {'status': 'no_processes'}

    async def _debug_response_times(self):
        """⏱️ Debug API response times"""
        print("\n⏱️ Response Time Analysis")
        print("-" * 30)
        
        test_endpoints = ['/api/health', '/api/campaigns', '/api/scans']
        response_times = {}
        
        for endpoint in test_endpoints:
            times = []
            for i in range(3):  # Test 3 times
                try:
                    start_time = time.time()
                    response = requests.get(f"{self.backend_url}{endpoint}", timeout=5)
                    response_time = time.time() - start_time
                    times.append(response_time)
                except Exception:
                    times.append(None)
            
            valid_times = [t for t in times if t is not None]
            if valid_times:
                avg_time = sum(valid_times) / len(valid_times)
                print(f"⏱️ {endpoint}: {avg_time:.3f}s avg")
                response_times[endpoint] = avg_time
            else:
                print(f"❌ {endpoint}: Failed")
                response_times[endpoint] = None
        
        self.results['response_times'] = response_times

    def _generate_backend_report(self):
        """Generate comprehensive backend debugging report"""
        print("\n" + "="*50)
        print("🔧 BACKEND DEBUGGING REPORT")
        print("="*50)
        
        # Save results to file
        report_file = self.project_root / f"backend_debug_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w') as f:
            json.dump(self.results, f, indent=2, default=str)
        
        print(f"\n📋 Detailed report saved to: {report_file}")
        
        # Summary
        issues = []
        if self.results.get('fastapi_server', {}).get('status') != 'healthy':
            issues.append("FastAPI server issues")
        if self.results.get('database', {}).get('status') != 'healthy':
            issues.append("Database connectivity issues")
        if 'websocket' in self.results and self.results['websocket'].get('status') != 'healthy':
            issues.append("WebSocket communication issues")
        
        if issues:
            print(f"\n⚠️ Issues found: {', '.join(issues)}")
        else:
            print("\n✅ All backend services appear healthy")
        
        return self.results

async def main():
    """Main entry point"""
    debugger = BackendDebugger()
    results = await debugger.debug_all_backend_services()
    return results

if __name__ == "__main__":
    asyncio.run(main())