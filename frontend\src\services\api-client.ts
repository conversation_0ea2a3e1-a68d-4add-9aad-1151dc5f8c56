/**
 * Comprehensive API Client for NexusScan Backend Integration
 * Connects to AWS EC2 backend with 22 operational security tools
 */
import { toast } from 'sonner';

// ===== TYPE DEFINITIONS =====

export interface BackendStatus {
  connected: boolean;
  url: string;
  version?: string;
  error?: string;
  lastConnected?: Date;
  tools?: {
    total: number;
    operational: number;
    failed: number;
  };
}

export interface ToolInfo {
  id: string;
  name: string;
  description: string;
  category: 'network-scanning' | 'vulnerability-assessment' | 'web-testing' | 'password-tools' | 'ssl-testing' | 'exploitation';
  version?: string;
  status: 'available' | 'unavailable' | 'error';
  platform?: string[];
  executable?: string;
  lastUsed?: Date;
  capabilities?: string[];
  requiredParams?: ToolParameter[];
}

export interface ToolParameter {
  name: string;
  type: 'string' | 'number' | 'boolean' | 'select' | 'multiselect' | 'file' | 'array';
  required: boolean;
  description: string;
  default?: any;
  options?: string[];
  validation?: {
    min?: number;
    max?: number;
    pattern?: string;
  };
}

export interface ToolExecution {
  id: string;
  toolId: string;
  status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';
  progress: number;
  startTime: Date;
  endTime?: Date;
  output?: string[];
  error?: string;
  results?: any;
  config: Record<string, any>;
}

export interface Campaign {
  id: string;
  name: string;
  description: string;
  target: string;
  status: 'active' | 'completed' | 'paused' | 'failed';
  createdAt: Date;
  updatedAt: Date;
  tools: string[];
  executions: ToolExecution[];
  results?: any;
}

export interface FerrariCapabilities {
  orchestrator: {
    available: boolean;
    templates: string[];
    capabilities: string[];
  };
  creativeExploits: {
    available: boolean;
    confidence: number;
    generators: string[];
  };
  behavioralAnalysis: {
    available: boolean;
    engines: string[];
    realTime: boolean;
  };
  aiProxy: {
    available: boolean;
    configurations: string[];
    rotationEnabled: boolean;
  };
}

export interface AIStatus {
  available: boolean;
  providers: {
    openai: { available: boolean; model?: string };
    deepseek: { available: boolean; model?: string };
    claude: { available: boolean; model?: string };
  };
  currentProvider: string;
  lastResponse?: Date;
}

// ===== TOOL TIMEOUT CONFIGURATION =====

interface ToolTimeoutConfig {
  toolId: string;
  category: string;
  baseTimeout: number; // Base timeout in milliseconds
  scalingFactor: number; // Multiplier based on scan complexity
  maxTimeout: number; // Maximum allowed timeout
  minTimeout: number; // Minimum timeout
}

const TOOL_TIMEOUT_CONFIGS: Record<string, ToolTimeoutConfig> = {
  // Network Scanning Tools
  'nmap': {
    toolId: 'nmap',
    category: 'network',
    baseTimeout: 120000, // 2 minutes base
    scalingFactor: 1.5, // Can scale up to 3 minutes for complex scans
    maxTimeout: 600000, // 10 minutes max
    minTimeout: 30000 // 30 seconds min
  },
  'masscan': {
    toolId: 'masscan',
    category: 'network',
    baseTimeout: 60000, // 1 minute base (faster than nmap)
    scalingFactor: 1.2,
    maxTimeout: 300000, // 5 minutes max
    minTimeout: 15000 // 15 seconds min
  },

  // Vulnerability Scanners
  'nuclei': {
    toolId: 'nuclei',
    category: 'vulnerability',
    baseTimeout: 45000, // 45 seconds base (template-based, usually fast)
    scalingFactor: 1.3,
    maxTimeout: 180000, // 3 minutes max
    minTimeout: 20000 // 20 seconds min
  },
  'openvas': {
    toolId: 'openvas',
    category: 'vulnerability',
    baseTimeout: 300000, // 5 minutes base (comprehensive scanning)
    scalingFactor: 2.0,
    maxTimeout: 1800000, // 30 minutes max
    minTimeout: 120000 // 2 minutes min
  },

  // Web Application Testing
  'sqlmap': {
    toolId: 'sqlmap',
    category: 'web',
    baseTimeout: 180000, // 3 minutes base (SQL injection testing can be slow)
    scalingFactor: 2.5,
    maxTimeout: 900000, // 15 minutes max
    minTimeout: 60000 // 1 minute min
  },
  'dirb': {
    toolId: 'dirb',
    category: 'web',
    baseTimeout: 90000, // 1.5 minutes base
    scalingFactor: 1.8,
    maxTimeout: 300000, // 5 minutes max
    minTimeout: 30000 // 30 seconds min
  },
  'gobuster': {
    toolId: 'gobuster',
    category: 'web',
    baseTimeout: 60000, // 1 minute base (faster than dirb)
    scalingFactor: 1.5,
    maxTimeout: 240000, // 4 minutes max
    minTimeout: 20000 // 20 seconds min
  },

  // Default fallback for unknown tools
  'default': {
    toolId: 'default',
    category: 'unknown',
    baseTimeout: 120000, // 2 minutes base
    scalingFactor: 1.5,
    maxTimeout: 300000, // 5 minutes max
    minTimeout: 30000 // 30 seconds min
  }
};

// ===== MAIN API CLIENT CLASS =====

class APIClient {
  private baseURL: string;
  private timeout: number;
  private retryAttempts: number;
  private retryDelay: number;
  private headers: Record<string, string>;

  constructor() {
    // Contabo backend URL (updated from AWS)
    this.baseURL = import.meta.env.VITE_BACKEND_URL || 'http://161.97.99.62:8090';
    this.timeout = 30000; // 30 seconds default for non-tool operations
    this.retryAttempts = 3;
    this.retryDelay = 1000; // 1 second
    this.headers = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    };

    console.log(`🔗 API Client initialized for: ${this.baseURL}`);
  }
  
  // ===== CORE UTILITY METHODS =====

  private async delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Calculate dynamic timeout for a specific tool based on its characteristics and scan complexity
   */
  private calculateToolTimeout(toolId: string, config: Record<string, any>): number {
    const toolConfig = TOOL_TIMEOUT_CONFIGS[toolId] || TOOL_TIMEOUT_CONFIGS['default'];

    let timeout = toolConfig.baseTimeout;

    // Apply scaling based on scan complexity
    const complexityFactor = this.calculateComplexityFactor(toolId, config);
    timeout = Math.round(timeout * complexityFactor * toolConfig.scalingFactor);

    // Ensure timeout is within bounds
    timeout = Math.max(toolConfig.minTimeout, Math.min(timeout, toolConfig.maxTimeout));

    console.log(`🕒 Dynamic timeout for ${toolId}: ${timeout}ms (${Math.round(timeout/1000)}s) - Complexity: ${complexityFactor.toFixed(2)}x`);

    return timeout;
  }

  /**
   * Calculate complexity factor based on tool configuration and target characteristics
   */
  private calculateComplexityFactor(toolId: string, config: Record<string, any>): number {
    let complexityFactor = 1.0;

    switch (toolId) {
      case 'nmap':
        // Nmap complexity factors
        if (config.portRange) {
          const portCount = this.estimatePortCount(config.portRange);
          if (portCount > 1000) complexityFactor *= 1.5;
          else if (portCount > 100) complexityFactor *= 1.2;
        }
        if (config.detectOS) complexityFactor *= 1.3;
        if (config.versionDetection) complexityFactor *= 1.4;
        if (config.scriptScan) complexityFactor *= 1.6;
        if (config.timing && parseInt(config.timing) <= 2) complexityFactor *= 1.8; // Slower timing
        break;

      case 'nuclei':
        // Nuclei complexity factors
        if (config.templates && Array.isArray(config.templates)) {
          const templateCount = config.templates.length;
          if (templateCount > 100) complexityFactor *= 1.4;
          else if (templateCount > 50) complexityFactor *= 1.2;
        }
        if (config.severity && config.severity.includes('critical')) complexityFactor *= 1.1;
        if (config.concurrency && config.concurrency < 10) complexityFactor *= 1.3;
        break;

      case 'sqlmap':
        // SQLMap complexity factors
        if (config.level && config.level > 3) complexityFactor *= 1.8;
        if (config.risk && config.risk > 2) complexityFactor *= 1.5;
        if (config.technique && config.technique.includes('T')) complexityFactor *= 1.4; // Time-based
        if (config.crawl) complexityFactor *= 2.0;
        if (config.forms) complexityFactor *= 1.6;
        break;

      case 'dirb':
      case 'gobuster':
        // Directory brute force complexity
        if (config.wordlist) {
          const wordlistSize = this.estimateWordlistSize(config.wordlist);
          if (wordlistSize > 10000) complexityFactor *= 2.0;
          else if (wordlistSize > 1000) complexityFactor *= 1.5;
        }
        if (config.extensions && Array.isArray(config.extensions)) {
          complexityFactor *= (1 + config.extensions.length * 0.2);
        }
        break;

      default:
        // Default complexity calculation
        if (config.comprehensive) complexityFactor *= 1.8;
        if (config.deep) complexityFactor *= 1.5;
        if (config.aggressive) complexityFactor *= 1.3;
        break;
    }

    // Target complexity (multiple targets increase time)
    if (config.target && typeof config.target === 'string') {
      const targets = config.target.split(/[,\s\n]+/).filter(t => t.trim());
      if (targets.length > 1) {
        complexityFactor *= Math.min(targets.length * 0.3 + 0.7, 3.0); // Cap at 3x
      }
    }

    return Math.max(0.5, Math.min(complexityFactor, 5.0)); // Cap between 0.5x and 5x
  }

  /**
   * Estimate port count from port range string
   */
  private estimatePortCount(portRange: string): number {
    if (!portRange || portRange === '-') return 65535; // All ports

    const ranges = portRange.split(',');
    let totalPorts = 0;

    for (const range of ranges) {
      const trimmed = range.trim();
      if (trimmed.includes('-')) {
        const [start, end] = trimmed.split('-').map(p => parseInt(p.trim()));
        if (!isNaN(start) && !isNaN(end)) {
          totalPorts += Math.abs(end - start) + 1;
        }
      } else {
        const port = parseInt(trimmed);
        if (!isNaN(port)) totalPorts += 1;
      }
    }

    return totalPorts || 1000; // Default estimate
  }

  /**
   * Estimate wordlist size from wordlist name/path
   */
  private estimateWordlistSize(wordlist: string): number {
    const wordlistSizes: Record<string, number> = {
      'common': 1000,
      'medium': 5000,
      'large': 20000,
      'huge': 50000,
      'rockyou': 14344391,
      'dirb-common': 4614,
      'dirb-big': 20469,
      'dirbuster-medium': 220560,
      'seclist-common': 4713
    };

    const lowerWordlist = wordlist.toLowerCase();
    for (const [name, size] of Object.entries(wordlistSizes)) {
      if (lowerWordlist.includes(name)) return size;
    }

    return 5000; // Default estimate
  }
  
  private async fetchWithRetry(
    url: string, 
    options: RequestInit = {}, 
    attempts: number = this.retryAttempts
  ): Promise<Response> {
    for (let i = 0; i < attempts; i++) {
      try {
        // Skip AbortController in test environments to avoid compatibility issues
        const isTestEnv = typeof process !== 'undefined' && process.env.NODE_ENV === 'test';
        
        let fetchOptions: RequestInit = {
          ...options,
          headers: {
            ...this.headers,
            ...options.headers,
          },
        };
        
        if (!isTestEnv) {
          const controller = new AbortController();
          const timeoutId = setTimeout(() => controller.abort(), this.timeout);
          fetchOptions.signal = controller.signal;
          
          const response = await fetch(url, fetchOptions);
          clearTimeout(timeoutId);
          
          if (response.ok) {
            return response;
          }
          
          // If it's the last attempt or non-retryable error, throw
          if (i === attempts - 1 || response.status < 500) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
          }
        } else {
          // Simplified fetch for test environments
          const response = await fetch(url, fetchOptions);
          
          if (response.ok) {
            return response;
          }
          
          if (i === attempts - 1 || response.status < 500) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
          }
        }
        
      } catch (error) {
        if (i === attempts - 1) {
          throw error;
        }
        
        console.warn(`🔄 Request failed (attempt ${i + 1}/${attempts}):`, error);
        await this.delay(this.retryDelay * Math.pow(2, i)); // Exponential backoff
      }
    }
    
    throw new Error('Max retry attempts exceeded');
  }
  
  // ===== BACKEND STATUS & HEALTH =====
  
  async checkHealth(): Promise<BackendStatus> {
    try {
      console.log('🏥 Checking backend health...');
      
      const response = await this.fetchWithRetry(`${this.baseURL}/api/health`);
      const data = await response.json();
      
      console.log('✅ Backend health check successful:', data);
      
      // Handle both response formats: direct and wrapped
      const healthData = data.data || data;
      
      return {
        connected: true,
        url: this.baseURL,
        version: healthData.version || data.version || '1.0.0',
        lastConnected: new Date(),
        tools: healthData.tools || data.tools || { total: 22, operational: 22, failed: 0 },
      };
    } catch (error) {
      console.error('❌ Backend health check failed:', error);
      console.error('❌ Error details:', {
        message: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined,
        type: typeof error,
        error: error
      });
      
      return {
        connected: false,
        url: this.baseURL,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }
  
  // ===== SECURITY TOOLS API =====
  
  async getAvailableTools(): Promise<ToolInfo[]> {
    try {
      console.log('🔧 Fetching available tools from simplified endpoint...');

      // Use the simple, reliable /api/tools endpoint instead of /api/tools/available
      const response = await this.fetchWithRetry(`${this.baseURL}/api/tools`);
      const data = await response.json();

      // Handle backend response format: {"success": true, "tools": [...]}
      const rawTools = data.tools || [];

      console.log(`🔍 API Response: success=${data.success}, tools.length=${rawTools.length}`);

      // Category mapping from backend to frontend format
      const categoryMapping: Record<string, ToolInfo['category']> = {
        'Network Scanners': 'network-scanning',
        'Web Scanners': 'web-testing',
        'Vulnerability Scanners': 'vulnerability-assessment',
        'Password Tools': 'password-tools',
        'SSL/TLS': 'ssl-testing',
        'Framework': 'exploitation',
        'Fingerprinting': 'network-scanning',
        'Fuzzer': 'web-testing',
        'Network Enumeration': 'network-scanning',
        'Exploit Database': 'exploitation'
      };

      // Map backend data to frontend ToolInfo format
      const tools: ToolInfo[] = rawTools.map((tool: any) => ({
        id: tool.name,                    // Use name as ID
        name: tool.name,                  // Direct mapping
        description: tool.description,    // Direct mapping
        category: categoryMapping[tool.category] || 'network-scanning', // Map category
        version: tool.version,            // Direct mapping
        status: tool.available ? 'available' : 'unavailable', // Convert boolean to status
        executable: tool.path,            // Map path to executable
        platform: ['linux'],             // Default platform
        capabilities: [],                 // Default empty capabilities
        requiredParams: []               // Default empty required params
      }));

      console.log(`✅ Retrieved and mapped ${tools.length} tools from simplified endpoint`);
      console.log(`🗂️ Tool categories: ${[...new Set(tools.map(t => t.category))].join(', ')}`);
      console.log(`🔧 Available tools: ${tools.filter(t => t.status === 'available').map(t => t.name).join(', ')}`);
      console.log(`📊 Tool availability: ${tools.filter(t => t.status === 'available').length}/${tools.length} tools available`);

      return tools;
    } catch (error) {
      console.error('❌ Failed to fetch available tools:', error);
      toast.error('Failed to load security tools');
      return [];
    }
  }
  
  async getToolDetails(toolId: string): Promise<ToolInfo | null> {
    try {
      const response = await this.fetchWithRetry(`${this.baseURL}/api/tools/${toolId}`);
      const data = await response.json();
      
      return data.tool || null;
    } catch (error) {
      console.error(`❌ Failed to fetch tool details for ${toolId}:`, error);
      return null;
    }
  }
  
  async executeTool(
    toolId: string,
    config: Record<string, any>,
    onProgress?: (progress: ToolExecution) => void
  ): Promise<ToolExecution> {
    // Store original timeout to restore later
    const originalTimeout = this.timeout;

    try {
      console.log(`🚀 Executing tool: ${toolId}`, config);

      // Try direct endpoint first (backend now working with correct endpoint)
      try {
        // Calculate dynamic timeout based on tool and configuration
        const dynamicTimeout = this.calculateToolTimeout(toolId, config);
        this.timeout = dynamicTimeout;

        const response = await this.fetchWithRetry(`${this.baseURL}/api/tools/${toolId}/scan`, {
          method: 'POST',
          body: JSON.stringify({
            target: config.target,
            timeout: config.timeout || 300,
            threads: config.threads || 1,
            output_format: config.output_format || 'json',
            options: config
          }),
        });

        // Restore original timeout
        this.timeout = originalTimeout;
        
        const backendResponse = await response.json();

        // Backend returns immediate results, so simulate progress for UI
        if (onProgress) {
          // Start progress
          onProgress({
            id: backendResponse.data?.scan_id || 'scan-' + Date.now(),
            toolId: toolId,
            status: 'running',
            progress: 0,
            startTime: new Date(),
            output: [`Starting ${toolId} scan...`],
            results: null,
            error: null,
            config: config
          });

          // Simulate progress
          setTimeout(() => {
            onProgress({
              id: backendResponse.data?.scan_id || 'scan-' + Date.now(),
              toolId: toolId,
              status: 'running',
              progress: 50,
              startTime: new Date(),
              output: [`${toolId} scan in progress...`],
              results: null,
              error: null,
              config: config
            });
          }, 1000);

          // Complete with results
          setTimeout(() => {
            onProgress({
              id: backendResponse.data?.scan_id || 'scan-' + Date.now(),
              toolId: toolId,
              status: backendResponse.success ? 'completed' : 'failed',
              progress: 100,
              startTime: new Date(),
              endTime: new Date(),
              output: [`${toolId} scan completed`],
              results: backendResponse.data,
              error: backendResponse.success ? null : backendResponse.error,
              config: config
            });
          }, 2000);
        }

        console.log(`✅ Direct execution succeeded for ${toolId}`);

        // Return formatted execution object
        return {
          id: backendResponse.data?.scan_id || 'scan-' + Date.now(),
          toolId: toolId,
          status: backendResponse.success ? 'completed' : 'failed',
          progress: 100,
          startTime: new Date(),
          endTime: new Date(),
          output: [`${toolId} scan completed`],
          results: backendResponse.data,
          error: backendResponse.success ? null : backendResponse.error,
          config: config
        };
      } catch (directError) {
        // Restore original timeout in case of error
        this.timeout = originalTimeout;
        console.warn(`⚠️ Direct execution failed for ${toolId}, falling back to workflow approach:`, directError);

        // Fallback to working workflow approach
        return await this.executeToolViaWorkflow(toolId, config, onProgress);
      }
    } catch (error) {
      // Restore original timeout in case of error
      this.timeout = originalTimeout;
      console.error(`❌ Tool execution failed for ${toolId}:`, error);
      toast.error(`Failed to execute ${toolId}: ${error instanceof Error ? error.message : 'Unknown error'}`);
      throw error;
    }
  }
  
  async executeToolViaWorkflow(
    toolId: string, 
    config: Record<string, any>, 
    onProgress?: (progress: ToolExecution) => void
  ): Promise<ToolExecution> {
    try {
      console.log(`🔄 Executing ${toolId} via workflow approach`);
      
      // 1. Create temporary campaign
      const campaign = await this.createCampaign({
        name: `${toolId}-execution-${Date.now()}`,
        target: config.target || 'Unknown',
        description: `${toolId} execution via workflow fallback`
      });
      
      if (!campaign) {
        throw new Error('Failed to create campaign for tool execution');
      }
      
      console.log(`📁 Created campaign ${campaign.id} for ${toolId}`);
      
      // 2. Create scan within campaign
      const scanResponse = await this.fetchWithRetry(`${this.baseURL}/api/campaigns/${campaign.id}/scans`, {
        method: 'POST',
        body: JSON.stringify({
          tool: toolId,
          target: config.target || 'Unknown',
          config: config
        }),
      });
      
      const scan = await scanResponse.json();
      console.log(`🔍 Created scan ${scan.id} for ${toolId}`);
      
      // 3. Start scan execution
      const executionResponse = await this.fetchWithRetry(`${this.baseURL}/api/scans/${scan.id}/start`, {
        method: 'POST',
      });
      
      const execution = await executionResponse.json();
      console.log(`🚀 Started execution ${execution.id} for ${toolId}`);
      
      // Set up real-time progress monitoring if callback provided
      if (onProgress && execution.id) {
        this.monitorExecution(execution.id, onProgress);
      }
      
      toast.success(`${toolId} started successfully via workflow`);
      return execution;
    } catch (error) {
      console.error(`❌ Workflow execution failed for ${toolId}:`, error);
      throw new Error(`Workflow execution failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }
  
  async getExecutionStatus(executionId: string): Promise<ToolExecution | null> {
    try {
      const response = await this.fetchWithRetry(`${this.baseURL}/api/executions/${executionId}`);
      const data = await response.json();
      
      return data.execution || null;
    } catch (error) {
      console.error(`❌ Failed to get execution status for ${executionId}:`, error);
      return null;
    }
  }
  
  async cancelExecution(executionId: string): Promise<boolean> {
    try {
      const response = await this.fetchWithRetry(`${this.baseURL}/api/executions/${executionId}/cancel`, {
        method: 'POST',
      });
      
      return response.ok;
    } catch (error) {
      console.error(`❌ Failed to cancel execution ${executionId}:`, error);
      return false;
    }
  }
  
  private async monitorExecution(executionId: string, onProgress: (progress: ToolExecution) => void): Promise<void> {
    const checkProgress = async () => {
      const execution = await this.getExecutionStatus(executionId);
      if (execution) {
        onProgress(execution);
        
        // Continue monitoring if still running
        if (execution.status === 'running' || execution.status === 'pending') {
          setTimeout(checkProgress, 2000); // Check every 2 seconds
        }
      }
    };
    
    setTimeout(checkProgress, 1000); // Start after 1 second
  }
  
  // ===== CAMPAIGNS API =====
  
  async getCampaigns(): Promise<Campaign[]> {
    try {
      const response = await this.fetchWithRetry(`${this.baseURL}/api/campaigns`);
      const data = await response.json();
      
      return data.campaigns || [];
    } catch (error) {
      console.error('❌ Failed to fetch campaigns:', error);
      return [];
    }
  }
  
  async createCampaign(campaign: Partial<Campaign>): Promise<Campaign | null> {
    try {
      const response = await this.fetchWithRetry(`${this.baseURL}/api/campaigns`, {
        method: 'POST',
        body: JSON.stringify(campaign),
      });
      
      const data = await response.json();
      return data.campaign || null;
    } catch (error) {
      console.error('❌ Failed to create campaign:', error);
      toast.error('Failed to create campaign');
      return null;
    }
  }
  
  async updateCampaign(campaignId: string, updates: Partial<Campaign>): Promise<Campaign | null> {
    try {
      const response = await this.fetchWithRetry(`${this.baseURL}/api/campaigns/${campaignId}`, {
        method: 'PUT',
        body: JSON.stringify(updates),
      });
      
      const data = await response.json();
      return data.campaign || null;
    } catch (error) {
      console.error(`❌ Failed to update campaign ${campaignId}:`, error);
      return null;
    }
  }
  
  async deleteCampaign(campaignId: string): Promise<boolean> {
    try {
      const response = await this.fetchWithRetry(`${this.baseURL}/api/campaigns/${campaignId}`, {
        method: 'DELETE',
      });
      
      return response.ok;
    } catch (error) {
      console.error(`❌ Failed to delete campaign ${campaignId}:`, error);
      return false;
    }
  }
  
  // ===== FERRARI AI CAPABILITIES =====
  
  async getFerrariCapabilities(): Promise<FerrariCapabilities | null> {
    try {
      console.log('🏎️ Fetching Ferrari AI capabilities...');
      
      const [orchestrator, creativeExploits, behavioralAnalysis, aiProxy] = await Promise.all([
        this.fetchWithRetry(`${this.baseURL}/api/orchestrator/capabilities`),
        this.fetchWithRetry(`${this.baseURL}/api/ai/creative-exploits/capabilities`),
        this.fetchWithRetry(`${this.baseURL}/api/ai/behavioral-analysis/capabilities`),
        this.fetchWithRetry(`${this.baseURL}/api/proxy/configurations`),
      ]);
      
      const [orchestratorData, exploitsData, analysisData, proxyData] = await Promise.all([
        orchestrator.json(),
        creativeExploits.json(),
        behavioralAnalysis.json(),
        aiProxy.json(),
      ]);
      
      return {
        orchestrator: {
          available: orchestrator.ok,
          templates: orchestratorData.templates || [],
          capabilities: orchestratorData.capabilities || [],
        },
        creativeExploits: {
          available: creativeExploits.ok,
          confidence: exploitsData.confidence || 0,
          generators: exploitsData.generators || [],
        },
        behavioralAnalysis: {
          available: behavioralAnalysis.ok,
          engines: analysisData.engines || [],
          realTime: analysisData.realTime || false,
        },
        aiProxy: {
          available: aiProxy.ok,
          configurations: proxyData.configurations || [],
          rotationEnabled: proxyData.rotationEnabled || false,
        },
      };
    } catch (error) {
      console.error('❌ Failed to fetch Ferrari capabilities:', error);
      return null;
    }
  }
  
  async getAIStatus(): Promise<AIStatus | null> {
    try {
      const response = await this.fetchWithRetry(`${this.baseURL}/api/ai/capabilities/status`);
      const data = await response.json();

      console.log('🤖 Raw AI status response:', data);

      // Handle the actual backend response format
      const aiData = data.data || data;

      // Extract provider information from active_providers array
      const activeProviders = aiData.core_ai_services?.active_providers || [];
      const providers = {
        openai: {
          available: activeProviders.includes('openai'),
          model: 'gpt-4'
        },
        deepseek: {
          available: activeProviders.includes('deepseek'),
          model: 'deepseek-v3'
        },
        claude: {
          available: activeProviders.includes('anthropic'),
          model: 'claude-3.5-sonnet'
        },
      };

      return {
        available: aiData.core_ai_services?.available || false,
        providers,
        currentProvider: aiData.configuration?.default_provider || 'openai',
        lastResponse: aiData.last_updated ? new Date(aiData.last_updated) : undefined,
      };
    } catch (error) {
      console.error('❌ Failed to fetch AI status:', error);
      return null;
    }
  }
  
  // ===== ORCHESTRATOR API =====
  
  async getOrchestratorTemplates(): Promise<any[]> {
    try {
      const response = await this.fetchWithRetry(`${this.baseURL}/api/orchestrator/templates`);
      const data = await response.json();
      
      return data.templates || [];
    } catch (error) {
      console.error('❌ Failed to fetch orchestrator templates:', error);
      return [];
    }
  }
  
  async executeAttackChain(chain: any): Promise<any> {
    try {
      const response = await this.fetchWithRetry(`${this.baseURL}/api/orchestrator/execute`, {
        method: 'POST',
        body: JSON.stringify(chain),
      });
      
      return await response.json();
    } catch (error) {
      console.error('❌ Failed to execute attack chain:', error);
      throw error;
    }
  }
  
  // ===== CREATIVE EXPLOITS API =====
  
  async generateCreativeExploit(target: any): Promise<any> {
    try {
      const response = await this.fetchWithRetry(`${this.baseURL}/api/ai/creative-exploits/generate`, {
        method: 'POST',
        body: JSON.stringify(target),
      });
      
      return await response.json();
    } catch (error) {
      console.error('❌ Failed to generate creative exploit:', error);
      throw error;
    }
  }
  
  // ===== BEHAVIORAL ANALYSIS API =====
  
  async analyzeBehavior(data: any): Promise<any> {
    try {
      const response = await this.fetchWithRetry(`${this.baseURL}/api/ai/behavioral-analysis/analyze`, {
        method: 'POST',
        body: JSON.stringify(data),
      });
      
      return await response.json();
    } catch (error) {
      console.error('❌ Failed to analyze behavior:', error);
      throw error;
    }
  }
  
  // ===== PROXY API =====
  
  async getProxyConfigurations(): Promise<any[]> {
    try {
      const response = await this.fetchWithRetry(`${this.baseURL}/api/proxy/configurations`);
      const data = await response.json();
      
      return data.configurations || [];
    } catch (error) {
      console.error('❌ Failed to fetch proxy configurations:', error);
      return [];
    }
  }
  
  // ===== UTILITY METHODS =====
  
  getBaseURL(): string {
    return this.baseURL;
  }
  
  setBaseURL(url: string): void {
    this.baseURL = url;
    console.log(`🔗 API Client URL updated to: ${this.baseURL}`);
  }
  
  setTimeout(timeout: number): void {
    this.timeout = timeout;
  }
  
  setRetryAttempts(attempts: number): void {
    this.retryAttempts = attempts;
  }
}

// ===== SINGLETON EXPORT =====

export const apiClient = new APIClient();