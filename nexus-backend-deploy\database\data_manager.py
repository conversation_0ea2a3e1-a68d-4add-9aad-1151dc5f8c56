#!/usr/bin/env python3
"""
Data Storage and Retrieval System for NexusScan Desktop Application
Handles storage, retrieval, and management of scan results and vulnerability data
"""

import json
import logging
import hashlib
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple, Union
from dataclasses import dataclass, asdict
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, desc, asc, func, text

from .models import (
    Scan, Vulnerability, AIAnalysis, FileAttachment, ActivityLog,
    ScanStatus, VulnerabilitySeverity, VulnerabilityStatus,
    DatabaseManager
)

logger = logging.getLogger(__name__)


@dataclass
class ScanCreateRequest:
    """Request structure for creating a new scan"""
    campaign_id: int
    name: str
    scan_type: str  # network, web, api, mobile
    target: str
    tools_config: Dict[str, Any] = None
    scan_config: Dict[str, Any] = None

    def __post_init__(self):
        if self.tools_config is None:
            self.tools_config = {}
        if self.scan_config is None:
            self.scan_config = {}


@dataclass
class VulnerabilityCreateRequest:
    """Request structure for creating a new vulnerability"""
    scan_id: int
    campaign_id: int
    vuln_type: str
    severity: str
    title: str
    description: str
    affected_url: Optional[str] = None
    affected_parameter: Optional[str] = None
    evidence: Optional[str] = None
    remediation: Optional[str] = None
    cvss_score: Optional[float] = None
    cve_id: Optional[str] = None
    tags: Optional[List[str]] = None
    metadata: Optional[Dict[str, Any]] = None

    def __post_init__(self):
        if self.tags is None:
            self.tags = []
        if self.metadata is None:
            self.metadata = {}


@dataclass
class AIAnalysisCreateRequest:
    """Request structure for creating AI analysis record"""
    analysis_type: str
    ai_provider: str
    model_used: str
    input_data: Dict[str, Any]
    output_data: Dict[str, Any]
    confidence_score: float = 0.0
    execution_time_ms: int = 0
    vulnerability_id: Optional[int] = None
    scan_id: Optional[int] = None
    campaign_id: Optional[int] = None


@dataclass
class SearchFilters:
    """Search and filter options for data retrieval"""
    campaign_id: Optional[int] = None
    scan_id: Optional[int] = None
    vulnerability_type: Optional[str] = None
    severity: Optional[str] = None
    status: Optional[str] = None
    date_from: Optional[datetime] = None
    date_to: Optional[datetime] = None
    search_term: Optional[str] = None
    tags: Optional[List[str]] = None
    cvss_min: Optional[float] = None
    cvss_max: Optional[float] = None
    verified_only: Optional[bool] = None
    exploitable_only: Optional[bool] = None
    limit: int = 100
    offset: int = 0
    sort_by: str = "discovered_at"
    sort_desc: bool = True


class DataManager:
    """Manages data storage and retrieval for scans, vulnerabilities, and analysis results"""

    def __init__(self, db_manager: DatabaseManager):
        """Initialize data manager
        
        Args:
            db_manager: Database manager instance
        """
        self.db_manager = db_manager
        self.logger = logging.getLogger(__name__)

    # =============================================================================
    # SCAN MANAGEMENT
    # =============================================================================

    def create_scan(self, request: ScanCreateRequest) -> Scan:
        """Create a new scan record
        
        Args:
            request: Scan creation request
            
        Returns:
            Created scan object
        """
        session = self.db_manager.get_session()
        try:
            scan = Scan(
                campaign_id=request.campaign_id,
                name=request.name,
                scan_type=request.scan_type,
                target=request.target,
                tools_used=list(request.tools_config.keys()) if request.tools_config else [],
                scan_config=request.scan_config,
                status=ScanStatus.PENDING.value
            )
            
            session.add(scan)
            session.commit()
            session.refresh(scan)
            
            self.logger.info(f"Created scan: {scan.name} (ID: {scan.id})")
            return scan
            
        except Exception as e:
            session.rollback()
            self.logger.error(f"Failed to create scan: {e}")
            raise
        finally:
            session.close()

    def start_scan(self, scan_id: int) -> bool:
        """Mark scan as started and update status
        
        Args:
            scan_id: Scan ID
            
        Returns:
            True if updated successfully
        """
        session = self.db_manager.get_session()
        try:
            scan = session.query(Scan).filter_by(id=scan_id).first()
            if not scan:
                return False
            
            scan.status = ScanStatus.RUNNING.value
            scan.started_at = datetime.utcnow()
            session.commit()
            
            self.logger.info(f"Started scan: {scan.name} (ID: {scan_id})")
            return True
            
        except Exception as e:
            session.rollback()
            self.logger.error(f"Failed to start scan {scan_id}: {e}")
            raise
        finally:
            session.close()

    def complete_scan(self, scan_id: int, raw_output: str = "", summary: str = "") -> bool:
        """Mark scan as completed and store results
        
        Args:
            scan_id: Scan ID
            raw_output: Raw tool output
            summary: Scan summary
            
        Returns:
            True if updated successfully
        """
        session = self.db_manager.get_session()
        try:
            scan = session.query(Scan).filter_by(id=scan_id).first()
            if not scan:
                return False
            
            scan.complete_scan()  # Uses model method to set completion time and duration
            scan.raw_output = raw_output
            scan.summary = summary
            session.commit()
            
            self.logger.info(f"Completed scan: {scan.name} (ID: {scan_id})")
            return True
            
        except Exception as e:
            session.rollback()
            self.logger.error(f"Failed to complete scan {scan_id}: {e}")
            raise
        finally:
            session.close()

    def fail_scan(self, scan_id: int, error_message: str = "") -> bool:
        """Mark scan as failed
        
        Args:
            scan_id: Scan ID
            error_message: Error description
            
        Returns:
            True if updated successfully
        """
        session = self.db_manager.get_session()
        try:
            scan = session.query(Scan).filter_by(id=scan_id).first()
            if not scan:
                return False
            
            scan.status = ScanStatus.FAILED.value
            scan.completed_at = datetime.utcnow()
            if error_message:
                scan.summary = f"Scan failed: {error_message}"
            
            session.commit()
            
            self.logger.warning(f"Failed scan: {scan.name} (ID: {scan_id}) - {error_message}")
            return True
            
        except Exception as e:
            session.rollback()
            self.logger.error(f"Failed to update scan {scan_id}: {e}")
            raise
        finally:
            session.close()

    def get_scan(self, scan_id: int) -> Optional[Scan]:
        """Get scan by ID
        
        Args:
            scan_id: Scan ID
            
        Returns:
            Scan object or None if not found
        """
        session = self.db_manager.get_session()
        try:
            return session.query(Scan).filter_by(id=scan_id).first()
        finally:
            session.close()

    def get_all_scans(self) -> List[Scan]:
        """Get all scans without filtering
        
        Returns:
            List of all scan objects
        """
        from sqlalchemy.orm import joinedload
        
        session = self.db_manager.get_session()
        try:
            # Eagerly load vulnerabilities to avoid lazy loading issues
            scans = session.query(Scan)\
                .options(joinedload(Scan.vulnerabilities))\
                .order_by(desc(Scan.created_at))\
                .all()
            
            # Detach from session to avoid lazy loading issues
            for scan in scans:
                session.expunge(scan)
            
            return scans
        finally:
            session.close()

    def get_scans_by_campaign(self, campaign_id: int) -> List[Scan]:
        """Get all scans for a specific campaign
        
        Args:
            campaign_id: Campaign ID
            
        Returns:
            List of scan objects for the campaign
        """
        from sqlalchemy.orm import joinedload
        
        session = self.db_manager.get_session()
        try:
            # Eagerly load vulnerabilities to avoid lazy loading issues
            scans = session.query(Scan)\
                .options(joinedload(Scan.vulnerabilities))\
                .filter(Scan.campaign_id == campaign_id)\
                .order_by(desc(Scan.created_at))\
                .all()
            
            # Detach from session to avoid lazy loading issues
            for scan in scans:
                session.expunge(scan)
            
            return scans
        finally:
            session.close()

    def update_scan_status(self, scan_id: int, status: str) -> bool:
        """Update scan status
        
        Args:
            scan_id: Scan ID
            status: New status
            
        Returns:
            True if updated successfully
        """
        session = self.db_manager.get_session()
        try:
            scan = session.query(Scan).filter_by(id=scan_id).first()
            if not scan:
                return False
            
            scan.status = status
            if status == ScanStatus.RUNNING.value and not scan.started_at:
                scan.started_at = datetime.utcnow()
            elif status == ScanStatus.COMPLETED.value and not scan.completed_at:
                scan.completed_at = datetime.utcnow()
                if scan.started_at:
                    delta = scan.completed_at - scan.started_at
                    scan.duration_seconds = int(delta.total_seconds())
                scan.progress = 100
            elif status == ScanStatus.FAILED.value and not scan.completed_at:
                scan.completed_at = datetime.utcnow()
                if scan.started_at:
                    delta = scan.completed_at - scan.started_at
                    scan.duration_seconds = int(delta.total_seconds())
            
            session.commit()
            
            self.logger.info(f"Updated scan {scan_id} status to: {status}")
            return True
            
        except Exception as e:
            session.rollback()
            self.logger.error(f"Failed to update scan {scan_id} status: {e}")
            raise
        finally:
            session.close()

    def update_scan_progress(self, scan_id: int, progress: int) -> bool:
        """Update scan progress
        
        Args:
            scan_id: Scan ID
            progress: Progress percentage (0-100)
            
        Returns:
            True if updated successfully
        """
        session = self.db_manager.get_session()
        try:
            scan = session.query(Scan).filter_by(id=scan_id).first()
            if not scan:
                return False
            
            scan.progress = max(0, min(100, progress))  # Clamp between 0-100
            session.commit()
            
            self.logger.debug(f"Updated scan {scan_id} progress to: {progress}%")
            return True
            
        except Exception as e:
            session.rollback()
            self.logger.error(f"Failed to update scan {scan_id} progress: {e}")
            raise
        finally:
            session.close()

    def update_scan_results(self, scan_id: int, results: Dict[str, Any]) -> bool:
        """Update scan results
        
        Args:
            scan_id: Scan ID
            results: Scan results data
            
        Returns:
            True if updated successfully
        """
        session = self.db_manager.get_session()
        try:
            scan = session.query(Scan).filter_by(id=scan_id).first()
            if not scan:
                return False
            
            scan.results = results
            session.commit()
            
            self.logger.info(f"Updated scan {scan_id} results")
            return True
            
        except Exception as e:
            session.rollback()
            self.logger.error(f"Failed to update scan {scan_id} results: {e}")
            raise
        finally:
            session.close()

    def get_scans(self, filters: SearchFilters) -> List[Scan]:
        """Get scans with filtering and pagination
        
        Args:
            filters: Search and filter options
            
        Returns:
            List of scan objects
        """
        session = self.db_manager.get_session()
        try:
            query = session.query(Scan)
            
            # Apply filters
            if filters.campaign_id:
                query = query.filter(Scan.campaign_id == filters.campaign_id)
            
            if filters.status:
                query = query.filter(Scan.status == filters.status)
            
            if filters.date_from:
                query = query.filter(Scan.started_at >= filters.date_from)
            
            if filters.date_to:
                query = query.filter(Scan.started_at <= filters.date_to)
            
            if filters.search_term:
                query = query.filter(
                    or_(
                        Scan.name.contains(filters.search_term),
                        Scan.target.contains(filters.search_term),
                        Scan.summary.contains(filters.search_term)
                    )
                )
            
            # Apply sorting
            sort_column = getattr(Scan, filters.sort_by, Scan.started_at)
            if filters.sort_desc:
                query = query.order_by(desc(sort_column))
            else:
                query = query.order_by(asc(sort_column))
            
            # Apply pagination
            query = query.offset(filters.offset).limit(filters.limit)
            
            return query.all()
            
        finally:
            session.close()

    # =============================================================================
    # VULNERABILITY MANAGEMENT
    # =============================================================================

    def create_vulnerability(self, request: VulnerabilityCreateRequest) -> Vulnerability:
        """Create a new vulnerability record
        
        Args:
            request: Vulnerability creation request
            
        Returns:
            Created vulnerability object
        """
        session = self.db_manager.get_session()
        try:
            vulnerability = Vulnerability(
                scan_id=request.scan_id,
                campaign_id=request.campaign_id,
                vuln_type=request.vuln_type,
                severity=request.severity,
                title=request.title,
                description=request.description,
                affected_url=request.affected_url,
                affected_parameter=request.affected_parameter,
                evidence=request.evidence,
                remediation=request.remediation,
                cvss_score=request.cvss_score,
                cve_id=request.cve_id,
                tags=request.tags,
                metadata=request.metadata
            )
            
            session.add(vulnerability)
            session.commit()
            session.refresh(vulnerability)
            
            self.logger.info(f"Created vulnerability: {vulnerability.title} (ID: {vulnerability.id})")
            return vulnerability
            
        except Exception as e:
            session.rollback()
            self.logger.error(f"Failed to create vulnerability: {e}")
            raise
        finally:
            session.close()

    def update_vulnerability_status(self, vulnerability_id: int, status: str, notes: str = "") -> bool:
        """Update vulnerability status
        
        Args:
            vulnerability_id: Vulnerability ID
            status: New status
            notes: Optional status change notes
            
        Returns:
            True if updated successfully
        """
        session = self.db_manager.get_session()
        try:
            vulnerability = session.query(Vulnerability).filter_by(id=vulnerability_id).first()
            if not vulnerability:
                return False
            
            old_status = vulnerability.status
            vulnerability.status = status
            
            # Add to metadata
            if not vulnerability.metadata:
                vulnerability.metadata = {}
            
            if 'status_history' not in vulnerability.metadata:
                vulnerability.metadata['status_history'] = []
            
            vulnerability.metadata['status_history'].append({
                'from': old_status,
                'to': status,
                'timestamp': datetime.utcnow().isoformat(),
                'notes': notes
            })
            
            session.commit()
            
            self.logger.info(f"Updated vulnerability {vulnerability_id} status: {old_status} -> {status}")
            return True
            
        except Exception as e:
            session.rollback()
            self.logger.error(f"Failed to update vulnerability {vulnerability_id}: {e}")
            raise
        finally:
            session.close()

    def verify_vulnerability(self, vulnerability_id: int, verified: bool = True) -> bool:
        """Mark vulnerability as verified or unverified
        
        Args:
            vulnerability_id: Vulnerability ID
            verified: Verification status
            
        Returns:
            True if updated successfully
        """
        session = self.db_manager.get_session()
        try:
            vulnerability = session.query(Vulnerability).filter_by(id=vulnerability_id).first()
            if not vulnerability:
                return False
            
            vulnerability.verified = verified
            session.commit()
            
            self.logger.info(f"Vulnerability {vulnerability_id} verification: {verified}")
            return True
            
        except Exception as e:
            session.rollback()
            self.logger.error(f"Failed to verify vulnerability {vulnerability_id}: {e}")
            raise
        finally:
            session.close()

    def get_vulnerability(self, vulnerability_id: int) -> Optional[Vulnerability]:
        """Get vulnerability by ID
        
        Args:
            vulnerability_id: Vulnerability ID
            
        Returns:
            Vulnerability object or None if not found
        """
        session = self.db_manager.get_session()
        try:
            return session.query(Vulnerability).filter_by(id=vulnerability_id).first()
        finally:
            session.close()

    def get_vulnerabilities(self, filters: SearchFilters) -> List[Vulnerability]:
        """Get vulnerabilities with filtering and pagination
        
        Args:
            filters: Search and filter options
            
        Returns:
            List of vulnerability objects
        """
        session = self.db_manager.get_session()
        try:
            query = session.query(Vulnerability)
            
            # Apply filters
            if filters.campaign_id:
                query = query.filter(Vulnerability.campaign_id == filters.campaign_id)
            
            if filters.scan_id:
                query = query.filter(Vulnerability.scan_id == filters.scan_id)
            
            if filters.vulnerability_type:
                query = query.filter(Vulnerability.vuln_type == filters.vulnerability_type)
            
            if filters.severity:
                query = query.filter(Vulnerability.severity == filters.severity)
            
            if filters.status:
                query = query.filter(Vulnerability.status == filters.status)
            
            if filters.date_from:
                query = query.filter(Vulnerability.discovered_at >= filters.date_from)
            
            if filters.date_to:
                query = query.filter(Vulnerability.discovered_at <= filters.date_to)
            
            if filters.cvss_min is not None:
                query = query.filter(Vulnerability.cvss_score >= filters.cvss_min)
            
            if filters.cvss_max is not None:
                query = query.filter(Vulnerability.cvss_score <= filters.cvss_max)
            
            if filters.verified_only:
                query = query.filter(Vulnerability.verified == True)
            
            if filters.exploitable_only:
                query = query.filter(Vulnerability.exploitable == True)
            
            if filters.search_term:
                query = query.filter(
                    or_(
                        Vulnerability.title.contains(filters.search_term),
                        Vulnerability.description.contains(filters.search_term),
                        Vulnerability.affected_url.contains(filters.search_term),
                        Vulnerability.cve_id.contains(filters.search_term)
                    )
                )
            
            if filters.tags:
                # Filter vulnerabilities that contain any of the specified tags
                for tag in filters.tags:
                    query = query.filter(Vulnerability.tags.contains([tag]))
            
            # Apply sorting
            sort_column = getattr(Vulnerability, filters.sort_by, Vulnerability.discovered_at)
            if filters.sort_desc:
                query = query.order_by(desc(sort_column))
            else:
                query = query.order_by(asc(sort_column))
            
            # Apply pagination
            query = query.offset(filters.offset).limit(filters.limit)
            
            return query.all()
            
        finally:
            session.close()

    def get_vulnerability_statistics(self, campaign_id: Optional[int] = None) -> Dict[str, Any]:
        """Get vulnerability statistics
        
        Args:
            campaign_id: Optional campaign ID to filter by
            
        Returns:
            Statistics dictionary
        """
        session = self.db_manager.get_session()
        try:
            query = session.query(Vulnerability)
            if campaign_id:
                query = query.filter(Vulnerability.campaign_id == campaign_id)
            
            # Total count
            total = query.count()
            
            # Count by severity
            severity_stats = {}
            for severity in VulnerabilitySeverity:
                count = query.filter(Vulnerability.severity == severity.value).count()
                severity_stats[severity.value] = count
            
            # Count by status
            status_stats = {}
            for status in VulnerabilityStatus:
                count = query.filter(Vulnerability.status == status.value).count()
                status_stats[status.value] = count
            
            # Count by type
            type_stats = {}
            type_results = session.query(
                Vulnerability.vuln_type, 
                func.count(Vulnerability.id)
            ).group_by(Vulnerability.vuln_type)
            
            if campaign_id:
                type_results = type_results.filter(Vulnerability.campaign_id == campaign_id)
            
            for vuln_type, count in type_results.all():
                type_stats[vuln_type] = count
            
            # CVSS statistics
            cvss_stats = session.query(
                func.avg(Vulnerability.cvss_score),
                func.min(Vulnerability.cvss_score),
                func.max(Vulnerability.cvss_score)
            )
            
            if campaign_id:
                cvss_stats = cvss_stats.filter(Vulnerability.campaign_id == campaign_id)
            
            avg_cvss, min_cvss, max_cvss = cvss_stats.first()
            
            return {
                "total": total,
                "by_severity": severity_stats,
                "by_status": status_stats,
                "by_type": type_stats,
                "cvss_stats": {
                    "average": float(avg_cvss) if avg_cvss else 0.0,
                    "minimum": float(min_cvss) if min_cvss else 0.0,
                    "maximum": float(max_cvss) if max_cvss else 0.0
                },
                "verified_count": query.filter(Vulnerability.verified == True).count(),
                "exploitable_count": query.filter(Vulnerability.exploitable == True).count()
            }
            
        finally:
            session.close()

    # =============================================================================
    # AI ANALYSIS MANAGEMENT
    # =============================================================================

    def create_ai_analysis(self, request: AIAnalysisCreateRequest) -> AIAnalysis:
        """Create AI analysis record
        
        Args:
            request: AI analysis creation request
            
        Returns:
            Created AI analysis object
        """
        session = self.db_manager.get_session()
        try:
            analysis = AIAnalysis(
                vulnerability_id=request.vulnerability_id,
                scan_id=request.scan_id,
                campaign_id=request.campaign_id,
                analysis_type=request.analysis_type,
                ai_provider=request.ai_provider,
                model_used=request.model_used,
                input_data=request.input_data,
                output_data=request.output_data,
                confidence_score=request.confidence_score,
                execution_time_ms=request.execution_time_ms
            )
            
            session.add(analysis)
            session.commit()
            session.refresh(analysis)
            
            self.logger.info(f"Created AI analysis: {analysis.analysis_type} (ID: {analysis.id})")
            return analysis
            
        except Exception as e:
            session.rollback()
            self.logger.error(f"Failed to create AI analysis: {e}")
            raise
        finally:
            session.close()

    def get_ai_analyses(self, 
                       vulnerability_id: Optional[int] = None,
                       scan_id: Optional[int] = None,
                       campaign_id: Optional[int] = None,
                       analysis_type: Optional[str] = None,
                       ai_provider: Optional[str] = None,
                       limit: int = 100) -> List[AIAnalysis]:
        """Get AI analyses with filtering
        
        Args:
            vulnerability_id: Filter by vulnerability
            scan_id: Filter by scan
            campaign_id: Filter by campaign
            analysis_type: Filter by analysis type
            ai_provider: Filter by AI provider
            limit: Maximum number of results
            
        Returns:
            List of AI analysis objects
        """
        session = self.db_manager.get_session()
        try:
            query = session.query(AIAnalysis)
            
            if vulnerability_id:
                query = query.filter(AIAnalysis.vulnerability_id == vulnerability_id)
            
            if scan_id:
                query = query.filter(AIAnalysis.scan_id == scan_id)
            
            if campaign_id:
                query = query.filter(AIAnalysis.campaign_id == campaign_id)
            
            if analysis_type:
                query = query.filter(AIAnalysis.analysis_type == analysis_type)
            
            if ai_provider:
                query = query.filter(AIAnalysis.ai_provider == ai_provider)
            
            query = query.order_by(desc(AIAnalysis.created_at)).limit(limit)
            return query.all()
            
        finally:
            session.close()

    # =============================================================================
    # FILE ATTACHMENT MANAGEMENT
    # =============================================================================

    def create_file_attachment(self, 
                             filename: str,
                             file_path: str,
                             file_size: int,
                             mime_type: str,
                             file_content: bytes,
                             vulnerability_id: Optional[int] = None,
                             scan_id: Optional[int] = None,
                             campaign_id: Optional[int] = None,
                             description: str = "") -> FileAttachment:
        """Create file attachment record
        
        Args:
            filename: Display filename
            file_path: Storage file path
            file_size: File size in bytes
            mime_type: MIME type
            file_content: File content for hash calculation
            vulnerability_id: Associated vulnerability
            scan_id: Associated scan
            campaign_id: Associated campaign
            description: File description
            
        Returns:
            Created file attachment object
        """
        session = self.db_manager.get_session()
        try:
            # Calculate SHA256 hash
            file_hash = hashlib.sha256(file_content).hexdigest()
            
            attachment = FileAttachment(
                vulnerability_id=vulnerability_id,
                scan_id=scan_id,
                campaign_id=campaign_id,
                filename=filename,
                original_filename=filename,
                file_path=file_path,
                file_size=file_size,
                mime_type=mime_type,
                file_hash=file_hash,
                description=description
            )
            
            session.add(attachment)
            session.commit()
            session.refresh(attachment)
            
            self.logger.info(f"Created file attachment: {filename} (ID: {attachment.id})")
            return attachment
            
        except Exception as e:
            session.rollback()
            self.logger.error(f"Failed to create file attachment: {e}")
            raise
        finally:
            session.close()

    def get_file_attachments(self,
                           vulnerability_id: Optional[int] = None,
                           scan_id: Optional[int] = None,
                           campaign_id: Optional[int] = None) -> List[FileAttachment]:
        """Get file attachments
        
        Args:
            vulnerability_id: Filter by vulnerability
            scan_id: Filter by scan
            campaign_id: Filter by campaign
            
        Returns:
            List of file attachment objects
        """
        session = self.db_manager.get_session()
        try:
            query = session.query(FileAttachment)
            
            if vulnerability_id:
                query = query.filter(FileAttachment.vulnerability_id == vulnerability_id)
            
            if scan_id:
                query = query.filter(FileAttachment.scan_id == scan_id)
            
            if campaign_id:
                query = query.filter(FileAttachment.campaign_id == campaign_id)
            
            return query.order_by(desc(FileAttachment.uploaded_at)).all()
            
        finally:
            session.close()

    # =============================================================================
    # ACTIVITY LOGGING
    # =============================================================================

    def log_activity(self, 
                    user_id: str,
                    action: str,
                    resource_type: Optional[str] = None,
                    resource_id: Optional[int] = None,
                    details: Optional[Dict[str, Any]] = None,
                    ip_address: Optional[str] = None,
                    user_agent: Optional[str] = None,
                    session_id: Optional[str] = None) -> ActivityLog:
        """Log user activity
        
        Args:
            user_id: User identifier
            action: Action performed
            resource_type: Type of resource (campaign, scan, etc.)
            resource_id: Resource ID
            details: Additional details
            ip_address: User IP address
            user_agent: User agent string
            session_id: Session identifier
            
        Returns:
            Created activity log object
        """
        session = self.db_manager.get_session()
        try:
            activity = ActivityLog(
                user_id=user_id,
                action=action,
                resource_type=resource_type,
                resource_id=resource_id,
                details=details,
                ip_address=ip_address,
                user_agent=user_agent,
                session_id=session_id
            )
            
            session.add(activity)
            session.commit()
            session.refresh(activity)
            
            return activity
            
        except Exception as e:
            session.rollback()
            self.logger.error(f"Failed to log activity: {e}")
            raise
        finally:
            session.close()

    def get_activity_log(self, 
                        user_id: Optional[str] = None,
                        resource_type: Optional[str] = None,
                        resource_id: Optional[int] = None,
                        date_from: Optional[datetime] = None,
                        date_to: Optional[datetime] = None,
                        limit: int = 100) -> List[ActivityLog]:
        """Get activity log entries
        
        Args:
            user_id: Filter by user
            resource_type: Filter by resource type
            resource_id: Filter by resource ID
            date_from: Filter from date
            date_to: Filter to date
            limit: Maximum number of results
            
        Returns:
            List of activity log objects
        """
        session = self.db_manager.get_session()
        try:
            query = session.query(ActivityLog)
            
            if user_id:
                query = query.filter(ActivityLog.user_id == user_id)
            
            if resource_type:
                query = query.filter(ActivityLog.resource_type == resource_type)
            
            if resource_id:
                query = query.filter(ActivityLog.resource_id == resource_id)
            
            if date_from:
                query = query.filter(ActivityLog.timestamp >= date_from)
            
            if date_to:
                query = query.filter(ActivityLog.timestamp <= date_to)
            
            return query.order_by(desc(ActivityLog.timestamp)).limit(limit).all()
            
        finally:
            session.close()

    # =============================================================================
    # BULK OPERATIONS
    # =============================================================================

    def bulk_create_vulnerabilities(self, vulnerabilities: List[VulnerabilityCreateRequest]) -> List[Vulnerability]:
        """Create multiple vulnerabilities in a single transaction
        
        Args:
            vulnerabilities: List of vulnerability creation requests
            
        Returns:
            List of created vulnerability objects
        """
        session = self.db_manager.get_session()
        try:
            created_vulns = []
            
            for vuln_request in vulnerabilities:
                vulnerability = Vulnerability(
                    scan_id=vuln_request.scan_id,
                    campaign_id=vuln_request.campaign_id,
                    vuln_type=vuln_request.vuln_type,
                    severity=vuln_request.severity,
                    title=vuln_request.title,
                    description=vuln_request.description,
                    affected_url=vuln_request.affected_url,
                    affected_parameter=vuln_request.affected_parameter,
                    evidence=vuln_request.evidence,
                    remediation=vuln_request.remediation,
                    cvss_score=vuln_request.cvss_score,
                    cve_id=vuln_request.cve_id,
                    tags=vuln_request.tags,
                    metadata=vuln_request.metadata
                )
                
                session.add(vulnerability)
                created_vulns.append(vulnerability)
            
            session.commit()
            
            # Refresh all objects to get IDs
            for vuln in created_vulns:
                session.refresh(vuln)
            
            self.logger.info(f"Bulk created {len(created_vulns)} vulnerabilities")
            return created_vulns
            
        except Exception as e:
            session.rollback()
            self.logger.error(f"Failed to bulk create vulnerabilities: {e}")
            raise
        finally:
            session.close()

    def bulk_update_vulnerability_status(self, 
                                       vulnerability_ids: List[int], 
                                       status: str) -> int:
        """Update status for multiple vulnerabilities
        
        Args:
            vulnerability_ids: List of vulnerability IDs
            status: New status
            
        Returns:
            Number of vulnerabilities updated
        """
        session = self.db_manager.get_session()
        try:
            updated = session.query(Vulnerability).filter(
                Vulnerability.id.in_(vulnerability_ids)
            ).update({Vulnerability.status: status}, synchronize_session=False)
            
            session.commit()
            
            self.logger.info(f"Bulk updated {updated} vulnerabilities to status: {status}")
            return updated
            
        except Exception as e:
            session.rollback()
            self.logger.error(f"Failed to bulk update vulnerability status: {e}")
            raise
        finally:
            session.close()

    # =============================================================================
    # DATA CLEANUP AND MAINTENANCE
    # =============================================================================

    def cleanup_old_data(self, days_to_keep: int = 90) -> Dict[str, int]:
        """Clean up old data beyond retention period
        
        Args:
            days_to_keep: Number of days to keep data
            
        Returns:
            Dictionary with cleanup statistics
        """
        session = self.db_manager.get_session()
        try:
            cutoff_date = datetime.utcnow() - timedelta(days=days_to_keep)
            
            stats = {
                "activity_logs_deleted": 0,
                "old_scans_deleted": 0,
                "ai_analyses_deleted": 0
            }
            
            # Clean up old activity logs
            deleted_logs = session.query(ActivityLog).filter(
                ActivityLog.timestamp < cutoff_date
            ).delete(synchronize_session=False)
            stats["activity_logs_deleted"] = deleted_logs
            
            # Clean up old failed scans
            deleted_scans = session.query(Scan).filter(
                and_(
                    Scan.status == ScanStatus.FAILED.value,
                    Scan.started_at < cutoff_date
                )
            ).delete(synchronize_session=False)
            stats["old_scans_deleted"] = deleted_scans
            
            # Clean up old AI analyses (keep only recent ones)
            deleted_analyses = session.query(AIAnalysis).filter(
                AIAnalysis.created_at < cutoff_date
            ).delete(synchronize_session=False)
            stats["ai_analyses_deleted"] = deleted_analyses
            
            session.commit()
            
            self.logger.info(f"Data cleanup completed: {stats}")
            return stats
            
        except Exception as e:
            session.rollback()
            self.logger.error(f"Failed to cleanup old data: {e}")
            raise
        finally:
            session.close()