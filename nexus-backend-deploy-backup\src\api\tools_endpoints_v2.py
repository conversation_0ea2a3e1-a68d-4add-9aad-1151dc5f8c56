#!/usr/bin/env python3
"""
Enhanced Tools Endpoints V2 for NexusScan Desktop
Railway-compatible tool endpoints with simulation support
"""

import asyncio
import logging
import json
from typing import Optional, Dict, Any, List
from aiohttp import web, web_request, web_response
from datetime import datetime, timezone
import uuid

from core.config import Config
from security.tools.unified_tool_manager_v2 import unified_tool_manager
from security.tools.tool_registry import ScanOptions

logger = logging.getLogger(__name__)


class ToolsEndpointsV2:
    """Enhanced tools endpoints with Railway support"""
    
    def __init__(self, config=None):
        self.config = config or Config()
        self.active_scans: Dict[str, Dict] = {}
        self.scan_history: List[Dict] = []
        
    async def initialize(self):
        """Initialize tools infrastructure"""
        logger.info("Initializing tools endpoints v2...")
        try:
            init_result = await unified_tool_manager.initialize()
            # Handle different return formats from unified_tool_manager_v2
            if isinstance(init_result, dict):
                tools_count = init_result.get('tools_available', init_result.get('available_tools', len(init_result.get('tools', []))))
                logger.info(f"Tools initialized: {tools_count} available")
            else:
                logger.info(f"Tools initialized successfully")
            return init_result
        except Exception as e:
            logger.error(f"Tools initialization failed: {e}")
            # Don't raise - let the service continue without tools endpoints v2
            return None
    
    async def get_available_tools(self, request: web_request.Request) -> web_response.Response:
        """Get list of all available security tools"""
        try:
            tools_data = unified_tool_manager.get_available_tools()
            
            if not tools_data:
                # Ensure tools are initialized
                await self.initialize()
                tools_data = unified_tool_manager.get_available_tools()
            
            return web.json_response({
                "success": True,
                "data": tools_data[0]["data"] if tools_data else [],
                "metadata": tools_data[0]["metadata"] if tools_data else {}
            })
            
        except Exception as e:
            logger.error(f"Failed to get available tools: {e}")
            return web.json_response({
                "success": False,
                "error": str(e),
                "data": []
            }, status=500)
    
    async def get_tool_status(self, request: web_request.Request) -> web_response.Response:
        """Get status of a specific tool"""
        try:
            tool_name = request.match_info.get('tool_name')
            
            if not tool_name:
                return web.json_response({
                    "success": False,
                    "error": "Tool name is required"
                }, status=400)
            
            tool_info = unified_tool_manager.get_tool_info(tool_name)
            
            if not tool_info:
                return web.json_response({
                    "success": False,
                    "error": f"Tool {tool_name} not found"
                }, status=404)
            
            return web.json_response({
                "success": True,
                "data": tool_info
            })
            
        except Exception as e:
            logger.error(f"Failed to get tool status: {e}")
            return web.json_response({
                "success": False,
                "error": str(e)
            }, status=500)
    
    async def execute_tool_scan(self, request: web_request.Request) -> web_response.Response:
        """Execute a tool scan"""
        try:
            tool_name = request.match_info.get('tool_name')
            
            if not tool_name:
                return web.json_response({
                    "success": False,
                    "error": "Tool name is required"
                }, status=400)
            
            # Parse request body
            try:
                body = await request.json()
            except Exception:
                return web.json_response({
                    "success": False,
                    "error": "Invalid JSON in request body"
                }, status=400)
            
            # Validate required fields
            target = body.get('target')
            if not target:
                return web.json_response({
                    "success": False,
                    "error": "Target is required"
                }, status=400)
            
            # Create scan options
            scan_options = ScanOptions(
                target=target,
                timeout=body.get('timeout', 300),
                threads=body.get('threads', 1),
                output_format=body.get('output_format', 'json'),
                custom_options=body.get('options', {})
            )
            
            # Generate scan ID
            scan_id = str(uuid.uuid4())
            
            # Track active scan
            self.active_scans[scan_id] = {
                "id": scan_id,
                "tool": tool_name,
                "target": target,
                "status": "running",
                "start_time": datetime.now(timezone.utc).isoformat(),
                "progress": 0.0
            }
            
            # Progress callback
            async def progress_callback(progress: float, message: str):
                if scan_id in self.active_scans:
                    self.active_scans[scan_id]["progress"] = progress
                    self.active_scans[scan_id]["message"] = message
            
            try:
                # Execute tool
                result = await unified_tool_manager.execute_tool(
                    tool_name, 
                    scan_options, 
                    progress_callback
                )
                
                # Update scan status
                if scan_id in self.active_scans:
                    self.active_scans[scan_id].update({
                        "status": result.status,
                        "end_time": datetime.now(timezone.utc).isoformat(),
                        "progress": 100.0,
                        "result": result
                    })
                
                # Move to history
                self.scan_history.append(self.active_scans[scan_id])
                if len(self.scan_history) > 100:  # Keep last 100 scans
                    self.scan_history.pop(0)
                
                # Remove from active
                del self.active_scans[scan_id]
                
                # Return result
                return web.json_response({
                    "success": True,
                    "data": {
                        "scan_id": scan_id,
                        "tool": tool_name,
                        "target": target,
                        "status": result.status,
                        "start_time": result.start_time,
                        "end_time": result.end_time,
                        "duration_seconds": result.duration_seconds,
                        "results": result.parsed_results,
                        "vulnerabilities": result.vulnerabilities,
                        "metadata": result.metadata,
                        "errors": result.errors,
                        "warnings": result.warnings
                    }
                })
                
            except Exception as e:
                # Update scan with error
                if scan_id in self.active_scans:
                    self.active_scans[scan_id].update({
                        "status": "failed",
                        "end_time": datetime.now(timezone.utc).isoformat(),
                        "error": str(e)
                    })
                
                logger.error(f"Tool execution failed: {e}")
                return web.json_response({
                    "success": False,
                    "error": f"Tool execution failed: {str(e)}",
                    "scan_id": scan_id
                }, status=500)
                
        except Exception as e:
            logger.error(f"Failed to execute tool scan: {e}")
            return web.json_response({
                "success": False,
                "error": str(e)
            }, status=500)
    
    async def get_scan_status(self, request: web_request.Request) -> web_response.Response:
        """Get status of a running scan"""
        try:
            scan_id = request.match_info.get('scan_id')
            
            if not scan_id:
                return web.json_response({
                    "success": False,
                    "error": "Scan ID is required"
                }, status=400)
            
            # Check active scans
            if scan_id in self.active_scans:
                scan_info = self.active_scans[scan_id]
                return web.json_response({
                    "success": True,
                    "data": scan_info
                })
            
            # Check scan history
            for scan in self.scan_history:
                if scan["id"] == scan_id:
                    return web.json_response({
                        "success": True,
                        "data": scan
                    })
            
            return web.json_response({
                "success": False,
                "error": f"Scan {scan_id} not found"
            }, status=404)
            
        except Exception as e:
            logger.error(f"Failed to get scan status: {e}")
            return web.json_response({
                "success": False,
                "error": str(e)
            }, status=500)
    
    async def get_active_scans(self, request: web_request.Request) -> web_response.Response:
        """Get list of active scans"""
        try:
            return web.json_response({
                "success": True,
                "data": list(self.active_scans.values())
            })
        except Exception as e:
            logger.error(f"Failed to get active scans: {e}")
            return web.json_response({
                "success": False,
                "error": str(e)
            }, status=500)
    
    async def get_scan_history(self, request: web_request.Request) -> web_response.Response:
        """Get scan history"""
        try:
            limit = int(request.query.get('limit', 50))
            offset = int(request.query.get('offset', 0))
            
            # Get paginated history
            history_slice = self.scan_history[offset:offset + limit]
            
            return web.json_response({
                "success": True,
                "data": history_slice,
                "metadata": {
                    "total": len(self.scan_history),
                    "limit": limit,
                    "offset": offset
                }
            })
        except Exception as e:
            logger.error(f"Failed to get scan history: {e}")
            return web.json_response({
                "success": False,
                "error": str(e)
            }, status=500)
    
    async def get_tools_health(self, request: web_request.Request) -> web_response.Response:
        """Get tools health status"""
        try:
            status = unified_tool_manager.get_status()
            
            return web.json_response({
                "success": True,
                "data": status
            })
        except Exception as e:
            logger.error(f"Failed to get tools health: {e}")
            return web.json_response({
                "success": False,
                "error": str(e)
            }, status=500)


# Global instance
tools_endpoints = ToolsEndpointsV2()


def setup_tools_routes(app: web.Application):
    """Setup tools API routes"""
    # Tools management
    app.router.add_get('/api/tools/available', tools_endpoints.get_available_tools)
    app.router.add_get('/api/tools/health', tools_endpoints.get_tools_health)
    app.router.add_get('/api/tools/{tool_name}/status', tools_endpoints.get_tool_status)
    
    # Tool execution
    app.router.add_post('/api/tools/{tool_name}/scan', tools_endpoints.execute_tool_scan)
    
    # Scan management
    app.router.add_get('/api/scans/active', tools_endpoints.get_active_scans)
    app.router.add_get('/api/scans/history', tools_endpoints.get_scan_history)
    app.router.add_get('/api/scans/{scan_id}/status', tools_endpoints.get_scan_status)
    
    # Legacy compatibility routes
    app.router.add_post('/api/tools/nmap/scan', tools_endpoints.execute_tool_scan)
    app.router.add_post('/api/tools/nuclei/scan', tools_endpoints.execute_tool_scan)
    app.router.add_post('/api/tools/sqlmap/scan', tools_endpoints.execute_tool_scan)