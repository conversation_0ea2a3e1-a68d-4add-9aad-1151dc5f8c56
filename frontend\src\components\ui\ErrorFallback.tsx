/**
 * Error Fallback Component - Enhanced Professional Error Handling
 * Comprehensive error boundary with severity analysis and user guidance
 */
import React from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from './card';
import { But<PERSON> } from './button';
import { Badge } from './badge';
import { Alert } from './alert';
import { 
  AlertTriangle, 
  RefreshCw, 
  Home, 
  Bug, 
  Copy,
  ChevronDown,
  ChevronRight,
  ExternalLink,
  Shield,
  Activity
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface ErrorInfo {
  componentStack: string;
  errorBoundary: boolean;
}

interface ErrorFallbackProps {
  error: Error;
  errorInfo?: ErrorInfo;
  resetError?: () => void;
  onRetry?: () => void;
  onReportBug?: (error: Error, errorInfo?: ErrorInfo) => void;
  className?: string;
}

export const ErrorFallback: React.FC<ErrorFallbackProps> = ({
  error,
  errorInfo,
  resetError,
  onRetry,
  onReportBug,
  className
}) => {
  const [showDetails, setShowDetails] = React.useState(false);
  const [copied, setCopied] = React.useState(false);

  const errorDetails = {
    message: error.message,
    stack: error.stack,
    componentStack: errorInfo?.componentStack,
    timestamp: new Date().toISOString(),
    userAgent: navigator.userAgent,
    url: window.location.href
  };

  const copyErrorDetails = () => {
    const details = JSON.stringify(errorDetails, null, 2);
    navigator.clipboard.writeText(details);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  const getErrorSeverity = (error: Error): 'critical' | 'high' | 'medium' | 'low' => {
    const message = error.message.toLowerCase();
    
    if (message.includes('chunk') || message.includes('loading')) return 'medium';
    if (message.includes('network') || message.includes('fetch')) return 'high';
    if (message.includes('reference') || message.includes('undefined')) return 'critical';
    if (message.includes('permission') || message.includes('auth')) return 'high';
    
    return 'high';
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical':
        return 'bg-red-600 text-white';
      case 'high':
        return 'bg-red-500 text-white';
      case 'medium':
        return 'bg-yellow-500 text-white';
      case 'low':
        return 'bg-blue-500 text-white';
      default:
        return 'bg-gray-500 text-white';
    }
  };

  const getErrorRecommendation = (error: Error): string => {
    const message = error.message.toLowerCase();
    
    if (message.includes('chunk') || message.includes('loading')) {
      return 'This appears to be a loading issue. Try refreshing the page or checking your internet connection.';
    }
    if (message.includes('network') || message.includes('fetch')) {
      return 'This is a network connectivity issue. Check your connection to the backend server at ec2-3-89-91-209.compute-1.amazonaws.com:8000.';
    }
    if (message.includes('reference') || message.includes('undefined')) {
      return 'This is a code reference error. Please report this as a bug with the technical details below.';
    }
    if (message.includes('permission') || message.includes('auth')) {
      return 'This appears to be an authentication or permission issue. Try refreshing to re-authenticate.';
    }
    
    return 'An unexpected error occurred. Try refreshing or report this issue if it persists.';
  };

  const severity = getErrorSeverity(error);

  return (
    <div className={cn("min-h-screen flex items-center justify-center p-4 bg-background", className)}>
      <Card className="w-full max-w-3xl">
        <CardHeader className="text-center">
          <div className="flex items-center justify-center space-x-2 mb-4">
            <AlertTriangle className="h-8 w-8 text-red-500" />
            <CardTitle className="text-xl">NexusScan Desktop Error</CardTitle>
          </div>
          <Badge className={`mx-auto ${getSeverityColor(severity)}`}>
            {severity.toUpperCase()} SEVERITY
          </Badge>
        </CardHeader>
        
        <CardContent className="space-y-6">
          {/* Error Summary */}
          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <div>
              <h4 className="font-semibold mb-1">Error Details</h4>
              <p className="text-sm text-muted-foreground">{error.message}</p>
            </div>
          </Alert>

          {/* Recommendation */}
          <div className="p-4 bg-muted/50 rounded-lg">
            <h4 className="font-medium mb-2 flex items-center space-x-2">
              <Shield className="h-4 w-4" />
              <span>Recommended Action</span>
            </h4>
            <p className="text-sm text-muted-foreground">
              {getErrorRecommendation(error)}
            </p>
          </div>

          {/* Action Buttons */}
          <div className="flex flex-wrap gap-3 justify-center">
            {onRetry && (
              <Button onClick={onRetry} className="flex items-center space-x-2">
                <RefreshCw className="h-4 w-4" />
                <span>Retry</span>
              </Button>
            )}
            
            {resetError && (
              <Button variant="outline" onClick={resetError} className="flex items-center space-x-2">
                <Home className="h-4 w-4" />
                <span>Go Home</span>
              </Button>
            )}
            
            <Button 
              variant="outline" 
              onClick={() => window.location.reload()}
              className="flex items-center space-x-2"
            >
              <RefreshCw className="h-4 w-4" />
              <span>Refresh Page</span>
            </Button>
          </div>

          {/* Quick Diagnostics */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
            <div className="p-3 border rounded-lg">
              <div className="flex items-center space-x-2">
                <Activity className="h-4 w-4 text-blue-500" />
                <span className="text-sm font-medium">Connection Status</span>
              </div>
              <div className="text-xs text-muted-foreground mt-1">
                Check: AWS backend connectivity
              </div>
            </div>
            <div className="p-3 border rounded-lg">
              <div className="flex items-center space-x-2">
                <Shield className="h-4 w-4 text-green-500" />
                <span className="text-sm font-medium">Error Isolation</span>
              </div>
              <div className="text-xs text-muted-foreground mt-1">
                Component: {errorInfo?.componentStack ? 'Identified' : 'Application'}
              </div>
            </div>
            <div className="p-3 border rounded-lg">
              <div className="flex items-center space-x-2">
                <Bug className="h-4 w-4 text-orange-500" />
                <span className="text-sm font-medium">Recovery</span>
              </div>
              <div className="text-xs text-muted-foreground mt-1">
                Auto-recovery: Available
              </div>
            </div>
          </div>

          {/* Technical Details (Collapsible) */}
          <div className="border-t pt-4">
            <button
              onClick={() => setShowDetails(!showDetails)}
              className="flex items-center space-x-2 text-sm text-muted-foreground hover:text-foreground transition-colors"
            >
              {showDetails ? (
                <ChevronDown className="h-4 w-4" />
              ) : (
                <ChevronRight className="h-4 w-4" />
              )}
              <span>Technical Details & Debugging Information</span>
            </button>
            
            {showDetails && (
              <div className="mt-3 space-y-3">
                <div className="p-3 bg-muted rounded-md font-mono text-xs">
                  <div className="mb-2 font-semibold">Error Message:</div>
                  <div className="text-red-600 mb-3">{error.message}</div>
                  
                  {error.stack && (
                    <>
                      <div className="mb-2 font-semibold">Stack Trace:</div>
                      <pre className="whitespace-pre-wrap text-xs overflow-x-auto max-h-48">
                        {error.stack}
                      </pre>
                    </>
                  )}
                  
                  {errorInfo?.componentStack && (
                    <>
                      <div className="mb-2 font-semibold mt-3">Component Stack:</div>
                      <pre className="whitespace-pre-wrap text-xs overflow-x-auto max-h-32">
                        {errorInfo.componentStack}
                      </pre>
                    </>
                  )}
                  
                  <div className="mt-3 grid grid-cols-2 gap-2 text-xs">
                    <div>
                      <strong>Timestamp:</strong>
                      <div>{new Date().toLocaleString()}</div>
                    </div>
                    <div>
                      <strong>URL:</strong>
                      <div className="truncate">{window.location.href}</div>
                    </div>
                    <div>
                      <strong>User Agent:</strong>
                      <div className="truncate">{navigator.userAgent}</div>
                    </div>
                    <div>
                      <strong>Error Boundary:</strong>
                      <div>{errorInfo?.errorBoundary ? 'Yes' : 'No'}</div>
                    </div>
                  </div>
                </div>
                
                <div className="flex space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={copyErrorDetails}
                    className="flex items-center space-x-1"
                  >
                    <Copy className="h-3 w-3" />
                    <span>{copied ? 'Copied!' : 'Copy Details'}</span>
                  </Button>
                  
                  {onReportBug && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => onReportBug(error, errorInfo)}
                      className="flex items-center space-x-1"
                    >
                      <Bug className="h-3 w-3" />
                      <span>Report Bug</span>
                    </Button>
                  )}
                </div>
              </div>
            )}
          </div>

          {/* Additional Help */}
          <div className="text-center text-sm text-muted-foreground border-t pt-4">
            <p className="font-medium mb-2">Troubleshooting Checklist:</p>
            <ul className="space-y-1 text-left max-w-md mx-auto">
              <li>• Check internet connection</li>
              <li>• Verify backend server status (AWS EC2)</li>
              <li>• Clear browser cache and reload</li>
              <li>• Check browser console for additional errors</li>
              <li>• Ensure JavaScript is enabled</li>
            </ul>
            
            <div className="mt-4 flex justify-center space-x-4">
              <a
                href="https://github.com/nexusscan/nexusscan-desktop/issues"
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center space-x-1 text-blue-500 hover:underline"
              >
                <ExternalLink className="h-3 w-3" />
                <span>GitHub Issues</span>
              </a>
              <span className="text-muted-foreground">•</span>
              <a
                href="mailto:<EMAIL>"
                className="inline-flex items-center space-x-1 text-blue-500 hover:underline"
              >
                <span>Contact Support</span>
              </a>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

// Error Boundary Component
interface ErrorBoundaryState {
  hasError: boolean;
  error?: Error;
  errorInfo?: ErrorInfo;
}

export class ErrorBoundary extends React.Component<
  React.PropsWithChildren<{
    fallback?: React.ComponentType<ErrorFallbackProps>;
    onError?: (error: Error, errorInfo: ErrorInfo) => void;
  }>,
  ErrorBoundaryState
> {
  constructor(props: any) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return {
      hasError: true,
      error,
    };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    const info: ErrorInfo = {
      componentStack: errorInfo.componentStack,
      errorBoundary: true,
    };

    this.setState({
      error,
      errorInfo: info,
    });

    // Call the onError callback if provided
    this.props.onError?.(error, info);

    // Log to console for development
    console.error('🚨 Error caught by boundary:', error);
    console.error('📍 Error info:', errorInfo);

    // Report to external service in production
    if (process.env.NODE_ENV === 'production') {
      // TODO: Integrate with error reporting service
      console.log('📊 Error would be reported to monitoring service');
    }
  }

  resetError = () => {
    this.setState({ hasError: false, error: undefined, errorInfo: undefined });
  };

  render() {
    if (this.state.hasError && this.state.error) {
      const FallbackComponent = this.props.fallback || ErrorFallback;
      
      return (
        <FallbackComponent
          error={this.state.error}
          errorInfo={this.state.errorInfo}
          resetError={this.resetError}
        />
      );
    }

    return this.props.children;
  }
}

// Utility hook for handling async errors
export const useErrorHandler = () => {
  const [error, setError] = React.useState<Error | null>(null);

  const resetError = React.useCallback(() => {
    setError(null);
  }, []);

  const handleError = React.useCallback((error: Error | string) => {
    const errorObj = typeof error === 'string' ? new Error(error) : error;
    setError(errorObj);
    console.error('🚨 Handled error:', errorObj);
  }, []);

  const handleAsyncError = React.useCallback((asyncFn: () => Promise<any>) => {
    return asyncFn().catch(handleError);
  }, [handleError]);

  const reportError = React.useCallback((error: Error, context?: any) => {
    console.error('📊 Reporting error:', error, context);
    // TODO: Integrate with error reporting service
  }, []);

  return {
    error,
    handleError,
    handleAsyncError,
    resetError,
    reportError,
  };
};