#!/usr/bin/env python3
"""
Advanced Caching System for NexusScan
Enterprise-grade multi-layer caching with Redis, in-memory, and intelligent cache strategies
"""

import json
import time
import hashlib
import asyncio
import logging
import pickle
from typing import Any, Dict, List, Optional, Union, Callable, Tuple
from dataclasses import dataclass, asdict
from datetime import datetime, timed<PERSON>ta
from enum import Enum
import threading
from contextlib import asynccontextmanager

try:
    import redis.asyncio as redis
    REDIS_AVAILABLE = True
except ImportError:
    REDIS_AVAILABLE = False

# Setup logging
logger = logging.getLogger(__name__)

class CacheStrategy(Enum):
    """Cache strategy types"""
    LRU = "lru"  # Least Recently Used
    LFU = "lfu"  # Least Frequently Used
    TTL = "ttl"  # Time To Live
    FIFO = "fifo"  # First In First Out
    ADAPTIVE = "adaptive"  # Intelligent adaptive strategy

class CacheLayer(Enum):
    """Cache layer types"""
    MEMORY = "memory"
    REDIS = "redis"
    HYBRID = "hybrid"
    DISTRIBUTED = "distributed"

@dataclass
class CacheEntry:
    """Cache entry with metadata"""
    key: str
    value: Any
    created_at: float
    accessed_at: float
    access_count: int
    ttl: Optional[int] = None
    size_bytes: int = 0
    tags: List[str] = None
    
    def __post_init__(self):
        if self.tags is None:
            self.tags = []
        if self.size_bytes == 0:
            self.size_bytes = self._calculate_size()
    
    def _calculate_size(self) -> int:
        """Calculate approximate size in bytes"""
        try:
            return len(pickle.dumps(self.value))
        except:
            return len(str(self.value).encode('utf-8'))
    
    def is_expired(self) -> bool:
        """Check if entry is expired"""
        if self.ttl is None:
            return False
        return time.time() - self.created_at > self.ttl
    
    def touch(self):
        """Update access time and count"""
        self.accessed_at = time.time()
        self.access_count += 1

@dataclass
class CacheStats:
    """Cache statistics"""
    hits: int = 0
    misses: int = 0
    sets: int = 0
    deletes: int = 0
    evictions: int = 0
    size_bytes: int = 0
    entry_count: int = 0
    
    @property
    def hit_rate(self) -> float:
        """Calculate hit rate percentage"""
        total = self.hits + self.misses
        return (self.hits / total * 100) if total > 0 else 0.0

class InMemoryCache:
    """High-performance in-memory cache with multiple strategies"""
    
    def __init__(self, 
                 max_size: int = 1000,
                 max_memory_mb: int = 100,
                 strategy: CacheStrategy = CacheStrategy.LRU,
                 default_ttl: Optional[int] = None):
        self.max_size = max_size
        self.max_memory_bytes = max_memory_mb * 1024 * 1024
        self.strategy = strategy
        self.default_ttl = default_ttl
        
        self._cache: Dict[str, CacheEntry] = {}
        self._stats = CacheStats()
        self._lock = threading.RLock()
        
        # Strategy-specific data structures
        self._access_order = []  # For LRU
        self._access_counts = {}  # For LFU
        
    def get(self, key: str) -> Optional[Any]:
        """Get value from cache"""
        with self._lock:
            if key not in self._cache:
                self._stats.misses += 1
                return None
            
            entry = self._cache[key]
            
            # Check expiration
            if entry.is_expired():
                self._remove_entry(key)
                self._stats.misses += 1
                return None
            
            # Update access patterns
            entry.touch()
            self._update_access_pattern(key)
            
            self._stats.hits += 1
            return entry.value
    
    def set(self, key: str, value: Any, ttl: Optional[int] = None, tags: List[str] = None) -> bool:
        """Set value in cache"""
        with self._lock:
            # Use default TTL if not specified
            if ttl is None:
                ttl = self.default_ttl
            
            # Create cache entry
            entry = CacheEntry(
                key=key,
                value=value,
                created_at=time.time(),
                accessed_at=time.time(),
                access_count=1,
                ttl=ttl,
                tags=tags or []
            )
            
            # Check if we need to evict entries
            if key not in self._cache:
                self._ensure_space(entry.size_bytes)
            
            # Store entry
            old_entry = self._cache.get(key)
            self._cache[key] = entry
            
            # Update access patterns
            self._update_access_pattern(key)
            
            # Update stats
            self._stats.sets += 1
            if old_entry:
                self._stats.size_bytes += entry.size_bytes - old_entry.size_bytes
            else:
                self._stats.size_bytes += entry.size_bytes
                self._stats.entry_count += 1
            
            return True
    
    def delete(self, key: str) -> bool:
        """Delete key from cache"""
        with self._lock:
            if key in self._cache:
                self._remove_entry(key)
                self._stats.deletes += 1
                return True
            return False
    
    def delete_by_tags(self, tags: List[str]) -> int:
        """Delete entries by tags"""
        with self._lock:
            keys_to_delete = []
            for key, entry in self._cache.items():
                if any(tag in entry.tags for tag in tags):
                    keys_to_delete.append(key)
            
            for key in keys_to_delete:
                self._remove_entry(key)
            
            return len(keys_to_delete)
    
    def clear(self):
        """Clear all cache entries"""
        with self._lock:
            self._cache.clear()
            self._access_order.clear()
            self._access_counts.clear()
            self._stats = CacheStats()
    
    def get_stats(self) -> CacheStats:
        """Get cache statistics"""
        with self._lock:
            # Update current stats
            self._stats.entry_count = len(self._cache)
            self._stats.size_bytes = sum(entry.size_bytes for entry in self._cache.values())
            return self._stats
    
    def _ensure_space(self, required_bytes: int):
        """Ensure enough space for new entry"""
        # Check size limit
        while len(self._cache) >= self.max_size:
            self._evict_entry()
        
        # Check memory limit
        current_size = sum(entry.size_bytes for entry in self._cache.values())
        while current_size + required_bytes > self.max_memory_bytes and self._cache:
            self._evict_entry()
            current_size = sum(entry.size_bytes for entry in self._cache.values())
    
    def _evict_entry(self):
        """Evict entry based on strategy"""
        if not self._cache:
            return
        
        if self.strategy == CacheStrategy.LRU:
            key_to_evict = self._access_order[0]
        elif self.strategy == CacheStrategy.LFU:
            key_to_evict = min(self._access_counts.keys(), key=lambda k: self._access_counts[k])
        elif self.strategy == CacheStrategy.TTL:
            # Evict expired entries first, then oldest
            expired_keys = [k for k, e in self._cache.items() if e.is_expired()]
            if expired_keys:
                key_to_evict = expired_keys[0]
            else:
                key_to_evict = min(self._cache.keys(), key=lambda k: self._cache[k].created_at)
        elif self.strategy == CacheStrategy.FIFO:
            key_to_evict = min(self._cache.keys(), key=lambda k: self._cache[k].created_at)
        else:  # ADAPTIVE
            key_to_evict = self._adaptive_evict()
        
        self._remove_entry(key_to_evict)
        self._stats.evictions += 1
    
    def _adaptive_evict(self) -> str:
        """Intelligent adaptive eviction"""
        now = time.time()
        
        # Score entries based on multiple factors
        scores = {}
        for key, entry in self._cache.items():
            # Factors: recency, frequency, size, TTL remaining
            recency_score = 1.0 / (now - entry.accessed_at + 1)
            frequency_score = entry.access_count
            size_penalty = entry.size_bytes / 1024  # Penalize larger entries
            
            if entry.ttl:
                ttl_remaining = entry.ttl - (now - entry.created_at)
                ttl_score = max(0, ttl_remaining / entry.ttl)
            else:
                ttl_score = 1.0
            
            # Combined score (lower is more likely to be evicted)
            scores[key] = (recency_score * frequency_score * ttl_score) / size_penalty
        
        return min(scores.keys(), key=lambda k: scores[k])
    
    def _update_access_pattern(self, key: str):
        """Update access pattern for strategies"""
        # Update LRU order
        if key in self._access_order:
            self._access_order.remove(key)
        self._access_order.append(key)
        
        # Update LFU counts
        self._access_counts[key] = self._access_counts.get(key, 0) + 1
    
    def _remove_entry(self, key: str):
        """Remove entry and update tracking structures"""
        if key in self._cache:
            entry = self._cache.pop(key)
            self._stats.size_bytes -= entry.size_bytes
            self._stats.entry_count -= 1
        
        if key in self._access_order:
            self._access_order.remove(key)
        
        if key in self._access_counts:
            del self._access_counts[key]

class RedisCache:
    """Redis-based distributed cache"""
    
    def __init__(self, 
                 redis_url: str = "redis://localhost:6379",
                 prefix: str = "nexusscan:",
                 default_ttl: Optional[int] = None,
                 max_connections: int = 10):
        self.redis_url = redis_url
        self.prefix = prefix
        self.default_ttl = default_ttl
        self.max_connections = max_connections
        
        self._redis: Optional[redis.Redis] = None
        self._connected = False
        self._stats = CacheStats()
        
    async def connect(self):
        """Connect to Redis"""
        if not REDIS_AVAILABLE:
            logger.warning("Redis not available, falling back to memory cache")
            return False
        
        try:
            self._redis = redis.from_url(
                self.redis_url,
                decode_responses=False,
                max_connections=self.max_connections
            )
            
            # Test connection
            await self._redis.ping()
            self._connected = True
            logger.info("Connected to Redis cache")
            return True
        except Exception as e:
            logger.error(f"Failed to connect to Redis: {e}")
            self._connected = False
            return False
    
    async def disconnect(self):
        """Disconnect from Redis"""
        if self._redis:
            await self._redis.close()
            self._connected = False
    
    def _make_key(self, key: str) -> str:
        """Create prefixed key"""
        return f"{self.prefix}{key}"
    
    async def get(self, key: str) -> Optional[Any]:
        """Get value from Redis"""
        if not self._connected:
            self._stats.misses += 1
            return None
        
        try:
            redis_key = self._make_key(key)
            data = await self._redis.get(redis_key)
            
            if data is None:
                self._stats.misses += 1
                return None
            
            # Deserialize
            value = pickle.loads(data)
            self._stats.hits += 1
            return value
        except Exception as e:
            logger.error(f"Redis get error: {e}")
            self._stats.misses += 1
            return None
    
    async def set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """Set value in Redis"""
        if not self._connected:
            return False
        
        try:
            redis_key = self._make_key(key)
            
            # Serialize value
            data = pickle.dumps(value)
            
            # Use default TTL if not specified
            if ttl is None:
                ttl = self.default_ttl
            
            # Set in Redis
            await self._redis.set(redis_key, data, ex=ttl)
            self._stats.sets += 1
            return True
        except Exception as e:
            logger.error(f"Redis set error: {e}")
            return False
    
    async def delete(self, key: str) -> bool:
        """Delete key from Redis"""
        if not self._connected:
            return False
        
        try:
            redis_key = self._make_key(key)
            result = await self._redis.delete(redis_key)
            if result > 0:
                self._stats.deletes += 1
                return True
            return False
        except Exception as e:
            logger.error(f"Redis delete error: {e}")
            return False
    
    async def delete_by_pattern(self, pattern: str) -> int:
        """Delete keys matching pattern"""
        if not self._connected:
            return 0
        
        try:
            redis_pattern = self._make_key(pattern)
            keys = await self._redis.keys(redis_pattern)
            if keys:
                deleted = await self._redis.delete(*keys)
                self._stats.deletes += deleted
                return deleted
            return 0
        except Exception as e:
            logger.error(f"Redis delete by pattern error: {e}")
            return 0
    
    async def clear(self):
        """Clear all cache entries with prefix"""
        if not self._connected:
            return
        
        try:
            keys = await self._redis.keys(f"{self.prefix}*")
            if keys:
                await self._redis.delete(*keys)
        except Exception as e:
            logger.error(f"Redis clear error: {e}")
    
    def get_stats(self) -> CacheStats:
        """Get cache statistics"""
        return self._stats

class HybridCache:
    """Hybrid cache combining memory and Redis with intelligent routing"""
    
    def __init__(self,
                 memory_config: Dict[str, Any] = None,
                 redis_config: Dict[str, Any] = None,
                 write_through: bool = True,
                 read_through: bool = True):
        
        # Initialize memory cache
        memory_config = memory_config or {}
        self.memory_cache = InMemoryCache(**memory_config)
        
        # Initialize Redis cache
        redis_config = redis_config or {}
        self.redis_cache = RedisCache(**redis_config)
        
        self.write_through = write_through
        self.read_through = read_through
        self._redis_connected = False
        
    async def connect(self):
        """Connect to distributed cache"""
        self._redis_connected = await self.redis_cache.connect()
        return self._redis_connected
    
    async def disconnect(self):
        """Disconnect from distributed cache"""
        await self.redis_cache.disconnect()
        self._redis_connected = False
    
    async def get(self, key: str) -> Optional[Any]:
        """Get value with L1 (memory) -> L2 (Redis) fallback"""
        # Try memory cache first (L1)
        value = self.memory_cache.get(key)
        if value is not None:
            return value
        
        # Try Redis cache (L2) if connected and read-through enabled
        if self._redis_connected and self.read_through:
            value = await self.redis_cache.get(key)
            if value is not None:
                # Populate memory cache
                self.memory_cache.set(key, value)
                return value
        
        return None
    
    async def set(self, key: str, value: Any, ttl: Optional[int] = None, tags: List[str] = None) -> bool:
        """Set value in both caches if write-through enabled"""
        success = True
        
        # Always set in memory cache (L1)
        memory_success = self.memory_cache.set(key, value, ttl, tags)
        
        # Set in Redis cache (L2) if connected and write-through enabled
        if self._redis_connected and self.write_through:
            redis_success = await self.redis_cache.set(key, value, ttl)
            success = memory_success and redis_success
        else:
            success = memory_success
        
        return success
    
    async def delete(self, key: str) -> bool:
        """Delete from both caches"""
        memory_deleted = self.memory_cache.delete(key)
        redis_deleted = False
        
        if self._redis_connected:
            redis_deleted = await self.redis_cache.delete(key)
        
        return memory_deleted or redis_deleted
    
    async def delete_by_tags(self, tags: List[str]) -> int:
        """Delete by tags (memory only, Redis uses patterns)"""
        deleted = self.memory_cache.delete_by_tags(tags)
        
        # For Redis, convert tags to pattern and delete
        if self._redis_connected and tags:
            for tag in tags:
                pattern = f"*{tag}*"
                await self.redis_cache.delete_by_pattern(pattern)
        
        return deleted
    
    async def clear(self):
        """Clear both caches"""
        self.memory_cache.clear()
        if self._redis_connected:
            await self.redis_cache.clear()
    
    def get_stats(self) -> Dict[str, CacheStats]:
        """Get statistics from both caches"""
        stats = {
            "memory": self.memory_cache.get_stats(),
            "redis": self.redis_cache.get_stats()
        }
        
        # Combined stats
        combined = CacheStats()
        for cache_stats in stats.values():
            combined.hits += cache_stats.hits
            combined.misses += cache_stats.misses
            combined.sets += cache_stats.sets
            combined.deletes += cache_stats.deletes
            combined.evictions += cache_stats.evictions
        
        stats["combined"] = combined
        return stats

class CacheManager:
    """Advanced cache manager with intelligent routing and optimization"""
    
    def __init__(self, config: Dict[str, Any] = None):
        self.config = config or {}
        
        # Initialize cache based on configuration
        cache_type = self.config.get("type", "hybrid")
        
        if cache_type == "memory":
            self.cache = InMemoryCache(**self.config.get("memory", {}))
        elif cache_type == "redis":
            self.cache = RedisCache(**self.config.get("redis", {}))
        elif cache_type == "hybrid":
            self.cache = HybridCache(
                memory_config=self.config.get("memory", {}),
                redis_config=self.config.get("redis", {})
            )
        else:
            raise ValueError(f"Unknown cache type: {cache_type}")
        
        # Cache optimization settings
        self.auto_optimize = self.config.get("auto_optimize", True)
        self.optimization_interval = self.config.get("optimization_interval", 300)  # 5 minutes
        
        # Start optimization task if needed
        if self.auto_optimize and hasattr(self.cache, 'connect'):
            asyncio.create_task(self._optimization_task())
    
    async def connect(self):
        """Connect to cache backend"""
        if hasattr(self.cache, 'connect'):
            return await self.cache.connect()
        return True
    
    async def disconnect(self):
        """Disconnect from cache backend"""
        if hasattr(self.cache, 'disconnect'):
            await self.cache.disconnect()
    
    def cache_key(self, *args, **kwargs) -> str:
        """Generate cache key from arguments"""
        # Create deterministic key from arguments
        key_data = {
            "args": args,
            "kwargs": sorted(kwargs.items())
        }
        key_str = json.dumps(key_data, sort_keys=True)
        return hashlib.md5(key_str.encode()).hexdigest()
    
    async def get(self, key: str) -> Optional[Any]:
        """Get value from cache"""
        if hasattr(self.cache, 'get'):
            if asyncio.iscoroutinefunction(self.cache.get):
                return await self.cache.get(key)
            else:
                return self.cache.get(key)
        return None
    
    async def set(self, key: str, value: Any, ttl: Optional[int] = None, tags: List[str] = None) -> bool:
        """Set value in cache"""
        if hasattr(self.cache, 'set'):
            if asyncio.iscoroutinefunction(self.cache.set):
                return await self.cache.set(key, value, ttl, tags)
            else:
                return self.cache.set(key, value, ttl, tags)
        return False
    
    async def delete(self, key: str) -> bool:
        """Delete key from cache"""
        if hasattr(self.cache, 'delete'):
            if asyncio.iscoroutinefunction(self.cache.delete):
                return await self.cache.delete(key)
            else:
                return self.cache.delete(key)
        return False
    
    async def clear(self):
        """Clear cache"""
        if hasattr(self.cache, 'clear'):
            if asyncio.iscoroutinefunction(self.cache.clear):
                await self.cache.clear()
            else:
                self.cache.clear()
    
    def cached(self, ttl: Optional[int] = None, tags: List[str] = None):
        """Decorator for caching function results"""
        def decorator(func: Callable):
            async def async_wrapper(*args, **kwargs):
                # Generate cache key
                cache_key = self.cache_key(func.__name__, *args, **kwargs)
                
                # Try to get from cache
                cached_result = await self.get(cache_key)
                if cached_result is not None:
                    return cached_result
                
                # Execute function and cache result
                if asyncio.iscoroutinefunction(func):
                    result = await func(*args, **kwargs)
                else:
                    result = func(*args, **kwargs)
                
                await self.set(cache_key, result, ttl, tags)
                return result
            
            def sync_wrapper(*args, **kwargs):
                # For synchronous functions, use asyncio.run if needed
                loop = None
                try:
                    loop = asyncio.get_event_loop()
                except RuntimeError:
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                
                return loop.run_until_complete(async_wrapper(*args, **kwargs))
            
            if asyncio.iscoroutinefunction(func):
                return async_wrapper
            else:
                return sync_wrapper
        
        return decorator
    
    async def _optimization_task(self):
        """Background task for cache optimization"""
        while True:
            try:
                await asyncio.sleep(self.optimization_interval)
                await self._optimize_cache()
            except Exception as e:
                logger.error(f"Cache optimization error: {e}")
    
    async def _optimize_cache(self):
        """Optimize cache performance"""
        if hasattr(self.cache, 'get_stats'):
            stats = self.cache.get_stats()
            
            # Log cache performance
            if hasattr(stats, 'hit_rate'):
                logger.info(f"Cache hit rate: {stats.hit_rate:.1f}%")
            
            # Additional optimization logic can be added here
            # e.g., adjusting TTL based on hit rates, preloading popular items, etc.

# Global cache manager instance
cache_manager: Optional[CacheManager] = None

def get_cache_manager() -> CacheManager:
    """Get global cache manager instance"""
    global cache_manager
    if cache_manager is None:
        # Default configuration
        config = {
            "type": "hybrid",
            "memory": {
                "max_size": 1000,
                "max_memory_mb": 100,
                "strategy": CacheStrategy.ADAPTIVE,
                "default_ttl": 3600
            },
            "redis": {
                "redis_url": "redis://localhost:6379",
                "prefix": "nexusscan:",
                "default_ttl": 3600
            },
            "auto_optimize": True
        }
        cache_manager = CacheManager(config)
    return cache_manager

def cached(ttl: Optional[int] = None, tags: List[str] = None):
    """Convenience decorator for caching"""
    return get_cache_manager().cached(ttl, tags)

# Convenience functions
async def cache_get(key: str) -> Optional[Any]:
    """Get value from cache"""
    return await get_cache_manager().get(key)

async def cache_set(key: str, value: Any, ttl: Optional[int] = None, tags: List[str] = None) -> bool:
    """Set value in cache"""
    return await get_cache_manager().set(key, value, ttl, tags)

async def cache_delete(key: str) -> bool:
    """Delete key from cache"""
    return await get_cache_manager().delete(key)

async def cache_clear():
    """Clear cache"""
    await get_cache_manager().clear()