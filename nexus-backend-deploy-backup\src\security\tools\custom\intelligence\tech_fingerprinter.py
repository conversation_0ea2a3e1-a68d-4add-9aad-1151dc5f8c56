#!/usr/bin/env python3
"""
Technology Stack Fingerprinting System for NexusScan Desktop
Comprehensive technology detection, version identification, and vulnerability correlation.
"""

import asyncio
import logging
import json
import re
import hashlib
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Set, Tuple
from dataclasses import dataclass, asdict
from enum import Enum
from urllib.parse import urlparse, urljoin
import aiohttp
import ssl
import socket
from collections import defaultdict

from core.config import Config
from core.database import DatabaseManager
from ai.ai_service import AIServiceManager
from ai.services import AnalysisRequest

logger = logging.getLogger(__name__)


class TechnologyCategory(Enum):
    """Categories of technology"""
    WEB_SERVER = "web_server"
    APPLICATION_SERVER = "application_server"
    DATABASE = "database"
    PROGRAMMING_LANGUAGE = "programming_language"
    FRAMEWORK = "framework"
    CMS = "cms"
    ECOMMERCE = "ecommerce"
    ANALYTICS = "analytics"
    CDN = "cdn"
    LOAD_BALANCER = "load_balancer"
    WAF = "waf"
    OPERATING_SYSTEM = "operating_system"
    JAVASCRIPT_LIBRARY = "javascript_library"
    CSS_FRAMEWORK = "css_framework"
    FONT_SCRIPT = "font_script"
    PAYMENT_PROCESSOR = "payment_processor"
    SECURITY = "security"
    DEVTOOLS = "devtools"


class DetectionMethod(Enum):
    """Methods of technology detection"""
    HTTP_HEADERS = "http_headers"
    HTML_CONTENT = "html_content"
    JAVASCRIPT = "javascript"
    CSS = "css"
    COOKIES = "cookies"
    URL_PATTERNS = "url_patterns"
    ERROR_PAGES = "error_pages"
    FAVICON = "favicon"
    ROBOTS_TXT = "robots_txt"
    SSL_CERTIFICATE = "ssl_certificate"
    DNS_RECORDS = "dns_records"
    PORT_SCAN = "port_scan"
    RESPONSE_TIMING = "response_timing"
    AI_ANALYSIS = "ai_analysis"


class ConfidenceLevel(Enum):
    """Confidence levels for technology detection"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CERTAIN = "certain"


@dataclass
class TechnologySignature:
    """Technology detection signature"""
    name: str
    category: TechnologyCategory
    detection_method: DetectionMethod
    pattern: str
    version_pattern: Optional[str]
    confidence_weight: float
    description: str


@dataclass
class TechnologyDetection:
    """Technology detection result"""
    id: str
    name: str
    category: TechnologyCategory
    version: Optional[str]
    confidence: ConfidenceLevel
    detection_methods: List[DetectionMethod]
    evidence: List[str]
    website: Optional[str]
    description: str
    detected_at: datetime


@dataclass
class VulnerabilityMapping:
    """Vulnerability mapping for detected technology"""
    technology_name: str
    version: Optional[str]
    cve_ids: List[str]
    severity_scores: List[float]
    exploitability: str
    description: str


@dataclass
class StackProfile:
    """Complete technology stack profile"""
    id: str
    target_url: str
    technologies: List[TechnologyDetection]
    vulnerabilities: List[VulnerabilityMapping]
    security_score: float
    modernization_score: float
    recommendations: List[str]
    scan_metadata: Dict[str, Any]
    generated_at: datetime


class TechnologyStackFingerprinter:
    """Comprehensive technology stack fingerprinting system"""

    def __init__(self, config: Config, database: DatabaseManager, ai_service: AIServiceManager):
        """Initialize technology fingerprinter"""
        self.config = config
        self.database = database
        self.ai_service = ai_service
        
        # Technology signatures database
        self.signatures = self._load_technology_signatures()
        
        # Session management
        self.session: Optional[aiohttp.ClientSession] = None
        
        # Detection cache
        self.detection_cache: Dict[str, StackProfile] = {}
        
        # Configuration
        self.max_concurrent_requests = 10
        self.request_timeout = 10
        self.user_agent = "NexusScan Technology Fingerprinter v1.0"
        
        # Statistics
        self.stats = {
            "targets_scanned": 0,
            "technologies_detected": 0,
            "vulnerabilities_found": 0,
            "signatures_matched": 0,
            "requests_sent": 0,
            "cache_hits": 0
        }
        
        logger.info("Technology Stack Fingerprinter initialized")

    def _load_technology_signatures(self) -> List[TechnologySignature]:
        """Load technology detection signatures"""
        signatures = []
        
        # Web Servers
        signatures.extend([
            TechnologySignature(
                name="Apache",
                category=TechnologyCategory.WEB_SERVER,
                detection_method=DetectionMethod.HTTP_HEADERS,
                pattern=r"Server: Apache(?:/([0-9.]+))?",
                version_pattern=r"Apache/([0-9.]+)",
                confidence_weight=0.9,
                description="Apache HTTP Server"
            ),
            TechnologySignature(
                name="Nginx",
                category=TechnologyCategory.WEB_SERVER,
                detection_method=DetectionMethod.HTTP_HEADERS,
                pattern=r"Server: nginx(?:/([0-9.]+))?",
                version_pattern=r"nginx/([0-9.]+)",
                confidence_weight=0.9,
                description="Nginx Web Server"
            ),
            TechnologySignature(
                name="Microsoft-IIS",
                category=TechnologyCategory.WEB_SERVER,
                detection_method=DetectionMethod.HTTP_HEADERS,
                pattern=r"Server: Microsoft-IIS(?:/([0-9.]+))?",
                version_pattern=r"Microsoft-IIS/([0-9.]+)",
                confidence_weight=0.9,
                description="Microsoft Internet Information Services"
            ),
            TechnologySignature(
                name="LiteSpeed",
                category=TechnologyCategory.WEB_SERVER,
                detection_method=DetectionMethod.HTTP_HEADERS,
                pattern=r"Server: LiteSpeed",
                version_pattern=None,
                confidence_weight=0.8,
                description="LiteSpeed Web Server"
            )
        ])
        
        # Programming Languages
        signatures.extend([
            TechnologySignature(
                name="PHP",
                category=TechnologyCategory.PROGRAMMING_LANGUAGE,
                detection_method=DetectionMethod.HTTP_HEADERS,
                pattern=r"X-Powered-By: PHP(?:/([0-9.]+))?",
                version_pattern=r"PHP/([0-9.]+)",
                confidence_weight=0.9,
                description="PHP Server-side Programming"
            ),
            TechnologySignature(
                name="ASP.NET",
                category=TechnologyCategory.PROGRAMMING_LANGUAGE,
                detection_method=DetectionMethod.HTTP_HEADERS,
                pattern=r"X-AspNet-Version: ([0-9.]+)",
                version_pattern=r"X-AspNet-Version: ([0-9.]+)",
                confidence_weight=0.9,
                description="Microsoft ASP.NET Framework"
            ),
            TechnologySignature(
                name="Express",
                category=TechnologyCategory.FRAMEWORK,
                detection_method=DetectionMethod.HTTP_HEADERS,
                pattern=r"X-Powered-By: Express",
                version_pattern=None,
                confidence_weight=0.8,
                description="Express.js Web Framework"
            )
        ])
        
        # Content Management Systems
        signatures.extend([
            TechnologySignature(
                name="WordPress",
                category=TechnologyCategory.CMS,
                detection_method=DetectionMethod.HTML_CONTENT,
                pattern=r"/wp-content/|/wp-includes/|wp-json",
                version_pattern=r'content="WordPress ([0-9.]+)"',
                confidence_weight=0.9,
                description="WordPress Content Management System"
            ),
            TechnologySignature(
                name="Drupal",
                category=TechnologyCategory.CMS,
                detection_method=DetectionMethod.HTML_CONTENT,
                pattern=r"Drupal.settings|/sites/default/files/",
                version_pattern=r'content="Drupal ([0-9.]+)"',
                confidence_weight=0.8,
                description="Drupal Content Management System"
            ),
            TechnologySignature(
                name="Joomla",
                category=TechnologyCategory.CMS,
                detection_method=DetectionMethod.HTML_CONTENT,
                pattern=r"/media/system/js/|/templates/system/",
                version_pattern=r'content="Joomla! ([0-9.]+)"',
                confidence_weight=0.8,
                description="Joomla Content Management System"
            )
        ])
        
        # JavaScript Libraries
        signatures.extend([
            TechnologySignature(
                name="jQuery",
                category=TechnologyCategory.JAVASCRIPT_LIBRARY,
                detection_method=DetectionMethod.JAVASCRIPT,
                pattern=r"jquery(?:-([0-9.]+))?\.(?:min\.)?js",
                version_pattern=r"jquery-([0-9.]+)\.(?:min\.)?js",
                confidence_weight=0.7,
                description="jQuery JavaScript Library"
            ),
            TechnologySignature(
                name="React",
                category=TechnologyCategory.JAVASCRIPT_LIBRARY,
                detection_method=DetectionMethod.JAVASCRIPT,
                pattern=r"react(?:-([0-9.]+))?\.(?:min\.)?js|__REACT_DEVTOOLS_GLOBAL_HOOK__",
                version_pattern=r"react-([0-9.]+)\.(?:min\.)?js",
                confidence_weight=0.8,
                description="React JavaScript Library"
            ),
            TechnologySignature(
                name="Angular",
                category=TechnologyCategory.JAVASCRIPT_LIBRARY,
                detection_method=DetectionMethod.JAVASCRIPT,
                pattern=r"angular(?:-([0-9.]+))?\.(?:min\.)?js|ng-version",
                version_pattern=r"angular-([0-9.]+)\.(?:min\.)?js",
                confidence_weight=0.8,
                description="Angular JavaScript Framework"
            ),
            TechnologySignature(
                name="Vue.js",
                category=TechnologyCategory.JAVASCRIPT_LIBRARY,
                detection_method=DetectionMethod.JAVASCRIPT,
                pattern=r"vue(?:-([0-9.]+))?\.(?:min\.)?js|__VUE__",
                version_pattern=r"vue-([0-9.]+)\.(?:min\.)?js",
                confidence_weight=0.8,
                description="Vue.js JavaScript Framework"
            )
        ])
        
        # CSS Frameworks
        signatures.extend([
            TechnologySignature(
                name="Bootstrap",
                category=TechnologyCategory.CSS_FRAMEWORK,
                detection_method=DetectionMethod.CSS,
                pattern=r"bootstrap(?:-([0-9.]+))?\.(?:min\.)?css",
                version_pattern=r"bootstrap-([0-9.]+)\.(?:min\.)?css",
                confidence_weight=0.7,
                description="Bootstrap CSS Framework"
            ),
            TechnologySignature(
                name="Foundation",
                category=TechnologyCategory.CSS_FRAMEWORK,
                detection_method=DetectionMethod.CSS,
                pattern=r"foundation(?:-([0-9.]+))?\.(?:min\.)?css",
                version_pattern=r"foundation-([0-9.]+)\.(?:min\.)?css",
                confidence_weight=0.7,
                description="Foundation CSS Framework"
            )
        ])
        
        # Analytics and Tracking
        signatures.extend([
            TechnologySignature(
                name="Google Analytics",
                category=TechnologyCategory.ANALYTICS,
                detection_method=DetectionMethod.JAVASCRIPT,
                pattern=r"google-analytics\.com/analytics\.js|gtag\(|ga\('create'",
                version_pattern=None,
                confidence_weight=0.9,
                description="Google Analytics"
            ),
            TechnologySignature(
                name="Google Tag Manager",
                category=TechnologyCategory.ANALYTICS,
                detection_method=DetectionMethod.JAVASCRIPT,
                pattern=r"googletagmanager\.com/gtm\.js|dataLayer",
                version_pattern=None,
                confidence_weight=0.9,
                description="Google Tag Manager"
            )
        ])
        
        # CDN and Cloud Services
        signatures.extend([
            TechnologySignature(
                name="Cloudflare",
                category=TechnologyCategory.CDN,
                detection_method=DetectionMethod.HTTP_HEADERS,
                pattern=r"CF-RAY|Server: cloudflare",
                version_pattern=None,
                confidence_weight=0.9,
                description="Cloudflare CDN"
            ),
            TechnologySignature(
                name="Amazon CloudFront",
                category=TechnologyCategory.CDN,
                detection_method=DetectionMethod.HTTP_HEADERS,
                pattern=r"X-Amz-Cf-Id|Via: .*CloudFront",
                version_pattern=None,
                confidence_weight=0.9,
                description="Amazon CloudFront CDN"
            )
        ])
        
        # WAF Detection
        signatures.extend([
            TechnologySignature(
                name="Cloudflare WAF",
                category=TechnologyCategory.WAF,
                detection_method=DetectionMethod.HTTP_HEADERS,
                pattern=r"CF-RAY",
                version_pattern=None,
                confidence_weight=0.8,
                description="Cloudflare Web Application Firewall"
            ),
            TechnologySignature(
                name="AWS WAF",
                category=TechnologyCategory.WAF,
                detection_method=DetectionMethod.HTTP_HEADERS,
                pattern=r"X-Amzn-RequestId",
                version_pattern=None,
                confidence_weight=0.7,
                description="Amazon Web Services WAF"
            )
        ])
        
        return signatures

    async def start_session(self):
        """Start HTTP session for fingerprinting"""
        connector = aiohttp.TCPConnector(
            limit=self.max_concurrent_requests,
            ssl=False  # Allow insecure connections for testing
        )
        timeout = aiohttp.ClientTimeout(total=self.request_timeout)
        headers = {"User-Agent": self.user_agent}
        
        self.session = aiohttp.ClientSession(
            connector=connector,
            timeout=timeout,
            headers=headers
        )

    async def stop_session(self):
        """Stop HTTP session"""
        if self.session:
            await self.session.close()

    async def fingerprint_technology_stack(self, target_url: str, deep_scan: bool = True) -> StackProfile:
        """Comprehensive technology stack fingerprinting"""
        
        # Check cache first
        cache_key = hashlib.md5(f"{target_url}_{deep_scan}".encode()).hexdigest()
        if cache_key in self.detection_cache:
            self.stats["cache_hits"] += 1
            return self.detection_cache[cache_key]
        
        try:
            if not self.session:
                await self.start_session()
            
            technologies = []
            
            # Basic HTTP fingerprinting
            basic_techs = await self._fingerprint_http_layer(target_url)
            technologies.extend(basic_techs)
            
            # HTML content analysis
            content_techs = await self._fingerprint_content_layer(target_url)
            technologies.extend(content_techs)
            
            if deep_scan:
                # Deep scanning techniques
                deep_techs = await self._fingerprint_deep_layer(target_url)
                technologies.extend(deep_techs)
                
                # AI-powered analysis
                ai_techs = await self._fingerprint_ai_layer(target_url, technologies)
                technologies.extend(ai_techs)
            
            # Remove duplicates and merge detections
            technologies = self._merge_technology_detections(technologies)
            
            # Vulnerability correlation
            vulnerabilities = await self._correlate_vulnerabilities(technologies)
            
            # Generate security and modernization scores
            security_score = self._calculate_security_score(technologies, vulnerabilities)
            modernization_score = self._calculate_modernization_score(technologies)
            
            # Generate recommendations
            recommendations = self._generate_recommendations(technologies, vulnerabilities)
            
            # Create stack profile
            stack_profile = StackProfile(
                id=f"stack_{int(time.time())}",
                target_url=target_url,
                technologies=technologies,
                vulnerabilities=vulnerabilities,
                security_score=security_score,
                modernization_score=modernization_score,
                recommendations=recommendations,
                scan_metadata={
                    "deep_scan": deep_scan,
                    "signatures_checked": len(self.signatures),
                    "detection_methods_used": list(set(t.detection_methods[0] for t in technologies if t.detection_methods))
                },
                generated_at=datetime.now()
            )
            
            # Cache result
            self.detection_cache[cache_key] = stack_profile
            
            # Update statistics
            self.stats["targets_scanned"] += 1
            self.stats["technologies_detected"] += len(technologies)
            self.stats["vulnerabilities_found"] += len(vulnerabilities)
            
            return stack_profile
            
        except Exception as e:
            logger.error(f"Technology fingerprinting failed for {target_url}: {e}")
            # Return empty profile on error
            return StackProfile(
                id=f"stack_error_{int(time.time())}",
                target_url=target_url,
                technologies=[],
                vulnerabilities=[],
                security_score=0.0,
                modernization_score=0.0,
                recommendations=["Fingerprinting failed - manual analysis required"],
                scan_metadata={"error": str(e)},
                generated_at=datetime.now()
            )

    async def _fingerprint_http_layer(self, target_url: str) -> List[TechnologyDetection]:
        """Fingerprint technologies from HTTP layer"""
        technologies = []
        
        try:
            async with self.session.get(target_url) as response:
                headers = dict(response.headers)
                self.stats["requests_sent"] += 1
                
                # Check each signature against headers
                for signature in self.signatures:
                    if signature.detection_method == DetectionMethod.HTTP_HEADERS:
                        detection = self._check_signature_against_headers(signature, headers)
                        if detection:
                            technologies.append(detection)
                
                # Additional header analysis
                additional_techs = self._analyze_response_headers(headers, target_url)
                technologies.extend(additional_techs)
                
        except Exception as e:
            logger.error(f"HTTP layer fingerprinting failed: {e}")
        
        return technologies

    def _check_signature_against_headers(self, signature: TechnologySignature, headers: Dict[str, str]) -> Optional[TechnologyDetection]:
        """Check signature against HTTP headers"""
        
        try:
            headers_text = " ".join(f"{k}: {v}" for k, v in headers.items())
            
            match = re.search(signature.pattern, headers_text, re.IGNORECASE)
            if match:
                version = None
                if signature.version_pattern:
                    version_match = re.search(signature.version_pattern, headers_text, re.IGNORECASE)
                    if version_match:
                        version = version_match.group(1)
                
                confidence = self._calculate_detection_confidence(signature.confidence_weight, [signature.detection_method])
                
                detection = TechnologyDetection(
                    id=f"tech_{hashlib.md5(f'{signature.name}_{version}_{time.time()}'.encode()).hexdigest()[:12]}",
                    name=signature.name,
                    category=signature.category,
                    version=version,
                    confidence=confidence,
                    detection_methods=[signature.detection_method],
                    evidence=[f"Header pattern: {match.group(0)}"],
                    website=None,
                    description=signature.description,
                    detected_at=datetime.now()
                )
                
                self.stats["signatures_matched"] += 1
                return detection
                
        except Exception as e:
            logger.error(f"Signature check failed: {e}")
        
        return None

    def _analyze_response_headers(self, headers: Dict[str, str], target_url: str) -> List[TechnologyDetection]:
        """Analyze response headers for additional technology clues"""
        technologies = []
        
        try:
            # Server header analysis
            server = headers.get("Server", "")
            if server:
                # Parse complex server headers
                server_parts = server.split()
                for part in server_parts:
                    if "/" in part:
                        name, version = part.split("/", 1)
                        if name.lower() not in ["http", "https"]:
                            tech = TechnologyDetection(
                                id=f"server_{hashlib.md5(f'{name}_{version}'.encode()).hexdigest()[:12]}",
                                name=name,
                                category=self._guess_technology_category(name),
                                version=version,
                                confidence=ConfidenceLevel.MEDIUM,
                                detection_methods=[DetectionMethod.HTTP_HEADERS],
                                evidence=[f"Server header: {part}"],
                                website=None,
                                description=f"Detected from Server header",
                                detected_at=datetime.now()
                            )
                            technologies.append(tech)
            
            # X-Powered-By analysis
            powered_by = headers.get("X-Powered-By", "")
            if powered_by:
                tech = TechnologyDetection(
                    id=f"powered_{hashlib.md5(powered_by.encode()).hexdigest()[:12]}",
                    name=powered_by.split("/")[0] if "/" in powered_by else powered_by,
                    category=TechnologyCategory.PROGRAMMING_LANGUAGE,
                    version=powered_by.split("/")[1] if "/" in powered_by else None,
                    confidence=ConfidenceLevel.HIGH,
                    detection_methods=[DetectionMethod.HTTP_HEADERS],
                    evidence=[f"X-Powered-By: {powered_by}"],
                    website=None,
                    description="Detected from X-Powered-By header",
                    detected_at=datetime.now()
                )
                technologies.append(tech)
            
            # Cookie analysis
            set_cookie = headers.get("Set-Cookie", "")
            if set_cookie:
                cookie_techs = self._analyze_cookies(set_cookie)
                technologies.extend(cookie_techs)
                
        except Exception as e:
            logger.error(f"Response header analysis failed: {e}")
        
        return technologies

    def _guess_technology_category(self, name: str) -> TechnologyCategory:
        """Guess technology category from name"""
        name_lower = name.lower()
        
        web_servers = ["apache", "nginx", "iis", "lighttpd", "litespeed"]
        if any(server in name_lower for server in web_servers):
            return TechnologyCategory.WEB_SERVER
        
        languages = ["php", "python", "ruby", "java", "node"]
        if any(lang in name_lower for lang in languages):
            return TechnologyCategory.PROGRAMMING_LANGUAGE
        
        databases = ["mysql", "postgresql", "mongodb", "redis"]
        if any(db in name_lower for db in databases):
            return TechnologyCategory.DATABASE
        
        return TechnologyCategory.WEB_SERVER  # Default

    def _analyze_cookies(self, cookie_header: str) -> List[TechnologyDetection]:
        """Analyze cookies for technology detection"""
        technologies = []
        
        try:
            # Common cookie patterns
            cookie_patterns = {
                "PHPSESSID": ("PHP", TechnologyCategory.PROGRAMMING_LANGUAGE),
                "JSESSIONID": ("Java", TechnologyCategory.PROGRAMMING_LANGUAGE),
                "ASP.NET_SessionId": ("ASP.NET", TechnologyCategory.PROGRAMMING_LANGUAGE),
                "_ga": ("Google Analytics", TechnologyCategory.ANALYTICS),
                "__cfduid": ("Cloudflare", TechnologyCategory.CDN),
                "wordpress_": ("WordPress", TechnologyCategory.CMS),
                "Drupal.tableDrag": ("Drupal", TechnologyCategory.CMS)
            }
            
            for pattern, (name, category) in cookie_patterns.items():
                if pattern in cookie_header:
                    tech = TechnologyDetection(
                        id=f"cookie_{hashlib.md5(f'{name}_{pattern}'.encode()).hexdigest()[:12]}",
                        name=name,
                        category=category,
                        version=None,
                        confidence=ConfidenceLevel.MEDIUM,
                        detection_methods=[DetectionMethod.COOKIES],
                        evidence=[f"Cookie: {pattern}"],
                        website=None,
                        description=f"Detected from cookie pattern",
                        detected_at=datetime.now()
                    )
                    technologies.append(tech)
                    
        except Exception as e:
            logger.error(f"Cookie analysis failed: {e}")
        
        return technologies

    async def _fingerprint_content_layer(self, target_url: str) -> List[TechnologyDetection]:
        """Fingerprint technologies from HTML content"""
        technologies = []
        
        try:
            async with self.session.get(target_url) as response:
                if response.status == 200:
                    content = await response.text()
                    self.stats["requests_sent"] += 1
                    
                    # Check content-based signatures
                    for signature in self.signatures:
                        if signature.detection_method in [DetectionMethod.HTML_CONTENT, DetectionMethod.JAVASCRIPT, DetectionMethod.CSS]:
                            detection = self._check_signature_against_content(signature, content)
                            if detection:
                                technologies.append(detection)
                    
                    # Additional content analysis
                    additional_techs = self._analyze_html_content(content)
                    technologies.extend(additional_techs)
                    
        except Exception as e:
            logger.error(f"Content layer fingerprinting failed: {e}")
        
        return technologies

    def _check_signature_against_content(self, signature: TechnologySignature, content: str) -> Optional[TechnologyDetection]:
        """Check signature against HTML content"""
        
        try:
            match = re.search(signature.pattern, content, re.IGNORECASE)
            if match:
                version = None
                if signature.version_pattern:
                    version_match = re.search(signature.version_pattern, content, re.IGNORECASE)
                    if version_match:
                        version = version_match.group(1)
                
                confidence = self._calculate_detection_confidence(signature.confidence_weight, [signature.detection_method])
                
                detection = TechnologyDetection(
                    id=f"content_{hashlib.md5(f'{signature.name}_{version}_{time.time()}'.encode()).hexdigest()[:12]}",
                    name=signature.name,
                    category=signature.category,
                    version=version,
                    confidence=confidence,
                    detection_methods=[signature.detection_method],
                    evidence=[f"Content pattern: {match.group(0)[:100]}..."],
                    website=None,
                    description=signature.description,
                    detected_at=datetime.now()
                )
                
                self.stats["signatures_matched"] += 1
                return detection
                
        except Exception as e:
            logger.error(f"Content signature check failed: {e}")
        
        return None

    def _analyze_html_content(self, content: str) -> List[TechnologyDetection]:
        """Analyze HTML content for technology indicators"""
        technologies = []
        
        try:
            # Meta tag analysis
            meta_patterns = {
                r'<meta[^>]*name=["\']*generator["\']*[^>]*content=["\']*([^"\']+)': ("generator", TechnologyCategory.CMS),
                r'<meta[^>]*name=["\']*application-name["\']*[^>]*content=["\']*([^"\']+)': ("application", TechnologyCategory.FRAMEWORK)
            }
            
            for pattern, (detection_type, category) in meta_patterns.items():
                matches = re.findall(pattern, content, re.IGNORECASE)
                for match in matches:
                    name = match.strip()
                    if name:
                        tech = TechnologyDetection(
                            id=f"meta_{hashlib.md5(f'{name}_{detection_type}'.encode()).hexdigest()[:12]}",
                            name=name,
                            category=category,
                            version=None,
                            confidence=ConfidenceLevel.MEDIUM,
                            detection_methods=[DetectionMethod.HTML_CONTENT],
                            evidence=[f"Meta {detection_type}: {name}"],
                            website=None,
                            description=f"Detected from meta {detection_type}",
                            detected_at=datetime.now()
                        )
                        technologies.append(tech)
            
            # Script src analysis
            script_pattern = r'<script[^>]*src=["\']*([^"\']+)["\']*'
            script_matches = re.findall(script_pattern, content, re.IGNORECASE)
            
            for script_src in script_matches:
                script_techs = self._analyze_script_source(script_src)
                technologies.extend(script_techs)
            
            # CSS link analysis
            css_pattern = r'<link[^>]*href=["\']*([^"\']+\.css[^"\']*)'
            css_matches = re.findall(css_pattern, content, re.IGNORECASE)
            
            for css_href in css_matches:
                css_techs = self._analyze_css_source(css_href)
                technologies.extend(css_techs)
                
        except Exception as e:
            logger.error(f"HTML content analysis failed: {e}")
        
        return technologies

    def _analyze_script_source(self, script_src: str) -> List[TechnologyDetection]:
        """Analyze JavaScript source for technology detection"""
        technologies = []
        
        try:
            # Common JavaScript library patterns
            js_patterns = {
                r'jquery[-.]?([0-9.]+)?': ("jQuery", TechnologyCategory.JAVASCRIPT_LIBRARY),
                r'react[-.]?([0-9.]+)?': ("React", TechnologyCategory.JAVASCRIPT_LIBRARY),
                r'angular[-.]?([0-9.]+)?': ("Angular", TechnologyCategory.JAVASCRIPT_LIBRARY),
                r'vue[-.]?([0-9.]+)?': ("Vue.js", TechnologyCategory.JAVASCRIPT_LIBRARY),
                r'bootstrap[-.]?([0-9.]+)?': ("Bootstrap", TechnologyCategory.CSS_FRAMEWORK),
                r'underscore[-.]?([0-9.]+)?': ("Underscore.js", TechnologyCategory.JAVASCRIPT_LIBRARY),
                r'lodash[-.]?([0-9.]+)?': ("Lodash", TechnologyCategory.JAVASCRIPT_LIBRARY)
            }
            
            for pattern, (name, category) in js_patterns.items():
                match = re.search(pattern, script_src, re.IGNORECASE)
                if match:
                    version = match.group(1) if match.group(1) else None
                    
                    tech = TechnologyDetection(
                        id=f"js_{hashlib.md5(f'{name}_{version}'.encode()).hexdigest()[:12]}",
                        name=name,
                        category=category,
                        version=version,
                        confidence=ConfidenceLevel.MEDIUM,
                        detection_methods=[DetectionMethod.JAVASCRIPT],
                        evidence=[f"Script source: {script_src}"],
                        website=None,
                        description=f"Detected from JavaScript source",
                        detected_at=datetime.now()
                    )
                    technologies.append(tech)
                    
        except Exception as e:
            logger.error(f"Script source analysis failed: {e}")
        
        return technologies

    def _analyze_css_source(self, css_href: str) -> List[TechnologyDetection]:
        """Analyze CSS source for technology detection"""
        technologies = []
        
        try:
            # Common CSS framework patterns
            css_patterns = {
                r'bootstrap[-.]?([0-9.]+)?': ("Bootstrap", TechnologyCategory.CSS_FRAMEWORK),
                r'foundation[-.]?([0-9.]+)?': ("Foundation", TechnologyCategory.CSS_FRAMEWORK),
                r'bulma[-.]?([0-9.]+)?': ("Bulma", TechnologyCategory.CSS_FRAMEWORK),
                r'materialize[-.]?([0-9.]+)?': ("Materialize", TechnologyCategory.CSS_FRAMEWORK)
            }
            
            for pattern, (name, category) in css_patterns.items():
                match = re.search(pattern, css_href, re.IGNORECASE)
                if match:
                    version = match.group(1) if match.group(1) else None
                    
                    tech = TechnologyDetection(
                        id=f"css_{hashlib.md5(f'{name}_{version}'.encode()).hexdigest()[:12]}",
                        name=name,
                        category=category,
                        version=version,
                        confidence=ConfidenceLevel.MEDIUM,
                        detection_methods=[DetectionMethod.CSS],
                        evidence=[f"CSS source: {css_href}"],
                        website=None,
                        description=f"Detected from CSS source",
                        detected_at=datetime.now()
                    )
                    technologies.append(tech)
                    
        except Exception as e:
            logger.error(f"CSS source analysis failed: {e}")
        
        return technologies

    async def _fingerprint_deep_layer(self, target_url: str) -> List[TechnologyDetection]:
        """Deep fingerprinting using advanced techniques"""
        technologies = []
        
        try:
            # Robots.txt analysis
            robots_techs = await self._analyze_robots_txt(target_url)
            technologies.extend(robots_techs)
            
            # Favicon analysis
            favicon_techs = await self._analyze_favicon(target_url)
            technologies.extend(favicon_techs)
            
            # SSL certificate analysis
            ssl_techs = await self._analyze_ssl_certificate(target_url)
            technologies.extend(ssl_techs)
            
            # Error page analysis
            error_techs = await self._analyze_error_pages(target_url)
            technologies.extend(error_techs)
            
        except Exception as e:
            logger.error(f"Deep layer fingerprinting failed: {e}")
        
        return technologies

    async def _analyze_robots_txt(self, target_url: str) -> List[TechnologyDetection]:
        """Analyze robots.txt for technology clues"""
        technologies = []
        
        try:
            robots_url = urljoin(target_url, "/robots.txt")
            async with self.session.get(robots_url) as response:
                if response.status == 200:
                    content = await response.text()
                    self.stats["requests_sent"] += 1
                    
                    # Common CMS robots.txt patterns
                    if "/wp-admin/" in content or "/wp-content/" in content:
                        tech = TechnologyDetection(
                            id=f"robots_wp_{int(time.time())}",
                            name="WordPress",
                            category=TechnologyCategory.CMS,
                            version=None,
                            confidence=ConfidenceLevel.HIGH,
                            detection_methods=[DetectionMethod.ROBOTS_TXT],
                            evidence=["WordPress paths in robots.txt"],
                            website=None,
                            description="Detected from robots.txt",
                            detected_at=datetime.now()
                        )
                        technologies.append(tech)
                    
                    if "/sites/default/files/" in content:
                        tech = TechnologyDetection(
                            id=f"robots_drupal_{int(time.time())}",
                            name="Drupal",
                            category=TechnologyCategory.CMS,
                            version=None,
                            confidence=ConfidenceLevel.HIGH,
                            detection_methods=[DetectionMethod.ROBOTS_TXT],
                            evidence=["Drupal paths in robots.txt"],
                            website=None,
                            description="Detected from robots.txt",
                            detected_at=datetime.now()
                        )
                        technologies.append(tech)
                        
        except Exception as e:
            logger.debug(f"Robots.txt analysis failed: {e}")
        
        return technologies

    async def _analyze_favicon(self, target_url: str) -> List[TechnologyDetection]:
        """Analyze favicon for technology detection"""
        technologies = []
        
        try:
            favicon_url = urljoin(target_url, "/favicon.ico")
            async with self.session.get(favicon_url) as response:
                if response.status == 200:
                    content = await response.read()
                    self.stats["requests_sent"] += 1
                    
                    # Calculate favicon hash
                    favicon_hash = hashlib.md5(content).hexdigest()
                    
                    # Known favicon hashes (simplified example)
                    known_favicons = {
                        "e8d3b40d8c8f8e0b9f9c8d8f8e0b9f9c": ("WordPress", TechnologyCategory.CMS),
                        "f1e8d3b40d8c8f8e0b9f9c8d8f8e0b9f": ("Drupal", TechnologyCategory.CMS),
                        # Add more known favicon hashes
                    }
                    
                    if favicon_hash in known_favicons:
                        name, category = known_favicons[favicon_hash]
                        tech = TechnologyDetection(
                            id=f"favicon_{favicon_hash[:12]}",
                            name=name,
                            category=category,
                            version=None,
                            confidence=ConfidenceLevel.MEDIUM,
                            detection_methods=[DetectionMethod.FAVICON],
                            evidence=[f"Favicon hash: {favicon_hash}"],
                            website=None,
                            description="Detected from favicon hash",
                            detected_at=datetime.now()
                        )
                        technologies.append(tech)
                        
        except Exception as e:
            logger.debug(f"Favicon analysis failed: {e}")
        
        return technologies

    async def _analyze_ssl_certificate(self, target_url: str) -> List[TechnologyDetection]:
        """Analyze SSL certificate for technology clues"""
        technologies = []
        
        try:
            parsed_url = urlparse(target_url)
            if parsed_url.scheme == "https":
                hostname = parsed_url.hostname
                port = parsed_url.port or 443
                
                # Get SSL certificate
                context = ssl.create_default_context()
                context.check_hostname = False
                context.verify_mode = ssl.CERT_NONE
                
                with socket.create_connection((hostname, port), timeout=5) as sock:
                    with context.wrap_socket(sock, server_hostname=hostname) as ssock:
                        cert = ssock.getpeercert()
                        
                        if cert:
                            # Analyze certificate issuer
                            issuer = dict(x[0] for x in cert.get("issuer", []))
                            organization = issuer.get("organizationName", "")
                            
                            if "Cloudflare" in organization:
                                tech = TechnologyDetection(
                                    id=f"ssl_cloudflare_{int(time.time())}",
                                    name="Cloudflare",
                                    category=TechnologyCategory.CDN,
                                    version=None,
                                    confidence=ConfidenceLevel.HIGH,
                                    detection_methods=[DetectionMethod.SSL_CERTIFICATE],
                                    evidence=[f"SSL issuer: {organization}"],
                                    website=None,
                                    description="Detected from SSL certificate",
                                    detected_at=datetime.now()
                                )
                                technologies.append(tech)
                                
        except Exception as e:
            logger.debug(f"SSL certificate analysis failed: {e}")
        
        return technologies

    async def _analyze_error_pages(self, target_url: str) -> List[TechnologyDetection]:
        """Analyze error pages for technology detection"""
        technologies = []
        
        try:
            # Test common error endpoints
            error_endpoints = ["/404", "/nonexistent", "/error"]
            
            for endpoint in error_endpoints:
                try:
                    error_url = urljoin(target_url, endpoint)
                    async with self.session.get(error_url) as response:
                        if response.status in [404, 500]:
                            content = await response.text()
                            self.stats["requests_sent"] += 1
                            
                            # Look for technology indicators in error pages
                            error_patterns = {
                                r"Apache/([0-9.]+)": ("Apache", TechnologyCategory.WEB_SERVER),
                                r"nginx/([0-9.]+)": ("Nginx", TechnologyCategory.WEB_SERVER),
                                r"Microsoft-IIS/([0-9.]+)": ("Microsoft-IIS", TechnologyCategory.WEB_SERVER),
                                r"PHP/([0-9.]+)": ("PHP", TechnologyCategory.PROGRAMMING_LANGUAGE)
                            }
                            
                            for pattern, (name, category) in error_patterns.items():
                                match = re.search(pattern, content)
                                if match:
                                    version = match.group(1) if match.groups() else None
                                    
                                    tech = TechnologyDetection(
                                        id=f"error_{name}_{version}_{int(time.time())}",
                                        name=name,
                                        category=category,
                                        version=version,
                                        confidence=ConfidenceLevel.HIGH,
                                        detection_methods=[DetectionMethod.ERROR_PAGES],
                                        evidence=[f"Error page pattern: {match.group(0)}"],
                                        website=None,
                                        description="Detected from error page",
                                        detected_at=datetime.now()
                                    )
                                    technologies.append(tech)
                                    break  # Avoid duplicates
                            
                except Exception as e:
                    logger.debug(f"Error endpoint {endpoint} failed: {e}")
                    continue
                    
        except Exception as e:
            logger.error(f"Error page analysis failed: {e}")
        
        return technologies

    async def _fingerprint_ai_layer(self, target_url: str, existing_technologies: List[TechnologyDetection]) -> List[TechnologyDetection]:
        """AI-powered technology detection and analysis"""
        technologies = []
        
        try:
            # Get page content for AI analysis
            async with self.session.get(target_url) as response:
                if response.status == 200:
                    content = await response.text()
                    headers = dict(response.headers)
                    
                    # Prepare analysis request
                    analysis_request = AnalysisRequest(
                        analysis_type="technology_fingerprinting",
                        target_info={
                            "url": target_url,
                            "headers": headers,
                            "content_sample": content[:10000],  # Limit content size
                            "existing_detections": [
                                {"name": t.name, "category": t.category.value, "version": t.version}
                                for t in existing_technologies
                            ]
                        },
                        context={
                            "task": "Analyze this web application and identify additional technologies, frameworks, and tools that may not be detected by signature-based methods"
                        }
                    )
                    
                    ai_result = await self.ai_service.analyze(analysis_request)
                    
                    if ai_result and "additional_technologies" in ai_result:
                        for tech_info in ai_result["additional_technologies"]:
                            tech = TechnologyDetection(
                                id=f"ai_{hashlib.md5(f'{tech_info.get(\"name\", \"\")}_{time.time()}'.encode()).hexdigest()[:12]}",
                                name=tech_info.get("name", "Unknown"),
                                category=TechnologyCategory(tech_info.get("category", "framework")),
                                version=tech_info.get("version"),
                                confidence=ConfidenceLevel(tech_info.get("confidence", "low")),
                                detection_methods=[DetectionMethod.AI_ANALYSIS],
                                evidence=tech_info.get("evidence", []),
                                website=tech_info.get("website"),
                                description=tech_info.get("description", "AI-detected technology"),
                                detected_at=datetime.now()
                            )
                            technologies.append(tech)
                            
        except Exception as e:
            logger.error(f"AI fingerprinting failed: {e}")
        
        return technologies

    def _merge_technology_detections(self, technologies: List[TechnologyDetection]) -> List[TechnologyDetection]:
        """Merge duplicate technology detections"""
        merged = {}
        
        try:
            for tech in technologies:
                key = f"{tech.name}_{tech.version or 'no_version'}"
                
                if key in merged:
                    # Merge detection methods and evidence
                    existing = merged[key]
                    existing.detection_methods.extend(tech.detection_methods)
                    existing.evidence.extend(tech.evidence)
                    
                    # Use highest confidence
                    if self._confidence_to_score(tech.confidence) > self._confidence_to_score(existing.confidence):
                        existing.confidence = tech.confidence
                        
                    # Remove duplicates
                    existing.detection_methods = list(set(existing.detection_methods))
                    existing.evidence = list(set(existing.evidence))
                else:
                    merged[key] = tech
                    
        except Exception as e:
            logger.error(f"Technology merging failed: {e}")
        
        return list(merged.values())

    def _confidence_to_score(self, confidence: ConfidenceLevel) -> float:
        """Convert confidence level to numeric score"""
        scores = {
            ConfidenceLevel.LOW: 0.25,
            ConfidenceLevel.MEDIUM: 0.5,
            ConfidenceLevel.HIGH: 0.75,
            ConfidenceLevel.CERTAIN: 1.0
        }
        return scores.get(confidence, 0.0)

    def _calculate_detection_confidence(self, base_confidence: float, methods: List[DetectionMethod]) -> ConfidenceLevel:
        """Calculate detection confidence level"""
        # Adjust confidence based on detection methods
        method_weights = {
            DetectionMethod.HTTP_HEADERS: 1.0,
            DetectionMethod.HTML_CONTENT: 0.8,
            DetectionMethod.JAVASCRIPT: 0.7,
            DetectionMethod.CSS: 0.6,
            DetectionMethod.COOKIES: 0.8,
            DetectionMethod.URL_PATTERNS: 0.6,
            DetectionMethod.ERROR_PAGES: 0.9,
            DetectionMethod.FAVICON: 0.5,
            DetectionMethod.ROBOTS_TXT: 0.7,
            DetectionMethod.SSL_CERTIFICATE: 0.9,
            DetectionMethod.AI_ANALYSIS: 0.6
        }
        
        if not methods:
            adjusted_confidence = base_confidence
        else:
            avg_weight = sum(method_weights.get(method, 0.5) for method in methods) / len(methods)
            adjusted_confidence = base_confidence * avg_weight
        
        if adjusted_confidence >= 0.9:
            return ConfidenceLevel.CERTAIN
        elif adjusted_confidence >= 0.7:
            return ConfidenceLevel.HIGH
        elif adjusted_confidence >= 0.4:
            return ConfidenceLevel.MEDIUM
        else:
            return ConfidenceLevel.LOW

    async def _correlate_vulnerabilities(self, technologies: List[TechnologyDetection]) -> List[VulnerabilityMapping]:
        """Correlate detected technologies with known vulnerabilities"""
        vulnerabilities = []
        
        try:
            # Simple vulnerability correlation (in real implementation, use CVE database)
            vuln_db = {
                "Apache": {
                    "2.4.41": ["CVE-2019-10081", "CVE-2019-10082"],
                    "2.4.39": ["CVE-2019-0211", "CVE-2019-0215"]
                },
                "PHP": {
                    "7.3.0": ["CVE-2019-11036", "CVE-2019-11034"],
                    "7.2.0": ["CVE-2018-19518", "CVE-2018-19520"]
                },
                "WordPress": {
                    "5.2.0": ["CVE-2019-16773", "CVE-2019-17671"],
                    "5.1.0": ["CVE-2019-8942", "CVE-2019-8943"]
                }
            }
            
            for tech in technologies:
                if tech.name in vuln_db and tech.version:
                    version_vulns = vuln_db[tech.name].get(tech.version, [])
                    if version_vulns:
                        vuln = VulnerabilityMapping(
                            technology_name=tech.name,
                            version=tech.version,
                            cve_ids=version_vulns,
                            severity_scores=[7.5] * len(version_vulns),  # Example scores
                            exploitability="high",
                            description=f"Known vulnerabilities in {tech.name} {tech.version}"
                        )
                        vulnerabilities.append(vuln)
                        
        except Exception as e:
            logger.error(f"Vulnerability correlation failed: {e}")
        
        return vulnerabilities

    def _calculate_security_score(self, technologies: List[TechnologyDetection], vulnerabilities: List[VulnerabilityMapping]) -> float:
        """Calculate overall security score"""
        try:
            if not technologies:
                return 0.0
            
            # Base score
            score = 80.0
            
            # Deduct points for vulnerabilities
            for vuln in vulnerabilities:
                avg_severity = sum(vuln.severity_scores) / len(vuln.severity_scores) if vuln.severity_scores else 5.0
                score -= avg_severity
            
            # Deduct points for outdated technologies
            for tech in technologies:
                if tech.version:
                    # Simple heuristic: check if version seems old
                    try:
                        major_version = int(tech.version.split('.')[0])
                        if major_version < 2:  # Very old
                            score -= 10
                        elif major_version < 5:  # Somewhat old
                            score -= 5
                    except (ValueError, IndexError):
                        pass
            
            return max(0.0, min(100.0, score))
            
        except Exception as e:
            logger.error(f"Security score calculation failed: {e}")
            return 50.0  # Default moderate score

    def _calculate_modernization_score(self, technologies: List[TechnologyDetection]) -> float:
        """Calculate modernization score"""
        try:
            if not technologies:
                return 0.0
            
            # Modern technology indicators
            modern_techs = {
                "React": 25,
                "Vue.js": 25,
                "Angular": 20,
                "Node.js": 20,
                "Express": 15,
                "HTTP/2": 15,
                "TLS 1.3": 20
            }
            
            score = 0.0
            for tech in technologies:
                if tech.name in modern_techs:
                    score += modern_techs[tech.name]
            
            # Bonus for HTTPS
            if any(tech.name.lower() in ["cloudflare", "ssl", "tls"] for tech in technologies):
                score += 10
            
            return min(100.0, score)
            
        except Exception as e:
            logger.error(f"Modernization score calculation failed: {e}")
            return 50.0

    def _generate_recommendations(self, technologies: List[TechnologyDetection], vulnerabilities: List[VulnerabilityMapping]) -> List[str]:
        """Generate security and modernization recommendations"""
        recommendations = []
        
        try:
            # Vulnerability-based recommendations
            if vulnerabilities:
                recommendations.append("Update vulnerable software components identified in the scan")
                recommendations.append("Implement a regular security patching schedule")
            
            # Technology-specific recommendations
            tech_names = [tech.name for tech in technologies]
            
            if "WordPress" in tech_names:
                recommendations.append("Keep WordPress core, themes, and plugins updated")
                recommendations.append("Implement WordPress security hardening measures")
            
            if "Apache" in tech_names:
                recommendations.append("Configure Apache security headers (HSTS, CSP, etc.)")
                recommendations.append("Hide Apache version information")
            
            if "PHP" in tech_names:
                recommendations.append("Use supported PHP versions and configure security settings")
                recommendations.append("Implement proper input validation and sanitization")
            
            # General recommendations
            recommendations.append("Implement Web Application Firewall (WAF)")
            recommendations.append("Enable security headers for better protection")
            recommendations.append("Regular security assessments and penetration testing")
            
        except Exception as e:
            logger.error(f"Recommendation generation failed: {e}")
        
        return recommendations

    def get_fingerprinting_statistics(self) -> Dict[str, Any]:
        """Get fingerprinting statistics"""
        return {
            "statistics": self.stats,
            "signatures_loaded": len(self.signatures),
            "cache_size": len(self.detection_cache)
        }

    def export_stack_profile(self, profile: StackProfile, format: str = "json") -> str:
        """Export stack profile in specified format"""
        try:
            if format.lower() == "json":
                return json.dumps(asdict(profile), indent=2, default=str)
            
            elif format.lower() == "txt":
                lines = [
                    f"Technology Stack Profile: {profile.target_url}",
                    f"Generated: {profile.generated_at}",
                    f"Security Score: {profile.security_score:.1f}/100",
                    f"Modernization Score: {profile.modernization_score:.1f}/100",
                    "",
                    "Detected Technologies:",
                ]
                
                for tech in profile.technologies:
                    version_str = f" v{tech.version}" if tech.version else ""
                    lines.append(f"  • {tech.name}{version_str} ({tech.category.value}) - {tech.confidence.value}")
                
                if profile.vulnerabilities:
                    lines.extend(["", "Vulnerabilities:"])
                    for vuln in profile.vulnerabilities:
                        lines.append(f"  • {vuln.technology_name} {vuln.version}: {', '.join(vuln.cve_ids)}")
                
                if profile.recommendations:
                    lines.extend(["", "Recommendations:"])
                    for rec in profile.recommendations:
                        lines.append(f"  • {rec}")
                
                return "\n".join(lines)
                
        except Exception as e:
            logger.error(f"Stack profile export failed: {e}")
            return ""