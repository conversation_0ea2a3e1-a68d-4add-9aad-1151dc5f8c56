/**
 * Metasploit Tool Component
 * Comprehensive penetration testing framework with exploitation capabilities
 */
import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { 
  Key,
  Play,
  Square,
  Download,
  Settings,
  Target,
  Globe,
  FileText,
  Terminal,
  Copy,
  RotateCcw,
  Clock,
  Zap,
  Filter,
  Eye,
  AlertTriangle,
  CheckCircle,
  TrendingUp,
  Activity,
  Lock,
  Search,
  List,
  BarChart3,
  Database,
  Server,
  Bug,
  Info,
  Code,
  Package,
  Layers,
  Shield,
  Crosshair,
  Cpu,
  Network,
  Gauge,
  Tag,
  Hash,
  Workflow,
  GitBranch,
  Book,
  Users,
  Table,
  FileX,
  Skull,
  Unlock,
  HardDrive,
  Flame,
  Binary,
  Braces,
  FileKey,
  History,
  Folder,
  Star,
  Crown,
  Award,
  Sparkles,
  Wifi,
  Router,
  Mail,
  CloudRain,
  Rss,
  UserCheck,
  UserX,
  Timer,
  Pause,
  FastForward,
  Rewind,
  SkipForward,
  Certificate,
  ShieldCheck,
  ShieldAlert,
  AlertCircle,
  XCircle,
  RefreshCw,
  ExternalLink,
  FileCheck,
  FileWarning,
  Fingerprint,
  Key as KeyIcon,
  LockOpen,
  ShieldOff,
  Verified,
  ScanLine,
  Scan,
  FileJson,
  FileCode,
  Clipboard,
  Monitor,
  Globe2,
  Radar,
  Smartphone,
  Laptop,
  Tablet,
  Rocket,
  BugPlay,
  Swords,
  Bomb,
  Pickaxe,
  Gamepad2,
  Command,
  MousePointer2,
  Maximize,
  Eye as EyeIcon,
  Microscope,
  Wrench,
  Cog
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useBackendStore } from '@/stores/backend-store';
import { apiClient, ToolExecution } from '@/services/api-client';
import { toast } from 'sonner';

/**
 * Metasploit configuration interface
 */
interface MetasploitConfig {
  target: {
    host: string;
    port: number;
    protocol: 'tcp' | 'udp';
    service: string;
    hostsFile: string;
  };
  exploit: {
    module: string;
    category: string;
    rank: string;
    platform: string[];
    targets: string[];
    selectedTarget: number;
  };
  payload: {
    module: string;
    type: 'bind' | 'reverse' | 'staged' | 'inline';
    encoder: string;
    iterations: number;
    format: 'exe' | 'dll' | 'elf' | 'raw' | 'powershell' | 'python';
    badChars: string;
  };
  listener: {
    lhost: string;
    lport: number;
    bindAddress: string;
    ssl: boolean;
    stagerRetryCount: number;
    stagerRetryWait: number;
  };
  options: {
    [key: string]: string | number | boolean;
  };
  advanced: {
    evasion: {
      enabled: boolean;
      techniques: string[];
      customOptions: string;
    };
    postExploitation: {
      autoMigrate: boolean;
      getsystem: boolean;
      persistence: boolean;
      keylogger: boolean;
      screenshot: boolean;
      hashdump: boolean;
    };
    verification: {
      checkExploit: boolean;
      validateTarget: boolean;
      skipHandler: boolean;
    };
  };
}

/**
 * Metasploit exploit categories
 */
const EXPLOIT_CATEGORIES = {
  'auxiliary': {
    name: 'Auxiliary',
    description: 'Supporting modules (scanners, fuzzers, DoS)',
    icon: Wrench,
    count: 1500
  },
  'exploits': {
    name: 'Exploits',
    description: 'Vulnerability exploitation modules',
    icon: Bomb,
    count: 2000
  },
  'payloads': {
    name: 'Payloads',
    description: 'Code executed after successful exploitation',
    icon: Rocket,
    count: 800
  },
  'encoders': {
    name: 'Encoders',
    description: 'Payload encoding to evade detection',
    icon: Shield,
    count: 50
  },
  'nops': {
    name: 'NOPs',
    description: 'No-operation instructions for buffer alignment',
    icon: Package,
    count: 15
  },
  'post': {
    name: 'Post-Exploitation',
    description: 'Modules for post-exploitation activities',
    icon: Pickaxe,
    count: 400
  },
  'evasion': {
    name: 'Evasion',
    description: 'Anti-virus and security solution evasion',
    icon: EyeIcon,
    count: 25
  }
};

/**
 * Popular exploit modules by category
 */
const POPULAR_EXPLOITS = {
  'windows': [
    { module: 'exploit/windows/smb/ms17_010_eternalblue', name: 'EternalBlue SMB RCE', cve: 'CVE-2017-0144' },
    { module: 'exploit/windows/smb/ms08_067_netapi', name: 'MS08-067 NetAPI RCE', cve: 'CVE-2008-4250' },
    { module: 'exploit/windows/dcerpc/ms03_026_dcom', name: 'MS03-026 DCOM RCE', cve: 'CVE-2003-0352' },
    { module: 'exploit/windows/browser/ms10_002_aurora', name: 'MS10-002 Aurora IE RCE', cve: 'CVE-2010-0249' },
    { module: 'exploit/windows/local/ms16_032_secondary_logon_handle_privesc', name: 'MS16-032 Secondary Logon', cve: 'CVE-2016-0099' }
  ],
  'linux': [
    { module: 'exploit/linux/misc/lampp_phpmyadmin_exec', name: 'LAMPP phpMyAdmin RCE', cve: 'N/A' },
    { module: 'exploit/linux/http/apache_mod_cgi_bash_env_exec', name: 'Shellshock Apache CGI', cve: 'CVE-2014-6271' },
    { module: 'exploit/linux/local/dirty_cow', name: 'Dirty COW Privilege Escalation', cve: 'CVE-2016-5195' },
    { module: 'exploit/linux/ssh/libssh_auth_bypass', name: 'libssh Authentication Bypass', cve: 'CVE-2018-10933' },
    { module: 'exploit/linux/http/webmin_backdoor', name: 'Webmin Backdoor RCE', cve: 'CVE-2019-15107' }
  ],
  'web': [
    { module: 'exploit/multi/http/struts2_content_type_ognl', name: 'Struts2 OGNL Injection', cve: 'CVE-2017-5638' },
    { module: 'exploit/multi/http/drupal_drupageddon2', name: 'Drupal Drupageddon2 RCE', cve: 'CVE-2018-7600' },
    { module: 'exploit/multi/http/joomla_http_header_rce', name: 'Joomla HTTP Header RCE', cve: 'CVE-2015-8562' },
    { module: 'exploit/multi/http/tomcat_mgr_deploy', name: 'Tomcat Manager Deploy', cve: 'N/A' },
    { module: 'exploit/multi/http/jenkins_script_console', name: 'Jenkins Script Console', cve: 'N/A' }
  ],
  'database': [
    { module: 'exploit/windows/mssql/ms02_056_hello', name: 'MS SQL Server Hello Overflow', cve: 'CVE-2002-0649' },
    { module: 'exploit/linux/mysql/mysql_yassl_getname', name: 'MySQL yaSSL Hostname Overflow', cve: 'CVE-2008-0226' },
    { module: 'exploit/multi/postgres/postgres_copy_from_program_cmd_exec', name: 'PostgreSQL COPY FROM RCE', cve: 'CVE-2019-9193' },
    { module: 'exploit/multi/http/oracle_reports_rce', name: 'Oracle Reports RCE', cve: 'CVE-2012-3152' },
    { module: 'exploit/windows/oracle/tns_auth_sesskey', name: 'Oracle TNS AUTH_SESSKEY', cve: 'CVE-2009-1979' }
  ]
};

/**
 * Common payload types
 */
const PAYLOAD_TYPES = {
  'windows': {
    'meterpreter': [
      { module: 'windows/meterpreter/reverse_tcp', name: 'Reverse TCP Meterpreter', type: 'staged' },
      { module: 'windows/meterpreter_reverse_tcp', name: 'Reverse TCP Meterpreter (Inline)', type: 'inline' },
      { module: 'windows/meterpreter/bind_tcp', name: 'Bind TCP Meterpreter', type: 'staged' },
      { module: 'windows/x64/meterpreter/reverse_tcp', name: 'x64 Reverse TCP Meterpreter', type: 'staged' }
    ],
    'shell': [
      { module: 'windows/shell/reverse_tcp', name: 'Reverse TCP Shell', type: 'staged' },
      { module: 'windows/shell_reverse_tcp', name: 'Reverse TCP Shell (Inline)', type: 'inline' },
      { module: 'windows/shell/bind_tcp', name: 'Bind TCP Shell', type: 'staged' },
      { module: 'windows/powershell_reverse_tcp', name: 'PowerShell Reverse TCP', type: 'inline' }
    ]
  },
  'linux': {
    'meterpreter': [
      { module: 'linux/x86/meterpreter/reverse_tcp', name: 'x86 Reverse TCP Meterpreter', type: 'staged' },
      { module: 'linux/x64/meterpreter/reverse_tcp', name: 'x64 Reverse TCP Meterpreter', type: 'staged' },
      { module: 'linux/x86/meterpreter_reverse_tcp', name: 'x86 Reverse TCP Meterpreter (Inline)', type: 'inline' }
    ],
    'shell': [
      { module: 'linux/x86/shell/reverse_tcp', name: 'x86 Reverse TCP Shell', type: 'staged' },
      { module: 'linux/x64/shell_reverse_tcp', name: 'x64 Reverse TCP Shell (Inline)', type: 'inline' },
      { module: 'linux/x86/shell/bind_tcp', name: 'x86 Bind TCP Shell', type: 'staged' }
    ]
  },
  'java': {
    'meterpreter': [
      { module: 'java/meterpreter/reverse_tcp', name: 'Java Reverse TCP Meterpreter', type: 'staged' },
      { module: 'java/meterpreter/bind_tcp', name: 'Java Bind TCP Meterpreter', type: 'staged' }
    ],
    'shell': [
      { module: 'java/shell_reverse_tcp', name: 'Java Reverse TCP Shell', type: 'inline' }
    ]
  }
};

/**
 * Payload encoders for evasion
 */
const PAYLOAD_ENCODERS = {
  'x86': [
    { module: 'x86/shikata_ga_nai', name: 'Shikata Ga Nai', rating: 'excellent' },
    { module: 'x86/fnstenv_mov', name: 'FnstEnv Mov', rating: 'normal' },
    { module: 'x86/jmp_call_additive', name: 'Jump/Call Additive', rating: 'normal' },
    { module: 'x86/nonalpha', name: 'Non-Alpha', rating: 'low' },
    { module: 'x86/nonupper', name: 'Non-Upper', rating: 'low' }
  ],
  'x64': [
    { module: 'x64/xor', name: 'XOR Encoder', rating: 'normal' },
    { module: 'x64/zutto_dekiru', name: 'Zutto Dekiru', rating: 'good' }
  ],
  'generic': [
    { module: 'generic/none', name: 'No Encoding', rating: 'normal' },
    { module: 'cmd/echo', name: 'Echo', rating: 'good' }
  ]
};

/**
 * Exploit ranking system
 */
const EXPLOIT_RANKS = {
  'excellent': { name: 'Excellent', description: 'Exploit will never crash the service', color: 'green' },
  'great': { name: 'Great', description: 'Exploit has a default target and rarely crashes', color: 'blue' },
  'good': { name: 'Good', description: 'Exploit has a default target and is usually reliable', color: 'yellow' },
  'normal': { name: 'Normal', description: 'Exploit is generally reliable but depends on a specific version', color: 'orange' },
  'average': { name: 'Average', description: 'Exploit is generally unreliable for unknown reasons', color: 'orange' },
  'low': { name: 'Low', description: 'Exploit is nearly guaranteed to crash the service', color: 'red' },
  'manual': { name: 'Manual', description: 'Exploit is unstable or difficult to exploit and requires manual effort', color: 'red' }
};

export default function MetasploitTool() {
  const { isConnected } = useBackendStore();
  const [config, setConfig] = React.useState<MetasploitConfig>({
    target: {
      host: '',
      port: 445,
      protocol: 'tcp',
      service: 'smb',
      hostsFile: ''
    },
    exploit: {
      module: '',
      category: 'exploits',
      rank: 'normal',
      platform: [],
      targets: [],
      selectedTarget: 0
    },
    payload: {
      module: '',
      type: 'reverse',
      encoder: '',
      iterations: 1,
      format: 'exe',
      badChars: ''
    },
    listener: {
      lhost: '',
      lport: 4444,
      bindAddress: '0.0.0.0',
      ssl: false,
      stagerRetryCount: 10,
      stagerRetryWait: 5
    },
    options: {},
    advanced: {
      evasion: {
        enabled: false,
        techniques: [],
        customOptions: ''
      },
      postExploitation: {
        autoMigrate: false,
        getsystem: false,
        persistence: false,
        keylogger: false,
        screenshot: false,
        hashdump: false
      },
      verification: {
        checkExploit: true,
        validateTarget: true,
        skipHandler: false
      }
    }
  });

  const [isRunning, setIsRunning] = React.useState(false);
  const [output, setOutput] = React.useState<string[]>([]);
  const [results, setResults] = React.useState<any[]>([]);
  const [currentExecution, setCurrentExecution] = React.useState<ToolExecution | null>(null);
  const [progress, setProgress] = React.useState(0);
  const [sessionInfo, setSessionInfo] = React.useState<any>({
    sessions: [],
    activeSession: null,
    exploitResults: []
  });

  const [selectedCategory, setSelectedCategory] = React.useState<string>('windows');

  /**
   * Apply exploit preset
   */
  const applyExploitPreset = (platform: string, exploit: any) => {
    setConfig(prev => ({
      ...prev,
      exploit: {
        ...prev.exploit,
        module: exploit.module,
        platform: [platform]
      }
    }));
    toast.success(`Applied ${exploit.name} exploit`);
  };

  /**
   * Apply payload preset
   */
  const applyPayloadPreset = (platform: string, payloadType: string, payload: any) => {
    setConfig(prev => ({
      ...prev,
      payload: {
        ...prev.payload,
        module: payload.module,
        type: payload.type as any
      }
    }));
    toast.success(`Applied ${payload.name} payload`);
  };

  /**
   * Generate Metasploit commands
   */
  const generateCommands = (): string[] => {
    const commands: string[] = [];

    // Use exploit module
    if (config.exploit.module) {
      commands.push(`use ${config.exploit.module}`);
      
      // Set target options
      if (config.target.host) {
        commands.push(`set RHOSTS ${config.target.host}`);
      }
      if (config.target.port) {
        commands.push(`set RPORT ${config.target.port}`);
      }
      
      // Set payload
      if (config.payload.module) {
        commands.push(`set PAYLOAD ${config.payload.module}`);
        
        // Set payload options
        if (config.listener.lhost) {
          commands.push(`set LHOST ${config.listener.lhost}`);
        }
        if (config.listener.lport) {
          commands.push(`set LPORT ${config.listener.lport}`);
        }
        
        // Encoder options
        if (config.payload.encoder) {
          commands.push(`set ENCODER ${config.payload.encoder}`);
        }
        if (config.payload.iterations > 1) {
          commands.push(`set ITERATIONS ${config.payload.iterations}`);
        }
      }
      
      // Set exploit target
      if (config.exploit.selectedTarget > 0) {
        commands.push(`set TARGET ${config.exploit.selectedTarget}`);
      }
      
      // Set custom options
      Object.entries(config.options).forEach(([key, value]) => {
        if (value !== '' && value !== null && value !== undefined) {
          commands.push(`set ${key} ${value}`);
        }
      });
      
      // Advanced options
      if (config.advanced.verification.checkExploit) {
        commands.push('check');
      }
      
      // Execute exploit
      commands.push('exploit');
      
      // Post-exploitation commands
      if (config.advanced.postExploitation.autoMigrate) {
        commands.push('run post/windows/manage/migrate');
      }
      if (config.advanced.postExploitation.getsystem) {
        commands.push('getsystem');
      }
      if (config.advanced.postExploitation.hashdump) {
        commands.push('hashdump');
      }
      if (config.advanced.postExploitation.screenshot) {
        commands.push('screenshot');
      }
    }

    return commands;
  };

  /**
   * Generate standalone payload
   */
  const generatePayload = (): string => {
    if (!config.payload.module) return '';

    const parts = ['msfvenom'];
    parts.push('-p', config.payload.module);
    
    if (config.listener.lhost) {
      parts.push(`LHOST=${config.listener.lhost}`);
    }
    if (config.listener.lport) {
      parts.push(`LPORT=${config.listener.lport}`);
    }
    
    if (config.payload.encoder) {
      parts.push('-e', config.payload.encoder);
    }
    if (config.payload.iterations > 1) {
      parts.push('-i', config.payload.iterations.toString());
    }
    if (config.payload.badChars) {
      parts.push('-b', `"${config.payload.badChars}"`);
    }
    
    parts.push('-f', config.payload.format);
    parts.push('-o', `payload.${config.payload.format}`);
    
    return parts.join(' ');
  };

  /**
   * Start Metasploit execution
   */
  const startExecution = async () => {
    if (!isConnected) {
      toast.error('Backend not connected');
      return;
    }

    // Validation
    if (!config.exploit.module) {
      toast.error('Please select an exploit module');
      return;
    }

    if (!config.target.host && !config.target.hostsFile) {
      toast.error('Please specify a target host or hosts file');
      return;
    }

    try {
      setIsRunning(true);
      setOutput([]);
      setResults([]);
      setProgress(0);
      setSessionInfo({
        sessions: [],
        activeSession: null,
        exploitResults: []
      });

      const execution = await apiClient.startTool('metasploit', {
        ...config,
        commands: generateCommands()
      });

      setCurrentExecution(execution);
      toast.success('Metasploit exploit started');

      // Listen for progress updates
      const eventSource = new EventSource(`/api/tools/metasploit/execution/${execution.id}/stream`);
      
      eventSource.onmessage = (event) => {
        const data = JSON.parse(event.data);
        
        if (data.type === 'output') {
          setOutput(prev => [...prev, data.content]);
        } else if (data.type === 'progress') {
          setProgress(data.progress);
        } else if (data.type === 'result') {
          setResults(prev => [...prev, data.result]);
        } else if (data.type === 'session_info') {
          setSessionInfo(data.info);
        } else if (data.type === 'complete') {
          setIsRunning(false);
          eventSource.close();
          toast.success('Metasploit execution completed');
        } else if (data.type === 'error') {
          setIsRunning(false);
          eventSource.close();
          toast.error(`Error: ${data.message}`);
        }
      };

      eventSource.onerror = () => {
        setIsRunning(false);
        eventSource.close();
        toast.error('Connection to execution stream lost');
      };

    } catch (error) {
      setIsRunning(false);
      console.error('Failed to start Metasploit:', error);
      toast.error('Failed to start Metasploit');
    }
  };

  /**
   * Stop execution
   */
  const stopExecution = async () => {
    if (!currentExecution) return;

    try {
      await apiClient.stopTool('metasploit', currentExecution.id);
      setIsRunning(false);
      toast.success('Metasploit execution stopped');
    } catch (error) {
      console.error('Failed to stop Metasploit:', error);
      toast.error('Failed to stop Metasploit');
    }
  };

  /**
   * Export results
   */
  const exportResults = () => {
    const exportData = {
      config,
      results,
      sessionInfo,
      commands: generateCommands(),
      payload: generatePayload(),
      timestamp: new Date().toISOString()
    };

    const blob = new Blob([JSON.stringify(exportData, null, 2)], {
      type: 'application/json'
    });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `metasploit-results-${Date.now()}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    
    toast.success('Results exported successfully');
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className="flex items-center justify-center w-10 h-10 bg-red-100 rounded-lg">
            <Swords className="w-5 h-5 text-red-600" />
          </div>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Metasploit Framework</h1>
            <p className="text-sm text-gray-500">Comprehensive penetration testing framework with exploitation capabilities</p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Badge variant={isConnected ? "default" : "secondary"}>
            {isConnected ? "Connected" : "Disconnected"}
          </Badge>
          <Badge variant="outline" className="font-mono">v6.3.55</Badge>
        </div>
      </div>

      {/* Critical Warning */}
      <Card className="border-red-200 bg-red-50">
        <CardContent className="pt-4">
          <div className="flex items-start space-x-2">
            <AlertTriangle className="w-5 h-5 text-red-600 mt-0.5" />
            <div className="space-y-1">
              <p className="text-sm font-medium text-red-800">
                Authorized Testing Only - Legal Warning
              </p>
              <p className="text-xs text-red-700">
                Metasploit contains active exploitation capabilities. Only use on systems you own or have explicit written authorization to test. 
                Unauthorized access attempts are illegal and may result in severe legal consequences including criminal prosecution.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Exploit Categories */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Package className="w-5 h-5" />
            <span>Metasploit Framework Modules</span>
          </CardTitle>
          <CardDescription>
            Comprehensive collection of security testing modules
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
            {Object.entries(EXPLOIT_CATEGORIES).map(([key, category]) => (
              <div key={key} className="p-4 border rounded-lg hover:bg-gray-50 transition-colors">
                <div className="flex items-center space-x-3 mb-2">
                  <category.icon className="w-5 h-5 text-gray-600" />
                  <h3 className="font-semibold">{category.name}</h3>
                </div>
                <p className="text-sm text-gray-600 mb-2">{category.description}</p>
                <Badge variant="outline">{category.count}+ modules</Badge>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      <Tabs defaultValue="configure" className="w-full">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="configure" className="flex items-center space-x-2">
            <Settings className="w-4 h-4" />
            <span>Configure</span>
          </TabsTrigger>
          <TabsTrigger value="exploits" className="flex items-center space-x-2">
            <Bomb className="w-4 h-4" />
            <span>Exploits</span>
          </TabsTrigger>
          <TabsTrigger value="output" className="flex items-center space-x-2">
            <Terminal className="w-4 h-4" />
            <span>Output</span>
          </TabsTrigger>
          <TabsTrigger value="results" className="flex items-center space-x-2">
            <Target className="w-4 h-4" />
            <span>Results</span>
          </TabsTrigger>
          <TabsTrigger value="commands" className="flex items-center space-x-2">
            <Code className="w-4 h-4" />
            <span>Commands</span>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="configure" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Target Configuration */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Target className="w-5 h-5" />
                  <span>Target Configuration</span>
                </CardTitle>
                <CardDescription>
                  Specify the target system for exploitation
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium">Target Host (RHOSTS)</label>
                  <Input
                    placeholder="*************"
                    value={config.target.host}
                    onChange={(e) => setConfig(prev => ({
                      ...prev,
                      target: { ...prev.target, host: e.target.value }
                    }))}
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Port (RPORT)</label>
                    <Input
                      type="number"
                      min="1"
                      max="65535"
                      value={config.target.port}
                      onChange={(e) => setConfig(prev => ({
                        ...prev,
                        target: { ...prev.target, port: parseInt(e.target.value) || 445 }
                      }))}
                    />
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Protocol</label>
                    <Select 
                      value={config.target.protocol} 
                      onValueChange={(value: any) => setConfig(prev => ({
                        ...prev,
                        target: { ...prev.target, protocol: value }
                      }))}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="tcp">TCP</SelectItem>
                        <SelectItem value="udp">UDP</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">Target Service</label>
                  <Select 
                    value={config.target.service} 
                    onValueChange={(value) => setConfig(prev => ({
                      ...prev,
                      target: { ...prev.target, service: value }
                    }))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="smb">SMB (445)</SelectItem>
                      <SelectItem value="http">HTTP (80)</SelectItem>
                      <SelectItem value="https">HTTPS (443)</SelectItem>
                      <SelectItem value="ssh">SSH (22)</SelectItem>
                      <SelectItem value="ftp">FTP (21)</SelectItem>
                      <SelectItem value="telnet">Telnet (23)</SelectItem>
                      <SelectItem value="smtp">SMTP (25)</SelectItem>
                      <SelectItem value="dns">DNS (53)</SelectItem>
                      <SelectItem value="pop3">POP3 (110)</SelectItem>
                      <SelectItem value="imap">IMAP (143)</SelectItem>
                      <SelectItem value="mysql">MySQL (3306)</SelectItem>
                      <SelectItem value="mssql">MS SQL (1433)</SelectItem>
                      <SelectItem value="rdp">RDP (3389)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">Hosts File (Alternative)</label>
                  <Input
                    placeholder="/path/to/hosts.txt"
                    value={config.target.hostsFile}
                    onChange={(e) => setConfig(prev => ({
                      ...prev,
                      target: { ...prev.target, hostsFile: e.target.value }
                    }))}
                  />
                  <p className="text-xs text-gray-500">File with target hosts, one per line</p>
                </div>
              </CardContent>
            </Card>

            {/* Exploit Configuration */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Bomb className="w-5 h-5" />
                  <span>Exploit Configuration</span>
                </CardTitle>
                <CardDescription>
                  Select and configure the exploit module
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium">Exploit Module</label>
                  <Input
                    placeholder="exploit/windows/smb/ms17_010_eternalblue"
                    value={config.exploit.module}
                    onChange={(e) => setConfig(prev => ({
                      ...prev,
                      exploit: { ...prev.exploit, module: e.target.value }
                    }))}
                  />
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">Exploit Rank</label>
                  <Select 
                    value={config.exploit.rank} 
                    onValueChange={(value) => setConfig(prev => ({
                      ...prev,
                      exploit: { ...prev.exploit, rank: value }
                    }))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {Object.entries(EXPLOIT_RANKS).map(([key, rank]) => (
                        <SelectItem key={key} value={key}>
                          <div className="flex items-center space-x-2">
                            <div className={cn(
                              "w-2 h-2 rounded-full",
                              rank.color === 'green' && "bg-green-500",
                              rank.color === 'blue' && "bg-blue-500",
                              rank.color === 'yellow' && "bg-yellow-500",
                              rank.color === 'orange' && "bg-orange-500",
                              rank.color === 'red' && "bg-red-500"
                            )} />
                            <span>{rank.name}</span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <p className="text-xs text-gray-500">
                    {EXPLOIT_RANKS[config.exploit.rank as keyof typeof EXPLOIT_RANKS]?.description}
                  </p>
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">Target Platform</label>
                  <div className="grid grid-cols-2 gap-2">
                    {['windows', 'linux', 'unix', 'osx', 'android', 'java'].map((platform) => (
                      <div key={platform} className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          id={`platform-${platform}`}
                          checked={config.exploit.platform.includes(platform)}
                          onChange={(e) => {
                            if (e.target.checked) {
                              setConfig(prev => ({
                                ...prev,
                                exploit: {
                                  ...prev.exploit,
                                  platform: [...prev.exploit.platform, platform]
                                }
                              }));
                            } else {
                              setConfig(prev => ({
                                ...prev,
                                exploit: {
                                  ...prev.exploit,
                                  platform: prev.exploit.platform.filter(p => p !== platform)
                                }
                              }));
                            }
                          }}
                          className="rounded"
                        />
                        <label htmlFor={`platform-${platform}`} className="text-sm capitalize">
                          {platform}
                        </label>
                      </div>
                    ))}
                  </div>
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">Exploit Target ID</label>
                  <Input
                    type="number"
                    min="0"
                    max="50"
                    value={config.exploit.selectedTarget}
                    onChange={(e) => setConfig(prev => ({
                      ...prev,
                      exploit: { ...prev.exploit, selectedTarget: parseInt(e.target.value) || 0 }
                    }))}
                  />
                  <p className="text-xs text-gray-500">Target ID (0 = automatic)</p>
                </div>
              </CardContent>
            </Card>

            {/* Payload Configuration */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Rocket className="w-5 h-5" />
                  <span>Payload Configuration</span>
                </CardTitle>
                <CardDescription>
                  Configure the payload for successful exploitation
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium">Payload Module</label>
                  <Input
                    placeholder="windows/meterpreter/reverse_tcp"
                    value={config.payload.module}
                    onChange={(e) => setConfig(prev => ({
                      ...prev,
                      payload: { ...prev.payload, module: e.target.value }
                    }))}
                  />
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">Payload Type</label>
                  <Select 
                    value={config.payload.type} 
                    onValueChange={(value: any) => setConfig(prev => ({
                      ...prev,
                      payload: { ...prev.payload, type: value }
                    }))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="reverse">Reverse Connection</SelectItem>
                      <SelectItem value="bind">Bind Connection</SelectItem>
                      <SelectItem value="staged">Staged Payload</SelectItem>
                      <SelectItem value="inline">Inline Payload</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">Encoder</label>
                  <Select 
                    value={config.payload.encoder} 
                    onValueChange={(value) => setConfig(prev => ({
                      ...prev,
                      payload: { ...prev.payload, encoder: value }
                    }))}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select encoder..." />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="">No Encoding</SelectItem>
                      <SelectItem value="x86/shikata_ga_nai">Shikata Ga Nai (x86)</SelectItem>
                      <SelectItem value="x86/fnstenv_mov">FnstEnv Mov (x86)</SelectItem>
                      <SelectItem value="x86/jmp_call_additive">Jump/Call Additive (x86)</SelectItem>
                      <SelectItem value="x64/xor">XOR Encoder (x64)</SelectItem>
                      <SelectItem value="x64/zutto_dekiru">Zutto Dekiru (x64)</SelectItem>
                      <SelectItem value="cmd/echo">Echo (CMD)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Encoding Iterations</label>
                    <Input
                      type="number"
                      min="1"
                      max="10"
                      value={config.payload.iterations}
                      onChange={(e) => setConfig(prev => ({
                        ...prev,
                        payload: { ...prev.payload, iterations: parseInt(e.target.value) || 1 }
                      }))}
                    />
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Output Format</label>
                    <Select 
                      value={config.payload.format} 
                      onValueChange={(value: any) => setConfig(prev => ({
                        ...prev,
                        payload: { ...prev.payload, format: value }
                      }))}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="exe">Windows Executable (EXE)</SelectItem>
                        <SelectItem value="dll">Windows DLL</SelectItem>
                        <SelectItem value="elf">Linux Executable (ELF)</SelectItem>
                        <SelectItem value="powershell">PowerShell</SelectItem>
                        <SelectItem value="python">Python</SelectItem>
                        <SelectItem value="raw">Raw Shellcode</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">Bad Characters</label>
                  <Input
                    placeholder="\\x00\\x0a\\x0d"
                    value={config.payload.badChars}
                    onChange={(e) => setConfig(prev => ({
                      ...prev,
                      payload: { ...prev.payload, badChars: e.target.value }
                    }))}
                  />
                  <p className="text-xs text-gray-500">Characters to avoid in payload encoding</p>
                </div>
              </CardContent>
            </Card>

            {/* Listener Configuration */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Network className="w-5 h-5" />
                  <span>Listener Configuration</span>
                </CardTitle>
                <CardDescription>
                  Configure the connection handler for reverse payloads
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium">Listen Host (LHOST)</label>
                  <Input
                    placeholder="************"
                    value={config.listener.lhost}
                    onChange={(e) => setConfig(prev => ({
                      ...prev,
                      listener: { ...prev.listener, lhost: e.target.value }
                    }))}
                  />
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">Listen Port (LPORT)</label>
                  <Input
                    type="number"
                    min="1"
                    max="65535"
                    value={config.listener.lport}
                    onChange={(e) => setConfig(prev => ({
                      ...prev,
                      listener: { ...prev.listener, lport: parseInt(e.target.value) || 4444 }
                    }))}
                  />
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">Bind Address</label>
                  <Input
                    placeholder="0.0.0.0"
                    value={config.listener.bindAddress}
                    onChange={(e) => setConfig(prev => ({
                      ...prev,
                      listener: { ...prev.listener, bindAddress: e.target.value }
                    }))}
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Stager Retry Count</label>
                    <Input
                      type="number"
                      min="0"
                      max="100"
                      value={config.listener.stagerRetryCount}
                      onChange={(e) => setConfig(prev => ({
                        ...prev,
                        listener: { ...prev.listener, stagerRetryCount: parseInt(e.target.value) || 10 }
                      }))}
                    />
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Stager Retry Wait (s)</label>
                    <Input
                      type="number"
                      min="0"
                      max="60"
                      value={config.listener.stagerRetryWait}
                      onChange={(e) => setConfig(prev => ({
                        ...prev,
                        listener: { ...prev.listener, stagerRetryWait: parseInt(e.target.value) || 5 }
                      }))}
                    />
                  </div>
                </div>

                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="ssl"
                    checked={config.listener.ssl}
                    onChange={(e) => setConfig(prev => ({
                      ...prev,
                      listener: { ...prev.listener, ssl: e.target.checked }
                    }))}
                    className="rounded"
                  />
                  <label htmlFor="ssl" className="text-sm font-medium">
                    Enable SSL/TLS encryption
                  </label>
                </div>
              </CardContent>
            </Card>

            {/* Post-Exploitation */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Pickaxe className="w-5 h-5" />
                  <span>Post-Exploitation</span>
                </CardTitle>
                <CardDescription>
                  Automated post-exploitation activities
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="auto-migrate"
                      checked={config.advanced.postExploitation.autoMigrate}
                      onChange={(e) => setConfig(prev => ({
                        ...prev,
                        advanced: {
                          ...prev.advanced,
                          postExploitation: {
                            ...prev.advanced.postExploitation,
                            autoMigrate: e.target.checked
                          }
                        }
                      }))}
                      className="rounded"
                    />
                    <label htmlFor="auto-migrate" className="text-sm font-medium">
                      Auto-migrate to stable process
                    </label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="getsystem"
                      checked={config.advanced.postExploitation.getsystem}
                      onChange={(e) => setConfig(prev => ({
                        ...prev,
                        advanced: {
                          ...prev.advanced,
                          postExploitation: {
                            ...prev.advanced.postExploitation,
                            getsystem: e.target.checked
                          }
                        }
                      }))}
                      className="rounded"
                    />
                    <label htmlFor="getsystem" className="text-sm font-medium">
                      Attempt privilege escalation (getsystem)
                    </label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="hashdump"
                      checked={config.advanced.postExploitation.hashdump}
                      onChange={(e) => setConfig(prev => ({
                        ...prev,
                        advanced: {
                          ...prev.advanced,
                          postExploitation: {
                            ...prev.advanced.postExploitation,
                            hashdump: e.target.checked
                          }
                        }
                      }))}
                      className="rounded"
                    />
                    <label htmlFor="hashdump" className="text-sm font-medium">
                      Dump password hashes
                    </label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="screenshot"
                      checked={config.advanced.postExploitation.screenshot}
                      onChange={(e) => setConfig(prev => ({
                        ...prev,
                        advanced: {
                          ...prev.advanced,
                          postExploitation: {
                            ...prev.advanced.postExploitation,
                            screenshot: e.target.checked
                          }
                        }
                      }))}
                      className="rounded"
                    />
                    <label htmlFor="screenshot" className="text-sm font-medium">
                      Take screenshot
                    </label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="persistence"
                      checked={config.advanced.postExploitation.persistence}
                      onChange={(e) => setConfig(prev => ({
                        ...prev,
                        advanced: {
                          ...prev.advanced,
                          postExploitation: {
                            ...prev.advanced.postExploitation,
                            persistence: e.target.checked
                          }
                        }
                      }))}
                      className="rounded"
                    />
                    <label htmlFor="persistence" className="text-sm font-medium">
                      Install persistence mechanism
                    </label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="keylogger"
                      checked={config.advanced.postExploitation.keylogger}
                      onChange={(e) => setConfig(prev => ({
                        ...prev,
                        advanced: {
                          ...prev.advanced,
                          postExploitation: {
                            ...prev.advanced.postExploitation,
                            keylogger: e.target.checked
                          }
                        }
                      }))}
                      className="rounded"
                    />
                    <label htmlFor="keylogger" className="text-sm font-medium">
                      Start keylogger
                    </label>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Verification Options */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <CheckCircle className="w-5 h-5" />
                  <span>Verification Options</span>
                </CardTitle>
                <CardDescription>
                  Pre-exploitation verification and safety checks
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="check-exploit"
                      checked={config.advanced.verification.checkExploit}
                      onChange={(e) => setConfig(prev => ({
                        ...prev,
                        advanced: {
                          ...prev.advanced,
                          verification: {
                            ...prev.advanced.verification,
                            checkExploit: e.target.checked
                          }
                        }
                      }))}
                      className="rounded"
                    />
                    <label htmlFor="check-exploit" className="text-sm font-medium">
                      Check if target is vulnerable before exploiting
                    </label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="validate-target"
                      checked={config.advanced.verification.validateTarget}
                      onChange={(e) => setConfig(prev => ({
                        ...prev,
                        advanced: {
                          ...prev.advanced,
                          verification: {
                            ...prev.advanced.verification,
                            validateTarget: e.target.checked
                          }
                        }
                      }))}
                      className="rounded"
                    />
                    <label htmlFor="validate-target" className="text-sm font-medium">
                      Validate target accessibility
                    </label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="skip-handler"
                      checked={config.advanced.verification.skipHandler}
                      onChange={(e) => setConfig(prev => ({
                        ...prev,
                        advanced: {
                          ...prev.advanced,
                          verification: {
                            ...prev.advanced.verification,
                            skipHandler: e.target.checked
                          }
                        }
                      }))}
                      className="rounded"
                    />
                    <label htmlFor="skip-handler" className="text-sm font-medium">
                      Skip handler (don't wait for incoming connections)
                    </label>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Execution Controls */}
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <Button
                    onClick={startExecution}
                    disabled={!isConnected || isRunning}
                    size="lg"
                    className="bg-red-600 hover:bg-red-700"
                  >
                    <Play className="w-4 h-4 mr-2" />
                    Execute Exploit
                  </Button>
                  
                  {isRunning && (
                    <Button
                      onClick={stopExecution}
                      variant="outline"
                      size="lg"
                    >
                      <Square className="w-4 h-4 mr-2" />
                      Stop
                    </Button>
                  )}
                </div>

                <div className="flex items-center space-x-2">
                  {isRunning && (
                    <div className="flex items-center space-x-2">
                      <Activity className="w-4 h-4 text-red-600 animate-pulse" />
                      <span className="text-sm text-red-600">Exploiting...</span>
                    </div>
                  )}
                  <Badge variant="outline">
                    {sessionInfo.sessions?.length || 0} active sessions
                  </Badge>
                </div>
              </div>

              {isRunning && (
                <div className="mt-4">
                  <div className="flex items-center justify-between text-sm text-gray-600 mb-2">
                    <span>Exploitation Progress</span>
                    <span>{progress.toFixed(1)}%</span>
                  </div>
                  <Progress value={progress} className="h-2" />
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="exploits" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Bomb className="w-5 h-5" />
                <span>Popular Exploits Library</span>
              </CardTitle>
              <CardDescription>
                Quick access to commonly used exploit modules
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {/* Category Selection */}
                <div className="flex space-x-2">
                  {Object.keys(POPULAR_EXPLOITS).map((category) => (
                    <Button
                      key={category}
                      variant={selectedCategory === category ? "default" : "outline"}
                      size="sm"
                      onClick={() => setSelectedCategory(category)}
                      className="capitalize"
                    >
                      {category}
                    </Button>
                  ))}
                </div>

                {/* Exploits List */}
                <div className="space-y-3">
                  {POPULAR_EXPLOITS[selectedCategory as keyof typeof POPULAR_EXPLOITS]?.map((exploit, index) => (
                    <div key={index} className="p-4 border rounded-lg hover:bg-gray-50 transition-colors">
                      <div className="flex items-center justify-between">
                        <div className="flex-1">
                          <div className="flex items-center space-x-2 mb-1">
                            <h4 className="font-semibold">{exploit.name}</h4>
                            {exploit.cve !== 'N/A' && (
                              <Badge variant="outline" className="text-xs">
                                {exploit.cve}
                              </Badge>
                            )}
                          </div>
                          <p className="text-sm text-gray-600 font-mono">{exploit.module}</p>
                        </div>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => applyExploitPreset(selectedCategory, exploit)}
                        >
                          Use Exploit
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>

                {/* Payload Quick Select */}
                <div className="border-t pt-6">
                  <h3 className="font-semibold mb-4">Common Payloads</h3>
                  <div className="space-y-4">
                    {Object.entries(PAYLOAD_TYPES).map(([platform, types]) => (
                      <div key={platform} className="space-y-2">
                        <h4 className="font-medium capitalize text-sm text-gray-700">{platform} Payloads</h4>
                        <div className="grid grid-cols-1 lg:grid-cols-2 gap-2">
                          {Object.entries(types).map(([type, payloads]) => (
                            <div key={type} className="space-y-1">
                              <p className="text-xs text-gray-600 capitalize font-medium">{type}</p>
                              {payloads.map((payload, idx) => (
                                <Button
                                  key={idx}
                                  variant="outline"
                                  size="sm"
                                  onClick={() => applyPayloadPreset(platform, type, payload)}
                                  className="w-full text-left justify-start h-auto p-2"
                                >
                                  <div>
                                    <div className="text-xs font-medium">{payload.name}</div>
                                    <div className="text-xs text-gray-500 font-mono">{payload.module}</div>
                                  </div>
                                </Button>
                              ))}
                            </div>
                          ))}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="output" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Terminal className="w-5 h-5" />
                <span>Real-time Console Output</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="bg-black rounded-lg p-4 h-96 overflow-y-auto font-mono text-sm">
                {output.length === 0 ? (
                  <div className="text-gray-500 italic">
                    <div className="text-red-400 mb-2">
                      ██╗   ██╗ █████╗ ██████╗ ███╗   ██╗██╗███╗   ██╗ ██████╗ 
                      ██║   ██║██╔══██╗██╔══██╗████╗  ██║██║████╗  ██║██╔════╝ 
                      ██║   ██║███████║██████╔╝██╔██╗ ██║██║██╔██╗ ██║██║  ███╗
                      ╚██╗ ██╔╝██╔══██║██╔══██╗██║╚██╗██║██║██║╚██╗██║██║   ██║
                       ╚████╔╝ ██║  ██║██║  ██║██║ ╚████║██║██║ ╚████║╚██████╔╝
                        ╚═══╝  ╚═╝  ╚═╝╚═╝  ╚═╝╚═╝  ╚═══╝╚═╝╚═╝  ╚═══╝ ╚═════╝ 
                    </div>
                    <p>Metasploit console ready. Execute an exploit to see output.</p>
                    <p className="text-xs mt-2">
                      msf6 > use exploit/...
                    </p>
                  </div>
                ) : (
                  output.map((line, index) => (
                    <div key={index} className={cn(
                      "text-green-400",
                      line.includes('[!]') && "text-red-400",
                      line.includes('[*]') && "text-blue-400",
                      line.includes('[+]') && "text-green-400",
                      line.includes('[-]') && "text-yellow-400",
                      line.includes('msf6 >') && "text-cyan-400 font-bold",
                      line.includes('meterpreter >') && "text-green-400 font-bold",
                      line.includes('Session') && "text-purple-400 font-bold"
                    )}>
                      {line}
                    </div>
                  ))
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="results" className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold">Exploitation Results</h3>
            {(results.length > 0 || sessionInfo.sessions?.length > 0) && (
              <Button onClick={exportResults} variant="outline">
                <Download className="w-4 h-4 mr-2" />
                Export Results
              </Button>
            )}
          </div>

          {sessionInfo.sessions?.length === 0 && results.length === 0 ? (
            <Card>
              <CardContent className="pt-6">
                <div className="text-center py-8">
                  <Swords className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500">No exploitation results yet</p>
                  <p className="text-sm text-gray-400">Execute an exploit to see sessions and results</p>
                </div>
              </CardContent>
            </Card>
          ) : (
            <div className="space-y-6">
              {/* Active Sessions */}
              {sessionInfo.sessions && sessionInfo.sessions.length > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2">
                      <Users className="w-5 h-5" />
                      <span>Active Sessions</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {sessionInfo.sessions.map((session: any, index: number) => (
                        <div key={index} className="p-4 border rounded-lg bg-green-50 border-green-200">
                          <div className="flex items-center justify-between">
                            <div className="space-y-1">
                              <div className="flex items-center space-x-2">
                                <Badge variant="default" className="bg-green-600">
                                  Session {session.id}
                                </Badge>
                                <Badge variant="outline">
                                  {session.type}
                                </Badge>
                                <Badge variant="outline">
                                  {session.platform}
                                </Badge>
                              </div>
                              <div className="text-sm text-gray-600">
                                <span className="font-medium">Target:</span> {session.target}
                              </div>
                              <div className="text-sm text-gray-600">
                                <span className="font-medium">User:</span> {session.username} | 
                                <span className="font-medium ml-2">Computer:</span> {session.computer}
                              </div>
                              <div className="text-sm text-gray-600">
                                <span className="font-medium">Established:</span> {session.timestamp}
                              </div>
                            </div>
                            <div className="flex space-x-2">
                              <Button variant="outline" size="sm">
                                <Terminal className="w-4 h-4 mr-1" />
                                Console
                              </Button>
                              <Button variant="outline" size="sm">
                                <Pickaxe className="w-4 h-4 mr-1" />
                                Post-Exploit
                              </Button>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Exploitation Results */}
              {sessionInfo.exploitResults && sessionInfo.exploitResults.length > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2">
                      <Target className="w-5 h-5" />
                      <span>Exploitation Attempts</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {sessionInfo.exploitResults.map((result: any, index: number) => (
                        <div key={index} className={cn(
                          "p-3 border rounded-lg",
                          result.success ? "bg-green-50 border-green-200" : "bg-red-50 border-red-200"
                        )}>
                          <div className="flex items-center justify-between">
                            <div className="space-y-1">
                              <div className="flex items-center space-x-2">
                                {result.success ? (
                                  <CheckCircle className="w-4 h-4 text-green-600" />
                                ) : (
                                  <XCircle className="w-4 h-4 text-red-600" />
                                )}
                                <span className="font-medium">{result.exploit}</span>
                                <Badge variant="outline">
                                  {result.target}
                                </Badge>
                              </div>
                              <div className="text-sm text-gray-600">
                                {result.message || (result.success ? "Exploitation successful" : "Exploitation failed")}
                              </div>
                              {result.payload && (
                                <div className="text-xs text-gray-500">
                                  Payload: {result.payload}
                                </div>
                              )}
                            </div>
                            <Badge variant={result.success ? "default" : "destructive"}>
                              {result.success ? "Success" : "Failed"}
                            </Badge>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Individual Results */}
              {results.length > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2">
                      <List className="w-5 h-5" />
                      <span>Detailed Results</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      {results.map((result, index) => (
                        <div key={index} className="flex items-center justify-between p-2 border rounded">
                          <div className="flex items-center space-x-2">
                            {result.type === 'success' && <CheckCircle className="w-4 h-4 text-green-600" />}
                            {result.type === 'warning' && <AlertTriangle className="w-4 h-4 text-yellow-600" />}
                            {result.type === 'error' && <XCircle className="w-4 h-4 text-red-600" />}
                            <span className="text-sm">{result.message}</span>
                          </div>
                          <div className="flex items-center space-x-2">
                            <span className="text-sm text-gray-600">{result.details}</span>
                            {result.sessionId && (
                              <Badge variant="outline" className="text-xs">
                                Session {result.sessionId}
                              </Badge>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}
            </div>
          )}
        </TabsContent>

        <TabsContent value="commands" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Code className="w-5 h-5" />
                <span>Generated Commands</span>
              </CardTitle>
              <CardDescription>
                Review and copy the Metasploit commands
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <h4 className="font-semibold mb-2">Metasploit Console Commands</h4>
                  <div className="bg-gray-900 rounded-lg p-4">
                    <code className="text-green-400 font-mono text-sm whitespace-pre-line">
                      {generateCommands().join('
')}
                    </code>
                  </div>
                </div>

                {config.payload.module && (
                  <div>
                    <h4 className="font-semibold mb-2">Standalone Payload Generation (msfvenom)</h4>
                    <div className="bg-gray-900 rounded-lg p-4">
                      <code className="text-green-400 font-mono text-sm whitespace-pre-wrap">
                        {generatePayload()}
                      </code>
                    </div>
                  </div>
                )}

                <div className="flex space-x-2">
                  <Button
                    variant="outline"
                    onClick={() => {
                      navigator.clipboard.writeText(generateCommands().join('
'));
                      toast.success('Commands copied to clipboard');
                    }}
                  >
                    <Copy className="w-4 h-4 mr-2" />
                    Copy Commands
                  </Button>
                  {config.payload.module && (
                    <Button
                      variant="outline"
                      onClick={() => {
                        navigator.clipboard.writeText(generatePayload());
                        toast.success('Payload command copied to clipboard');
                      }}
                    >
                      <Copy className="w-4 h-4 mr-2" />
                      Copy Payload
                    </Button>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Command Explanation */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Book className="w-5 h-5" />
                <span>Command Explanation</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3 text-sm">
                {config.exploit.module && (
                  <div>
                    <span className="font-semibold">use {config.exploit.module}</span>
                    <span className="text-gray-600 ml-2">- Load the specified exploit module</span>
                  </div>
                )}
                
                {config.target.host && (
                  <div>
                    <span className="font-semibold">set RHOSTS {config.target.host}</span>
                    <span className="text-gray-600 ml-2">- Set target host(s)</span>
                  </div>
                )}
                
                {config.payload.module && (
                  <div>
                    <span className="font-semibold">set PAYLOAD {config.payload.module}</span>
                    <span className="text-gray-600 ml-2">- Set the payload to execute after exploitation</span>
                  </div>
                )}
                
                {config.listener.lhost && (
                  <div>
                    <span className="font-semibold">set LHOST {config.listener.lhost}</span>
                    <span className="text-gray-600 ml-2">- Set listening host for reverse connection</span>
                  </div>
                )}
                
                <div>
                  <span className="font-semibold">exploit</span>
                  <span className="text-gray-600 ml-2">- Execute the configured exploit</span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Exploit Ranking Reference */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Shield className="w-5 h-5" />
                <span>Exploit Ranking System</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {Object.entries(EXPLOIT_RANKS).map(([key, rank]) => (
                  <div key={key} className="flex items-center justify-between p-2 border rounded">
                    <div className="flex items-center space-x-2">
                      <div className={cn(
                        "w-3 h-3 rounded-full",
                        rank.color === 'green' && "bg-green-500",
                        rank.color === 'blue' && "bg-blue-500",
                        rank.color === 'yellow' && "bg-yellow-500",
                        rank.color === 'orange' && "bg-orange-500",
                        rank.color === 'red' && "bg-red-500"
                      )} />
                      <span className="font-semibold">{rank.name}</span>
                    </div>
                    <span className="text-sm text-gray-600">{rank.description}</span>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}