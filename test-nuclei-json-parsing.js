/**
 * Test Nuclei JSON Parsing Fix
 * Verifies that the frontend can properly parse backend Nuclei responses
 */

// Simulate the backend response structure from nuclei_test.json
const mockBackendResponse = {
  success: true,
  data: {
    scan_id: "test-scan-123",
    tool: "nuclei",
    target: "http://scanme.nmap.org",
    tool_name: "nuclei",
    status: "completed",
    start_time: "2025-07-13T06:15:09.811146",
    end_time: "2025-07-13T06:15:10.867702",
    duration_seconds: 1.056556,
    raw_output: "flag provided but not defined: -json\n",
    parsed_results: {
      scan_info: {
        scanner: "nuclei",
        scan_type: "vulnerability_scan",
        timestamp: "2025-07-13T06:15:09.911544",
        template_count: 4000,
        status: "completed"
      },
      vulnerabilities: [],
      detailed_findings: [],
      statistics: {
        total_findings: 0,
        severity_counts: {
          critical: 0,
          high: 0,
          medium: 0,
          low: 0,
          info: 0
        }
      }
    }
  }
};

// Simulate the frontend parsing logic (from our fix)
function parseNucleiResults(progressUpdate) {
  console.log('🔍 Testing Nuclei JSON parsing fix...');
  
  let parsedResults = progressUpdate.results;
  
  // Handle nested backend response structure
  if (progressUpdate.results?.parsed_results) {
    const backendData = progressUpdate.results.parsed_results;
    
    // Extract severity counts from the nested structure
    const severityCounts = backendData.statistics?.severity_counts || {};
    
    // Transform to frontend-expected format
    parsedResults = {
      critical: severityCounts.critical || 0,
      high: severityCounts.high || 0,
      medium: severityCounts.medium || 0,
      low: severityCounts.low || 0,
      info: severityCounts.info || 0,
      vulnerabilities: backendData.vulnerabilities || [],
      detailed_findings: backendData.detailed_findings || [],
      raw_backend_data: progressUpdate.results // Keep original for debugging
    };
  }
  
  return parsedResults;
}

// Test the parsing
console.log('🚀 Testing Nuclei JSON Parsing Fix');
console.log('=====================================');

try {
  // Simulate the progress update that would come from the backend
  const progressUpdate = {
    id: 'test-scan-123',
    toolId: 'nuclei',
    status: 'completed',
    progress: 100,
    results: mockBackendResponse.data
  };
  
  console.log('\n📥 Input (Backend Response):');
  console.log('- Status:', progressUpdate.status);
  console.log('- Has parsed_results:', !!progressUpdate.results?.parsed_results);
  console.log('- Severity counts location:', progressUpdate.results?.parsed_results?.statistics?.severity_counts);
  
  // Apply our parsing fix
  const parsedResults = parseNucleiResults(progressUpdate);
  
  console.log('\n📤 Output (Frontend Format):');
  console.log('- Critical:', parsedResults.critical);
  console.log('- High:', parsedResults.high);
  console.log('- Medium:', parsedResults.medium);
  console.log('- Low:', parsedResults.low);
  console.log('- Info:', parsedResults.info);
  console.log('- Vulnerabilities count:', parsedResults.vulnerabilities.length);
  console.log('- Detailed findings count:', parsedResults.detailed_findings.length);
  
  // Verify the fix works
  const expectedFormat = {
    critical: 0,
    high: 0,
    medium: 0,
    low: 0,
    info: 0
  };
  
  let testPassed = true;
  for (const [severity, expectedCount] of Object.entries(expectedFormat)) {
    if (parsedResults[severity] !== expectedCount) {
      console.error(`❌ FAIL: ${severity} count mismatch. Expected: ${expectedCount}, Got: ${parsedResults[severity]}`);
      testPassed = false;
    }
  }
  
  if (testPassed) {
    console.log('\n✅ SUCCESS: Nuclei JSON parsing fix works correctly!');
    console.log('✅ Frontend can now properly parse backend Nuclei responses');
    console.log('✅ Severity counts are correctly extracted from nested structure');
  } else {
    console.log('\n❌ FAILURE: JSON parsing fix has issues');
  }
  
} catch (error) {
  console.error('❌ ERROR during testing:', error.message);
  console.error(error.stack);
}

console.log('\n🎯 Next Steps:');
console.log('1. Test with real backend by running a Nuclei scan');
console.log('2. Verify the debug panel shows correct structure');
console.log('3. Check that severity counts display properly in UI');
console.log('4. Ensure vulnerability table handles empty results gracefully');
