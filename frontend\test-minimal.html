<!DOCTYPE html>
<html>
<head>
    <title>Minimal Backend Test</title>
    <style>
        body { font-family: monospace; padding: 20px; background: #1a1a1a; color: #0f0; }
        button { padding: 10px 20px; margin: 10px; cursor: pointer; }
        pre { background: #000; padding: 10px; overflow: auto; }
        .success { color: #0f0; }
        .error { color: #f00; }
    </style>
</head>
<body>
    <h1>NexusScan Backend Test</h1>
    
    <button onclick="testHealth()">Test Health</button>
    <button onclick="testTools()">Test Tools</button>
    <button onclick="testNmap()">Test Nmap Scan</button>
    
    <pre id="output"></pre>
    
    <script>
        const backend = 'http://************:8090';
        const output = document.getElementById('output');
        
        function log(msg, isError = false) {
            const time = new Date().toLocaleTimeString();
            const className = isError ? 'error' : 'success';
            output.innerHTML += `<span class="${className}">[${time}] ${msg}</span>\n`;
            output.scrollTop = output.scrollHeight;
        }
        
        async function testHealth() {
            log('Testing backend health...');
            try {
                const response = await fetch(`${backend}/api/health`);
                const data = await response.json();
                log('Health: ' + JSON.stringify(data, null, 2));
            } catch (error) {
                log('Health check failed: ' + error.message, true);
            }
        }
        
        async function testTools() {
            log('Fetching available tools...');
            try {
                const response = await fetch(`${backend}/api/tools/available`);
                const data = await response.json();
                if (data.success && data.data) {
                    log(`Found ${data.data.length} tools:`);
                    data.data.slice(0, 5).forEach(tool => {
                        log(`  - ${tool.name} (${tool.id}): ${tool.status}`);
                    });
                } else {
                    log('Tools response: ' + JSON.stringify(data, null, 2));
                }
            } catch (error) {
                log('Tools fetch failed: ' + error.message, true);
            }
        }
        
        async function testNmap() {
            log('Running Nmap scan on scanme.nmap.org...');
            try {
                const response = await fetch(`${backend}/api/simple-tools/nmap/scan`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        target: 'scanme.nmap.org',
                        port_range: '80,443',
                        scan_type: 'tcp'
                    })
                });
                const data = await response.json();
                log('Nmap result: ' + JSON.stringify(data, null, 2));
            } catch (error) {
                log('Nmap scan failed: ' + error.message, true);
            }
        }
        
        // Auto-test on load
        window.onload = () => {
            log('Backend test page loaded');
            log('Backend URL: ' + backend);
        };
    </script>
</body>
</html>