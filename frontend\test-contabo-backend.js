/**
 * Test script to verify Contabo backend connectivity
 * Run this in browser console to test the new backend endpoint
 */

const CONTABO_BACKEND = 'http://************:8090';

console.log('🔗 Testing Contabo Backend Connectivity');
console.log('=====================================');

async function testBackendHealth() {
  console.log('\n1. 🏥 Testing Health Endpoint...');
  try {
    const response = await fetch(`${CONTABO_BACKEND}/api/health`);
    const data = await response.json();
    console.log('✅ Health Check:', data);
    return true;
  } catch (error) {
    console.error('❌ Health Check Failed:', error.message);
    return false;
  }
}

async function testToolsAvailable() {
  console.log('\n2. 🔧 Testing Tools Endpoint...');
  try {
    const response = await fetch(`${CONTABO_BACKEND}/api/tools/available`);
    const data = await response.json();
    console.log('✅ Tools Available:', data);
    
    if (data.success && data.data) {
      console.log(`📊 Total Tools: ${data.data.length}`);
      const availableTools = data.data.filter(tool => tool.status === 'available');
      console.log(`📊 Available Tools: ${availableTools.length}`);
      
      // Show key tools
      const keyTools = ['nmap', 'nuclei', 'sqlmap'];
      keyTools.forEach(toolName => {
        const tool = data.data.find(t => t.name.toLowerCase().includes(toolName));
        if (tool) {
          console.log(`   ${toolName.toUpperCase()}: ${tool.status} ✅`);
        } else {
          console.log(`   ${toolName.toUpperCase()}: NOT FOUND ❌`);
        }
      });
    }
    return true;
  } catch (error) {
    console.error('❌ Tools Check Failed:', error.message);
    return false;
  }
}

async function testNmapTool() {
  console.log('\n3. 🗺️ Testing Nmap Tool...');
  try {
    const response = await fetch(`${CONTABO_BACKEND}/api/tools/nmap/scan`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        target: '127.0.0.1',
        timeout: 30,
        threads: 1,
        output_format: 'json',
        options: {
          port_range: '80,443',
          scan_type: 'tcp'
        }
      })
    });
    
    const data = await response.json();
    console.log('✅ Nmap Test:', data);
    
    if (data.success) {
      console.log('🎉 Nmap tool is working!');
    } else {
      console.log('⚠️ Nmap tool issue:', data.error);
    }
    return data.success;
  } catch (error) {
    console.error('❌ Nmap Test Failed:', error.message);
    return false;
  }
}

async function testNucleiTool() {
  console.log('\n4. 🔍 Testing Nuclei Tool...');
  try {
    const response = await fetch(`${CONTABO_BACKEND}/api/tools/nuclei/scan`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        target: 'http://httpbin.org',
        timeout: 30,
        threads: 5,
        output_format: 'json',
        options: {
          templates: ['http'],
          severity: ['medium', 'high', 'critical']
        }
      })
    });
    
    const data = await response.json();
    console.log('✅ Nuclei Test:', data);
    
    if (data.success) {
      console.log('🎉 Nuclei tool is working!');
    } else {
      console.log('⚠️ Nuclei tool issue:', data.error);
    }
    return data.success;
  } catch (error) {
    console.error('❌ Nuclei Test Failed:', error.message);
    return false;
  }
}

async function runAllTests() {
  console.log('🚀 Starting Comprehensive Backend Tests...\n');
  
  const results = {
    health: await testBackendHealth(),
    tools: await testToolsAvailable(),
    nmap: await testNmapTool(),
    nuclei: await testNucleiTool()
  };
  
  console.log('\n📊 Test Results Summary:');
  console.log('========================');
  Object.entries(results).forEach(([test, passed]) => {
    console.log(`${test.toUpperCase()}: ${passed ? '✅ PASS' : '❌ FAIL'}`);
  });
  
  const allPassed = Object.values(results).every(r => r);
  console.log(`\n🎯 Overall Status: ${allPassed ? '✅ ALL TESTS PASSED' : '❌ SOME TESTS FAILED'}`);
  
  if (allPassed) {
    console.log('🎉 Contabo backend is fully operational!');
    console.log('✨ Ready to proceed with frontend integration');
  } else {
    console.log('🔧 Some issues detected - check individual test results above');
  }
  
  return results;
}

// Auto-run tests
runAllTests();

// Export for manual testing
window.testContaboBackend = {
  runAllTests,
  testBackendHealth,
  testToolsAvailable,
  testNmapTool,
  testNucleiTool,
  CONTABO_BACKEND
};

console.log('\n💡 Manual testing available via: window.testContaboBackend');
