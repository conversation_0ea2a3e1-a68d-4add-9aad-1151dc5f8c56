import React, { useState, useMemo } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Badge } from '../ui/badge';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Select } from '../ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../ui/tabs';
import { 
  AlertTriangle, 
  Shield, 
  Target, 
  Search, 
  Filter, 
  Download, 
  Eye,
  ChevronDown,
  ChevronRight,
  ExternalLink,
  Copy,
  Info,
  Clock,
  MapPin
} from 'lucide-react';
import { WorkflowExecution, WorkflowStepResult } from '../../services/assessment-workflows';

interface Vulnerability {
  id: string;
  title: string;
  description: string;
  severity: 'critical' | 'high' | 'medium' | 'low' | 'info';
  cvss?: number;
  cve?: string;
  category: string;
  host: string;
  port?: number;
  service?: string;
  path?: string;
  evidence: string;
  recommendation: string;
  references: string[];
  toolId: string;
  stepNumber: number;
  discoveredAt: Date;
  status: 'new' | 'confirmed' | 'false_positive' | 'fixed' | 'accepted_risk';
}

interface VulnerabilityResultsDisplayProps {
  execution: WorkflowExecution;
  onExportResults?: () => void;
  onViewDetails?: (vulnerability: Vulnerability) => void;
  className?: string;
}

export const VulnerabilityResultsDisplay: React.FC<VulnerabilityResultsDisplayProps> = ({
  execution,
  onExportResults,
  onViewDetails,
  className = ''
}) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [severityFilter, setSeverityFilter] = useState<string>('all');
  const [categoryFilter, setCategoryFilter] = useState<string>('all');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [expandedVulns, setExpandedVulns] = useState<Set<string>>(new Set());

  // Extract and normalize vulnerabilities from execution results
  const vulnerabilities = useMemo(() => {
    const vulns: Vulnerability[] = [];
    
    execution.results.forEach(step => {
      if (step.vulnerabilities && step.vulnerabilities.length > 0) {
        step.vulnerabilities.forEach((vuln: any, index: number) => {
          vulns.push({
            id: `${step.toolId}-${step.stepNumber}-${index}`,
            title: vuln.title || vuln.name || `${step.toolId} Finding ${index + 1}`,
            description: vuln.description || vuln.info || 'No description available',
            severity: normalizeSeverity(vuln.severity || vuln.risk || 'info'),
            cvss: vuln.cvss || vuln.score,
            cve: vuln.cve || vuln.cve_id,
            category: vuln.category || vuln.type || inferCategory(step.toolId),
            host: vuln.host || vuln.target || extraction.metadata.targets?.[0] || 'Unknown',
            port: vuln.port || vuln.service_port,
            service: vuln.service || vuln.protocol,
            path: vuln.path || vuln.url,
            evidence: vuln.evidence || vuln.payload || vuln.output || 'No evidence provided',
            recommendation: vuln.recommendation || vuln.solution || getDefaultRecommendation(vuln.severity),
            references: vuln.references || vuln.links || [],
            toolId: step.toolId,
            stepNumber: step.stepNumber,
            discoveredAt: step.completedAt || new Date(),
            status: 'new'
          });
        });
      }
    });
    
    return vulns;
  }, [execution.results]);

  // Filter vulnerabilities based on search and filters
  const filteredVulnerabilities = useMemo(() => {
    return vulnerabilities.filter(vuln => {
      const matchesSearch = searchQuery === '' || 
        vuln.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        vuln.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
        vuln.host.toLowerCase().includes(searchQuery.toLowerCase());
      
      const matchesSeverity = severityFilter === 'all' || vuln.severity === severityFilter;
      const matchesCategory = categoryFilter === 'all' || vuln.category === categoryFilter;
      const matchesStatus = statusFilter === 'all' || vuln.status === statusFilter;
      
      return matchesSearch && matchesSeverity && matchesCategory && matchesStatus;
    });
  }, [vulnerabilities, searchQuery, severityFilter, categoryFilter, statusFilter]);

  // Get summary statistics
  const severityCounts = useMemo(() => {
    const counts = { critical: 0, high: 0, medium: 0, low: 0, info: 0 };
    vulnerabilities.forEach(vuln => {
      counts[vuln.severity]++;
    });
    return counts;
  }, [vulnerabilities]);

  const categories = useMemo(() => {
    const cats = new Set(vulnerabilities.map(v => v.category));
    return Array.from(cats).sort();
  }, [vulnerabilities]);

  const toggleVulnExpansion = (vulnId: string) => {
    const newExpanded = new Set(expandedVulns);
    if (newExpanded.has(vulnId)) {
      newExpanded.delete(vulnId);
    } else {
      newExpanded.add(vulnId);
    }
    setExpandedVulns(newExpanded);
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical':
        return 'bg-red-600 text-white';
      case 'high':
        return 'bg-red-500 text-white';
      case 'medium':
        return 'bg-yellow-500 text-white';
      case 'low':
        return 'bg-blue-500 text-white';
      case 'info':
        return 'bg-gray-500 text-white';
      default:
        return 'bg-gray-500 text-white';
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Summary Cards */}
      <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
        <Card className="p-4">
          <div className="flex items-center space-x-2">
            <AlertTriangle className="h-4 w-4 text-red-600" />
            <div>
              <div className="text-2xl font-bold">{severityCounts.critical}</div>
              <div className="text-xs text-muted-foreground">Critical</div>
            </div>
          </div>
        </Card>
        <Card className="p-4">
          <div className="flex items-center space-x-2">
            <Shield className="h-4 w-4 text-red-500" />
            <div>
              <div className="text-2xl font-bold">{severityCounts.high}</div>
              <div className="text-xs text-muted-foreground">High</div>
            </div>
          </div>
        </Card>
        <Card className="p-4">
          <div className="flex items-center space-x-2">
            <Target className="h-4 w-4 text-yellow-500" />
            <div>
              <div className="text-2xl font-bold">{severityCounts.medium}</div>
              <div className="text-xs text-muted-foreground">Medium</div>
            </div>
          </div>
        </Card>
        <Card className="p-4">
          <div className="flex items-center space-x-2">
            <Info className="h-4 w-4 text-blue-500" />
            <div>
              <div className="text-2xl font-bold">{severityCounts.low}</div>
              <div className="text-xs text-muted-foreground">Low</div>
            </div>
          </div>
        </Card>
        <Card className="p-4">
          <div className="flex items-center space-x-2">
            <Clock className="h-4 w-4 text-gray-500" />
            <div>
              <div className="text-2xl font-bold">{vulnerabilities.length}</div>
              <div className="text-xs text-muted-foreground">Total</div>
            </div>
          </div>
        </Card>
      </div>

      {/* Filters and Search */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center justify-between">
            <span className="flex items-center space-x-2">
              <Filter className="h-4 w-4" />
              <span>Vulnerability Results</span>
              <Badge>{filteredVulnerabilities.length} findings</Badge>
            </span>
            {onExportResults && (
              <Button variant="outline" size="sm" onClick={onExportResults}>
                <Download className="h-4 w-4 mr-2" />
                Export
              </Button>
            )}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Search and Filters */}
          <div className="flex flex-wrap gap-4">
            <div className="flex-1 min-w-64">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search vulnerabilities..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <select 
              value={severityFilter} 
              onChange={(e) => setSeverityFilter(e.target.value)}
              className="px-3 py-2 border rounded-md bg-background"
            >
              <option value="all">All Severities</option>
              <option value="critical">Critical</option>
              <option value="high">High</option>
              <option value="medium">Medium</option>
              <option value="low">Low</option>
              <option value="info">Info</option>
            </select>
            <select 
              value={categoryFilter} 
              onChange={(e) => setCategoryFilter(e.target.value)}
              className="px-3 py-2 border rounded-md bg-background"
            >
              <option value="all">All Categories</option>
              {categories.map(cat => (
                <option key={cat} value={cat}>{cat}</option>
              ))}
            </select>
            <select 
              value={statusFilter} 
              onChange={(e) => setStatusFilter(e.target.value)}
              className="px-3 py-2 border rounded-md bg-background"
            >
              <option value="all">All Status</option>
              <option value="new">New</option>
              <option value="confirmed">Confirmed</option>
              <option value="false_positive">False Positive</option>
              <option value="fixed">Fixed</option>
              <option value="accepted_risk">Accepted Risk</option>
            </select>
          </div>

          {/* Vulnerability List */}
          <div className="space-y-3">
            {filteredVulnerabilities.map((vuln) => (
              <Card key={vuln.id} className="hover:shadow-md transition-shadow">
                <CardContent className="p-4">
                  <div className="flex items-start justify-between">
                    <div className="flex-1 space-y-2">
                      {/* Header */}
                      <div className="flex items-center space-x-3">
                        <button
                          onClick={() => toggleVulnExpansion(vuln.id)}
                          className="text-muted-foreground hover:text-foreground"
                        >
                          {expandedVulns.has(vuln.id) ? (
                            <ChevronDown className="h-4 w-4" />
                          ) : (
                            <ChevronRight className="h-4 w-4" />
                          )}
                        </button>
                        <Badge className={getSeverityColor(vuln.severity)}>
                          {vuln.severity.toUpperCase()}
                        </Badge>
                        <h3 className="font-semibold">{vuln.title}</h3>
                        {vuln.cve && (
                          <Badge variant="outline">{vuln.cve}</Badge>
                        )}
                      </div>

                      {/* Basic Info */}
                      <div className="flex items-center space-x-4 text-sm text-muted-foreground ml-7">
                        <span className="flex items-center space-x-1">
                          <MapPin className="h-3 w-3" />
                          <span>{vuln.host}{vuln.port ? `:${vuln.port}` : ''}</span>
                        </span>
                        <span>Category: {vuln.category}</span>
                        <span>Tool: {vuln.toolId}</span>
                        {vuln.cvss && <span>CVSS: {vuln.cvss}</span>}
                      </div>

                      {/* Expanded Details */}
                      {expandedVulns.has(vuln.id) && (
                        <div className="ml-7 space-y-3 pt-3 border-t">
                          <div>
                            <h4 className="font-medium mb-1">Description</h4>
                            <p className="text-sm text-muted-foreground">{vuln.description}</p>
                          </div>
                          
                          <div>
                            <h4 className="font-medium mb-1">Evidence</h4>
                            <div className="bg-muted p-3 rounded font-mono text-sm relative">
                              <pre className="whitespace-pre-wrap overflow-x-auto">{vuln.evidence}</pre>
                              <Button
                                variant="ghost"
                                size="sm"
                                className="absolute top-2 right-2"
                                onClick={() => copyToClipboard(vuln.evidence)}
                              >
                                <Copy className="h-3 w-3" />
                              </Button>
                            </div>
                          </div>
                          
                          <div>
                            <h4 className="font-medium mb-1">Recommendation</h4>
                            <p className="text-sm text-muted-foreground">{vuln.recommendation}</p>
                          </div>
                          
                          {vuln.references.length > 0 && (
                            <div>
                              <h4 className="font-medium mb-1">References</h4>
                              <div className="space-y-1">
                                {vuln.references.map((ref, idx) => (
                                  <a
                                    key={idx}
                                    href={ref}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    className="text-sm text-blue-500 hover:underline flex items-center space-x-1"
                                  >
                                    <ExternalLink className="h-3 w-3" />
                                    <span>{ref}</span>
                                  </a>
                                ))}
                              </div>
                            </div>
                          )}
                        </div>
                      )}
                    </div>

                    {/* Actions */}
                    <div className="flex items-center space-x-2">
                      {onViewDetails && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => onViewDetails(vuln)}
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {filteredVulnerabilities.length === 0 && (
            <div className="text-center py-8 text-muted-foreground">
              <Shield className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <h3 className="text-lg font-semibold mb-2">No vulnerabilities found</h3>
              <p>Either no vulnerabilities were discovered or they don't match your current filters.</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

// Helper functions
function normalizeSeverity(severity: string): 'critical' | 'high' | 'medium' | 'low' | 'info' {
  const s = severity.toLowerCase();
  if (s.includes('critical') || s.includes('9') || s.includes('10')) return 'critical';
  if (s.includes('high') || s.includes('7') || s.includes('8')) return 'high';
  if (s.includes('medium') || s.includes('4') || s.includes('5') || s.includes('6')) return 'medium';
  if (s.includes('low') || s.includes('1') || s.includes('2') || s.includes('3')) return 'low';
  return 'info';
}

function inferCategory(toolId: string): string {
  const categoryMap: Record<string, string> = {
    'nmap': 'Network Discovery',
    'nuclei': 'Vulnerability Scanning',
    'sqlmap': 'SQL Injection',
    'nikto': 'Web Application',
    'dirb': 'Directory Enumeration',
    'gobuster': 'Directory Enumeration',
    'wpscan': 'CMS Security',
    'testssl': 'SSL/TLS Security',
    'sslyze': 'SSL/TLS Security',
    'enum4linux-ng': 'SMB Enumeration',
    'smbclient': 'SMB Security',
    'whatweb': 'Web Technology',
    'ffuf': 'Web Fuzzing',
    'feroxbuster': 'Directory Enumeration'
  };
  
  return categoryMap[toolId] || 'General Security';
}

function getDefaultRecommendation(severity?: string): string {
  switch (severity?.toLowerCase()) {
    case 'critical':
      return 'Immediate action required. This vulnerability poses a severe security risk and should be patched or mitigated immediately.';
    case 'high':
      return 'High priority remediation required. This vulnerability should be addressed as soon as possible.';
    case 'medium':
      return 'Moderate priority remediation. This vulnerability should be addressed in the next maintenance cycle.';
    case 'low':
      return 'Low priority remediation. This vulnerability should be addressed when convenient.';
    default:
      return 'Review this finding and determine appropriate remediation steps based on your security requirements.';
  }
}