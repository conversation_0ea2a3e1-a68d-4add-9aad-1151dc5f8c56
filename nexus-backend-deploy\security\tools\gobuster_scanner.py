#!/usr/bin/env python3
"""
Gobuster Scanner for NexusScan Desktop
Multi-purpose security scanner for directory, subdomain, and DNS enumeration
"""

import asyncio
import json
import logging
import subprocess
from datetime import datetime
from typing import Optional, Callable, Dict, Any, List
from dataclasses import dataclass

from security.tools.base_scanner import BaseScanner, ScanResult, ScanOptions
from security.tools.tool_registry import register_tool, ToolCategory, ToolCapabilities

logger = logging.getLogger(__name__)

@dataclass
class GobusterScanOptions(ScanOptions):
    """Options for Gobuster scan"""
    mode: str = "dir"  # dir, vhost, dns
    wordlist: str = ""
    extensions: str = ""
    threads: int = 10
    timeout: int = 30
    user_agent: str = "gobuster"
    follow_redirects: bool = False
    quiet: bool = False

@register_tool
class GobusterScanner(BaseScanner):
    """Gobuster multi-purpose security scanner"""
    
    def __init__(self):
        super().__init__()
        self.tool_name = "gobuster"
        self.description = "Multi-purpose security scanner for directory, subdomain, and DNS enumeration"
        self.category = ToolCategory.WEB_SCANNER
        self.capabilities = ToolCapabilities(
            supports_async=True,
            supports_cancellation=True,
            supported_targets=["url", "domain", "ip"]
        )
        self.scan_types = ["dir", "vhost", "dns"]
        
    def get_frontend_interface_data(self) -> Dict[str, Any]:
        """Get frontend interface data"""
        return {
            "tool_info": {
                "name": "Gobuster",
                "description": self.description,
                "version": "3.6.0",
                "author": "OJ Reeves",
                "category": self.category.value,
                "tags": ["directory", "enumeration", "brute-force", "web"],
                "website": "https://github.com/OJ/gobuster"
            },
            "scan_options": {
                "mode": {
                    "type": "select",
                    "label": "Scan Mode",
                    "options": ["dir", "vhost", "dns"],
                    "default": "dir",
                    "required": True,
                    "description": "Scanning mode"
                },
                "wordlist": {
                    "type": "file",
                    "label": "Wordlist",
                    "required": False,
                    "description": "Path to wordlist file"
                },
                "extensions": {
                    "type": "text",
                    "label": "Extensions",
                    "placeholder": "php,html,txt",
                    "required": False,
                    "description": "File extensions to search for"
                },
                "threads": {
                    "type": "number",
                    "label": "Threads",
                    "min": 1,
                    "max": 100,
                    "default": 10,
                    "required": False,
                    "description": "Number of concurrent threads"
                },
                "timeout": {
                    "type": "number",
                    "label": "Timeout",
                    "min": 1,
                    "max": 300,
                    "default": 30,
                    "required": False,
                    "description": "Request timeout in seconds"
                },
                "user_agent": {
                    "type": "text",
                    "label": "User Agent",
                    "default": "gobuster",
                    "required": False,
                    "description": "Custom user agent string"
                },
                "follow_redirects": {
                    "type": "checkbox",
                    "label": "Follow Redirects",
                    "default": False,
                    "required": False,
                    "description": "Follow HTTP redirects"
                },
                "quiet": {
                    "type": "checkbox",
                    "label": "Quiet Mode",
                    "default": False,
                    "required": False,
                    "description": "Suppress verbose output"
                }
            },
            "scan_profiles": self.get_scan_profiles(),
            "output_formats": ["json", "txt"],
            "capabilities": [
                "directory_enumeration",
                "subdomain_discovery", 
                "dns_brute_force",
                "vhost_discovery",
                "multi_threaded"
            ]
        }
    
    def get_scan_profiles(self) -> List[Dict[str, Any]]:
        """Get predefined scan profiles"""
        return [
            {
                "name": "Directory Enumeration",
                "description": "Standard directory brute-force",
                "mode": "dir",
                "extensions": "php,html,txt,js",
                "threads": 10
            },
            {
                "name": "Subdomain Discovery",
                "description": "Subdomain enumeration",
                "mode": "dns",
                "threads": 50
            },
            {
                "name": "VHost Discovery",
                "description": "Virtual host enumeration",
                "mode": "vhost",
                "threads": 20
            }
        ]
    
    async def scan(self, target: str, options: Optional[GobusterScanOptions] = None,
                   progress_callback: Optional[Callable[[float, str], None]] = None) -> ScanResult:
        """Perform Gobuster scan"""
        if options is None:
            options = GobusterScanOptions(target=target)
        
        scan_id = f"gobuster_{options.mode}_{target.replace('://', '_').replace('/', '_')}_{int(datetime.now().timestamp())}"
        start_time = datetime.now()
        
        logger.info(f"Starting Gobuster scan: {scan_id}")
        
        if progress_callback:
            progress_callback(0.1, "Initializing Gobuster scan...")
        
        try:
            # Build command
            cmd = self._build_command(target, options)
            
            if progress_callback:
                progress_callback(0.2, "Executing Gobuster command...")
            
            # Execute command
            result = await self._execute_command(cmd, progress_callback)
            
            if progress_callback:
                progress_callback(0.9, "Processing results...")
            
            # Parse results
            parsed_results = self._parse_results(result.stdout, options.mode)
            
            if progress_callback:
                progress_callback(1.0, "Scan completed")
            
            return ScanResult(
                scan_id=scan_id,
                tool_name=self.tool_name,
                target=target,
                start_time=start_time.isoformat(),
                end_time=datetime.now().isoformat(),
                status="completed",
                results=parsed_results,
                raw_output=result.stdout,
                metadata={
                    "mode": options.mode,
                    "threads": options.threads,
                    "timeout": options.timeout,
                    "command": " ".join(cmd)
                }
            )
            
        except Exception as e:
            logger.error(f"Gobuster scan failed: {e}")
            return ScanResult(
                scan_id=scan_id,
                tool_name=self.tool_name,
                target=target,
                start_time=start_time.isoformat(),
                end_time=datetime.now().isoformat(),
                status="failed",
                errors=[str(e)]
            )
    
    def check_native_availability(self) -> bool:
        """Check if native tool is available"""
        return self.is_available()
    
    async def execute_native_scan(self, options: ScanOptions,
                                 progress_callback: Optional[Callable[[float, str], None]] = None) -> ScanResult:
        """Execute native scan"""
        if not isinstance(options, GobusterScanOptions):
            options = GobusterScanOptions(target=options.target)
        
        return await self.scan(options.target, options, progress_callback)
    
    def _build_command(self, target: str, options: GobusterScanOptions) -> List[str]:
        """Build Gobuster command"""
        cmd = ["gobuster", options.mode]
        
        if options.mode == "dir":
            cmd.extend(["-u", target])
        elif options.mode == "dns":
            cmd.extend(["-d", target])
        elif options.mode == "vhost":
            cmd.extend(["-u", target])
        
        # Add wordlist
        if options.wordlist:
            cmd.extend(["-w", options.wordlist])
        else:
            # Default wordlists
            default_wordlists = {
                "dir": "/usr/share/wordlists/dirb/common.txt",
                "dns": "/usr/share/wordlists/dnsrecon/subdomains-top1mil.txt",
                "vhost": "/usr/share/wordlists/dirb/common.txt"
            }
            cmd.extend(["-w", default_wordlists.get(options.mode, "/usr/share/wordlists/dirb/common.txt")])
        
        # Add extensions
        if options.extensions:
            cmd.extend(["-x", options.extensions])
        
        # Add threads
        cmd.extend(["-t", str(options.threads)])
        
        # Add timeout
        cmd.extend(["--timeout", f"{options.timeout}s"])
        
        # Add user agent
        if options.user_agent:
            cmd.extend(["-a", options.user_agent])
        
        # Add follow redirects
        if options.follow_redirects:
            cmd.append("-r")
        
        # Add quiet mode
        if options.quiet:
            cmd.append("-q")
        
        return cmd
    
    async def _execute_command(self, cmd: List[str], progress_callback: Optional[Callable] = None) -> subprocess.CompletedProcess:
        """Execute Gobuster command"""
        logger.info(f"Executing command: {' '.join(cmd)}")
        
        try:
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await process.communicate()
            
            return subprocess.CompletedProcess(
                args=cmd,
                returncode=process.returncode,
                stdout=stdout.decode(),
                stderr=stderr.decode()
            )
            
        except Exception as e:
            logger.error(f"Command execution failed: {e}")
            raise
    
    def _parse_results(self, output: str, mode: str) -> List[Dict[str, Any]]:
        """Parse Gobuster output"""
        results = []
        
        for line in output.split('\n'):
            line = line.strip()
            if not line or line.startswith('=') or line.startswith('Gobuster'):
                continue
            
            if mode == "dir":
                # Parse directory enumeration results
                if " (Status: " in line:
                    parts = line.split(" (Status: ")
                    if len(parts) >= 2:
                        path = parts[0].strip()
                        status_part = parts[1].split(")")[0]
                        status_code = status_part.split("[")[0].strip()
                        
                        result = {
                            "type": "directory",
                            "path": path,
                            "status_code": status_code,
                            "url": path if path.startswith('http') else f"/{path.lstrip('/')}"
                        }
                        
                        # Extract size if available
                        if "[Size: " in line:
                            size_match = line.split("[Size: ")[1].split("]")[0]
                            result["size"] = size_match
                        
                        results.append(result)
            
            elif mode == "dns":
                # Parse DNS enumeration results
                if "Found: " in line:
                    subdomain = line.split("Found: ")[1].strip()
                    results.append({
                        "type": "subdomain",
                        "subdomain": subdomain,
                        "full_domain": subdomain
                    })
            
            elif mode == "vhost":
                # Parse virtual host results
                if "Found: " in line:
                    vhost = line.split("Found: ")[1].strip()
                    results.append({
                        "type": "vhost",
                        "vhost": vhost,
                        "host": vhost
                    })
        
        return results
    
    def is_available(self) -> bool:
        """Check if Gobuster is available"""
        try:
            result = subprocess.run(
                ["gobuster", "--help"],
                capture_output=True,
                text=True,
                timeout=5
            )
            return result.returncode == 0
        except Exception:
            return False