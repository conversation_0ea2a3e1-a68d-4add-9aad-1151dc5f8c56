/**
 * AI-Powered Proxy Manager Component
 * Intelligent proxy rotation and traffic analysis for detection avoidance
 */
import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { 
  Network,
  Play,
  Square,
  Download,
  Settings,
  Target,
  Globe,
  FileText,
  Terminal,
  Copy,
  RotateCcw,
  Clock,
  Zap,
  Filter,
  Eye,
  AlertTriangle,
  CheckCircle,
  TrendingUp,
  Activity,
  Lock,
  Search,
  List,
  BarChart3,
  Database,
  Server,
  Bug,
  Info,
  Code,
  Package,
  Layers,
  Shield,
  Crosshair,
  Cpu,
  Gauge,
  Tag,
  Hash,
  Workflow,
  GitBranch,
  Book,
  Users,
  Table,
  Binary,
  Braces,
  FileKey,
  History,
  Folder,
  Star,
  Crown,
  Award,
  Timer,
  FastForward,
  SkipForward,
  RefreshCw,
  ExternalLink,
  FileCheck,
  FileWarning,
  Fingerprint,
  Key as KeyIcon,
  LockOpen,
  ShieldOff,
  Verified,
  ScanLine,
  Scan,
  FileJson,
  FileCode,
  Brain,
  Lightbulb,
  Router,
  Wifi,
  MapPin,
  Signal,
  Shuffle,
  RotateCw,
  Repeat,
  Pause,
  StopCircle,
  PlayCircle,
  Navigation,
  Compass,
  Satellite,
  Radio,
  Antenna,
  CloudRain,
  CloudSnow,
  CloudSun,
  Sun,
  Moon,
  Sunrise,
  Sunset,
  Wind,
  Thermometer,
  Droplets,
  Umbrella,
  Snowflake,
  Zap as Lightning,
  Flame,
  Waves,
  Mountain,
  TreePine,
  Map,
  Flag,
  Plus,
  Minus,
  Edit,
  Trash2,
  Save,
  Upload,
  FileUp,
  FolderOpen,
  Maximize2,
  Minimize2,
  MoreHorizontal,
  Move,
  LineChart,
  AreaChart,
  PieChart,
  BarChart4,
  TrendingDown
} from 'lucide-react';

interface ProxyServer {
  id: string;
  ip: string;
  port: number;
  type: 'http' | 'https' | 'socks4' | 'socks5';
  country: string;
  city: string;
  provider: string;
  anonymity: 'transparent' | 'anonymous' | 'elite';
  speed: number; // ms latency
  reliability: number; // percentage uptime
  lastChecked: string;
  status: 'active' | 'inactive' | 'testing' | 'failed';
  bandwidth: number; // MB/s
  concurrent_connections: number;
  detection_rate: number; // AI-calculated detection likelihood
  trust_score: number; // AI-calculated trust score
}

interface ProxyChain {
  id: string;
  name: string;
  description: string;
  proxies: string[]; // proxy IDs in chain order
  total_latency: number;
  anonymity_level: number;
  detection_probability: number;
  success_rate: number;
  created_at: string;
  last_used: string;
}

interface TrafficPattern {
  id: string;
  name: string;
  description: string;
  pattern_type: 'human_like' | 'burst' | 'stealth' | 'aggressive' | 'custom';
  request_interval: { min: number; max: number };
  user_agents: string[];
  headers: Record<string, string>;
  timing_jitter: number;
  success_probability: number;
}

interface GeoLocation {
  country: string;
  country_code: string;
  city: string;
  latitude: number;
  longitude: number;
  region: string;
  timezone: string;
}

interface ProxyPerformanceMetrics {
  proxy_id: string;
  response_time: number;
  success_rate: number;
  detection_events: number;
  data_transferred: number;
  uptime_percentage: number;
  error_rate: number;
  geographic_diversity: number;
  anonymity_score: number;
}

// Sample proxy data
const SAMPLE_PROXIES: ProxyServer[] = [
  {
    id: '1',
    ip: '*************',
    port: 8080,
    type: 'http',
    country: 'United States',
    city: 'New York',
    provider: 'ProxyProvider A',
    anonymity: 'elite',
    speed: 45,
    reliability: 98.5,
    lastChecked: '2025-07-11T10:30:00Z',
    status: 'active',
    bandwidth: 15.2,
    concurrent_connections: 3,
    detection_rate: 12,
    trust_score: 92
  },
  {
    id: '2',
    ip: '*********',
    port: 3128,
    type: 'https',
    country: 'Germany',
    city: 'Berlin',
    provider: 'ProxyProvider B',
    anonymity: 'anonymous',
    speed: 67,
    reliability: 94.2,
    lastChecked: '2025-07-11T10:25:00Z',
    status: 'active',
    bandwidth: 22.8,
    concurrent_connections: 1,
    detection_rate: 8,
    trust_score: 87
  },
  {
    id: '3',
    ip: '***********',
    port: 1080,
    type: 'socks5',
    country: 'Japan',
    city: 'Tokyo',
    provider: 'ProxyProvider C',
    anonymity: 'elite',
    speed: 89,
    reliability: 91.7,
    lastChecked: '2025-07-11T10:20:00Z',
    status: 'testing',
    bandwidth: 18.5,
    concurrent_connections: 0,
    detection_rate: 15,
    trust_score: 85
  }
];

// Traffic patterns for detection avoidance
const TRAFFIC_PATTERNS: TrafficPattern[] = [
  {
    id: 'human_like',
    name: 'Human-like Browsing',
    description: 'Mimics natural human browsing patterns with realistic timing',
    pattern_type: 'human_like',
    request_interval: { min: 2000, max: 8000 },
    user_agents: [
      'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
      'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
      'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36'
    ],
    headers: {
      'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
      'Accept-Language': 'en-US,en;q=0.5',
      'Accept-Encoding': 'gzip, deflate',
      'DNT': '1',
      'Connection': 'keep-alive',
      'Upgrade-Insecure-Requests': '1'
    },
    timing_jitter: 0.3,
    success_probability: 85
  },
  {
    id: 'stealth',
    name: 'Stealth Mode',
    description: 'Minimal footprint with maximum anonymity',
    pattern_type: 'stealth',
    request_interval: { min: 5000, max: 15000 },
    user_agents: [
      'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:91.0) Gecko/20100101 Firefox/91.0'
    ],
    headers: {
      'Accept': '*/*',
      'Connection': 'close'
    },
    timing_jitter: 0.5,
    success_probability: 92
  },
  {
    id: 'aggressive',
    name: 'Aggressive Scanning',
    description: 'Fast scanning with higher detection risk',
    pattern_type: 'aggressive',
    request_interval: { min: 100, max: 500 },
    user_agents: [
      'curl/7.68.0',
      'python-requests/2.25.1'
    ],
    headers: {
      'Accept': '*/*',
      'User-Agent': 'Security Scanner 1.0'
    },
    timing_jitter: 0.1,
    success_probability: 65
  }
];

export const AIProxyManager: React.FC = () => {
  const [activeTab, setActiveTab] = useState('dashboard');
  const [proxies, setProxies] = useState<ProxyServer[]>(SAMPLE_PROXIES);
  const [proxyChains, setProxyChains] = useState<ProxyChain[]>([]);
  const [selectedPattern, setSelectedPattern] = useState<string>('human_like');
  const [isRotating, setIsRotating] = useState(false);
  const [rotationInterval, setRotationInterval] = useState(30);
  
  // Configuration state
  const [proxyConfig, setProxyConfig] = useState({
    auto_rotation: true,
    rotation_trigger: 'time_based', // 'time_based', 'request_count', 'detection_risk'
    max_requests_per_proxy: 100,
    geographic_distribution: true,
    anonymity_preference: 'elite',
    speed_threshold: 1000, // ms
    reliability_threshold: 90, // percentage
    ai_optimization: true
  });

  // Performance metrics
  const [performanceMetrics, setPerformanceMetrics] = useState<ProxyPerformanceMetrics[]>([]);
  const [trafficAnalysis, setTrafficAnalysis] = useState<any>({});

  useEffect(() => {
    if (isRotating) {
      const interval = setInterval(rotateProxies, rotationInterval * 1000);
      return () => clearInterval(interval);
    }
  }, [isRotating, rotationInterval]);

  useEffect(() => {
    updatePerformanceMetrics();
  }, [proxies]);

  const updatePerformanceMetrics = () => {
    const metrics = proxies.map(proxy => ({
      proxy_id: proxy.id,
      response_time: proxy.speed,
      success_rate: proxy.reliability,
      detection_events: Math.floor(proxy.detection_rate / 10),
      data_transferred: proxy.bandwidth * proxy.concurrent_connections,
      uptime_percentage: proxy.reliability,
      error_rate: 100 - proxy.reliability,
      geographic_diversity: 75, // Mock value
      anonymity_score: proxy.trust_score
    }));
    setPerformanceMetrics(metrics);
  };

  const testProxy = async (proxyId: string) => {
    const proxy = proxies.find(p => p.id === proxyId);
    if (!proxy) return;

    // Update proxy status to testing
    setProxies(prev => prev.map(p => 
      p.id === proxyId ? { ...p, status: 'testing' } : p
    ));

    try {
      const response = await fetch('http://ec2-3-89-91-209.compute-1.amazonaws.com:8000/api/proxy/test', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          proxy: {
            ip: proxy.ip,
            port: proxy.port,
            type: proxy.type
          },
          test_url: 'https://httpbin.org/ip',
          timeout: 10000
        })
      });

      if (response.ok) {
        const testResult = await response.json();
        
        // Update proxy with test results
        setProxies(prev => prev.map(p => 
          p.id === proxyId ? {
            ...p,
            status: testResult.success ? 'active' : 'failed',
            speed: testResult.response_time || p.speed,
            lastChecked: new Date().toISOString(),
            detection_rate: testResult.detection_score || p.detection_rate
          } : p
        ));
      }
    } catch (error) {
      console.error('Proxy test failed:', error);
      setProxies(prev => prev.map(p => 
        p.id === proxyId ? { ...p, status: 'failed' } : p
      ));
    }
  };

  const rotateProxies = () => {
    // AI-powered proxy rotation logic
    const activeProxies = proxies.filter(p => p.status === 'active');
    if (activeProxies.length === 0) return;

    // Select best proxy based on AI criteria
    const bestProxy = activeProxies.reduce((best, current) => {
      const bestScore = (best.trust_score * 0.4) + 
                       ((100 - best.detection_rate) * 0.3) + 
                       (best.reliability * 0.3);
      const currentScore = (current.trust_score * 0.4) + 
                          ((100 - current.detection_rate) * 0.3) + 
                          (current.reliability * 0.3);
      return currentScore > bestScore ? current : best;
    });

    console.log('Rotated to proxy:', bestProxy.ip);
  };

  const startAIOptimization = async () => {
    try {
      const response = await fetch('http://ec2-3-89-91-209.compute-1.amazonaws.com:8000/api/proxy/optimize', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          proxies: proxies,
          config: proxyConfig,
          traffic_pattern: selectedPattern,
          optimization_goals: ['anonymity', 'speed', 'reliability']
        })
      });

      if (response.ok) {
        const optimizationResult = await response.json();
        
        // Update proxies with AI optimization
        setProxies(optimizationResult.optimized_proxies || proxies);
        setProxyChains(optimizationResult.recommended_chains || []);
      }
    } catch (error) {
      console.error('AI optimization failed:', error);
    }
  };

  const addProxy = () => {
    const newProxy: ProxyServer = {
      id: Date.now().toString(),
      ip: '',
      port: 8080,
      type: 'http',
      country: '',
      city: '',
      provider: '',
      anonymity: 'anonymous',
      speed: 0,
      reliability: 0,
      lastChecked: new Date().toISOString(),
      status: 'inactive',
      bandwidth: 0,
      concurrent_connections: 0,
      detection_rate: 0,
      trust_score: 0
    };
    setProxies([...proxies, newProxy]);
  };

  const removeProxy = (proxyId: string) => {
    setProxies(proxies.filter(p => p.id !== proxyId));
  };

  const exportConfiguration = () => {
    const config = {
      proxies: proxies,
      chains: proxyChains,
      configuration: proxyConfig,
      traffic_patterns: TRAFFIC_PATTERNS,
      performance_metrics: performanceMetrics,
      exported_at: new Date().toISOString()
    };

    const dataStr = JSON.stringify(config, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `proxy_configuration_${new Date().toISOString().split('T')[0]}.json`;
    link.click();
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'text-green-600 bg-green-50 border-green-200';
      case 'inactive': return 'text-gray-600 bg-gray-50 border-gray-200';
      case 'testing': return 'text-blue-600 bg-blue-50 border-blue-200';
      case 'failed': return 'text-red-600 bg-red-50 border-red-200';
      default: return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const getAnonymityColor = (anonymity: string) => {
    switch (anonymity) {
      case 'elite': return 'text-green-600 bg-green-50 border-green-200';
      case 'anonymous': return 'text-blue-600 bg-blue-50 border-blue-200';
      case 'transparent': return 'text-orange-600 bg-orange-50 border-orange-200';
      default: return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const getSpeedIndicator = (speed: number) => {
    if (speed < 100) return { color: 'text-green-600', label: 'Fast' };
    if (speed < 300) return { color: 'text-yellow-600', label: 'Medium' };
    return { color: 'text-red-600', label: 'Slow' };
  };

  return (
    <div className="space-y-6">
      {/* Header Actions */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button 
            onClick={startAIOptimization}
            className="bg-purple-600 hover:bg-purple-700"
          >
            <Brain className="h-4 w-4 mr-2" />
            AI Optimize
          </Button>
          <Button 
            onClick={() => setIsRotating(!isRotating)}
            variant={isRotating ? 'destructive' : 'default'}
          >
            {isRotating ? <Square className="h-4 w-4 mr-2" /> : <RotateCw className="h-4 w-4 mr-2" />}
            {isRotating ? 'Stop Rotation' : 'Start Rotation'}
          </Button>
          <Button onClick={addProxy} variant="outline">
            <Plus className="h-4 w-4 mr-2" />
            Add Proxy
          </Button>
        </div>
        
        <div className="flex items-center space-x-3">
          {isRotating && (
            <Badge variant="default" className="bg-green-600">
              <Activity className="h-4 w-4 mr-1" />
              Auto-rotating every {rotationInterval}s
            </Badge>
          )}
          <Button onClick={exportConfiguration} variant="outline">
            <Download className="h-4 w-4 mr-2" />
            Export Config
          </Button>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-6">
          <TabsTrigger value="dashboard">Dashboard</TabsTrigger>
          <TabsTrigger value="proxies">Proxy Pool</TabsTrigger>
          <TabsTrigger value="chains">Proxy Chains</TabsTrigger>
          <TabsTrigger value="patterns">Traffic Patterns</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
          <TabsTrigger value="settings">Settings</TabsTrigger>
        </TabsList>

        {/* Dashboard Tab */}
        <TabsContent value="dashboard" className="space-y-6">
          {/* Overview Stats */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card className="p-4">
              <div className="flex items-center space-x-3">
                <Server className="h-8 w-8 text-blue-600" />
                <div>
                  <p className="text-sm text-gray-600">Total Proxies</p>
                  <p className="text-2xl font-bold">{proxies.length}</p>
                </div>
              </div>
            </Card>
            <Card className="p-4">
              <div className="flex items-center space-x-3">
                <CheckCircle className="h-8 w-8 text-green-600" />
                <div>
                  <p className="text-sm text-gray-600">Active Proxies</p>
                  <p className="text-2xl font-bold">{proxies.filter(p => p.status === 'active').length}</p>
                </div>
              </div>
            </Card>
            <Card className="p-4">
              <div className="flex items-center space-x-3">
                <Gauge className="h-8 w-8 text-purple-600" />
                <div>
                  <p className="text-sm text-gray-600">Avg Speed</p>
                  <p className="text-2xl font-bold">
                    {Math.round(proxies.reduce((sum, p) => sum + p.speed, 0) / proxies.length)}ms
                  </p>
                </div>
              </div>
            </Card>
            <Card className="p-4">
              <div className="flex items-center space-x-3">
                <Shield className="h-8 w-8 text-orange-600" />
                <div>
                  <p className="text-sm text-gray-600">Avg Anonymity</p>
                  <p className="text-2xl font-bold">
                    {Math.round(proxies.reduce((sum, p) => sum + p.trust_score, 0) / proxies.length)}%
                  </p>
                </div>
              </div>
            </Card>
          </div>

          {/* Geographic Distribution */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Globe className="h-5 w-5" />
                <span>Geographic Distribution</span>
              </CardTitle>
              <CardDescription>Proxy distribution across different countries</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {Array.from(new Set(proxies.map(p => p.country))).map((country) => {
                  const countryProxies = proxies.filter(p => p.country === country);
                  const activeCount = countryProxies.filter(p => p.status === 'active').length;
                  
                  return (
                    <Card key={country} className="p-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <h4 className="font-semibold">{country}</h4>
                          <p className="text-sm text-gray-600">{countryProxies.length} proxies</p>
                        </div>
                        <div className="text-right">
                          <Badge variant={activeCount > 0 ? 'default' : 'secondary'}>
                            {activeCount} active
                          </Badge>
                        </div>
                      </div>
                      <Progress value={(activeCount / countryProxies.length) * 100} className="mt-2 h-2" />
                    </Card>
                  );
                })}
              </div>
            </CardContent>
          </Card>

          {/* Performance Overview */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <BarChart3 className="h-5 w-5" />
                <span>Performance Overview</span>
              </CardTitle>
              <CardDescription>Real-time proxy performance metrics</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {performanceMetrics.slice(0, 3).map((metric) => {
                  const proxy = proxies.find(p => p.id === metric.proxy_id);
                  if (!proxy) return null;
                  
                  return (
                    <div key={metric.proxy_id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <div className="flex items-center space-x-3">
                        <div className="p-2 bg-blue-100 rounded">
                          <Network className="h-4 w-4 text-blue-600" />
                        </div>
                        <div>
                          <p className="font-medium">{proxy.ip}:{proxy.port}</p>
                          <p className="text-sm text-gray-600">{proxy.country} • {proxy.type.toUpperCase()}</p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-6 text-sm">
                        <div className="text-center">
                          <p className="font-medium">{metric.response_time}ms</p>
                          <p className="text-gray-500">Latency</p>
                        </div>
                        <div className="text-center">
                          <p className="font-medium">{metric.success_rate.toFixed(1)}%</p>
                          <p className="text-gray-500">Success</p>
                        </div>
                        <div className="text-center">
                          <p className="font-medium">{metric.anonymity_score}</p>
                          <p className="text-gray-500">Trust</p>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Proxy Pool Tab */}
        <TabsContent value="proxies" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Server className="h-5 w-5" />
                <span>Proxy Pool Management</span>
              </CardTitle>
              <CardDescription>Manage and monitor your proxy servers</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {proxies.map((proxy) => {
                  const speedInfo = getSpeedIndicator(proxy.speed);
                  
                  return (
                    <Card key={proxy.id} className="p-4">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-4">
                          <div className="p-2 bg-blue-100 rounded-lg">
                            <Network className="h-6 w-6 text-blue-600" />
                          </div>
                          <div>
                            <h4 className="font-semibold">{proxy.ip}:{proxy.port}</h4>
                            <div className="flex items-center space-x-3 mt-1 text-sm text-gray-600">
                              <span>{proxy.country}, {proxy.city}</span>
                              <span>•</span>
                              <span>{proxy.type.toUpperCase()}</span>
                              <span>•</span>
                              <span>{proxy.provider}</span>
                            </div>
                          </div>
                        </div>
                        
                        <div className="flex items-center space-x-4">
                          <div className="text-right text-sm">
                            <div className="flex items-center space-x-2">
                              <Badge className={getStatusColor(proxy.status)} variant="outline">
                                {proxy.status}
                              </Badge>
                              <Badge className={getAnonymityColor(proxy.anonymity)} variant="outline">
                                {proxy.anonymity}
                              </Badge>
                            </div>
                            <div className="flex items-center space-x-4 mt-2 text-gray-500">
                              <span className={speedInfo.color}>{proxy.speed}ms</span>
                              <span>{proxy.reliability.toFixed(1)}% uptime</span>
                              <span>{proxy.trust_score}% trust</span>
                            </div>
                          </div>
                          
                          <div className="flex flex-col space-y-1">
                            <Button size="sm" variant="outline" onClick={() => testProxy(proxy.id)}>
                              <Eye className="h-4 w-4" />
                            </Button>
                            <Button size="sm" variant="outline" onClick={() => removeProxy(proxy.id)}>
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                      </div>
                      
                      {/* Detailed metrics */}
                      <div className="mt-4 grid grid-cols-2 md:grid-cols-4 gap-4 pt-4 border-t border-gray-100">
                        <div className="text-sm">
                          <p className="text-gray-500">Bandwidth</p>
                          <p className="font-medium">{proxy.bandwidth.toFixed(1)} MB/s</p>
                        </div>
                        <div className="text-sm">
                          <p className="text-gray-500">Connections</p>
                          <p className="font-medium">{proxy.concurrent_connections}</p>
                        </div>
                        <div className="text-sm">
                          <p className="text-gray-500">Detection Risk</p>
                          <p className="font-medium">{proxy.detection_rate}%</p>
                        </div>
                        <div className="text-sm">
                          <p className="text-gray-500">Last Checked</p>
                          <p className="font-medium">{new Date(proxy.lastChecked).toLocaleTimeString()}</p>
                        </div>
                      </div>
                    </Card>
                  );
                })}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Proxy Chains Tab */}
        <TabsContent value="chains" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Workflow className="h-5 w-5" />
                <span>Proxy Chains</span>
              </CardTitle>
              <CardDescription>Create and manage multi-proxy chains for enhanced anonymity</CardDescription>
            </CardHeader>
            <CardContent>
              {proxyChains.length > 0 ? (
                <div className="space-y-4">
                  {proxyChains.map((chain) => (
                    <Card key={chain.id} className="p-4">
                      <div className="space-y-3">
                        <div className="flex items-start justify-between">
                          <div>
                            <h4 className="font-semibold">{chain.name}</h4>
                            <p className="text-sm text-gray-600">{chain.description}</p>
                          </div>
                          <div className="flex space-x-2">
                            <Badge variant="outline">{chain.proxies.length} proxies</Badge>
                            <Badge variant={chain.success_rate > 80 ? 'default' : 'secondary'}>
                              {chain.success_rate.toFixed(1)}% success
                            </Badge>
                          </div>
                        </div>
                        
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                          <div>
                            <p className="text-gray-500">Total Latency</p>
                            <p className="font-medium">{chain.total_latency}ms</p>
                          </div>
                          <div>
                            <p className="text-gray-500">Anonymity Level</p>
                            <p className="font-medium">{chain.anonymity_level}/10</p>
                          </div>
                          <div>
                            <p className="text-gray-500">Detection Risk</p>
                            <p className="font-medium">{chain.detection_probability.toFixed(1)}%</p>
                          </div>
                          <div>
                            <p className="text-gray-500">Last Used</p>
                            <p className="font-medium">{new Date(chain.last_used).toLocaleDateString()}</p>
                          </div>
                        </div>
                        
                        <div className="flex items-center space-x-2 pt-2 border-t border-gray-100">
                          <span className="text-sm text-gray-500">Chain:</span>
                          {chain.proxies.map((proxyId, index) => {
                            const proxy = proxies.find(p => p.id === proxyId);
                            return (
                              <div key={proxyId} className="flex items-center space-x-2">
                                {index > 0 && <span className="text-gray-400">→</span>}
                                <Badge variant="outline" className="text-xs">
                                  {proxy?.ip || proxyId}
                                </Badge>
                              </div>
                            );
                          })}
                        </div>
                      </div>
                    </Card>
                  ))}
                </div>
              ) : (
                <div className="text-center py-12">
                  <Workflow className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">No Proxy Chains</h3>
                  <p className="text-gray-600 mb-4">Create proxy chains for enhanced anonymity and routing</p>
                  <Button onClick={startAIOptimization} className="bg-purple-600 hover:bg-purple-700">
                    <Brain className="h-4 w-4 mr-2" />
                    Generate AI Chains
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Traffic Patterns Tab */}
        <TabsContent value="patterns" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Activity className="h-5 w-5" />
                <span>Traffic Patterns</span>
              </CardTitle>
              <CardDescription>Configure traffic patterns for detection avoidance</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Pattern Selection */}
                <div className="space-y-4">
                  <h3 className="font-semibold">Available Patterns</h3>
                  <div className="space-y-3">
                    {TRAFFIC_PATTERNS.map((pattern) => (
                      <Card key={pattern.id} 
                            className={`p-4 cursor-pointer transition-all ${
                              selectedPattern === pattern.id ? 'ring-2 ring-purple-500 bg-purple-50' : 'hover:bg-gray-50'
                            }`}
                            onClick={() => setSelectedPattern(pattern.id)}>
                        <div className="space-y-2">
                          <div className="flex items-center justify-between">
                            <h4 className="font-semibold">{pattern.name}</h4>
                            <Badge variant="outline" className="capitalize">
                              {pattern.pattern_type.replace('_', ' ')}
                            </Badge>
                          </div>
                          <p className="text-sm text-gray-600">{pattern.description}</p>
                          <div className="flex items-center space-x-4 text-xs text-gray-500">
                            <span>Interval: {pattern.request_interval.min}-{pattern.request_interval.max}ms</span>
                            <span>Success: {pattern.success_probability}%</span>
                            <span>Jitter: {pattern.timing_jitter * 100}%</span>
                          </div>
                        </div>
                      </Card>
                    ))}
                  </div>
                </div>

                {/* Pattern Details */}
                <div className="space-y-4">
                  <h3 className="font-semibold">Pattern Configuration</h3>
                  {(() => {
                    const pattern = TRAFFIC_PATTERNS.find(p => p.id === selectedPattern);
                    if (!pattern) return null;
                    
                    return (
                      <Card className="p-4">
                        <div className="space-y-4">
                          <div>
                            <h4 className="font-semibold mb-2">{pattern.name}</h4>
                            <p className="text-sm text-gray-600">{pattern.description}</p>
                          </div>
                          
                          <div className="space-y-3">
                            <div>
                              <label className="text-sm font-medium">Request Interval</label>
                              <div className="text-sm text-gray-600">
                                {pattern.request_interval.min}ms - {pattern.request_interval.max}ms
                              </div>
                            </div>
                            
                            <div>
                              <label className="text-sm font-medium">User Agents ({pattern.user_agents.length})</label>
                              <div className="max-h-32 overflow-y-auto">
                                {pattern.user_agents.map((ua, index) => (
                                  <div key={index} className="text-xs text-gray-600 font-mono p-1 bg-gray-50 rounded mt-1">
                                    {ua}
                                  </div>
                                ))}
                              </div>
                            </div>
                            
                            <div>
                              <label className="text-sm font-medium">Headers</label>
                              <div className="max-h-32 overflow-y-auto">
                                {Object.entries(pattern.headers).map(([key, value]) => (
                                  <div key={key} className="text-xs text-gray-600 p-1">
                                    <span className="font-medium">{key}:</span> {value}
                                  </div>
                                ))}
                              </div>
                            </div>
                            
                            <div className="grid grid-cols-2 gap-4 text-sm">
                              <div>
                                <p className="text-gray-500">Timing Jitter</p>
                                <p className="font-medium">{pattern.timing_jitter * 100}%</p>
                              </div>
                              <div>
                                <p className="text-gray-500">Success Rate</p>
                                <p className="font-medium">{pattern.success_probability}%</p>
                              </div>
                            </div>
                          </div>
                        </div>
                      </Card>
                    );
                  })()}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Analytics Tab */}
        <TabsContent value="analytics" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <BarChart3 className="h-5 w-5" />
                <span>Proxy Analytics</span>
              </CardTitle>
              <CardDescription>Performance analytics and detection insights</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Performance Metrics */}
                <Card className="p-4">
                  <h3 className="font-semibold mb-4">Performance Metrics</h3>
                  <div className="space-y-4">
                    {performanceMetrics.map((metric) => {
                      const proxy = proxies.find(p => p.id === metric.proxy_id);
                      return (
                        <div key={metric.proxy_id} className="space-y-2">
                          <div className="flex items-center justify-between">
                            <span className="text-sm font-medium">{proxy?.ip}</span>
                            <span className="text-sm text-gray-500">{metric.response_time}ms</span>
                          </div>
                          <Progress value={Math.min((metric.response_time / 1000) * 100, 100)} className="h-2" />
                        </div>
                      );
                    })}
                  </div>
                </Card>

                {/* Detection Analysis */}
                <Card className="p-4">
                  <h3 className="font-semibold mb-4">Detection Risk Analysis</h3>
                  <div className="space-y-4">
                    {proxies.map((proxy) => (
                      <div key={proxy.id} className="space-y-2">
                        <div className="flex items-center justify-between">
                          <span className="text-sm font-medium">{proxy.ip}</span>
                          <span className="text-sm text-gray-500">{proxy.detection_rate}%</span>
                        </div>
                        <Progress value={proxy.detection_rate} className="h-2" />
                      </div>
                    ))}
                  </div>
                </Card>

                {/* Geographic Distribution Chart */}
                <Card className="p-4 lg:col-span-2">
                  <h3 className="font-semibold mb-4">Geographic Distribution</h3>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    {Array.from(new Set(proxies.map(p => p.country))).map((country) => {
                      const count = proxies.filter(p => p.country === country).length;
                      const percentage = (count / proxies.length) * 100;
                      
                      return (
                        <div key={country} className="text-center">
                          <div className="text-lg font-bold">{count}</div>
                          <div className="text-sm text-gray-600">{country}</div>
                          <Progress value={percentage} className="h-2 mt-2" />
                          <div className="text-xs text-gray-500 mt-1">{percentage.toFixed(1)}%</div>
                        </div>
                      );
                    })}
                  </div>
                </Card>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Settings Tab */}
        <TabsContent value="settings" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Settings className="h-5 w-5" />
                <span>Proxy Configuration</span>
              </CardTitle>
              <CardDescription>Configure proxy rotation and AI optimization settings</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Rotation Trigger</label>
                    <Select value={proxyConfig.rotation_trigger} onValueChange={(value) => 
                      setProxyConfig({...proxyConfig, rotation_trigger: value})}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="time_based">Time-based</SelectItem>
                        <SelectItem value="request_count">Request Count</SelectItem>
                        <SelectItem value="detection_risk">Detection Risk</SelectItem>
                        <SelectItem value="performance">Performance</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <label className="text-sm font-medium">Rotation Interval (seconds)</label>
                    <Input
                      type="number"
                      value={rotationInterval}
                      onChange={(e) => setRotationInterval(Number(e.target.value))}
                      min="10"
                      max="3600"
                    />
                  </div>

                  <div className="space-y-2">
                    <label className="text-sm font-medium">Max Requests per Proxy</label>
                    <Input
                      type="number"
                      value={proxyConfig.max_requests_per_proxy}
                      onChange={(e) => setProxyConfig({
                        ...proxyConfig, 
                        max_requests_per_proxy: Number(e.target.value)
                      })}
                      min="1"
                      max="1000"
                    />
                  </div>

                  <div className="space-y-2">
                    <label className="text-sm font-medium">Anonymity Preference</label>
                    <Select value={proxyConfig.anonymity_preference} onValueChange={(value) => 
                      setProxyConfig({...proxyConfig, anonymity_preference: value})}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="transparent">Transparent</SelectItem>
                        <SelectItem value="anonymous">Anonymous</SelectItem>
                        <SelectItem value="elite">Elite</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="space-y-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Speed Threshold (ms)</label>
                    <Input
                      type="number"
                      value={proxyConfig.speed_threshold}
                      onChange={(e) => setProxyConfig({
                        ...proxyConfig, 
                        speed_threshold: Number(e.target.value)
                      })}
                      min="100"
                      max="5000"
                    />
                  </div>

                  <div className="space-y-2">
                    <label className="text-sm font-medium">Reliability Threshold (%)</label>
                    <Input
                      type="number"
                      value={proxyConfig.reliability_threshold}
                      onChange={(e) => setProxyConfig({
                        ...proxyConfig, 
                        reliability_threshold: Number(e.target.value)
                      })}
                      min="50"
                      max="100"
                    />
                  </div>

                  <div className="space-y-3">
                    <label className="text-sm font-medium">Advanced Options</label>
                    <div className="space-y-2">
                      <div className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          checked={proxyConfig.auto_rotation}
                          onChange={(e) => setProxyConfig({...proxyConfig, auto_rotation: e.target.checked})}
                          className="rounded"
                        />
                        <label className="text-sm">Enable Auto-rotation</label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          checked={proxyConfig.geographic_distribution}
                          onChange={(e) => setProxyConfig({...proxyConfig, geographic_distribution: e.target.checked})}
                          className="rounded"
                        />
                        <label className="text-sm">Geographic Distribution</label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          checked={proxyConfig.ai_optimization}
                          onChange={(e) => setProxyConfig({...proxyConfig, ai_optimization: e.target.checked})}
                          className="rounded"
                        />
                        <label className="text-sm">AI Optimization</label>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <Card className="p-4 bg-blue-50 border-blue-200">
                <h4 className="font-semibold text-blue-800 mb-2">Configuration Summary</h4>
                <div className="text-sm text-blue-700 space-y-1">
                  <p>Rotation: {proxyConfig.rotation_trigger.replace('_', ' ')} every {rotationInterval}s</p>
                  <p>Max requests per proxy: {proxyConfig.max_requests_per_proxy}</p>
                  <p>Anonymity preference: {proxyConfig.anonymity_preference}</p>
                  <p>Speed threshold: {proxyConfig.speed_threshold}ms</p>
                  <p>Reliability threshold: {proxyConfig.reliability_threshold}%</p>
                  <p>AI optimization: {proxyConfig.ai_optimization ? 'Enabled' : 'Disabled'}</p>
                </div>
              </Card>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};