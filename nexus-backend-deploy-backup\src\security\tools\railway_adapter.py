#!/usr/bin/env python3
"""
Railway Adapter for Security Tools
Makes tools work in Railway environment without requiring actual binaries
"""

import os
import logging
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
import json
import asyncio
from datetime import datetime

logger = logging.getLogger(__name__)


@dataclass
class RailwayEnvironment:
    """Railway environment configuration"""
    is_railway: bool
    environment_name: str
    deployment_id: Optional[str]
    region: Optional[str]
    
    @classmethod
    def detect(cls) -> 'RailwayEnvironment':
        """Detect if running on Railway"""
        return cls(
            is_railway=bool(os.environ.get("RAILWAY_ENVIRONMENT")),
            environment_name=os.environ.get("RAILWAY_ENVIRONMENT", "local"),
            deployment_id=os.environ.get("RAILWAY_DEPLOYMENT_ID"),
            region=os.environ.get("RAILWAY_REGION")
        )


class RailwayToolAdapter:
    """Adapter to make security tools work on Railway platform"""
    
    def __init__(self):
        self.environment = RailwayEnvironment.detect()
        self.simulation_mode = True  # Always use simulation on Railway
        logger.info(f"Railway adapter initialized - Environment: {self.environment.environment_name}")
    
    def should_use_simulation(self, tool_name: str) -> bool:
        """Determine if tool should use simulation mode"""
        # On Railway, always use simulation for binary-dependent tools
        if self.environment.is_railway:
            binary_tools = [
                "nmap", "nuclei", "sqlmap", "gobuster", "nikto", 
                "dirb", "wpscan", "ffuf", "feroxbuster", "whatweb",
                "hashcat", "john", "metasploit", "searchsploit"
            ]
            return tool_name in binary_tools
        return False
    
    def create_simulated_result(self, tool_name: str, target: str, scan_type: str = "default") -> Dict[str, Any]:
        """Create realistic simulated results for tools"""
        timestamp = datetime.now().isoformat()
        
        # Tool-specific simulated results
        simulated_results = {
            "nmap": self._simulate_nmap_scan(target, scan_type),
            "nuclei": self._simulate_nuclei_scan(target, scan_type),
            "sqlmap": self._simulate_sqlmap_scan(target),
            "gobuster": self._simulate_gobuster_scan(target),
            "nikto": self._simulate_nikto_scan(target),
            "dirb": self._simulate_dirb_scan(target),
            "wpscan": self._simulate_wpscan_scan(target),
            "ffuf": self._simulate_ffuf_scan(target),
            "feroxbuster": self._simulate_feroxbuster_scan(target),
            "whatweb": self._simulate_whatweb_scan(target),
            "hashcat": self._simulate_hashcat_crack(target),
            "john": self._simulate_john_crack(target)
        }
        
        base_result = {
            "tool": tool_name,
            "target": target,
            "scan_type": scan_type,
            "timestamp": timestamp,
            "duration": "2.5s",
            "status": "completed",
            "simulation_mode": True,
            "railway_environment": True
        }
        
        if tool_name in simulated_results:
            base_result.update(simulated_results[tool_name])
        else:
            base_result["error"] = f"No simulation available for {tool_name}"
        
        return base_result
    
    def _simulate_nmap_scan(self, target: str, scan_type: str) -> Dict[str, Any]:
        """Simulate Nmap scan results"""
        return {
            "ports": [
                {"port": 22, "state": "open", "service": "ssh", "version": "OpenSSH 8.0"},
                {"port": 80, "state": "open", "service": "http", "version": "nginx 1.18.0"},
                {"port": 443, "state": "open", "service": "https", "version": "nginx 1.18.0"},
                {"port": 3306, "state": "filtered", "service": "mysql", "version": ""}
            ],
            "os_detection": {
                "os": "Linux",
                "accuracy": 95,
                "kernel": "4.x or 5.x"
            },
            "vulnerabilities": [
                {
                    "port": 22,
                    "severity": "info",
                    "description": "SSH service detected - ensure strong key-based authentication"
                }
            ]
        }
    
    def _simulate_nuclei_scan(self, target: str, scan_type: str) -> Dict[str, Any]:
        """Simulate Nuclei scan results"""
        severities = ["info", "low", "medium", "high", "critical"]
        severity = severities[2] if scan_type == "critical" else "medium"
        
        return {
            "vulnerabilities": [
                {
                    "template_id": "apache-version-detect",
                    "name": "Apache Version Detection",
                    "severity": "info",
                    "matched_at": f"{target}/",
                    "type": "version-detection"
                },
                {
                    "template_id": "security-headers-missing",
                    "name": "Missing Security Headers",
                    "severity": severity,
                    "matched_at": target,
                    "missing_headers": ["X-Frame-Options", "X-Content-Type-Options"]
                }
            ],
            "templates_used": 2500,
            "requests_made": 150
        }
    
    def _simulate_sqlmap_scan(self, target: str) -> Dict[str, Any]:
        """Simulate SQLMap scan results"""
        return {
            "injection_points": [
                {
                    "parameter": "id",
                    "type": "GET",
                    "injectable": True,
                    "dbms": "MySQL",
                    "techniques": ["boolean-based blind", "time-based blind"]
                }
            ],
            "database_info": {
                "dbms": "MySQL",
                "version": "5.7.x",
                "current_database": "testdb",
                "current_user": "root@localhost"
            },
            "risk_level": "high",
            "confidence": 85
        }
    
    def _simulate_gobuster_scan(self, target: str) -> Dict[str, Any]:
        """Simulate Gobuster scan results"""
        return {
            "directories_found": [
                {"path": "/admin", "status": 403, "size": 278},
                {"path": "/api", "status": 200, "size": 1024},
                {"path": "/backup", "status": 403, "size": 278},
                {"path": "/config", "status": 403, "size": 278},
                {"path": "/uploads", "status": 200, "size": 512}
            ],
            "files_found": [
                {"path": "/robots.txt", "status": 200, "size": 128},
                {"path": "/.htaccess", "status": 403, "size": 278}
            ],
            "total_requests": 1000
        }
    
    def _simulate_nikto_scan(self, target: str) -> Dict[str, Any]:
        """Simulate Nikto scan results"""
        return {
            "vulnerabilities": [
                {
                    "id": "OSVDB-3092",
                    "description": "/admin/: This might be interesting...",
                    "method": "GET",
                    "uri": "/admin/"
                },
                {
                    "id": "OSVDB-3268",
                    "description": "/config/: Directory indexing found.",
                    "method": "GET",
                    "uri": "/config/"
                }
            ],
            "server_info": {
                "server": "nginx/1.18.0",
                "technologies": ["PHP/7.4.3"]
            },
            "items_found": 15,
            "elapsed_time": "42 seconds"
        }
    
    def _simulate_dirb_scan(self, target: str) -> Dict[str, Any]:
        """Simulate DIRB scan results"""
        return {
            "directories": [
                f"{target}/images/",
                f"{target}/css/",
                f"{target}/js/",
                f"{target}/api/",
                f"{target}/docs/"
            ],
            "files": [
                f"{target}/index.html",
                f"{target}/login.php",
                f"{target}/config.php.bak"
            ],
            "response_codes": {
                "200": 8,
                "403": 3,
                "404": 150
            }
        }
    
    def _simulate_wpscan_scan(self, target: str) -> Dict[str, Any]:
        """Simulate WPScan results"""
        return {
            "wordpress_version": "5.8.1",
            "theme": {
                "name": "twentytwentyone",
                "version": "1.4",
                "vulnerabilities": []
            },
            "plugins": [
                {
                    "name": "contact-form-7",
                    "version": "5.4.2",
                    "vulnerabilities": [
                        {
                            "title": "Contact Form 7 < 5.3.2 - Unrestricted File Upload",
                            "severity": "critical"
                        }
                    ]
                }
            ],
            "users": ["admin", "editor"],
            "security_issues": [
                "WordPress version publicly visible",
                "Upload directory has listing enabled"
            ]
        }
    
    def _simulate_ffuf_scan(self, target: str) -> Dict[str, Any]:
        """Simulate FFUF scan results"""
        return {
            "fuzzing_results": [
                {"path": "/api/v1", "status": 200, "length": 42, "words": 5},
                {"path": "/api/users", "status": 401, "length": 89, "words": 12},
                {"path": "/api/admin", "status": 403, "length": 134, "words": 18},
                {"path": "/test", "status": 200, "length": 1337, "words": 142}
            ],
            "statistics": {
                "requests_total": 5000,
                "requests_per_second": 850,
                "duration": "5.9s"
            }
        }
    
    def _simulate_feroxbuster_scan(self, target: str) -> Dict[str, Any]:
        """Simulate FeroxBuster scan results"""
        return {
            "discovered_paths": [
                {"url": f"{target}/admin", "status": 403, "size": 278, "wildcard": False},
                {"url": f"{target}/api", "status": 200, "size": 1024, "wildcard": False},
                {"url": f"{target}/backup.zip", "status": 200, "size": 5242880, "wildcard": False},
                {"url": f"{target}/.git", "status": 403, "size": 278, "wildcard": False}
            ],
            "scan_statistics": {
                "requests": 10000,
                "errors": 12,
                "successes": 9988,
                "redirects": 45,
                "timeouts": 3
            }
        }
    
    def _simulate_whatweb_scan(self, target: str) -> Dict[str, Any]:
        """Simulate WhatWeb scan results"""
        return {
            "technologies": {
                "CMS": ["WordPress 5.8.1"],
                "Web Server": ["nginx/1.18.0"],
                "Programming Language": ["PHP 7.4.3"],
                "JavaScript Libraries": ["jQuery 3.6.0", "Bootstrap 4.6.0"],
                "Analytics": ["Google Analytics UA-123456"],
                "CDN": ["Cloudflare"]
            },
            "headers": {
                "Server": "cloudflare",
                "X-Powered-By": "PHP/7.4.3",
                "Content-Type": "text/html; charset=UTF-8"
            },
            "meta_tags": {
                "generator": "WordPress 5.8.1",
                "viewport": "width=device-width, initial-scale=1"
            }
        }
    
    def _simulate_hashcat_crack(self, target: str) -> Dict[str, Any]:
        """Simulate Hashcat password cracking results"""
        return {
            "cracked_hashes": [
                {
                    "hash": "5f4dcc3b5aa765d61d8327deb882cf99",
                    "plaintext": "password",
                    "hash_type": "MD5"
                },
                {
                    "hash": "e10adc3949ba59abbe56e057f20f883e",
                    "plaintext": "123456",
                    "hash_type": "MD5"
                }
            ],
            "statistics": {
                "total_hashes": 5,
                "cracked": 2,
                "time_elapsed": "0:00:42",
                "hashrate": "1.5 MH/s"
            }
        }
    
    def _simulate_john_crack(self, target: str) -> Dict[str, Any]:
        """Simulate John the Ripper results"""
        return {
            "cracked_passwords": [
                {
                    "username": "admin",
                    "password": "admin123",
                    "hash_format": "md5crypt"
                },
                {
                    "username": "user",
                    "password": "password1",
                    "hash_format": "md5crypt"
                }
            ],
            "statistics": {
                "passwords_cracked": 2,
                "time_taken": "1m 23s",
                "guesses": 150000
            }
        }
    
    async def execute_with_simulation(self, tool_name: str, command: List[str], 
                                    target: str, options: Dict[str, Any]) -> Dict[str, Any]:
        """Execute tool with simulation fallback"""
        if self.should_use_simulation(tool_name):
            logger.info(f"Using simulation mode for {tool_name} on Railway")
            # Simulate some execution time
            await asyncio.sleep(0.5)
            return self.create_simulated_result(tool_name, target, options.get("scan_type", "default"))
        
        # If not simulation, return error (tools not available on Railway)
        return {
            "error": f"Tool {tool_name} not available in Railway environment",
            "suggestion": "Simulation mode is available for this tool"
        }


# Global Railway adapter instance
railway_adapter = RailwayToolAdapter()