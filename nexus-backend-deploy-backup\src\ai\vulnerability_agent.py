#!/usr/bin/env python3
"""
Vulnerability Assessment AI Agent for NexusScan
Advanced AI-powered vulnerability analysis, classification, and risk assessment
"""

import asyncio
import logging
import json
import re
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union, Tuple
from dataclasses import dataclass, field
from enum import Enum
import hashlib

from .ai_service import (
    AIServiceProvider, AIServiceConfig, AIAnalysisRequest, AIAnalysisResult,
    AICapability, AIModelType, AnalysisType, VulnerabilityContext
)

logger = logging.getLogger(__name__)


class VulnerabilityCategory(Enum):
    """Vulnerability categories based on OWASP and CWE"""
    INJECTION = "injection"
    BROKEN_AUTHENTICATION = "broken_authentication"
    SENSITIVE_DATA_EXPOSURE = "sensitive_data_exposure"
    XML_EXTERNAL_ENTITIES = "xml_external_entities"
    BROKEN_ACCESS_CONTROL = "broken_access_control"
    SECURITY_MISCONFIGURATION = "security_misconfiguration"
    CROSS_SITE_SCRIPTING = "cross_site_scripting"
    INSECURE_DESERIALIZATION = "insecure_deserialization"
    COMPONENTS_KNOWN_VULNS = "components_known_vulns"
    INSUFFICIENT_LOGGING = "insufficient_logging"
    NETWORK_VULNERABILITY = "network_vulnerability"
    CRYPTOGRAPHIC_FAILURE = "cryptographic_failure"
    BUFFER_OVERFLOW = "buffer_overflow"
    PRIVILEGE_ESCALATION = "privilege_escalation"
    DENIAL_OF_SERVICE = "denial_of_service"
    INFORMATION_DISCLOSURE = "information_disclosure"
    OTHER = "other"


class ExploitComplexity(Enum):
    """Exploit complexity levels"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"


class AttackVector(Enum):
    """Attack vectors"""
    NETWORK = "network"
    ADJACENT = "adjacent"
    LOCAL = "local"
    PHYSICAL = "physical"


@dataclass
class VulnerabilityAssessment:
    """Comprehensive vulnerability assessment result"""
    vulnerability_id: str
    category: VulnerabilityCategory
    severity_score: float
    risk_level: str
    exploitability_score: float
    impact_score: float
    attack_vector: AttackVector
    exploit_complexity: ExploitComplexity
    privileges_required: str
    user_interaction: bool
    scope_changed: bool
    confidentiality_impact: str
    integrity_impact: str
    availability_impact: str
    cvss_vector: str = ""
    cvss_score: float = 0.0
    business_impact: str = ""
    technical_impact: str = ""
    likelihood: float = 0.0
    attack_scenarios: List[str] = field(default_factory=list)
    mitigation_strategies: List[str] = field(default_factory=list)
    detection_methods: List[str] = field(default_factory=list)
    references: List[str] = field(default_factory=list)
    tags: List[str] = field(default_factory=list)
    confidence: float = 0.0
    analysis_notes: str = ""


@dataclass
class AttackPath:
    """Potential attack path"""
    path_id: str
    attack_steps: List[str]
    required_conditions: List[str]
    exploited_vulnerabilities: List[str]
    final_impact: str
    probability: float
    complexity: ExploitComplexity
    mitigation_steps: List[str] = field(default_factory=list)


@dataclass
class ThreatLandscape:
    """Threat landscape analysis"""
    threat_actors: List[str]
    attack_trends: List[str]
    prevalent_techniques: List[str]
    seasonal_patterns: Dict[str, Any]
    geography_risks: Dict[str, float]
    industry_threats: List[str]
    emerging_threats: List[str]


class VulnerabilityAgent(AIServiceProvider):
    """AI agent specialized in vulnerability assessment and analysis"""

    def __init__(self, config: AIServiceConfig):
        super().__init__(config)
        
        # Vulnerability knowledge base
        self.vulnerability_patterns = self._load_vulnerability_patterns()
        self.exploit_techniques = self._load_exploit_techniques()
        self.mitigation_database = self._load_mitigation_database()
        
        # AI model for vulnerability analysis
        self.model = None  # Would be initialized with actual AI model
        
        # Assessment history for learning
        self.assessment_history: List[VulnerabilityAssessment] = []
        self.false_positive_patterns: Dict[str, int] = {}
        
        # Statistics
        self.assessment_stats = {
            "total_assessments": 0,
            "high_risk_found": 0,
            "false_positives_detected": 0,
            "attack_paths_identified": 0,
            "mitigations_suggested": 0
        }

    async def analyze(self, request: AIAnalysisRequest) -> AIAnalysisResult:
        """Perform vulnerability analysis
        
        Args:
            request: Analysis request
            
        Returns:
            Analysis result with vulnerability assessment
        """
        start_time = datetime.now()
        
        try:
            if request.capability != AICapability.VULNERABILITY_ASSESSMENT:
                raise ValueError(f"Unsupported capability: {request.capability}")
            
            # Extract vulnerability context
            if isinstance(request.data, VulnerabilityContext):
                context = request.data
            else:
                context = VulnerabilityContext(**request.context.get("vulnerability", {}))
            
            # Perform comprehensive assessment
            assessment = await self._assess_vulnerability(context)
            
            # Generate attack paths
            attack_paths = await self._analyze_attack_paths(context, assessment)
            
            # Assess threat landscape
            threat_landscape = await self._analyze_threat_landscape(context)
            
            # Calculate confidence score
            confidence = self._calculate_confidence(context, assessment)
            
            # Generate recommendations
            recommendations = self._generate_recommendations(assessment, attack_paths)
            
            # Generate insights
            insights = self._generate_insights(assessment, threat_landscape)
            
            result = AIAnalysisResult(
                request_id=request.request_id,
                analysis_type=request.analysis_type,
                capability=request.capability,
                success=True,
                confidence=confidence,
                result={
                    "assessment": assessment.__dict__,
                    "attack_paths": [path.__dict__ for path in attack_paths],
                    "threat_landscape": threat_landscape.__dict__,
                    "risk_score": assessment.severity_score,
                    "exploitability": assessment.exploitability_score,
                    "business_impact": assessment.business_impact
                },
                recommendations=recommendations,
                insights=insights,
                processing_time=(datetime.now() - start_time).total_seconds(),
                model_used=self.config.service_name
            )
            
            # Update statistics
            self._update_assessment_statistics(assessment)
            self._update_statistics(result)
            
            # Store assessment for learning
            self.assessment_history.append(assessment)
            self._cleanup_assessment_history()
            
            return result
            
        except Exception as e:
            self.logger.error(f"Vulnerability analysis failed: {e}")
            return AIAnalysisResult(
                request_id=request.request_id,
                analysis_type=request.analysis_type,
                capability=request.capability,
                success=False,
                confidence=0.0,
                result={},
                error_message=str(e),
                processing_time=(datetime.now() - start_time).total_seconds(),
                model_used=self.config.service_name
            )

    async def assess_vulnerability_batch(self, contexts: List[VulnerabilityContext]) -> List[VulnerabilityAssessment]:
        """Assess multiple vulnerabilities in batch
        
        Args:
            contexts: List of vulnerability contexts
            
        Returns:
            List of vulnerability assessments
        """
        assessments = []
        
        # Process in batches to avoid overwhelming the system
        batch_size = 10
        for i in range(0, len(contexts), batch_size):
            batch = contexts[i:i + batch_size]
            batch_tasks = [self._assess_vulnerability(context) for context in batch]
            batch_results = await asyncio.gather(*batch_tasks, return_exceptions=True)
            
            for result in batch_results:
                if isinstance(result, VulnerabilityAssessment):
                    assessments.append(result)
                else:
                    self.logger.error(f"Assessment failed: {result}")
        
        return assessments

    async def correlate_vulnerabilities(self, assessments: List[VulnerabilityAssessment]) -> Dict[str, Any]:
        """Correlate multiple vulnerabilities to find attack chains
        
        Args:
            assessments: List of vulnerability assessments
            
        Returns:
            Correlation analysis result
        """
        correlation_result = {
            "attack_chains": [],
            "vulnerability_clusters": [],
            "compound_risks": [],
            "priority_order": []
        }
        
        try:
            # Group vulnerabilities by target/host
            host_vulns = {}
            for assessment in assessments:
                vuln_ctx = next((ctx for ctx in self.assessment_history 
                               if ctx.vulnerability_id == assessment.vulnerability_id), None)
                if vuln_ctx:
                    host = getattr(vuln_ctx, 'target_host', 'unknown')
                    if host not in host_vulns:
                        host_vulns[host] = []
                    host_vulns[host].append(assessment)
            
            # Analyze attack chains for each host
            for host, vulnerabilities in host_vulns.items():
                chains = await self._find_attack_chains(vulnerabilities)
                correlation_result["attack_chains"].extend(chains)
            
            # Cluster similar vulnerabilities
            clusters = self._cluster_vulnerabilities(assessments)
            correlation_result["vulnerability_clusters"] = clusters
            
            # Identify compound risks
            compound_risks = self._identify_compound_risks(assessments)
            correlation_result["compound_risks"] = compound_risks
            
            # Calculate priority order
            priority_order = self._calculate_priority_order(assessments)
            correlation_result["priority_order"] = priority_order
            
        except Exception as e:
            self.logger.error(f"Vulnerability correlation failed: {e}")
        
        return correlation_result

    async def predict_exploit_likelihood(self, assessment: VulnerabilityAssessment) -> Dict[str, float]:
        """Predict likelihood of exploitation
        
        Args:
            assessment: Vulnerability assessment
            
        Returns:
            Exploitation likelihood predictions
        """
        predictions = {
            "immediate_risk": 0.0,
            "30_day_risk": 0.0,
            "90_day_risk": 0.0,
            "annual_risk": 0.0
        }
        
        try:
            # Base likelihood from CVSS scores
            base_likelihood = assessment.exploitability_score / 10.0
            
            # Adjust for attack complexity
            complexity_multiplier = {
                ExploitComplexity.LOW: 1.5,
                ExploitComplexity.MEDIUM: 1.0,
                ExploitComplexity.HIGH: 0.5
            }.get(assessment.exploit_complexity, 1.0)
            
            # Adjust for attack vector
            vector_multiplier = {
                AttackVector.NETWORK: 1.5,
                AttackVector.ADJACENT: 1.2,
                AttackVector.LOCAL: 0.8,
                AttackVector.PHYSICAL: 0.3
            }.get(assessment.attack_vector, 1.0)
            
            # Calculate time-based predictions
            adjusted_likelihood = base_likelihood * complexity_multiplier * vector_multiplier
            
            predictions["immediate_risk"] = min(adjusted_likelihood * 0.1, 1.0)
            predictions["30_day_risk"] = min(adjusted_likelihood * 0.3, 1.0)
            predictions["90_day_risk"] = min(adjusted_likelihood * 0.6, 1.0)
            predictions["annual_risk"] = min(adjusted_likelihood * 0.9, 1.0)
            
            # Adjust based on threat intelligence
            threat_multiplier = await self._get_threat_intelligence_multiplier(assessment)
            for key in predictions:
                predictions[key] = min(predictions[key] * threat_multiplier, 1.0)
                
        except Exception as e:
            self.logger.error(f"Exploit prediction failed: {e}")
        
        return predictions

    async def is_available(self) -> bool:
        """Check if vulnerability agent is available"""
        try:
            # Check if AI model is loaded and responsive
            test_context = VulnerabilityContext(
                vulnerability_id="test",
                severity="low",
                evidence="test vulnerability"
            )
            
            # Quick health check assessment
            start_time = datetime.now()
            await self._assess_vulnerability(test_context)
            response_time = (datetime.now() - start_time).total_seconds()
            
            # Consider available if response time is reasonable
            return response_time < 5.0
            
        except Exception as e:
            self.logger.error(f"Availability check failed: {e}")
            return False

    async def health_check(self) -> Dict[str, Any]:
        """Perform comprehensive health check"""
        health_info = {
            "status": "healthy",
            "response_time": 0.0,
            "model_loaded": bool(self.model),
            "patterns_loaded": len(self.vulnerability_patterns),
            "techniques_loaded": len(self.exploit_techniques),
            "assessments_performed": self.assessment_stats["total_assessments"],
            "error": None
        }
        
        try:
            start_time = datetime.now()
            available = await self.is_available()
            health_info["response_time"] = (datetime.now() - start_time).total_seconds()
            
            if not available:
                health_info["status"] = "unhealthy"
                health_info["error"] = "Agent not responding properly"
                
        except Exception as e:
            health_info["status"] = "unhealthy"
            health_info["error"] = str(e)
        
        return health_info

    # Private methods

    async def _assess_vulnerability(self, context: VulnerabilityContext) -> VulnerabilityAssessment:
        """Perform detailed vulnerability assessment"""
        
        # Categorize vulnerability
        category = self._categorize_vulnerability(context)
        
        # Calculate base scores
        severity_score = self._calculate_severity_score(context)
        exploitability_score = self._calculate_exploitability_score(context)
        impact_score = self._calculate_impact_score(context)
        
        # Determine attack characteristics
        attack_vector = self._determine_attack_vector(context)
        exploit_complexity = self._determine_exploit_complexity(context)
        privileges_required = self._determine_privileges_required(context)
        user_interaction = self._requires_user_interaction(context)
        scope_changed = self._scope_changes(context)
        
        # Calculate impact levels
        confidentiality_impact = self._calculate_confidentiality_impact(context)
        integrity_impact = self._calculate_integrity_impact(context)
        availability_impact = self._calculate_availability_impact(context)
        
        # Generate CVSS metrics
        cvss_vector, cvss_score = self._calculate_cvss_score(
            attack_vector, exploit_complexity, privileges_required,
            user_interaction, scope_changed, confidentiality_impact,
            integrity_impact, availability_impact
        )
        
        # Assess business and technical impact
        business_impact = self._assess_business_impact(context, impact_score)
        technical_impact = self._assess_technical_impact(context, impact_score)
        
        # Calculate likelihood
        likelihood = self._calculate_likelihood(exploitability_score, context)
        
        # Generate attack scenarios
        attack_scenarios = self._generate_attack_scenarios(context, category)
        
        # Generate mitigation strategies
        mitigation_strategies = self._generate_mitigation_strategies(context, category)
        
        # Generate detection methods
        detection_methods = self._generate_detection_methods(context, category)
        
        # Get references
        references = self._get_vulnerability_references(context, category)
        
        # Generate tags
        tags = self._generate_vulnerability_tags(context, category)
        
        # Calculate confidence
        confidence = self._calculate_assessment_confidence(context)
        
        # Risk level determination
        risk_level = self._determine_risk_level(severity_score, likelihood)
        
        return VulnerabilityAssessment(
            vulnerability_id=context.vulnerability_id,
            category=category,
            severity_score=severity_score,
            risk_level=risk_level,
            exploitability_score=exploitability_score,
            impact_score=impact_score,
            attack_vector=attack_vector,
            exploit_complexity=exploit_complexity,
            privileges_required=privileges_required,
            user_interaction=user_interaction,
            scope_changed=scope_changed,
            confidentiality_impact=confidentiality_impact,
            integrity_impact=integrity_impact,
            availability_impact=availability_impact,
            cvss_vector=cvss_vector,
            cvss_score=cvss_score,
            business_impact=business_impact,
            technical_impact=technical_impact,
            likelihood=likelihood,
            attack_scenarios=attack_scenarios,
            mitigation_strategies=mitigation_strategies,
            detection_methods=detection_methods,
            references=references,
            tags=tags,
            confidence=confidence,
            analysis_notes=f"Automated assessment by {self.config.service_name}"
        )

    async def _analyze_attack_paths(self, context: VulnerabilityContext, assessment: VulnerabilityAssessment) -> List[AttackPath]:
        """Analyze potential attack paths"""
        attack_paths = []
        
        try:
            # Generate basic attack path
            basic_path = AttackPath(
                path_id=f"path_{context.vulnerability_id}_basic",
                attack_steps=self._generate_attack_steps(context, assessment),
                required_conditions=self._identify_required_conditions(context, assessment),
                exploited_vulnerabilities=[context.vulnerability_id],
                final_impact=assessment.technical_impact,
                probability=assessment.likelihood,
                complexity=assessment.exploit_complexity,
                mitigation_steps=assessment.mitigation_strategies
            )
            attack_paths.append(basic_path)
            
            # Generate advanced attack paths if high severity
            if assessment.severity_score >= 7.0:
                advanced_paths = await self._generate_advanced_attack_paths(context, assessment)
                attack_paths.extend(advanced_paths)
                
        except Exception as e:
            self.logger.error(f"Attack path analysis failed: {e}")
        
        return attack_paths

    async def _analyze_threat_landscape(self, context: VulnerabilityContext) -> ThreatLandscape:
        """Analyze current threat landscape"""
        return ThreatLandscape(
            threat_actors=["APT groups", "Cybercriminals", "Script kiddies"],
            attack_trends=["Ransomware", "Supply chain attacks", "Cloud misconfiguration"],
            prevalent_techniques=["Phishing", "Exploitation", "Lateral movement"],
            seasonal_patterns={"Q4": "increased activity"},
            geography_risks={"global": 1.0},
            industry_threats=["All sectors"],
            emerging_threats=["AI-powered attacks", "Quantum computing threats"]
        )

    # Utility methods for assessment calculations

    def _categorize_vulnerability(self, context: VulnerabilityContext) -> VulnerabilityCategory:
        """Categorize vulnerability based on context"""
        evidence_lower = (context.evidence + " " + context.tool_output).lower()
        
        # Pattern matching for categorization
        if any(term in evidence_lower for term in ["sql", "injection", "sqli"]):
            return VulnerabilityCategory.INJECTION
        elif any(term in evidence_lower for term in ["xss", "cross-site", "script"]):
            return VulnerabilityCategory.CROSS_SITE_SCRIPTING
        elif any(term in evidence_lower for term in ["auth", "login", "session"]):
            return VulnerabilityCategory.BROKEN_AUTHENTICATION
        elif any(term in evidence_lower for term in ["ssl", "tls", "crypto", "cipher"]):
            return VulnerabilityCategory.CRYPTOGRAPHIC_FAILURE
        elif any(term in evidence_lower for term in ["buffer", "overflow", "memory"]):
            return VulnerabilityCategory.BUFFER_OVERFLOW
        elif any(term in evidence_lower for term in ["access", "privilege", "authorization"]):
            return VulnerabilityCategory.BROKEN_ACCESS_CONTROL
        elif any(term in evidence_lower for term in ["config", "misconfiguration", "default"]):
            return VulnerabilityCategory.SECURITY_MISCONFIGURATION
        elif any(term in evidence_lower for term in ["disclosure", "information", "leak"]):
            return VulnerabilityCategory.INFORMATION_DISCLOSURE
        elif any(term in evidence_lower for term in ["dos", "denial", "service"]):
            return VulnerabilityCategory.DENIAL_OF_SERVICE
        else:
            return VulnerabilityCategory.OTHER

    def _calculate_severity_score(self, context: VulnerabilityContext) -> float:
        """Calculate severity score (0-10)"""
        base_score = 5.0  # Medium by default
        
        # Adjust based on context severity
        severity_map = {
            "critical": 9.5,
            "high": 8.0,
            "medium": 5.0,
            "low": 2.0,
            "info": 1.0
        }
        
        if context.severity.lower() in severity_map:
            base_score = severity_map[context.severity.lower()]
        
        # Adjust based on evidence content
        evidence_lower = context.evidence.lower()
        if "remote" in evidence_lower:
            base_score += 1.0
        if "unauthenticated" in evidence_lower:
            base_score += 1.5
        if "privilege escalation" in evidence_lower:
            base_score += 1.0
        
        return min(base_score, 10.0)

    def _calculate_exploitability_score(self, context: VulnerabilityContext) -> float:
        """Calculate exploitability score (0-10)"""
        base_score = 5.0
        
        evidence_lower = context.evidence.lower()
        
        # High exploitability indicators
        if any(term in evidence_lower for term in ["remote", "unauthenticated", "public exploit"]):
            base_score += 2.0
        
        # Medium exploitability indicators
        if any(term in evidence_lower for term in ["local", "authenticated", "low complexity"]):
            base_score += 1.0
        
        # Low exploitability indicators
        if any(term in evidence_lower for term in ["requires interaction", "high complexity", "specific conditions"]):
            base_score -= 1.0
        
        return max(0.0, min(base_score, 10.0))

    def _calculate_impact_score(self, context: VulnerabilityContext) -> float:
        """Calculate impact score (0-10)"""
        base_score = 5.0
        
        evidence_lower = context.evidence.lower()
        
        # High impact indicators
        if any(term in evidence_lower for term in ["data breach", "system compromise", "admin access"]):
            base_score += 3.0
        
        # Medium impact indicators
        if any(term in evidence_lower for term in ["user data", "service disruption", "file access"]):
            base_score += 1.5
        
        # Consider service criticality
        if context.affected_service in ["database", "authentication", "payment"]:
            base_score += 2.0
        
        return max(0.0, min(base_score, 10.0))

    def _determine_attack_vector(self, context: VulnerabilityContext) -> AttackVector:
        """Determine attack vector"""
        evidence_lower = context.evidence.lower()
        
        if "network" in evidence_lower or "remote" in evidence_lower:
            return AttackVector.NETWORK
        elif "adjacent" in evidence_lower or "lan" in evidence_lower:
            return AttackVector.ADJACENT
        elif "local" in evidence_lower:
            return AttackVector.LOCAL
        elif "physical" in evidence_lower:
            return AttackVector.PHYSICAL
        else:
            return AttackVector.NETWORK  # Default assumption

    def _determine_exploit_complexity(self, context: VulnerabilityContext) -> ExploitComplexity:
        """Determine exploit complexity"""
        evidence_lower = context.evidence.lower()
        
        if any(term in evidence_lower for term in ["simple", "trivial", "low complexity"]):
            return ExploitComplexity.LOW
        elif any(term in evidence_lower for term in ["complex", "difficult", "high complexity"]):
            return ExploitComplexity.HIGH
        else:
            return ExploitComplexity.MEDIUM

    def _determine_privileges_required(self, context: VulnerabilityContext) -> str:
        """Determine privileges required"""
        evidence_lower = context.evidence.lower()
        
        if "unauthenticated" in evidence_lower or "anonymous" in evidence_lower:
            return "none"
        elif "admin" in evidence_lower or "root" in evidence_lower:
            return "high"
        else:
            return "low"

    def _requires_user_interaction(self, context: VulnerabilityContext) -> bool:
        """Check if user interaction is required"""
        evidence_lower = context.evidence.lower()
        return any(term in evidence_lower for term in ["click", "interaction", "user action", "social engineering"])

    def _scope_changes(self, context: VulnerabilityContext) -> bool:
        """Check if scope changes during exploitation"""
        evidence_lower = context.evidence.lower()
        return any(term in evidence_lower for term in ["escalation", "lateral movement", "pivot"])

    def _calculate_confidentiality_impact(self, context: VulnerabilityContext) -> str:
        """Calculate confidentiality impact"""
        evidence_lower = context.evidence.lower()
        
        if any(term in evidence_lower for term in ["data breach", "information disclosure", "sensitive data"]):
            return "high"
        elif any(term in evidence_lower for term in ["partial disclosure", "limited access"]):
            return "low"
        else:
            return "none"

    def _calculate_integrity_impact(self, context: VulnerabilityContext) -> str:
        """Calculate integrity impact"""
        evidence_lower = context.evidence.lower()
        
        if any(term in evidence_lower for term in ["modify", "alter", "corrupt", "inject"]):
            return "high"
        elif any(term in evidence_lower for term in ["limited modification", "partial"]):
            return "low"
        else:
            return "none"

    def _calculate_availability_impact(self, context: VulnerabilityContext) -> str:
        """Calculate availability impact"""
        evidence_lower = context.evidence.lower()
        
        if any(term in evidence_lower for term in ["denial of service", "crash", "unavailable", "dos"]):
            return "high"
        elif any(term in evidence_lower for term in ["performance", "degradation", "slow"]):
            return "low"
        else:
            return "none"

    def _calculate_cvss_score(self, attack_vector, exploit_complexity, privileges_required,
                            user_interaction, scope_changed, confidentiality_impact,
                            integrity_impact, availability_impact) -> Tuple[str, float]:
        """Calculate CVSS v3.1 score and vector"""
        
        # Simplified CVSS calculation
        # In a real implementation, this would use the official CVSS algorithm
        
        vector_parts = []
        base_score = 0.0
        
        # Attack Vector
        av_scores = {AttackVector.NETWORK: 0.85, AttackVector.ADJACENT: 0.62, 
                    AttackVector.LOCAL: 0.55, AttackVector.PHYSICAL: 0.2}
        av_score = av_scores.get(attack_vector, 0.85)
        vector_parts.append(f"AV:{attack_vector.value[0].upper()}")
        
        # Attack Complexity
        ac_scores = {ExploitComplexity.LOW: 0.77, ExploitComplexity.MEDIUM: 0.62, ExploitComplexity.HIGH: 0.44}
        ac_score = ac_scores.get(exploit_complexity, 0.62)
        vector_parts.append(f"AC:{exploit_complexity.value[0].upper()}")
        
        # Calculate base score (simplified)
        base_score = (av_score + ac_score) * 5
        
        # Impact adjustments
        if confidentiality_impact == "high":
            base_score += 2.0
        elif confidentiality_impact == "low":
            base_score += 1.0
            
        if integrity_impact == "high":
            base_score += 2.0
        elif integrity_impact == "low":
            base_score += 1.0
            
        if availability_impact == "high":
            base_score += 2.0
        elif availability_impact == "low":
            base_score += 1.0
        
        cvss_vector = f"CVSS:3.1/{'/'.join(vector_parts)}"
        return cvss_vector, min(base_score, 10.0)

    def _assess_business_impact(self, context: VulnerabilityContext, impact_score: float) -> str:
        """Assess business impact"""
        if impact_score >= 8.0:
            return "Critical business impact - potential for significant financial loss and reputation damage"
        elif impact_score >= 6.0:
            return "High business impact - may affect operations and customer trust"
        elif impact_score >= 4.0:
            return "Medium business impact - limited operational disruption possible"
        else:
            return "Low business impact - minimal effect on business operations"

    def _assess_technical_impact(self, context: VulnerabilityContext, impact_score: float) -> str:
        """Assess technical impact"""
        if impact_score >= 8.0:
            return "Complete system compromise possible"
        elif impact_score >= 6.0:
            return "Significant system access and data exposure"
        elif impact_score >= 4.0:
            return "Limited system access or data exposure"
        else:
            return "Minimal technical impact"

    def _calculate_likelihood(self, exploitability_score: float, context: VulnerabilityContext) -> float:
        """Calculate exploitation likelihood"""
        base_likelihood = exploitability_score / 10.0
        
        # Adjust based on context
        if context.affected_service in ["web", "http", "https"]:
            base_likelihood += 0.2  # Web services are commonly targeted
        
        if context.port and context.port in [80, 443, 22, 21, 25]:
            base_likelihood += 0.1  # Common ports are frequently scanned
        
        return min(base_likelihood, 1.0)

    def _determine_risk_level(self, severity_score: float, likelihood: float) -> str:
        """Determine overall risk level"""
        risk_score = severity_score * likelihood
        
        if risk_score >= 8.0:
            return "critical"
        elif risk_score >= 6.0:
            return "high"
        elif risk_score >= 4.0:
            return "medium"
        else:
            return "low"

    def _generate_attack_scenarios(self, context: VulnerabilityContext, category: VulnerabilityCategory) -> List[str]:
        """Generate potential attack scenarios"""
        scenarios = []
        
        scenario_templates = {
            VulnerabilityCategory.INJECTION: [
                "Attacker injects malicious SQL commands to extract database contents",
                "Unauthorized data modification through injection attacks",
                "Command injection leading to system compromise"
            ],
            VulnerabilityCategory.CROSS_SITE_SCRIPTING: [
                "Session hijacking through malicious script injection",
                "Phishing attacks using reflected XSS",
                "Account takeover via stored XSS payload"
            ],
            VulnerabilityCategory.BROKEN_AUTHENTICATION: [
                "Brute force attacks against weak authentication",
                "Session fixation leading to account compromise",
                "Credential stuffing using leaked passwords"
            ]
        }
        
        if category in scenario_templates:
            scenarios = scenario_templates[category]
        else:
            scenarios = ["Generic exploitation of the identified vulnerability"]
        
        return scenarios

    def _generate_mitigation_strategies(self, context: VulnerabilityContext, category: VulnerabilityCategory) -> List[str]:
        """Generate mitigation strategies"""
        strategies = []
        
        mitigation_templates = {
            VulnerabilityCategory.INJECTION: [
                "Implement parameterized queries and prepared statements",
                "Apply input validation and sanitization",
                "Use least privilege database accounts",
                "Enable SQL injection detection systems"
            ],
            VulnerabilityCategory.CROSS_SITE_SCRIPTING: [
                "Implement Content Security Policy (CSP)",
                "Apply output encoding and validation",
                "Use XSS protection headers",
                "Sanitize user input on both client and server side"
            ],
            VulnerabilityCategory.BROKEN_AUTHENTICATION: [
                "Implement multi-factor authentication",
                "Use strong password policies",
                "Apply account lockout mechanisms",
                "Implement session management best practices"
            ]
        }
        
        if category in mitigation_templates:
            strategies = mitigation_templates[category]
        else:
            strategies = ["Apply security patches and updates", "Review security configuration"]
        
        return strategies

    def _generate_detection_methods(self, context: VulnerabilityContext, category: VulnerabilityCategory) -> List[str]:
        """Generate detection methods"""
        methods = []
        
        detection_templates = {
            VulnerabilityCategory.INJECTION: [
                "Monitor for SQL error messages in logs",
                "Implement database activity monitoring",
                "Use web application firewalls with injection detection",
                "Deploy intrusion detection systems"
            ],
            VulnerabilityCategory.CROSS_SITE_SCRIPTING: [
                "Monitor for script injection attempts",
                "Implement client-side security monitoring",
                "Use Content Security Policy violation reporting",
                "Deploy web application security scanners"
            ]
        }
        
        if category in detection_templates:
            methods = detection_templates[category]
        else:
            methods = ["Regular vulnerability scanning", "Security monitoring and logging"]
        
        return methods

    def _get_vulnerability_references(self, context: VulnerabilityContext, category: VulnerabilityCategory) -> List[str]:
        """Get relevant security references"""
        references = []
        
        # Add CWE references based on category
        cwe_map = {
            VulnerabilityCategory.INJECTION: ["CWE-89", "CWE-78", "CWE-94"],
            VulnerabilityCategory.CROSS_SITE_SCRIPTING: ["CWE-79", "CWE-80"],
            VulnerabilityCategory.BROKEN_AUTHENTICATION: ["CWE-287", "CWE-384", "CWE-306"]
        }
        
        if category in cwe_map:
            references.extend([f"https://cwe.mitre.org/data/definitions/{cwe.split('-')[1]}.html" 
                             for cwe in cwe_map[category]])
        
        # Add CVE reference if available
        if context.cve_id:
            references.append(f"https://cve.mitre.org/cgi-bin/cvename.cgi?name={context.cve_id}")
        
        # Add OWASP references
        references.append("https://owasp.org/Top10/")
        
        return references

    def _generate_vulnerability_tags(self, context: VulnerabilityContext, category: VulnerabilityCategory) -> List[str]:
        """Generate relevant tags"""
        tags = [category.value]
        
        # Add severity tag
        tags.append(f"severity_{context.severity.lower()}")
        
        # Add service tag
        if context.affected_service:
            tags.append(f"service_{context.affected_service}")
        
        # Add port tag if available
        if context.port:
            tags.append(f"port_{context.port}")
        
        return tags

    def _calculate_assessment_confidence(self, context: VulnerabilityContext) -> float:
        """Calculate confidence in the assessment"""
        base_confidence = 0.7
        
        # Increase confidence with more evidence
        if context.evidence:
            base_confidence += 0.1
        if context.tool_output:
            base_confidence += 0.1
        if context.cve_id:
            base_confidence += 0.1
        
        return min(base_confidence, 1.0)

    def _calculate_confidence(self, context: VulnerabilityContext, assessment: VulnerabilityAssessment) -> float:
        """Calculate overall confidence in analysis"""
        return assessment.confidence

    def _generate_recommendations(self, assessment: VulnerabilityAssessment, attack_paths: List[AttackPath]) -> List[str]:
        """Generate actionable recommendations"""
        recommendations = []
        
        # Priority recommendations based on risk level
        if assessment.risk_level == "critical":
            recommendations.append("IMMEDIATE ACTION REQUIRED: Address this critical vulnerability within 24 hours")
        elif assessment.risk_level == "high":
            recommendations.append("HIGH PRIORITY: Address this vulnerability within 72 hours")
        
        # Add mitigation strategies as recommendations
        recommendations.extend(assessment.mitigation_strategies[:3])  # Top 3 mitigations
        
        # Add attack path specific recommendations
        if attack_paths:
            recommendations.append("Implement network segmentation to limit attack path progression")
        
        return recommendations

    def _generate_insights(self, assessment: VulnerabilityAssessment, threat_landscape: ThreatLandscape) -> List[str]:
        """Generate analytical insights"""
        insights = []
        
        # Risk insights
        if assessment.exploitability_score > 8.0:
            insights.append("This vulnerability is highly exploitable and likely to be targeted")
        
        if assessment.attack_vector == AttackVector.NETWORK:
            insights.append("Network-accessible vulnerability increases exposure to remote attackers")
        
        # Threat landscape insights
        if "Ransomware" in threat_landscape.attack_trends:
            insights.append("Current threat landscape shows increased ransomware activity")
        
        return insights

    # Additional helper methods

    def _load_vulnerability_patterns(self) -> Dict[str, Any]:
        """Load vulnerability patterns database"""
        return {
            "sql_injection": ["sql", "union", "select", "drop", "insert"],
            "xss": ["script", "javascript", "onerror", "onclick"],
            "directory_traversal": ["../", "..\\", "path", "file"]
        }

    def _load_exploit_techniques(self) -> Dict[str, Any]:
        """Load exploit techniques database"""
        return {
            "injection": ["parameter_manipulation", "blind_injection", "union_based"],
            "xss": ["reflected", "stored", "dom_based"],
            "authentication": ["brute_force", "credential_stuffing", "session_hijacking"]
        }

    def _load_mitigation_database(self) -> Dict[str, Any]:
        """Load mitigation strategies database"""
        return {
            "injection": ["input_validation", "parameterized_queries", "least_privilege"],
            "xss": ["output_encoding", "csp", "input_sanitization"],
            "authentication": ["mfa", "strong_passwords", "session_management"]
        }

    def _update_assessment_statistics(self, assessment: VulnerabilityAssessment):
        """Update assessment statistics"""
        self.assessment_stats["total_assessments"] += 1
        
        if assessment.risk_level in ["high", "critical"]:
            self.assessment_stats["high_risk_found"] += 1

    def _cleanup_assessment_history(self):
        """Clean up old assessments from history"""
        max_history = 1000
        if len(self.assessment_history) > max_history:
            self.assessment_history = self.assessment_history[-max_history:]

    async def _find_attack_chains(self, vulnerabilities: List[VulnerabilityAssessment]) -> List[Dict[str, Any]]:
        """Find attack chains in vulnerabilities"""
        # Simplified attack chain detection
        chains = []
        
        for i, vuln1 in enumerate(vulnerabilities):
            for j, vuln2 in enumerate(vulnerabilities[i+1:], i+1):
                if self._can_chain_vulnerabilities(vuln1, vuln2):
                    chains.append({
                        "chain_id": f"chain_{vuln1.vulnerability_id}_{vuln2.vulnerability_id}",
                        "vulnerabilities": [vuln1.vulnerability_id, vuln2.vulnerability_id],
                        "risk_multiplier": 1.5,
                        "description": f"Exploitation of {vuln1.category.value} can lead to {vuln2.category.value}"
                    })
        
        return chains

    def _can_chain_vulnerabilities(self, vuln1: VulnerabilityAssessment, vuln2: VulnerabilityAssessment) -> bool:
        """Check if two vulnerabilities can be chained"""
        # Simple chaining logic
        chain_patterns = [
            (VulnerabilityCategory.INJECTION, VulnerabilityCategory.PRIVILEGE_ESCALATION),
            (VulnerabilityCategory.CROSS_SITE_SCRIPTING, VulnerabilityCategory.BROKEN_AUTHENTICATION),
            (VulnerabilityCategory.BROKEN_ACCESS_CONTROL, VulnerabilityCategory.SENSITIVE_DATA_EXPOSURE)
        ]
        
        return (vuln1.category, vuln2.category) in chain_patterns

    def _cluster_vulnerabilities(self, assessments: List[VulnerabilityAssessment]) -> List[Dict[str, Any]]:
        """Cluster similar vulnerabilities"""
        clusters = {}
        
        for assessment in assessments:
            cluster_key = f"{assessment.category.value}_{assessment.risk_level}"
            if cluster_key not in clusters:
                clusters[cluster_key] = {
                    "cluster_id": cluster_key,
                    "category": assessment.category.value,
                    "risk_level": assessment.risk_level,
                    "vulnerabilities": [],
                    "avg_severity": 0.0
                }
            clusters[cluster_key]["vulnerabilities"].append(assessment.vulnerability_id)
        
        # Calculate average severity for each cluster
        for cluster in clusters.values():
            cluster_assessments = [a for a in assessments if a.vulnerability_id in cluster["vulnerabilities"]]
            cluster["avg_severity"] = sum(a.severity_score for a in cluster_assessments) / len(cluster_assessments)
        
        return list(clusters.values())

    def _identify_compound_risks(self, assessments: List[VulnerabilityAssessment]) -> List[Dict[str, Any]]:
        """Identify compound risks from multiple vulnerabilities"""
        compound_risks = []
        
        # Group by host/target
        host_groups = {}
        for assessment in assessments:
            # In a real implementation, we'd extract host information
            host = "target_host"  # Placeholder
            if host not in host_groups:
                host_groups[host] = []
            host_groups[host].append(assessment)
        
        # Analyze each host for compound risks
        for host, vulns in host_groups.items():
            if len(vulns) > 1:
                total_risk = sum(v.severity_score for v in vulns)
                if total_risk > 15.0:  # Threshold for compound risk
                    compound_risks.append({
                        "risk_id": f"compound_{host}",
                        "host": host,
                        "vulnerability_count": len(vulns),
                        "combined_risk_score": total_risk,
                        "description": f"Multiple vulnerabilities on {host} create compound risk"
                    })
        
        return compound_risks

    def _calculate_priority_order(self, assessments: List[VulnerabilityAssessment]) -> List[str]:
        """Calculate priority order for vulnerability remediation"""
        # Sort by risk score (severity * likelihood)
        sorted_assessments = sorted(
            assessments,
            key=lambda a: a.severity_score * a.likelihood,
            reverse=True
        )
        
        return [a.vulnerability_id for a in sorted_assessments]

    async def _generate_advanced_attack_paths(self, context: VulnerabilityContext, assessment: VulnerabilityAssessment) -> List[AttackPath]:
        """Generate advanced attack paths for high-severity vulnerabilities"""
        advanced_paths = []
        
        # Multi-stage attack path
        if assessment.category == VulnerabilityCategory.INJECTION:
            advanced_path = AttackPath(
                path_id=f"path_{context.vulnerability_id}_advanced",
                attack_steps=[
                    "Initial reconnaissance and target identification",
                    "Exploit injection vulnerability for initial access",
                    "Enumerate database and system information",
                    "Escalate privileges using database functions",
                    "Establish persistence and lateral movement",
                    "Exfiltrate sensitive data"
                ],
                required_conditions=[
                    "Network connectivity to target",
                    "Vulnerable application endpoint",
                    "Database user with elevated privileges"
                ],
                exploited_vulnerabilities=[context.vulnerability_id],
                final_impact="Complete system compromise and data exfiltration",
                probability=assessment.likelihood * 0.7,  # Lower probability for advanced attacks
                complexity=ExploitComplexity.HIGH,
                mitigation_steps=[
                    "Implement defense in depth",
                    "Network segmentation",
                    "Database access controls",
                    "Monitoring and alerting"
                ]
            )
            advanced_paths.append(advanced_path)
        
        return advanced_paths

    def _generate_attack_steps(self, context: VulnerabilityContext, assessment: VulnerabilityAssessment) -> List[str]:
        """Generate attack steps for the vulnerability"""
        steps = [
            "Target reconnaissance and vulnerability discovery",
            f"Exploit {assessment.category.value} vulnerability",
            "Gain unauthorized access or information"
        ]
        
        if assessment.severity_score >= 7.0:
            steps.append("Attempt privilege escalation")
            steps.append("Establish persistence")
        
        return steps

    def _identify_required_conditions(self, context: VulnerabilityContext, assessment: VulnerabilityAssessment) -> List[str]:
        """Identify conditions required for exploitation"""
        conditions = ["Network access to target"]
        
        if assessment.privileges_required != "none":
            conditions.append(f"Authentication with {assessment.privileges_required} privileges")
        
        if assessment.user_interaction:
            conditions.append("User interaction required")
        
        if assessment.exploit_complexity == ExploitComplexity.HIGH:
            conditions.append("Advanced technical knowledge")
        
        return conditions

    async def _get_threat_intelligence_multiplier(self, assessment: VulnerabilityAssessment) -> float:
        """Get threat intelligence multiplier for likelihood calculation"""
        # In a real implementation, this would query threat intelligence feeds
        
        # Default multiplier
        multiplier = 1.0
        
        # Increase multiplier for commonly exploited vulnerability types
        high_risk_categories = [
            VulnerabilityCategory.INJECTION,
            VulnerabilityCategory.CROSS_SITE_SCRIPTING,
            VulnerabilityCategory.BROKEN_AUTHENTICATION
        ]
        
        if assessment.category in high_risk_categories:
            multiplier = 1.3
        
        # Increase multiplier for network-accessible vulnerabilities
        if assessment.attack_vector == AttackVector.NETWORK:
            multiplier *= 1.2
        
        return multiplier