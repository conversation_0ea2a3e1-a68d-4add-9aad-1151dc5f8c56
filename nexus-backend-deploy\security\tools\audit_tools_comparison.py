#!/usr/bin/env python3
"""
Tools Audit: Compare tools-based-frontend.md requirements with actual AWS backend
"""

import asyncio
import aiohttp
import json
from typing import Dict, List, Set

# AWS backend URL
AWS_URL = "http://ec2-3-89-91-209.compute-1.amazonaws.com:8000"

# Tools listed in tools-based-frontend.md (what frontend expects)
FRONTEND_EXPECTED_TOOLS = {
    "Core Security Scanners (3 tools)": [
        "nmap",
        "nuclei", 
        "sqlmap"
    ],
    
    "AI-Powered Tools (28+ tools)": [
        "creative_exploit_engine",
        "multi_stage_orchestrator", 
        "behavioral_analysis_engine",
        "adaptive_exploit_modifier",
        "evasion_technique_generator",
        "vulnerability_agent",
        "threat_intelligence_analyzer",
        "scan_recommendation_engine",
        "remediation_engine",
        "intelligent_payload_optimizer",
        "predictive_vulnerability_discovery",
        "automated_exploit_chaining",
        "realtime_threat_intelligence_correlator"
    ],
    
    "Custom Intelligence Tools (3 tools)": [
        "ai_reconnaissance_engine",
        "tech_fingerprinter", 
        "mitre_attack_mapper"
    ],
    
    "Compliance Testing (1 comprehensive)": [
        "automated_compliance_tester"
    ],
    
    "Utility Tools (7 tools)": [
        "universal_parser",
        "evidence_collector",
        "proxy_engine", 
        "metasploit_bridge",
        "credential_validator",
        "payload_encoder",
        "report_formatter"
    ],
    
    "Infrastructure Tools (8 tools)": [
        "performance_monitor",
        "resource_optimizer",
        "self_healing_manager",
        "environment_detector", 
        "execution_orchestrator",
        "hybrid_execution_engine",
        "tool_registry",
        "capability_mapper"
    ],
    
    "External Tool Integration (20+ tools)": [
        "gobuster",
        "dirb",
        "nikto", 
        "wpscan",
        "ffuf",
        "feroxbuster",
        "whatweb",
        "hashcat",
        "john",
        "enum4linux",
        "smbclient",
        "testssl",
        "sslyze",
        "metasploit",
        "searchsploit"
    ]
}


class ToolsAuditor:
    """Compare frontend requirements with backend reality"""
    
    def __init__(self):
        self.backend_tools = {}
        self.audit_results = {
            "summary": {
                "total_frontend_expected": 0,
                "total_backend_available": 0,
                "tools_present": 0,
                "tools_missing": 0,
                "tools_available": 0,
                "tools_unavailable": 0,
                "completion_rate": 0.0
            },
            "category_analysis": {},
            "missing_tools": [],
            "unavailable_tools": [],
            "available_tools": [],
            "unexpected_tools": []
        }
    
    async def run_audit(self):
        """Run comprehensive tools audit"""
        print("🔍 TOOLS AUDIT: Frontend Requirements vs Backend Reality")
        print("=" * 80)
        
        # Get backend tools
        await self._fetch_backend_tools()
        
        # Analyze requirements
        self._analyze_frontend_requirements()
        
        # Compare and generate report
        self._generate_audit_report()
    
    async def _fetch_backend_tools(self):
        """Fetch actual tools from backend"""
        print("📡 Fetching backend tools...")
        
        try:
            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=30)) as session:
                async with session.get(f"{AWS_URL}/api/tools/available") as response:
                    if response.status == 200:
                        data = await response.json()
                        if data.get("success"):
                            tools = data.get("data", [])
                            self.backend_tools = {tool["id"]: tool for tool in tools}
                            
                            total_backend = len(self.backend_tools)
                            available_backend = len([t for t in tools if t.get("status") == "available"])
                            
                            print(f"✅ Backend responded: {total_backend} tools found, {available_backend} available")
                        else:
                            print(f"❌ Backend error: {data.get('error')}")
                    else:
                        print(f"❌ Backend request failed: {response.status}")
        except Exception as e:
            print(f"❌ Failed to fetch backend tools: {e}")
    
    def _analyze_frontend_requirements(self):
        """Analyze frontend tool requirements"""
        print("\n📋 Analyzing frontend requirements...")
        
        total_expected = 0
        for category, tools in FRONTEND_EXPECTED_TOOLS.items():
            total_expected += len(tools)
        
        self.audit_results["summary"]["total_frontend_expected"] = total_expected
        self.audit_results["summary"]["total_backend_available"] = len(self.backend_tools)
        
        print(f"📊 Frontend expects: {total_expected} tools")
        print(f"📊 Backend provides: {len(self.backend_tools)} tools")
    
    def _generate_audit_report(self):
        """Generate comprehensive audit report"""
        print("\n" + "=" * 80)
        print("📊 TOOLS AUDIT RESULTS")
        print("=" * 80)
        
        # Track totals
        tools_present = 0
        tools_missing = 0
        tools_available = 0
        tools_unavailable = 0
        
        # Analyze each category
        for category, expected_tools in FRONTEND_EXPECTED_TOOLS.items():
            print(f"\n📂 {category}:")
            print("-" * 60)
            
            category_stats = {
                "expected": len(expected_tools),
                "present": 0,
                "available": 0,
                "missing": [],
                "unavailable": []
            }
            
            for tool_name in expected_tools:
                if tool_name in self.backend_tools:
                    tool_info = self.backend_tools[tool_name]
                    status = tool_info.get("status", "unknown")
                    version = tool_info.get("version", "unknown")
                    
                    tools_present += 1
                    category_stats["present"] += 1
                    
                    if status == "available":
                        print(f"   ✅ {tool_name} - Available (v{version})")
                        tools_available += 1
                        category_stats["available"] += 1
                        self.audit_results["available_tools"].append(tool_name)
                    else:
                        print(f"   ⚠️  {tool_name} - Present but {status}")
                        tools_unavailable += 1
                        category_stats["unavailable"].append(tool_name)
                        self.audit_results["unavailable_tools"].append(tool_name)
                else:
                    print(f"   ❌ {tool_name} - MISSING")
                    tools_missing += 1
                    category_stats["missing"].append(tool_name)
                    self.audit_results["missing_tools"].append(tool_name)
            
            # Category completion rate
            completion_rate = (category_stats["available"] / category_stats["expected"]) * 100
            print(f"\n   📈 Category Completion: {completion_rate:.1f}% ({category_stats['available']}/{category_stats['expected']})")
            
            self.audit_results["category_analysis"][category] = category_stats
        
        # Check for unexpected tools
        all_expected_tools = set()
        for tools in FRONTEND_EXPECTED_TOOLS.values():
            all_expected_tools.update(tools)
        
        unexpected = [tool_id for tool_id in self.backend_tools.keys() if tool_id not in all_expected_tools]
        if unexpected:
            print(f"\n🆕 Unexpected tools found in backend:")
            for tool in unexpected:
                tool_info = self.backend_tools[tool]
                status = tool_info.get("status", "unknown")
                print(f"   + {tool} ({status})")
            self.audit_results["unexpected_tools"] = unexpected
        
        # Update summary
        total_expected = self.audit_results["summary"]["total_frontend_expected"]
        completion_rate = (tools_available / total_expected) * 100 if total_expected > 0 else 0
        
        self.audit_results["summary"].update({
            "tools_present": tools_present,
            "tools_missing": tools_missing,
            "tools_available": tools_available,
            "tools_unavailable": tools_unavailable,
            "completion_rate": completion_rate
        })
        
        # Overall summary
        print("\n" + "=" * 80)
        print("🎯 OVERALL AUDIT SUMMARY")
        print("=" * 80)
        print(f"📊 Frontend expects: {total_expected} tools")
        print(f"📊 Backend has: {len(self.backend_tools)} tools")
        print(f"✅ Tools present: {tools_present}")
        print(f"❌ Tools missing: {tools_missing}")
        print(f"🟢 Tools available: {tools_available}")
        print(f"🟡 Tools unavailable: {tools_unavailable}")
        print(f"📈 Completion rate: {completion_rate:.1f}%")
        
        # Status assessment
        print(f"\n🏆 Platform Status:")
        if completion_rate >= 80:
            print("   🎉 EXCELLENT - Ready for frontend development!")
            print("   ✅ Most tools available, platform is production-ready")
        elif completion_rate >= 60:
            print("   ⚠️  GOOD - Most core tools available, some advanced features missing")
            print("   ✅ Core security functionality ready")
        elif completion_rate >= 40:
            print("   ❌ NEEDS WORK - Many core tools missing")
            print("   🔧 Install missing tools before frontend development")
        else:
            print("   🚨 CRITICAL - Major tools missing")
            print("   🔧 Significant backend work needed")
        
        # Missing tools priority
        if self.audit_results["missing_tools"]:
            print(f"\n🔧 MISSING TOOLS TO INSTALL ({len(self.audit_results['missing_tools'])}):")
            print("-" * 60)
            
            # Group by category for easier installation
            for category, expected_tools in FRONTEND_EXPECTED_TOOLS.items():
                category_missing = [tool for tool in expected_tools if tool in self.audit_results["missing_tools"]]
                if category_missing:
                    print(f"\n📂 {category}:")
                    for tool in category_missing:
                        print(f"   - {tool}")
        
        # Installation recommendations
        print(f"\n📝 INSTALLATION RECOMMENDATIONS:")
        print("-" * 60)
        
        if "dirb" in self.audit_results["missing_tools"]:
            print("• Install dirb: sudo apt install dirb")
        if "wpscan" in self.audit_results["missing_tools"]:
            print("• Install wpscan: gem install wpscan")
        if "ffuf" in self.audit_results["missing_tools"]:
            print("• Install ffuf: go install github.com/ffuf/ffuf@latest")
        if "testssl" in self.audit_results["missing_tools"]:
            print("• Install testssl: sudo apt install testssl.sh")
        
        # AI tools
        ai_missing = [tool for tool in self.audit_results["missing_tools"] if "ai_" in tool or "_engine" in tool]
        if ai_missing:
            print("• AI tools missing - these need backend code implementation")
        
        # Save detailed results
        with open("tools_audit_results.json", "w") as f:
            json.dump(self.audit_results, f, indent=2)
        
        print(f"\n💾 Detailed audit results saved to: tools_audit_results.json")


async def main():
    """Main audit function"""
    auditor = ToolsAuditor()
    await auditor.run_audit()


if __name__ == "__main__":
    asyncio.run(main())