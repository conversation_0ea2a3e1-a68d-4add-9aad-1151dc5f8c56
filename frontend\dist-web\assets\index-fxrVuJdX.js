var Ua=Object.defineProperty;var Ha=(t,s,a)=>s in t?Ua(t,s,{enumerable:!0,configurable:!0,writable:!0,value:a}):t[s]=a;var ge=(t,s,a)=>Ha(t,typeof s!="symbol"?s+"":s,a);import{a as Va,b as qa,g as Ga,R as x,r as K,v as Qa,L as Xs,u as dt,c as ra,d as mt,e as te,N as Ja,H as Ka}from"./react-D5Q8GhuV.js";import{t as Ya,c as ia,B as ts,a as Xa,N as Oe,G as bs,S as fe,L as Za,b as Xe,T as ue,C as en,F as xe,d as Y,e as ut,f as la,X as Zs,M as sn,g as Se,H as tn,h as ca,i as We,j as be,W as an,k as nn,A as ee,l as as,m as ve,n as je,D as ke,o as rn,p as ln,q as Ve,r as He,P as Rs,s as Fe,u as oa,v as et,w as ae,x as cn,y as Ns,z as Fs,E as St,I as da,U as on,J as Ct,K as dn,O as fs,Q as mn,R as ys,V as qe,Y as un,Z as Ge,_ as ma,$ as hn,a0 as Ls,a1 as xn,a2 as ua,a3 as pn,a4 as fn,a5 as gn,a6 as jn,a7 as Tt,a8 as vn,a9 as bn,aa as st,ab as Es,ac as At,ad as ht,ae as Nn,af as yn,ag as wn,ah as Rt,ai as kn,aj as Sn,ak as Cn}from"./ui-PpgiV5M2.js";import{c as ha}from"./state-fRPoTWzM.js";(function(){const s=document.createElement("link").relList;if(s&&s.supports&&s.supports("modulepreload"))return;for(const r of document.querySelectorAll('link[rel="modulepreload"]'))n(r);new MutationObserver(r=>{for(const c of r)if(c.type==="childList")for(const p of c.addedNodes)p.tagName==="LINK"&&p.rel==="modulepreload"&&n(p)}).observe(document,{childList:!0,subtree:!0});function a(r){const c={};return r.integrity&&(c.integrity=r.integrity),r.referrerPolicy&&(c.referrerPolicy=r.referrerPolicy),r.crossOrigin==="use-credentials"?c.credentials="include":r.crossOrigin==="anonymous"?c.credentials="omit":c.credentials="same-origin",c}function n(r){if(r.ep)return;r.ep=!0;const c=a(r);fetch(r.href,c)}})();var Gs={exports:{}},ps={};/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Et;function Tn(){if(Et)return ps;Et=1;var t=Va(),s=Symbol.for("react.element"),a=Symbol.for("react.fragment"),n=Object.prototype.hasOwnProperty,r=t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,c={key:!0,ref:!0,__self:!0,__source:!0};function p(d,g,b){var j,R={},E=null,f=null;b!==void 0&&(E=""+b),g.key!==void 0&&(E=""+g.key),g.ref!==void 0&&(f=g.ref);for(j in g)n.call(g,j)&&!c.hasOwnProperty(j)&&(R[j]=g[j]);if(d&&d.defaultProps)for(j in g=d.defaultProps,g)R[j]===void 0&&(R[j]=g[j]);return{$$typeof:s,type:d,key:E,ref:f,props:R,_owner:r.current}}return ps.Fragment=a,ps.jsx=p,ps.jsxs=p,ps}var Pt;function An(){return Pt||(Pt=1,Gs.exports=Tn()),Gs.exports}var e=An(),Cs={},It;function Rn(){if(It)return Cs;It=1;var t=qa();return Cs.createRoot=t.createRoot,Cs.hydrateRoot=t.hydrateRoot,Cs}var En=Rn();const Pn=Ga(En);var In=t=>{switch(t){case"success":return Ln;case"info":return $n;case"warning":return Dn;case"error":return Mn;default:return null}},On=Array(12).fill(0),Fn=({visible:t,className:s})=>x.createElement("div",{className:["sonner-loading-wrapper",s].filter(Boolean).join(" "),"data-visible":t},x.createElement("div",{className:"sonner-spinner"},On.map((a,n)=>x.createElement("div",{className:"sonner-loading-bar",key:`spinner-bar-${n}`})))),Ln=x.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},x.createElement("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 10-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z",clipRule:"evenodd"})),Dn=x.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor",height:"20",width:"20"},x.createElement("path",{fillRule:"evenodd",d:"M9.401 3.003c1.155-2 4.043-2 5.197 0l7.355 12.748c1.154 2-.29 4.5-2.599 4.5H4.645c-2.309 0-3.752-2.5-2.598-4.5L9.4 3.003zM12 8.25a.75.75 0 01.75.75v3.75a.75.75 0 01-1.5 0V9a.75.75 0 01.75-.75zm0 8.25a.75.75 0 100-********* 0 000 1.5z",clipRule:"evenodd"})),$n=x.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},x.createElement("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a.75.75 0 000 1.5h.253a.25.25 0 01.244.304l-.459 2.066A1.75 1.75 0 0010.747 15H11a.75.75 0 000-1.5h-.253a.25.25 0 01-.244-.304l.459-2.066A1.75 1.75 0 009.253 9H9z",clipRule:"evenodd"})),Mn=x.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},x.createElement("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z",clipRule:"evenodd"})),_n=x.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:"12",height:"12",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"},x.createElement("line",{x1:"18",y1:"6",x2:"6",y2:"18"}),x.createElement("line",{x1:"6",y1:"6",x2:"18",y2:"18"})),zn=()=>{let[t,s]=x.useState(document.hidden);return x.useEffect(()=>{let a=()=>{s(document.hidden)};return document.addEventListener("visibilitychange",a),()=>window.removeEventListener("visibilitychange",a)},[]),t},tt=1,Bn=class{constructor(){this.subscribe=t=>(this.subscribers.push(t),()=>{let s=this.subscribers.indexOf(t);this.subscribers.splice(s,1)}),this.publish=t=>{this.subscribers.forEach(s=>s(t))},this.addToast=t=>{this.publish(t),this.toasts=[...this.toasts,t]},this.create=t=>{var s;let{message:a,...n}=t,r=typeof t?.id=="number"||((s=t.id)==null?void 0:s.length)>0?t.id:tt++,c=this.toasts.find(d=>d.id===r),p=t.dismissible===void 0?!0:t.dismissible;return this.dismissedToasts.has(r)&&this.dismissedToasts.delete(r),c?this.toasts=this.toasts.map(d=>d.id===r?(this.publish({...d,...t,id:r,title:a}),{...d,...t,id:r,dismissible:p,title:a}):d):this.addToast({title:a,...n,dismissible:p,id:r}),r},this.dismiss=t=>(this.dismissedToasts.add(t),t||this.toasts.forEach(s=>{this.subscribers.forEach(a=>a({id:s.id,dismiss:!0}))}),this.subscribers.forEach(s=>s({id:t,dismiss:!0})),t),this.message=(t,s)=>this.create({...s,message:t}),this.error=(t,s)=>this.create({...s,message:t,type:"error"}),this.success=(t,s)=>this.create({...s,type:"success",message:t}),this.info=(t,s)=>this.create({...s,type:"info",message:t}),this.warning=(t,s)=>this.create({...s,type:"warning",message:t}),this.loading=(t,s)=>this.create({...s,type:"loading",message:t}),this.promise=(t,s)=>{if(!s)return;let a;s.loading!==void 0&&(a=this.create({...s,promise:t,type:"loading",message:s.loading,description:typeof s.description!="function"?s.description:void 0}));let n=t instanceof Promise?t:t(),r=a!==void 0,c,p=n.then(async g=>{if(c=["resolve",g],x.isValidElement(g))r=!1,this.create({id:a,type:"default",message:g});else if(Un(g)&&!g.ok){r=!1;let b=typeof s.error=="function"?await s.error(`HTTP error! status: ${g.status}`):s.error,j=typeof s.description=="function"?await s.description(`HTTP error! status: ${g.status}`):s.description;this.create({id:a,type:"error",message:b,description:j})}else if(s.success!==void 0){r=!1;let b=typeof s.success=="function"?await s.success(g):s.success,j=typeof s.description=="function"?await s.description(g):s.description;this.create({id:a,type:"success",message:b,description:j})}}).catch(async g=>{if(c=["reject",g],s.error!==void 0){r=!1;let b=typeof s.error=="function"?await s.error(g):s.error,j=typeof s.description=="function"?await s.description(g):s.description;this.create({id:a,type:"error",message:b,description:j})}}).finally(()=>{var g;r&&(this.dismiss(a),a=void 0),(g=s.finally)==null||g.call(s)}),d=()=>new Promise((g,b)=>p.then(()=>c[0]==="reject"?b(c[1]):g(c[1])).catch(b));return typeof a!="string"&&typeof a!="number"?{unwrap:d}:Object.assign(a,{unwrap:d})},this.custom=(t,s)=>{let a=s?.id||tt++;return this.create({jsx:t(a),id:a,...s}),a},this.getActiveToasts=()=>this.toasts.filter(t=>!this.dismissedToasts.has(t.id)),this.subscribers=[],this.toasts=[],this.dismissedToasts=new Set}},Te=new Bn,Wn=(t,s)=>{let a=s?.id||tt++;return Te.addToast({title:t,...s,id:a}),a},Un=t=>t&&typeof t=="object"&&"ok"in t&&typeof t.ok=="boolean"&&"status"in t&&typeof t.status=="number",Hn=Wn,Vn=()=>Te.toasts,qn=()=>Te.getActiveToasts(),C=Object.assign(Hn,{success:Te.success,info:Te.info,warning:Te.warning,error:Te.error,custom:Te.custom,message:Te.message,promise:Te.promise,dismiss:Te.dismiss,loading:Te.loading},{getHistory:Vn,getToasts:qn});function Gn(t,{insertAt:s}={}){if(typeof document>"u")return;let a=document.head||document.getElementsByTagName("head")[0],n=document.createElement("style");n.type="text/css",s==="top"&&a.firstChild?a.insertBefore(n,a.firstChild):a.appendChild(n),n.styleSheet?n.styleSheet.cssText=t:n.appendChild(document.createTextNode(t))}Gn(`:where(html[dir="ltr"]),:where([data-sonner-toaster][dir="ltr"]){--toast-icon-margin-start: -3px;--toast-icon-margin-end: 4px;--toast-svg-margin-start: -1px;--toast-svg-margin-end: 0px;--toast-button-margin-start: auto;--toast-button-margin-end: 0;--toast-close-button-start: 0;--toast-close-button-end: unset;--toast-close-button-transform: translate(-35%, -35%)}:where(html[dir="rtl"]),:where([data-sonner-toaster][dir="rtl"]){--toast-icon-margin-start: 4px;--toast-icon-margin-end: -3px;--toast-svg-margin-start: 0px;--toast-svg-margin-end: -1px;--toast-button-margin-start: 0;--toast-button-margin-end: auto;--toast-close-button-start: unset;--toast-close-button-end: 0;--toast-close-button-transform: translate(35%, -35%)}:where([data-sonner-toaster]){position:fixed;width:var(--width);font-family:ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji;--gray1: hsl(0, 0%, 99%);--gray2: hsl(0, 0%, 97.3%);--gray3: hsl(0, 0%, 95.1%);--gray4: hsl(0, 0%, 93%);--gray5: hsl(0, 0%, 90.9%);--gray6: hsl(0, 0%, 88.7%);--gray7: hsl(0, 0%, 85.8%);--gray8: hsl(0, 0%, 78%);--gray9: hsl(0, 0%, 56.1%);--gray10: hsl(0, 0%, 52.3%);--gray11: hsl(0, 0%, 43.5%);--gray12: hsl(0, 0%, 9%);--border-radius: 8px;box-sizing:border-box;padding:0;margin:0;list-style:none;outline:none;z-index:999999999;transition:transform .4s ease}:where([data-sonner-toaster][data-lifted="true"]){transform:translateY(-10px)}@media (hover: none) and (pointer: coarse){:where([data-sonner-toaster][data-lifted="true"]){transform:none}}:where([data-sonner-toaster][data-x-position="right"]){right:var(--offset-right)}:where([data-sonner-toaster][data-x-position="left"]){left:var(--offset-left)}:where([data-sonner-toaster][data-x-position="center"]){left:50%;transform:translate(-50%)}:where([data-sonner-toaster][data-y-position="top"]){top:var(--offset-top)}:where([data-sonner-toaster][data-y-position="bottom"]){bottom:var(--offset-bottom)}:where([data-sonner-toast]){--y: translateY(100%);--lift-amount: calc(var(--lift) * var(--gap));z-index:var(--z-index);position:absolute;opacity:0;transform:var(--y);filter:blur(0);touch-action:none;transition:transform .4s,opacity .4s,height .4s,box-shadow .2s;box-sizing:border-box;outline:none;overflow-wrap:anywhere}:where([data-sonner-toast][data-styled="true"]){padding:16px;background:var(--normal-bg);border:1px solid var(--normal-border);color:var(--normal-text);border-radius:var(--border-radius);box-shadow:0 4px 12px #0000001a;width:var(--width);font-size:13px;display:flex;align-items:center;gap:6px}:where([data-sonner-toast]:focus-visible){box-shadow:0 4px 12px #0000001a,0 0 0 2px #0003}:where([data-sonner-toast][data-y-position="top"]){top:0;--y: translateY(-100%);--lift: 1;--lift-amount: calc(1 * var(--gap))}:where([data-sonner-toast][data-y-position="bottom"]){bottom:0;--y: translateY(100%);--lift: -1;--lift-amount: calc(var(--lift) * var(--gap))}:where([data-sonner-toast]) :where([data-description]){font-weight:400;line-height:1.4;color:inherit}:where([data-sonner-toast]) :where([data-title]){font-weight:500;line-height:1.5;color:inherit}:where([data-sonner-toast]) :where([data-icon]){display:flex;height:16px;width:16px;position:relative;justify-content:flex-start;align-items:center;flex-shrink:0;margin-left:var(--toast-icon-margin-start);margin-right:var(--toast-icon-margin-end)}:where([data-sonner-toast][data-promise="true"]) :where([data-icon])>svg{opacity:0;transform:scale(.8);transform-origin:center;animation:sonner-fade-in .3s ease forwards}:where([data-sonner-toast]) :where([data-icon])>*{flex-shrink:0}:where([data-sonner-toast]) :where([data-icon]) svg{margin-left:var(--toast-svg-margin-start);margin-right:var(--toast-svg-margin-end)}:where([data-sonner-toast]) :where([data-content]){display:flex;flex-direction:column;gap:2px}[data-sonner-toast][data-styled=true] [data-button]{border-radius:4px;padding-left:8px;padding-right:8px;height:24px;font-size:12px;color:var(--normal-bg);background:var(--normal-text);margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end);border:none;cursor:pointer;outline:none;display:flex;align-items:center;flex-shrink:0;transition:opacity .4s,box-shadow .2s}:where([data-sonner-toast]) :where([data-button]):focus-visible{box-shadow:0 0 0 2px #0006}:where([data-sonner-toast]) :where([data-button]):first-of-type{margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end)}:where([data-sonner-toast]) :where([data-cancel]){color:var(--normal-text);background:rgba(0,0,0,.08)}:where([data-sonner-toast][data-theme="dark"]) :where([data-cancel]){background:rgba(255,255,255,.3)}:where([data-sonner-toast]) :where([data-close-button]){position:absolute;left:var(--toast-close-button-start);right:var(--toast-close-button-end);top:0;height:20px;width:20px;display:flex;justify-content:center;align-items:center;padding:0;color:var(--gray12);border:1px solid var(--gray4);transform:var(--toast-close-button-transform);border-radius:50%;cursor:pointer;z-index:1;transition:opacity .1s,background .2s,border-color .2s}[data-sonner-toast] [data-close-button]{background:var(--gray1)}:where([data-sonner-toast]) :where([data-close-button]):focus-visible{box-shadow:0 4px 12px #0000001a,0 0 0 2px #0003}:where([data-sonner-toast]) :where([data-disabled="true"]){cursor:not-allowed}:where([data-sonner-toast]):hover :where([data-close-button]):hover{background:var(--gray2);border-color:var(--gray5)}:where([data-sonner-toast][data-swiping="true"]):before{content:"";position:absolute;left:-50%;right:-50%;height:100%;z-index:-1}:where([data-sonner-toast][data-y-position="top"][data-swiping="true"]):before{bottom:50%;transform:scaleY(3) translateY(50%)}:where([data-sonner-toast][data-y-position="bottom"][data-swiping="true"]):before{top:50%;transform:scaleY(3) translateY(-50%)}:where([data-sonner-toast][data-swiping="false"][data-removed="true"]):before{content:"";position:absolute;inset:0;transform:scaleY(2)}:where([data-sonner-toast]):after{content:"";position:absolute;left:0;height:calc(var(--gap) + 1px);bottom:100%;width:100%}:where([data-sonner-toast][data-mounted="true"]){--y: translateY(0);opacity:1}:where([data-sonner-toast][data-expanded="false"][data-front="false"]){--scale: var(--toasts-before) * .05 + 1;--y: translateY(calc(var(--lift-amount) * var(--toasts-before))) scale(calc(-1 * var(--scale)));height:var(--front-toast-height)}:where([data-sonner-toast])>*{transition:opacity .4s}:where([data-sonner-toast][data-expanded="false"][data-front="false"][data-styled="true"])>*{opacity:0}:where([data-sonner-toast][data-visible="false"]){opacity:0;pointer-events:none}:where([data-sonner-toast][data-mounted="true"][data-expanded="true"]){--y: translateY(calc(var(--lift) * var(--offset)));height:var(--initial-height)}:where([data-sonner-toast][data-removed="true"][data-front="true"][data-swipe-out="false"]){--y: translateY(calc(var(--lift) * -100%));opacity:0}:where([data-sonner-toast][data-removed="true"][data-front="false"][data-swipe-out="false"][data-expanded="true"]){--y: translateY(calc(var(--lift) * var(--offset) + var(--lift) * -100%));opacity:0}:where([data-sonner-toast][data-removed="true"][data-front="false"][data-swipe-out="false"][data-expanded="false"]){--y: translateY(40%);opacity:0;transition:transform .5s,opacity .2s}:where([data-sonner-toast][data-removed="true"][data-front="false"]):before{height:calc(var(--initial-height) + 20%)}[data-sonner-toast][data-swiping=true]{transform:var(--y) translateY(var(--swipe-amount-y, 0px)) translate(var(--swipe-amount-x, 0px));transition:none}[data-sonner-toast][data-swiped=true]{user-select:none}[data-sonner-toast][data-swipe-out=true][data-y-position=bottom],[data-sonner-toast][data-swipe-out=true][data-y-position=top]{animation-duration:.2s;animation-timing-function:ease-out;animation-fill-mode:forwards}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=left]{animation-name:swipe-out-left}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=right]{animation-name:swipe-out-right}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=up]{animation-name:swipe-out-up}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=down]{animation-name:swipe-out-down}@keyframes swipe-out-left{0%{transform:var(--y) translate(var(--swipe-amount-x));opacity:1}to{transform:var(--y) translate(calc(var(--swipe-amount-x) - 100%));opacity:0}}@keyframes swipe-out-right{0%{transform:var(--y) translate(var(--swipe-amount-x));opacity:1}to{transform:var(--y) translate(calc(var(--swipe-amount-x) + 100%));opacity:0}}@keyframes swipe-out-up{0%{transform:var(--y) translateY(var(--swipe-amount-y));opacity:1}to{transform:var(--y) translateY(calc(var(--swipe-amount-y) - 100%));opacity:0}}@keyframes swipe-out-down{0%{transform:var(--y) translateY(var(--swipe-amount-y));opacity:1}to{transform:var(--y) translateY(calc(var(--swipe-amount-y) + 100%));opacity:0}}@media (max-width: 600px){[data-sonner-toaster]{position:fixed;right:var(--mobile-offset-right);left:var(--mobile-offset-left);width:100%}[data-sonner-toaster][dir=rtl]{left:calc(var(--mobile-offset-left) * -1)}[data-sonner-toaster] [data-sonner-toast]{left:0;right:0;width:calc(100% - var(--mobile-offset-left) * 2)}[data-sonner-toaster][data-x-position=left]{left:var(--mobile-offset-left)}[data-sonner-toaster][data-y-position=bottom]{bottom:var(--mobile-offset-bottom)}[data-sonner-toaster][data-y-position=top]{top:var(--mobile-offset-top)}[data-sonner-toaster][data-x-position=center]{left:var(--mobile-offset-left);right:var(--mobile-offset-right);transform:none}}[data-sonner-toaster][data-theme=light]{--normal-bg: #fff;--normal-border: var(--gray4);--normal-text: var(--gray12);--success-bg: hsl(143, 85%, 96%);--success-border: hsl(145, 92%, 91%);--success-text: hsl(140, 100%, 27%);--info-bg: hsl(208, 100%, 97%);--info-border: hsl(221, 91%, 91%);--info-text: hsl(210, 92%, 45%);--warning-bg: hsl(49, 100%, 97%);--warning-border: hsl(49, 91%, 91%);--warning-text: hsl(31, 92%, 45%);--error-bg: hsl(359, 100%, 97%);--error-border: hsl(359, 100%, 94%);--error-text: hsl(360, 100%, 45%)}[data-sonner-toaster][data-theme=light] [data-sonner-toast][data-invert=true]{--normal-bg: #000;--normal-border: hsl(0, 0%, 20%);--normal-text: var(--gray1)}[data-sonner-toaster][data-theme=dark] [data-sonner-toast][data-invert=true]{--normal-bg: #fff;--normal-border: var(--gray3);--normal-text: var(--gray12)}[data-sonner-toaster][data-theme=dark]{--normal-bg: #000;--normal-bg-hover: hsl(0, 0%, 12%);--normal-border: hsl(0, 0%, 20%);--normal-border-hover: hsl(0, 0%, 25%);--normal-text: var(--gray1);--success-bg: hsl(150, 100%, 6%);--success-border: hsl(147, 100%, 12%);--success-text: hsl(150, 86%, 65%);--info-bg: hsl(215, 100%, 6%);--info-border: hsl(223, 100%, 12%);--info-text: hsl(216, 87%, 65%);--warning-bg: hsl(64, 100%, 6%);--warning-border: hsl(60, 100%, 12%);--warning-text: hsl(46, 87%, 65%);--error-bg: hsl(358, 76%, 10%);--error-border: hsl(357, 89%, 16%);--error-text: hsl(358, 100%, 81%)}[data-sonner-toaster][data-theme=dark] [data-sonner-toast] [data-close-button]{background:var(--normal-bg);border-color:var(--normal-border);color:var(--normal-text)}[data-sonner-toaster][data-theme=dark] [data-sonner-toast] [data-close-button]:hover{background:var(--normal-bg-hover);border-color:var(--normal-border-hover)}[data-rich-colors=true][data-sonner-toast][data-type=success],[data-rich-colors=true][data-sonner-toast][data-type=success] [data-close-button]{background:var(--success-bg);border-color:var(--success-border);color:var(--success-text)}[data-rich-colors=true][data-sonner-toast][data-type=info],[data-rich-colors=true][data-sonner-toast][data-type=info] [data-close-button]{background:var(--info-bg);border-color:var(--info-border);color:var(--info-text)}[data-rich-colors=true][data-sonner-toast][data-type=warning],[data-rich-colors=true][data-sonner-toast][data-type=warning] [data-close-button]{background:var(--warning-bg);border-color:var(--warning-border);color:var(--warning-text)}[data-rich-colors=true][data-sonner-toast][data-type=error],[data-rich-colors=true][data-sonner-toast][data-type=error] [data-close-button]{background:var(--error-bg);border-color:var(--error-border);color:var(--error-text)}.sonner-loading-wrapper{--size: 16px;height:var(--size);width:var(--size);position:absolute;inset:0;z-index:10}.sonner-loading-wrapper[data-visible=false]{transform-origin:center;animation:sonner-fade-out .2s ease forwards}.sonner-spinner{position:relative;top:50%;left:50%;height:var(--size);width:var(--size)}.sonner-loading-bar{animation:sonner-spin 1.2s linear infinite;background:var(--gray11);border-radius:6px;height:8%;left:-10%;position:absolute;top:-3.9%;width:24%}.sonner-loading-bar:nth-child(1){animation-delay:-1.2s;transform:rotate(.0001deg) translate(146%)}.sonner-loading-bar:nth-child(2){animation-delay:-1.1s;transform:rotate(30deg) translate(146%)}.sonner-loading-bar:nth-child(3){animation-delay:-1s;transform:rotate(60deg) translate(146%)}.sonner-loading-bar:nth-child(4){animation-delay:-.9s;transform:rotate(90deg) translate(146%)}.sonner-loading-bar:nth-child(5){animation-delay:-.8s;transform:rotate(120deg) translate(146%)}.sonner-loading-bar:nth-child(6){animation-delay:-.7s;transform:rotate(150deg) translate(146%)}.sonner-loading-bar:nth-child(7){animation-delay:-.6s;transform:rotate(180deg) translate(146%)}.sonner-loading-bar:nth-child(8){animation-delay:-.5s;transform:rotate(210deg) translate(146%)}.sonner-loading-bar:nth-child(9){animation-delay:-.4s;transform:rotate(240deg) translate(146%)}.sonner-loading-bar:nth-child(10){animation-delay:-.3s;transform:rotate(270deg) translate(146%)}.sonner-loading-bar:nth-child(11){animation-delay:-.2s;transform:rotate(300deg) translate(146%)}.sonner-loading-bar:nth-child(12){animation-delay:-.1s;transform:rotate(330deg) translate(146%)}@keyframes sonner-fade-in{0%{opacity:0;transform:scale(.8)}to{opacity:1;transform:scale(1)}}@keyframes sonner-fade-out{0%{opacity:1;transform:scale(1)}to{opacity:0;transform:scale(.8)}}@keyframes sonner-spin{0%{opacity:1}to{opacity:.15}}@media (prefers-reduced-motion){[data-sonner-toast],[data-sonner-toast]>*,.sonner-loading-bar{transition:none!important;animation:none!important}}.sonner-loader{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);transform-origin:center;transition:opacity .2s,transform .2s}.sonner-loader[data-visible=false]{opacity:0;transform:scale(.8) translate(-50%,-50%)}
`);function Ts(t){return t.label!==void 0}var Qn=3,Jn="32px",Kn="16px",Ot=4e3,Yn=356,Xn=14,Zn=20,er=200;function _e(...t){return t.filter(Boolean).join(" ")}function sr(t){let[s,a]=t.split("-"),n=[];return s&&n.push(s),a&&n.push(a),n}var tr=t=>{var s,a,n,r,c,p,d,g,b,j,R;let{invert:E,toast:f,unstyled:L,interacting:i,setHeights:u,visibleToasts:k,heights:N,index:D,toasts:W,expanded:U,removeToast:S,defaultRichColors:I,closeButton:q,style:z,cancelButtonStyle:X,actionButtonStyle:m,className:o="",descriptionClassName:l="",duration:h,position:O,gap:M,loadingIcon:H,expandByDefault:le,classNames:G,icons:ye,closeButtonAriaLabel:Bs="Close toast",pauseWhenPageIsHidden:se}=t,[re,ce]=x.useState(null),[Ce,is]=x.useState(null),[he,Ws]=x.useState(!1),[us,ks]=x.useState(!1),[hs,Us]=x.useState(!1),[gt,Ea]=x.useState(!1),[Pa,jt]=x.useState(!1),[Ia,Hs]=x.useState(0),[Oa,vt]=x.useState(0),xs=x.useRef(f.duration||h||Ot),bt=x.useRef(null),Ze=x.useRef(null),Fa=D===0,La=D+1<=k,Ie=f.type,ls=f.dismissible!==!1,Da=f.className||"",$a=f.descriptionClassName||"",Ss=x.useMemo(()=>N.findIndex(V=>V.toastId===f.id)||0,[N,f.id]),Ma=x.useMemo(()=>{var V;return(V=f.closeButton)!=null?V:q},[f.closeButton,q]),Nt=x.useMemo(()=>f.duration||h||Ot,[f.duration,h]),Vs=x.useRef(0),cs=x.useRef(0),yt=x.useRef(0),os=x.useRef(null),[_a,za]=O.split("-"),wt=x.useMemo(()=>N.reduce((V,Z,ie)=>ie>=Ss?V:V+Z.height,0),[N,Ss]),kt=zn(),Ba=f.invert||E,qs=Ie==="loading";cs.current=x.useMemo(()=>Ss*M+wt,[Ss,wt]),x.useEffect(()=>{xs.current=Nt},[Nt]),x.useEffect(()=>{Ws(!0)},[]),x.useEffect(()=>{let V=Ze.current;if(V){let Z=V.getBoundingClientRect().height;return vt(Z),u(ie=>[{toastId:f.id,height:Z,position:f.position},...ie]),()=>u(ie=>ie.filter(De=>De.toastId!==f.id))}},[u,f.id]),x.useLayoutEffect(()=>{if(!he)return;let V=Ze.current,Z=V.style.height;V.style.height="auto";let ie=V.getBoundingClientRect().height;V.style.height=Z,vt(ie),u(De=>De.find($e=>$e.toastId===f.id)?De.map($e=>$e.toastId===f.id?{...$e,height:ie}:$e):[{toastId:f.id,height:ie,position:f.position},...De])},[he,f.title,f.description,u,f.id]);let Qe=x.useCallback(()=>{ks(!0),Hs(cs.current),u(V=>V.filter(Z=>Z.toastId!==f.id)),setTimeout(()=>{S(f)},er)},[f,S,u,cs]);x.useEffect(()=>{if(f.promise&&Ie==="loading"||f.duration===1/0||f.type==="loading")return;let V;return U||i||se&&kt?(()=>{if(yt.current<Vs.current){let Z=new Date().getTime()-Vs.current;xs.current=xs.current-Z}yt.current=new Date().getTime()})():xs.current!==1/0&&(Vs.current=new Date().getTime(),V=setTimeout(()=>{var Z;(Z=f.onAutoClose)==null||Z.call(f,f),Qe()},xs.current)),()=>clearTimeout(V)},[U,i,f,Ie,se,kt,Qe]),x.useEffect(()=>{f.delete&&Qe()},[Qe,f.delete]);function Wa(){var V,Z,ie;return ye!=null&&ye.loading?x.createElement("div",{className:_e(G?.loader,(V=f?.classNames)==null?void 0:V.loader,"sonner-loader"),"data-visible":Ie==="loading"},ye.loading):H?x.createElement("div",{className:_e(G?.loader,(Z=f?.classNames)==null?void 0:Z.loader,"sonner-loader"),"data-visible":Ie==="loading"},H):x.createElement(Fn,{className:_e(G?.loader,(ie=f?.classNames)==null?void 0:ie.loader),visible:Ie==="loading"})}return x.createElement("li",{tabIndex:0,ref:Ze,className:_e(o,Da,G?.toast,(s=f?.classNames)==null?void 0:s.toast,G?.default,G?.[Ie],(a=f?.classNames)==null?void 0:a[Ie]),"data-sonner-toast":"","data-rich-colors":(n=f.richColors)!=null?n:I,"data-styled":!(f.jsx||f.unstyled||L),"data-mounted":he,"data-promise":!!f.promise,"data-swiped":Pa,"data-removed":us,"data-visible":La,"data-y-position":_a,"data-x-position":za,"data-index":D,"data-front":Fa,"data-swiping":hs,"data-dismissible":ls,"data-type":Ie,"data-invert":Ba,"data-swipe-out":gt,"data-swipe-direction":Ce,"data-expanded":!!(U||le&&he),style:{"--index":D,"--toasts-before":D,"--z-index":W.length-D,"--offset":`${us?Ia:cs.current}px`,"--initial-height":le?"auto":`${Oa}px`,...z,...f.style},onDragEnd:()=>{Us(!1),ce(null),os.current=null},onPointerDown:V=>{qs||!ls||(bt.current=new Date,Hs(cs.current),V.target.setPointerCapture(V.pointerId),V.target.tagName!=="BUTTON"&&(Us(!0),os.current={x:V.clientX,y:V.clientY}))},onPointerUp:()=>{var V,Z,ie,De;if(gt||!ls)return;os.current=null;let $e=Number(((V=Ze.current)==null?void 0:V.style.getPropertyValue("--swipe-amount-x").replace("px",""))||0),Je=Number(((Z=Ze.current)==null?void 0:Z.style.getPropertyValue("--swipe-amount-y").replace("px",""))||0),es=new Date().getTime()-((ie=bt.current)==null?void 0:ie.getTime()),Me=re==="x"?$e:Je,Ke=Math.abs(Me)/es;if(Math.abs(Me)>=Zn||Ke>.11){Hs(cs.current),(De=f.onDismiss)==null||De.call(f,f),is(re==="x"?$e>0?"right":"left":Je>0?"down":"up"),Qe(),Ea(!0),jt(!1);return}Us(!1),ce(null)},onPointerMove:V=>{var Z,ie,De,$e;if(!os.current||!ls||((Z=window.getSelection())==null?void 0:Z.toString().length)>0)return;let Je=V.clientY-os.current.y,es=V.clientX-os.current.x,Me=(ie=t.swipeDirections)!=null?ie:sr(O);!re&&(Math.abs(es)>1||Math.abs(Je)>1)&&ce(Math.abs(es)>Math.abs(Je)?"x":"y");let Ke={x:0,y:0};re==="y"?(Me.includes("top")||Me.includes("bottom"))&&(Me.includes("top")&&Je<0||Me.includes("bottom")&&Je>0)&&(Ke.y=Je):re==="x"&&(Me.includes("left")||Me.includes("right"))&&(Me.includes("left")&&es<0||Me.includes("right")&&es>0)&&(Ke.x=es),(Math.abs(Ke.x)>0||Math.abs(Ke.y)>0)&&jt(!0),(De=Ze.current)==null||De.style.setProperty("--swipe-amount-x",`${Ke.x}px`),($e=Ze.current)==null||$e.style.setProperty("--swipe-amount-y",`${Ke.y}px`)}},Ma&&!f.jsx?x.createElement("button",{"aria-label":Bs,"data-disabled":qs,"data-close-button":!0,onClick:qs||!ls?()=>{}:()=>{var V;Qe(),(V=f.onDismiss)==null||V.call(f,f)},className:_e(G?.closeButton,(r=f?.classNames)==null?void 0:r.closeButton)},(c=ye?.close)!=null?c:_n):null,f.jsx||K.isValidElement(f.title)?f.jsx?f.jsx:typeof f.title=="function"?f.title():f.title:x.createElement(x.Fragment,null,Ie||f.icon||f.promise?x.createElement("div",{"data-icon":"",className:_e(G?.icon,(p=f?.classNames)==null?void 0:p.icon)},f.promise||f.type==="loading"&&!f.icon?f.icon||Wa():null,f.type!=="loading"?f.icon||ye?.[Ie]||In(Ie):null):null,x.createElement("div",{"data-content":"",className:_e(G?.content,(d=f?.classNames)==null?void 0:d.content)},x.createElement("div",{"data-title":"",className:_e(G?.title,(g=f?.classNames)==null?void 0:g.title)},typeof f.title=="function"?f.title():f.title),f.description?x.createElement("div",{"data-description":"",className:_e(l,$a,G?.description,(b=f?.classNames)==null?void 0:b.description)},typeof f.description=="function"?f.description():f.description):null),K.isValidElement(f.cancel)?f.cancel:f.cancel&&Ts(f.cancel)?x.createElement("button",{"data-button":!0,"data-cancel":!0,style:f.cancelButtonStyle||X,onClick:V=>{var Z,ie;Ts(f.cancel)&&ls&&((ie=(Z=f.cancel).onClick)==null||ie.call(Z,V),Qe())},className:_e(G?.cancelButton,(j=f?.classNames)==null?void 0:j.cancelButton)},f.cancel.label):null,K.isValidElement(f.action)?f.action:f.action&&Ts(f.action)?x.createElement("button",{"data-button":!0,"data-action":!0,style:f.actionButtonStyle||m,onClick:V=>{var Z,ie;Ts(f.action)&&((ie=(Z=f.action).onClick)==null||ie.call(Z,V),!V.defaultPrevented&&Qe())},className:_e(G?.actionButton,(R=f?.classNames)==null?void 0:R.actionButton)},f.action.label):null))};function Ft(){if(typeof window>"u"||typeof document>"u")return"ltr";let t=document.documentElement.getAttribute("dir");return t==="auto"||!t?window.getComputedStyle(document.documentElement).direction:t}function ar(t,s){let a={};return[t,s].forEach((n,r)=>{let c=r===1,p=c?"--mobile-offset":"--offset",d=c?Kn:Jn;function g(b){["top","right","bottom","left"].forEach(j=>{a[`${p}-${j}`]=typeof b=="number"?`${b}px`:b})}typeof n=="number"||typeof n=="string"?g(n):typeof n=="object"?["top","right","bottom","left"].forEach(b=>{n[b]===void 0?a[`${p}-${b}`]=d:a[`${p}-${b}`]=typeof n[b]=="number"?`${n[b]}px`:n[b]}):g(d)}),a}var nr=K.forwardRef(function(t,s){let{invert:a,position:n="bottom-right",hotkey:r=["altKey","KeyT"],expand:c,closeButton:p,className:d,offset:g,mobileOffset:b,theme:j="light",richColors:R,duration:E,style:f,visibleToasts:L=Qn,toastOptions:i,dir:u=Ft(),gap:k=Xn,loadingIcon:N,icons:D,containerAriaLabel:W="Notifications",pauseWhenPageIsHidden:U}=t,[S,I]=x.useState([]),q=x.useMemo(()=>Array.from(new Set([n].concat(S.filter(se=>se.position).map(se=>se.position)))),[S,n]),[z,X]=x.useState([]),[m,o]=x.useState(!1),[l,h]=x.useState(!1),[O,M]=x.useState(j!=="system"?j:typeof window<"u"&&window.matchMedia&&window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light"),H=x.useRef(null),le=r.join("+").replace(/Key/g,"").replace(/Digit/g,""),G=x.useRef(null),ye=x.useRef(!1),Bs=x.useCallback(se=>{I(re=>{var ce;return(ce=re.find(Ce=>Ce.id===se.id))!=null&&ce.delete||Te.dismiss(se.id),re.filter(({id:Ce})=>Ce!==se.id)})},[]);return x.useEffect(()=>Te.subscribe(se=>{if(se.dismiss){I(re=>re.map(ce=>ce.id===se.id?{...ce,delete:!0}:ce));return}setTimeout(()=>{Qa.flushSync(()=>{I(re=>{let ce=re.findIndex(Ce=>Ce.id===se.id);return ce!==-1?[...re.slice(0,ce),{...re[ce],...se},...re.slice(ce+1)]:[se,...re]})})})}),[]),x.useEffect(()=>{if(j!=="system"){M(j);return}if(j==="system"&&(window.matchMedia&&window.matchMedia("(prefers-color-scheme: dark)").matches?M("dark"):M("light")),typeof window>"u")return;let se=window.matchMedia("(prefers-color-scheme: dark)");try{se.addEventListener("change",({matches:re})=>{M(re?"dark":"light")})}catch{se.addListener(({matches:ce})=>{try{M(ce?"dark":"light")}catch(Ce){console.error(Ce)}})}},[j]),x.useEffect(()=>{S.length<=1&&o(!1)},[S]),x.useEffect(()=>{let se=re=>{var ce,Ce;r.every(is=>re[is]||re.code===is)&&(o(!0),(ce=H.current)==null||ce.focus()),re.code==="Escape"&&(document.activeElement===H.current||(Ce=H.current)!=null&&Ce.contains(document.activeElement))&&o(!1)};return document.addEventListener("keydown",se),()=>document.removeEventListener("keydown",se)},[r]),x.useEffect(()=>{if(H.current)return()=>{G.current&&(G.current.focus({preventScroll:!0}),G.current=null,ye.current=!1)}},[H.current]),x.createElement("section",{ref:s,"aria-label":`${W} ${le}`,tabIndex:-1,"aria-live":"polite","aria-relevant":"additions text","aria-atomic":"false",suppressHydrationWarning:!0},q.map((se,re)=>{var ce;let[Ce,is]=se.split("-");return S.length?x.createElement("ol",{key:se,dir:u==="auto"?Ft():u,tabIndex:-1,ref:H,className:d,"data-sonner-toaster":!0,"data-theme":O,"data-y-position":Ce,"data-lifted":m&&S.length>1&&!c,"data-x-position":is,style:{"--front-toast-height":`${((ce=z[0])==null?void 0:ce.height)||0}px`,"--width":`${Yn}px`,"--gap":`${k}px`,...f,...ar(g,b)},onBlur:he=>{ye.current&&!he.currentTarget.contains(he.relatedTarget)&&(ye.current=!1,G.current&&(G.current.focus({preventScroll:!0}),G.current=null))},onFocus:he=>{he.target instanceof HTMLElement&&he.target.dataset.dismissible==="false"||ye.current||(ye.current=!0,G.current=he.relatedTarget)},onMouseEnter:()=>o(!0),onMouseMove:()=>o(!0),onMouseLeave:()=>{l||o(!1)},onDragEnd:()=>o(!1),onPointerDown:he=>{he.target instanceof HTMLElement&&he.target.dataset.dismissible==="false"||h(!0)},onPointerUp:()=>h(!1)},S.filter(he=>!he.position&&re===0||he.position===se).map((he,Ws)=>{var us,ks;return x.createElement(tr,{key:he.id,icons:D,index:Ws,toast:he,defaultRichColors:R,duration:(us=i?.duration)!=null?us:E,className:i?.className,descriptionClassName:i?.descriptionClassName,invert:a,visibleToasts:L,closeButton:(ks=i?.closeButton)!=null?ks:p,interacting:l,position:se,style:i?.style,unstyled:i?.unstyled,classNames:i?.classNames,cancelButtonStyle:i?.cancelButtonStyle,actionButtonStyle:i?.actionButtonStyle,removeToast:Bs,toasts:S.filter(hs=>hs.position==he.position),heights:z.filter(hs=>hs.position==he.position),setHeights:X,expandByDefault:c,gap:k,loadingIcon:N,expanded:m,pauseWhenPageIsHidden:U,swipeDirections:t.swipeDirections})})):null}))});const rr=K.createContext(null),Qs={didCatch:!1,error:null};let ir=class extends K.Component{constructor(s){super(s),this.resetErrorBoundary=this.resetErrorBoundary.bind(this),this.state=Qs}static getDerivedStateFromError(s){return{didCatch:!0,error:s}}resetErrorBoundary(){const{error:s}=this.state;if(s!==null){for(var a,n,r=arguments.length,c=new Array(r),p=0;p<r;p++)c[p]=arguments[p];(a=(n=this.props).onReset)===null||a===void 0||a.call(n,{args:c,reason:"imperative-api"}),this.setState(Qs)}}componentDidCatch(s,a){var n,r;(n=(r=this.props).onError)===null||n===void 0||n.call(r,s,a)}componentDidUpdate(s,a){const{didCatch:n}=this.state,{resetKeys:r}=this.props;if(n&&a.error!==null&&lr(s.resetKeys,r)){var c,p;(c=(p=this.props).onReset)===null||c===void 0||c.call(p,{next:r,prev:s.resetKeys,reason:"keys"}),this.setState(Qs)}}render(){const{children:s,fallbackRender:a,FallbackComponent:n,fallback:r}=this.props,{didCatch:c,error:p}=this.state;let d=s;if(c){const g={error:p,resetErrorBoundary:this.resetErrorBoundary};if(typeof a=="function")d=a(g);else if(n)d=K.createElement(n,g);else if(r!==void 0)d=r;else throw p}return K.createElement(rr.Provider,{value:{didCatch:c,error:p,resetErrorBoundary:this.resetErrorBoundary}},d)}};function lr(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[],s=arguments.length>1&&arguments[1]!==void 0?arguments[1]:[];return t.length!==s.length||t.some((a,n)=>!Object.is(a,s[n]))}function B(...t){return Ya(ia(t))}function Lt(t,s){if(typeof t=="function")return t(s);t!=null&&(t.current=s)}function cr(...t){return s=>{let a=!1;const n=t.map(r=>{const c=Lt(r,s);return!a&&typeof c=="function"&&(a=!0),c});if(a)return()=>{for(let r=0;r<n.length;r++){const c=n[r];typeof c=="function"?c():Lt(t[r],null)}}}}function or(t){const s=mr(t),a=K.forwardRef((n,r)=>{const{children:c,...p}=n,d=K.Children.toArray(c),g=d.find(hr);if(g){const b=g.props.children,j=d.map(R=>R===g?K.Children.count(b)>1?K.Children.only(null):K.isValidElement(b)?b.props.children:null:R);return e.jsx(s,{...p,ref:r,children:K.isValidElement(b)?K.cloneElement(b,void 0,j):null})}return e.jsx(s,{...p,ref:r,children:c})});return a.displayName=`${t}.Slot`,a}var dr=or("Slot");function mr(t){const s=K.forwardRef((a,n)=>{const{children:r,...c}=a;if(K.isValidElement(r)){const p=pr(r),d=xr(c,r.props);return r.type!==K.Fragment&&(d.ref=n?cr(n,p):p),K.cloneElement(r,d)}return K.Children.count(r)>1?K.Children.only(null):null});return s.displayName=`${t}.SlotClone`,s}var ur=Symbol("radix.slottable");function hr(t){return K.isValidElement(t)&&typeof t.type=="function"&&"__radixId"in t.type&&t.type.__radixId===ur}function xr(t,s){const a={...s};for(const n in s){const r=t[n],c=s[n];/^on[A-Z]/.test(n)?r&&c?a[n]=(...d)=>{const g=c(...d);return r(...d),g}:r&&(a[n]=r):n==="style"?a[n]={...r,...c}:n==="className"&&(a[n]=[r,c].filter(Boolean).join(" "))}return{...t,...a}}function pr(t){let s=Object.getOwnPropertyDescriptor(t.props,"ref")?.get,a=s&&"isReactWarning"in s&&s.isReactWarning;return a?t.ref:(s=Object.getOwnPropertyDescriptor(t,"ref")?.get,a=s&&"isReactWarning"in s&&s.isReactWarning,a?t.props.ref:t.props.ref||t.ref)}const Dt=t=>typeof t=="boolean"?`${t}`:t===0?"0":t,$t=ia,Ds=(t,s)=>a=>{var n;if(s?.variants==null)return $t(t,a?.class,a?.className);const{variants:r,defaultVariants:c}=s,p=Object.keys(r).map(b=>{const j=a?.[b],R=c?.[b];if(j===null)return null;const E=Dt(j)||Dt(R);return r[b][E]}),d=a&&Object.entries(a).reduce((b,j)=>{let[R,E]=j;return E===void 0||(b[R]=E),b},{}),g=s==null||(n=s.compoundVariants)===null||n===void 0?void 0:n.reduce((b,j)=>{let{class:R,className:E,...f}=j;return Object.entries(f).every(L=>{let[i,u]=L;return Array.isArray(u)?u.includes({...c,...d}[i]):{...c,...d}[i]===u})?[...b,R,E]:b},[]);return $t(t,p,g,a?.class,a?.className)},fr=Ds("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground shadow hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",outline:"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),v=x.forwardRef(({className:t,variant:s,size:a,asChild:n=!1,...r},c)=>{const p=n?dr:"button";return e.jsx(p,{className:B(fr({variant:s,size:a,className:t})),ref:c,...r})});v.displayName="Button";const gr=Ds("inline-flex items-center rounded-md border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground shadow hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground shadow hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function $({className:t,variant:s,...a}){return e.jsx("div",{className:B(gr({variant:s}),t),...a})}const jr={},vr=t=>(s,a,n)=>{const r=n.subscribe;return n.subscribe=(p,d,g)=>{let b=p;if(d){const j=g?.equalityFn||Object.is;let R=p(n.getState());b=E=>{const f=p(E);if(!j(R,f)){const L=R;d(R=f,L)}},g?.fireImmediately&&d(R,R)}return r(b)},t(s,a,n)},br=vr;function Nr(t,s){let a;try{a=t()}catch{return}return{getItem:r=>{var c;const p=g=>g===null?null:JSON.parse(g,void 0),d=(c=a.getItem(r))!=null?c:null;return d instanceof Promise?d.then(p):p(d)},setItem:(r,c)=>a.setItem(r,JSON.stringify(c,void 0)),removeItem:r=>a.removeItem(r)}}const gs=t=>s=>{try{const a=t(s);return a instanceof Promise?a:{then(n){return gs(n)(a)},catch(n){return this}}}catch(a){return{then(n){return this},catch(n){return gs(n)(a)}}}},yr=(t,s)=>(a,n,r)=>{let c={getStorage:()=>localStorage,serialize:JSON.stringify,deserialize:JSON.parse,partialize:u=>u,version:0,merge:(u,k)=>({...k,...u}),...s},p=!1;const d=new Set,g=new Set;let b;try{b=c.getStorage()}catch{}if(!b)return t((...u)=>{console.warn(`[zustand persist middleware] Unable to update item '${c.name}', the given storage is currently unavailable.`),a(...u)},n,r);const j=gs(c.serialize),R=()=>{const u=c.partialize({...n()});let k;const N=j({state:u,version:c.version}).then(D=>b.setItem(c.name,D)).catch(D=>{k=D});if(k)throw k;return N},E=r.setState;r.setState=(u,k)=>{E(u,k),R()};const f=t((...u)=>{a(...u),R()},n,r);let L;const i=()=>{var u;if(!b)return;p=!1,d.forEach(N=>N(n()));const k=((u=c.onRehydrateStorage)==null?void 0:u.call(c,n()))||void 0;return gs(b.getItem.bind(b))(c.name).then(N=>{if(N)return c.deserialize(N)}).then(N=>{if(N)if(typeof N.version=="number"&&N.version!==c.version){if(c.migrate)return c.migrate(N.state,N.version);console.error("State loaded from storage couldn't be migrated since no migrate function was provided")}else return N.state}).then(N=>{var D;return L=c.merge(N,(D=n())!=null?D:f),a(L,!0),R()}).then(()=>{k?.(L,void 0),p=!0,g.forEach(N=>N(L))}).catch(N=>{k?.(void 0,N)})};return r.persist={setOptions:u=>{c={...c,...u},u.getStorage&&(b=u.getStorage())},clearStorage:()=>{b?.removeItem(c.name)},getOptions:()=>c,rehydrate:()=>i(),hasHydrated:()=>p,onHydrate:u=>(d.add(u),()=>{d.delete(u)}),onFinishHydration:u=>(g.add(u),()=>{g.delete(u)})},i(),L||f},wr=(t,s)=>(a,n,r)=>{let c={storage:Nr(()=>localStorage),partialize:i=>i,version:0,merge:(i,u)=>({...u,...i}),...s},p=!1;const d=new Set,g=new Set;let b=c.storage;if(!b)return t((...i)=>{console.warn(`[zustand persist middleware] Unable to update item '${c.name}', the given storage is currently unavailable.`),a(...i)},n,r);const j=()=>{const i=c.partialize({...n()});return b.setItem(c.name,{state:i,version:c.version})},R=r.setState;r.setState=(i,u)=>{R(i,u),j()};const E=t((...i)=>{a(...i),j()},n,r);r.getInitialState=()=>E;let f;const L=()=>{var i,u;if(!b)return;p=!1,d.forEach(N=>{var D;return N((D=n())!=null?D:E)});const k=((u=c.onRehydrateStorage)==null?void 0:u.call(c,(i=n())!=null?i:E))||void 0;return gs(b.getItem.bind(b))(c.name).then(N=>{if(N)if(typeof N.version=="number"&&N.version!==c.version){if(c.migrate)return[!0,c.migrate(N.state,N.version)];console.error("State loaded from storage couldn't be migrated since no migrate function was provided")}else return[!1,N.state];return[!1,void 0]}).then(N=>{var D;const[W,U]=N;if(f=c.merge(U,(D=n())!=null?D:E),a(f,!0),W)return j()}).then(()=>{k?.(f,void 0),f=n(),p=!0,g.forEach(N=>N(f))}).catch(N=>{k?.(void 0,N)})};return r.persist={setOptions:i=>{c={...c,...i},i.storage&&(b=i.storage)},clearStorage:()=>{b?.removeItem(c.name)},getOptions:()=>c,rehydrate:()=>L(),hasHydrated:()=>p,onHydrate:i=>(d.add(i),()=>{d.delete(i)}),onFinishHydration:i=>(g.add(i),()=>{g.delete(i)})},c.skipHydration||L(),f||E},kr=(t,s)=>"getStorage"in s||"serialize"in s||"deserialize"in s?((jr?"production":void 0)!=="production"&&console.warn("[DEPRECATED] `getStorage`, `serialize` and `deserialize` options are deprecated. Use `storage` option instead."),yr(t,s)):wr(t,s),Sr=kr;var xa=Symbol.for("immer-nothing"),Mt=Symbol.for("immer-draftable"),Re=Symbol.for("immer-state");function ze(t,...s){throw new Error(`[Immer] minified error nr: ${t}. Full error at: https://bit.ly/3cXEKWf`)}var ds=Object.getPrototypeOf;function ms(t){return!!t&&!!t[Re]}function ns(t){return t?pa(t)||Array.isArray(t)||!!t[Mt]||!!t.constructor?.[Mt]||Ms(t)||_s(t):!1}var Cr=Object.prototype.constructor.toString();function pa(t){if(!t||typeof t!="object")return!1;const s=ds(t);if(s===null)return!0;const a=Object.hasOwnProperty.call(s,"constructor")&&s.constructor;return a===Object?!0:typeof a=="function"&&Function.toString.call(a)===Cr}function Ps(t,s){$s(t)===0?Reflect.ownKeys(t).forEach(a=>{s(a,t[a],t)}):t.forEach((a,n)=>s(n,a,t))}function $s(t){const s=t[Re];return s?s.type_:Array.isArray(t)?1:Ms(t)?2:_s(t)?3:0}function at(t,s){return $s(t)===2?t.has(s):Object.prototype.hasOwnProperty.call(t,s)}function fa(t,s,a){const n=$s(t);n===2?t.set(s,a):n===3?t.add(a):t[s]=a}function Tr(t,s){return t===s?t!==0||1/t===1/s:t!==t&&s!==s}function Ms(t){return t instanceof Map}function _s(t){return t instanceof Set}function ss(t){return t.copy_||t.base_}function nt(t,s){if(Ms(t))return new Map(t);if(_s(t))return new Set(t);if(Array.isArray(t))return Array.prototype.slice.call(t);const a=pa(t);if(s===!0||s==="class_only"&&!a){const n=Object.getOwnPropertyDescriptors(t);delete n[Re];let r=Reflect.ownKeys(n);for(let c=0;c<r.length;c++){const p=r[c],d=n[p];d.writable===!1&&(d.writable=!0,d.configurable=!0),(d.get||d.set)&&(n[p]={configurable:!0,writable:!0,enumerable:d.enumerable,value:t[p]})}return Object.create(ds(t),n)}else{const n=ds(t);if(n!==null&&a)return{...t};const r=Object.create(n);return Object.assign(r,t)}}function xt(t,s=!1){return zs(t)||ms(t)||!ns(t)||($s(t)>1&&(t.set=t.add=t.clear=t.delete=Ar),Object.freeze(t),s&&Object.entries(t).forEach(([a,n])=>xt(n,!0))),t}function Ar(){ze(2)}function zs(t){return Object.isFrozen(t)}var Rr={};function rs(t){const s=Rr[t];return s||ze(0,t),s}var js;function ga(){return js}function Er(t,s){return{drafts_:[],parent_:t,immer_:s,canAutoFreeze_:!0,unfinalizedDrafts_:0}}function _t(t,s){s&&(rs("Patches"),t.patches_=[],t.inversePatches_=[],t.patchListener_=s)}function rt(t){it(t),t.drafts_.forEach(Pr),t.drafts_=null}function it(t){t===js&&(js=t.parent_)}function zt(t){return js=Er(js,t)}function Pr(t){const s=t[Re];s.type_===0||s.type_===1?s.revoke_():s.revoked_=!0}function Bt(t,s){s.unfinalizedDrafts_=s.drafts_.length;const a=s.drafts_[0];return t!==void 0&&t!==a?(a[Re].modified_&&(rt(s),ze(4)),ns(t)&&(t=Is(s,t),s.parent_||Os(s,t)),s.patches_&&rs("Patches").generateReplacementPatches_(a[Re].base_,t,s.patches_,s.inversePatches_)):t=Is(s,a,[]),rt(s),s.patches_&&s.patchListener_(s.patches_,s.inversePatches_),t!==xa?t:void 0}function Is(t,s,a){if(zs(s))return s;const n=s[Re];if(!n)return Ps(s,(r,c)=>Wt(t,n,s,r,c,a)),s;if(n.scope_!==t)return s;if(!n.modified_)return Os(t,n.base_,!0),n.base_;if(!n.finalized_){n.finalized_=!0,n.scope_.unfinalizedDrafts_--;const r=n.copy_;let c=r,p=!1;n.type_===3&&(c=new Set(r),r.clear(),p=!0),Ps(c,(d,g)=>Wt(t,n,r,d,g,a,p)),Os(t,r,!1),a&&t.patches_&&rs("Patches").generatePatches_(n,a,t.patches_,t.inversePatches_)}return n.copy_}function Wt(t,s,a,n,r,c,p){if(ms(r)){const d=c&&s&&s.type_!==3&&!at(s.assigned_,n)?c.concat(n):void 0,g=Is(t,r,d);if(fa(a,n,g),ms(g))t.canAutoFreeze_=!1;else return}else p&&a.add(r);if(ns(r)&&!zs(r)){if(!t.immer_.autoFreeze_&&t.unfinalizedDrafts_<1)return;Is(t,r),(!s||!s.scope_.parent_)&&typeof n!="symbol"&&Object.prototype.propertyIsEnumerable.call(a,n)&&Os(t,r)}}function Os(t,s,a=!1){!t.parent_&&t.immer_.autoFreeze_&&t.canAutoFreeze_&&xt(s,a)}function Ir(t,s){const a=Array.isArray(t),n={type_:a?1:0,scope_:s?s.scope_:ga(),modified_:!1,finalized_:!1,assigned_:{},parent_:s,base_:t,draft_:null,copy_:null,revoke_:null,isManual_:!1};let r=n,c=pt;a&&(r=[n],c=vs);const{revoke:p,proxy:d}=Proxy.revocable(r,c);return n.draft_=d,n.revoke_=p,d}var pt={get(t,s){if(s===Re)return t;const a=ss(t);if(!at(a,s))return Or(t,a,s);const n=a[s];return t.finalized_||!ns(n)?n:n===Js(t.base_,s)?(Ks(t),t.copy_[s]=ct(n,t)):n},has(t,s){return s in ss(t)},ownKeys(t){return Reflect.ownKeys(ss(t))},set(t,s,a){const n=ja(ss(t),s);if(n?.set)return n.set.call(t.draft_,a),!0;if(!t.modified_){const r=Js(ss(t),s),c=r?.[Re];if(c&&c.base_===a)return t.copy_[s]=a,t.assigned_[s]=!1,!0;if(Tr(a,r)&&(a!==void 0||at(t.base_,s)))return!0;Ks(t),lt(t)}return t.copy_[s]===a&&(a!==void 0||s in t.copy_)||Number.isNaN(a)&&Number.isNaN(t.copy_[s])||(t.copy_[s]=a,t.assigned_[s]=!0),!0},deleteProperty(t,s){return Js(t.base_,s)!==void 0||s in t.base_?(t.assigned_[s]=!1,Ks(t),lt(t)):delete t.assigned_[s],t.copy_&&delete t.copy_[s],!0},getOwnPropertyDescriptor(t,s){const a=ss(t),n=Reflect.getOwnPropertyDescriptor(a,s);return n&&{writable:!0,configurable:t.type_!==1||s!=="length",enumerable:n.enumerable,value:a[s]}},defineProperty(){ze(11)},getPrototypeOf(t){return ds(t.base_)},setPrototypeOf(){ze(12)}},vs={};Ps(pt,(t,s)=>{vs[t]=function(){return arguments[0]=arguments[0][0],s.apply(this,arguments)}});vs.deleteProperty=function(t,s){return vs.set.call(this,t,s,void 0)};vs.set=function(t,s,a){return pt.set.call(this,t[0],s,a,t[0])};function Js(t,s){const a=t[Re];return(a?ss(a):t)[s]}function Or(t,s,a){const n=ja(s,a);return n?"value"in n?n.value:n.get?.call(t.draft_):void 0}function ja(t,s){if(!(s in t))return;let a=ds(t);for(;a;){const n=Object.getOwnPropertyDescriptor(a,s);if(n)return n;a=ds(a)}}function lt(t){t.modified_||(t.modified_=!0,t.parent_&&lt(t.parent_))}function Ks(t){t.copy_||(t.copy_=nt(t.base_,t.scope_.immer_.useStrictShallowCopy_))}var Fr=class{constructor(t){this.autoFreeze_=!0,this.useStrictShallowCopy_=!1,this.produce=(s,a,n)=>{if(typeof s=="function"&&typeof a!="function"){const c=a;a=s;const p=this;return function(g=c,...b){return p.produce(g,j=>a.call(this,j,...b))}}typeof a!="function"&&ze(6),n!==void 0&&typeof n!="function"&&ze(7);let r;if(ns(s)){const c=zt(this),p=ct(s,void 0);let d=!0;try{r=a(p),d=!1}finally{d?rt(c):it(c)}return _t(c,n),Bt(r,c)}else if(!s||typeof s!="object"){if(r=a(s),r===void 0&&(r=s),r===xa&&(r=void 0),this.autoFreeze_&&xt(r,!0),n){const c=[],p=[];rs("Patches").generateReplacementPatches_(s,r,c,p),n(c,p)}return r}else ze(1,s)},this.produceWithPatches=(s,a)=>{if(typeof s=="function")return(p,...d)=>this.produceWithPatches(p,g=>s(g,...d));let n,r;return[this.produce(s,a,(p,d)=>{n=p,r=d}),n,r]},typeof t?.autoFreeze=="boolean"&&this.setAutoFreeze(t.autoFreeze),typeof t?.useStrictShallowCopy=="boolean"&&this.setUseStrictShallowCopy(t.useStrictShallowCopy)}createDraft(t){ns(t)||ze(8),ms(t)&&(t=Lr(t));const s=zt(this),a=ct(t,void 0);return a[Re].isManual_=!0,it(s),a}finishDraft(t,s){const a=t&&t[Re];(!a||!a.isManual_)&&ze(9);const{scope_:n}=a;return _t(n,s),Bt(void 0,n)}setAutoFreeze(t){this.autoFreeze_=t}setUseStrictShallowCopy(t){this.useStrictShallowCopy_=t}applyPatches(t,s){let a;for(a=s.length-1;a>=0;a--){const r=s[a];if(r.path.length===0&&r.op==="replace"){t=r.value;break}}a>-1&&(s=s.slice(a+1));const n=rs("Patches").applyPatches_;return ms(t)?n(t,s):this.produce(t,r=>n(r,s))}};function ct(t,s){const a=Ms(t)?rs("MapSet").proxyMap_(t,s):_s(t)?rs("MapSet").proxySet_(t,s):Ir(t,s);return(s?s.scope_:ga()).drafts_.push(a),a}function Lr(t){return ms(t)||ze(10,t),va(t)}function va(t){if(!ns(t)||zs(t))return t;const s=t[Re];let a;if(s){if(!s.modified_)return s.base_;s.finalized_=!0,a=nt(t,s.scope_.immer_.useStrictShallowCopy_)}else a=nt(t,!0);return Ps(a,(n,r)=>{fa(a,n,va(r))}),s&&(s.finalized_=!1),a}var Ee=new Fr,Dr=Ee.produce;Ee.produceWithPatches.bind(Ee);Ee.setAutoFreeze.bind(Ee);Ee.setUseStrictShallowCopy.bind(Ee);Ee.applyPatches.bind(Ee);Ee.createDraft.bind(Ee);Ee.finishDraft.bind(Ee);const $r=t=>(s,a,n)=>(n.setState=(r,c,...p)=>{const d=typeof r=="function"?Dr(r):r;return s(d,c,...p)},t(n.setState,a,n)),ba=$r,Ut={nmap:{toolId:"nmap",category:"network",baseTimeout:12e4,scalingFactor:1.5,maxTimeout:6e5,minTimeout:3e4},masscan:{toolId:"masscan",category:"network",baseTimeout:6e4,scalingFactor:1.2,maxTimeout:3e5,minTimeout:15e3},nuclei:{toolId:"nuclei",category:"vulnerability",baseTimeout:45e3,scalingFactor:1.3,maxTimeout:18e4,minTimeout:2e4},openvas:{toolId:"openvas",category:"vulnerability",baseTimeout:3e5,scalingFactor:2,maxTimeout:18e5,minTimeout:12e4},sqlmap:{toolId:"sqlmap",category:"web",baseTimeout:18e4,scalingFactor:2.5,maxTimeout:9e5,minTimeout:6e4},dirb:{toolId:"dirb",category:"web",baseTimeout:9e4,scalingFactor:1.8,maxTimeout:3e5,minTimeout:3e4},gobuster:{toolId:"gobuster",category:"web",baseTimeout:6e4,scalingFactor:1.5,maxTimeout:24e4,minTimeout:2e4},default:{toolId:"default",category:"unknown",baseTimeout:12e4,scalingFactor:1.5,maxTimeout:3e5,minTimeout:3e4}};class Mr{constructor(){ge(this,"baseURL");ge(this,"timeout");ge(this,"retryAttempts");ge(this,"retryDelay");ge(this,"headers");this.baseURL="http://************:8090",this.timeout=3e4,this.retryAttempts=3,this.retryDelay=1e3,this.headers={"Content-Type":"application/json",Accept:"application/json"},console.log(`🔗 API Client initialized for: ${this.baseURL}`)}async delay(s){return new Promise(a=>setTimeout(a,s))}calculateToolTimeout(s,a){const n=Ut[s]||Ut.default;let r=n.baseTimeout;const c=this.calculateComplexityFactor(s,a);return r=Math.round(r*c*n.scalingFactor),r=Math.max(n.minTimeout,Math.min(r,n.maxTimeout)),console.log(`🕒 Dynamic timeout for ${s}: ${r}ms (${Math.round(r/1e3)}s) - Complexity: ${c.toFixed(2)}x`),r}calculateComplexityFactor(s,a){let n=1;switch(s){case"nmap":if(a.portRange){const r=this.estimatePortCount(a.portRange);r>1e3?n*=1.5:r>100&&(n*=1.2)}a.detectOS&&(n*=1.3),a.versionDetection&&(n*=1.4),a.scriptScan&&(n*=1.6),a.timing&&parseInt(a.timing)<=2&&(n*=1.8);break;case"nuclei":if(a.templates&&Array.isArray(a.templates)){const r=a.templates.length;r>100?n*=1.4:r>50&&(n*=1.2)}a.severity&&a.severity.includes("critical")&&(n*=1.1),a.concurrency&&a.concurrency<10&&(n*=1.3);break;case"sqlmap":a.level&&a.level>3&&(n*=1.8),a.risk&&a.risk>2&&(n*=1.5),a.technique&&a.technique.includes("T")&&(n*=1.4),a.crawl&&(n*=2),a.forms&&(n*=1.6);break;case"dirb":case"gobuster":if(a.wordlist){const r=this.estimateWordlistSize(a.wordlist);r>1e4?n*=2:r>1e3&&(n*=1.5)}a.extensions&&Array.isArray(a.extensions)&&(n*=1+a.extensions.length*.2);break;default:a.comprehensive&&(n*=1.8),a.deep&&(n*=1.5),a.aggressive&&(n*=1.3);break}if(a.target&&typeof a.target=="string"){const r=a.target.split(/[,\s\n]+/).filter(c=>c.trim());r.length>1&&(n*=Math.min(r.length*.3+.7,3))}return Math.max(.5,Math.min(n,5))}estimatePortCount(s){if(!s||s==="-")return 65535;const a=s.split(",");let n=0;for(const r of a){const c=r.trim();if(c.includes("-")){const[p,d]=c.split("-").map(g=>parseInt(g.trim()));!isNaN(p)&&!isNaN(d)&&(n+=Math.abs(d-p)+1)}else{const p=parseInt(c);isNaN(p)||(n+=1)}}return n||1e3}estimateWordlistSize(s){const a={common:1e3,medium:5e3,large:2e4,huge:5e4,rockyou:14344391,"dirb-common":4614,"dirb-big":20469,"dirbuster-medium":220560,"seclist-common":4713},n=s.toLowerCase();for(const[r,c]of Object.entries(a))if(n.includes(r))return c;return 5e3}async fetchWithRetry(s,a={},n=this.retryAttempts){for(let r=0;r<n;r++)try{const c=typeof process<"u"&&!1;let p={...a,headers:{...this.headers,...a.headers}};if(!c){const d=new AbortController,g=setTimeout(()=>d.abort(),this.timeout);p.signal=d.signal;const b=await fetch(s,p);if(clearTimeout(g),b.ok)return b;if(r===n-1||b.status<500)throw new Error(`HTTP ${b.status}: ${b.statusText}`)}}catch(c){if(r===n-1)throw c;console.warn(`🔄 Request failed (attempt ${r+1}/${n}):`,c),await this.delay(this.retryDelay*Math.pow(2,r))}throw new Error("Max retry attempts exceeded")}async checkHealth(){try{console.log("🏥 Checking backend health...");const a=await(await this.fetchWithRetry(`${this.baseURL}/api/health`)).json();console.log("✅ Backend health check successful:",a);const n=a.data||a;return{connected:!0,url:this.baseURL,version:n.version||a.version||"1.0.0",lastConnected:new Date,tools:n.tools||a.tools||{total:22,operational:22,failed:0}}}catch(s){return console.error("❌ Backend health check failed:",s),console.error("❌ Error details:",{message:s instanceof Error?s.message:"Unknown error",stack:s instanceof Error?s.stack:void 0,type:typeof s,error:s}),{connected:!1,url:this.baseURL,error:s instanceof Error?s.message:"Unknown error"}}}async getAvailableTools(){try{console.log("🔧 Fetching available tools from simplified endpoint...");const a=await(await this.fetchWithRetry(`${this.baseURL}/api/tools`)).json(),n=a.tools||[];console.log(`🔍 API Response: success=${a.success}, tools.length=${n.length}`);const r={"Network Scanners":"network-scanning","Web Scanners":"web-testing","Vulnerability Scanners":"vulnerability-assessment","Password Tools":"password-tools","SSL/TLS":"ssl-testing",Framework:"exploitation",Fingerprinting:"network-scanning",Fuzzer:"web-testing","Network Enumeration":"network-scanning","Exploit Database":"exploitation"},c=n.map(p=>({id:p.name,name:p.name,description:p.description,category:r[p.category]||"network-scanning",version:p.version,status:p.available?"available":"unavailable",executable:p.path,platform:["linux"],capabilities:[],requiredParams:[]}));return console.log(`✅ Retrieved and mapped ${c.length} tools from simplified endpoint`),console.log(`🗂️ Tool categories: ${[...new Set(c.map(p=>p.category))].join(", ")}`),console.log(`🔧 Available tools: ${c.filter(p=>p.status==="available").map(p=>p.name).join(", ")}`),console.log(`📊 Tool availability: ${c.filter(p=>p.status==="available").length}/${c.length} tools available`),c}catch(s){return console.error("❌ Failed to fetch available tools:",s),C.error("Failed to load security tools"),[]}}async getToolDetails(s){try{return(await(await this.fetchWithRetry(`${this.baseURL}/api/tools/${s}`)).json()).tool||null}catch(a){return console.error(`❌ Failed to fetch tool details for ${s}:`,a),null}}async executeTool(s,a,n){const r=this.timeout;try{console.log(`🚀 Executing tool: ${s}`,a);try{const c=this.calculateToolTimeout(s,a);this.timeout=c;const p=await this.fetchWithRetry(`${this.baseURL}/api/tools/${s}/scan`,{method:"POST",body:JSON.stringify({target:a.target,timeout:a.timeout||300,threads:a.threads||1,output_format:a.output_format||"json",options:a})});this.timeout=r;const d=await p.json();return n&&(n({id:d.data?.scan_id||"scan-"+Date.now(),toolId:s,status:"running",progress:0,startTime:new Date,output:[`Starting ${s} scan...`],results:null,error:null,config:a}),setTimeout(()=>{n({id:d.data?.scan_id||"scan-"+Date.now(),toolId:s,status:"running",progress:50,startTime:new Date,output:[`${s} scan in progress...`],results:null,error:null,config:a})},1e3),setTimeout(()=>{n({id:d.data?.scan_id||"scan-"+Date.now(),toolId:s,status:d.success?"completed":"failed",progress:100,startTime:new Date,endTime:new Date,output:[`${s} scan completed`],results:d.data,error:d.success?null:d.error,config:a})},2e3)),console.log(`✅ Direct execution succeeded for ${s}`),{id:d.data?.scan_id||"scan-"+Date.now(),toolId:s,status:d.success?"completed":"failed",progress:100,startTime:new Date,endTime:new Date,output:[`${s} scan completed`],results:d.data,error:d.success?null:d.error,config:a}}catch(c){return this.timeout=r,console.warn(`⚠️ Direct execution failed for ${s}, falling back to workflow approach:`,c),await this.executeToolViaWorkflow(s,a,n)}}catch(c){throw this.timeout=r,console.error(`❌ Tool execution failed for ${s}:`,c),C.error(`Failed to execute ${s}: ${c instanceof Error?c.message:"Unknown error"}`),c}}async executeToolViaWorkflow(s,a,n){try{console.log(`🔄 Executing ${s} via workflow approach`);const r=await this.createCampaign({name:`${s}-execution-${Date.now()}`,target:a.target||"Unknown",description:`${s} execution via workflow fallback`});if(!r)throw new Error("Failed to create campaign for tool execution");console.log(`📁 Created campaign ${r.id} for ${s}`);const p=await(await this.fetchWithRetry(`${this.baseURL}/api/campaigns/${r.id}/scans`,{method:"POST",body:JSON.stringify({tool:s,target:a.target||"Unknown",config:a})})).json();console.log(`🔍 Created scan ${p.id} for ${s}`);const g=await(await this.fetchWithRetry(`${this.baseURL}/api/scans/${p.id}/start`,{method:"POST"})).json();return console.log(`🚀 Started execution ${g.id} for ${s}`),n&&g.id&&this.monitorExecution(g.id,n),C.success(`${s} started successfully via workflow`),g}catch(r){throw console.error(`❌ Workflow execution failed for ${s}:`,r),new Error(`Workflow execution failed: ${r instanceof Error?r.message:"Unknown error"}`)}}async getExecutionStatus(s){try{return(await(await this.fetchWithRetry(`${this.baseURL}/api/executions/${s}`)).json()).execution||null}catch(a){return console.error(`❌ Failed to get execution status for ${s}:`,a),null}}async cancelExecution(s){try{return(await this.fetchWithRetry(`${this.baseURL}/api/executions/${s}/cancel`,{method:"POST"})).ok}catch(a){return console.error(`❌ Failed to cancel execution ${s}:`,a),!1}}async monitorExecution(s,a){const n=async()=>{const r=await this.getExecutionStatus(s);r&&(a(r),(r.status==="running"||r.status==="pending")&&setTimeout(n,2e3))};setTimeout(n,1e3)}async getCampaigns(){try{return(await(await this.fetchWithRetry(`${this.baseURL}/api/campaigns`)).json()).campaigns||[]}catch(s){return console.error("❌ Failed to fetch campaigns:",s),[]}}async createCampaign(s){try{return(await(await this.fetchWithRetry(`${this.baseURL}/api/campaigns`,{method:"POST",body:JSON.stringify(s)})).json()).campaign||null}catch(a){return console.error("❌ Failed to create campaign:",a),C.error("Failed to create campaign"),null}}async updateCampaign(s,a){try{return(await(await this.fetchWithRetry(`${this.baseURL}/api/campaigns/${s}`,{method:"PUT",body:JSON.stringify(a)})).json()).campaign||null}catch(n){return console.error(`❌ Failed to update campaign ${s}:`,n),null}}async deleteCampaign(s){try{return(await this.fetchWithRetry(`${this.baseURL}/api/campaigns/${s}`,{method:"DELETE"})).ok}catch(a){return console.error(`❌ Failed to delete campaign ${s}:`,a),!1}}async getFerrariCapabilities(){try{console.log("🏎️ Fetching Ferrari AI capabilities...");const[s,a,n,r]=await Promise.all([this.fetchWithRetry(`${this.baseURL}/api/orchestrator/capabilities`),this.fetchWithRetry(`${this.baseURL}/api/ai/creative-exploits/capabilities`),this.fetchWithRetry(`${this.baseURL}/api/ai/behavioral-analysis/capabilities`),this.fetchWithRetry(`${this.baseURL}/api/proxy/configurations`)]),[c,p,d,g]=await Promise.all([s.json(),a.json(),n.json(),r.json()]);return{orchestrator:{available:s.ok,templates:c.templates||[],capabilities:c.capabilities||[]},creativeExploits:{available:a.ok,confidence:p.confidence||0,generators:p.generators||[]},behavioralAnalysis:{available:n.ok,engines:d.engines||[],realTime:d.realTime||!1},aiProxy:{available:r.ok,configurations:g.configurations||[],rotationEnabled:g.rotationEnabled||!1}}}catch(s){return console.error("❌ Failed to fetch Ferrari capabilities:",s),null}}async getAIStatus(){try{const a=await(await this.fetchWithRetry(`${this.baseURL}/api/ai/capabilities/status`)).json();return{available:a.available||!1,providers:a.providers||{},currentProvider:a.currentProvider||"none",lastResponse:a.lastResponse?new Date(a.lastResponse):void 0}}catch(s){return console.error("❌ Failed to fetch AI status:",s),null}}async getOrchestratorTemplates(){try{return(await(await this.fetchWithRetry(`${this.baseURL}/api/orchestrator/templates`)).json()).templates||[]}catch(s){return console.error("❌ Failed to fetch orchestrator templates:",s),[]}}async executeAttackChain(s){try{return await(await this.fetchWithRetry(`${this.baseURL}/api/orchestrator/execute`,{method:"POST",body:JSON.stringify(s)})).json()}catch(a){throw console.error("❌ Failed to execute attack chain:",a),a}}async generateCreativeExploit(s){try{return await(await this.fetchWithRetry(`${this.baseURL}/api/ai/creative-exploits/generate`,{method:"POST",body:JSON.stringify(s)})).json()}catch(a){throw console.error("❌ Failed to generate creative exploit:",a),a}}async analyzeBehavior(s){try{return await(await this.fetchWithRetry(`${this.baseURL}/api/ai/behavioral-analysis/analyze`,{method:"POST",body:JSON.stringify(s)})).json()}catch(a){throw console.error("❌ Failed to analyze behavior:",a),a}}async getProxyConfigurations(){try{return(await(await this.fetchWithRetry(`${this.baseURL}/api/proxy/configurations`)).json()).configurations||[]}catch(s){return console.error("❌ Failed to fetch proxy configurations:",s),[]}}getBaseURL(){return this.baseURL}setBaseURL(s){this.baseURL=s,console.log(`🔗 API Client URL updated to: ${this.baseURL}`)}setTimeout(s){this.timeout=s}setRetryAttempts(s){this.retryAttempts=s}}const ne=new Mr;class _r{constructor(s){ge(this,"ws",null);ge(this,"config");ge(this,"callbacks",{});ge(this,"reconnectAttempts",0);ge(this,"reconnectTimer",null);ge(this,"heartbeatTimer",null);ge(this,"isConnecting",!1);ge(this,"isConnected",!1);ge(this,"messageQueue",[]);ge(this,"subscriptions",new Set);this.config={url:"wss://************:8090/ws",reconnectAttempts:5,reconnectDelay:1e3,heartbeatInterval:3e4,maxReconnectDelay:3e4,...s},console.log(`🔌 WebSocket Service initialized for: ${this.config.url}`)}async connect(){if(this.isConnecting||this.isConnected)return console.log("🔌 WebSocket already connecting or connected"),this.isConnected;try{return this.isConnecting=!0,console.log(`🔌 Connecting to WebSocket: ${this.config.url}`),this.ws=new WebSocket(this.config.url),new Promise(s=>{if(!this.ws){s(!1);return}const a=setTimeout(()=>{console.error("❌ WebSocket connection timeout"),this.cleanup(),s(!1)},1e4);this.ws.onopen=()=>{clearTimeout(a),this.isConnecting=!1,this.isConnected=!0,this.reconnectAttempts=0,console.log("✅ WebSocket connected successfully"),this.startHeartbeat(),this.processMessageQueue(),this.resubscribe(),this.callbacks.onConnect?.(),s(!0)},this.ws.onclose=n=>{clearTimeout(a),this.isConnecting=!1,this.isConnected=!1,this.stopHeartbeat();const r=n.reason||`Code: ${n.code}`;console.log(`🔌 WebSocket disconnected: ${r}`),this.callbacks.onDisconnect?.(r),n.code!==1e3&&this.reconnectAttempts<this.config.reconnectAttempts&&this.scheduleReconnect(),s(!1)},this.ws.onerror=n=>{clearTimeout(a),console.error("❌ WebSocket error:",n);const r=new Error("WebSocket connection error");this.callbacks.onError?.(r),s(!1)},this.ws.onmessage=n=>{try{const r=JSON.parse(n.data);this.handleMessage(r)}catch(r){console.error("❌ Failed to parse WebSocket message:",r,n.data)}}})}catch(s){return this.isConnecting=!1,console.error("❌ Failed to create WebSocket connection:",s),!1}}disconnect(){console.log("🔌 Disconnecting WebSocket..."),this.cleanup(),this.ws&&(this.ws.close(1e3,"Client disconnect"),this.ws=null)}cleanup(){this.isConnecting=!1,this.isConnected=!1,this.stopHeartbeat(),this.stopReconnectTimer()}scheduleReconnect(){if(this.reconnectTimer)return;this.reconnectAttempts++;const s=Math.min(this.config.reconnectDelay*Math.pow(2,this.reconnectAttempts-1),this.config.maxReconnectDelay);console.log(`🔄 Scheduling reconnect attempt ${this.reconnectAttempts}/${this.config.reconnectAttempts} in ${s}ms`),this.reconnectTimer=setTimeout(async()=>{this.reconnectTimer=null,await this.connect()},s)}stopReconnectTimer(){this.reconnectTimer&&(clearTimeout(this.reconnectTimer),this.reconnectTimer=null)}startHeartbeat(){this.stopHeartbeat(),this.heartbeatTimer=setInterval(()=>{this.isConnected&&this.ws?.readyState===WebSocket.OPEN&&this.send({type:"ping",timestamp:new Date().toISOString(),data:{}})},this.config.heartbeatInterval)}stopHeartbeat(){this.heartbeatTimer&&(clearInterval(this.heartbeatTimer),this.heartbeatTimer=null)}handleMessage(s){switch(console.log("📨 WebSocket message received:",s.type,s),this.callbacks.onMessage?.(s),s.type){case"tool_progress":this.callbacks.onToolProgress?.(s);break;case"tool_output":this.callbacks.onToolOutput?.(s);break;case"ai_update":this.callbacks.onAIUpdate?.(s);break;case"system_event":this.callbacks.onSystemEvent?.(s);break;case"pong":break;default:console.warn("🤷 Unknown WebSocket message type:",s.type)}}send(s){if(!this.isConnected||!this.ws||this.ws.readyState!==WebSocket.OPEN)return console.warn("📤 WebSocket not connected, queueing message:",s),this.messageQueue.push(s),!1;try{const a={...s,timestamp:s.timestamp||new Date().toISOString(),id:s.id||crypto.randomUUID()};return this.ws.send(JSON.stringify(a)),!0}catch(a){return console.error("❌ Failed to send WebSocket message:",a),!1}}processMessageQueue(){for(console.log(`📤 Processing ${this.messageQueue.length} queued messages`);this.messageQueue.length>0&&this.isConnected;){const s=this.messageQueue.shift();s&&this.send(s)}}subscribe(s){return console.log(`📺 Subscribing to channel: ${s}`),this.subscriptions.add(s),this.send({type:"subscribe",data:{channel:s}})}unsubscribe(s){return console.log(`📺 Unsubscribing from channel: ${s}`),this.subscriptions.delete(s),this.send({type:"unsubscribe",data:{channel:s}})}resubscribe(){this.subscriptions.size>0&&(console.log(`📺 Resubscribing to ${this.subscriptions.size} channels`),this.subscriptions.forEach(s=>{this.send({type:"subscribe",data:{channel:s}})}))}on(s,a){this.callbacks[s]=a}off(s){delete this.callbacks[s]}subscribeToToolProgress(s){return this.subscribe(`tool_progress:${s}`)}subscribeToToolOutput(s){return this.subscribe(`tool_output:${s}`)}subscribeToAIUpdates(){return this.subscribe("ai_updates")}subscribeToSystemEvents(){return this.subscribe("system_events")}isConnectedToBackend(){return this.isConnected&&this.ws?.readyState===WebSocket.OPEN}getConnectionState(){return this.isConnecting?"connecting":this.isConnected?"connected":this.ws?.readyState===WebSocket.CLOSED||this.ws?.readyState===WebSocket.CLOSING?"disconnected":"error"}getReconnectAttempts(){return this.reconnectAttempts}getSubscriptions(){return Array.from(this.subscriptions)}updateConfig(s){this.config={...this.config,...s},console.log("🔧 WebSocket config updated:",this.config)}getConfig(){return{...this.config}}}const Ye=new _r,Ht={status:{connected:!1,url:ne.getBaseURL()},isConnecting:!1,connectionHistory:[],tools:[],toolsLoading:!1,ferrariCapabilities:null,ferrariLoading:!1,aiStatus:null,aiLoading:!1,websocketConnected:!1,websocketReconnectAttempts:0,activeWorkflows:new Map,workflowHistory:[]},Pe=ha()(br(ba((t,s)=>({...Ht,connect:async()=>{console.log("🔗 Connecting to backend..."),t(a=>{a.isConnecting=!0,a.lastConnectionAttempt=new Date});try{console.log("🔗 Attempting to connect to backend...");let a;if(window.electronAPI){console.log("🔗 Using Electron Backend Manager for connection...");try{const n=await window.electronAPI.backend.getStatus();if(a={connected:n.connected,url:n.url,version:n.version,lastConnected:n.connected?new Date:void 0,tools:n.toolsCount?{total:n.toolsCount,operational:n.toolsCount,failed:0}:void 0,error:n.error},!n.connected&&(console.log("🔄 Electron backend not connected, attempting connection..."),await window.electronAPI.backend.connect())){const c=await window.electronAPI.backend.getStatus();a.connected=c.connected,a.lastConnected=new Date}}catch(n){console.warn("⚠️ Electron Backend Manager failed, falling back to API client:",n),a=await ne.checkHealth()}}else console.log("🔗 Using API client for connection..."),a=await ne.checkHealth();return t(n=>{n.status=a,n.isConnecting=!1,n.connectionHistory.unshift({timestamp:new Date,success:a.connected,error:a.error}),n.connectionHistory.length>50&&(n.connectionHistory=n.connectionHistory.slice(0,50))}),a.connected?(console.log("✅ Backend connected successfully"),C.success("Connected to backend"),console.log("🔧 Starting tools refresh..."),await s().refreshTools(),console.log("🏎️ Starting Ferrari capabilities refresh..."),s().refreshFerrariCapabilities(),console.log("🤖 Starting AI status refresh..."),s().refreshAIStatus(),s().connectWebSocket(),!0):(console.error("❌ Backend connection failed:",a.error),C.error(`Backend connection failed: ${a.error}`),!1)}catch(a){return console.error("❌ Backend connection error:",a),t(n=>{n.status={connected:!1,url:ne.getBaseURL(),error:a instanceof Error?a.message:"Unknown error"},n.isConnecting=!1,n.connectionHistory.unshift({timestamp:new Date,success:!1,error:a instanceof Error?a.message:"Unknown error"})}),C.error("Failed to connect to backend"),!1}},disconnect:()=>{console.log("🔌 Disconnecting from backend..."),t(a=>{a.status={connected:!1,url:ne.getBaseURL()},a.tools=[],a.ferrariCapabilities=null,a.aiStatus=null}),s().disconnectWebSocket(),C.info("Disconnected from backend")},checkHealth:async()=>{try{let a;if(window.electronAPI){const n=await window.electronAPI.backend.getStatus();a={connected:n.connected,url:n.url,version:n.version,lastConnected:n.connected?new Date:void 0,tools:n.toolsCount?{total:n.toolsCount,operational:n.toolsCount,failed:0}:void 0,error:n.error}}else a=await ne.checkHealth();return t(n=>{n.status=a}),a.connected}catch(a){return console.error("❌ Health check failed:",a),t(n=>{n.status={connected:!1,url:window.electronAPI?"Electron Backend Manager":ne.getBaseURL(),error:a instanceof Error?a.message:"Unknown error"}}),!1}},refreshTools:async()=>{if(!s().status.connected){console.warn("⚠️ Cannot refresh tools - backend not connected");return}t(n=>{n.toolsLoading=!0});try{console.log("🔧 Refreshing tools list...");const n=await ne.getAvailableTools();t(p=>{p.tools=n,p.toolsLoading=!1,p.toolsLastUpdated=new Date;const d=n.filter(b=>b.status==="available").length,g=n.filter(b=>b.status==="error").length;p.status.tools={total:n.length,operational:d,failed:g},console.log("🔧 Backend Store - Updated status.tools:",{total:n.length,operational:d,failed:g})});const r=n.filter(p=>p.status==="available").length,c=n.filter(p=>p.status==="error").length;console.log(`✅ Loaded ${n.length} tools (${r} operational, ${c} failed)`)}catch(n){console.error("❌ Failed to refresh tools:",n),t(r=>{r.toolsLoading=!1}),C.error("Failed to load security tools")}},getToolDetails:async a=>{try{return await ne.getToolDetails(a)}catch(n){return console.error(`❌ Failed to get tool details for ${a}:`,n),null}},refreshFerrariCapabilities:async()=>{if(!s().status.connected){console.warn("⚠️ Cannot refresh Ferrari capabilities - backend not connected");return}t(n=>{n.ferrariLoading=!0});try{console.log("🏎️ Refreshing Ferrari AI capabilities...");const n=await ne.getFerrariCapabilities();t(r=>{r.ferrariCapabilities=n,r.ferrariLoading=!1}),console.log("✅ Ferrari AI capabilities loaded")}catch(n){console.error("❌ Failed to refresh Ferrari capabilities:",n),t(r=>{r.ferrariLoading=!1})}},refreshAIStatus:async()=>{if(!s().status.connected){console.warn("⚠️ Cannot refresh AI status - backend not connected");return}t(n=>{n.aiLoading=!0});try{console.log("🤖 Refreshing AI status...");const n=await ne.getAIStatus();t(r=>{r.aiStatus=n,r.aiLoading=!1}),console.log("✅ AI status loaded")}catch(n){console.error("❌ Failed to refresh AI status:",n),t(r=>{r.aiLoading=!1})}},connectWebSocket:async()=>{try{console.log("🔌 Connecting to WebSocket...");const a=await Ye.connect();return t(n=>{n.websocketConnected=a,a&&(n.websocketReconnectAttempts=0)}),a&&(Ye.on("onDisconnect",()=>{t(n=>{n.websocketConnected=!1})}),Ye.on("onConnect",()=>{t(n=>{n.websocketConnected=!0,n.websocketReconnectAttempts=0})}),Ye.subscribeToSystemEvents(),Ye.subscribeToAIUpdates()),a}catch(a){return console.error("❌ WebSocket connection failed:",a),t(n=>{n.websocketConnected=!1,n.websocketReconnectAttempts++}),!1}},disconnectWebSocket:()=>{console.log("🔌 Disconnecting WebSocket..."),Ye.disconnect(),t(a=>{a.websocketConnected=!1,a.websocketReconnectAttempts=0})},updateWorkflowExecution:a=>{t(n=>{if(n.activeWorkflows.set(a.id,a),a.status==="completed"||a.status==="failed"||a.status==="cancelled"){const r={...a};n.workflowHistory.unshift(r),n.activeWorkflows.delete(a.id),n.workflowHistory.length>100&&(n.workflowHistory=n.workflowHistory.slice(0,100))}})},getActiveWorkflows:()=>{const{activeWorkflows:a}=s();return Array.from(a.values())},getWorkflowById:a=>{const{activeWorkflows:n,workflowHistory:r}=s();return n.get(a)||r.find(c=>c.id===a)},cancelWorkflow:a=>{t(n=>{const r=n.activeWorkflows.get(a);r&&r.status==="running"&&(r.status="cancelled",r.completedAt=new Date,n.workflowHistory.unshift({...r}),n.activeWorkflows.delete(a))})},clearWorkflowHistory:()=>{t(a=>{a.workflowHistory=[]})},setConnectionStatus:a=>{t(n=>{Object.assign(n.status,a)})},reset:()=>{t(()=>({...Ht}))}})))),zr=t=>Pe(s=>s.tools.filter(a=>a.category===t));function Br({isOpen:t,currentPath:s,className:a}){const{status:n}=Pe(),[r,c]=x.useState(new Set(["tools"])),p=[{id:"dashboard",label:"Dashboard",path:"/dashboard",icon:e.jsx(ts,{className:"h-4 w-4"})},{id:"campaigns",label:"Campaigns",path:"/campaigns",icon:e.jsx(Xa,{className:"h-4 w-4"})},{id:"tools",label:"Security Tools",path:"/tools",icon:e.jsx(Za,{className:"h-4 w-4"}),badge:n.tools?.operational||0,children:[{id:"network-scanning",label:"Network Scanning",path:"/tools/network-scanning",icon:e.jsx(Oe,{className:"h-4 w-4"})},{id:"web-testing",label:"Web Testing",path:"/tools/web-testing",icon:e.jsx(bs,{className:"h-4 w-4"})},{id:"vulnerability-assessment",label:"Vulnerability Assessment",path:"/tools/vulnerability-assessment",icon:e.jsx(fe,{className:"h-4 w-4"})}]},{id:"ferrari",label:"Ferrari AI",path:"/ferrari",icon:e.jsx(en,{className:"h-4 w-4"}),children:[{id:"orchestrator",label:"Multi-Stage Orchestrator",path:"/ferrari/orchestrator",icon:e.jsx(Xe,{className:"h-4 w-4"})},{id:"creative-exploits",label:"Creative Exploits",path:"/ferrari/creative-exploits",icon:e.jsx(ue,{className:"h-4 w-4"})}]},{id:"reports",label:"Reports",path:"/reports",icon:e.jsx(xe,{className:"h-4 w-4"})},{id:"settings",label:"Settings",path:"/settings",icon:e.jsx(Y,{className:"h-4 w-4"})}],d=j=>{const R=new Set(r);R.has(j)?R.delete(j):R.add(j),c(R)},g=j=>s.startsWith(j.path)&&j.path!=="/",b=(j,R=0)=>{const E=g(j),f=j.children&&j.children.length>0,L=r.has(j.id),i=R>0?"ml-6":"";return e.jsxs("div",{className:B("space-y-1",i),children:[f?e.jsxs(v,{variant:"ghost",onClick:()=>d(j.id),className:B("w-full justify-start",E?"bg-accent":""),children:[e.jsxs("div",{className:"flex items-center gap-3 flex-1",children:[j.icon,e.jsx("span",{className:"flex-1 text-left",children:j.label}),j.badge&&e.jsx($,{variant:"secondary",className:"ml-auto",children:j.badge})]}),L?e.jsx(ut,{className:"h-4 w-4 ml-2"}):e.jsx(la,{className:"h-4 w-4 ml-2"})]}):e.jsx(Xs,{to:j.path,children:e.jsx(v,{variant:"ghost",className:B("w-full justify-start",E?"bg-accent":""),children:e.jsxs("div",{className:"flex items-center gap-3 w-full",children:[j.icon,e.jsx("span",{className:"flex-1 text-left",children:j.label}),j.badge&&e.jsx($,{variant:"secondary",className:"ml-auto",children:j.badge})]})})}),f&&L&&e.jsx("div",{className:"ml-4 space-y-1",children:j.children?.map(u=>b(u,R+1))})]},j.id)};return t?e.jsxs("aside",{className:B("w-64 flex-shrink-0 border-r border-border bg-card","flex flex-col h-full",a),children:[e.jsx("div",{className:"p-4 border-b border-border",children:e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center",children:e.jsx("span",{className:"text-white font-bold text-sm",children:"N"})}),e.jsxs("div",{children:[e.jsx("h2",{className:"font-semibold text-foreground",children:"NexusScan"}),e.jsx("p",{className:"text-xs text-muted-foreground",children:"Desktop"})]})]})}),e.jsx("nav",{className:"flex-1 p-4 space-y-2 overflow-y-auto",children:p.map(j=>b(j))}),e.jsx("div",{className:"p-4 border-t border-border",children:e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex items-center justify-between text-xs",children:[e.jsx("span",{className:"text-muted-foreground",children:"Backend"}),e.jsx($,{variant:n.connected?"default":"destructive",className:"text-xs",children:n.connected?"Online":"Offline"})]}),n.tools&&e.jsxs("div",{className:"flex items-center justify-between text-xs",children:[e.jsx("span",{className:"text-muted-foreground",children:"Tools"}),e.jsxs($,{variant:"outline",className:"text-xs",children:[n.tools.operational,"/",n.tools.total]})]})]})})]}):null}const ot={autoSave:!0,notifications:!0,soundEnabled:!1,compactMode:!1,defaultTimeout:300,maxConcurrentScans:3,retryAttempts:3},Wr={width:1200,height:800,x:100,y:100,isMaximized:!1,isFullscreen:!1},Vt={isLoading:!1,sidebarOpen:!0,terminalOpen:!1,theme:"system",version:"1.0.0",platform:"unknown",arch:"unknown",preferences:ot,windowState:Wr,recentActivity:[]},Na=ha()(Sr(ba((t,s)=>({...Vt,setLoading:a=>t(n=>{n.isLoading=a}),toggleSidebar:()=>t(a=>{a.sidebarOpen=!a.sidebarOpen}),setSidebarOpen:a=>t(n=>{n.sidebarOpen=a}),toggleTerminal:()=>t(a=>{a.terminalOpen=!a.terminalOpen}),setTerminalOpen:a=>t(n=>{n.terminalOpen=a}),setTheme:a=>t(n=>{n.theme=a;const r=document.documentElement;a==="dark"?r.classList.add("dark"):a==="light"?r.classList.remove("dark"):window.matchMedia("(prefers-color-scheme: dark)").matches?r.classList.add("dark"):r.classList.remove("dark")}),toggleTheme:()=>{const a=s().theme,n=a==="light"?"dark":a==="dark"?"system":"light";s().setTheme(n)},setAppInfo:a=>t(n=>{n.version=a.version,n.platform=a.platform,n.arch=a.arch}),updatePreferences:a=>t(n=>{Object.assign(n.preferences,a)}),resetPreferences:()=>t(a=>{a.preferences={...ot}}),updateWindowState:a=>t(n=>{Object.assign(n.windowState,a)}),addActivity:a=>t(n=>{const r={...a,id:crypto.randomUUID(),timestamp:new Date};n.recentActivity.unshift(r),n.recentActivity.length>100&&(n.recentActivity=n.recentActivity.slice(0,100))}),clearActivity:()=>t(a=>{a.recentActivity=[]}),reset:()=>t(()=>({...Vt}))})),{name:"nexusscan-app-state",version:1,partialize:t=>({sidebarOpen:t.sidebarOpen,terminalOpen:t.terminalOpen,theme:t.theme,preferences:t.preferences,windowState:t.windowState}),migrate:(t,s)=>s===0?{...t,preferences:{...ot,...t.preferences}}:t}));class Ur{constructor(){ge(this,"api",null);this.api=this.getAPI()}isElectron(){return typeof window<"u"&&window.electronAPI!==void 0}getAPI(){return typeof window<"u"&&window.electronAPI?window.electronAPI:null}ensureAPI(){if(!this.api)throw new Error("Electron API not available. Make sure you are running in Electron.");return this.api}async getName(){return await this.ensureAPI().getName()}async getVersion(){return await this.ensureAPI().getVersion()}async getPlatform(){return await this.ensureAPI().getPlatform()}async getArch(){return await this.ensureAPI().getArch()}async readFile(s){return await this.ensureAPI().fileSystem.readFile(s)}async writeFile(s,a){await this.ensureAPI().fileSystem.writeFile(s,a)}async fileExists(s){return await this.ensureAPI().fileSystem.exists(s)}async selectFile(s){return await this.ensureAPI().fileSystem.selectFile(s)}async selectDirectory(s){return await this.ensureAPI().fileSystem.selectDirectory(s)}async saveFile(s){return await this.ensureAPI().fileSystem.saveFile(s)}async getSystemInfo(){return await this.ensureAPI().system.getSystemInfo()}async openExternal(s){await this.ensureAPI().system.openExternal(s)}async showItemInFolder(s){await this.ensureAPI().system.showItemInFolder(s)}async showNotification(s){await this.ensureAPI().notification.show(s)}on(s,a){this.ensureAPI().on(s,a)}off(s,a){this.ensureAPI().off(s,a)}once(s,a){this.ensureAPI().once(s,a)}async invoke(s,...a){return await this.ensureAPI().invoke(s,...a)}send(s,...a){this.ensureAPI().send(s,...a)}get window(){return{minimize:()=>this.invoke("window-minimize"),maximize:()=>this.invoke("window-maximize"),toggleMaximize:()=>this.invoke("window-toggle-maximize"),close:()=>this.invoke("window-close"),isMaximized:()=>this.invoke("window-is-maximized")}}}const we=new Ur;function Hr({className:t}){const s=dt(),a=ra(),{sidebarOpen:n,terminalOpen:r,toggleSidebar:c,toggleTerminal:p,theme:d,toggleTheme:g}=Na(),{status:b}=Pe(),j=()=>{const u=s.pathname;if(u.startsWith("/ferrari"))return u.includes("orchestrator")?"Ferrari AI - Orchestrator":u.includes("creative-exploits")?"Ferrari AI - Creative Exploits":u.includes("behavioral-analysis")?"Ferrari AI - Behavioral Analysis":u.includes("ai-proxy")?"Ferrari AI - AI Proxy":"Ferrari AI Dashboard";if(u.startsWith("/tools"))return u.includes("network-scanning")?"Tools - Network Scanning":u.includes("web-testing")?"Tools - Web Testing":u.includes("vulnerability-assessment")?"Tools - Vulnerability Assessment":u.includes("password-tools")?"Tools - Password Tools":u.includes("ssl-testing")?"Tools - SSL Testing":u.includes("exploitation")?"Tools - Exploitation":"Security Tools";switch(u){case"/dashboard":return"Dashboard";case"/campaigns":return"Campaigns";case"/reports":return"Reports";case"/settings":return"Settings";default:return"NexusScan Desktop"}},R=async u=>{if(we.isElectron())try{switch(u){case"minimize":await we.window.minimize();break;case"maximize":await we.window.toggleMaximize();break;case"close":await we.window.close();break}}catch(k){console.error(`Failed to ${u} window:`,k)}},E=()=>b.connected?{icon:e.jsx(be,{className:"h-4 w-4"}),text:"Connected",variant:"default",className:"status-online"}:{icon:e.jsx(an,{className:"h-4 w-4"}),text:"Offline",variant:"destructive",className:"status-offline"},f=()=>({icon:e.jsx(be,{className:"h-4 w-4"}),text:"Remote Backend",variant:"default",className:"status-remote"}),L=E(),i=f();return e.jsxs("header",{className:B("flex items-center justify-between h-14 px-4 border-b border-border bg-background/95 backdrop-blur-sm","supports-[backdrop-filter]:bg-background/60",t),children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(v,{variant:"ghost",size:"sm",onClick:()=>{console.log("🔘 Sidebar toggle clicked!"),c()},className:"p-2",title:n?"Hide sidebar":"Show sidebar",children:n?e.jsx(Zs,{className:"h-4 w-4"}):e.jsx(sn,{className:"h-4 w-4"})}),e.jsx("div",{className:"flex items-center gap-2",children:e.jsx("h1",{className:"text-lg font-semibold text-foreground",children:j()})})]}),e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsxs($,{variant:L.variant,className:B("flex items-center gap-1.5",L.className),children:[L.icon,e.jsx("span",{className:"text-xs",children:L.text})]}),e.jsxs($,{variant:"default",className:"flex items-center gap-1.5 bg-blue-500 hover:bg-blue-600",children:[i.icon,e.jsx("span",{className:"text-xs",children:i.text})]}),b.connected&&b.tools&&e.jsx($,{variant:"outline",className:"flex items-center gap-1.5",children:e.jsxs("span",{className:"text-xs",children:[b.tools.operational,"/",b.tools.total," Tools"]})})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(v,{variant:"ghost",size:"sm",onClick:()=>{console.log("🔘 Terminal toggle clicked!"),p()},className:B("p-2",r&&"bg-accent text-accent-foreground"),title:r?"Hide terminal":"Show terminal",children:e.jsx(Se,{className:"h-4 w-4"})}),e.jsx(v,{variant:"ghost",size:"sm",onClick:()=>a("/settings"),className:"p-2",title:"Settings",children:e.jsx(Y,{className:"h-4 w-4"})}),e.jsx(v,{variant:"ghost",size:"sm",onClick:()=>{console.log("Help clicked")},className:"p-2",title:"Help",children:e.jsx(tn,{className:"h-4 w-4"})}),we.isElectron()&&e.jsxs("div",{className:"flex items-center ml-2 -mr-2",children:[e.jsx(v,{variant:"ghost",size:"sm",onClick:()=>R("minimize"),className:"p-2 hover:bg-muted/50 rounded-none h-8",title:"Minimize",children:e.jsx(ca,{className:"h-3 w-3"})}),e.jsx(v,{variant:"ghost",size:"sm",onClick:()=>R("maximize"),className:"p-2 hover:bg-muted/50 rounded-none h-8",title:"Maximize",children:e.jsx(We,{className:"h-3 w-3"})}),e.jsx(v,{variant:"ghost",size:"sm",onClick:()=>R("close"),className:"p-2 hover:bg-destructive hover:text-destructive-foreground rounded-none h-8",title:"Close",children:e.jsx(Zs,{className:"h-3 w-3"})})]})]})]})}function Vr({backendStatus:t}){return x.useEffect(()=>{console.log("📊 StatusBar - Remote Backend Status:",{connected:t.connected,error:t.error,url:t.url,tools:t.tools}),t.tools?console.log("🔧 StatusBar - Remote Tools Available:",{total:t.tools.total,operational:t.tools.operational,failed:t.tools.failed,display:`${t.tools.operational}/${t.tools.total}`}):console.log("⚠️ StatusBar - No tools data from remote backend")},[t]),e.jsxs("div",{className:"h-8 bg-background border-t px-4 flex items-center justify-between text-xs",children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("div",{className:"flex items-center gap-2",children:t.connected?e.jsxs(e.Fragment,{children:[e.jsx(nn,{className:"h-3 w-3 text-green-500"}),e.jsx("span",{className:"text-green-500",children:"Remote Backend Connected"})]}):t.error?e.jsxs(e.Fragment,{children:[e.jsx(ee,{className:"h-3 w-3 text-red-500"}),e.jsx("span",{className:"text-red-500",children:"Remote Backend Error"})]}):e.jsxs(e.Fragment,{children:[e.jsx(ee,{className:"h-3 w-3 text-yellow-500"}),e.jsx("span",{className:"text-yellow-500",children:"Remote Backend Offline"})]})}),e.jsx("div",{className:"h-4 w-px bg-border"}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(bs,{className:"h-3 w-3 text-blue-500"}),e.jsx("span",{className:"text-blue-500",children:t.url?`Server: ${t.url.replace("http://","").replace("https://","")}`:"Server: ************:8090"})]}),e.jsx("div",{className:"h-4 w-px bg-border"}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(as,{className:"h-3 w-3 text-muted-foreground"}),e.jsx("span",{children:t.tools?`Security Tools: ${t.tools.operational}/${t.tools.total}`:t.connected?"Security Tools: Loading...":"Security Tools: Offline"})]})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(ve,{className:"h-3 w-3 text-muted-foreground"}),e.jsx("span",{children:"NexusScan Desktop - Remote Mode"})]}),e.jsx("div",{className:"h-4 w-px bg-border"}),e.jsx("span",{className:"text-muted-foreground",children:"v1.0.0"})]})]})}function qr({isOpen:t,onToggle:s,className:a,height:n=300}){const[r,c]=x.useState([]),[p,d]=x.useState(!1),[g,b]=x.useState(!0),[j,R]=x.useState(""),E=K.useRef(null),f=K.useRef(null);x.useEffect(()=>(i({content:"NexusScan Desktop Terminal initialized. Ready for tool execution...",type:"info"}),Ye.on("onToolOutput",L),()=>{Ye.off("onToolOutput")}),[]),K.useEffect(()=>{g&&E.current&&(E.current.scrollTop=E.current.scrollHeight)},[r,g]);const L=z=>{i({content:z.data.output,type:z.data.stream==="stderr"?"error":"output",executionId:z.data.executionId,toolId:z.data.toolId})},i=z=>{const X={id:crypto.randomUUID(),timestamp:new Date,...z};c(m=>[...m,X].slice(-1e3))},u=()=>{c([]),i({content:"Terminal cleared",type:"info"})},k=async()=>{const z=U.map(X=>`[${X.timestamp.toLocaleTimeString()}] ${X.content}`).join(`
`);try{await navigator.clipboard.writeText(z),C.success("Terminal content copied to clipboard")}catch{C.error("Failed to copy terminal content")}},N=()=>{const z=U.map(l=>`[${l.timestamp.toISOString()}] [${l.type.toUpperCase()}] ${l.content}`).join(`
`),X=new Blob([z],{type:"text/plain"}),m=URL.createObjectURL(X),o=document.createElement("a");o.href=m,o.download=`nexusscan-terminal-${new Date().toISOString().slice(0,19)}.txt`,document.body.appendChild(o),o.click(),document.body.removeChild(o),URL.revokeObjectURL(m),C.success("Terminal content exported")},D=()=>{d(!p)},W=()=>{if(E.current){const{scrollTop:z,scrollHeight:X,clientHeight:m}=E.current,o=z+m>=X-10;b(o)}},U=x.useMemo(()=>j?r.filter(z=>z.content.toLowerCase().includes(j.toLowerCase())||z.type.toLowerCase().includes(j.toLowerCase())||z.toolId&&z.toolId.toLowerCase().includes(j.toLowerCase())):r,[r,j]),S=z=>{switch(z){case"info":return"text-blue-400";case"success":return"text-green-400";case"warning":return"text-yellow-400";case"error":return"text-red-400";case"command":return"text-purple-400 font-medium";case"output":default:return"text-gray-300"}},I=z=>{switch(z){case"info":return"[INFO]";case"success":return"[SUCCESS]";case"warning":return"[WARN]";case"error":return"[ERROR]";case"command":return"[CMD]";case"output":default:return"[OUT]"}};if(!t)return null;const q=p?"60vh":`${n}px`;return e.jsxs("div",{ref:f,className:B("border-t border-border bg-card","flex flex-col",a),style:{height:q},children:[e.jsxs("div",{className:"flex items-center justify-between px-4 py-2 border-b border-border bg-muted/30",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(Se,{className:"h-4 w-4"}),e.jsx("span",{className:"font-medium text-sm",children:"Terminal"}),e.jsxs($,{variant:"outline",className:"text-xs",children:[U.length," lines"]})]}),e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx("input",{type:"text",placeholder:"Filter...",value:j,onChange:z=>R(z.target.value),className:"px-2 py-1 text-xs bg-background border border-border rounded w-24 focus:w-32 transition-all focus:outline-none focus:ring-1 focus:ring-primary"}),!g&&e.jsx($,{variant:"secondary",className:"text-xs cursor-pointer",onClick:()=>b(!0),children:"Scroll paused"}),e.jsx(v,{variant:"ghost",size:"sm",onClick:k,className:"p-1 h-6 w-6",title:"Copy all",children:e.jsx(je,{className:"h-3 w-3"})}),e.jsx(v,{variant:"ghost",size:"sm",onClick:N,className:"p-1 h-6 w-6",title:"Export to file",children:e.jsx(ke,{className:"h-3 w-3"})}),e.jsx(v,{variant:"ghost",size:"sm",onClick:u,className:"p-1 h-6 w-6",title:"Clear terminal",children:e.jsx(rn,{className:"h-3 w-3"})}),e.jsx(v,{variant:"ghost",size:"sm",onClick:D,className:"p-1 h-6 w-6",title:p?"Minimize":"Maximize",children:p?e.jsx(ca,{className:"h-3 w-3"}):e.jsx(ln,{className:"h-3 w-3"})}),e.jsx(v,{variant:"ghost",size:"sm",onClick:s,className:"p-1 h-6 w-6",title:"Close terminal",children:e.jsx(Zs,{className:"h-3 w-3"})})]})]}),e.jsxs("div",{ref:E,className:"flex-1 overflow-y-auto p-4 bg-gray-900 text-gray-100 font-mono text-sm leading-relaxed",onScroll:W,children:[U.length===0?e.jsx("div",{className:"text-gray-500 italic",children:j?"No lines match the current filter":"No output yet..."}):U.map(z=>e.jsxs("div",{className:"flex gap-3 mb-1 hover:bg-gray-800/50 px-2 py-1 rounded",children:[e.jsx("span",{className:"text-gray-500 text-xs shrink-0 w-20",children:z.timestamp.toLocaleTimeString()}),e.jsx("span",{className:B("text-xs shrink-0 w-16",S(z.type)),children:I(z.type)}),z.toolId&&e.jsxs("span",{className:"text-blue-300 text-xs shrink-0 w-20 truncate",children:["[",z.toolId,"]"]}),e.jsx("span",{className:B("flex-1 break-all",S(z.type)),children:z.content})]},z.id)),e.jsx("div",{id:"terminal-bottom"})]})]})}const y=x.forwardRef(({className:t,...s},a)=>e.jsx("div",{ref:a,className:B("rounded-lg border bg-card text-card-foreground shadow-sm",t),...s}));y.displayName="Card";const T=x.forwardRef(({className:t,...s},a)=>e.jsx("div",{ref:a,className:B("flex flex-col space-y-1.5 p-6",t),...s}));T.displayName="CardHeader";const A=x.forwardRef(({className:t,...s},a)=>e.jsx("h3",{ref:a,className:B("text-2xl font-semibold leading-none tracking-tight",t),...s}));A.displayName="CardTitle";const F=x.forwardRef(({className:t,...s},a)=>e.jsx("p",{ref:a,className:B("text-sm text-muted-foreground",t),...s}));F.displayName="CardDescription";const w=x.forwardRef(({className:t,...s},a)=>e.jsx("div",{ref:a,className:B("p-6 pt-0",t),...s}));w.displayName="CardContent";const Gr=x.forwardRef(({className:t,...s},a)=>e.jsx("div",{ref:a,className:B("flex items-center p-6 pt-0",t),...s}));Gr.displayName="CardFooter";const Ae=x.forwardRef(({className:t,value:s=0,max:a=100,...n},r)=>{const c=Math.min(Math.max(s/a*100,0),100);return e.jsx("div",{ref:r,className:B("relative h-4 w-full overflow-hidden rounded-full bg-secondary",t),...n,children:e.jsx("div",{className:"h-full w-full flex-1 bg-primary transition-all",style:{transform:`translateX(-${100-c}%)`}})})});Ae.displayName="Progress";function Qr(){const[t,s]=x.useState({totalScans:0,activeScans:0,vulnerabilities:0,lastScanTime:"Never",systemHealth:"healthy",toolsAvailable:0}),[a,n]=x.useState([]),r=p=>{switch(p){case"healthy":return"text-green-500";case"warning":return"text-yellow-500";case"error":return"text-red-500";default:return"text-gray-500"}},c=p=>{switch(p){case"healthy":return e.jsx(be,{className:"h-4 w-4"});case"warning":return e.jsx(ee,{className:"h-4 w-4"});case"error":return e.jsx(ee,{className:"h-4 w-4"});default:return e.jsx(ve,{className:"h-4 w-4"})}};return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-3xl font-bold tracking-tight",children:"Dashboard"}),e.jsx("p",{className:"text-muted-foreground",children:"Overview of your security testing activities"})]}),e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsxs($,{variant:"outline",className:r(t.systemHealth),children:[c(t.systemHealth),"System ",t.systemHealth]}),e.jsx(v,{children:"Start New Scan"})]})]}),e.jsxs("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-4",children:[e.jsxs(y,{children:[e.jsxs(T,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[e.jsx(A,{className:"text-sm font-medium",children:"Total Scans"}),e.jsx(fe,{className:"h-4 w-4 text-muted-foreground"})]}),e.jsxs(w,{children:[e.jsx("div",{className:"text-2xl font-bold",children:t.totalScans}),e.jsx("p",{className:"text-xs text-muted-foreground",children:"+2 from last week"})]})]}),e.jsxs(y,{children:[e.jsxs(T,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[e.jsx(A,{className:"text-sm font-medium",children:"Active Scans"}),e.jsx(ve,{className:"h-4 w-4 text-muted-foreground"})]}),e.jsxs(w,{children:[e.jsx("div",{className:"text-2xl font-bold",children:t.activeScans}),e.jsx("p",{className:"text-xs text-muted-foreground",children:"Currently running"})]})]}),e.jsxs(y,{children:[e.jsxs(T,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[e.jsx(A,{className:"text-sm font-medium",children:"Vulnerabilities"}),e.jsx(ee,{className:"h-4 w-4 text-muted-foreground"})]}),e.jsxs(w,{children:[e.jsx("div",{className:"text-2xl font-bold",children:t.vulnerabilities}),e.jsx("p",{className:"text-xs text-muted-foreground",children:"Discovered this month"})]})]}),e.jsxs(y,{children:[e.jsxs(T,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[e.jsx(A,{className:"text-sm font-medium",children:"Tools Available"}),e.jsx(as,{className:"h-4 w-4 text-muted-foreground"})]}),e.jsxs(w,{children:[e.jsx("div",{className:"text-2xl font-bold",children:t.toolsAvailable}),e.jsx("p",{className:"text-xs text-muted-foreground",children:"Security tools ready"})]})]})]}),e.jsxs("div",{className:"grid gap-6 md:grid-cols-7",children:[e.jsxs(y,{className:"md:col-span-4",children:[e.jsxs(T,{children:[e.jsx(A,{children:"Recent Activity"}),e.jsx(F,{children:"Latest security testing activities"})]}),e.jsx(w,{children:e.jsx("div",{className:"space-y-4",children:a.length>0?a.map(p=>e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs("div",{className:"h-8 w-8 rounded-full bg-primary/10 flex items-center justify-center",children:[p.type==="scan"&&e.jsx(fe,{className:"h-4 w-4"}),p.type==="vulnerability"&&e.jsx(ee,{className:"h-4 w-4"}),p.type==="system"&&e.jsx(as,{className:"h-4 w-4"})]}),e.jsxs("div",{className:"flex-1 space-y-1",children:[e.jsx("p",{className:"text-sm font-medium",children:p.message}),e.jsxs("p",{className:"text-xs text-muted-foreground flex items-center gap-1",children:[e.jsx(Ve,{className:"h-3 w-3"}),p.time]})]})]},p.id)):e.jsxs("div",{className:"text-center py-8 text-muted-foreground",children:[e.jsx(ve,{className:"h-8 w-8 mx-auto mb-2 opacity-50"}),e.jsx("p",{children:"No recent activity"}),e.jsx("p",{className:"text-xs",children:"Start a security scan to see activity here"})]})})})]}),e.jsxs(y,{className:"md:col-span-3",children:[e.jsxs(T,{children:[e.jsx(A,{children:"Quick Actions"}),e.jsx(F,{children:"Common security testing tasks"})]}),e.jsx(w,{children:e.jsxs("div",{className:"space-y-3",children:[e.jsxs(v,{className:"w-full justify-start",variant:"outline",children:[e.jsx(Oe,{className:"mr-2 h-4 w-4"}),"Network Scan"]}),e.jsxs(v,{className:"w-full justify-start",variant:"outline",children:[e.jsx(He,{className:"mr-2 h-4 w-4"}),"Vulnerability Assessment"]}),e.jsxs(v,{className:"w-full justify-start",variant:"outline",children:[e.jsx(fe,{className:"mr-2 h-4 w-4"}),"Security Audit"]}),e.jsxs(v,{className:"w-full justify-start",variant:"outline",children:[e.jsx(ve,{className:"mr-2 h-4 w-4"}),"Performance Test"]})]})})]})]}),e.jsxs(y,{children:[e.jsxs(T,{children:[e.jsx(A,{children:"System Status"}),e.jsx(F,{children:"Current status of backend services and tools"})]}),e.jsx(w,{children:e.jsxs("div",{className:"grid gap-4 md:grid-cols-3",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm font-medium",children:"Backend Connection"}),e.jsxs($,{variant:"outline",className:"text-green-500",children:[e.jsx(be,{className:"mr-1 h-3 w-3"}),"Connected"]})]}),e.jsx(Ae,{value:100,className:"h-2"})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm font-medium",children:"AI Services"}),e.jsxs($,{variant:"outline",className:"text-green-500",children:[e.jsx(be,{className:"mr-1 h-3 w-3"}),"Online"]})]}),e.jsx(Ae,{value:85,className:"h-2"})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm font-medium",children:"Security Tools"}),e.jsxs($,{variant:"outline",className:"text-yellow-500",children:[e.jsx(ve,{className:"mr-1 h-3 w-3"}),"Loading"]})]}),e.jsx(Ae,{value:60,className:"h-2"})]})]})})]})]})}const P=x.forwardRef(({className:t,type:s,...a},n)=>e.jsx("input",{type:s,className:B("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",t),ref:n,...a}));P.displayName="Input";function Jr(){const[t,s]=x.useState(""),[a,n]=x.useState([{id:"1",name:"Production Environment Audit",description:"Comprehensive security assessment of production systems",status:"active",target:"***********/24",createdAt:"2024-01-15",lastRun:"2024-01-20",progress:75,vulnerabilities:3,toolsUsed:["nmap","nuclei","sqlmap"]},{id:"2",name:"Web Application Security Test",description:"OWASP Top 10 vulnerability assessment",status:"completed",target:"https://example.com",createdAt:"2024-01-10",lastRun:"2024-01-18",progress:100,vulnerabilities:7,toolsUsed:["dirb","nuclei","nikto"]}]),r=j=>{switch(j){case"active":return"bg-green-500";case"paused":return"bg-yellow-500";case"completed":return"bg-blue-500";case"failed":return"bg-red-500";default:return"bg-gray-500"}},c=j=>{switch(j){case"active":return e.jsx(ae,{className:"h-3 w-3"});case"paused":return e.jsx(et,{className:"h-3 w-3"});case"completed":return e.jsx(be,{className:"h-3 w-3"});case"failed":return e.jsx(ee,{className:"h-3 w-3"});default:return e.jsx(cn,{className:"h-3 w-3"})}},p=a.filter(j=>j.name.toLowerCase().includes(t.toLowerCase())||j.target.toLowerCase().includes(t.toLowerCase())),d=()=>{console.log("Creating new campaign...")},g=j=>{console.log(`Running campaign ${j}...`)},b=j=>{console.log(`Pausing campaign ${j}...`)};return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-3xl font-bold tracking-tight",children:"Security Campaigns"}),e.jsx("p",{className:"text-muted-foreground",children:"Manage and monitor your security testing campaigns"})]}),e.jsxs(v,{onClick:d,children:[e.jsx(Rs,{className:"mr-2 h-4 w-4"}),"New Campaign"]})]}),e.jsx(y,{children:e.jsx(w,{className:"pt-6",children:e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs("div",{className:"relative flex-1",children:[e.jsx(Fe,{className:"absolute left-3 top-3 h-4 w-4 text-muted-foreground"}),e.jsx(P,{placeholder:"Search campaigns...",value:t,onChange:j=>s(j.target.value),className:"pl-10"})]}),e.jsx(v,{variant:"outline",children:"Filter"}),e.jsx(v,{variant:"outline",children:"Sort"})]})})}),e.jsx("div",{className:"grid gap-6 md:grid-cols-2 lg:grid-cols-3",children:p.map(j=>e.jsxs(y,{className:"hover:shadow-lg transition-shadow",children:[e.jsx(T,{children:e.jsxs("div",{className:"flex items-start justify-between",children:[e.jsxs("div",{className:"space-y-1",children:[e.jsx(A,{className:"text-lg",children:j.name}),e.jsx(F,{children:j.description})]}),e.jsxs($,{variant:"outline",className:`${r(j.status)} text-white`,children:[c(j.status),j.status]})]})}),e.jsxs(w,{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(ue,{className:"h-4 w-4 text-muted-foreground"}),e.jsx("span",{className:"text-sm font-medium",children:"Target:"}),e.jsx("span",{className:"text-sm text-muted-foreground",children:j.target})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm font-medium",children:"Progress"}),e.jsxs("span",{className:"text-sm text-muted-foreground",children:[j.progress,"%"]})]}),e.jsx("div",{className:"w-full bg-secondary rounded-full h-2",children:e.jsx("div",{className:"bg-primary h-2 rounded-full transition-all",style:{width:`${j.progress}%`}})})]}),e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(ee,{className:"h-4 w-4 text-yellow-500"}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium",children:j.vulnerabilities}),e.jsx("p",{className:"text-xs text-muted-foreground",children:"Vulnerabilities"})]})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(fe,{className:"h-4 w-4 text-blue-500"}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium",children:j.toolsUsed.length}),e.jsx("p",{className:"text-xs text-muted-foreground",children:"Tools Used"})]})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("span",{className:"text-sm font-medium",children:"Tools:"}),e.jsx("div",{className:"flex flex-wrap gap-1",children:j.toolsUsed.map(R=>e.jsx($,{variant:"secondary",className:"text-xs",children:R},R))})]}),e.jsx("div",{className:"flex items-center gap-4 text-xs text-muted-foreground",children:e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx(oa,{className:"h-3 w-3"}),"Created: ",j.createdAt]})}),e.jsxs("div",{className:"flex gap-2 pt-2",children:[j.status==="active"?e.jsxs(v,{size:"sm",variant:"outline",onClick:()=>b(j.id),children:[e.jsx(et,{className:"mr-1 h-3 w-3"}),"Pause"]}):e.jsxs(v,{size:"sm",onClick:()=>g(j.id),children:[e.jsx(ae,{className:"mr-1 h-3 w-3"}),"Run"]}),e.jsx(v,{size:"sm",variant:"outline",children:"View Details"})]})]})]},j.id))}),p.length===0&&e.jsx(y,{children:e.jsx(w,{className:"pt-6",children:e.jsxs("div",{className:"text-center py-8",children:[e.jsx(fe,{className:"mx-auto h-12 w-12 text-muted-foreground"}),e.jsx("h3",{className:"mt-2 text-sm font-semibold",children:"No campaigns found"}),e.jsx("p",{className:"mt-1 text-sm text-muted-foreground",children:t?"Try adjusting your search terms.":"Get started by creating a new security campaign."}),!t&&e.jsx("div",{className:"mt-6",children:e.jsxs(v,{onClick:d,children:[e.jsx(Rs,{className:"mr-2 h-4 w-4"}),"Create Campaign"]})})]})})})]})}const ft=x.createContext(void 0),Ue=({value:t,onValueChange:s,children:a,className:n})=>e.jsx(ft.Provider,{value:{value:t,onValueChange:s},children:e.jsx("div",{className:B("w-full",n),children:a})}),Be=x.forwardRef(({className:t,...s},a)=>e.jsx("div",{ref:a,className:B("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",t),...s}));Be.displayName="TabsList";const Q=x.forwardRef(({className:t,value:s,...a},n)=>{const r=x.useContext(ft);if(!r)throw new Error("TabsTrigger must be used within a Tabs component");const c=r.value===s;return e.jsx("button",{ref:n,className:B("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",c?"bg-background text-foreground shadow-sm":"hover:bg-background/50",t),onClick:()=>r.onValueChange(s),...a})});Q.displayName="TabsTrigger";const J=x.forwardRef(({className:t,value:s,...a},n)=>{const r=x.useContext(ft);if(!r)throw new Error("TabsContent must be used within a Tabs component");return r.value!==s?null:e.jsx("div",{ref:n,className:B("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",t),...a})});J.displayName="TabsContent";function Kr(){const[t,s]=x.useState(""),[a,n]=x.useState("all"),[r,c]=x.useState([{id:"1",title:"Production Environment Security Assessment",type:"penetration",status:"generated",createdAt:"2024-01-20",target:"***********/24",format:"pdf",size:"2.4 MB",vulnerabilities:{critical:2,high:5,medium:12,low:8},campaignId:"camp-1"},{id:"2",title:"Web Application OWASP Compliance Report",type:"compliance",status:"generated",createdAt:"2024-01-19",target:"web-app.example.com",format:"html",size:"1.8 MB",vulnerabilities:{critical:1,high:3,medium:7,low:4},campaignId:"camp-2"},{id:"3",title:"Network Infrastructure Vulnerability Scan",type:"vulnerability",status:"generating",createdAt:"2024-01-20",target:"10.0.0.0/16",format:"pdf",size:"0 MB",vulnerabilities:{critical:0,high:0,medium:0,low:0}},{id:"4",title:"Database Security Assessment",type:"vulnerability",status:"generated",createdAt:"2024-01-18",target:"db.example.com",format:"json",size:"845 KB",vulnerabilities:{critical:0,high:2,medium:6,low:3}}]),p=[{id:"all",name:"All Reports",icon:xe},{id:"vulnerability",name:"Vulnerability",icon:ee},{id:"compliance",name:"Compliance",icon:be},{id:"penetration",name:"Penetration Test",icon:ts},{id:"network",name:"Network",icon:St}],d=u=>{switch(u){case"generated":return"text-green-500";case"generating":return"text-blue-500";case"failed":return"text-red-500";case"draft":return"text-yellow-500";default:return"text-gray-500"}},g=u=>{switch(u){case"generated":return e.jsx(be,{className:"h-3 w-3"});case"generating":return e.jsx(Ve,{className:"h-3 w-3"});case"failed":return e.jsx(ee,{className:"h-3 w-3"});case"draft":return e.jsx(xe,{className:"h-3 w-3"});default:return e.jsx(xe,{className:"h-3 w-3"})}},b=u=>{switch(u){case"vulnerability":return e.jsx(ee,{className:"h-4 w-4"});case"compliance":return e.jsx(be,{className:"h-4 w-4"});case"penetration":return e.jsx(ts,{className:"h-4 w-4"});case"network":return e.jsx(St,{className:"h-4 w-4"});default:return e.jsx(xe,{className:"h-4 w-4"})}},j=u=>{switch(u){case"critical":return"text-red-600 bg-red-100 dark:bg-red-900/20";case"high":return"text-orange-600 bg-orange-100 dark:bg-orange-900/20";case"medium":return"text-yellow-600 bg-yellow-100 dark:bg-yellow-900/20";case"low":return"text-green-600 bg-green-100 dark:bg-green-900/20";default:return"text-gray-600 bg-gray-100 dark:bg-gray-900/20"}},R=r.filter(u=>{const k=u.title.toLowerCase().includes(t.toLowerCase())||u.target.toLowerCase().includes(t.toLowerCase()),N=a==="all"||u.type===a;return k&&N}),E=r.reduce((u,k)=>({critical:u.critical+k.vulnerabilities.critical,high:u.high+k.vulnerabilities.high,medium:u.medium+k.vulnerabilities.medium,low:u.low+k.vulnerabilities.low}),{critical:0,high:0,medium:0,low:0}),f=u=>{console.log(`Downloading report: ${u}`)},L=u=>{console.log(`Sharing report: ${u}`)},i=()=>{console.log("Generating new report...")};return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-3xl font-bold tracking-tight",children:"Security Reports"}),e.jsx("p",{className:"text-muted-foreground",children:"Generate and manage security assessment reports"})]}),e.jsxs(v,{onClick:i,children:[e.jsx(Rs,{className:"mr-2 h-4 w-4"}),"Generate Report"]})]}),e.jsxs("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-4",children:[e.jsxs(y,{children:[e.jsxs(T,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[e.jsx(A,{className:"text-sm font-medium",children:"Total Reports"}),e.jsx(xe,{className:"h-4 w-4 text-muted-foreground"})]}),e.jsxs(w,{children:[e.jsx("div",{className:"text-2xl font-bold",children:r.length}),e.jsxs("p",{className:"text-xs text-muted-foreground",children:[r.filter(u=>u.status==="generated").length," completed"]})]})]}),e.jsxs(y,{children:[e.jsxs(T,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[e.jsx(A,{className:"text-sm font-medium",children:"Critical Issues"}),e.jsx(ee,{className:"h-4 w-4 text-red-500"})]}),e.jsxs(w,{children:[e.jsx("div",{className:"text-2xl font-bold text-red-600",children:E.critical}),e.jsx("p",{className:"text-xs text-muted-foreground",children:"Across all reports"})]})]}),e.jsxs(y,{children:[e.jsxs(T,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[e.jsx(A,{className:"text-sm font-medium",children:"High Severity"}),e.jsx(Ns,{className:"h-4 w-4 text-orange-500"})]}),e.jsxs(w,{children:[e.jsx("div",{className:"text-2xl font-bold text-orange-600",children:E.high}),e.jsx("p",{className:"text-xs text-muted-foreground",children:"Require attention"})]})]}),e.jsxs(y,{children:[e.jsxs(T,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[e.jsx(A,{className:"text-sm font-medium",children:"Recent Reports"}),e.jsx(oa,{className:"h-4 w-4 text-muted-foreground"})]}),e.jsxs(w,{children:[e.jsx("div",{className:"text-2xl font-bold",children:r.filter(u=>new Date(u.createdAt)>=new Date(Date.now()-7*24*60*60*1e3)).length}),e.jsx("p",{className:"text-xs text-muted-foreground",children:"This week"})]})]})]}),e.jsx(y,{children:e.jsx(w,{className:"pt-6",children:e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs("div",{className:"relative flex-1",children:[e.jsx(Fe,{className:"absolute left-3 top-3 h-4 w-4 text-muted-foreground"}),e.jsx(P,{placeholder:"Search reports...",value:t,onChange:u=>s(u.target.value),className:"pl-10"})]}),e.jsxs(v,{variant:"outline",children:[e.jsx(Fs,{className:"mr-2 h-4 w-4"}),"Filter"]})]})})}),e.jsxs(Ue,{value:a,onValueChange:n,children:[e.jsx(Be,{className:"grid w-full grid-cols-5",children:p.map(u=>{const k=u.icon;return e.jsxs(Q,{value:u.id,children:[e.jsx(k,{className:"mr-2 h-4 w-4"}),u.name]},u.id)})}),e.jsxs(J,{value:a,className:"mt-6",children:[e.jsx("div",{className:"space-y-4",children:R.map(u=>e.jsx(y,{className:"hover:shadow-lg transition-shadow",children:e.jsx(w,{className:"pt-6",children:e.jsxs("div",{className:"flex items-start justify-between",children:[e.jsxs("div",{className:"flex items-start gap-4 flex-1",children:[e.jsx("div",{className:"h-10 w-10 rounded-lg bg-primary/10 flex items-center justify-center",children:b(u.type)}),e.jsxs("div",{className:"flex-1 space-y-3",children:[e.jsxs("div",{children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("h3",{className:"text-lg font-semibold",children:u.title}),e.jsxs($,{variant:"outline",className:d(u.status),children:[g(u.status),u.status]}),e.jsx($,{variant:"secondary",className:"capitalize",children:u.type})]}),e.jsxs("p",{className:"text-sm text-muted-foreground mt-1",children:["Target: ",u.target," • Created: ",u.createdAt," • Format: ",u.format.toUpperCase()," • Size: ",u.size]})]}),u.status==="generated"&&e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("span",{className:"text-sm font-medium",children:"Vulnerabilities:"}),e.jsx("div",{className:"flex items-center gap-2",children:Object.entries(u.vulnerabilities).map(([k,N])=>N>0&&e.jsxs($,{variant:"secondary",className:`text-xs ${j(k)}`,children:[k,": ",N]},k))})]})]})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[u.status==="generated"&&e.jsxs(e.Fragment,{children:[e.jsxs(v,{size:"sm",variant:"outline",onClick:()=>f(u.id),children:[e.jsx(ke,{className:"mr-1 h-3 w-3"}),"Download"]}),e.jsxs(v,{size:"sm",variant:"outline",onClick:()=>L(u.id),children:[e.jsx(da,{className:"mr-1 h-3 w-3"}),"Share"]})]}),e.jsx(v,{size:"sm",variant:"outline",children:"View Details"})]})]})})},u.id))}),R.length===0&&e.jsx(y,{children:e.jsx(w,{className:"pt-6",children:e.jsxs("div",{className:"text-center py-8",children:[e.jsx(xe,{className:"mx-auto h-12 w-12 text-muted-foreground"}),e.jsx("h3",{className:"mt-2 text-sm font-semibold",children:"No reports found"}),e.jsx("p",{className:"mt-1 text-sm text-muted-foreground",children:t?"Try adjusting your search terms.":"Generate your first security report to get started."}),!t&&e.jsx("div",{className:"mt-6",children:e.jsxs(v,{onClick:i,children:[e.jsx(Rs,{className:"mr-2 h-4 w-4"}),"Generate Report"]})})]})})})]})]})]})}const Yr=Ds("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),Ne=x.forwardRef(({className:t,...s},a)=>e.jsx("label",{ref:a,className:B(Yr(),t),...s}));Ne.displayName="Label";function Xr(){const[t,s]=x.useState("general"),[a,n]=x.useState(!1);x.useEffect(()=>{try{const S=localStorage.getItem("nexusscan-settings");if(S){const I=JSON.parse(S);if(I.general&&c(I.general),I.backend,I.security&&E(I.security),I.apiKeys&&j(I.apiKeys),console.log("📥 Settings loaded from storage"),I.security?.executionMode)try{fetch("http://ec2-3-89-91-209.compute-1.amazonaws.com:8000/api/settings/execution-mode",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({execution_mode:I.security.executionMode})}).then(q=>{q.ok&&console.log("✅ Execution mode synced with backend:",I.security.executionMode)}).catch(q=>{console.warn("⚠️ Could not sync execution mode with backend:",q)})}catch(q){console.warn("⚠️ Failed to sync execution mode:",q)}}}catch(S){console.error("❌ Failed to load settings:",S)}},[]);const[r,c]=x.useState({theme:"dark",autoSave:!0,notifications:!0,autoUpdates:!0,language:"en"}),[p,d]=x.useState({url:"http://************:8090",timeout:3e4,retries:3,status:"connected"}),g=!1,[b,j]=x.useState([{id:"1",name:"OpenAI GPT-4",service:"openai",status:"active",lastUsed:"2024-01-20",masked:"sk-...h4k2"},{id:"2",name:"DeepSeek API",service:"deepseek",status:"active",lastUsed:"2024-01-19",masked:"ds-...x7m9"},{id:"3",name:"Anthropic Claude",service:"anthropic",status:"inactive",lastUsed:"2024-01-15",masked:"sk-...p3n8"}]),[R,E]=x.useState({executionMode:"real",requireConfirmation:!0,logLevel:"info",encryptData:!0,sessionTimeout:60}),[f,L]=x.useState({maxConcurrent:5,defaultTimeout:300,autoInstall:!0,wslIntegration:!0,toolPath:"/usr/bin"}),i=S=>{switch(S){case"active":case"connected":return"text-green-500";case"inactive":case"disconnected":return"text-yellow-500";case"expired":case"error":return"text-red-500";default:return"text-gray-500"}},u=S=>{switch(S){case"active":case"connected":return e.jsx(be,{className:"h-3 w-3"});case"inactive":case"disconnected":return e.jsx(ee,{className:"h-3 w-3"});case"expired":case"error":return e.jsx(ee,{className:"h-3 w-3"});default:return e.jsx(ee,{className:"h-3 w-3"})}},k=async()=>{console.log("💾 Saving settings...");const S={general:r,backend:void 0,security:R,apiKeys:b.map(I=>({...I,masked:I.masked}))};try{localStorage.setItem("nexusscan-settings",JSON.stringify(S)),console.log("✅ Settings saved to localStorage");try{(await fetch("http://ec2-3-89-91-209.compute-1.amazonaws.com:8000/api/settings/execution-mode",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({execution_mode:R.executionMode})})).ok?console.log("✅ Execution mode updated on backend:",R.executionMode):console.warn("⚠️ Failed to update backend execution mode")}catch(I){console.warn("⚠️ Backend not available for execution mode update:",I)}alert("Settings saved successfully!")}catch(I){console.error("❌ Failed to save settings:",I),alert("Failed to save settings. Please try again.")}},N=()=>{console.log("🔄 Resetting settings..."),confirm("Are you sure you want to reset all settings to defaults? This cannot be undone.")&&(c({theme:"dark",autoSave:!0,notifications:!0,autoUpdates:!0,language:"en"}),E({executionMode:"real",requireConfirmation:!0,logLevel:"info",encryptData:!0,auditLog:!0}),localStorage.removeItem("nexusscan-settings"),console.log("✅ Settings reset to defaults"),alert("Settings reset to defaults!"))},D=()=>{console.log("Adding new API key...")},W=()=>{console.log("Exporting settings...")},U=()=>{console.log("Importing settings...")};return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-3xl font-bold tracking-tight",children:"Settings"}),e.jsx("p",{className:"text-muted-foreground",children:"Configure application preferences and integrations"})]}),e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsxs(v,{variant:"outline",onClick:W,children:[e.jsx(ke,{className:"mr-2 h-4 w-4"}),"Export"]}),e.jsxs(v,{variant:"outline",onClick:U,children:[e.jsx(on,{className:"mr-2 h-4 w-4"}),"Import"]}),e.jsxs(v,{onClick:k,children:[e.jsx(Ct,{className:"mr-2 h-4 w-4"}),"Save Changes"]})]})]}),e.jsxs(Ue,{value:t,onValueChange:s,children:[e.jsxs(Be,{className:"grid w-full grid-cols-4",children:[e.jsxs(Q,{value:"general",children:[e.jsx(Y,{className:"mr-2 h-4 w-4"}),"General"]}),g,e.jsxs(Q,{value:"security",children:[e.jsx(fe,{className:"mr-2 h-4 w-4"}),"Security"]}),e.jsxs(Q,{value:"tools",children:[e.jsx(dn,{className:"mr-2 h-4 w-4"}),"Tools"]}),e.jsxs(Q,{value:"api-keys",children:[e.jsx(fs,{className:"mr-2 h-4 w-4"}),"API Keys"]})]}),e.jsxs(J,{value:"general",className:"space-y-6",children:[e.jsxs(y,{children:[e.jsxs(T,{children:[e.jsx(A,{children:"Appearance"}),e.jsx(F,{children:"Customize the application appearance"})]}),e.jsx(w,{className:"space-y-4",children:e.jsxs("div",{className:"grid gap-4 md:grid-cols-2",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx(Ne,{children:"Theme"}),e.jsxs("select",{className:"w-full px-3 py-2 text-sm border rounded-md",children:[e.jsx("option",{value:"dark",children:"Dark"}),e.jsx("option",{value:"light",children:"Light"}),e.jsx("option",{value:"system",children:"System"})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(Ne,{children:"Language"}),e.jsxs("select",{className:"w-full px-3 py-2 text-sm border rounded-md",children:[e.jsx("option",{value:"en",children:"English"}),e.jsx("option",{value:"es",children:"Español"}),e.jsx("option",{value:"fr",children:"Français"})]})]})]})})]}),e.jsxs(y,{children:[e.jsxs(T,{children:[e.jsx(A,{children:"Preferences"}),e.jsx(F,{children:"Application behavior settings"})]}),e.jsxs(w,{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx(Ne,{children:"Auto-save"}),e.jsx("p",{className:"text-sm text-muted-foreground",children:"Automatically save changes"})]}),e.jsx("input",{type:"checkbox",checked:r.autoSave,readOnly:!0})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx(Ne,{children:"Notifications"}),e.jsx("p",{className:"text-sm text-muted-foreground",children:"Show desktop notifications"})]}),e.jsx("input",{type:"checkbox",checked:r.notifications,readOnly:!0})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx(Ne,{children:"Auto-updates"}),e.jsx("p",{className:"text-sm text-muted-foreground",children:"Automatically check for updates"})]}),e.jsx("input",{type:"checkbox",checked:r.autoUpdates,readOnly:!0})]})]})]})]}),g,e.jsx(J,{value:"security",className:"space-y-6",children:e.jsxs(y,{children:[e.jsxs(T,{children:[e.jsx(A,{children:"Execution Security"}),e.jsx(F,{children:"Configure tool execution and safety settings"})]}),e.jsxs(w,{className:"space-y-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx(Ne,{children:"Execution Mode"}),e.jsxs("select",{className:"w-full px-3 py-2 text-sm border rounded-md",value:R.executionMode,onChange:S=>E(I=>({...I,executionMode:S.target.value})),children:[e.jsx("option",{value:"simulation",children:"Simulation (Safe)"}),e.jsx("option",{value:"real",children:"Real Execution"})]}),e.jsxs("p",{className:"text-xs text-muted-foreground",children:[e.jsx("strong",{children:"Real mode"}),": Executes actual security tools with real results.",e.jsx("strong",{children:"Simulation mode"}),": Provides safe, educational outputs for learning."]})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx(Ne,{children:"Require Confirmation"}),e.jsx("p",{className:"text-sm text-muted-foreground",children:"Confirm before executing potentially dangerous operations"})]}),e.jsx("input",{type:"checkbox",checked:R.requireConfirmation,onChange:S=>E(I=>({...I,requireConfirmation:S.target.checked}))})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx(Ne,{children:"Encrypt Data"}),e.jsx("p",{className:"text-sm text-muted-foreground",children:"Encrypt sensitive data at rest"})]}),e.jsx("input",{type:"checkbox",checked:R.encryptData,onChange:S=>E(I=>({...I,encryptData:S.target.checked}))})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(Ne,{children:"Session Timeout (minutes)"}),e.jsx(P,{type:"number",value:R.sessionTimeout,onChange:S=>E(I=>({...I,sessionTimeout:parseInt(S.target.value)}))})]})]})]})}),e.jsx(J,{value:"tools",className:"space-y-6",children:e.jsxs(y,{children:[e.jsxs(T,{children:[e.jsx(A,{children:"Tool Configuration"}),e.jsx(F,{children:"Configure security tool execution settings"})]}),e.jsxs(w,{className:"space-y-4",children:[e.jsxs("div",{className:"grid gap-4 md:grid-cols-2",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx(Ne,{children:"Max Concurrent Tools"}),e.jsx(P,{type:"number",value:f.maxConcurrent,onChange:S=>L(I=>({...I,maxConcurrent:parseInt(S.target.value)}))})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(Ne,{children:"Default Timeout (seconds)"}),e.jsx(P,{type:"number",value:f.defaultTimeout,onChange:S=>L(I=>({...I,defaultTimeout:parseInt(S.target.value)}))})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(Ne,{children:"Tool Installation Path"}),e.jsx(P,{value:f.toolPath,onChange:S=>L(I=>({...I,toolPath:S.target.value}))})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx(Ne,{children:"Auto-install Tools"}),e.jsx("p",{className:"text-sm text-muted-foreground",children:"Automatically install missing security tools"})]}),e.jsx("input",{type:"checkbox",checked:f.autoInstall,onChange:S=>L(I=>({...I,autoInstall:S.target.checked}))})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx(Ne,{children:"WSL Integration"}),e.jsx("p",{className:"text-sm text-muted-foreground",children:"Enable Windows Subsystem for Linux integration"})]}),e.jsx("input",{type:"checkbox",checked:f.wslIntegration,onChange:S=>L(I=>({...I,wslIntegration:S.target.checked}))})]})]})]})}),e.jsx(J,{value:"api-keys",className:"space-y-6",children:e.jsxs(y,{children:[e.jsx(T,{children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx(A,{children:"AI Service API Keys"}),e.jsx(F,{children:"Manage API keys for AI services"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsxs(v,{variant:"outline",size:"sm",onClick:()=>n(!a),children:[a?e.jsx(mn,{className:"h-4 w-4"}):e.jsx(ys,{className:"h-4 w-4"}),a?"Hide":"Show"," Keys"]}),e.jsxs(v,{size:"sm",onClick:D,children:[e.jsx(fs,{className:"mr-2 h-4 w-4"}),"Add Key"]})]})]})}),e.jsx(w,{children:e.jsx("div",{className:"space-y-4",children:b.map(S=>e.jsxs("div",{className:"flex items-center gap-4 p-4 rounded-lg border",children:[e.jsx("div",{className:"h-10 w-10 rounded-lg bg-primary/10 flex items-center justify-center",children:e.jsx(fs,{className:"h-4 w-4"})}),e.jsxs("div",{className:"flex-1 space-y-1",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("p",{className:"text-sm font-medium",children:S.name}),e.jsxs($,{variant:"outline",className:i(S.status),children:[u(S.status),S.status]})]}),e.jsxs("p",{className:"text-xs text-muted-foreground",children:["Key: ",a?"sk-1234567890abcdef...":S.masked,S.lastUsed&&` • Last used: ${S.lastUsed}`]})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(v,{size:"sm",variant:"outline",children:"Edit"}),e.jsx(v,{size:"sm",variant:"outline",children:"Test"})]})]},S.id))})})]})})]}),e.jsxs("div",{className:"flex items-center justify-end gap-3 pt-6 border-t",children:[e.jsxs(v,{variant:"outline",onClick:N,children:[e.jsx(qe,{className:"mr-2 h-4 w-4"}),"Reset to Defaults"]}),e.jsxs(v,{onClick:k,children:[e.jsx(Ct,{className:"mr-2 h-4 w-4"}),"Save All Changes"]})]})]})}const ws=x.createContext(void 0),pe=({value:t,defaultValue:s,onValueChange:a,children:n})=>{const[r,c]=x.useState(s||""),[p,d]=x.useState(!1),g=t!==void 0?t:r,b=j=>{t===void 0&&c(j),a?.(j),d(!1)};return e.jsx(ws.Provider,{value:{value:g,onValueChange:b,open:p,onOpenChange:d},children:e.jsx("div",{className:"relative",children:n})})},oe=x.forwardRef(({className:t,children:s,...a},n)=>{const r=x.useContext(ws);if(!r)throw new Error("SelectTrigger must be used within a Select");return e.jsxs("button",{ref:n,className:B("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",t),onClick:()=>r.onOpenChange(!r.open),...a,children:[s,e.jsx(ut,{className:"h-4 w-4 opacity-50"})]})});oe.displayName="SelectTrigger";const de=x.forwardRef(({placeholder:t,...s},a)=>{const n=x.useContext(ws);if(!n)throw new Error("SelectValue must be used within a Select");return e.jsx("span",{ref:a,...s,children:n.value||t})});de.displayName="SelectValue";const me=x.forwardRef(({className:t,children:s,...a},n)=>{const r=x.useContext(ws);if(!r)throw new Error("SelectContent must be used within a Select");return r.open?e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"fixed inset-0 z-50",onClick:()=>r.onOpenChange(!1)}),e.jsx("div",{ref:n,className:B("absolute z-50 mt-1 w-full rounded-md border bg-popover text-popover-foreground shadow-md outline-none animate-in fade-in-0 zoom-in-95",t),...a,children:e.jsx("div",{className:"p-1",children:s})})]}):null});me.displayName="SelectContent";const _=x.forwardRef(({className:t,children:s,value:a,...n},r)=>{const c=x.useContext(ws);if(!c)throw new Error("SelectItem must be used within a Select");const p=c.value===a;return e.jsxs("div",{ref:r,className:B("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50 hover:bg-accent hover:text-accent-foreground",t),onClick:()=>c.onValueChange(a),...n,children:[p&&e.jsx("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:e.jsx(un,{className:"h-4 w-4"})}),s]})});_.displayName="SelectItem";const qt={quick:{scanType:"syn",portRange:"1-1000",timing:4,detectOS:!1,detectVersion:!1,enableScripts:!1},comprehensive:{scanType:"comprehensive",portRange:"1-65535",timing:3,detectOS:!0,detectVersion:!0,enableScripts:!0,scriptCategories:["default","safe"]},stealth:{scanType:"stealth",portRange:"1-1000",timing:1,detectOS:!1,detectVersion:!1,enableScripts:!1},vulnerability:{scanType:"comprehensive",portRange:"1-1000",timing:3,detectOS:!0,detectVersion:!0,enableScripts:!0,scriptCategories:["vuln","safe","default"]}},Zr=[{value:"auth",label:"Authentication"},{value:"broadcast",label:"Broadcast"},{value:"brute",label:"Brute Force"},{value:"default",label:"Default"},{value:"discovery",label:"Discovery"},{value:"dos",label:"Denial of Service"},{value:"exploit",label:"Exploit"},{value:"external",label:"External"},{value:"fuzzer",label:"Fuzzer"},{value:"intrusive",label:"Intrusive"},{value:"malware",label:"Malware"},{value:"safe",label:"Safe"},{value:"version",label:"Version"},{value:"vuln",label:"Vulnerability"}];function ya(){const[t,s]=x.useState({target:"",scanType:"syn",portRange:"1-1000",timing:3,detectOS:!1,detectVersion:!1,enableScripts:!1,scriptCategories:["safe"],outputFormat:"normal",customArgs:""}),[a,n]=x.useState(!1),[r,c]=x.useState(null),[p,d]=x.useState(0),[g,b]=x.useState([]),[j,R]=x.useState(null),[E,f]=x.useState("configure"),{status:L}=Pe(),i=S=>{s(I=>({...I,...S}))},u=S=>{const I=qt[S];I&&(i(I),C.success(`Applied ${S} scan preset`))},k=()=>{let S="nmap";switch(t.scanType){case"syn":S+=" -sS";break;case"tcp":S+=" -sT";break;case"udp":S+=" -sU";break;case"comprehensive":S+=" -sS -sU";break;case"stealth":S+=" -sS -f";break;case"aggressive":S+=" -A";break}switch(t.portRange&&t.portRange!=="1-65535"&&(S+=` -p ${t.portRange}`),S+=` -T${t.timing}`,t.detectOS&&(S+=" -O"),t.detectVersion&&(S+=" -sV"),t.enableScripts&&t.scriptCategories.length>0&&(S+=` --script=${t.scriptCategories.join(",")}`),t.outputFormat){case"xml":S+=" -oX -";break;case"grepable":S+=" -oG -";break;case"json":S+=" -oJ -";break}return t.customArgs.trim()&&(S+=` ${t.customArgs.trim()}`),S+=` ${t.target}`,S},N=async()=>{if(!t.target.trim()){C.error("Please specify a target");return}if(!L.connected){C.error("Backend not connected");return}try{n(!0),d(0),b([]),R(null),f("output"),console.log("🚀 Starting Nmap scan with config:",t);const S=await ne.executeTool("nmap",t,I=>{console.log("📊 Progress update:",I),c(I),d(I.progress),I.output&&b(q=>[...q,...I.output]),I.status==="completed"?(n(!1),R(I.results),console.log("✅ Scan completed with results:",I.results),C.success("Nmap scan completed")):I.status==="failed"&&(n(!1),console.error("❌ Scan failed:",I.error),C.error(`Scan failed: ${I.error}`))});c(S),C.info("Nmap scan started")}catch(S){n(!1),console.error("Failed to start Nmap scan:",S),C.error("Failed to start scan")}},D=async()=>{if(r)try{await ne.cancelExecution(r.id),n(!1),c(null),C.info("Scan cancelled")}catch(S){console.error("Failed to stop scan:",S),C.error("Failed to stop scan")}},W=async()=>{try{await navigator.clipboard.writeText(k()),C.success("Command copied to clipboard")}catch{C.error("Failed to copy command")}},U=()=>{if(!j&&g.length===0){C.error("No results to export");return}const S=j||g.join(`

`),I=new Blob([JSON.stringify(S,null,2)],{type:"application/json"}),q=URL.createObjectURL(I),z=document.createElement("a");z.href=q,z.download=`nmap-scan-${new Date().toISOString().slice(0,19)}.json`,document.body.appendChild(z),z.click(),document.body.removeChild(z),URL.revokeObjectURL(q),C.success("Results exported")};return e.jsxs("div",{className:"container-desktop py-6 space-y-6",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"p-2 bg-blue-100 dark:bg-blue-900/20 rounded-lg",children:e.jsx(Oe,{className:"h-6 w-6 text-blue-600 dark:text-blue-400"})}),e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold",children:"Nmap Scanner"}),e.jsx("p",{className:"text-muted-foreground",children:"Network discovery and port scanning with OS and service detection"})]})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx($,{variant:L.connected?"default":"destructive",children:L.connected?"Connected":"Offline"}),a&&e.jsx($,{variant:"secondary",className:"animate-pulse",children:"Scanning..."})]})]}),e.jsxs(Ue,{value:E,onValueChange:f,className:"space-y-6",children:[e.jsxs(Be,{className:"grid w-full grid-cols-4",children:[e.jsxs(Q,{value:"configure",className:"flex items-center gap-2",children:[e.jsx(Y,{className:"h-4 w-4"}),"Configure"]}),e.jsxs(Q,{value:"output",className:"flex items-center gap-2",children:[e.jsx(Se,{className:"h-4 w-4"}),"Output"]}),e.jsxs(Q,{value:"results",className:"flex items-center gap-2",children:[e.jsx(xe,{className:"h-4 w-4"}),"Results"]}),e.jsxs(Q,{value:"command",className:"flex items-center gap-2",children:[e.jsx(ue,{className:"h-4 w-4"}),"Command"]})]}),e.jsxs(J,{value:"configure",className:"space-y-6",children:[e.jsxs("div",{className:"grid gap-6 lg:grid-cols-2",children:[e.jsxs(y,{children:[e.jsxs(T,{children:[e.jsxs(A,{className:"flex items-center gap-2",children:[e.jsx(ue,{className:"h-5 w-5"}),"Target Configuration"]}),e.jsx(F,{children:"Specify the target hosts and basic scan parameters"})]}),e.jsxs(w,{className:"space-y-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium",children:"Target(s)"}),e.jsx(P,{placeholder:"e.g., ***********, 10.0.0.0/24, example.com",value:t.target,onChange:S=>i({target:S.target.value})}),e.jsx("p",{className:"text-xs text-muted-foreground",children:"IP addresses, CIDR ranges, or hostnames"})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium",children:"Scan Type"}),e.jsxs(pe,{value:t.scanType,onValueChange:S=>i({scanType:S}),children:[e.jsx(oe,{children:e.jsx(de,{})}),e.jsxs(me,{children:[e.jsx(_,{value:"syn",children:"SYN Scan (-sS)"}),e.jsx(_,{value:"tcp",children:"TCP Connect (-sT)"}),e.jsx(_,{value:"udp",children:"UDP Scan (-sU)"}),e.jsx(_,{value:"comprehensive",children:"Comprehensive (TCP+UDP)"}),e.jsx(_,{value:"stealth",children:"Stealth (Fragmented)"}),e.jsx(_,{value:"aggressive",children:"Aggressive (-A)"})]})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium",children:"Port Range"}),e.jsx(P,{placeholder:"e.g., 1-1000, 22,80,443, -",value:t.portRange,onChange:S=>i({portRange:S.target.value})}),e.jsx("p",{className:"text-xs text-muted-foreground",children:"Specific ports, ranges, or '-' for all ports"})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium",children:"Timing Template"}),e.jsxs(pe,{value:t.timing.toString(),onValueChange:S=>i({timing:parseInt(S)}),children:[e.jsx(oe,{children:e.jsx(de,{})}),e.jsxs(me,{children:[e.jsx(_,{value:"0",children:"Paranoid (T0) - Very Slow"}),e.jsx(_,{value:"1",children:"Sneaky (T1) - Slow"}),e.jsx(_,{value:"2",children:"Polite (T2) - Normal"}),e.jsx(_,{value:"3",children:"Normal (T3) - Default"}),e.jsx(_,{value:"4",children:"Aggressive (T4) - Fast"}),e.jsx(_,{value:"5",children:"Insane (T5) - Very Fast"})]})]})]})]})]}),e.jsxs(y,{children:[e.jsxs(T,{children:[e.jsxs(A,{className:"flex items-center gap-2",children:[e.jsx(fe,{className:"h-5 w-5"}),"Detection Options"]}),e.jsx(F,{children:"Enable additional detection and enumeration features"})]}),e.jsxs(w,{className:"space-y-4",children:[e.jsxs("div",{className:"space-y-3",children:[e.jsxs("label",{className:"flex items-center space-x-2",children:[e.jsx("input",{type:"checkbox",checked:t.detectOS,onChange:S=>i({detectOS:S.target.checked}),className:"rounded border-gray-300"}),e.jsx("span",{className:"text-sm",children:"OS Detection (-O)"})]}),e.jsxs("label",{className:"flex items-center space-x-2",children:[e.jsx("input",{type:"checkbox",checked:t.detectVersion,onChange:S=>i({detectVersion:S.target.checked}),className:"rounded border-gray-300"}),e.jsx("span",{className:"text-sm",children:"Version Detection (-sV)"})]}),e.jsxs("label",{className:"flex items-center space-x-2",children:[e.jsx("input",{type:"checkbox",checked:t.enableScripts,onChange:S=>i({enableScripts:S.target.checked}),className:"rounded border-gray-300"}),e.jsx("span",{className:"text-sm",children:"Enable NSE Scripts"})]})]}),t.enableScripts&&e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium",children:"Script Categories"}),e.jsx("div",{className:"grid grid-cols-2 gap-2",children:Zr.map(S=>e.jsxs("label",{className:"flex items-center space-x-2",children:[e.jsx("input",{type:"checkbox",checked:t.scriptCategories.includes(S.value),onChange:I=>{I.target.checked?i({scriptCategories:[...t.scriptCategories,S.value]}):i({scriptCategories:t.scriptCategories.filter(q=>q!==S.value)})},className:"rounded border-gray-300"}),e.jsx("span",{className:"text-xs",children:S.label})]},S.value))})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium",children:"Output Format"}),e.jsxs(pe,{value:t.outputFormat,onValueChange:S=>i({outputFormat:S}),children:[e.jsx(oe,{children:e.jsx(de,{})}),e.jsxs(me,{children:[e.jsx(_,{value:"normal",children:"Normal Text"}),e.jsx(_,{value:"xml",children:"XML Format"}),e.jsx(_,{value:"grepable",children:"Grepable Format"}),e.jsx(_,{value:"json",children:"JSON Format"})]})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium",children:"Custom Arguments"}),e.jsx(P,{placeholder:"Additional nmap arguments",value:t.customArgs,onChange:S=>i({customArgs:S.target.value})})]})]})]})]}),e.jsxs(y,{children:[e.jsxs(T,{children:[e.jsxs(A,{className:"flex items-center gap-2",children:[e.jsx(Ge,{className:"h-5 w-5"}),"Quick Presets & Actions"]}),e.jsx(F,{children:"Use predefined configurations or start your scan"})]}),e.jsx(w,{children:e.jsxs("div",{className:"flex flex-wrap gap-3",children:[Object.keys(qt).map(S=>e.jsxs(v,{variant:"outline",size:"sm",onClick:()=>u(S),className:"capitalize",children:[S," Scan"]},S)),e.jsxs("div",{className:"flex gap-2 ml-auto",children:[e.jsxs(v,{variant:"outline",size:"sm",onClick:W,children:[e.jsx(je,{className:"h-4 w-4 mr-1"}),"Copy Command"]}),a?e.jsxs(v,{variant:"destructive",onClick:D,children:[e.jsx(We,{className:"h-4 w-4 mr-2"}),"Stop Scan"]}):e.jsxs(v,{onClick:N,disabled:!t.target.trim()||!L.connected,children:[e.jsx(ae,{className:"h-4 w-4 mr-2"}),"Start Scan"]})]})]})})]})]}),e.jsx(J,{value:"output",className:"space-y-6",children:e.jsxs(y,{children:[e.jsx(T,{children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsxs(A,{className:"flex items-center gap-2",children:[e.jsx(Se,{className:"h-5 w-5"}),"Real-time Output"]}),e.jsx(F,{children:"Live output from the Nmap scan process"})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[a&&e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(Ae,{value:p,className:"w-32"}),e.jsxs("span",{className:"text-sm text-muted-foreground",children:[p,"%"]})]}),e.jsx(v,{variant:"outline",size:"sm",onClick:()=>b([]),disabled:a,children:e.jsx(qe,{className:"h-4 w-4"})})]})]})}),e.jsx(w,{children:e.jsx("div",{className:"bg-gray-900 text-gray-100 p-4 rounded-lg h-96 overflow-y-auto font-mono text-sm",children:g.length===0?e.jsx("div",{className:"text-gray-500 italic",children:a?"Waiting for output...":"No output yet. Start a scan to see results here."}):g.map((S,I)=>e.jsx("div",{className:"mb-1",children:S},I))})})]})}),e.jsx(J,{value:"results",className:"space-y-6",children:e.jsxs(y,{children:[e.jsx(T,{children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsxs(A,{className:"flex items-center gap-2",children:[e.jsx(xe,{className:"h-5 w-5"}),"Scan Results"]}),e.jsx(F,{children:"Structured results and analysis from the completed scan"})]}),e.jsxs(v,{variant:"outline",onClick:U,disabled:!j&&g.length===0,children:[e.jsx(ke,{className:"h-4 w-4 mr-2"}),"Export Results"]})]})}),e.jsx(w,{children:j?e.jsx("div",{className:"space-y-4",children:e.jsx("pre",{className:"bg-muted p-4 rounded-lg overflow-auto text-sm",children:JSON.stringify(j,null,2)})}):e.jsxs("div",{className:"text-center py-8 text-muted-foreground",children:[e.jsx(xe,{className:"h-12 w-12 mx-auto mb-4 opacity-50"}),e.jsx("p",{children:"No structured results available yet."}),e.jsx("p",{className:"text-sm",children:"Complete a scan to see parsed results here."})]})})]})}),e.jsx(J,{value:"command",className:"space-y-6",children:e.jsxs(y,{children:[e.jsxs(T,{children:[e.jsxs(A,{className:"flex items-center gap-2",children:[e.jsx(ue,{className:"h-5 w-5"}),"Generated Command"]}),e.jsx(F,{children:"The Nmap command that will be executed based on your configuration"})]}),e.jsx(w,{children:e.jsxs("div",{className:"space-y-4",children:[e.jsx("div",{className:"bg-muted p-4 rounded-lg",children:e.jsx("code",{className:"text-sm font-mono break-all",children:k()})}),e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("p",{className:"text-sm text-muted-foreground",children:"This command will be executed on the backend server"}),e.jsxs(v,{variant:"outline",onClick:W,children:[e.jsx(je,{className:"h-4 w-4 mr-2"}),"Copy Command"]})]})]})})]})})]})]})}const Le=x.forwardRef(({className:t,...s},a)=>e.jsx("textarea",{className:B("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",t),ref:a,...s}));Le.displayName="Textarea";const Gt={conservative:{rate:100,label:"Conservative (100 pps)",description:"Safe for most networks"},moderate:{rate:1e3,label:"Moderate (1K pps)",description:"Good balance of speed and safety"},aggressive:{rate:1e4,label:"Aggressive (10K pps)",description:"Fast scanning, may trigger IDS"},maximum:{rate:1e5,label:"Maximum (100K pps)",description:"Maximum speed, high risk"},internet:{rate:1e6,label:"Internet Scale (1M pps)",description:"For Internet-wide scans"}},Qt={top100:"7,9,13,21-23,25-26,37,53,79-81,88,106,110-111,113,119,135,139,143-144,179,199,389,427,443-445,465,513-515,543-544,548,554,587,631,646,873,990,993,995,1025-1029,1110,1433,1720,1723,1755,1900,2000-2001,2049,2121,2717,3000,3128,3306,3389,3986,4899,5000,5009,5051,5060,5101,5190,5357,5432,5631,5666,5800,5900,6000-6001,6646,7070,8000,8008-8009,8080-8081,8443,8888,9100,9999-10000,32768,49152-49157",top1000:"1,3-4,6-7,9,13,17,19-26,30,32-33,37,42-43,49,53,70,79-85,88-90,99-100,106,109-111,113,119,125,135,139,143-144,146,161,163,179,199,211-212,222,254-256,259,264,280,301,306,311,340,366,389,406-407,416,417,425,427,443-445,458,464-465,481,497,500,512-515,524,541,543-545,548,554-555,563,587,593,616-617,625,631,636,646,648,666-668,683,687,691,700,705,711,714,720,722,726,749,765,777,783,787,800-801,808,843,873,880,888,898,900-903,911-912,981,987,990,992-995,999-1002,1007,1009-1011,1021-1100,1102,1104-1108,1110-1114,1117,1119,1121-1124,1126,1130-1132,1137-1138,1141,1145,1147-1149,1151-1152,1154,1163-1166,1169,1174-1175,1183,1185-1187,1192,1198-1199,1201,1213,1216-1218,1233-1234,1236,1244,1247-1248,1259,1271-1272,1277,1287,1296,1300-1301,1309-1311,1322,1328,1334,1352,1417,1433-1434,1443,1455,1461,1494,1500-1501,1503,1521,1524,1533,1556,1580,1583,1594,1600,1641,1658,1666,1687-1688,1700,1717-1721,1723,1755,1761,1782-1783,1801,1805,1812,1839-1840,1862-1864,1875,1900,1914,1935,1947,1971-1972,1974,1984,1998-2010,2013,2020-2022,2030,2033-2035,2038,2040-2043,2045-2049,2065,2068,2099-2100,2103,2105-2107,2111,2119,2121,2126,2135,2144,2160-2161,2170,2179,2190-2191,2196,2200,2222,2251,2260,2288,2301,2323,2366,2381-2383,2393-2394,2399,2401,2492,2500,2522,2525,2557,2601-2602,2604-2605,2607-2608,2638,2701-2702,2710,2717-2718,2725,2800,2809,2811,2869,2875,2909-2910,2920,2967-2968,2998,3000-3001,3003,3005-3007,3011,3013,3017,3030-3031,3052,3071,3077,3128,3168,3211,3221,3260-3261,3268-3269,3283,3300-3301,3306,3322-3325,3333,3351,3367,3369-3372,3389-3390,3404,3476,3493,3517,3527,3546,3551,3580,3659,3689-3690,3703,3737,3766,3784,3800-3801,3809,3814,3826-3828,3851,3869,3871,3878,3880,3889,3905,3914,3918,3920,3945,3971,3986,3995,3998,4000-4006,4045,4111,4125-4126,4129,4224,4242,4279,4321,4343,4443-4446,4449,4550,4567,4662,4848,4899-4900,4998,5000-5004,5009,5030,5033,5050-5051,5054,5060-5061,5080,5087,5100-5102,5120,5190,5200,5214,5221-5222,5225-5226,5269,5280,5298,5357,5405,5414,5431-5432,5440,5500,5510,5544,5550,5555,5560,5566,5631,5633,5666,5678-5679,5718,5730,5800-5802,5810-5811,5815,5822,5825,5850,5859,5862,5877,5900-5904,5906-5907,5910-5911,5915,5922,5925,5950,5952,5959-5963,5987-5989,5998-6007,6009,6025,6059,6100-6101,6106,6112,6123,6129,6156,6346,6389,6502,6510,6543,6547,6565-6567,6580,6646,6666-6669,6689,6692,6699,6779,6788-6789,6792,6839,6881,6901,6969,7000-7002,7004,7007,7019,7025,7070,7100,7103,7106,7200-7201,7402,7435,7443,7496,7512,7625,7627,7676,7741,7777-7778,7800,7911,7920-7921,7937-7938,7999-8002,8007-8011,8021-8022,8031,8042,8045,8080-8090,8093,8099-8100,8180-8181,8192-8194,8200,8222,8254,8290-8292,8300,8333,8383,8400,8402,8443,8500,8600,8649,8651-8652,8654,8701,8800,8873,8888,8899,8994,9000-9003,9009-9011,9040,9050,9071,9080-9081,9090-9091,9099-9103,9110-9111,9200,9207,9220,9290,9415,9418,9485,9500,9502-9503,9535,9575,9593-9595,9618,9666,9876-9878,9898,9900,9917,9929,9943-9944,9968,9998-10004,10009-10010,10012,10024-10025,10082,10180,10215,10243,10566,10616-10617,10621,10626,10628-10629,10778,11110-11111,11967,12000,12174,12265,12345,13456,13722,13782-13783,14000,14238,14441-14442,15000,15002-15004,15660,15742,16000-16001,16012,16016,16018,16080,16113,16992-16993,17877,17988,18040,18101,18988,19101,19283,19315,19350,19780,19801,19842,20000,20005,20031,20221-20222,20828,21571,22939,23502,24444,24800,25734-25735,26214,27000,27352-27353,27355-27356,27715,28201,30000,30718,30951,31038,31337,32768-32785,33354,33899,34571-34573,35500,38292,40193,40911,41511,42510,44176,44442-44443,44501,45100,48080,49152-49161,49163,49165,49167,49175-49176,49400,49999-50003,50006,50300,50389,50500,50636,50800,51103,51493,52673,52822,52848,52869,54045,54328,55055-55056,55555,55600,56737-56738,57294,57797,58080,60020,60443,61532,61900,62078,63331,64623,64680,65000,65129,65389",web:"80,443,8080,8443,8000,8888,9000,3000,5000",common:"21,22,23,25,53,80,110,111,135,139,143,443,993,995,1723,3389,5900",database:"1433,1521,3306,5432,6379,27017,27018,27019"};function wa(){const[t,s]=x.useState({targets:"",ports:"80,443",rate:1e3,maxRuntime:3600,excludeRanges:"",sourcePort:"",sourceIP:"",interface:"",outputFormat:"list",banners:!1,includeClosed:!1,ping:!0,randomizeTargets:!0,customArgs:""}),[a,n]=x.useState(!1),[r,c]=x.useState(null),[p,d]=x.useState(0),[g,b]=x.useState([]),[j,R]=x.useState(null),[E,f]=x.useState("configure"),[L,i]=x.useState({targetCount:0,portCount:0,estimatedTime:0,totalCombinations:0}),{status:u}=Pe();x.useEffect(()=>{const m=()=>{const o=t.targets.includes("/")?Math.pow(2,32-parseInt(t.targets.split("/")[1]||"32")):t.targets.split(",").length,l=t.ports.includes("-")?t.ports.split(",").reduce((M,H)=>{if(H.includes("-")){const[le,G]=H.split("-").map(Number);return M+(G-le+1)}return M+1},0):t.ports.split(",").length,h=o*l,O=h/t.rate;i({targetCount:o,portCount:l,estimatedTime:O,totalCombinations:h})};t.targets&&t.ports&&m()},[t.targets,t.ports,t.rate]);const k=m=>{s(o=>({...o,...m}))},N=m=>{const o=Gt[m];o&&(k({rate:o.rate}),C.success(`Applied ${o.label}`))},D=m=>{const o=Qt[m];o&&(k({ports:o}),C.success(`Applied ${m} port preset`))},W=()=>{let m="masscan";switch(m+=` ${t.targets}`,m+=` -p${t.ports}`,m+=` --rate=${t.rate}`,t.maxRuntime>0&&(m+=` --max-runtime=${t.maxRuntime}`),t.excludeRanges.trim()&&(m+=` --exclude=${t.excludeRanges}`),t.sourcePort.trim()&&(m+=` --source-port=${t.sourcePort}`),t.sourceIP.trim()&&(m+=` --source-ip=${t.sourceIP}`),t.interface.trim()&&(m+=` --interface=${t.interface}`),t.outputFormat){case"json":m+=" --output-format=json";break;case"xml":m+=" --output-format=xml";break;case"binary":m+=" --output-format=binary";break;default:m+=" --output-format=list"}return t.banners&&(m+=" --banners"),t.includeClosed&&(m+=" --include-closed"),t.ping||(m+=" --ping=false"),t.randomizeTargets||(m+=" --randomize-hosts=false"),t.customArgs.trim()&&(m+=` ${t.customArgs.trim()}`),m},U=m=>m<60?`${Math.round(m)}s`:m<3600?`${Math.round(m/60)}m`:`${Math.round(m/3600)}h ${Math.round(m%3600/60)}m`,S=m=>m.toLocaleString(),I=async()=>{if(!t.targets.trim()){C.error("Please specify targets");return}if(!t.ports.trim()){C.error("Please specify ports");return}if(!u.connected){C.error("Backend not connected");return}try{n(!0),d(0),b([]),R(null),f("output");const m=await ne.executeTool("masscan",t,o=>{c(o),d(o.progress),o.output&&b(l=>[...l,...o.output]),o.status==="completed"?(n(!1),R(o.results),C.success("Masscan scan completed")):o.status==="failed"&&(n(!1),C.error(`Scan failed: ${o.error}`))});c(m),C.info("Masscan scan started")}catch(m){n(!1),console.error("Failed to start Masscan scan:",m),C.error("Failed to start scan")}},q=async()=>{if(r)try{await ne.cancelExecution(r.id),n(!1),c(null),C.info("Scan cancelled")}catch(m){console.error("Failed to stop scan:",m),C.error("Failed to stop scan")}},z=async()=>{try{await navigator.clipboard.writeText(W()),C.success("Command copied to clipboard")}catch{C.error("Failed to copy command")}},X=()=>{if(!j&&g.length===0){C.error("No results to export");return}const m=j||g.join(`
`),o=new Blob([JSON.stringify(m,null,2)],{type:"application/json"}),l=URL.createObjectURL(o),h=document.createElement("a");h.href=l,h.download=`masscan-scan-${new Date().toISOString().slice(0,19)}.json`,document.body.appendChild(h),h.click(),document.body.removeChild(h),URL.revokeObjectURL(l),C.success("Results exported")};return e.jsxs("div",{className:"container-desktop py-6 space-y-6",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"p-2 bg-orange-100 dark:bg-orange-900/20 rounded-lg",children:e.jsx(Ge,{className:"h-6 w-6 text-orange-600 dark:text-orange-400"})}),e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold",children:"Masscan Scanner"}),e.jsx("p",{className:"text-muted-foreground",children:"High-speed asynchronous port scanner for large-scale network scanning"})]})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx($,{variant:u.connected?"default":"destructive",children:u.connected?"Connected":"Offline"}),a&&e.jsxs($,{variant:"secondary",className:"animate-pulse",children:["Scanning at ",S(t.rate)," pps"]})]})]}),t.targets&&t.ports&&e.jsxs(y,{className:"border-orange-200 dark:border-orange-800",children:[e.jsx(T,{className:"pb-3",children:e.jsxs(A,{className:"flex items-center gap-2 text-orange-600 dark:text-orange-400",children:[e.jsx(Ns,{className:"h-5 w-5"}),"Scan Statistics"]})}),e.jsx(w,{children:e.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-orange-600 dark:text-orange-400",children:S(L.targetCount)}),e.jsx("div",{className:"text-xs text-muted-foreground",children:"Targets"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-orange-600 dark:text-orange-400",children:S(L.portCount)}),e.jsx("div",{className:"text-xs text-muted-foreground",children:"Ports"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-orange-600 dark:text-orange-400",children:S(L.totalCombinations)}),e.jsx("div",{className:"text-xs text-muted-foreground",children:"Total Combinations"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-orange-600 dark:text-orange-400",children:U(L.estimatedTime)}),e.jsx("div",{className:"text-xs text-muted-foreground",children:"Estimated Time"})]})]})})]}),e.jsxs(Ue,{value:E,onValueChange:f,className:"space-y-6",children:[e.jsxs(Be,{className:"grid w-full grid-cols-4",children:[e.jsxs(Q,{value:"configure",className:"flex items-center gap-2",children:[e.jsx(Y,{className:"h-4 w-4"}),"Configure"]}),e.jsxs(Q,{value:"output",className:"flex items-center gap-2",children:[e.jsx(Se,{className:"h-4 w-4"}),"Output"]}),e.jsxs(Q,{value:"results",className:"flex items-center gap-2",children:[e.jsx(xe,{className:"h-4 w-4"}),"Results"]}),e.jsxs(Q,{value:"command",className:"flex items-center gap-2",children:[e.jsx(ue,{className:"h-4 w-4"}),"Command"]})]}),e.jsxs(J,{value:"configure",className:"space-y-6",children:[e.jsxs("div",{className:"grid gap-6 lg:grid-cols-2",children:[e.jsxs(y,{children:[e.jsxs(T,{children:[e.jsxs(A,{className:"flex items-center gap-2",children:[e.jsx(ue,{className:"h-5 w-5"}),"Target Configuration"]}),e.jsx(F,{children:"Specify targets and basic scan parameters"})]}),e.jsxs(w,{className:"space-y-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium",children:"Targets"}),e.jsx(Le,{placeholder:"e.g., ***********/24, ********-**********, example.com",value:t.targets,onChange:m=>k({targets:m.target.value}),className:"min-h-[80px]"}),e.jsx("p",{className:"text-xs text-muted-foreground",children:"IP addresses, CIDR ranges, or hostnames (one per line or comma-separated)"})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium",children:"Ports"}),e.jsx(P,{placeholder:"e.g., 80,443, 1-1000, 22,80,443,8080",value:t.ports,onChange:m=>k({ports:m.target.value})}),e.jsx("div",{className:"flex flex-wrap gap-1",children:Object.entries(Qt).slice(0,4).map(([m])=>e.jsx(v,{variant:"outline",size:"sm",className:"text-xs h-6",onClick:()=>D(m),children:m},m))})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium",children:"Exclude Ranges"}),e.jsx(P,{placeholder:"e.g., ***********, 10.0.0.0/8",value:t.excludeRanges,onChange:m=>k({excludeRanges:m.target.value})}),e.jsx("p",{className:"text-xs text-muted-foreground",children:"IP ranges to exclude from scanning"})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium",children:"Max Runtime (seconds)"}),e.jsx(P,{type:"number",placeholder:"3600",value:t.maxRuntime,onChange:m=>k({maxRuntime:parseInt(m.target.value)||0})}),e.jsx("p",{className:"text-xs text-muted-foreground",children:"Maximum scan duration (0 for unlimited)"})]})]})]}),e.jsxs(y,{children:[e.jsxs(T,{children:[e.jsxs(A,{className:"flex items-center gap-2",children:[e.jsx(ma,{className:"h-5 w-5"}),"Performance Settings"]}),e.jsx(F,{children:"Configure scan speed and network parameters"})]}),e.jsxs(w,{className:"space-y-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsxs("label",{className:"text-sm font-medium",children:["Scan Rate: ",S(t.rate)," packets/second"]}),e.jsx("input",{type:"range",min:"100",max:"1000000",step:"100",value:t.rate,onChange:m=>k({rate:parseInt(m.target.value)}),className:"w-full"}),e.jsx("div",{className:"flex flex-wrap gap-1",children:Object.entries(Gt).map(([m,o])=>e.jsx(v,{variant:"outline",size:"sm",className:"text-xs h-6",onClick:()=>N(m),title:o.description,children:o.label.split(" ")[0]},m))})]}),e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium",children:"Source Port"}),e.jsx(P,{placeholder:"Random",value:t.sourcePort,onChange:m=>k({sourcePort:m.target.value})})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium",children:"Source IP"}),e.jsx(P,{placeholder:"Auto-detect",value:t.sourceIP,onChange:m=>k({sourceIP:m.target.value})})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium",children:"Network Interface"}),e.jsx(P,{placeholder:"Auto-detect (eth0, wlan0, etc.)",value:t.interface,onChange:m=>k({interface:m.target.value})})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium",children:"Output Format"}),e.jsxs(pe,{value:t.outputFormat,onValueChange:m=>k({outputFormat:m}),children:[e.jsx(oe,{children:e.jsx(de,{})}),e.jsxs(me,{children:[e.jsx(_,{value:"list",children:"List Format"}),e.jsx(_,{value:"json",children:"JSON Format"}),e.jsx(_,{value:"xml",children:"XML Format"}),e.jsx(_,{value:"binary",children:"Binary Format"})]})]})]})]})]})]}),e.jsxs(y,{children:[e.jsxs(T,{children:[e.jsxs(A,{className:"flex items-center gap-2",children:[e.jsx(Oe,{className:"h-5 w-5"}),"Advanced Options"]}),e.jsx(F,{children:"Additional scanning options and custom arguments"})]}),e.jsxs(w,{className:"space-y-4",children:[e.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[e.jsxs("label",{className:"flex items-center space-x-2",children:[e.jsx("input",{type:"checkbox",checked:t.banners,onChange:m=>k({banners:m.target.checked}),className:"rounded border-gray-300"}),e.jsx("span",{className:"text-sm",children:"Banner Grabbing"})]}),e.jsxs("label",{className:"flex items-center space-x-2",children:[e.jsx("input",{type:"checkbox",checked:t.includeClosed,onChange:m=>k({includeClosed:m.target.checked}),className:"rounded border-gray-300"}),e.jsx("span",{className:"text-sm",children:"Include Closed"})]}),e.jsxs("label",{className:"flex items-center space-x-2",children:[e.jsx("input",{type:"checkbox",checked:t.ping,onChange:m=>k({ping:m.target.checked}),className:"rounded border-gray-300"}),e.jsx("span",{className:"text-sm",children:"Ping Hosts"})]}),e.jsxs("label",{className:"flex items-center space-x-2",children:[e.jsx("input",{type:"checkbox",checked:t.randomizeTargets,onChange:m=>k({randomizeTargets:m.target.checked}),className:"rounded border-gray-300"}),e.jsx("span",{className:"text-sm",children:"Randomize Order"})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium",children:"Custom Arguments"}),e.jsx(P,{placeholder:"Additional masscan arguments",value:t.customArgs,onChange:m=>k({customArgs:m.target.value})})]}),e.jsxs("div",{className:"flex justify-between items-center pt-4",children:[e.jsx("div",{className:"flex gap-2",children:e.jsxs(v,{variant:"outline",size:"sm",onClick:z,children:[e.jsx(je,{className:"h-4 w-4 mr-1"}),"Copy Command"]})}),e.jsx("div",{className:"flex gap-2",children:a?e.jsxs(v,{variant:"destructive",onClick:q,children:[e.jsx(We,{className:"h-4 w-4 mr-2"}),"Stop Scan"]}):e.jsxs(v,{onClick:I,disabled:!t.targets.trim()||!t.ports.trim()||!u.connected,children:[e.jsx(ae,{className:"h-4 w-4 mr-2"}),"Start Scan"]})})]})]})]})]}),e.jsx(J,{value:"output",className:"space-y-6",children:e.jsxs(y,{children:[e.jsx(T,{children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsxs(A,{className:"flex items-center gap-2",children:[e.jsx(Se,{className:"h-5 w-5"}),"Real-time Output"]}),e.jsx(F,{children:"Live output from the Masscan process"})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[a&&e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(Ae,{value:p,className:"w-32"}),e.jsxs("span",{className:"text-sm text-muted-foreground",children:[p,"%"]})]}),e.jsx(v,{variant:"outline",size:"sm",onClick:()=>b([]),disabled:a,children:e.jsx(qe,{className:"h-4 w-4"})})]})]})}),e.jsx(w,{children:e.jsx("div",{className:"bg-gray-900 text-gray-100 p-4 rounded-lg h-96 overflow-y-auto font-mono text-sm",children:g.length===0?e.jsx("div",{className:"text-gray-500 italic",children:a?"Waiting for output...":"No output yet. Start a scan to see results here."}):g.map((m,o)=>e.jsx("div",{className:"mb-1",children:m},o))})})]})}),e.jsx(J,{value:"results",className:"space-y-6",children:e.jsxs(y,{children:[e.jsx(T,{children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsxs(A,{className:"flex items-center gap-2",children:[e.jsx(xe,{className:"h-5 w-5"}),"Scan Results"]}),e.jsx(F,{children:"Structured results from the completed scan"})]}),e.jsxs(v,{variant:"outline",onClick:X,disabled:!j&&g.length===0,children:[e.jsx(ke,{className:"h-4 w-4 mr-2"}),"Export Results"]})]})}),e.jsx(w,{children:j?e.jsx("div",{className:"space-y-4",children:e.jsx("pre",{className:"bg-muted p-4 rounded-lg overflow-auto text-sm",children:JSON.stringify(j,null,2)})}):e.jsxs("div",{className:"text-center py-8 text-muted-foreground",children:[e.jsx(xe,{className:"h-12 w-12 mx-auto mb-4 opacity-50"}),e.jsx("p",{children:"No structured results available yet."}),e.jsx("p",{className:"text-sm",children:"Complete a scan to see parsed results here."})]})})]})}),e.jsx(J,{value:"command",className:"space-y-6",children:e.jsxs(y,{children:[e.jsxs(T,{children:[e.jsxs(A,{className:"flex items-center gap-2",children:[e.jsx(ue,{className:"h-5 w-5"}),"Generated Command"]}),e.jsx(F,{children:"The Masscan command that will be executed based on your configuration"})]}),e.jsx(w,{children:e.jsxs("div",{className:"space-y-4",children:[e.jsx("div",{className:"bg-muted p-4 rounded-lg",children:e.jsx("code",{className:"text-sm font-mono break-all",children:W()})}),e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("p",{className:"text-sm text-muted-foreground",children:"This command will be executed on the backend server"}),e.jsxs(v,{variant:"outline",onClick:z,children:[e.jsx(je,{className:"h-4 w-4 mr-2"}),"Copy Command"]})]}),t.rate>1e4&&e.jsxs("div",{className:"flex items-start gap-2 p-3 bg-yellow-50 dark:bg-yellow-900/10 rounded-md border border-yellow-200 dark:border-yellow-800",children:[e.jsx(ee,{className:"h-4 w-4 text-yellow-600 dark:text-yellow-400 mt-0.5 flex-shrink-0"}),e.jsxs("div",{className:"text-sm text-yellow-700 dark:text-yellow-300",children:[e.jsx("p",{className:"font-medium",children:"High Rate Warning"}),e.jsxs("p",{children:["Scanning at ",S(t.rate)," pps may trigger intrusion detection systems and could impact network performance."]})]})]})]})})]})})]})]})}const Ys={tcp_synscan:{name:"TCP SYN Scan",description:"Standard TCP SYN port scanning",defaultPort:80,fields:["saddr","daddr","sport","dport","seqnum","acknum","window"]},icmp_echoscan:{name:"ICMP Echo Scan",description:"ICMP ping sweep for host discovery",defaultPort:0,fields:["saddr","daddr","type","code","icmp-id","seq"]},udp:{name:"UDP Scan",description:"UDP port scanning",defaultPort:53,fields:["saddr","daddr","sport","dport","udp-plen","data"]},tcp_ack:{name:"TCP ACK Scan",description:"TCP ACK scanning for firewall detection",defaultPort:80,fields:["saddr","daddr","sport","dport","seqnum","acknum","window"]},dns:{name:"DNS Scan",description:"DNS resolver scanning",defaultPort:53,fields:["saddr","daddr","sport","dport","dns-qname","dns-qtype","dns-rcode"]}},ei=["saddr","daddr","sport","dport","seqnum","acknum","window","classification","success","repeat","cooldown","timestamp-str","timestamp-ts","timestamp-us"],Jt={conservative:{value:"1M",label:"1 Mbps",description:"Safe for most networks"},moderate:{value:"10M",label:"10 Mbps",description:"Moderate scanning speed"},aggressive:{value:"100M",label:"100 Mbps",description:"Fast scanning"},maximum:{value:"1G",label:"1 Gbps",description:"Maximum bandwidth"},research:{value:"10G",label:"10 Gbps",description:"Research-grade scanning"}},Kt={single:{subnet:"",max:1,description:"Single host"},local:{subnet:"***********/24",max:256,description:"Local subnet"},enterprise:{subnet:"10.0.0.0/16",max:65536,description:"Enterprise network"},research:{subnet:"0.0.0.0/0",max:1e6,description:"Internet research (1M hosts)"},survey:{subnet:"0.0.0.0/0",max:1e7,description:"Internet survey (10M hosts)"}};function ka(){const[t,s]=x.useState({port:80,targetSubnet:"",bandwidth:"10M",sourcePort:"",sourceIP:"",interface:"",probeModule:"tcp_synscan",outputFields:["saddr","daddr","sport","dport","classification"],outputFilter:"",blacklistFile:"",whitelistFile:"",maxTargets:1e3,maxRuntime:3600,maxResults:1e6,cooldownTime:8,seed:"",verbosity:1,dryRun:!1,summaryStats:!0,metadataFile:"",userAgent:"",customArgs:""}),[a,n]=x.useState(!1),[r,c]=x.useState(null),[p,d]=x.useState(0),[g,b]=x.useState([]),[j,R]=x.useState(null),[E,f]=x.useState("configure"),[L,i]=x.useState({estimatedHosts:0,estimatedTime:0,packetsPerSecond:0,totalPackets:0}),{status:u}=Pe();x.useEffect(()=>{(()=>{let h=t.maxTargets;if(t.targetSubnet.includes("/")){const G=parseInt(t.targetSubnet.split("/")[1]);if(G){const ye=Math.pow(2,32-G);h=Math.min(ye,t.maxTargets)}}const M=k(t.bandwidth)/(64*8),H=h,le=H/M;i({estimatedHosts:h,estimatedTime:le,packetsPerSecond:M,totalPackets:H})})()},[t.targetSubnet,t.maxTargets,t.bandwidth]);const k=l=>{const h=parseFloat(l);switch(l.replace(/[0-9.]/g,"").toLowerCase()){case"k":case"kbps":return h*1e3;case"m":case"mbps":return h*1e6;case"g":case"gbps":return h*1e9;default:return h}},N=l=>{s(h=>({...h,...l}))},D=l=>{const h=Kt[l];h&&(N({targetSubnet:h.subnet,maxTargets:h.max}),C.success(`Applied ${l} target preset`))},W=l=>{const h=Jt[l];h&&(N({bandwidth:h.value}),C.success(`Applied ${h.label} bandwidth`))},U=l=>{const h=Ys[l];h&&N({probeModule:l,port:h.defaultPort,outputFields:h.fields.slice(0,5)})},S=()=>{let l="zmap";return t.port>0&&(l+=` -p ${t.port}`),l+=` -M ${t.probeModule}`,t.targetSubnet.trim()&&(l+=` ${t.targetSubnet}`),t.bandwidth.trim()&&(l+=` -B ${t.bandwidth}`),t.sourcePort.trim()&&(l+=` -s ${t.sourcePort}`),t.sourceIP.trim()&&(l+=` -S ${t.sourceIP}`),t.interface.trim()&&(l+=` -i ${t.interface}`),t.outputFields.length>0&&(l+=` -f "${t.outputFields.join(",")}"`),t.outputFilter.trim()&&(l+=` --output-filter="${t.outputFilter}"`),t.blacklistFile.trim()&&(l+=` -b ${t.blacklistFile}`),t.whitelistFile.trim()&&(l+=` -w ${t.whitelistFile}`),t.maxTargets>0&&(l+=` -n ${t.maxTargets}`),t.maxRuntime>0&&(l+=` --max-runtime=${t.maxRuntime}`),t.maxResults>0&&(l+=` --max-results=${t.maxResults}`),t.cooldownTime!==8&&(l+=` -c ${t.cooldownTime}`),t.seed.trim()&&(l+=` --seed=${t.seed}`),t.verbosity!==1&&(l+=` -v ${t.verbosity}`),t.dryRun&&(l+=" --dryrun"),t.summaryStats||(l+=" --disable-syslog"),t.metadataFile.trim()&&(l+=` --metadata-file=${t.metadataFile}`),t.userAgent.trim()&&(l+=` --user-agent="${t.userAgent}"`),t.customArgs.trim()&&(l+=` ${t.customArgs.trim()}`),l},I=l=>l<60?`${Math.round(l)}s`:l<3600?`${Math.round(l/60)}m`:l<86400?`${Math.round(l/3600)}h`:`${Math.round(l/86400)}d`,q=l=>l>=1e9?`${(l/1e9).toFixed(1)}B`:l>=1e6?`${(l/1e6).toFixed(1)}M`:l>=1e3?`${(l/1e3).toFixed(1)}K`:l.toString(),z=async()=>{if(!t.targetSubnet.trim()&&!t.whitelistFile.trim()){C.error("Please specify targets or whitelist file");return}if(t.port<=0&&t.probeModule!=="icmp_echoscan"){C.error("Please specify a valid port");return}if(!u.connected){C.error("Backend not connected");return}if(!(t.maxTargets>1e5&&!window.confirm(`You are about to scan ${q(t.maxTargets)} hosts. This is a large-scale scan that may:

• Take ${I(L.estimatedTime)} to complete
• Generate significant network traffic
• Trigger security monitoring systems

Are you sure you want to proceed?`)))try{n(!0),d(0),b([]),R(null),f("output");const l=await ne.executeTool("zmap",t,h=>{c(h),d(h.progress),h.output&&b(O=>[...O,...h.output]),h.status==="completed"?(n(!1),R(h.results),C.success("Zmap scan completed")):h.status==="failed"&&(n(!1),C.error(`Scan failed: ${h.error}`))});c(l),C.info("Zmap scan started")}catch(l){n(!1),console.error("Failed to start Zmap scan:",l),C.error("Failed to start scan")}},X=async()=>{if(r)try{await ne.cancelExecution(r.id),n(!1),c(null),C.info("Scan cancelled")}catch(l){console.error("Failed to stop scan:",l),C.error("Failed to stop scan")}},m=async()=>{try{await navigator.clipboard.writeText(S()),C.success("Command copied to clipboard")}catch{C.error("Failed to copy command")}},o=()=>{if(!j&&g.length===0){C.error("No results to export");return}const l=j||g.join(`
`),h=new Blob([JSON.stringify(l,null,2)],{type:"application/json"}),O=URL.createObjectURL(h),M=document.createElement("a");M.href=O,M.download=`zmap-scan-${new Date().toISOString().slice(0,19)}.json`,document.body.appendChild(M),M.click(),document.body.removeChild(M),URL.revokeObjectURL(O),C.success("Results exported")};return e.jsxs("div",{className:"container-desktop py-6 space-y-6",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"p-2 bg-purple-100 dark:bg-purple-900/20 rounded-lg",children:e.jsx(bs,{className:"h-6 w-6 text-purple-600 dark:text-purple-400"})}),e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold",children:"Zmap Scanner"}),e.jsx("p",{className:"text-muted-foreground",children:"Internet-wide network scanner for research and large-scale security assessment"})]})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx($,{variant:u.connected?"default":"destructive",children:u.connected?"Connected":"Offline"}),a&&e.jsxs($,{variant:"secondary",className:"animate-pulse",children:["Scanning ",q(t.maxTargets)," hosts"]})]})]}),e.jsxs(y,{className:"border-yellow-200 dark:border-yellow-800 bg-yellow-50/50 dark:bg-yellow-900/10",children:[e.jsx(T,{className:"pb-3",children:e.jsxs(A,{className:"flex items-center gap-2 text-yellow-600 dark:text-yellow-400",children:[e.jsx(fe,{className:"h-5 w-5"}),"Research Tool Notice"]})}),e.jsxs(w,{className:"text-sm space-y-2",children:[e.jsx("p",{children:"Zmap is designed for Internet-wide research and large-scale security studies. Please ensure:"}),e.jsxs("ul",{className:"list-disc list-inside space-y-1 text-muted-foreground",children:[e.jsx("li",{children:"You have permission to scan the target networks"}),e.jsx("li",{children:"Your scans comply with your organization's policies and local laws"}),e.jsx("li",{children:"You use appropriate rate limiting to avoid network disruption"}),e.jsx("li",{children:"You consider the ethical implications of large-scale scanning"})]})]})]}),t.targetSubnet&&e.jsxs(y,{className:"border-purple-200 dark:border-purple-800",children:[e.jsx(T,{className:"pb-3",children:e.jsxs(A,{className:"flex items-center gap-2 text-purple-600 dark:text-purple-400",children:[e.jsx(ts,{className:"h-5 w-5"}),"Scan Projection"]})}),e.jsx(w,{children:e.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-purple-600 dark:text-purple-400",children:q(L.estimatedHosts)}),e.jsx("div",{className:"text-xs text-muted-foreground",children:"Target Hosts"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-purple-600 dark:text-purple-400",children:q(L.packetsPerSecond)}),e.jsx("div",{className:"text-xs text-muted-foreground",children:"Packets/sec"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-purple-600 dark:text-purple-400",children:I(L.estimatedTime)}),e.jsx("div",{className:"text-xs text-muted-foreground",children:"Est. Duration"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-purple-600 dark:text-purple-400",children:t.bandwidth}),e.jsx("div",{className:"text-xs text-muted-foreground",children:"Bandwidth"})]})]})})]}),e.jsxs(Ue,{value:E,onValueChange:f,className:"space-y-6",children:[e.jsxs(Be,{className:"grid w-full grid-cols-4",children:[e.jsxs(Q,{value:"configure",className:"flex items-center gap-2",children:[e.jsx(Y,{className:"h-4 w-4"}),"Configure"]}),e.jsxs(Q,{value:"output",className:"flex items-center gap-2",children:[e.jsx(Se,{className:"h-4 w-4"}),"Output"]}),e.jsxs(Q,{value:"results",className:"flex items-center gap-2",children:[e.jsx(xe,{className:"h-4 w-4"}),"Results"]}),e.jsxs(Q,{value:"command",className:"flex items-center gap-2",children:[e.jsx(ue,{className:"h-4 w-4"}),"Command"]})]}),e.jsxs(J,{value:"configure",className:"space-y-6",children:[e.jsxs("div",{className:"grid gap-6 lg:grid-cols-2",children:[e.jsxs(y,{children:[e.jsxs(T,{children:[e.jsxs(A,{className:"flex items-center gap-2",children:[e.jsx(ue,{className:"h-5 w-5"}),"Target Configuration"]}),e.jsx(F,{children:"Specify scan targets and basic parameters"})]}),e.jsxs(w,{className:"space-y-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium",children:"Target Presets"}),e.jsx("div",{className:"grid grid-cols-2 gap-2",children:Object.entries(Kt).map(([l,h])=>e.jsxs(v,{variant:"outline",size:"sm",onClick:()=>D(l),className:"h-auto p-3 flex flex-col items-start",children:[e.jsx("span",{className:"font-medium capitalize",children:l}),e.jsx("span",{className:"text-xs text-muted-foreground",children:h.description})]},l))})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium",children:"Target Subnet"}),e.jsx(P,{placeholder:"e.g., ***********/24, 0.0.0.0/0",value:t.targetSubnet,onChange:l=>N({targetSubnet:l.target.value})}),e.jsx("p",{className:"text-xs text-muted-foreground",children:"CIDR notation subnet to scan (0.0.0.0/0 for Internet-wide)"})]}),e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium",children:"Port"}),e.jsx(P,{type:"number",placeholder:"80",value:t.port,onChange:l=>N({port:parseInt(l.target.value)||0})})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium",children:"Probe Module"}),e.jsxs(pe,{value:t.probeModule,onValueChange:U,children:[e.jsx(oe,{children:e.jsx(de,{})}),e.jsx(me,{children:Object.entries(Ys).map(([l,h])=>e.jsx(_,{value:l,children:h.name},l))})]})]})]}),e.jsx("div",{className:"text-xs text-muted-foreground p-2 bg-muted/50 rounded",children:Ys[t.probeModule].description}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs("label",{className:"text-sm font-medium",children:["Max Targets: ",q(t.maxTargets)]}),e.jsx("input",{type:"range",min:"1",max:"10000000",step:"1000",value:t.maxTargets,onChange:l=>N({maxTargets:parseInt(l.target.value)}),className:"w-full"}),e.jsxs("div",{className:"flex justify-between text-xs text-muted-foreground",children:[e.jsx("span",{children:"1"}),e.jsx("span",{children:"1K"}),e.jsx("span",{children:"100K"}),e.jsx("span",{children:"1M"}),e.jsx("span",{children:"10M"})]})]})]})]}),e.jsxs(y,{children:[e.jsxs(T,{children:[e.jsxs(A,{className:"flex items-center gap-2",children:[e.jsx(hn,{className:"h-5 w-5"}),"Performance Settings"]}),e.jsx(F,{children:"Configure bandwidth and timing parameters"})]}),e.jsxs(w,{className:"space-y-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium",children:"Bandwidth Presets"}),e.jsx("div",{className:"grid grid-cols-2 gap-2",children:Object.entries(Jt).map(([l,h])=>e.jsxs(v,{variant:"outline",size:"sm",onClick:()=>W(l),className:"h-auto p-3 flex flex-col items-start",children:[e.jsx("span",{className:"font-medium",children:h.label}),e.jsx("span",{className:"text-xs text-muted-foreground",children:h.description})]},l))})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium",children:"Bandwidth"}),e.jsx(P,{placeholder:"e.g., 10M, 1G",value:t.bandwidth,onChange:l=>N({bandwidth:l.target.value})}),e.jsx("p",{className:"text-xs text-muted-foreground",children:"Bandwidth limit (K/M/G suffix for Kbps/Mbps/Gbps)"})]}),e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium",children:"Source Port"}),e.jsx(P,{placeholder:"Random",value:t.sourcePort,onChange:l=>N({sourcePort:l.target.value})})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium",children:"Source IP"}),e.jsx(P,{placeholder:"Auto-detect",value:t.sourceIP,onChange:l=>N({sourceIP:l.target.value})})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium",children:"Network Interface"}),e.jsx(P,{placeholder:"Auto-detect (eth0, wlan0, etc.)",value:t.interface,onChange:l=>N({interface:l.target.value})})]}),e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium",children:"Max Runtime (sec)"}),e.jsx(P,{type:"number",placeholder:"3600",value:t.maxRuntime,onChange:l=>N({maxRuntime:parseInt(l.target.value)||0})})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium",children:"Cooldown Time"}),e.jsx(P,{type:"number",placeholder:"8",value:t.cooldownTime,onChange:l=>N({cooldownTime:parseInt(l.target.value)||8})})]})]})]})]})]}),e.jsxs(y,{children:[e.jsxs(T,{children:[e.jsxs(A,{className:"flex items-center gap-2",children:[e.jsx(He,{className:"h-5 w-5"}),"Output & Advanced Options"]}),e.jsx(F,{children:"Configure output fields, filters, and advanced scanning options"})]}),e.jsxs(w,{className:"space-y-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium",children:"Output Fields"}),e.jsx("div",{className:"grid grid-cols-3 gap-2",children:ei.map(l=>e.jsxs("label",{className:"flex items-center space-x-2",children:[e.jsx("input",{type:"checkbox",checked:t.outputFields.includes(l),onChange:h=>{h.target.checked?N({outputFields:[...t.outputFields,l]}):N({outputFields:t.outputFields.filter(O=>O!==l)})},className:"rounded border-gray-300"}),e.jsx("span",{className:"text-xs font-mono",children:l})]},l))})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium",children:"Output Filter"}),e.jsx(P,{placeholder:"e.g., success = 1 && repeat = 0",value:t.outputFilter,onChange:l=>N({outputFilter:l.target.value})}),e.jsx("p",{className:"text-xs text-muted-foreground",children:"Filter expression for output results"})]}),e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium",children:"Blacklist File"}),e.jsx(P,{placeholder:"Path to blacklist file",value:t.blacklistFile,onChange:l=>N({blacklistFile:l.target.value})})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium",children:"Whitelist File"}),e.jsx(P,{placeholder:"Path to whitelist file",value:t.whitelistFile,onChange:l=>N({whitelistFile:l.target.value})})]})]}),e.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[e.jsxs("label",{className:"flex items-center space-x-2",children:[e.jsx("input",{type:"checkbox",checked:t.dryRun,onChange:l=>N({dryRun:l.target.checked}),className:"rounded border-gray-300"}),e.jsx("span",{className:"text-sm",children:"Dry Run"})]}),e.jsxs("label",{className:"flex items-center space-x-2",children:[e.jsx("input",{type:"checkbox",checked:t.summaryStats,onChange:l=>N({summaryStats:l.target.checked}),className:"rounded border-gray-300"}),e.jsx("span",{className:"text-sm",children:"Summary Stats"})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium",children:"Custom Arguments"}),e.jsx(P,{placeholder:"Additional zmap arguments",value:t.customArgs,onChange:l=>N({customArgs:l.target.value})})]}),e.jsxs("div",{className:"flex justify-between items-center pt-4",children:[e.jsx("div",{className:"flex gap-2",children:e.jsxs(v,{variant:"outline",size:"sm",onClick:m,children:[e.jsx(je,{className:"h-4 w-4 mr-1"}),"Copy Command"]})}),e.jsx("div",{className:"flex gap-2",children:a?e.jsxs(v,{variant:"destructive",onClick:X,children:[e.jsx(We,{className:"h-4 w-4 mr-2"}),"Stop Scan"]}):e.jsxs(v,{onClick:z,disabled:!t.targetSubnet.trim()&&!t.whitelistFile.trim()||!u.connected,children:[e.jsx(ae,{className:"h-4 w-4 mr-2"}),"Start Scan"]})})]})]})]})]}),e.jsx(J,{value:"output",className:"space-y-6",children:e.jsxs(y,{children:[e.jsx(T,{children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsxs(A,{className:"flex items-center gap-2",children:[e.jsx(Se,{className:"h-5 w-5"}),"Real-time Output"]}),e.jsx(F,{children:"Live output from the Zmap scanning process"})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[a&&e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(Ae,{value:p,className:"w-32"}),e.jsxs("span",{className:"text-sm text-muted-foreground",children:[p,"%"]})]}),e.jsx(v,{variant:"outline",size:"sm",onClick:()=>b([]),disabled:a,children:e.jsx(qe,{className:"h-4 w-4"})})]})]})}),e.jsx(w,{children:e.jsx("div",{className:"bg-gray-900 text-gray-100 p-4 rounded-lg h-96 overflow-y-auto font-mono text-sm",children:g.length===0?e.jsx("div",{className:"text-gray-500 italic",children:a?"Waiting for output...":"No output yet. Start a scan to see results here."}):g.map((l,h)=>e.jsx("div",{className:"mb-1",children:l},h))})})]})}),e.jsx(J,{value:"results",className:"space-y-6",children:e.jsxs(y,{children:[e.jsx(T,{children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsxs(A,{className:"flex items-center gap-2",children:[e.jsx(xe,{className:"h-5 w-5"}),"Scan Results"]}),e.jsx(F,{children:"Structured results and analysis from the completed scan"})]}),e.jsxs(v,{variant:"outline",onClick:o,disabled:!j&&g.length===0,children:[e.jsx(ke,{className:"h-4 w-4 mr-2"}),"Export Results"]})]})}),e.jsx(w,{children:j?e.jsx("div",{className:"space-y-4",children:e.jsx("pre",{className:"bg-muted p-4 rounded-lg overflow-auto text-sm",children:JSON.stringify(j,null,2)})}):e.jsxs("div",{className:"text-center py-8 text-muted-foreground",children:[e.jsx(xe,{className:"h-12 w-12 mx-auto mb-4 opacity-50"}),e.jsx("p",{children:"No structured results available yet."}),e.jsx("p",{className:"text-sm",children:"Complete a scan to see parsed results here."})]})})]})}),e.jsx(J,{value:"command",className:"space-y-6",children:e.jsxs(y,{children:[e.jsxs(T,{children:[e.jsxs(A,{className:"flex items-center gap-2",children:[e.jsx(ue,{className:"h-5 w-5"}),"Generated Command"]}),e.jsx(F,{children:"The Zmap command that will be executed based on your configuration"})]}),e.jsx(w,{children:e.jsxs("div",{className:"space-y-4",children:[e.jsx("div",{className:"bg-muted p-4 rounded-lg",children:e.jsx("code",{className:"text-sm font-mono break-all",children:S()})}),e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("p",{className:"text-sm text-muted-foreground",children:"This command will be executed on the backend server"}),e.jsxs(v,{variant:"outline",onClick:m,children:[e.jsx(je,{className:"h-4 w-4 mr-2"}),"Copy Command"]})]}),t.maxTargets>1e5&&e.jsxs("div",{className:"flex items-start gap-2 p-3 bg-red-50 dark:bg-red-900/10 rounded-md border border-red-200 dark:border-red-800",children:[e.jsx(ee,{className:"h-4 w-4 text-red-600 dark:text-red-400 mt-0.5 flex-shrink-0"}),e.jsxs("div",{className:"text-sm text-red-700 dark:text-red-300",children:[e.jsx("p",{className:"font-medium",children:"Large Scale Scan Warning"}),e.jsxs("p",{children:["This scan targets ",q(t.maxTargets)," hosts and may:"]}),e.jsxs("ul",{className:"list-disc list-inside mt-1 space-y-1",children:[e.jsxs("li",{children:["Generate significant network traffic (",t.bandwidth," bandwidth)"]}),e.jsxs("li",{children:["Take ",I(L.estimatedTime)," to complete"]}),e.jsx("li",{children:"Trigger security monitoring and intrusion detection systems"}),e.jsx("li",{children:"Require compliance with responsible disclosure practices"})]})]})]})]})})]})})]})]})}const As={"full-and-fast":{name:"Full and Fast",description:"Comprehensive scan with optimal speed/accuracy balance",estimatedTime:"2-4 hours",severity:"medium"},"full-and-deep":{name:"Full and Deep",description:"Thorough scan with maximum vulnerability coverage",estimatedTime:"4-8 hours",severity:"high"},"system-discovery":{name:"System Discovery",description:"Basic host and service discovery",estimatedTime:"30-60 minutes",severity:"low"},"host-discovery":{name:"Host Discovery",description:"Simple host availability check",estimatedTime:"5-15 minutes",severity:"low"},"full-and-very-deep":{name:"Full and Very Deep",description:"Maximum depth vulnerability assessment",estimatedTime:"8-24 hours",severity:"very-high"},"full-and-very-deep-ultimate":{name:"Full and Very Deep Ultimate",description:"Ultimate comprehensive vulnerability scan",estimatedTime:"24+ hours",severity:"extreme"}},si={"all-iana-assigned":"All IANA assigned TCP and UDP ports (1-65535)","all-tcp":"All TCP ports (1-65535)","all-tcp-nmap-top-1000":"Top 1000 TCP ports by frequency","all-privileged-tcp":"All privileged TCP ports (1-1023)","all-tcp-nmap-top-100":"Top 100 TCP ports","openvas-default":"OpenVAS default port list"},ti=[{value:"log",label:"Log",color:"gray",description:"Informational entries"},{value:"low",label:"Low",color:"blue",description:"Minor security issues"},{value:"medium",label:"Medium",color:"yellow",description:"Moderate vulnerabilities"},{value:"high",label:"High",color:"orange",description:"Serious security flaws"},{value:"critical",label:"Critical",color:"red",description:"Critical vulnerabilities"}];function Sa(){const[t,s]=x.useState({targets:"",scanConfigId:"full-and-fast",scannerId:"default",portList:"all-tcp-nmap-top-1000",credentials:null,preferences:{maxChecks:5,maxHosts:20,sourceInterface:"",optimizeTest:!0,reverseLookup:!1,dropPrivileges:!0},scheduleEnabled:!1,scheduleTime:"",alertsEnabled:!1,reportFormat:"html",customArgs:""}),[a,n]=x.useState(!1),[r,c]=x.useState(null),[p,d]=x.useState(0),[g,b]=x.useState([]),[j,R]=x.useState(null),[E,f]=x.useState("configure"),[L,i]=x.useState(!1),[u,k]=x.useState(null),{status:N}=Pe(),D=l=>{s(h=>({...h,...l}))},W=l=>{s(h=>({...h,preferences:{...h.preferences,...l}}))},U=l=>{s(h=>({...h,credentials:h.credentials?{...h.credentials,...l}:{username:"",password:"",type:"ssh",...l}}))},S=l=>{D({scanConfigId:l});const h=As[l];h&&C.success(`Applied ${h.name} configuration`)},I=()=>As[t.scanConfigId]?.estimatedTime||"Unknown",q=()=>{let l='gvmd --xml="<create_task>"';return l+=`<name>NexusScan-${Date.now()}</name>`,l+="<comment>Automated vulnerability scan</comment>",l+=`<target id="${t.targets}"/>`,l+=`<config id="${t.scanConfigId}"/>`,l+=`<scanner id="${t.scannerId}"/>`,t.preferences.maxChecks!==5&&(l+=`<preferences><preference><scanner_name>max_checks</scanner_name><value>${t.preferences.maxChecks}</value></preference></preferences>`),t.scheduleEnabled&&t.scheduleTime&&(l+=`<schedule id="schedule-${t.scheduleTime}"/>`),l+='"</create_task>"',t.customArgs.trim()&&(l+=` ${t.customArgs.trim()}`),l},z=async()=>{if(!t.targets.trim()){C.error("Please specify target hosts");return}if(!N.connected){C.error("Backend not connected");return}try{n(!0),d(0),b([]),R(null),k(null),f("output");const l=await ne.executeTool("openvas",t,h=>{if(c(h),d(h.progress),h.output&&b(O=>[...O,...h.output]),h.status==="completed"){if(n(!1),R(h.results),h.results?.vulnerabilities){const O=h.results.vulnerabilities.reduce((M,H)=>{const le=H.severity||"low";return M[le]=(M[le]||0)+1,M},{});k(O)}C.success("OpenVAS scan completed")}else h.status==="failed"&&(n(!1),C.error(`Scan failed: ${h.error}`))});c(l),C.info("OpenVAS scan started")}catch(l){n(!1),console.error("Failed to start OpenVAS scan:",l),C.error("Failed to start scan")}},X=async()=>{if(r)try{await ne.cancelExecution(r.id),n(!1),c(null),C.info("Scan cancelled")}catch(l){console.error("Failed to stop scan:",l),C.error("Failed to stop scan")}},m=async()=>{try{await navigator.clipboard.writeText(q()),C.success("Command copied to clipboard")}catch{C.error("Failed to copy command")}},o=()=>{if(!j&&g.length===0){C.error("No results to export");return}const l=j||g.join(`
`),h=new Blob([JSON.stringify(l,null,2)],{type:"application/json"}),O=URL.createObjectURL(h),M=document.createElement("a");M.href=O,M.download=`openvas-scan-${new Date().toISOString().slice(0,19)}.json`,document.body.appendChild(M),M.click(),document.body.removeChild(M),URL.revokeObjectURL(O),C.success("Results exported")};return e.jsxs("div",{className:"container-desktop py-6 space-y-6",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"p-2 bg-green-100 dark:bg-green-900/20 rounded-lg",children:e.jsx(fe,{className:"h-6 w-6 text-green-600 dark:text-green-400"})}),e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold",children:"OpenVAS Scanner"}),e.jsx("p",{className:"text-muted-foreground",children:"Comprehensive vulnerability assessment and management framework"})]})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx($,{variant:N.connected?"default":"destructive",children:N.connected?"Connected":"Offline"}),a&&e.jsx($,{variant:"secondary",className:"animate-pulse",children:"Scanning..."})]})]}),e.jsxs(Ue,{value:E,onValueChange:f,className:"space-y-6",children:[e.jsxs(Be,{className:"grid w-full grid-cols-4",children:[e.jsxs(Q,{value:"configure",className:"flex items-center gap-2",children:[e.jsx(Y,{className:"h-4 w-4"}),"Configure"]}),e.jsxs(Q,{value:"output",className:"flex items-center gap-2",children:[e.jsx(Se,{className:"h-4 w-4"}),"Output"]}),e.jsxs(Q,{value:"results",className:"flex items-center gap-2",children:[e.jsx(xe,{className:"h-4 w-4"}),"Results"]}),e.jsxs(Q,{value:"command",className:"flex items-center gap-2",children:[e.jsx(ue,{className:"h-4 w-4"}),"Command"]})]}),e.jsxs(J,{value:"configure",className:"space-y-6",children:[e.jsxs("div",{className:"grid gap-6 lg:grid-cols-2",children:[e.jsxs(y,{children:[e.jsxs(T,{children:[e.jsxs(A,{className:"flex items-center gap-2",children:[e.jsx(ue,{className:"h-5 w-5"}),"Target Configuration"]}),e.jsx(F,{children:"Specify targets and scan scope for vulnerability assessment"})]}),e.jsxs(w,{className:"space-y-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium",children:"Target Hosts"}),e.jsx(Le,{placeholder:"e.g., ***********, 10.0.0.0/24, example.com",value:t.targets,onChange:l=>D({targets:l.target.value}),rows:3}),e.jsx("p",{className:"text-xs text-muted-foreground",children:"IP addresses, CIDR ranges, or hostnames (one per line)"})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium",children:"Scan Configuration"}),e.jsxs(pe,{value:t.scanConfigId,onValueChange:l=>D({scanConfigId:l}),children:[e.jsx(oe,{children:e.jsx(de,{})}),e.jsx(me,{children:Object.entries(As).map(([l,h])=>e.jsx(_,{value:l,children:e.jsxs("div",{className:"flex flex-col",children:[e.jsx("span",{children:h.name}),e.jsx("span",{className:"text-xs text-muted-foreground",children:h.description})]})},l))})]}),e.jsxs("div",{className:"flex items-center gap-2 text-xs text-muted-foreground",children:[e.jsx(Ve,{className:"h-3 w-3"}),e.jsxs("span",{children:["Estimated time: ",I()]})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium",children:"Port List"}),e.jsxs(pe,{value:t.portList,onValueChange:l=>D({portList:l}),children:[e.jsx(oe,{children:e.jsx(de,{})}),e.jsx(me,{children:Object.entries(si).map(([l,h])=>e.jsx(_,{value:l,children:e.jsxs("div",{className:"flex flex-col",children:[e.jsx("span",{className:"capitalize",children:l.replace(/-/g," ")}),e.jsx("span",{className:"text-xs text-muted-foreground",children:h})]})},l))})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium",children:"Report Format"}),e.jsxs(pe,{value:t.reportFormat,onValueChange:l=>D({reportFormat:l}),children:[e.jsx(oe,{children:e.jsx(de,{})}),e.jsxs(me,{children:[e.jsx(_,{value:"html",children:"HTML Report"}),e.jsx(_,{value:"pdf",children:"PDF Report"}),e.jsx(_,{value:"xml",children:"XML Data"}),e.jsx(_,{value:"csv",children:"CSV Export"}),e.jsx(_,{value:"txt",children:"Text Summary"})]})]})]})]})]}),e.jsxs(y,{children:[e.jsxs(T,{children:[e.jsxs(A,{className:"flex items-center gap-2",children:[e.jsx(He,{className:"h-5 w-5"}),"Scan Preferences"]}),e.jsx(F,{children:"Advanced scanning options and performance tuning"})]}),e.jsxs(w,{className:"space-y-4",children:[e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium",children:"Max Concurrent Checks"}),e.jsx(P,{type:"number",min:"1",max:"10",value:t.preferences.maxChecks,onChange:l=>W({maxChecks:parseInt(l.target.value)||5})}),e.jsx("p",{className:"text-xs text-muted-foreground",children:"Number of simultaneous vulnerability checks per host"})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium",children:"Max Concurrent Hosts"}),e.jsx(P,{type:"number",min:"1",max:"100",value:t.preferences.maxHosts,onChange:l=>W({maxHosts:parseInt(l.target.value)||20})}),e.jsx("p",{className:"text-xs text-muted-foreground",children:"Number of hosts to scan simultaneously"})]})]}),e.jsxs("div",{className:"space-y-3",children:[e.jsxs("label",{className:"flex items-center space-x-2",children:[e.jsx("input",{type:"checkbox",checked:t.preferences.optimizeTest,onChange:l=>W({optimizeTest:l.target.checked}),className:"rounded border-gray-300"}),e.jsx("span",{className:"text-sm",children:"Optimize Test"})]}),e.jsxs("label",{className:"flex items-center space-x-2",children:[e.jsx("input",{type:"checkbox",checked:t.preferences.reverseLookup,onChange:l=>W({reverseLookup:l.target.checked}),className:"rounded border-gray-300"}),e.jsx("span",{className:"text-sm",children:"Reverse DNS Lookup"})]}),e.jsxs("label",{className:"flex items-center space-x-2",children:[e.jsx("input",{type:"checkbox",checked:t.preferences.dropPrivileges,onChange:l=>W({dropPrivileges:l.target.checked}),className:"rounded border-gray-300"}),e.jsx("span",{className:"text-sm",children:"Drop Privileges"})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium",children:"Source Interface (Optional)"}),e.jsx(P,{placeholder:"e.g., eth0, wlan0",value:t.preferences.sourceInterface,onChange:l=>W({sourceInterface:l.target.value})})]})]})]})]}),e.jsxs(y,{children:[e.jsxs(T,{children:[e.jsxs(A,{className:"flex items-center gap-2",children:[e.jsx(Ls,{className:"h-5 w-5"}),"Authentication (Optional)"]}),e.jsx(F,{children:"Configure authenticated scanning for deeper vulnerability assessment"})]}),e.jsxs(w,{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("input",{type:"checkbox",checked:L,onChange:l=>{i(l.target.checked),l.target.checked?D({credentials:{username:"",password:"",type:"ssh"}}):D({credentials:null})},className:"rounded border-gray-300"}),e.jsx("span",{className:"text-sm font-medium",children:"Enable Authenticated Scanning"})]}),L&&e.jsxs("div",{className:"grid gap-4 md:grid-cols-2",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium",children:"Authentication Type"}),e.jsxs(pe,{value:t.credentials?.type||"ssh",onValueChange:l=>U({type:l}),children:[e.jsx(oe,{children:e.jsx(de,{})}),e.jsxs(me,{children:[e.jsx(_,{value:"ssh",children:"SSH"}),e.jsx(_,{value:"smb",children:"SMB/CIFS"}),e.jsx(_,{value:"snmp",children:"SNMP"}),e.jsx(_,{value:"http",children:"HTTP/HTTPS"})]})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium",children:"Username"}),e.jsx(P,{placeholder:"Username",value:t.credentials?.username||"",onChange:l=>U({username:l.target.value})})]}),e.jsxs("div",{className:"space-y-2 md:col-span-2",children:[e.jsx("label",{className:"text-sm font-medium",children:"Password"}),e.jsx(P,{type:"password",placeholder:"Password",value:t.credentials?.password||"",onChange:l=>U({password:l.target.value})})]})]})]})]}),e.jsxs(y,{children:[e.jsxs(T,{children:[e.jsxs(A,{className:"flex items-center gap-2",children:[e.jsx(Ge,{className:"h-5 w-5"}),"Quick Configurations & Actions"]}),e.jsx(F,{children:"Use predefined scan configurations or start your vulnerability assessment"})]}),e.jsx(w,{children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("h4",{className:"text-sm font-medium mb-3",children:"Scan Configurations"}),e.jsx("div",{className:"flex flex-wrap gap-2",children:Object.entries(As).map(([l,h])=>e.jsxs(v,{variant:t.scanConfigId===l?"default":"outline",size:"sm",onClick:()=>S(l),className:"flex items-center gap-2",children:[e.jsx("div",{className:B("w-2 h-2 rounded-full",h.severity==="low"&&"bg-blue-500",h.severity==="medium"&&"bg-yellow-500",h.severity==="high"&&"bg-orange-500",h.severity==="very-high"&&"bg-red-500",h.severity==="extreme"&&"bg-purple-500")}),h.name]},l))})]}),e.jsxs("div",{className:"flex items-center justify-between pt-4 border-t",children:[e.jsx("div",{className:"flex gap-2",children:e.jsxs(v,{variant:"outline",size:"sm",onClick:m,children:[e.jsx(je,{className:"h-4 w-4 mr-1"}),"Copy Command"]})}),e.jsx("div",{className:"flex gap-2",children:a?e.jsxs(v,{variant:"destructive",onClick:X,children:[e.jsx(We,{className:"h-4 w-4 mr-2"}),"Stop Scan"]}):e.jsxs(v,{onClick:z,disabled:!t.targets.trim()||!N.connected,children:[e.jsx(ae,{className:"h-4 w-4 mr-2"}),"Start Vulnerability Scan"]})})]})]})})]})]}),e.jsx(J,{value:"output",className:"space-y-6",children:e.jsxs(y,{children:[e.jsx(T,{children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsxs(A,{className:"flex items-center gap-2",children:[e.jsx(Se,{className:"h-5 w-5"}),"Real-time Output"]}),e.jsx(F,{children:"Live output from the OpenVAS scan process"})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[a&&e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(Ae,{value:p,className:"w-32"}),e.jsxs("span",{className:"text-sm text-muted-foreground",children:[p,"%"]})]}),e.jsx(v,{variant:"outline",size:"sm",onClick:()=>b([]),disabled:a,children:e.jsx(qe,{className:"h-4 w-4"})})]})]})}),e.jsx(w,{children:e.jsx("div",{className:"bg-gray-900 text-gray-100 p-4 rounded-lg h-96 overflow-y-auto font-mono text-sm",children:g.length===0?e.jsx("div",{className:"text-gray-500 italic",children:a?"Waiting for output...":"No output yet. Start a scan to see results here."}):g.map((l,h)=>e.jsx("div",{className:"mb-1",children:l},h))})})]})}),e.jsxs(J,{value:"results",className:"space-y-6",children:[u&&e.jsxs(y,{children:[e.jsxs(T,{children:[e.jsxs(A,{className:"flex items-center gap-2",children:[e.jsx(ts,{className:"h-5 w-5"}),"Vulnerability Summary"]}),e.jsx(F,{children:"Overview of discovered vulnerabilities by severity"})]}),e.jsx(w,{children:e.jsx("div",{className:"grid gap-4 md:grid-cols-5",children:ti.map(l=>e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:B("text-2xl font-bold",l.color==="gray"&&"text-gray-600",l.color==="blue"&&"text-blue-600",l.color==="yellow"&&"text-yellow-600",l.color==="orange"&&"text-orange-600",l.color==="red"&&"text-red-600"),children:u[l.value]||0}),e.jsx("div",{className:"text-sm text-muted-foreground",children:l.label})]},l.value))})})]}),e.jsxs(y,{children:[e.jsx(T,{children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsxs(A,{className:"flex items-center gap-2",children:[e.jsx(xe,{className:"h-5 w-5"}),"Detailed Results"]}),e.jsx(F,{children:"Comprehensive vulnerability assessment results"})]}),e.jsxs(v,{variant:"outline",onClick:o,disabled:!j&&g.length===0,children:[e.jsx(ke,{className:"h-4 w-4 mr-2"}),"Export Results"]})]})}),e.jsx(w,{children:j?e.jsx("div",{className:"space-y-4",children:e.jsx("pre",{className:"bg-muted p-4 rounded-lg overflow-auto text-sm max-h-96",children:JSON.stringify(j,null,2)})}):e.jsxs("div",{className:"text-center py-8 text-muted-foreground",children:[e.jsx(fe,{className:"h-12 w-12 mx-auto mb-4 opacity-50"}),e.jsx("p",{children:"No vulnerability results available yet."}),e.jsx("p",{className:"text-sm",children:"Complete a scan to see detailed vulnerability assessment here."})]})})]})]}),e.jsx(J,{value:"command",className:"space-y-6",children:e.jsxs(y,{children:[e.jsxs(T,{children:[e.jsxs(A,{className:"flex items-center gap-2",children:[e.jsx(ue,{className:"h-5 w-5"}),"Generated Command"]}),e.jsx(F,{children:"The OpenVAS command that will be executed based on your configuration"})]}),e.jsx(w,{children:e.jsxs("div",{className:"space-y-4",children:[e.jsx("div",{className:"bg-muted p-4 rounded-lg",children:e.jsx("code",{className:"text-sm font-mono break-all",children:q()})}),e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("p",{className:"text-sm text-muted-foreground",children:"This command will be executed on the backend server"}),e.jsxs(v,{variant:"outline",onClick:m,children:[e.jsx(je,{className:"h-4 w-4 mr-2"}),"Copy Command"]})]})]})})]})})]})]})}const Yt={enumerate:{name:"Enumerate Shares",description:"List all available SMB shares on the target",icon:e.jsx(Fe,{className:"h-4 w-4"}),requiresAuth:!1},"list-shares":{name:"List Share Contents",description:"Browse files and directories in a specific share",icon:e.jsx(ua,{className:"h-4 w-4"}),requiresAuth:!0},"connect-share":{name:"Connect to Share",description:"Establish connection to a specific SMB share",icon:e.jsx(Oe,{className:"h-4 w-4"}),requiresAuth:!0},"download-file":{name:"Download File",description:"Download a file from the remote SMB share",icon:e.jsx(ke,{className:"h-4 w-4"}),requiresAuth:!0},"upload-file":{name:"Upload File",description:"Upload a file to the remote SMB share",icon:e.jsx(pn,{className:"h-4 w-4"}),requiresAuth:!0},"execute-command":{name:"Execute Command",description:"Execute a command on the remote system via SMB",icon:e.jsx(Se,{className:"h-4 w-4"}),requiresAuth:!0}},ai=[{name:"C$",description:"Administrative share for C: drive"},{name:"ADMIN$",description:"Administrative share for system root"},{name:"IPC$",description:"Inter-process communication share"},{name:"NETLOGON",description:"Domain controller logon scripts"},{name:"SYSVOL",description:"Domain controller shared folder"},{name:"print$",description:"Printer drivers share"},{name:"Users",description:"User profiles and home directories"},{name:"Public",description:"Public shared folder"}],ni=[{value:"SMB1",label:"SMB 1.0",description:"Legacy protocol (not recommended)"},{value:"SMB2",label:"SMB 2.0/2.1",description:"Modern protocol with better performance"},{value:"SMB3",label:"SMB 3.0+",description:"Latest protocol with encryption support"}];function Ca(){const[t,s]=x.useState({target:"",operation:"enumerate",authentication:{username:"",password:"",domain:"",useNullSession:!1,useKerberos:!1,hash:""},share:"",remotePath:"",localPath:"",command:"",options:{port:445,timeout:30,maxProtocol:"SMB3",encryption:!1,signing:!1,verbose:!1,debugLevel:0},customArgs:""}),[a,n]=x.useState(!1),[r,c]=x.useState(null),[p,d]=x.useState(0),[g,b]=x.useState([]),[j,R]=x.useState(null),[E,f]=x.useState("configure"),[L,i]=x.useState([]),{status:u}=Pe(),k=m=>{s(o=>({...o,...m}))},N=m=>{s(o=>({...o,authentication:{...o.authentication,...m}}))},D=m=>{s(o=>({...o,options:{...o.options,...m}}))},W=()=>Yt[t.operation]?.requiresAuth&&!t.authentication.useNullSession,U=()=>{let m="smbclient";switch(t.operation==="enumerate"||t.operation==="list-shares"?m+=` -L ${t.target}`:t.share?m+=` //${t.target}/${t.share}`:m+=` //${t.target}`,t.authentication.useNullSession?m+=" -N":(t.authentication.username&&(m+=` -U ${t.authentication.domain?t.authentication.domain+"\\":""}${t.authentication.username}`),t.authentication.hash&&(m+=" --pw-nt-hash")),t.options.port!==445&&(m+=` -p ${t.options.port}`),t.options.maxProtocol!=="SMB3"&&(m+=` --max-protocol=${t.options.maxProtocol}`),t.options.encryption&&(m+=" --encrypt"),t.options.signing&&(m+=" --sign"),t.options.debugLevel>0&&(m+=` -d ${t.options.debugLevel}`),t.operation){case"download-file":t.remotePath&&t.localPath&&(m+=` -c "get ${t.remotePath} ${t.localPath}"`);break;case"upload-file":t.localPath&&t.remotePath&&(m+=` -c "put ${t.localPath} ${t.remotePath}"`);break;case"execute-command":t.command&&(m+=` -c "${t.command}"`);break;case"list-shares":t.remotePath?m+=` -c "ls ${t.remotePath}"`:m+=' -c "ls"';break}return t.customArgs.trim()&&(m+=` ${t.customArgs.trim()}`),m},S=async()=>{if(!t.target.trim()){C.error("Please specify a target host");return}if(W()&&!t.authentication.username.trim()){C.error("This operation requires authentication");return}if(!u.connected){C.error("Backend not connected");return}try{n(!0),d(0),b([]),R(null),f("output");const m=await ne.executeTool("smbclient",t,o=>{c(o),d(o.progress),o.output&&b(l=>[...l,...o.output]),o.status==="completed"?(n(!1),R(o.results),t.operation==="enumerate"&&o.results?.shares&&i(o.results.shares),C.success("SMB operation completed")):o.status==="failed"&&(n(!1),C.error(`Operation failed: ${o.error}`))});c(m),C.info("SMB operation started")}catch(m){n(!1),console.error("Failed to execute SMB operation:",m),C.error("Failed to execute operation")}},I=async()=>{if(r)try{await ne.cancelExecution(r.id),n(!1),c(null),C.info("Operation cancelled")}catch(m){console.error("Failed to stop operation:",m),C.error("Failed to stop operation")}},q=async()=>{try{await navigator.clipboard.writeText(U()),C.success("Command copied to clipboard")}catch{C.error("Failed to copy command")}},z=()=>{if(!j&&g.length===0){C.error("No results to export");return}const m=j||g.join(`
`),o=new Blob([JSON.stringify(m,null,2)],{type:"application/json"}),l=URL.createObjectURL(o),h=document.createElement("a");h.href=l,h.download=`smbclient-${t.operation}-${new Date().toISOString().slice(0,19)}.json`,document.body.appendChild(h),h.click(),document.body.removeChild(h),URL.revokeObjectURL(l),C.success("Results exported")},X=m=>{k({share:m}),C.success(`Selected share: ${m}`)};return e.jsxs("div",{className:"container-desktop py-6 space-y-6",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"p-2 bg-indigo-100 dark:bg-indigo-900/20 rounded-lg",children:e.jsx(as,{className:"h-6 w-6 text-indigo-600 dark:text-indigo-400"})}),e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold",children:"SMBClient Tool"}),e.jsx("p",{className:"text-muted-foreground",children:"SMB/CIFS client for accessing Windows shares and services"})]})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx($,{variant:u.connected?"default":"destructive",children:u.connected?"Connected":"Offline"}),a&&e.jsx($,{variant:"secondary",className:"animate-pulse",children:"Executing..."})]})]}),e.jsxs(Ue,{value:E,onValueChange:f,className:"space-y-6",children:[e.jsxs(Be,{className:"grid w-full grid-cols-4",children:[e.jsxs(Q,{value:"configure",className:"flex items-center gap-2",children:[e.jsx(Y,{className:"h-4 w-4"}),"Configure"]}),e.jsxs(Q,{value:"output",className:"flex items-center gap-2",children:[e.jsx(Se,{className:"h-4 w-4"}),"Output"]}),e.jsxs(Q,{value:"results",className:"flex items-center gap-2",children:[e.jsx(xe,{className:"h-4 w-4"}),"Results"]}),e.jsxs(Q,{value:"command",className:"flex items-center gap-2",children:[e.jsx(ue,{className:"h-4 w-4"}),"Command"]})]}),e.jsxs(J,{value:"configure",className:"space-y-6",children:[e.jsxs("div",{className:"grid gap-6 lg:grid-cols-2",children:[e.jsxs(y,{children:[e.jsxs(T,{children:[e.jsxs(A,{className:"flex items-center gap-2",children:[e.jsx(ue,{className:"h-5 w-5"}),"Target & Operation"]}),e.jsx(F,{children:"Specify the target host and SMB operation to perform"})]}),e.jsxs(w,{className:"space-y-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium",children:"Target Host"}),e.jsx(P,{placeholder:"e.g., ***********00, server.domain.com",value:t.target,onChange:m=>k({target:m.target.value})}),e.jsx("p",{className:"text-xs text-muted-foreground",children:"IP address or hostname of the SMB server"})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium",children:"Operation"}),e.jsxs(pe,{value:t.operation,onValueChange:m=>k({operation:m}),children:[e.jsx(oe,{children:e.jsx(de,{})}),e.jsx(me,{children:Object.entries(Yt).map(([m,o])=>e.jsx(_,{value:m,children:e.jsxs("div",{className:"flex items-center gap-2",children:[o.icon,e.jsxs("div",{className:"flex flex-col",children:[e.jsx("span",{children:o.name}),e.jsx("span",{className:"text-xs text-muted-foreground",children:o.description})]})]})},m))})]}),W()&&e.jsxs("div",{className:"flex items-center gap-2 text-xs text-orange-600",children:[e.jsx(Ls,{className:"h-3 w-3"}),e.jsx("span",{children:"This operation requires authentication"})]})]}),["list-shares","connect-share","download-file","upload-file"].includes(t.operation)&&e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium",children:"Share Name"}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx(P,{placeholder:"e.g., C$, Users, Public",value:t.share,onChange:m=>k({share:m.target.value})}),e.jsxs(pe,{onValueChange:X,children:[e.jsx(oe,{className:"w-32",children:e.jsx(de,{placeholder:"Common"})}),e.jsx(me,{children:ai.map(m=>e.jsx(_,{value:m.name,children:e.jsxs("div",{className:"flex flex-col",children:[e.jsx("span",{children:m.name}),e.jsx("span",{className:"text-xs text-muted-foreground",children:m.description})]})},m.name))})]})]})]}),t.operation==="list-shares"&&e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium",children:"Remote Path (Optional)"}),e.jsx(P,{placeholder:"e.g., /folder, /folder/subfolder",value:t.remotePath,onChange:m=>k({remotePath:m.target.value})})]}),["download-file","upload-file"].includes(t.operation)&&e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium",children:"Remote File Path"}),e.jsx(P,{placeholder:"e.g., /folder/file.txt",value:t.remotePath,onChange:m=>k({remotePath:m.target.value})})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium",children:"Local File Path"}),e.jsx(P,{placeholder:"e.g., ./downloaded-file.txt",value:t.localPath,onChange:m=>k({localPath:m.target.value})})]})]}),t.operation==="execute-command"&&e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium",children:"Command to Execute"}),e.jsx(P,{placeholder:"e.g., dir, whoami, systeminfo",value:t.command,onChange:m=>k({command:m.target.value})})]})]})]}),e.jsxs(y,{children:[e.jsxs(T,{children:[e.jsxs(A,{className:"flex items-center gap-2",children:[e.jsx(fs,{className:"h-5 w-5"}),"Authentication"]}),e.jsx(F,{children:"Configure authentication credentials for SMB access"})]}),e.jsxs(w,{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("input",{type:"checkbox",checked:t.authentication.useNullSession,onChange:m=>N({useNullSession:m.target.checked}),className:"rounded border-gray-300"}),e.jsx("span",{className:"text-sm",children:"Use Null Session (Anonymous)"})]}),!t.authentication.useNullSession&&e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium",children:"Username"}),e.jsx(P,{placeholder:"Username",value:t.authentication.username,onChange:m=>N({username:m.target.value})})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium",children:"Password"}),e.jsx(P,{type:"password",placeholder:"Password",value:t.authentication.password,onChange:m=>N({password:m.target.value})})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium",children:"Domain (Optional)"}),e.jsx(P,{placeholder:"DOMAIN or WORKGROUP",value:t.authentication.domain,onChange:m=>N({domain:m.target.value})})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium",children:"NTLM Hash (Optional)"}),e.jsx(P,{placeholder:"LM:NTLM hash for pass-the-hash attacks",value:t.authentication.hash,onChange:m=>N({hash:m.target.value})}),e.jsx("p",{className:"text-xs text-muted-foreground",children:"Use NTLM hash instead of password for authentication"})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("input",{type:"checkbox",checked:t.authentication.useKerberos,onChange:m=>N({useKerberos:m.target.checked}),className:"rounded border-gray-300"}),e.jsx("span",{className:"text-sm",children:"Use Kerberos Authentication"})]})]})]})]})]}),e.jsxs(y,{children:[e.jsxs(T,{children:[e.jsxs(A,{className:"flex items-center gap-2",children:[e.jsx(Y,{className:"h-5 w-5"}),"Connection Options"]}),e.jsx(F,{children:"Advanced SMB connection and protocol options"})]}),e.jsxs(w,{className:"space-y-4",children:[e.jsxs("div",{className:"grid gap-4 md:grid-cols-3",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium",children:"Port"}),e.jsx(P,{type:"number",min:"1",max:"65535",value:t.options.port,onChange:m=>D({port:parseInt(m.target.value)||445})})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium",children:"Timeout (seconds)"}),e.jsx(P,{type:"number",min:"1",max:"300",value:t.options.timeout,onChange:m=>D({timeout:parseInt(m.target.value)||30})})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium",children:"Max Protocol"}),e.jsxs(pe,{value:t.options.maxProtocol,onValueChange:m=>D({maxProtocol:m}),children:[e.jsx(oe,{children:e.jsx(de,{})}),e.jsx(me,{children:ni.map(m=>e.jsx(_,{value:m.value,children:e.jsxs("div",{className:"flex flex-col",children:[e.jsx("span",{children:m.label}),e.jsx("span",{className:"text-xs text-muted-foreground",children:m.description})]})},m.value))})]})]})]}),e.jsxs("div",{className:"grid gap-3 md:grid-cols-2",children:[e.jsxs("label",{className:"flex items-center space-x-2",children:[e.jsx("input",{type:"checkbox",checked:t.options.encryption,onChange:m=>D({encryption:m.target.checked}),className:"rounded border-gray-300"}),e.jsx("span",{className:"text-sm",children:"Force Encryption"})]}),e.jsxs("label",{className:"flex items-center space-x-2",children:[e.jsx("input",{type:"checkbox",checked:t.options.signing,onChange:m=>D({signing:m.target.checked}),className:"rounded border-gray-300"}),e.jsx("span",{className:"text-sm",children:"Force Signing"})]}),e.jsxs("label",{className:"flex items-center space-x-2",children:[e.jsx("input",{type:"checkbox",checked:t.options.verbose,onChange:m=>D({verbose:m.target.checked}),className:"rounded border-gray-300"}),e.jsx("span",{className:"text-sm",children:"Verbose Output"})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium",children:"Debug Level"}),e.jsxs(pe,{value:t.options.debugLevel.toString(),onValueChange:m=>D({debugLevel:parseInt(m)}),children:[e.jsx(oe,{children:e.jsx(de,{})}),e.jsxs(me,{children:[e.jsx(_,{value:"0",children:"0 - No Debug"}),e.jsx(_,{value:"1",children:"1 - Basic"}),e.jsx(_,{value:"2",children:"2 - Detailed"}),e.jsx(_,{value:"3",children:"3 - Verbose"}),e.jsx(_,{value:"10",children:"10 - Maximum"})]})]})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium",children:"Custom Arguments"}),e.jsx(P,{placeholder:"Additional smbclient arguments",value:t.customArgs,onChange:m=>k({customArgs:m.target.value})})]})]})]}),e.jsxs(y,{children:[e.jsxs(T,{children:[e.jsxs(A,{className:"flex items-center gap-2",children:[e.jsx(ve,{className:"h-5 w-5"}),"Actions"]}),e.jsx(F,{children:"Execute SMB operations and manage results"})]}),e.jsx(w,{children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("div",{className:"flex gap-2",children:e.jsxs(v,{variant:"outline",size:"sm",onClick:q,children:[e.jsx(je,{className:"h-4 w-4 mr-1"}),"Copy Command"]})}),e.jsx("div",{className:"flex gap-2",children:a?e.jsxs(v,{variant:"destructive",onClick:I,children:[e.jsx(We,{className:"h-4 w-4 mr-2"}),"Stop Operation"]}):e.jsxs(v,{onClick:S,disabled:!t.target.trim()||!u.connected,children:[e.jsx(ae,{className:"h-4 w-4 mr-2"}),"Execute Operation"]})})]})})]})]}),e.jsx(J,{value:"output",className:"space-y-6",children:e.jsxs(y,{children:[e.jsx(T,{children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsxs(A,{className:"flex items-center gap-2",children:[e.jsx(Se,{className:"h-5 w-5"}),"Real-time Output"]}),e.jsx(F,{children:"Live output from the SMBClient operation"})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[a&&e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(Ae,{value:p,className:"w-32"}),e.jsxs("span",{className:"text-sm text-muted-foreground",children:[p,"%"]})]}),e.jsx(v,{variant:"outline",size:"sm",onClick:()=>b([]),disabled:a,children:e.jsx(qe,{className:"h-4 w-4"})})]})]})}),e.jsx(w,{children:e.jsx("div",{className:"bg-gray-900 text-gray-100 p-4 rounded-lg h-96 overflow-y-auto font-mono text-sm",children:g.length===0?e.jsx("div",{className:"text-gray-500 italic",children:a?"Waiting for output...":"No output yet. Execute an operation to see results here."}):g.map((m,o)=>e.jsx("div",{className:"mb-1",children:m},o))})})]})}),e.jsxs(J,{value:"results",className:"space-y-6",children:[L.length>0&&e.jsxs(y,{children:[e.jsxs(T,{children:[e.jsxs(A,{className:"flex items-center gap-2",children:[e.jsx(xn,{className:"h-5 w-5"}),"Discovered Shares"]}),e.jsx(F,{children:"SMB shares found on the target system"})]}),e.jsx(w,{children:e.jsx("div",{className:"grid gap-2",children:L.map((m,o)=>e.jsxs("div",{className:"flex items-center justify-between p-3 border rounded-lg",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx(ua,{className:"h-4 w-4 text-blue-500"}),e.jsxs("div",{children:[e.jsx("div",{className:"font-medium",children:m.name}),e.jsx("div",{className:"text-sm text-muted-foreground",children:m.description})]})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx($,{variant:m.type==="Disk"?"default":"secondary",children:m.type}),e.jsx(v,{size:"sm",variant:"outline",onClick:()=>X(m.name),children:"Select"})]})]},o))})})]}),e.jsxs(y,{children:[e.jsx(T,{children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsxs(A,{className:"flex items-center gap-2",children:[e.jsx(xe,{className:"h-5 w-5"}),"Operation Results"]}),e.jsx(F,{children:"Detailed results from the SMB operation"})]}),e.jsxs(v,{variant:"outline",onClick:z,disabled:!j&&g.length===0,children:[e.jsx(ke,{className:"h-4 w-4 mr-2"}),"Export Results"]})]})}),e.jsx(w,{children:j?e.jsx("div",{className:"space-y-4",children:e.jsx("pre",{className:"bg-muted p-4 rounded-lg overflow-auto text-sm max-h-96",children:JSON.stringify(j,null,2)})}):e.jsxs("div",{className:"text-center py-8 text-muted-foreground",children:[e.jsx(as,{className:"h-12 w-12 mx-auto mb-4 opacity-50"}),e.jsx("p",{children:"No results available yet."}),e.jsx("p",{className:"text-sm",children:"Execute an SMB operation to see results here."})]})})]})]}),e.jsx(J,{value:"command",className:"space-y-6",children:e.jsxs(y,{children:[e.jsxs(T,{children:[e.jsxs(A,{className:"flex items-center gap-2",children:[e.jsx(ue,{className:"h-5 w-5"}),"Generated Command"]}),e.jsx(F,{children:"The SMBClient command that will be executed based on your configuration"})]}),e.jsx(w,{children:e.jsxs("div",{className:"space-y-4",children:[e.jsx("div",{className:"bg-muted p-4 rounded-lg",children:e.jsx("code",{className:"text-sm font-mono break-all",children:U()})}),e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("p",{className:"text-sm text-muted-foreground",children:"This command will be executed on the backend server"}),e.jsxs(v,{variant:"outline",onClick:q,children:[e.jsx(je,{className:"h-4 w-4 mr-2"}),"Copy Command"]})]})]})})]})})]})]})}const ri={shares:{name:"Share Enumeration",description:"Enumerate SMB shares and their permissions",icon:e.jsx(da,{className:"h-4 w-4"}),risk:"low"},users:{name:"User Enumeration",description:"Discover user accounts and detailed information",icon:e.jsx(jn,{className:"h-4 w-4"}),risk:"medium"},groups:{name:"Group Enumeration",description:"Enumerate security groups and memberships",icon:e.jsx(gn,{className:"h-4 w-4"}),risk:"medium"},policies:{name:"Password Policies",description:"Extract password and account policies",icon:e.jsx(fe,{className:"h-4 w-4"}),risk:"low"},printers:{name:"Printer Enumeration",description:"Discover shared printers and print queues",icon:e.jsx(ve,{className:"h-4 w-4"}),risk:"low"},machines:{name:"Machine Accounts",description:"Enumerate domain machine accounts",icon:e.jsx(as,{className:"h-4 w-4"}),risk:"medium"},osinfo:{name:"OS Information",description:"Gather operating system and version details",icon:e.jsx(fn,{className:"h-4 w-4"}),risk:"low"},ridCycling:{name:"RID Cycling",description:"Bruteforce RIDs to discover users and groups",icon:e.jsx(Fe,{className:"h-4 w-4"}),risk:"high"}},Xt={"users-standard":{start:1e3,end:1100,description:"Standard user accounts (1000-1100)"},"users-extended":{start:1e3,end:2e3,description:"Extended user range (1000-2000)"},"builtin-groups":{start:500,end:600,description:"Built-in groups (500-600)"},"domain-groups":{start:1100,end:1200,description:"Domain groups (1100-1200)"},comprehensive:{start:500,end:5e3,description:"Comprehensive scan (500-5000)"},"fast-scan":{start:1e3,end:1050,description:"Quick user scan (1000-1050)"}},ii=t=>{switch(t){case"low":return"text-green-600 bg-green-100 dark:bg-green-900/20";case"medium":return"text-yellow-600 bg-yellow-100 dark:bg-yellow-900/20";case"high":return"text-red-600 bg-red-100 dark:bg-red-900/20";default:return"text-gray-600 bg-gray-100 dark:bg-gray-900/20"}};function Ta(){const[t,s]=x.useState({target:"",modules:{shares:!0,users:!0,groups:!0,policies:!0,printers:!1,machines:!1,osinfo:!0,ridCycling:!1},authentication:{username:"",password:"",domain:"",useNullSession:!0,hash:""},ridCycling:{enabled:!1,startRid:1e3,endRid:1100,step:1},options:{timeout:5,threads:4,verbose:!1,jsonOutput:!0,fullOutput:!1,keepLogging:!1},customArgs:""}),[a,n]=x.useState(!1),[r,c]=x.useState(null),[p,d]=x.useState(0),[g,b]=x.useState([]),[j,R]=x.useState(null),[E,f]=x.useState("configure"),[L,i]=x.useState(null),{status:u}=Pe(),k=h=>{s(O=>({...O,...h}))},N=h=>{s(O=>({...O,modules:{...O.modules,...h}}))},D=h=>{s(O=>({...O,authentication:{...O.authentication,...h}}))},W=h=>{s(O=>({...O,ridCycling:{...O.ridCycling,...h}})),h.enabled&&N({ridCycling:!0})},U=h=>{s(O=>({...O,options:{...O.options,...h}}))},S=h=>{const O=Xt[h];O&&(W({startRid:O.start,endRid:O.end,enabled:!0}),C.success(`Applied RID preset: ${O.description}`))},I=h=>{switch(h){case"basic":N({shares:!0,users:!0,groups:!1,policies:!0,printers:!1,machines:!1,osinfo:!0,ridCycling:!1});break;case"comprehensive":N({shares:!0,users:!0,groups:!0,policies:!0,printers:!0,machines:!0,osinfo:!0,ridCycling:!1});break;case"stealth":N({shares:!0,users:!1,groups:!1,policies:!1,printers:!1,machines:!1,osinfo:!0,ridCycling:!1}),U({threads:1,timeout:10});break;case"aggressive":N({shares:!0,users:!0,groups:!0,policies:!0,printers:!0,machines:!0,osinfo:!0,ridCycling:!0}),W({enabled:!0,startRid:500,endRid:2e3});break}C.success(`Applied ${h} configuration`)},q=()=>{let h="enum4linux-ng";return Object.entries(t.modules).filter(([M,H])=>H).map(([M])=>M).length>0&&(h+=" -A"),!t.authentication.useNullSession&&t.authentication.username&&(h+=` -u ${t.authentication.username}`,t.authentication.password&&(h+=` -p ${t.authentication.password}`),t.authentication.domain&&(h+=` -d ${t.authentication.domain}`),t.authentication.hash&&(h+=` -H ${t.authentication.hash}`)),t.modules.ridCycling&&t.ridCycling.enabled&&(h+=` -r ${t.ridCycling.startRid}-${t.ridCycling.endRid}`),h+=` -t ${t.options.timeout}`,h+=` -th ${t.options.threads}`,t.options.verbose&&(h+=" -v"),t.options.jsonOutput&&(h+=" -oJ enum4linux-ng.json"),t.options.fullOutput&&(h+=" -oA enum4linux-ng"),t.customArgs.trim()&&(h+=` ${t.customArgs.trim()}`),h+=` ${t.target}`,h},z=async()=>{if(!t.target.trim()){C.error("Please specify a target host");return}if(Object.values(t.modules).filter(Boolean).length===0){C.error("Please select at least one enumeration module");return}if(!u.connected){C.error("Backend not connected");return}try{n(!0),d(0),b([]),R(null),i(null),f("output"),C.info("Note: Tool functional but may show detection issues in backend registry",{duration:5e3});const O=await ne.executeTool("enum4linux-ng",t,M=>{if(c(M),d(M.progress),M.output&&b(H=>[...H,...M.output]),M.status==="completed"){if(n(!1),R(M.results),M.results){const H={users:M.results.users?.length||0,groups:M.results.groups?.length||0,shares:M.results.shares?.length||0,policies:M.results.policies?1:0};i(H)}C.success("enum4linux-ng enumeration completed")}else M.status==="failed"&&(n(!1),C.error(`Enumeration failed: ${M.error}`))});c(O),C.info("enum4linux-ng enumeration started")}catch(O){n(!1),console.error("Failed to start enum4linux-ng:",O),C.error("Failed to start enumeration")}},X=async()=>{if(r)try{await ne.cancelExecution(r.id),n(!1),c(null),C.info("Enumeration cancelled")}catch(h){console.error("Failed to stop enumeration:",h),C.error("Failed to stop enumeration")}},m=async()=>{try{await navigator.clipboard.writeText(q()),C.success("Command copied to clipboard")}catch{C.error("Failed to copy command")}},o=()=>{if(!j&&g.length===0){C.error("No results to export");return}const h=j||g.join(`
`),O=new Blob([JSON.stringify(h,null,2)],{type:"application/json"}),M=URL.createObjectURL(O),H=document.createElement("a");H.href=M,H.download=`enum4linux-ng-${t.target}-${new Date().toISOString().slice(0,19)}.json`,document.body.appendChild(H),H.click(),document.body.removeChild(H),URL.revokeObjectURL(M),C.success("Results exported")},l=()=>{const O=Object.values(t.modules).filter(Boolean).length*30,M=t.modules.ridCycling?(t.ridCycling.endRid-t.ridCycling.startRid)/100*10:0,H=O+M;return H<60?`~${H}s`:H<3600?`~${Math.ceil(H/60)}m`:`~${Math.ceil(H/3600)}h`};return e.jsxs("div",{className:"container-desktop py-6 space-y-6",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"p-2 bg-purple-100 dark:bg-purple-900/20 rounded-lg",children:e.jsx(Fe,{className:"h-6 w-6 text-purple-600 dark:text-purple-400"})}),e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold",children:"enum4linux-ng"}),e.jsx("p",{className:"text-muted-foreground",children:"Modern SMB enumeration tool for comprehensive Windows network analysis"})]})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx($,{variant:u.connected?"default":"destructive",children:u.connected?"Connected":"Offline"}),e.jsxs($,{variant:"secondary",className:"flex items-center gap-1",children:[e.jsx(ee,{className:"h-3 w-3"}),"Detection Issues"]}),a&&e.jsx($,{variant:"secondary",className:"animate-pulse",children:"Enumerating..."})]})]}),e.jsx(y,{className:"border-yellow-200 bg-yellow-50 dark:border-yellow-800 dark:bg-yellow-900/10",children:e.jsx(w,{className:"pt-6",children:e.jsxs("div",{className:"flex items-start gap-3",children:[e.jsx(ee,{className:"h-5 w-5 text-yellow-600 dark:text-yellow-400 mt-0.5"}),e.jsxs("div",{className:"space-y-1",children:[e.jsx("p",{className:"text-sm font-medium text-yellow-800 dark:text-yellow-200",children:"Tool Status: Functional with Detection Issues"}),e.jsx("p",{className:"text-xs text-yellow-700 dark:text-yellow-300",children:"enum4linux-ng is fully operational but may show platform detection issues in the backend registry. All enumeration capabilities work correctly despite this cosmetic issue."})]})]})})}),e.jsxs(Ue,{value:E,onValueChange:f,className:"space-y-6",children:[e.jsxs(Be,{className:"grid w-full grid-cols-4",children:[e.jsxs(Q,{value:"configure",className:"flex items-center gap-2",children:[e.jsx(Y,{className:"h-4 w-4"}),"Configure"]}),e.jsxs(Q,{value:"output",className:"flex items-center gap-2",children:[e.jsx(Se,{className:"h-4 w-4"}),"Output"]}),e.jsxs(Q,{value:"results",className:"flex items-center gap-2",children:[e.jsx(xe,{className:"h-4 w-4"}),"Results"]}),e.jsxs(Q,{value:"command",className:"flex items-center gap-2",children:[e.jsx(ue,{className:"h-4 w-4"}),"Command"]})]}),e.jsxs(J,{value:"configure",className:"space-y-6",children:[e.jsxs(y,{children:[e.jsxs(T,{children:[e.jsxs(A,{className:"flex items-center gap-2",children:[e.jsx(Ge,{className:"h-5 w-5"}),"Quick Configuration Presets"]}),e.jsx(F,{children:"Use predefined configurations for common enumeration scenarios"})]}),e.jsx(w,{children:e.jsxs("div",{className:"grid gap-3 md:grid-cols-4",children:[e.jsxs(v,{variant:"outline",onClick:()=>I("basic"),className:"flex flex-col h-auto p-4 text-left",children:[e.jsx("div",{className:"font-medium text-sm",children:"Basic Scan"}),e.jsx("div",{className:"text-xs text-muted-foreground",children:"Shares, users, policies, OS info"})]}),e.jsxs(v,{variant:"outline",onClick:()=>I("comprehensive"),className:"flex flex-col h-auto p-4 text-left",children:[e.jsx("div",{className:"font-medium text-sm",children:"Comprehensive"}),e.jsx("div",{className:"text-xs text-muted-foreground",children:"All modules except RID cycling"})]}),e.jsxs(v,{variant:"outline",onClick:()=>I("stealth"),className:"flex flex-col h-auto p-4 text-left",children:[e.jsx("div",{className:"font-medium text-sm",children:"Stealth Mode"}),e.jsx("div",{className:"text-xs text-muted-foreground",children:"Minimal detection footprint"})]}),e.jsxs(v,{variant:"outline",onClick:()=>I("aggressive"),className:"flex flex-col h-auto p-4 text-left",children:[e.jsx("div",{className:"font-medium text-sm",children:"Aggressive"}),e.jsx("div",{className:"text-xs text-muted-foreground",children:"All modules including RID cycling"})]})]})})]}),e.jsxs("div",{className:"grid gap-6 lg:grid-cols-2",children:[e.jsxs(y,{children:[e.jsxs(T,{children:[e.jsxs(A,{className:"flex items-center gap-2",children:[e.jsx(ue,{className:"h-5 w-5"}),"Target Configuration"]}),e.jsx(F,{children:"Specify the target host for SMB enumeration"})]}),e.jsxs(w,{className:"space-y-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium",children:"Target Host"}),e.jsx(P,{placeholder:"e.g., ***********00, dc01.domain.local",value:t.target,onChange:h=>k({target:h.target.value})}),e.jsx("p",{className:"text-xs text-muted-foreground",children:"IP address or hostname of the Windows target"})]}),e.jsxs("div",{className:"flex items-center gap-2 p-3 bg-muted rounded-lg",children:[e.jsx(Ve,{className:"h-4 w-4 text-muted-foreground"}),e.jsxs("span",{className:"text-sm",children:["Estimated enumeration time: ",e.jsx("strong",{children:l()})]})]})]})]}),e.jsxs(y,{children:[e.jsxs(T,{children:[e.jsxs(A,{className:"flex items-center gap-2",children:[e.jsx(Ls,{className:"h-5 w-5"}),"Authentication"]}),e.jsx(F,{children:"Configure authentication credentials for authenticated enumeration"})]}),e.jsxs(w,{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("input",{type:"checkbox",checked:t.authentication.useNullSession,onChange:h=>D({useNullSession:h.target.checked}),className:"rounded border-gray-300"}),e.jsx("span",{className:"text-sm",children:"Use Null Session (Anonymous)"})]}),!t.authentication.useNullSession&&e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium",children:"Username"}),e.jsx(P,{placeholder:"Username",value:t.authentication.username,onChange:h=>D({username:h.target.value})})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium",children:"Password"}),e.jsx(P,{type:"password",placeholder:"Password",value:t.authentication.password,onChange:h=>D({password:h.target.value})})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium",children:"Domain (Optional)"}),e.jsx(P,{placeholder:"DOMAIN or WORKGROUP",value:t.authentication.domain,onChange:h=>D({domain:h.target.value})})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium",children:"NTLM Hash (Optional)"}),e.jsx(P,{placeholder:"NTLM hash for pass-the-hash",value:t.authentication.hash,onChange:h=>D({hash:h.target.value})})]})]})]})]})]}),e.jsxs(y,{children:[e.jsxs(T,{children:[e.jsxs(A,{className:"flex items-center gap-2",children:[e.jsx(He,{className:"h-5 w-5"}),"Enumeration Modules"]}),e.jsx(F,{children:"Select which information types to enumerate from the target"})]}),e.jsx(w,{children:e.jsx("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-4",children:Object.entries(ri).map(([h,O])=>e.jsx("div",{className:B("p-4 border rounded-lg cursor-pointer transition-all",t.modules[h]?"border-primary bg-primary/5":"border-border hover:border-primary/50"),onClick:()=>N({[h]:!t.modules[h]}),children:e.jsxs("div",{className:"flex items-start gap-3",children:[e.jsx("div",{className:B("p-2 rounded-md",ii(O.risk)),children:O.icon}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsxs("div",{className:"flex items-center gap-2 mb-1",children:[e.jsx("span",{className:"text-sm font-medium",children:O.name}),e.jsx($,{variant:"outline",className:"text-xs",children:O.risk})]}),e.jsx("p",{className:"text-xs text-muted-foreground",children:O.description}),e.jsx("div",{className:"mt-2",children:e.jsx("input",{type:"checkbox",checked:t.modules[h],onChange:M=>N({[h]:M.target.checked}),className:"rounded border-gray-300"})})]})]})},h))})})]}),t.modules.ridCycling&&e.jsxs(y,{children:[e.jsxs(T,{children:[e.jsxs(A,{className:"flex items-center gap-2",children:[e.jsx(Fe,{className:"h-5 w-5"}),"RID Cycling Configuration"]}),e.jsx(F,{children:"Configure RID bruteforcing to discover users and groups"})]}),e.jsxs(w,{className:"space-y-4",children:[e.jsxs("div",{className:"grid gap-4 md:grid-cols-3",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium",children:"Start RID"}),e.jsx(P,{type:"number",min:"500",max:"10000",value:t.ridCycling.startRid,onChange:h=>W({startRid:parseInt(h.target.value)||1e3})})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium",children:"End RID"}),e.jsx(P,{type:"number",min:"500",max:"10000",value:t.ridCycling.endRid,onChange:h=>W({endRid:parseInt(h.target.value)||1100})})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium",children:"Step Size"}),e.jsx(P,{type:"number",min:"1",max:"10",value:t.ridCycling.step,onChange:h=>W({step:parseInt(h.target.value)||1})})]})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"text-sm font-medium mb-3",children:"RID Range Presets"}),e.jsx("div",{className:"flex flex-wrap gap-2",children:Object.entries(Xt).map(([h,O])=>e.jsx(v,{variant:"outline",size:"sm",onClick:()=>S(h),children:O.description},h))})]}),e.jsx("div",{className:"p-3 bg-orange-50 dark:bg-orange-900/10 rounded-lg border border-orange-200 dark:border-orange-800",children:e.jsxs("div",{className:"flex items-start gap-2",children:[e.jsx(ee,{className:"h-4 w-4 text-orange-600 dark:text-orange-400 mt-0.5"}),e.jsxs("div",{className:"text-sm text-orange-700 dark:text-orange-300",children:[e.jsx("strong",{children:"Warning:"})," RID cycling can be detected by security monitoring systems. Use with caution in production environments."]})]})})]})]}),e.jsxs(y,{children:[e.jsxs(T,{children:[e.jsxs(A,{className:"flex items-center gap-2",children:[e.jsx(Y,{className:"h-5 w-5"}),"Enumeration Options"]}),e.jsx(F,{children:"Advanced options for performance tuning and output formatting"})]}),e.jsxs(w,{className:"space-y-4",children:[e.jsxs("div",{className:"grid gap-4 md:grid-cols-3",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium",children:"Timeout (seconds)"}),e.jsx(P,{type:"number",min:"1",max:"60",value:t.options.timeout,onChange:h=>U({timeout:parseInt(h.target.value)||5})})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium",children:"Threads"}),e.jsx(P,{type:"number",min:"1",max:"20",value:t.options.threads,onChange:h=>U({threads:parseInt(h.target.value)||4})})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium",children:"Custom Arguments"}),e.jsx(P,{placeholder:"Additional arguments",value:t.customArgs,onChange:h=>k({customArgs:h.target.value})})]})]}),e.jsxs("div",{className:"grid gap-3 md:grid-cols-2",children:[e.jsxs("label",{className:"flex items-center space-x-2",children:[e.jsx("input",{type:"checkbox",checked:t.options.verbose,onChange:h=>U({verbose:h.target.checked}),className:"rounded border-gray-300"}),e.jsx("span",{className:"text-sm",children:"Verbose Output"})]}),e.jsxs("label",{className:"flex items-center space-x-2",children:[e.jsx("input",{type:"checkbox",checked:t.options.jsonOutput,onChange:h=>U({jsonOutput:h.target.checked}),className:"rounded border-gray-300"}),e.jsx("span",{className:"text-sm",children:"JSON Output"})]}),e.jsxs("label",{className:"flex items-center space-x-2",children:[e.jsx("input",{type:"checkbox",checked:t.options.fullOutput,onChange:h=>U({fullOutput:h.target.checked}),className:"rounded border-gray-300"}),e.jsx("span",{className:"text-sm",children:"Full Output Files"})]}),e.jsxs("label",{className:"flex items-center space-x-2",children:[e.jsx("input",{type:"checkbox",checked:t.options.keepLogging,onChange:h=>U({keepLogging:h.target.checked}),className:"rounded border-gray-300"}),e.jsx("span",{className:"text-sm",children:"Keep Detailed Logs"})]})]})]})]}),e.jsxs(y,{children:[e.jsxs(T,{children:[e.jsxs(A,{className:"flex items-center gap-2",children:[e.jsx(ve,{className:"h-5 w-5"}),"Start Enumeration"]}),e.jsx(F,{children:"Execute the enumeration with your configured parameters"})]}),e.jsx(w,{children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("div",{className:"flex gap-2",children:e.jsxs(v,{variant:"outline",size:"sm",onClick:m,children:[e.jsx(je,{className:"h-4 w-4 mr-1"}),"Copy Command"]})}),e.jsx("div",{className:"flex gap-2",children:a?e.jsxs(v,{variant:"destructive",onClick:X,children:[e.jsx(We,{className:"h-4 w-4 mr-2"}),"Stop Enumeration"]}):e.jsxs(v,{onClick:z,disabled:!t.target.trim()||!u.connected,children:[e.jsx(ae,{className:"h-4 w-4 mr-2"}),"Start Enumeration"]})})]})})]})]}),e.jsx(J,{value:"output",className:"space-y-6",children:e.jsxs(y,{children:[e.jsx(T,{children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsxs(A,{className:"flex items-center gap-2",children:[e.jsx(Se,{className:"h-5 w-5"}),"Real-time Output"]}),e.jsx(F,{children:"Live output from the enum4linux-ng enumeration process"})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[a&&e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(Ae,{value:p,className:"w-32"}),e.jsxs("span",{className:"text-sm text-muted-foreground",children:[p,"%"]})]}),e.jsx(v,{variant:"outline",size:"sm",onClick:()=>b([]),disabled:a,children:e.jsx(qe,{className:"h-4 w-4"})})]})]})}),e.jsx(w,{children:e.jsx("div",{className:"bg-gray-900 text-gray-100 p-4 rounded-lg h-96 overflow-y-auto font-mono text-sm",children:g.length===0?e.jsx("div",{className:"text-gray-500 italic",children:a?"Waiting for output...":"No output yet. Start enumeration to see results here."}):g.map((h,O)=>e.jsx("div",{className:"mb-1",children:h},O))})})]})}),e.jsxs(J,{value:"results",className:"space-y-6",children:[L&&e.jsxs(y,{children:[e.jsxs(T,{children:[e.jsxs(A,{className:"flex items-center gap-2",children:[e.jsx(ys,{className:"h-5 w-5"}),"Enumeration Summary"]}),e.jsx(F,{children:"Overview of discovered information from the target"})]}),e.jsx(w,{children:e.jsxs("div",{className:"grid gap-4 md:grid-cols-4",children:[e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-blue-600",children:L.users}),e.jsx("div",{className:"text-sm text-muted-foreground",children:"Users"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-green-600",children:L.groups}),e.jsx("div",{className:"text-sm text-muted-foreground",children:"Groups"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-purple-600",children:L.shares}),e.jsx("div",{className:"text-sm text-muted-foreground",children:"Shares"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-orange-600",children:L.policies}),e.jsx("div",{className:"text-sm text-muted-foreground",children:"Policies"})]})]})})]}),e.jsxs(y,{children:[e.jsx(T,{children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsxs(A,{className:"flex items-center gap-2",children:[e.jsx(xe,{className:"h-5 w-5"}),"Detailed Results"]}),e.jsx(F,{children:"Comprehensive enumeration results and discovered information"})]}),e.jsxs(v,{variant:"outline",onClick:o,disabled:!j&&g.length===0,children:[e.jsx(ke,{className:"h-4 w-4 mr-2"}),"Export Results"]})]})}),e.jsx(w,{children:j?e.jsx("div",{className:"space-y-4",children:e.jsx("pre",{className:"bg-muted p-4 rounded-lg overflow-auto text-sm max-h-96",children:JSON.stringify(j,null,2)})}):e.jsxs("div",{className:"text-center py-8 text-muted-foreground",children:[e.jsx(Fe,{className:"h-12 w-12 mx-auto mb-4 opacity-50"}),e.jsx("p",{children:"No enumeration results available yet."}),e.jsx("p",{className:"text-sm",children:"Complete an enumeration to see detailed results here."})]})})]})]}),e.jsx(J,{value:"command",className:"space-y-6",children:e.jsxs(y,{children:[e.jsxs(T,{children:[e.jsxs(A,{className:"flex items-center gap-2",children:[e.jsx(ue,{className:"h-5 w-5"}),"Generated Command"]}),e.jsx(F,{children:"The enum4linux-ng command that will be executed based on your configuration"})]}),e.jsx(w,{children:e.jsxs("div",{className:"space-y-4",children:[e.jsx("div",{className:"bg-muted p-4 rounded-lg",children:e.jsx("code",{className:"text-sm font-mono break-all",children:q()})}),e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("p",{className:"text-sm text-muted-foreground",children:"This command will be executed on the backend server"}),e.jsxs(v,{variant:"outline",onClick:m,children:[e.jsx(je,{className:"h-4 w-4 mr-2"}),"Copy Command"]})]})]})})]})})]})]})}const li=()=>{const[t,s]=K.useState("scanme.nmap.org"),[a,n]=K.useState(!1),[r,c]=K.useState([]),[p,d]=K.useState([]),g=async E=>{const f=`exec_${Date.now()}`,L={id:f,tool:"nmap",target:E,status:"running",output:[],startTime:new Date};c(u=>[L,...u]),n(!0),d([]);const i=[`Starting Nmap 7.94 ( https://nmap.org ) at ${new Date().toISOString()}`,`Nmap scan report for ${E}`,"Host is up (0.12s latency).","Not shown: 996 closed ports","PORT     STATE SERVICE","22/tcp   open  ssh","80/tcp   open  http","443/tcp  open  https","9929/tcp open  nping-echo","","Nmap done: 1 IP address (1 host up) scanned in 2.45 seconds"];for(let u=0;u<i.length;u++){await new Promise(N=>setTimeout(N,300));const k=i[u];d(N=>[...N,k]),c(N=>N.map(D=>D.id===f?{...D,output:[...D.output,k]}:D))}c(u=>u.map(k=>k.id===f?{...k,status:"completed",endTime:new Date}:k)),n(!1)},b=async()=>{if(!t.trim()){alert("Please enter a target to scan");return}console.log("🔧 Starting Nmap scan for:",t);try{const f=await(await fetch("http://************:8090/api/tools/nmap/scan",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({target:t,timeout:60,threads:1,output_format:"json",options:{port_range:"80,443,8080,8443",scan_type:"tcp"}})})).json();f.success?(console.log("✅ Real backend execution successful"),await g(t)):(console.log("⚠️ Backend issue, using simulation:",f.error),await g(t))}catch(E){console.log("⚠️ Backend unavailable, using simulation:",E),await g(t)}},j=()=>{n(!1),console.log("🛑 Scan stopped")},R=()=>{const E=p.join(`
`);navigator.clipboard.writeText(E),console.log("📋 Output copied to clipboard")};return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h2",{className:"text-2xl font-bold",children:"NexusScan Security Tools"}),e.jsx("p",{className:"text-muted-foreground",children:"Professional penetration testing tools with real-time execution"})]}),e.jsxs("div",{className:"flex gap-2",children:[e.jsxs($,{variant:"outline",className:"bg-blue-500/10 text-blue-500 border-blue-500",children:[e.jsx(be,{className:"h-3 w-3 mr-1"}),"Backend Connected"]}),e.jsxs($,{variant:"outline",className:"bg-green-500/10 text-green-500 border-green-500",children:[e.jsx(be,{className:"h-3 w-3 mr-1"}),"Tools Ready"]})]})]}),e.jsxs(y,{children:[e.jsxs(T,{children:[e.jsx(A,{children:"Nmap Network Scanner"}),e.jsx(F,{children:"Port scanning and service detection"})]}),e.jsxs(w,{className:"space-y-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx(Ne,{htmlFor:"target",children:"Target Host/IP"}),e.jsx(P,{id:"target",value:t,onChange:E=>s(E.target.value),placeholder:"e.g., scanme.nmap.org, ***********",disabled:a})]}),e.jsxs("div",{className:"flex gap-2",children:[a?e.jsxs(v,{onClick:j,variant:"destructive",children:[e.jsx(We,{className:"h-4 w-4 mr-2"}),"Stop Scan"]}):e.jsxs(v,{onClick:b,children:[e.jsx(ae,{className:"h-4 w-4 mr-2"}),"Start Nmap Scan"]}),e.jsxs(v,{variant:"outline",onClick:R,disabled:p.length===0,children:[e.jsx(je,{className:"h-4 w-4 mr-2"}),"Copy Output"]})]})]})]}),p.length>0&&e.jsxs(y,{children:[e.jsx(T,{children:e.jsx(A,{children:"Live Output"})}),e.jsx(w,{children:e.jsx(Le,{value:p.join(`
`),readOnly:!0,className:"font-mono text-sm min-h-[200px] bg-black text-green-400"})})]}),r.length>0&&e.jsxs(y,{children:[e.jsx(T,{children:e.jsx(A,{children:"Execution History"})}),e.jsx(w,{children:e.jsx("div",{className:"space-y-2",children:r.map(E=>e.jsxs("div",{className:"flex items-center justify-between p-3 border rounded",children:[e.jsxs("div",{children:[e.jsxs("div",{className:"font-medium",children:[E.tool," → ",E.target]}),e.jsxs("div",{className:"text-sm text-muted-foreground",children:[E.startTime.toLocaleTimeString(),E.endTime&&` - ${E.endTime.toLocaleTimeString()}`]})]}),e.jsx($,{variant:E.status==="completed"?"default":E.status==="running"?"secondary":"destructive",children:E.status})]},E.id))})})]})]})},Zt=[{id:"nmap",name:"Nmap",description:"Network discovery and port scanning with comprehensive OS and service detection",icon:e.jsx(Oe,{className:"h-6 w-6"}),features:["Port scanning (TCP/UDP/SYN)","OS fingerprinting","Service version detection","NSE scripting engine","Timing templates","Output formats (XML, JSON, Grepable)"],useCases:["Network discovery and mapping","Port and service enumeration","Operating system detection","Vulnerability scanning with scripts","Network inventory and monitoring"],component:ya,status:"available"},{id:"masscan",name:"Masscan",description:"High-speed port scanner capable of scanning the entire Internet",icon:e.jsx(Ge,{className:"h-6 w-6"}),features:["Asynchronous transmission","Custom packet rate control","Multiple output formats","IPv4 and IPv6 support","Banner grabbing","Large-scale scanning"],useCases:["Internet-wide port scans","Large network reconnaissance","High-speed service discovery","Network security assessments","Bulk vulnerability scanning"],component:wa,status:"available"},{id:"zmap",name:"Zmap",description:"Internet-wide network scanner for research and security assessment",icon:e.jsx(bs,{className:"h-6 w-6"}),features:["Single-port scanning","Stateless scanning","High-speed transmission","Research-oriented","Geographic mapping","Statistical analysis"],useCases:["Internet-wide surveys","Security research","Protocol analysis","Geographic distribution studies","Large-scale vulnerability assessment"],component:ka,status:"available"},{id:"openvas",name:"OpenVAS",description:"Comprehensive vulnerability assessment and management framework",icon:e.jsx(fe,{className:"h-6 w-6"}),features:["Vulnerability scanning","Network vulnerability tests","Authenticated scanning","Compliance checking","Report generation","Continuous monitoring"],useCases:["Comprehensive vulnerability assessment","Compliance auditing","Continuous security monitoring","Risk assessment","Patch management planning"],component:Sa,status:"available"},{id:"smbclient",name:"SMBClient",description:"SMB/CIFS client for accessing Windows shares and services",icon:e.jsx(as,{className:"h-6 w-6"}),features:["SMB share enumeration","File and directory access","Authentication testing","Share permissions analysis","Windows service interaction","NetBIOS name resolution"],useCases:["Windows network enumeration","Share accessibility testing","Credential validation","File system analysis","Windows security assessment"],component:Ca,status:"available"},{id:"enum4linux-ng",name:"enum4linux-ng",description:"Modern SMB enumeration tool for comprehensive Windows network analysis",icon:e.jsx(Fe,{className:"h-6 w-6"}),features:["SMB share enumeration","User and group enumeration","Password policy detection","OS information gathering","RID cycling","Modern Python implementation"],useCases:["Windows domain enumeration","User account discovery","Share and permission analysis","Security policy assessment","Active Directory reconnaissance"],component:Ta,status:"warning",warning:"Tool functional but may show detection issues in backend registry"}];function ci(){const t=dt(),{tools:s}=Pe(),a=zr("network-scanning"),n=t.pathname!=="/tools/network-scanning",r=d=>{const g=s.find(b=>b.id===d);return g&&g.status==="available"?"available":"unavailable"},c=d=>{switch(d){case"available":return e.jsx(be,{className:"h-4 w-4 text-green-500"});case"warning":return e.jsx(ee,{className:"h-4 w-4 text-yellow-500"});case"unavailable":return e.jsx(Ve,{className:"h-4 w-4 text-red-500"});default:return e.jsx(Ve,{className:"h-4 w-4 text-gray-500"})}},p=d=>{switch(d){case"available":return"Ready";case"warning":return"Available (Issues)";case"unavailable":return"Unavailable";default:return"Unknown"}};return n?e.jsx("div",{className:"h-full",children:e.jsxs(mt,{children:[e.jsx(te,{path:"/working-tools/*",element:e.jsx(li,{})}),e.jsx(te,{path:"/nmap/*",element:e.jsx(ya,{})}),e.jsx(te,{path:"/masscan/*",element:e.jsx(wa,{})}),e.jsx(te,{path:"/zmap/*",element:e.jsx(ka,{})}),e.jsx(te,{path:"/openvas/*",element:e.jsx(Sa,{})}),e.jsx(te,{path:"/smbclient/*",element:e.jsx(Ca,{})}),e.jsx(te,{path:"/enum4linux-ng/*",element:e.jsx(Ta,{})})]})}):e.jsxs("div",{className:"container-desktop py-6 space-y-6",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"p-2 bg-blue-100 dark:bg-blue-900/20 rounded-lg",children:e.jsx(Oe,{className:"h-6 w-6 text-blue-600 dark:text-blue-400"})}),e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold",children:"Network Scanning Tools"}),e.jsx("p",{className:"text-muted-foreground",children:"Discover and analyze network infrastructure with professional scanning tools"})]})]}),e.jsxs("div",{className:"flex items-center gap-4 pt-2",children:[e.jsxs($,{variant:"outline",className:"flex items-center gap-1.5",children:[e.jsx(be,{className:"h-3 w-3 text-green-500"}),a.filter(d=>d.status==="available").length," Available"]}),e.jsxs($,{variant:"outline",className:"flex items-center gap-1.5",children:[e.jsx(ee,{className:"h-3 w-3 text-yellow-500"}),"1 With Issues"]}),e.jsxs($,{variant:"outline",children:[Zt.length," Total Tools"]})]})]}),e.jsx("div",{className:"grid gap-6 md:grid-cols-2 lg:grid-cols-3",children:Zt.map(d=>{const g=r(d.id),b=d.status==="warning"?"warning":g;return e.jsxs(y,{className:"tool-card group relative",children:[e.jsxs(T,{className:"pb-3",children:[e.jsxs("div",{className:"flex items-start justify-between",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:B("p-2 rounded-lg",b==="available"&&"bg-green-100 dark:bg-green-900/20",b==="warning"&&"bg-yellow-100 dark:bg-yellow-900/20",b==="unavailable"&&"bg-red-100 dark:bg-red-900/20"),children:d.icon}),e.jsxs("div",{children:[e.jsx(A,{className:"text-lg",children:d.name}),e.jsxs("div",{className:"flex items-center gap-2 mt-1",children:[c(b),e.jsx("span",{className:"text-xs text-muted-foreground",children:p(b)})]})]})]}),e.jsx("div",{className:"flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity",children:e.jsx(Xs,{to:`/tools/network-scanning/${d.id}`,children:e.jsx(v,{size:"sm",variant:"outline",className:"h-8 w-8 p-0",children:e.jsx(Tt,{className:"h-4 w-4"})})})})]}),e.jsx(F,{className:"text-sm",children:d.description}),d.warning&&e.jsxs("div",{className:"flex items-start gap-2 p-2 bg-yellow-50 dark:bg-yellow-900/10 rounded-md border border-yellow-200 dark:border-yellow-800",children:[e.jsx(ee,{className:"h-4 w-4 text-yellow-600 dark:text-yellow-400 mt-0.5 flex-shrink-0"}),e.jsx("p",{className:"text-xs text-yellow-700 dark:text-yellow-300",children:d.warning})]})]}),e.jsxs(w,{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("h4",{className:"text-sm font-medium mb-2",children:"Key Features"}),e.jsxs("div",{className:"flex flex-wrap gap-1",children:[d.features.slice(0,3).map((j,R)=>e.jsx($,{variant:"secondary",className:"text-xs",children:j},R)),d.features.length>3&&e.jsxs($,{variant:"outline",className:"text-xs",children:["+",d.features.length-3," more"]})]})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"text-sm font-medium mb-2",children:"Primary Use Cases"}),e.jsx("ul",{className:"text-xs text-muted-foreground space-y-1",children:d.useCases.slice(0,2).map((j,R)=>e.jsxs("li",{className:"flex items-start gap-1",children:[e.jsx("span",{className:"text-primary mt-1",children:"•"}),e.jsx("span",{children:j})]},R))})]}),e.jsx("div",{className:"flex gap-2 pt-2",children:e.jsx(Xs,{to:`/tools/network-scanning/${d.id}`,className:"flex-1",children:e.jsxs(v,{className:"w-full",disabled:b==="unavailable",children:[e.jsx(Tt,{className:"h-4 w-4 mr-2"}),"Launch Tool"]})})})]})]},d.id)})}),e.jsxs(y,{className:"mt-8",children:[e.jsxs(T,{children:[e.jsxs(A,{className:"flex items-center gap-2",children:[e.jsx(Y,{className:"h-5 w-5"}),"Quick Start Guide"]}),e.jsx(F,{children:"Best practices for network scanning and reconnaissance"})]}),e.jsx(w,{className:"space-y-4",children:e.jsxs("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-3",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx("h4",{className:"font-medium text-sm",children:"1. Discovery Phase"}),e.jsx("p",{className:"text-xs text-muted-foreground",children:"Start with Nmap for initial network discovery and port scanning. Use ping sweeps and basic port scans to identify live hosts."})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("h4",{className:"font-medium text-sm",children:"2. Service Enumeration"}),e.jsx("p",{className:"text-xs text-muted-foreground",children:"Use Nmap with service detection (-sV) and OS fingerprinting (-O) to identify running services and operating systems."})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("h4",{className:"font-medium text-sm",children:"3. Specialized Scanning"}),e.jsx("p",{className:"text-xs text-muted-foreground",children:"Deploy Masscan for high-speed scans, OpenVAS for comprehensive vulnerability assessment, and SMB tools for Windows environments."})]})]})})]})]})}function oi(){return e.jsxs("div",{className:"space-y-6",children:[e.jsx("div",{className:"flex items-center justify-between",children:e.jsxs("div",{children:[e.jsx("h1",{className:"text-3xl font-bold tracking-tight",children:"Web Application Testing"}),e.jsx("p",{className:"text-muted-foreground",children:"Web security testing and analysis tools"})]})}),e.jsxs("div",{className:"grid gap-6 md:grid-cols-2",children:[e.jsxs(y,{children:[e.jsxs(T,{children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx(He,{className:"h-5 w-5"}),e.jsx(A,{children:"SQL Injection Testing"})]}),e.jsx(F,{children:"Automated SQL injection detection and exploitation"})]}),e.jsx(w,{children:e.jsxs("div",{className:"space-y-4",children:[e.jsx(P,{placeholder:"Target URL"}),e.jsxs("div",{className:"flex gap-2",children:[e.jsxs(v,{children:[e.jsx(ae,{className:"mr-2 h-4 w-4"}),"Test SQLi"]}),e.jsxs(v,{variant:"outline",children:[e.jsx(Y,{className:"mr-2 h-4 w-4"}),"Configure"]})]})]})})]}),e.jsxs(y,{children:[e.jsxs(T,{children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx(Fe,{className:"h-5 w-5"}),e.jsx(A,{children:"Directory Discovery"})]}),e.jsx(F,{children:"Find hidden files and directories"})]}),e.jsx(w,{children:e.jsxs("div",{className:"space-y-4",children:[e.jsx(P,{placeholder:"Website URL"}),e.jsxs("div",{className:"flex gap-2",children:[e.jsxs(v,{children:[e.jsx(ae,{className:"mr-2 h-4 w-4"}),"Discover"]}),e.jsxs(v,{variant:"outline",children:[e.jsx(Y,{className:"mr-2 h-4 w-4"}),"Configure"]})]})]})})]})]})]})}const ea={cves:{name:"CVEs",description:"Common Vulnerabilities and Exposures",count:"2500+"},vulnerabilities:{name:"Vulnerabilities",description:"General vulnerability templates",count:"1500+"},exposures:{name:"Exposures",description:"Information disclosure and exposures",count:"800+"},technologies:{name:"Technologies",description:"Technology detection templates",count:"600+"},misconfiguration:{name:"Misconfigurations",description:"Common misconfigurations",count:"400+"},"default-logins":{name:"Default Logins",description:"Default credential checks",count:"300+"},takeovers:{name:"Takeovers",description:"Subdomain takeover templates",count:"200+"},file:{name:"File Checks",description:"File-based vulnerabilities",count:"150+"},workflows:{name:"Workflows",description:"Multi-step scan workflows",count:"100+"},headless:{name:"Headless",description:"Browser-based templates",count:"80+"},code:{name:"Code Analysis",description:"Source code analysis",count:"70+"},dns:{name:"DNS",description:"DNS-based templates",count:"60+"},ssl:{name:"SSL/TLS",description:"SSL/TLS security checks",count:"50+"},network:{name:"Network",description:"Network service templates",count:"40+"}},sa={info:{name:"Info",color:"text-blue-600",bgColor:"bg-blue-50",description:"Informational findings"},low:{name:"Low",color:"text-green-600",bgColor:"bg-green-50",description:"Low risk vulnerabilities"},medium:{name:"Medium",color:"text-yellow-600",bgColor:"bg-yellow-50",description:"Medium risk vulnerabilities"},high:{name:"High",color:"text-orange-600",bgColor:"bg-orange-50",description:"High risk vulnerabilities"},critical:{name:"Critical",color:"text-red-600",bgColor:"bg-red-50",description:"Critical vulnerabilities"}},di={table:{name:"Table",description:"Human-readable table format"},json:{name:"JSON",description:"Structured JSON output"},jsonl:{name:"JSON Lines",description:"Line-delimited JSON"},csv:{name:"CSV",description:"Comma-separated values"},sarif:{name:"SARIF",description:"Static Analysis Results Interchange Format"},md:{name:"Markdown",description:"Markdown report format"}},ta=["oob","rce","xss","sqli","ssrf","cve","lfi","rfi","redirect","disclosure","misconfig","default-login","takeover","dos","injection","auth-bypass","panel","cms","wordpress","joomla","drupal","apache","nginx","iis"];function mi(){const{status:t}=Pe(),[s,a]=x.useState({targets:[""],inputFile:"",templates:{enabled:[],disabled:[],categories:["cves","vulnerabilities","exposures"],severities:["medium","high","critical"],customTemplates:[],workflowTemplates:[]},scanning:{concurrency:25,bulkSize:25,timeout:5,retries:1,rateLimit:150,randomAgent:!1,followRedirects:!1,maxRedirects:3,disableUpdateCheck:!1},filters:{excludeTags:[],includeTags:[],includeTemplates:[],excludeTemplates:[],author:[],excludeMatchers:[]},output:{format:"json",file:"",verbose:!1,silent:!1,noColor:!1,includeRaw:!1,markdown:!1,stats:!0},authentication:{enabled:!1,username:"",password:"",headers:{},cookies:""},proxy:{enabled:!1,url:"",credentials:""},system:{updateTemplates:!1,templateDirectory:"",configFile:"",systemResolvers:!0,offlineHTTP:!1,envVars:{}},advanced:{interactsh:!0,interactions:!1,projectFile:"",resumeScan:!1,sandbox:!1,stopAtFirstMatch:!1,metrics:!0,debug:!1,debugResponse:!1,trace:!1},customArgs:""}),[n,r]=x.useState(null),[c,p]=x.useState("configure"),[d,g]=x.useState(!1),[b,j]=x.useState(!1),[R,E]=x.useState(0),[f,L]=x.useState([]),[i,u]=x.useState(null),k={connected:t.connected},N=(o,l)=>{a(h=>{const O=o.split("."),M={...h};let H=M;for(let le=0;le<O.length-1;le++)H[O[le]]={...H[O[le]]},H=H[O[le]];return H[O[O.length-1]]=l,M})},D=o=>{const l=o.split(`
`).map(h=>h.trim()).filter(h=>h);N("targets",l)},W=o=>{const l={quick_scan:{templates:{...s.templates,categories:["cves","vulnerabilities"],severities:["high","critical"]},scanning:{...s.scanning,concurrency:10,timeout:3},output:{...s.output,format:"table",stats:!0}},comprehensive_scan:{templates:{...s.templates,categories:Object.keys(ea),severities:["info","low","medium","high","critical"]},scanning:{...s.scanning,concurrency:50,timeout:10},output:{...s.output,format:"json",verbose:!0,stats:!0}},cve_focused:{templates:{...s.templates,categories:["cves"],severities:["medium","high","critical"]},scanning:{...s.scanning,concurrency:30},filters:{...s.filters,includeTags:["cve"]}},web_app_scan:{templates:{...s.templates,categories:["vulnerabilities","exposures","misconfiguration"],severities:["low","medium","high","critical"]},filters:{...s.filters,includeTags:["xss","sqli","ssrf","lfi","rfi"]}},misconfiguration_scan:{templates:{...s.templates,categories:["misconfiguration","default-logins","exposures"],severities:["info","low","medium","high"]},filters:{...s.filters,includeTags:["misconfig","default-login","disclosure"]}},stealth_scan:{templates:{...s.templates,categories:["cves","vulnerabilities"],severities:["high","critical"]},scanning:{...s.scanning,concurrency:5,rateLimit:50,randomAgent:!0,timeout:15}}};l[o]&&a(h=>({...h,...l[o]}))},U=o=>{const l=s.templates.categories.includes(o)?s.templates.categories.filter(h=>h!==o):[...s.templates.categories,o];N("templates.categories",l)},S=o=>{const l=s.templates.severities.includes(o)?s.templates.severities.filter(h=>h!==o):[...s.templates.severities,o];N("templates.severities",l)},I=async()=>{g(!0);try{C.success("Nuclei templates are automatically updated by the backend")}catch(o){C.error(`Failed to update templates: ${o.message}`)}finally{g(!1)}},q=async()=>{const o=s.targets.filter(l=>l.trim());if(o.length===0&&!s.inputFile){C.error("Please provide at least one target URL or input file");return}if(s.templates.categories.length===0&&s.templates.enabled.length===0){C.error("Please select at least one template category or specific template");return}if(!k.connected){C.error("Backend not connected");return}try{j(!0),E(0),L([]),u(null),p("output"),console.log("🚀 Starting Nuclei scan with config:",s);const l=o.length>0?o:[s.inputFile].filter(Boolean),h=await ne.executeTool("nuclei",{target:l[0],timeout:s.timeout||300,threads:s.concurrency||25,output_format:"json",options:{templates:s.templates,severity:s.severity,tags:s.tags,exclude_tags:s.excludeTags,rate_limit:s.rateLimit,bulk_size:s.bulkSize,template_threads:s.templateThreads,custom_headers:s.customHeaders,proxy:s.proxy,user_agent:s.userAgent,follow_redirects:s.followRedirects,max_redirects:s.maxRedirects,disable_clustering:s.disableClustering,passive:s.passive,force_attempt_http2:s.forceAttemptHTTP2,dialer_timeout:s.dialerTimeout,dialer_keep_alive:s.dialerKeepAlive,max_host_error:s.maxHostError,track_error:s.trackError,max_template_error:s.maxTemplateError}},O=>{if(console.log("📊 Nuclei progress update:",O),r(O),E(O.progress),O.output&&L(M=>[...M,...O.output]),O.status==="completed"){j(!1);let M=O.results;if(O.results?.parsed_results){const H=O.results.parsed_results,le=H.statistics?.severity_counts||{};M={critical:le.critical||0,high:le.high||0,medium:le.medium||0,low:le.low||0,info:le.info||0,vulnerabilities:H.vulnerabilities||[],detailed_findings:H.detailed_findings||[],raw_backend_data:O.results}}u(M),console.log("✅ Nuclei scan completed with results:",M),console.log("🔍 Original backend response:",O.results),C.success("Nuclei scan completed")}else O.status==="failed"&&(j(!1),console.error("❌ Nuclei scan failed:",O.error),C.error(`Scan failed: ${O.error}`))});r(h),C.info("Nuclei scan started")}catch(l){j(!1),console.error("Failed to start Nuclei scan:",l),C.error(`Failed to start Nuclei scan: ${l.message}`)}},z=async()=>{b&&(j(!1),E(0),C.success("Nuclei scan stopped"))},X=()=>{let o="nuclei";return s.inputFile?o+=` -l "${s.inputFile}"`:s.targets.filter(l=>l.trim()).forEach(l=>{o+=` -u "${l}"`}),s.templates.categories.length>0&&(o+=` -tags ${s.templates.categories.join(",")}`),s.templates.severities.length>0&&(o+=` -severity ${s.templates.severities.join(",")}`),s.templates.enabled.length>0&&(o+=` -templates ${s.templates.enabled.join(",")}`),s.templates.disabled.length>0&&(o+=` -exclude-templates ${s.templates.disabled.join(",")}`),s.templates.customTemplates.length>0&&(o+=` -templates ${s.templates.customTemplates.join(",")}`),s.templates.workflowTemplates.length>0&&(o+=` -workflows ${s.templates.workflowTemplates.join(",")}`),s.filters.includeTags.length>0&&(o+=` -include-tags ${s.filters.includeTags.join(",")}`),s.filters.excludeTags.length>0&&(o+=` -exclude-tags ${s.filters.excludeTags.join(",")}`),s.filters.author.length>0&&(o+=` -author ${s.filters.author.join(",")}`),s.scanning.concurrency!==25&&(o+=` -c ${s.scanning.concurrency}`),s.scanning.bulkSize!==25&&(o+=` -bulk-size ${s.scanning.bulkSize}`),s.scanning.timeout!==5&&(o+=` -timeout ${s.scanning.timeout}`),s.scanning.retries!==1&&(o+=` -retries ${s.scanning.retries}`),s.scanning.rateLimit!==150&&(o+=` -rate-limit ${s.scanning.rateLimit}`),s.scanning.randomAgent&&(o+=" -random-agent"),s.scanning.followRedirects&&(o+=" -follow-redirects"),s.scanning.maxRedirects!==3&&(o+=` -max-redirects ${s.scanning.maxRedirects}`),s.authentication.enabled&&s.authentication.username&&s.authentication.password&&(o+=` -header "Authorization: Basic $(echo -n '${s.authentication.username}:${s.authentication.password}' | base64)"`),Object.entries(s.authentication.headers).forEach(([l,h])=>{o+=` -header "${l}: ${h}"`}),s.authentication.cookies&&(o+=` -header "Cookie: ${s.authentication.cookies}"`),s.proxy.enabled&&s.proxy.url&&(o+=` -proxy-url "${s.proxy.url}"`),s.output.format!=="table"&&(o+=" -json",s.output.file&&(o+=` -o "${s.output.file}"`)),s.output.verbose&&(o+=" -v"),s.output.silent&&(o+=" -silent"),s.output.noColor&&(o+=" -no-color"),s.output.stats&&(o+=" -stats"),s.output.markdown&&(o+=" -markdown-export"),s.system.updateTemplates&&(o+=" -update-templates"),s.system.templateDirectory&&(o+=` -templates-directory "${s.system.templateDirectory}"`),s.system.configFile&&(o+=` -config "${s.system.configFile}"`),s.system.systemResolvers||(o+=" -system-resolvers"),s.advanced.interactsh||(o+=" -no-interactsh"),s.advanced.interactions&&(o+=" -interactions-cache-size 5000"),s.advanced.projectFile&&(o+=` -project-path "${s.advanced.projectFile}"`),s.advanced.resumeScan&&(o+=" -resume"),s.advanced.sandbox&&(o+=" -sandbox"),s.advanced.stopAtFirstMatch&&(o+=" -stop-at-first-match"),s.advanced.metrics&&(o+=" -metrics"),s.advanced.debug&&(o+=" -debug"),s.advanced.debugResponse&&(o+=" -debug-response"),s.advanced.trace&&(o+=" -trace-log-file trace.log"),s.customArgs&&(o+=` ${s.customArgs}`),o},m=t.connected&&(s.targets.some(o=>o.trim())||s.inputFile)&&(s.templates.categories.length>0||s.templates.enabled.length>0)&&!b;return x.useEffect(()=>{console.log("🔍 Nuclei canStart debug:",{connected:t.connected,hasTargets:s.targets.some(o=>o.trim()),hasInputFile:!!s.inputFile,hasCategories:s.templates.categories.length>0,hasEnabled:s.templates.enabled.length>0,isScanning:b,canStart:m,targets:s.targets,categories:s.templates.categories})},[t.connected,s.targets,s.inputFile,s.templates.categories,s.templates.enabled,b,m]),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"flex items-center justify-center w-10 h-10 rounded-lg bg-gradient-to-br from-purple-500 to-pink-600",children:e.jsx(ue,{className:"h-5 w-5 text-white"})}),e.jsxs("div",{children:[e.jsx("h2",{className:"text-2xl font-bold text-gray-900 dark:text-gray-100",children:"Nuclei Scanner"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-400",children:"Fast and customizable vulnerability scanner with 4000+ templates"})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[e.jsx(y,{children:e.jsx(w,{className:"pt-6",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400",children:"Status"}),e.jsx("p",{className:"text-2xl font-bold",children:b?"Scanning":"Ready"})]}),e.jsx(ve,{className:B("h-8 w-8",b?"text-green-600":"text-gray-400")})]})})}),e.jsx(y,{children:e.jsx(w,{className:"pt-6",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400",children:"Templates"}),e.jsx("p",{className:"text-2xl font-bold",children:s.templates.categories.length})]}),e.jsx(vn,{className:"h-8 w-8 text-blue-600"})]})})}),e.jsx(y,{children:e.jsx(w,{className:"pt-6",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400",children:"Concurrency"}),e.jsx("p",{className:"text-2xl font-bold",children:s.scanning.concurrency})]}),e.jsx(Ns,{className:"h-8 w-8 text-purple-600"})]})})}),e.jsx(y,{children:e.jsx(w,{className:"pt-6",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400",children:"Progress"}),e.jsx("p",{className:"text-2xl font-bold",children:b?`${R}%`:"0%"})]}),e.jsx(ts,{className:"h-8 w-8 text-green-600"})]})})})]}),e.jsxs(y,{children:[e.jsx(T,{children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsxs(A,{className:"flex items-center gap-2",children:[e.jsx(bn,{className:"h-5 w-5"}),"Nuclei Configuration"]}),e.jsx(F,{children:"Configure template-based vulnerability scanning with comprehensive detection capabilities"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsxs(v,{variant:"outline",size:"sm",onClick:I,disabled:d,children:[d?e.jsx(st,{className:"h-4 w-4 mr-2 animate-spin"}):e.jsx(ke,{className:"h-4 w-4 mr-2"}),"Update Templates"]}),e.jsxs(v,{variant:"outline",size:"sm",onClick:()=>navigator.clipboard.writeText(X()),children:[e.jsx(je,{className:"h-4 w-4 mr-2"}),"Copy Command"]}),e.jsxs(v,{variant:"outline",size:"sm",onClick:()=>a(o=>({...o,targets:[""],inputFile:"",templates:{...o.templates,enabled:[],disabled:[],customTemplates:[]},authentication:{...o.authentication,headers:{},cookies:""},customArgs:""})),children:[e.jsx(qe,{className:"h-4 w-4 mr-2"}),"Reset"]}),b?e.jsxs(v,{variant:"destructive",size:"sm",onClick:z,children:[e.jsx(We,{className:"h-4 w-4 mr-2"}),"Stop Scan"]}):e.jsxs(v,{size:"sm",onClick:q,disabled:!m,children:[e.jsx(ae,{className:"h-4 w-4 mr-2"}),"Start Scan"]})]})]})}),e.jsx(w,{children:e.jsxs(Ue,{value:c,onValueChange:p,children:[e.jsxs(Be,{className:"grid w-full grid-cols-4",children:[e.jsx(Q,{value:"configure",children:"Configure"}),e.jsx(Q,{value:"output",children:"Output"}),e.jsx(Q,{value:"results",children:"Results"}),e.jsx(Q,{value:"command",children:"Command"})]}),e.jsxs(J,{value:"configure",className:"space-y-6",children:[e.jsxs("div",{className:"space-y-4",children:[e.jsx("h3",{className:"text-lg font-semibold",children:"Quick Presets"}),e.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-3 gap-3",children:[e.jsxs(v,{variant:"outline",onClick:()=>W("quick_scan"),children:[e.jsx(Ge,{className:"h-4 w-4 mr-2"}),"Quick Scan"]}),e.jsxs(v,{variant:"outline",onClick:()=>W("comprehensive_scan"),children:[e.jsx(Fe,{className:"h-4 w-4 mr-2"}),"Comprehensive"]}),e.jsxs(v,{variant:"outline",onClick:()=>W("cve_focused"),children:[e.jsx(Es,{className:"h-4 w-4 mr-2"}),"CVE Focused"]}),e.jsxs(v,{variant:"outline",onClick:()=>W("web_app_scan"),children:[e.jsx(bs,{className:"h-4 w-4 mr-2"}),"Web App Scan"]}),e.jsxs(v,{variant:"outline",onClick:()=>W("misconfiguration_scan"),children:[e.jsx(Y,{className:"h-4 w-4 mr-2"}),"Misconfigurations"]}),e.jsxs(v,{variant:"outline",onClick:()=>W("stealth_scan"),children:[e.jsx(ys,{className:"h-4 w-4 mr-2"}),"Stealth Scan"]})]})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsx("h3",{className:"text-lg font-semibold",children:"Target Configuration"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium",children:"Target URLs"}),e.jsx(Le,{value:s.targets.join(`
`),onChange:o=>D(o.target.value),placeholder:`https://example.com
https://test.com
***********`,className:"min-h-[120px]"}),e.jsx("p",{className:"text-xs text-gray-500",children:"Enter one URL per line"})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium",children:"Input File (Alternative)"}),e.jsx(P,{value:s.inputFile,onChange:o=>N("inputFile",o.target.value),placeholder:"/path/to/targets.txt"}),e.jsx("p",{className:"text-xs text-gray-500",children:"File containing URLs to scan"})]})]})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsx("h3",{className:"text-lg font-semibold",children:"Template Categories"}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-3",children:Object.entries(ea).map(([o,l])=>e.jsx(y,{className:B("cursor-pointer transition-colors",s.templates.categories.includes(o)?"border-purple-500 bg-purple-50 dark:bg-purple-950":""),onClick:()=>U(o),children:e.jsx(w,{className:"pt-6",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex-1",children:[e.jsx("div",{className:"font-medium",children:l.name}),e.jsx("div",{className:"text-sm text-gray-500",children:l.description})]}),e.jsx($,{variant:"outline",children:l.count})]})})},o))})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsx("h3",{className:"text-lg font-semibold",children:"Severity Levels"}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-5 gap-3",children:Object.entries(sa).map(([o,l])=>e.jsx(y,{className:B("cursor-pointer transition-colors",s.templates.severities.includes(o)?"border-purple-500":"",l.bgColor),onClick:()=>S(o),children:e.jsx(w,{className:"pt-6",children:e.jsxs("div",{className:"text-center space-y-2",children:[e.jsx("div",{className:B("font-medium",l.color),children:l.name}),e.jsx("div",{className:"text-xs text-gray-600",children:l.description})]})})},o))})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsx("h3",{className:"text-lg font-semibold",children:"Template Filters"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium",children:"Include Tags"}),e.jsx("div",{className:"flex flex-wrap gap-1 mb-2",children:s.filters.includeTags.map(o=>e.jsxs($,{variant:"secondary",className:"cursor-pointer",onClick:()=>N("filters.includeTags",s.filters.includeTags.filter(l=>l!==o)),children:[o," ×"]},o))}),e.jsxs(pe,{onValueChange:o=>{o&&!s.filters.includeTags.includes(o)&&N("filters.includeTags",[...s.filters.includeTags,o])},children:[e.jsx(oe,{children:e.jsx(de,{placeholder:"Add include tag"})}),e.jsx(me,{children:ta.map(o=>e.jsx(_,{value:o,children:o},o))})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium",children:"Exclude Tags"}),e.jsx("div",{className:"flex flex-wrap gap-1 mb-2",children:s.filters.excludeTags.map(o=>e.jsxs($,{variant:"destructive",className:"cursor-pointer",onClick:()=>N("filters.excludeTags",s.filters.excludeTags.filter(l=>l!==o)),children:[o," ×"]},o))}),e.jsxs(pe,{onValueChange:o=>{o&&!s.filters.excludeTags.includes(o)&&N("filters.excludeTags",[...s.filters.excludeTags,o])},children:[e.jsx(oe,{children:e.jsx(de,{placeholder:"Add exclude tag"})}),e.jsx(me,{children:ta.map(o=>e.jsx(_,{value:o,children:o},o))})]})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium",children:"Custom Templates"}),e.jsx(Le,{value:s.templates.customTemplates.join(`
`),onChange:o=>N("templates.customTemplates",o.target.value.split(`
`).filter(l=>l.trim())),placeholder:`/path/to/custom-template.yaml
/path/to/another-template.yaml`,className:"min-h-[80px]"})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium",children:"Workflow Templates"}),e.jsx(Le,{value:s.templates.workflowTemplates.join(`
`),onChange:o=>N("templates.workflowTemplates",o.target.value.split(`
`).filter(l=>l.trim())),placeholder:`/path/to/workflow.yaml
/path/to/another-workflow.yaml`,className:"min-h-[80px]"})]})]})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsx("h3",{className:"text-lg font-semibold",children:"Scanning Configuration"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium",children:"Concurrency"}),e.jsx(P,{type:"number",value:s.scanning.concurrency,onChange:o=>N("scanning.concurrency",parseInt(o.target.value)||25),min:"1",max:"100"})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium",children:"Timeout (s)"}),e.jsx(P,{type:"number",value:s.scanning.timeout,onChange:o=>N("scanning.timeout",parseInt(o.target.value)||5),min:"1",max:"60"})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium",children:"Rate Limit"}),e.jsx(P,{type:"number",value:s.scanning.rateLimit,onChange:o=>N("scanning.rateLimit",parseInt(o.target.value)||150),min:"1",max:"1000"})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium",children:"Retries"}),e.jsx(P,{type:"number",value:s.scanning.retries,onChange:o=>N("scanning.retries",parseInt(o.target.value)||1),min:"0",max:"5"})]})]}),e.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("input",{type:"checkbox",id:"randomAgent",checked:s.scanning.randomAgent,onChange:o=>N("scanning.randomAgent",o.target.checked),className:"rounded"}),e.jsx("label",{htmlFor:"randomAgent",className:"text-sm font-medium",children:"Random Agent"})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("input",{type:"checkbox",id:"followRedirects",checked:s.scanning.followRedirects,onChange:o=>N("scanning.followRedirects",o.target.checked),className:"rounded"}),e.jsx("label",{htmlFor:"followRedirects",className:"text-sm font-medium",children:"Follow Redirects"})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("input",{type:"checkbox",id:"interactsh",checked:s.advanced.interactsh,onChange:o=>N("advanced.interactsh",o.target.checked),className:"rounded"}),e.jsx("label",{htmlFor:"interactsh",className:"text-sm font-medium",children:"Interactsh"})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("input",{type:"checkbox",id:"sandbox",checked:s.advanced.sandbox,onChange:o=>N("advanced.sandbox",o.target.checked),className:"rounded"}),e.jsx("label",{htmlFor:"sandbox",className:"text-sm font-medium",children:"Sandbox Mode"})]})]})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsx("h3",{className:"text-lg font-semibold",children:"Output Configuration"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium",children:"Output Format"}),e.jsxs(pe,{value:s.output.format,onValueChange:o=>N("output.format",o),children:[e.jsx(oe,{children:e.jsx(de,{})}),e.jsx(me,{children:Object.entries(di).map(([o,l])=>e.jsx(_,{value:o,children:e.jsxs("div",{children:[e.jsx("div",{className:"font-medium",children:l.name}),e.jsx("div",{className:"text-xs text-gray-500",children:l.description})]})},o))})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium",children:"Output File"}),e.jsx(P,{value:s.output.file,onChange:o=>N("output.file",o.target.value),placeholder:"/path/to/output.json"})]})]}),e.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("input",{type:"checkbox",id:"verbose",checked:s.output.verbose,onChange:o=>N("output.verbose",o.target.checked),className:"rounded"}),e.jsx("label",{htmlFor:"verbose",className:"text-sm font-medium",children:"Verbose"})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("input",{type:"checkbox",id:"silent",checked:s.output.silent,onChange:o=>N("output.silent",o.target.checked),className:"rounded"}),e.jsx("label",{htmlFor:"silent",className:"text-sm font-medium",children:"Silent"})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("input",{type:"checkbox",id:"stats",checked:s.output.stats,onChange:o=>N("output.stats",o.target.checked),className:"rounded"}),e.jsx("label",{htmlFor:"stats",className:"text-sm font-medium",children:"Show Stats"})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("input",{type:"checkbox",id:"markdown",checked:s.output.markdown,onChange:o=>N("output.markdown",o.target.checked),className:"rounded"}),e.jsx("label",{htmlFor:"markdown",className:"text-sm font-medium",children:"Markdown Report"})]})]})]})]}),e.jsxs(J,{value:"output",className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h3",{className:"text-lg font-semibold",children:"Real-time Output"}),e.jsxs("div",{className:"flex items-center gap-2",children:[b&&e.jsxs($,{variant:"secondary",className:"animate-pulse",children:[e.jsx(ve,{className:"h-3 w-3 mr-1"}),"Scanning in progress..."]}),e.jsxs(v,{variant:"outline",size:"sm",children:[e.jsx(ke,{className:"h-4 w-4 mr-2"}),"Export Output"]})]})]}),b&&e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex justify-between text-sm",children:[e.jsx("span",{children:"Progress"}),e.jsxs("span",{children:[R,"%"]})]}),e.jsx(Ae,{value:R,className:"w-full"})]}),e.jsx(y,{children:e.jsx(w,{className:"p-4",children:e.jsx("div",{className:"bg-gray-900 dark:bg-gray-800 rounded-lg p-4 min-h-[400px] font-mono text-sm overflow-auto",children:e.jsx("div",{className:"text-green-400",children:f.length>0?f.map((o,l)=>e.jsx("div",{className:"mb-1",children:o},l)):"Nuclei output will appear here when scanning starts..."})})})})]}),e.jsxs(J,{value:"results",className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h3",{className:"text-lg font-semibold",children:"Vulnerability Results"}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsxs(v,{variant:"outline",size:"sm",children:[e.jsx(ke,{className:"h-4 w-4 mr-2"}),"Export Results"]}),e.jsxs(v,{variant:"outline",size:"sm",children:[e.jsx(Fs,{className:"h-4 w-4 mr-2"}),"Filter Results"]})]})]}),i?e.jsxs("div",{className:"space-y-4",children:[!1,e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-5 gap-4",children:Object.entries(sa).map(([o,l])=>e.jsx(y,{children:e.jsx(w,{className:"pt-6",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400",children:l.name}),e.jsx("p",{className:B("text-2xl font-bold",l.color),children:i[o]||0})]}),e.jsx(ee,{className:B("h-8 w-8",l.color)})]})})},o))}),e.jsx(y,{children:e.jsx(w,{className:"p-0",children:e.jsx("div",{className:"overflow-x-auto",children:e.jsxs("table",{className:"w-full",children:[e.jsx("thead",{className:"border-b",children:e.jsxs("tr",{className:"text-left",children:[e.jsx("th",{className:"p-4 font-medium",children:"Template"}),e.jsx("th",{className:"p-4 font-medium",children:"Severity"}),e.jsx("th",{className:"p-4 font-medium",children:"Target"}),e.jsx("th",{className:"p-4 font-medium",children:"Tags"}),e.jsx("th",{className:"p-4 font-medium",children:"Info"})]})}),e.jsx("tbody",{children:(i.vulnerabilities||i.detailed_findings||[]).length>0?(i.vulnerabilities||i.detailed_findings||[]).map((o,l)=>e.jsxs("tr",{className:"border-b hover:bg-gray-50 dark:hover:bg-gray-800",children:[e.jsx("td",{className:"p-4 font-medium",children:o.template_id||o.id||`Finding ${l+1}`}),e.jsx("td",{className:"p-4",children:e.jsx($,{variant:o.severity==="critical"||o.severity==="high"?"destructive":o.severity==="medium"?"default":"secondary",children:o.severity||"info"})}),e.jsx("td",{className:"p-4 font-mono text-sm",children:o.url||o.target||"N/A"}),e.jsx("td",{className:"p-4",children:e.jsxs("div",{className:"flex flex-wrap gap-1",children:[(o.tags||[]).slice(0,3).map((h,O)=>e.jsx($,{variant:"outline",className:"text-xs",children:h},O)),(o.tags||[]).length>3&&e.jsxs($,{variant:"outline",className:"text-xs",children:["+",(o.tags||[]).length-3]})]})}),e.jsx("td",{className:"p-4 text-sm",children:o.name||o.info})]},l)):e.jsx("tr",{children:e.jsx("td",{colSpan:5,className:"p-8 text-center text-gray-500 dark:text-gray-400",children:e.jsxs("div",{className:"flex flex-col items-center space-y-2",children:[e.jsx(fe,{className:"h-8 w-8 text-green-500"}),e.jsx("p",{className:"font-medium",children:"No vulnerabilities found"}),e.jsx("p",{className:"text-sm",children:"The target appears to be secure against the tested templates."})]})})})})]})})})})]}):e.jsx(y,{children:e.jsxs(w,{className:"p-8 text-center",children:[e.jsx(ue,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),e.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2",children:"No Results Yet"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-400",children:"Start a vulnerability scan to see results here. Nuclei will identify vulnerabilities using its comprehensive template database."})]})})]}),e.jsxs(J,{value:"command",className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h3",{className:"text-lg font-semibold",children:"Generated Command"}),e.jsxs(v,{variant:"outline",size:"sm",onClick:()=>navigator.clipboard.writeText(X()),children:[e.jsx(je,{className:"h-4 w-4 mr-2"}),"Copy Command"]})]}),e.jsx(y,{children:e.jsx(w,{className:"p-4",children:e.jsx("div",{className:"bg-gray-900 dark:bg-gray-800 rounded-lg p-4 overflow-x-auto",children:e.jsx("code",{className:"text-green-400 whitespace-pre-wrap break-all",children:X()})})})}),e.jsxs("div",{className:"space-y-4",children:[e.jsx("h4",{className:"font-semibold",children:"Command Explanation"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm",children:[e.jsxs("div",{children:[e.jsx("h5",{className:"font-medium mb-2",children:"Target Options"}),e.jsxs("ul",{className:"space-y-1 text-gray-600 dark:text-gray-400",children:[e.jsxs("li",{children:["• ",e.jsx("code",{children:"-u"}),": Single target URL"]}),e.jsxs("li",{children:["• ",e.jsx("code",{children:"-l"}),": List of targets from file"]}),e.jsxs("li",{children:["• ",e.jsx("code",{children:"-tags"}),": Template categories to include"]}),e.jsxs("li",{children:["• ",e.jsx("code",{children:"-severity"}),": Severity levels to scan"]})]})]}),e.jsxs("div",{children:[e.jsx("h5",{className:"font-medium mb-2",children:"Template Control"}),e.jsxs("ul",{className:"space-y-1 text-gray-600 dark:text-gray-400",children:[e.jsxs("li",{children:["• ",e.jsx("code",{children:"-templates"}),": Specific templates to use"]}),e.jsxs("li",{children:["• ",e.jsx("code",{children:"-exclude-templates"}),": Templates to skip"]}),e.jsxs("li",{children:["• ",e.jsx("code",{children:"-include-tags"}),": Include specific tags"]}),e.jsxs("li",{children:["• ",e.jsx("code",{children:"-exclude-tags"}),": Exclude specific tags"]})]})]}),e.jsxs("div",{children:[e.jsx("h5",{className:"font-medium mb-2",children:"Performance"}),e.jsxs("ul",{className:"space-y-1 text-gray-600 dark:text-gray-400",children:[e.jsxs("li",{children:["• ",e.jsx("code",{children:"-c"}),": Concurrency level"]}),e.jsxs("li",{children:["• ",e.jsx("code",{children:"-bulk-size"}),": Bulk request size"]}),e.jsxs("li",{children:["• ",e.jsx("code",{children:"-timeout"}),": Request timeout"]}),e.jsxs("li",{children:["• ",e.jsx("code",{children:"-rate-limit"}),": Requests per second"]})]})]}),e.jsxs("div",{children:[e.jsx("h5",{className:"font-medium mb-2",children:"Output & Debug"}),e.jsxs("ul",{className:"space-y-1 text-gray-600 dark:text-gray-400",children:[e.jsxs("li",{children:["• ",e.jsx("code",{children:"-json"}),": JSON output format"]}),e.jsxs("li",{children:["• ",e.jsx("code",{children:"-o"}),": Output file path"]}),e.jsxs("li",{children:["• ",e.jsx("code",{children:"-v"}),": Verbose output"]}),e.jsxs("li",{children:["• ",e.jsx("code",{children:"-stats"}),": Show statistics"]})]})]})]})]})]})]})})]})]})}const aa={boolean:{name:"Boolean-based Blind",code:"B",description:"Boolean-based blind SQL injection"},error:{name:"Error-based",code:"E",description:"Error-based SQL injection"},union:{name:"UNION Query-based",code:"U",description:"UNION query-based SQL injection"},stacked:{name:"Stacked Queries",code:"S",description:"Stacked queries SQL injection"},time:{name:"Time-based Blind",code:"T",description:"Time-based blind SQL injection"},inline:{name:"Inline Queries",code:"Q",description:"Inline query SQL injection"}},ui=["MySQL","Oracle","PostgreSQL","Microsoft SQL Server","SQLite","MongoDB","Microsoft Access","Firebird","Sybase","SAP MaxDB","IBM DB2","HSQLDB","Informix","MariaDB","MemSQL","TiDB","CockroachDB","H2","MonetDB"],hi=["Linux","Windows","FreeBSD","NetBSD","OpenBSD","Mac","Solaris"],xi=["apostrophemask","apostrophenullencode","appendnullbyte","base64encode","between","bluecoat","chardoubleencode","charencode","charunicodeencode","concat2concatws","equaltolike","greatest","halfversionedmorekeywords","ifnull2ifisnull","modsecurityversioned","modsecurityzeroversioned","multiplespaces","percentage","randomcase","randomcomments","securesphere","space2comment","space2dash","space2hash","space2morehash","space2mssqlblank","space2mssqlhash","space2mysqlblank","space2mysqldash","space2plus","space2randomblank","unionalltounion","unmagicquotes","versionedkeywords","versionedmorekeywords","xforwardedfor"];function pi(){const{connectionStatus:t}=Pe(),[s,a]=x.useState({target:{url:"",method:"GET",data:"",cookies:"",headers:{},userAgent:"",referer:"",requestFile:"",bulkFile:""},injection:{parameters:[],skipParameters:[],testParameter:"",dbms:"",os:"",techniques:{boolean:!0,error:!0,union:!0,stacked:!1,time:!1,inline:!1},tamper:[],prefix:"",suffix:"",level:1,risk:1},enumeration:{currentUser:!1,currentDb:!1,isDba:!1,users:!1,passwords:!1,privileges:!1,roles:!1,databases:!1,tables:!1,columns:!1,count:!1,dump:!1,dumpAll:!1,search:!1,comments:!1,statements:!1,excludeColumns:[],limitStart:0,limitStop:0,firstChar:1,lastChar:0,query:"",sqlShell:!1,osShell:!1},advanced:{threads:1,delay:0,timeout:30,retries:3,randomize:!1,safeFreq:0,skipWaf:!1,hpp:!1,chunked:!1,parseErrors:!1,keepAlive:!1,nullConnection:!1,predictOutput:!1,testFilter:"",testSkip:""},output:{verbose:1,dumpTable:"",dumpWhere:"",dumpFile:"",csvDel:",",outputDir:"",dumpFormat:"CSV",logFile:"",trafficFile:"",answers:"",batch:!1,binaryFields:"",charset:"",encoding:"",forms:!1,fresh:!1,hexdump:!1,parseErrors:!1,repair:!1,saveConfig:""},brute:{commonTables:!1,commonColumns:!1,bruteForce:!1,commonFiles:!1,udfInject:!1,sharedLib:""},filesystem:{readFile:"",writeFile:"",destFile:""},takeover:{osCmd:"",osShell:!1,osPwn:!1,osSmb:!1,osBof:!1,priv:!1,regRead:!1,regAdd:!1,regDel:!1,regKey:"",regVal:"",regData:"",regType:""},authentication:{authType:"Basic",authCred:"",authFile:""},proxy:{enabled:!1,url:"",credentials:"",proxyCred:"",proxyFile:"",ignoreProxy:!1},evasion:{randomAgent:!1,mobile:!1,pageRank:!1,titles:!1,checkWaf:!1,identifyWaf:!1,skipHeuristics:!1,smart:!1,textOnly:!1,tor:!1,torPort:9050,torType:"HTTP",checkTor:!1},customArgs:""}),[n,r]=x.useState(null),[c,p]=x.useState("configure"),d=(i,u)=>{a(k=>{const N=i.split("."),D={...k};let W=D;for(let U=0;U<N.length-1;U++)W[N[U]]={...W[N[U]]},W=W[N[U]];return W[N[N.length-1]]=u,D})},g=i=>{const u={basic_injection:{injection:{...s.injection,techniques:{boolean:!0,error:!0,union:!0,stacked:!1,time:!1,inline:!1},level:1,risk:1},enumeration:{...s.enumeration,currentUser:!0,currentDb:!0,isDba:!0}},comprehensive_enum:{injection:{...s.injection,techniques:{boolean:!0,error:!0,union:!0,stacked:!0,time:!0,inline:!1},level:3,risk:2},enumeration:{...s.enumeration,currentUser:!0,currentDb:!0,isDba:!0,users:!0,passwords:!0,privileges:!0,databases:!0,tables:!0,columns:!0}},database_takeover:{injection:{...s.injection,techniques:{boolean:!0,error:!0,union:!0,stacked:!0,time:!0,inline:!0},level:5,risk:3},enumeration:{...s.enumeration,currentUser:!0,currentDb:!0,isDba:!0,users:!0,passwords:!0,privileges:!0,databases:!0,tables:!0,columns:!0,dump:!0},takeover:{...s.takeover,osShell:!0,priv:!0}},waf_bypass:{injection:{...s.injection,tamper:["space2comment","randomcase","charencode"],level:3,risk:2},evasion:{...s.evasion,randomAgent:!0,checkWaf:!0,identifyWaf:!0},advanced:{...s.advanced,delay:1,randomize:!0}},time_based_blind:{injection:{...s.injection,techniques:{boolean:!1,error:!1,union:!1,stacked:!1,time:!0,inline:!1},level:2,risk:1},advanced:{...s.advanced,timeout:60,delay:2}},stealth_scan:{injection:{...s.injection,level:1,risk:1,tamper:["space2comment","randomcase"]},evasion:{...s.evasion,randomAgent:!0,smart:!0},advanced:{...s.advanced,threads:1,delay:3,randomize:!0}}};u[i]&&a(k=>({...k,...u[i]}))},b=i=>{d(`injection.techniques.${i}`,!s.injection.techniques[i])},j=async()=>{if(!s.target.url.trim()&&!s.target.requestFile&&!s.target.bulkFile){C.error("Please provide a target URL, request file, or bulk file");return}if(!Object.values(s.injection.techniques).some(u=>u)){C.error("Please select at least one SQL injection technique");return}try{const u=await ne.executeSecurityTool("sqlmap",s);r(u),p("output"),C.success("SQLMap scan started successfully")}catch(u){C.error(`Failed to start SQLMap scan: ${u.message}`)}},R=async()=>{if(n)try{await ne.stopToolExecution(n.id),C.success("SQLMap scan stopped")}catch(i){C.error(`Failed to stop scan: ${i.message}`)}},E=()=>{let i="sqlmap";s.target.url&&(i+=` -u "${s.target.url}"`),s.target.requestFile&&(i+=` -r "${s.target.requestFile}"`),s.target.bulkFile&&(i+=` -m "${s.target.bulkFile}"`),s.target.method!=="GET"&&(i+=` --method=${s.target.method}`),s.target.data&&(i+=` --data="${s.target.data}"`),s.target.cookies&&(i+=` --cookie="${s.target.cookies}"`),s.target.userAgent&&(i+=` --user-agent="${s.target.userAgent}"`),s.target.referer&&(i+=` --referer="${s.target.referer}"`),Object.entries(s.target.headers).forEach(([k,N])=>{i+=` --header="${k}: ${N}"`});const u=Object.entries(s.injection.techniques).filter(([k,N])=>N).map(([k,N])=>aa[k].code).join("");return u&&(i+=` --technique=${u}`),s.injection.testParameter&&(i+=` -p "${s.injection.testParameter}"`),s.injection.skipParameters.length>0&&(i+=` --skip="${s.injection.skipParameters.join(",")}"`),s.injection.dbms&&(i+=` --dbms="${s.injection.dbms}"`),s.injection.os&&(i+=` --os="${s.injection.os}"`),s.injection.level!==1&&(i+=` --level=${s.injection.level}`),s.injection.risk!==1&&(i+=` --risk=${s.injection.risk}`),s.injection.tamper.length>0&&(i+=` --tamper="${s.injection.tamper.join(",")}"`),s.injection.prefix&&(i+=` --prefix="${s.injection.prefix}"`),s.injection.suffix&&(i+=` --suffix="${s.injection.suffix}"`),s.enumeration.currentUser&&(i+=" --current-user"),s.enumeration.currentDb&&(i+=" --current-db"),s.enumeration.isDba&&(i+=" --is-dba"),s.enumeration.users&&(i+=" --users"),s.enumeration.passwords&&(i+=" --passwords"),s.enumeration.privileges&&(i+=" --privileges"),s.enumeration.roles&&(i+=" --roles"),s.enumeration.databases&&(i+=" --dbs"),s.enumeration.tables&&(i+=" --tables"),s.enumeration.columns&&(i+=" --columns"),s.enumeration.count&&(i+=" --count"),s.enumeration.dump&&(i+=" --dump"),s.enumeration.dumpAll&&(i+=" --dump-all"),s.enumeration.search&&(i+=" --search"),s.enumeration.comments&&(i+=" --comments"),s.enumeration.statements&&(i+=" --statements"),s.enumeration.sqlShell&&(i+=" --sql-shell"),s.enumeration.osShell&&(i+=" --os-shell"),s.enumeration.query&&(i+=` --sql-query="${s.enumeration.query}"`),s.output.dumpTable&&(i+=` -T "${s.output.dumpTable}"`),s.output.dumpWhere&&(i+=` --where="${s.output.dumpWhere}"`),(s.enumeration.limitStart>0||s.enumeration.limitStop>0)&&(i+=` --start=${s.enumeration.limitStart} --stop=${s.enumeration.limitStop}`),s.advanced.threads>1&&(i+=` --threads=${s.advanced.threads}`),s.advanced.delay>0&&(i+=` --delay=${s.advanced.delay}`),s.advanced.timeout!==30&&(i+=` --timeout=${s.advanced.timeout}`),s.advanced.retries!==3&&(i+=` --retries=${s.advanced.retries}`),s.advanced.randomize&&(i+=" --randomize"),s.advanced.skipWaf&&(i+=" --skip-waf"),s.advanced.hpp&&(i+=" --hpp"),s.advanced.chunked&&(i+=" --chunked"),s.advanced.parseErrors&&(i+=" --parse-errors"),s.advanced.keepAlive&&(i+=" --keep-alive"),s.advanced.nullConnection&&(i+=" --null-connection"),s.output.verbose>1&&(i+=` -v ${s.output.verbose}`),s.output.outputDir&&(i+=` --output-dir="${s.output.outputDir}"`),s.output.dumpFormat!=="CSV"&&(i+=` --dump-format=${s.output.dumpFormat}`),s.output.batch&&(i+=" --batch"),s.output.forms&&(i+=" --forms"),s.output.fresh&&(i+=" --fresh-queries"),s.authentication.authCred&&(i+=` --auth-type=${s.authentication.authType} --auth-cred="${s.authentication.authCred}"`),s.authentication.authFile&&(i+=` --auth-file="${s.authentication.authFile}"`),s.proxy.enabled&&s.proxy.url&&(i+=` --proxy="${s.proxy.url}"`),s.proxy.proxyCred&&(i+=` --proxy-cred="${s.proxy.proxyCred}"`),s.evasion.randomAgent&&(i+=" --random-agent"),s.evasion.mobile&&(i+=" --mobile"),s.evasion.checkWaf&&(i+=" --check-waf"),s.evasion.identifyWaf&&(i+=" --identify-waf"),s.evasion.skipHeuristics&&(i+=" --skip-heuristics"),s.evasion.smart&&(i+=" --smart"),s.evasion.textOnly&&(i+=" --text-only"),s.evasion.tor&&(i+=" --tor"),s.evasion.torPort!==9050&&(i+=` --tor-port=${s.evasion.torPort}`),s.evasion.checkTor&&(i+=" --check-tor"),s.filesystem.readFile&&(i+=` --file-read="${s.filesystem.readFile}"`),s.filesystem.writeFile&&(i+=` --file-write="${s.filesystem.writeFile}"`),s.filesystem.destFile&&(i+=` --file-dest="${s.filesystem.destFile}"`),s.takeover.osCmd&&(i+=` --os-cmd="${s.takeover.osCmd}"`),s.takeover.osPwn&&(i+=" --os-pwn"),s.takeover.osSmb&&(i+=" --os-smbrelay"),s.takeover.osBof&&(i+=" --os-bof"),s.takeover.priv&&(i+=" --priv-esc"),s.takeover.regRead&&(i+=" --reg-read"),s.takeover.regAdd&&(i+=" --reg-add"),s.takeover.regDel&&(i+=" --reg-del"),s.takeover.regKey&&(i+=` --reg-key="${s.takeover.regKey}"`),s.brute.commonTables&&(i+=" --common-tables"),s.brute.commonColumns&&(i+=" --common-columns"),s.brute.bruteForce&&(i+=" --brute-force"),s.customArgs&&(i+=` ${s.customArgs}`),i},f=n?.status==="running",L=t==="connected"&&(s.target.url.trim()||s.target.requestFile||s.target.bulkFile)&&Object.values(s.injection.techniques).some(i=>i)&&!f;return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"flex items-center justify-center w-10 h-10 rounded-lg bg-gradient-to-br from-red-500 to-pink-600",children:e.jsx(He,{className:"h-5 w-5 text-white"})}),e.jsxs("div",{children:[e.jsx("h2",{className:"text-2xl font-bold text-gray-900 dark:text-gray-100",children:"SQLMap Scanner"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-400",children:"Automatic SQL injection and database takeover tool"})]})]}),e.jsx(y,{className:"border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-900/10",children:e.jsx(w,{className:"pt-6",children:e.jsxs("div",{className:"flex items-start gap-3",children:[e.jsx(At,{className:"h-5 w-5 text-red-600 mt-0.5"}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-red-800 dark:text-red-200",children:"⚠️ AUTHORIZED TESTING ONLY"}),e.jsx("p",{className:"text-sm text-red-700 dark:text-red-300 mt-1",children:"SQLMap is a powerful penetration testing tool. Only use on systems you own or have explicit authorization to test. Unauthorized access to computer systems is illegal."})]})]})})}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[e.jsx(y,{children:e.jsx(w,{className:"pt-6",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400",children:"Status"}),e.jsx("p",{className:"text-2xl font-bold",children:f?"Testing":"Ready"})]}),e.jsx(ve,{className:B("h-8 w-8",f?"text-green-600":"text-gray-400")})]})})}),e.jsx(y,{children:e.jsx(w,{className:"pt-6",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400",children:"Techniques"}),e.jsx("p",{className:"text-2xl font-bold",children:Object.values(s.injection.techniques).filter(i=>i).length})]}),e.jsx(ht,{className:"h-8 w-8 text-blue-600"})]})})}),e.jsx(y,{children:e.jsx(w,{className:"pt-6",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400",children:"Risk Level"}),e.jsx("p",{className:"text-2xl font-bold",children:s.injection.risk})]}),e.jsx(ma,{className:B("h-8 w-8",s.injection.risk===1?"text-green-600":s.injection.risk===2?"text-yellow-600":"text-red-600")})]})})}),e.jsx(y,{children:e.jsx(w,{className:"pt-6",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400",children:"Progress"}),e.jsx("p",{className:"text-2xl font-bold",children:n?`${n.progress}%`:"0%"})]}),e.jsx(ts,{className:"h-8 w-8 text-purple-600"})]})})})]}),e.jsxs(y,{children:[e.jsx(T,{children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsxs(A,{className:"flex items-center gap-2",children:[e.jsx(He,{className:"h-5 w-5"}),"SQLMap Configuration"]}),e.jsx(F,{children:"Configure SQL injection testing parameters and database enumeration options"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsxs(v,{variant:"outline",size:"sm",onClick:()=>navigator.clipboard.writeText(E()),children:[e.jsx(je,{className:"h-4 w-4 mr-2"}),"Copy Command"]}),e.jsxs(v,{variant:"outline",size:"sm",onClick:()=>a(i=>({...i,target:{...i.target,url:"",data:"",cookies:"",headers:{}},injection:{...i.injection,parameters:[],tamper:[]},enumeration:{...i.enumeration,query:""},customArgs:""})),children:[e.jsx(qe,{className:"h-4 w-4 mr-2"}),"Reset"]}),f?e.jsxs(v,{variant:"destructive",size:"sm",onClick:R,children:[e.jsx(We,{className:"h-4 w-4 mr-2"}),"Stop Scan"]}):e.jsxs(v,{size:"sm",onClick:j,disabled:!L,children:[e.jsx(ae,{className:"h-4 w-4 mr-2"}),"Start Test"]})]})]})}),e.jsx(w,{children:e.jsxs(Ue,{value:c,onValueChange:p,children:[e.jsxs(Be,{className:"grid w-full grid-cols-4",children:[e.jsx(Q,{value:"configure",children:"Configure"}),e.jsx(Q,{value:"output",children:"Output"}),e.jsx(Q,{value:"results",children:"Results"}),e.jsx(Q,{value:"command",children:"Command"})]}),e.jsxs(J,{value:"configure",className:"space-y-6",children:[e.jsxs("div",{className:"space-y-4",children:[e.jsx("h3",{className:"text-lg font-semibold",children:"Quick Presets"}),e.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-3 gap-3",children:[e.jsxs(v,{variant:"outline",onClick:()=>g("basic_injection"),children:[e.jsx(Ge,{className:"h-4 w-4 mr-2"}),"Basic Injection"]}),e.jsxs(v,{variant:"outline",onClick:()=>g("comprehensive_enum"),children:[e.jsx(Fe,{className:"h-4 w-4 mr-2"}),"Comprehensive"]}),e.jsxs(v,{variant:"outline",onClick:()=>g("database_takeover"),children:[e.jsx(At,{className:"h-4 w-4 mr-2"}),"DB Takeover"]}),e.jsxs(v,{variant:"outline",onClick:()=>g("waf_bypass"),children:[e.jsx(fe,{className:"h-4 w-4 mr-2"}),"WAF Bypass"]}),e.jsxs(v,{variant:"outline",onClick:()=>g("time_based_blind"),children:[e.jsx(Ve,{className:"h-4 w-4 mr-2"}),"Time-based"]}),e.jsxs(v,{variant:"outline",onClick:()=>g("stealth_scan"),children:[e.jsx(ys,{className:"h-4 w-4 mr-2"}),"Stealth Mode"]})]})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsx("h3",{className:"text-lg font-semibold",children:"Target Configuration"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium",children:"Target URL"}),e.jsx(P,{value:s.target.url,onChange:i=>d("target.url",i.target.value),placeholder:"https://example.com/page.php?id=1"})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium",children:"HTTP Method"}),e.jsxs(pe,{value:s.target.method,onValueChange:i=>d("target.method",i),children:[e.jsx(oe,{children:e.jsx(de,{})}),e.jsxs(me,{children:[e.jsx(_,{value:"GET",children:"GET"}),e.jsx(_,{value:"POST",children:"POST"}),e.jsx(_,{value:"PUT",children:"PUT"}),e.jsx(_,{value:"DELETE",children:"DELETE"}),e.jsx(_,{value:"PATCH",children:"PATCH"}),e.jsx(_,{value:"HEAD",children:"HEAD"})]})]})]})]}),s.target.method!=="GET"&&e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium",children:"POST Data"}),e.jsx(Le,{value:s.target.data,onChange:i=>d("target.data",i.target.value),placeholder:"username=admin&password=test&id=1",className:"min-h-[80px]"})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium",children:"Cookies"}),e.jsx(P,{value:s.target.cookies,onChange:i=>d("target.cookies",i.target.value),placeholder:"PHPSESSID=abc123; token=xyz789"})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium",children:"User Agent"}),e.jsx(P,{value:s.target.userAgent,onChange:i=>d("target.userAgent",i.target.value),placeholder:"Mozilla/5.0 (Windows NT 10.0; Win64; x64)"})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium",children:"Custom Headers"}),e.jsx(Le,{value:Object.entries(s.target.headers).map(([i,u])=>`${i}: ${u}`).join(`
`),onChange:i=>{const u={};i.target.value.split(`
`).forEach(k=>{const[N,...D]=k.split(":");N&&D.length>0&&(u[N.trim()]=D.join(":").trim())}),d("target.headers",u)},placeholder:`Authorization: Bearer token
X-Custom-Header: value`,className:"min-h-[80px]"})]})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsx("h3",{className:"text-lg font-semibold",children:"SQL Injection Techniques"}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-3",children:Object.entries(aa).map(([i,u])=>e.jsx(y,{className:B("cursor-pointer transition-colors",s.injection.techniques[i]?"border-red-500 bg-red-50 dark:bg-red-950":""),onClick:()=>b(i),children:e.jsx(w,{className:"pt-6",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex-1",children:[e.jsx("div",{className:"font-medium",children:u.name}),e.jsx("div",{className:"text-sm text-gray-500",children:u.description})]}),e.jsx($,{variant:"outline",children:u.code})]})})},i))})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsx("h3",{className:"text-lg font-semibold",children:"Injection Parameters"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium",children:"Level (1-5)"}),e.jsxs(pe,{value:s.injection.level.toString(),onValueChange:i=>d("injection.level",parseInt(i)),children:[e.jsx(oe,{children:e.jsx(de,{})}),e.jsxs(me,{children:[e.jsx(_,{value:"1",children:"1 - Basic"}),e.jsx(_,{value:"2",children:"2 - Cookie testing"}),e.jsx(_,{value:"3",children:"3 - User-Agent testing"}),e.jsx(_,{value:"4",children:"4 - Referer testing"}),e.jsx(_,{value:"5",children:"5 - Host header testing"})]})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium",children:"Risk (1-3)"}),e.jsxs(pe,{value:s.injection.risk.toString(),onValueChange:i=>d("injection.risk",parseInt(i)),children:[e.jsx(oe,{children:e.jsx(de,{})}),e.jsxs(me,{children:[e.jsx(_,{value:"1",children:"1 - Safe"}),e.jsx(_,{value:"2",children:"2 - Heavy queries"}),e.jsx(_,{value:"3",children:"3 - OR-based queries"})]})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium",children:"Test Parameter"}),e.jsx(P,{value:s.injection.testParameter,onChange:i=>d("injection.testParameter",i.target.value),placeholder:"id,username,search"})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium",children:"DBMS"}),e.jsxs(pe,{value:s.injection.dbms,onValueChange:i=>d("injection.dbms",i),children:[e.jsx(oe,{children:e.jsx(de,{placeholder:"Auto-detect"})}),e.jsx(me,{children:ui.map(i=>e.jsx(_,{value:i,children:i},i))})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium",children:"Operating System"}),e.jsxs(pe,{value:s.injection.os,onValueChange:i=>d("injection.os",i),children:[e.jsx(oe,{children:e.jsx(de,{placeholder:"Auto-detect"})}),e.jsx(me,{children:hi.map(i=>e.jsx(_,{value:i,children:i},i))})]})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium",children:"Tamper Scripts"}),e.jsx("div",{className:"flex flex-wrap gap-1 mb-2",children:s.injection.tamper.map(i=>e.jsxs($,{variant:"secondary",className:"cursor-pointer",onClick:()=>d("injection.tamper",s.injection.tamper.filter(u=>u!==i)),children:[i," ×"]},i))}),e.jsxs(pe,{onValueChange:i=>{i&&!s.injection.tamper.includes(i)&&d("injection.tamper",[...s.injection.tamper,i])},children:[e.jsx(oe,{children:e.jsx(de,{placeholder:"Add tamper script"})}),e.jsx(me,{children:xi.map(i=>e.jsx(_,{value:i,children:i},i))})]})]})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsx("h3",{className:"text-lg font-semibold",children:"Database Enumeration"}),e.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("input",{type:"checkbox",id:"currentUser",checked:s.enumeration.currentUser,onChange:i=>d("enumeration.currentUser",i.target.checked),className:"rounded"}),e.jsx("label",{htmlFor:"currentUser",className:"text-sm font-medium",children:"Current User"})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("input",{type:"checkbox",id:"currentDb",checked:s.enumeration.currentDb,onChange:i=>d("enumeration.currentDb",i.target.checked),className:"rounded"}),e.jsx("label",{htmlFor:"currentDb",className:"text-sm font-medium",children:"Current DB"})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("input",{type:"checkbox",id:"isDba",checked:s.enumeration.isDba,onChange:i=>d("enumeration.isDba",i.target.checked),className:"rounded"}),e.jsx("label",{htmlFor:"isDba",className:"text-sm font-medium",children:"Is DBA"})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("input",{type:"checkbox",id:"users",checked:s.enumeration.users,onChange:i=>d("enumeration.users",i.target.checked),className:"rounded"}),e.jsx("label",{htmlFor:"users",className:"text-sm font-medium",children:"Users"})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("input",{type:"checkbox",id:"passwords",checked:s.enumeration.passwords,onChange:i=>d("enumeration.passwords",i.target.checked),className:"rounded"}),e.jsx("label",{htmlFor:"passwords",className:"text-sm font-medium",children:"Passwords"})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("input",{type:"checkbox",id:"privileges",checked:s.enumeration.privileges,onChange:i=>d("enumeration.privileges",i.target.checked),className:"rounded"}),e.jsx("label",{htmlFor:"privileges",className:"text-sm font-medium",children:"Privileges"})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("input",{type:"checkbox",id:"databases",checked:s.enumeration.databases,onChange:i=>d("enumeration.databases",i.target.checked),className:"rounded"}),e.jsx("label",{htmlFor:"databases",className:"text-sm font-medium",children:"Databases"})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("input",{type:"checkbox",id:"tables",checked:s.enumeration.tables,onChange:i=>d("enumeration.tables",i.target.checked),className:"rounded"}),e.jsx("label",{htmlFor:"tables",className:"text-sm font-medium",children:"Tables"})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("input",{type:"checkbox",id:"columns",checked:s.enumeration.columns,onChange:i=>d("enumeration.columns",i.target.checked),className:"rounded"}),e.jsx("label",{htmlFor:"columns",className:"text-sm font-medium",children:"Columns"})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("input",{type:"checkbox",id:"dump",checked:s.enumeration.dump,onChange:i=>d("enumeration.dump",i.target.checked),className:"rounded"}),e.jsx("label",{htmlFor:"dump",className:"text-sm font-medium",children:"Dump Data"})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("input",{type:"checkbox",id:"sqlShell",checked:s.enumeration.sqlShell,onChange:i=>d("enumeration.sqlShell",i.target.checked),className:"rounded"}),e.jsx("label",{htmlFor:"sqlShell",className:"text-sm font-medium",children:"SQL Shell"})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("input",{type:"checkbox",id:"osShell",checked:s.enumeration.osShell,onChange:i=>d("enumeration.osShell",i.target.checked),className:"rounded"}),e.jsx("label",{htmlFor:"osShell",className:"text-sm font-medium",children:"OS Shell"})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium",children:"Custom SQL Query"}),e.jsx(Le,{value:s.enumeration.query,onChange:i=>d("enumeration.query",i.target.value),placeholder:"SELECT * FROM users WHERE id=1",className:"min-h-[80px]"})]})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsx("h3",{className:"text-lg font-semibold",children:"Advanced Options"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium",children:"Threads"}),e.jsx(P,{type:"number",value:s.advanced.threads,onChange:i=>d("advanced.threads",parseInt(i.target.value)||1),min:"1",max:"10"})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium",children:"Delay (s)"}),e.jsx(P,{type:"number",value:s.advanced.delay,onChange:i=>d("advanced.delay",parseInt(i.target.value)||0),min:"0",max:"30"})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium",children:"Timeout (s)"}),e.jsx(P,{type:"number",value:s.advanced.timeout,onChange:i=>d("advanced.timeout",parseInt(i.target.value)||30),min:"1",max:"300"})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium",children:"Retries"}),e.jsx(P,{type:"number",value:s.advanced.retries,onChange:i=>d("advanced.retries",parseInt(i.target.value)||3),min:"0",max:"10"})]})]}),e.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("input",{type:"checkbox",id:"randomize",checked:s.advanced.randomize,onChange:i=>d("advanced.randomize",i.target.checked),className:"rounded"}),e.jsx("label",{htmlFor:"randomize",className:"text-sm font-medium",children:"Randomize"})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("input",{type:"checkbox",id:"skipWaf",checked:s.advanced.skipWaf,onChange:i=>d("advanced.skipWaf",i.target.checked),className:"rounded"}),e.jsx("label",{htmlFor:"skipWaf",className:"text-sm font-medium",children:"Skip WAF"})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("input",{type:"checkbox",id:"randomAgent",checked:s.evasion.randomAgent,onChange:i=>d("evasion.randomAgent",i.target.checked),className:"rounded"}),e.jsx("label",{htmlFor:"randomAgent",className:"text-sm font-medium",children:"Random Agent"})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("input",{type:"checkbox",id:"batch",checked:s.output.batch,onChange:i=>d("output.batch",i.target.checked),className:"rounded"}),e.jsx("label",{htmlFor:"batch",className:"text-sm font-medium",children:"Batch Mode"})]})]})]})]}),e.jsxs(J,{value:"output",className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h3",{className:"text-lg font-semibold",children:"Real-time Output"}),e.jsxs("div",{className:"flex items-center gap-2",children:[f&&e.jsxs($,{variant:"secondary",className:"animate-pulse",children:[e.jsx(ve,{className:"h-3 w-3 mr-1"}),"Testing in progress..."]}),e.jsxs(v,{variant:"outline",size:"sm",children:[e.jsx(ke,{className:"h-4 w-4 mr-2"}),"Export Output"]})]})]}),n?.progress!==void 0&&e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex justify-between text-sm",children:[e.jsx("span",{children:"Progress"}),e.jsxs("span",{children:[n.progress,"%"]})]}),e.jsx(Ae,{value:n.progress,className:"w-full"})]}),e.jsx(y,{children:e.jsx(w,{className:"p-4",children:e.jsx("div",{className:"bg-gray-900 dark:bg-gray-800 rounded-lg p-4 min-h-[400px] font-mono text-sm overflow-auto",children:e.jsx("div",{className:"text-green-400",children:n?.output||"SQLMap output will appear here when testing starts..."})})})})]}),e.jsxs(J,{value:"results",className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h3",{className:"text-lg font-semibold",children:"Injection Results"}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsxs(v,{variant:"outline",size:"sm",children:[e.jsx(ke,{className:"h-4 w-4 mr-2"}),"Export Results"]}),e.jsxs(v,{variant:"outline",size:"sm",children:[e.jsx(Fs,{className:"h-4 w-4 mr-2"}),"Filter Results"]})]})]}),n?.results?e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[e.jsx(y,{children:e.jsx(w,{className:"pt-6",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400",children:"Vulnerabilities"}),e.jsx("p",{className:"text-2xl font-bold text-red-600",children:n.results.vulnerabilities||0})]}),e.jsx(Es,{className:"h-8 w-8 text-red-600"})]})})}),e.jsx(y,{children:e.jsx(w,{className:"pt-6",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400",children:"Databases"}),e.jsx("p",{className:"text-2xl font-bold",children:n.results.databases||0})]}),e.jsx(He,{className:"h-8 w-8 text-blue-600"})]})})}),e.jsx(y,{children:e.jsx(w,{className:"pt-6",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400",children:"Tables"}),e.jsx("p",{className:"text-2xl font-bold",children:n.results.tables||0})]}),e.jsx(Nn,{className:"h-8 w-8 text-green-600"})]})})}),e.jsx(y,{children:e.jsx(w,{className:"pt-6",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400",children:"Extracted"}),e.jsx("p",{className:"text-2xl font-bold",children:n.results.extracted||0})]}),e.jsx(yn,{className:"h-8 w-8 text-purple-600"})]})})})]}),e.jsx(y,{children:e.jsx(w,{className:"p-0",children:e.jsx("div",{className:"overflow-x-auto",children:e.jsxs("table",{className:"w-full",children:[e.jsx("thead",{className:"border-b",children:e.jsxs("tr",{className:"text-left",children:[e.jsx("th",{className:"p-4 font-medium",children:"Parameter"}),e.jsx("th",{className:"p-4 font-medium",children:"Technique"}),e.jsx("th",{className:"p-4 font-medium",children:"Payload"}),e.jsx("th",{className:"p-4 font-medium",children:"DBMS"}),e.jsx("th",{className:"p-4 font-medium",children:"Status"})]})}),e.jsx("tbody",{children:n.results.findings?.map((i,u)=>e.jsxs("tr",{className:"border-b hover:bg-gray-50 dark:hover:bg-gray-800",children:[e.jsx("td",{className:"p-4 font-medium",children:i.parameter}),e.jsx("td",{className:"p-4",children:e.jsx($,{variant:"outline",children:i.technique})}),e.jsx("td",{className:"p-4 font-mono text-sm max-w-xs truncate",children:i.payload}),e.jsx("td",{className:"p-4",children:i.dbms}),e.jsx("td",{className:"p-4",children:e.jsx($,{variant:i.vulnerable?"destructive":"secondary",children:i.vulnerable?"Vulnerable":"Safe"})})]},u))})]})})})})]}):e.jsx(y,{children:e.jsxs(w,{className:"p-8 text-center",children:[e.jsx(He,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),e.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2",children:"No Results Yet"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-400",children:"Start a SQL injection test to see results here. SQLMap will identify injection points and extract database information."})]})})]}),e.jsxs(J,{value:"command",className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h3",{className:"text-lg font-semibold",children:"Generated Command"}),e.jsxs(v,{variant:"outline",size:"sm",onClick:()=>navigator.clipboard.writeText(E()),children:[e.jsx(je,{className:"h-4 w-4 mr-2"}),"Copy Command"]})]}),e.jsx(y,{children:e.jsx(w,{className:"p-4",children:e.jsx("div",{className:"bg-gray-900 dark:bg-gray-800 rounded-lg p-4 overflow-x-auto",children:e.jsx("code",{className:"text-green-400 whitespace-pre-wrap break-all",children:E()})})})}),e.jsxs("div",{className:"space-y-4",children:[e.jsx("h4",{className:"font-semibold",children:"Command Explanation"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm",children:[e.jsxs("div",{children:[e.jsx("h5",{className:"font-medium mb-2",children:"Target Options"}),e.jsxs("ul",{className:"space-y-1 text-gray-600 dark:text-gray-400",children:[e.jsxs("li",{children:["• ",e.jsx("code",{children:"-u"}),": Target URL"]}),e.jsxs("li",{children:["• ",e.jsx("code",{children:"--data"}),": POST data"]}),e.jsxs("li",{children:["• ",e.jsx("code",{children:"--cookie"}),": HTTP cookies"]}),e.jsxs("li",{children:["• ",e.jsx("code",{children:"--user-agent"}),": User agent string"]})]})]}),e.jsxs("div",{children:[e.jsx("h5",{className:"font-medium mb-2",children:"Injection Control"}),e.jsxs("ul",{className:"space-y-1 text-gray-600 dark:text-gray-400",children:[e.jsxs("li",{children:["• ",e.jsx("code",{children:"--technique"}),": SQL injection techniques"]}),e.jsxs("li",{children:["• ",e.jsx("code",{children:"--level"}),": Tests to perform (1-5)"]}),e.jsxs("li",{children:["• ",e.jsx("code",{children:"--risk"}),": Risk of tests (1-3)"]}),e.jsxs("li",{children:["• ",e.jsx("code",{children:"-p"}),": Testable parameters"]})]})]}),e.jsxs("div",{children:[e.jsx("h5",{className:"font-medium mb-2",children:"Enumeration"}),e.jsxs("ul",{className:"space-y-1 text-gray-600 dark:text-gray-400",children:[e.jsxs("li",{children:["• ",e.jsx("code",{children:"--current-user"}),": Current database user"]}),e.jsxs("li",{children:["• ",e.jsx("code",{children:"--dbs"}),": Enumerate databases"]}),e.jsxs("li",{children:["• ",e.jsx("code",{children:"--tables"}),": Enumerate tables"]}),e.jsxs("li",{children:["• ",e.jsx("code",{children:"--dump"}),": Dump table data"]})]})]}),e.jsxs("div",{children:[e.jsx("h5",{className:"font-medium mb-2",children:"Advanced Features"}),e.jsxs("ul",{className:"space-y-1 text-gray-600 dark:text-gray-400",children:[e.jsxs("li",{children:["• ",e.jsx("code",{children:"--os-shell"}),": OS command shell"]}),e.jsxs("li",{children:["• ",e.jsx("code",{children:"--sql-shell"}),": SQL command shell"]}),e.jsxs("li",{children:["• ",e.jsx("code",{children:"--tamper"}),": Tamper injection payloads"]}),e.jsxs("li",{children:["• ",e.jsx("code",{children:"--batch"}),": Never ask for user input"]})]})]})]})]})]})]})})]})]})}function fi(){const t=ra();return e.jsxs("div",{className:"space-y-6",children:[e.jsx("div",{className:"flex items-center justify-between",children:e.jsxs("div",{children:[e.jsx("h1",{className:"text-3xl font-bold tracking-tight",children:"Vulnerability Assessment"}),e.jsx("p",{className:"text-muted-foreground",children:"Comprehensive vulnerability scanning and analysis"})]})}),e.jsxs("div",{className:"grid gap-6 md:grid-cols-2",children:[e.jsxs(y,{children:[e.jsxs(T,{children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx(ee,{className:"h-5 w-5"}),e.jsx(A,{children:"Nuclei Scanner"})]}),e.jsx(F,{children:"Fast vulnerability scanner with custom templates"})]}),e.jsx(w,{children:e.jsxs("div",{className:"space-y-4",children:[e.jsx(P,{placeholder:"Target URL or IP"}),e.jsxs("div",{className:"flex gap-2",children:[e.jsxs(v,{onClick:()=>{console.log("🚀 Scan button clicked - navigating to Nuclei"),t("nuclei")},children:[e.jsx(ae,{className:"mr-2 h-4 w-4"}),"Scan"]}),e.jsxs(v,{variant:"outline",onClick:()=>{console.log("🔧 Configure button clicked - navigating to Nuclei"),t("nuclei")},children:[e.jsx(Y,{className:"mr-2 h-4 w-4"}),"Configure"]})]})]})})]}),e.jsxs(y,{children:[e.jsxs(T,{children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx(Fe,{className:"h-5 w-5"}),e.jsx(A,{children:"Custom Vulnerability Scan"})]}),e.jsx(F,{children:"Targeted vulnerability assessment"})]}),e.jsx(w,{children:e.jsxs("div",{className:"space-y-4",children:[e.jsx(P,{placeholder:"Target system"}),e.jsxs("div",{className:"flex gap-2",children:[e.jsxs(v,{onClick:()=>t("/tools/vulnerability-assessment/nuclei"),children:[e.jsx(ae,{className:"mr-2 h-4 w-4"}),"Start Assessment"]}),e.jsxs(v,{variant:"outline",onClick:()=>t("/tools/vulnerability-assessment/nuclei"),children:[e.jsx(Y,{className:"mr-2 h-4 w-4"}),"Configure"]})]})]})})]})]})]})}function gi(){return e.jsxs(mt,{children:[e.jsx(te,{index:!0,element:e.jsx(fi,{})}),e.jsx(te,{path:"nuclei/*",element:e.jsx(mi,{})}),e.jsx(te,{path:"sqlmap/*",element:e.jsx(pi,{})})]})}function ji(){return e.jsxs("div",{className:"space-y-6",children:[e.jsx("div",{className:"flex items-center justify-between",children:e.jsxs("div",{children:[e.jsx("h1",{className:"text-3xl font-bold tracking-tight",children:"Password Security Tools"}),e.jsx("p",{className:"text-muted-foreground",children:"Password testing and analysis tools"})]})}),e.jsxs("div",{className:"grid gap-6 md:grid-cols-2",children:[e.jsxs(y,{children:[e.jsxs(T,{children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx(fs,{className:"h-5 w-5"}),e.jsx(A,{children:"Password Cracking"})]}),e.jsx(F,{children:"Advanced password recovery tools"})]}),e.jsx(w,{children:e.jsxs("div",{className:"space-y-4",children:[e.jsx(P,{placeholder:"Target hash or file"}),e.jsxs("div",{className:"flex gap-2",children:[e.jsxs(v,{children:[e.jsx(ae,{className:"mr-2 h-4 w-4"}),"Start"]}),e.jsxs(v,{variant:"outline",children:[e.jsx(Y,{className:"mr-2 h-4 w-4"}),"Configure"]})]})]})})]}),e.jsxs(y,{children:[e.jsxs(T,{children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx(fe,{className:"h-5 w-5"}),e.jsx(A,{children:"Password Policy Analysis"})]}),e.jsx(F,{children:"Analyze password strength and policies"})]}),e.jsx(w,{children:e.jsxs("div",{className:"space-y-4",children:[e.jsx(P,{placeholder:"Enter password or policy file"}),e.jsxs("div",{className:"flex gap-2",children:[e.jsxs(v,{children:[e.jsx(ae,{className:"mr-2 h-4 w-4"}),"Analyze"]}),e.jsxs(v,{variant:"outline",children:[e.jsx(Y,{className:"mr-2 h-4 w-4"}),"Configure"]})]})]})})]})]})]})}function vi(){return e.jsxs("div",{className:"space-y-6",children:[e.jsx("div",{className:"flex items-center justify-between",children:e.jsxs("div",{children:[e.jsx("h1",{className:"text-3xl font-bold tracking-tight",children:"SSL/TLS Testing Tools"}),e.jsx("p",{className:"text-muted-foreground",children:"SSL certificate and configuration testing"})]})}),e.jsxs("div",{className:"grid gap-6 md:grid-cols-2",children:[e.jsxs(y,{children:[e.jsxs(T,{children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx(wn,{className:"h-5 w-5"}),e.jsx(A,{children:"SSL Certificate Analysis"})]}),e.jsx(F,{children:"Analyze SSL certificates and chains"})]}),e.jsx(w,{children:e.jsxs("div",{className:"space-y-4",children:[e.jsx(P,{placeholder:"example.com:443"}),e.jsxs("div",{className:"flex gap-2",children:[e.jsxs(v,{children:[e.jsx(ae,{className:"mr-2 h-4 w-4"}),"Test SSL"]}),e.jsxs(v,{variant:"outline",children:[e.jsx(Y,{className:"mr-2 h-4 w-4"}),"Configure"]})]})]})})]}),e.jsxs(y,{children:[e.jsxs(T,{children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx(Ls,{className:"h-5 w-5"}),e.jsx(A,{children:"TLS Configuration Check"})]}),e.jsx(F,{children:"Test TLS protocols and cipher suites"})]}),e.jsx(w,{children:e.jsxs("div",{className:"space-y-4",children:[e.jsx(P,{placeholder:"Target hostname"}),e.jsxs("div",{className:"flex gap-2",children:[e.jsxs(v,{children:[e.jsx(ae,{className:"mr-2 h-4 w-4"}),"Test TLS"]}),e.jsxs(v,{variant:"outline",children:[e.jsx(Y,{className:"mr-2 h-4 w-4"}),"Configure"]})]})]})})]})]})]})}function bi(){return e.jsxs("div",{className:"space-y-6",children:[e.jsx("div",{className:"flex items-center justify-between",children:e.jsxs("div",{children:[e.jsx("h1",{className:"text-3xl font-bold tracking-tight",children:"Exploitation Tools"}),e.jsx("p",{className:"text-muted-foreground",children:"Advanced exploitation frameworks and tools"})]})}),e.jsxs("div",{className:"grid gap-6 md:grid-cols-2",children:[e.jsxs(y,{children:[e.jsxs(T,{children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx(ue,{className:"h-5 w-5"}),e.jsx(A,{children:"Exploit Framework"})]}),e.jsx(F,{children:"Advanced exploitation framework integration"})]}),e.jsx(w,{children:e.jsxs("div",{className:"space-y-4",children:[e.jsx(P,{placeholder:"Target system"}),e.jsxs("div",{className:"flex gap-2",children:[e.jsxs(v,{children:[e.jsx(ae,{className:"mr-2 h-4 w-4"}),"Launch"]}),e.jsxs(v,{variant:"outline",children:[e.jsx(Y,{className:"mr-2 h-4 w-4"}),"Configure"]})]})]})})]}),e.jsxs(y,{children:[e.jsxs(T,{children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx(ht,{className:"h-5 w-5"}),e.jsx(A,{children:"Payload Generator"})]}),e.jsx(F,{children:"Generate custom exploit payloads"})]}),e.jsx(w,{children:e.jsxs("div",{className:"space-y-4",children:[e.jsx(P,{placeholder:"Target architecture"}),e.jsxs("div",{className:"flex gap-2",children:[e.jsxs(v,{children:[e.jsx(ae,{className:"mr-2 h-4 w-4"}),"Generate"]}),e.jsxs(v,{variant:"outline",children:[e.jsx(Y,{className:"mr-2 h-4 w-4"}),"Configure"]})]})]})})]})]})]})}function Ni(){const t=[{name:"Creative Exploits",status:"online",confidence:92,tasks:247},{name:"Multi-Stage Orchestrator",status:"online",confidence:88,tasks:43},{name:"Behavioral Analysis",status:"online",confidence:85,tasks:89},{name:"AI Proxy",status:"online",confidence:90,tasks:156},{name:"Evasion Generator",status:"online",confidence:87,tasks:78},{name:"Adaptive Modifier",status:"online",confidence:89,tasks:134}];return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-3xl font-bold tracking-tight",children:"Ferrari AI Platform"}),e.jsx("p",{className:"text-muted-foreground",children:"Advanced AI-powered security testing capabilities"})]}),e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsxs($,{variant:"outline",className:"text-green-500",children:[e.jsx(Ge,{className:"mr-1 h-3 w-3"}),"All Systems Online"]}),e.jsxs(v,{children:[e.jsx(Xe,{className:"mr-2 h-4 w-4"}),"New AI Task"]})]})]}),e.jsxs("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-4",children:[e.jsxs(y,{children:[e.jsxs(T,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[e.jsx(A,{className:"text-sm font-medium",children:"AI Services"}),e.jsx(Xe,{className:"h-4 w-4 text-muted-foreground"})]}),e.jsxs(w,{children:[e.jsx("div",{className:"text-2xl font-bold",children:"6"}),e.jsx("p",{className:"text-xs text-muted-foreground",children:"All online and ready"})]})]}),e.jsxs(y,{children:[e.jsxs(T,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[e.jsx(A,{className:"text-sm font-medium",children:"AI Confidence"}),e.jsx(fe,{className:"h-4 w-4 text-muted-foreground"})]}),e.jsxs(w,{children:[e.jsx("div",{className:"text-2xl font-bold",children:"88.5%"}),e.jsx("p",{className:"text-xs text-muted-foreground",children:"Average across all services"})]})]}),e.jsxs(y,{children:[e.jsxs(T,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[e.jsx(A,{className:"text-sm font-medium",children:"Tasks Completed"}),e.jsx(Ns,{className:"h-4 w-4 text-muted-foreground"})]}),e.jsxs(w,{children:[e.jsx("div",{className:"text-2xl font-bold",children:"747"}),e.jsx("p",{className:"text-xs text-muted-foreground",children:"This month"})]})]}),e.jsxs(y,{children:[e.jsxs(T,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[e.jsx(A,{className:"text-sm font-medium",children:"Success Rate"}),e.jsx(be,{className:"h-4 w-4 text-muted-foreground"})]}),e.jsxs(w,{children:[e.jsx("div",{className:"text-2xl font-bold",children:"94.2%"}),e.jsx("p",{className:"text-xs text-muted-foreground",children:"AI task success rate"})]})]})]}),e.jsx("div",{className:"grid gap-6 md:grid-cols-2 lg:grid-cols-3",children:t.map((s,a)=>e.jsxs(y,{className:"hover:shadow-lg transition-shadow",children:[e.jsx(T,{children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx(A,{className:"text-lg",children:s.name}),e.jsxs($,{variant:"outline",className:"text-green-500",children:[e.jsx(be,{className:"mr-1 h-3 w-3"}),s.status]})]})}),e.jsxs(w,{className:"space-y-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm font-medium",children:"AI Confidence"}),e.jsxs("span",{className:"text-sm text-muted-foreground",children:[s.confidence,"%"]})]}),e.jsx(Ae,{value:s.confidence,className:"h-2"})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm font-medium",children:"Tasks Completed"}),e.jsx("span",{className:"text-sm text-muted-foreground",children:s.tasks})]}),e.jsxs("div",{className:"flex gap-2 pt-2",children:[e.jsx(v,{size:"sm",className:"flex-1",children:"Execute"}),e.jsx(v,{size:"sm",variant:"outline",children:"Configure"})]})]})]},a))}),e.jsxs(y,{children:[e.jsxs(T,{children:[e.jsx(A,{children:"Real-time AI Activity"}),e.jsx(F,{children:"Live Ferrari AI processing status"})]}),e.jsx(w,{children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center gap-4 p-3 rounded-lg border",children:[e.jsx("div",{className:"h-8 w-8 rounded-full bg-blue-100 dark:bg-blue-900/20 flex items-center justify-center",children:e.jsx(Xe,{className:"h-4 w-4 text-blue-600"})}),e.jsxs("div",{className:"flex-1",children:[e.jsx("p",{className:"text-sm font-medium",children:"Creative Exploit Generation"}),e.jsx("p",{className:"text-xs text-muted-foreground",children:"Generating novel payloads for web-app.example.com"})]}),e.jsx($,{variant:"secondary",children:"Running"})]}),e.jsxs("div",{className:"flex items-center gap-4 p-3 rounded-lg border",children:[e.jsx("div",{className:"h-8 w-8 rounded-full bg-green-100 dark:bg-green-900/20 flex items-center justify-center",children:e.jsx(Oe,{className:"h-4 w-4 text-green-600"})}),e.jsxs("div",{className:"flex-1",children:[e.jsx("p",{className:"text-sm font-medium",children:"Multi-Stage Attack Analysis"}),e.jsx("p",{className:"text-xs text-muted-foreground",children:"Completed attack chain for ***********/24"})]}),e.jsx($,{variant:"secondary",children:"Completed"})]}),e.jsxs("div",{className:"flex items-center gap-4 p-3 rounded-lg border",children:[e.jsx("div",{className:"h-8 w-8 rounded-full bg-purple-100 dark:bg-purple-900/20 flex items-center justify-center",children:e.jsx(ve,{className:"h-4 w-4 text-purple-600"})}),e.jsxs("div",{className:"flex-1",children:[e.jsx("p",{className:"text-sm font-medium",children:"Behavioral Pattern Detection"}),e.jsx("p",{className:"text-xs text-muted-foreground",children:"Analyzing API traffic patterns"})]}),e.jsx($,{variant:"secondary",children:"Running"})]})]})})]})]})}function yi(){const[t]=x.useState([{id:"1",name:"Web Application Takeover",stages:["Reconnaissance","Initial Access","Privilege Escalation","Persistence"],status:"completed",confidence:89,duration:"12m 34s"},{id:"2",name:"Network Lateral Movement",stages:["Discovery","Credential Access","Lateral Movement","Collection"],status:"running",confidence:76,currentStage:2},{id:"3",name:"Database Exfiltration",stages:["SQL Injection","Database Enumeration","Data Export","Cover Tracks"],status:"pending",confidence:92}]),s=[{tactic:"Initial Access",techniques:12,enabled:!0},{tactic:"Execution",techniques:8,enabled:!0},{tactic:"Persistence",techniques:15,enabled:!0},{tactic:"Privilege Escalation",techniques:10,enabled:!0},{tactic:"Defense Evasion",techniques:20,enabled:!1},{tactic:"Credential Access",techniques:9,enabled:!0}];return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-3xl font-bold tracking-tight",children:"Ferrari Attack Orchestrator"}),e.jsx("p",{className:"text-muted-foreground",children:"Multi-stage attack chain planning and execution"})]}),e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsxs($,{variant:"outline",className:"text-green-500",children:[e.jsx(Oe,{className:"mr-1 h-3 w-3"}),"Orchestrator Online"]}),e.jsxs(v,{children:[e.jsx(Rt,{className:"mr-2 h-4 w-4"}),"New Attack Chain"]})]})]}),e.jsxs("div",{className:"grid gap-6 md:grid-cols-2",children:[e.jsxs(y,{children:[e.jsxs(T,{children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx(Rt,{className:"h-5 w-5"}),e.jsx(A,{children:"Chain Builder"})]}),e.jsx(F,{children:"Design custom attack chains"})]}),e.jsx(w,{children:e.jsxs("div",{className:"space-y-4",children:[e.jsx(P,{placeholder:"Attack chain name"}),e.jsx(Le,{placeholder:"Describe the attack objectives...",rows:3}),e.jsxs("select",{className:"w-full px-3 py-2 text-sm border rounded-md",children:[e.jsx("option",{children:"Select framework"}),e.jsx("option",{children:"MITRE ATT&CK"}),e.jsx("option",{children:"Cyber Kill Chain"}),e.jsx("option",{children:"Custom"})]}),e.jsxs("div",{className:"flex gap-2",children:[e.jsxs(v,{children:[e.jsx(ae,{className:"mr-2 h-4 w-4"}),"Build Chain"]}),e.jsxs(v,{variant:"outline",children:[e.jsx(Y,{className:"mr-2 h-4 w-4"}),"Configure"]})]})]})})]}),e.jsxs(y,{children:[e.jsxs(T,{children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx(fe,{className:"h-5 w-5"}),e.jsx(A,{children:"MITRE ATT&CK Matrix"})]}),e.jsx(F,{children:"Available tactics and techniques"})]}),e.jsx(w,{children:e.jsx("div",{className:"space-y-2",children:s.map(a=>e.jsxs("div",{className:"flex items-center justify-between p-2 rounded-lg border",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("input",{type:"checkbox",checked:a.enabled,readOnly:!0}),e.jsx("span",{className:"text-sm font-medium",children:a.tactic})]}),e.jsxs($,{variant:"outline",children:[a.techniques," techniques"]})]},a.tactic))})})]})]}),e.jsxs(y,{children:[e.jsxs(T,{children:[e.jsx(A,{children:"Attack Chains"}),e.jsx(F,{children:"Managed multi-stage attack scenarios"})]}),e.jsx(w,{children:e.jsx("div",{className:"space-y-4",children:t.map(a=>e.jsxs("div",{className:"p-4 rounded-lg border",children:[e.jsxs("div",{className:"flex items-start justify-between mb-3",children:[e.jsxs("div",{children:[e.jsx("h4",{className:"text-sm font-medium",children:a.name}),e.jsxs("p",{className:"text-xs text-muted-foreground mt-1",children:[a.stages.length," stages • ",a.confidence,"% confidence"]})]}),e.jsxs($,{variant:"outline",className:a.status==="completed"?"text-green-500":a.status==="running"?"text-blue-500":"text-gray-500",children:[a.status==="completed"&&e.jsx(be,{className:"mr-1 h-3 w-3"}),a.status==="running"&&e.jsx(Ve,{className:"mr-1 h-3 w-3"}),a.status==="pending"&&e.jsx(ee,{className:"mr-1 h-3 w-3"}),a.status]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("div",{className:"flex items-center gap-2",children:a.stages.map((n,r)=>e.jsxs(x.Fragment,{children:[e.jsx("div",{className:`flex items-center justify-center h-6 px-2 rounded text-xs font-medium ${a.status==="completed"||a.status==="running"&&a.currentStage&&r<a.currentStage?"bg-green-100 text-green-700 dark:bg-green-900/20 dark:text-green-300":a.status==="running"&&a.currentStage===r?"bg-blue-100 text-blue-700 dark:bg-blue-900/20 dark:text-blue-300":"bg-gray-100 text-gray-700 dark:bg-gray-900/20 dark:text-gray-300"}`,children:n}),r<a.stages.length-1&&e.jsx("div",{className:"w-4 h-0.5 bg-gray-300 dark:bg-gray-700"})]},r))}),a.duration&&e.jsxs("p",{className:"text-xs text-muted-foreground",children:["Duration: ",a.duration]})]}),e.jsxs("div",{className:"flex gap-2 mt-3",children:[a.status==="pending"&&e.jsxs(v,{size:"sm",children:[e.jsx(ae,{className:"mr-1 h-3 w-3"}),"Execute"]}),a.status==="running"&&e.jsxs(v,{size:"sm",variant:"outline",children:[e.jsx(Ve,{className:"mr-1 h-3 w-3"}),"View Progress"]}),a.status==="completed"&&e.jsx(v,{size:"sm",variant:"outline",children:"View Report"}),e.jsxs(v,{size:"sm",variant:"outline",children:[e.jsx(Y,{className:"mr-1 h-3 w-3"}),"Configure"]})]})]},a.id))})})]})]})}function wi(){const[t]=x.useState([{id:"1",type:"SQL Injection",confidence:94,payload:"UNION SELECT..."},{id:"2",type:"XSS Polyglot",confidence:87,payload:"<script>alert(1)<\/script>"},{id:"3",type:"Command Injection",confidence:91,payload:"; cat /etc/passwd"}]);return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-3xl font-bold tracking-tight",children:"Ferrari Creative Exploits"}),e.jsx("p",{className:"text-muted-foreground",children:"AI-powered novel attack vector generation"})]}),e.jsx("div",{className:"flex items-center gap-3",children:e.jsxs($,{variant:"outline",className:"text-green-500",children:[e.jsx(Xe,{className:"mr-1 h-3 w-3"}),"AI Engine Ready"]})})]}),e.jsxs("div",{className:"grid gap-6 md:grid-cols-2",children:[e.jsxs(y,{children:[e.jsxs(T,{children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx(kn,{className:"h-5 w-5"}),e.jsx(A,{children:"Exploit Generator"})]}),e.jsx(F,{children:"Generate novel exploit payloads using AI"})]}),e.jsx(w,{children:e.jsxs("div",{className:"space-y-4",children:[e.jsx(P,{placeholder:"Target URL or system"}),e.jsx(Le,{placeholder:"Describe the target vulnerability or context...",rows:3}),e.jsxs("div",{className:"flex gap-2",children:[e.jsxs(v,{children:[e.jsx(ae,{className:"mr-2 h-4 w-4"}),"Generate"]}),e.jsxs(v,{variant:"outline",children:[e.jsx(Y,{className:"mr-2 h-4 w-4"}),"Configure"]})]})]})})]}),e.jsxs(y,{children:[e.jsxs(T,{children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx(ht,{className:"h-5 w-5"}),e.jsx(A,{children:"Polyglot Payloads"})]}),e.jsx(F,{children:"Multi-context exploit combinations"})]}),e.jsx(w,{children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("select",{className:"w-full px-3 py-2 text-sm border rounded-md",children:[e.jsx("option",{children:"SQL + XSS Combination"}),e.jsx("option",{children:"Command + SQL Injection"}),e.jsx("option",{children:"LDAP + NoSQL Injection"})]}),e.jsxs("div",{className:"flex gap-2",children:[e.jsxs(v,{children:[e.jsx(ae,{className:"mr-2 h-4 w-4"}),"Generate Polyglot"]}),e.jsxs(v,{variant:"outline",children:[e.jsx(Y,{className:"mr-2 h-4 w-4"}),"Configure"]})]})]})})]})]}),e.jsxs(y,{children:[e.jsxs(T,{children:[e.jsx(A,{children:"Generated Exploits"}),e.jsx(F,{children:"Recently generated creative exploit payloads"})]}),e.jsx(w,{children:e.jsx("div",{className:"space-y-4",children:t.map(s=>e.jsxs("div",{className:"flex items-center gap-4 p-4 rounded-lg border",children:[e.jsx("div",{className:"h-10 w-10 rounded-lg bg-primary/10 flex items-center justify-center",children:e.jsx(Ge,{className:"h-4 w-4"})}),e.jsxs("div",{className:"flex-1 space-y-2",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("p",{className:"text-sm font-medium",children:s.type}),e.jsxs($,{variant:"outline",className:"text-green-500",children:[e.jsx(be,{className:"mr-1 h-3 w-3"}),s.confidence,"% confidence"]})]}),e.jsx("div",{className:"p-2 bg-muted rounded text-xs font-mono",children:s.payload})]}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx(v,{size:"sm",variant:"outline",children:"Copy"}),e.jsx(v,{size:"sm",variant:"outline",children:"Test"})]})]},s.id))})})]})]})}function ki(){const[t]=x.useState([{id:"1",pattern:"Login Pattern Anomaly",confidence:92,severity:"high",description:"Unusual login timing detected"},{id:"2",pattern:"API Call Frequency",confidence:85,severity:"medium",description:"Elevated API request rate"},{id:"3",pattern:"Data Access Pattern",confidence:78,severity:"low",description:"Abnormal data retrieval pattern"}]);return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-3xl font-bold tracking-tight",children:"Ferrari Behavioral Analysis"}),e.jsx("p",{className:"text-muted-foreground",children:"AI-powered behavioral pattern recognition and anomaly detection"})]}),e.jsx("div",{className:"flex items-center gap-3",children:e.jsxs($,{variant:"outline",className:"text-green-500",children:[e.jsx(Xe,{className:"mr-1 h-3 w-3"}),"AI Engine Online"]})})]}),e.jsxs("div",{className:"grid gap-6 md:grid-cols-2",children:[e.jsxs(y,{children:[e.jsxs(T,{children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx(Xe,{className:"h-5 w-5"}),e.jsx(A,{children:"Pattern Analysis"})]}),e.jsx(F,{children:"Analyze behavioral patterns for anomalies"})]}),e.jsx(w,{children:e.jsxs("div",{className:"space-y-4",children:[e.jsx(P,{placeholder:"Target application or system"}),e.jsxs("div",{className:"flex gap-2",children:[e.jsxs(v,{children:[e.jsx(ae,{className:"mr-2 h-4 w-4"}),"Start Analysis"]}),e.jsxs(v,{variant:"outline",children:[e.jsx(Y,{className:"mr-2 h-4 w-4"}),"Configure"]})]})]})})]}),e.jsxs(y,{children:[e.jsxs(T,{children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx(ve,{className:"h-5 w-5"}),e.jsx(A,{children:"Real-time Monitoring"})]}),e.jsx(F,{children:"Monitor live behavioral patterns"})]}),e.jsx(w,{children:e.jsx("div",{className:"space-y-4",children:e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-green-500",children:"12"}),e.jsx("p",{className:"text-xs text-muted-foreground",children:"Normal Patterns"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-red-500",children:"3"}),e.jsx("p",{className:"text-xs text-muted-foreground",children:"Anomalies"})]})]})})})]})]}),e.jsxs(y,{children:[e.jsxs(T,{children:[e.jsx(A,{children:"Analysis Results"}),e.jsx(F,{children:"Recent behavioral pattern analysis findings"})]}),e.jsx(w,{children:e.jsx("div",{className:"space-y-4",children:t.map(s=>e.jsxs("div",{className:"flex items-center gap-4 p-4 rounded-lg border",children:[e.jsx("div",{className:"h-10 w-10 rounded-lg bg-primary/10 flex items-center justify-center",children:e.jsx(Ns,{className:"h-4 w-4"})}),e.jsxs("div",{className:"flex-1 space-y-2",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("p",{className:"text-sm font-medium",children:s.pattern}),e.jsx($,{variant:"outline",className:s.severity==="high"?"text-red-500":s.severity==="medium"?"text-yellow-500":"text-green-500",children:s.severity})]}),e.jsx("p",{className:"text-xs text-muted-foreground",children:s.description}),e.jsxs("div",{className:"space-y-1",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-xs",children:"Confidence"}),e.jsxs("span",{className:"text-xs",children:[s.confidence,"%"]})]}),e.jsx(Ae,{value:s.confidence,className:"h-1"})]})]})]},s.id))})})]})]})}function Si(){const[t]=x.useState({requestsIntercepted:1247,vulnerabilitiesFound:23,payloadsModified:89,activeConnections:5}),[s]=x.useState([{id:"1",url:"/api/login",method:"POST",status:"modified",vulnerability:"SQL Injection"},{id:"2",url:"/search?q=test",method:"GET",status:"analyzed",vulnerability:"XSS"},{id:"3",url:"/api/users/123",method:"PUT",status:"passed",vulnerability:null}]);return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-3xl font-bold tracking-tight",children:"Ferrari AI Proxy"}),e.jsx("p",{className:"text-muted-foreground",children:"Intelligent traffic interception and analysis"})]}),e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsxs($,{variant:"outline",className:"text-green-500",children:[e.jsx(ve,{className:"mr-1 h-3 w-3"}),"Proxy Active"]}),e.jsxs(v,{variant:"outline",children:[e.jsx(et,{className:"mr-2 h-4 w-4"}),"Pause"]})]})]}),e.jsxs("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-4",children:[e.jsxs(y,{children:[e.jsxs(T,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[e.jsx(A,{className:"text-sm font-medium",children:"Requests Intercepted"}),e.jsx(Oe,{className:"h-4 w-4 text-muted-foreground"})]}),e.jsxs(w,{children:[e.jsx("div",{className:"text-2xl font-bold",children:t.requestsIntercepted}),e.jsx("p",{className:"text-xs text-muted-foreground",children:"Total this session"})]})]}),e.jsxs(y,{children:[e.jsxs(T,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[e.jsx(A,{className:"text-sm font-medium",children:"Vulnerabilities Found"}),e.jsx(fe,{className:"h-4 w-4 text-muted-foreground"})]}),e.jsxs(w,{children:[e.jsx("div",{className:"text-2xl font-bold text-red-600",children:t.vulnerabilitiesFound}),e.jsx("p",{className:"text-xs text-muted-foreground",children:"Potential issues"})]})]}),e.jsxs(y,{children:[e.jsxs(T,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[e.jsx(A,{className:"text-sm font-medium",children:"Payloads Modified"}),e.jsx(Xe,{className:"h-4 w-4 text-muted-foreground"})]}),e.jsxs(w,{children:[e.jsx("div",{className:"text-2xl font-bold",children:t.payloadsModified}),e.jsx("p",{className:"text-xs text-muted-foreground",children:"AI-enhanced requests"})]})]}),e.jsxs(y,{children:[e.jsxs(T,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[e.jsx(A,{className:"text-sm font-medium",children:"Active Connections"}),e.jsx(ve,{className:"h-4 w-4 text-muted-foreground"})]}),e.jsxs(w,{children:[e.jsx("div",{className:"text-2xl font-bold",children:t.activeConnections}),e.jsx("p",{className:"text-xs text-muted-foreground",children:"Current connections"})]})]})]}),e.jsxs("div",{className:"grid gap-6 md:grid-cols-2",children:[e.jsxs(y,{children:[e.jsxs(T,{children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx(Oe,{className:"h-5 w-5"}),e.jsx(A,{children:"Proxy Configuration"})]}),e.jsx(F,{children:"Configure AI proxy settings"})]}),e.jsx(w,{children:e.jsxs("div",{className:"space-y-4",children:[e.jsx(P,{placeholder:"Proxy listen address",defaultValue:"127.0.0.1:8080"}),e.jsx(P,{placeholder:"Target host",defaultValue:"example.com"}),e.jsxs("div",{className:"flex gap-2",children:[e.jsxs(v,{children:[e.jsx(ae,{className:"mr-2 h-4 w-4"}),"Start Proxy"]}),e.jsxs(v,{variant:"outline",children:[e.jsx(Y,{className:"mr-2 h-4 w-4"}),"Advanced"]})]})]})})]}),e.jsxs(y,{children:[e.jsxs(T,{children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx(Fs,{className:"h-5 w-5"}),e.jsx(A,{children:"AI Filter Rules"})]}),e.jsx(F,{children:"Configure AI analysis rules"})]}),e.jsx(w,{children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("label",{className:"text-sm",children:"Auto-detect vulnerabilities"}),e.jsx("input",{type:"checkbox",defaultChecked:!0})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("label",{className:"text-sm",children:"Modify payloads"}),e.jsx("input",{type:"checkbox",defaultChecked:!0})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("label",{className:"text-sm",children:"Block malicious requests"}),e.jsx("input",{type:"checkbox"})]})]})})]})]}),e.jsxs(y,{children:[e.jsxs(T,{children:[e.jsx(A,{children:"Intercepted Requests"}),e.jsx(F,{children:"Real-time traffic analysis and modification"})]}),e.jsx(w,{children:e.jsx("div",{className:"space-y-4",children:s.map(a=>e.jsxs("div",{className:"flex items-center gap-4 p-4 rounded-lg border",children:[e.jsx("div",{className:"h-10 w-10 rounded-lg bg-primary/10 flex items-center justify-center",children:e.jsx(Oe,{className:"h-4 w-4"})}),e.jsxs("div",{className:"flex-1 space-y-1",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx($,{variant:"secondary",children:a.method}),e.jsx("p",{className:"text-sm font-medium font-mono",children:a.url})]}),a.vulnerability&&e.jsxs("p",{className:"text-xs text-red-600",children:["Vulnerability detected: ",a.vulnerability]})]}),e.jsx($,{variant:"outline",className:a.status==="modified"?"text-yellow-500":a.status==="analyzed"?"text-blue-500":"text-green-500",children:a.status}),e.jsx(v,{size:"sm",variant:"outline",children:e.jsx(ys,{className:"h-3 w-3"})})]},a.id))})})]})]})}const Ci=Ds("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),Aa=x.forwardRef(({className:t,variant:s,...a},n)=>e.jsx("div",{ref:n,role:"alert",className:B(Ci({variant:s}),t),...a}));Aa.displayName="Alert";const Ti=x.forwardRef(({className:t,...s},a)=>e.jsx("h5",{ref:a,className:B("mb-1 font-medium leading-none tracking-tight",t),...s}));Ti.displayName="AlertTitle";const Ai=x.forwardRef(({className:t,...s},a)=>e.jsx("div",{ref:a,className:B("text-sm [&_p]:leading-relaxed",t),...s}));Ai.displayName="AlertDescription";const Ra=({error:t,errorInfo:s,resetError:a,onRetry:n,onReportBug:r,className:c})=>{const[p,d]=x.useState(!1),[g,b]=x.useState(!1),j={message:t.message,stack:t.stack,componentStack:s?.componentStack,timestamp:new Date().toISOString(),userAgent:navigator.userAgent,url:window.location.href},R=()=>{const u=JSON.stringify(j,null,2);navigator.clipboard.writeText(u),b(!0),setTimeout(()=>b(!1),2e3)},E=u=>{const k=u.message.toLowerCase();return k.includes("chunk")||k.includes("loading")?"medium":k.includes("network")||k.includes("fetch")?"high":k.includes("reference")||k.includes("undefined")?"critical":(k.includes("permission")||k.includes("auth"),"high")},f=u=>{switch(u){case"critical":return"bg-red-600 text-white";case"high":return"bg-red-500 text-white";case"medium":return"bg-yellow-500 text-white";case"low":return"bg-blue-500 text-white";default:return"bg-gray-500 text-white"}},L=u=>{const k=u.message.toLowerCase();return k.includes("chunk")||k.includes("loading")?"This appears to be a loading issue. Try refreshing the page or checking your internet connection.":k.includes("network")||k.includes("fetch")?"This is a network connectivity issue. Check your connection to the backend server at ec2-3-89-91-209.compute-1.amazonaws.com:8000.":k.includes("reference")||k.includes("undefined")?"This is a code reference error. Please report this as a bug with the technical details below.":k.includes("permission")||k.includes("auth")?"This appears to be an authentication or permission issue. Try refreshing to re-authenticate.":"An unexpected error occurred. Try refreshing or report this issue if it persists."},i=E(t);return e.jsx("div",{className:B("min-h-screen flex items-center justify-center p-4 bg-background",c),children:e.jsxs(y,{className:"w-full max-w-3xl",children:[e.jsxs(T,{className:"text-center",children:[e.jsxs("div",{className:"flex items-center justify-center space-x-2 mb-4",children:[e.jsx(ee,{className:"h-8 w-8 text-red-500"}),e.jsx(A,{className:"text-xl",children:"NexusScan Desktop Error"})]}),e.jsxs($,{className:`mx-auto ${f(i)}`,children:[i.toUpperCase()," SEVERITY"]})]}),e.jsxs(w,{className:"space-y-6",children:[e.jsxs(Aa,{children:[e.jsx(ee,{className:"h-4 w-4"}),e.jsxs("div",{children:[e.jsx("h4",{className:"font-semibold mb-1",children:"Error Details"}),e.jsx("p",{className:"text-sm text-muted-foreground",children:t.message})]})]}),e.jsxs("div",{className:"p-4 bg-muted/50 rounded-lg",children:[e.jsxs("h4",{className:"font-medium mb-2 flex items-center space-x-2",children:[e.jsx(fe,{className:"h-4 w-4"}),e.jsx("span",{children:"Recommended Action"})]}),e.jsx("p",{className:"text-sm text-muted-foreground",children:L(t)})]}),e.jsxs("div",{className:"flex flex-wrap gap-3 justify-center",children:[n&&e.jsxs(v,{onClick:n,className:"flex items-center space-x-2",children:[e.jsx(st,{className:"h-4 w-4"}),e.jsx("span",{children:"Retry"})]}),a&&e.jsxs(v,{variant:"outline",onClick:a,className:"flex items-center space-x-2",children:[e.jsx(Sn,{className:"h-4 w-4"}),e.jsx("span",{children:"Go Home"})]}),e.jsxs(v,{variant:"outline",onClick:()=>window.location.reload(),className:"flex items-center space-x-2",children:[e.jsx(st,{className:"h-4 w-4"}),e.jsx("span",{children:"Refresh Page"})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-3",children:[e.jsxs("div",{className:"p-3 border rounded-lg",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(ve,{className:"h-4 w-4 text-blue-500"}),e.jsx("span",{className:"text-sm font-medium",children:"Connection Status"})]}),e.jsx("div",{className:"text-xs text-muted-foreground mt-1",children:"Check: AWS backend connectivity"})]}),e.jsxs("div",{className:"p-3 border rounded-lg",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(fe,{className:"h-4 w-4 text-green-500"}),e.jsx("span",{className:"text-sm font-medium",children:"Error Isolation"})]}),e.jsxs("div",{className:"text-xs text-muted-foreground mt-1",children:["Component: ",s?.componentStack?"Identified":"Application"]})]}),e.jsxs("div",{className:"p-3 border rounded-lg",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(Es,{className:"h-4 w-4 text-orange-500"}),e.jsx("span",{className:"text-sm font-medium",children:"Recovery"})]}),e.jsx("div",{className:"text-xs text-muted-foreground mt-1",children:"Auto-recovery: Available"})]})]}),e.jsxs("div",{className:"border-t pt-4",children:[e.jsxs("button",{onClick:()=>d(!p),className:"flex items-center space-x-2 text-sm text-muted-foreground hover:text-foreground transition-colors",children:[p?e.jsx(ut,{className:"h-4 w-4"}):e.jsx(la,{className:"h-4 w-4"}),e.jsx("span",{children:"Technical Details & Debugging Information"})]}),p&&e.jsxs("div",{className:"mt-3 space-y-3",children:[e.jsxs("div",{className:"p-3 bg-muted rounded-md font-mono text-xs",children:[e.jsx("div",{className:"mb-2 font-semibold",children:"Error Message:"}),e.jsx("div",{className:"text-red-600 mb-3",children:t.message}),t.stack&&e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"mb-2 font-semibold",children:"Stack Trace:"}),e.jsx("pre",{className:"whitespace-pre-wrap text-xs overflow-x-auto max-h-48",children:t.stack})]}),s?.componentStack&&e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"mb-2 font-semibold mt-3",children:"Component Stack:"}),e.jsx("pre",{className:"whitespace-pre-wrap text-xs overflow-x-auto max-h-32",children:s.componentStack})]}),e.jsxs("div",{className:"mt-3 grid grid-cols-2 gap-2 text-xs",children:[e.jsxs("div",{children:[e.jsx("strong",{children:"Timestamp:"}),e.jsx("div",{children:new Date().toLocaleString()})]}),e.jsxs("div",{children:[e.jsx("strong",{children:"URL:"}),e.jsx("div",{className:"truncate",children:window.location.href})]}),e.jsxs("div",{children:[e.jsx("strong",{children:"User Agent:"}),e.jsx("div",{className:"truncate",children:navigator.userAgent})]}),e.jsxs("div",{children:[e.jsx("strong",{children:"Error Boundary:"}),e.jsx("div",{children:s?.errorBoundary?"Yes":"No"})]})]})]}),e.jsxs("div",{className:"flex space-x-2",children:[e.jsxs(v,{variant:"outline",size:"sm",onClick:R,className:"flex items-center space-x-1",children:[e.jsx(je,{className:"h-3 w-3"}),e.jsx("span",{children:g?"Copied!":"Copy Details"})]}),r&&e.jsxs(v,{variant:"outline",size:"sm",onClick:()=>r(t,s),className:"flex items-center space-x-1",children:[e.jsx(Es,{className:"h-3 w-3"}),e.jsx("span",{children:"Report Bug"})]})]})]})]}),e.jsxs("div",{className:"text-center text-sm text-muted-foreground border-t pt-4",children:[e.jsx("p",{className:"font-medium mb-2",children:"Troubleshooting Checklist:"}),e.jsxs("ul",{className:"space-y-1 text-left max-w-md mx-auto",children:[e.jsx("li",{children:"• Check internet connection"}),e.jsx("li",{children:"• Verify backend server status (AWS EC2)"}),e.jsx("li",{children:"• Clear browser cache and reload"}),e.jsx("li",{children:"• Check browser console for additional errors"}),e.jsx("li",{children:"• Ensure JavaScript is enabled"})]}),e.jsxs("div",{className:"mt-4 flex justify-center space-x-4",children:[e.jsxs("a",{href:"https://github.com/nexusscan/nexusscan-desktop/issues",target:"_blank",rel:"noopener noreferrer",className:"inline-flex items-center space-x-1 text-blue-500 hover:underline",children:[e.jsx(Cn,{className:"h-3 w-3"}),e.jsx("span",{children:"GitHub Issues"})]}),e.jsx("span",{className:"text-muted-foreground",children:"•"}),e.jsx("a",{href:"mailto:<EMAIL>",className:"inline-flex items-center space-x-1 text-blue-500 hover:underline",children:e.jsx("span",{children:"Contact Support"})})]})]})]})]})})};class _i extends x.Component{constructor(a){super(a);ge(this,"resetError",()=>{this.setState({hasError:!1,error:void 0,errorInfo:void 0})});this.state={hasError:!1}}static getDerivedStateFromError(a){return{hasError:!0,error:a}}componentDidCatch(a,n){const r={componentStack:n.componentStack,errorBoundary:!0};this.setState({error:a,errorInfo:r}),this.props.onError?.(a,r),console.error("🚨 Error caught by boundary:",a),console.error("📍 Error info:",n),console.log("📊 Error would be reported to monitoring service")}render(){if(this.state.hasError&&this.state.error){const a=this.props.fallback||Ra;return e.jsx(a,{error:this.state.error,errorInfo:this.state.errorInfo,resetError:this.resetError})}return this.props.children}}function Ri(){const t=dt(),{isLoading:s,sidebarOpen:a,terminalOpen:n,theme:r,setLoading:c,toggleSidebar:p,toggleTerminal:d}=Na(),{status:g,connect:b,refreshTools:j}=Pe();x.useEffect(()=>{console.log("📊 App.tsx - Remote Backend Status:",{connected:g.connected,error:g.error,url:g.url,tools:g.tools,hasTools:!!g.tools,toolsType:typeof g.tools}),console.log("🎯 App.tsx - Passing to StatusBar (Remote Mode):",{backendStatus:g})},[g]);const[R,E]=K.useState(!1),[f,L]=K.useState(null);K.useEffect(()=>{async function u(){try{c(!0),console.log("🚀 Initializing NexusScan Desktop App..."),we.isElectron()||console.warn("⚠️ Running in browser mode - some features may be limited"),console.log("🔗 Connecting to backend...");const k=new Promise((N,D)=>setTimeout(()=>D(new Error("Backend connection timeout")),15e3));try{const N=await Promise.race([b(),k]);console.log("✅ Backend connection completed, status:",N),N&&(console.log("🔧 Force refreshing tools after connection..."),await j(),console.log("🔧 Tools refresh completed"))}catch(N){console.warn("⚠️ Backend connection failed or timed out:",N)}console.log("🌐 Remote backend mode - WSL not required");try{i(),console.log("✅ Event listeners setup complete")}catch(N){console.warn("⚠️ Event listeners setup failed:",N)}E(!0),console.log("✅ NexusScan Desktop App initialized successfully")}catch(k){console.error("❌ App initialization failed:",k),E(!0),console.log("✅ NexusScan Desktop App initialized (with warnings)")}finally{c(!1)}}u()},[c,b,initializeWSL]);function i(){we.isElectron()&&(we.on("navigate",u=>{window.history.pushState(null,"",u)}),we.on("toggle-sidebar",()=>{p()}),we.on("toggle-terminal",()=>{d()}),we.on("backend-connected",()=>{console.log("📡 Backend connected")}),we.on("backend-disconnected",()=>{console.log("📡 Backend disconnected")}),we.on("backend-error",u=>{console.error("📡 Backend error:",u)}),we.on("tool-output",u=>{console.log("🔧 Tool output:",u)}),we.on("tool-progress",u=>{console.log("🔧 Tool progress:",u)}),console.log("👂 Event listeners setup complete"))}return K.useEffect(()=>{function u(k){if(k.target instanceof HTMLInputElement||k.target instanceof HTMLTextAreaElement)return;const{ctrlKey:N,metaKey:D,shiftKey:W,key:U}=k;if(N||D)switch(U){case"b":k.preventDefault(),p();break;case"t":k.preventDefault(),d();break;case"1":k.preventDefault(),window.history.pushState(null,"","/dashboard");break;case"2":k.preventDefault(),window.history.pushState(null,"","/campaigns");break;case"3":k.preventDefault(),window.history.pushState(null,"","/reports");break}}return window.addEventListener("keydown",u),()=>window.removeEventListener("keydown",u)},[p,d]),s||!R?e.jsx("div",{className:"flex items-center justify-center h-screen bg-background",children:e.jsxs("div",{className:"text-center space-y-4",children:[e.jsx("div",{className:"loading-spinner mx-auto"}),e.jsx("div",{className:"text-lg font-semibold",children:"Initializing NexusScan Desktop..."}),e.jsx("div",{className:"text-sm text-muted-foreground",children:g.connected?"Backend connected":"Connecting to backend..."})]})}):f?e.jsx("div",{className:"flex items-center justify-center h-screen bg-background p-8",children:e.jsxs("div",{className:"text-center space-y-4 max-w-md",children:[e.jsx("div",{className:"text-red-500 text-xl",children:"⚠️"}),e.jsx("div",{className:"text-lg font-semibold",children:"Initialization Failed"}),e.jsx("div",{className:"text-sm text-muted-foreground",children:f}),e.jsx("button",{onClick:()=>window.location.reload(),className:"px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90",children:"Restart Application"})]})}):e.jsx("div",{className:B("h-screen flex flex-col bg-background",r),children:e.jsxs(ir,{FallbackComponent:Ra,onError:(u,k)=>{console.error("🚨 React Error Boundary:",u,k)},onReset:()=>{window.location.reload()},children:[e.jsx(Hr,{}),e.jsxs("div",{className:"flex flex-1 overflow-hidden",children:[e.jsx(Br,{isOpen:a,currentPath:t.pathname}),e.jsxs("main",{className:"flex-1 flex flex-col overflow-hidden",children:[e.jsx("div",{className:"flex-1 overflow-auto",children:e.jsxs(mt,{children:[e.jsx(te,{path:"/",element:e.jsx(Ja,{to:"/dashboard",replace:!0})}),e.jsx(te,{path:"/dashboard",element:e.jsx(Qr,{})}),e.jsx(te,{path:"/campaigns/*",element:e.jsx(Jr,{})}),e.jsx(te,{path:"/reports/*",element:e.jsx(Kr,{})}),e.jsx(te,{path:"/settings/*",element:e.jsx(Xr,{})}),e.jsx(te,{path:"/tools/network-scanning/*",element:e.jsx(ci,{})}),e.jsx(te,{path:"/tools/web-testing/*",element:e.jsx(oi,{})}),e.jsx(te,{path:"/tools/vulnerability-assessment/*",element:e.jsx(gi,{})}),e.jsx(te,{path:"/tools/password-tools/*",element:e.jsx(ji,{})}),e.jsx(te,{path:"/tools/ssl-testing/*",element:e.jsx(vi,{})}),e.jsx(te,{path:"/tools/exploitation/*",element:e.jsx(bi,{})}),e.jsx(te,{path:"/ferrari",element:e.jsx(Ni,{})}),e.jsx(te,{path:"/ferrari/orchestrator/*",element:e.jsx(yi,{})}),e.jsx(te,{path:"/ferrari/creative-exploits/*",element:e.jsx(wi,{})}),e.jsx(te,{path:"/ferrari/behavioral-analysis/*",element:e.jsx(ki,{})}),e.jsx(te,{path:"/ferrari/ai-proxy/*",element:e.jsx(Si,{})}),e.jsx(te,{path:"*",element:e.jsx("div",{className:"flex items-center justify-center h-full",children:e.jsxs("div",{className:"text-center space-y-4",children:[e.jsx("div",{className:"text-4xl",children:"🔍"}),e.jsx("div",{className:"text-xl font-semibold",children:"Page Not Found"}),e.jsx("div",{className:"text-muted-foreground",children:"The page you're looking for doesn't exist."}),e.jsx("button",{onClick:()=>window.history.pushState(null,"","/dashboard"),className:"px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90",children:"Go to Dashboard"})]})})})]})}),n&&e.jsx(qr,{isOpen:n,onToggle:d})]})]}),e.jsx(Vr,{backendStatus:g})]})})}class Ei{constructor(){ge(this,"baseURL","http://************:8090")}async testConnection(){console.log("🔧 Testing DIRECT backend connection (bypassing Electron IPC)...");try{const a=await(await fetch(`${this.baseURL}/api/health`,{method:"GET",headers:{"Content-Type":"application/json"},mode:"cors"})).json();return console.log("✅ DIRECT CONNECTION SUCCESS:",a),{success:!0,data:a}}catch(s){return console.error("❌ DIRECT CONNECTION FAILED:",s),{success:!1,error:s.message}}}async getTools(){try{const a=await(await fetch(`${this.baseURL}/api/tools`,{method:"GET",headers:{"Content-Type":"application/json"},mode:"cors"})).json();return console.log("✅ TOOLS FETCHED:",a.data?.length||0,"tools"),a}catch(s){return console.error("❌ TOOLS FETCH FAILED:",s),{success:!1,error:s.message}}}}typeof window<"u"&&window.addEventListener("DOMContentLoaded",async()=>{console.log("🚀 Running Direct Backend Connection Test...");const t=new Ei,s=await t.testConnection();if(console.log("Health Check Result:",s),s.success){const a=await t.getTools();console.log("Tools Fetch Result:",a)}});async function Pi(){try{console.log("🚀 Initializing NexusScan Desktop...");const t=window.electronAPI!==void 0;if(console.log(`📱 Runtime Environment: ${t?"Electron Desktop":"Browser"}`),t){await new Promise(s=>setTimeout(s,100));try{const[s,a,n]=await Promise.all([window.electronAPI.app.getVersion(),window.electronAPI.app.getPlatform(),window.electronAPI.app.getArch()]);console.log(`📋 App Version: ${s}`),console.log(`💻 Platform: ${a} (${n})`);try{const r=await window.electronAPI.backend.getStatus();if(console.log("📡 Backend Status:",r),!r.connected){console.log("🔄 Attempting backend connection...");const c=await window.electronAPI.backend.connect();console.log(`🔗 Backend Connection: ${c?"Success":"Failed"}`)}}catch(r){console.warn("⚠️ Backend connection error:",r)}}catch(s){console.warn("⚠️ Failed to get app information:",s)}if(platform==="win32")try{const s=await window.electronAPI.wsl.getStatus();console.log("🐧 WSL Status:",s)}catch(s){console.warn("⚠️ WSL check error:",s)}}console.log("✅ NexusScan Desktop initialized successfully"),window.dispatchEvent(new CustomEvent("appReady"))}catch(t){console.error("❌ Failed to initialize NexusScan Desktop:",t),window.dispatchEvent(new CustomEvent("appReady"))}}function na(){Pn.createRoot(document.getElementById("root")).render(e.jsx(x.StrictMode,{children:e.jsxs(Ka,{children:[e.jsx(Ri,{}),e.jsx(nr,{position:"top-right",toastOptions:{duration:4e3,style:{background:"hsl(var(--card))",color:"hsl(var(--card-foreground))",border:"1px solid hsl(var(--border))"}}})]})}))}function Ii(){window.addEventListener("error",t=>{console.error("🚨 Uncaught error:",t.error),window.electronAPI&&window.electronAPI.notification.show({title:"Application Error",body:"An unexpected error occurred. Check the console for details."}).catch(()=>{console.error("Failed to show error notification")})}),window.addEventListener("unhandledrejection",t=>{console.error("🚨 Unhandled promise rejection:",t.reason),t.preventDefault()}),console.log("🛡️ Error handling configured")}async function Oi(){try{Ii(),na(),await Pi(),console.log("🎉 NexusScan Desktop ready!")}catch(t){console.error("💥 Bootstrap failed:",t);try{na()}catch(s){console.error("💥 Failed to render app:",s),document.body.innerHTML=`
        <div style="
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          height: 100vh;
          background: #0f172a;
          color: #e2e8f0;
          font-family: system-ui, sans-serif;
          text-align: center;
          padding: 2rem;
        ">
          <h1 style="color: #ef4444; margin-bottom: 1rem;">Application Error</h1>
          <p style="margin-bottom: 2rem;">Failed to start NexusScan Desktop</p>
          <details style="max-width: 600px;">
            <summary style="cursor: pointer; margin-bottom: 1rem;">Error Details</summary>
            <pre style="
              background: #1e293b;
              padding: 1rem;
              border-radius: 0.5rem;
              text-align: left;
              overflow: auto;
              font-size: 0.875rem;
            ">${t.message}

${t.stack}</pre>
          </details>
          <button onclick="window.location.reload()" style="
            background: #3b82f6;
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 0.5rem;
            cursor: pointer;
            margin-top: 2rem;
          ">Restart Application</button>
        </div>
      `}}}Oi();
//# sourceMappingURL=index-fxrVuJdX.js.map
