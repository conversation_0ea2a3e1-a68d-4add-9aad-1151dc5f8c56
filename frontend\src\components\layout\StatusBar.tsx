/**
 * StatusBar Component - Application status bar
 * Remote Backend Architecture - WSL not required
 */
import React from 'react';
import { Badge } from '@/components/ui/badge';
import {
  Server,
  CheckCircle,
  AlertTriangle,
  Activity,
  Cpu,
  HardDrive,
  Network,
  Cloud,
  Globe
} from 'lucide-react';

interface StatusBarProps {
  backendStatus: {
    connected: boolean;
    error?: string;
    url?: string;
    tools?: {
      total: number;
      operational: number;
      failed: number;
    };
  };
}

export function StatusBar({ backendStatus }: StatusBarProps) {
  // Debug logging for backend status
  React.useEffect(() => {
    console.log('📊 StatusBar - Remote Backend Status:', {
      connected: backendStatus.connected,
      error: backendStatus.error,
      url: backendStatus.url,
      tools: backendStatus.tools
    });

    if (backendStatus.tools) {
      console.log('🔧 StatusBar - Remote Tools Available:', {
        total: backendStatus.tools.total,
        operational: backendStatus.tools.operational,
        failed: backendStatus.tools.failed,
        display: `${backendStatus.tools.operational}/${backendStatus.tools.total}`
      });
    } else {
      console.log('⚠️ StatusBar - No tools data from remote backend');
    }
  }, [backendStatus]);

  return (
    <div className="h-8 bg-background border-t px-4 flex items-center justify-between text-xs">
      {/* Left Section - Remote Backend Status */}
      <div className="flex items-center gap-4">
        <div className="flex items-center gap-2">
          {backendStatus.connected ? (
            <>
              <Cloud className="h-3 w-3 text-green-500" />
              <span className="text-green-500">Remote Backend Connected</span>
            </>
          ) : backendStatus.error ? (
            <>
              <AlertTriangle className="h-3 w-3 text-red-500" />
              <span className="text-red-500">Remote Backend Error</span>
            </>
          ) : (
            <>
              <AlertTriangle className="h-3 w-3 text-yellow-500" />
              <span className="text-yellow-500">Remote Backend Offline</span>
            </>
          )}
        </div>

        <div className="h-4 w-px bg-border" />

        <div className="flex items-center gap-2">
          <Globe className="h-3 w-3 text-blue-500" />
          <span className="text-blue-500">
            {backendStatus.url ?
              `Server: ${backendStatus.url.replace('http://', '').replace('https://', '')}` :
              'Server: 161.97.99.62:8090'
            }
          </span>
        </div>

        <div className="h-4 w-px bg-border" />

        <div className="flex items-center gap-2">
          <Server className="h-3 w-3 text-muted-foreground" />
          <span>
            {backendStatus.tools ? (
              `Security Tools: ${backendStatus.tools.operational}/${backendStatus.tools.total}`
            ) : backendStatus.connected ? (
              'Security Tools: Loading...'
            ) : (
              'Security Tools: Offline'
            )}
          </span>
        </div>
      </div>

      {/* Right Section - App Info */}
      <div className="flex items-center gap-4">
        <div className="flex items-center gap-2">
          <Activity className="h-3 w-3 text-muted-foreground" />
          <span>NexusScan Desktop - Remote Mode</span>
        </div>

        <div className="h-4 w-px bg-border" />

        <span className="text-muted-foreground">v1.0.0</span>
      </div>
    </div>
  );
}