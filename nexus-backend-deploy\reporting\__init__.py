"""
Advanced Reporting Package for NexusScan
Multi-format report generation with intelligence
"""

from .report_generator import ReportGenerator, ReportData, ScanMetadata, VulnerabilityData

# Enterprise dashboard components
try:
    from .enterprise_dashboard import (
        DashboardManager, ReportGenerator as EnterpriseReportGenerator,
        MetricsCollector, ChartGenerator, get_enterprise_dashboard, get_report_generator
    )
    ENTERPRISE_REPORTING_AVAILABLE = True
except ImportError:
    ENTERPRISE_REPORTING_AVAILABLE = False

# Integration module with optional import
try:
    from .integration import ReportingIntegration
    INTEGRATION_AVAILABLE = True
except ImportError:
    INTEGRATION_AVAILABLE = False

__all__ = ['ReportGenerator', 'ReportData', 'ScanMetadata', 'VulnerabilityData']

if ENTERPRISE_REPORTING_AVAILABLE:
    __all__.extend(['DashboardManager', 'EnterpriseReportGenerator', 'MetricsCollector', 'ChartGenerator'])

if INTEGRATION_AVAILABLE:
    __all__.append('ReportingIntegration')