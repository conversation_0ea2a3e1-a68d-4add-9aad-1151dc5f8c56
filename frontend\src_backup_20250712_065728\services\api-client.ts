/**
 * Comprehensive API Client for NexusScan Backend Integration
 * Connects to AWS EC2 backend with 22 operational security tools
 */
import { toast } from 'sonner';

// ===== TYPE DEFINITIONS =====

export interface BackendStatus {
  connected: boolean;
  url: string;
  version?: string;
  error?: string;
  lastConnected?: Date;
  tools?: {
    total: number;
    operational: number;
    failed: number;
  };
}

export interface ToolInfo {
  id: string;
  name: string;
  description: string;
  category: 'network-scanning' | 'vulnerability-assessment' | 'web-testing' | 'password-tools' | 'ssl-testing' | 'exploitation';
  version?: string;
  status: 'available' | 'unavailable' | 'error';
  platform?: string[];
  executable?: string;
  lastUsed?: Date;
  capabilities?: string[];
  requiredParams?: ToolParameter[];
}

export interface ToolParameter {
  name: string;
  type: 'string' | 'number' | 'boolean' | 'select' | 'multiselect' | 'file' | 'array';
  required: boolean;
  description: string;
  default?: any;
  options?: string[];
  validation?: {
    min?: number;
    max?: number;
    pattern?: string;
  };
}

export interface ToolExecution {
  id: string;
  toolId: string;
  status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';
  progress: number;
  startTime: Date;
  endTime?: Date;
  output?: string[];
  error?: string;
  results?: any;
  config: Record<string, any>;
}

export interface Campaign {
  id: string;
  name: string;
  description: string;
  target: string;
  status: 'active' | 'completed' | 'paused' | 'failed';
  createdAt: Date;
  updatedAt: Date;
  tools: string[];
  executions: ToolExecution[];
  results?: any;
}

export interface FerrariCapabilities {
  orchestrator: {
    available: boolean;
    templates: string[];
    capabilities: string[];
  };
  creativeExploits: {
    available: boolean;
    confidence: number;
    generators: string[];
  };
  behavioralAnalysis: {
    available: boolean;
    engines: string[];
    realTime: boolean;
  };
  aiProxy: {
    available: boolean;
    configurations: string[];
    rotationEnabled: boolean;
  };
}

export interface AIStatus {
  available: boolean;
  providers: {
    openai: { available: boolean; model?: string };
    deepseek: { available: boolean; model?: string };
    claude: { available: boolean; model?: string };
  };
  currentProvider: string;
  lastResponse?: Date;
}

// ===== MAIN API CLIENT CLASS =====

class APIClient {
  private baseURL: string;
  private timeout: number;
  private retryAttempts: number;
  private retryDelay: number;
  private headers: Record<string, string>;
  
  constructor() {
    // AWS EC2 backend URL
    this.baseURL = import.meta.env.VITE_BACKEND_URL || 'http://ec2-3-89-91-209.compute-1.amazonaws.com:8000';
    this.timeout = 30000; // 30 seconds
    this.retryAttempts = 3;
    this.retryDelay = 1000; // 1 second
    this.headers = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    };
    
    console.log(`🔗 API Client initialized for: ${this.baseURL}`);
  }
  
  // ===== CORE UTILITY METHODS =====
  
  private async delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
  
  private async fetchWithRetry(
    url: string, 
    options: RequestInit = {}, 
    attempts: number = this.retryAttempts
  ): Promise<Response> {
    for (let i = 0; i < attempts; i++) {
      try {
        // Skip AbortController in test environments to avoid compatibility issues
        const isTestEnv = typeof process !== 'undefined' && process.env.NODE_ENV === 'test';
        
        let fetchOptions: RequestInit = {
          ...options,
          headers: {
            ...this.headers,
            ...options.headers,
          },
        };
        
        if (!isTestEnv) {
          const controller = new AbortController();
          const timeoutId = setTimeout(() => controller.abort(), this.timeout);
          fetchOptions.signal = controller.signal;
          
          const response = await fetch(url, fetchOptions);
          clearTimeout(timeoutId);
          
          if (response.ok) {
            return response;
          }
          
          // If it's the last attempt or non-retryable error, throw
          if (i === attempts - 1 || response.status < 500) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
          }
        } else {
          // Simplified fetch for test environments
          const response = await fetch(url, fetchOptions);
          
          if (response.ok) {
            return response;
          }
          
          if (i === attempts - 1 || response.status < 500) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
          }
        }
        
      } catch (error) {
        if (i === attempts - 1) {
          throw error;
        }
        
        console.warn(`🔄 Request failed (attempt ${i + 1}/${attempts}):`, error);
        await this.delay(this.retryDelay * Math.pow(2, i)); // Exponential backoff
      }
    }
    
    throw new Error('Max retry attempts exceeded');
  }
  
  // ===== BACKEND STATUS & HEALTH =====
  
  async checkHealth(): Promise<BackendStatus> {
    try {
      console.log('🏥 Checking backend health...');
      
      const response = await this.fetchWithRetry(`${this.baseURL}/api/health`);
      const data = await response.json();
      
      console.log('✅ Backend health check successful:', data);
      
      // Handle both response formats: direct and wrapped
      const healthData = data.data || data;
      
      return {
        connected: true,
        url: this.baseURL,
        version: healthData.version || data.version || '1.0.0',
        lastConnected: new Date(),
        tools: healthData.tools || data.tools || { total: 22, operational: 22, failed: 0 },
      };
    } catch (error) {
      console.error('❌ Backend health check failed:', error);
      console.error('❌ Error details:', {
        message: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined,
        type: typeof error,
        error: error
      });
      
      return {
        connected: false,
        url: this.baseURL,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }
  
  // ===== SECURITY TOOLS API =====
  
  async getAvailableTools(): Promise<ToolInfo[]> {
    try {
      console.log('🔧 Fetching available tools...');
      
      const response = await this.fetchWithRetry(`${this.baseURL}/api/tools/available`);
      const data = await response.json();
      
      // Handle backend response format: {"success": true, "data": [...]}
      const tools = data.data || data.tools || data || [];
      
      console.log(`🔍 API Response format: success=${data.success}, data.length=${data.data?.length}, tools.length=${data.tools?.length}`);
      
      console.log(`✅ Retrieved ${tools.length} available tools`);
      
      return tools;
    } catch (error) {
      console.error('❌ Failed to fetch available tools:', error);
      toast.error('Failed to load security tools');
      return [];
    }
  }
  
  async getToolDetails(toolId: string): Promise<ToolInfo | null> {
    try {
      const response = await this.fetchWithRetry(`${this.baseURL}/api/tools/${toolId}`);
      const data = await response.json();
      
      return data.tool || null;
    } catch (error) {
      console.error(`❌ Failed to fetch tool details for ${toolId}:`, error);
      return null;
    }
  }
  
  async executeTool(
    toolId: string, 
    config: Record<string, any>, 
    onProgress?: (progress: ToolExecution) => void
  ): Promise<ToolExecution> {
    try {
      console.log(`🚀 Executing tool: ${toolId}`, config);
      
      const response = await this.fetchWithRetry(`${this.baseURL}/api/tools/${toolId}/execute`, {
        method: 'POST',
        body: JSON.stringify(config),
      });
      
      const execution = await response.json();
      
      // Set up real-time progress monitoring if callback provided
      if (onProgress && execution.id) {
        this.monitorExecution(execution.id, onProgress);
      }
      
      return execution;
    } catch (error) {
      console.error(`❌ Tool execution failed for ${toolId}:`, error);
      toast.error(`Failed to execute ${toolId}`);
      throw error;
    }
  }
  
  async getExecutionStatus(executionId: string): Promise<ToolExecution | null> {
    try {
      const response = await this.fetchWithRetry(`${this.baseURL}/api/executions/${executionId}`);
      const data = await response.json();
      
      return data.execution || null;
    } catch (error) {
      console.error(`❌ Failed to get execution status for ${executionId}:`, error);
      return null;
    }
  }
  
  async cancelExecution(executionId: string): Promise<boolean> {
    try {
      const response = await this.fetchWithRetry(`${this.baseURL}/api/executions/${executionId}/cancel`, {
        method: 'POST',
      });
      
      return response.ok;
    } catch (error) {
      console.error(`❌ Failed to cancel execution ${executionId}:`, error);
      return false;
    }
  }
  
  private async monitorExecution(executionId: string, onProgress: (progress: ToolExecution) => void): Promise<void> {
    const checkProgress = async () => {
      const execution = await this.getExecutionStatus(executionId);
      if (execution) {
        onProgress(execution);
        
        // Continue monitoring if still running
        if (execution.status === 'running' || execution.status === 'pending') {
          setTimeout(checkProgress, 2000); // Check every 2 seconds
        }
      }
    };
    
    setTimeout(checkProgress, 1000); // Start after 1 second
  }
  
  // ===== CAMPAIGNS API =====
  
  async getCampaigns(): Promise<Campaign[]> {
    try {
      const response = await this.fetchWithRetry(`${this.baseURL}/api/campaigns`);
      const data = await response.json();
      
      return data.campaigns || [];
    } catch (error) {
      console.error('❌ Failed to fetch campaigns:', error);
      return [];
    }
  }
  
  async createCampaign(campaign: Partial<Campaign>): Promise<Campaign | null> {
    try {
      const response = await this.fetchWithRetry(`${this.baseURL}/api/campaigns`, {
        method: 'POST',
        body: JSON.stringify(campaign),
      });
      
      const data = await response.json();
      return data.campaign || null;
    } catch (error) {
      console.error('❌ Failed to create campaign:', error);
      toast.error('Failed to create campaign');
      return null;
    }
  }
  
  async updateCampaign(campaignId: string, updates: Partial<Campaign>): Promise<Campaign | null> {
    try {
      const response = await this.fetchWithRetry(`${this.baseURL}/api/campaigns/${campaignId}`, {
        method: 'PUT',
        body: JSON.stringify(updates),
      });
      
      const data = await response.json();
      return data.campaign || null;
    } catch (error) {
      console.error(`❌ Failed to update campaign ${campaignId}:`, error);
      return null;
    }
  }
  
  async deleteCampaign(campaignId: string): Promise<boolean> {
    try {
      const response = await this.fetchWithRetry(`${this.baseURL}/api/campaigns/${campaignId}`, {
        method: 'DELETE',
      });
      
      return response.ok;
    } catch (error) {
      console.error(`❌ Failed to delete campaign ${campaignId}:`, error);
      return false;
    }
  }
  
  // ===== FERRARI AI CAPABILITIES =====
  
  async getFerrariCapabilities(): Promise<FerrariCapabilities | null> {
    try {
      console.log('🏎️ Fetching Ferrari AI capabilities...');
      
      const [orchestrator, creativeExploits, behavioralAnalysis, aiProxy] = await Promise.all([
        this.fetchWithRetry(`${this.baseURL}/api/orchestrator/capabilities`),
        this.fetchWithRetry(`${this.baseURL}/api/ai/creative-exploits/capabilities`),
        this.fetchWithRetry(`${this.baseURL}/api/ai/behavioral-analysis/capabilities`),
        this.fetchWithRetry(`${this.baseURL}/api/proxy/configurations`),
      ]);
      
      const [orchestratorData, exploitsData, analysisData, proxyData] = await Promise.all([
        orchestrator.json(),
        creativeExploits.json(),
        behavioralAnalysis.json(),
        aiProxy.json(),
      ]);
      
      return {
        orchestrator: {
          available: orchestrator.ok,
          templates: orchestratorData.templates || [],
          capabilities: orchestratorData.capabilities || [],
        },
        creativeExploits: {
          available: creativeExploits.ok,
          confidence: exploitsData.confidence || 0,
          generators: exploitsData.generators || [],
        },
        behavioralAnalysis: {
          available: behavioralAnalysis.ok,
          engines: analysisData.engines || [],
          realTime: analysisData.realTime || false,
        },
        aiProxy: {
          available: aiProxy.ok,
          configurations: proxyData.configurations || [],
          rotationEnabled: proxyData.rotationEnabled || false,
        },
      };
    } catch (error) {
      console.error('❌ Failed to fetch Ferrari capabilities:', error);
      return null;
    }
  }
  
  async getAIStatus(): Promise<AIStatus | null> {
    try {
      const response = await this.fetchWithRetry(`${this.baseURL}/api/ai/capabilities/status`);
      const data = await response.json();
      
      return {
        available: data.available || false,
        providers: data.providers || {},
        currentProvider: data.currentProvider || 'none',
        lastResponse: data.lastResponse ? new Date(data.lastResponse) : undefined,
      };
    } catch (error) {
      console.error('❌ Failed to fetch AI status:', error);
      return null;
    }
  }
  
  // ===== ORCHESTRATOR API =====
  
  async getOrchestratorTemplates(): Promise<any[]> {
    try {
      const response = await this.fetchWithRetry(`${this.baseURL}/api/orchestrator/templates`);
      const data = await response.json();
      
      return data.templates || [];
    } catch (error) {
      console.error('❌ Failed to fetch orchestrator templates:', error);
      return [];
    }
  }
  
  async executeAttackChain(chain: any): Promise<any> {
    try {
      const response = await this.fetchWithRetry(`${this.baseURL}/api/orchestrator/execute`, {
        method: 'POST',
        body: JSON.stringify(chain),
      });
      
      return await response.json();
    } catch (error) {
      console.error('❌ Failed to execute attack chain:', error);
      throw error;
    }
  }
  
  // ===== CREATIVE EXPLOITS API =====
  
  async generateCreativeExploit(target: any): Promise<any> {
    try {
      const response = await this.fetchWithRetry(`${this.baseURL}/api/ai/creative-exploits/generate`, {
        method: 'POST',
        body: JSON.stringify(target),
      });
      
      return await response.json();
    } catch (error) {
      console.error('❌ Failed to generate creative exploit:', error);
      throw error;
    }
  }
  
  // ===== BEHAVIORAL ANALYSIS API =====
  
  async analyzeBehavior(data: any): Promise<any> {
    try {
      const response = await this.fetchWithRetry(`${this.baseURL}/api/ai/behavioral-analysis/analyze`, {
        method: 'POST',
        body: JSON.stringify(data),
      });
      
      return await response.json();
    } catch (error) {
      console.error('❌ Failed to analyze behavior:', error);
      throw error;
    }
  }
  
  // ===== PROXY API =====
  
  async getProxyConfigurations(): Promise<any[]> {
    try {
      const response = await this.fetchWithRetry(`${this.baseURL}/api/proxy/configurations`);
      const data = await response.json();
      
      return data.configurations || [];
    } catch (error) {
      console.error('❌ Failed to fetch proxy configurations:', error);
      return [];
    }
  }
  
  // ===== UTILITY METHODS =====
  
  getBaseURL(): string {
    return this.baseURL;
  }
  
  setBaseURL(url: string): void {
    this.baseURL = url;
    console.log(`🔗 API Client URL updated to: ${this.baseURL}`);
  }
  
  setTimeout(timeout: number): void {
    this.timeout = timeout;
  }
  
  setRetryAttempts(attempts: number): void {
    this.retryAttempts = attempts;
  }
}

// ===== SINGLETON EXPORT =====

export const apiClient = new APIClient();