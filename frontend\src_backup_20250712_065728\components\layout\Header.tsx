/**
 * Desktop Application Header Component
 * Provides navigation, status indicators, and global actions
 */
import React from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';\nimport { Button } from '@/components/ui/button';\nimport { Badge } from '@/components/ui/badge';\nimport { \n  Menu, \n  X, \n  Terminal as TerminalIcon, \n  Wifi, \n  WifiOff, \n  AlertTriangle, \n  CheckCircle, \n  Settings, \n  HelpCircle,\n  Minimize2,\n  Square,\n  X as CloseIcon\n} from 'lucide-react';\nimport { cn } from '@/lib/utils';\nimport { useAppStore } from '@/stores/app-store';\nimport { useBackendStore } from '@/stores/backend-store';\nimport { useWSLStore } from '@/stores/wsl-store';\nimport { electronService } from '@/services/electron-service';\n\n/**\n * Header component props\n */\ninterface HeaderProps {\n  className?: string;\n}\n\n/**\n * Main header component for the desktop application\n */\nexport function Header({ className }: HeaderProps) {\n  const location = useLocation();\n  const navigate = useNavigate();\n  \n  // App state\n  const {\n    sidebarOpen,\n    terminalOpen,\n    toggleSidebar,\n    toggleTerminal,\n    theme,\n    toggleTheme\n  } = useAppStore();\n  \n  // Backend and WSL state\n  const { status: backendStatus } = useBackendStore();\n  const { status: wslStatus } = useWSLStore();\n  \n  // Navigation items for breadcrumb\n  const getPageTitle = () => {\n    const path = location.pathname;\n    \n    if (path.startsWith('/ferrari')) {\n      if (path.includes('orchestrator')) return 'Ferrari AI - Orchestrator';\n      if (path.includes('creative-exploits')) return 'Ferrari AI - Creative Exploits';\n      if (path.includes('behavioral-analysis')) return 'Ferrari AI - Behavioral Analysis';\n      if (path.includes('ai-proxy')) return 'Ferrari AI - AI Proxy';\n      return 'Ferrari AI Dashboard';\n    }\n    \n    if (path.startsWith('/tools')) {\n      if (path.includes('network-scanning')) return 'Tools - Network Scanning';\n      if (path.includes('web-testing')) return 'Tools - Web Testing';\n      if (path.includes('vulnerability-assessment')) return 'Tools - Vulnerability Assessment';\n      if (path.includes('password-tools')) return 'Tools - Password Tools';\n      if (path.includes('ssl-testing')) return 'Tools - SSL Testing';\n      if (path.includes('exploitation')) return 'Tools - Exploitation';\n      return 'Security Tools';\n    }\n    \n    switch (path) {\n      case '/dashboard': return 'Dashboard';\n      case '/campaigns': return 'Campaigns';\n      case '/reports': return 'Reports';\n      case '/settings': return 'Settings';\n      default: return 'NexusScan Desktop';\n    }\n  };\n  \n  const handleWindowControl = async (action: 'minimize' | 'maximize' | 'close') => {\n    if (!electronService.isElectron()) return;\n    \n    try {\n      switch (action) {\n        case 'minimize':\n          await electronService.window.minimize();\n          break;\n        case 'maximize':\n          await electronService.window.toggleMaximize();\n          break;\n        case 'close':\n          await electronService.window.close();\n          break;\n      }\n    } catch (error) {\n      console.error(`Failed to ${action} window:`, error);\n    }\n  };\n  \n  const getConnectionStatus = () => {\n    if (backendStatus.connected) {\n      return {\n        icon: <CheckCircle className=\"h-4 w-4\" />,\n        text: 'Connected',\n        variant: 'default' as const,\n        className: 'status-online'\n      };\n    } else {\n      return {\n        icon: <WifiOff className=\"h-4 w-4\" />,\n        text: 'Offline',\n        variant: 'destructive' as const,\n        className: 'status-offline'\n      };\n    }\n  };\n  \n  const getWSLStatus = () => {\n    if (!electronService.isElectron()) return null;\n    \n    switch (wslStatus.status) {\n      case 'available':\n        return {\n          icon: <CheckCircle className=\"h-4 w-4\" />,\n          text: 'WSL Ready',\n          variant: 'default' as const,\n          className: 'status-online'\n        };\n      case 'unavailable':\n        return {\n          icon: <AlertTriangle className=\"h-4 w-4\" />,\n          text: 'WSL Unavailable',\n          variant: 'secondary' as const,\n          className: 'status-warning'\n        };\n      default:\n        return {\n          icon: <AlertTriangle className=\"h-4 w-4\" />,\n          text: 'WSL Unknown',\n          variant: 'secondary' as const,\n          className: 'status-warning'\n        };\n    }\n  };\n  \n  const connectionStatus = getConnectionStatus();\n  const wslStatusInfo = getWSLStatus();\n  \n  return (\n    <header className={cn(\n      'flex items-center justify-between h-14 px-4 border-b border-border bg-background/95 backdrop-blur-sm',\n      'supports-[backdrop-filter]:bg-background/60',\n      className\n    )}>\n      {/* Left section */}\n      <div className=\"flex items-center gap-4\">\n        {/* Sidebar toggle */}\n        <Button\n          variant=\"ghost\"\n          size=\"sm\"\n          onClick={toggleSidebar}\n          className=\"p-2\"\n          title={sidebarOpen ? 'Hide sidebar' : 'Show sidebar'}\n        >\n          {sidebarOpen ? (\n            <X className=\"h-4 w-4\" />\n          ) : (\n            <Menu className=\"h-4 w-4\" />\n          )}\n        </Button>\n        \n        {/* Page title */}\n        <div className=\"flex items-center gap-2\">\n          <h1 className=\"text-lg font-semibold text-foreground\">\n            {getPageTitle()}\n          </h1>\n        </div>\n      </div>\n      \n      {/* Center section - Connection status */}\n      <div className=\"flex items-center gap-3\">\n        {/* Backend connection status */}\n        <Badge\n          variant={connectionStatus.variant}\n          className={cn('flex items-center gap-1.5', connectionStatus.className)}\n        >\n          {connectionStatus.icon}\n          <span className=\"text-xs\">{connectionStatus.text}</span>\n        </Badge>\n        \n        {/* WSL status (Windows only) */}\n        {wslStatusInfo && (\n          <Badge\n            variant={wslStatusInfo.variant}\n            className={cn('flex items-center gap-1.5', wslStatusInfo.className)}\n          >\n            {wslStatusInfo.icon}\n            <span className=\"text-xs\">{wslStatusInfo.text}</span>\n          </Badge>\n        )}\n        \n        {/* Tools count */}\n        {backendStatus.connected && backendStatus.tools && (\n          <Badge variant=\"outline\" className=\"flex items-center gap-1.5\">\n            <span className=\"text-xs\">\n              {backendStatus.tools.operational}/{backendStatus.tools.total} Tools\n            </span>\n          </Badge>\n        )}\n      </div>\n      \n      {/* Right section */}\n      <div className=\"flex items-center gap-2\">\n        {/* Terminal toggle */}\n        <Button\n          variant=\"ghost\"\n          size=\"sm\"\n          onClick={toggleTerminal}\n          className={cn(\n            'p-2',\n            terminalOpen && 'bg-accent text-accent-foreground'\n          )}\n          title={terminalOpen ? 'Hide terminal' : 'Show terminal'}\n        >\n          <TerminalIcon className=\"h-4 w-4\" />\n        </Button>\n        \n        {/* Settings */}\n        <Button\n          variant=\"ghost\"\n          size=\"sm\"\n          onClick={() => navigate('/settings')}\n          className=\"p-2\"\n          title=\"Settings\"\n        >\n          <Settings className=\"h-4 w-4\" />\n        </Button>\n        \n        {/* Help */}\n        <Button\n          variant=\"ghost\"\n          size=\"sm\"\n          onClick={() => {\n            // TODO: Show help dialog or navigate to help page\n            console.log('Help clicked');\n          }}\n          className=\"p-2\"\n          title=\"Help\"\n        >\n          <HelpCircle className=\"h-4 w-4\" />\n        </Button>\n        \n        {/* Window controls (Electron only) */}\n        {electronService.isElectron() && (\n          <div className=\"flex items-center ml-2 -mr-2\">\n            <Button\n              variant=\"ghost\"\n              size=\"sm\"\n              onClick={() => handleWindowControl('minimize')}\n              className=\"p-2 hover:bg-muted/50 rounded-none h-8\"\n              title=\"Minimize\"\n            >\n              <Minimize2 className=\"h-3 w-3\" />\n            </Button>\n            \n            <Button\n              variant=\"ghost\"\n              size=\"sm\"\n              onClick={() => handleWindowControl('maximize')}\n              className=\"p-2 hover:bg-muted/50 rounded-none h-8\"\n              title=\"Maximize\"\n            >\n              <Square className=\"h-3 w-3\" />\n            </Button>\n            \n            <Button\n              variant=\"ghost\"\n              size=\"sm\"\n              onClick={() => handleWindowControl('close')}\n              className=\"p-2 hover:bg-destructive hover:text-destructive-foreground rounded-none h-8\"\n              title=\"Close\"\n            >\n              <CloseIcon className=\"h-3 w-3\" />\n            </Button>\n          </div>\n        )}\n      </div>\n    </header>\n  );\n}"