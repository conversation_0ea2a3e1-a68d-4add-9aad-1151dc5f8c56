#!/usr/bin/env python3
"""
Campaign Orchestration Engine for NexusScan Desktop Application
Implements unified tool orchestration for multi-tool security campaigns
"""

import asyncio
import logging
import json
from datetime import datetime
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
from enum import Enum

from core.events import EventManager, EventTypes
from security.unified_tool_manager import UnifiedToolManager
from database.campaign_manager import CampaignManager
from database.models import Campaign, Scan, ScanStatus

logger = logging.getLogger(__name__)


class OrchestrationPhase(Enum):
    """Campaign orchestration phases"""
    RECONNAISSANCE = "reconnaissance"
    VULNERABILITY_SCANNING = "vulnerability_scanning" 
    EXPLOITATION = "exploitation"
    POST_EXPLOITATION = "post_exploitation"
    REPORTING = "reporting"


@dataclass
class OrchestrationStep:
    """Individual orchestration step"""
    phase: OrchestrationPhase
    tool_id: str
    target: str
    scan_type: str
    depends_on: List[str] = None
    parallel: bool = False
    timeout_seconds: int = 3600
    
    def __post_init__(self):
        if self.depends_on is None:
            self.depends_on = []


@dataclass
class OrchestrationPlan:
    """Complete orchestration plan for a campaign"""
    campaign_id: int
    target_scope: List[str]
    steps: List[OrchestrationStep]
    estimated_duration_minutes: int
    parallel_execution: bool = True


@dataclass
class OrchestrationProgress:
    """Real-time orchestration progress"""
    campaign_id: int
    current_phase: OrchestrationPhase
    completed_steps: int
    total_steps: int
    progress_percentage: float
    status: str
    current_tool: Optional[str] = None
    estimated_remaining_minutes: Optional[int] = None


class CampaignOrchestrationEngine:
    """Orchestrates multi-tool security testing campaigns"""
    
    def __init__(self, 
                 campaign_manager: CampaignManager,
                 tool_manager: UnifiedToolManager,
                 event_manager: EventManager):
        """Initialize orchestration engine
        
        Args:
            campaign_manager: Campaign management service
            tool_manager: Unified tool management service
            event_manager: Event management service
        """
        self.campaign_manager = campaign_manager
        self.tool_manager = tool_manager
        self.event_manager = event_manager
        self.logger = logging.getLogger(__name__)
        
        # Active orchestrations
        self.active_orchestrations: Dict[int, OrchestrationProgress] = {}
        
        # Orchestration plans cache
        self.orchestration_plans: Dict[int, OrchestrationPlan] = {}
        
    async def create_orchestration_plan(self, campaign_id: int) -> OrchestrationPlan:
        """Create comprehensive orchestration plan for campaign
        
        Args:
            campaign_id: Campaign ID to orchestrate
            
        Returns:
            Complete orchestration plan
        """
        campaign = self.campaign_manager.get_campaign(campaign_id)
        if not campaign:
            raise ValueError(f"Campaign {campaign_id} not found")
        
        target_scope = campaign.target_scope or []
        steps = []
        
        # Phase 1: Reconnaissance
        for target in target_scope:
            # Nmap discovery scan
            steps.append(OrchestrationStep(
                phase=OrchestrationPhase.RECONNAISSANCE,
                tool_id="nmap",
                target=target,
                scan_type="quick",
                parallel=True
            ))
            
            # Masscan for port discovery on IP addresses
            if self._is_ip_address(target):
                steps.append(OrchestrationStep(
                    phase=OrchestrationPhase.RECONNAISSANCE,
                    tool_id="masscan",
                    target=target,
                    scan_type="fast",
                    parallel=True
                ))
        
        # Phase 2: Vulnerability Scanning  
        for target in target_scope:
            if self._is_web_target(target):
                # Nuclei vulnerability scanning for web targets
                steps.append(OrchestrationStep(
                    phase=OrchestrationPhase.VULNERABILITY_SCANNING,
                    tool_id="nuclei",
                    target=target,
                    scan_type="critical",
                    depends_on=[f"nmap_{target}"],
                    parallel=True
                ))
                
                # Gobuster directory enumeration
                steps.append(OrchestrationStep(
                    phase=OrchestrationPhase.VULNERABILITY_SCANNING,
                    tool_id="gobuster",
                    target=target,
                    scan_type="dir",
                    depends_on=[f"nmap_{target}"],
                    parallel=True
                ))
                
        # Phase 3: Exploitation (SQL injection testing)
        for target in target_scope:
            if self._is_web_target(target):
                steps.append(OrchestrationStep(
                    phase=OrchestrationPhase.EXPLOITATION,
                    tool_id="sqlmap",
                    target=target,
                    scan_type="detection",
                    depends_on=[f"nuclei_{target}", f"gobuster_{target}"],
                    parallel=False,  # SQLMap should run carefully
                    timeout_seconds=7200  # Longer timeout for exploitation
                ))
        
        # Calculate estimated duration (basic implementation)
        estimated_duration = len(steps) * 10  # 10 minutes per step average
        
        plan = OrchestrationPlan(
            campaign_id=campaign_id,
            target_scope=target_scope,
            steps=steps,
            estimated_duration_minutes=estimated_duration,
            parallel_execution=True
        )
        
        # Cache the plan
        self.orchestration_plans[campaign_id] = plan
        
        self.logger.info(f"Created orchestration plan for campaign {campaign_id}: {len(steps)} steps, {estimated_duration} min estimated")
        return plan
    
    async def execute_campaign_orchestration(self, campaign_id: int) -> bool:
        """Execute unified tool orchestration for campaign
        
        Args:
            campaign_id: Campaign ID to execute
            
        Returns:
            True if orchestration started successfully
        """
        try:
            # Get or create orchestration plan
            if campaign_id not in self.orchestration_plans:
                await self.create_orchestration_plan(campaign_id)
            
            plan = self.orchestration_plans[campaign_id]
            
            # Initialize progress tracking
            progress = OrchestrationProgress(
                campaign_id=campaign_id,
                current_phase=OrchestrationPhase.RECONNAISSANCE,
                completed_steps=0,
                total_steps=len(plan.steps),
                progress_percentage=0.0,
                status="running"
            )
            self.active_orchestrations[campaign_id] = progress
            
            # Start campaign
            self.campaign_manager.start_campaign(campaign_id)
            
            # Emit orchestration started event
            await self.event_manager.emit_event(
                EventTypes.CAMPAIGN_STARTED,
                {
                    "campaign_id": campaign_id,
                    "orchestration_plan": asdict(plan),
                    "progress": asdict(progress)
                }
            )
            
            # Execute orchestration asynchronously
            asyncio.create_task(self._execute_orchestration_workflow(plan))
            
            self.logger.info(f"Started campaign orchestration for campaign {campaign_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to execute campaign orchestration {campaign_id}: {e}")
            return False
    
    async def _execute_orchestration_workflow(self, plan: OrchestrationPlan):
        """Execute the complete orchestration workflow
        
        Args:
            plan: Orchestration plan to execute
        """
        campaign_id = plan.campaign_id
        progress = self.active_orchestrations[campaign_id]
        
        try:
            # Group steps by phase for sequential phase execution
            phases = {}
            for step in plan.steps:
                if step.phase not in phases:
                    phases[step.phase] = []
                phases[step.phase].append(step)
            
            # Execute phases sequentially
            for phase in OrchestrationPhase:
                if phase not in phases:
                    continue
                    
                progress.current_phase = phase
                await self._update_progress(campaign_id, progress)
                
                self.logger.info(f"Campaign {campaign_id}: Starting phase {phase.value}")
                
                # Execute steps within phase (parallel if configured)
                phase_steps = phases[phase]
                if plan.parallel_execution:
                    await self._execute_steps_parallel(campaign_id, phase_steps)
                else:
                    await self._execute_steps_sequential(campaign_id, phase_steps)
            
            # Complete orchestration
            progress.status = "completed"
            progress.progress_percentage = 100.0
            await self._update_progress(campaign_id, progress)
            
            # Complete campaign
            self.campaign_manager.complete_campaign(campaign_id)
            
            await self.event_manager.emit_event(
                EventTypes.CAMPAIGN_COMPLETED,
                {
                    "campaign_id": campaign_id,
                    "final_progress": asdict(progress)
                }
            )
            
            self.logger.info(f"Campaign {campaign_id}: Orchestration completed successfully")
            
        except Exception as e:
            # Handle orchestration failure
            progress.status = "failed"
            await self._update_progress(campaign_id, progress)
            
            await self.event_manager.emit_event(
                EventTypes.CAMPAIGN_FAILED,
                {
                    "campaign_id": campaign_id,
                    "error": str(e),
                    "progress": asdict(progress)
                }
            )
            
            self.logger.error(f"Campaign {campaign_id}: Orchestration failed: {e}")
        
        finally:
            # Clean up active orchestration
            if campaign_id in self.active_orchestrations:
                del self.active_orchestrations[campaign_id]
    
    async def _execute_steps_parallel(self, campaign_id: int, steps: List[OrchestrationStep]):
        """Execute orchestration steps in parallel
        
        Args:
            campaign_id: Campaign ID
            steps: Steps to execute in parallel
        """
        tasks = []
        for step in steps:
            task = asyncio.create_task(self._execute_single_step(campaign_id, step))
            tasks.append(task)
        
        # Wait for all parallel steps to complete
        await asyncio.gather(*tasks, return_exceptions=True)
    
    async def _execute_steps_sequential(self, campaign_id: int, steps: List[OrchestrationStep]):
        """Execute orchestration steps sequentially
        
        Args:
            campaign_id: Campaign ID  
            steps: Steps to execute sequentially
        """
        for step in steps:
            await self._execute_single_step(campaign_id, step)
    
    async def _execute_single_step(self, campaign_id: int, step: OrchestrationStep):
        """Execute a single orchestration step
        
        Args:
            campaign_id: Campaign ID
            step: Orchestration step to execute
        """
        progress = self.active_orchestrations.get(campaign_id)
        if not progress:
            return
        
        try:
            progress.current_tool = step.tool_id
            await self._update_progress(campaign_id, progress)
            
            self.logger.info(f"Campaign {campaign_id}: Executing {step.tool_id} on {step.target}")
            
            # Execute tool scan
            scan_result = await self.tool_manager.execute_scan(
                tool_id=step.tool_id,
                target=step.target,
                scan_type=step.scan_type,
                timeout=step.timeout_seconds
            )
            
            # Update progress
            progress.completed_steps += 1
            progress.progress_percentage = (progress.completed_steps / progress.total_steps) * 100
            await self._update_progress(campaign_id, progress)
            
            # Emit step completed event
            await self.event_manager.emit_event(
                EventTypes.SCAN_COMPLETED,
                {
                    "campaign_id": campaign_id,
                    "step": asdict(step),
                    "result": scan_result,
                    "progress": asdict(progress)
                }
            )
            
        except Exception as e:
            self.logger.error(f"Campaign {campaign_id}: Step {step.tool_id} failed: {e}")
            
            await self.event_manager.emit_event(
                EventTypes.SCAN_FAILED,
                {
                    "campaign_id": campaign_id,
                    "step": asdict(step),
                    "error": str(e)
                }
            )
    
    async def _update_progress(self, campaign_id: int, progress: OrchestrationProgress):
        """Update and broadcast orchestration progress
        
        Args:
            campaign_id: Campaign ID
            progress: Current progress state
        """
        # Emit progress update event
        await self.event_manager.emit_event(
            EventTypes.CAMPAIGN_PROGRESS,
            {
                "campaign_id": campaign_id,
                "progress": asdict(progress)
            }
        )
    
    def get_orchestration_progress(self, campaign_id: int) -> Optional[OrchestrationProgress]:
        """Get current orchestration progress
        
        Args:
            campaign_id: Campaign ID
            
        Returns:
            Current progress or None if not active
        """
        return self.active_orchestrations.get(campaign_id)
    
    def get_orchestration_plan(self, campaign_id: int) -> Optional[OrchestrationPlan]:
        """Get orchestration plan for campaign
        
        Args:
            campaign_id: Campaign ID
            
        Returns:
            Orchestration plan or None if not found
        """
        return self.orchestration_plans.get(campaign_id)
    
    async def pause_orchestration(self, campaign_id: int) -> bool:
        """Pause active orchestration
        
        Args:
            campaign_id: Campaign ID to pause
            
        Returns:
            True if paused successfully
        """
        if campaign_id not in self.active_orchestrations:
            return False
        
        progress = self.active_orchestrations[campaign_id]
        progress.status = "paused"
        
        # Pause campaign
        self.campaign_manager.pause_campaign(campaign_id)
        
        await self.event_manager.emit_event(
            EventTypes.CAMPAIGN_PAUSED,
            {
                "campaign_id": campaign_id,
                "progress": asdict(progress)
            }
        )
        
        self.logger.info(f"Paused orchestration for campaign {campaign_id}")
        return True
    
    async def resume_orchestration(self, campaign_id: int) -> bool:
        """Resume paused orchestration
        
        Args:
            campaign_id: Campaign ID to resume
            
        Returns:
            True if resumed successfully
        """
        if campaign_id not in self.active_orchestrations:
            return False
        
        progress = self.active_orchestrations[campaign_id]
        if progress.status != "paused":
            return False
        
        progress.status = "running"
        
        # Resume campaign
        self.campaign_manager.start_campaign(campaign_id)
        
        await self.event_manager.emit_event(
            EventTypes.CAMPAIGN_RESUMED,
            {
                "campaign_id": campaign_id,
                "progress": asdict(progress)
            }
        )
        
        self.logger.info(f"Resumed orchestration for campaign {campaign_id}")
        return True
    
    def _is_ip_address(self, target: str) -> bool:
        """Check if target is an IP address"""
        import ipaddress
        try:
            ipaddress.ip_address(target)
            return True
        except ValueError:
            return False
    
    def _is_web_target(self, target: str) -> bool:
        """Check if target is a web URL or domain"""
        return target.startswith(('http://', 'https://')) or '.' in target


# Event type extensions for orchestration
class OrchestrationEventTypes:
    """Additional event types for orchestration"""
    ORCHESTRATION_STARTED = "orchestration_started"
    ORCHESTRATION_COMPLETED = "orchestration_completed"
    ORCHESTRATION_FAILED = "orchestration_failed"
    ORCHESTRATION_PAUSED = "orchestration_paused"
    ORCHESTRATION_RESUMED = "orchestration_resumed"
    ORCHESTRATION_PROGRESS = "orchestration_progress"
    PHASE_STARTED = "phase_started"
    PHASE_COMPLETED = "phase_completed"
    STEP_STARTED = "step_started"
    STEP_COMPLETED = "step_completed"
    STEP_FAILED = "step_failed"