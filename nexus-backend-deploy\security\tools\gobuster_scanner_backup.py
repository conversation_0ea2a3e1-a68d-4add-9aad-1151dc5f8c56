"""
Gobuster Scanner Integration for NexusScan Desktop Application
Directory/file enumeration and subdomain discovery using gobuster.
"""

import json
import asyncio
import logging
import subprocess
import re
from datetime import datetime
from typing import Dict, List, Optional, Callable, Any
from dataclasses import dataclass

from security.tools.tool_registry import (
    ToolMetadata, ToolCategory, ToolCapabilities,
    ScanOptions, ScanResult, ToolStatus, register_tool
)
from security.tools.base_scanner import BaseScanner

logger = logging.getLogger(__name__)


@dataclass
class GobusterScanOptions(ScanOptions):
    """Gobuster-specific scan options"""
    mode: str = "dir"  # dir, dns, vhost, fuzz
    wordlist: str = "/usr/share/wordlists/dirb/common.txt"
    extensions: List[str] = None
    user_agent: str = ""
    username: str = ""
    password: str = ""
    proxy: str = ""
    cookies: str = ""
    headers: List[str] = None
    threads: int = 10
    timeout: int = 10
    delay: str = ""
    status_codes: List[int] = None
    exclude_length: List[int] = None
    include_length: List[int] = None
    wildcard: bool = False
    expanded: bool = False
    no_status: bool = False
    no_error: bool = False
    
    def __post_init__(self):
        super().__post_init__()
        if self.extensions is None:
            self.extensions = []
        if self.headers is None:
            self.headers = []
        if self.status_codes is None:
            self.status_codes = [200, 204, 301, 302, 307, 401, 403]
        if self.exclude_length is None:
            self.exclude_length = []
        if self.include_length is None:
            self.include_length = []


@dataclass
class GobusterResult:
    """Gobuster scan result entry"""
    url: str
    status_code: int
    size: int
    redirect_location: str = ""
    method: str = "GET"


class GobusterScanner(BaseScanner):
    """Gobuster directory/file enumeration and subdomain discovery"""
    
    def __init__(self):
        super().__init__()
        self.tool_name = "gobuster"
        self.scan_types = ["dir", "dns", "vhost", "fuzz"]
        self.current_process = None
        
    def get_metadata(self) -> ToolMetadata:
        """Get tool metadata"""
        return ToolMetadata(
            name="Gobuster",
            category=ToolCategory.WEB_SCANNER,
            description="Directory/file enumeration and subdomain discovery",
            version=self._get_version(),
            author="OJ Reeves",
            dependencies=["gobuster"],
            capabilities=ToolCapabilities(
                scan_types=self.scan_types,
                output_formats=["json", "txt"],
                authentication_support=True,
                custom_headers_support=True,
                proxy_support=True,
                rate_limiting=True
            )
        )
    
    def _get_version(self) -> str:
        """Get Gobuster version"""
        try:
            result = subprocess.run(
                ["gobuster", "version"],
                capture_output=True,
                text=True,
                timeout=10
            )
            if result.returncode == 0:
                version_match = re.search(r"v([\d.]+)", result.stdout)
                if version_match:
                    return version_match.group(1)
            return "3.6"  # Default fallback
        except Exception as e:
            logger.warning(f"Could not get Gobuster version: {e}")
            return "3.6"
    
    def is_available(self) -> bool:
        """Check if Gobuster is available"""
        try:
            result = subprocess.run(
                ["gobuster", "version"],
                capture_output=True,
                text=True,
                timeout=10
            )
            return result.returncode == 0
        except (FileNotFoundError, subprocess.TimeoutExpired):
            return False
    
    async def scan(self, 
                   target: str, 
                   options: Optional[GobusterScanOptions] = None,
                   progress_callback: Optional[Callable] = None) -> ScanResult:
        """Perform Gobuster scan"""
        if options is None:
            options = GobusterScanOptions(target=target)
        
        scan_id = f"gobuster_{options.mode}"
    
    def check_native_availability(self) -> bool:
        """Check if native tool is available"""
        return self.is_available()
    
    async def execute_native_scan(self, options: ScanOptions,
                                 progress_callback: Optional[Callable[[float, str], None]] = None) -> ScanResult:}"
        """Execute native scan""}"
        if not isinstance(options, GobusterScanOptions):
            options = GobusterScanOptions(target=options.target)
        
        return await self.scan(options.target, options, progress_callback)}_{target.replace('://', '_').replace('/', '_')}_{int(datetime.now().timestamp())}"
        start_time = datetime.now()
        
        logger.info(f"Starting Gobuster scan: {scan_id}")
        
        if progress_callback:
            await progress_callback(0.1, "Initializing", f"Preparing Gobuster {options.mode} scan")
        
        try:
            # Build command
            command = self._build_command(target, options)
            
            if progress_callback:
                await progress_callback(0.2, "Scanning", f"Running Gobuster {options.mode} enumeration")
            
            # Execute scan
            process_result = await self._execute_command(command, progress_callback)
            
            if progress_callback:
                await progress_callback(0.8, "Processing", "Parsing scan results")
            
            # Parse results
            parsed_results = self._parse_output(process_result["stdout"], target, options.mode)
            
            if progress_callback:
                await progress_callback(1.0, "Complete", "Gobuster scan completed")
            
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            return ScanResult(
                scan_id=scan_id,
                tool_name="gobuster",
                target=target,
                status=ToolStatus.COMPLETED,
                start_time=start_time,
                end_time=end_time,
                duration_seconds=duration,
                command_executed=" ".join(command),
                exit_code=process_result["exit_code"],
                raw_output=process_result["stdout"],
                error_output=process_result["stderr"],
                parsed_results=parsed_results,
                vulnerabilities=parsed_results.get("vulnerabilities", []),
                summary=parsed_results.get("summary", {}),
                metadata={
                    "gobuster_version": self._get_version(),
                    "scan_mode": options.mode,
                    "wordlist": options.wordlist,
                    "scan_options": {
                        "extensions": options.extensions,
                        "threads": options.threads,
                        "timeout": options.timeout,
                        "status_codes": options.status_codes
                    }
                }
            )
            
        except Exception as e:
            logger.error(f"Gobuster scan failed: {e}")
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            return ScanResult(
                scan_id=scan_id,
                tool_name="gobuster",
                target=target,
                status=ToolStatus.FAILED,
                start_time=start_time,
                end_time=end_time,
                duration_seconds=duration,
                command_executed="",
                exit_code=1,
                raw_output="",
                error_output=str(e),
                parsed_results={},
                vulnerabilities=[],
                summary={"error": str(e)},
                metadata={}
            )
    
    def _build_command(self, target: str, options: GobusterScanOptions) -> List[str]:
        """Build Gobuster command"""
        command = ["gobuster", options.mode]
        
        # Target URL or domain
        if options.mode == "dns":
            command.extend(["-d", target])
        elif options.mode == "vhost":
            command.extend(["-u", target])
        else:  # dir, fuzz
            command.extend(["-u", target])
        
        # Wordlist
        if options.wordlist:
            command.extend(["-w", options.wordlist])
        
        # Extensions (for dir mode)
        if options.extensions and options.mode in ["dir", "fuzz"]:
            command.extend(["-x", ",".join(options.extensions)])
        
        # User agent
        if options.user_agent:
            command.extend(["-a", options.user_agent])
        
        # Authentication
        if options.username and options.password:
            command.extend(["-U", options.username, "-P", options.password])
        
        # Proxy
        if options.proxy:
            command.extend(["-p", options.proxy])
        
        # Cookies
        if options.cookies:
            command.extend(["-c", options.cookies])
        
        # Headers
        for header in options.headers:
            command.extend(["-H", header])
        
        # Threads
        if options.threads:
            command.extend(["-t", str(options.threads)])
        
        # Timeout
        if options.timeout:
            command.extend(["--timeout", f"{options.timeout}s"])
        
        # Delay
        if options.delay:
            command.extend(["--delay", options.delay])
        
        # Status codes
        if options.status_codes:
            command.extend(["-s", ",".join(map(str, options.status_codes))])
        
        # Exclude length
        if options.exclude_length:
            command.extend(["--exclude-length", ",".join(map(str, options.exclude_length))])
        
        # Include length
        if options.include_length:
            command.extend(["--include-length", ",".join(map(str, options.include_length))])
        
        # Wildcard
        if options.wildcard:
            command.append("--wildcard")
        
        # Expanded
        if options.expanded:
            command.append("-e")
        
        # No status
        if options.no_status:
            command.append("-n")
        
        # No error
        if options.no_error:
            command.append("-z")
        
        # Quiet mode for cleaner output
        command.append("-q")
        
        return command
    
    async def _execute_command(self, command: List[str], progress_callback: Optional[Callable] = None) -> Dict[str, Any]:
        """Execute Gobuster command"""
        try:
            process = await asyncio.create_subprocess_exec(
                *command,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            self.current_process = process
            
            # Monitor progress
            if progress_callback:
                monitor_task = asyncio.create_task(
                    self._monitor_progress(process, progress_callback)
                )
            
            stdout, stderr = await process.communicate()
            
            if progress_callback:
                monitor_task.cancel()
            
            return {
                "exit_code": process.returncode,
                "stdout": stdout.decode('utf-8', errors='ignore'),
                "stderr": stderr.decode('utf-8', errors='ignore')
            }
            
        except Exception as e:
            logger.error(f"Command execution failed: {e}")
            return {
                "exit_code": 1,
                "stdout": "",
                "stderr": str(e)
            }
        finally:
            self.current_process = None
    
    async def _monitor_progress(self, process, progress_callback):
        """Monitor scan progress"""
        try:
            progress = 0.2
            while process.returncode is None:
                await asyncio.sleep(2)
                progress = min(0.7, progress + 0.05)
                await progress_callback(progress, "Scanning", "Gobuster enumeration in progress")
        except asyncio.CancelledError:
            pass
    
    def _parse_output(self, output: str, target: str, mode: str) -> Dict[str, Any]:
        """Parse Gobuster output"""
        parsed = {
            "discovered_items": [],
            "vulnerabilities": [],
            "summary": {
                "total_found": 0,
                "target": target,
                "mode": mode,
                "scan_time": datetime.now().isoformat()
            },
            "statistics": {
                "status_codes": {},
                "file_extensions": {},
                "directory_count": 0,
                "file_count": 0
            }
        }
        
        lines = output.split('\n')
        discovered_items = []
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
            
            # Parse different modes
            if mode == "dir":
                item = self._parse_dir_line(line, target)
            elif mode == "dns":
                item = self._parse_dns_line(line, target)
            elif mode == "vhost":
                item = self._parse_vhost_line(line, target)
            elif mode == "fuzz":
                item = self._parse_fuzz_line(line, target)
            else:
                item = None
            
            if item:
                discovered_items.append(item)
        
        # Process discovered items
        parsed['discovered_items'] = discovered_items
        parsed['summary']['total_found'] = len(discovered_items)
        
        # Generate statistics
        self._generate_statistics(parsed, discovered_items, mode)
        
        # Generate vulnerabilities from interesting findings
        parsed['vulnerabilities'] = self._generate_vulnerabilities(discovered_items, target, mode)
        
        return parsed
    
    def _parse_dir_line(self, line: str, target: str) -> Optional[Dict[str, Any]]:
        """Parse directory mode line"""
        # Format: /path (Status: 200) [Size: 1234]
        match = re.match(r'(.+?)\s+\(Status:\s+(\d+)\)\s+\[Size:\s+(\d+)\]', line)
        if match:
            path = match.group(1)
            status_code = int(match.group(2))
            size = int(match.group(3))
            
            full_url = f"{target.rstrip('/')}{path}"
            
            return {
                "url": full_url,
                "path": path,
                "status_code": status_code,
                "size": size,
                "type": "directory" if path.endswith('/') else "file"
            }
        return None
    
    def _parse_dns_line(self, line: str, target: str) -> Optional[Dict[str, Any]]:
        """Parse DNS mode line"""
        # Format: Found: subdomain.example.com
        if line.startswith('Found: '):
            subdomain = line.replace('Found: ', '').strip()
            return {
                "subdomain": subdomain,
                "type": "subdomain",
                "status": "found"
            }
        return None
    
    def _parse_vhost_line(self, line: str, target: str) -> Optional[Dict[str, Any]]:
        """Parse vhost mode line"""
        # Format: Found: vhost.example.com (Status: 200) [Size: 1234]
        match = re.match(r'Found:\s+(.+?)\s+\(Status:\s+(\d+)\)\s+\[Size:\s+(\d+)\]', line)
        if match:
            vhost = match.group(1)
            status_code = int(match.group(2))
            size = int(match.group(3))
            
            return {
                "vhost": vhost,
                "status_code": status_code,
                "size": size,
                "type": "vhost"
            }
        return None
    
    def _parse_fuzz_line(self, line: str, target: str) -> Optional[Dict[str, Any]]:
        """Parse fuzz mode line"""
        # Similar to dir mode but with fuzzing context
        match = re.match(r'(.+?)\s+\(Status:\s+(\d+)\)\s+\[Size:\s+(\d+)\]', line)
        if match:
            fuzzed_url = match.group(1)
            status_code = int(match.group(2))
            size = int(match.group(3))
            
            return {
                "url": fuzzed_url,
                "status_code": status_code,
                "size": size,
                "type": "fuzz_result"
            }
        return None
    
    def _generate_statistics(self, parsed: Dict[str, Any], discovered_items: List[Dict[str, Any]], mode: str):
        """Generate statistics from discovered items"""
        status_codes = {}
        file_extensions = {}
        directory_count = 0
        file_count = 0
        
        for item in discovered_items:
            # Status codes
            if 'status_code' in item:
                status_code = item['status_code']
                status_codes[status_code] = status_codes.get(status_code, 0) + 1
            
            # File extensions and types
            if mode == "dir":
                if item.get('type') == 'file':
                    file_count += 1
                    url = item.get('url', '')
                    if '.' in url:
                        ext = url.split('.')[-1].lower()
                        file_extensions[ext] = file_extensions.get(ext, 0) + 1
                else:
                    directory_count += 1
        
        parsed['statistics'] = {
            "status_codes": status_codes,
            "file_extensions": file_extensions,
            "directory_count": directory_count,
            "file_count": file_count
        }
    
    def _generate_vulnerabilities(self, discovered_items: List[Dict[str, Any]], target: str, mode: str) -> List[Dict[str, Any]]:
        """Generate vulnerability findings from discovered items"""
        vulnerabilities = []
        
        for item in discovered_items:
            if mode == "dir":
                url = item.get('url', '')
                path = item.get('path', '')
                status_code = item.get('status_code', 200)
                
                # Check for sensitive directories/files
                if any(keyword in path.lower() for keyword in [
                    'admin', 'config', 'backup', 'test', 'dev', 'debug',
                    'log', 'tmp', 'temp', 'old', 'bak', 'phpinfo', 'manager'
                ]):
                    vulnerabilities.append({
                        "id": f"gobuster_sensitive_{len(vulnerabilities)}",
                        "type": "Information Disclosure",
                        "severity": "medium",
                        "url": url,
                        "description": f"Potentially sensitive path discovered: {path}",
                        "recommendation": "Review if this path should be publicly accessible",
                        "status_code": status_code
                    })
                
                # Check for administrative interfaces
                if any(keyword in path.lower() for keyword in ['admin', 'manager', 'console', 'panel']):
                    vulnerabilities.append({
                        "id": f"gobuster_admin_{len(vulnerabilities)}",
                        "type": "Administrative Interface",
                        "severity": "high",
                        "url": url,
                        "description": f"Administrative interface discovered: {path}",
                        "recommendation": "Ensure proper authentication and access controls",
                        "status_code": status_code
                    })
            
            elif mode == "dns":
                subdomain = item.get('subdomain', '')
                if subdomain:
                    vulnerabilities.append({
                        "id": f"gobuster_subdomain_{len(vulnerabilities)}",
                        "type": "Subdomain Discovery",
                        "severity": "info",
                        "subdomain": subdomain,
                        "description": f"Subdomain discovered: {subdomain}",
                        "recommendation": "Review subdomain for additional attack surface",
                        "status": "found"
                    })
        
        return vulnerabilities
    
    async def stop_scan(self) -> bool:
        """Stop current scan"""
        if self.current_process:
            try:
                self.current_process.terminate()
                await asyncio.sleep(2)
                if self.current_process.returncode is None:
                    self.current_process.kill()
                return True
            except Exception as e:
                logger.error(f"Failed to stop scan: {e}")
                return False
        return True
    
    def get_scan_profiles(self) -> Dict[str, Dict[str, Any]]:
        """Get predefined scan profiles"""
        return {
            "dir": {
                "name": "Directory Enumeration",
                "description": "Discover directories and files",
                "options": {
                    "mode": "dir",
                    "extensions": ["php", "html", "txt", "js", "css"],
                    "threads": 10
                }
            },
            "dns": {
                "name": "Subdomain Discovery",
                "description": "Discover subdomains",
                "options": {
                    "mode": "dns",
                    "threads": 50
                }
            },
            "vhost": {
                "name": "Virtual Host Discovery",
                "description": "Discover virtual hosts",
                "options": {
                    "mode": "vhost",
                    "threads": 20
                }
            },
            "fuzz": {
                "name": "Fuzzing Mode",
                "description": "Custom fuzzing with wordlist",
                "options": {
                    "mode": "fuzz",
                    "threads": 15
                }
            }
        }
    
    def get_frontend_interface_data(self) -> Dict[str, Any]:
        """Get data for frontend interface"""
        return {
            "tool_info": {
                "name": "Gobuster",
                "description": "Directory/file enumeration and subdomain discovery",
                "version": self._get_version(),
                "category": "Web Scanner",
                "status": "available" if self.is_available() else "unavailable"
            },
            "scan_options": {
                "target": {
                    "type": "text",
                    "required": True,
                    "placeholder": "https://example.com or example.com",
                    "validation": "url_or_domain"
                },
                "mode": {
                    "type": "select",
                    "options": ["dir", "dns", "vhost", "fuzz"],
                    "default": "dir",
                    "label": "Scan mode"
                },
                "wordlist": {
                    "type": "select",
                    "options": [
                        "/usr/share/wordlists/dirb/common.txt",
                        "/usr/share/wordlists/dirb/big.txt",
                        "/usr/share/wordlists/dirbuster/directory-list-2.3-medium.txt"
                    ],
                    "default": "/usr/share/wordlists/dirb/common.txt"
                },
                "extensions": {
                    "type": "multiselect",
                    "options": ["php", "html", "txt", "js", "css", "xml", "json", "asp", "jsp"],
                    "default": ["php", "html", "txt"]
                },
                "threads": {
                    "type": "number",
                    "default": 10,
                    "min": 1,
                    "max": 100,
                    "label": "Number of threads"
                },
                "timeout": {
                    "type": "number",
                    "default": 10,
                    "min": 1,
                    "max": 60,
                    "label": "Timeout (seconds)"
                }
            },
            "scan_profiles": self.get_scan_profiles(),
            "output_formats": ["json", "txt"],
            "capabilities": [
                "Directory enumeration",
                "File discovery",
                "Subdomain discovery",
                "Virtual host discovery",
                "Custom fuzzing",
                "Multi-threaded scanning"
            ]
        }


# Register the tool
register_tool(GobusterScanner)