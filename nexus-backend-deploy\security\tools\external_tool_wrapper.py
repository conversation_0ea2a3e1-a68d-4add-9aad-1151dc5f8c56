#!/usr/bin/env python3
"""
External Tool Wrapper for NexusScan Desktop
Wraps external security tools to work with Railway and simulation mode
"""

import asyncio
import logging
from typing import Dict, List, Optional, Callable, Any
from abc import abstractmethod
from datetime import datetime

from security.tools.tool_registry import (
    ToolMetadata, ToolCategory, ToolCapabilities,
    ScanOptions, ScanResult, ToolStatus, register_tool
)
from security.tools.base_scanner import BaseScanner

logger = logging.getLogger(__name__)


class ExternalToolWrapper(BaseScanner):
    """Base wrapper for external security tools"""
    
    def __init__(self):
        """Initialize external tool wrapper"""
        super().__init__()
        self.simulation_available = True
        self.tool_executable = None
    
    @abstractmethod
    def get_tool_command(self, options: ScanOptions) -> List[str]:
        """Get the command to execute for this tool"""
        pass
    
    @abstractmethod
    def parse_tool_output(self, output: str, target: str) -> Dict[str, Any]:
        """Parse the tool's output into structured format"""
        pass
    
    def check_native_availability(self) -> bool:
        """Check if the external tool is available"""
        try:
            import subprocess
            result = subprocess.run(
                [self.tool_executable, "--version"],
                capture_output=True,
                timeout=5
            )
            return result.returncode == 0
        except Exception:
            return False
    
    async def execute_native_scan(self, options: ScanOptions,
                                  progress_callback: Optional[Callable[[float, str], None]] = None) -> ScanResult:
        """Execute the external tool natively"""
        start_time = datetime.now().isoformat()
        
        try:
            if progress_callback:
                await progress_callback(0.1, f"Executing {self.metadata.name}...")
            
            # Get command to execute
            command = self.get_tool_command(options)
            
            if progress_callback:
                await progress_callback(0.3, f"Running: {' '.join(command)}")
            
            # Execute command
            process = await asyncio.create_subprocess_exec(
                *command,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await process.communicate()
            
            if progress_callback:
                await progress_callback(0.8, "Parsing tool output...")
            
            # Parse output
            output_text = stdout.decode('utf-8', errors='ignore')
            parsed_results = self.parse_tool_output(output_text, options.target)
            
            if progress_callback:
                await progress_callback(1.0, "Scan complete")
            
            return ScanResult(
                tool_name=self.metadata.name,
                target=options.target,
                status=ToolStatus.COMPLETED if process.returncode == 0 else ToolStatus.FAILED,
                start_time=start_time,
                end_time=datetime.now().isoformat(),
                duration_seconds=(datetime.now() - datetime.fromisoformat(start_time)).total_seconds(),
                raw_output=output_text,
                parsed_results=parsed_results,
                errors=[stderr.decode('utf-8', errors='ignore')] if stderr else []
            )
            
        except Exception as e:
            logger.error(f"External tool execution failed: {e}")
            return ScanResult(
                tool_name=self.metadata.name,
                target=options.target,
                status=ToolStatus.FAILED,
                start_time=start_time,
                end_time=datetime.now().isoformat(),
                errors=[str(e)]
            )


# Example wrapper for Gobuster
@register_tool
class GobusterWrapper(ExternalToolWrapper):
    """Gobuster directory/file brute force tool wrapper"""
    
    def __init__(self):
        super().__init__()
        self.tool_executable = "gobuster"
    
    def get_metadata(self) -> ToolMetadata:
        return ToolMetadata(
            name="gobuster",
            display_name="Gobuster",
            description="Directory/file bruteforcer",
            version="1.0.0",
            category=ToolCategory.WEB_SCANNER,
            capabilities=ToolCapabilities(
                supports_async=True,
                network_access_required=True,
                supported_targets=["url"]
            ),
            default_options={
                "scan_type": "dir",
                "wordlist": "/usr/share/wordlists/dirb/common.txt"
            }
        )
    
    def get_tool_command(self, options: ScanOptions) -> List[str]:
        """Get Gobuster command"""
        scan_type = options.custom_options.get("scan_type", "dir")
        wordlist = options.custom_options.get("wordlist", "/usr/share/wordlists/dirb/common.txt")
        
        command = [
            "gobuster", scan_type,
            "-u", options.target,
            "-w", wordlist,
            "-q"  # Quiet mode
        ]
        
        return command
    
    def parse_tool_output(self, output: str, target: str) -> Dict[str, Any]:
        """Parse Gobuster output"""
        lines = output.strip().split('\n')
        found_paths = []
        
        for line in lines:
            if line.startswith('/'):
                parts = line.split()
                if len(parts) >= 2:
                    path = parts[0]
                    status = parts[1].strip('()')
                    found_paths.append({
                        "path": path,
                        "status": status,
                        "url": f"{target.rstrip('/')}{path}"
                    })
        
        return {
            "target": target,
            "paths_found": found_paths,
            "total_found": len(found_paths)
        }


# Example wrapper for Nikto
@register_tool
class NiktoWrapper(ExternalToolWrapper):
    """Nikto web vulnerability scanner wrapper"""
    
    def __init__(self):
        super().__init__()
        self.tool_executable = "nikto"
    
    def get_metadata(self) -> ToolMetadata:
        return ToolMetadata(
            name="nikto",
            display_name="Nikto",
            description="Web server vulnerability scanner",
            version="1.0.0",
            category=ToolCategory.WEB_SCANNER,
            capabilities=ToolCapabilities(
                supports_async=True,
                network_access_required=True,
                supported_targets=["url", "domain"]
            )
        )
    
    def get_tool_command(self, options: ScanOptions) -> List[str]:
        """Get Nikto command"""
        return [
            "nikto",
            "-h", options.target,
            "-Format", "txt",
            "-nointeractive"
        ]
    
    def parse_tool_output(self, output: str, target: str) -> Dict[str, Any]:
        """Parse Nikto output"""
        lines = output.strip().split('\n')
        vulnerabilities = []
        server_info = {}
        
        for line in lines:
            if line.startswith('+ Server:'):
                server_info['server'] = line.split(':', 1)[1].strip()
            elif line.startswith('+ Target IP:'):
                server_info['ip'] = line.split(':', 1)[1].strip()
            elif line.startswith('+ '):
                # Potential vulnerability finding
                vulnerabilities.append({
                    "description": line[2:],  # Remove '+ ' prefix
                    "severity": "info"  # Default severity
                })
        
        return {
            "target": target,
            "server_info": server_info,
            "vulnerabilities": vulnerabilities,
            "findings_count": len(vulnerabilities)
        }