#!/usr/bin/env python3
"""
Advanced Caching Strategies for Security Tools
Specialized caching for scan results, vulnerability data, and AI-generated content
"""

import asyncio
import hashlib
import json
import time
import logging
from typing import Any, Dict, List, Optional, Tuple, Union
from dataclasses import dataclass, asdict
from datetime import datetime, timed<PERSON><PERSON>
from enum import Enum

from core.cache import <PERSON><PERSON><PERSON><PERSON><PERSON>, get_cache_manager, cached

logger = logging.getLogger(__name__)

class SecurityCacheType(Enum):
    """Security-specific cache types"""
    SCAN_RESULTS = "scan_results"
    VULNERABILITY_DATA = "vulnerability_data"
    AI_PAYLOADS = "ai_payloads"
    TOOL_OUTPUTS = "tool_outputs"
    TARGET_PROFILES = "target_profiles"
    THREAT_INTELLIGENCE = "threat_intelligence"
    EXPLOIT_TEMPLATES = "exploit_templates"

@dataclass
class ScanCacheKey:
    """Structured cache key for scan results"""
    tool_name: str
    target: str
    scan_type: str
    options_hash: str
    version: str = "1.0"
    
    def to_string(self) -> str:
        """Convert to cache key string"""
        data = asdict(self)
        key_str = json.dumps(data, sort_keys=True)
        return f"scan:{hashlib.md5(key_str.encode()).hexdigest()}"

@dataclass
class VulnerabilityCacheKey:
    """Structured cache key for vulnerability data"""
    vulnerability_id: str
    target_type: str
    context_hash: str
    version: str = "1.0"
    
    def to_string(self) -> str:
        """Convert to cache key string"""
        data = asdict(self)
        key_str = json.dumps(data, sort_keys=True)
        return f"vuln:{hashlib.md5(key_str.encode()).hexdigest()}"

@dataclass
class AICacheKey:
    """Structured cache key for AI-generated content"""
    ai_function: str
    input_hash: str
    model_version: str
    temperature: float
    version: str = "1.0"
    
    def to_string(self) -> str:
        """Convert to cache key string"""
        data = asdict(self)
        key_str = json.dumps(data, sort_keys=True)
        return f"ai:{hashlib.md5(key_str.encode()).hexdigest()}"

class SecurityCacheManager:
    """Specialized cache manager for security operations"""
    
    def __init__(self, base_cache_manager: Optional[CacheManager] = None):
        self.cache = base_cache_manager or get_cache_manager()
        
        # Cache TTL configurations for different data types
        self.ttl_config = {
            SecurityCacheType.SCAN_RESULTS: 3600,  # 1 hour
            SecurityCacheType.VULNERABILITY_DATA: 86400,  # 24 hours
            SecurityCacheType.AI_PAYLOADS: 7200,  # 2 hours
            SecurityCacheType.TOOL_OUTPUTS: 1800,  # 30 minutes
            SecurityCacheType.TARGET_PROFILES: 43200,  # 12 hours
            SecurityCacheType.THREAT_INTELLIGENCE: 3600,  # 1 hour
            SecurityCacheType.EXPLOIT_TEMPLATES: 86400,  # 24 hours
        }
        
        # Cache tags for organized cleanup
        self.cache_tags = {
            SecurityCacheType.SCAN_RESULTS: ["security", "scans"],
            SecurityCacheType.VULNERABILITY_DATA: ["security", "vulnerabilities"],
            SecurityCacheType.AI_PAYLOADS: ["ai", "payloads"],
            SecurityCacheType.TOOL_OUTPUTS: ["tools", "outputs"],
            SecurityCacheType.TARGET_PROFILES: ["targets", "profiles"],
            SecurityCacheType.THREAT_INTELLIGENCE: ["threat", "intelligence"],
            SecurityCacheType.EXPLOIT_TEMPLATES: ["exploits", "templates"],
        }
    
    async def cache_scan_result(self, 
                              tool_name: str,
                              target: str,
                              scan_type: str,
                              options: Dict[str, Any],
                              result: Any,
                              custom_ttl: Optional[int] = None) -> bool:
        """Cache scan result with structured key"""
        
        # Create options hash for cache key
        options_str = json.dumps(options, sort_keys=True)
        options_hash = hashlib.md5(options_str.encode()).hexdigest()
        
        # Create structured cache key
        cache_key = ScanCacheKey(
            tool_name=tool_name,
            target=target,
            scan_type=scan_type,
            options_hash=options_hash
        )
        
        # Get TTL
        ttl = custom_ttl or self.ttl_config[SecurityCacheType.SCAN_RESULTS]
        tags = self.cache_tags[SecurityCacheType.SCAN_RESULTS] + [tool_name, scan_type]
        
        # Add metadata to cached result
        cached_data = {
            "result": result,
            "metadata": {
                "tool_name": tool_name,
                "target": target,
                "scan_type": scan_type,
                "options": options,
                "cached_at": time.time(),
                "cache_version": "1.0"
            }
        }
        
        return await self.cache.set(cache_key.to_string(), cached_data, ttl, tags)
    
    async def get_scan_result(self,
                            tool_name: str,
                            target: str,
                            scan_type: str,
                            options: Dict[str, Any]) -> Optional[Any]:
        """Get cached scan result"""
        
        # Create options hash for cache key
        options_str = json.dumps(options, sort_keys=True)
        options_hash = hashlib.md5(options_str.encode()).hexdigest()
        
        # Create structured cache key
        cache_key = ScanCacheKey(
            tool_name=tool_name,
            target=target,
            scan_type=scan_type,
            options_hash=options_hash
        )
        
        cached_data = await self.cache.get(cache_key.to_string())
        if cached_data and isinstance(cached_data, dict):
            return cached_data.get("result")
        
        return None
    
    async def cache_vulnerability_analysis(self,
                                         vulnerability_id: str,
                                         target_type: str,
                                         context: Dict[str, Any],
                                         analysis: Any,
                                         custom_ttl: Optional[int] = None) -> bool:
        """Cache vulnerability analysis result"""
        
        # Create context hash for cache key
        context_str = json.dumps(context, sort_keys=True)
        context_hash = hashlib.md5(context_str.encode()).hexdigest()
        
        # Create structured cache key
        cache_key = VulnerabilityCacheKey(
            vulnerability_id=vulnerability_id,
            target_type=target_type,
            context_hash=context_hash
        )
        
        # Get TTL
        ttl = custom_ttl or self.ttl_config[SecurityCacheType.VULNERABILITY_DATA]
        tags = self.cache_tags[SecurityCacheType.VULNERABILITY_DATA] + [vulnerability_id, target_type]
        
        # Add metadata to cached analysis
        cached_data = {
            "analysis": analysis,
            "metadata": {
                "vulnerability_id": vulnerability_id,
                "target_type": target_type,
                "context": context,
                "analyzed_at": time.time(),
                "cache_version": "1.0"
            }
        }
        
        return await self.cache.set(cache_key.to_string(), cached_data, ttl, tags)
    
    async def get_vulnerability_analysis(self,
                                       vulnerability_id: str,
                                       target_type: str,
                                       context: Dict[str, Any]) -> Optional[Any]:
        """Get cached vulnerability analysis"""
        
        # Create context hash for cache key
        context_str = json.dumps(context, sort_keys=True)
        context_hash = hashlib.md5(context_str.encode()).hexdigest()
        
        # Create structured cache key
        cache_key = VulnerabilityCacheKey(
            vulnerability_id=vulnerability_id,
            target_type=target_type,
            context_hash=context_hash
        )
        
        cached_data = await self.cache.get(cache_key.to_string())
        if cached_data and isinstance(cached_data, dict):
            return cached_data.get("analysis")
        
        return None
    
    async def cache_ai_payload(self,
                             ai_function: str,
                             input_data: Any,
                             model_version: str,
                             temperature: float,
                             payload: Any,
                             custom_ttl: Optional[int] = None) -> bool:
        """Cache AI-generated payload"""
        
        # Create input hash for cache key
        input_str = json.dumps(input_data, sort_keys=True, default=str)
        input_hash = hashlib.md5(input_str.encode()).hexdigest()
        
        # Create structured cache key
        cache_key = AICacheKey(
            ai_function=ai_function,
            input_hash=input_hash,
            model_version=model_version,
            temperature=temperature
        )
        
        # Get TTL
        ttl = custom_ttl or self.ttl_config[SecurityCacheType.AI_PAYLOADS]
        tags = self.cache_tags[SecurityCacheType.AI_PAYLOADS] + [ai_function, model_version]
        
        # Add metadata to cached payload
        cached_data = {
            "payload": payload,
            "metadata": {
                "ai_function": ai_function,
                "input_data": input_data,
                "model_version": model_version,
                "temperature": temperature,
                "generated_at": time.time(),
                "cache_version": "1.0"
            }
        }
        
        return await self.cache.set(cache_key.to_string(), cached_data, ttl, tags)
    
    async def get_ai_payload(self,
                           ai_function: str,
                           input_data: Any,
                           model_version: str,
                           temperature: float) -> Optional[Any]:
        """Get cached AI-generated payload"""
        
        # Create input hash for cache key
        input_str = json.dumps(input_data, sort_keys=True, default=str)
        input_hash = hashlib.md5(input_str.encode()).hexdigest()
        
        # Create structured cache key
        cache_key = AICacheKey(
            ai_function=ai_function,
            input_hash=input_hash,
            model_version=model_version,
            temperature=temperature
        )
        
        cached_data = await self.cache.get(cache_key.to_string())
        if cached_data and isinstance(cached_data, dict):
            return cached_data.get("payload")
        
        return None
    
    async def cache_target_profile(self,
                                 target: str,
                                 profile_data: Dict[str, Any],
                                 custom_ttl: Optional[int] = None) -> bool:
        """Cache target profile information"""
        
        cache_key = f"target_profile:{hashlib.md5(target.encode()).hexdigest()}"
        ttl = custom_ttl or self.ttl_config[SecurityCacheType.TARGET_PROFILES]
        tags = self.cache_tags[SecurityCacheType.TARGET_PROFILES] + [f"target:{target}"]
        
        # Add metadata
        cached_data = {
            "profile": profile_data,
            "metadata": {
                "target": target,
                "profiled_at": time.time(),
                "cache_version": "1.0"
            }
        }
        
        return await self.cache.set(cache_key, cached_data, ttl, tags)
    
    async def get_target_profile(self, target: str) -> Optional[Dict[str, Any]]:
        """Get cached target profile"""
        
        cache_key = f"target_profile:{hashlib.md5(target.encode()).hexdigest()}"
        cached_data = await self.cache.get(cache_key)
        
        if cached_data and isinstance(cached_data, dict):
            return cached_data.get("profile")
        
        return None
    
    async def cache_threat_intelligence(self,
                                      threat_type: str,
                                      indicators: List[str],
                                      intelligence_data: Dict[str, Any],
                                      custom_ttl: Optional[int] = None) -> bool:
        """Cache threat intelligence data"""
        
        # Create cache key from threat type and indicators
        indicators_str = json.dumps(sorted(indicators))
        key_data = f"{threat_type}:{indicators_str}"
        cache_key = f"threat_intel:{hashlib.md5(key_data.encode()).hexdigest()}"
        
        ttl = custom_ttl or self.ttl_config[SecurityCacheType.THREAT_INTELLIGENCE]
        tags = self.cache_tags[SecurityCacheType.THREAT_INTELLIGENCE] + [threat_type]
        
        # Add metadata
        cached_data = {
            "intelligence": intelligence_data,
            "metadata": {
                "threat_type": threat_type,
                "indicators": indicators,
                "collected_at": time.time(),
                "cache_version": "1.0"
            }
        }
        
        return await self.cache.set(cache_key, cached_data, ttl, tags)
    
    async def get_threat_intelligence(self,
                                    threat_type: str,
                                    indicators: List[str]) -> Optional[Dict[str, Any]]:
        """Get cached threat intelligence"""
        
        # Create cache key from threat type and indicators
        indicators_str = json.dumps(sorted(indicators))
        key_data = f"{threat_type}:{indicators_str}"
        cache_key = f"threat_intel:{hashlib.md5(key_data.encode()).hexdigest()}"
        
        cached_data = await self.cache.get(cache_key)
        if cached_data and isinstance(cached_data, dict):
            return cached_data.get("intelligence")
        
        return None
    
    async def invalidate_by_target(self, target: str) -> int:
        """Invalidate all cache entries for a specific target"""
        target_tags = [f"target:{target}"]
        
        # If using hybrid cache with tag support
        if hasattr(self.cache.cache, 'delete_by_tags'):
            return await self.cache.cache.delete_by_tags(target_tags)
        
        return 0
    
    async def invalidate_by_tool(self, tool_name: str) -> int:
        """Invalidate all cache entries for a specific tool"""
        tool_tags = [tool_name]
        
        # If using hybrid cache with tag support
        if hasattr(self.cache.cache, 'delete_by_tags'):
            return await self.cache.cache.delete_by_tags(tool_tags)
        
        return 0
    
    async def invalidate_ai_cache(self, ai_function: Optional[str] = None) -> int:
        """Invalidate AI-generated content cache"""
        if ai_function:
            tags = [ai_function]
        else:
            tags = ["ai"]
        
        # If using hybrid cache with tag support
        if hasattr(self.cache.cache, 'delete_by_tags'):
            return await self.cache.cache.delete_by_tags(tags)
        
        return 0
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics with security-specific metrics"""
        base_stats = self.cache.cache.get_stats()
        
        # Add security-specific statistics
        security_stats = {
            "base_stats": base_stats,
            "ttl_config": self.ttl_config,
            "cache_types": list(SecurityCacheType),
            "timestamp": time.time()
        }
        
        return security_stats

# Convenience decorators for security-specific caching
def cached_scan_result(tool_name: str, ttl: Optional[int] = None):
    """Decorator for caching scan results"""
    def decorator(func):
        async def wrapper(target: str, scan_type: str, options: Dict[str, Any], *args, **kwargs):
            cache_manager = SecurityCacheManager()
            
            # Try to get from cache first
            cached_result = await cache_manager.get_scan_result(tool_name, target, scan_type, options)
            if cached_result is not None:
                logger.debug(f"Cache hit for {tool_name} scan of {target}")
                return cached_result
            
            # Execute function and cache result
            logger.debug(f"Cache miss for {tool_name} scan of {target}, executing...")
            if asyncio.iscoroutinefunction(func):
                result = await func(target, scan_type, options, *args, **kwargs)
            else:
                result = func(target, scan_type, options, *args, **kwargs)
            
            # Cache the result
            await cache_manager.cache_scan_result(tool_name, target, scan_type, options, result, ttl)
            return result
        
        return wrapper
    return decorator

def cached_vulnerability_analysis(ttl: Optional[int] = None):
    """Decorator for caching vulnerability analysis"""
    def decorator(func):
        async def wrapper(vulnerability_id: str, target_type: str, context: Dict[str, Any], *args, **kwargs):
            cache_manager = SecurityCacheManager()
            
            # Try to get from cache first
            cached_analysis = await cache_manager.get_vulnerability_analysis(vulnerability_id, target_type, context)
            if cached_analysis is not None:
                logger.debug(f"Cache hit for vulnerability analysis {vulnerability_id}")
                return cached_analysis
            
            # Execute function and cache result
            logger.debug(f"Cache miss for vulnerability analysis {vulnerability_id}, executing...")
            if asyncio.iscoroutinefunction(func):
                result = await func(vulnerability_id, target_type, context, *args, **kwargs)
            else:
                result = func(vulnerability_id, target_type, context, *args, **kwargs)
            
            # Cache the result
            await cache_manager.cache_vulnerability_analysis(vulnerability_id, target_type, context, result, ttl)
            return result
        
        return wrapper
    return decorator

def cached_ai_payload(ai_function: str, model_version: str, temperature: float = 0.7, ttl: Optional[int] = None):
    """Decorator for caching AI-generated payloads"""
    def decorator(func):
        async def wrapper(input_data: Any, *args, **kwargs):
            cache_manager = SecurityCacheManager()
            
            # Try to get from cache first
            cached_payload = await cache_manager.get_ai_payload(ai_function, input_data, model_version, temperature)
            if cached_payload is not None:
                logger.debug(f"Cache hit for AI payload {ai_function}")
                return cached_payload
            
            # Execute function and cache result
            logger.debug(f"Cache miss for AI payload {ai_function}, executing...")
            if asyncio.iscoroutinefunction(func):
                result = await func(input_data, *args, **kwargs)
            else:
                result = func(input_data, *args, **kwargs)
            
            # Cache the result
            await cache_manager.cache_ai_payload(ai_function, input_data, model_version, temperature, result, ttl)
            return result
        
        return wrapper
    return decorator

# Global security cache manager instance
security_cache_manager: Optional[SecurityCacheManager] = None

def get_security_cache_manager() -> SecurityCacheManager:
    """Get global security cache manager instance"""
    global security_cache_manager
    if security_cache_manager is None:
        security_cache_manager = SecurityCacheManager()
    return security_cache_manager