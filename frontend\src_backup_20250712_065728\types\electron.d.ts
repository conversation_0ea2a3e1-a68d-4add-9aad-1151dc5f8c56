/**
 * TypeScript definitions for NexusScan Desktop Electron API
 * This file provides type safety for the Electron API exposed via preload script
 */

export interface ElectronAPI {
  // App information
  app: {
    getVersion: () => Promise<string>;
    getName: () => Promise<string>;
    getPlatform: () => Promise<string>;
    getArch: () => Promise<string>;
  };

  // Window management
  window: {
    minimize: () => Promise<void>;
    maximize: () => Promise<void>;
    close: () => Promise<void>;
    isMaximized: () => Promise<boolean>;
  };

  // File system operations
  fs: {
    showOpenDialog: (options: Electron.OpenDialogOptions) => Promise<Electron.OpenDialogReturnValue>;
    showSaveDialog: (options: Electron.SaveDialogOptions) => Promise<Electron.SaveDialogReturnValue>;
    readFile: (filePath: string) => Promise<{ success: boolean; content?: string; error?: string }>;
    writeFile: (filePath: string, content: string) => Promise<{ success: boolean; error?: string }>;
  };

  // Store operations (persistent storage)
  store: {
    get: (key: string) => Promise<any>;
    set: (key: string, value: any) => Promise<void>;
    delete: (key: string) => Promise<void>;
    clear: () => Promise<void>;
  };

  // Backend operations
  backend: {
    getStatus: () => Promise<BackendStatus>;
    connect: () => Promise<boolean>;
    disconnect: () => Promise<void>;
    executeRequest: (method: string, endpoint: string, data?: any) => Promise<any>;
    getAvailableTools: () => Promise<SecurityTool[]>;
    executeTool: (toolId: string, config: ToolExecutionConfig) => Promise<ToolExecutionResult>;
    getFerrariCapabilities: () => Promise<FerrariCapabilities>;
  };

  // WSL operations (Windows only)
  wsl: {
    getStatus: () => Promise<WSLStatus>;
    getDistributions: () => Promise<WSLDistribution[]>;
    installTools: () => Promise<ToolInstallationStatus[]>;
    executeCommand: (command: string, distribution?: string) => Promise<string>;
    executeSecurityTool: (tool: string, args: string[], options?: WSLExecutionOptions) => Promise<WSLExecutionResult>;
  };

  // Notifications
  notification: {
    show: (options: NotificationOptions) => Promise<void>;
  };

  // Shell operations
  shell: {
    openExternal: (url: string) => Promise<void>;
    showItemInFolder: (path: string) => Promise<void>;
  };

  // Event listeners
  on: (channel: string, callback: (...args: any[]) => void) => void;
  off: (channel: string, callback: (...args: any[]) => void) => void;
  once: (channel: string, callback: (...args: any[]) => void) => void;
  removeAllListeners: (channel: string) => void;
}

// Backend Types
export interface BackendStatus {
  connected: boolean;
  url: string;
  lastPing: Date | null;
  version: string | null;
  health: 'healthy' | 'degraded' | 'unhealthy' | 'unknown';
  toolsCount: number;
  latency: number | null;
}

// Security Tool Types
export interface SecurityTool {
  id: string;
  name: string;
  category: ToolCategory;
  description: string;
  version: string | null;
  installed: boolean;
  platform: 'windows' | 'linux' | 'wsl' | 'cross-platform';
  capabilities: ToolCapabilities;
  parameters: ToolParameter[];
}

export type ToolCategory = 
  | 'network_scanning'
  | 'web_testing'
  | 'vulnerability_assessment'
  | 'password_tools'
  | 'ssl_testing'
  | 'exploitation'
  | 'ai_tools';

export interface ToolCapabilities {
  realTimeOutput: boolean;
  batchExecution: boolean;
  configurable: boolean;
  requiresTarget: boolean;
  outputFormats: string[];
}

export interface ToolParameter {
  name: string;
  type: 'string' | 'number' | 'boolean' | 'array' | 'file' | 'select';
  required: boolean;
  description: string;
  default?: any;
  options?: string[];
  validation?: {
    min?: number;
    max?: number;
    pattern?: string;
  };
}

export interface ToolExecutionConfig {
  target?: string;
  parameters: Record<string, any>;
  outputFormat?: string;
  timeout?: number;
  saveResults?: boolean;
}

export interface ToolExecutionResult {
  id: string;
  tool: string;
  status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';
  startTime: Date;
  endTime?: Date;
  output: string;
  error?: string;
  exitCode?: number;
  results?: any;
  metadata: {
    duration?: number;
    target?: string;
    parameters: Record<string, any>;
  };
}

// Ferrari AI Types
export interface FerrariCapabilities {
  orchestrator: OrchestratorCapabilities;
  creativeExploits: CreativeExploitCapabilities;
  behavioralAnalysis: BehavioralAnalysisCapabilities;
  proxy: AIProxyCapabilities;
}

export interface OrchestratorCapabilities {
  available: boolean;
  mitreIntegration: boolean;
  chainTemplates: AttackChainTemplate[];
  realTimeAdaptation: boolean;
  safetyConstraints: SafetyRule[];
}

export interface CreativeExploitCapabilities {
  available: boolean;
  payloadTypes: string[];
  confidenceScoring: boolean;
  mutationEngine: boolean;
  noveltyDetection: boolean;
}

export interface BehavioralAnalysisCapabilities {
  available: boolean;
  anomalyDetection: boolean;
  patternRecognition: boolean;
  predictiveAnalytics: boolean;
  baselineEstablishment: boolean;
}

export interface AIProxyCapabilities {
  available: boolean;
  intelligentRotation: boolean;
  trafficAnalysis: boolean;
  detectionAvoidance: boolean;
  performanceOptimization: boolean;
}

export interface AttackChainTemplate {
  id: string;
  name: string;
  description: string;
  mitreMapping: string[];
  stages: AttackStage[];
}

export interface AttackStage {
  id: string;
  name: string;
  techniques: string[];
  tools: string[];
  dependencies: string[];
}

export interface SafetyRule {
  id: string;
  name: string;
  description: string;
  type: 'target_validation' | 'technique_restriction' | 'time_limit' | 'data_protection';
  active: boolean;
}

// WSL Types
export interface WSLStatus {
  available: boolean;
  version: number | null;
  distributions: WSLDistribution[];
  currentDistribution: string | null;
  toolsInstalled: string[];
  installationProgress: number;
}

export interface WSLDistribution {
  name: string;
  version: number;
  default: boolean;
  state: 'Running' | 'Stopped' | 'Installing' | 'Converting';
}

export interface ToolInstallationStatus {
  tool: string;
  installed: boolean;
  version: string | null;
  error: string | null;
}

export interface WSLExecutionOptions {
  timeout?: number;
  distribution?: string;
  workingDirectory?: string;
}

export interface WSLExecutionResult {
  stdout: string;
  stderr: string;
  exitCode: number;
  duration: number;
}

// Notification Types
export interface NotificationOptions {
  title: string;
  body: string;
  icon?: string;
  tag?: string;
  silent?: boolean;
}

// Event Types
export type ElectronEventChannel = 
  | 'navigate'
  | 'open-campaign'
  | 'import-config'
  | 'export-config'
  | 'export-report'
  | 'open-search'
  | 'toggle-terminal'
  | 'toggle-sidebar'
  | 'show-ai-status'
  | 'cascade-windows'
  | 'tile-windows'
  | 'show-shortcuts'
  | 'check-updates'
  | 'backend-status-changed'
  | 'backend-connected'
  | 'backend-disconnected'
  | 'backend-error'
  | 'websocket-message'
  | 'wsl-status-changed'
  | 'tool-output'
  | 'tool-progress'
  | 'scan-completed'
  | 'ai-response';

// Global declarations
declare global {
  interface Window {
    electronAPI: ElectronAPI;
  }
}

export {};