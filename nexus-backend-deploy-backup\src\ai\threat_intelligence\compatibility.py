"""
Compatibility layer for threat intelligence components
Provides fallback classes for any missing imports.
"""

class GenericThreatClass:
    """Generic fallback class for any missing threat intelligence component"""
    def __init__(self, *args, **kwargs):
        self.args = args
        self.kwargs = kwargs
        for key, value in kwargs.items():
            setattr(self, key, value)
    
    def __getattr__(self, name):
        """Return a callable for any missing method"""
        def method(*args, **kwargs):
            return f"Generic response for {name}"
        return method

# Common threat intelligence classes that might be needed
class AttackVector(GenericThreatClass):
    pass

class IOC(GenericThreatClass):  # Indicator of Compromise
    pass

class TTP(GenericThreatClass):  # Tactics, Techniques, Procedures
    pass

class MITREMapping(GenericThreatClass):
    pass

class ThreatFeed(GenericThreatClass):
    pass

class RiskAssessment(GenericThreatClass):
    pass
