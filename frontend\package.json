{"name": "nexusscan-web", "version": "1.0.0", "description": "NexusScan Web - Professional Penetration Testing Platform", "type": "module", "homepage": "./", "author": {"name": "NexusScan Team", "email": "<EMAIL>"}, "license": "MIT", "private": true, "scripts": {"dev": "vite --config vite.config.web.ts", "build": "vite build --config vite.config.web.ts", "preview": "vite preview --config vite.config.web.ts", "deploy": "npm run build && npm run deploy:vercel", "deploy:vercel": "vercel --prod", "deploy:netlify": "netlify deploy --prod --dir=dist-web", "deploy:github": "gh-pages -d dist-web", "type-check": "tsc --noEmit", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix", "format": "prettier --write \"src/**/*.{ts,tsx,json,css,md}\"", "test": "vitest", "test:ui": "vitest --ui", "test:run": "vitest run", "test:coverage": "vitest run --coverage", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:e2e:headed": "playwright test --headed", "analyze": "npm run build && npx vite-bundle-analyzer dist-web/stats.html", "clean": "rimraf dist-web node_modules/.vite"}, "dependencies": {"@hookform/resolvers": "^3.3.2", "@radix-ui/react-slot": "^1.2.3", "@tanstack/react-query": "^5.0.0", "@xterm/addon-fit": "^0.10.0", "@xterm/addon-web-links": "^0.11.0", "@xterm/xterm": "^5.5.0", "@xyflow/react": "^12.0.0", "axios": "^1.6.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^0.2.0", "date-fns": "^2.30.0", "framer-motion": "^10.16.4", "immer": "^10.0.3", "lucide-react": "^0.294.0", "monaco-editor": "^0.45.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-error-boundary": "^6.0.0", "react-hook-form": "^7.47.0", "react-router-dom": "^6.18.0", "react-virtualized": "^9.22.5", "recharts": "^2.8.0", "sonner": "^1.2.0", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "zod": "^3.22.4", "zustand": "^4.4.7"}, "devDependencies": {"@playwright/test": "^1.40.0", "@testing-library/jest-dom": "^6.1.4", "@testing-library/react": "^14.0.0", "@testing-library/user-event": "^14.5.1", "@types/node": "^20.8.10", "@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@types/react-virtualized": "^9.21.24", "@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0", "@vitejs/plugin-react": "^4.1.1", "@vitest/ui": "^3.2.4", "autoprefixer": "^10.4.16", "c8": "^8.0.1", "eslint": "^8.53.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "jsdom": "^22.1.0", "playwright": "^1.40.0", "postcss": "^8.4.31", "prettier": "^3.0.3", "rimraf": "^5.0.5", "tailwindcss": "^3.3.5", "typescript": "^5.2.2", "vite": "^7.0.4", "vite-plugin-pwa": "^0.20.5", "vitest": "^3.2.4"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}}