"""
GDPR Compliance Checker for NexusScan Desktop Application
General Data Protection Regulation compliance assessment and validation.
"""

import json
import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Callable, Any
from dataclasses import dataclass
from enum import Enum

from security.tools.tool_registry import (
    ToolMetadata, ToolCategory, ToolC<PERSON>bilities,
    ScanOptions, ScanResult, ToolStatus, register_tool
)
from security.tools.base_scanner import BaseScanner
from ai.services import AIServiceManager, AIProvider

logger = logging.getLogger(__name__)


class GDPRArticle(Enum):
    """GDPR Article enumeration"""
    LAWFUL_BASIS = "art_6"
    CONSENT = "art_7"
    CHILDREN = "art_8"
    PROCESSING_RECORDS = "art_30"
    SECURITY_PROCESSING = "art_32"
    DATA_BREACH = "art_33"
    BREACH_NOTIFICATION = "art_34"
    IMPACT_ASSESSMENT = "art_35"
    DATA_CONTROLLER = "art_24"
    DATA_PROCESSOR = "art_28"


class ComplianceStatus(Enum):
    """GDPR compliance status enumeration"""
    COMPLIANT = "compliant"
    NON_COMPLIANT = "non_compliant"
    PARTIALLY_COMPLIANT = "partially_compliant"
    REQUIRES_ASSESSMENT = "requires_assessment"
    NOT_APPLICABLE = "not_applicable"


@dataclass
class GDPROptions(ScanOptions):
    """GDPR compliance check options"""
    organization_type: str = "controller"  # controller, processor, joint_controller
    data_processing_activities: List[str] = None
    cross_border_transfers: bool = False
    high_risk_processing: bool = False
    automated_decision_making: bool = False
    special_categories: bool = False
    children_data: bool = False
    assessment_scope: str = "comprehensive"  # comprehensive, basic, specific
    include_technical_measures: bool = True
    include_organizational_measures: bool = True
    include_breach_procedures: bool = True
    previous_assessment_date: str = ""
    
    def __post_init__(self):
        super().__post_init__()
        if self.data_processing_activities is None:
            self.data_processing_activities = []


@dataclass
class GDPRPrinciple:
    """GDPR principle definition"""
    id: str
    name: str
    description: str
    requirements: List[str]
    assessment_criteria: List[str]
    evidence_required: List[str]


@dataclass
class GDPRRights:
    """GDPR data subject rights"""
    right_name: str
    article: str
    description: str
    implementation_required: bool
    procedures_needed: List[str]
    response_timeframe: str


class GDPRComplianceChecker(BaseScanner):
    """GDPR compliance assessment and validation"""
    
    def __init__(self):
        super().__init__()
        self.tool_name = "gdpr_compliance_checker"
        self.scan_types = ["comprehensive", "basic", "specific"]
        self.ai_manager = None
        self.gdpr_principles = self._load_gdpr_principles()
        self.data_subject_rights = self._load_data_subject_rights()
        
    def get_metadata(self) -> ToolMetadata:
        """Get tool metadata"""
        return ToolMetadata(
            name="GDPR Compliance Checker",
            category=ToolCategory.COMPLIANCE_CHECKER,
            description="General Data Protection Regulation compliance assessment",
            version="1.0.0",
            author="NexusScan Compliance Team",
            dependencies=["ai_services"],
            capabilities=ToolCapabilities(
                scan_types=self.scan_types,
                output_formats=["json", "html", "pdf"],
                ai_powered=True,
                real_time_analysis=True,
                compliance_assessment=True
            )
        )
    
    def is_available(self) -> bool:
        """Check if AI services are available"""
        try:
            from ai.services import AIServiceManager
            return True
        except ImportError:
            return False
    
    def _load_gdpr_principles(self) -> List[GDPRPrinciple]:
        """Load GDPR principles"""
        return [
            GDPRPrinciple(
                id="lawfulness",
                name="Lawfulness, fairness and transparency",
                description="Processing must be lawful, fair and transparent",
                requirements=[
                    "Identify lawful basis for processing",
                    "Ensure processing is fair",
                    "Provide clear privacy notices",
                    "Be transparent about data use"
                ],
                assessment_criteria=[
                    "Lawful basis documented",
                    "Privacy notices available",
                    "Data subjects informed",
                    "Processing activities recorded"
                ],
                evidence_required=[
                    "Privacy policy",
                    "Legal basis documentation",
                    "Data processing records",
                    "Consent records"
                ]
            ),
            GDPRPrinciple(
                id="purpose_limitation",
                name="Purpose limitation",
                description="Data collected for specified, explicit and legitimate purposes",
                requirements=[
                    "Define specific purposes",
                    "Ensure purposes are explicit",
                    "Verify purposes are legitimate",
                    "Restrict further processing"
                ],
                assessment_criteria=[
                    "Purposes clearly defined",
                    "Compatible use only",
                    "No function creep",
                    "Purpose binding"
                ],
                evidence_required=[
                    "Purpose documentation",
                    "Data mapping",
                    "Processing records",
                    "Change control records"
                ]
            ),
            GDPRPrinciple(
                id="data_minimisation",
                name="Data minimisation",
                description="Data adequate, relevant and limited to what is necessary",
                requirements=[
                    "Collect only necessary data",
                    "Ensure data is adequate",
                    "Verify data relevance",
                    "Regular data review"
                ],
                assessment_criteria=[
                    "Minimal data collection",
                    "Data necessity justified",
                    "Regular data audits",
                    "Data inventory maintained"
                ],
                evidence_required=[
                    "Data inventory",
                    "Necessity assessments",
                    "Collection justification",
                    "Regular review records"
                ]
            ),
            GDPRPrinciple(
                id="accuracy",
                name="Accuracy",
                description="Data must be accurate and kept up to date",
                requirements=[
                    "Ensure data accuracy",
                    "Keep data up to date",
                    "Correct inaccurate data",
                    "Implement quality controls"
                ],
                assessment_criteria=[
                    "Data quality procedures",
                    "Regular updates",
                    "Correction mechanisms",
                    "Quality monitoring"
                ],
                evidence_required=[
                    "Data quality procedures",
                    "Update mechanisms",
                    "Correction logs",
                    "Quality reports"
                ]
            ),
            GDPRPrinciple(
                id="storage_limitation",
                name="Storage limitation",
                description="Data kept only as long as necessary",
                requirements=[
                    "Define retention periods",
                    "Implement deletion schedules",
                    "Regular data purging",
                    "Archive procedures"
                ],
                assessment_criteria=[
                    "Retention schedule exists",
                    "Automated deletion",
                    "Archive procedures",
                    "Regular purging"
                ],
                evidence_required=[
                    "Retention schedule",
                    "Deletion procedures",
                    "Archive policies",
                    "Purging logs"
                ]
            ),
            GDPRPrinciple(
                id="integrity_confidentiality",
                name="Integrity and confidentiality",
                description="Appropriate security for personal data",
                requirements=[
                    "Implement security measures",
                    "Protect data integrity",
                    "Ensure confidentiality",
                    "Prevent unauthorized access"
                ],
                assessment_criteria=[
                    "Security measures documented",
                    "Access controls implemented",
                    "Encryption where appropriate",
                    "Regular security testing"
                ],
                evidence_required=[
                    "Security policies",
                    "Access control records",
                    "Encryption evidence",
                    "Security test results"
                ]
            ),
            GDPRPrinciple(
                id="accountability",
                name="Accountability",
                description="Demonstrate compliance with GDPR principles",
                requirements=[
                    "Document compliance measures",
                    "Maintain evidence",
                    "Regular compliance reviews",
                    "Staff training"
                ],
                assessment_criteria=[
                    "Compliance documentation",
                    "Evidence maintained",
                    "Regular reviews",
                    "Training records"
                ],
                evidence_required=[
                    "Compliance documentation",
                    "Training records",
                    "Review reports",
                    "Policy documents"
                ]
            )
        ]
    
    def _load_data_subject_rights(self) -> List[GDPRRights]:
        """Load data subject rights"""
        return [
            GDPRRights(
                right_name="Right to be informed",
                article="Articles 13-14",
                description="Individuals have the right to be informed about data processing",
                implementation_required=True,
                procedures_needed=["Privacy notices", "Data collection notifications"],
                response_timeframe="At time of collection"
            ),
            GDPRRights(
                right_name="Right of access",
                article="Article 15",
                description="Individuals have the right to access their personal data",
                implementation_required=True,
                procedures_needed=["Subject access request procedures", "Data retrieval systems"],
                response_timeframe="1 month"
            ),
            GDPRRights(
                right_name="Right to rectification",
                article="Article 16",
                description="Individuals have the right to have inaccurate data corrected",
                implementation_required=True,
                procedures_needed=["Data correction procedures", "Verification processes"],
                response_timeframe="1 month"
            ),
            GDPRRights(
                right_name="Right to erasure",
                article="Article 17",
                description="Individuals have the right to have data erased",
                implementation_required=True,
                procedures_needed=["Data deletion procedures", "Erasure verification"],
                response_timeframe="1 month"
            ),
            GDPRRights(
                right_name="Right to restrict processing",
                article="Article 18",
                description="Individuals have the right to restrict processing",
                implementation_required=True,
                procedures_needed=["Processing restriction procedures", "Data flagging systems"],
                response_timeframe="1 month"
            ),
            GDPRRights(
                right_name="Right to data portability",
                article="Article 20",
                description="Individuals have the right to move data between services",
                implementation_required=True,
                procedures_needed=["Data export procedures", "Structured data formats"],
                response_timeframe="1 month"
            ),
            GDPRRights(
                right_name="Right to object",
                article="Article 21",
                description="Individuals have the right to object to processing",
                implementation_required=True,
                procedures_needed=["Objection handling procedures", "Processing cessation"],
                response_timeframe="1 month"
            ),
            GDPRRights(
                right_name="Rights related to automated decision making",
                article="Article 22",
                description="Rights related to automated decision making and profiling",
                implementation_required=False,
                procedures_needed=["Human review procedures", "Decision explanation"],
                response_timeframe="1 month"
            )
        ]
    
    async def _get_ai_manager(self) -> AIServiceManager:
        """Get AI service manager"""
        if self.ai_manager is None:
            from ai.services import AIServiceManager
            self.ai_manager = AIServiceManager()
        return self.ai_manager
    
    async def scan(self, 
                   target: str, 
                   options: Optional[GDPROptions] = None,
                   progress_callback: Optional[Callable] = None) -> ScanResult:
        """Perform GDPR compliance assessment"""
        if options is None:
            options = GDPROptions(target=target)
        
        scan_id = f"gdpr_{target.replace('://', '_').replace('/', '_')}"
    
    def check_native_availability(self) -> bool:
        """Check if native tool is available"""
        return self.is_available()
    
    async def execute_native_scan(self, options: ScanOptions,
                                 progress_callback: Optional[Callable[[float, str], None]] = None) -> ScanResult:
        """Execute native scan"""
        if not isinstance(options, GDPROptions):
            options = GDPROptions(target=options.target)
        
        return await self.scan(options.target, options, progress_callback)
        start_time = datetime.now()
        
        logger.info(f"Starting GDPR compliance assessment: {scan_id}")
        
        if progress_callback:
            await progress_callback(0.1, "Initializing", "Preparing GDPR compliance assessment")
        
        try:
            ai_manager = await self._get_ai_manager()
            
            # Phase 1: Data processing analysis
            if progress_callback:
                await progress_callback(0.15, "Data Processing", "Analyzing data processing activities")
            
            processing_analysis = await self._analyze_data_processing(target, options, ai_manager)
            
            # Phase 2: Principles compliance assessment
            if progress_callback:
                await progress_callback(0.25, "Principles Assessment", "Assessing GDPR principles compliance")
            
            principles_assessment = await self._assess_gdpr_principles(target, processing_analysis, options, ai_manager)
            
            # Phase 3: Data subject rights assessment
            if progress_callback:
                await progress_callback(0.35, "Rights Assessment", "Assessing data subject rights implementation")
            
            rights_assessment = await self._assess_data_subject_rights(target, processing_analysis, options, ai_manager)
            
            # Phase 4: Technical and organizational measures
            if progress_callback:
                await progress_callback(0.45, "Technical Measures", "Assessing technical and organizational measures")
            
            technical_measures = await self._assess_technical_measures(target, processing_analysis, options, ai_manager)
            
            # Phase 5: Cross-border transfers assessment
            if progress_callback:
                await progress_callback(0.55, "Cross-Border Transfers", "Assessing international data transfers")
            
            transfers_assessment = await self._assess_cross_border_transfers(target, processing_analysis, options, ai_manager)
            
            # Phase 6: Breach notification procedures
            if progress_callback:
                await progress_callback(0.65, "Breach Procedures", "Assessing breach notification procedures")
            
            breach_procedures = await self._assess_breach_procedures(target, processing_analysis, options, ai_manager)
            
            # Phase 7: Impact assessment requirements
            if progress_callback:
                await progress_callback(0.75, "Impact Assessment", "Assessing DPIA requirements")
            
            impact_assessment = await self._assess_impact_assessment(target, processing_analysis, options, ai_manager)
            
            # Phase 8: Governance and accountability
            if progress_callback:
                await progress_callback(0.85, "Governance", "Assessing governance and accountability measures")
            
            governance_assessment = await self._assess_governance(target, processing_analysis, options, ai_manager)
            
            # Phase 9: Compliance scoring and reporting
            if progress_callback:
                await progress_callback(0.95, "Compliance Scoring", "Calculating compliance scores and generating report")
            
            compliance_assessment = await self._generate_compliance_assessment(
                target, processing_analysis, principles_assessment, rights_assessment,
                technical_measures, transfers_assessment, breach_procedures,
                impact_assessment, governance_assessment, options, ai_manager
            )
            
            if progress_callback:
                await progress_callback(1.0, "Complete", "GDPR compliance assessment completed")
            
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            return ScanResult(
                scan_id=scan_id,
                tool_name="gdpr_compliance_checker",
                target=target,
                status=ToolStatus.COMPLETED,
                start_time=start_time,
                end_time=end_time,
                duration_seconds=duration,
                command_executed=f"gdpr_compliance_checker --target {target} --type {options.organization_type}",
                exit_code=0,
                raw_output=json.dumps(compliance_assessment, indent=2),
                error_output="",
                parsed_results=compliance_assessment,
                vulnerabilities=compliance_assessment.get("vulnerabilities", []),
                summary=compliance_assessment.get("summary", {}),
                metadata={
                    "organization_type": options.organization_type,
                    "assessment_scope": options.assessment_scope,
                    "ai_analysis_enabled": True,
                    "principles_assessed": len(self.gdpr_principles),
                    "rights_assessed": len(self.data_subject_rights),
                    "overall_compliance_score": compliance_assessment.get("overall_compliance_score", 0)
                }
            )
            
        except Exception as e:
            logger.error(f"GDPR compliance assessment failed: {e}")
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            return ScanResult(
                scan_id=scan_id,
                tool_name="gdpr_compliance_checker",
                target=target,
                status=ToolStatus.FAILED,
                start_time=start_time,
                end_time=end_time,
                duration_seconds=duration,
                command_executed="",
                exit_code=1,
                raw_output="",
                error_output=str(e),
                parsed_results={},
                vulnerabilities=[],
                summary={"error": str(e)},
                metadata={}
            )
    
    async def _analyze_data_processing(self, target: str, options: GDPROptions, ai_manager: AIServiceManager) -> Dict[str, Any]:
        """Analyze data processing activities"""
        prompt = f"""
        Analyze data processing activities for GDPR compliance assessment:
        
        Target: {target}
        Organization Type: {options.organization_type}
        Processing Activities: {options.data_processing_activities}
        Cross-border Transfers: {options.cross_border_transfers}
        High Risk Processing: {options.high_risk_processing}
        Special Categories: {options.special_categories}
        Children Data: {options.children_data}
        
        Please analyze:
        1. Personal data categories processed
        2. Processing purposes and lawful basis
        3. Data subjects categories
        4. Recipients and third parties
        5. Data retention periods
        6. International transfers
        7. Security measures
        8. Risk assessment
        
        Provide comprehensive analysis for GDPR compliance evaluation.
        """
        
        try:
            ai_response = await ai_manager.generate_analysis(
                prompt=prompt,
                analysis_type="gdpr_data_processing",
                provider=AIProvider.OPENAI
            )
            
            if ai_response and "analysis" in ai_response:
                return ai_response["analysis"]
            else:
                return self._generate_default_processing_analysis(options)
        
        except Exception as e:
            logger.warning(f"AI data processing analysis failed: {e}")
            return self._generate_default_processing_analysis(options)
    
    def _generate_default_processing_analysis(self, options: GDPROptions) -> Dict[str, Any]:
        """Generate default data processing analysis"""
        return {
            "personal_data_categories": [
                "Contact information",
                "Account details",
                "Usage data",
                "Technical data"
            ],
            "processing_purposes": [
                "Service provision",
                "Customer support",
                "Marketing",
                "Legal compliance"
            ],
            "lawful_basis": [
                "Legitimate interests",
                "Contract performance",
                "Legal obligation",
                "Consent"
            ],
            "data_subjects": [
                "Customers",
                "Website visitors",
                "Employees",
                "Suppliers"
            ],
            "recipients": [
                "Internal staff",
                "Third-party processors",
                "Legal authorities",
                "Service providers"
            ],
            "retention_periods": {
                "customer_data": "5 years",
                "marketing_data": "2 years",
                "technical_logs": "1 year",
                "legal_data": "7 years"
            },
            "international_transfers": {
                "transfers_occur": options.cross_border_transfers,
                "destinations": ["USA", "UK"] if options.cross_border_transfers else [],
                "safeguards": ["Standard Contractual Clauses"] if options.cross_border_transfers else []
            },
            "security_measures": [
                "Encryption",
                "Access controls",
                "Monitoring",
                "Backup procedures"
            ],
            "risk_level": "high" if options.high_risk_processing else "medium"
        }
    
    async def _assess_gdpr_principles(self, target: str, processing_analysis: Dict[str, Any], 
                                    options: GDPROptions, ai_manager: AIServiceManager) -> Dict[str, Any]:
        """Assess GDPR principles compliance"""
        prompt = f"""
        Assess GDPR principles compliance based on data processing analysis:
        
        Target: {target}
        Processing Analysis: {processing_analysis}
        Organization Type: {options.organization_type}
        
        Evaluate compliance with GDPR principles:
        1. Lawfulness, fairness and transparency
        2. Purpose limitation
        3. Data minimisation
        4. Accuracy
        5. Storage limitation
        6. Integrity and confidentiality
        7. Accountability
        
        For each principle, assess:
        - Current implementation status
        - Compliance level
        - Gaps and risks
        - Evidence requirements
        - Remediation needs
        
        Provide detailed assessment with specific findings.
        """
        
        try:
            ai_response = await ai_manager.generate_analysis(
                prompt=prompt,
                analysis_type="gdpr_principles",
                provider=AIProvider.OPENAI
            )
            
            if ai_response and "analysis" in ai_response:
                return ai_response["analysis"]
            else:
                return self._generate_default_principles_assessment()
        
        except Exception as e:
            logger.warning(f"AI principles assessment failed: {e}")
            return self._generate_default_principles_assessment()
    
    def _generate_default_principles_assessment(self) -> Dict[str, Any]:
        """Generate default principles assessment"""
        return {
            "lawfulness": {
                "status": "partially_compliant",
                "findings": [
                    "Privacy policy exists but needs updating",
                    "Lawful basis documented for most processing",
                    "Some consent mechanisms unclear"
                ],
                "gaps": [
                    "Outdated privacy notices",
                    "Incomplete lawful basis documentation",
                    "Consent withdrawal mechanisms unclear"
                ],
                "score": 65
            },
            "purpose_limitation": {
                "status": "compliant",
                "findings": [
                    "Processing purposes clearly defined",
                    "Data use restricted to stated purposes",
                    "Change control procedures in place"
                ],
                "gaps": [
                    "Minor documentation improvements needed"
                ],
                "score": 85
            },
            "data_minimisation": {
                "status": "partially_compliant",
                "findings": [
                    "Data collection policies exist",
                    "Some unnecessary data collection identified",
                    "Regular data reviews not systematic"
                ],
                "gaps": [
                    "Excessive data collection in some areas",
                    "Need for systematic data minimisation review",
                    "Data inventory incomplete"
                ],
                "score": 60
            },
            "accuracy": {
                "status": "partially_compliant",
                "findings": [
                    "Data correction procedures exist",
                    "User can update some information",
                    "Data quality controls basic"
                ],
                "gaps": [
                    "Systematic data quality procedures needed",
                    "Automated accuracy checks limited",
                    "Data validation improvements required"
                ],
                "score": 70
            },
            "storage_limitation": {
                "status": "non_compliant",
                "findings": [
                    "Basic retention policy exists",
                    "Automated deletion not implemented",
                    "Data kept longer than necessary"
                ],
                "gaps": [
                    "Comprehensive retention schedule needed",
                    "Automated deletion procedures required",
                    "Data purging not systematic"
                ],
                "score": 40
            },
            "integrity_confidentiality": {
                "status": "compliant",
                "findings": [
                    "Strong security measures implemented",
                    "Encryption in place",
                    "Access controls effective"
                ],
                "gaps": [
                    "Minor security enhancements needed"
                ],
                "score": 90
            },
            "accountability": {
                "status": "partially_compliant",
                "findings": [
                    "Some compliance documentation exists",
                    "Training provided to staff",
                    "Governance framework basic"
                ],
                "gaps": [
                    "Comprehensive compliance documentation needed",
                    "Regular compliance reviews required",
                    "Evidence management immature"
                ],
                "score": 65
            }
        }
    
    async def _assess_data_subject_rights(self, target: str, processing_analysis: Dict[str, Any], 
                                        options: GDPROptions, ai_manager: AIServiceManager) -> Dict[str, Any]:
        """Assess data subject rights implementation"""
        prompt = f"""
        Assess data subject rights implementation for GDPR compliance:
        
        Target: {target}
        Processing Analysis: {processing_analysis}
        Organization Type: {options.organization_type}
        
        Evaluate implementation of data subject rights:
        1. Right to be informed
        2. Right of access
        3. Right to rectification
        4. Right to erasure
        5. Right to restrict processing
        6. Right to data portability
        7. Right to object
        8. Rights related to automated decision making
        
        For each right, assess:
        - Implementation status
        - Procedures in place
        - Response mechanisms
        - Compliance with timeframes
        - Staff training
        
        Provide detailed assessment of rights implementation.
        """
        
        try:
            ai_response = await ai_manager.generate_analysis(
                prompt=prompt,
                analysis_type="gdpr_rights",
                provider=AIProvider.OPENAI
            )
            
            if ai_response and "analysis" in ai_response:
                return ai_response["analysis"]
            else:
                return self._generate_default_rights_assessment()
        
        except Exception as e:
            logger.warning(f"AI rights assessment failed: {e}")
            return self._generate_default_rights_assessment()
    
    def _generate_default_rights_assessment(self) -> Dict[str, Any]:
        """Generate default rights assessment"""
        return {
            "right_to_be_informed": {
                "status": "partially_compliant",
                "implementation": "Basic privacy notices exist",
                "gaps": ["Privacy notices need updating", "Collection notifications unclear"],
                "score": 65
            },
            "right_of_access": {
                "status": "compliant",
                "implementation": "Subject access request procedures in place",
                "gaps": ["Minor process improvements needed"],
                "score": 85
            },
            "right_to_rectification": {
                "status": "partially_compliant",
                "implementation": "Data correction procedures exist",
                "gaps": ["Verification processes need improvement"],
                "score": 70
            },
            "right_to_erasure": {
                "status": "non_compliant",
                "implementation": "Basic deletion procedures",
                "gaps": ["Comprehensive erasure procedures needed", "Verification lacking"],
                "score": 40
            },
            "right_to_restrict_processing": {
                "status": "non_compliant",
                "implementation": "No formal restriction procedures",
                "gaps": ["Processing restriction procedures needed", "Data flagging system required"],
                "score": 30
            },
            "right_to_data_portability": {
                "status": "partially_compliant",
                "implementation": "Basic data export available",
                "gaps": ["Structured data formats needed", "Automated export required"],
                "score": 60
            },
            "right_to_object": {
                "status": "partially_compliant",
                "implementation": "Basic objection handling",
                "gaps": ["Formal objection procedures needed", "Processing cessation automation"],
                "score": 55
            },
            "automated_decision_making": {
                "status": "not_applicable",
                "implementation": "No automated decision making",
                "gaps": [],
                "score": 100
            }
        }
    
    async def _assess_technical_measures(self, target: str, processing_analysis: Dict[str, Any], 
                                       options: GDPROptions, ai_manager: AIServiceManager) -> Dict[str, Any]:
        """Assess technical and organizational measures"""
        prompt = f"""
        Assess technical and organizational measures for GDPR compliance:
        
        Target: {target}
        Processing Analysis: {processing_analysis}
        Include Technical Measures: {options.include_technical_measures}
        Include Organizational Measures: {options.include_organizational_measures}
        
        Evaluate:
        Technical Measures:
        - Encryption (at rest and in transit)
        - Access controls and authentication
        - Logging and monitoring
        - Data backup and recovery
        - System security
        
        Organizational Measures:
        - Data protection policies
        - Staff training and awareness
        - Access management procedures
        - Incident response procedures
        - Vendor management
        
        Assess implementation and effectiveness of measures.
        """
        
        try:
            ai_response = await ai_manager.generate_analysis(
                prompt=prompt,
                analysis_type="gdpr_technical_measures",
                provider=AIProvider.OPENAI
            )
            
            if ai_response and "analysis" in ai_response:
                return ai_response["analysis"]
            else:
                return self._generate_default_technical_measures()
        
        except Exception as e:
            logger.warning(f"AI technical measures assessment failed: {e}")
            return self._generate_default_technical_measures()
    
    def _generate_default_technical_measures(self) -> Dict[str, Any]:
        """Generate default technical measures assessment"""
        return {
            "technical_measures": {
                "encryption": {
                    "status": "compliant",
                    "implementation": "Strong encryption at rest and in transit",
                    "gaps": ["Some legacy systems need upgrade"],
                    "score": 85
                },
                "access_controls": {
                    "status": "partially_compliant",
                    "implementation": "Role-based access controls implemented",
                    "gaps": ["MFA needed for sensitive systems", "Regular access reviews required"],
                    "score": 70
                },
                "logging_monitoring": {
                    "status": "partially_compliant",
                    "implementation": "Basic logging and monitoring",
                    "gaps": ["Comprehensive audit trails needed", "Real-time monitoring limited"],
                    "score": 65
                },
                "backup_recovery": {
                    "status": "compliant",
                    "implementation": "Regular backups and tested recovery",
                    "gaps": ["Minor improvements to recovery procedures"],
                    "score": 90
                },
                "system_security": {
                    "status": "compliant",
                    "implementation": "Strong system security measures",
                    "gaps": ["Regular security assessments needed"],
                    "score": 85
                }
            },
            "organizational_measures": {
                "data_protection_policies": {
                    "status": "partially_compliant",
                    "implementation": "Basic data protection policies exist",
                    "gaps": ["Policies need updating", "Implementation guidance needed"],
                    "score": 65
                },
                "staff_training": {
                    "status": "partially_compliant",
                    "implementation": "Basic GDPR training provided",
                    "gaps": ["Regular training updates needed", "Role-specific training required"],
                    "score": 60
                },
                "access_management": {
                    "status": "partially_compliant",
                    "implementation": "Basic access management procedures",
                    "gaps": ["Formal procedures needed", "Regular reviews required"],
                    "score": 70
                },
                "incident_response": {
                    "status": "non_compliant",
                    "implementation": "Basic incident response plan",
                    "gaps": ["GDPR-specific procedures needed", "Breach notification processes"],
                    "score": 40
                },
                "vendor_management": {
                    "status": "partially_compliant",
                    "implementation": "Basic vendor agreements",
                    "gaps": ["Data processing agreements needed", "Vendor assessments required"],
                    "score": 55
                }
            }
        }
    
    async def _assess_cross_border_transfers(self, target: str, processing_analysis: Dict[str, Any], 
                                           options: GDPROptions, ai_manager: AIServiceManager) -> Dict[str, Any]:
        """Assess cross-border data transfers"""
        if not options.cross_border_transfers:
            return {
                "applicable": False,
                "status": "not_applicable",
                "score": 100
            }
        
        prompt = f"""
        Assess cross-border data transfers for GDPR compliance:
        
        Target: {target}
        Processing Analysis: {processing_analysis}
        Cross-border Transfers: {options.cross_border_transfers}
        
        Evaluate:
        1. Transfer mechanisms and safeguards
        2. Adequacy decisions
        3. Standard Contractual Clauses
        4. Binding Corporate Rules
        5. Transfer Impact Assessments
        6. Data subject rights in transfers
        
        Assess compliance with Chapter V of GDPR.
        """
        
        try:
            ai_response = await ai_manager.generate_analysis(
                prompt=prompt,
                analysis_type="gdpr_transfers",
                provider=AIProvider.OPENAI
            )
            
            if ai_response and "analysis" in ai_response:
                return ai_response["analysis"]
            else:
                return self._generate_default_transfers_assessment()
        
        except Exception as e:
            logger.warning(f"AI transfers assessment failed: {e}")
            return self._generate_default_transfers_assessment()
    
    def _generate_default_transfers_assessment(self) -> Dict[str, Any]:
        """Generate default transfers assessment"""
        return {
            "applicable": True,
            "status": "partially_compliant",
            "transfer_mechanisms": [
                "Standard Contractual Clauses",
                "Adequacy decisions (UK)"
            ],
            "gaps": [
                "Transfer Impact Assessments needed",
                "Additional safeguards required",
                "Data subject rights documentation"
            ],
            "score": 60
        }
    
    async def _assess_breach_procedures(self, target: str, processing_analysis: Dict[str, Any], 
                                      options: GDPROptions, ai_manager: AIServiceManager) -> Dict[str, Any]:
        """Assess breach notification procedures"""
        prompt = f"""
        Assess breach notification procedures for GDPR compliance:
        
        Target: {target}
        Processing Analysis: {processing_analysis}
        Organization Type: {options.organization_type}
        
        Evaluate:
        1. Breach detection procedures
        2. Risk assessment processes
        3. Notification to supervisory authority (72 hours)
        4. Notification to data subjects
        5. Documentation requirements
        6. Breach register maintenance
        
        Assess compliance with Articles 33-34 of GDPR.
        """
        
        try:
            ai_response = await ai_manager.generate_analysis(
                prompt=prompt,
                analysis_type="gdpr_breach_procedures",
                provider=AIProvider.OPENAI
            )
            
            if ai_response and "analysis" in ai_response:
                return ai_response["analysis"]
            else:
                return self._generate_default_breach_procedures()
        
        except Exception as e:
            logger.warning(f"AI breach procedures assessment failed: {e}")
            return self._generate_default_breach_procedures()
    
    def _generate_default_breach_procedures(self) -> Dict[str, Any]:
        """Generate default breach procedures assessment"""
        return {
            "breach_detection": {
                "status": "partially_compliant",
                "implementation": "Basic detection mechanisms",
                "gaps": ["Automated detection needed", "Staff awareness training"],
                "score": 60
            },
            "risk_assessment": {
                "status": "non_compliant",
                "implementation": "No formal risk assessment process",
                "gaps": ["Risk assessment procedures needed", "Decision criteria unclear"],
                "score": 30
            },
            "authority_notification": {
                "status": "non_compliant",
                "implementation": "No specific procedures for 72-hour notification",
                "gaps": ["Notification procedures needed", "Authority contact details"],
                "score": 25
            },
            "data_subject_notification": {
                "status": "non_compliant",
                "implementation": "No procedures for data subject notification",
                "gaps": ["Notification procedures needed", "Communication templates"],
                "score": 20
            },
            "documentation": {
                "status": "non_compliant",
                "implementation": "Basic incident documentation",
                "gaps": ["GDPR-specific documentation needed", "Breach register required"],
                "score": 35
            },
            "overall_score": 34
        }
    
    async def _assess_impact_assessment(self, target: str, processing_analysis: Dict[str, Any], 
                                      options: GDPROptions, ai_manager: AIServiceManager) -> Dict[str, Any]:
        """Assess Data Protection Impact Assessment requirements"""
        prompt = f"""
        Assess Data Protection Impact Assessment (DPIA) requirements:
        
        Target: {target}
        Processing Analysis: {processing_analysis}
        High Risk Processing: {options.high_risk_processing}
        Special Categories: {options.special_categories}
        Automated Decision Making: {options.automated_decision_making}
        
        Evaluate:
        1. DPIA requirement triggers
        2. DPIA methodology
        3. Risk assessment processes
        4. Mitigation measures
        5. Consultation requirements
        6. Review and monitoring
        
        Assess compliance with Article 35 of GDPR.
        """
        
        try:
            ai_response = await ai_manager.generate_analysis(
                prompt=prompt,
                analysis_type="gdpr_impact_assessment",
                provider=AIProvider.OPENAI
            )
            
            if ai_response and "analysis" in ai_response:
                return ai_response["analysis"]
            else:
                return self._generate_default_impact_assessment()
        
        except Exception as e:
            logger.warning(f"AI impact assessment failed: {e}")
            return self._generate_default_impact_assessment()
    
    def _generate_default_impact_assessment(self) -> Dict[str, Any]:
        """Generate default impact assessment"""
        return {
            "dpia_required": True,
            "status": "partially_compliant",
            "dpia_triggers": [
                "High risk processing activities",
                "Special categories of data",
                "Automated decision making"
            ],
            "implementation": {
                "methodology": "Basic DPIA template exists",
                "risk_assessment": "Informal risk assessment",
                "mitigation_measures": "Some measures identified",
                "consultation": "No formal consultation process",
                "review_monitoring": "No regular review process"
            },
            "gaps": [
                "Formal DPIA methodology needed",
                "Comprehensive risk assessment required",
                "Consultation procedures needed",
                "Regular review process required"
            ],
            "score": 45
        }
    
    async def _assess_governance(self, target: str, processing_analysis: Dict[str, Any], 
                               options: GDPROptions, ai_manager: AIServiceManager) -> Dict[str, Any]:
        """Assess governance and accountability measures"""
        prompt = f"""
        Assess governance and accountability measures for GDPR compliance:
        
        Target: {target}
        Processing Analysis: {processing_analysis}
        Organization Type: {options.organization_type}
        
        Evaluate:
        1. Data Protection Officer (DPO) requirements
        2. Records of processing activities
        3. Accountability measures
        4. Governance framework
        5. Compliance monitoring
        6. Third-party management
        
        Assess compliance with accountability requirements.
        """
        
        try:
            ai_response = await ai_manager.generate_analysis(
                prompt=prompt,
                analysis_type="gdpr_governance",
                provider=AIProvider.OPENAI
            )
            
            if ai_response and "analysis" in ai_response:
                return ai_response["analysis"]
            else:
                return self._generate_default_governance()
        
        except Exception as e:
            logger.warning(f"AI governance assessment failed: {e}")
            return self._generate_default_governance()
    
    def _generate_default_governance(self) -> Dict[str, Any]:
        """Generate default governance assessment"""
        return {
            "dpo_requirements": {
                "dpo_required": True,
                "dpo_appointed": False,
                "gaps": ["DPO appointment required", "DPO training needed"],
                "score": 30
            },
            "records_processing": {
                "status": "partially_compliant",
                "implementation": "Basic processing records exist",
                "gaps": ["Comprehensive records needed", "Regular updates required"],
                "score": 55
            },
            "accountability": {
                "status": "partially_compliant",
                "implementation": "Some accountability measures",
                "gaps": ["Comprehensive accountability framework needed"],
                "score": 60
            },
            "governance_framework": {
                "status": "partially_compliant",
                "implementation": "Basic governance structure",
                "gaps": ["Formal governance framework needed", "Clear roles and responsibilities"],
                "score": 50
            },
            "compliance_monitoring": {
                "status": "non_compliant",
                "implementation": "No systematic compliance monitoring",
                "gaps": ["Monitoring procedures needed", "Regular compliance reviews"],
                "score": 25
            },
            "third_party_management": {
                "status": "partially_compliant",
                "implementation": "Basic third-party agreements",
                "gaps": ["Data processing agreements needed", "Regular assessments required"],
                "score": 55
            }
        }
    
    async def _generate_compliance_assessment(self, target: str, processing_analysis: Dict[str, Any], 
                                            principles_assessment: Dict[str, Any], rights_assessment: Dict[str, Any],
                                            technical_measures: Dict[str, Any], transfers_assessment: Dict[str, Any],
                                            breach_procedures: Dict[str, Any], impact_assessment: Dict[str, Any],
                                            governance_assessment: Dict[str, Any], options: GDPROptions,
                                            ai_manager: AIServiceManager) -> Dict[str, Any]:
        """Generate comprehensive GDPR compliance assessment"""
        
        # Calculate overall compliance score
        score_components = []
        
        # Principles scores
        for principle in principles_assessment.values():
            if isinstance(principle, dict) and "score" in principle:
                score_components.append(principle["score"])
        
        # Rights scores
        for right in rights_assessment.values():
            if isinstance(right, dict) and "score" in right:
                score_components.append(right["score"])
        
        # Technical measures scores
        tech_scores = []
        for measure_type in technical_measures.values():
            if isinstance(measure_type, dict):
                for measure in measure_type.values():
                    if isinstance(measure, dict) and "score" in measure:
                        tech_scores.append(measure["score"])
        if tech_scores:
            score_components.append(sum(tech_scores) / len(tech_scores))
        
        # Other assessment scores
        if transfers_assessment.get("applicable", False):
            score_components.append(transfers_assessment.get("score", 0))
        
        score_components.append(breach_procedures.get("overall_score", 0))
        score_components.append(impact_assessment.get("score", 0))
        
        # Governance scores
        for component in governance_assessment.values():
            if isinstance(component, dict) and "score" in component:
                score_components.append(component["score"])
        
        overall_score = sum(score_components) / len(score_components) if score_components else 0
        
        # Generate vulnerabilities from non-compliant areas
        vulnerabilities = []
        
        # Add principle vulnerabilities
        for principle_id, principle_data in principles_assessment.items():
            if isinstance(principle_data, dict) and principle_data.get("status") == "non_compliant":
                vulnerabilities.append({
                    "id": f"gdpr_principle_{principle_id}",
                    "type": "GDPR Non-Compliance",
                    "severity": "high",
                    "description": f"Non-compliance with GDPR principle: {principle_id}",
                    "category": "principles",
                    "gaps": principle_data.get("gaps", []),
                    "recommendation": f"Address gaps in {principle_id} principle implementation"
                })
        
        # Add rights vulnerabilities
        for right_id, right_data in rights_assessment.items():
            if isinstance(right_data, dict) and right_data.get("status") == "non_compliant":
                vulnerabilities.append({
                    "id": f"gdpr_right_{right_id}",
                    "type": "GDPR Rights Violation",
                    "severity": "high",
                    "description": f"Non-compliance with data subject right: {right_id}",
                    "category": "rights",
                    "gaps": right_data.get("gaps", []),
                    "recommendation": f"Implement procedures for {right_id}"
                })
        
        # Add breach procedure vulnerabilities
        for procedure_id, procedure_data in breach_procedures.items():
            if isinstance(procedure_data, dict) and procedure_data.get("status") == "non_compliant":
                vulnerabilities.append({
                    "id": f"gdpr_breach_{procedure_id}",
                    "type": "GDPR Breach Procedure Gap",
                    "severity": "critical",
                    "description": f"Non-compliance with breach procedure: {procedure_id}",
                    "category": "breach_procedures",
                    "gaps": procedure_data.get("gaps", []),
                    "recommendation": f"Implement {procedure_id} procedures"
                })
        
        compliance_assessment = {
            "target_info": {
                "target": target,
                "organization_type": options.organization_type,
                "assessment_scope": options.assessment_scope,
                "assessment_date": datetime.now().isoformat()
            },
            "processing_analysis": processing_analysis,
            "principles_assessment": principles_assessment,
            "rights_assessment": rights_assessment,
            "technical_measures": technical_measures,
            "transfers_assessment": transfers_assessment,
            "breach_procedures": breach_procedures,
            "impact_assessment": impact_assessment,
            "governance_assessment": governance_assessment,
            "vulnerabilities": vulnerabilities,
            "overall_compliance_score": round(overall_score, 2),
            "compliance_status": self._determine_compliance_status(overall_score),
            "summary": {
                "total_principles": len(self.gdpr_principles),
                "compliant_principles": len([p for p in principles_assessment.values() if isinstance(p, dict) and p.get("status") == "compliant"]),
                "total_rights": len(self.data_subject_rights),
                "compliant_rights": len([r for r in rights_assessment.values() if isinstance(r, dict) and r.get("status") == "compliant"]),
                "critical_gaps": len([v for v in vulnerabilities if v.get("severity") == "critical"]),
                "high_risk_gaps": len([v for v in vulnerabilities if v.get("severity") == "high"]),
                "dpo_required": governance_assessment.get("dpo_requirements", {}).get("dpo_required", False),
                "dpia_required": impact_assessment.get("dpia_required", False),
                "next_assessment_due": (datetime.now() + timedelta(days=365)).isoformat(),
                "assessment_timestamp": datetime.now().isoformat()
            },
            "remediation_roadmap": self._generate_remediation_roadmap(vulnerabilities),
            "compliance_certification": {
                "certification_ready": overall_score >= 85,
                "major_gaps": len([v for v in vulnerabilities if v.get("severity") in ["critical", "high"]]),
                "next_steps": "Address critical gaps" if overall_score < 85 else "Maintain compliance"
            }
        }
        
        return compliance_assessment
    
    def _determine_compliance_status(self, score: float) -> str:
        """Determine compliance status from score"""
        if score >= 90:
            return "fully_compliant"
        elif score >= 75:
            return "substantially_compliant"
        elif score >= 60:
            return "partially_compliant"
        else:
            return "non_compliant"
    
    def _generate_remediation_roadmap(self, vulnerabilities: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Generate remediation roadmap"""
        immediate_actions = []
        short_term_actions = []
        long_term_actions = []
        
        critical_vulns = [v for v in vulnerabilities if v.get("severity") == "critical"]
        high_vulns = [v for v in vulnerabilities if v.get("severity") == "high"]
        medium_vulns = [v for v in vulnerabilities if v.get("severity") == "medium"]
        
        # Critical vulnerabilities are immediate
        for vuln in critical_vulns[:5]:
            immediate_actions.append(vuln.get("recommendation", "Address critical vulnerability"))
        
        # High vulnerabilities are short-term
        for vuln in high_vulns[:10]:
            short_term_actions.append(vuln.get("recommendation", "Address high-risk vulnerability"))
        
        # Medium vulnerabilities are long-term
        for vuln in medium_vulns[:15]:
            long_term_actions.append(vuln.get("recommendation", "Address medium-risk vulnerability"))
        
        return {
            "immediate_actions": immediate_actions,
            "short_term_actions": short_term_actions,
            "long_term_actions": long_term_actions,
            "timeline": {
                "immediate": "0-30 days",
                "short_term": "1-6 months",
                "long_term": "6-12 months"
            },
            "priority_areas": [
                "Data subject rights implementation",
                "Breach notification procedures",
                "Technical and organizational measures",
                "Governance and accountability"
            ]
        }
    
    def get_frontend_interface_data(self) -> Dict[str, Any]:
        """Get data for frontend interface"""
        return {
            "tool_info": {
                "name": "GDPR Compliance Checker",
                "description": "General Data Protection Regulation compliance assessment",
                "version": "1.0.0",
                "category": "Compliance Checker",
                "status": "available" if self.is_available() else "unavailable"
            },
            "scan_options": {
                "target": {
                    "type": "text",
                    "required": True,
                    "placeholder": "Organization Name",
                    "validation": "text"
                },
                "organization_type": {
                    "type": "select",
                    "options": ["controller", "processor", "joint_controller"],
                    "default": "controller",
                    "label": "Organization type"
                },
                "assessment_scope": {
                    "type": "select",
                    "options": ["comprehensive", "basic", "specific"],
                    "default": "comprehensive",
                    "label": "Assessment scope"
                },
                "cross_border_transfers": {
                    "type": "boolean",
                    "default": False,
                    "label": "Cross-border data transfers"
                },
                "high_risk_processing": {
                    "type": "boolean",
                    "default": False,
                    "label": "High-risk processing activities"
                },
                "special_categories": {
                    "type": "boolean",
                    "default": False,
                    "label": "Special categories of data"
                },
                "children_data": {
                    "type": "boolean",
                    "default": False,
                    "label": "Children's data processing"
                },
                "automated_decision_making": {
                    "type": "boolean",
                    "default": False,
                    "label": "Automated decision making"
                }
            },
            "output_formats": ["json", "html", "pdf"],
            "capabilities": [
                "GDPR compliance assessment",
                "7 principles evaluation",
                "Data subject rights analysis",
                "Technical measures assessment",
                "Cross-border transfer evaluation",
                "Breach procedure analysis",
                "Impact assessment requirements",
                "Governance framework review"
            ]
        }


# Register the tool
register_tool(GDPRComplianceChecker)