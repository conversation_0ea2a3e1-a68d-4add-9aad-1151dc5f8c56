/**
 * Direct Backend Connection Fix
 * Bypasses Electron IPC and connects directly to AWS backend
 */

export class DirectBackendConnection {
  private baseURL = 'http://************:8090';
  
  async testConnection() {
    console.log('🔧 Testing DIRECT backend connection (bypassing Electron IPC)...');
    
    try {
      const response = await fetch(`${this.baseURL}/api/health`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        mode: 'cors',
      });
      
      const data = await response.json();
      console.log('✅ DIRECT CONNECTION SUCCESS:', data);
      return { success: true, data };
    } catch (error) {
      console.error('❌ DIRECT CONNECTION FAILED:', error);
      return { success: false, error: error.message };
    }
  }
  
  async getTools() {
    try {
      const response = await fetch(`${this.baseURL}/api/tools`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        mode: 'cors',
      });

      const data = await response.json();
      console.log('✅ TOOLS FETCHED:', data.data?.length || 0, 'tools');
      return data;
    } catch (error) {
      console.error('❌ TOOLS FETCH FAILED:', error);
      return { success: false, error: error.message };
    }
  }
}

// Auto-test on load
if (typeof window !== 'undefined') {
  window.addEventListener('DOMContentLoaded', async () => {
    console.log('🚀 Running Direct Backend Connection Test...');
    const connection = new DirectBackendConnection();
    
    const healthResult = await connection.testConnection();
    console.log('Health Check Result:', healthResult);
    
    if (healthResult.success) {
      const toolsResult = await connection.getTools();
      console.log('Tools Fetch Result:', toolsResult);
    }
  });
}