#!/usr/bin/env python3
"""
Responsible Usage Guide and Educational Framework for NexusScan Backend
Provides educational guidance and best practices for ethical security testing.
Backend-only version without UI dependencies.
"""

import asyncio
import logging
import json
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
from enum import Enum

from core.config import Config

logger = logging.getLogger(__name__)


class UsageCategory(Enum):
    """Categories of usage guidance"""
    LEGAL_COMPLIANCE = "legal_compliance"
    AUTHORIZATION = "authorization"
    SCOPE_MANAGEMENT = "scope_management"
    DATA_HANDLING = "data_handling"
    RESPONSIBLE_DISCLOSURE = "responsible_disclosure"
    PROFESSIONAL_ETHICS = "professional_ethics"
    TECHNICAL_BEST_PRACTICES = "technical_best_practices"
    INCIDENT_RESPONSE = "incident_response"


class GuidanceLevel(Enum):
    """Levels of guidance detail"""
    QUICK_REFERENCE = "quick_reference"
    DETAILED = "detailed"
    COMPREHENSIVE = "comprehensive"
    REGULATORY_SPECIFIC = "regulatory_specific"


class Priority(Enum):
    """Priority levels for guidelines"""
    CRITICAL = "critical"
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"


class ComplexityLevel(Enum):
    """Complexity levels for implementation"""
    BASIC = "basic"
    INTERMEDIATE = "intermediate"
    ADVANCED = "advanced"
    EXPERT = "expert"


@dataclass
class UsageGuideline:
    """Individual usage guideline"""
    guideline_id: str
    category: UsageCategory
    title: str
    summary: str
    detailed_guidance: str
    best_practices: List[str]
    common_mistakes: List[str]
    regulatory_notes: List[str]
    examples: List[str]
    resources: List[str]
    priority: Priority
    complexity: ComplexityLevel


class ResponsibleUsageGuide:
    """
    Backend-only responsible usage guide system.
    Provides educational guidance without UI dependencies.
    """
    
    def __init__(self, config: Config):
        self.config = config
        self.guidelines = self._initialize_guidelines()
        logger.info("Responsible Usage Guide initialized (backend-only)")
    
    def _initialize_guidelines(self) -> Dict[str, UsageGuideline]:
        """Initialize all usage guidelines"""
        return {
            "legal_001": UsageGuideline(
                guideline_id="legal_001",
                category=UsageCategory.LEGAL_COMPLIANCE,
                title="Written Authorization Requirements",
                summary="Always obtain explicit written authorization before conducting security testing",
                detailed_guidance="""
Security testing must only be performed with explicit written authorization from the system owner or authorized representative. This authorization should clearly define:

• Scope of testing (systems, networks, applications)
• Testing timeframes and windows
• Acceptable testing methods and intensity levels
• Contact information for technical coordination
• Incident escalation procedures
• Data handling and confidentiality requirements

Verbal authorization is insufficient for professional security testing. Written documentation protects both the tester and the organization.
                """.strip(),
                best_practices=[
                    "Obtain signed testing agreements before beginning any assessment",
                    "Verify authorization with multiple stakeholders when possible",
                    "Document scope limitations and exclusions clearly",
                    "Establish communication protocols for testing coordination",
                    "Include legal review of testing agreements when required"
                ],
                common_mistakes=[
                    "Proceeding with only verbal authorization",
                    "Assuming broad authorization covers all systems",
                    "Testing outside defined scope or timeframes",
                    "Failing to coordinate with system administrators",
                    "Not documenting authorization properly"
                ],
                regulatory_notes=[
                    "Computer Fraud and Abuse Act (CFAA) requires explicit authorization",
                    "GDPR implications for personal data discovered during testing",
                    "Industry-specific regulations may impose additional requirements"
                ],
                examples=[
                    "Penetration testing agreement template",
                    "Red team engagement scope definition",
                    "Bug bounty program terms of service"
                ],
                resources=[
                    "NIST SP 800-115: Technical Guide to Information Security Testing",
                    "PTES: Penetration Testing Execution Standard",
                    "OWASP Testing Guide v4"
                ],
                priority=Priority.CRITICAL,
                complexity=ComplexityLevel.BASIC
            ),
            
            "tech_001": UsageGuideline(
                guideline_id="tech_001", 
                category=UsageCategory.TECHNICAL_BEST_PRACTICES,
                title="Scan Intensity and Timing Considerations",
                summary="Configure security tools appropriately to minimize system impact",
                detailed_guidance="""
Security scanning tools should be configured with appropriate intensity levels based on:

• Target system capacity and current load
• Network bandwidth limitations
• Business hours and maintenance windows
• Criticality of target systems
• Detection sensitivity requirements

Aggressive scanning can cause service disruptions, trigger security alerts, and impact business operations. Professional testing requires balancing thoroughness with operational responsibility.
                """.strip(),
                best_practices=[
                    "Start with passive reconnaissance and low-intensity scans",
                    "Coordinate timing with system administrators",
                    "Monitor target systems for performance impact during testing",
                    "Use appropriate thread counts and request rates",
                    "Implement scan scheduling during approved windows"
                ],
                common_mistakes=[
                    "Using maximum thread counts without consideration",
                    "Scanning during peak business hours",
                    "Ignoring system performance impact",
                    "Not coordinating with operations teams",
                    "Using default aggressive tool configurations"
                ],
                regulatory_notes=[
                    "Service level agreements may define availability requirements",
                    "Some regulations require minimal business impact testing"
                ],
                examples=[
                    "Nmap timing templates (-T0 through -T5)",
                    "Nuclei rate limiting configuration",
                    "SQLMap delay and timeout settings"
                ],
                resources=[
                    "Nmap documentation on timing and performance",
                    "Nuclei rate limiting best practices",
                    "Network scanning impact assessment guides"
                ],
                priority=Priority.HIGH,
                complexity=ComplexityLevel.INTERMEDIATE
            )
        }
    
    def get_usage_guidelines(self, category: UsageCategory = None) -> List[Dict[str, Any]]:
        """Get usage guidelines for frontend display (backend-only)"""
        try:
            # Filter guidelines by category if specified
            relevant_guidelines = [
                guideline for guideline in self.guidelines.values()
                if not category or guideline.category == category
            ]
            
            # Convert to dict format for frontend
            guidelines_data = []
            for guideline in relevant_guidelines:
                guidelines_data.append({
                    "guideline_id": guideline.guideline_id,
                    "category": guideline.category.value,
                    "title": guideline.title,
                    "summary": guideline.summary,
                    "detailed_guidance": guideline.detailed_guidance,
                    "best_practices": guideline.best_practices,
                    "common_mistakes": guideline.common_mistakes,
                    "regulatory_notes": guideline.regulatory_notes,
                    "examples": guideline.examples,
                    "resources": guideline.resources,
                    "priority": guideline.priority.value,
                    "complexity": guideline.complexity.value
                })
            
            logger.info(f"Retrieved {len(guidelines_data)} guidelines for category: {category.value if category else 'all'}")
            return guidelines_data
            
        except Exception as e:
            logger.error(f"Failed to get usage guidelines: {e}")
            return []
    
    def get_guidelines_by_category(self, category: UsageCategory) -> List[Dict[str, Any]]:
        """Get guidelines filtered by specific category"""
        return self.get_usage_guidelines(category)
    
    def get_quick_reference(self, tools: List[str] = None) -> Dict[str, List[str]]:
        """Get quick reference guide for specified tools"""
        quick_ref = {
            "general_principles": [
                "Always obtain written authorization before testing",
                "Coordinate with system administrators and stakeholders", 
                "Start with passive reconnaissance and low-intensity scans",
                "Monitor target systems for performance impact",
                "Document all testing activities thoroughly",
                "Follow responsible disclosure practices for findings"
            ],
            "legal_requirements": [
                "Verify explicit authorization for all target systems",
                "Respect scope limitations and exclusions",
                "Comply with data handling and confidentiality requirements",
                "Follow incident escalation procedures if issues arise"
            ]
        }
        
        if tools:
            tool_specific = {}
            for tool in tools:
                if tool.lower() == "nmap":
                    tool_specific["nmap"] = [
                        "Use appropriate timing templates (-T0 to -T5)",
                        "Consider --max-rate for bandwidth control",
                        "Use --scan-delay for stealth scanning",
                        "Avoid aggressive scans during business hours"
                    ]
                elif tool.lower() == "sqlmap":
                    tool_specific["sqlmap"] = [
                        "Start with --risk=1 --level=1 for initial testing",
                        "Use --delay for request rate limiting",
                        "Implement --timeout for response handling", 
                        "Consider --threads for controlled concurrency"
                    ]
                elif tool.lower() == "nuclei":
                    tool_specific["nuclei"] = [
                        "Use -rl flag for rate limiting",
                        "Start with low-severity templates",
                        "Use -timeout for request timeouts",
                        "Implement -c for concurrency control"
                    ]
            
            quick_ref["tool_specific"] = tool_specific
        
        return quick_ref
    
    def analyze_command_for_guidance(self, command: str, tool_name: str) -> Dict[str, Any]:
        """Analyze command and provide relevant guidance"""
        guidance = {
            "command": command,
            "tool": tool_name,
            "relevant_guidelines": [],
            "recommendations": [],
            "risk_factors": []
        }
        
        command_lower = command.lower()
        
        # Analyze for high-impact patterns
        if any(pattern in command_lower for pattern in ["--dump", "--all", "--aggressive"]):
            guidance["risk_factors"].append("High-impact operation detected")
            guidance["recommendations"].append("Verify explicit authorization for data extraction")
            guidance["relevant_guidelines"].append("legal_001")
        
        if any(pattern in command_lower for pattern in ["--threads", "-t"]):
            import re
            thread_match = re.search(r'(?:--threads|(?<!\w)-t)\s*[=\s]\s*(\d+)', command_lower)
            if thread_match and int(thread_match.group(1)) > 10:
                guidance["risk_factors"].append("High thread count may impact target systems")
                guidance["recommendations"].append("Consider reducing thread count")
                guidance["relevant_guidelines"].append("tech_001")
        
        return guidance


# Backend factory function
def create_responsible_usage_guide(config: Config) -> ResponsibleUsageGuide:
    """Create responsible usage guide for backend use"""
    return ResponsibleUsageGuide(config)


if __name__ == "__main__":
    # Backend-only example
    from core.config import Config
    
    print("Responsible Usage Guide - Backend Only Version")
    
    config = Config()
    usage_guide = ResponsibleUsageGuide(config)
    
    # Get quick reference
    quick_ref = usage_guide.get_quick_reference(["nmap", "sqlmap"])
    print("Quick Reference:")
    for category, items in quick_ref.items():
        print(f"\n{category.upper()}:")
        if isinstance(items, dict):
            for tool, guidance in items.items():
                print(f"  {tool}:")
                for item in guidance:
                    print(f"    • {item}")
        else:
            for item in items:
                print(f"  • {item}")
    
    # Get specific guidelines
    legal_guidelines = usage_guide.get_guidelines_by_category(UsageCategory.LEGAL_COMPLIANCE)
    print(f"\nFound {len(legal_guidelines)} legal compliance guidelines")