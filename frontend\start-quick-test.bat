@echo off
echo ==========================================
echo    NEXUSSCAN DESKTOP - QUICK TEST MODE
echo ==========================================
echo.
echo This version skips WSL initialization for quick testing
echo.

cd frontend

echo [1/3] Cleaning up old processes...
taskkill /F /IM electron.exe 2>nul
taskkill /F /IM node.exe 2>nul

echo [2/3] Building Electron files...
call npm run build:electron

echo [3/3] Starting in test mode...
echo.
echo Starting NexusScan Desktop in quick test mode...
echo WSL initialization will be skipped for faster startup.
echo.

set NODE_ENV=development
set ELECTRON_ENABLE_LOGGING=1
set SKIP_WSL_INIT=true
set OFFLINE_MODE=true

npm run dev:electron

pause
