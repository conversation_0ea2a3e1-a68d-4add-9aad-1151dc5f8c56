/**
 * SearchSploit Tool Component
 * Exploit database search tool for finding and analyzing known exploits
 */
import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { 
  Key,
  Play,
  Square,
  Download,
  Settings,
  Target,
  Globe,
  FileText,
  Terminal,
  Copy,
  RotateCcw,
  Clock,
  Zap,
  Filter,
  Eye,
  AlertTriangle,
  CheckCircle,
  TrendingUp,
  Activity,
  Lock,
  Search,
  List,
  BarChart3,
  Database,
  Server,
  Bug,
  Info,
  Code,
  Package,
  Layers,
  Shield,
  Crosshair,
  Cpu,
  Network,
  Gauge,
  Tag,
  Hash,
  Workflow,
  GitBranch,
  Book,
  Users,
  Table,
  FileX,
  Skull,
  Unlock,
  HardDrive,
  Flame,
  Binary,
  Braces,
  FileKey,
  History,
  Folder,
  Star,
  Crown,
  Award,
  Sparkles,
  Wifi,
  Router,
  Mail,
  CloudRain,
  Rss,
  UserCheck,
  UserX,
  Timer,
  Pause,
  FastForward,
  Rewind,
  SkipForward,
  Certificate,
  ShieldCheck,
  ShieldAlert,
  AlertCircle,
  XCircle,
  RefreshCw,
  ExternalLink,
  FileCheck,
  FileWarning,
  Fingerprint,
  Key as KeyIcon,
  LockOpen,
  ShieldOff,
  Verified,
  ScanLine,
  Scan,
  FileJson,
  FileCode,
  Clipboard,
  Monitor,
  Globe2,
  Radar,
  Smartphone,
  Laptop,
  Tablet,
  Rocket,
  BugPlay,
  Swords,
  Bomb,
  Pickaxe,
  Gamepad2,
  Command,
  MousePointer2,
  Maximize,
  Eye as EyeIcon,
  Microscope,
  Wrench,
  Cog,
  Archive,
  FileSearch,
  Filter as FilterIcon,
  Calendar,
  MapPin,
  Link,
  BookOpen,
  ExternalLinkIcon
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useBackendStore } from '@/stores/backend-store';
import { apiClient, ToolExecution } from '@/services/api-client';
import { toast } from 'sonner';

/**
 * SearchSploit configuration interface
 */
interface SearchSploitConfig {
  search: {
    query: string;
    platform: string;
    type: string;
    author: string;
    cve: string;
    osvdb: string;
    bid: string;
    edbid: string;
  };
  filters: {
    exact: boolean;
    strict: boolean;
    title: boolean;
    exclude: string;
    case: boolean;
    color: boolean;
    overflow: boolean;
    json: boolean;
  };
  output: {
    path: boolean;
    examine: boolean;
    mirror: boolean;
    www: boolean;
    updateDb: boolean;
    help: boolean;
  };
  advanced: {
    nmap: string;
    maxResults: number;
    sortBy: 'date' | 'title' | 'platform' | 'type';
    sortOrder: 'asc' | 'desc';
    customPath: string;
  };
}

/**
 * Exploit platforms commonly found in Exploit-DB
 */
const EXPLOIT_PLATFORMS = {
  'windows': { name: 'Windows', icon: Monitor, count: 8000 },
  'linux': { name: 'Linux', icon: Terminal, count: 6500 },
  'multiple': { name: 'Multiple', icon: Layers, count: 4000 },
  'php': { name: 'PHP', icon: Code, count: 3500 },
  'web': { name: 'Web Applications', icon: Globe, count: 3000 },
  'unix': { name: 'Unix', icon: Server, count: 2500 },
  'hardware': { name: 'Hardware', icon: Cpu, count: 1500 },
  'osx': { name: 'macOS', icon: Laptop, count: 1200 },
  'solaris': { name: 'Solaris', icon: Server, count: 800 },
  'android': { name: 'Android', icon: Smartphone, count: 600 },
  'freebsd': { name: 'FreeBSD', icon: Server, count: 500 },
  'ios': { name: 'iOS', icon: Tablet, count: 400 },
  'aix': { name: 'AIX', icon: Server, count: 300 },
  'bsd': { name: 'BSD', icon: Server, count: 250 },
  'netware': { name: 'NetWare', icon: Network, count: 200 }
};

/**
 * Exploit types/categories
 */
const EXPLOIT_TYPES = {
  'remote': { name: 'Remote Code Execution', description: 'Execute code remotely', severity: 'critical' },
  'local': { name: 'Local Privilege Escalation', description: 'Escalate local privileges', severity: 'high' },
  'webapps': { name: 'Web Applications', description: 'Web application vulnerabilities', severity: 'high' },
  'dos': { name: 'Denial of Service', description: 'Crash or hang systems', severity: 'medium' },
  'papers': { name: 'Papers & Whitepapers', description: 'Security research papers', severity: 'info' },
  'shellcode': { name: 'Shellcode', description: 'Payload code snippets', severity: 'medium' },
  'hardware': { name: 'Hardware', description: 'Hardware vulnerabilities', severity: 'high' }
};

/**
 * Common search examples and presets
 */
const SEARCH_PRESETS = {
  'popular_cves': {
    name: 'Popular CVEs',
    searches: [
      { query: 'CVE-2017-0144', description: 'EternalBlue (MS17-010)' },
      { query: 'CVE-2019-0708', description: 'BlueKeep RDP RCE' },
      { query: 'CVE-2021-44228', description: 'Log4Shell (Log4j)' },
      { query: 'CVE-2020-1472', description: 'Zerologon (Netlogon)' },
      { query: 'CVE-2017-5638', description: 'Apache Struts2' },
      { query: 'CVE-2014-6271', description: 'Shellshock (Bash)' }
    ]
  },
  'platforms': {
    name: 'Platform-Specific',
    searches: [
      { query: 'windows kernel', description: 'Windows kernel exploits' },
      { query: 'linux privilege escalation', description: 'Linux privesc' },
      { query: 'apache', description: 'Apache web server' },
      { query: 'nginx', description: 'Nginx web server' },
      { query: 'wordpress', description: 'WordPress CMS' },
      { query: 'mysql', description: 'MySQL database' }
    ]
  },
  'applications': {
    name: 'Common Applications',
    searches: [
      { query: 'adobe reader', description: 'Adobe Reader/Acrobat' },
      { query: 'microsoft office', description: 'Microsoft Office' },
      { query: 'internet explorer', description: 'Internet Explorer' },
      { query: 'firefox', description: 'Mozilla Firefox' },
      { query: 'chrome', description: 'Google Chrome' },
      { query: 'java', description: 'Oracle Java' }
    ]
  },
  'recent': {
    name: 'Recent Exploits',
    searches: [
      { query: '2024', description: '2024 exploits' },
      { query: '2023', description: '2023 exploits' },
      { query: 'remote code execution', description: 'Recent RCE' },
      { query: 'privilege escalation', description: 'Recent privesc' },
      { query: 'buffer overflow', description: 'Recent buffer overflows' },
      { query: 'sql injection', description: 'Recent SQLi' }
    ]
  }
};

/**
 * Sort options for results
 */
const SORT_OPTIONS = {
  'date': { name: 'Date', description: 'Sort by publication date' },
  'title': { name: 'Title', description: 'Sort alphabetically by title' },
  'platform': { name: 'Platform', description: 'Sort by target platform' },
  'type': { name: 'Type', description: 'Sort by exploit type' }
};

export default function SearchSploitTool() {
  const { isConnected } = useBackendStore();
  const [config, setConfig] = React.useState<SearchSploitConfig>({
    search: {
      query: '',
      platform: '',
      type: '',
      author: '',
      cve: '',
      osvdb: '',
      bid: '',
      edbid: ''
    },
    filters: {
      exact: false,
      strict: false,
      title: false,
      exclude: '',
      case: false,
      color: true,
      overflow: false,
      json: false
    },
    output: {
      path: false,
      examine: false,
      mirror: false,
      www: false,
      updateDb: false,
      help: false
    },
    advanced: {
      nmap: '',
      maxResults: 100,
      sortBy: 'date',
      sortOrder: 'desc',
      customPath: ''
    }
  });

  const [isRunning, setIsRunning] = React.useState(false);
  const [output, setOutput] = React.useState<string[]>([]);
  const [results, setResults] = React.useState<any[]>([]);
  const [currentExecution, setCurrentExecution] = React.useState<ToolExecution | null>(null);
  const [progress, setProgress] = React.useState(0);
  const [searchResults, setSearchResults] = React.useState<any>({
    exploits: [],
    total: 0,
    query: '',
    filters: {},
    statistics: {
      byPlatform: {},
      byType: {},
      byYear: {}
    }
  });

  const [selectedPreset, setSelectedPreset] = React.useState<string>('popular_cves');

  /**
   * Apply search preset
   */
  const applySearchPreset = (query: string) => {
    setConfig(prev => ({
      ...prev,
      search: {
        ...prev.search,
        query
      }
    }));
    toast.success('Search query applied');
  };

  /**
   * Quick platform filter
   */
  const applyPlatformFilter = (platform: string) => {
    setConfig(prev => ({
      ...prev,
      search: {
        ...prev.search,
        platform
      }
    }));
    toast.success(`Filtered by ${platform}`);
  };

  /**
   * Generate SearchSploit command
   */
  const generateCommand = (): string => {
    const parts = ['searchsploit'];

    // Basic search query
    if (config.search.query) {
      parts.push(`"${config.search.query}"`);
    }

    // Search filters
    if (config.search.platform) {
      parts.push('--platform', config.search.platform);
    }
    if (config.search.type) {
      parts.push('--type', config.search.type);
    }
    if (config.search.author) {
      parts.push('--author', config.search.author);
    }
    if (config.search.cve) {
      parts.push('--cve', config.search.cve);
    }
    if (config.search.osvdb) {
      parts.push('--osvdb', config.search.osvdb);
    }
    if (config.search.bid) {
      parts.push('--bid', config.search.bid);
    }
    if (config.search.edbid) {
      parts.push('--id', config.search.edbid);
    }

    // Filter options
    if (config.filters.exact) {
      parts.push('--exact');
    }
    if (config.filters.strict) {
      parts.push('--strict');
    }
    if (config.filters.title) {
      parts.push('--title');
    }
    if (config.filters.exclude) {
      parts.push('--exclude', `"${config.filters.exclude}"`);
    }
    if (config.filters.case) {
      parts.push('--case');
    }
    if (!config.filters.color) {
      parts.push('--disable-colour');
    }
    if (config.filters.overflow) {
      parts.push('--overflow');
    }
    if (config.filters.json) {
      parts.push('--json');
    }

    // Output options
    if (config.output.path) {
      parts.push('--path');
    }
    if (config.output.examine) {
      parts.push('--examine');
    }
    if (config.output.mirror) {
      parts.push('--mirror');
    }
    if (config.output.www) {
      parts.push('--www');
    }

    // Advanced options
    if (config.advanced.nmap) {
      parts.push('--nmap', config.advanced.nmap);
    }

    return parts.join(' ');
  };

  /**
   * Start SearchSploit execution
   */
  const startExecution = async () => {
    if (!isConnected) {
      toast.error('Backend not connected');
      return;
    }

    // Validation
    if (!config.search.query && !config.search.cve && !config.search.edbid && !config.advanced.nmap) {
      toast.error('Please provide a search query, CVE number, or EDB-ID');
      return;
    }

    try {
      setIsRunning(true);
      setOutput([]);
      setResults([]);
      setProgress(0);
      setSearchResults({
        exploits: [],
        total: 0,
        query: '',
        filters: {},
        statistics: {
          byPlatform: {},
          byType: {},
          byYear: {}
        }
      });

      const execution = await apiClient.startTool('searchsploit', {
        ...config,
        command: generateCommand()
      });

      setCurrentExecution(execution);
      toast.success('SearchSploit search started');

      // Listen for progress updates
      const eventSource = new EventSource(`/api/tools/searchsploit/execution/${execution.id}/stream`);
      
      eventSource.onmessage = (event) => {
        const data = JSON.parse(event.data);
        
        if (data.type === 'output') {
          setOutput(prev => [...prev, data.content]);
        } else if (data.type === 'progress') {
          setProgress(data.progress);
        } else if (data.type === 'result') {
          setResults(prev => [...prev, data.result]);
        } else if (data.type === 'search_results') {
          setSearchResults(data.results);
        } else if (data.type === 'complete') {
          setIsRunning(false);
          eventSource.close();
          toast.success('SearchSploit search completed');
        } else if (data.type === 'error') {
          setIsRunning(false);
          eventSource.close();
          toast.error(`Error: ${data.message}`);
        }
      };

      eventSource.onerror = () => {
        setIsRunning(false);
        eventSource.close();
        toast.error('Connection to execution stream lost');
      };

    } catch (error) {
      setIsRunning(false);
      console.error('Failed to start SearchSploit:', error);
      toast.error('Failed to start SearchSploit');
    }
  };

  /**
   * Stop execution
   */
  const stopExecution = async () => {
    if (!currentExecution) return;

    try {
      await apiClient.stopTool('searchsploit', currentExecution.id);
      setIsRunning(false);
      toast.success('SearchSploit search stopped');
    } catch (error) {
      console.error('Failed to stop SearchSploit:', error);
      toast.error('Failed to stop SearchSploit');
    }
  };

  /**
   * Export results
   */
  const exportResults = () => {
    const exportData = {
      config,
      results,
      searchResults,
      command: generateCommand(),
      timestamp: new Date().toISOString()
    };

    const blob = new Blob([JSON.stringify(exportData, null, 2)], {
      type: 'application/json'
    });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `searchsploit-results-${Date.now()}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    
    toast.success('Results exported successfully');
  };

  /**
   * Update database
   */
  const updateDatabase = async () => {
    try {
      await apiClient.startTool('searchsploit', {
        command: 'searchsploit -u'
      });
      toast.success('Database update started');
    } catch (error) {
      toast.error('Failed to update database');
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className="flex items-center justify-center w-10 h-10 bg-orange-100 rounded-lg">
            <FileSearch className="w-5 h-5 text-orange-600" />
          </div>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">SearchSploit</h1>
            <p className="text-sm text-gray-500">Exploit database search tool for finding and analyzing known exploits</p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Badge variant={isConnected ? "default" : "secondary"}>
            {isConnected ? "Connected" : "Disconnected"}
          </Badge>
          <Badge variant="outline" className="font-mono">Exploit-DB</Badge>
        </div>
      </div>

      {/* Database Statistics */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center space-x-2">
                <Archive className="w-5 h-5" />
                <span>Exploit Database Statistics</span>
              </CardTitle>
              <CardDescription>
                Comprehensive database of public exploits and proof-of-concepts
              </CardDescription>
            </div>
            <Button variant="outline" onClick={updateDatabase}>
              <RefreshCw className="w-4 h-4 mr-2" />
              Update DB
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
            <div className="text-center p-4 border rounded-lg">
              <div className="text-2xl font-bold text-blue-600">50,000+</div>
              <div className="text-sm text-gray-600">Total Exploits</div>
            </div>
            <div className="text-center p-4 border rounded-lg">
              <div className="text-2xl font-bold text-green-600">15+</div>
              <div className="text-sm text-gray-600">Platforms</div>
            </div>
            <div className="text-center p-4 border rounded-lg">
              <div className="text-2xl font-bold text-purple-600">25</div>
              <div className="text-sm text-gray-600">Years of Data</div>
            </div>
            <div className="text-center p-4 border rounded-lg">
              <div className="text-2xl font-bold text-orange-600">Daily</div>
              <div className="text-sm text-gray-600">Updates</div>
            </div>
          </div>

          {/* Platform Overview */}
          <div className="grid grid-cols-2 lg:grid-cols-5 gap-3">
            {Object.entries(EXPLOIT_PLATFORMS).slice(0, 10).map(([key, platform]) => (
              <Button
                key={key}
                variant="outline"
                size="sm"
                onClick={() => applyPlatformFilter(key)}
                className="h-auto p-3 text-left justify-start"
              >
                <div className="flex items-center space-x-2">
                  <platform.icon className="w-4 h-4" />
                  <div>
                    <div className="font-medium text-xs">{platform.name}</div>
                    <div className="text-xs text-gray-500">{platform.count}+</div>
                  </div>
                </div>
              </Button>
            ))}
          </div>
        </CardContent>
      </Card>

      <Tabs defaultValue="search" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="search" className="flex items-center space-x-2">
            <Search className="w-4 h-4" />
            <span>Search</span>
          </TabsTrigger>
          <TabsTrigger value="output" className="flex items-center space-x-2">
            <Terminal className="w-4 h-4" />
            <span>Output</span>
          </TabsTrigger>
          <TabsTrigger value="results" className="flex items-center space-x-2">
            <Target className="w-4 h-4" />
            <span>Results</span>
          </TabsTrigger>
          <TabsTrigger value="command" className="flex items-center space-x-2">
            <Code className="w-4 h-4" />
            <span>Command</span>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="search" className="space-y-6">
          {/* Quick Search Presets */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Zap className="w-5 h-5" />
                <span>Quick Search Presets</span>
              </CardTitle>
              <CardDescription>
                Popular searches and common vulnerability patterns
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {/* Preset Categories */}
                <div className="flex space-x-2 mb-4">
                  {Object.keys(SEARCH_PRESETS).map((category) => (
                    <Button
                      key={category}
                      variant={selectedPreset === category ? "default" : "outline"}
                      size="sm"
                      onClick={() => setSelectedPreset(category)}
                      className="capitalize"
                    >
                      {SEARCH_PRESETS[category as keyof typeof SEARCH_PRESETS].name}
                    </Button>
                  ))}
                </div>

                {/* Search Options */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-3">
                  {SEARCH_PRESETS[selectedPreset as keyof typeof SEARCH_PRESETS].searches.map((search, index) => (
                    <Button
                      key={index}
                      variant="outline"
                      onClick={() => applySearchPreset(search.query)}
                      className="h-auto p-3 text-left justify-start"
                    >
                      <div>
                        <div className="font-medium text-sm">{search.description}</div>
                        <div className="text-xs text-gray-500 font-mono">{search.query}</div>
                      </div>
                    </Button>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Search Configuration */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Search className="w-5 h-5" />
                  <span>Search Configuration</span>
                </CardTitle>
                <CardDescription>
                  Configure search terms and criteria
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium">Search Query</label>
                  <Input
                    placeholder="apache, windows kernel, CVE-2021-44228..."
                    value={config.search.query}
                    onChange={(e) => setConfig(prev => ({
                      ...prev,
                      search: { ...prev.search, query: e.target.value }
                    }))}
                  />
                  <p className="text-xs text-gray-500">Enter keywords, application names, or vulnerability descriptions</p>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Platform Filter</label>
                    <Select 
                      value={config.search.platform} 
                      onValueChange={(value) => setConfig(prev => ({
                        ...prev,
                        search: { ...prev.search, platform: value }
                      }))}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="All platforms" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="">All Platforms</SelectItem>
                        {Object.entries(EXPLOIT_PLATFORMS).map(([key, platform]) => (
                          <SelectItem key={key} value={key}>
                            {platform.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Exploit Type</label>
                    <Select 
                      value={config.search.type} 
                      onValueChange={(value) => setConfig(prev => ({
                        ...prev,
                        search: { ...prev.search, type: value }
                      }))}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="All types" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="">All Types</SelectItem>
                        {Object.entries(EXPLOIT_TYPES).map(([key, type]) => (
                          <SelectItem key={key} value={key}>
                            {type.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">Author</label>
                  <Input
                    placeholder="exploit author name"
                    value={config.search.author}
                    onChange={(e) => setConfig(prev => ({
                      ...prev,
                      search: { ...prev.search, author: e.target.value }
                    }))}
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">CVE Number</label>
                    <Input
                      placeholder="CVE-2021-44228"
                      value={config.search.cve}
                      onChange={(e) => setConfig(prev => ({
                        ...prev,
                        search: { ...prev.search, cve: e.target.value }
                      }))}
                    />
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium">EDB-ID</label>
                    <Input
                      placeholder="12345"
                      value={config.search.edbid}
                      onChange={(e) => setConfig(prev => ({
                        ...prev,
                        search: { ...prev.search, edbid: e.target.value }
                      }))}
                    />
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">OSVDB ID</label>
                    <Input
                      placeholder="12345"
                      value={config.search.osvdb}
                      onChange={(e) => setConfig(prev => ({
                        ...prev,
                        search: { ...prev.search, osvdb: e.target.value }
                      }))}
                    />
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Bugtraq ID</label>
                    <Input
                      placeholder="12345"
                      value={config.search.bid}
                      onChange={(e) => setConfig(prev => ({
                        ...prev,
                        search: { ...prev.search, bid: e.target.value }
                      }))}
                    />
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Search Filters */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <FilterIcon className="w-5 h-5" />
                  <span>Search Filters</span>
                </CardTitle>
                <CardDescription>
                  Refine search behavior and output options
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="exact"
                      checked={config.filters.exact}
                      onChange={(e) => setConfig(prev => ({
                        ...prev,
                        filters: { ...prev.filters, exact: e.target.checked }
                      }))}
                      className="rounded"
                    />
                    <label htmlFor="exact" className="text-sm font-medium">
                      Exact match only
                    </label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="strict"
                      checked={config.filters.strict}
                      onChange={(e) => setConfig(prev => ({
                        ...prev,
                        filters: { ...prev.filters, strict: e.target.checked }
                      }))}
                      className="rounded"
                    />
                    <label htmlFor="strict" className="text-sm font-medium">
                      Strict search (all terms must match)
                    </label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="title"
                      checked={config.filters.title}
                      onChange={(e) => setConfig(prev => ({
                        ...prev,
                        filters: { ...prev.filters, title: e.target.checked }
                      }))}
                      className="rounded"
                    />
                    <label htmlFor="title" className="text-sm font-medium">
                      Search titles only
                    </label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="case"
                      checked={config.filters.case}
                      onChange={(e) => setConfig(prev => ({
                        ...prev,
                        filters: { ...prev.filters, case: e.target.checked }
                      }))}
                      className="rounded"
                    />
                    <label htmlFor="case" className="text-sm font-medium">
                      Case sensitive search
                    </label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="overflow"
                      checked={config.filters.overflow}
                      onChange={(e) => setConfig(prev => ({
                        ...prev,
                        filters: { ...prev.filters, overflow: e.target.checked }
                      }))}
                      className="rounded"
                    />
                    <label htmlFor="overflow" className="text-sm font-medium">
                      Exploit contains buffer overflow
                    </label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="color"
                      checked={config.filters.color}
                      onChange={(e) => setConfig(prev => ({
                        ...prev,
                        filters: { ...prev.filters, color: e.target.checked }
                      }))}
                      className="rounded"
                    />
                    <label htmlFor="color" className="text-sm font-medium">
                      Enable colored output
                    </label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="json"
                      checked={config.filters.json}
                      onChange={(e) => setConfig(prev => ({
                        ...prev,
                        filters: { ...prev.filters, json: e.target.checked }
                      }))}
                      className="rounded"
                    />
                    <label htmlFor="json" className="text-sm font-medium">
                      JSON output format
                    </label>
                  </div>
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">Exclude Terms</label>
                  <Input
                    placeholder="terms to exclude from results"
                    value={config.filters.exclude}
                    onChange={(e) => setConfig(prev => ({
                      ...prev,
                      filters: { ...prev.filters, exclude: e.target.value }
                    }))}
                  />
                  <p className="text-xs text-gray-500">Comma-separated terms to exclude</p>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Sort By</label>
                    <Select 
                      value={config.advanced.sortBy} 
                      onValueChange={(value: any) => setConfig(prev => ({
                        ...prev,
                        advanced: { ...prev.advanced, sortBy: value }
                      }))}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {Object.entries(SORT_OPTIONS).map(([key, option]) => (
                          <SelectItem key={key} value={key}>
                            {option.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Sort Order</label>
                    <Select 
                      value={config.advanced.sortOrder} 
                      onValueChange={(value: any) => setConfig(prev => ({
                        ...prev,
                        advanced: { ...prev.advanced, sortOrder: value }
                      }))}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="desc">Newest First</SelectItem>
                        <SelectItem value="asc">Oldest First</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">Max Results</label>
                  <Input
                    type="number"
                    min="1"
                    max="1000"
                    value={config.advanced.maxResults}
                    onChange={(e) => setConfig(prev => ({
                      ...prev,
                      advanced: { ...prev.advanced, maxResults: parseInt(e.target.value) || 100 }
                    }))}
                  />
                </div>
              </CardContent>
            </Card>

            {/* Output Options */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <FileText className="w-5 h-5" />
                  <span>Output Options</span>
                </CardTitle>
                <CardDescription>
                  Configure what information to display and actions to take
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="path"
                      checked={config.output.path}
                      onChange={(e) => setConfig(prev => ({
                        ...prev,
                        output: { ...prev.output, path: e.target.checked }
                      }))}
                      className="rounded"
                    />
                    <label htmlFor="path" className="text-sm font-medium">
                      Show full file paths
                    </label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="examine"
                      checked={config.output.examine}
                      onChange={(e) => setConfig(prev => ({
                        ...prev,
                        output: { ...prev.output, examine: e.target.checked }
                      }))}
                      className="rounded"
                    />
                    <label htmlFor="examine" className="text-sm font-medium">
                      Examine exploit files (show content)
                    </label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="mirror"
                      checked={config.output.mirror}
                      onChange={(e) => setConfig(prev => ({
                        ...prev,
                        output: { ...prev.output, mirror: e.target.checked }
                      }))}
                      className="rounded"
                    />
                    <label htmlFor="mirror" className="text-sm font-medium">
                      Copy exploits to current directory
                    </label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="www"
                      checked={config.output.www}
                      onChange={(e) => setConfig(prev => ({
                        ...prev,
                        output: { ...prev.output, www: e.target.checked }
                      }))}
                      className="rounded"
                    />
                    <label htmlFor="www" className="text-sm font-medium">
                      Show Exploit-DB URLs
                    </label>
                  </div>
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">Nmap XML File</label>
                  <Input
                    placeholder="/path/to/nmap.xml"
                    value={config.advanced.nmap}
                    onChange={(e) => setConfig(prev => ({
                      ...prev,
                      advanced: { ...prev.advanced, nmap: e.target.value }
                    }))}
                  />
                  <p className="text-xs text-gray-500">Search for exploits based on Nmap scan results</p>
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">Custom Database Path</label>
                  <Input
                    placeholder="/custom/path/to/exploitdb"
                    value={config.advanced.customPath}
                    onChange={(e) => setConfig(prev => ({
                      ...prev,
                      advanced: { ...prev.advanced, customPath: e.target.value }
                    }))}
                  />
                  <p className="text-xs text-gray-500">Override default Exploit-DB location</p>
                </div>
              </CardContent>
            </Card>

            {/* Advanced Features */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Cog className="w-5 h-5" />
                  <span>Advanced Features</span>
                </CardTitle>
                <CardDescription>
                  Additional SearchSploit capabilities
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <Button
                    variant="outline"
                    onClick={updateDatabase}
                    className="h-auto p-4 text-left justify-start"
                  >
                    <div>
                      <div className="flex items-center space-x-2 mb-1">
                        <RefreshCw className="w-4 h-4" />
                        <span className="font-medium">Update Database</span>
                      </div>
                      <div className="text-xs text-gray-500">Sync with latest Exploit-DB</div>
                    </div>
                  </Button>

                  <Button
                    variant="outline"
                    onClick={() => {
                      setConfig(prev => ({
                        ...prev,
                        search: { ...prev.search, query: 'buffer overflow' },
                        filters: { ...prev.filters, overflow: true }
                      }));
                    }}
                    className="h-auto p-4 text-left justify-start"
                  >
                    <div>
                      <div className="flex items-center space-x-2 mb-1">
                        <Bug className="w-4 h-4" />
                        <span className="font-medium">Buffer Overflows</span>
                      </div>
                      <div className="text-xs text-gray-500">Find buffer overflow exploits</div>
                    </div>
                  </Button>

                  <Button
                    variant="outline"
                    onClick={() => {
                      setConfig(prev => ({
                        ...prev,
                        search: { ...prev.search, query: 'remote code execution' }
                      }));
                    }}
                    className="h-auto p-4 text-left justify-start"
                  >
                    <div>
                      <div className="flex items-center space-x-2 mb-1">
                        <Rocket className="w-4 h-4" />
                        <span className="font-medium">Remote Code Execution</span>
                      </div>
                      <div className="text-xs text-gray-500">Find RCE exploits</div>
                    </div>
                  </Button>

                  <Button
                    variant="outline"
                    onClick={() => {
                      setConfig(prev => ({
                        ...prev,
                        search: { ...prev.search, query: 'privilege escalation' }
                      }));
                    }}
                    className="h-auto p-4 text-left justify-start"
                  >
                    <div>
                      <div className="flex items-center space-x-2 mb-1">
                        <TrendingUp className="w-4 h-4" />
                        <span className="font-medium">Privilege Escalation</span>
                      </div>
                      <div className="text-xs text-gray-500">Find privesc exploits</div>
                    </div>
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Execution Controls */}
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <Button
                    onClick={startExecution}
                    disabled={!isConnected || isRunning}
                    size="lg"
                    className="bg-orange-600 hover:bg-orange-700"
                  >
                    <Play className="w-4 h-4 mr-2" />
                    Search Exploits
                  </Button>
                  
                  {isRunning && (
                    <Button
                      onClick={stopExecution}
                      variant="outline"
                      size="lg"
                    >
                      <Square className="w-4 h-4 mr-2" />
                      Stop
                    </Button>
                  )}
                </div>

                <div className="flex items-center space-x-2">
                  {isRunning && (
                    <div className="flex items-center space-x-2">
                      <Activity className="w-4 h-4 text-orange-600 animate-pulse" />
                      <span className="text-sm text-orange-600">Searching...</span>
                    </div>
                  )}
                  <Badge variant="outline">
                    {searchResults.total || 0} results
                  </Badge>
                </div>
              </div>

              {isRunning && (
                <div className="mt-4">
                  <div className="flex items-center justify-between text-sm text-gray-600 mb-2">
                    <span>Search Progress</span>
                    <span>{progress.toFixed(1)}%</span>
                  </div>
                  <Progress value={progress} className="h-2" />
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="output" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Terminal className="w-5 h-5" />
                <span>Search Output</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="bg-black rounded-lg p-4 h-96 overflow-y-auto font-mono text-sm">
                {output.length === 0 ? (
                  <div className="text-gray-500 italic">
                    <div className="text-orange-400 mb-2">
                      SearchSploit v4.2.3 | Exploit Database Archive
                    </div>
                    <p>Enter search terms to begin exploit database search.</p>
                    <p className="text-xs mt-2">
                      root@kali:~# searchsploit [options] &lt;search terms&gt;
                    </p>
                  </div>
                ) : (
                  output.map((line, index) => (
                    <div key={index} className={cn(
                      "text-green-400",
                      line.includes('[!]') && "text-red-400",
                      line.includes('[*]') && "text-blue-400",
                      line.includes('[+]') && "text-green-400",
                      line.includes('Exploit Title') && "text-cyan-400 font-bold",
                      line.includes('-----') && "text-gray-600",
                      line.includes('.txt') && "text-yellow-400",
                      line.includes('.py') && "text-purple-400",
                      line.includes('.rb') && "text-red-400",
                      line.includes('.c') && "text-blue-400"
                    )}>
                      {line}
                    </div>
                  ))
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="results" className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold">Exploit Search Results</h3>
            {(results.length > 0 || searchResults.exploits?.length > 0) && (
              <Button onClick={exportResults} variant="outline">
                <Download className="w-4 h-4 mr-2" />
                Export Results
              </Button>
            )}
          </div>

          {searchResults.exploits?.length === 0 && results.length === 0 ? (
            <Card>
              <CardContent className="pt-6">
                <div className="text-center py-8">
                  <FileSearch className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500">No exploits found</p>
                  <p className="text-sm text-gray-400">Try different search terms or adjust filters</p>
                </div>
              </CardContent>
            </Card>
          ) : (
            <div className="space-y-6">
              {/* Search Statistics */}
              {searchResults.total > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2">
                      <BarChart3 className="w-5 h-5" />
                      <span>Search Statistics</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                      <div className="text-center">
                        <div className="text-2xl font-bold text-orange-600">
                          {searchResults.total}
                        </div>
                        <div className="text-sm text-gray-600">Total Results</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-blue-600">
                          {Object.keys(searchResults.statistics?.byPlatform || {}).length}
                        </div>
                        <div className="text-sm text-gray-600">Platforms</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-green-600">
                          {Object.keys(searchResults.statistics?.byType || {}).length}
                        </div>
                        <div className="text-sm text-gray-600">Exploit Types</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-purple-600">
                          {Object.keys(searchResults.statistics?.byYear || {}).length}
                        </div>
                        <div className="text-sm text-gray-600">Years</div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Found Exploits */}
              {searchResults.exploits && searchResults.exploits.length > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2">
                      <Archive className="w-5 h-5" />
                      <span>Found Exploits</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {searchResults.exploits.map((exploit: any, index: number) => (
                        <div key={index} className="p-4 border rounded-lg hover:bg-gray-50 transition-colors">
                          <div className="flex items-start justify-between">
                            <div className="flex-1">
                              <div className="flex items-center space-x-2 mb-2">
                                <h4 className="font-semibold">{exploit.title}</h4>
                                <Badge variant="outline">
                                  EDB-{exploit.edbId}
                                </Badge>
                                {exploit.cve && (
                                  <Badge variant="outline" className="bg-red-50 text-red-700">
                                    {exploit.cve}
                                  </Badge>
                                )}
                              </div>
                              <div className="flex items-center space-x-4 text-sm text-gray-600 mb-2">
                                <div className="flex items-center space-x-1">
                                  <Monitor className="w-4 h-4" />
                                  <span>{exploit.platform}</span>
                                </div>
                                <div className="flex items-center space-x-1">
                                  <Tag className="w-4 h-4" />
                                  <span>{exploit.type}</span>
                                </div>
                                <div className="flex items-center space-x-1">
                                  <Calendar className="w-4 h-4" />
                                  <span>{exploit.date}</span>
                                </div>
                                <div className="flex items-center space-x-1">
                                  <Users className="w-4 h-4" />
                                  <span>{exploit.author}</span>
                                </div>
                              </div>
                              <div className="text-sm text-gray-500 font-mono">
                                {exploit.path}
                              </div>
                            </div>
                            <div className="flex space-x-2 ml-4">
                              {exploit.url && (
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => window.open(exploit.url, '_blank')}
                                >
                                  <ExternalLinkIcon className="w-4 h-4 mr-1" />
                                  View Online
                                </Button>
                              )}
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => {
                                  navigator.clipboard.writeText(exploit.path);
                                  toast.success('Path copied to clipboard');
                                }}
                              >
                                <Copy className="w-4 h-4" />
                              </Button>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Platform Distribution */}
              {searchResults.statistics?.byPlatform && Object.keys(searchResults.statistics.byPlatform).length > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2">
                      <Monitor className="w-5 h-5" />
                      <span>Results by Platform</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                      {Object.entries(searchResults.statistics.byPlatform).map(([platform, count]: [string, any]) => (
                        <div key={platform} className="p-3 border rounded-lg text-center">
                          <div className="font-semibold capitalize">{platform}</div>
                          <div className="text-2xl font-bold text-blue-600">{count}</div>
                          <div className="text-xs text-gray-500">exploits</div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Individual Results */}
              {results.length > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2">
                      <List className="w-5 h-5" />
                      <span>Search Details</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      {results.map((result, index) => (
                        <div key={index} className="flex items-center justify-between p-2 border rounded">
                          <div className="flex items-center space-x-2">
                            <FileText className="w-4 h-4 text-gray-500" />
                            <span className="text-sm">{result.description}</span>
                          </div>
                          <div className="flex items-center space-x-2">
                            <span className="text-sm text-gray-600">{result.details}</span>
                            {result.count && (
                              <Badge variant="outline" className="text-xs">
                                {result.count} found
                              </Badge>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}
            </div>
          )}
        </TabsContent>

        <TabsContent value="command" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Code className="w-5 h-5" />
                <span>Generated Command</span>
              </CardTitle>
              <CardDescription>
                Review and copy the SearchSploit command
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="bg-gray-900 rounded-lg p-4">
                  <code className="text-green-400 font-mono text-sm whitespace-pre-wrap">
                    {generateCommand()}
                  </code>
                </div>
                <div className="flex space-x-2">
                  <Button
                    variant="outline"
                    onClick={() => {
                      navigator.clipboard.writeText(generateCommand());
                      toast.success('Command copied to clipboard');
                    }}
                  >
                    <Copy className="w-4 h-4 mr-2" />
                    Copy Command
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Command Options Reference */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Book className="w-5 h-5" />
                <span>SearchSploit Options Reference</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h4 className="font-semibold mb-3">Search Options</h4>
                  <div className="space-y-2 text-sm">
                    <div><code className="bg-gray-100 px-1 rounded">--exact</code> - Exact match only</div>
                    <div><code className="bg-gray-100 px-1 rounded">--strict</code> - Strict search (all terms)</div>
                    <div><code className="bg-gray-100 px-1 rounded">--title</code> - Search titles only</div>
                    <div><code className="bg-gray-100 px-1 rounded">--exclude</code> - Exclude terms</div>
                    <div><code className="bg-gray-100 px-1 rounded">--case</code> - Case sensitive</div>
                    <div><code className="bg-gray-100 px-1 rounded">--overflow</code> - Buffer overflow filter</div>
                  </div>
                </div>
                
                <div>
                  <h4 className="font-semibold mb-3">Output Options</h4>
                  <div className="space-y-2 text-sm">
                    <div><code className="bg-gray-100 px-1 rounded">--path</code> - Show full paths</div>
                    <div><code className="bg-gray-100 px-1 rounded">--examine</code> - Show file content</div>
                    <div><code className="bg-gray-100 px-1 rounded">--mirror</code> - Copy to current dir</div>
                    <div><code className="bg-gray-100 px-1 rounded">--www</code> - Show web URLs</div>
                    <div><code className="bg-gray-100 px-1 rounded">--json</code> - JSON output</div>
                    <div><code className="bg-gray-100 px-1 rounded">--disable-colour</code> - No colors</div>
                  </div>
                </div>
                
                <div>
                  <h4 className="font-semibold mb-3">ID-based Searches</h4>
                  <div className="space-y-2 text-sm">
                    <div><code className="bg-gray-100 px-1 rounded">--cve</code> - Search by CVE number</div>
                    <div><code className="bg-gray-100 px-1 rounded">--id</code> - Search by EDB-ID</div>
                    <div><code className="bg-gray-100 px-1 rounded">--osvdb</code> - Search by OSVDB ID</div>
                    <div><code className="bg-gray-100 px-1 rounded">--bid</code> - Search by Bugtraq ID</div>
                  </div>
                </div>
                
                <div>
                  <h4 className="font-semibold mb-3">Advanced Features</h4>
                  <div className="space-y-2 text-sm">
                    <div><code className="bg-gray-100 px-1 rounded">--nmap</code> - Parse Nmap XML</div>
                    <div><code className="bg-gray-100 px-1 rounded">--platform</code> - Filter by platform</div>
                    <div><code className="bg-gray-100 px-1 rounded">--type</code> - Filter by type</div>
                    <div><code className="bg-gray-100 px-1 rounded">--author</code> - Filter by author</div>
                    <div><code className="bg-gray-100 px-1 rounded">-u</code> - Update database</div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Example Searches */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Search className="w-5 h-5" />
                <span>Example Searches</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="p-3 bg-gray-50 rounded-lg">
                  <code className="text-sm">searchsploit apache 2.4</code>
                  <p className="text-xs text-gray-600 mt-1">Find Apache 2.4 exploits</p>
                </div>
                <div className="p-3 bg-gray-50 rounded-lg">
                  <code className="text-sm">searchsploit --cve CVE-2021-44228</code>
                  <p className="text-xs text-gray-600 mt-1">Search for Log4Shell exploit</p>
                </div>
                <div className="p-3 bg-gray-50 rounded-lg">
                  <code className="text-sm">searchsploit --platform windows kernel</code>
                  <p className="text-xs text-gray-600 mt-1">Windows kernel exploits only</p>
                </div>
                <div className="p-3 bg-gray-50 rounded-lg">
                  <code className="text-sm">searchsploit --type remote --overflow</code>
                  <p className="text-xs text-gray-600 mt-1">Remote buffer overflow exploits</p>
                </div>
                <div className="p-3 bg-gray-50 rounded-lg">
                  <code className="text-sm">searchsploit --nmap /path/to/scan.xml</code>
                  <p className="text-xs text-gray-600 mt-1">Parse Nmap results for relevant exploits</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}