/**
 * Network Scanning Tools Page
 * Provides access to 6 network scanning and discovery tools
 */
import React from 'react';
import { Routes, Route, Link, useLocation } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  Network,
  Zap,
  Globe,
  Shield,
  Server,
  Search,
  PlayCircle,
  Settings,
  AlertTriangle,
  CheckCircle,
  Clock
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useBackendStore, useToolsByCategory } from '@/stores/backend-store';

// Tool-specific components (will be implemented)
import { NmapScanner } from '@/components/tools/network-scanning/NmapScanner';
import { MasscanScanner } from '@/components/tools/network-scanning/MasscanScanner';
import { ZmapScanner } from '@/components/tools/network-scanning/ZmapScanner';
import { OpenVASScanner } from '@/components/tools/network-scanning/OpenVASScanner';
import { SMBClientTool } from '@/components/tools/network-scanning/SMBClientTool';
import { Enum4LinuxNGTool } from '@/components/tools/network-scanning/Enum4LinuxNGTool';

/**
 * Network scanning tool configuration
 */
interface NetworkTool {
  id: string;
  name: string;
  description: string;
  icon: React.ReactNode;
  features: string[];
  useCases: string[];
  component: React.ComponentType;
  status: 'available' | 'unavailable' | 'warning';
  warning?: string;
}

/**
 * Network scanning tools configuration
 */
const NETWORK_TOOLS: NetworkTool[] = [
  {
    id: 'nmap',
    name: 'Nmap',
    description: 'Network discovery and port scanning with comprehensive OS and service detection',
    icon: <Network className="h-6 w-6" />,
    features: [
      'Port scanning (TCP/UDP/SYN)',
      'OS fingerprinting',
      'Service version detection',
      'NSE scripting engine',
      'Timing templates',
      'Output formats (XML, JSON, Grepable)'
    ],
    useCases: [
      'Network discovery and mapping',
      'Port and service enumeration',
      'Operating system detection',
      'Vulnerability scanning with scripts',
      'Network inventory and monitoring'
    ],
    component: NmapScanner,
    status: 'available'
  },
  {
    id: 'masscan',
    name: 'Masscan',
    description: 'High-speed port scanner capable of scanning the entire Internet',
    icon: <Zap className="h-6 w-6" />,
    features: [
      'Asynchronous transmission',
      'Custom packet rate control',
      'Multiple output formats',
      'IPv4 and IPv6 support',
      'Banner grabbing',
      'Large-scale scanning'
    ],
    useCases: [
      'Internet-wide port scans',
      'Large network reconnaissance',
      'High-speed service discovery',
      'Network security assessments',
      'Bulk vulnerability scanning'
    ],
    component: MasscanScanner,
    status: 'available'
  },
  {
    id: 'zmap',
    name: 'Zmap',
    description: 'Internet-wide network scanner for research and security assessment',
    icon: <Globe className="h-6 w-6" />,
    features: [
      'Single-port scanning',
      'Stateless scanning',
      'High-speed transmission',
      'Research-oriented',
      'Geographic mapping',
      'Statistical analysis'
    ],
    useCases: [
      'Internet-wide surveys',
      'Security research',
      'Protocol analysis',
      'Geographic distribution studies',
      'Large-scale vulnerability assessment'
    ],
    component: ZmapScanner,
    status: 'available'
  },
  {
    id: 'openvas',
    name: 'OpenVAS',
    description: 'Comprehensive vulnerability assessment and management framework',
    icon: <Shield className="h-6 w-6" />,
    features: [
      'Vulnerability scanning',
      'Network vulnerability tests',
      'Authenticated scanning',
      'Compliance checking',
      'Report generation',
      'Continuous monitoring'
    ],
    useCases: [
      'Comprehensive vulnerability assessment',
      'Compliance auditing',
      'Continuous security monitoring',
      'Risk assessment',
      'Patch management planning'
    ],
    component: OpenVASScanner,
    status: 'available'
  },
  {
    id: 'smbclient',
    name: 'SMBClient',
    description: 'SMB/CIFS client for accessing Windows shares and services',
    icon: <Server className="h-6 w-6" />,
    features: [
      'SMB share enumeration',
      'File and directory access',
      'Authentication testing',
      'Share permissions analysis',
      'Windows service interaction',
      'NetBIOS name resolution'
    ],
    useCases: [
      'Windows network enumeration',
      'Share accessibility testing',
      'Credential validation',
      'File system analysis',
      'Windows security assessment'
    ],
    component: SMBClientTool,
    status: 'available'
  },
  {
    id: 'enum4linux-ng',
    name: 'enum4linux-ng',
    description: 'Modern SMB enumeration tool for comprehensive Windows network analysis',
    icon: <Search className="h-6 w-6" />,
    features: [
      'SMB share enumeration',
      'User and group enumeration',
      'Password policy detection',
      'OS information gathering',
      'RID cycling',
      'Modern Python implementation'
    ],
    useCases: [
      'Windows domain enumeration',
      'User account discovery',
      'Share and permission analysis',
      'Security policy assessment',
      'Active Directory reconnaissance'
    ],
    component: Enum4LinuxNGTool,
    status: 'warning',
    warning: 'Tool functional but may show detection issues in backend registry'
  }
];

/**
 * Network Scanning Tools Page Component
 */
export function NetworkScanningPage() {
  const location = useLocation();
  const { tools: backendTools } = useBackendStore();
  const networkTools = useToolsByCategory('network-scanning');
  
  // Check if we're on a specific tool page
  const isToolPage = location.pathname !== '/tools/network-scanning';
  
  const getToolStatus = (toolId: string) => {
    const backendTool = backendTools.find(t => t.id === toolId);
    if (!backendTool) return 'unavailable';
    return backendTool.status === 'available' ? 'available' : 'unavailable';
  };
  
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'available':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'warning':
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
      case 'unavailable':
        return <Clock className="h-4 w-4 text-red-500" />;
      default:
        return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };
  
  const getStatusText = (status: string) => {
    switch (status) {
      case 'available':
        return 'Ready';
      case 'warning':
        return 'Available (Issues)';
      case 'unavailable':
        return 'Unavailable';
      default:
        return 'Unknown';
    }
  };
  
  // If we're on a specific tool page, render the tool component
  if (isToolPage) {
    return (
      <div className="h-full">
        <Routes>
          <Route path="/nmap/*" element={<NmapScanner />} />
          <Route path="/masscan/*" element={<MasscanScanner />} />
          <Route path="/zmap/*" element={<ZmapScanner />} />
          <Route path="/openvas/*" element={<OpenVASScanner />} />
          <Route path="/smbclient/*" element={<SMBClientTool />} />
          <Route path="/enum4linux-ng/*" element={<Enum4LinuxNGTool />} />
        </Routes>
      </div>
    );
  }
  
  return (
    <div className="container-desktop py-6 space-y-6">
      {/* Page Header */}
      <div className="space-y-2">
        <div className="flex items-center gap-3">
          <div className="p-2 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
            <Network className="h-6 w-6 text-blue-600 dark:text-blue-400" />
          </div>
          <div>
            <h1 className="text-2xl font-bold">Network Scanning Tools</h1>
            <p className="text-muted-foreground">
              Discover and analyze network infrastructure with professional scanning tools
            </p>
          </div>
        </div>
        
        {/* Stats */}
        <div className="flex items-center gap-4 pt-2">
          <Badge variant="outline" className="flex items-center gap-1.5">
            <CheckCircle className="h-3 w-3 text-green-500" />
            {networkTools.filter(t => t.status === 'available').length} Available
          </Badge>
          <Badge variant="outline" className="flex items-center gap-1.5">
            <AlertTriangle className="h-3 w-3 text-yellow-500" />
            1 With Issues
          </Badge>
          <Badge variant="outline">
            {NETWORK_TOOLS.length} Total Tools
          </Badge>
        </div>
      </div>
      
      {/* Tools Grid */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {NETWORK_TOOLS.map((tool) => {
          const backendStatus = getToolStatus(tool.id);
          const finalStatus = tool.status === 'warning' ? 'warning' : backendStatus;
          
          return (
            <Card key={tool.id} className="tool-card group relative">
              <CardHeader className="pb-3">
                <div className="flex items-start justify-between">
                  <div className="flex items-center gap-3">
                    <div className={cn(
                      'p-2 rounded-lg',
                      finalStatus === 'available' && 'bg-green-100 dark:bg-green-900/20',
                      finalStatus === 'warning' && 'bg-yellow-100 dark:bg-yellow-900/20',
                      finalStatus === 'unavailable' && 'bg-red-100 dark:bg-red-900/20'
                    )}>
                      {tool.icon}
                    </div>
                    <div>
                      <CardTitle className="text-lg">{tool.name}</CardTitle>
                      <div className="flex items-center gap-2 mt-1">
                        {getStatusIcon(finalStatus)}
                        <span className="text-xs text-muted-foreground">
                          {getStatusText(finalStatus)}
                        </span>
                      </div>
                    </div>
                  </div>
                  
                  {/* Action buttons */}
                  <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
                    <Link to={`/tools/network-scanning/${tool.id}`}>
                      <Button size="sm" variant="outline" className="h-8 w-8 p-0">
                        <PlayCircle className="h-4 w-4" />
                      </Button>
                    </Link>
                  </div>
                </div>
                
                <CardDescription className="text-sm">
                  {tool.description}
                </CardDescription>
                
                {/* Warning message */}
                {tool.warning && (
                  <div className="flex items-start gap-2 p-2 bg-yellow-50 dark:bg-yellow-900/10 rounded-md border border-yellow-200 dark:border-yellow-800">
                    <AlertTriangle className="h-4 w-4 text-yellow-600 dark:text-yellow-400 mt-0.5 flex-shrink-0" />
                    <p className="text-xs text-yellow-700 dark:text-yellow-300">
                      {tool.warning}
                    </p>
                  </div>
                )}
              </CardHeader>
              
              <CardContent className="space-y-4">
                {/* Key Features */}
                <div>
                  <h4 className="text-sm font-medium mb-2">Key Features</h4>
                  <div className="flex flex-wrap gap-1">
                    {tool.features.slice(0, 3).map((feature, index) => (
                      <Badge key={index} variant="secondary" className="text-xs">
                        {feature}
                      </Badge>
                    ))}
                    {tool.features.length > 3 && (
                      <Badge variant="outline" className="text-xs">
                        +{tool.features.length - 3} more
                      </Badge>
                    )}
                  </div>
                </div>
                
                {/* Primary Use Cases */}
                <div>
                  <h4 className="text-sm font-medium mb-2">Primary Use Cases</h4>
                  <ul className="text-xs text-muted-foreground space-y-1">
                    {tool.useCases.slice(0, 2).map((useCase, index) => (
                      <li key={index} className="flex items-start gap-1">
                        <span className="text-primary mt-1">•</span>
                        <span>{useCase}</span>
                      </li>
                    ))}
                  </ul>
                </div>
                
                {/* Action buttons */}
                <div className="flex gap-2 pt-2">
                  <Link to={`/tools/network-scanning/${tool.id}`} className="flex-1">
                    <Button 
                      className="w-full" 
                      disabled={finalStatus === 'unavailable'}
                    >
                      <PlayCircle className="h-4 w-4 mr-2" />
                      Launch Tool
                    </Button>
                  </Link>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>
      
      {/* Quick Start Guide */}
      <Card className="mt-8">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Quick Start Guide
          </CardTitle>
          <CardDescription>
            Best practices for network scanning and reconnaissance
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            <div className="space-y-2">
              <h4 className="font-medium text-sm">1. Discovery Phase</h4>
              <p className="text-xs text-muted-foreground">
                Start with Nmap for initial network discovery and port scanning. Use ping sweeps and basic port scans to identify live hosts.
              </p>
            </div>
            <div className="space-y-2">
              <h4 className="font-medium text-sm">2. Service Enumeration</h4>
              <p className="text-xs text-muted-foreground">
                Use Nmap with service detection (-sV) and OS fingerprinting (-O) to identify running services and operating systems.
              </p>
            </div>
            <div className="space-y-2">
              <h4 className="font-medium text-sm">3. Specialized Scanning</h4>
              <p className="text-xs text-muted-foreground">
                Deploy Masscan for high-speed scans, OpenVAS for comprehensive vulnerability assessment, and SMB tools for Windows environments.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}"