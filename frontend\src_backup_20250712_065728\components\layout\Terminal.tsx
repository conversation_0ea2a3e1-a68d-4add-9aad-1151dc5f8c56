/**
 * Integrated Terminal Component
 * Provides real-time command output and tool execution monitoring
 */
import React, { useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Terminal as TerminalIcon,\n  X,\n  Maximize2,\n  Minimize2,\n  Copy,\n  Trash2,\n  Download,\n  Play,\n  Square\n} from 'lucide-react';\nimport { cn } from '@/lib/utils';\nimport { webSocketService, ToolOutputMessage } from '@/services/websocket';\nimport { toast } from 'sonner';\n\n/**\n * Terminal output line interface\n */\ninterface TerminalLine {\n  id: string;\n  timestamp: Date;\n  content: string;\n  type: 'info' | 'success' | 'warning' | 'error' | 'command' | 'output';\n  executionId?: string;\n  toolId?: string;\n}\n\n/**\n * Terminal component props\n */\ninterface TerminalProps {\n  isOpen: boolean;\n  onToggle: () => void;\n  className?: string;\n  height?: number;\n}\n\n/**\n * Terminal component for real-time command output\n */\nexport function Terminal({ isOpen, onToggle, className, height = 300 }: TerminalProps) {\n  const [lines, setLines] = React.useState<TerminalLine[]>([]);\n  const [isMaximized, setIsMaximized] = React.useState(false);\n  const [isAutoScroll, setIsAutoScroll] = React.useState(true);\n  const [filter, setFilter] = React.useState<string>('');\n  const outputRef = useRef<HTMLDivElement>(null);\n  const containerRef = useRef<HTMLDivElement>(null);\n  \n  // Add initial welcome message\n  React.useEffect(() => {\n    addLine({\n      content: 'NexusScan Desktop Terminal initialized. Ready for tool execution...',\n      type: 'info'\n    });\n    \n    // Subscribe to WebSocket tool output\n    webSocketService.on('onToolOutput', handleToolOutput);\n    \n    return () => {\n      webSocketService.off('onToolOutput');\n    };\n  }, []);\n  \n  // Auto-scroll to bottom when new lines are added\n  useEffect(() => {\n    if (isAutoScroll && outputRef.current) {\n      outputRef.current.scrollTop = outputRef.current.scrollHeight;\n    }\n  }, [lines, isAutoScroll]);\n  \n  // Handle tool output from WebSocket\n  const handleToolOutput = (message: ToolOutputMessage) => {\n    addLine({\n      content: message.data.output,\n      type: message.data.stream === 'stderr' ? 'error' : 'output',\n      executionId: message.data.executionId,\n      toolId: message.data.toolId\n    });\n  };\n  \n  // Add a new line to the terminal\n  const addLine = (lineData: Omit<TerminalLine, 'id' | 'timestamp'>) => {\n    const newLine: TerminalLine = {\n      id: crypto.randomUUID(),\n      timestamp: new Date(),\n      ...lineData\n    };\n    \n    setLines(prev => {\n      // Keep only the last 1000 lines for performance\n      const newLines = [...prev, newLine];\n      return newLines.slice(-1000);\n    });\n  };\n  \n  // Clear terminal\n  const clearTerminal = () => {\n    setLines([]);\n    addLine({\n      content: 'Terminal cleared',\n      type: 'info'\n    });\n  };\n  \n  // Copy terminal content\n  const copyTerminal = async () => {\n    const content = filteredLines\n      .map(line => `[${line.timestamp.toLocaleTimeString()}] ${line.content}`)\n      .join('\\n');\n    \n    try {\n      await navigator.clipboard.writeText(content);\n      toast.success('Terminal content copied to clipboard');\n    } catch (error) {\n      toast.error('Failed to copy terminal content');\n    }\n  };\n  \n  // Export terminal content\n  const exportTerminal = () => {\n    const content = filteredLines\n      .map(line => `[${line.timestamp.toISOString()}] [${line.type.toUpperCase()}] ${line.content}`)\n      .join('\\n');\n    \n    const blob = new Blob([content], { type: 'text/plain' });\n    const url = URL.createObjectURL(blob);\n    const a = document.createElement('a');\n    a.href = url;\n    a.download = `nexusscan-terminal-${new Date().toISOString().slice(0, 19)}.txt`;\n    document.body.appendChild(a);\n    a.click();\n    document.body.removeChild(a);\n    URL.revokeObjectURL(url);\n    \n    toast.success('Terminal content exported');\n  };\n  \n  // Toggle maximize\n  const toggleMaximize = () => {\n    setIsMaximized(!isMaximized);\n  };\n  \n  // Handle scroll to detect if user scrolled up\n  const handleScroll = () => {\n    if (outputRef.current) {\n      const { scrollTop, scrollHeight, clientHeight } = outputRef.current;\n      const isAtBottom = scrollTop + clientHeight >= scrollHeight - 10;\n      setIsAutoScroll(isAtBottom);\n    }\n  };\n  \n  // Filter lines based on search\n  const filteredLines = React.useMemo(() => {\n    if (!filter) return lines;\n    return lines.filter(line => \n      line.content.toLowerCase().includes(filter.toLowerCase()) ||\n      line.type.toLowerCase().includes(filter.toLowerCase()) ||\n      (line.toolId && line.toolId.toLowerCase().includes(filter.toLowerCase()))\n    );\n  }, [lines, filter]);\n  \n  // Get line type styles\n  const getLineStyle = (type: TerminalLine['type']) => {\n    switch (type) {\n      case 'info':\n        return 'text-blue-400';\n      case 'success':\n        return 'text-green-400';\n      case 'warning':\n        return 'text-yellow-400';\n      case 'error':\n        return 'text-red-400';\n      case 'command':\n        return 'text-purple-400 font-medium';\n      case 'output':\n      default:\n        return 'text-gray-300';\n    }\n  };\n  \n  const getLinePrefix = (type: TerminalLine['type']) => {\n    switch (type) {\n      case 'info':\n        return '[INFO]';\n      case 'success':\n        return '[SUCCESS]';\n      case 'warning':\n        return '[WARN]';\n      case 'error':\n        return '[ERROR]';\n      case 'command':\n        return '[CMD]';\n      case 'output':\n      default:\n        return '[OUT]';\n    }\n  };\n  \n  if (!isOpen) {\n    return null;\n  }\n  \n  const terminalHeight = isMaximized ? '60vh' : `${height}px`;\n  \n  return (\n    <div \n      ref={containerRef}\n      className={cn(\n        'border-t border-border bg-card',\n        'flex flex-col',\n        className\n      )}\n      style={{ height: terminalHeight }}\n    >\n      {/* Terminal header */}\n      <div className=\"flex items-center justify-between px-4 py-2 border-b border-border bg-muted/30\">\n        <div className=\"flex items-center gap-2\">\n          <TerminalIcon className=\"h-4 w-4\" />\n          <span className=\"font-medium text-sm\">Terminal</span>\n          <Badge variant=\"outline\" className=\"text-xs\">\n            {filteredLines.length} lines\n          </Badge>\n        </div>\n        \n        <div className=\"flex items-center gap-1\">\n          {/* Filter input */}\n          <input\n            type=\"text\"\n            placeholder=\"Filter...\"\n            value={filter}\n            onChange={(e) => setFilter(e.target.value)}\n            className=\"px-2 py-1 text-xs bg-background border border-border rounded w-24 focus:w-32 transition-all focus:outline-none focus:ring-1 focus:ring-primary\"\n          />\n          \n          {/* Auto-scroll indicator */}\n          {!isAutoScroll && (\n            <Badge variant=\"secondary\" className=\"text-xs cursor-pointer\" \n                   onClick={() => setIsAutoScroll(true)}>\n              Scroll paused\n            </Badge>\n          )}\n          \n          {/* Control buttons */}\n          <Button\n            variant=\"ghost\"\n            size=\"sm\"\n            onClick={copyTerminal}\n            className=\"p-1 h-6 w-6\"\n            title=\"Copy all\"\n          >\n            <Copy className=\"h-3 w-3\" />\n          </Button>\n          \n          <Button\n            variant=\"ghost\"\n            size=\"sm\"\n            onClick={exportTerminal}\n            className=\"p-1 h-6 w-6\"\n            title=\"Export to file\"\n          >\n            <Download className=\"h-3 w-3\" />\n          </Button>\n          \n          <Button\n            variant=\"ghost\"\n            size=\"sm\"\n            onClick={clearTerminal}\n            className=\"p-1 h-6 w-6\"\n            title=\"Clear terminal\"\n          >\n            <Trash2 className=\"h-3 w-3\" />\n          </Button>\n          \n          <Button\n            variant=\"ghost\"\n            size=\"sm\"\n            onClick={toggleMaximize}\n            className=\"p-1 h-6 w-6\"\n            title={isMaximized ? 'Minimize' : 'Maximize'}\n          >\n            {isMaximized ? (\n              <Minimize2 className=\"h-3 w-3\" />\n            ) : (\n              <Maximize2 className=\"h-3 w-3\" />\n            )}\n          </Button>\n          \n          <Button\n            variant=\"ghost\"\n            size=\"sm\"\n            onClick={onToggle}\n            className=\"p-1 h-6 w-6\"\n            title=\"Close terminal\"\n          >\n            <X className=\"h-3 w-3\" />\n          </Button>\n        </div>\n      </div>\n      \n      {/* Terminal output */}\n      <div \n        ref={outputRef}\n        className=\"flex-1 overflow-y-auto p-4 bg-gray-900 text-gray-100 font-mono text-sm leading-relaxed\"\n        onScroll={handleScroll}\n      >\n        {filteredLines.length === 0 ? (\n          <div className=\"text-gray-500 italic\">\n            {filter ? 'No lines match the current filter' : 'No output yet...'}\n          </div>\n        ) : (\n          filteredLines.map((line) => (\n            <div key={line.id} className=\"flex gap-3 mb-1 hover:bg-gray-800/50 px-2 py-1 rounded\">\n              <span className=\"text-gray-500 text-xs shrink-0 w-20\">\n                {line.timestamp.toLocaleTimeString()}\n              </span>\n              <span className={cn('text-xs shrink-0 w-16', getLineStyle(line.type))}>\n                {getLinePrefix(line.type)}\n              </span>\n              {line.toolId && (\n                <span className=\"text-blue-300 text-xs shrink-0 w-20 truncate\">\n                  [{line.toolId}]\n                </span>\n              )}\n              <span className={cn('flex-1 break-all', getLineStyle(line.type))}>\n                {line.content}\n              </span>\n            </div>\n          ))\n        )}\n        \n        {/* Auto-scroll anchor */}\n        <div id=\"terminal-bottom\" />\n      </div>\n    </div>\n  );\n}\n\n// Export utility function for adding lines from other components\nexport const addTerminalLine = (content: string, type: TerminalLine['type'] = 'info') => {\n  // This would need to be implemented with a global state manager\n  // For now, it's a placeholder for the pattern\n  console.log(`Terminal: [${type.toUpperCase()}] ${content}`);\n};"