"""
SQLMap Scanner Integration for NexusScan Desktop Application
SQL injection vulnerability detection and exploitation using SQLMap.
"""

import json
import asyncio
import logging
import subprocess
import tempfile
import os
from datetime import datetime
from typing import Dict, List, Optional, Callable, Any
from dataclasses import dataclass
from urllib.parse import urlparse

from security.tools.tool_registry import (
    ToolMetadata, ToolCategory, ToolCapabilities,
    ScanOptions, ScanResult, ToolStatus, register_tool
)
from security.tools.base_scanner import BaseScanner

logger = logging.getLogger(__name__)


@dataclass
class SQLMapScanOptions(ScanOptions):
    """SQLMap-specific scan options"""
    # Target options
    url: str = ""
    data: str = ""  # POST data
    cookie: str = ""
    headers: Dict[str, str] = None
    user_agent: str = ""
    referer: str = ""
    
    # Request options
    method: str = "GET"  # GET, POST, PUT, DELETE, etc.
    delay: float = 0.0  # Delay between requests
    timeout: int = 30
    retries: int = 3
    randomize: bool = True  # Randomize parameter values
    
    # Detection options
    level: int = 1  # Detection level (1-5)
    risk: int = 1   # Risk level (1-3)
    string: str = ""  # String to match when query is valid
    not_string: str = ""  # String to match when query is invalid
    regexp: str = ""  # Regular expression to match
    
    # Injection options
    technique: str = "BEUSTQ"  # Injection techniques to use
    time_sec: int = 5  # Time delay for time-based attacks
    union_cols: str = ""  # Range of columns to test for UNION attacks
    union_char: str = ""  # Character to use for UNION attacks
    
    # Enumeration options
    current_user: bool = False
    current_db: bool = False
    is_dba: bool = False
    users: bool = False
    passwords: bool = False
    privileges: bool = False
    roles: bool = False
    dbs: bool = False
    tables: bool = False
    columns: bool = False
    schema: bool = False
    count: bool = False
    dump: bool = False
    dump_all: bool = False
    
    # Database options
    db: str = ""  # Database to enumerate
    tbl: str = ""  # Table to enumerate
    col: str = ""  # Column to enumerate
    exclude_sysdbs: bool = True  # Exclude system databases
    
    # Advanced options
    tamper: List[str] = None  # Tamper scripts to use
    batch: bool = True  # Non-interactive mode
    flush_session: bool = False  # Flush session files
    fresh_queries: bool = False  # Force fresh queries
    hex: bool = False  # Use hex encoding
    output_dir: str = ""  # Output directory
    
    # Proxy and authentication
    proxy: str = ""
    proxy_cred: str = ""
    auth_type: str = ""  # HTTP, NTLM, Digest
    auth_cred: str = ""
    
    # Evasion options
    random_agent: bool = False
    mobile: bool = False
    hpp: bool = False  # HTTP Parameter Pollution
    skip_urlencode: bool = False

    def __post_init__(self):
        super().__post_init__()
        if self.headers is None:
            self.headers = {}
        if self.tamper is None:
            self.tamper = []


@dataclass
class SQLMapVulnerability:
    """SQLMap vulnerability finding"""
    parameter: str
    parameter_type: str  # GET, POST, Cookie, etc.
    technique: str
    title: str
    payload: str
    vector: str
    where: str = ""
    dbms: str = ""
    os: str = ""
    
    # Injection details
    injectable: bool = True
    exploitable: bool = False
    
    # Additional data
    data_extracted: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.data_extracted is None:
            self.data_extracted = {}


@register_tool
class SQLMapScanner(BaseScanner):
    """SQLMap SQL injection scanner implementation"""

    def __init__(self):
        """Initialize SQLMap scanner"""
        self.sqlmap_binary = None
        self.session_dir = None
        super().__init__()

    def get_metadata(self) -> ToolMetadata:
        """Get SQLMap tool metadata"""
        return ToolMetadata(
            name="sqlmap",
            display_name="SQLMap SQL Injection Scanner",
            description="Automatic SQL injection and database takeover tool",
            version="1.0.0",
            category=ToolCategory.WEB_SCANNER,
            author="Bernardo Damele A. G.",
            website="https://sqlmap.org/",
            documentation="https://github.com/sqlmapproject/sqlmap/wiki",
            capabilities=ToolCapabilities(
                supports_async=True,
                supports_progress=True,
                supports_cancellation=True,
                requires_root=False,
                network_access_required=True,
                output_formats=["json", "txt"],
                supported_targets=["url", "file"]
            ),
            default_options={
                "level": 1,
                "risk": 1,
                "technique": "BEUSTQ",
                "batch": True,
                "randomize": True
            },
            required_dependencies=["sqlmap"]
        )

    def check_native_availability(self) -> bool:
        """Check if SQLMap is available"""
        try:
            # Try direct sqlmap command first (from pip install)
            result = subprocess.run(
                ["sqlmap", "--version"], 
                capture_output=True, 
                text=True, 
                timeout=10
            )
            
            if result.returncode == 0:
                self.sqlmap_binary = ["sqlmap"]
                logger.info(f"SQLMap found directly: {result.stdout.strip()}")
                return True
            
            # Try python module
            result = subprocess.run(
                ["python", "-m", "sqlmap", "--version"], 
                capture_output=True, 
                text=True, 
                timeout=10
            )
            
            if result.returncode == 0:
                self.sqlmap_binary = ["python", "-m", "sqlmap"]
                logger.info(f"SQLMap found via python module: {result.stdout.strip()}")
                return True
            
            # Try python3 module
            result = subprocess.run(
                ["python3", "-m", "sqlmap", "--version"], 
                capture_output=True, 
                text=True, 
                timeout=10
            )
            
            if result.returncode == 0:
                self.sqlmap_binary = ["python3", "-m", "sqlmap"]
                logger.info(f"SQLMap found via python3 module: {result.stdout.strip()}")
                return True
                
            logger.error("SQLMap binary not found")
            return False
                
        except FileNotFoundError:
            logger.error("SQLMap not found in PATH")
            return False
        except subprocess.TimeoutExpired:
            logger.error("SQLMap version check timed out")
            return False
        except Exception as e:
            logger.error(f"SQLMap availability check failed: {e}")
            return False

    async def execute_native_scan(self, options: ScanOptions,
                                  progress_callback: Optional[Callable[[float, str], None]] = None) -> ScanResult:
        """Execute SQLMap scan"""
        if not isinstance(options, SQLMapScanOptions):
            # Convert generic options to SQLMap options
            sqlmap_options = SQLMapScanOptions(
                url=options.target,
                timeout=options.timeout or 30
            )
        else:
            sqlmap_options = options

        start_time = datetime.now()
        
        if progress_callback:
            progress_callback(0.0, "Initializing SQLMap scan...")

        try:
            # Create session directory
            self.session_dir = tempfile.mkdtemp(prefix="sqlmap_session_")
            
            # Build SQLMap command
            command = self._build_sqlmap_command(sqlmap_options)
            
            if progress_callback:
                progress_callback(0.1, "Starting SQL injection scan...")

            # Execute scan
            scan_output = await self._execute_sqlmap_scan(command, progress_callback)
            
            if progress_callback:
                progress_callback(0.8, "Parsing scan results...")

            # Parse results
            parsed_results = self._parse_scan_results(scan_output, sqlmap_options)
            
            if progress_callback:
                progress_callback(0.9, "Processing vulnerabilities...")

            # Extract vulnerabilities
            vulnerabilities = self._extract_vulnerabilities(parsed_results, sqlmap_options)

            if progress_callback:
                progress_callback(1.0, "Scan completed")

            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()

            return ScanResult(
                tool_name="sqlmap",
                target=sqlmap_options.url or sqlmap_options.target,
                status=ToolStatus.COMPLETED,
                start_time=start_time.isoformat(),
                end_time=end_time.isoformat(),
                duration_seconds=duration,
                raw_output=scan_output,
                parsed_results=parsed_results,
                vulnerabilities=vulnerabilities,
                metadata={
                    "sqlmap_version": self._get_sqlmap_version(),
                    "scan_options": {
                        "level": sqlmap_options.level,
                        "risk": sqlmap_options.risk,
                        "technique": sqlmap_options.technique
                    },
                    "vulnerabilities_found": len(vulnerabilities)
                }
            )

        except Exception as e:
            logger.error(f"SQLMap scan failed: {e}")
            return ScanResult(
                tool_name="sqlmap",
                target=sqlmap_options.url or sqlmap_options.target,
                status=ToolStatus.FAILED,
                start_time=start_time.isoformat(),
                errors=[str(e)]
            )
        finally:
            # Cleanup session directory
            if self.session_dir and os.path.exists(self.session_dir):
                try:
                    import shutil
                    shutil.rmtree(self.session_dir)
                except Exception as e:
                    logger.warning(f"Failed to cleanup session directory: {e}")

    def _build_sqlmap_command(self, options: SQLMapScanOptions) -> List[str]:
        """Build SQLMap command arguments"""
        command = self.sqlmap_binary.copy()
        
        # Target URL
        if options.url:
            command.extend(["-u", options.url])
        elif options.target:
            command.extend(["-u", options.target])
        
        # Request options
        if options.data:
            command.extend(["--data", options.data])
        
        if options.cookie:
            command.extend(["--cookie", options.cookie])
        
        if options.headers:
            for key, value in options.headers.items():
                command.extend(["--header", f"{key}: {value}"])
        
        if options.user_agent:
            command.extend(["--user-agent", options.user_agent])
        
        if options.referer:
            command.extend(["--referer", options.referer])
        
        # Method
        if options.method and options.method.upper() != "GET":
            command.extend(["--method", options.method.upper()])
        
        # Timing options
        if options.delay > 0:
            command.extend(["--delay", str(options.delay)])
        
        if options.timeout != 30:
            command.extend(["--timeout", str(options.timeout)])
        
        if options.retries != 3:
            command.extend(["--retries", str(options.retries)])
        
        # Detection options
        command.extend(["--level", str(options.level)])
        command.extend(["--risk", str(options.risk)])
        
        if options.string:
            command.extend(["--string", options.string])
        
        if options.not_string:
            command.extend(["--not-string", options.not_string])
        
        if options.regexp:
            command.extend(["--regexp", options.regexp])
        
        # Injection options
        if options.technique != "BEUSTQ":
            command.extend(["--technique", options.technique])
        
        if options.time_sec != 5:
            command.extend(["--time-sec", str(options.time_sec)])
        
        if options.union_cols:
            command.extend(["--union-cols", options.union_cols])
        
        if options.union_char:
            command.extend(["--union-char", options.union_char])
        
        # Enumeration options
        if options.current_user:
            command.append("--current-user")
        
        if options.current_db:
            command.append("--current-db")
        
        if options.is_dba:
            command.append("--is-dba")
        
        if options.users:
            command.append("--users")
        
        if options.passwords:
            command.append("--passwords")
        
        if options.privileges:
            command.append("--privileges")
        
        if options.roles:
            command.append("--roles")
        
        if options.dbs:
            command.append("--dbs")
        
        if options.tables:
            command.append("--tables")
        
        if options.columns:
            command.append("--columns")
        
        if options.schema:
            command.append("--schema")
        
        if options.count:
            command.append("--count")
        
        if options.dump:
            command.append("--dump")
        
        if options.dump_all:
            command.append("--dump-all")
        
        # Database-specific options
        if options.db:
            command.extend(["-D", options.db])
        
        if options.tbl:
            command.extend(["-T", options.tbl])
        
        if options.col:
            command.extend(["-C", options.col])
        
        if options.exclude_sysdbs:
            command.append("--exclude-sysdbs")
        
        # Tamper scripts
        if options.tamper:
            command.extend(["--tamper", ",".join(options.tamper)])
        
        # Advanced options
        if options.batch:
            command.append("--batch")
        
        if options.flush_session:
            command.append("--flush-session")
        
        if options.fresh_queries:
            command.append("--fresh-queries")
        
        if options.hex:
            command.append("--hex")
        
        if options.randomize:
            command.append("--randomize")
        
        # Output options
        if self.session_dir:
            command.extend(["--output-dir", self.session_dir])
        
        # Proxy options
        if options.proxy:
            command.extend(["--proxy", options.proxy])
        
        if options.proxy_cred:
            command.extend(["--proxy-cred", options.proxy_cred])
        
        # Authentication
        if options.auth_type:
            command.extend(["--auth-type", options.auth_type])
        
        if options.auth_cred:
            command.extend(["--auth-cred", options.auth_cred])
        
        # Evasion options
        if options.random_agent:
            command.append("--random-agent")
        
        if options.mobile:
            command.append("--mobile")
        
        if options.hpp:
            command.append("--hpp")
        
        if options.skip_urlencode:
            command.append("--skip-urlencode")
        
        # Always use these for better automation
        command.extend(["--parse-errors", "--threads", "1"])
        
        return command

    async def _execute_sqlmap_scan(self, command: List[str], 
                                  progress_callback: Optional[Callable[[float, str], None]] = None) -> str:
        """Execute the SQLMap scan command"""
        try:
            if progress_callback:
                progress_callback(0.2, f"Running SQLMap scan...")

            # Execute scan
            try:
                loop = asyncio.get_running_loop()
            except RuntimeError:
                # No running loop, create one
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
            
            result = await loop.run_in_executor(
                None, self._run_subprocess, command
            )
            
            if progress_callback:
                progress_callback(0.7, "Scan execution completed")

            return result

        except Exception as e:
            logger.error(f"SQLMap execution failed: {e}")
            raise

    def _run_subprocess(self, command: List[str]) -> str:
        """Run subprocess synchronously"""
        try:
            result = subprocess.run(
                command,
                capture_output=True,
                text=True,
                timeout=600,  # 10 minutes timeout
                cwd=self.session_dir
            )
            
            # SQLMap may return non-zero even on success
            output = result.stdout
            if result.stderr:
                output += "\n" + result.stderr
            
            return output
            
        except subprocess.TimeoutExpired:
            logger.error("SQLMap scan timed out")
            raise Exception("Scan timed out")
        except Exception as e:
            logger.error(f"Subprocess execution failed: {e}")
            raise

    def _parse_scan_results(self, output: str, options: SQLMapScanOptions) -> Dict[str, Any]:
        """Parse SQLMap output"""
        results = {
            "scan_info": {
                "scanner": "sqlmap",
                "target": options.url or options.target,
                "scan_type": "sql_injection"
            },
            "vulnerabilities": [],
            "injections": [],
            "database_info": {},
            "enumeration_results": {},
            "statistics": {
                "total_injections": 0,
                "parameters_tested": 0,
                "vulnerable_parameters": 0
            }
        }
        
        if not output.strip():
            return results
        
        # Parse output line by line
        lines = output.split('\n')
        current_injection = None
        in_injection_section = False
        
        for line in lines:
            line = line.strip()
            
            # Detection of injection points
            if "sqlmap identified the following injection point" in line.lower():
                in_injection_section = True
                current_injection = {}
                continue
            
            if in_injection_section and line.startswith("Parameter:"):
                if current_injection is not None:
                    current_injection["parameter"] = line.split(":", 1)[1].strip()
            
            elif in_injection_section and line.startswith("Type:"):
                if current_injection is not None:
                    current_injection["technique"] = line.split(":", 1)[1].strip()
            
            elif in_injection_section and line.startswith("Title:"):
                if current_injection is not None:
                    current_injection["title"] = line.split(":", 1)[1].strip()
            
            elif in_injection_section and line.startswith("Payload:"):
                if current_injection is not None:
                    current_injection["payload"] = line.split(":", 1)[1].strip()
                    results["injections"].append(current_injection)
                    current_injection = None
                    in_injection_section = False
            
            # Database information
            elif "back-end DBMS:" in line.lower():
                dbms_info = line.split(":", 1)[1].strip()
                results["database_info"]["dbms"] = dbms_info
            
            elif "current user:" in line.lower():
                user_info = line.split(":", 1)[1].strip()
                results["database_info"]["current_user"] = user_info
            
            elif "current database:" in line.lower():
                db_info = line.split(":", 1)[1].strip()
                results["database_info"]["current_database"] = db_info
        
        # Update statistics
        results["statistics"]["total_injections"] = len(results["injections"])
        results["statistics"]["vulnerable_parameters"] = len(results["injections"])
        
        return results

    def _extract_vulnerabilities(self, parsed_results: Dict[str, Any], options: SQLMapScanOptions) -> List[Dict[str, Any]]:
        """Extract vulnerabilities from parsed results"""
        vulnerabilities = []
        
        for injection in parsed_results.get("injections", []):
            try:
                vulnerability = {
                    "id": f"sqlmap_{injection.get('parameter', 'unknown')}_{options.url or options.target}",
                    "type": "sql_injection",
                    "severity": self._assess_severity(injection),
                    "title": injection.get("title", "SQL Injection Vulnerability"),
                    "description": self._build_description(injection, parsed_results),
                    "target": options.url or options.target,
                    "parameter": injection.get("parameter", ""),
                    "technique": injection.get("technique", ""),
                    "payload": injection.get("payload", ""),
                    "evidence": {
                        "injection_data": injection,
                        "database_info": parsed_results.get("database_info", {}),
                        "detection_method": "sqlmap_scan"
                    },
                    "remediation": self._get_remediation_advice(injection),
                    "references": [
                        "https://owasp.org/www-project-top-ten/OWASP_Top_Ten_2017/Top_10-2017_A1-Injection",
                        "https://cwe.mitre.org/data/definitions/89.html"
                    ],
                    "cvss_score": self._calculate_cvss_score(injection),
                    "exploitability": self._assess_exploitability(injection)
                }
                
                vulnerabilities.append(vulnerability)
                
            except Exception as e:
                logger.error(f"Failed to process injection: {e}")
                continue
        
        return vulnerabilities

    def _assess_severity(self, injection: Dict[str, Any]) -> str:
        """Assess vulnerability severity"""
        technique = injection.get("technique", "").lower()
        
        if "union" in technique or "boolean" in technique:
            return "high"
        elif "time" in technique or "error" in technique:
            return "medium"
        else:
            return "medium"

    def _build_description(self, injection: Dict[str, Any], parsed_results: Dict[str, Any]) -> str:
        """Build vulnerability description"""
        parameter = injection.get("parameter", "unknown")
        technique = injection.get("technique", "unknown")
        dbms = parsed_results.get("database_info", {}).get("dbms", "unknown")
        
        return f"SQL injection vulnerability detected in parameter '{parameter}' using {technique} technique. Backend DBMS appears to be {dbms}."

    def _get_remediation_advice(self, injection: Dict[str, Any]) -> str:
        """Get remediation advice for SQL injection"""
        return "Use parameterized queries or prepared statements to prevent SQL injection. Validate and sanitize all user input. Implement proper error handling to avoid information disclosure."

    def _calculate_cvss_score(self, injection: Dict[str, Any]) -> float:
        """Calculate CVSS score for SQL injection"""
        # SQL injection typically scores high due to potential for data access
        base_score = 7.5
        
        technique = injection.get("technique", "").lower()
        if "union" in technique:
            base_score = 9.0  # High for UNION attacks (data extraction)
        elif "boolean" in technique:
            base_score = 8.0  # High for boolean-based attacks
        elif "time" in technique:
            base_score = 7.0  # Medium-high for time-based attacks
        
        return base_score

    def _assess_exploitability(self, injection: Dict[str, Any]) -> str:
        """Assess exploitability level"""
        technique = injection.get("technique", "").lower()
        
        if "union" in technique:
            return "high"
        elif "boolean" in technique or "error" in technique:
            return "medium"
        elif "time" in technique:
            return "medium"
        else:
            return "low"

    def _get_sqlmap_version(self) -> str:
        """Get SQLMap version"""
        try:
            if self.sqlmap_binary:
                result = subprocess.run(
                    self.sqlmap_binary + ["--version"],
                    capture_output=True,
                    text=True,
                    timeout=5
                )
                if result.returncode == 0:
                    return result.stdout.strip()
        except Exception:
            pass
        return "unknown"

    def get_scan_presets(self) -> Dict[str, SQLMapScanOptions]:
        """Get predefined scan presets"""
        return {
            "quick_scan": SQLMapScanOptions(
                target="",
                url="",
                level=1,
                risk=1,
                technique="B",  # Boolean-based only
                batch=True,
                randomize=True
            ),
            "comprehensive_scan": SQLMapScanOptions(
                target="",
                url="",
                level=3,
                risk=2,
                technique="BEUSTQ",  # All techniques
                batch=True,
                randomize=True,
                current_user=True,
                current_db=True,
                dbs=True
            ),
            "stealth_scan": SQLMapScanOptions(
                target="",
                url="",
                level=2,
                risk=1,
                technique="T",  # Time-based only
                delay=1.0,
                randomize=True,
                random_agent=True,
                batch=True
            ),
            "aggressive_scan": SQLMapScanOptions(
                target="",
                url="",
                level=5,
                risk=3,
                technique="BEUSTQ",
                batch=True,
                randomize=True,
                current_user=True,
                current_db=True,
                users=True,
                passwords=True,
                dbs=True,
                tables=True,
                fresh_queries=True
            )
        }

    def get_tamper_scripts(self) -> List[str]:
        """Get available tamper scripts"""
        return [
            "apostrophemask",
            "apostrophenullencode", 
            "base64encode",
            "between",
            "chardoubleencode",
            "charencode",
            "charunicodeencode",
            "equaltolike",
            "greatest",
            "halfversionedmorekeywords",
            "ifnull2ifisnull",
            "modsecurityversioned",
            "modsecurityzeroversioned",
            "multiplespaces",
            "percentage",
            "randomcase",
            "randomcomments",
            "space2comment",
            "space2hash",
            "space2morehash",
            "space2mysqldash",
            "space2plus",
            "space2randomblank",
            "unionalltounion",
            "unmagicquotes",
            "versionedkeywords",
            "versionedmorekeywords"
        ]