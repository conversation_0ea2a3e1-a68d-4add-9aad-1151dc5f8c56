#!/usr/bin/env python3
"""
NexusScan Setup with Virtual Environment
Properly sets up venv and installs all dependencies to fix WSL limitations.
"""

import sys
import os
import subprocess
import tempfile
import shutil
from pathlib import Path

def print_header(text):
    """Print a formatted header"""
    print(f"\n{'='*60}")
    print(f"🛡️ {text}")
    print(f"{'='*60}")

def print_step(step, text):
    """Print a formatted step"""
    print(f"\n{step}️⃣ {text}")
    print("-" * 50)

def run_command(command, description, check=True, timeout=300, env=None):
    """Run a command with proper error handling"""
    print(f"  Running: {description}")
    try:
        if isinstance(command, str):
            result = subprocess.run(command, shell=True, capture_output=True, text=True, timeout=timeout, env=env)
        else:
            result = subprocess.run(command, capture_output=True, text=True, timeout=timeout, env=env)
        
        if result.returncode == 0:
            print(f"  ✅ {description} - Success")
            return True, result.stdout
        else:
            if check:
                print(f"  ❌ {description} - Failed")
                print(f"     Error: {result.stderr[:200]}...")
            else:
                print(f"  ⚠️ {description} - Non-critical failure")
            return False, result.stderr
    except subprocess.TimeoutExpired:
        print(f"  ⏰ {description} - Timed out")
        return False, "Command timed out"
    except Exception as e:
        print(f"  💥 {description} - Exception: {e}")
        return False, str(e)

def check_python_version():
    """Check Python version"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print(f"❌ Python {version.major}.{version.minor} is not supported")
        print("Please use Python 3.8 or later")
        return False
    
    print(f"✅ Python {version.major}.{version.minor}.{version.micro} is compatible")
    return True

def create_virtual_environment():
    """Create and setup virtual environment"""
    print_step("1", "Creating Virtual Environment")
    
    venv_path = Path("venv")
    
    # Remove existing venv if it exists
    if venv_path.exists():
        print("  Removing existing virtual environment...")
        shutil.rmtree(venv_path)
    
    # Create new venv
    success, output = run_command(
        [sys.executable, "-m", "venv", "venv"],
        "Creating virtual environment"
    )
    
    if not success:
        return False, None
    
    # Determine activation script path
    if os.name == 'nt':  # Windows
        activate_script = venv_path / "Scripts" / "activate"
        python_exe = venv_path / "Scripts" / "python.exe"
        pip_exe = venv_path / "Scripts" / "pip.exe"
    else:  # Linux/Mac
        activate_script = venv_path / "bin" / "activate"
        python_exe = venv_path / "bin" / "python"
        pip_exe = venv_path / "bin" / "pip"
    
    # Verify venv was created
    if not python_exe.exists():
        print("  ❌ Virtual environment creation failed")
        return False, None
    
    print(f"  ✅ Virtual environment created at: {venv_path}")
    print(f"  📍 Python executable: {python_exe}")
    print(f"  📍 Pip executable: {pip_exe}")
    
    return True, {
        'venv_path': venv_path,
        'python_exe': str(python_exe),
        'pip_exe': str(pip_exe),
        'activate_script': str(activate_script)
    }

def install_dependencies_in_venv(venv_info):
    """Install all dependencies in virtual environment"""
    print_step("2", "Installing Dependencies in Virtual Environment")
    
    python_exe = venv_info['python_exe']
    pip_exe = venv_info['pip_exe']
    
    # Upgrade pip first
    success, output = run_command(
        [python_exe, "-m", "pip", "install", "--upgrade", "pip"],
        "Upgrading pip in virtual environment"
    )
    
    # Core dependencies
    core_deps = [
        "flet>=0.24.0",
        "requests>=2.28.0",
        "sqlalchemy>=2.0.0",
        "cryptography>=40.0.0",
        "pydantic>=2.0.0"
    ]
    
    # Security tool dependencies
    security_deps = [
        "python-nmap>=0.7.1",
        "aiohttp>=3.8.0"
    ]
    
    # AI dependencies
    ai_deps = [
        "openai>=1.0.0",
        "anthropic>=0.25.0"
    ]
    
    # Reporting dependencies (optional)
    reporting_deps = [
        "reportlab>=4.0.0",
        "python-docx>=1.0.0",
        "openpyxl>=3.1.0",
        "Pillow>=10.0.0"
    ]
    
    # Development dependencies
    dev_deps = [
        "pytest>=7.0.0",
        "pytest-asyncio>=0.21.0"
    ]
    
    # Install in groups
    dep_groups = [
        ("Core", core_deps),
        ("Security Tools", security_deps),
        ("AI Services", ai_deps),
        ("Reporting", reporting_deps),
        ("Development", dev_deps)
    ]
    
    successful_installs = 0
    total_packages = sum(len(deps) for _, deps in dep_groups)
    
    for group_name, deps in dep_groups:
        print(f"\n  Installing {group_name} dependencies...")
        for dep in deps:
            success, output = run_command(
                [python_exe, "-m", "pip", "install", dep],
                f"Installing {dep}",
                check=False,
                timeout=120
            )
            if success:
                successful_installs += 1
    
    print(f"\n  📊 Installation Summary: {successful_installs}/{total_packages} packages installed")
    
    # Test critical imports in venv
    print("\n  🧪 Testing critical imports in virtual environment...")
    test_imports = [
        ("flet", "Flet UI framework"),
        ("requests", "HTTP requests"),
        ("sqlalchemy", "Database ORM"),
        ("nmap", "Python-nmap"),
        ("aiohttp", "Async HTTP"),
        ("openai", "OpenAI API"),
        ("anthropic", "Anthropic API")
    ]
    
    working_imports = []
    for module, description in test_imports:
        success, output = run_command(
            [python_exe, "-c", f"import {module}; print('✅ {description}')"],
            f"Testing {description}",
            check=False
        )
        if success:
            working_imports.append(module)
            print(f"    ✅ {description}")
        else:
            print(f"    ❌ {description}")
    
    return len(working_imports) >= 4  # Need at least 4 critical imports working

def setup_database_in_venv(venv_info):
    """Setup database using virtual environment"""
    print_step("3", "Setting up Database")
    
    python_exe = venv_info['python_exe']
    
    # Add src to PYTHONPATH for venv
    env = os.environ.copy()
    src_path = os.path.join(os.getcwd(), 'src')
    if 'PYTHONPATH' in env:
        env['PYTHONPATH'] = f"{src_path}{os.pathsep}{env['PYTHONPATH']}"
    else:
        env['PYTHONPATH'] = src_path
    
    success, output = run_command(
        [python_exe, "scripts/init_database.py"],
        "Initializing database schema",
        env=env
    )
    
    return success

def test_framework_in_venv(venv_info):
    """Test framework components in virtual environment"""
    print_step("4", "Testing Framework Components")
    
    python_exe = venv_info['python_exe']
    
    # Set up environment
    env = os.environ.copy()
    src_path = os.path.join(os.getcwd(), 'src')
    if 'PYTHONPATH' in env:
        env['PYTHONPATH'] = f"{src_path}{os.pathsep}{env['PYTHONPATH']}"
    else:
        env['PYTHONPATH'] = src_path
    
    # Test basic functionality
    print("  Testing basic functionality...")
    success, output = run_command(
        [python_exe, "test_basic.py"],
        "Basic functionality test",
        env=env
    )
    
    if not success:
        return False
    
    # Test security framework
    print("\n  Testing security framework...")
    success, output = run_command(
        [python_exe, "test_security_framework.py"],
        "Security framework test",
        env=env,
        check=False  # Allow warnings
    )
    
    return True

def create_venv_launcher():
    """Create launcher scripts for virtual environment"""
    print_step("5", "Creating Virtual Environment Launchers")
    
    # Create activation helper script
    launcher_content = '''#!/bin/bash
# NexusScan Virtual Environment Launcher

echo "🛡️ NexusScan Virtual Environment"
echo "==============================="

# Check if virtual environment exists
if [ ! -d "venv" ]; then
    echo "❌ Virtual environment not found. Run setup_with_venv.py first."
    exit 1
fi

# Activate virtual environment
echo "✅ Activating virtual environment..."
source venv/bin/activate

# Add src to PYTHONPATH
export PYTHONPATH="$(pwd)/src:$PYTHONPATH"

echo "✅ Environment ready!"
echo ""
echo "Available commands:"
echo "  python test_basic.py              # Test basic functionality"
echo "  python test_security_framework.py # Test security framework"
echo "  python quick_test.py              # Quick status check"
echo "  python src/main.py                # Run NexusScan application"
echo ""
echo "To exit: deactivate"
echo ""

# Start interactive shell with venv activated
bash --rcfile <(echo '. ~/.bashrc; PS1="(nexusscan-venv) $ "')
'''
    
    try:
        with open("launch_venv.sh", 'w') as f:
            f.write(launcher_content)
        os.chmod("launch_venv.sh", 0o755)
        print("  ✅ Created launch_venv.sh")
    except Exception as e:
        print(f"  ⚠️ Failed to create launcher: {e}")
    
    # Create Windows batch file
    batch_content = '''@echo off
echo 🛡️ NexusScan Virtual Environment
echo ===============================

if not exist "venv" (
    echo ❌ Virtual environment not found. Run setup_with_venv.py first.
    pause
    exit /b 1
)

echo ✅ Activating virtual environment...
call venv\\Scripts\\activate.bat

set PYTHONPATH=%cd%\\src;%PYTHONPATH%

echo ✅ Environment ready!
echo.
echo Available commands:
echo   python test_basic.py              # Test basic functionality
echo   python test_security_framework.py # Test security framework  
echo   python quick_test.py              # Quick status check
echo   python src/main.py                # Run NexusScan application
echo.
echo To exit: deactivate
echo.

cmd /k
'''
    
    try:
        with open("launch_venv.bat", 'w') as f:
            f.write(batch_content)
        print("  ✅ Created launch_venv.bat")
    except Exception as e:
        print(f"  ⚠️ Failed to create batch file: {e}")
    
    return True

def create_test_runner():
    """Create test runner that uses venv"""
    print_step("6", "Creating Test Runner")
    
    test_runner_content = '''#!/usr/bin/env python3
"""
NexusScan Test Runner (uses virtual environment)
Runs all tests within the virtual environment context.
"""

import sys
import os
import subprocess
from pathlib import Path

def run_in_venv(script_name, description):
    """Run a script in the virtual environment"""
    venv_path = Path("venv")
    
    if os.name == 'nt':  # Windows
        python_exe = venv_path / "Scripts" / "python.exe"
    else:  # Linux/Mac
        python_exe = venv_path / "bin" / "python"
    
    if not python_exe.exists():
        print(f"❌ Virtual environment not found. Run setup_with_venv.py first.")
        return False
    
    # Set up environment
    env = os.environ.copy()
    src_path = os.path.join(os.getcwd(), 'src')
    if 'PYTHONPATH' in env:
        env['PYTHONPATH'] = f"{src_path}{os.pathsep}{env['PYTHONPATH']}"
    else:
        env['PYTHONPATH'] = src_path
    
    print(f"🧪 {description}")
    print("-" * 50)
    
    try:
        result = subprocess.run(
            [str(python_exe), script_name],
            env=env,
            text=True
        )
        return result.returncode == 0
    except Exception as e:
        print(f"❌ Failed to run {script_name}: {e}")
        return False

def main():
    """Main test runner"""
    print("🛡️ NexusScan Test Runner (Virtual Environment)")
    print("=" * 60)
    
    tests = [
        ("test_basic.py", "Basic Functionality Test"),
        ("quick_test.py", "Quick Status Check"),
        ("test_security_framework.py", "Security Framework Test"),
        ("test_nuclei.py", "Nuclei Scanner Test"),
        ("test_sqlmap.py", "SQLMap Scanner Test"),
        ("test_ai_services.py", "AI Services Test")
    ]
    
    results = {}
    
    for script, description in tests:
        if os.path.exists(script):
            results[script] = run_in_venv(script, description)
            print()
        else:
            print(f"⚠️ {script} not found, skipping...")
            results[script] = None
    
    # Summary
    print("📊 Test Results Summary")
    print("=" * 40)
    
    passed = sum(1 for result in results.values() if result is True)
    total = sum(1 for result in results.values() if result is not None)
    
    for script, result in results.items():
        if result is True:
            print(f"✅ {script}")
        elif result is False:
            print(f"❌ {script}")
        else:
            print(f"⚠️ {script} (not found)")
    
    print(f"\\n🎯 Overall: {passed}/{total} tests passed")
    
    if passed == total and total > 0:
        print("🎉 All tests passed in virtual environment!")
    elif passed > 0:
        print("⚠️ Some tests passed, check individual results above")
    else:
        print("❌ Most tests failed, check virtual environment setup")
    
    return 0 if passed == total else 1

if __name__ == "__main__":
    sys.exit(main())
'''
    
    try:
        with open("test_runner_venv.py", 'w') as f:
            f.write(test_runner_content)
        os.chmod("test_runner_venv.py", 0o755)
        print("  ✅ Created test_runner_venv.py")
        return True
    except Exception as e:
        print(f"  ❌ Failed to create test runner: {e}")
        return False

def main():
    """Main setup function"""
    print_header("NexusScan Setup with Virtual Environment")
    print("This script will create a virtual environment and install all dependencies")
    print("to fix WSL environment limitations.")
    
    if not check_python_version():
        return 1
    
    # Step 1: Create virtual environment
    success, venv_info = create_virtual_environment()
    if not success:
        print("❌ Failed to create virtual environment")
        return 1
    
    # Step 2: Install dependencies
    success = install_dependencies_in_venv(venv_info)
    if not success:
        print("❌ Failed to install critical dependencies")
        return 1
    
    # Step 3: Setup database
    success = setup_database_in_venv(venv_info)
    if not success:
        print("⚠️ Database setup failed, but continuing...")
    
    # Step 4: Test framework
    success = test_framework_in_venv(venv_info)
    if not success:
        print("⚠️ Some framework tests failed, but continuing...")
    
    # Step 5: Create launchers
    create_venv_launcher()
    
    # Step 6: Create test runner
    create_test_runner()
    
    # Final summary
    print_header("Setup Complete!")
    
    print("🎉 Virtual environment setup completed successfully!")
    print("")
    print("📋 Next steps:")
    print("1. Activate virtual environment:")
    print("   Linux/Mac: source venv/bin/activate")
    print("   Windows:   venv\\Scripts\\activate.bat")
    print("")
    print("2. Or use the launcher scripts:")
    print("   Linux/Mac: ./launch_venv.sh")
    print("   Windows:   launch_venv.bat")
    print("")
    print("3. Run tests in virtual environment:")
    print("   python test_runner_venv.py")
    print("")
    print("4. Run NexusScan application:")
    print("   python src/main.py")
    print("")
    print("📁 Files created:")
    print("   - venv/                 # Virtual environment")
    print("   - launch_venv.sh        # Linux/Mac launcher")
    print("   - launch_venv.bat       # Windows launcher")
    print("   - test_runner_venv.py   # Test runner")
    
    return 0

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n⏹️ Setup interrupted by user")
        sys.exit(130)
    except Exception as e:
        print(f"\n💥 Setup failed: {e}")
        sys.exit(1)