/**
 * Dirb <PERSON>anner Component
 * Web content scanner for discovering hidden directories and files
 */
import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { 
  FolderOpen,
  Play,
  Square,
  Download,
  Settings,
  Target,
  Globe,
  FileText,
  Terminal,
  Copy,
  RotateCcw,
  Clock,
  Zap,
  Filter,
  Eye,
  AlertTriangle,
  CheckCircle,
  TrendingUp,
  Activity,
  Lock,
  Search,
  List,
  BarChart3,
  Database,
  Server,
  Link,
  Info
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useBackendStore } from '@/stores/backend-store';
import { apiClient, ToolExecution } from '@/services/api-client';
import { toast } from 'sonner';

/**
 * Dirb configuration interface
 */
interface DirbConfig {
  url: string;
  wordlist: string;
  customWordlist: string;
  extensions: string[];
  speed: 'slow' | 'normal' | 'fast' | 'turbo';
  recursive: boolean;
  ignoreCase: boolean;
  followRedirects: boolean;
  includeStatusCodes: string[];
  excludeStatusCodes: string[];
  authentication: {
    enabled: boolean;
    username: string;
    password: string;
    type: 'basic' | 'ntlm';
  };
  proxy: {
    enabled: boolean;
    url: string;
    credentials: string;
  };
  headers: { [key: string]: string };
  cookies: string;
  userAgent: string;
  timeout: number;
  maxRedirects: number;
  outputFile: string;
  silent: boolean;
  generateTree: boolean;
  showNotFound: boolean;
  customArgs: string;
}

/**
 * Dirb wordlists
 */
const WORDLISTS = {
  'common': '/usr/share/dirb/wordlists/common.txt',
  'big': '/usr/share/dirb/wordlists/big.txt',
  'catala': '/usr/share/dirb/wordlists/catala.txt',
  'spanish': '/usr/share/dirb/wordlists/spanish.txt',
  'euskera': '/usr/share/dirb/wordlists/euskera.txt',
  'extensions_common': '/usr/share/dirb/wordlists/extensions_common.txt',
  'indexes': '/usr/share/dirb/wordlists/indexes.txt',
  'mutations_common': '/usr/share/dirb/wordlists/mutations_common.txt',
  'small': '/usr/share/dirb/wordlists/small.txt',
  'stress': '/usr/share/dirb/wordlists/stress.txt',
  'vulns': '/usr/share/dirb/wordlists/vulns.txt'
};

/**
 * Speed configurations
 */
const SPEED_CONFIGS = {
  'slow': { delay: 1000, description: 'Slow scan (1s delay) - Stealth mode' },
  'normal': { delay: 500, description: 'Normal scan (0.5s delay) - Balanced' },
  'fast': { delay: 100, description: 'Fast scan (0.1s delay) - Quick discovery' },
  'turbo': { delay: 0, description: 'Turbo scan (no delay) - Maximum speed' }
};

/**
 * Common file extensions for web content
 */
const EXTENSION_PRESETS = {
  'web-common': ['php', 'asp', 'aspx', 'jsp', 'html', 'htm', 'js', 'css'],
  'php': ['php', 'php3', 'php4', 'php5', 'phtml'],
  'asp': ['asp', 'aspx', 'ascx', 'ashx'],
  'jsp': ['jsp', 'jspx', 'jsw', 'jsv'],
  'backup': ['bak', 'backup', 'old', 'orig'],
  'config': ['conf', 'config', 'cfg', 'ini'],
  'text': ['txt', 'log', 'readme'],
  'archives': ['zip', 'tar', 'gz', 'rar']
};

/**
 * HTTP status codes
 */
const STATUS_CODES = [
  { code: '200', description: 'OK', color: 'green' },
  { code: '201', description: 'Created', color: 'green' },
  { code: '204', description: 'No Content', color: 'blue' },
  { code: '301', description: 'Moved Permanently', color: 'yellow' },
  { code: '302', description: 'Found', color: 'yellow' },
  { code: '307', description: 'Temporary Redirect', color: 'yellow' },
  { code: '401', description: 'Unauthorized', color: 'orange' },
  { code: '403', description: 'Forbidden', color: 'orange' },
  { code: '404', description: 'Not Found', color: 'red' },
  { code: '500', description: 'Internal Server Error', color: 'red' },
  { code: '503', description: 'Service Unavailable', color: 'red' }
];

/**
 * Dirb Scanner Component
 */
export function DirbScanner() {
  // State management
  const [config, setConfig] = React.useState<DirbConfig>({
    url: '',
    wordlist: 'common',
    customWordlist: '',
    extensions: [],
    speed: 'normal',
    recursive: false,
    ignoreCase: false,
    followRedirects: true,
    includeStatusCodes: ['200', '204', '301', '302', '307', '401', '403'],
    excludeStatusCodes: ['404'],
    authentication: {
      enabled: false,
      username: '',
      password: '',
      type: 'basic'
    },
    proxy: {
      enabled: false,
      url: '',
      credentials: ''
    },
    headers: {},
    cookies: '',
    userAgent: '',
    timeout: 30,
    maxRedirects: 5,
    outputFile: '',
    silent: false,
    generateTree: false,
    showNotFound: false,
    customArgs: ''
  });
  
  const [isScanning, setIsScanning] = React.useState(false);
  const [currentExecution, setCurrentExecution] = React.useState<ToolExecution | null>(null);
  const [progress, setProgress] = React.useState(0);
  const [output, setOutput] = React.useState<string[]>([]);
  const [results, setResults] = React.useState<any>(null);
  const [activeTab, setActiveTab] = React.useState('configure');
  const [scanStats, setScanStats] = React.useState<any>(null);
  const [newHeaderKey, setNewHeaderKey] = React.useState('');
  const [newHeaderValue, setNewHeaderValue] = React.useState('');
  
  // Backend connection
  const { status: backendStatus } = useBackendStore();
  
  // Update configuration
  const updateConfig = (updates: Partial<DirbConfig>) => {
    setConfig(prev => ({ ...prev, ...updates }));
  };
  
  // Update authentication
  const updateAuth = (updates: Partial<DirbConfig['authentication']>) => {
    setConfig(prev => ({
      ...prev,
      authentication: { ...prev.authentication, ...updates }
    }));
  };
  
  // Update proxy
  const updateProxy = (updates: Partial<DirbConfig['proxy']>) => {
    setConfig(prev => ({
      ...prev,
      proxy: { ...prev.proxy, ...updates }
    }));
  };
  
  // Apply extension preset
  const applyExtensionPreset = (presetKey: string) => {
    const preset = EXTENSION_PRESETS[presetKey as keyof typeof EXTENSION_PRESETS];
    if (preset) {
      updateConfig({ extensions: preset });
      toast.success(`Applied ${presetKey} extensions`);
    }
  };
  
  // Toggle status code
  const toggleStatusCode = (code: string, isInclude: boolean) => {
    if (isInclude) {
      if (config.includeStatusCodes.includes(code)) {
        updateConfig({
          includeStatusCodes: config.includeStatusCodes.filter(c => c !== code)
        });
      } else {
        updateConfig({
          includeStatusCodes: [...config.includeStatusCodes, code]
        });
      }
    } else {
      if (config.excludeStatusCodes.includes(code)) {
        updateConfig({
          excludeStatusCodes: config.excludeStatusCodes.filter(c => c !== code)
        });
      } else {
        updateConfig({
          excludeStatusCodes: [...config.excludeStatusCodes, code]
        });
      }
    }
  };
  
  // Add custom header
  const addHeader = () => {
    if (newHeaderKey.trim() && newHeaderValue.trim()) {
      updateConfig({
        headers: {
          ...config.headers,
          [newHeaderKey.trim()]: newHeaderValue.trim()
        }
      });
      setNewHeaderKey('');
      setNewHeaderValue('');
      toast.success('Header added');
    }
  };
  
  // Remove header
  const removeHeader = (key: string) => {
    const { [key]: removed, ...remainingHeaders } = config.headers;
    updateConfig({ headers: remainingHeaders });
    toast.success('Header removed');
  };
  
  // Generate Dirb command
  const generateCommand = (): string => {
    let command = 'dirb';
    
    // URL
    command += ` ${config.url}`;
    
    // Wordlist
    const wordlistPath = config.customWordlist.trim() || WORDLISTS[config.wordlist as keyof typeof WORDLISTS];
    command += ` ${wordlistPath}`;
    
    // Options
    
    // Speed/Delay
    const speedConfig = SPEED_CONFIGS[config.speed];
    if (speedConfig.delay > 0) {
      command += ` -z ${speedConfig.delay}`;
    }
    
    // Extensions
    if (config.extensions.length > 0) {
      command += ` -X .${config.extensions.join(',.')}`;
    }
    
    // Recursive
    if (config.recursive) {
      command += ` -r`;
    }
    
    // Case sensitivity
    if (config.ignoreCase) {
      command += ` -i`;
    }
    
    // Follow redirects
    if (!config.followRedirects) {
      command += ` -R`;
    }
    
    // Status codes
    if (config.includeStatusCodes.length > 0) {
      command += ` -N ${config.includeStatusCodes.join(',')}`;
    }
    
    // Authentication
    if (config.authentication.enabled && config.authentication.username) {
      command += ` -u ${config.authentication.username}:${config.authentication.password}`;
      if (config.authentication.type === 'ntlm') {
        command += ` -w`;
      }
    }
    
    // Proxy
    if (config.proxy.enabled && config.proxy.url) {
      command += ` -p ${config.proxy.url}`;
      if (config.proxy.credentials) {
        command += ` -P ${config.proxy.credentials}`;
      }
    }
    
    // Headers
    Object.entries(config.headers).forEach(([key, value]) => {
      command += ` -H "${key}: ${value}"`;
    });
    
    // Cookies
    if (config.cookies.trim()) {
      command += ` -c "${config.cookies}"`;
    }
    
    // User Agent
    if (config.userAgent.trim()) {
      command += ` -a "${config.userAgent}"`;
    }
    
    // Timeout
    if (config.timeout !== 30) {
      command += ` -t ${config.timeout}`;
    }
    
    // Output file
    if (config.outputFile.trim()) {
      command += ` -o ${config.outputFile}`;
    }
    
    // Silent mode
    if (config.silent) {
      command += ` -S`;
    }
    
    // Generate tree
    if (config.generateTree) {
      command += ` -g`;
    }
    
    // Show not found
    if (config.showNotFound) {
      command += ` -v`;
    }
    
    // Custom arguments
    if (config.customArgs.trim()) {
      command += ` ${config.customArgs.trim()}`;
    }
    
    return command;
  };
  
  // Start scan
  const startScan = async () => {
    if (!config.url.trim()) {
      toast.error('Please specify a target URL');
      return;
    }
    
    if (!backendStatus.connected) {
      toast.error('Backend not connected');
      return;
    }
    
    try {
      setIsScanning(true);
      setProgress(0);
      setOutput([]);
      setResults(null);
      setScanStats(null);
      setActiveTab('output');
      
      const execution = await apiClient.executeTool('dirb', config, (progressUpdate) => {
        setCurrentExecution(progressUpdate);
        setProgress(progressUpdate.progress);
        if (progressUpdate.output) {
          setOutput(prev => [...prev, ...progressUpdate.output!]);
        }
        
        if (progressUpdate.status === 'completed') {
          setIsScanning(false);
          setResults(progressUpdate.results);
          
          // Parse scan statistics
          if (progressUpdate.results?.findings) {
            const stats = {
              totalFound: progressUpdate.results.findings.length,
              statusCodes: progressUpdate.results.findings.reduce((acc: any, finding: any) => {
                const code = finding.status || '200';
                acc[code] = (acc[code] || 0) + 1;
                return acc;
              }, {}),
              totalTested: progressUpdate.results.totalTested || 0,
              duration: progressUpdate.results.duration || 0
            };
            setScanStats(stats);
          }
          
          toast.success('Dirb scan completed');
        } else if (progressUpdate.status === 'failed') {
          setIsScanning(false);
          toast.error(`Scan failed: ${progressUpdate.error}`);
        }
      });
      
      setCurrentExecution(execution);
      toast.info('Dirb scan started');
    } catch (error) {
      setIsScanning(false);
      console.error('Failed to start Dirb scan:', error);
      toast.error('Failed to start scan');
    }
  };
  
  // Stop scan
  const stopScan = async () => {
    if (currentExecution) {
      try {
        await apiClient.cancelExecution(currentExecution.id);
        setIsScanning(false);
        setCurrentExecution(null);
        toast.info('Scan cancelled');
      } catch (error) {
        console.error('Failed to stop scan:', error);
        toast.error('Failed to stop scan');
      }
    }
  };
  
  // Copy command to clipboard
  const copyCommand = async () => {
    try {
      await navigator.clipboard.writeText(generateCommand());
      toast.success('Command copied to clipboard');
    } catch (error) {
      toast.error('Failed to copy command');
    }
  };
  
  // Export results
  const exportResults = () => {
    if (!results && output.length === 0) {
      toast.error('No results to export');
      return;
    }
    
    const data = results || output.join('
');
    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `dirb-scan-${new Date().toISOString().slice(0, 19)}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    
    toast.success('Results exported');
  };
  
  // Get status code color
  const getStatusColor = (code: string) => {
    const status = STATUS_CODES.find(s => s.code === code);
    return status?.color || 'gray';
  };
  
  return (
    <div className="container-desktop py-6 space-y-6">
      {/* Tool Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className="p-2 bg-teal-100 dark:bg-teal-900/20 rounded-lg">
            <FolderOpen className="h-6 w-6 text-teal-600 dark:text-teal-400" />
          </div>
          <div>
            <h1 className="text-2xl font-bold">Dirb Scanner</h1>
            <p className="text-muted-foreground">
              Web content scanner for discovering hidden directories and files
            </p>
          </div>
        </div>
        
        {/* Status indicators */}
        <div className="flex items-center gap-2">
          <Badge variant={backendStatus.connected ? 'default' : 'destructive'}>
            {backendStatus.connected ? 'Connected' : 'Offline'}
          </Badge>
          {isScanning && (
            <Badge variant="secondary" className="animate-pulse">
              Scanning...
            </Badge>
          )}
        </div>
      </div>
      
      {/* Main Interface */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="configure" className="flex items-center gap-2">
            <Settings className="h-4 w-4" />
            Configure
          </TabsTrigger>
          <TabsTrigger value="output" className="flex items-center gap-2">
            <Terminal className="h-4 w-4" />
            Output
          </TabsTrigger>
          <TabsTrigger value="results" className="flex items-center gap-2">
            <FileText className="h-4 w-4" />
            Results
          </TabsTrigger>
          <TabsTrigger value="command" className="flex items-center gap-2">
            <Target className="h-4 w-4" />
            Command
          </TabsTrigger>
        </TabsList>
        
        {/* Configuration Tab */}
        <TabsContent value="configure" className="space-y-6">
          <div className="grid gap-6 lg:grid-cols-2">
            {/* Target Configuration */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Target className="h-5 w-5" />
                  Target Configuration
                </CardTitle>
                <CardDescription>
                  Specify the target URL and scanning parameters
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* URL */}
                <div className="space-y-2">
                  <label className="text-sm font-medium">Target URL</label>
                  <Input
                    placeholder="e.g., https://example.com"
                    value={config.url}
                    onChange={(e) => updateConfig({ url: e.target.value })}
                  />
                  <p className="text-xs text-muted-foreground">
                    Full URL including protocol (http/https)
                  </p>
                </div>
                
                {/* Wordlist */}
                <div className="space-y-2">
                  <label className="text-sm font-medium">Wordlist</label>
                  <Select 
                    value={config.wordlist} 
                    onValueChange={(value) => updateConfig({ wordlist: value })}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {Object.entries(WORDLISTS).map(([key, path]) => (
                        <SelectItem key={key} value={key}>
                          <div className="flex flex-col">
                            <span className="capitalize">{key.replace(/_/g, ' ')}</span>
                            <span className="text-xs text-muted-foreground">
                              {path}
                            </span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                
                {/* Custom Wordlist */}
                <div className="space-y-2">
                  <label className="text-sm font-medium">Custom Wordlist (Optional)</label>
                  <Input
                    placeholder="/path/to/custom/wordlist.txt"
                    value={config.customWordlist}
                    onChange={(e) => updateConfig({ customWordlist: e.target.value })}
                  />
                  <p className="text-xs text-muted-foreground">
                    Override default wordlist with custom file path
                  </p>
                </div>
                
                {/* Extensions */}
                <div className="space-y-2">
                  <label className="text-sm font-medium">File Extensions (Optional)</label>
                  <div className="space-y-2">
                    <Textarea
                      placeholder="php,asp,html,txt"
                      value={config.extensions.join(',')}
                      onChange={(e) => updateConfig({ 
                        extensions: e.target.value.split(',').map(ext => ext.trim()).filter(Boolean)
                      })}
                      rows={2}
                    />
                    <div className="flex flex-wrap gap-2">
                      {Object.keys(EXTENSION_PRESETS).map((preset) => (
                        <Button
                          key={preset}
                          variant="outline"
                          size="sm"
                          onClick={() => applyExtensionPreset(preset)}
                        >
                          {preset}
                        </Button>
                      ))}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            {/* Scan Options */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <TrendingUp className="h-5 w-5" />
                  Scan Options
                </CardTitle>
                <CardDescription>
                  Configure scanning behavior and performance
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Speed */}
                <div className="space-y-2">
                  <label className="text-sm font-medium">Scan Speed</label>
                  <Select 
                    value={config.speed} 
                    onValueChange={(value) => updateConfig({ speed: value as any })}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {Object.entries(SPEED_CONFIGS).map(([key, speedConfig]) => (
                        <SelectItem key={key} value={key}>
                          <div className="flex flex-col">
                            <span className="capitalize">{key}</span>
                            <span className="text-xs text-muted-foreground">
                              {speedConfig.description}
                            </span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="grid gap-4 md:grid-cols-2">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Timeout (seconds)</label>
                    <Input
                      type="number"
                      min="1"
                      max="300"
                      value={config.timeout}
                      onChange={(e) => updateConfig({ timeout: parseInt(e.target.value) || 30 })}
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Max Redirects</label>
                    <Input
                      type="number"
                      min="0"
                      max="20"
                      value={config.maxRedirects}
                      onChange={(e) => updateConfig({ maxRedirects: parseInt(e.target.value) || 5 })}
                    />
                  </div>
                </div>
                
                <div className="space-y-3">
                  <label className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      checked={config.recursive}
                      onChange={(e) => updateConfig({ recursive: e.target.checked })}
                      className="rounded border-gray-300"
                    />
                    <span className="text-sm">Recursive Scanning</span>
                  </label>
                  
                  <label className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      checked={config.ignoreCase}
                      onChange={(e) => updateConfig({ ignoreCase: e.target.checked })}
                      className="rounded border-gray-300"
                    />
                    <span className="text-sm">Case Insensitive</span>
                  </label>
                  
                  <label className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      checked={config.followRedirects}
                      onChange={(e) => updateConfig({ followRedirects: e.target.checked })}
                      className="rounded border-gray-300"
                    />
                    <span className="text-sm">Follow Redirects</span>
                  </label>
                  
                  <label className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      checked={config.generateTree}
                      onChange={(e) => updateConfig({ generateTree: e.target.checked })}
                      className="rounded border-gray-300"
                    />
                    <span className="text-sm">Generate Directory Tree</span>
                  </label>
                  
                  <label className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      checked={config.showNotFound}
                      onChange={(e) => updateConfig({ showNotFound: e.target.checked })}
                      className="rounded border-gray-300"
                    />
                    <span className="text-sm">Show Not Found (404) Results</span>
                  </label>
                  
                  <label className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      checked={config.silent}
                      onChange={(e) => updateConfig({ silent: e.target.checked })}
                      className="rounded border-gray-300"
                    />
                    <span className="text-sm">Silent Mode</span>
                  </label>
                </div>
              </CardContent>
            </Card>
          </div>
          
          {/* Status Code Filtering */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Filter className="h-5 w-5" />
                Status Code Filtering
              </CardTitle>
              <CardDescription>
                Configure which HTTP status codes to include or exclude from results
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-2">
                  <label className="text-sm font-medium">Include Status Codes</label>
                  <div className="grid gap-2 grid-cols-3">
                    {STATUS_CODES.map((status) => (
                      <Button
                        key={status.code}
                        variant={config.includeStatusCodes.includes(status.code) ? 'default' : 'outline'}
                        size="sm"
                        onClick={() => toggleStatusCode(status.code, true)}
                        className="justify-start"
                      >
                        <div className={cn(
                          'w-2 h-2 rounded-full mr-2',
                          status.color === 'green' && 'bg-green-500',
                          status.color === 'blue' && 'bg-blue-500',
                          status.color === 'yellow' && 'bg-yellow-500',
                          status.color === 'orange' && 'bg-orange-500',
                          status.color === 'red' && 'bg-red-500'
                        )} />
                        {status.code}
                      </Button>
                    ))}
                  </div>
                </div>
                
                <div className="space-y-2">
                  <label className="text-sm font-medium">Exclude Status Codes</label>
                  <div className="grid gap-2 grid-cols-3">
                    {STATUS_CODES.map((status) => (
                      <Button
                        key={status.code}
                        variant={config.excludeStatusCodes.includes(status.code) ? 'destructive' : 'outline'}
                        size="sm"
                        onClick={() => toggleStatusCode(status.code, false)}
                        className="justify-start"
                      >
                        <div className={cn(
                          'w-2 h-2 rounded-full mr-2',
                          status.color === 'green' && 'bg-green-500',
                          status.color === 'blue' && 'bg-blue-500',
                          status.color === 'yellow' && 'bg-yellow-500',
                          status.color === 'orange' && 'bg-orange-500',
                          status.color === 'red' && 'bg-red-500'
                        )} />
                        {status.code}
                      </Button>
                    ))}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
          
          {/* HTTP Configuration */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Globe className="h-5 w-5" />
                HTTP Configuration
              </CardTitle>
              <CardDescription>
                Configure HTTP headers, cookies, and request parameters
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-2">
                  <label className="text-sm font-medium">User Agent</label>
                  <Input
                    placeholder="Custom User-Agent string"
                    value={config.userAgent}
                    onChange={(e) => updateConfig({ userAgent: e.target.value })}
                  />
                </div>
                
                <div className="space-y-2">
                  <label className="text-sm font-medium">Cookies</label>
                  <Input
                    placeholder="cookie1=value1; cookie2=value2"
                    value={config.cookies}
                    onChange={(e) => updateConfig({ cookies: e.target.value })}
                  />
                </div>
              </div>
              
              {/* Custom Headers */}
              <div className="space-y-2">
                <label className="text-sm font-medium">Custom Headers</label>
                <div className="space-y-2">
                  {Object.entries(config.headers).map(([key, value]) => (
                    <div key={key} className="flex items-center gap-2 p-2 border rounded">
                      <span className="text-sm font-mono">{key}:</span>
                      <span className="text-sm flex-1">{value}</span>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => removeHeader(key)}
                      >
                        ×
                      </Button>
                    </div>
                  ))}
                  
                  <div className="flex gap-2">
                    <Input
                      placeholder="Header name"
                      value={newHeaderKey}
                      onChange={(e) => setNewHeaderKey(e.target.value)}
                    />
                    <Input
                      placeholder="Header value"
                      value={newHeaderValue}
                      onChange={(e) => setNewHeaderValue(e.target.value)}
                    />
                    <Button onClick={addHeader} disabled={!newHeaderKey.trim() || !newHeaderValue.trim()}>
                      Add
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
          
          {/* Authentication & Proxy */}
          <div className="grid gap-6 lg:grid-cols-2">
            {/* Authentication */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Lock className="h-5 w-5" />
                  Authentication
                </CardTitle>
                <CardDescription>
                  Configure HTTP authentication if required
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    checked={config.authentication.enabled}
                    onChange={(e) => updateAuth({ enabled: e.target.checked })}
                    className="rounded border-gray-300"
                  />
                  <span className="text-sm font-medium">Enable Authentication</span>
                </div>
                
                {config.authentication.enabled && (
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <label className="text-sm font-medium">Type</label>
                      <Select 
                        value={config.authentication.type} 
                        onValueChange={(value) => updateAuth({ type: value as any })}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="basic">Basic Auth</SelectItem>
                          <SelectItem value="ntlm">NTLM Auth</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    
                    <div className="space-y-2">
                      <label className="text-sm font-medium">Username</label>
                      <Input
                        placeholder="Username"
                        value={config.authentication.username}
                        onChange={(e) => updateAuth({ username: e.target.value })}
                      />
                    </div>
                    
                    <div className="space-y-2">
                      <label className="text-sm font-medium">Password</label>
                      <Input
                        type="password"
                        placeholder="Password"
                        value={config.authentication.password}
                        onChange={(e) => updateAuth({ password: e.target.value })}
                      />
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
            
            {/* Proxy */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Server className="h-5 w-5" />
                  Proxy Configuration
                </CardTitle>
                <CardDescription>
                  Configure proxy settings for requests
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    checked={config.proxy.enabled}
                    onChange={(e) => updateProxy({ enabled: e.target.checked })}
                    className="rounded border-gray-300"
                  />
                  <span className="text-sm font-medium">Enable Proxy</span>
                </div>
                
                {config.proxy.enabled && (
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <label className="text-sm font-medium">Proxy URL</label>
                      <Input
                        placeholder="http://proxy:8080"
                        value={config.proxy.url}
                        onChange={(e) => updateProxy({ url: e.target.value })}
                      />
                    </div>
                    
                    <div className="space-y-2">
                      <label className="text-sm font-medium">Credentials (Optional)</label>
                      <Input
                        placeholder="username:password"
                        value={config.proxy.credentials}
                        onChange={(e) => updateProxy({ credentials: e.target.value })}
                      />
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
          
          {/* Output & Advanced */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Output & Advanced Options
              </CardTitle>
              <CardDescription>
                Configure output settings and advanced scan parameters
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-2">
                  <label className="text-sm font-medium">Output File (Optional)</label>
                  <Input
                    placeholder="dirb-results.txt"
                    value={config.outputFile}
                    onChange={(e) => updateConfig({ outputFile: e.target.value })}
                  />
                </div>
                
                <div className="space-y-2">
                  <label className="text-sm font-medium">Custom Arguments</label>
                  <Input
                    placeholder="Additional dirb arguments"
                    value={config.customArgs}
                    onChange={(e) => updateConfig({ customArgs: e.target.value })}
                  />
                </div>
              </div>
            </CardContent>
          </Card>
          
          {/* Actions */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Activity className="h-5 w-5" />
                Start Scan
              </CardTitle>
              <CardDescription>
                Execute the Dirb content discovery scan with your configured parameters
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between">
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={copyCommand}
                  >
                    <Copy className="h-4 w-4 mr-1" />
                    Copy Command
                  </Button>
                </div>
                
                <div className="flex gap-2">
                  {isScanning ? (
                    <Button
                      variant="destructive"
                      onClick={stopScan}
                    >
                      <Square className="h-4 w-4 mr-2" />
                      Stop Scan
                    </Button>
                  ) : (
                    <Button
                      onClick={startScan}
                      disabled={!config.url.trim() || !backendStatus.connected}
                    >
                      <Play className="h-4 w-4 mr-2" />
                      Start Content Discovery
                    </Button>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        {/* Output Tab */}
        <TabsContent value="output" className="space-y-6">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="flex items-center gap-2">
                    <Terminal className="h-5 w-5" />
                    Real-time Output
                  </CardTitle>
                  <CardDescription>
                    Live output from the Dirb content discovery scan
                  </CardDescription>
                </div>
                
                {/* Progress and controls */}
                <div className="flex items-center gap-4">
                  {isScanning && (
                    <div className="flex items-center gap-2">
                      <Progress value={progress} className="w-32" />
                      <span className="text-sm text-muted-foreground">
                        {progress}%
                      </span>
                    </div>
                  )}
                  
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setOutput([])}
                    disabled={isScanning}
                  >
                    <RotateCcw className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="bg-gray-900 text-gray-100 p-4 rounded-lg h-96 overflow-y-auto font-mono text-sm">
                {output.length === 0 ? (
                  <div className="text-gray-500 italic">
                    {isScanning ? 'Waiting for output...' : 'No output yet. Start a scan to see results here.'}
                  </div>
                ) : (
                  output.map((line, index) => (
                    <div key={index} className="mb-1">
                      {line}
                    </div>
                  ))
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        {/* Results Tab */}
        <TabsContent value="results" className="space-y-6">
          {/* Scan Statistics */}
          {scanStats && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BarChart3 className="h-5 w-5" />
                  Scan Statistics
                </CardTitle>
                <CardDescription>
                  Overview of discovered content and scan performance
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid gap-4 md:grid-cols-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-600">{scanStats.totalFound}</div>
                    <div className="text-sm text-muted-foreground">Found</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-600">{scanStats.totalTested}</div>
                    <div className="text-sm text-muted-foreground">Tested</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-purple-600">{Object.keys(scanStats.statusCodes || {}).length}</div>
                    <div className="text-sm text-muted-foreground">Status Types</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-orange-600">{scanStats.duration}s</div>
                    <div className="text-sm text-muted-foreground">Duration</div>
                  </div>
                </div>
                
                {scanStats.statusCodes && (
                  <div className="mt-4">
                    <h4 className="text-sm font-medium mb-2">Status Code Distribution</h4>
                    <div className="flex flex-wrap gap-2">
                      {Object.entries(scanStats.statusCodes).map(([code, count]) => (
                        <Badge 
                          key={code} 
                          variant="outline"
                          className={cn(
                            'flex items-center gap-1',
                            getStatusColor(code) === 'green' && 'border-green-500 text-green-700',
                            getStatusColor(code) === 'blue' && 'border-blue-500 text-blue-700',
                            getStatusColor(code) === 'yellow' && 'border-yellow-500 text-yellow-700',
                            getStatusColor(code) === 'orange' && 'border-orange-500 text-orange-700',
                            getStatusColor(code) === 'red' && 'border-red-500 text-red-700'
                          )}
                        >
                          <div className={cn(
                            'w-2 h-2 rounded-full',
                            getStatusColor(code) === 'green' && 'bg-green-500',
                            getStatusColor(code) === 'blue' && 'bg-blue-500',
                            getStatusColor(code) === 'yellow' && 'bg-yellow-500',
                            getStatusColor(code) === 'orange' && 'bg-orange-500',
                            getStatusColor(code) === 'red' && 'bg-red-500'
                          )} />
                          {code}: {count as number}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          )}
          
          {/* Detailed Results */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="flex items-center gap-2">
                    <FileText className="h-5 w-5" />
                    Scan Results
                  </CardTitle>
                  <CardDescription>
                    Detailed findings from the Dirb content discovery scan
                  </CardDescription>
                </div>
                
                <Button
                  variant="outline"
                  onClick={exportResults}
                  disabled={!results && output.length === 0}
                >
                  <Download className="h-4 w-4 mr-2" />
                  Export Results
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              {results ? (
                <div className="space-y-4">
                  <pre className="bg-muted p-4 rounded-lg overflow-auto text-sm max-h-96">
                    {JSON.stringify(results, null, 2)}
                  </pre>
                </div>
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  <FolderOpen className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>No scan results available yet.</p>
                  <p className="text-sm">Complete a scan to see discovered directories and files here.</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
        
        {/* Command Tab */}
        <TabsContent value="command" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Target className="h-5 w-5" />
                Generated Command
              </CardTitle>
              <CardDescription>
                The Dirb command that will be executed based on your configuration
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="bg-muted p-4 rounded-lg">
                  <code className="text-sm font-mono break-all">
                    {generateCommand()}
                  </code>
                </div>
                
                <div className="flex justify-between items-center">
                  <p className="text-sm text-muted-foreground">
                    This command will be executed on the backend server
                  </p>
                  
                  <Button variant="outline" onClick={copyCommand}>
                    <Copy className="h-4 w-4 mr-2" />
                    Copy Command
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}