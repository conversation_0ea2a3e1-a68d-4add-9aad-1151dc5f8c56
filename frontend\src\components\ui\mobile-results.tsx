/**
 * Mobile-Responsive Results Display Components
 * Provides mobile-optimized layouts for scan results and data tables
 */
import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';

interface MobileResultsGridProps {
  className?: string;
  children: React.ReactNode;
}

interface MobileResultCardProps {
  title: string;
  subtitle?: string;
  status?: 'success' | 'warning' | 'error' | 'info';
  badge?: string;
  className?: string;
  children: React.ReactNode;
}

interface MobileTableProps {
  headers: string[];
  data: Array<Record<string, any>>;
  className?: string;
  onRowClick?: (row: Record<string, any>) => void;
}

interface MobileDataListProps {
  items: Array<{
    label: string;
    value: string | React.ReactNode;
    status?: 'success' | 'warning' | 'error' | 'info';
  }>;
  className?: string;
}

// Mobile-responsive results grid
export function MobileResultsGrid({ className, children }: MobileResultsGridProps) {
  return (
    <div className={cn(
      // Mobile: Single column with spacing
      "space-y-4",
      // Desktop: Grid layout
      "md:grid md:grid-cols-1 lg:grid-cols-2 md:gap-4 md:space-y-0",
      className
    )}>
      {children}
    </div>
  );
}

// Mobile-optimized result card
export function MobileResultCard({ title, subtitle, status, badge, className, children }: MobileResultCardProps) {
  const getStatusColor = (status?: string) => {
    switch (status) {
      case 'success': return 'border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-900/10';
      case 'warning': return 'border-yellow-200 bg-yellow-50 dark:border-yellow-800 dark:bg-yellow-900/10';
      case 'error': return 'border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-900/10';
      case 'info': return 'border-blue-200 bg-blue-50 dark:border-blue-800 dark:bg-blue-900/10';
      default: return '';
    }
  };

  return (
    <Card className={cn(
      "tool-card",
      status && getStatusColor(status),
      className
    )}>
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex-1 min-w-0">
            <CardTitle className="text-base font-medium truncate">
              {title}
            </CardTitle>
            {subtitle && (
              <CardDescription className="text-sm mt-1">
                {subtitle}
              </CardDescription>
            )}
          </div>
          {badge && (
            <Badge variant="outline" className="ml-2 flex-shrink-0">
              {badge}
            </Badge>
          )}
        </div>
      </CardHeader>
      <CardContent className="pt-0">
        {children}
      </CardContent>
    </Card>
  );
}

// Mobile-responsive table
export function MobileTable({ headers, data, className, onRowClick }: MobileTableProps) {
  return (
    <div className={cn("space-y-2", className)}>
      {/* Mobile: Card-based layout */}
      <div className="block md:hidden space-y-3">
        {data.map((row, index) => (
          <Card 
            key={index} 
            className={cn(
              "tool-card cursor-pointer hover:border-primary/50",
              onRowClick && "cursor-pointer"
            )}
            onClick={() => onRowClick?.(row)}
          >
            <CardContent className="p-4 space-y-2">
              {headers.map((header, headerIndex) => (
                <div key={headerIndex} className="flex justify-between items-center">
                  <span className="text-sm font-medium text-muted-foreground">
                    {header}
                  </span>
                  <span className="text-sm font-mono">
                    {row[header.toLowerCase().replace(/\s+/g, '_')] || '-'}
                  </span>
                </div>
              ))}
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Desktop: Traditional table */}
      <div className="hidden md:block">
        <div className="overflow-x-auto">
          <table className="data-table w-full">
            <thead>
              <tr>
                {headers.map((header, index) => (
                  <th key={index} className="text-left">
                    {header}
                  </th>
                ))}
              </tr>
            </thead>
            <tbody>
              {data.map((row, index) => (
                <tr 
                  key={index}
                  className={onRowClick ? "cursor-pointer hover:bg-muted/40" : ""}
                  onClick={() => onRowClick?.(row)}
                >
                  {headers.map((header, headerIndex) => (
                    <td key={headerIndex} className="font-mono text-sm">
                      {row[header.toLowerCase().replace(/\s+/g, '_')] || '-'}
                    </td>
                  ))}
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}

// Mobile-optimized data list
export function MobileDataList({ items, className }: MobileDataListProps) {
  const getStatusColor = (status?: string) => {
    switch (status) {
      case 'success': return 'text-green-600 dark:text-green-400';
      case 'warning': return 'text-yellow-600 dark:text-yellow-400';
      case 'error': return 'text-red-600 dark:text-red-400';
      case 'info': return 'text-blue-600 dark:text-blue-400';
      default: return '';
    }
  };

  return (
    <div className={cn("space-y-3", className)}>
      {items.map((item, index) => (
        <div key={index} className="flex justify-between items-start gap-4 py-2 border-b border-border last:border-0">
          <span className="text-sm font-medium text-muted-foreground flex-shrink-0">
            {item.label}
          </span>
          <div className={cn(
            "text-sm font-mono text-right",
            item.status && getStatusColor(item.status)
          )}>
            {item.value}
          </div>
        </div>
      ))}
    </div>
  );
}

// Mobile-responsive action bar
export function MobileActionBar({ className, children }: { className?: string; children: React.ReactNode }) {
  return (
    <div className={cn(
      // Mobile: Stacked buttons with full width
      "flex flex-col gap-2",
      // Desktop: Horizontal layout
      "md:flex-row md:items-center md:justify-between",
      className
    )}>
      {children}
    </div>
  );
}
