#!/usr/bin/env python3
"""
Feroxbuster Wrapper for NexusScan Desktop
Fast content discovery tool integration
"""

import asyncio
import logging
from typing import Dict, List, Optional, Callable, Any
from datetime import datetime

from security.tools.tool_registry import (
    ToolMetadata, ToolCategory, ToolCapabilities,
    ScanOptions, ScanResult, ToolStatus, register_tool
)
from security.tools.external_tool_wrapper import ExternalToolWrapper

logger = logging.getLogger(__name__)


@register_tool
class FeroxbusterWrapper(ExternalToolWrapper):
    """Feroxbuster content discovery tool wrapper"""
    
    def __init__(self):
        super().__init__()
        self.tool_executable = "feroxbuster"
    
    def get_metadata(self) -> ToolMetadata:
        return ToolMetadata(
            name="feroxbuster",
            display_name="Feroxbuster",
            description="Fast, simple, recursive content discovery tool written in Rust",
            version="1.0.0",
            category=ToolCategory.WEB_SCANNER,
            capabilities=ToolCapabilities(
                supports_async=True,
                network_access_required=True,
                supported_targets=["url"],
                requires_root=False
            ),
            default_options={
                "wordlist": "/usr/share/wordlists/dirb/common.txt",
                "threads": 50,
                "depth": 4,
                "timeout": 7,
                "status_codes": [200, 204, 301, 302, 307, 308, 401, 403, 405],
                "extensions": [],
                "follow_redirects": False,
                "insecure": False,
                "no_recursion": False,
                "extract_links": True,
                "scan_limit": 0,  # 0 = unlimited
                "rate_limit": 0,  # 0 = unlimited
                "filter_status": [],
                "filter_size": [],
                "filter_words": [],
                "filter_lines": [],
                "user_agent": "feroxbuster"
            }
        )
    
    def get_tool_command(self, options: ScanOptions) -> List[str]:
        """Get Feroxbuster command"""
        wordlist = options.custom_options.get("wordlist", "/usr/share/wordlists/dirb/common.txt")
        threads = options.custom_options.get("threads", 50)
        depth = options.custom_options.get("depth", 4)
        timeout = options.custom_options.get("timeout", 7)
        
        command = [
            "feroxbuster",
            "-u", options.target,
            "-w", wordlist,
            "-t", str(threads),
            "-d", str(depth),
            "-T", str(timeout),
            "-v",  # Verbose output
            "--no-state"  # Don't save state file
        ]
        
        # Add status codes
        status_codes = options.custom_options.get("status_codes", [200, 204, 301, 302, 307, 308, 401, 403, 405])
        if status_codes:
            command.extend(["-s", ",".join(map(str, status_codes))])
        
        # Add extensions
        extensions = options.custom_options.get("extensions", [])
        if extensions:
            command.extend(["-x", ",".join(extensions)])
        
        # Add flags
        if options.custom_options.get("follow_redirects", False):
            command.append("-r")
        
        if options.custom_options.get("insecure", False):
            command.append("-k")
        
        if options.custom_options.get("no_recursion", False):
            command.append("-n")
        
        if options.custom_options.get("extract_links", True):
            command.append("-e")
        
        # Add scan limit
        scan_limit = options.custom_options.get("scan_limit", 0)
        if scan_limit > 0:
            command.extend(["-L", str(scan_limit)])
        
        # Add rate limit
        rate_limit = options.custom_options.get("rate_limit", 0)
        if rate_limit > 0:
            command.extend(["-R", str(rate_limit)])
        
        # Add filters
        filter_status = options.custom_options.get("filter_status", [])
        if filter_status:
            command.extend(["-C", ",".join(map(str, filter_status))])
        
        filter_size = options.custom_options.get("filter_size", [])
        if filter_size:
            command.extend(["-S", ",".join(map(str, filter_size))])
        
        filter_words = options.custom_options.get("filter_words", [])
        if filter_words:
            command.extend(["-W", ",".join(map(str, filter_words))])
        
        filter_lines = options.custom_options.get("filter_lines", [])
        if filter_lines:
            command.extend(["-N", ",".join(map(str, filter_lines))])
        
        # Add user agent
        user_agent = options.custom_options.get("user_agent", "feroxbuster")
        command.extend(["-a", user_agent])
        
        return command
    
    def parse_tool_output(self, output: str, target: str) -> Dict[str, Any]:
        """Parse Feroxbuster output"""
        lines = output.strip().split('\n')
        found_items = []
        extracted_links = []
        statistics = {
            "requests": 0,
            "errors": 0,
            "timeouts": 0,
            "rate": 0,
            "duration": 0
        }
        
        for line in lines:
            # Parse found items (format: status_code size_bytes url)
            if line.strip() and line[0].isdigit() and "http" in line:
                parts = line.strip().split()
                if len(parts) >= 3:
                    try:
                        status_code = int(parts[0])
                        size = parts[1]
                        url = parts[2]
                        
                        # Determine type based on URL
                        item_type = "directory" if url.endswith('/') else "file"
                        
                        found_items.append({
                            "url": url,
                            "path": url.replace(target.rstrip('/'), ''),
                            "status": status_code,
                            "size": size,
                            "type": item_type,
                            "response_time": None
                        })
                    except:
                        pass
            
            # Parse extracted links
            elif line.startswith("EXT") and "=>" in line:
                parts = line.split("=>")
                if len(parts) == 2:
                    extracted_url = parts[1].strip()
                    extracted_links.append({
                        "url": extracted_url,
                        "source": parts[0].replace("EXT", "").strip()
                    })
            
            # Parse statistics
            elif "requests" in line.lower() and ":" in line:
                parts = line.split(":")
                if len(parts) == 2:
                    try:
                        statistics["requests"] = int(parts[1].strip().split()[0])
                    except:
                        pass
            elif "errors" in line.lower() and ":" in line:
                parts = line.split(":")
                if len(parts) == 2:
                    try:
                        statistics["errors"] = int(parts[1].strip().split()[0])
                    except:
                        pass
            elif "req/s" in line.lower():
                parts = line.split()
                for i, part in enumerate(parts):
                    if part == "req/s" and i > 0:
                        try:
                            statistics["rate"] = float(parts[i-1])
                        except:
                            pass
        
        # Sort items by status code and path
        found_items.sort(key=lambda x: (x["status"], x["path"]))
        
        # Create vulnerability findings
        vulnerabilities = []
        
        # Check for sensitive files/directories
        sensitive_patterns = [
            "backup", "admin", "config", ".git", ".env", "wp-admin", "phpmyadmin",
            "database", "db", "sql", "api", "private", "secret", "key", "token"
        ]
        
        for item in found_items:
            path_lower = item["path"].lower()
            is_sensitive = any(pattern in path_lower for pattern in sensitive_patterns)
            
            if item["status"] in [200, 301, 302] and is_sensitive:
                vulnerabilities.append({
                    "name": f"Sensitive {item['type']} exposed: {item['path']}",
                    "severity": "high" if "config" in path_lower or ".env" in path_lower else "medium",
                    "description": f"Found potentially sensitive {item['type']} at {item['url']} with status {item['status']}",
                    "type": "information_disclosure",
                    "cve": None,
                    "remediation": "Restrict access to sensitive resources and review server configuration"
                })
            elif item["status"] == 200 and item["type"] == "directory":
                vulnerabilities.append({
                    "name": f"Directory listing enabled: {item['path']}",
                    "severity": "medium",
                    "description": f"Directory listing is enabled at {item['url']}",
                    "type": "information_disclosure",
                    "cve": None,
                    "remediation": "Disable directory listing in server configuration"
                })
        
        return {
            "target": target,
            "items_found": found_items,
            "extracted_links": extracted_links,
            "total_found": len(found_items),
            "total_links": len(extracted_links),
            "statistics": statistics,
            "vulnerabilities": vulnerabilities,
            "summary": {
                "directories": len([i for i in found_items if i["type"] == "directory"]),
                "files": len([i for i in found_items if i["type"] == "file"]),
                "status_200": len([i for i in found_items if i["status"] == 200]),
                "status_301_302": len([i for i in found_items if i["status"] in [301, 302]]),
                "status_401_403": len([i for i in found_items if i["status"] in [401, 403]]),
                "sensitive_found": len(vulnerabilities)
            }
        }
    
    async def execute_simulation(self, options: ScanOptions,
                                 progress_callback: Optional[Callable[[float, str], None]] = None) -> ScanResult:
        """Execute simulated scan for Railway/testing"""
        start_time = datetime.now().isoformat()
        
        if progress_callback:
            await progress_callback(0.1, "Starting content discovery...")
        
        await asyncio.sleep(1)
        
        if progress_callback:
            await progress_callback(0.3, "Scanning directories recursively...")
        
        await asyncio.sleep(1)
        
        if progress_callback:
            await progress_callback(0.6, "Extracting links from responses...")
        
        await asyncio.sleep(1)
        
        # Simulated output
        simulated_output = f"""
 ___  ___  __   __     __      __         __   ___
|__  |__  |__) |__) | /  `    /  \ \_/ | |  \ |__
|    |___ |  \ |  \ | \__,    \__/ / \ | |__/ |___
by Ben "epi" Risher 🤓                 ver: 2.10.2
───────────────────────────┬──────────────────────
 🎯  Target Url            │ {options.target}
 🚀  Threads               │ 50
 📖  Wordlist              │ /usr/share/wordlists/dirb/common.txt
 👌  Status Codes          │ [200, 204, 301, 302, 307, 308, 401, 403, 405]
 💥  Timeout (secs)        │ 7
 🦡  User-Agent            │ feroxbuster
 💉  Config File           │ /etc/feroxbuster/ferox-config.toml
 🔎  Extract Links         │ true
 🏁  HTTP methods          │ [GET]
 🔃  Recursion Depth       │ 4
───────────────────────────┴──────────────────────
 🏁  Press [ENTER] to use the Scan Management Menu™
──────────────────────────────────────────────────
301      9l       28w      313c {options.target}/admin => {options.target}/admin/
301      9l       28w      313c {options.target}/api => {options.target}/api/
301      9l       28w      313c {options.target}/assets => {options.target}/assets/
403      9l       28w      276c {options.target}/backup
403      9l       28w      276c {options.target}/.htaccess
403      9l       28w      276c {options.target}/.htpasswd
301      9l       28w      313c {options.target}/css => {options.target}/css/
200     85l      256w     3456c {options.target}/index.html
301      9l       28w      313c {options.target}/images => {options.target}/images/
301      9l       28w      313c {options.target}/js => {options.target}/js/
200    123l      456w     5678c {options.target}/login
200      4l       12w       48c {options.target}/robots.txt
301      9l       28w      313c {options.target}/uploads => {options.target}/uploads/
403      9l       28w      276c {options.target}/config.php
403      9l       28w      276c {options.target}/.env
301      9l       28w      313c {options.target}/api/v1 => {options.target}/api/v1/
301      9l       28w      313c {options.target}/api/v2 => {options.target}/api/v2/
200     45l      123w     1234c {options.target}/api/docs
EXT {options.target}/index.html => {options.target}/css/style.css
EXT {options.target}/index.html => {options.target}/js/main.js
EXT {options.target}/login => {options.target}/api/auth
[####################] - 4m     20000/20000   0s      found:18      errors:0      
[####################] - 4m      4000/4000    17/s    {options.target}/ 
[####################] - 3m      4000/4000    22/s    {options.target}/admin/ 
[####################] - 3m      4000/4000    22/s    {options.target}/api/ 
[####################] - 2m      4000/4000    33/s    {options.target}/api/v1/"""
        
        parsed_results = self.parse_tool_output(simulated_output, options.target)
        
        if progress_callback:
            await progress_callback(1.0, "Content discovery complete")
        
        return ScanResult(
            tool_name=self.metadata.name,
            target=options.target,
            status=ToolStatus.COMPLETED,
            start_time=start_time,
            end_time=datetime.now().isoformat(),
            duration_seconds=(datetime.now() - datetime.fromisoformat(start_time)).total_seconds(),
            raw_output=simulated_output,
            parsed_results=parsed_results,
            vulnerabilities=parsed_results.get("vulnerabilities", []),
            metadata={
                "wordlist_used": options.custom_options.get("wordlist", "/usr/share/wordlists/dirb/common.txt"),
                "items_found": parsed_results.get("total_found", 0),
                "links_extracted": parsed_results.get("total_links", 0),
                "recursion_depth": options.custom_options.get("depth", 4)
            }
        )