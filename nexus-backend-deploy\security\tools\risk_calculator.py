"""
Risk Calculator for NexusScan Desktop Application
AI-powered risk assessment and calculation engine.
"""

import json
import asyncio
import logging
import math
from datetime import datetime
from typing import Dict, List, Optional, Callable, Any, Tuple
from dataclasses import dataclass
from enum import Enum

from security.tools.tool_registry import (
    ToolMetadata, ToolCategory, ToolCapabilities,
    ScanOptions, ScanResult, ToolStatus, register_tool
)
from security.tools.base_scanner import BaseScanner
from ai.services import AIServiceManager, AIProvider

logger = logging.getLogger(__name__)


class RiskLevel(Enum):
    """Risk level enumeration"""
    VERY_LOW = "very_low"
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    VERY_HIGH = "very_high"
    CRITICAL = "critical"


class ImpactLevel(Enum):
    """Impact level enumeration"""
    MINIMAL = "minimal"
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    VERY_HIGH = "very_high"
    CATASTROPHIC = "catastrophic"


class LikelihoodLevel(Enum):
    """Likelihood level enumeration"""
    VERY_LOW = "very_low"
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    VERY_HIGH = "very_high"


@dataclass
class RiskCalculationOptions(ScanOptions):
    """Risk calculation options"""
    methodology: str = "qualitative"  # qualitative, quantitative, hybrid
    risk_framework: str = "nist"  # nist, iso27001, fair, custom
    include_financial_impact: bool = True
    include_compliance_impact: bool = True
    include_reputation_impact: bool = True
    include_operational_impact: bool = True
    business_context: str = ""
    asset_values: Dict[str, float] = None
    threat_frequencies: Dict[str, float] = None
    
    def __post_init__(self):
        super().__post_init__()
        if self.asset_values is None:
            self.asset_values = {}
        if self.threat_frequencies is None:
            self.threat_frequencies = {}


@dataclass
class RiskFactor:
    """Risk factor definition"""
    id: str
    name: str
    category: str
    description: str
    weight: float
    value: float
    confidence: float
    source: str


@dataclass
class RiskScore:
    """Risk score calculation result"""
    overall_score: float
    risk_level: RiskLevel
    impact_score: float
    likelihood_score: float
    confidence_score: float
    factors: List[RiskFactor]
    methodology: str


class RiskCalculator(BaseScanner):
    """AI-powered risk assessment and calculation engine"""
    
    def __init__(self):
        super().__init__()
        self.tool_name = "risk_calculator"
        self.scan_types = ["qualitative", "quantitative", "hybrid"]
        self.ai_manager = None
        
    def get_metadata(self) -> ToolMetadata:
        """Get tool metadata"""
        return ToolMetadata(
            name="Risk Calculator",
            category=ToolCategory.AI_ANALYZER,
            description="AI-powered risk assessment and calculation engine",
            version="1.0.0",
            author="NexusScan AI Team",
            dependencies=["ai_services"],
            capabilities=ToolCapabilities(
                scan_types=self.scan_types,
                output_formats=["json", "html", "pdf"],
                ai_powered=True,
                real_time_analysis=True,
                risk_assessment=True
            )
        )
    
    def is_available(self) -> bool:
        """Check if AI services are available"""
        try:
            from ai.services import AIServiceManager
            return True
        except ImportError:
            return False
    
    async def _get_ai_manager(self) -> AIServiceManager:
        """Get AI service manager"""
        if self.ai_manager is None:
            from ai.services import AIServiceManager
            self.ai_manager = AIServiceManager()
        return self.ai_manager
    
    async def scan(self, 
                   target: str, 
                   options: Optional[RiskCalculationOptions] = None,
                   progress_callback: Optional[Callable] = None) -> ScanResult:
        """Perform risk calculation and assessment"""
        if options is None:
            options = RiskCalculationOptions(target=target)
        
        scan_id = f"risk_calc_{target.replace('://', '_').replace('/', '_')}"
    
    def check_native_availability(self) -> bool:
        """Check if native tool is available"""
        return self.is_available()"
    
    async def execute_native_scan(self, options: ScanOptions,
                                 progress_callback: Optional[Callable[[float, str], None]] = None) -> ScanResult:}"
        """Execute native scan""}"
        if not isinstance(options, RiskCalculationOptions):
            options = RiskCalculationOptions(target=options.target)
        
        return await self.scan(options.target, options, progress_callback)"
        start_time = datetime.now()
        
        logger.info(f"Starting risk calculation: {scan_id}")
        
        if progress_callback:
            await progress_callback(0.1, "Initializing", "Preparing risk assessment")
        
        try:
            ai_manager = await self._get_ai_manager()
            
            # Phase 1: Risk factor identification
            if progress_callback:
                await progress_callback(0.2, "Factor Analysis", "Identifying risk factors")
            
            risk_factors = await self._identify_risk_factors(target, options, ai_manager)
            
            # Phase 2: Impact assessment
            if progress_callback:
                await progress_callback(0.4, "Impact Assessment", "Assessing potential impacts")
            
            impact_assessment = await self._assess_impact(target, risk_factors, options, ai_manager)
            
            # Phase 3: Likelihood assessment
            if progress_callback:
                await progress_callback(0.6, "Likelihood Assessment", "Evaluating threat likelihood")
            
            likelihood_assessment = await self._assess_likelihood(target, risk_factors, options, ai_manager)
            
            # Phase 4: Risk score calculation
            if progress_callback:
                await progress_callback(0.8, "Risk Calculation", "Calculating risk scores")
            
            risk_scores = await self._calculate_risk_scores(
                target, risk_factors, impact_assessment, likelihood_assessment, options, ai_manager
            )
            
            # Phase 5: Risk prioritization and recommendations
            if progress_callback:
                await progress_callback(0.9, "Risk Prioritization", "Prioritizing risks and generating recommendations")
            
            risk_prioritization = await self._prioritize_risks(risk_scores, options, ai_manager)
            
            # Phase 6: Generate comprehensive risk assessment
            if progress_callback:
                await progress_callback(0.95, "Report Generation", "Generating risk assessment report")
            
            risk_assessment = await self._generate_risk_assessment(
                target, risk_factors, impact_assessment, likelihood_assessment, 
                risk_scores, risk_prioritization, options, ai_manager
            )
            
            if progress_callback:
                await progress_callback(1.0, "Complete", "Risk calculation completed")
            
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            return ScanResult(
                scan_id=scan_id,
                tool_name="risk_calculator",
                target=target,
                status=ToolStatus.COMPLETED,
                start_time=start_time,
                end_time=end_time,
                duration_seconds=duration,
                command_executed=f"risk_calculator --target {target} --methodology {options.methodology}",
                exit_code=0,
                raw_output=json.dumps(risk_assessment, indent=2),
                error_output="",
                parsed_results=risk_assessment,
                vulnerabilities=risk_assessment.get("vulnerabilities", []),
                summary=risk_assessment.get("summary", {}),
                metadata={
                    "methodology": options.methodology,
                    "risk_framework": options.risk_framework,
                    "ai_analysis_enabled": True,
                    "risk_factors_identified": len(risk_factors),
                    "overall_risk_score": risk_assessment.get("overall_risk_score", 0)
                }
            )
            
        except Exception as e:
            logger.error(f"Risk calculation failed: {e}")
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            return ScanResult(
                scan_id=scan_id,
                tool_name="risk_calculator",
                target=target,
                status=ToolStatus.FAILED,
                start_time=start_time,
                end_time=end_time,
                duration_seconds=duration,
                command_executed="",
                exit_code=1,
                raw_output="",
                error_output=str(e),
                parsed_results={},
                vulnerabilities=[],
                summary={"error": str(e)},
                metadata={}
            )
    
    async def _identify_risk_factors(self, target: str, options: RiskCalculationOptions, ai_manager: AIServiceManager) -> List[RiskFactor]:
        """Identify risk factors for the target"""
        prompt = f"""
        Identify and analyze risk factors for the following target:
        
        Target: {target}
        Business Context: {options.business_context}
        Risk Framework: {options.risk_framework}
        
        Please identify risk factors in the following categories:
        1. Technical risks (vulnerabilities, misconfigurations)
        2. Operational risks (processes, procedures)
        3. Human factors (training, awareness)
        4. Physical security risks
        5. Third-party risks
        6. Compliance and legal risks
        7. Financial risks
        
        For each risk factor, provide:
        - Factor ID and name
        - Category
        - Description
        - Weight (0.0-1.0)
        - Current value/score (0.0-1.0)
        - Confidence level (0.0-1.0)
        - Data source
        
        Focus on factors most relevant to the target and business context.
        """
        
        try:
            ai_response = await ai_manager.generate_analysis(
                prompt=prompt,
                analysis_type="risk_factor_identification",
                provider=AIProvider.OPENAI
            )
            
            if ai_response and "analysis" in ai_response:
                factors_data = ai_response["analysis"].get("risk_factors", [])
                return [
                    RiskFactor(
                        id=factor.get("id", f"factor_{i}"),
                        name=factor.get("name", "Unknown Factor"),
                        category=factor.get("category", "technical"),
                        description=factor.get("description", ""),
                        weight=factor.get("weight", 0.5),
                        value=factor.get("value", 0.5),
                        confidence=factor.get("confidence", 0.7),
                        source=factor.get("source", "ai_analysis")
                    ) for i, factor in enumerate(factors_data)
                ]
            else:
                return self._generate_default_risk_factors()
        
        except Exception as e:
            logger.warning(f"AI risk factor identification failed: {e}")
            return self._generate_default_risk_factors()
    
    def _generate_default_risk_factors(self) -> List[RiskFactor]:
        """Generate default risk factors"""
        return [
            RiskFactor(
                id="tech_vulns",
                name="Technical Vulnerabilities",
                category="technical",
                description="Presence of known security vulnerabilities",
                weight=0.8,
                value=0.6,
                confidence=0.8,
                source="default_analysis"
            ),
            RiskFactor(
                id="access_controls",
                name="Access Control Weaknesses",
                category="technical",
                description="Inadequate access control mechanisms",
                weight=0.7,
                value=0.5,
                confidence=0.7,
                source="default_analysis"
            ),
            RiskFactor(
                id="human_factor",
                name="Human Factor Risks",
                category="human",
                description="Risks from human error and social engineering",
                weight=0.6,
                value=0.7,
                confidence=0.6,
                source="default_analysis"
            ),
            RiskFactor(
                id="data_handling",
                name="Data Handling Practices",
                category="operational",
                description="Risks from poor data handling practices",
                weight=0.7,
                value=0.4,
                confidence=0.7,
                source="default_analysis"
            )
        ]
    
    async def _assess_impact(self, target: str, risk_factors: List[RiskFactor], 
                           options: RiskCalculationOptions, ai_manager: AIServiceManager) -> Dict[str, Any]:
        """Assess potential impact of risks"""
        prompt = f"""
        Assess the potential impact of the identified risk factors:
        
        Target: {target}
        Risk Factors: {[{'name': f.name, 'category': f.category, 'value': f.value} for f in risk_factors]}
        Business Context: {options.business_context}
        
        Please assess impact in the following areas:
        1. Financial impact (direct costs, lost revenue, fines)
        2. Operational impact (downtime, process disruption)
        3. Reputational impact (brand damage, customer loss)
        4. Compliance impact (regulatory violations, legal issues)
        5. Strategic impact (competitive disadvantage, market position)
        
        For each impact area, provide:
        - Impact level (minimal, low, medium, high, very_high, catastrophic)
        - Quantitative estimates where possible
        - Qualitative description
        - Time frame (immediate, short-term, long-term)
        - Affected stakeholders
        
        Consider both immediate and long-term impacts.
        """
        
        try:
            ai_response = await ai_manager.generate_analysis(
                prompt=prompt,
                analysis_type="impact_assessment",
                provider=AIProvider.OPENAI
            )
            
            if ai_response and "analysis" in ai_response:
                return ai_response["analysis"]
            else:
                return self._generate_default_impact_assessment()
        
        except Exception as e:
            logger.warning(f"AI impact assessment failed: {e}")
            return self._generate_default_impact_assessment()
    
    def _generate_default_impact_assessment(self) -> Dict[str, Any]:
        """Generate default impact assessment"""
        return {
            "financial_impact": {
                "level": "medium",
                "estimated_cost": 50000,
                "description": "Moderate financial impact from security incidents",
                "timeframe": "short-term"
            },
            "operational_impact": {
                "level": "high",
                "estimated_downtime": 24,
                "description": "Significant operational disruption",
                "timeframe": "immediate"
            },
            "reputational_impact": {
                "level": "medium",
                "description": "Moderate damage to brand reputation",
                "timeframe": "long-term"
            },
            "compliance_impact": {
                "level": "medium",
                "description": "Potential regulatory compliance violations",
                "timeframe": "short-term"
            },
            "overall_impact_score": 0.6
        }
    
    async def _assess_likelihood(self, target: str, risk_factors: List[RiskFactor], 
                               options: RiskCalculationOptions, ai_manager: AIServiceManager) -> Dict[str, Any]:
        """Assess likelihood of risk materialization"""
        prompt = f"""
        Assess the likelihood of risks materializing for the target:
        
        Target: {target}
        Risk Factors: {[{'name': f.name, 'category': f.category, 'value': f.value} for f in risk_factors]}
        Business Context: {options.business_context}
        
        Please assess likelihood considering:
        1. Threat landscape and actor motivation
        2. Existing security controls and their effectiveness
        3. Historical incident data and trends
        4. Environmental factors (industry, geography, size)
        5. Vulnerability exposure and exploitability
        
        For each risk factor, provide:
        - Likelihood level (very_low, low, medium, high, very_high)
        - Probability estimate (0.0-1.0)
        - Contributing factors
        - Confidence level
        - Time horizon (1 year, 3 years, 5 years)
        
        Consider both internal and external threat sources.
        """
        
        try:
            ai_response = await ai_manager.generate_analysis(
                prompt=prompt,
                analysis_type="likelihood_assessment",
                provider=AIProvider.OPENAI
            )
            
            if ai_response and "analysis" in ai_response:
                return ai_response["analysis"]
            else:
                return self._generate_default_likelihood_assessment()
        
        except Exception as e:
            logger.warning(f"AI likelihood assessment failed: {e}")
            return self._generate_default_likelihood_assessment()
    
    def _generate_default_likelihood_assessment(self) -> Dict[str, Any]:
        """Generate default likelihood assessment"""
        return {
            "technical_vulnerabilities": {
                "likelihood_level": "medium",
                "probability": 0.4,
                "contributing_factors": ["Common vulnerability types", "Attack automation"],
                "confidence": 0.7,
                "timeframe": "1_year"
            },
            "human_factors": {
                "likelihood_level": "high",
                "probability": 0.6,
                "contributing_factors": ["Social engineering trends", "Remote work risks"],
                "confidence": 0.8,
                "timeframe": "1_year"
            },
            "operational_risks": {
                "likelihood_level": "medium",
                "probability": 0.3,
                "contributing_factors": ["Process maturity", "Change management"],
                "confidence": 0.6,
                "timeframe": "1_year"
            },
            "overall_likelihood_score": 0.4
        }
    
    async def _calculate_risk_scores(self, target: str, risk_factors: List[RiskFactor], 
                                   impact_assessment: Dict[str, Any], likelihood_assessment: Dict[str, Any],
                                   options: RiskCalculationOptions, ai_manager: AIServiceManager) -> Dict[str, Any]:
        """Calculate comprehensive risk scores"""
        
        # Calculate weighted risk scores
        risk_scores = {}
        
        if options.methodology == "qualitative":
            risk_scores = self._calculate_qualitative_scores(risk_factors, impact_assessment, likelihood_assessment)
        elif options.methodology == "quantitative":
            risk_scores = self._calculate_quantitative_scores(risk_factors, impact_assessment, likelihood_assessment, options)
        else:  # hybrid
            risk_scores = self._calculate_hybrid_scores(risk_factors, impact_assessment, likelihood_assessment, options)
        
        # Calculate overall risk score
        overall_score = self._calculate_overall_risk_score(risk_scores, risk_factors)
        
        return {
            "methodology": options.methodology,
            "overall_risk_score": overall_score,
            "risk_level": self._determine_risk_level(overall_score),
            "factor_scores": risk_scores,
            "impact_score": impact_assessment.get("overall_impact_score", 0.5),
            "likelihood_score": likelihood_assessment.get("overall_likelihood_score", 0.5),
            "confidence_score": self._calculate_confidence_score(risk_factors),
            "calculation_details": {
                "risk_factors_count": len(risk_factors),
                "weighted_calculation": True,
                "framework": options.risk_framework
            }
        }
    
    def _calculate_qualitative_scores(self, risk_factors: List[RiskFactor], 
                                    impact_assessment: Dict[str, Any], likelihood_assessment: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate qualitative risk scores"""
        scores = {}
        
        for factor in risk_factors:
            impact_score = impact_assessment.get("overall_impact_score", 0.5)
            likelihood_score = likelihood_assessment.get("overall_likelihood_score", 0.5)
            
            # Qualitative calculation: Risk = Impact × Likelihood × Factor Weight
            factor_score = impact_score * likelihood_score * factor.weight * factor.value
            
            scores[factor.id] = {
                "score": factor_score,
                "level": self._determine_risk_level(factor_score),
                "impact_contribution": impact_score,
                "likelihood_contribution": likelihood_score,
                "factor_weight": factor.weight,
                "confidence": factor.confidence
            }
        
        return scores
    
    def _calculate_quantitative_scores(self, risk_factors: List[RiskFactor], 
                                     impact_assessment: Dict[str, Any], likelihood_assessment: Dict[str, Any],
                                     options: RiskCalculationOptions) -> Dict[str, Any]:
        """Calculate quantitative risk scores"""
        scores = {}
        
        for factor in risk_factors:
            # Use financial impact if available
            financial_impact = impact_assessment.get("financial_impact", {}).get("estimated_cost", 50000)
            probability = likelihood_assessment.get("overall_likelihood_score", 0.5)
            
            # Quantitative calculation: Risk = Impact ($) × Probability × Factor Weight
            factor_score = (financial_impact * probability * factor.weight * factor.value) / 100000  # Normalize
            
            scores[factor.id] = {
                "score": min(factor_score, 1.0),  # Cap at 1.0
                "level": self._determine_risk_level(min(factor_score, 1.0)),
                "financial_impact": financial_impact,
                "probability": probability,
                "factor_weight": factor.weight,
                "confidence": factor.confidence
            }
        
        return scores
    
    def _calculate_hybrid_scores(self, risk_factors: List[RiskFactor], 
                               impact_assessment: Dict[str, Any], likelihood_assessment: Dict[str, Any],
                               options: RiskCalculationOptions) -> Dict[str, Any]:
        """Calculate hybrid risk scores"""
        # Combine qualitative and quantitative approaches
        qual_scores = self._calculate_qualitative_scores(risk_factors, impact_assessment, likelihood_assessment)
        quant_scores = self._calculate_quantitative_scores(risk_factors, impact_assessment, likelihood_assessment, options)
        
        scores = {}
        for factor in risk_factors:
            qual_score = qual_scores.get(factor.id, {}).get("score", 0.5)
            quant_score = quant_scores.get(factor.id, {}).get("score", 0.5)
            
            # Weighted average of qualitative and quantitative scores
            hybrid_score = (qual_score * 0.6) + (quant_score * 0.4)
            
            scores[factor.id] = {
                "score": hybrid_score,
                "level": self._determine_risk_level(hybrid_score),
                "qualitative_score": qual_score,
                "quantitative_score": quant_score,
                "factor_weight": factor.weight,
                "confidence": factor.confidence
            }
        
        return scores
    
    def _calculate_overall_risk_score(self, risk_scores: Dict[str, Any], risk_factors: List[RiskFactor]) -> float:
        """Calculate overall risk score"""
        if not risk_scores:
            return 0.5
        
        weighted_sum = 0.0
        total_weight = 0.0
        
        for factor in risk_factors:
            if factor.id in risk_scores:
                score = risk_scores[factor.id].get("score", 0.5)
                weight = factor.weight
                weighted_sum += score * weight
                total_weight += weight
        
        return weighted_sum / total_weight if total_weight > 0 else 0.5
    
    def _calculate_confidence_score(self, risk_factors: List[RiskFactor]) -> float:
        """Calculate overall confidence score"""
        if not risk_factors:
            return 0.5
        
        return sum(factor.confidence for factor in risk_factors) / len(risk_factors)
    
    def _determine_risk_level(self, score: float) -> str:
        """Determine risk level from score"""
        if score >= 0.9:
            return RiskLevel.CRITICAL.value
        elif score >= 0.7:
            return RiskLevel.VERY_HIGH.value
        elif score >= 0.5:
            return RiskLevel.HIGH.value
        elif score >= 0.3:
            return RiskLevel.MEDIUM.value
        elif score >= 0.1:
            return RiskLevel.LOW.value
        else:
            return RiskLevel.VERY_LOW.value
    
    async def _prioritize_risks(self, risk_scores: Dict[str, Any], options: RiskCalculationOptions, 
                              ai_manager: AIServiceManager) -> Dict[str, Any]:
        """Prioritize risks and generate recommendations"""
        prompt = f"""
        Prioritize the following risks and provide recommendations:
        
        Risk Scores: {risk_scores}
        Methodology: {options.methodology}
        Business Context: {options.business_context}
        
        Please provide:
        1. Risk prioritization matrix
        2. Top 5 priority risks
        3. Risk treatment recommendations for each priority risk
        4. Resource allocation suggestions
        5. Timeline for risk mitigation
        6. Key performance indicators for risk monitoring
        
        Focus on actionable recommendations.
        """
        
        try:
            ai_response = await ai_manager.generate_analysis(
                prompt=prompt,
                analysis_type="risk_prioritization",
                provider=AIProvider.OPENAI
            )
            
            if ai_response and "analysis" in ai_response:
                return ai_response["analysis"]
            else:
                return self._generate_default_prioritization(risk_scores)
        
        except Exception as e:
            logger.warning(f"AI risk prioritization failed: {e}")
            return self._generate_default_prioritization(risk_scores)
    
    def _generate_default_prioritization(self, risk_scores: Dict[str, Any]) -> Dict[str, Any]:
        """Generate default risk prioritization"""
        # Sort risks by score
        sorted_risks = sorted(
            risk_scores.items(),
            key=lambda x: x[1].get("score", 0),
            reverse=True
        )
        
        return {
            "priority_risks": [
                {
                    "risk_id": risk_id,
                    "risk_score": risk_data.get("score", 0),
                    "risk_level": risk_data.get("level", "medium"),
                    "priority_rank": i + 1,
                    "recommendation": f"Address {risk_id} with high priority"
                }
                for i, (risk_id, risk_data) in enumerate(sorted_risks[:5])
            ],
            "treatment_recommendations": [
                "Implement security controls for high-priority risks",
                "Regular risk monitoring and assessment",
                "Staff training and awareness programs",
                "Incident response plan development"
            ],
            "resource_allocation": {
                "immediate_action": "60% of resources",
                "medium_term": "30% of resources",
                "long_term": "10% of resources"
            }
        }
    
    async def _generate_risk_assessment(self, target: str, risk_factors: List[RiskFactor], 
                                      impact_assessment: Dict[str, Any], likelihood_assessment: Dict[str, Any],
                                      risk_scores: Dict[str, Any], risk_prioritization: Dict[str, Any],
                                      options: RiskCalculationOptions, ai_manager: AIServiceManager) -> Dict[str, Any]:
        """Generate comprehensive risk assessment"""
        
        # Generate vulnerabilities from high-risk factors
        vulnerabilities = []
        for factor in risk_factors:
            if factor.id in risk_scores:
                score_data = risk_scores[factor.id]
                if score_data.get("score", 0) >= 0.5:  # High risk threshold
                    vulnerabilities.append({
                        "id": f"risk_factor_{factor.id}",
                        "type": "Risk Factor",
                        "severity": score_data.get("level", "medium"),
                        "description": f"High-risk factor identified: {factor.description}",
                        "category": factor.category,
                        "risk_score": score_data.get("score", 0),
                        "confidence": factor.confidence,
                        "recommendation": f"Implement controls to mitigate {factor.name}"
                    })
        
        risk_assessment = {
            "target_info": {
                "target": target,
                "business_context": options.business_context,
                "methodology": options.methodology,
                "risk_framework": options.risk_framework,
                "assessment_timestamp": datetime.now().isoformat()
            },
            "risk_factors": [
                {
                    "id": factor.id,
                    "name": factor.name,
                    "category": factor.category,
                    "description": factor.description,
                    "weight": factor.weight,
                    "value": factor.value,
                    "confidence": factor.confidence,
                    "source": factor.source
                } for factor in risk_factors
            ],
            "impact_assessment": impact_assessment,
            "likelihood_assessment": likelihood_assessment,
            "risk_scores": risk_scores,
            "risk_prioritization": risk_prioritization,
            "vulnerabilities": vulnerabilities,
            "overall_risk_score": risk_scores.get("overall_risk_score", 0.5),
            "summary": {
                "total_risk_factors": len(risk_factors),
                "high_risk_factors": len([f for f in risk_factors if f.id in risk_scores and risk_scores[f.id].get("score", 0) >= 0.5]),
                "overall_risk_level": risk_scores.get("risk_level", "medium"),
                "confidence_score": risk_scores.get("confidence_score", 0.7),
                "methodology_used": options.methodology,
                "priority_recommendations": len(risk_prioritization.get("priority_risks", [])),
                "assessment_date": datetime.now().isoformat()
            }
        }
        
        return risk_assessment
    
    def get_frontend_interface_data(self) -> Dict[str, Any]:
        """Get data for frontend interface"""
        return {
            "tool_info": {
                "name": "Risk Calculator",
                "description": "AI-powered risk assessment and calculation engine",
                "version": "1.0.0",
                "category": "AI Analyzer",
                "status": "available" if self.is_available() else "unavailable"
            },
            "scan_options": {
                "target": {
                    "type": "text",
                    "required": True,
                    "placeholder": "https://example.com or System Name",
                    "validation": "url_or_text"
                },
                "methodology": {
                    "type": "select",
                    "options": ["qualitative", "quantitative", "hybrid"],
                    "default": "qualitative",
                    "label": "Risk assessment methodology"
                },
                "risk_framework": {
                    "type": "select",
                    "options": ["nist", "iso27001", "fair", "custom"],
                    "default": "nist",
                    "label": "Risk framework"
                },
                "business_context": {
                    "type": "textarea",
                    "required": False,
                    "placeholder": "Describe the business context and environment",
                    "label": "Business context"
                },
                "include_financial_impact": {
                    "type": "boolean",
                    "default": True,
                    "label": "Include financial impact assessment"
                },
                "include_compliance_impact": {
                    "type": "boolean",
                    "default": True,
                    "label": "Include compliance impact assessment"
                },
                "include_operational_impact": {
                    "type": "boolean",
                    "default": True,
                    "label": "Include operational impact assessment"
                }
            },
            "output_formats": ["json", "html", "pdf"],
            "capabilities": [
                "AI-powered risk assessment",
                "Multiple risk methodologies",
                "Risk factor identification",
                "Impact and likelihood assessment",
                "Quantitative and qualitative analysis",
                "Risk prioritization",
                "Treatment recommendations",
                "Compliance framework support"
            ]
        }


# Register the tool
register_tool(RiskCalculator)