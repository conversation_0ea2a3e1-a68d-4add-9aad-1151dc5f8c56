#!/usr/bin/env python3
"""
AI Prompt Template System for NexusScan
Comprehensive prompt templates for security AI operations
"""

import json
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from enum import Enum

class PromptCategory(Enum):
    """Categories of AI prompts"""
    PAYLOAD_GENERATION = "payload_generation"
    VULNERABILITY_ANALYSIS = "vulnerability_analysis"
    RISK_ASSESSMENT = "risk_assessment"
    EXPLOIT_CHAINING = "exploit_chaining"
    REMEDIATION_PLANNING = "remediation_planning"
    COMPLIANCE_MAPPING = "compliance_mapping"
    THREAT_MODELING = "threat_modeling"
    BUSINESS_IMPACT = "business_impact"

class DifficultyLevel(Enum):
    """Difficulty levels for payload generation"""
    BEGINNER = "beginner"
    INTERMEDIATE = "intermediate"
    ADVANCED = "advanced"
    EXPERT = "expert"

class VulnerabilityType(Enum):
    """Types of vulnerabilities for payload generation"""
    SQL_INJECTION = "sql_injection"
    XSS = "xss"
    RCE = "remote_code_execution"
    LFI = "local_file_inclusion"
    RFI = "remote_file_inclusion"
    SSRF = "server_side_request_forgery"
    XXE = "xml_external_entity"
    CSRF = "cross_site_request_forgery"
    AUTH_BYPASS = "authentication_bypass"
    PRIVILEGE_ESCALATION = "privilege_escalation"
    BUFFER_OVERFLOW = "buffer_overflow"
    DESERIALIZATION = "insecure_deserialization"
    PATH_TRAVERSAL = "path_traversal"
    FILE_UPLOAD = "file_upload"
    LDAP_INJECTION = "ldap_injection"
    COMMAND_INJECTION = "command_injection"

@dataclass
class PromptTemplate:
    """Template for AI prompts"""
    name: str
    category: PromptCategory
    system_prompt: str
    user_prompt_template: str
    variables: List[str]
    output_format: str
    model_preferences: Dict[str, Any]
    description: str

class PromptTemplateManager:
    """Manages AI prompt templates for different security scenarios"""
    
    def __init__(self):
        """Initialize prompt template manager"""
        self.templates = {}
        self._load_default_templates()
    
    def _load_default_templates(self):
        """Load default prompt templates"""
        
        # Payload Generation Templates
        self._add_payload_generation_templates()
        
        # Vulnerability Analysis Templates
        self._add_vulnerability_analysis_templates()
        
        # Risk Assessment Templates
        self._add_risk_assessment_templates()
        
        # Exploit Chaining Templates
        self._add_exploit_chaining_templates()
        
        # Remediation Planning Templates
        self._add_remediation_templates()
        
        # Compliance Templates
        self._add_compliance_templates()
        
        # Threat Modeling Templates
        self._add_threat_modeling_templates()
        
        # Business Impact Templates
        self._add_business_impact_templates()
    
    def _add_payload_generation_templates(self):
        """Add payload generation prompt templates"""
        
        # SQL Injection Template
        self.templates["sql_injection_payload"] = PromptTemplate(
            name="SQL Injection Payload Generator",
            category=PromptCategory.PAYLOAD_GENERATION,
            system_prompt="""You are an expert SQL injection specialist with deep knowledge of:
            - Database-specific SQL injection techniques (MySQL, PostgreSQL, MSSQL, Oracle, SQLite)
            - Advanced SQL injection types (Boolean-based, Time-based, Union-based, Error-based)
            - WAF bypass techniques and encoding methods
            - Post-exploitation database enumeration and data extraction
            - Modern defense mechanisms and detection methods

            Generate educational SQL injection payloads for authorized penetration testing.

            CRITICAL REQUIREMENTS:
            1. Payloads must be for authorized testing only
            2. Include comprehensive technical explanations
            3. Provide specific remediation guidance
            4. Assess realistic success probability
            5. Include WAF bypass variations when applicable
            6. Generate detection signatures for blue teams

            Output must be valid JSON with detailed payload analysis.""",
            
            user_prompt_template="""Generate a {difficulty_level} SQL injection payload for authorized testing:

            TARGET DETAILS:
            - Database Type: {database_type}
            - Application Framework: {framework}
            - Input Context: {input_context}
            - WAF Present: {waf_present}
            - Authentication Required: {auth_required}
            
            INJECTION CONTEXT:
            - Parameter Type: {parameter_type}
            - Injection Point: {injection_point}
            - Character Restrictions: {restrictions}
            
            PAYLOAD REQUIREMENTS:
            - Objective: {objective}
            - Stealth Level: {stealth_level}
            - Preferred Technique: {technique}
            
            Generate comprehensive payload with:
            1. Primary functional SQL injection payload
            2. 3-4 alternative approaches for different scenarios
            3. WAF bypass variations if applicable
            4. Step-by-step exploitation guide
            5. Required preconditions and assumptions
            6. Post-exploitation enumeration commands
            7. Risk assessment and business impact
            8. Specific remediation recommendations
            9. Detection signatures and monitoring rules""",
            
            variables=[
                "difficulty_level", "database_type", "framework", "input_context", 
                "waf_present", "auth_required", "parameter_type", "injection_point", 
                "restrictions", "objective", "stealth_level", "technique"
            ],
            
            output_format="""{
                "payload": "primary SQL injection payload",
                "alternatives": ["payload1", "payload2", "payload3"],
                "waf_bypasses": ["bypass1", "bypass2"],
                "explanation": "detailed technical explanation",
                "steps": ["step1", "step2", "step3"],
                "preconditions": ["condition1", "condition2"],
                "post_exploitation": ["enum1", "enum2"],
                "risk_assessment": {
                    "likelihood": "high|medium|low",
                    "impact": "critical|high|medium|low",
                    "cvss_score": 8.5
                },
                "remediation": "specific remediation steps",
                "detection_rules": ["rule1", "rule2"],
                "confidence": 0.9
            }""",
            
            model_preferences={
                "openai": {"temperature": 0.7, "max_tokens": 2000},
                "deepseek": {"temperature": 0.6, "max_tokens": 1800}
            },
            
            description="Generates advanced SQL injection payloads with comprehensive analysis and remediation guidance"
        )
        
        # XSS Payload Template
        self.templates["xss_payload"] = PromptTemplate(
            name="XSS Payload Generator",
            category=PromptCategory.PAYLOAD_GENERATION,
            system_prompt="""You are an expert cross-site scripting (XSS) specialist with expertise in:
            - All XSS types (Reflected, Stored, DOM-based, Self-XSS)
            - Advanced XSS techniques and filter bypass methods
            - Content Security Policy (CSP) bypass techniques
            - JavaScript obfuscation and encoding methods
            - Browser-specific XSS vectors and exploitation
            - Modern XSS defense mechanisms and sanitization

            Generate educational XSS payloads for authorized security testing.

            REQUIREMENTS:
            1. Authorized testing purposes only
            2. Comprehensive technical explanations
            3. Browser compatibility considerations
            4. CSP bypass techniques when applicable
            5. Remediation and prevention guidance
            6. Detection and monitoring recommendations""",
            
            user_prompt_template="""Generate a {difficulty_level} XSS payload for authorized testing:

            TARGET CONTEXT:
            - XSS Type: {xss_type}
            - Target Browser: {target_browser}
            - Input Sanitization: {sanitization_level}
            - CSP Present: {csp_present}
            - Output Context: {output_context}
            
            PAYLOAD SPECIFICATIONS:
            - Objective: {objective}
            - Evasion Requirements: {evasion_requirements}
            - Character Restrictions: {char_restrictions}
            - Encoding Preferences: {encoding_preferences}
            
            Generate comprehensive XSS payload including:
            1. Primary functional XSS payload
            2. Browser-specific variations
            3. Filter bypass alternatives
            4. CSP bypass techniques if applicable
            5. Execution verification methods
            6. Post-exploitation possibilities
            7. Impact assessment and scenarios
            8. Remediation recommendations
            9. Detection signatures""",
            
            variables=[
                "difficulty_level", "xss_type", "target_browser", "sanitization_level",
                "csp_present", "output_context", "objective", "evasion_requirements",
                "char_restrictions", "encoding_preferences"
            ],
            
            output_format="""{
                "payload": "primary XSS payload",
                "browser_variants": {"chrome": "payload1", "firefox": "payload2"},
                "filter_bypasses": ["bypass1", "bypass2"],
                "csp_bypasses": ["csp_bypass1", "csp_bypass2"],
                "explanation": "detailed XSS explanation",
                "execution_steps": ["step1", "step2"],
                "verification": "payload verification method",
                "post_exploitation": ["lateral1", "lateral2"],
                "risk_assessment": {
                    "impact": "high|medium|low",
                    "affected_users": "estimate",
                    "data_exposure": "type of data at risk"
                },
                "remediation": "specific prevention measures",
                "detection_rules": ["detection1", "detection2"],
                "confidence": 0.85
            }""",
            
            model_preferences={
                "openai": {"temperature": 0.8, "max_tokens": 1800},
                "deepseek": {"temperature": 0.7, "max_tokens": 1600}
            },
            
            description="Generates sophisticated XSS payloads with browser compatibility and bypass techniques"
        )
    
    def _add_vulnerability_analysis_templates(self):
        """Add vulnerability analysis templates"""
        
        self.templates["comprehensive_vuln_analysis"] = PromptTemplate(
            name="Comprehensive Vulnerability Analysis",
            category=PromptCategory.VULNERABILITY_ANALYSIS,
            system_prompt="""You are a senior vulnerability analyst with expertise in:
            - CVSS scoring and vulnerability classification
            - Exploit development and feasibility assessment
            - Business impact analysis and risk quantification
            - Vulnerability chaining and attack path analysis
            - Industry-specific security requirements
            - Compliance framework mapping (OWASP, NIST, ISO 27001)

            Analyze security vulnerabilities with business context and technical depth.

            ANALYSIS REQUIREMENTS:
            1. Technical accuracy and depth
            2. Business impact consideration
            3. Realistic exploitability assessment
            4. Actionable remediation guidance
            5. Compliance framework alignment
            6. Risk-based prioritization""",
            
            user_prompt_template="""Analyze the following vulnerabilities with {analysis_depth} detail:

            VULNERABILITY DATA:
            {vulnerability_data}

            BUSINESS CONTEXT:
            - Organization Type: {org_type}
            - Critical Assets: {critical_assets}
            - Compliance Requirements: {compliance_frameworks}
            - Risk Tolerance: {risk_tolerance}
            - Available Resources: {resources}

            ANALYSIS SCOPE:
            - Technical Assessment: {technical_scope}
            - Business Impact: {business_scope}
            - Threat Modeling: {threat_modeling}
            - Remediation Planning: {remediation_scope}

            Provide comprehensive analysis including:
            1. Executive summary for leadership
            2. Technical vulnerability assessment
            3. CVSS scoring with justification
            4. Exploitability and threat landscape analysis
            5. Business impact and risk quantification
            6. Attack scenario development
            7. Compliance gap analysis
            8. Prioritized remediation roadmap
            9. Cost-benefit analysis of fixes
            10. Monitoring and detection recommendations""",
            
            variables=[
                "analysis_depth", "vulnerability_data", "org_type", "critical_assets",
                "compliance_frameworks", "risk_tolerance", "resources", "technical_scope",
                "business_scope", "threat_modeling", "remediation_scope"
            ],
            
            output_format="""{
                "executive_summary": "business-focused summary",
                "technical_analysis": "detailed technical assessment",
                "cvss_scores": [{"vuln_id": "CVE-2024-001", "score": 8.5, "vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H"}],
                "exploitability": {
                    "likelihood": "high|medium|low",
                    "complexity": "low|medium|high",
                    "skill_required": "novice|intermediate|expert",
                    "time_to_exploit": "minutes|hours|days"
                },
                "business_impact": {
                    "financial_impact": "estimated cost",
                    "operational_impact": "description",
                    "reputation_impact": "assessment",
                    "compliance_impact": "framework violations"
                },
                "attack_scenarios": ["scenario1", "scenario2"],
                "compliance_gaps": {"pci_dss": "violations", "sox": "issues"},
                "remediation_roadmap": [
                    {
                        "phase": "immediate",
                        "timeline": "24-48 hours",
                        "actions": ["action1", "action2"],
                        "cost": "estimated cost",
                        "priority": "critical"
                    }
                ],
                "monitoring_recommendations": ["recommendation1", "recommendation2"]
            }""",
            
            model_preferences={
                "openai": {"temperature": 0.3, "max_tokens": 2500},
                "deepseek": {"temperature": 0.4, "max_tokens": 2200}
            },
            
            description="Provides comprehensive vulnerability analysis with business context and remediation planning"
        )
    
    def _add_risk_assessment_templates(self):
        """Add risk assessment templates"""
        
        self.templates["quantitative_risk_assessment"] = PromptTemplate(
            name="Quantitative Risk Assessment",
            category=PromptCategory.RISK_ASSESSMENT,
            system_prompt="""You are a risk assessment expert specializing in:
            - Quantitative risk analysis methodologies (FAIR, OCTAVE, NIST)
            - Threat modeling and attack surface analysis
            - Business impact assessment and financial modeling
            - Risk treatment strategies and control effectiveness
            - Regulatory compliance and audit requirements
            - Security metrics and KPI development

            Perform quantitative risk assessments with financial modeling and business context.""",
            
            user_prompt_template="""Perform quantitative risk assessment for:

            ASSET INVENTORY:
            {asset_inventory}

            THREAT LANDSCAPE:
            {threat_data}

            VULNERABILITY PROFILE:
            {vulnerability_profile}

            BUSINESS PARAMETERS:
            - Annual Revenue: {annual_revenue}
            - Industry Sector: {industry}
            - Geographic Presence: {geography}
            - Regulatory Environment: {regulations}
            - Risk Appetite: {risk_appetite}

            Calculate and provide:
            1. Asset valuation and criticality ranking
            2. Threat frequency and capability assessment
            3. Vulnerability exploitability scores
            4. Annual Loss Expectancy (ALE) calculations
            5. Risk heat maps and prioritization matrices
            6. Control effectiveness analysis
            7. Risk treatment recommendations
            8. ROI analysis for security investments
            9. Residual risk assessment
            10. Compliance risk evaluation""",
            
            variables=[
                "asset_inventory", "threat_data", "vulnerability_profile", "annual_revenue",
                "industry", "geography", "regulations", "risk_appetite"
            ],
            
            output_format="""{
                "asset_valuation": {"asset1": 1000000, "asset2": 500000},
                "threat_analysis": [
                    {
                        "threat": "ransomware",
                        "frequency": 0.15,
                        "capability": 0.8,
                        "motivation": 0.9
                    }
                ],
                "vulnerability_scores": {"vuln1": 0.85, "vuln2": 0.65},
                "ale_calculations": {
                    "total_ale": 2500000,
                    "by_threat": {"ransomware": 1500000, "data_breach": 1000000}
                },
                "risk_matrix": "visual representation data",
                "control_effectiveness": {"firewall": 0.7, "ids": 0.6},
                "treatment_recommendations": [
                    {
                        "risk": "data breach",
                        "strategy": "mitigate",
                        "controls": ["encryption", "dlp"],
                        "cost": 200000,
                        "risk_reduction": 0.6
                    }
                ],
                "roi_analysis": {"investment": 500000, "risk_reduction": 1800000, "roi": 260},
                "residual_risk": 700000,
                "compliance_risk": {"gdpr": "high", "pci_dss": "medium"}
            }""",
            
            model_preferences={
                "openai": {"temperature": 0.2, "max_tokens": 2000},
                "deepseek": {"temperature": 0.3, "max_tokens": 1800}
            },
            
            description="Performs quantitative risk assessment with financial modeling and business impact analysis"
        )
    
    def _add_exploit_chaining_templates(self):
        """Add exploit chaining templates"""
        
        self.templates["multi_stage_exploit_chain"] = PromptTemplate(
            name="Multi-Stage Exploit Chain Analysis",
            category=PromptCategory.EXPLOIT_CHAINING,
            system_prompt="""You are an advanced threat modeling expert specializing in:
            - Multi-stage attack planning and orchestration
            - Vulnerability interdependency analysis
            - Attack tree modeling and path optimization
            - Lateral movement and privilege escalation techniques
            - Post-exploitation and persistence mechanisms
            - Detection evasion and anti-forensics

            Design realistic attack chains for security assessment and defense planning.""",
            
            user_prompt_template="""Design multi-stage exploit chain using discovered vulnerabilities:

            VULNERABILITY INVENTORY:
            {vulnerabilities}

            TARGET ENVIRONMENT:
            {environment_details}

            ATTACK PARAMETERS:
            - Attacker Skill Level: {attacker_skill}
            - Available Resources: {attacker_resources}
            - Time Constraints: {time_constraints}
            - Stealth Requirements: {stealth_level}
            - Objectives: {attack_objectives}

            DEFENSIVE POSTURE:
            {defensive_measures}

            Generate comprehensive attack chain including:
            1. Initial access vectors and entry points
            2. Vulnerability chaining sequence
            3. Lateral movement pathways
            4. Privilege escalation opportunities
            5. Persistence mechanisms
            6. Data exfiltration methods
            7. Detection probability at each stage
            8. Alternative paths and contingencies
            9. Timeline and resource requirements
            10. Defensive countermeasures""",
            
            variables=[
                "vulnerabilities", "environment_details", "attacker_skill", "attacker_resources",
                "time_constraints", "stealth_level", "attack_objectives", "defensive_measures"
            ],
            
            output_format="""{
                "attack_chain": [
                    {
                        "stage": 1,
                        "name": "Initial Access",
                        "vulnerabilities": ["CVE-2024-001"],
                        "techniques": ["phishing", "exploit"],
                        "success_probability": 0.8,
                        "detection_probability": 0.3,
                        "time_required": "2 hours",
                        "skills_required": "intermediate"
                    }
                ],
                "lateral_movement": [
                    {
                        "from": "workstation",
                        "to": "server",
                        "method": "credential harvesting",
                        "probability": 0.7
                    }
                ],
                "privilege_escalation": ["technique1", "technique2"],
                "persistence_mechanisms": ["backdoor", "scheduled_task"],
                "exfiltration_methods": ["dns_tunneling", "https_beacon"],
                "detection_analysis": {
                    "overall_detection_probability": 0.4,
                    "detection_points": ["stage2", "stage5"],
                    "evasion_techniques": ["technique1", "technique2"]
                },
                "alternative_paths": ["path1", "path2"],
                "timeline": "6-12 hours total",
                "countermeasures": ["defense1", "defense2"]
            }""",
            
            model_preferences={
                "openai": {"temperature": 0.6, "max_tokens": 2200},
                "deepseek": {"temperature": 0.7, "max_tokens": 2000}
            },
            
            description="Designs sophisticated multi-stage attack chains with detection analysis and countermeasures"
        )
    
    def _add_remediation_templates(self):
        """Add remediation planning templates"""
        
        self.templates["comprehensive_remediation_plan"] = PromptTemplate(
            name="Comprehensive Remediation Planning",
            category=PromptCategory.REMEDIATION_PLANNING,
            system_prompt="""You are a remediation planning expert with expertise in:
            - Vulnerability management and patch management
            - Security architecture and control implementation
            - Risk-based prioritization and resource allocation
            - Change management and operational impact assessment
            - Compliance requirements and audit considerations
            - Business continuity and disaster recovery planning

            Create comprehensive, actionable remediation plans with business context.""",
            
            user_prompt_template="""Create comprehensive remediation plan for identified vulnerabilities:

            VULNERABILITY ASSESSMENT:
            {vulnerability_summary}

            ORGANIZATIONAL CONTEXT:
            - Organization Size: {org_size}
            - IT Infrastructure: {infrastructure}
            - Available Budget: {budget}
            - Skilled Personnel: {personnel}
            - Business Criticality: {criticality}
            - Compliance Requirements: {compliance}

            OPERATIONAL CONSTRAINTS:
            - Maintenance Windows: {maintenance_windows}
            - Business Impact Tolerance: {impact_tolerance}
            - Testing Requirements: {testing_requirements}
            - Approval Processes: {approval_process}

            Develop remediation plan including:
            1. Risk-based vulnerability prioritization
            2. Phased implementation approach
            3. Resource requirements and allocation
            4. Timeline with dependencies
            5. Testing and validation procedures
            6. Business impact mitigation
            7. Contingency and rollback plans
            8. Progress monitoring and metrics
            9. Cost-benefit analysis
            10. Compliance verification steps""",
            
            variables=[
                "vulnerability_summary", "org_size", "infrastructure", "budget",
                "personnel", "criticality", "compliance", "maintenance_windows",
                "impact_tolerance", "testing_requirements", "approval_process"
            ],
            
            output_format="""{
                "remediation_strategy": {
                    "approach": "risk-based phased implementation",
                    "duration": "6 months",
                    "total_cost": 500000,
                    "risk_reduction": "85%"
                },
                "priority_matrix": [
                    {
                        "vulnerability": "CVE-2024-001",
                        "priority": "critical",
                        "timeline": "immediate",
                        "effort": "high",
                        "cost": 50000
                    }
                ],
                "implementation_phases": [
                    {
                        "phase": "Emergency Response",
                        "duration": "48 hours",
                        "vulnerabilities": ["critical_vulns"],
                        "actions": ["patch", "isolate"],
                        "resources": ["2 engineers", "$10K"],
                        "success_criteria": "critical vulns patched"
                    }
                ],
                "resource_allocation": {
                    "personnel": {"security_team": 3, "it_ops": 2, "qa": 1},
                    "budget": {"patching": 100000, "tools": 50000, "training": 25000},
                    "timeline": {"q1": "critical", "q2": "high", "q3": "medium"}
                },
                "testing_procedures": ["procedure1", "procedure2"],
                "business_impact_mitigation": ["strategy1", "strategy2"],
                "contingency_plans": ["plan1", "plan2"],
                "success_metrics": ["metric1", "metric2"],
                "compliance_validation": {"pci_dss": "validated", "sox": "pending"}
            }""",
            
            model_preferences={
                "openai": {"temperature": 0.3, "max_tokens": 2300},
                "deepseek": {"temperature": 0.4, "max_tokens": 2000}
            },
            
            description="Creates comprehensive remediation plans with resource allocation and business impact consideration"
        )
    
    def _add_compliance_templates(self):
        """Add compliance mapping templates"""
        
        self.templates["compliance_gap_analysis"] = PromptTemplate(
            name="Compliance Gap Analysis",
            category=PromptCategory.COMPLIANCE_MAPPING,
            system_prompt="""You are a compliance expert specializing in:
            - Regulatory frameworks (PCI DSS, GDPR, HIPAA, SOX, NIST, ISO 27001)
            - Audit preparation and evidence collection
            - Control mapping and gap analysis
            - Risk assessment for compliance purposes
            - Remediation planning for regulatory requirements
            - Continuous compliance monitoring

            Perform comprehensive compliance gap analysis and remediation planning.""",
            
            user_prompt_template="""Perform compliance gap analysis for the following frameworks:

            TARGET FRAMEWORKS:
            {compliance_frameworks}

            CURRENT SECURITY POSTURE:
            {security_assessment}

            ORGANIZATIONAL CONTEXT:
            - Industry: {industry}
            - Geographic Scope: {geography}
            - Data Types: {data_types}
            - System Architecture: {architecture}
            - Existing Controls: {current_controls}

            AUDIT TIMELINE:
            - Next Audit Date: {audit_date}
            - Audit Scope: {audit_scope}
            - Previous Findings: {previous_findings}

            Provide comprehensive analysis including:
            1. Framework-specific requirement mapping
            2. Current compliance status assessment
            3. Gap identification and prioritization
            4. Risk analysis for non-compliance
            5. Remediation roadmap with timelines
            6. Evidence collection requirements
            7. Monitoring and maintenance procedures
            8. Cost-benefit analysis of compliance
            9. Audit readiness assessment
            10. Continuous improvement recommendations""",
            
            variables=[
                "compliance_frameworks", "security_assessment", "industry", "geography",
                "data_types", "architecture", "current_controls", "audit_date",
                "audit_scope", "previous_findings"
            ],
            
            output_format="""{
                "compliance_status": {
                    "pci_dss": {"status": "partial", "compliance_percentage": 75},
                    "gdpr": {"status": "compliant", "compliance_percentage": 95},
                    "hipaa": {"status": "non_compliant", "compliance_percentage": 45}
                },
                "gap_analysis": [
                    {
                        "framework": "PCI DSS",
                        "requirement": "2.4",
                        "description": "Maintain inventory of system components",
                        "current_status": "partial",
                        "gap": "incomplete asset inventory",
                        "risk_level": "medium",
                        "remediation_effort": "low"
                    }
                ],
                "risk_assessment": {
                    "financial_penalties": {"pci_dss": 500000, "gdpr": 2000000},
                    "business_impact": "moderate",
                    "reputation_risk": "high"
                },
                "remediation_roadmap": [
                    {
                        "framework": "PCI DSS",
                        "timeline": "3 months",
                        "priority_actions": ["asset_inventory", "network_segmentation"],
                        "estimated_cost": 150000,
                        "responsible_team": "IT Security"
                    }
                ],
                "evidence_requirements": {
                    "documentation": ["policies", "procedures"],
                    "technical_evidence": ["scan_reports", "log_analysis"],
                    "interviews": ["security_team", "management"]
                },
                "monitoring_framework": {
                    "continuous_monitoring": ["automated_scans", "log_analysis"],
                    "periodic_reviews": ["quarterly_assessments", "annual_audits"],
                    "metrics": ["compliance_score", "gap_closure_rate"]
                },
                "audit_readiness": {
                    "current_readiness": "60%",
                    "required_actions": ["action1", "action2"],
                    "timeline_to_ready": "2 months"
                }
            }""",
            
            model_preferences={
                "openai": {"temperature": 0.2, "max_tokens": 2500},
                "deepseek": {"temperature": 0.3, "max_tokens": 2200}
            },
            
            description="Performs comprehensive compliance gap analysis with remediation planning and audit preparation"
        )
    
    def _add_threat_modeling_templates(self):
        """Add threat modeling templates"""
        
        self.templates["stride_threat_model"] = PromptTemplate(
            name="STRIDE Threat Modeling",
            category=PromptCategory.THREAT_MODELING,
            system_prompt="""You are a threat modeling expert specializing in:
            - STRIDE methodology and threat categorization
            - Attack surface analysis and decomposition
            - Data flow diagram analysis
            - Trust boundary identification
            - Threat agent profiling and capability assessment
            - Risk rating and prioritization methodologies

            Perform comprehensive STRIDE-based threat modeling with actionable security recommendations.""",
            
            user_prompt_template="""Perform STRIDE threat modeling analysis for:

            SYSTEM ARCHITECTURE:
            {architecture_description}

            DATA FLOW DIAGRAMS:
            {data_flows}

            TRUST BOUNDARIES:
            {trust_boundaries}

            ASSETS AND DATA:
            {assets_inventory}

            THREAT LANDSCAPE:
            - Known Threat Actors: {threat_actors}
            - Industry Threats: {industry_threats}
            - Historical Incidents: {incident_history}

            BUSINESS CONTEXT:
            {business_context}

            Conduct comprehensive threat modeling including:
            1. System decomposition and analysis
            2. STRIDE threat enumeration per component
            3. Attack tree development
            4. Threat agent analysis and motivation
            5. Impact and likelihood assessment
            6. Risk rating and prioritization
            7. Security control recommendations
            8. Threat mitigation strategies
            9. Detection and monitoring requirements
            10. Threat model maintenance procedures""",
            
            variables=[
                "architecture_description", "data_flows", "trust_boundaries", "assets_inventory",
                "threat_actors", "industry_threats", "incident_history", "business_context"
            ],
            
            output_format="""{
                "system_decomposition": {
                    "components": ["web_server", "database", "api_gateway"],
                    "trust_boundaries": ["internet", "dmz", "internal_network"],
                    "data_stores": ["user_data", "transaction_data", "logs"]
                },
                "stride_analysis": [
                    {
                        "component": "web_server",
                        "spoofing": {"threats": ["threat1"], "likelihood": "medium", "impact": "high"},
                        "tampering": {"threats": ["threat2"], "likelihood": "low", "impact": "medium"},
                        "repudiation": {"threats": ["threat3"], "likelihood": "low", "impact": "low"},
                        "information_disclosure": {"threats": ["threat4"], "likelihood": "high", "impact": "high"},
                        "denial_of_service": {"threats": ["threat5"], "likelihood": "medium", "impact": "medium"},
                        "elevation_of_privilege": {"threats": ["threat6"], "likelihood": "low", "impact": "high"}
                    }
                ],
                "attack_trees": {
                    "compromise_user_data": {
                        "root_goal": "access user data",
                        "attack_paths": ["sql_injection", "privilege_escalation"],
                        "leaf_conditions": ["vulnerable_input", "weak_authentication"]
                    }
                },
                "threat_agents": [
                    {
                        "name": "External Hacker",
                        "motivation": "financial_gain",
                        "capability": "intermediate",
                        "resources": "medium",
                        "preferred_attacks": ["web_attacks", "social_engineering"]
                    }
                ],
                "risk_assessment": [
                    {
                        "threat": "SQL Injection",
                        "likelihood": 0.7,
                        "impact": 0.9,
                        "risk_score": 0.63,
                        "priority": "high"
                    }
                ],
                "security_controls": [
                    {
                        "threat": "SQL Injection",
                        "controls": ["input_validation", "parameterized_queries", "waf"],
                        "effectiveness": 0.9,
                        "implementation_cost": "medium"
                    }
                ],
                "monitoring_requirements": ["log_sql_queries", "monitor_failed_logins"],
                "maintenance_schedule": "quarterly_review"
            }""",
            
            model_preferences={
                "openai": {"temperature": 0.4, "max_tokens": 2400},
                "deepseek": {"temperature": 0.5, "max_tokens": 2100}
            },
            
            description="Performs comprehensive STRIDE threat modeling with risk assessment and control recommendations"
        )
    
    def _add_business_impact_templates(self):
        """Add business impact analysis templates"""
        
        self.templates["business_impact_analysis"] = PromptTemplate(
            name="Security Business Impact Analysis",
            category=PromptCategory.BUSINESS_IMPACT,
            system_prompt="""You are a business impact analysis expert specializing in:
            - Financial impact modeling and quantification
            - Business process analysis and dependencies
            - Operational risk assessment
            - Reputation and brand impact evaluation
            - Regulatory and legal consequence analysis
            - Recovery time and cost estimation

            Perform comprehensive business impact analysis for security incidents and vulnerabilities.""",
            
            user_prompt_template="""Analyze business impact for the following security scenario:

            SECURITY INCIDENT/VULNERABILITY:
            {security_scenario}

            BUSINESS PROFILE:
            - Organization: {organization_profile}
            - Annual Revenue: {annual_revenue}
            - Industry: {industry_sector}
            - Geographic Presence: {geography}
            - Customer Base: {customer_base}
            - Critical Business Processes: {critical_processes}

            OPERATIONAL CONTEXT:
            - System Dependencies: {system_dependencies}
            - Data Sensitivity: {data_classification}
            - Regulatory Environment: {regulatory_requirements}
            - Business Continuity Plans: {continuity_plans}
            - Insurance Coverage: {insurance_details}

            Provide comprehensive impact analysis including:
            1. Direct financial impact calculation
            2. Indirect and opportunity cost assessment
            3. Operational disruption analysis
            4. Customer and stakeholder impact
            5. Reputation and brand damage evaluation
            6. Legal and regulatory consequences
            7. Recovery timeline and cost estimation
            8. Long-term business effects
            9. Competitive disadvantage analysis
            10. Stakeholder communication requirements""",
            
            variables=[
                "security_scenario", "organization_profile", "annual_revenue", "industry_sector",
                "geography", "customer_base", "critical_processes", "system_dependencies",
                "data_classification", "regulatory_requirements", "continuity_plans", "insurance_details"
            ],
            
            output_format="""{
                "financial_impact": {
                    "immediate_costs": {
                        "incident_response": 100000,
                        "system_restoration": 250000,
                        "legal_fees": 150000,
                        "regulatory_fines": 500000
                    },
                    "ongoing_costs": {
                        "lost_revenue": 1000000,
                        "customer_compensation": 200000,
                        "increased_security": 300000,
                        "monitoring_costs": 50000
                    },
                    "opportunity_costs": {
                        "delayed_projects": 400000,
                        "lost_partnerships": 300000,
                        "market_share_loss": 800000
                    },
                    "total_estimated_impact": 4050000
                },
                "operational_impact": {
                    "affected_processes": ["order_processing", "customer_service"],
                    "downtime_duration": "72 hours",
                    "productivity_loss": "40%",
                    "recovery_timeline": "2-4 weeks"
                },
                "stakeholder_impact": {
                    "customers": {
                        "affected_count": 50000,
                        "impact_severity": "high",
                        "trust_degradation": "significant"
                    },
                    "employees": {
                        "productivity_impact": "medium",
                        "morale_impact": "high",
                        "additional_workload": "substantial"
                    },
                    "partners": {
                        "relationship_strain": "moderate",
                        "contract_implications": "potential_penalties"
                    },
                    "investors": {
                        "confidence_impact": "negative",
                        "stock_price_impact": "5-15% decline",
                        "rating_implications": "potential_downgrade"
                    }
                },
                "reputation_analysis": {
                    "brand_damage": "significant",
                    "media_coverage": "negative_widespread",
                    "social_media_sentiment": "strongly_negative",
                    "recovery_timeline": "6-18 months",
                    "mitigation_strategies": ["strategy1", "strategy2"]
                },
                "legal_regulatory": {
                    "potential_lawsuits": "class_action_likely",
                    "regulatory_investigations": ["data_protection_authority", "financial_regulator"],
                    "compliance_violations": ["gdpr", "pci_dss"],
                    "estimated_fines": 2000000
                },
                "recovery_analysis": {
                    "immediate_response": "24-48 hours",
                    "system_restoration": "1-2 weeks",
                    "business_normalization": "1-3 months",
                    "full_recovery": "6-12 months",
                    "total_recovery_cost": 1500000
                },
                "competitive_impact": {
                    "market_position": "weakened",
                    "competitor_advantage": "significant",
                    "customer_migration": "15-25%",
                    "market_share_loss": "5-10%"
                },
                "recommendations": {
                    "immediate_actions": ["action1", "action2"],
                    "short_term_strategy": ["strategy1", "strategy2"],
                    "long_term_investments": ["investment1", "investment2"]
                }
            }""",
            
            model_preferences={
                "openai": {"temperature": 0.3, "max_tokens": 2600},
                "deepseek": {"temperature": 0.4, "max_tokens": 2300}
            },
            
            description="Provides comprehensive business impact analysis with financial modeling and stakeholder assessment"
        )
    
    def get_template(self, template_name: str) -> Optional[PromptTemplate]:
        """Get a specific prompt template"""
        return self.templates.get(template_name)
    
    def get_templates_by_category(self, category: PromptCategory) -> List[PromptTemplate]:
        """Get all templates for a specific category"""
        return [template for template in self.templates.values() if template.category == category]
    
    def list_available_templates(self) -> Dict[str, str]:
        """List all available templates with descriptions"""
        return {name: template.description for name, template in self.templates.items()}
    
    def build_prompt(self, template_name: str, variables: Dict[str, Any]) -> Dict[str, str]:
        """Build a complete prompt from template and variables"""
        template = self.get_template(template_name)
        if not template:
            raise ValueError(f"Template '{template_name}' not found")
        
        # Validate required variables
        missing_vars = set(template.variables) - set(variables.keys())
        if missing_vars:
            raise ValueError(f"Missing required variables: {missing_vars}")
        
        # Format the user prompt with variables
        user_prompt = template.user_prompt_template.format(**variables)
        
        return {
            "system_prompt": template.system_prompt,
            "user_prompt": user_prompt,
            "output_format": template.output_format,
            "model_preferences": template.model_preferences
        }