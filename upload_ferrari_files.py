#!/usr/bin/env python3
"""
<PERSON>rip<PERSON> to upload complete Ferrari AI files to the Contabo server
"""

import subprocess
import os
import sys

def upload_file_to_server(local_path, remote_path, server="root@************"):
    """Upload a file to the server using scp"""
    try:
        cmd = f'scp "{local_path}" {server}:"{remote_path}"'
        print(f"Uploading {local_path} to {remote_path}...")
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ Successfully uploaded {local_path}")
            return True
        else:
            print(f"❌ Failed to upload {local_path}: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ Error uploading {local_path}: {e}")
        return False

def main():
    """Upload all Ferrari AI files"""
    base_local = "nexus-backend-deploy/ai"
    base_remote = "nexus-backend/ai"
    
    # List of Ferrari AI files to upload
    ferrari_files = [
        "creative_exploit_engine.py",
        "multi_stage_orchestrator.py", 
        "adaptive_exploit_modifier.py",
        "behavioral_analysis_engine.py",
        "evasion_technique_generator.py"
    ]
    
    print("🚀 Starting Ferrari AI files upload...")
    
    success_count = 0
    for file_name in ferrari_files:
        local_path = os.path.join(base_local, file_name)
        remote_path = os.path.join(base_remote, file_name)
        
        if os.path.exists(local_path):
            if upload_file_to_server(local_path, remote_path):
                success_count += 1
        else:
            print(f"⚠️  Local file not found: {local_path}")
    
    print(f"\n📊 Upload Summary: {success_count}/{len(ferrari_files)} files uploaded successfully")
    
    if success_count == len(ferrari_files):
        print("✅ All Ferrari AI files uploaded successfully!")
        print("\n🔄 Next steps:")
        print("1. SSH to the server")
        print("2. Restart the backend")
        print("3. Test Ferrari AI functionality")
    else:
        print("❌ Some files failed to upload. Please check the errors above.")

if __name__ == "__main__":
    main()
