"""
Nuclei Scanner Integration for NexusScan Desktop Application
Vulnerability scanning using Nuclei templates and custom rules.
"""

import json
import asyncio
import logging
import subprocess
import tempfile
import os
from datetime import datetime
from typing import Dict, List, Optional, Callable, Any
from dataclasses import dataclass

from security.tools.tool_registry import (
    ToolMetadata, ToolCategory, ToolCapabilities,
    ScanOptions, ScanResult, ToolStatus, register_tool
)
from security.tools.base_scanner import BaseScanner

logger = logging.getLogger(__name__)


@dataclass
class NucleiScanOptions(ScanOptions):
    """Nuclei-specific scan options"""
    templates: List[str] = None  # Specific templates to use
    template_tags: List[str] = None  # Template tags to filter by
    template_severity: List[str] = None  # Severity levels to include
    template_directory: str = ""  # Custom template directory
    
    # Scanning options
    rate_limit: int = 150  # Requests per second
    bulk_size: int = 25  # Number of hosts to process in parallel
    concurrency: int = 25  # Template concurrency
    retries: int = 1  # Number of retries on failure
    
    # Output options
    include_raw_requests: bool = False
    include_raw_responses: bool = False
    store_response_dir: str = ""
    
    # Advanced options
    follow_redirects: bool = False
    follow_host_redirects: bool = False
    max_redirects: int = 10
    disable_clustering: bool = False
    custom_headers: Dict[str, str] = None
    variables: Dict[str, str] = None
    
    # Exclusions
    exclude_tags: List[str] = None
    exclude_templates: List[str] = None
    exclude_severity: List[str] = None
    exclude_matchers: List[str] = None

    def __post_init__(self):
        super().__post_init__()
        if self.templates is None:
            self.templates = []
        if self.template_tags is None:
            self.template_tags = []
        if self.template_severity is None:
            self.template_severity = ["critical", "high", "medium"]
        if self.custom_headers is None:
            self.custom_headers = {}
        if self.variables is None:
            self.variables = {}
        if self.exclude_tags is None:
            self.exclude_tags = []
        if self.exclude_templates is None:
            self.exclude_templates = []
        if self.exclude_severity is None:
            self.exclude_severity = []
        if self.exclude_matchers is None:
            self.exclude_matchers = []


@dataclass
class NucleiVulnerability:
    """Nuclei vulnerability finding"""
    template_id: str
    template_name: str
    severity: str
    type: str
    host: str
    matched_at: str
    extracted_results: List[str] = None
    curl_command: str = ""
    request: str = ""
    response: str = ""
    ip: str = ""
    timestamp: str = ""
    matcher_status: bool = True
    
    def __post_init__(self):
        if self.extracted_results is None:
            self.extracted_results = []


@register_tool
class NucleiScanner(BaseScanner):
    """Nuclei vulnerability scanner implementation"""

    def __init__(self):
        """Initialize Nuclei scanner"""
        self.nuclei_binary = None
        self.templates_path = None
        super().__init__()

    def get_metadata(self) -> ToolMetadata:
        """Get Nuclei tool metadata"""
        return ToolMetadata(
            name="nuclei",
            display_name="Nuclei Vulnerability Scanner",
            description="Fast and customizable vulnerability scanner based on simple YAML templates",
            version="1.0.0",
            category=ToolCategory.VULNERABILITY_SCANNER,
            author="ProjectDiscovery",
            website="https://nuclei.projectdiscovery.io/",
            documentation="https://docs.projectdiscovery.io/tools/nuclei/",
            capabilities=ToolCapabilities(
                supports_async=True,
                supports_progress=True,
                supports_cancellation=True,
                requires_root=False,
                network_access_required=True,
                output_formats=["json", "yaml", "txt"],
                supported_targets=["url", "ip", "domain", "file"]
            ),
            default_options={
                "rate_limit": 150,
                "concurrency": 25,
                "template_severity": ["critical", "high", "medium"],
                "retries": 1
            },
            required_dependencies=["nuclei"]
        )

    def check_native_availability(self) -> bool:
        """Check if Nuclei is available"""
        try:
            # Try to find nuclei binary
            result = subprocess.run(
                ["nuclei", "-version"], 
                capture_output=True, 
                text=True, 
                timeout=10
            )
            
            if result.returncode == 0:
                self.nuclei_binary = "nuclei"
                logger.info(f"Nuclei found: {result.stdout.strip()}")
                
                # Check for templates
                self._check_templates()
                return True
            else:
                logger.error("Nuclei binary not found or not working")
                return False
                
        except FileNotFoundError:
            logger.error("Nuclei binary not found in PATH")
            return False
        except subprocess.TimeoutExpired:
            logger.error("Nuclei version check timed out")
            return False
        except Exception as e:
            logger.error(f"Nuclei availability check failed: {e}")
            return False

    def _check_templates(self):
        """Check and update Nuclei templates"""
        try:
            # Try to update templates
            result = subprocess.run(
                ["nuclei", "-update-templates"],
                capture_output=True,
                text=True,
                timeout=60
            )
            
            if result.returncode == 0:
                logger.info("Nuclei templates updated successfully")
            else:
                logger.warning("Failed to update Nuclei templates, using existing")
                
        except Exception as e:
            logger.warning(f"Template update failed: {e}")

    async def execute_native_scan(self, options: ScanOptions,
                                  progress_callback: Optional[Callable[[float, str], None]] = None) -> ScanResult:
        """Execute Nuclei scan"""
        if not isinstance(options, NucleiScanOptions):
            # Convert generic options to Nuclei options
            nuclei_options = NucleiScanOptions(
                target=options.target,
                timeout=options.timeout,
                threads=options.threads,
                output_format=options.output_format
            )
        else:
            nuclei_options = options

        start_time = datetime.now()
        
        if progress_callback:
            progress_callback(0.0, "Initializing Nuclei scan...")

        try:
            # Build Nuclei command
            command = self._build_nuclei_command(nuclei_options)
            
            if progress_callback:
                progress_callback(0.1, "Starting vulnerability scan...")

            # Execute scan
            scan_output = await self._execute_nuclei_scan(command, progress_callback)
            
            if progress_callback:
                progress_callback(0.8, "Parsing scan results...")

            # Parse results
            parsed_results = self._parse_scan_results(scan_output)
            
            if progress_callback:
                progress_callback(0.9, "Processing vulnerabilities...")

            # Extract vulnerabilities
            vulnerabilities = self._extract_vulnerabilities(parsed_results)

            if progress_callback:
                progress_callback(1.0, "Scan completed")

            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()

            return ScanResult(
                tool_name="nuclei",
                target=nuclei_options.target,
                status=ToolStatus.COMPLETED,
                start_time=start_time.isoformat(),
                end_time=end_time.isoformat(),
                duration_seconds=duration,
                raw_output=scan_output,
                parsed_results=parsed_results,
                vulnerabilities=vulnerabilities,
                metadata={
                    "nuclei_version": self._get_nuclei_version(),
                    "templates_used": len(nuclei_options.templates) if nuclei_options.templates else "default",
                    "vulnerabilities_found": len(vulnerabilities)
                }
            )

        except Exception as e:
            logger.error(f"Nuclei scan failed: {e}")
            return ScanResult(
                tool_name="nuclei",
                target=nuclei_options.target,
                status=ToolStatus.FAILED,
                start_time=start_time.isoformat(),
                errors=[str(e)]
            )

    def _build_nuclei_command(self, options: NucleiScanOptions) -> List[str]:
        """Build Nuclei command arguments"""
        command = [self.nuclei_binary]
        
        # Target
        if options.target.startswith(('http://', 'https://')):
            command.extend(["-u", options.target])
        else:
            # Assume it's a host/IP, add protocol
            command.extend(["-u", f"http://{options.target}"])
        
        # Output format
        command.extend(["-json"])
        
        # Rate limiting
        command.extend(["-rate-limit", str(options.rate_limit)])
        command.extend(["-bulk-size", str(options.bulk_size)])
        command.extend(["-c", str(options.concurrency)])
        
        # Retries
        command.extend(["-retries", str(options.retries)])
        
        # Templates
        if options.templates:
            for template in options.templates:
                command.extend(["-t", template])
        
        # Template tags
        if options.template_tags:
            command.extend(["-tags", ",".join(options.template_tags)])
        
        # Severity filtering
        if options.template_severity:
            command.extend(["-severity", ",".join(options.template_severity)])
        
        # Template directory
        if options.template_directory:
            command.extend(["-t", options.template_directory])
        
        # Exclusions
        if options.exclude_tags:
            command.extend(["-exclude-tags", ",".join(options.exclude_tags)])
        
        if options.exclude_templates:
            for template in options.exclude_templates:
                command.extend(["-exclude-templates", template])
        
        if options.exclude_severity:
            command.extend(["-exclude-severity", ",".join(options.exclude_severity)])
        
        # Advanced options
        if options.follow_redirects:
            command.append("-follow-redirects")
        
        if options.follow_host_redirects:
            command.append("-follow-host-redirects")
        
        if options.max_redirects != 10:
            command.extend(["-max-redirects", str(options.max_redirects)])
        
        if options.disable_clustering:
            command.append("-disable-clustering")
        
        # Custom headers
        if options.custom_headers:
            for key, value in options.custom_headers.items():
                command.extend(["-H", f"{key}: {value}"])
        
        # Variables
        if options.variables:
            for key, value in options.variables.items():
                command.extend(["-var", f"{key}={value}"])
        
        # Response options
        if options.include_raw_requests:
            command.append("-include-rr")
        
        if options.store_response_dir:
            command.extend(["-store-resp", options.store_response_dir])
        
        # Timeout
        if options.timeout:
            command.extend(["-timeout", str(options.timeout)])
        
        # Disable colors and interactive mode
        command.extend(["-no-color", "-silent"])
        
        return command

    async def _execute_nuclei_scan(self, command: List[str], 
                                  progress_callback: Optional[Callable[[float, str], None]] = None) -> str:
        """Execute the Nuclei scan command"""
        try:
            if progress_callback:
                progress_callback(0.2, f"Running: {' '.join(command[:5])}...")

            # Execute scan
            try:
                loop = asyncio.get_running_loop()
            except RuntimeError:
                # No running loop, create one
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
            
            result = await loop.run_in_executor(
                None, self._run_subprocess, command
            )
            
            if progress_callback:
                progress_callback(0.7, "Scan execution completed")

            return result

        except Exception as e:
            logger.error(f"Nuclei execution failed: {e}")
            raise

    def _run_subprocess(self, command: List[str]) -> str:
        """Run subprocess synchronously"""
        try:
            result = subprocess.run(
                command,
                capture_output=True,
                text=True,
                timeout=300  # 5 minutes timeout
            )
            
            if result.returncode != 0 and result.stderr:
                logger.warning(f"Nuclei stderr: {result.stderr}")
            
            return result.stdout
            
        except subprocess.TimeoutExpired:
            logger.error("Nuclei scan timed out")
            raise Exception("Scan timed out")
        except Exception as e:
            logger.error(f"Subprocess execution failed: {e}")
            raise

    def _parse_scan_results(self, output: str) -> Dict[str, Any]:
        """Parse Nuclei JSON output into frontend-ready structured format"""
        findings = []
        
        if output.strip():
            # Parse JSON lines
            for line in output.strip().split('\n'):
                if line.strip():
                    try:
                        finding = json.loads(line)
                        findings.append(finding)
                    except json.JSONDecodeError as e:
                        logger.warning(f"Failed to parse JSON line: {e}")
                        continue
        
        # Count by severity
        severity_counts = {"critical": 0, "high": 0, "medium": 0, "low": 0, "info": 0}
        for finding in findings:
            severity = finding.get("info", {}).get("severity", "info").lower()
            if severity in severity_counts:
                severity_counts[severity] += 1
        
        # Create template statistics
        template_stats = self._create_template_statistics(findings)
        
        # Frontend-ready data structure matching tools-based-frontend.md specs
        return {
            "scan_info": {
                "scanner": "nuclei",
                "scan_type": "vulnerability_scan",
                "timestamp": datetime.now().isoformat(),
                "template_count": self._get_total_template_count(),
                "last_update": self._get_template_last_update(),
                "status": "completed",
                "scan_configuration": self._get_scan_configuration_info()
            },
            
            # Raw findings data
            "vulnerabilities": findings,
            "detailed_findings": self._create_detailed_findings(findings),
            
            # Frontend visualization data
            "vulnerability_grid": self._create_vulnerability_grid(findings),
            "severity_chart_data": self._create_severity_chart_data(severity_counts),
            "template_coverage": self._create_template_coverage(findings),
            "live_results": self._format_live_results(findings),
            
            # Statistics and progress data
            "statistics": {
                "total_findings": len(findings),
                "severity_counts": severity_counts,
                "template_stats": template_stats,
                "scan_coverage": self._calculate_scan_coverage(findings)
            },
            
            # Progress indicator data
            "progress_data": {
                "templates_run": len(set(f.get("template-id", "") for f in findings)),
                "total_templates": self._get_total_template_count(),
                "vulnerabilities_found": len(findings),
                "completion_percentage": 100,  # Scan is complete
                "current_phase": "Analysis Complete"
            },
            
            # Export options data
            "export_formats": {
                "json": self._prepare_json_export(findings),
                "sarif": self._prepare_sarif_export(findings),
                "csv": self._prepare_csv_export(findings),
                "html": self._prepare_html_export_data(findings)
            },
            
            # Configuration data for frontend
            "template_filters": {
                "severity": ["critical", "high", "medium", "low", "info"],
                "tags": self._get_available_tags(),
                "categories": ["cve", "exposures", "technologies", "takeovers", "vulnerabilities"]
            },
            
            "scan_options": {
                "rate_limit": 150,
                "concurrency": 25,
                "timeout": 300,
                "custom_headers": {},
                "bulk_scan": False
            }
        }

    def _extract_vulnerabilities(self, parsed_results: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Extract vulnerabilities from parsed results"""
        vulnerabilities = []
        
        for finding in parsed_results.get("vulnerabilities", []):
            try:
                info = finding.get("info", {})
                
                vulnerability = {
                    "id": f"nuclei_{info.get('name', 'unknown')}_{finding.get('host', 'unknown')}",
                    "type": "nuclei_template",
                    "severity": info.get("severity", "info").lower(),
                    "title": info.get("name", "Unknown Vulnerability"),
                    "description": info.get("description", ""),
                    "target": finding.get("host", ""),
                    "template_id": finding.get("template-id", ""),
                    "template_path": finding.get("template-path", ""),
                    "matched_at": finding.get("matched-at", ""),
                    "extracted_results": finding.get("extracted-results", []),
                    "evidence": {
                        "finding_data": finding,
                        "detection_method": "nuclei_template_scan",
                        "template_info": info,
                        "matcher_name": finding.get("matcher-name", ""),
                        "matcher_status": finding.get("matcher-status", True)
                    },
                    "remediation": self._get_remediation_for_template(info),
                    "references": info.get("reference", []) if isinstance(info.get("reference"), list) else [info.get("reference")] if info.get("reference") else [],
                    "tags": info.get("tags", []),
                    "classification": info.get("classification", {}),
                    "metadata": info.get("metadata", {})
                }
                
                # Add CVE information if available
                cve_ids = []
                if "cve-id" in info:
                    cve_ids = info["cve-id"] if isinstance(info["cve-id"], list) else [info["cve-id"]]
                elif "classification" in info and "cve-id" in info["classification"]:
                    classification = info["classification"]
                    cve_ids = classification["cve-id"] if isinstance(classification["cve-id"], list) else [classification["cve-id"]]
                
                if cve_ids:
                    vulnerability["cve_id"] = cve_ids[0]  # Use first CVE ID
                    vulnerability["cve_ids"] = cve_ids
                
                # Add CVSS score if available
                if "classification" in info and "cvss-score" in info["classification"]:
                    vulnerability["cvss_score"] = info["classification"]["cvss-score"]
                
                vulnerabilities.append(vulnerability)
                
            except Exception as e:
                logger.error(f"Failed to process vulnerability: {e}")
                continue
        
        return vulnerabilities

    def _get_remediation_for_template(self, template_info: Dict[str, Any]) -> str:
        """Get remediation advice for template"""
        template_name = template_info.get("name", "").lower()
        severity = template_info.get("severity", "").lower()
        
        # Generic remediation based on template category
        if "xss" in template_name:
            return "Implement proper input validation and output encoding to prevent XSS attacks"
        elif "sql" in template_name or "injection" in template_name:
            return "Use parameterized queries and input validation to prevent injection attacks"
        elif "rce" in template_name or "command" in template_name:
            return "Implement strict input validation and avoid executing user-controlled commands"
        elif "lfi" in template_name or "file" in template_name:
            return "Implement proper file path validation and access controls"
        elif "config" in template_name or "exposure" in template_name:
            return "Secure configuration files and restrict access to sensitive information"
        elif severity in ["critical", "high"]:
            return "This is a high-priority vulnerability that should be addressed immediately"
        else:
            return "Review the vulnerability details and apply appropriate security measures"

    def _get_nuclei_version(self) -> str:
        """Get Nuclei version"""
        try:
            result = subprocess.run(
                ["nuclei", "-version"],
                capture_output=True,
                text=True,
                timeout=5
            )
            if result.returncode == 0:
                return result.stdout.strip()
        except Exception:
            pass
        return "unknown"

    def get_scan_presets(self) -> Dict[str, NucleiScanOptions]:
        """Get predefined scan presets"""
        return {
            "quick_scan": NucleiScanOptions(
                target="",
                template_severity=["critical", "high"],
                rate_limit=100,
                concurrency=15,
                template_tags=["exposure", "rce", "sqli"]
            ),
            "comprehensive_scan": NucleiScanOptions(
                target="",
                template_severity=["critical", "high", "medium"],
                rate_limit=150,
                concurrency=25,
                retries=2
            ),
            "web_app_scan": NucleiScanOptions(
                target="",
                template_tags=["xss", "sqli", "lfi", "rfi", "ssrf"],
                template_severity=["critical", "high", "medium"],
                rate_limit=100,
                concurrency=20
            ),
            "cve_scan": NucleiScanOptions(
                target="",
                template_tags=["cve"],
                template_severity=["critical", "high"],
                rate_limit=80,
                concurrency=15,
                retries=1
            ),
            "exposure_scan": NucleiScanOptions(
                target="",
                template_tags=["exposure", "config", "default-login"],
                template_severity=["critical", "high", "medium"],
                rate_limit=120,
                concurrency=20
            )
        }

    def get_available_templates(self) -> Dict[str, List[str]]:
        """Get available Nuclei templates by category"""
        # This would ideally scan the templates directory
        # For now, return common categories
        return {
            "cves": ["CVE-based templates for known vulnerabilities"],
            "exposures": ["Configuration and information disclosure"],
            "technologies": ["Technology-specific templates"],
            "takeovers": ["Subdomain takeover templates"],
            "vulnerabilities": ["Generic vulnerability templates"],
            "workflows": ["Multi-step vulnerability workflows"],
            "file": ["File-based vulnerability checks"],
            "network": ["Network service vulnerabilities"],
            "dns": ["DNS-related security checks"],
            "headless": ["Browser-based testing templates"]
        }

    # Frontend Helper Methods for tools-based-frontend.md Interface

    def _create_template_statistics(self, findings: List[Dict]) -> Dict:
        """Create template usage statistics"""
        template_usage = {}
        template_categories = {}
        
        for finding in findings:
            template_id = finding.get("template-id", "unknown")
            template_info = finding.get("info", {})
            
            # Count template usage
            template_usage[template_id] = template_usage.get(template_id, 0) + 1
            
            # Categorize templates
            tags = template_info.get("tags", [])
            for tag in tags:
                template_categories[tag] = template_categories.get(tag, 0) + 1
        
        return {
            "templates_executed": len(template_usage),
            "template_usage": template_usage,
            "category_distribution": template_categories,
            "most_active_templates": dict(sorted(template_usage.items(), key=lambda x: x[1], reverse=True)[:10])
        }

    def _get_total_template_count(self) -> int:
        """Get total number of available templates"""
        try:
            # Try to get template count from nuclei
            result = subprocess.run(
                ["nuclei", "-templates", "-list"],
                capture_output=True,
                text=True,
                timeout=10
            )
            if result.returncode == 0:
                return len(result.stdout.strip().split('\n'))
        except Exception:
            pass
        return 4000  # Default estimate

    def _get_template_last_update(self) -> str:
        """Get last template update time"""
        # This would check template directory modification time
        return datetime.now().isoformat()

    def _get_scan_configuration_info(self) -> Dict:
        """Get scan configuration information"""
        return {
            "default_rate_limit": 150,
            "max_concurrency": 25,
            "available_severities": ["critical", "high", "medium", "low", "info"],
            "template_categories": list(self.get_available_templates().keys()),
            "supported_export_formats": ["JSON", "SARIF", "CSV", "HTML"]
        }

    def _create_detailed_findings(self, findings: List[Dict]) -> List[Dict]:
        """Create detailed findings for frontend display"""
        detailed_findings = []
        
        for finding in findings:
            info = finding.get("info", {})
            detailed = {
                "id": f"nuclei_{info.get('name', 'unknown')}_{finding.get('host', 'unknown')}",
                "template_id": finding.get("template-id", ""),
                "template_name": info.get("name", "Unknown"),
                "severity": info.get("severity", "info").lower(),
                "severity_color": self._get_severity_color(info.get("severity", "info")),
                "target": finding.get("host", ""),
                "matched_at": finding.get("matched-at", ""),
                "description": info.get("description", ""),
                "tags": info.get("tags", []),
                "references": info.get("reference", []) if isinstance(info.get("reference"), list) else [info.get("reference")] if info.get("reference") else [],
                "classification": info.get("classification", {}),
                "extracted_results": finding.get("extracted-results", []),
                "timestamp": datetime.now().isoformat(),
                "template_path": finding.get("template-path", ""),
                "matcher_name": finding.get("matcher-name", ""),
                "curl_command": finding.get("curl-command", ""),
                "metadata": info.get("metadata", {}),
                "risk_score": self._calculate_risk_score(info.get("severity", "info"), info.get("tags", []))
            }
            detailed_findings.append(detailed)
        
        return detailed_findings

    def _get_severity_color(self, severity: str) -> str:
        """Get color code for severity level"""
        severity_colors = {
            "critical": "#ef4444",  # Red
            "high": "#f97316",      # Orange
            "medium": "#eab308",    # Yellow
            "low": "#16a34a",       # Green
            "info": "#3b82f6"       # Blue
        }
        return severity_colors.get(severity.lower(), "#6b7280")  # Gray default

    def _calculate_risk_score(self, severity: str, tags: List[str]) -> int:
        """Calculate numeric risk score for sorting"""
        severity_scores = {
            "critical": 100,
            "high": 75,
            "medium": 50,
            "low": 25,
            "info": 10
        }
        
        base_score = severity_scores.get(severity.lower(), 10)
        
        # Add risk modifiers based on tags
        high_risk_tags = ["rce", "sqli", "xss", "lfi", "rfi", "auth-bypass"]
        modifier = sum(5 for tag in tags if tag.lower() in high_risk_tags)
        
        return min(100, base_score + modifier)

    def _create_vulnerability_grid(self, findings: List[Dict]) -> List[Dict]:
        """Create vulnerability grid data for frontend table"""
        grid_data = []
        
        for finding in findings:
            info = finding.get("info", {})
            grid_item = {
                "id": f"nuclei_{info.get('name', 'unknown')}_{finding.get('host', 'unknown')}",
                "template_id": finding.get("template-id", ""),
                "name": info.get("name", "Unknown"),
                "severity": info.get("severity", "info").lower(),
                "severity_numeric": self._get_severity_numeric(info.get("severity", "info")),
                "target": finding.get("host", ""),
                "matched_at": finding.get("matched-at", ""),
                "tags": ", ".join(info.get("tags", [])),
                "template_path": finding.get("template-path", ""),
                "timestamp": datetime.now().isoformat(),
                "status": "confirmed",
                "risk_score": self._calculate_risk_score(info.get("severity", "info"), info.get("tags", [])),
                "cve_ids": self._extract_cve_ids(info),
                "references_count": len(info.get("reference", []) if isinstance(info.get("reference"), list) else [info.get("reference")] if info.get("reference") else [])
            }
            grid_data.append(grid_item)
        
        # Sort by severity and risk score
        grid_data.sort(key=lambda x: (x["severity_numeric"], x["risk_score"]), reverse=True)
        return grid_data

    def _get_severity_numeric(self, severity: str) -> int:
        """Get numeric value for severity (for sorting)"""
        severity_values = {
            "critical": 5,
            "high": 4,
            "medium": 3,
            "low": 2,
            "info": 1
        }
        return severity_values.get(severity.lower(), 1)

    def _extract_cve_ids(self, info: Dict) -> List[str]:
        """Extract CVE IDs from template info"""
        cve_ids = []
        
        # Check direct cve-id field
        if "cve-id" in info:
            cve_ids = info["cve-id"] if isinstance(info["cve-id"], list) else [info["cve-id"]]
        
        # Check classification
        elif "classification" in info and "cve-id" in info["classification"]:
            classification = info["classification"]
            cve_ids = classification["cve-id"] if isinstance(classification["cve-id"], list) else [classification["cve-id"]]
        
        return cve_ids

    def _create_severity_chart_data(self, severity_counts: Dict) -> Dict:
        """Create severity chart data for frontend donut chart"""
        total = sum(severity_counts.values())
        
        chart_data = {
            "labels": ["Critical", "High", "Medium", "Low", "Info"],
            "datasets": [{
                "data": [
                    severity_counts.get("critical", 0),
                    severity_counts.get("high", 0),
                    severity_counts.get("medium", 0),
                    severity_counts.get("low", 0),
                    severity_counts.get("info", 0)
                ],
                "backgroundColor": [
                    "#ef4444",  # Critical - Red
                    "#f97316",  # High - Orange
                    "#eab308",  # Medium - Yellow
                    "#16a34a",  # Low - Green
                    "#3b82f6"   # Info - Blue
                ],
                "borderWidth": 2,
                "borderColor": "#1f2937"
            }],
            "total": total,
            "distribution": {
                "critical_percentage": (severity_counts.get("critical", 0) / max(1, total)) * 100,
                "high_percentage": (severity_counts.get("high", 0) / max(1, total)) * 100,
                "medium_percentage": (severity_counts.get("medium", 0) / max(1, total)) * 100,
                "low_percentage": (severity_counts.get("low", 0) / max(1, total)) * 100,
                "info_percentage": (severity_counts.get("info", 0) / max(1, total)) * 100
            }
        }
        
        return chart_data

    def _create_template_coverage(self, findings: List[Dict]) -> Dict:
        """Create template coverage heatmap data"""
        category_coverage = {}
        template_categories = ["cve", "exposures", "technologies", "takeovers", "vulnerabilities", "workflows"]
        
        for finding in findings:
            info = finding.get("info", {})
            tags = info.get("tags", [])
            
            for category in template_categories:
                if category in tags:
                    category_coverage[category] = category_coverage.get(category, 0) + 1
        
        # Create heatmap data
        heatmap_data = []
        for category in template_categories:
            count = category_coverage.get(category, 0)
            intensity = min(1.0, count / 10)  # Normalize to 0-1 scale
            
            heatmap_data.append({
                "category": category,
                "count": count,
                "intensity": intensity,
                "color_intensity": f"rgba(59, 130, 246, {intensity})"  # Blue with variable alpha
            })
        
        return {
            "categories": template_categories,
            "coverage_data": heatmap_data,
            "total_categories_tested": len([c for c in category_coverage.values() if c > 0]),
            "most_active_category": max(category_coverage.items(), key=lambda x: x[1]) if category_coverage else ("none", 0)
        }

    def _format_live_results(self, findings: List[Dict]) -> List[Dict]:
        """Format results for real-time display"""
        live_results = []
        
        for finding in findings:
            info = finding.get("info", {})
            live_result = {
                "id": f"live_{len(live_results)}",
                "timestamp": datetime.now().isoformat(),
                "severity": info.get("severity", "info").lower(),
                "template_name": info.get("name", "Unknown"),
                "target": finding.get("host", ""),
                "matched_at": finding.get("matched-at", ""),
                "template_id": finding.get("template-id", ""),
                "tags": info.get("tags", []),
                "status": "new",
                "animation_class": "slide-in-right"  # For frontend animations
            }
            live_results.append(live_result)
        
        # Sort by timestamp (most recent first)
        live_results.sort(key=lambda x: x["timestamp"], reverse=True)
        return live_results

    def _calculate_scan_coverage(self, findings: List[Dict]) -> Dict:
        """Calculate scan coverage metrics"""
        template_ids = set(finding.get("template-id", "") for finding in findings)
        tags_covered = set()
        
        for finding in findings:
            info = finding.get("info", {})
            tags_covered.update(info.get("tags", []))
        
        return {
            "templates_used": len(template_ids),
            "estimated_total_templates": self._get_total_template_count(),
            "coverage_percentage": (len(template_ids) / max(1, self._get_total_template_count())) * 100,
            "tags_covered": len(tags_covered),
            "categories_tested": len(tags_covered.intersection({"cve", "exposures", "technologies", "takeovers", "vulnerabilities"}))
        }

    def _prepare_json_export(self, findings: List[Dict]) -> Dict:
        """Prepare JSON export data"""
        return {
            "export_format": "nuclei_json",
            "scan_timestamp": datetime.now().isoformat(),
            "scanner": "nuclei",
            "total_findings": len(findings),
            "findings": findings,
            "export_metadata": {
                "exported_by": "nexusscan",
                "export_version": "1.0"
            }
        }

    def _prepare_sarif_export(self, findings: List[Dict]) -> Dict:
        """Prepare SARIF export data"""
        sarif_runs = []
        
        for finding in findings:
            info = finding.get("info", {})
            sarif_result = {
                "ruleId": finding.get("template-id", "unknown"),
                "message": {
                    "text": info.get("description", info.get("name", "Unknown vulnerability"))
                },
                "level": self._map_severity_to_sarif(info.get("severity", "info")),
                "locations": [{
                    "physicalLocation": {
                        "artifactLocation": {
                            "uri": finding.get("host", "")
                        }
                    }
                }],
                "properties": {
                    "tags": info.get("tags", []),
                    "matched_at": finding.get("matched-at", ""),
                    "template_path": finding.get("template-path", "")
                }
            }
            sarif_runs.append(sarif_result)
        
        return {
            "version": "2.1.0",
            "runs": [{
                "tool": {
                    "driver": {
                        "name": "nuclei",
                        "version": self._get_nuclei_version()
                    }
                },
                "results": sarif_runs
            }]
        }

    def _map_severity_to_sarif(self, severity: str) -> str:
        """Map Nuclei severity to SARIF level"""
        mapping = {
            "critical": "error",
            "high": "error",
            "medium": "warning",
            "low": "note",
            "info": "note"
        }
        return mapping.get(severity.lower(), "note")

    def _prepare_csv_export(self, findings: List[Dict]) -> str:
        """Prepare CSV export data"""
        csv_lines = ["Template ID,Template Name,Severity,Target,Matched At,Tags,Description"]
        
        for finding in findings:
            info = finding.get("info", {})
            csv_line = f"{finding.get('template-id', '')},{info.get('name', 'Unknown')},{info.get('severity', 'info')},{finding.get('host', '')},{finding.get('matched-at', '')},\"{', '.join(info.get('tags', []))}\",\"{info.get('description', '').replace('\"', '\"\"')}\""
            csv_lines.append(csv_line)
        
        return "\n".join(csv_lines)

    def _prepare_html_export_data(self, findings: List[Dict]) -> Dict:
        """Prepare HTML export data structure"""
        return {
            "title": "Nuclei Vulnerability Scan Report",
            "timestamp": datetime.now().isoformat(),
            "summary": {
                "total_findings": len(findings),
                "severity_distribution": self._create_severity_chart_data(
                    {"critical": 0, "high": 0, "medium": 0, "low": 0, "info": 0}
                )["distribution"],
                "templates_used": len(set(f.get("template-id", "") for f in findings))
            },
            "findings": self._create_detailed_findings(findings),
            "template_coverage": self._create_template_coverage(findings)
        }

    def _get_available_tags(self) -> List[str]:
        """Get list of available template tags"""
        # Common Nuclei template tags
        return [
            "cve", "exposure", "config", "rce", "sqli", "xss", "lfi", "rfi", 
            "ssrf", "auth-bypass", "default-login", "misconfig", "file", 
            "network", "dns", "takeover", "tech", "cms", "ecommerce",
            "iot", "intrusive", "dos", "redirect", "plugin"
        ]

    def get_frontend_interface_data(self) -> Dict[str, Any]:
        """Get comprehensive frontend interface data matching tools-based-frontend.md specifications"""
        return {
            "header": {
                "title": "Nuclei Vulnerability Scanner",
                "subtitle": "Fast & Customizable Vulnerability Detection",
                "description": "Community-powered vulnerability scanner with 9000+ templates for modern applications",
                "icon": "bug-report",
                "status": "Active",
                "version": "3.2.8",
                "template_count": "9000+"
            },
            "educational_content": {
                "overview": "Nuclei is a fast, community-driven vulnerability scanner that uses YAML-based templates to detect security issues across web applications, networks, and infrastructure. It's designed for modern DevSecOps workflows.",
                "use_cases": [
                    "Automated security testing in CI/CD pipelines",
                    "Continuous vulnerability assessment",
                    "Bug bounty reconnaissance and testing",
                    "Security compliance verification",
                    "Zero-day vulnerability detection using custom templates"
                ],
                "learning_resources": [
                    {
                        "title": "Nuclei Documentation",
                        "url": "https://docs.nuclei.sh/",
                        "type": "documentation"
                    },
                    {
                        "title": "Nuclei Templates",
                        "url": "https://github.com/projectdiscovery/nuclei-templates",
                        "type": "repository"
                    },
                    {
                        "title": "Creating Custom Templates",
                        "url": "https://docs.nuclei.sh/template-guides/",
                        "type": "guide"
                    }
                ],
                "best_practices": [
                    "Always get proper authorization before scanning targets",
                    "Use rate limiting to avoid overwhelming target systems",
                    "Start with info/low severity templates for initial reconnaissance",
                    "Regularly update nuclei and templates for latest coverage",
                    "Create custom templates for organization-specific vulnerabilities"
                ]
            },
            "scan_interface": {
                "target_input": {
                    "label": "Target URL",
                    "placeholder": "https://example.com, http://********:8080",
                    "examples": [
                        "https://example.com - Single URL",
                        "https://api.example.com - API endpoint",
                        "http://*************:8080 - IP with port",
                        "@urls.txt - URL list from file"
                    ],
                    "validation": "HTTP/HTTPS URL or file path with @"
                },
                "template_selection": {
                    "quick_scans": {
                        "web_vulnerabilities": {
                            "name": "Web Vulnerabilities",
                            "description": "Common web application security issues",
                            "tags": ["xss", "sqli", "lfi", "rfi", "ssrf"],
                            "template_count": "450+",
                            "estimated_time": "5-10 minutes"
                        },
                        "misconfigurations": {
                            "name": "Misconfigurations",
                            "description": "Configuration issues and exposures",
                            "tags": ["config", "exposure", "misconfig"],
                            "template_count": "800+",
                            "estimated_time": "3-7 minutes"
                        },
                        "cve_detection": {
                            "name": "CVE Detection",
                            "description": "Known CVE vulnerabilities",
                            "tags": ["cve"],
                            "template_count": "2500+",
                            "estimated_time": "10-20 minutes"
                        },
                        "tech_detection": {
                            "name": "Technology Detection",
                            "description": "Identify technologies and versions",
                            "tags": ["tech", "detect"],
                            "template_count": "300+",
                            "estimated_time": "2-5 minutes"
                        }
                    },
                    "severity_filters": [
                        {"value": "critical", "label": "Critical", "color": "#dc2626"},
                        {"value": "high", "label": "High", "color": "#ea580c"},
                        {"value": "medium", "label": "Medium", "color": "#ca8a04"},
                        {"value": "low", "label": "Low", "color": "#16a34a"},
                        {"value": "info", "label": "Info", "color": "#2563eb"}
                    ],
                    "tag_categories": {
                        "vulnerability_types": ["xss", "sqli", "lfi", "rfi", "ssrf", "rce", "auth-bypass"],
                        "technologies": ["apache", "nginx", "wordpress", "drupal", "jenkins", "docker"],
                        "attack_vectors": ["file", "network", "dns", "subdomain-takeover"],
                        "compliance": ["owasp", "cwe", "pci", "hipaa"]
                    }
                },
                "advanced_options": {
                    "performance": {
                        "rate_limit": {
                            "label": "Rate Limit (req/sec)",
                            "default": 150,
                            "range": [1, 1000],
                            "description": "Number of requests per second"
                        },
                        "concurrency": {
                            "label": "Template Concurrency",
                            "default": 25,
                            "range": [1, 100],
                            "description": "Number of templates to run in parallel"
                        },
                        "bulk_size": {
                            "label": "Bulk Size",
                            "default": 25,
                            "range": [1, 100],
                            "description": "Number of hosts to process in parallel"
                        }
                    },
                    "request_options": {
                        "follow_redirects": {
                            "label": "Follow Redirects",
                            "type": "boolean",
                            "default": False
                        },
                        "max_redirects": {
                            "label": "Max Redirects",
                            "default": 10,
                            "range": [1, 50]
                        },
                        "custom_headers": {
                            "label": "Custom Headers",
                            "type": "key_value_pairs",
                            "placeholder": "User-Agent: Custom Scanner"
                        }
                    }
                }
            },
            "results_interface": {
                "vulnerability_dashboard": {
                    "title": "Vulnerability Overview",
                    "description": "High-level summary of detected vulnerabilities",
                    "widgets": [
                        {"type": "severity_distribution", "title": "Findings by Severity"},
                        {"type": "vulnerability_types", "title": "Vulnerability Categories"},
                        {"type": "timeline", "title": "Discovery Timeline"},
                        {"type": "risk_score", "title": "Overall Risk Score"}
                    ]
                },
                "findings_table": {
                    "title": "Detailed Findings",
                    "description": "Comprehensive list of all detected vulnerabilities",
                    "columns": [
                        {"key": "severity", "label": "Severity", "sortable": True, "filterable": True},
                        {"key": "template_name", "label": "Vulnerability", "sortable": True},
                        {"key": "target", "label": "Target", "sortable": True},
                        {"key": "template_id", "label": "Template ID", "sortable": True},
                        {"key": "tags", "label": "Tags", "filterable": True},
                        {"key": "matched_at", "label": "Evidence", "sortable": False},
                        {"key": "cvss_score", "label": "CVSS", "sortable": True}
                    ],
                    "grouping": ["severity", "template_id", "target"],
                    "export_options": ["json", "csv", "html", "sarif"]
                },
                "template_coverage": {
                    "title": "Template Coverage",
                    "description": "Analysis of templates used and coverage achieved",
                    "sections": [
                        "templates_executed",
                        "template_categories",
                        "success_rate",
                        "skipped_templates"
                    ]
                },
                "vulnerability_details": {
                    "title": "Vulnerability Details",
                    "description": "In-depth analysis of individual findings",
                    "sections": [
                        "description",
                        "impact_assessment",
                        "exploitation_details",
                        "remediation_steps",
                        "references"
                    ]
                }
            },
            "real_time_features": {
                "live_scanning": {
                    "show_current_template": True,
                    "show_findings_counter": True,
                    "show_progress_percentage": True,
                    "show_rate_statistics": True
                },
                "streaming_results": {
                    "immediate_finding_alerts": True,
                    "severity_based_notifications": True,
                    "template_completion_updates": True
                }
            },
            "integration_capabilities": {
                "template_management": {
                    "title": "Template Management",
                    "features": [
                        "browse_community_templates",
                        "create_custom_templates", 
                        "template_validation",
                        "template_sharing"
                    ]
                },
                "automation": {
                    "title": "Automation Features",
                    "features": [
                        "scheduled_scans",
                        "ci_cd_integration",
                        "webhook_notifications",
                        "api_integration"
                    ]
                },
                "reporting": {
                    "title": "Advanced Reporting",
                    "formats": [
                        {
                            "type": "executive_summary",
                            "description": "High-level overview for management"
                        },
                        {
                            "type": "technical_report", 
                            "description": "Detailed technical findings"
                        },
                        {
                            "type": "sarif",
                            "description": "SARIF format for tool integration"
                        },
                        {
                            "type": "compliance_report",
                            "description": "Compliance-focused analysis"
                        }
                    ]
                }
            },
            "educational_panel": {
                "vulnerability_education": {
                    "title": "Understanding Vulnerabilities",
                    "content": "Learn about different types of security vulnerabilities and their impact"
                },
                "template_syntax": {
                    "title": "Template Creation Guide",
                    "content": "Step-by-step guide to creating custom Nuclei templates"
                },
                "remediation_guidance": {
                    "title": "Remediation Best Practices",
                    "content": "How to effectively fix discovered vulnerabilities"
                }
            },
            "performance_analytics": {
                "scan_metrics": [
                    "templates_per_second",
                    "requests_per_second",
                    "success_rate",
                    "error_rate",
                    "template_efficiency"
                ],
                "optimization_tips": [
                    "Adjust rate limiting based on target capacity",
                    "Use specific templates for faster scanning",
                    "Optimize concurrency for better performance",
                    "Filter by severity for focused testing"
                ],
                "resource_monitoring": {
                    "cpu_usage": True,
                    "memory_consumption": True,
                    "network_throughput": True,
                    "template_cache_status": True
                }
            },
            "collaboration_features": {
                "team_sharing": {
                    "share_scan_configs": True,
                    "share_custom_templates": True,
                    "collaborative_findings_review": True
                },
                "knowledge_base": {
                    "vulnerability_database": True,
                    "remediation_library": True,
                    "template_documentation": True
                }
            }
        }