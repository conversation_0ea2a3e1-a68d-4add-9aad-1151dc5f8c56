#!/usr/bin/env python3
"""
Fix AICapability import errors in the backend
"""
import os
import re

def fix_ai_services_imports():
    """Add AICapability import to services.py"""
    services_path = "src/ai/services.py"
    
    # Read the file
    with open(services_path, 'r') as f:
        content = f.read()
    
    # Check if AICapability is already imported
    if 'from .ai_service import AICapability' not in content:
        # Find the import section and add the import
        lines = content.split('\n')
        
        # Find where to insert the import (after other local imports)
        insert_index = -1
        for i, line in enumerate(lines):
            if line.startswith('from core.security'):
                insert_index = i + 1
                break
        
        if insert_index > 0:
            lines.insert(insert_index, 'from .ai_service import AICapability')
            
            # Write back
            with open(services_path, 'w') as f:
                f.write('\n'.join(lines))
            print(f"✅ Fixed AICapability import in {services_path}")
        else:
            print(f"❌ Could not find appropriate location to add import in {services_path}")
    else:
        print(f"✅ AICapability already imported in {services_path}")

def fix_parameter_fuzzer_imports():
    """Fix missing Callable import in parameter_fuzzer.py"""
    fuzzer_path = "src/security/tools/custom/utilities/parameter_fuzzer.py"
    
    if os.path.exists(fuzzer_path):
        with open(fuzzer_path, 'r') as f:
            content = f.read()
        
        # Check if Callable is imported
        if 'from typing import' in content and 'Callable' not in content:
            # Add Callable to existing typing import
            content = re.sub(
                r'from typing import ([^\\n]+)',
                lambda m: f"from typing import {m.group(1)}, Callable" if 'Callable' not in m.group(1) else m.group(0),
                content
            )
            
            with open(fuzzer_path, 'w') as f:
                f.write(content)
            print(f"✅ Fixed Callable import in {fuzzer_path}")
        else:
            print(f"✅ Callable already imported or no typing import found in {fuzzer_path}")
    else:
        print(f"❌ File not found: {fuzzer_path}")

def fix_external_tool_gateway():
    """Fix syntax error in external_tool_gateway.py line 430"""
    gateway_path = "src/security/tools/external_tool_gateway.py"
    
    if os.path.exists(gateway_path):
        with open(gateway_path, 'r') as f:
            lines = f.readlines()
        
        # Check around line 430 for syntax issues
        if len(lines) >= 430:
            # Common issue is line continuation
            for i in range(425, min(435, len(lines))):
                if lines[i].rstrip().endswith('\\') and i < len(lines) - 1:
                    # Check if next line is improperly indented
                    if lines[i+1].strip() == '':
                        lines[i] = lines[i].rstrip()[:-1] + '\n'
                        print(f"✅ Fixed line continuation issue at line {i+1} in {gateway_path}")
                        
                        with open(gateway_path, 'w') as f:
                            f.writelines(lines)
                        break
        else:
            print(f"❌ File has fewer than 430 lines: {gateway_path}")
    else:
        print(f"❌ File not found: {gateway_path}")

if __name__ == "__main__":
    print("Fixing AI import errors...")
    fix_ai_services_imports()
    fix_parameter_fuzzer_imports()
    fix_external_tool_gateway()
    print("\nDone! Please restart the backend to apply changes.")