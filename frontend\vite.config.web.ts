import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import path from 'path';
import { VitePWA } from 'vite-plugin-pwa';

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    react(),
    VitePWA({
      registerType: 'autoUpdate',
      workbox: {
        globPatterns: ['**/*.{js,css,html,ico,png,svg,woff2}']
      },
      includeAssets: ['favicon.ico'],
      manifest: {
        name: 'NexusScan - Professional Security Scanner',
        short_name: 'NexusScan',
        description: 'Professional cybersecurity scanning platform with 23+ security tools',
        theme_color: '#3b82f6',
        background_color: '#0f172a',
        display: 'standalone',
        scope: '/',
        start_url: '/',
        icons: [
          {
            src: 'icons/icon-192x192.svg',
            sizes: '192x192',
            type: 'image/svg+xml'
          },
          {
            src: 'icons/icon-512x512.svg',
            sizes: '512x512',
            type: 'image/svg+xml'
          }
        ]
      },
      devOptions: {
        enabled: true
      }
    })
  ],
  
  // Development server configuration
  server: {
    port: 3000,
    host: '0.0.0.0',
    strictPort: false,
    cors: true,
    open: true
  },

  // Build configuration
  build: {
    outDir: 'dist-web',
    emptyOutDir: true,
    sourcemap: true,
    target: 'es2020',
    rollupOptions: {
      output: {
        manualChunks: {
          // Vendor chunks for better caching
          react: ['react', 'react-dom', 'react-router-dom'],
          ui: ['lucide-react', 'clsx', 'tailwind-merge'],
          editor: ['monaco-editor'],
          terminal: ['@xterm/xterm', '@xterm/addon-fit', '@xterm/addon-web-links'],
          charts: ['recharts'],
          state: ['zustand'],
          http: ['axios']
        }
      }
    },
    // Optimize for web deployment
    chunkSizeWarningLimit: 1000,
    assetsInlineLimit: 4096
  },

  // Path resolution
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src')
    }
  },

  // Define configuration for web environment
  define: {
    __APP_VERSION__: JSON.stringify(process.env.npm_package_version || '1.0.0'),
    __APP_NAME__: JSON.stringify('NexusScan'),
    __IS_ELECTRON__: JSON.stringify(false),
    __IS_WEB__: JSON.stringify(true),
    __BACKEND_URL__: JSON.stringify(
      process.env.NODE_ENV === 'development'
        ? 'http://************:8090'
        : 'http://************:8090'
    )
  },

  // Optimization for web deployment
  optimizeDeps: {
    include: [
      'react',
      'react-dom',
      'react-router-dom',
      'zustand',
      'axios',
      'lucide-react',
      'clsx',
      'tailwind-merge',
      'recharts',
      'framer-motion'
    ],
    exclude: [
      'electron'
    ]
  },

  // Base path configuration
  base: '/',

  // Preview configuration for testing builds
  preview: {
    port: 4173,
    host: '0.0.0.0',
    strictPort: true,
    cors: true
  },

  // CSS configuration
  css: {
    postcss: './postcss.config.js'
  },

  // Test configuration
  test: {
    environment: 'jsdom',
    setupFiles: ['./src/test/setup.ts'],
    globals: true,
    css: true,
    clearMocks: true,
    mockReset: true,
    restoreMocks: true
  }
});
