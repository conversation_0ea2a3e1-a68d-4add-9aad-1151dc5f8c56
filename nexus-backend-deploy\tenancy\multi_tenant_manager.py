#!/usr/bin/env python3
"""
Multi-Tenancy Manager
Enterprise-grade multi-tenant architecture with data isolation,
resource management, and tenant-specific configurations
"""

import asyncio
import json
import logging
import hashlib
import time
import os
from typing import Any, Dict, List, Optional, Set, Tuple, Union
from dataclasses import dataclass, field, asdict
from datetime import datetime, <PERSON><PERSON><PERSON>
from enum import Enum
from pathlib import Path
import sqlite3
import threading

try:
    from ..auth.rbac_system import RBACManager, Organization, User, get_rbac_manager
    from ..database.optimization import OptimizedDatabase, OptimizationConfig
    from ..core.events import EventManager, EventTypes
except ImportError:
    # Handle import for standalone testing
    import sys
    sys.path.append(str(Path(__file__).parent.parent))
    from auth.rbac_system import RBACManager, Organization, User, get_rbac_manager
    from database.optimization import OptimizedDatabase, OptimizationConfig
    from core.events import EventManager, EventTypes

logger = logging.getLogger(__name__)

class TenantStatus(Enum):
    """Tenant status levels"""
    ACTIVE = "active"
    SUSPENDED = "suspended"
    TRIAL = "trial"
    EXPIRED = "expired"
    PENDING = "pending"

class IsolationLevel(Enum):
    """Data isolation levels"""
    SHARED_DATABASE = "shared_database"      # Shared DB with tenant_id filtering
    DEDICATED_SCHEMA = "dedicated_schema"    # Separate schema per tenant
    DEDICATED_DATABASE = "dedicated_database" # Separate DB per tenant

class ResourceQuotaType(Enum):
    """Types of resource quotas"""
    CAMPAIGNS_MAX = "campaigns_max"
    SCANS_PER_DAY = "scans_per_day"
    USERS_MAX = "users_max"
    STORAGE_MB = "storage_mb"
    AI_REQUESTS_PER_DAY = "ai_requests_per_day"
    CONCURRENT_SCANS = "concurrent_scans"
    DATA_RETENTION_DAYS = "data_retention_days"

@dataclass
class ResourceQuota:
    """Resource quota definition"""
    quota_type: ResourceQuotaType
    limit: int
    current_usage: int = 0
    reset_period: str = "daily"  # daily, monthly, none
    last_reset: datetime = field(default_factory=datetime.now)
    
    def is_exceeded(self) -> bool:
        """Check if quota is exceeded"""
        return self.current_usage >= self.limit
    
    def usage_percentage(self) -> float:
        """Get usage percentage"""
        return (self.current_usage / self.limit * 100) if self.limit > 0 else 0
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        result = asdict(self)
        result['quota_type'] = self.quota_type.value
        result['last_reset'] = self.last_reset.isoformat()
        return result

@dataclass
class TenantConfiguration:
    """Tenant-specific configuration"""
    tenant_id: str
    features_enabled: Dict[str, bool] = field(default_factory=dict)
    ai_providers: List[str] = field(default_factory=list)
    allowed_tools: List[str] = field(default_factory=list)
    security_settings: Dict[str, Any] = field(default_factory=dict)
    integration_settings: Dict[str, Any] = field(default_factory=dict)
    custom_branding: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return asdict(self)

@dataclass
class Tenant:
    """Multi-tenant organization with isolation and quotas"""
    tenant_id: str
    tenant_name: str
    organization_id: str  # Links to RBAC organization
    status: TenantStatus
    isolation_level: IsolationLevel
    subscription_tier: str = "basic"
    created_at: datetime = field(default_factory=datetime.now)
    expires_at: Optional[datetime] = None
    
    # Resource management
    quotas: Dict[str, ResourceQuota] = field(default_factory=dict)
    configuration: Optional[TenantConfiguration] = None
    
    # Database and storage
    database_path: Optional[str] = None
    schema_name: Optional[str] = None
    
    # Metadata
    contact_email: str = ""
    billing_info: Dict[str, Any] = field(default_factory=dict)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def is_active(self) -> bool:
        """Check if tenant is active"""
        if self.status != TenantStatus.ACTIVE:
            return False
        
        if self.expires_at and datetime.now() > self.expires_at:
            return False
        
        return True
    
    def has_quota_available(self, quota_type: ResourceQuotaType) -> bool:
        """Check if tenant has quota available"""
        if quota_type.value not in self.quotas:
            return True  # No quota set means unlimited
        
        quota = self.quotas[quota_type.value]
        return not quota.is_exceeded()
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        result = asdict(self)
        result['status'] = self.status.value
        result['isolation_level'] = self.isolation_level.value
        result['created_at'] = self.created_at.isoformat()
        result['expires_at'] = self.expires_at.isoformat() if self.expires_at else None
        result['quotas'] = {k: v.to_dict() for k, v in self.quotas.items()}
        return result

class TenantDatabase:
    """Database manager for multi-tenancy"""
    
    def __init__(self, db_path: str = "data/tenancy.db"):
        self.db_path = db_path
        self._lock = threading.RLock()
        self._initialize_database()
    
    def _initialize_database(self):
        """Initialize tenancy database schema"""
        Path(self.db_path).parent.mkdir(parents=True, exist_ok=True)
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Tenants table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS tenants (
                tenant_id TEXT PRIMARY KEY,
                tenant_name TEXT NOT NULL,
                organization_id TEXT NOT NULL,
                status TEXT NOT NULL,
                isolation_level TEXT NOT NULL,
                subscription_tier TEXT DEFAULT 'basic',
                created_at TEXT NOT NULL,
                expires_at TEXT,
                database_path TEXT,
                schema_name TEXT,
                contact_email TEXT,
                billing_info TEXT,
                metadata TEXT
            )
        """)
        
        # Resource quotas table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS tenant_quotas (
                tenant_id TEXT,
                quota_type TEXT,
                quota_limit INTEGER NOT NULL,
                current_usage INTEGER DEFAULT 0,
                reset_period TEXT DEFAULT 'daily',
                last_reset TEXT NOT NULL,
                PRIMARY KEY (tenant_id, quota_type),
                FOREIGN KEY (tenant_id) REFERENCES tenants (tenant_id)
            )
        """)
        
        # Tenant configurations table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS tenant_configurations (
                tenant_id TEXT PRIMARY KEY,
                features_enabled TEXT,
                ai_providers TEXT,
                allowed_tools TEXT,
                security_settings TEXT,
                integration_settings TEXT,
                custom_branding TEXT,
                FOREIGN KEY (tenant_id) REFERENCES tenants (tenant_id)
            )
        """)
        
        # Resource usage logs table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS resource_usage_logs (
                log_id TEXT PRIMARY KEY,
                tenant_id TEXT NOT NULL,
                resource_type TEXT NOT NULL,
                usage_amount INTEGER NOT NULL,
                timestamp TEXT NOT NULL,
                details TEXT,
                FOREIGN KEY (tenant_id) REFERENCES tenants (tenant_id)
            )
        """)
        
        # Create indexes
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_tenants_status ON tenants(status)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_tenants_org ON tenants(organization_id)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_usage_logs_tenant ON resource_usage_logs(tenant_id)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_usage_logs_timestamp ON resource_usage_logs(timestamp)")
        
        conn.commit()
        conn.close()
    
    def create_tenant(self, tenant: Tenant) -> bool:
        """Create new tenant"""
        with self._lock:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            try:
                cursor.execute("""
                    INSERT INTO tenants 
                    (tenant_id, tenant_name, organization_id, status, isolation_level,
                     subscription_tier, created_at, expires_at, database_path, schema_name,
                     contact_email, billing_info, metadata)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    tenant.tenant_id,
                    tenant.tenant_name,
                    tenant.organization_id,
                    tenant.status.value,
                    tenant.isolation_level.value,
                    tenant.subscription_tier,
                    tenant.created_at.isoformat(),
                    tenant.expires_at.isoformat() if tenant.expires_at else None,
                    tenant.database_path,
                    tenant.schema_name,
                    tenant.contact_email,
                    json.dumps(tenant.billing_info),
                    json.dumps(tenant.metadata)
                ))
                
                # Create quotas
                for quota_type, quota in tenant.quotas.items():
                    cursor.execute("""
                        INSERT INTO tenant_quotas 
                        (tenant_id, quota_type, quota_limit, current_usage, reset_period, last_reset)
                        VALUES (?, ?, ?, ?, ?, ?)
                    """, (
                        tenant.tenant_id,
                        quota_type,
                        quota.limit,
                        quota.current_usage,
                        quota.reset_period,
                        quota.last_reset.isoformat()
                    ))
                
                # Create configuration
                if tenant.configuration:
                    cursor.execute("""
                        INSERT INTO tenant_configurations 
                        (tenant_id, features_enabled, ai_providers, allowed_tools,
                         security_settings, integration_settings, custom_branding)
                        VALUES (?, ?, ?, ?, ?, ?, ?)
                    """, (
                        tenant.tenant_id,
                        json.dumps(tenant.configuration.features_enabled),
                        json.dumps(tenant.configuration.ai_providers),
                        json.dumps(tenant.configuration.allowed_tools),
                        json.dumps(tenant.configuration.security_settings),
                        json.dumps(tenant.configuration.integration_settings),
                        json.dumps(tenant.configuration.custom_branding)
                    ))
                
                conn.commit()
                return True
                
            except sqlite3.IntegrityError:
                return False
            finally:
                conn.close()
    
    def get_tenant(self, tenant_id: str) -> Optional[Tenant]:
        """Get tenant by ID"""
        with self._lock:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Get tenant basic info
            cursor.execute("""
                SELECT tenant_id, tenant_name, organization_id, status, isolation_level,
                       subscription_tier, created_at, expires_at, database_path, schema_name,
                       contact_email, billing_info, metadata
                FROM tenants WHERE tenant_id = ?
            """, (tenant_id,))
            
            row = cursor.fetchone()
            if not row:
                conn.close()
                return None
            
            tenant = Tenant(
                tenant_id=row[0],
                tenant_name=row[1],
                organization_id=row[2],
                status=TenantStatus(row[3]),
                isolation_level=IsolationLevel(row[4]),
                subscription_tier=row[5],
                created_at=datetime.fromisoformat(row[6]),
                expires_at=datetime.fromisoformat(row[7]) if row[7] else None,
                database_path=row[8],
                schema_name=row[9],
                contact_email=row[10] or "",
                billing_info=json.loads(row[11]) if row[11] else {},
                metadata=json.loads(row[12]) if row[12] else {}
            )
            
            # Load quotas
            cursor.execute("""
                SELECT quota_type, quota_limit, current_usage, reset_period, last_reset
                FROM tenant_quotas WHERE tenant_id = ?
            """, (tenant_id,))
            
            for quota_row in cursor.fetchall():
                quota = ResourceQuota(
                    quota_type=ResourceQuotaType(quota_row[0]),
                    limit=quota_row[1],
                    current_usage=quota_row[2],
                    reset_period=quota_row[3],
                    last_reset=datetime.fromisoformat(quota_row[4])
                )
                tenant.quotas[quota_row[0]] = quota
            
            # Load configuration
            cursor.execute("""
                SELECT features_enabled, ai_providers, allowed_tools,
                       security_settings, integration_settings, custom_branding
                FROM tenant_configurations WHERE tenant_id = ?
            """, (tenant_id,))
            
            config_row = cursor.fetchone()
            if config_row:
                tenant.configuration = TenantConfiguration(
                    tenant_id=tenant_id,
                    features_enabled=json.loads(config_row[0]) if config_row[0] else {},
                    ai_providers=json.loads(config_row[1]) if config_row[1] else [],
                    allowed_tools=json.loads(config_row[2]) if config_row[2] else [],
                    security_settings=json.loads(config_row[3]) if config_row[3] else {},
                    integration_settings=json.loads(config_row[4]) if config_row[4] else {},
                    custom_branding=json.loads(config_row[5]) if config_row[5] else {}
                )
            
            conn.close()
            return tenant
    
    def update_quota_usage(self, tenant_id: str, quota_type: ResourceQuotaType, 
                          usage_change: int) -> bool:
        """Update resource quota usage"""
        with self._lock:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            try:
                cursor.execute("""
                    UPDATE tenant_quotas 
                    SET current_usage = current_usage + ?
                    WHERE tenant_id = ? AND quota_type = ?
                """, (usage_change, tenant_id, quota_type.value))
                
                conn.commit()
                return cursor.rowcount > 0
                
            except Exception:
                return False
            finally:
                conn.close()
    
    def log_resource_usage(self, tenant_id: str, resource_type: str, 
                          usage_amount: int, details: Optional[Dict[str, Any]] = None) -> bool:
        """Log resource usage"""
        log_id = hashlib.md5(f"{tenant_id}_{resource_type}_{time.time()}".encode()).hexdigest()
        
        with self._lock:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            try:
                cursor.execute("""
                    INSERT INTO resource_usage_logs 
                    (log_id, tenant_id, resource_type, usage_amount, timestamp, details)
                    VALUES (?, ?, ?, ?, ?, ?)
                """, (
                    log_id,
                    tenant_id,
                    resource_type,
                    usage_amount,
                    datetime.now().isoformat(),
                    json.dumps(details) if details else None
                ))
                
                conn.commit()
                return True
                
            except Exception:
                return False
            finally:
                conn.close()

class TenantIsolationManager:
    """Manages data isolation between tenants"""
    
    def __init__(self, base_data_path: str = "data/tenants"):
        self.base_data_path = Path(base_data_path)
        self.base_data_path.mkdir(parents=True, exist_ok=True)
        self.tenant_databases: Dict[str, OptimizedDatabase] = {}
        self._lock = threading.RLock()
    
    def setup_tenant_isolation(self, tenant: Tenant) -> bool:
        """Setup data isolation for tenant based on isolation level"""
        try:
            if tenant.isolation_level == IsolationLevel.DEDICATED_DATABASE:
                return self._setup_dedicated_database(tenant)
            elif tenant.isolation_level == IsolationLevel.DEDICATED_SCHEMA:
                return self._setup_dedicated_schema(tenant)
            else:  # SHARED_DATABASE
                return self._setup_shared_database(tenant)
                
        except Exception as e:
            logger.error(f"Failed to setup isolation for tenant {tenant.tenant_id}: {e}")
            return False
    
    def _setup_dedicated_database(self, tenant: Tenant) -> bool:
        """Setup dedicated database for tenant"""
        db_path = self.base_data_path / f"tenant_{tenant.tenant_id}.db"
        
        # Create optimized database for tenant
        config = OptimizationConfig(
            max_connections=10,
            enable_query_cache=True,
            cache_size_kb=32000
        )
        
        tenant_db = OptimizedDatabase(str(db_path), config)
        
        # Initialize tenant-specific schema
        tenant_db.execute_query("""
            CREATE TABLE IF NOT EXISTS tenant_campaigns (
                campaign_id TEXT PRIMARY KEY,
                campaign_name TEXT NOT NULL,
                description TEXT,
                created_by TEXT NOT NULL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                status TEXT DEFAULT 'active',
                targets TEXT,
                configuration TEXT
            )
        """)
        
        tenant_db.execute_query("""
            CREATE TABLE IF NOT EXISTS tenant_scans (
                scan_id TEXT PRIMARY KEY,
                campaign_id TEXT,
                scan_type TEXT NOT NULL,
                target TEXT NOT NULL,
                status TEXT DEFAULT 'pending',
                started_at DATETIME,
                completed_at DATETIME,
                results TEXT,
                FOREIGN KEY (campaign_id) REFERENCES tenant_campaigns (campaign_id)
            )
        """)
        
        tenant_db.execute_query("""
            CREATE TABLE IF NOT EXISTS tenant_vulnerabilities (
                vuln_id TEXT PRIMARY KEY,
                scan_id TEXT,
                vulnerability_type TEXT NOT NULL,
                severity TEXT NOT NULL,
                title TEXT NOT NULL,
                description TEXT,
                cvss_score REAL,
                remediation TEXT,
                discovered_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (scan_id) REFERENCES tenant_scans (scan_id)
            )
        """)
        
        # Create indexes
        tenant_db.create_index("tenant_campaigns", ["created_by"])
        tenant_db.create_index("tenant_scans", ["campaign_id"])
        tenant_db.create_index("tenant_scans", ["target"])
        tenant_db.create_index("tenant_vulnerabilities", ["scan_id"])
        tenant_db.create_index("tenant_vulnerabilities", ["severity"])
        
        with self._lock:
            self.tenant_databases[tenant.tenant_id] = tenant_db
        
        # Update tenant record
        tenant.database_path = str(db_path)
        
        logger.info(f"Setup dedicated database for tenant {tenant.tenant_id}")
        return True
    
    def _setup_dedicated_schema(self, tenant: Tenant) -> bool:
        """Setup dedicated schema for tenant (SQLite doesn't support schemas, use prefixed tables)"""
        # For SQLite, we'll use table prefixes to simulate schemas
        schema_prefix = f"t_{tenant.tenant_id}"
        tenant.schema_name = schema_prefix
        
        # Use shared database but with prefixed tables
        shared_db_path = self.base_data_path / "shared_tenants.db"
        
        if tenant.tenant_id not in self.tenant_databases:
            config = OptimizationConfig(
                max_connections=20,
                enable_query_cache=True
            )
            
            shared_db = OptimizedDatabase(str(shared_db_path), config)
            
            with self._lock:
                self.tenant_databases[tenant.tenant_id] = shared_db
        
        # Create tenant-specific tables with prefix
        db = self.tenant_databases[tenant.tenant_id]
        
        db.execute_query(f"""
            CREATE TABLE IF NOT EXISTS {schema_prefix}_campaigns (
                campaign_id TEXT PRIMARY KEY,
                campaign_name TEXT NOT NULL,
                description TEXT,
                created_by TEXT NOT NULL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                status TEXT DEFAULT 'active',
                targets TEXT,
                configuration TEXT
            )
        """)
        
        db.execute_query(f"""
            CREATE TABLE IF NOT EXISTS {schema_prefix}_scans (
                scan_id TEXT PRIMARY KEY,
                campaign_id TEXT,
                scan_type TEXT NOT NULL,
                target TEXT NOT NULL,
                status TEXT DEFAULT 'pending',
                started_at DATETIME,
                completed_at DATETIME,
                results TEXT,
                FOREIGN KEY (campaign_id) REFERENCES {schema_prefix}_campaigns (campaign_id)
            )
        """)
        
        logger.info(f"Setup dedicated schema for tenant {tenant.tenant_id}")
        return True
    
    def _setup_shared_database(self, tenant: Tenant) -> bool:
        """Setup shared database with tenant_id filtering"""
        shared_db_path = self.base_data_path / "shared_tenants.db"
        
        if "shared" not in self.tenant_databases:
            config = OptimizationConfig(
                max_connections=50,
                enable_query_cache=True,
                cache_size_kb=64000
            )
            
            shared_db = OptimizedDatabase(str(shared_db_path), config)
            
            # Create shared tables with tenant_id columns
            shared_db.execute_query("""
                CREATE TABLE IF NOT EXISTS shared_campaigns (
                    campaign_id TEXT PRIMARY KEY,
                    tenant_id TEXT NOT NULL,
                    campaign_name TEXT NOT NULL,
                    description TEXT,
                    created_by TEXT NOT NULL,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    status TEXT DEFAULT 'active',
                    targets TEXT,
                    configuration TEXT
                )
            """)
            
            shared_db.execute_query("""
                CREATE TABLE IF NOT EXISTS shared_scans (
                    scan_id TEXT PRIMARY KEY,
                    tenant_id TEXT NOT NULL,
                    campaign_id TEXT,
                    scan_type TEXT NOT NULL,
                    target TEXT NOT NULL,
                    status TEXT DEFAULT 'pending',
                    started_at DATETIME,
                    completed_at DATETIME,
                    results TEXT,
                    FOREIGN KEY (campaign_id) REFERENCES shared_campaigns (campaign_id)
                )
            """)
            
            # Create tenant-aware indexes
            shared_db.create_index("shared_campaigns", ["tenant_id"])
            shared_db.create_index("shared_campaigns", ["tenant_id", "created_by"])
            shared_db.create_index("shared_scans", ["tenant_id"])
            shared_db.create_index("shared_scans", ["tenant_id", "campaign_id"])
            
            with self._lock:
                self.tenant_databases["shared"] = shared_db
                self.tenant_databases[tenant.tenant_id] = shared_db
        else:
            with self._lock:
                self.tenant_databases[tenant.tenant_id] = self.tenant_databases["shared"]
        
        logger.info(f"Setup shared database for tenant {tenant.tenant_id}")
        return True
    
    def get_tenant_database(self, tenant_id: str) -> Optional[OptimizedDatabase]:
        """Get database instance for tenant"""
        with self._lock:
            return self.tenant_databases.get(tenant_id)
    
    def cleanup_tenant_data(self, tenant_id: str, isolation_level: IsolationLevel) -> bool:
        """Cleanup tenant data based on isolation level"""
        try:
            if isolation_level == IsolationLevel.DEDICATED_DATABASE:
                # Remove dedicated database file
                db_path = self.base_data_path / f"tenant_{tenant_id}.db"
                if db_path.exists():
                    db_path.unlink()
                
                # Close and remove from cache
                with self._lock:
                    if tenant_id in self.tenant_databases:
                        self.tenant_databases[tenant_id].close()
                        del self.tenant_databases[tenant_id]
                
            elif isolation_level == IsolationLevel.DEDICATED_SCHEMA:
                # Drop prefixed tables
                db = self.get_tenant_database(tenant_id)
                if db:
                    schema_prefix = f"t_{tenant_id}"
                    db.execute_query(f"DROP TABLE IF EXISTS {schema_prefix}_campaigns")
                    db.execute_query(f"DROP TABLE IF EXISTS {schema_prefix}_scans")
                
            else:  # SHARED_DATABASE
                # Delete tenant data from shared tables
                db = self.get_tenant_database(tenant_id)
                if db:
                    db.execute_query("DELETE FROM shared_campaigns WHERE tenant_id = ?", (tenant_id,))
                    db.execute_query("DELETE FROM shared_scans WHERE tenant_id = ?", (tenant_id,))
            
            logger.info(f"Cleaned up data for tenant {tenant_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to cleanup data for tenant {tenant_id}: {e}")
            return False

class MultiTenantManager:
    """Main multi-tenancy management system"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        self.config = config or {}
        self.db = TenantDatabase(self.config.get('db_path', 'data/tenancy.db'))
        self.isolation_manager = TenantIsolationManager(
            self.config.get('tenant_data_path', 'data/tenants')
        )
        self.rbac_manager = get_rbac_manager()
        
        # Tenant cache
        self._tenant_cache: Dict[str, Tenant] = {}
        self._cache_lock = threading.RLock()
        
        # Metrics
        self.metrics = {
            'total_tenants': 0,
            'active_tenants': 0,
            'quota_violations': 0,
            'resource_usage_logs': 0
        }
    
    async def create_tenant(self, tenant_name: str, contact_email: str,
                          subscription_tier: str = "basic",
                          isolation_level: IsolationLevel = IsolationLevel.SHARED_DATABASE,
                          expires_at: Optional[datetime] = None) -> Tenant:
        """Create new tenant with organization and isolation"""
        
        # Generate tenant ID
        tenant_id = hashlib.md5(f"{tenant_name}_{contact_email}_{time.time()}".encode()).hexdigest()
        
        # Create RBAC organization
        organization = await self.rbac_manager.create_organization(
            tenant_name, 
            "tenant"
        )
        
        # Setup default quotas based on subscription tier
        quotas = self._get_default_quotas(subscription_tier)
        
        # Setup default configuration
        configuration = TenantConfiguration(
            tenant_id=tenant_id,
            features_enabled=self._get_default_features(subscription_tier),
            ai_providers=["openai", "deepseek"] if subscription_tier != "basic" else ["openai"],
            allowed_tools=self._get_allowed_tools(subscription_tier),
            security_settings={
                "mfa_required": subscription_tier in ["enterprise", "premium"],
                "session_timeout": 3600,
                "password_policy": "strong"
            }
        )
        
        # Create tenant
        tenant = Tenant(
            tenant_id=tenant_id,
            tenant_name=tenant_name,
            organization_id=organization.org_id,
            status=TenantStatus.ACTIVE,
            isolation_level=isolation_level,
            subscription_tier=subscription_tier,
            expires_at=expires_at,
            quotas=quotas,
            configuration=configuration,
            contact_email=contact_email
        )
        
        # Setup data isolation
        isolation_success = self.isolation_manager.setup_tenant_isolation(tenant)
        if not isolation_success:
            raise RuntimeError(f"Failed to setup isolation for tenant {tenant_id}")
        
        # Save to database
        success = self.db.create_tenant(tenant)
        if not success:
            raise RuntimeError(f"Failed to create tenant {tenant_id}")
        
        # Cache tenant
        with self._cache_lock:
            self._tenant_cache[tenant_id] = tenant
        
        # Update metrics
        self.metrics['total_tenants'] += 1
        self.metrics['active_tenants'] += 1
        
        logger.info(f"Created tenant: {tenant_name} ({tenant_id})")
        return tenant
    
    def _get_default_quotas(self, subscription_tier: str) -> Dict[str, ResourceQuota]:
        """Get default quotas based on subscription tier"""
        quota_configs = {
            "basic": {
                ResourceQuotaType.CAMPAIGNS_MAX: 5,
                ResourceQuotaType.SCANS_PER_DAY: 10,
                ResourceQuotaType.USERS_MAX: 3,
                ResourceQuotaType.STORAGE_MB: 1000,
                ResourceQuotaType.AI_REQUESTS_PER_DAY: 50,
                ResourceQuotaType.CONCURRENT_SCANS: 2,
                ResourceQuotaType.DATA_RETENTION_DAYS: 30
            },
            "premium": {
                ResourceQuotaType.CAMPAIGNS_MAX: 25,
                ResourceQuotaType.SCANS_PER_DAY: 100,
                ResourceQuotaType.USERS_MAX: 15,
                ResourceQuotaType.STORAGE_MB: 10000,
                ResourceQuotaType.AI_REQUESTS_PER_DAY: 500,
                ResourceQuotaType.CONCURRENT_SCANS: 5,
                ResourceQuotaType.DATA_RETENTION_DAYS: 90
            },
            "enterprise": {
                ResourceQuotaType.CAMPAIGNS_MAX: 100,
                ResourceQuotaType.SCANS_PER_DAY: 1000,
                ResourceQuotaType.USERS_MAX: 100,
                ResourceQuotaType.STORAGE_MB: 100000,
                ResourceQuotaType.AI_REQUESTS_PER_DAY: 5000,
                ResourceQuotaType.CONCURRENT_SCANS: 20,
                ResourceQuotaType.DATA_RETENTION_DAYS: 365
            }
        }
        
        limits = quota_configs.get(subscription_tier, quota_configs["basic"])
        quotas = {}
        
        for quota_type, limit in limits.items():
            quotas[quota_type.value] = ResourceQuota(
                quota_type=quota_type,
                limit=limit
            )
        
        return quotas
    
    def _get_default_features(self, subscription_tier: str) -> Dict[str, bool]:
        """Get default features based on subscription tier"""
        features = {
            "ai_enhanced_scanning": subscription_tier != "basic",
            "advanced_reporting": subscription_tier in ["premium", "enterprise"],
            "threat_intelligence": subscription_tier in ["premium", "enterprise"],
            "compliance_reporting": subscription_tier == "enterprise",
            "api_access": subscription_tier in ["premium", "enterprise"],
            "webhook_integrations": subscription_tier == "enterprise",
            "custom_branding": subscription_tier == "enterprise",
            "sso_integration": subscription_tier == "enterprise"
        }
        return features
    
    def _get_allowed_tools(self, subscription_tier: str) -> List[str]:
        """Get allowed tools based on subscription tier"""
        basic_tools = ["nmap", "nuclei"]
        premium_tools = basic_tools + ["sqlmap", "dirb", "gobuster"]
        enterprise_tools = premium_tools + ["burp", "metasploit", "custom_tools"]
        
        if subscription_tier == "basic":
            return basic_tools
        elif subscription_tier == "premium":
            return premium_tools
        else:
            return enterprise_tools
    
    def get_tenant(self, tenant_id: str) -> Optional[Tenant]:
        """Get tenant by ID with caching"""
        # Check cache first
        with self._cache_lock:
            if tenant_id in self._tenant_cache:
                return self._tenant_cache[tenant_id]
        
        # Load from database
        tenant = self.db.get_tenant(tenant_id)
        if tenant:
            with self._cache_lock:
                self._tenant_cache[tenant_id] = tenant
        
        return tenant
    
    def get_tenant_by_organization(self, organization_id: str) -> Optional[Tenant]:
        """Get tenant by organization ID"""
        # Simple implementation - in production would have index
        # For now, check cache first
        with self._cache_lock:
            for tenant in self._tenant_cache.values():
                if tenant.organization_id == organization_id:
                    return tenant
        
        # Could implement database query here
        return None
    
    async def check_resource_quota(self, tenant_id: str, quota_type: ResourceQuotaType,
                                 requested_amount: int = 1) -> bool:
        """Check if tenant has available quota for resource"""
        tenant = self.get_tenant(tenant_id)
        if not tenant:
            return False
        
        if not tenant.is_active():
            return False
        
        if quota_type.value not in tenant.quotas:
            return True  # No quota set means unlimited
        
        quota = tenant.quotas[quota_type.value]
        return (quota.current_usage + requested_amount) <= quota.limit
    
    async def consume_quota(self, tenant_id: str, quota_type: ResourceQuotaType,
                          amount: int = 1, details: Optional[Dict[str, Any]] = None) -> bool:
        """Consume tenant quota and log usage"""
        tenant = self.get_tenant(tenant_id)
        if not tenant:
            return False
        
        # Check if quota available
        if not await self.check_resource_quota(tenant_id, quota_type, amount):
            self.metrics['quota_violations'] += 1
            logger.warning(f"Quota exceeded for tenant {tenant_id}: {quota_type.value}")
            return False
        
        # Update quota usage
        success = self.db.update_quota_usage(tenant_id, quota_type, amount)
        if success:
            # Update cache
            if quota_type.value in tenant.quotas:
                tenant.quotas[quota_type.value].current_usage += amount
            
            # Log usage
            self.db.log_resource_usage(tenant_id, quota_type.value, amount, details)
            self.metrics['resource_usage_logs'] += 1
        
        return success
    
    def get_tenant_database(self, tenant_id: str) -> Optional[OptimizedDatabase]:
        """Get database instance for tenant"""
        return self.isolation_manager.get_tenant_database(tenant_id)
    
    async def suspend_tenant(self, tenant_id: str, reason: str) -> bool:
        """Suspend tenant access"""
        tenant = self.get_tenant(tenant_id)
        if not tenant:
            return False
        
        # Update status
        tenant.status = TenantStatus.SUSPENDED
        tenant.metadata['suspension_reason'] = reason
        tenant.metadata['suspended_at'] = datetime.now().isoformat()
        
        # Update in database (simplified)
        # In production, would have update method
        
        # Clear from cache to force reload
        with self._cache_lock:
            if tenant_id in self._tenant_cache:
                del self._tenant_cache[tenant_id]
        
        self.metrics['active_tenants'] -= 1
        logger.info(f"Suspended tenant {tenant_id}: {reason}")
        return True
    
    async def delete_tenant(self, tenant_id: str) -> bool:
        """Delete tenant and cleanup all data"""
        tenant = self.get_tenant(tenant_id)
        if not tenant:
            return False
        
        try:
            # Cleanup isolation data
            self.isolation_manager.cleanup_tenant_data(tenant_id, tenant.isolation_level)
            
            # Remove from cache
            with self._cache_lock:
                if tenant_id in self._tenant_cache:
                    del self._tenant_cache[tenant_id]
            
            # Update metrics
            if tenant.status == TenantStatus.ACTIVE:
                self.metrics['active_tenants'] -= 1
            self.metrics['total_tenants'] -= 1
            
            logger.info(f"Deleted tenant {tenant_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to delete tenant {tenant_id}: {e}")
            return False
    
    def get_tenant_metrics(self, tenant_id: str) -> Dict[str, Any]:
        """Get metrics for specific tenant"""
        tenant = self.get_tenant(tenant_id)
        if not tenant:
            return {}
        
        metrics = {
            "tenant_id": tenant_id,
            "status": tenant.status.value,
            "subscription_tier": tenant.subscription_tier,
            "isolation_level": tenant.isolation_level.value,
            "quotas": {}
        }
        
        for quota_type, quota in tenant.quotas.items():
            metrics["quotas"][quota_type] = {
                "limit": quota.limit,
                "current_usage": quota.current_usage,
                "usage_percentage": quota.usage_percentage(),
                "is_exceeded": quota.is_exceeded()
            }
        
        return metrics
    
    def get_system_metrics(self) -> Dict[str, Any]:
        """Get system-wide tenancy metrics"""
        return self.metrics.copy()

# Global multi-tenant manager instance
multi_tenant_manager: Optional[MultiTenantManager] = None

def get_multi_tenant_manager() -> MultiTenantManager:
    """Get global multi-tenant manager instance"""
    global multi_tenant_manager
    
    if multi_tenant_manager is None:
        multi_tenant_manager = MultiTenantManager()
    
    return multi_tenant_manager

def close_multi_tenant_manager():
    """Close global multi-tenant manager"""
    global multi_tenant_manager
    multi_tenant_manager = None