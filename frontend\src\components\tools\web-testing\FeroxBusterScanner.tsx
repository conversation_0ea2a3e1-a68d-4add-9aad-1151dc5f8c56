/**
 * FeroxBuster Scanner Component
 * Fast, simple, recursive content discovery tool built in Rust
 */
import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { 
  Crosshair,
  Play,
  Square,
  Download,
  Settings,
  Target,
  Globe,
  FileText,
  Terminal,
  Copy,
  RotateCcw,
  Clock,
  Zap,
  Filter,
  Eye,
  AlertTriangle,
  CheckCircle,
  TrendingUp,
  Activity,
  Lock,
  Search,
  List,
  BarChart3,
  Database,
  Server,
  Bug,
  Info,
  FolderOpen,
  Layers,
  Shield,
  FastForward,
  Timer,
  Network,
  Gauge
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useBackendStore } from '@/stores/backend-store';
import { apiClient, ToolExecution } from '@/services/api-client';
import { toast } from 'sonner';

/**
 * FeroxBuster configuration interface
 */
interface FeroxBusterConfig {
  url: string;
  wordlist: string;
  customWordlist: string;
  extensions: string[];
  depth: number;
  threads: number;
  timeout: number;
  delay: number;
  userAgent: string;
  headers: { [key: string]: string };
  authentication: {
    enabled: boolean;
    username: string;
    password: string;
    type: 'basic' | 'digest';
  };
  filters: {
    statusCodes: string[];
    excludeStatusCodes: string[];
    contentLength: {
      enabled: boolean;
      size: number;
    };
    excludeContentLength: string[];
    words: {
      enabled: boolean;
      count: number;
    };
    excludeWords: string[];
    lines: {
      enabled: boolean;
      count: number;
    };
    excludeLines: string[];
    regex: string;
    excludeRegex: string;
  };
  scanning: {
    noRecursion: boolean;
    extractLinks: boolean;
    followRedirects: boolean;
    autoTune: boolean;
    autoSkip: boolean;
    randomUserAgent: boolean;
    collectBackups: boolean;
    collectWords: boolean;
    dontScan: string[];
  };
  proxy: {
    enabled: boolean;
    url: string;
    credentials: string;
  };
  output: {
    format: 'default' | 'json' | 'csv';
    file: string;
    quiet: boolean;
    silent: boolean;
    noColor: boolean;
    verbosity: number;
  };
  rateLimit: {
    enabled: boolean;
    requestsPerSecond: number;
  };
  resume: {
    enabled: boolean;
    stateFile: string;
  };
  customArgs: string;
}

/**
 * Common wordlists for FeroxBuster
 */
const WORDLISTS = {
  'raft_directories_lowercase': '/usr/share/seclists/Discovery/Web-Content/raft-medium-directories-lowercase.txt',
  'raft_directories': '/usr/share/seclists/Discovery/Web-Content/raft-medium-directories.txt',
  'raft_files_lowercase': '/usr/share/seclists/Discovery/Web-Content/raft-medium-files-lowercase.txt',
  'raft_files': '/usr/share/seclists/Discovery/Web-Content/raft-medium-files.txt',
  'dirb_common': '/usr/share/dirb/wordlists/common.txt',
  'dirb_big': '/usr/share/dirb/wordlists/big.txt',
  'dirbuster_medium': '/usr/share/dirbuster/wordlists/directory-list-2.3-medium.txt',
  'dirbuster_small': '/usr/share/dirbuster/wordlists/directory-list-2.3-small.txt',
  'feroxbuster_default': '/usr/share/feroxbuster/ferox-wordlist.txt',
  'seclist_directory_2_3_medium': '/usr/share/seclists/Discovery/Web-Content/directory-list-2.3-medium.txt',
  'seclist_common': '/usr/share/seclists/Discovery/Web-Content/common.txt',
  'quickhits': '/usr/share/seclists/Discovery/Web-Content/quickhits.txt'
};

/**
 * Common file extensions
 */
const COMMON_EXTENSIONS = {
  'web': ['php', 'html', 'htm', 'jsp', 'asp', 'aspx'],
  'backup': ['bak', 'backup', 'old', 'orig', 'save', 'tmp'],
  'config': ['conf', 'config', 'cfg', 'ini', 'xml', 'yaml', 'yml'],
  'data': ['txt', 'csv', 'json', 'sql', 'db', 'log'],
  'scripts': ['js', 'py', 'pl', 'sh', 'bat', 'cmd'],
  'docs': ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx']
};

/**
 * Status code groups
 */
const STATUS_CODE_GROUPS = {
  'success': ['200', '201', '202', '204'],
  'redirects': ['301', '302', '303', '307', '308'],
  'client_errors': ['400', '401', '403', '404', '405', '406', '408', '409', '410', '429'],
  'server_errors': ['500', '501', '502', '503', '504', '505']
};

export default function FeroxBusterScanner() {
  const { connectionStatus } = useBackendStore();
  const [config, setConfig] = React.useState<FeroxBusterConfig>({
    url: '',
    wordlist: 'feroxbuster_default',
    customWordlist: '',
    extensions: [],
    depth: 4,
    threads: 50,
    timeout: 7,
    delay: 0,
    userAgent: '',
    headers: {},
    authentication: {
      enabled: false,
      username: '',
      password: '',
      type: 'basic'
    },
    filters: {
      statusCodes: ['200', '204', '301', '302', '307', '308', '401', '403', '405'],
      excludeStatusCodes: [],
      contentLength: { enabled: false, size: 0 },
      excludeContentLength: [],
      words: { enabled: false, count: 0 },
      excludeWords: [],
      lines: { enabled: false, count: 0 },
      excludeLines: [],
      regex: '',
      excludeRegex: ''
    },
    scanning: {
      noRecursion: false,
      extractLinks: true,
      followRedirects: false,
      autoTune: true,
      autoSkip: true,
      randomUserAgent: false,
      collectBackups: false,
      collectWords: false,
      dontScan: []
    },
    proxy: {
      enabled: false,
      url: '',
      credentials: ''
    },
    output: {
      format: 'default',
      file: '',
      quiet: false,
      silent: false,
      noColor: false,
      verbosity: 1
    },
    rateLimit: {
      enabled: false,
      requestsPerSecond: 0
    },
    resume: {
      enabled: false,
      stateFile: ''
    },
    customArgs: ''
  });

  const [execution, setExecution] = React.useState<ToolExecution | null>(null);
  const [activeTab, setActiveTab] = React.useState('configure');

  /**
   * Handle configuration changes
   */
  const updateConfig = (path: string, value: any) => {
    setConfig(prev => {
      const keys = path.split('.');
      const newConfig = { ...prev };
      let current = newConfig;
      
      for (let i = 0; i < keys.length - 1; i++) {
        current[keys[i]] = { ...current[keys[i]] };
        current = current[keys[i]];
      }
      
      current[keys[keys.length - 1]] = value;
      return newConfig;
    });
  };

  /**
   * Handle preset configurations
   */
  const applyPreset = (preset: string) => {
    const presets = {
      'quick_scan': {
        wordlist: 'dirb_common',
        extensions: ['php', 'html', 'txt'],
        depth: 2,
        threads: 30,
        timeout: 5,
        scanning: { ...config.scanning, autoTune: true, autoSkip: true }
      },
      'comprehensive_scan': {
        wordlist: 'raft_directories',
        extensions: ['php', 'html', 'htm', 'asp', 'aspx', 'jsp', 'txt', 'pdf', 'js'],
        depth: 6,
        threads: 50,
        timeout: 10,
        scanning: { ...config.scanning, extractLinks: true, collectBackups: true }
      },
      'stealth_scan': {
        wordlist: 'dirb_common',
        extensions: ['php', 'html'],
        depth: 3,
        threads: 10,
        timeout: 15,
        delay: 1000,
        scanning: { ...config.scanning, randomUserAgent: true }
      },
      'backup_discovery': {
        wordlist: 'raft_files',
        extensions: ['bak', 'backup', 'old', 'orig', 'save', 'tmp', 'swp'],
        depth: 3,
        threads: 20,
        scanning: { ...config.scanning, collectBackups: true }
      },
      'api_discovery': {
        wordlist: 'seclist_common',
        extensions: ['json', 'xml', 'api', 'rest'],
        depth: 4,
        threads: 40,
        filters: {
          ...config.filters,
          statusCodes: ['200', '201', '400', '401', '403', '404', '405', '500']
        }
      },
      'aggressive_scan': {
        wordlist: 'dirbuster_medium',
        extensions: ['php', 'html', 'htm', 'asp', 'aspx', 'jsp', 'txt', 'pdf', 'js', 'css', 'xml', 'json'],
        depth: 8,
        threads: 100,
        timeout: 5,
        scanning: { 
          ...config.scanning, 
          extractLinks: true, 
          collectBackups: true, 
          collectWords: true 
        }
      }
    };

    if (presets[preset]) {
      setConfig(prev => ({ ...prev, ...presets[preset] }));
    }
  };

  /**
   * Toggle extension group
   */
  const toggleExtensionGroup = (group: string) => {
    const groupExtensions = COMMON_EXTENSIONS[group];
    const hasAll = groupExtensions.every(ext => config.extensions.includes(ext));
    
    if (hasAll) {
      // Remove all extensions from this group
      updateConfig('extensions', config.extensions.filter(ext => !groupExtensions.includes(ext)));
    } else {
      // Add all extensions from this group
      const newExtensions = [...new Set([...config.extensions, ...groupExtensions])];
      updateConfig('extensions', newExtensions);
    }
  };

  /**
   * Toggle status code group
   */
  const toggleStatusCodeGroup = (group: string) => {
    const groupCodes = STATUS_CODE_GROUPS[group];
    const hasAll = groupCodes.every(code => config.filters.statusCodes.includes(code));
    
    if (hasAll) {
      // Remove all codes from this group
      updateConfig('filters.statusCodes', config.filters.statusCodes.filter(code => !groupCodes.includes(code)));
    } else {
      // Add all codes from this group
      const newCodes = [...new Set([...config.filters.statusCodes, ...groupCodes])];
      updateConfig('filters.statusCodes', newCodes);
    }
  };

  /**
   * Start FeroxBuster scan
   */
  const startScan = async () => {
    if (!config.url.trim()) {
      toast.error('Please provide a target URL');
      return;
    }

    try {
      const execution = await apiClient.executeSecurityTool('feroxbuster', config);
      setExecution(execution);
      setActiveTab('output');
      toast.success('FeroxBuster scan started successfully');
    } catch (error) {
      toast.error(`Failed to start FeroxBuster scan: ${error.message}`);
    }
  };

  /**
   * Stop scan
   */
  const stopScan = async () => {
    if (execution) {
      try {
        await apiClient.stopToolExecution(execution.id);
        toast.success('FeroxBuster scan stopped');
      } catch (error) {
        toast.error(`Failed to stop scan: ${error.message}`);
      }
    }
  };

  /**
   * Generate command line
   */
  const generateCommand = () => {
    let cmd = 'feroxbuster';
    
    // URL
    cmd += ` -u "${config.url}"`;
    
    // Wordlist
    const wordlistPath = config.customWordlist || WORDLISTS[config.wordlist];
    cmd += ` -w "${wordlistPath}"`;
    
    // Extensions
    if (config.extensions.length > 0) {
      cmd += ` -x ${config.extensions.join(',')}`;
    }
    
    // Depth
    if (config.depth !== 4) {
      cmd += ` -d ${config.depth}`;
    }
    
    // Threads
    if (config.threads !== 50) {
      cmd += ` -t ${config.threads}`;
    }
    
    // Timeout
    if (config.timeout !== 7) {
      cmd += ` -T ${config.timeout}`;
    }
    
    // Delay
    if (config.delay > 0) {
      cmd += ` --scan-delay ${config.delay}`;
    }
    
    // User Agent
    if (config.userAgent) {
      cmd += ` -a "${config.userAgent}"`;
    }
    
    // Headers
    Object.entries(config.headers).forEach(([key, value]) => {
      cmd += ` -H "${key}: ${value}"`;
    });
    
    // Authentication
    if (config.authentication.enabled) {
      cmd += ` --auth-user "${config.authentication.username}"`;
      cmd += ` --auth-password "${config.authentication.password}"`;
      if (config.authentication.type === 'digest') {
        cmd += ' --auth-digest';
      }
    }
    
    // Status code filters
    if (config.filters.statusCodes.length > 0) {
      cmd += ` -s ${config.filters.statusCodes.join(',')}`;
    }
    
    if (config.filters.excludeStatusCodes.length > 0) {
      cmd += ` -C ${config.filters.excludeStatusCodes.join(',')}`;
    }
    
    // Size filters
    if (config.filters.excludeContentLength.length > 0) {
      cmd += ` -S ${config.filters.excludeContentLength.join(',')}`;
    }
    
    if (config.filters.excludeWords.length > 0) {
      cmd += ` -W ${config.filters.excludeWords.join(',')}`;
    }
    
    if (config.filters.excludeLines.length > 0) {
      cmd += ` -N ${config.filters.excludeLines.join(',')}`;
    }
    
    // Regex filters
    if (config.filters.regex) {
      cmd += ` --filter-regex "${config.filters.regex}"`;
    }
    
    if (config.filters.excludeRegex) {
      cmd += ` --filter-regex "${config.filters.excludeRegex}"`;
    }
    
    // Scanning options
    if (config.scanning.noRecursion) {
      cmd += ' --no-recursion';
    }
    
    if (config.scanning.extractLinks) {
      cmd += ' --extract-links';
    }
    
    if (config.scanning.followRedirects) {
      cmd += ' -r';
    }
    
    if (!config.scanning.autoTune) {
      cmd += ' --dont-auto-tune';
    }
    
    if (config.scanning.randomUserAgent) {
      cmd += ' --random-agent';
    }
    
    if (config.scanning.collectBackups) {
      cmd += ' --collect-backups';
    }
    
    if (config.scanning.collectWords) {
      cmd += ' --collect-words';
    }
    
    // Proxy
    if (config.proxy.enabled && config.proxy.url) {
      cmd += ` --proxy "${config.proxy.url}"`;
    }
    
    // Output format
    if (config.output.format !== 'default') {
      cmd += ` --output-format ${config.output.format}`;
    }
    
    if (config.output.file) {
      cmd += ` -o "${config.output.file}"`;
    }
    
    // Verbosity
    if (config.output.quiet) {
      cmd += ' -q';
    }
    
    if (config.output.silent) {
      cmd += ' --silent';
    }
    
    if (config.output.noColor) {
      cmd += ' --no-color';
    }
    
    if (config.output.verbosity > 1) {
      cmd += ' -v'.repeat(config.output.verbosity - 1);
    }
    
    // Rate limiting
    if (config.rateLimit.enabled && config.rateLimit.requestsPerSecond > 0) {
      cmd += ` --rate-limit ${config.rateLimit.requestsPerSecond}`;
    }
    
    // Resume
    if (config.resume.enabled && config.resume.stateFile) {
      cmd += ` --resume-from "${config.resume.stateFile}"`;
    }
    
    // Custom arguments
    if (config.customArgs) {
      cmd += ` ${config.customArgs}`;
    }
    
    return cmd;
  };

  const isScanning = execution?.status === 'running';
  const canStart = connectionStatus === 'connected' && config.url.trim() && !isScanning;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center gap-3">
        <div className="flex items-center justify-center w-10 h-10 rounded-lg bg-gradient-to-br from-orange-500 to-red-600">
          <Crosshair className="h-5 w-5 text-white" />
        </div>
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100">FeroxBuster Scanner</h2>
          <p className="text-gray-600 dark:text-gray-400">Fast, simple, recursive content discovery tool built in Rust</p>
        </div>
      </div>

      {/* Status Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Status</p>
                <p className="text-2xl font-bold">
                  {isScanning ? 'Scanning' : 'Ready'}
                </p>
              </div>
              <Activity className={cn("h-8 w-8", isScanning ? "text-green-600" : "text-gray-400")} />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Depth</p>
                <p className="text-2xl font-bold">{config.depth}</p>
              </div>
              <Layers className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Threads</p>
                <p className="text-2xl font-bold">{config.threads}</p>
              </div>
              <TrendingUp className="h-8 w-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Progress</p>
                <p className="text-2xl font-bold">{execution ? `${execution.progress}%` : '0%'}</p>
              </div>
              <BarChart3 className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Interface */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <FastForward className="h-5 w-5" />
                FeroxBuster Configuration
              </CardTitle>
              <CardDescription>
                Configure content discovery parameters and start recursive scanning
              </CardDescription>
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => navigator.clipboard.writeText(generateCommand())}
              >
                <Copy className="h-4 w-4 mr-2" />
                Copy Command
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setConfig(prev => ({ 
                  ...prev, 
                  url: '', 
                  customWordlist: '', 
                  extensions: [],
                  headers: {},
                  customArgs: ''
                }))}
              >
                <RotateCcw className="h-4 w-4 mr-2" />
                Reset
              </Button>
              {isScanning ? (
                <Button variant="destructive" size="sm" onClick={stopScan}>
                  <Square className="h-4 w-4 mr-2" />
                  Stop Scan
                </Button>
              ) : (
                <Button 
                  size="sm" 
                  onClick={startScan}
                  disabled={!canStart}
                >
                  <Play className="h-4 w-4 mr-2" />
                  Start Scan
                </Button>
              )}
            </div>
          </div>
        </CardHeader>

        <CardContent>
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="configure">Configure</TabsTrigger>
              <TabsTrigger value="output">Output</TabsTrigger>
              <TabsTrigger value="results">Results</TabsTrigger>
              <TabsTrigger value="command">Command</TabsTrigger>
            </TabsList>

            <TabsContent value="configure" className="space-y-6">
              {/* Quick Presets */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Quick Presets</h3>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                  <Button variant="outline" onClick={() => applyPreset('quick_scan')}>
                    <Zap className="h-4 w-4 mr-2" />
                    Quick Scan
                  </Button>
                  <Button variant="outline" onClick={() => applyPreset('comprehensive_scan')}>
                    <Search className="h-4 w-4 mr-2" />
                    Comprehensive
                  </Button>
                  <Button variant="outline" onClick={() => applyPreset('stealth_scan')}>
                    <Shield className="h-4 w-4 mr-2" />
                    Stealth Scan
                  </Button>
                  <Button variant="outline" onClick={() => applyPreset('backup_discovery')}>
                    <Database className="h-4 w-4 mr-2" />
                    Backup Discovery
                  </Button>
                  <Button variant="outline" onClick={() => applyPreset('api_discovery')}>
                    <Network className="h-4 w-4 mr-2" />
                    API Discovery
                  </Button>
                  <Button variant="outline" onClick={() => applyPreset('aggressive_scan')}>
                    <Crosshair className="h-4 w-4 mr-2" />
                    Aggressive Scan
                  </Button>
                </div>
              </div>

              {/* Target Configuration */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Target Configuration</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Target URL</label>
                    <Input
                      value={config.url}
                      onChange={(e) => updateConfig('url', e.target.value)}
                      placeholder="https://example.com"
                    />
                  </div>

                  <div className="space-y-2">
                    <label className="text-sm font-medium">Wordlist</label>
                    <Select value={config.wordlist} onValueChange={(value) => updateConfig('wordlist', value)}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="feroxbuster_default">FeroxBuster Default</SelectItem>
                        <SelectItem value="raft_directories_lowercase">Raft Directories (Lowercase)</SelectItem>
                        <SelectItem value="raft_directories">Raft Directories</SelectItem>
                        <SelectItem value="raft_files_lowercase">Raft Files (Lowercase)</SelectItem>
                        <SelectItem value="raft_files">Raft Files</SelectItem>
                        <SelectItem value="dirb_common">Dirb Common</SelectItem>
                        <SelectItem value="dirb_big">Dirb Big</SelectItem>
                        <SelectItem value="dirbuster_medium">DirBuster Medium</SelectItem>
                        <SelectItem value="dirbuster_small">DirBuster Small</SelectItem>
                        <SelectItem value="seclist_directory_2_3_medium">SecLists Directory 2.3 Medium</SelectItem>
                        <SelectItem value="seclist_common">SecLists Common</SelectItem>
                        <SelectItem value="quickhits">Quick Hits</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">Custom Wordlist Path</label>
                  <Input
                    value={config.customWordlist}
                    onChange={(e) => updateConfig('customWordlist', e.target.value)}
                    placeholder="/path/to/custom/wordlist.txt"
                  />
                </div>
              </div>

              {/* Extensions Configuration */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">File Extensions</h3>
                
                <div className="space-y-3">
                  <div className="flex flex-wrap gap-2">
                    {Object.entries(COMMON_EXTENSIONS).map(([group, extensions]) => {
                      const hasAll = extensions.every(ext => config.extensions.includes(ext));
                      return (
                        <Button
                          key={group}
                          variant={hasAll ? "default" : "outline"}
                          size="sm"
                          onClick={() => toggleExtensionGroup(group)}
                        >
                          {group.charAt(0).toUpperCase() + group.slice(1)} ({extensions.length})
                        </Button>
                      );
                    })}
                  </div>
                  
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Selected Extensions</label>
                    <div className="flex flex-wrap gap-1">
                      {config.extensions.map(ext => (
                        <Badge
                          key={ext}
                          variant="secondary"
                          className="cursor-pointer"
                          onClick={() => updateConfig('extensions', config.extensions.filter(e => e !== ext))}
                        >
                          {ext} ×
                        </Badge>
                      ))}
                    </div>
                    <Input
                      placeholder="Add custom extensions (comma-separated)"
                      onKeyPress={(e) => {
                        if (e.key === 'Enter') {
                          const newExts = e.target.value.split(',').map(s => s.trim()).filter(s => s);
                          updateConfig('extensions', [...new Set([...config.extensions, ...newExts])]);
                          e.target.value = '';
                        }
                      }}
                    />
                  </div>
                </div>
              </div>

              {/* Scanning Parameters */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Scanning Parameters</h3>
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Recursion Depth</label>
                    <Input
                      type="number"
                      value={config.depth}
                      onChange={(e) => updateConfig('depth', parseInt(e.target.value) || 4)}
                      min="1"
                      max="20"
                    />
                  </div>

                  <div className="space-y-2">
                    <label className="text-sm font-medium">Threads</label>
                    <Input
                      type="number"
                      value={config.threads}
                      onChange={(e) => updateConfig('threads', parseInt(e.target.value) || 50)}
                      min="1"
                      max="200"
                    />
                  </div>

                  <div className="space-y-2">
                    <label className="text-sm font-medium">Timeout (s)</label>
                    <Input
                      type="number"
                      value={config.timeout}
                      onChange={(e) => updateConfig('timeout', parseInt(e.target.value) || 7)}
                      min="1"
                      max="60"
                    />
                  </div>

                  <div className="space-y-2">
                    <label className="text-sm font-medium">Delay (ms)</label>
                    <Input
                      type="number"
                      value={config.delay}
                      onChange={(e) => updateConfig('delay', parseInt(e.target.value) || 0)}
                      min="0"
                      max="10000"
                    />
                  </div>
                </div>
              </div>

              {/* Status Code Filters */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Status Code Filters</h3>
                
                <div className="space-y-3">
                  <div className="flex flex-wrap gap-2">
                    {Object.entries(STATUS_CODE_GROUPS).map(([group, codes]) => {
                      const hasAll = codes.every(code => config.filters.statusCodes.includes(code));
                      return (
                        <Button
                          key={group}
                          variant={hasAll ? "default" : "outline"}
                          size="sm"
                          onClick={() => toggleStatusCodeGroup(group)}
                        >
                          {group.replace('_', ' ').toUpperCase()} ({codes.length})
                        </Button>
                      );
                    })}
                  </div>
                  
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Include Status Codes</label>
                    <div className="flex flex-wrap gap-1">
                      {config.filters.statusCodes.map(code => (
                        <Badge
                          key={code}
                          variant="secondary"
                          className="cursor-pointer"
                          onClick={() => updateConfig('filters.statusCodes', config.filters.statusCodes.filter(c => c !== code))}
                        >
                          {code} ×
                        </Badge>
                      ))}
                    </div>
                  </div>

                  <div className="space-y-2">
                    <label className="text-sm font-medium">Exclude Status Codes</label>
                    <Input
                      value={config.filters.excludeStatusCodes.join(',')}
                      onChange={(e) => updateConfig('filters.excludeStatusCodes', e.target.value.split(',').map(s => s.trim()).filter(s => s))}
                      placeholder="404,500,502"
                    />
                  </div>
                </div>
              </div>

              {/* Scanning Options */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Scanning Options</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="noRecursion"
                      checked={config.scanning.noRecursion}
                      onChange={(e) => updateConfig('scanning.noRecursion', e.target.checked)}
                      className="rounded"
                    />
                    <label htmlFor="noRecursion" className="text-sm font-medium">No Recursion</label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="extractLinks"
                      checked={config.scanning.extractLinks}
                      onChange={(e) => updateConfig('scanning.extractLinks', e.target.checked)}
                      className="rounded"
                    />
                    <label htmlFor="extractLinks" className="text-sm font-medium">Extract Links</label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="followRedirects"
                      checked={config.scanning.followRedirects}
                      onChange={(e) => updateConfig('scanning.followRedirects', e.target.checked)}
                      className="rounded"
                    />
                    <label htmlFor="followRedirects" className="text-sm font-medium">Follow Redirects</label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="autoTune"
                      checked={config.scanning.autoTune}
                      onChange={(e) => updateConfig('scanning.autoTune', e.target.checked)}
                      className="rounded"
                    />
                    <label htmlFor="autoTune" className="text-sm font-medium">Auto-Tune Performance</label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="autoSkip"
                      checked={config.scanning.autoSkip}
                      onChange={(e) => updateConfig('scanning.autoSkip', e.target.checked)}
                      className="rounded"
                    />
                    <label htmlFor="autoSkip" className="text-sm font-medium">Auto-Skip Wildcards</label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="randomUserAgent"
                      checked={config.scanning.randomUserAgent}
                      onChange={(e) => updateConfig('scanning.randomUserAgent', e.target.checked)}
                      className="rounded"
                    />
                    <label htmlFor="randomUserAgent" className="text-sm font-medium">Random User Agent</label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="collectBackups"
                      checked={config.scanning.collectBackups}
                      onChange={(e) => updateConfig('scanning.collectBackups', e.target.checked)}
                      className="rounded"
                    />
                    <label htmlFor="collectBackups" className="text-sm font-medium">Collect Backup Files</label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="collectWords"
                      checked={config.scanning.collectWords}
                      onChange={(e) => updateConfig('scanning.collectWords', e.target.checked)}
                      className="rounded"
                    />
                    <label htmlFor="collectWords" className="text-sm font-medium">Collect Words</label>
                  </div>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="output" className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold">Real-time Output</h3>
                <div className="flex items-center gap-2">
                  {isScanning && (
                    <Badge variant="secondary" className="animate-pulse">
                      <Activity className="h-3 w-3 mr-1" />
                      Scanning in progress...
                    </Badge>
                  )}
                  <Button variant="outline" size="sm">
                    <Download className="h-4 w-4 mr-2" />
                    Export Output
                  </Button>
                </div>
              </div>
              
              {execution?.progress !== undefined && (
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Progress</span>
                    <span>{execution.progress}%</span>
                  </div>
                  <Progress value={execution.progress} className="w-full" />
                </div>
              )}

              <Card>
                <CardContent className="p-4">
                  <div className="bg-gray-900 dark:bg-gray-800 rounded-lg p-4 min-h-[400px] font-mono text-sm overflow-auto">
                    <div className="text-green-400">
                      {execution?.output || 'FeroxBuster output will appear here when scanning starts...'}
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="results" className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold">Scan Results</h3>
                <div className="flex items-center gap-2">
                  <Button variant="outline" size="sm">
                    <Download className="h-4 w-4 mr-2" />
                    Export Results
                  </Button>
                  <Button variant="outline" size="sm">
                    <Filter className="h-4 w-4 mr-2" />
                    Filter Results
                  </Button>
                </div>
              </div>

              {execution?.results ? (
                <div className="space-y-4">
                  {/* Results Summary */}
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <Card>
                      <CardContent className="pt-6">
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Total Found</p>
                            <p className="text-2xl font-bold">{execution.results.total || 0}</p>
                          </div>
                          <CheckCircle className="h-8 w-8 text-green-600" />
                        </div>
                      </CardContent>
                    </Card>

                    <Card>
                      <CardContent className="pt-6">
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Directories</p>
                            <p className="text-2xl font-bold">{execution.results.directories || 0}</p>
                          </div>
                          <FolderOpen className="h-8 w-8 text-blue-600" />
                        </div>
                      </CardContent>
                    </Card>

                    <Card>
                      <CardContent className="pt-6">
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Files</p>
                            <p className="text-2xl font-bold">{execution.results.files || 0}</p>
                          </div>
                          <FileText className="h-8 w-8 text-purple-600" />
                        </div>
                      </CardContent>
                    </Card>

                    <Card>
                      <CardContent className="pt-6">
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Status 200</p>
                            <p className="text-2xl font-bold">{execution.results.status_200 || 0}</p>
                          </div>
                          <CheckCircle className="h-8 w-8 text-green-600" />
                        </div>
                      </CardContent>
                    </Card>
                  </div>

                  {/* Results Table */}
                  <Card>
                    <CardContent className="p-0">
                      <div className="overflow-x-auto">
                        <table className="w-full">
                          <thead className="border-b">
                            <tr className="text-left">
                              <th className="p-4 font-medium">URL</th>
                              <th className="p-4 font-medium">Status</th>
                              <th className="p-4 font-medium">Size</th>
                              <th className="p-4 font-medium">Type</th>
                              <th className="p-4 font-medium">Words</th>
                            </tr>
                          </thead>
                          <tbody>
                            {execution.results.findings?.map((finding, index) => (
                              <tr key={index} className="border-b hover:bg-gray-50 dark:hover:bg-gray-800">
                                <td className="p-4 font-mono text-sm">{finding.url}</td>
                                <td className="p-4">
                                  <Badge 
                                    variant={finding.status >= 200 && finding.status < 300 ? 'default' : 
                                            finding.status >= 300 && finding.status < 400 ? 'secondary' : 'destructive'}
                                  >
                                    {finding.status}
                                  </Badge>
                                </td>
                                <td className="p-4 text-sm">{finding.size}</td>
                                <td className="p-4 text-sm">
                                  <Badge variant="outline">
                                    {finding.type || 'Unknown'}
                                  </Badge>
                                </td>
                                <td className="p-4 text-sm">{finding.words}</td>
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              ) : (
                <Card>
                  <CardContent className="p-8 text-center">
                    <Search className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">No Results Yet</h3>
                    <p className="text-gray-600 dark:text-gray-400">
                      Start a content discovery scan to see results here. Results will update in real-time as the scan progresses.
                    </p>
                  </CardContent>
                </Card>
              )}
            </TabsContent>

            <TabsContent value="command" className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold">Generated Command</h3>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => navigator.clipboard.writeText(generateCommand())}
                >
                  <Copy className="h-4 w-4 mr-2" />
                  Copy Command
                </Button>
              </div>

              <Card>
                <CardContent className="p-4">
                  <div className="bg-gray-900 dark:bg-gray-800 rounded-lg p-4 overflow-x-auto">
                    <code className="text-green-400 whitespace-pre-wrap break-all">
                      {generateCommand()}
                    </code>
                  </div>
                </CardContent>
              </Card>

              <div className="space-y-4">
                <h4 className="font-semibold">Command Explanation</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                  <div>
                    <h5 className="font-medium mb-2">Basic Options</h5>
                    <ul className="space-y-1 text-gray-600 dark:text-gray-400">
                      <li>• <code>-u</code>: Target URL</li>
                      <li>• <code>-w</code>: Wordlist to use</li>
                      <li>• <code>-x</code>: File extensions</li>
                      <li>• <code>-d</code>: Recursion depth</li>
                    </ul>
                  </div>
                  <div>
                    <h5 className="font-medium mb-2">Performance</h5>
                    <ul className="space-y-1 text-gray-600 dark:text-gray-400">
                      <li>• <code>-t</code>: Number of threads</li>
                      <li>• <code>-T</code>: Request timeout</li>
                      <li>• <code>--scan-delay</code>: Delay between requests</li>
                      <li>• <code>--rate-limit</code>: Requests per second</li>
                    </ul>
                  </div>
                  <div>
                    <h5 className="font-medium mb-2">Filtering</h5>
                    <ul className="space-y-1 text-gray-600 dark:text-gray-400">
                      <li>• <code>-s</code>: Status codes to include</li>
                      <li>• <code>-C</code>: Status codes to exclude</li>
                      <li>• <code>-S</code>: Filter by content size</li>
                      <li>• <code>-W</code>: Filter by word count</li>
                    </ul>
                  </div>
                  <div>
                    <h5 className="font-medium mb-2">Advanced</h5>
                    <ul className="space-y-1 text-gray-600 dark:text-gray-400">
                      <li>• <code>--extract-links</code>: Extract links from responses</li>
                      <li>• <code>--collect-backups</code>: Find backup files</li>
                      <li>• <code>--random-agent</code>: Use random user agents</li>
                      <li>• <code>-r</code>: Follow redirects</li>
                    </ul>
                  </div>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}