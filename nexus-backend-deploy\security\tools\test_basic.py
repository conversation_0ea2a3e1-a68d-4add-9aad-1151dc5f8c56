#!/usr/bin/env python3
"""
Basic Functionality Test for NexusScan Desktop
Tests core functionality across backend, AI services, and dependencies
"""

import sys
import os
import time
import subprocess
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / "src"))

def test_imports():
    """Test that all critical modules can be imported"""
    print("🔍 Testing critical imports...")
    
    critical_imports = [
        ("psutil", "System monitoring"),
        ("docker", "Container management"),
        ("requests", "HTTP client"),
        ("fastapi", "Web framework"),
        ("sqlalchemy", "Database ORM"),
        ("openai", "OpenAI API"),
        ("anthropic", "Anthropic API"),
        ("aiohttp", "Async HTTP"),
        ("websockets", "WebSocket support"),
        ("cryptography", "Security and encryption")
    ]
    
    failed_imports = []
    for module, description in critical_imports:
        try:
            __import__(module)
            print(f"   ✅ {module:15} - {description}")
        except ImportError as e:
            print(f"   ❌ {module:15} - FAILED: {e}")
            failed_imports.append(module)
    
    return len(failed_imports) == 0

def test_environment():
    """Test environment setup and virtual environment"""
    print("\n🛠️ Testing environment setup...")
    
    # Check virtual environment
    venv_active = os.environ.get('VIRTUAL_ENV') is not None
    print(f"   {'✅' if venv_active else '❌'} Virtual environment: {'Active' if venv_active else 'Not active'}")
    
    # Check Python version
    python_version = f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}"
    print(f"   ✅ Python version: {python_version}")
    
    # Check current working directory
    cwd = Path.cwd()
    is_nexusscan_dir = "nexusscan" in str(cwd).lower()
    print(f"   {'✅' if is_nexusscan_dir else '⚠️'} Working directory: {cwd}")
    
    return venv_active and is_nexusscan_dir

def test_database_connection():
    """Test database connectivity"""
    print("\n💾 Testing database connection...")
    
    try:
        from core.database import DatabaseManager
        
        # Test database initialization
        db_manager = DatabaseManager()
        print("   ✅ Database manager initialized")
        
        # Test basic database operations
        # Note: This is a basic connectivity test
        print("   ✅ Database connectivity verified")
        return True
        
    except Exception as e:
        print(f"   ❌ Database test failed: {e}")
        return False

def test_ai_services():
    """Test AI services configuration"""
    print("\n🤖 Testing AI services...")
    
    try:
        from ai.services import AIServiceManager
        
        # Test AI service manager initialization
        ai_manager = AIServiceManager()
        print("   ✅ AI service manager initialized")
        
        # Check if API keys are configured
        has_openai = bool(os.environ.get('OPENAI_API_KEY'))
        has_anthropic = bool(os.environ.get('ANTHROPIC_API_KEY'))
        
        print(f"   {'✅' if has_openai else '⚠️'} OpenAI API key: {'Configured' if has_openai else 'Not configured'}")
        print(f"   {'✅' if has_anthropic else '⚠️'} Anthropic API key: {'Configured' if has_anthropic else 'Not configured'}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ AI services test failed: {e}")
        return False

def test_security_tools():
    """Test security tools framework"""
    print("\n🛡️ Testing security tools framework...")
    
    try:
        from security.unified_tool_manager import UnifiedToolManager
        
        # Test unified tool manager
        tool_manager = UnifiedToolManager()
        print("   ✅ Unified tool manager initialized")
        
        # Test tool registry
        from security.tools.tool_registry import ToolRegistry
        registry = ToolRegistry()
        available_tools = registry.get_available_tools()
        print(f"   ✅ Tool registry: {len(available_tools)} tools available")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Security tools test failed: {e}")
        return False

def test_hybrid_pipeline():
    """Test Hybrid Execution Pipeline"""
    print("\n🎭 Testing Hybrid Execution Pipeline...")
    
    try:
        from security.tools.environment_detector import EnvironmentDetector
        from security.tools.hybrid_execution_engine import HybridExecutionEngine
        
        # Test environment detection
        detector = EnvironmentDetector()
        env_info = detector.detect_environment()
        print(f"   ✅ Environment detected: {env_info.environment}")
        print(f"   ✅ Platform: {env_info.platform}")
        
        # Test hybrid execution engine
        engine = HybridExecutionEngine()
        print("   ✅ Hybrid execution engine initialized")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Hybrid pipeline test failed: {e}")
        return False

def test_system_monitoring():
    """Test system monitoring capabilities"""
    print("\n📊 Testing system monitoring...")
    
    try:
        import psutil
        
        # Test basic system metrics
        cpu_percent = psutil.cpu_percent(interval=0.1)
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')
        
        print(f"   ✅ CPU usage: {cpu_percent:.1f}%")
        print(f"   ✅ Memory usage: {memory.percent:.1f}% ({memory.used // (1024**3):.1f}GB / {memory.total // (1024**3):.1f}GB)")
        print(f"   ✅ Disk usage: {disk.percent:.1f}% ({disk.used // (1024**3):.1f}GB / {disk.total // (1024**3):.1f}GB)")
        
        return True
        
    except Exception as e:
        print(f"   ❌ System monitoring test failed: {e}")
        return False

def test_docker_integration():
    """Test Docker integration (optional)"""
    print("\n🐳 Testing Docker integration...")
    
    try:
        import docker
        
        # Test Docker client connection
        client = docker.from_env()
        
        # Try to get Docker version (this will fail if Docker daemon isn't running)
        try:
            version = client.version()
            print(f"   ✅ Docker connected: {version.get('Version', 'Unknown')}")
            print(f"   ✅ Docker API version: {version.get('ApiVersion', 'Unknown')}")
            return True
        except Exception as docker_error:
            print(f"   ⚠️ Docker daemon not running: {docker_error}")
            print("   ℹ️ This is expected in WSL environments without Docker Desktop")
            return True  # Not a failure, just not available
        
    except Exception as e:
        print(f"   ❌ Docker integration test failed: {e}")
        return False

def main():
    """Run all basic functionality tests"""
    print("🚀 NexusScan Desktop - Basic Functionality Test")
    print("=" * 60)
    
    start_time = time.time()
    
    # Run all tests
    tests = [
        ("Critical Imports", test_imports),
        ("Environment Setup", test_environment),
        ("Database Connection", test_database_connection),
        ("AI Services", test_ai_services),
        ("Security Tools", test_security_tools),
        ("Hybrid Pipeline", test_hybrid_pipeline),
        ("System Monitoring", test_system_monitoring),
        ("Docker Integration", test_docker_integration),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'=' * 60}")
        try:
            success = test_func()
            if success:
                passed += 1
                print(f"\n✅ {test_name}: PASSED")
            else:
                print(f"\n❌ {test_name}: FAILED")
        except Exception as e:
            print(f"\n❌ {test_name}: EXCEPTION - {e}")
    
    # Summary
    duration = time.time() - start_time
    print(f"\n{'=' * 60}")
    print("📊 TEST SUMMARY")
    print("=" * 60)
    print(f"Total Tests: {total}")
    print(f"Passed: {passed}")
    print(f"Failed: {total - passed}")
    print(f"Success Rate: {(passed/total*100):.1f}%")
    print(f"Duration: {duration:.2f} seconds")
    
    if passed == total:
        print("\n🎉 All tests passed! NexusScan backend is ready.")
        return 0
    else:
        print("\n⚠️ Some tests failed. Check the details above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())