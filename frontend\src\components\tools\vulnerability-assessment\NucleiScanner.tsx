/**
 * Nuclei Scanner Component
 * Fast and customizable vulnerability scanner based on simple YAML templates
 */
import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import {
  Target,
  Play,
  Square,
  Download,
  Settings,
  RefreshCw,
  Globe,
  FileText,
  Terminal,
  Copy,
  RotateCcw,
  Clock,
  Zap,
  Filter,
  Eye,
  AlertTriangle,
  Shield,
  CheckCircle,
  TrendingUp,
  Activity,
  Lock,
  Search,
  List,
  BarChart3,
  Database,
  Server,
  Bug,
  Info,
  Code,
  Package,
  Layers,
  Crosshair,
  Cpu,
  Network,
  Gauge,
  Tag,
  Hash,
  Workflow,
  GitBranch,
  Book
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useBackendStore } from '@/stores/backend-store';
import { apiClient, ToolExecution } from '@/services/api-client';
import { toast } from 'sonner';

/**
 * Nuclei configuration interface
 */
interface NucleiConfig {
  targets: string[];
  inputFile: string;
  templates: {
    enabled: string[];
    disabled: string[];
    categories: string[];
    severities: ('info' | 'low' | 'medium' | 'high' | 'critical')[];
    customTemplates: string[];
    workflowTemplates: string[];
  };
  scanning: {
    concurrency: number;
    bulkSize: number;
    timeout: number;
    retries: number;
    rateLimit: number;
    randomAgent: boolean;
    followRedirects: boolean;
    maxRedirects: number;
    disableUpdateCheck: boolean;
  };
  filters: {
    excludeTags: string[];
    includeTags: string[];
    includeTemplates: string[];
    excludeTemplates: string[];
    author: string[];
    excludeMatchers: string[];
  };
  output: {
    format: 'table' | 'json' | 'jsonl' | 'csv' | 'sarif' | 'md';
    file: string;
    verbose: boolean;
    silent: boolean;
    noColor: boolean;
    includeRaw: boolean;
    markdown: boolean;
    stats: boolean;
  };
  authentication: {
    enabled: boolean;
    username: string;
    password: string;
    headers: { [key: string]: string };
    cookies: string;
  };
  proxy: {
    enabled: boolean;
    url: string;
    credentials: string;
  };
  system: {
    updateTemplates: boolean;
    templateDirectory: string;
    configFile: string;
    systemResolvers: boolean;
    offlineHTTP: boolean;
    envVars: { [key: string]: string };
  };
  advanced: {
    interactsh: boolean;
    interactions: boolean;
    projectFile: string;
    resumeScan: boolean;
    sandbox: boolean;
    stopAtFirstMatch: boolean;
    metrics: boolean;
    debug: boolean;
    debugResponse: boolean;
    trace: boolean;
  };
  customArgs: string;
}

/**
 * Nuclei template categories
 */
const TEMPLATE_CATEGORIES = {
  'cves': { name: 'CVEs', description: 'Common Vulnerabilities and Exposures', count: '2500+' },
  'vulnerabilities': { name: 'Vulnerabilities', description: 'General vulnerability templates', count: '1500+' },
  'exposures': { name: 'Exposures', description: 'Information disclosure and exposures', count: '800+' },
  'technologies': { name: 'Technologies', description: 'Technology detection templates', count: '600+' },
  'misconfiguration': { name: 'Misconfigurations', description: 'Common misconfigurations', count: '400+' },
  'default-logins': { name: 'Default Logins', description: 'Default credential checks', count: '300+' },
  'takeovers': { name: 'Takeovers', description: 'Subdomain takeover templates', count: '200+' },
  'file': { name: 'File Checks', description: 'File-based vulnerabilities', count: '150+' },
  'workflows': { name: 'Workflows', description: 'Multi-step scan workflows', count: '100+' },
  'headless': { name: 'Headless', description: 'Browser-based templates', count: '80+' },
  'code': { name: 'Code Analysis', description: 'Source code analysis', count: '70+' },
  'dns': { name: 'DNS', description: 'DNS-based templates', count: '60+' },
  'ssl': { name: 'SSL/TLS', description: 'SSL/TLS security checks', count: '50+' },
  'network': { name: 'Network', description: 'Network service templates', count: '40+' }
};

/**
 * Severity levels configuration
 */
const SEVERITY_LEVELS = {
  'info': { name: 'Info', color: 'text-blue-600', bgColor: 'bg-blue-50', description: 'Informational findings' },
  'low': { name: 'Low', color: 'text-green-600', bgColor: 'bg-green-50', description: 'Low risk vulnerabilities' },
  'medium': { name: 'Medium', color: 'text-yellow-600', bgColor: 'bg-yellow-50', description: 'Medium risk vulnerabilities' },
  'high': { name: 'High', color: 'text-orange-600', bgColor: 'bg-orange-50', description: 'High risk vulnerabilities' },
  'critical': { name: 'Critical', color: 'text-red-600', bgColor: 'bg-red-50', description: 'Critical vulnerabilities' }
};

/**
 * Output formats
 */
const OUTPUT_FORMATS = {
  'table': { name: 'Table', description: 'Human-readable table format' },
  'json': { name: 'JSON', description: 'Structured JSON output' },
  'jsonl': { name: 'JSON Lines', description: 'Line-delimited JSON' },
  'csv': { name: 'CSV', description: 'Comma-separated values' },
  'sarif': { name: 'SARIF', description: 'Static Analysis Results Interchange Format' },
  'md': { name: 'Markdown', description: 'Markdown report format' }
};

/**
 * Common template tags
 */
const COMMON_TAGS = [
  'oob', 'rce', 'xss', 'sqli', 'ssrf', 'cve', 'lfi', 'rfi', 'redirect', 'disclosure',
  'misconfig', 'default-login', 'takeover', 'dos', 'injection', 'auth-bypass',
  'panel', 'cms', 'wordpress', 'joomla', 'drupal', 'apache', 'nginx', 'iis'
];

export default function NucleiScanner() {
  const { status } = useBackendStore();
  const [config, setConfig] = React.useState<NucleiConfig>({
    targets: [''],
    inputFile: '',
    templates: {
      enabled: [],
      disabled: [],
      categories: ['cves', 'vulnerabilities', 'exposures'],
      severities: ['medium', 'high', 'critical'],
      customTemplates: [],
      workflowTemplates: []
    },
    scanning: {
      concurrency: 25,
      bulkSize: 25,
      timeout: 5,
      retries: 1,
      rateLimit: 150,
      randomAgent: false,
      followRedirects: false,
      maxRedirects: 3,
      disableUpdateCheck: false
    },
    filters: {
      excludeTags: [],
      includeTags: [],
      includeTemplates: [],
      excludeTemplates: [],
      author: [],
      excludeMatchers: []
    },
    output: {
      format: 'json',
      file: '',
      verbose: false,
      silent: false,
      noColor: false,
      includeRaw: false,
      markdown: false,
      stats: true
    },
    authentication: {
      enabled: false,
      username: '',
      password: '',
      headers: {},
      cookies: ''
    },
    proxy: {
      enabled: false,
      url: '',
      credentials: ''
    },
    system: {
      updateTemplates: false,
      templateDirectory: '',
      configFile: '',
      systemResolvers: true,
      offlineHTTP: false,
      envVars: {}
    },
    advanced: {
      interactsh: true,
      interactions: false,
      projectFile: '',
      resumeScan: false,
      sandbox: false,
      stopAtFirstMatch: false,
      metrics: true,
      debug: false,
      debugResponse: false,
      trace: false
    },
    customArgs: ''
  });

  const [execution, setExecution] = React.useState<ToolExecution | null>(null);
  const [activeTab, setActiveTab] = React.useState('configure');
  const [templateUpdate, setTemplateUpdate] = React.useState(false);

  // Progress tracking states (like Nmap)
  const [isScanning, setIsScanning] = React.useState(false);
  const [progress, setProgress] = React.useState(0);
  const [output, setOutput] = React.useState<string[]>([]);
  const [results, setResults] = React.useState<any>(null);

  // Backend connection
  const backendStatus = { connected: status.connected };

  /**
   * Handle configuration changes
   */
  const updateConfig = (path: string, value: any) => {
    setConfig(prev => {
      const keys = path.split('.');
      const newConfig = { ...prev };
      let current = newConfig;
      
      for (let i = 0; i < keys.length - 1; i++) {
        current[keys[i]] = { ...current[keys[i]] };
        current = current[keys[i]];
      }
      
      current[keys[keys.length - 1]] = value;
      return newConfig;
    });
  };

  /**
   * Handle target list changes
   */
  const updateTargets = (targets: string) => {
    const targetList = targets.split('\n').map(t => t.trim()).filter(t => t);
    updateConfig('targets', targetList);
  };

  /**
   * Handle preset configurations
   */
  const applyPreset = (preset: string) => {
    const presets = {
      'quick_scan': {
        templates: {
          ...config.templates,
          categories: ['cves', 'vulnerabilities'],
          severities: ['high', 'critical']
        },
        scanning: { ...config.scanning, concurrency: 10, timeout: 3 },
        output: { ...config.output, format: 'table', stats: true }
      },
      'comprehensive_scan': {
        templates: {
          ...config.templates,
          categories: Object.keys(TEMPLATE_CATEGORIES),
          severities: ['info', 'low', 'medium', 'high', 'critical']
        },
        scanning: { ...config.scanning, concurrency: 50, timeout: 10 },
        output: { ...config.output, format: 'json', verbose: true, stats: true }
      },
      'cve_focused': {
        templates: {
          ...config.templates,
          categories: ['cves'],
          severities: ['medium', 'high', 'critical']
        },
        scanning: { ...config.scanning, concurrency: 30 },
        filters: { ...config.filters, includeTags: ['cve'] }
      },
      'web_app_scan': {
        templates: {
          ...config.templates,
          categories: ['vulnerabilities', 'exposures', 'misconfiguration'],
          severities: ['low', 'medium', 'high', 'critical']
        },
        filters: { ...config.filters, includeTags: ['xss', 'sqli', 'ssrf', 'lfi', 'rfi'] }
      },
      'misconfiguration_scan': {
        templates: {
          ...config.templates,
          categories: ['misconfiguration', 'default-logins', 'exposures'],
          severities: ['info', 'low', 'medium', 'high']
        },
        filters: { ...config.filters, includeTags: ['misconfig', 'default-login', 'disclosure'] }
      },
      'stealth_scan': {
        templates: {
          ...config.templates,
          categories: ['cves', 'vulnerabilities'],
          severities: ['high', 'critical']
        },
        scanning: {
          ...config.scanning,
          concurrency: 5,
          rateLimit: 50,
          randomAgent: true,
          timeout: 15
        }
      }
    };

    if (presets[preset]) {
      setConfig(prev => ({ ...prev, ...presets[preset] }));
    }
  };

  /**
   * Toggle template category
   */
  const toggleTemplateCategory = (category: string) => {
    const categories = config.templates.categories.includes(category)
      ? config.templates.categories.filter(c => c !== category)
      : [...config.templates.categories, category];
    updateConfig('templates.categories', categories);
  };

  /**
   * Toggle severity level
   */
  const toggleSeverity = (severity: string) => {
    const severities = config.templates.severities.includes(severity)
      ? config.templates.severities.filter(s => s !== severity)
      : [...config.templates.severities, severity];
    updateConfig('templates.severities', severities);
  };

  /**
   * Update templates
   */
  const updateTemplates = async () => {
    setTemplateUpdate(true);
    try {
      // Template updates are handled automatically by the backend
      toast.success('Nuclei templates are automatically updated by the backend');
    } catch (error) {
      toast.error(`Failed to update templates: ${error.message}`);
    } finally {
      setTemplateUpdate(false);
    }
  };

  /**
   * Start Nuclei scan
   */
  const startScan = async () => {
    const validTargets = config.targets.filter(t => t.trim());
    if (validTargets.length === 0 && !config.inputFile) {
      toast.error('Please provide at least one target URL or input file');
      return;
    }

    if (config.templates.categories.length === 0 && config.templates.enabled.length === 0) {
      toast.error('Please select at least one template category or specific template');
      return;
    }

    if (!backendStatus.connected) {
      toast.error('Backend not connected');
      return;
    }

    try {
      setIsScanning(true);
      setProgress(0);
      setOutput([]);
      setResults(null);
      setActiveTab('output');

      console.log('🚀 Starting Nuclei scan with config:', config);

      // Prepare targets for backend API
      const targets = validTargets.length > 0 ? validTargets : [config.inputFile].filter(Boolean);

      const execution = await apiClient.executeTool('nuclei', {
        target: targets[0], // Backend expects single target for now
        timeout: config.timeout || 300,
        threads: config.concurrency || 25,
        output_format: 'json',
        options: {
          templates: config.templates,
          severity: config.severity,
          tags: config.tags,
          exclude_tags: config.excludeTags,
          rate_limit: config.rateLimit,
          bulk_size: config.bulkSize,
          template_threads: config.templateThreads,
          custom_headers: config.customHeaders,
          proxy: config.proxy,
          user_agent: config.userAgent,
          follow_redirects: config.followRedirects,
          max_redirects: config.maxRedirects,
          disable_clustering: config.disableClustering,
          passive: config.passive,
          force_attempt_http2: config.forceAttemptHTTP2,
          dialer_timeout: config.dialerTimeout,
          dialer_keep_alive: config.dialerKeepAlive,
          max_host_error: config.maxHostError,
          track_error: config.trackError,
          max_template_error: config.maxTemplateError
        }
      }, (progressUpdate) => {
        console.log('📊 Nuclei progress update:', progressUpdate);
        setExecution(progressUpdate);
        setProgress(progressUpdate.progress);
        if (progressUpdate.output) {
          setOutput(prev => [...prev, ...progressUpdate.output!]);
        }

        if (progressUpdate.status === 'completed') {
          setIsScanning(false);

          // Parse the backend response structure
          let parsedResults = progressUpdate.results;

          // Handle nested backend response structure
          if (progressUpdate.results?.parsed_results) {
            const backendData = progressUpdate.results.parsed_results;

            // Extract severity counts from the nested structure
            const severityCounts = backendData.statistics?.severity_counts || {};

            // Transform to frontend-expected format
            parsedResults = {
              critical: severityCounts.critical || 0,
              high: severityCounts.high || 0,
              medium: severityCounts.medium || 0,
              low: severityCounts.low || 0,
              info: severityCounts.info || 0,
              vulnerabilities: backendData.vulnerabilities || [],
              detailed_findings: backendData.detailed_findings || [],
              raw_backend_data: progressUpdate.results // Keep original for debugging
            };
          }

          setResults(parsedResults);
          console.log('✅ Nuclei scan completed with results:', parsedResults);
          console.log('🔍 Original backend response:', progressUpdate.results);
          toast.success('Nuclei scan completed');
        } else if (progressUpdate.status === 'failed') {
          setIsScanning(false);
          console.error('❌ Nuclei scan failed:', progressUpdate.error);
          toast.error(`Scan failed: ${progressUpdate.error}`);
        }
      });

      setExecution(execution);
      toast.info('Nuclei scan started');
    } catch (error) {
      setIsScanning(false);
      console.error('Failed to start Nuclei scan:', error);
      toast.error(`Failed to start Nuclei scan: ${error.message}`);
    }
  };

  /**
   * Stop scan
   */
  const stopScan = async () => {
    if (isScanning) {
      setIsScanning(false);
      setProgress(0);
      toast.success('Nuclei scan stopped');
    }
  };

  /**
   * Generate command line
   */
  const generateCommand = () => {
    let cmd = 'nuclei';
    
    // Targets
    if (config.inputFile) {
      cmd += ` -l "${config.inputFile}"`;
    } else {
      config.targets.filter(t => t.trim()).forEach(target => {
        cmd += ` -u "${target}"`;
      });
    }
    
    // Template categories
    if (config.templates.categories.length > 0) {
      cmd += ` -tags ${config.templates.categories.join(',')}`;
    }
    
    // Severity levels
    if (config.templates.severities.length > 0) {
      cmd += ` -severity ${config.templates.severities.join(',')}`;
    }
    
    // Include/exclude specific templates
    if (config.templates.enabled.length > 0) {
      cmd += ` -templates ${config.templates.enabled.join(',')}`;
    }
    
    if (config.templates.disabled.length > 0) {
      cmd += ` -exclude-templates ${config.templates.disabled.join(',')}`;
    }
    
    // Custom templates
    if (config.templates.customTemplates.length > 0) {
      cmd += ` -templates ${config.templates.customTemplates.join(',')}`;
    }
    
    // Workflow templates
    if (config.templates.workflowTemplates.length > 0) {
      cmd += ` -workflows ${config.templates.workflowTemplates.join(',')}`;
    }
    
    // Filters
    if (config.filters.includeTags.length > 0) {
      cmd += ` -include-tags ${config.filters.includeTags.join(',')}`;
    }
    
    if (config.filters.excludeTags.length > 0) {
      cmd += ` -exclude-tags ${config.filters.excludeTags.join(',')}`;
    }
    
    if (config.filters.author.length > 0) {
      cmd += ` -author ${config.filters.author.join(',')}`;
    }
    
    // Scanning options
    if (config.scanning.concurrency !== 25) {
      cmd += ` -c ${config.scanning.concurrency}`;
    }
    
    if (config.scanning.bulkSize !== 25) {
      cmd += ` -bulk-size ${config.scanning.bulkSize}`;
    }
    
    if (config.scanning.timeout !== 5) {
      cmd += ` -timeout ${config.scanning.timeout}`;
    }
    
    if (config.scanning.retries !== 1) {
      cmd += ` -retries ${config.scanning.retries}`;
    }
    
    if (config.scanning.rateLimit !== 150) {
      cmd += ` -rate-limit ${config.scanning.rateLimit}`;
    }
    
    if (config.scanning.randomAgent) {
      cmd += ' -random-agent';
    }
    
    if (config.scanning.followRedirects) {
      cmd += ' -follow-redirects';
    }
    
    if (config.scanning.maxRedirects !== 3) {
      cmd += ` -max-redirects ${config.scanning.maxRedirects}`;
    }
    
    // Authentication
    if (config.authentication.enabled) {
      if (config.authentication.username && config.authentication.password) {
        cmd += ` -header "Authorization: Basic $(echo -n '${config.authentication.username}:${config.authentication.password}' | base64)"`;
      }
    }
    
    // Custom headers
    Object.entries(config.authentication.headers).forEach(([key, value]) => {
      cmd += ` -header "${key}: ${value}"`;
    });
    
    // Cookies
    if (config.authentication.cookies) {
      cmd += ` -header "Cookie: ${config.authentication.cookies}"`;
    }
    
    // Proxy
    if (config.proxy.enabled && config.proxy.url) {
      cmd += ` -proxy-url "${config.proxy.url}"`;
    }
    
    // Output options
    if (config.output.format !== 'table') {
      cmd += ` -json`;
      if (config.output.file) {
        cmd += ` -o "${config.output.file}"`;
      }
    }
    
    if (config.output.verbose) {
      cmd += ' -v';
    }
    
    if (config.output.silent) {
      cmd += ' -silent';
    }
    
    if (config.output.noColor) {
      cmd += ' -no-color';
    }
    
    if (config.output.stats) {
      cmd += ' -stats';
    }
    
    if (config.output.markdown) {
      cmd += ' -markdown-export';
    }
    
    // System options
    if (config.system.updateTemplates) {
      cmd += ' -update-templates';
    }
    
    if (config.system.templateDirectory) {
      cmd += ` -templates-directory "${config.system.templateDirectory}"`;
    }
    
    if (config.system.configFile) {
      cmd += ` -config "${config.system.configFile}"`;
    }
    
    if (!config.system.systemResolvers) {
      cmd += ' -system-resolvers';
    }
    
    // Advanced options
    if (!config.advanced.interactsh) {
      cmd += ' -no-interactsh';
    }
    
    if (config.advanced.interactions) {
      cmd += ' -interactions-cache-size 5000';
    }
    
    if (config.advanced.projectFile) {
      cmd += ` -project-path "${config.advanced.projectFile}"`;
    }
    
    if (config.advanced.resumeScan) {
      cmd += ' -resume';
    }
    
    if (config.advanced.sandbox) {
      cmd += ' -sandbox';
    }
    
    if (config.advanced.stopAtFirstMatch) {
      cmd += ' -stop-at-first-match';
    }
    
    if (config.advanced.metrics) {
      cmd += ' -metrics';
    }
    
    if (config.advanced.debug) {
      cmd += ' -debug';
    }
    
    if (config.advanced.debugResponse) {
      cmd += ' -debug-response';
    }
    
    if (config.advanced.trace) {
      cmd += ' -trace-log-file trace.log';
    }
    
    // Custom arguments
    if (config.customArgs) {
      cmd += ` ${config.customArgs}`;
    }
    
    return cmd;
  };

  const canStart = status.connected &&
    ((config.targets.some(t => t.trim()) || config.inputFile)) &&
    (config.templates.categories.length > 0 || config.templates.enabled.length > 0) && !isScanning;

  // Debug logging
  React.useEffect(() => {
    console.log('🔍 Nuclei canStart debug:', {
      connected: status.connected,
      hasTargets: config.targets.some(t => t.trim()),
      hasInputFile: !!config.inputFile,
      hasCategories: config.templates.categories.length > 0,
      hasEnabled: config.templates.enabled.length > 0,
      isScanning,
      canStart,
      targets: config.targets,
      categories: config.templates.categories
    });
  }, [status.connected, config.targets, config.inputFile, config.templates.categories, config.templates.enabled, isScanning, canStart]);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center gap-3">
        <div className="flex items-center justify-center w-10 h-10 rounded-lg bg-gradient-to-br from-purple-500 to-pink-600">
          <Target className="h-5 w-5 text-white" />
        </div>
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100">Nuclei Scanner</h2>
          <p className="text-gray-600 dark:text-gray-400">Fast and customizable vulnerability scanner with 4000+ templates</p>
        </div>
      </div>

      {/* Status Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Status</p>
                <p className="text-2xl font-bold">
                  {isScanning ? 'Scanning' : 'Ready'}
                </p>
              </div>
              <Activity className={cn("h-8 w-8", isScanning ? "text-green-600" : "text-gray-400")} />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Templates</p>
                <p className="text-2xl font-bold">{config.templates.categories.length}</p>
              </div>
              <Package className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Concurrency</p>
                <p className="text-2xl font-bold">{config.scanning.concurrency}</p>
              </div>
              <TrendingUp className="h-8 w-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Progress</p>
                <p className="text-2xl font-bold">{isScanning ? `${progress}%` : '0%'}</p>
              </div>
              <BarChart3 className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Interface */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Crosshair className="h-5 w-5" />
                Nuclei Configuration
              </CardTitle>
              <CardDescription>
                Configure template-based vulnerability scanning with comprehensive detection capabilities
              </CardDescription>
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={updateTemplates}
                disabled={templateUpdate}
              >
                {templateUpdate ? (
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                ) : (
                  <Download className="h-4 w-4 mr-2" />
                )}
                Update Templates
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => navigator.clipboard.writeText(generateCommand())}
              >
                <Copy className="h-4 w-4 mr-2" />
                Copy Command
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setConfig(prev => ({ 
                  ...prev, 
                  targets: [''], 
                  inputFile: '', 
                  templates: { ...prev.templates, enabled: [], disabled: [], customTemplates: [] },
                  authentication: { ...prev.authentication, headers: {}, cookies: '' },
                  customArgs: ''
                }))}
              >
                <RotateCcw className="h-4 w-4 mr-2" />
                Reset
              </Button>
              {isScanning ? (
                <Button variant="destructive" size="sm" onClick={stopScan}>
                  <Square className="h-4 w-4 mr-2" />
                  Stop Scan
                </Button>
              ) : (
                <Button 
                  size="sm" 
                  onClick={startScan}
                  disabled={!canStart}
                >
                  <Play className="h-4 w-4 mr-2" />
                  Start Scan
                </Button>
              )}
            </div>
          </div>
        </CardHeader>

        <CardContent>
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="configure">Configure</TabsTrigger>
              <TabsTrigger value="output">Output</TabsTrigger>
              <TabsTrigger value="results">Results</TabsTrigger>
              <TabsTrigger value="command">Command</TabsTrigger>
            </TabsList>

            <TabsContent value="configure" className="space-y-6">
              {/* Quick Presets */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Quick Presets</h3>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                  <Button variant="outline" onClick={() => applyPreset('quick_scan')}>
                    <Zap className="h-4 w-4 mr-2" />
                    Quick Scan
                  </Button>
                  <Button variant="outline" onClick={() => applyPreset('comprehensive_scan')}>
                    <Search className="h-4 w-4 mr-2" />
                    Comprehensive
                  </Button>
                  <Button variant="outline" onClick={() => applyPreset('cve_focused')}>
                    <Bug className="h-4 w-4 mr-2" />
                    CVE Focused
                  </Button>
                  <Button variant="outline" onClick={() => applyPreset('web_app_scan')}>
                    <Globe className="h-4 w-4 mr-2" />
                    Web App Scan
                  </Button>
                  <Button variant="outline" onClick={() => applyPreset('misconfiguration_scan')}>
                    <Settings className="h-4 w-4 mr-2" />
                    Misconfigurations
                  </Button>
                  <Button variant="outline" onClick={() => applyPreset('stealth_scan')}>
                    <Eye className="h-4 w-4 mr-2" />
                    Stealth Scan
                  </Button>
                </div>
              </div>

              {/* Target Configuration */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Target Configuration</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Target URLs</label>
                    <Textarea
                      value={config.targets.join('\n')}
                      onChange={(e) => updateTargets(e.target.value)}
                      placeholder="https://example.com&#10;https://test.com&#10;192.168.1.1"
                      className="min-h-[120px]"
                    />
                    <p className="text-xs text-gray-500">Enter one URL per line</p>
                  </div>

                  <div className="space-y-2">
                    <label className="text-sm font-medium">Input File (Alternative)</label>
                    <Input
                      value={config.inputFile}
                      onChange={(e) => updateConfig('inputFile', e.target.value)}
                      placeholder="/path/to/targets.txt"
                    />
                    <p className="text-xs text-gray-500">File containing URLs to scan</p>
                  </div>
                </div>
              </div>

              {/* Template Categories */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Template Categories</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  {Object.entries(TEMPLATE_CATEGORIES).map(([category, info]) => (
                    <Card 
                      key={category}
                      className={cn(
                        "cursor-pointer transition-colors",
                        config.templates.categories.includes(category) ? "border-purple-500 bg-purple-50 dark:bg-purple-950" : ""
                      )}
                      onClick={() => toggleTemplateCategory(category)}
                    >
                      <CardContent className="pt-6">
                        <div className="flex items-center justify-between">
                          <div className="flex-1">
                            <div className="font-medium">{info.name}</div>
                            <div className="text-sm text-gray-500">{info.description}</div>
                          </div>
                          <Badge variant="outline">{info.count}</Badge>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>

              {/* Severity Levels */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Severity Levels</h3>
                <div className="grid grid-cols-1 md:grid-cols-5 gap-3">
                  {Object.entries(SEVERITY_LEVELS).map(([severity, info]) => (
                    <Card 
                      key={severity}
                      className={cn(
                        "cursor-pointer transition-colors",
                        config.templates.severities.includes(severity) ? "border-purple-500" : "",
                        info.bgColor
                      )}
                      onClick={() => toggleSeverity(severity)}
                    >
                      <CardContent className="pt-6">
                        <div className="text-center space-y-2">
                          <div className={cn("font-medium", info.color)}>{info.name}</div>
                          <div className="text-xs text-gray-600">{info.description}</div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>

              {/* Template Filters */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Template Filters</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Include Tags</label>
                    <div className="flex flex-wrap gap-1 mb-2">
                      {config.filters.includeTags.map(tag => (
                        <Badge
                          key={tag}
                          variant="secondary"
                          className="cursor-pointer"
                          onClick={() => updateConfig('filters.includeTags', config.filters.includeTags.filter(t => t !== tag))}
                        >
                          {tag} ×
                        </Badge>
                      ))}
                    </div>
                    <Select onValueChange={(value) => {
                      if (value && !config.filters.includeTags.includes(value)) {
                        updateConfig('filters.includeTags', [...config.filters.includeTags, value]);
                      }
                    }}>
                      <SelectTrigger>
                        <SelectValue placeholder="Add include tag" />
                      </SelectTrigger>
                      <SelectContent>
                        {COMMON_TAGS.map(tag => (
                          <SelectItem key={tag} value={tag}>{tag}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <label className="text-sm font-medium">Exclude Tags</label>
                    <div className="flex flex-wrap gap-1 mb-2">
                      {config.filters.excludeTags.map(tag => (
                        <Badge
                          key={tag}
                          variant="destructive"
                          className="cursor-pointer"
                          onClick={() => updateConfig('filters.excludeTags', config.filters.excludeTags.filter(t => t !== tag))}
                        >
                          {tag} ×
                        </Badge>
                      ))}
                    </div>
                    <Select onValueChange={(value) => {
                      if (value && !config.filters.excludeTags.includes(value)) {
                        updateConfig('filters.excludeTags', [...config.filters.excludeTags, value]);
                      }
                    }}>
                      <SelectTrigger>
                        <SelectValue placeholder="Add exclude tag" />
                      </SelectTrigger>
                      <SelectContent>
                        {COMMON_TAGS.map(tag => (
                          <SelectItem key={tag} value={tag}>{tag}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Custom Templates</label>
                    <Textarea
                      value={config.templates.customTemplates.join('\n')}
                      onChange={(e) => updateConfig('templates.customTemplates', e.target.value.split('\n').filter(t => t.trim()))}
                      placeholder="/path/to/custom-template.yaml&#10;/path/to/another-template.yaml"
                      className="min-h-[80px]"
                    />
                  </div>

                  <div className="space-y-2">
                    <label className="text-sm font-medium">Workflow Templates</label>
                    <Textarea
                      value={config.templates.workflowTemplates.join('\n')}
                      onChange={(e) => updateConfig('templates.workflowTemplates', e.target.value.split('\n').filter(t => t.trim()))}
                      placeholder="/path/to/workflow.yaml&#10;/path/to/another-workflow.yaml"
                      className="min-h-[80px]"
                    />
                  </div>
                </div>
              </div>

              {/* Scanning Configuration */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Scanning Configuration</h3>
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Concurrency</label>
                    <Input
                      type="number"
                      value={config.scanning.concurrency}
                      onChange={(e) => updateConfig('scanning.concurrency', parseInt(e.target.value) || 25)}
                      min="1"
                      max="100"
                    />
                  </div>

                  <div className="space-y-2">
                    <label className="text-sm font-medium">Timeout (s)</label>
                    <Input
                      type="number"
                      value={config.scanning.timeout}
                      onChange={(e) => updateConfig('scanning.timeout', parseInt(e.target.value) || 5)}
                      min="1"
                      max="60"
                    />
                  </div>

                  <div className="space-y-2">
                    <label className="text-sm font-medium">Rate Limit</label>
                    <Input
                      type="number"
                      value={config.scanning.rateLimit}
                      onChange={(e) => updateConfig('scanning.rateLimit', parseInt(e.target.value) || 150)}
                      min="1"
                      max="1000"
                    />
                  </div>

                  <div className="space-y-2">
                    <label className="text-sm font-medium">Retries</label>
                    <Input
                      type="number"
                      value={config.scanning.retries}
                      onChange={(e) => updateConfig('scanning.retries', parseInt(e.target.value) || 1)}
                      min="0"
                      max="5"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="randomAgent"
                      checked={config.scanning.randomAgent}
                      onChange={(e) => updateConfig('scanning.randomAgent', e.target.checked)}
                      className="rounded"
                    />
                    <label htmlFor="randomAgent" className="text-sm font-medium">Random Agent</label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="followRedirects"
                      checked={config.scanning.followRedirects}
                      onChange={(e) => updateConfig('scanning.followRedirects', e.target.checked)}
                      className="rounded"
                    />
                    <label htmlFor="followRedirects" className="text-sm font-medium">Follow Redirects</label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="interactsh"
                      checked={config.advanced.interactsh}
                      onChange={(e) => updateConfig('advanced.interactsh', e.target.checked)}
                      className="rounded"
                    />
                    <label htmlFor="interactsh" className="text-sm font-medium">Interactsh</label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="sandbox"
                      checked={config.advanced.sandbox}
                      onChange={(e) => updateConfig('advanced.sandbox', e.target.checked)}
                      className="rounded"
                    />
                    <label htmlFor="sandbox" className="text-sm font-medium">Sandbox Mode</label>
                  </div>
                </div>
              </div>

              {/* Output Configuration */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Output Configuration</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Output Format</label>
                    <Select value={config.output.format} onValueChange={(value) => updateConfig('output.format', value)}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {Object.entries(OUTPUT_FORMATS).map(([format, info]) => (
                          <SelectItem key={format} value={format}>
                            <div>
                              <div className="font-medium">{info.name}</div>
                              <div className="text-xs text-gray-500">{info.description}</div>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <label className="text-sm font-medium">Output File</label>
                    <Input
                      value={config.output.file}
                      onChange={(e) => updateConfig('output.file', e.target.value)}
                      placeholder="/path/to/output.json"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="verbose"
                      checked={config.output.verbose}
                      onChange={(e) => updateConfig('output.verbose', e.target.checked)}
                      className="rounded"
                    />
                    <label htmlFor="verbose" className="text-sm font-medium">Verbose</label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="silent"
                      checked={config.output.silent}
                      onChange={(e) => updateConfig('output.silent', e.target.checked)}
                      className="rounded"
                    />
                    <label htmlFor="silent" className="text-sm font-medium">Silent</label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="stats"
                      checked={config.output.stats}
                      onChange={(e) => updateConfig('output.stats', e.target.checked)}
                      className="rounded"
                    />
                    <label htmlFor="stats" className="text-sm font-medium">Show Stats</label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="markdown"
                      checked={config.output.markdown}
                      onChange={(e) => updateConfig('output.markdown', e.target.checked)}
                      className="rounded"
                    />
                    <label htmlFor="markdown" className="text-sm font-medium">Markdown Report</label>
                  </div>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="output" className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold">Real-time Output</h3>
                <div className="flex items-center gap-2">
                  {isScanning && (
                    <Badge variant="secondary" className="animate-pulse">
                      <Activity className="h-3 w-3 mr-1" />
                      Scanning in progress...
                    </Badge>
                  )}
                  <Button variant="outline" size="sm">
                    <Download className="h-4 w-4 mr-2" />
                    Export Output
                  </Button>
                </div>
              </div>
              
              {isScanning && (
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Progress</span>
                    <span>{progress}%</span>
                  </div>
                  <Progress value={progress} className="w-full" />
                </div>
              )}

              <Card>
                <CardContent className="p-4">
                  <div className="bg-gray-900 dark:bg-gray-800 rounded-lg p-4 min-h-[400px] font-mono text-sm overflow-auto">
                    <div className="text-green-400">
                      {output.length > 0 ? (
                        output.map((line, index) => (
                          <div key={index} className="mb-1">{line}</div>
                        ))
                      ) : (
                        'Nuclei output will appear here when scanning starts...'
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="results" className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold">Vulnerability Results</h3>
                <div className="flex items-center gap-2">
                  <Button variant="outline" size="sm">
                    <Download className="h-4 w-4 mr-2" />
                    Export Results
                  </Button>
                  <Button variant="outline" size="sm">
                    <Filter className="h-4 w-4 mr-2" />
                    Filter Results
                  </Button>
                </div>
              </div>

              {results ? (
                <div className="space-y-4">
                  {/* Debug Information (development only) */}
                  {process.env.NODE_ENV === 'development' && (
                    <Card>
                      <CardContent className="pt-6">
                        <details>
                          <summary className="cursor-pointer text-sm font-medium text-gray-600 dark:text-gray-400 mb-2">
                            🔍 Debug: Results Structure
                          </summary>
                          <pre className="text-xs bg-gray-100 dark:bg-gray-800 p-2 rounded overflow-auto max-h-40">
                            {JSON.stringify(results, null, 2)}
                          </pre>
                        </details>
                      </CardContent>
                    </Card>
                  )}

                  {/* Results Summary */}
                  <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
                    {Object.entries(SEVERITY_LEVELS).map(([severity, info]) => (
                      <Card key={severity}>
                        <CardContent className="pt-6">
                          <div className="flex items-center justify-between">
                            <div>
                              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">{info.name}</p>
                              <p className={cn("text-2xl font-bold", info.color)}>
                                {results[severity] || 0}
                              </p>
                            </div>
                            <AlertTriangle className={cn("h-8 w-8", info.color)} />
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>

                  {/* Vulnerability Results Table */}
                  <Card>
                    <CardContent className="p-0">
                      <div className="overflow-x-auto">
                        <table className="w-full">
                          <thead className="border-b">
                            <tr className="text-left">
                              <th className="p-4 font-medium">Template</th>
                              <th className="p-4 font-medium">Severity</th>
                              <th className="p-4 font-medium">Target</th>
                              <th className="p-4 font-medium">Tags</th>
                              <th className="p-4 font-medium">Info</th>
                            </tr>
                          </thead>
                          <tbody>
                            {(results.vulnerabilities || results.detailed_findings || []).length > 0 ? (
                              (results.vulnerabilities || results.detailed_findings || []).map((finding, index) => (
                                <tr key={index} className="border-b hover:bg-gray-50 dark:hover:bg-gray-800">
                                  <td className="p-4 font-medium">{finding.template_id || finding.id || `Finding ${index + 1}`}</td>
                                  <td className="p-4">
                                    <Badge
                                      variant={finding.severity === 'critical' || finding.severity === 'high' ? 'destructive' :
                                              finding.severity === 'medium' ? 'default' : 'secondary'}
                                    >
                                      {finding.severity || 'info'}
                                    </Badge>
                                  </td>
                                  <td className="p-4 font-mono text-sm">{finding.url || finding.target || 'N/A'}</td>
                                  <td className="p-4">
                                    <div className="flex flex-wrap gap-1">
                                      {(finding.tags || []).slice(0, 3).map((tag, i) => (
                                        <Badge key={i} variant="outline" className="text-xs">
                                          {tag}
                                        </Badge>
                                      ))}
                                      {(finding.tags || []).length > 3 && (
                                        <Badge variant="outline" className="text-xs">
                                          +{(finding.tags || []).length - 3}
                                        </Badge>
                                      )}
                                    </div>
                                  </td>
                                  <td className="p-4 text-sm">{finding.name || finding.info}</td>
                                </tr>
                              ))
                            ) : (
                              <tr>
                                <td colSpan={5} className="p-8 text-center text-gray-500 dark:text-gray-400">
                                  <div className="flex flex-col items-center space-y-2">
                                    <Shield className="h-8 w-8 text-green-500" />
                                    <p className="font-medium">No vulnerabilities found</p>
                                    <p className="text-sm">The target appears to be secure against the tested templates.</p>
                                  </div>
                                </td>
                              </tr>
                            )}
                          </tbody>
                        </table>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              ) : (
                <Card>
                  <CardContent className="p-8 text-center">
                    <Target className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">No Results Yet</h3>
                    <p className="text-gray-600 dark:text-gray-400">
                      Start a vulnerability scan to see results here. Nuclei will identify vulnerabilities using its comprehensive template database.
                    </p>
                  </CardContent>
                </Card>
              )}
            </TabsContent>

            <TabsContent value="command" className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold">Generated Command</h3>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => navigator.clipboard.writeText(generateCommand())}
                >
                  <Copy className="h-4 w-4 mr-2" />
                  Copy Command
                </Button>
              </div>

              <Card>
                <CardContent className="p-4">
                  <div className="bg-gray-900 dark:bg-gray-800 rounded-lg p-4 overflow-x-auto">
                    <code className="text-green-400 whitespace-pre-wrap break-all">
                      {generateCommand()}
                    </code>
                  </div>
                </CardContent>
              </Card>

              <div className="space-y-4">
                <h4 className="font-semibold">Command Explanation</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                  <div>
                    <h5 className="font-medium mb-2">Target Options</h5>
                    <ul className="space-y-1 text-gray-600 dark:text-gray-400">
                      <li>• <code>-u</code>: Single target URL</li>
                      <li>• <code>-l</code>: List of targets from file</li>
                      <li>• <code>-tags</code>: Template categories to include</li>
                      <li>• <code>-severity</code>: Severity levels to scan</li>
                    </ul>
                  </div>
                  <div>
                    <h5 className="font-medium mb-2">Template Control</h5>
                    <ul className="space-y-1 text-gray-600 dark:text-gray-400">
                      <li>• <code>-templates</code>: Specific templates to use</li>
                      <li>• <code>-exclude-templates</code>: Templates to skip</li>
                      <li>• <code>-include-tags</code>: Include specific tags</li>
                      <li>• <code>-exclude-tags</code>: Exclude specific tags</li>
                    </ul>
                  </div>
                  <div>
                    <h5 className="font-medium mb-2">Performance</h5>
                    <ul className="space-y-1 text-gray-600 dark:text-gray-400">
                      <li>• <code>-c</code>: Concurrency level</li>
                      <li>• <code>-bulk-size</code>: Bulk request size</li>
                      <li>• <code>-timeout</code>: Request timeout</li>
                      <li>• <code>-rate-limit</code>: Requests per second</li>
                    </ul>
                  </div>
                  <div>
                    <h5 className="font-medium mb-2">Output & Debug</h5>
                    <ul className="space-y-1 text-gray-600 dark:text-gray-400">
                      <li>• <code>-json</code>: JSON output format</li>
                      <li>• <code>-o</code>: Output file path</li>
                      <li>• <code>-v</code>: Verbose output</li>
                      <li>• <code>-stats</code>: Show statistics</li>
                    </ul>
                  </div>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}