/**
 * WPScan Tool Component
 * WordPress security scanner for comprehensive CMS vulnerability testing
 */
import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { 
  Wordpress,
  Play,
  Square,
  Download,
  Settings,
  Target,
  Globe,
  FileText,
  Terminal,
  Copy,
  RotateCcw,
  Clock,
  Zap,
  Filter,
  Eye,
  AlertTriangle,
  CheckCircle,
  TrendingUp,
  Activity,
  Lock,
  Search,
  List,
  BarChart3,
  Database,
  Server,
  Bug,
  Info,
  Users,
  Package,
  Palette,
  Key,
  Shield
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useBackendStore } from '@/stores/backend-store';
import { apiClient, ToolExecution } from '@/services/api-client';
import { toast } from 'sonner';

/**
 * WPScan configuration interface
 */
interface WPScanConfig {
  url: string;
  enumeration: {
    vulnerablePlugins: boolean;
    allPlugins: boolean;
    pluginList: string;
    vulnerableThemes: boolean;
    allThemes: boolean;
    themeList: string;
    timthumbs: boolean;
    configBackups: boolean;
    dbExports: boolean;
    users: boolean;
    userIds: string;
    medias: boolean;
  };
  detection: {
    mode: 'mixed' | 'passive' | 'aggressive';
    pluginDetection: 'mixed' | 'passive' | 'aggressive';
    themeDetection: 'mixed' | 'passive' | 'aggressive';
  };
  authentication: {
    enabled: boolean;
    username: string;
    password: string;
    passwordList: string;
  };
  bruteForce: {
    enabled: boolean;
    usernames: string[];
    passwordList: string;
    threads: number;
    loginUri: string;
  };
  apiToken: string;
  proxy: {
    enabled: boolean;
    url: string;
    credentials: string;
  };
  headers: { [key: string]: string };
  cookies: string;
  userAgent: string;
  timeout: number;
  maxRedirects: number;
  throttle: number;
  requestInterval: number;
  output: {
    format: 'cli' | 'json' | 'cli-no-colour';
    file: string;
  };
  stealthMode: boolean;
  randomUserAgent: boolean;
  followRedirection: boolean;
  skipSSLChecks: boolean;
  verbose: boolean;
  customArgs: string;
}

/**
 * WPScan enumeration options
 */
const ENUMERATION_OPTIONS = {
  vulnerablePlugins: {
    name: 'Vulnerable Plugins',
    description: 'Enumerate known vulnerable plugins',
    flag: 'vp',
    risk: 'medium'
  },
  allPlugins: {
    name: 'All Plugins',
    description: 'Enumerate all installed plugins',
    flag: 'ap',
    risk: 'high'
  },
  vulnerableThemes: {
    name: 'Vulnerable Themes',
    description: 'Enumerate known vulnerable themes',
    flag: 'vt',
    risk: 'medium'
  },
  allThemes: {
    name: 'All Themes',
    description: 'Enumerate all installed themes',
    flag: 'at',
    risk: 'high'
  },
  timthumbs: {
    name: 'TimThumbs',
    description: 'Enumerate TimThumb files',
    flag: 'tt',
    risk: 'medium'
  },
  configBackups: {
    name: 'Config Backups',
    description: 'Enumerate config backup files',
    flag: 'cb',
    risk: 'high'
  },
  dbExports: {
    name: 'Database Exports',
    description: 'Enumerate database export files',
    flag: 'dbe',
    risk: 'critical'
  },
  users: {
    name: 'Users',
    description: 'Enumerate WordPress users',
    flag: 'u',
    risk: 'medium'
  },
  medias: {
    name: 'Media Files',
    description: 'Enumerate media files',
    flag: 'm',
    risk: 'low'
  }
};

/**
 * Detection modes
 */
const DETECTION_MODES = {
  'mixed': {
    name: 'Mixed Mode',
    description: 'Use both passive and aggressive techniques',
    risk: 'medium'
  },
  'passive': {
    name: 'Passive Mode',
    description: 'Only passive detection techniques',
    risk: 'low'
  },
  'aggressive': {
    name: 'Aggressive Mode',
    description: 'Use aggressive detection techniques',
    risk: 'high'
  }
};

/**
 * Common WordPress password lists
 */
const PASSWORD_LISTS = {
  'rockyou': '/usr/share/wordlists/rockyou.txt',
  'fasttrack': '/usr/share/wordlists/fasttrack.txt',
  'common-passwords': '/usr/share/wordlists/common-passwords.txt',
  'wp-common': '/usr/share/wpscan/passwords/wp-common.txt',
  'darkweb2017-top1000': '/usr/share/wordlists/darkweb2017-top1000.txt'
};

/**
 * Vulnerability severity levels
 */
const SEVERITY_LEVELS = [
  { value: 'info', label: 'Info', color: 'blue', description: 'Informational findings' },
  { value: 'low', label: 'Low', color: 'green', description: 'Low risk issues' },
  { value: 'medium', label: 'Medium', color: 'yellow', description: 'Medium risk vulnerabilities' },
  { value: 'high', label: 'High', color: 'orange', description: 'High risk security flaws' },
  { value: 'critical', label: 'Critical', color: 'red', description: 'Critical vulnerabilities' }
];

/**
 * Risk level styling
 */
const getRiskColor = (risk: string) => {
  switch (risk) {
    case 'low': return 'text-green-600 bg-green-100 dark:bg-green-900/20';
    case 'medium': return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900/20';
    case 'high': return 'text-orange-600 bg-orange-100 dark:bg-orange-900/20';
    case 'critical': return 'text-red-600 bg-red-100 dark:bg-red-900/20';
    default: return 'text-gray-600 bg-gray-100 dark:bg-gray-900/20';
  }
};

/**
 * WPScan Tool Component
 */
export function WPScanTool() {
  // State management
  const [config, setConfig] = React.useState<WPScanConfig>({
    url: '',
    enumeration: {
      vulnerablePlugins: true,
      allPlugins: false,
      pluginList: '',
      vulnerableThemes: true,
      allThemes: false,
      themeList: '',
      timthumbs: false,
      configBackups: true,
      dbExports: true,
      users: true,
      userIds: '',
      medias: false
    },
    detection: {
      mode: 'mixed',
      pluginDetection: 'mixed',
      themeDetection: 'mixed'
    },
    authentication: {
      enabled: false,
      username: '',
      password: '',
      passwordList: ''
    },
    bruteForce: {
      enabled: false,
      usernames: [],
      passwordList: 'common-passwords',
      threads: 5,
      loginUri: ''
    },
    apiToken: '',
    proxy: {
      enabled: false,
      url: '',
      credentials: ''
    },
    headers: {},
    cookies: '',
    userAgent: '',
    timeout: 60,
    maxRedirects: 3,
    throttle: 0,
    requestInterval: 0,
    output: {
      format: 'json',
      file: ''
    },
    stealthMode: false,
    randomUserAgent: false,
    followRedirection: true,
    skipSSLChecks: false,
    verbose: false,
    customArgs: ''
  });
  
  const [isScanning, setIsScanning] = React.useState(false);
  const [currentExecution, setCurrentExecution] = React.useState<ToolExecution | null>(null);
  const [progress, setProgress] = React.useState(0);
  const [output, setOutput] = React.useState<string[]>([]);
  const [results, setResults] = React.useState<any>(null);
  const [activeTab, setActiveTab] = React.useState('configure');
  const [vulnerabilityStats, setVulnerabilityStats] = React.useState<any>(null);
  const [newHeaderKey, setNewHeaderKey] = React.useState('');
  const [newHeaderValue, setNewHeaderValue] = React.useState('');
  const [newUsername, setNewUsername] = React.useState('');
  
  // Backend connection
  const { status: backendStatus } = useBackendStore();
  
  // Update configuration
  const updateConfig = (updates: Partial<WPScanConfig>) => {
    setConfig(prev => ({ ...prev, ...updates }));
  };
  
  // Update enumeration
  const updateEnumeration = (updates: Partial<WPScanConfig['enumeration']>) => {
    setConfig(prev => ({
      ...prev,
      enumeration: { ...prev.enumeration, ...updates }
    }));
  };
  
  // Update detection
  const updateDetection = (updates: Partial<WPScanConfig['detection']>) => {
    setConfig(prev => ({
      ...prev,
      detection: { ...prev.detection, ...updates }
    }));
  };
  
  // Update authentication
  const updateAuth = (updates: Partial<WPScanConfig['authentication']>) => {
    setConfig(prev => ({
      ...prev,
      authentication: { ...prev.authentication, ...updates }
    }));
  };
  
  // Update brute force
  const updateBruteForce = (updates: Partial<WPScanConfig['bruteForce']>) => {
    setConfig(prev => ({
      ...prev,
      bruteForce: { ...prev.bruteForce, ...updates }
    }));
  };
  
  // Update proxy
  const updateProxy = (updates: Partial<WPScanConfig['proxy']>) => {
    setConfig(prev => ({
      ...prev,
      proxy: { ...prev.proxy, ...updates }
    }));
  };
  
  // Update output
  const updateOutput = (updates: Partial<WPScanConfig['output']>) => {
    setConfig(prev => ({
      ...prev,
      output: { ...prev.output, ...updates }
    }));
  };
  
  // Apply scan preset
  const applyScanPreset = (preset: string) => {
    switch (preset) {
      case 'basic':
        updateEnumeration({
          vulnerablePlugins: true,
          allPlugins: false,
          vulnerableThemes: true,
          allThemes: false,
          timthumbs: false,
          configBackups: false,
          dbExports: false,
          users: true,
          medias: false
        });
        updateDetection({ mode: 'passive' });
        break;
      case 'standard':
        updateEnumeration({
          vulnerablePlugins: true,
          allPlugins: false,
          vulnerableThemes: true,
          allThemes: false,
          timthumbs: true,
          configBackups: true,
          dbExports: true,
          users: true,
          medias: false
        });
        updateDetection({ mode: 'mixed' });
        break;
      case 'comprehensive':
        updateEnumeration({
          vulnerablePlugins: true,
          allPlugins: true,
          vulnerableThemes: true,
          allThemes: true,
          timthumbs: true,
          configBackups: true,
          dbExports: true,
          users: true,
          medias: true
        });
        updateDetection({ mode: 'aggressive' });
        break;
      case 'stealth':
        updateEnumeration({
          vulnerablePlugins: true,
          allPlugins: false,
          vulnerableThemes: true,
          allThemes: false,
          timthumbs: false,
          configBackups: false,
          dbExports: false,
          users: false,
          medias: false
        });
        updateDetection({ mode: 'passive' });
        updateConfig({ stealthMode: true, randomUserAgent: true });
        break;
    }
    toast.success(`Applied ${preset} scan preset`);
  };
  
  // Add custom header
  const addHeader = () => {
    if (newHeaderKey.trim() && newHeaderValue.trim()) {
      updateConfig({
        headers: {
          ...config.headers,
          [newHeaderKey.trim()]: newHeaderValue.trim()
        }
      });
      setNewHeaderKey('');
      setNewHeaderValue('');
      toast.success('Header added');
    }
  };
  
  // Remove header
  const removeHeader = (key: string) => {
    const { [key]: removed, ...remainingHeaders } = config.headers;
    updateConfig({ headers: remainingHeaders });
    toast.success('Header removed');
  };
  
  // Add username for brute force
  const addUsername = () => {
    if (newUsername.trim() && !config.bruteForce.usernames.includes(newUsername.trim())) {
      updateBruteForce({
        usernames: [...config.bruteForce.usernames, newUsername.trim()]
      });
      setNewUsername('');
      toast.success('Username added');
    }
  };
  
  // Remove username
  const removeUsername = (username: string) => {
    updateBruteForce({
      usernames: config.bruteForce.usernames.filter(u => u !== username)
    });
    toast.success('Username removed');
  };
  
  // Generate WPScan command
  const generateCommand = (): string => {
    let command = 'wpscan';
    
    // URL
    command += ` --url ${config.url}`;
    
    // Enumeration
    const enumFlags = [];
    Object.entries(config.enumeration).forEach(([key, value]) => {
      if (value === true && ENUMERATION_OPTIONS[key as keyof typeof ENUMERATION_OPTIONS]) {
        enumFlags.push(ENUMERATION_OPTIONS[key as keyof typeof ENUMERATION_OPTIONS].flag);
      }
    });
    
    if (enumFlags.length > 0) {
      command += ` --enumerate ${enumFlags.join(',')}`;
    }
    
    // User IDs for enumeration
    if (config.enumeration.userIds.trim()) {
      command += ` --enumerate u[${config.enumeration.userIds}]`;
    }
    
    // Plugin/Theme lists
    if (config.enumeration.pluginList.trim()) {
      command += ` --enumerate p[${config.enumeration.pluginList}]`;
    }
    if (config.enumeration.themeList.trim()) {
      command += ` --enumerate t[${config.enumeration.themeList}]`;
    }
    
    // Detection modes
    if (config.detection.mode !== 'mixed') {
      command += ` --detection-mode ${config.detection.mode}`;
    }
    if (config.detection.pluginDetection !== 'mixed') {
      command += ` --plugins-detection ${config.detection.pluginDetection}`;
    }
    if (config.detection.themeDetection !== 'mixed') {
      command += ` --themes-detection ${config.detection.themeDetection}`;
    }
    
    // Authentication
    if (config.authentication.enabled && config.authentication.username) {
      command += ` --wp-username ${config.authentication.username}`;
      if (config.authentication.password) {
        command += ` --wp-password ${config.authentication.password}`;
      }
      if (config.authentication.passwordList) {
        command += ` --passwords ${config.authentication.passwordList}`;
      }
    }
    
    // Brute force
    if (config.bruteForce.enabled) {
      if (config.bruteForce.usernames.length > 0) {
        command += ` --usernames ${config.bruteForce.usernames.join(',')}`;
      }
      const passwordListPath = PASSWORD_LISTS[config.bruteForce.passwordList as keyof typeof PASSWORD_LISTS] || config.bruteForce.passwordList;
      command += ` --passwords ${passwordListPath}`;
      command += ` --max-threads ${config.bruteForce.threads}`;
      if (config.bruteForce.loginUri) {
        command += ` --login-uri ${config.bruteForce.loginUri}`;
      }
    }
    
    // API Token
    if (config.apiToken.trim()) {
      command += ` --api-token ${config.apiToken}`;
    }
    
    // Proxy
    if (config.proxy.enabled && config.proxy.url) {
      command += ` --proxy ${config.proxy.url}`;
      if (config.proxy.credentials) {
        command += ` --proxy-auth ${config.proxy.credentials}`;
      }
    }
    
    // Headers
    Object.entries(config.headers).forEach(([key, value]) => {
      command += ` --headers "${key}: ${value}"`;
    });
    
    // Cookies
    if (config.cookies.trim()) {
      command += ` --cookie "${config.cookies}"`;
    }
    
    // User Agent
    if (config.userAgent.trim()) {
      command += ` --user-agent "${config.userAgent}"`;
    }
    
    // Random User Agent
    if (config.randomUserAgent) {
      command += ` --random-user-agent`;
    }
    
    // Timeout and limits
    if (config.timeout !== 60) {
      command += ` --request-timeout ${config.timeout}`;
    }
    if (config.maxRedirects !== 3) {
      command += ` --max-redirects ${config.maxRedirects}`;
    }
    if (config.throttle > 0) {
      command += ` --throttle ${config.throttle}`;
    }
    if (config.requestInterval > 0) {
      command += ` --request-interval ${config.requestInterval}`;
    }
    
    // Output format
    if (config.output.format !== 'cli') {
      command += ` --format ${config.output.format}`;
    }
    if (config.output.file.trim()) {
      command += ` --output ${config.output.file}`;
    }
    
    // Options
    if (config.stealthMode) {
      command += ` --stealthy`;
    }
    if (!config.followRedirection) {
      command += ` --no-update`;
    }
    if (config.skipSSLChecks) {
      command += ` --disable-tls-checks`;
    }
    if (config.verbose) {
      command += ` --verbose`;
    }
    
    // Custom arguments
    if (config.customArgs.trim()) {
      command += ` ${config.customArgs.trim()}`;
    }
    
    return command;
  };
  
  // Start scan
  const startScan = async () => {
    if (!config.url.trim()) {
      toast.error('Please specify a WordPress URL');
      return;
    }
    
    if (!backendStatus.connected) {
      toast.error('Backend not connected');
      return;
    }
    
    try {
      setIsScanning(true);
      setProgress(0);
      setOutput([]);
      setResults(null);
      setVulnerabilityStats(null);
      setActiveTab('output');
      
      const execution = await apiClient.executeTool('wpscan', config, (progressUpdate) => {
        setCurrentExecution(progressUpdate);
        setProgress(progressUpdate.progress);
        if (progressUpdate.output) {
          setOutput(prev => [...prev, ...progressUpdate.output!]);
        }
        
        if (progressUpdate.status === 'completed') {
          setIsScanning(false);
          setResults(progressUpdate.results);
          
          // Parse vulnerability statistics
          if (progressUpdate.results?.vulnerabilities) {
            const stats = progressUpdate.results.vulnerabilities.reduce((acc: any, vuln: any) => {
              const severity = vuln.severity || 'info';
              acc[severity] = (acc[severity] || 0) + 1;
              return acc;
            }, {});
            stats.total = progressUpdate.results.vulnerabilities.length;
            stats.plugins = progressUpdate.results.plugins?.length || 0;
            stats.themes = progressUpdate.results.themes?.length || 0;
            stats.users = progressUpdate.results.users?.length || 0;
            setVulnerabilityStats(stats);
          }
          
          toast.success('WPScan completed');
        } else if (progressUpdate.status === 'failed') {
          setIsScanning(false);
          toast.error(`Scan failed: ${progressUpdate.error}`);
        }
      });
      
      setCurrentExecution(execution);
      toast.info('WPScan started');
    } catch (error) {
      setIsScanning(false);
      console.error('Failed to start WPScan:', error);
      toast.error('Failed to start scan');
    }
  };
  
  // Stop scan
  const stopScan = async () => {
    if (currentExecution) {
      try {
        await apiClient.cancelExecution(currentExecution.id);
        setIsScanning(false);
        setCurrentExecution(null);
        toast.info('Scan cancelled');
      } catch (error) {
        console.error('Failed to stop scan:', error);
        toast.error('Failed to stop scan');
      }
    }
  };
  
  // Copy command to clipboard
  const copyCommand = async () => {
    try {
      await navigator.clipboard.writeText(generateCommand());
      toast.success('Command copied to clipboard');
    } catch (error) {
      toast.error('Failed to copy command');
    }
  };
  
  // Export results
  const exportResults = () => {
    if (!results && output.length === 0) {
      toast.error('No results to export');
      return;
    }
    
    const data = results || output.join('
');
    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `wpscan-${new Date().toISOString().slice(0, 19)}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    
    toast.success('Results exported');
  };
  
  return (
    <div className="container-desktop py-6 space-y-6">
      {/* Tool Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className="p-2 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
            <Wordpress className="h-6 w-6 text-blue-600 dark:text-blue-400" />
          </div>
          <div>
            <h1 className="text-2xl font-bold">WPScan Tool</h1>
            <p className="text-muted-foreground">
              WordPress security scanner for comprehensive CMS vulnerability testing
            </p>
          </div>
        </div>
        
        {/* Status indicators */}
        <div className="flex items-center gap-2">
          <Badge variant={backendStatus.connected ? 'default' : 'destructive'}>
            {backendStatus.connected ? 'Connected' : 'Offline'}
          </Badge>
          {isScanning && (
            <Badge variant="secondary" className="animate-pulse">
              Scanning...
            </Badge>
          )}
        </div>
      </div>
      
      {/* Main Interface */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="configure" className="flex items-center gap-2">
            <Settings className="h-4 w-4" />
            Configure
          </TabsTrigger>
          <TabsTrigger value="output" className="flex items-center gap-2">
            <Terminal className="h-4 w-4" />
            Output
          </TabsTrigger>
          <TabsTrigger value="results" className="flex items-center gap-2">
            <FileText className="h-4 w-4" />
            Results
          </TabsTrigger>
          <TabsTrigger value="command" className="flex items-center gap-2">
            <Target className="h-4 w-4" />
            Command
          </TabsTrigger>
        </TabsList>
        
        {/* Configuration Tab */}
        <TabsContent value="configure" className="space-y-6">
          {/* Quick Presets */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Zap className="h-5 w-5" />
                Quick Scan Presets
              </CardTitle>
              <CardDescription>
                Use predefined configurations for common WordPress security assessments
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-3 md:grid-cols-4">
                <Button
                  variant="outline"
                  onClick={() => applyScanPreset('basic')}
                  className="flex flex-col h-auto p-4 text-left"
                >
                  <div className="font-medium text-sm">Basic Scan</div>
                  <div className="text-xs text-muted-foreground">
                    Vulnerable plugins/themes, users
                  </div>
                </Button>
                
                <Button
                  variant="outline"
                  onClick={() => applyScanPreset('standard')}
                  className="flex flex-col h-auto p-4 text-left"
                >
                  <div className="font-medium text-sm">Standard Scan</div>
                  <div className="text-xs text-muted-foreground">
                    + Config backups, DB exports
                  </div>
                </Button>
                
                <Button
                  variant="outline"
                  onClick={() => applyScanPreset('comprehensive')}
                  className="flex flex-col h-auto p-4 text-left"
                >
                  <div className="font-medium text-sm">Comprehensive</div>
                  <div className="text-xs text-muted-foreground">
                    All plugins/themes + media files
                  </div>
                </Button>
                
                <Button
                  variant="outline"
                  onClick={() => applyScanPreset('stealth')}
                  className="flex flex-col h-auto p-4 text-left"
                >
                  <div className="font-medium text-sm">Stealth Mode</div>
                  <div className="text-xs text-muted-foreground">
                    Passive detection, minimal footprint
                  </div>
                </Button>
              </div>
            </CardContent>
          </Card>
          
          <div className="grid gap-6 lg:grid-cols-2">
            {/* Target Configuration */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Target className="h-5 w-5" />
                  Target Configuration
                </CardTitle>
                <CardDescription>
                  Specify the WordPress site URL and API configuration
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* URL */}
                <div className="space-y-2">
                  <label className="text-sm font-medium">WordPress URL</label>
                  <Input
                    placeholder="e.g., https://example.com"
                    value={config.url}
                    onChange={(e) => updateConfig({ url: e.target.value })}
                  />
                  <p className="text-xs text-muted-foreground">
                    Full URL to the WordPress site including protocol
                  </p>
                </div>
                
                {/* API Token */}
                <div className="space-y-2">
                  <label className="text-sm font-medium">WPScan API Token (Optional)</label>
                  <Input
                    placeholder="API token for vulnerability database"
                    value={config.apiToken}
                    onChange={(e) => updateConfig({ apiToken: e.target.value })}
                    type="password"
                  />
                  <p className="text-xs text-muted-foreground">
                    Get free API token from <a href="https://wpscan.com/" target="_blank" className="text-blue-600 hover:underline">wpscan.com</a> for vulnerability data
                  </p>
                </div>
                
                {/* Detection Mode */}
                <div className="space-y-2">
                  <label className="text-sm font-medium">Detection Mode</label>
                  <Select 
                    value={config.detection.mode} 
                    onValueChange={(value) => updateDetection({ mode: value as any })}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {Object.entries(DETECTION_MODES).map(([key, mode]) => (
                        <SelectItem key={key} value={key}>
                          <div className="flex flex-col">
                            <span>{mode.name}</span>
                            <span className="text-xs text-muted-foreground">
                              {mode.description}
                            </span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </CardContent>
            </Card>
            
            {/* Performance Options */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <TrendingUp className="h-5 w-5" />
                  Performance & Stealth
                </CardTitle>
                <CardDescription>
                  Configure scanning performance and stealth options
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid gap-4 md:grid-cols-2">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Timeout (seconds)</label>
                    <Input
                      type="number"
                      min="1"
                      max="300"
                      value={config.timeout}
                      onChange={(e) => updateConfig({ timeout: parseInt(e.target.value) || 60 })}
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Throttle (ms)</label>
                    <Input
                      type="number"
                      min="0"
                      max="5000"
                      value={config.throttle}
                      onChange={(e) => updateConfig({ throttle: parseInt(e.target.value) || 0 })}
                      placeholder="0 = no throttle"
                    />
                  </div>
                </div>
                
                <div className="space-y-3">
                  <label className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      checked={config.stealthMode}
                      onChange={(e) => updateConfig({ stealthMode: e.target.checked })}
                      className="rounded border-gray-300"
                    />
                    <span className="text-sm">Stealth Mode</span>
                  </label>
                  
                  <label className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      checked={config.randomUserAgent}
                      onChange={(e) => updateConfig({ randomUserAgent: e.target.checked })}
                      className="rounded border-gray-300"
                    />
                    <span className="text-sm">Random User Agent</span>
                  </label>
                  
                  <label className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      checked={config.followRedirection}
                      onChange={(e) => updateConfig({ followRedirection: e.target.checked })}
                      className="rounded border-gray-300"
                    />
                    <span className="text-sm">Follow Redirects</span>
                  </label>
                  
                  <label className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      checked={config.skipSSLChecks}
                      onChange={(e) => updateConfig({ skipSSLChecks: e.target.checked })}
                      className="rounded border-gray-300"
                    />
                    <span className="text-sm">Skip SSL Certificate Checks</span>
                  </label>
                  
                  <label className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      checked={config.verbose}
                      onChange={(e) => updateConfig({ verbose: e.target.checked })}
                      className="rounded border-gray-300"
                    />
                    <span className="text-sm">Verbose Output</span>
                  </label>
                </div>
              </CardContent>
            </Card>
          </div>
          
          {/* Enumeration Options */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Search className="h-5 w-5" />
                Enumeration Options
              </CardTitle>
              <CardDescription>
                Select what WordPress components to enumerate and analyze
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                {Object.entries(ENUMERATION_OPTIONS).map(([key, option]) => (
                  <div
                    key={key}
                    className={cn(
                      'p-4 border rounded-lg cursor-pointer transition-all',
                      config.enumeration[key as keyof typeof config.enumeration]
                        ? 'border-primary bg-primary/5'
                        : 'border-border hover:border-primary/50'
                    )}
                    onClick={() => updateEnumeration({ 
                      [key]: !config.enumeration[key as keyof typeof config.enumeration] 
                    })}
                  >
                    <div className="flex items-start gap-3">
                      <div className="mt-1">
                        <input
                          type="checkbox"
                          checked={config.enumeration[key as keyof typeof config.enumeration] as boolean}
                          onChange={(e) => updateEnumeration({ [key]: e.target.checked })}
                          className="rounded border-gray-300"
                        />
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2 mb-1">
                          <span className="text-sm font-medium">{option.name}</span>
                          <Badge variant="outline" className={cn('text-xs', getRiskColor(option.risk))}>
                            {option.risk}
                          </Badge>
                        </div>
                        <p className="text-xs text-muted-foreground">{option.description}</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
              
              {/* Additional enumeration options */}
              <div className="grid gap-4 md:grid-cols-2 mt-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium">Specific Plugin List (Optional)</label>
                  <Input
                    placeholder="plugin1,plugin2,plugin3"
                    value={config.enumeration.pluginList}
                    onChange={(e) => updateEnumeration({ pluginList: e.target.value })}
                  />
                </div>
                
                <div className="space-y-2">
                  <label className="text-sm font-medium">Specific User IDs (Optional)</label>
                  <Input
                    placeholder="1-10,50,100"
                    value={config.enumeration.userIds}
                    onChange={(e) => updateEnumeration({ userIds: e.target.value })}
                  />
                </div>
              </div>
            </CardContent>
          </Card>
          
          {/* Brute Force */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Key className="h-5 w-5" />
                Brute Force Attack
              </CardTitle>
              <CardDescription>
                Configure password brute force attacks against WordPress users
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={config.bruteForce.enabled}
                  onChange={(e) => updateBruteForce({ enabled: e.target.checked })}
                  className="rounded border-gray-300"
                />
                <span className="text-sm font-medium">Enable Brute Force Attack</span>
              </div>
              
              {config.bruteForce.enabled && (
                <div className="space-y-4">
                  {/* Warning */}
                  <div className="p-3 bg-orange-50 dark:bg-orange-900/10 rounded-lg border border-orange-200 dark:border-orange-800">
                    <div className="flex items-start gap-2">
                      <AlertTriangle className="h-4 w-4 text-orange-600 dark:text-orange-400 mt-0.5" />
                      <div className="text-sm text-orange-700 dark:text-orange-300">
                        <strong>Warning:</strong> Brute force attacks can be detected by security systems and may violate terms of service. Use only on authorized targets.
                      </div>
                    </div>
                  </div>
                  
                  {/* Usernames */}
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Target Usernames</label>
                    <div className="space-y-2">
                      {config.bruteForce.usernames.map((username, index) => (
                        <div key={index} className="flex items-center gap-2 p-2 border rounded">
                          <Users className="h-4 w-4 text-muted-foreground" />
                          <span className="text-sm flex-1">{username}</span>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => removeUsername(username)}
                          >
                            ×
                          </Button>
                        </div>
                      ))}
                      
                      <div className="flex gap-2">
                        <Input
                          placeholder="Username to attack"
                          value={newUsername}
                          onChange={(e) => setNewUsername(e.target.value)}
                        />
                        <Button onClick={addUsername} disabled={!newUsername.trim()}>
                          Add
                        </Button>
                      </div>
                    </div>
                  </div>
                  
                  <div className="grid gap-4 md:grid-cols-2">
                    <div className="space-y-2">
                      <label className="text-sm font-medium">Password List</label>
                      <Select 
                        value={config.bruteForce.passwordList} 
                        onValueChange={(value) => updateBruteForce({ passwordList: value })}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {Object.entries(PASSWORD_LISTS).map(([key, path]) => (
                            <SelectItem key={key} value={key}>
                              <div className="flex flex-col">
                                <span className="capitalize">{key.replace(/-/g, ' ')}</span>
                                <span className="text-xs text-muted-foreground">
                                  {path}
                                </span>
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                    
                    <div className="space-y-2">
                      <label className="text-sm font-medium">Threads</label>
                      <Input
                        type="number"
                        min="1"
                        max="20"
                        value={config.bruteForce.threads}
                        onChange={(e) => updateBruteForce({ threads: parseInt(e.target.value) || 5 })}
                      />
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Custom Login URI (Optional)</label>
                    <Input
                      placeholder="e.g., /wp-admin/admin-ajax.php"
                      value={config.bruteForce.loginUri}
                      onChange={(e) => updateBruteForce({ loginUri: e.target.value })}
                    />
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
          
          {/* HTTP Configuration */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Globe className="h-5 w-5" />
                HTTP Configuration
              </CardTitle>
              <CardDescription>
                Configure HTTP headers, cookies, and request parameters
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-2">
                  <label className="text-sm font-medium">User Agent</label>
                  <Input
                    placeholder="Custom User-Agent string"
                    value={config.userAgent}
                    onChange={(e) => updateConfig({ userAgent: e.target.value })}
                  />
                </div>
                
                <div className="space-y-2">
                  <label className="text-sm font-medium">Cookies</label>
                  <Input
                    placeholder="cookie1=value1; cookie2=value2"
                    value={config.cookies}
                    onChange={(e) => updateConfig({ cookies: e.target.value })}
                  />
                </div>
              </div>
              
              {/* Custom Headers */}
              <div className="space-y-2">
                <label className="text-sm font-medium">Custom Headers</label>
                <div className="space-y-2">
                  {Object.entries(config.headers).map(([key, value]) => (
                    <div key={key} className="flex items-center gap-2 p-2 border rounded">
                      <span className="text-sm font-mono">{key}:</span>
                      <span className="text-sm flex-1">{value}</span>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => removeHeader(key)}
                      >
                        ×
                      </Button>
                    </div>
                  ))}
                  
                  <div className="flex gap-2">
                    <Input
                      placeholder="Header name"
                      value={newHeaderKey}
                      onChange={(e) => setNewHeaderKey(e.target.value)}
                    />
                    <Input
                      placeholder="Header value"
                      value={newHeaderValue}
                      onChange={(e) => setNewHeaderValue(e.target.value)}
                    />
                    <Button onClick={addHeader} disabled={!newHeaderKey.trim() || !newHeaderValue.trim()}>
                      Add
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
          
          {/* Proxy & Authentication */}
          <div className="grid gap-6 lg:grid-cols-2">
            {/* Proxy */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Server className="h-5 w-5" />
                  Proxy Configuration
                </CardTitle>
                <CardDescription>
                  Configure proxy settings for requests
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    checked={config.proxy.enabled}
                    onChange={(e) => updateProxy({ enabled: e.target.checked })}
                    className="rounded border-gray-300"
                  />
                  <span className="text-sm font-medium">Enable Proxy</span>
                </div>
                
                {config.proxy.enabled && (
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <label className="text-sm font-medium">Proxy URL</label>
                      <Input
                        placeholder="http://proxy:8080"
                        value={config.proxy.url}
                        onChange={(e) => updateProxy({ url: e.target.value })}
                      />
                    </div>
                    
                    <div className="space-y-2">
                      <label className="text-sm font-medium">Credentials (Optional)</label>
                      <Input
                        placeholder="username:password"
                        value={config.proxy.credentials}
                        onChange={(e) => updateProxy({ credentials: e.target.value })}
                      />
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
            
            {/* WordPress Authentication */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Lock className="h-5 w-5" />
                  WordPress Authentication
                </CardTitle>
                <CardDescription>
                  Authenticate to WordPress for deeper scanning
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    checked={config.authentication.enabled}
                    onChange={(e) => updateAuth({ enabled: e.target.checked })}
                    className="rounded border-gray-300"
                  />
                  <span className="text-sm font-medium">Enable Authentication</span>
                </div>
                
                {config.authentication.enabled && (
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <label className="text-sm font-medium">Username</label>
                      <Input
                        placeholder="WordPress username"
                        value={config.authentication.username}
                        onChange={(e) => updateAuth({ username: e.target.value })}
                      />
                    </div>
                    
                    <div className="space-y-2">
                      <label className="text-sm font-medium">Password</label>
                      <Input
                        type="password"
                        placeholder="WordPress password"
                        value={config.authentication.password}
                        onChange={(e) => updateAuth({ password: e.target.value })}
                      />
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
          
          {/* Output Configuration */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Output Configuration
              </CardTitle>
              <CardDescription>
                Configure output format and advanced options
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid gap-4 md:grid-cols-3">
                <div className="space-y-2">
                  <label className="text-sm font-medium">Output Format</label>
                  <Select 
                    value={config.output.format} 
                    onValueChange={(value) => updateOutput({ format: value as any })}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="json">JSON Format</SelectItem>
                      <SelectItem value="cli">CLI Format</SelectItem>
                      <SelectItem value="cli-no-colour">CLI (No Color)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="space-y-2">
                  <label className="text-sm font-medium">Output File (Optional)</label>
                  <Input
                    placeholder="wpscan-results.json"
                    value={config.output.file}
                    onChange={(e) => updateOutput({ file: e.target.value })}
                  />
                </div>
                
                <div className="space-y-2">
                  <label className="text-sm font-medium">Custom Arguments</label>
                  <Input
                    placeholder="Additional WPScan arguments"
                    value={config.customArgs}
                    onChange={(e) => updateConfig({ customArgs: e.target.value })}
                  />
                </div>
              </div>
            </CardContent>
          </Card>
          
          {/* Actions */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Activity className="h-5 w-5" />
                Start WordPress Scan
              </CardTitle>
              <CardDescription>
                Execute the WPScan with your configured parameters
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between">
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={copyCommand}
                  >
                    <Copy className="h-4 w-4 mr-1" />
                    Copy Command
                  </Button>
                </div>
                
                <div className="flex gap-2">
                  {isScanning ? (
                    <Button
                      variant="destructive"
                      onClick={stopScan}
                    >
                      <Square className="h-4 w-4 mr-2" />
                      Stop Scan
                    </Button>
                  ) : (
                    <Button
                      onClick={startScan}
                      disabled={!config.url.trim() || !backendStatus.connected}
                    >
                      <Play className="h-4 w-4 mr-2" />
                      Start WordPress Scan
                    </Button>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        {/* Output Tab */}
        <TabsContent value="output" className="space-y-6">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="flex items-center gap-2">
                    <Terminal className="h-5 w-5" />
                    Real-time Output
                  </CardTitle>
                  <CardDescription>
                    Live output from the WPScan process
                  </CardDescription>
                </div>
                
                {/* Progress and controls */}
                <div className="flex items-center gap-4">
                  {isScanning && (
                    <div className="flex items-center gap-2">
                      <Progress value={progress} className="w-32" />
                      <span className="text-sm text-muted-foreground">
                        {progress}%
                      </span>
                    </div>
                  )}
                  
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setOutput([])}
                    disabled={isScanning}
                  >
                    <RotateCcw className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="bg-gray-900 text-gray-100 p-4 rounded-lg h-96 overflow-y-auto font-mono text-sm">
                {output.length === 0 ? (
                  <div className="text-gray-500 italic">
                    {isScanning ? 'Waiting for output...' : 'No output yet. Start a scan to see results here.'}
                  </div>
                ) : (
                  output.map((line, index) => (
                    <div key={index} className="mb-1">
                      {line}
                    </div>
                  ))
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        {/* Results Tab */}
        <TabsContent value="results" className="space-y-6">
          {/* WordPress Statistics */}
          {vulnerabilityStats && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BarChart3 className="h-5 w-5" />
                  WordPress Security Summary
                </CardTitle>
                <CardDescription>
                  Overview of discovered vulnerabilities and WordPress components
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid gap-4 md:grid-cols-4 lg:grid-cols-7">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-purple-600">{vulnerabilityStats.total || 0}</div>
                    <div className="text-sm text-muted-foreground">Vulnerabilities</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-600">{vulnerabilityStats.plugins || 0}</div>
                    <div className="text-sm text-muted-foreground">Plugins</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-600">{vulnerabilityStats.themes || 0}</div>
                    <div className="text-sm text-muted-foreground">Themes</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-orange-600">{vulnerabilityStats.users || 0}</div>
                    <div className="text-sm text-muted-foreground">Users</div>
                  </div>
                  {SEVERITY_LEVELS.slice(1).map((level) => (
                    <div key={level.value} className="text-center">
                      <div className={cn(
                        'text-2xl font-bold',
                        level.color === 'green' && 'text-green-600',
                        level.color === 'yellow' && 'text-yellow-600',
                        level.color === 'orange' && 'text-orange-600',
                        level.color === 'red' && 'text-red-600'
                      )}>
                        {vulnerabilityStats[level.value] || 0}
                      </div>
                      <div className="text-sm text-muted-foreground">{level.label}</div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
          
          {/* Detailed Results */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="flex items-center gap-2">
                    <FileText className="h-5 w-5" />
                    Scan Results
                  </CardTitle>
                  <CardDescription>
                    Detailed WordPress security findings and vulnerability reports
                  </CardDescription>
                </div>
                
                <Button
                  variant="outline"
                  onClick={exportResults}
                  disabled={!results && output.length === 0}
                >
                  <Download className="h-4 w-4 mr-2" />
                  Export Results
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              {results ? (
                <div className="space-y-4">
                  <pre className="bg-muted p-4 rounded-lg overflow-auto text-sm max-h-96">
                    {JSON.stringify(results, null, 2)}
                  </pre>
                </div>
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  <Wordpress className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>No WordPress scan results available yet.</p>
                  <p className="text-sm">Complete a scan to see detailed security findings here.</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
        
        {/* Command Tab */}
        <TabsContent value="command" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Target className="h-5 w-5" />
                Generated Command
              </CardTitle>
              <CardDescription>
                The WPScan command that will be executed based on your configuration
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="bg-muted p-4 rounded-lg">
                  <code className="text-sm font-mono break-all">
                    {generateCommand()}
                  </code>
                </div>
                
                <div className="flex justify-between items-center">
                  <p className="text-sm text-muted-foreground">
                    This command will be executed on the backend server
                  </p>
                  
                  <Button variant="outline" onClick={copyCommand}>
                    <Copy className="h-4 w-4 mr-2" />
                    Copy Command
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}