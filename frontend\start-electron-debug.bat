@echo off
echo ===================================
echo Starting NexusScan Electron Debug
echo ===================================
echo.

REM Set environment variables
set NODE_ENV=development
set ELECTRON_ENABLE_LOGGING=1
set DEBUG=*

echo Environment variables set:
echo NODE_ENV=%NODE_ENV%
echo ELECTRON_ENABLE_LOGGING=%ELECTRON_ENABLE_LOGGING%
echo.

echo Starting Vite dev server...
start cmd /k "npm run dev:vite"

echo Waiting for Vite to start...
timeout /t 5 /nobreak > nul

echo Starting Electron...
npx electron . --enable-logging --log-level=3

pause