#!/usr/bin/env python3
"""
Progress Tracker for NexusScan Security Scanning Engine
Real-time progress tracking and reporting for security scans
"""

import asyncio
import logging
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Callable, Union
from dataclasses import dataclass, field
from enum import Enum
import json

logger = logging.getLogger(__name__)


class ProgressStatus(Enum):
    """Progress tracking status"""
    INITIALIZING = "initializing"
    IN_PROGRESS = "in_progress"
    PAUSED = "paused"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class ProgressPhase(Enum):
    """Scan progress phases"""
    INITIALIZATION = "initialization"
    DISCOVERY = "discovery"
    ENUMERATION = "enumeration"
    VULNERABILITY_SCANNING = "vulnerability_scanning"
    EXPLOITATION = "exploitation"
    POST_EXPLOITATION = "post_exploitation"
    REPORTING = "reporting"
    CLEANUP = "cleanup"


@dataclass
class ProgressMetrics:
    """Progress metrics for tracking"""
    total_items: int
    completed_items: int
    failed_items: int
    skipped_items: int
    current_item: str = ""
    progress_percentage: float = 0.0
    estimated_remaining_time: Optional[timedelta] = None
    items_per_second: float = 0.0
    success_rate: float = 0.0


@dataclass
class PhaseProgress:
    """Progress information for a specific phase"""
    phase: ProgressPhase
    status: ProgressStatus
    start_time: datetime
    end_time: Optional[datetime] = None
    duration: Optional[timedelta] = None
    metrics: ProgressMetrics = field(default_factory=lambda: ProgressMetrics(0, 0, 0, 0))
    sub_phases: Dict[str, 'PhaseProgress'] = field(default_factory=dict)
    messages: List[str] = field(default_factory=list)
    errors: List[str] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)


@dataclass
class ProgressUpdate:
    """Individual progress update event"""
    scan_id: str
    timestamp: datetime
    phase: ProgressPhase
    status: ProgressStatus
    message: str
    metrics: ProgressMetrics
    metadata: Dict[str, Any] = field(default_factory=dict)
    level: str = "info"  # info, warning, error


@dataclass
class ScanProgress:
    """Complete scan progress information"""
    scan_id: str
    start_time: datetime
    current_phase: ProgressPhase
    overall_status: ProgressStatus
    overall_metrics: ProgressMetrics
    phases: Dict[ProgressPhase, PhaseProgress] = field(default_factory=dict)
    recent_updates: List[ProgressUpdate] = field(default_factory=list)
    estimated_completion: Optional[datetime] = None
    total_duration: Optional[timedelta] = None


class ProgressTracker:
    """Tracks and reports real-time progress for security scans"""

    def __init__(self, scan_id: str, max_updates_history: int = 100):
        """Initialize progress tracker
        
        Args:
            scan_id: Unique scan identifier
            max_updates_history: Maximum number of updates to keep in history
        """
        self.scan_id = scan_id
        self.max_updates_history = max_updates_history
        
        # Progress state
        self.progress = ScanProgress(
            scan_id=scan_id,
            start_time=datetime.now(),
            current_phase=ProgressPhase.INITIALIZATION,
            overall_status=ProgressStatus.INITIALIZING,
            overall_metrics=ProgressMetrics(0, 0, 0, 0)
        )
        
        # Callbacks for progress updates
        self.update_callbacks: List[Callable[[ProgressUpdate], None]] = []
        self.phase_callbacks: Dict[ProgressPhase, List[Callable]] = {}
        self.completion_callbacks: List[Callable[[ScanProgress], None]] = []
        
        # Performance tracking
        self.performance_metrics = {
            "start_time": time.time(),
            "phase_times": {},
            "throughput_samples": [],
            "memory_usage": [],
            "cpu_usage": []
        }
        
        self.logger = logging.getLogger(__name__)
        self.logger.info(f"Progress tracker initialized for scan {scan_id}")

    async def initialize(self, total_phases: int, total_targets: int, **kwargs):
        """Initialize progress tracking
        
        Args:
            total_phases: Total number of phases
            total_targets: Total number of targets
            **kwargs: Additional initialization parameters
        """
        # Initialize overall metrics
        self.progress.overall_metrics = ProgressMetrics(
            total_items=total_targets * total_phases,
            completed_items=0,
            failed_items=0,
            skipped_items=0
        )
        
        # Initialize phase structures
        for phase in ProgressPhase:
            self.progress.phases[phase] = PhaseProgress(
                phase=phase,
                status=ProgressStatus.INITIALIZING,
                start_time=datetime.now(),
                metrics=ProgressMetrics(total_targets, 0, 0, 0)
            )
        
        await self._emit_update(
            ProgressPhase.INITIALIZATION,
            ProgressStatus.IN_PROGRESS,
            f"Initialized progress tracking for {total_targets} targets across {total_phases} phases",
            metadata=kwargs
        )

    async def start_phase(self, phase: ProgressPhase, total_items: int = 0, message: str = ""):
        """Start tracking a new phase
        
        Args:
            phase: Phase being started
            total_items: Total items to process in this phase
            message: Optional status message
        """
        if phase not in self.progress.phases:
            self.progress.phases[phase] = PhaseProgress(
                phase=phase,
                status=ProgressStatus.IN_PROGRESS,
                start_time=datetime.now(),
                metrics=ProgressMetrics(total_items, 0, 0, 0)
            )
        else:
            phase_progress = self.progress.phases[phase]
            phase_progress.status = ProgressStatus.IN_PROGRESS
            phase_progress.start_time = datetime.now()
            phase_progress.metrics.total_items = total_items
        
        self.progress.current_phase = phase
        self.performance_metrics["phase_times"][phase.value] = time.time()
        
        if not message:
            message = f"Started {phase.value.replace('_', ' ').title()} phase"
        
        await self._emit_update(phase, ProgressStatus.IN_PROGRESS, message)
        
        # Trigger phase-specific callbacks
        if phase in self.phase_callbacks:
            for callback in self.phase_callbacks[phase]:
                try:
                    await callback(self.progress.phases[phase])
                except Exception as e:
                    self.logger.error(f"Phase callback error: {e}")

    async def update_phase_progress(self, 
                                  phase: ProgressPhase,
                                  completed_items: int = None,
                                  failed_items: int = None,
                                  current_item: str = "",
                                  message: str = "",
                                  metadata: Dict[str, Any] = None):
        """Update progress for a specific phase
        
        Args:
            phase: Phase to update
            completed_items: Number of completed items
            failed_items: Number of failed items
            current_item: Currently processing item
            message: Status message
            metadata: Additional metadata
        """
        if phase not in self.progress.phases:
            await self.start_phase(phase)
        
        phase_progress = self.progress.phases[phase]
        metrics = phase_progress.metrics
        
        # Update metrics
        if completed_items is not None:
            metrics.completed_items = completed_items
        if failed_items is not None:
            metrics.failed_items = failed_items
        if current_item:
            metrics.current_item = current_item
        
        # Calculate progress percentage
        total_processed = metrics.completed_items + metrics.failed_items + metrics.skipped_items
        if metrics.total_items > 0:
            metrics.progress_percentage = (total_processed / metrics.total_items) * 100
        
        # Calculate success rate
        if total_processed > 0:
            metrics.success_rate = (metrics.completed_items / total_processed) * 100
        
        # Calculate throughput
        elapsed_time = (datetime.now() - phase_progress.start_time).total_seconds()
        if elapsed_time > 0:
            metrics.items_per_second = total_processed / elapsed_time
            
            # Estimate remaining time
            if metrics.items_per_second > 0 and metrics.total_items > total_processed:
                remaining_items = metrics.total_items - total_processed
                remaining_seconds = remaining_items / metrics.items_per_second
                metrics.estimated_remaining_time = timedelta(seconds=remaining_seconds)
        
        # Update overall progress
        await self._update_overall_progress()
        
        # Emit update
        if not message:
            message = f"{phase.value.replace('_', ' ').title()}: {total_processed}/{metrics.total_items} items processed"
        
        await self._emit_update(phase, ProgressStatus.IN_PROGRESS, message, metadata or {})

    async def complete_phase(self, phase: ProgressPhase, message: str = ""):
        """Mark a phase as completed
        
        Args:
            phase: Phase that completed
            message: Completion message
        """
        if phase not in self.progress.phases:
            return
        
        phase_progress = self.progress.phases[phase]
        phase_progress.status = ProgressStatus.COMPLETED
        phase_progress.end_time = datetime.now()
        phase_progress.duration = phase_progress.end_time - phase_progress.start_time
        
        # Record phase completion time
        if phase.value in self.performance_metrics["phase_times"]:
            start_time = self.performance_metrics["phase_times"][phase.value]
            self.performance_metrics["phase_times"][phase.value] = time.time() - start_time
        
        if not message:
            metrics = phase_progress.metrics
            message = f"Completed {phase.value.replace('_', ' ').title()} phase: {metrics.completed_items} successful, {metrics.failed_items} failed"
        
        await self._emit_update(phase, ProgressStatus.COMPLETED, message)
        await self._update_overall_progress()

    async def fail_phase(self, phase: ProgressPhase, error_message: str):
        """Mark a phase as failed
        
        Args:
            phase: Phase that failed
            error_message: Error description
        """
        if phase not in self.progress.phases:
            await self.start_phase(phase)
        
        phase_progress = self.progress.phases[phase]
        phase_progress.status = ProgressStatus.FAILED
        phase_progress.end_time = datetime.now()
        phase_progress.duration = phase_progress.end_time - phase_progress.start_time
        phase_progress.errors.append(error_message)
        
        await self._emit_update(phase, ProgressStatus.FAILED, f"Phase failed: {error_message}", level="error")
        await self._update_overall_progress()

    async def add_phase_message(self, phase: ProgressPhase, message: str, level: str = "info"):
        """Add a message to a phase
        
        Args:
            phase: Target phase
            message: Message to add
            level: Message level (info, warning, error)
        """
        if phase in self.progress.phases:
            phase_progress = self.progress.phases[phase]
            
            if level == "error":
                phase_progress.errors.append(message)
            elif level == "warning":
                phase_progress.warnings.append(message)
            else:
                phase_progress.messages.append(message)
            
            await self._emit_update(phase, phase_progress.status, message, level=level)

    async def pause_tracking(self):
        """Pause progress tracking"""
        self.progress.overall_status = ProgressStatus.PAUSED
        current_phase = self.progress.current_phase
        
        if current_phase in self.progress.phases:
            self.progress.phases[current_phase].status = ProgressStatus.PAUSED
        
        await self._emit_update(current_phase, ProgressStatus.PAUSED, "Progress tracking paused")

    async def resume_tracking(self):
        """Resume progress tracking"""
        self.progress.overall_status = ProgressStatus.IN_PROGRESS
        current_phase = self.progress.current_phase
        
        if current_phase in self.progress.phases:
            self.progress.phases[current_phase].status = ProgressStatus.IN_PROGRESS
        
        await self._emit_update(current_phase, ProgressStatus.IN_PROGRESS, "Progress tracking resumed")

    async def complete_tracking(self, message: str = ""):
        """Complete progress tracking
        
        Args:
            message: Completion message
        """
        self.progress.overall_status = ProgressStatus.COMPLETED
        self.progress.total_duration = datetime.now() - self.progress.start_time
        
        if not message:
            message = f"Scan completed in {self.progress.total_duration}"
        
        await self._emit_update(self.progress.current_phase, ProgressStatus.COMPLETED, message)
        
        # Trigger completion callbacks
        for callback in self.completion_callbacks:
            try:
                await callback(self.progress)
            except Exception as e:
                self.logger.error(f"Completion callback error: {e}")

    async def fail_tracking(self, error_message: str):
        """Fail progress tracking
        
        Args:
            error_message: Error description
        """
        self.progress.overall_status = ProgressStatus.FAILED
        self.progress.total_duration = datetime.now() - self.progress.start_time
        
        await self._emit_update(self.progress.current_phase, ProgressStatus.FAILED, f"Scan failed: {error_message}", level="error")

    def add_update_callback(self, callback: Callable[[ProgressUpdate], None]):
        """Add callback for progress updates
        
        Args:
            callback: Callback function
        """
        self.update_callbacks.append(callback)

    def add_phase_callback(self, phase: ProgressPhase, callback: Callable):
        """Add callback for specific phase events
        
        Args:
            phase: Target phase
            callback: Callback function
        """
        if phase not in self.phase_callbacks:
            self.phase_callbacks[phase] = []
        self.phase_callbacks[phase].append(callback)

    def add_completion_callback(self, callback: Callable[[ScanProgress], None]):
        """Add callback for scan completion
        
        Args:
            callback: Callback function
        """
        self.completion_callbacks.append(callback)

    def get_current_progress(self) -> ScanProgress:
        """Get current progress state
        
        Returns:
            Current progress information
        """
        return self.progress

    def get_phase_progress(self, phase: ProgressPhase) -> Optional[PhaseProgress]:
        """Get progress for specific phase
        
        Args:
            phase: Target phase
            
        Returns:
            Phase progress or None if not found
        """
        return self.progress.phases.get(phase)

    def get_performance_metrics(self) -> Dict[str, Any]:
        """Get performance metrics
        
        Returns:
            Performance metrics dictionary
        """
        return self.performance_metrics.copy()

    def export_progress_data(self) -> Dict[str, Any]:
        """Export progress data for persistence or analysis
        
        Returns:
            Serializable progress data
        """
        def serialize_datetime(obj):
            if isinstance(obj, datetime):
                return obj.isoformat()
            elif isinstance(obj, timedelta):
                return obj.total_seconds()
            return obj
        
        # Convert progress to dictionary
        progress_dict = {
            "scan_id": self.progress.scan_id,
            "start_time": serialize_datetime(self.progress.start_time),
            "current_phase": self.progress.current_phase.value,
            "overall_status": self.progress.overall_status.value,
            "overall_metrics": {
                "total_items": self.progress.overall_metrics.total_items,
                "completed_items": self.progress.overall_metrics.completed_items,
                "failed_items": self.progress.overall_metrics.failed_items,
                "skipped_items": self.progress.overall_metrics.skipped_items,
                "progress_percentage": self.progress.overall_metrics.progress_percentage,
                "success_rate": self.progress.overall_metrics.success_rate
            },
            "phases": {},
            "performance_metrics": self.performance_metrics
        }
        
        # Add phase data
        for phase, phase_progress in self.progress.phases.items():
            progress_dict["phases"][phase.value] = {
                "status": phase_progress.status.value,
                "start_time": serialize_datetime(phase_progress.start_time),
                "end_time": serialize_datetime(phase_progress.end_time),
                "duration": serialize_datetime(phase_progress.duration),
                "metrics": {
                    "total_items": phase_progress.metrics.total_items,
                    "completed_items": phase_progress.metrics.completed_items,
                    "failed_items": phase_progress.metrics.failed_items,
                    "progress_percentage": phase_progress.metrics.progress_percentage,
                    "success_rate": phase_progress.metrics.success_rate
                },
                "messages": phase_progress.messages,
                "errors": phase_progress.errors,
                "warnings": phase_progress.warnings
            }
        
        return progress_dict

    # Private methods

    async def _update_overall_progress(self):
        """Update overall progress metrics"""
        total_completed = 0
        total_failed = 0
        total_skipped = 0
        total_items = 0
        
        for phase_progress in self.progress.phases.values():
            metrics = phase_progress.metrics
            total_completed += metrics.completed_items
            total_failed += metrics.failed_items
            total_skipped += metrics.skipped_items
            total_items += metrics.total_items
        
        overall_metrics = self.progress.overall_metrics
        overall_metrics.completed_items = total_completed
        overall_metrics.failed_items = total_failed
        overall_metrics.skipped_items = total_skipped
        overall_metrics.total_items = total_items
        
        # Calculate overall progress percentage
        total_processed = total_completed + total_failed + total_skipped
        if total_items > 0:
            overall_metrics.progress_percentage = (total_processed / total_items) * 100
        
        # Calculate overall success rate
        if total_processed > 0:
            overall_metrics.success_rate = (total_completed / total_processed) * 100
        
        # Estimate completion time
        if overall_metrics.progress_percentage > 0:
            elapsed_time = datetime.now() - self.progress.start_time
            total_estimated_time = elapsed_time.total_seconds() / (overall_metrics.progress_percentage / 100)
            remaining_seconds = total_estimated_time - elapsed_time.total_seconds()
            
            if remaining_seconds > 0:
                self.progress.estimated_completion = datetime.now() + timedelta(seconds=remaining_seconds)

    async def _emit_update(self, 
                         phase: ProgressPhase, 
                         status: ProgressStatus, 
                         message: str,
                         metadata: Dict[str, Any] = None,
                         level: str = "info"):
        """Emit a progress update
        
        Args:
            phase: Current phase
            status: Current status
            message: Update message
            metadata: Additional metadata
            level: Message level
        """
        # Get current metrics for the phase
        phase_metrics = ProgressMetrics(0, 0, 0, 0)
        if phase in self.progress.phases:
            phase_metrics = self.progress.phases[phase].metrics
        
        # Create update
        update = ProgressUpdate(
            scan_id=self.scan_id,
            timestamp=datetime.now(),
            phase=phase,
            status=status,
            message=message,
            metrics=phase_metrics,
            metadata=metadata or {},
            level=level
        )
        
        # Add to recent updates
        self.progress.recent_updates.append(update)
        
        # Limit history size
        if len(self.progress.recent_updates) > self.max_updates_history:
            self.progress.recent_updates = self.progress.recent_updates[-self.max_updates_history:]
        
        # Log update
        log_method = getattr(self.logger, level)
        log_method(f"[{self.scan_id}] {phase.value}: {message}")
        
        # Trigger callbacks
        for callback in self.update_callbacks:
            try:
                await callback(update)
            except Exception as e:
                self.logger.error(f"Update callback error: {e}")


class ProgressAggregator:
    """Aggregates progress from multiple scans"""
    
    def __init__(self):
        self.trackers: Dict[str, ProgressTracker] = {}
        self.logger = logging.getLogger(__name__)
    
    def add_tracker(self, tracker: ProgressTracker):
        """Add a progress tracker
        
        Args:
            tracker: Progress tracker to add
        """
        self.trackers[tracker.scan_id] = tracker
        tracker.add_completion_callback(self._on_scan_completed)
    
    def remove_tracker(self, scan_id: str):
        """Remove a progress tracker
        
        Args:
            scan_id: Scan ID to remove
        """
        if scan_id in self.trackers:
            del self.trackers[scan_id]
    
    def get_aggregate_progress(self) -> Dict[str, Any]:
        """Get aggregated progress across all scans
        
        Returns:
            Aggregate progress information
        """
        if not self.trackers:
            return {}
        
        total_scans = len(self.trackers)
        completed_scans = 0
        failed_scans = 0
        running_scans = 0
        
        overall_progress = 0.0
        
        for tracker in self.trackers.values():
            progress = tracker.get_current_progress()
            overall_progress += progress.overall_metrics.progress_percentage
            
            if progress.overall_status == ProgressStatus.COMPLETED:
                completed_scans += 1
            elif progress.overall_status == ProgressStatus.FAILED:
                failed_scans += 1
            else:
                running_scans += 1
        
        return {
            "total_scans": total_scans,
            "completed_scans": completed_scans,
            "failed_scans": failed_scans,
            "running_scans": running_scans,
            "overall_progress_percentage": overall_progress / total_scans if total_scans > 0 else 0,
            "success_rate": (completed_scans / total_scans * 100) if total_scans > 0 else 0
        }
    
    async def _on_scan_completed(self, progress: ScanProgress):
        """Handle scan completion"""
        self.logger.info(f"Scan {progress.scan_id} completed with status: {progress.overall_status.value}")