#!/usr/bin/env python3
"""
Database Performance Monitor
Real-time database performance monitoring, query analysis, and optimization recommendations
"""

import asyncio
import time
import threading
import json
from typing import Any, Dict, List, Optional, Tuple, Callable
from dataclasses import dataclass, field, asdict
from datetime import datetime, timedelta
from collections import deque, defaultdict
from enum import Enum
import logging
import statistics
import os

logger = logging.getLogger(__name__)

class AlertLevel(Enum):
    """Performance alert levels"""
    INFO = "info"
    WARNING = "warning"
    CRITICAL = "critical"

class MetricType(Enum):
    """Performance metric types"""
    QUERY_TIME = "query_time"
    CONNECTION_COUNT = "connection_count"
    CACHE_HIT_RATE = "cache_hit_rate"
    DATABASE_SIZE = "database_size"
    SLOW_QUERIES = "slow_queries"
    LOCK_CONTENTION = "lock_contention"
    MEMORY_USAGE = "memory_usage"

@dataclass
class PerformanceAlert:
    """Performance alert"""
    level: AlertLevel
    metric: MetricType
    message: str
    value: float
    threshold: float
    timestamp: datetime = field(default_factory=datetime.now)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            "level": self.level.value,
            "metric": self.metric.value,
            "message": self.message,
            "value": self.value,
            "threshold": self.threshold,
            "timestamp": self.timestamp.isoformat()
        }

@dataclass
class QueryAnalysis:
    """Query performance analysis"""
    query_pattern: str
    execution_count: int = 0
    total_time: float = 0.0
    min_time: float = float('inf')
    max_time: float = 0.0
    avg_time: float = 0.0
    percentile_95: float = 0.0
    error_count: int = 0
    last_execution: Optional[datetime] = None
    execution_times: deque = field(default_factory=lambda: deque(maxlen=1000))
    
    def add_execution(self, execution_time: float, success: bool = True):
        """Add execution record"""
        self.execution_count += 1
        self.total_time += execution_time
        self.min_time = min(self.min_time, execution_time)
        self.max_time = max(self.max_time, execution_time)
        self.avg_time = self.total_time / self.execution_count
        self.last_execution = datetime.now()
        self.execution_times.append(execution_time)
        
        if not success:
            self.error_count += 1
        
        # Calculate 95th percentile
        if len(self.execution_times) >= 20:
            self.percentile_95 = statistics.quantiles(list(self.execution_times), n=20)[18]
    
    def get_stats(self) -> Dict[str, Any]:
        """Get query statistics"""
        error_rate = (self.error_count / self.execution_count * 100) if self.execution_count > 0 else 0
        
        return {
            "query_pattern": self.query_pattern,
            "execution_count": self.execution_count,
            "avg_time": self.avg_time,
            "min_time": self.min_time if self.min_time != float('inf') else 0,
            "max_time": self.max_time,
            "percentile_95": self.percentile_95,
            "error_count": self.error_count,
            "error_rate": error_rate,
            "last_execution": self.last_execution.isoformat() if self.last_execution else None
        }

@dataclass
class SystemMetrics:
    """System performance metrics"""
    timestamp: datetime = field(default_factory=datetime.now)
    cpu_usage: float = 0.0
    memory_usage: float = 0.0
    disk_usage: float = 0.0
    connection_count: int = 0
    active_queries: int = 0
    cache_hit_rate: float = 0.0
    database_size_mb: float = 0.0
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return asdict(self)

class PerformanceMonitor:
    """Real-time database performance monitor"""
    
    def __init__(self, 
                 sample_interval: int = 60,  # seconds
                 history_retention_hours: int = 24,
                 slow_query_threshold: float = 1.0,
                 enable_alerts: bool = True):
        
        self.sample_interval = sample_interval
        self.history_retention = timedelta(hours=history_retention_hours)
        self.slow_query_threshold = slow_query_threshold
        self.enable_alerts = enable_alerts
        
        # Monitoring data
        self.query_analytics: Dict[str, QueryAnalysis] = {}
        self.system_metrics: deque = deque(maxlen=1440)  # 24 hours at 1-minute intervals
        self.alerts: deque = deque(maxlen=1000)
        
        # Thresholds for alerts
        self.alert_thresholds = {
            MetricType.QUERY_TIME: 5.0,  # seconds
            MetricType.CONNECTION_COUNT: 50,
            MetricType.CACHE_HIT_RATE: 70.0,  # percentage
            MetricType.DATABASE_SIZE: 1000.0,  # MB
            MetricType.MEMORY_USAGE: 80.0,  # percentage
        }
        
        # Monitoring state
        self.monitoring_active = False
        self.lock = threading.RLock()
        
        # Callbacks
        self.alert_callbacks: List[Callable[[PerformanceAlert], None]] = []
    
    def start_monitoring(self):
        """Start performance monitoring"""
        if self.monitoring_active:
            return
        
        self.monitoring_active = True
        asyncio.create_task(self._monitoring_loop())
        logger.info("Performance monitoring started")
    
    def stop_monitoring(self):
        """Stop performance monitoring"""
        self.monitoring_active = False
        logger.info("Performance monitoring stopped")
    
    def record_query_execution(self, query: str, execution_time: float, success: bool = True):
        """Record query execution for analysis"""
        query_pattern = self._normalize_query(query)
        
        with self.lock:
            if query_pattern not in self.query_analytics:
                self.query_analytics[query_pattern] = QueryAnalysis(query_pattern)
            
            self.query_analytics[query_pattern].add_execution(execution_time, success)
            
            # Check for slow query alert
            if execution_time > self.slow_query_threshold and self.enable_alerts:
                alert = PerformanceAlert(
                    level=AlertLevel.WARNING if execution_time < self.slow_query_threshold * 2 else AlertLevel.CRITICAL,
                    metric=MetricType.SLOW_QUERIES,
                    message=f"Slow query detected: {execution_time:.2f}s - {query[:100]}...",
                    value=execution_time,
                    threshold=self.slow_query_threshold
                )
                self._add_alert(alert)
    
    def record_system_metrics(self, metrics: SystemMetrics):
        """Record system performance metrics"""
        with self.lock:
            self.system_metrics.append(metrics)
            
            # Check for alerts
            if self.enable_alerts:
                self._check_metric_alerts(metrics)
    
    def _normalize_query(self, query: str) -> str:
        """Normalize query for pattern analysis"""
        # Remove extra whitespace
        normalized = ' '.join(query.split())
        
        # Replace literal values with placeholders
        import re
        
        # Replace numbers
        normalized = re.sub(r'\b\d+\b', '?', normalized)
        
        # Replace quoted strings
        normalized = re.sub(r"'[^']*'", '?', normalized)
        normalized = re.sub(r'"[^"]*"', '?', normalized)
        
        # Replace IN clauses with multiple values
        normalized = re.sub(r'IN\s*\([^)]+\)', 'IN (?)', normalized, flags=re.IGNORECASE)
        
        return normalized[:200]  # Limit length
    
    def _check_metric_alerts(self, metrics: SystemMetrics):
        """Check metrics against alert thresholds"""
        # Connection count alert
        if metrics.connection_count > self.alert_thresholds[MetricType.CONNECTION_COUNT]:
            alert = PerformanceAlert(
                level=AlertLevel.WARNING,
                metric=MetricType.CONNECTION_COUNT,
                message=f"High connection count: {metrics.connection_count}",
                value=metrics.connection_count,
                threshold=self.alert_thresholds[MetricType.CONNECTION_COUNT]
            )
            self._add_alert(alert)
        
        # Cache hit rate alert
        if metrics.cache_hit_rate < self.alert_thresholds[MetricType.CACHE_HIT_RATE]:
            alert = PerformanceAlert(
                level=AlertLevel.WARNING,
                metric=MetricType.CACHE_HIT_RATE,
                message=f"Low cache hit rate: {metrics.cache_hit_rate:.1f}%",
                value=metrics.cache_hit_rate,
                threshold=self.alert_thresholds[MetricType.CACHE_HIT_RATE]
            )
            self._add_alert(alert)
        
        # Database size alert
        if metrics.database_size_mb > self.alert_thresholds[MetricType.DATABASE_SIZE]:
            alert = PerformanceAlert(
                level=AlertLevel.INFO,
                metric=MetricType.DATABASE_SIZE,
                message=f"Large database size: {metrics.database_size_mb:.1f} MB",
                value=metrics.database_size_mb,
                threshold=self.alert_thresholds[MetricType.DATABASE_SIZE]
            )
            self._add_alert(alert)
        
        # Memory usage alert
        if metrics.memory_usage > self.alert_thresholds[MetricType.MEMORY_USAGE]:
            level = AlertLevel.CRITICAL if metrics.memory_usage > 90 else AlertLevel.WARNING
            alert = PerformanceAlert(
                level=level,
                metric=MetricType.MEMORY_USAGE,
                message=f"High memory usage: {metrics.memory_usage:.1f}%",
                value=metrics.memory_usage,
                threshold=self.alert_thresholds[MetricType.MEMORY_USAGE]
            )
            self._add_alert(alert)
    
    def _add_alert(self, alert: PerformanceAlert):
        """Add alert and trigger callbacks"""
        with self.lock:
            self.alerts.append(alert)
            
            # Trigger callbacks
            for callback in self.alert_callbacks:
                try:
                    callback(alert)
                except Exception as e:
                    logger.error(f"Alert callback error: {e}")
            
            # Log alert
            log_level = logging.INFO
            if alert.level == AlertLevel.WARNING:
                log_level = logging.WARNING
            elif alert.level == AlertLevel.CRITICAL:
                log_level = logging.ERROR
            
            logger.log(log_level, f"Performance Alert [{alert.level.value.upper()}]: {alert.message}")
    
    def add_alert_callback(self, callback: Callable[[PerformanceAlert], None]):
        """Add alert callback function"""
        self.alert_callbacks.append(callback)
    
    async def _monitoring_loop(self):
        """Main monitoring loop"""
        while self.monitoring_active:
            try:
                await asyncio.sleep(self.sample_interval)
                
                # Collect system metrics
                metrics = await self._collect_system_metrics()
                self.record_system_metrics(metrics)
                
                # Cleanup old data
                self._cleanup_old_data()
                
            except Exception as e:
                logger.error(f"Monitoring loop error: {e}")
    
    async def _collect_system_metrics(self) -> SystemMetrics:
        """Collect current system metrics"""
        metrics = SystemMetrics()
        
        try:
            # Get CPU and memory usage
            import psutil
            
            metrics.cpu_usage = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            metrics.memory_usage = memory.percent
            
            disk = psutil.disk_usage('/')
            metrics.disk_usage = disk.percent
            
        except ImportError:
            # psutil not available, use basic metrics
            pass
        except Exception as e:
            logger.warning(f"Failed to collect system metrics: {e}")
        
        return metrics
    
    def _cleanup_old_data(self):
        """Clean up old monitoring data"""
        cutoff_time = datetime.now() - self.history_retention
        
        with self.lock:
            # Clean up old system metrics (deque handles this automatically)
            
            # Clean up old query analytics
            for pattern in list(self.query_analytics.keys()):
                analysis = self.query_analytics[pattern]
                if (analysis.last_execution and 
                    analysis.last_execution < cutoff_time):
                    del self.query_analytics[pattern]
    
    def get_query_analytics(self, limit: int = 50) -> List[Dict[str, Any]]:
        """Get query performance analytics"""
        with self.lock:
            analytics = list(self.query_analytics.values())
            
            # Sort by average execution time (descending)
            analytics.sort(key=lambda x: x.avg_time, reverse=True)
            
            return [analysis.get_stats() for analysis in analytics[:limit]]
    
    def get_slow_queries(self, threshold: Optional[float] = None) -> List[Dict[str, Any]]:
        """Get slow queries above threshold"""
        threshold = threshold or self.slow_query_threshold
        
        slow_queries = []
        with self.lock:
            for analysis in self.query_analytics.values():
                if analysis.avg_time > threshold:
                    stats = analysis.get_stats()
                    stats['severity'] = 'critical' if analysis.avg_time > threshold * 2 else 'warning'
                    slow_queries.append(stats)
        
        # Sort by average time
        slow_queries.sort(key=lambda x: x['avg_time'], reverse=True)
        return slow_queries
    
    def get_system_metrics_history(self, hours: int = 24) -> List[Dict[str, Any]]:
        """Get system metrics history"""
        cutoff_time = datetime.now() - timedelta(hours=hours)
        
        with self.lock:
            filtered_metrics = [
                metrics.to_dict() for metrics in self.system_metrics
                if metrics.timestamp >= cutoff_time
            ]
        
        return filtered_metrics
    
    def get_recent_alerts(self, hours: int = 24, level: Optional[AlertLevel] = None) -> List[Dict[str, Any]]:
        """Get recent alerts"""
        cutoff_time = datetime.now() - timedelta(hours=hours)
        
        with self.lock:
            filtered_alerts = [
                alert.to_dict() for alert in self.alerts
                if alert.timestamp >= cutoff_time and (level is None or alert.level == level)
            ]
        
        # Sort by timestamp (most recent first)
        filtered_alerts.sort(key=lambda x: x['timestamp'], reverse=True)
        return filtered_alerts
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """Get performance summary"""
        with self.lock:
            current_time = datetime.now()
            
            # Query analytics summary
            total_queries = len(self.query_analytics)
            slow_query_count = len([
                a for a in self.query_analytics.values() 
                if a.avg_time > self.slow_query_threshold
            ])
            
            # Recent alerts summary
            recent_alerts = self.get_recent_alerts(hours=24)
            alert_counts = defaultdict(int)
            for alert in recent_alerts:
                alert_counts[alert['level']] += 1
            
            # System metrics summary
            recent_metrics = self.get_system_metrics_history(hours=1)
            avg_metrics = {}
            if recent_metrics:
                for key in ['cpu_usage', 'memory_usage', 'disk_usage', 'cache_hit_rate']:
                    values = [m.get(key, 0) for m in recent_metrics if m.get(key) is not None]
                    avg_metrics[key] = statistics.mean(values) if values else 0
            
            return {
                "timestamp": current_time.isoformat(),
                "query_analytics": {
                    "total_patterns": total_queries,
                    "slow_queries": slow_query_count,
                    "slow_query_rate": (slow_query_count / total_queries * 100) if total_queries > 0 else 0
                },
                "alerts": {
                    "total_24h": len(recent_alerts),
                    "critical": alert_counts.get('critical', 0),
                    "warning": alert_counts.get('warning', 0),
                    "info": alert_counts.get('info', 0)
                },
                "system_metrics": avg_metrics,
                "status": self._get_overall_status()
            }
    
    def _get_overall_status(self) -> str:
        """Get overall system status"""
        recent_alerts = self.get_recent_alerts(hours=1)
        
        critical_alerts = len([a for a in recent_alerts if a['level'] == 'critical'])
        warning_alerts = len([a for a in recent_alerts if a['level'] == 'warning'])
        
        if critical_alerts > 0:
            return "critical"
        elif warning_alerts > 2:
            return "warning"
        else:
            return "healthy"
    
    def generate_optimization_recommendations(self) -> List[Dict[str, Any]]:
        """Generate optimization recommendations"""
        recommendations = []
        
        with self.lock:
            # Analyze slow queries
            slow_queries = self.get_slow_queries()
            if slow_queries:
                recommendations.append({
                    "type": "query_optimization",
                    "priority": "high",
                    "title": "Optimize Slow Queries",
                    "description": f"Found {len(slow_queries)} slow query patterns. Consider adding indexes or optimizing query structure.",
                    "details": slow_queries[:5]  # Top 5 slow queries
                })
            
            # Analyze cache hit rate
            recent_metrics = self.get_system_metrics_history(hours=1)
            if recent_metrics:
                avg_cache_hit_rate = statistics.mean([m.get('cache_hit_rate', 0) for m in recent_metrics])
                if avg_cache_hit_rate < 80:
                    recommendations.append({
                        "type": "cache_optimization",
                        "priority": "medium",
                        "title": "Improve Cache Hit Rate",
                        "description": f"Current cache hit rate is {avg_cache_hit_rate:.1f}%. Consider increasing cache size or reviewing cache strategy.",
                        "current_value": avg_cache_hit_rate,
                        "target_value": 90
                    })
            
            # Analyze connection patterns
            high_connection_alerts = [
                a for a in self.get_recent_alerts(hours=24)
                if a['metric'] == 'connection_count'
            ]
            if high_connection_alerts:
                recommendations.append({
                    "type": "connection_pooling",
                    "priority": "medium",
                    "title": "Optimize Connection Usage",
                    "description": "High connection count detected. Consider optimizing connection pooling or query patterns.",
                    "alert_count": len(high_connection_alerts)
                })
            
            # Analyze database size growth
            database_size_alerts = [
                a for a in self.get_recent_alerts(hours=24)
                if a['metric'] == 'database_size'
            ]
            if database_size_alerts:
                recommendations.append({
                    "type": "data_management",
                    "priority": "low",
                    "title": "Consider Data Archiving",
                    "description": "Database size is growing. Consider implementing data archiving or partitioning strategies.",
                    "current_size": database_size_alerts[-1]['value']
                })
        
        return recommendations
    
    def export_metrics(self, filepath: str, format: str = "json"):
        """Export metrics to file"""
        export_data = {
            "export_timestamp": datetime.now().isoformat(),
            "query_analytics": self.get_query_analytics(),
            "system_metrics": self.get_system_metrics_history(),
            "alerts": self.get_recent_alerts(),
            "performance_summary": self.get_performance_summary(),
            "recommendations": self.generate_optimization_recommendations()
        }
        
        if format.lower() == "json":
            with open(filepath, 'w') as f:
                json.dump(export_data, f, indent=2)
        else:
            raise ValueError(f"Unsupported export format: {format}")
        
        logger.info(f"Performance metrics exported to {filepath}")

# Global performance monitor instance
performance_monitor: Optional[PerformanceMonitor] = None

def get_performance_monitor() -> PerformanceMonitor:
    """Get global performance monitor instance"""
    global performance_monitor
    
    if performance_monitor is None:
        performance_monitor = PerformanceMonitor()
        performance_monitor.start_monitoring()
    
    return performance_monitor

def close_performance_monitor():
    """Close global performance monitor"""
    global performance_monitor
    
    if performance_monitor:
        performance_monitor.stop_monitoring()
        performance_monitor = None