"""
Dirb Scanner Integration for NexusScan Desktop Application
Directory and file bruteforcing using dirb.
"""

import json
import asyncio
import logging
import subprocess
import re
from datetime import datetime
from typing import Dict, List, Optional, Callable, Any
from dataclasses import dataclass

from security.tools.tool_registry import (
    ToolMetadata, ToolCategory, ToolCapabilities,
    ScanOptions, ScanResult, ToolStatus, register_tool
)
from security.tools.base_scanner import BaseScanner

logger = logging.getLogger(__name__)


@dataclass
class DirbScanOptions(ScanOptions):
    """Dirb-specific scan options"""
    wordlist: str = "/usr/share/dirb/wordlists/common.txt"
    extensions: List[str] = None
    user_agent: str = ""
    username: str = ""
    password: str = ""
    proxy: str = ""
    cookie: str = ""
    delay: int = 0
    max_threads: int = 20
    recursive: bool = False
    case_insensitive: bool = False
    silent: bool = False
    
    def __post_init__(self):
        super().__post_init__()
        if self.extensions is None:
            self.extensions = []


@dataclass
class DirbResult:
    """Dirb scan result entry"""
    url: str
    status_code: int
    size: int
    response_time: float = 0.0
    method: str = "GET"
    content_type: str = ""


class DirbScanner(BaseScanner):
    """Dirb directory/file bruteforcer"""
    
    def __init__(self):
        super().__init__()
        self.tool_name = "dirb"
        self.scan_types = ["directory", "file", "recursive"]
        self.current_process = None
        
    def get_metadata(self) -> ToolMetadata:
        """Get tool metadata"""
        return ToolMetadata(
            name="Dirb",
            category=ToolCategory.WEB_SCANNER,
            description="Directory and file bruteforcer",
            version=self._get_version(),
            author="Dark Raver",
            dependencies=["dirb"],
            capabilities=ToolCapabilities(
                scan_types=self.scan_types,
                output_formats=["txt", "json"],
                authentication_support=True,
                custom_headers_support=True,
                proxy_support=True,
                rate_limiting=True
            )
        )
    
    def _get_version(self) -> str:
        """Get Dirb version"""
        try:
            result = subprocess.run(
                ["dirb"],
                capture_output=True,
                text=True,
                timeout=10
            )
            if "DIRB" in result.stdout or "DIRB" in result.stderr:
                version_match = re.search(r"DIRB v([\d.]+)", result.stdout + result.stderr)
                if version_match:
                    return version_match.group(1)
            return "2.22"  # Default fallback
        except Exception as e:
            logger.warning(f"Could not get Dirb version: {e}")
            return "2.22"
    
    def is_available(self) -> bool:
        """Check if Dirb is available"""
        try:
            result = subprocess.run(
                ["dirb"],
                capture_output=True,
                text=True,
                timeout=10
            )
            return "DIRB" in result.stdout or "DIRB" in result.stderr
        except (FileNotFoundError, subprocess.TimeoutExpired):
            return False
    
    async def scan(self, 
                   target: str, 
                   options: Optional[DirbScanOptions] = None,
                   progress_callback: Optional[Callable] = None) -> ScanResult:
        """Perform Dirb scan"""
        if options is None:
            options = DirbScanOptions(target=target)
        
        scan_id = f"dirb_{target.replace('://', '_').replace('/', '_')}"
    
    def check_native_availability(self) -> bool:
        """Check if native tool is available"""
        return self.is_available()
    
    async def execute_native_scan(self, options: ScanOptions,
                                 progress_callback: Optional[Callable[[float, str], None]] = None) -> ScanResult:
        """Execute native scan"""
        if not isinstance(options, DirbScanOptions):
            options = DirbScanOptions(target=options.target)
        
        return await self.scan(options.target, options, progress_callback)
        start_time = datetime.now()
        
        logger.info(f"Starting Dirb scan: {scan_id}")
        
        if progress_callback:
            await progress_callback(0.1, "Initializing", "Preparing Dirb scan")
        
        try:
            # Build command
            command = self._build_command(target, options)
            
            if progress_callback:
                await progress_callback(0.2, "Scanning", "Running directory bruteforce")
            
            # Execute scan
            process_result = await self._execute_command(command, progress_callback)
            
            if progress_callback:
                await progress_callback(0.8, "Processing", "Parsing scan results")
            
            # Parse results
            parsed_results = self._parse_output(process_result["stdout"], target)
            
            if progress_callback:
                await progress_callback(1.0, "Complete", "Dirb scan completed")
            
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            return ScanResult(
                scan_id=scan_id,
                tool_name="dirb",
                target=target,
                status=ToolStatus.COMPLETED,
                start_time=start_time,
                end_time=end_time,
                duration_seconds=duration,
                command_executed=" ".join(command),
                exit_code=process_result["exit_code"],
                raw_output=process_result["stdout"],
                error_output=process_result["stderr"],
                parsed_results=parsed_results,
                vulnerabilities=parsed_results.get("vulnerabilities", []),
                summary=parsed_results.get("summary", {}),
                metadata={
                    "dirb_version": self._get_version(),
                    "wordlist": options.wordlist,
                    "scan_options": {
                        "extensions": options.extensions,
                        "recursive": options.recursive,
                        "max_threads": options.max_threads,
                        "case_insensitive": options.case_insensitive
                    }
                }
            )
            
        except Exception as e:
            logger.error(f"Dirb scan failed: {e}")
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            return ScanResult(
                scan_id=scan_id,
                tool_name="dirb",
                target=target,
                status=ToolStatus.FAILED,
                start_time=start_time,
                end_time=end_time,
                duration_seconds=duration,
                command_executed="",
                exit_code=1,
                raw_output="",
                error_output=str(e),
                parsed_results={},
                vulnerabilities=[],
                summary={"error": str(e)},
                metadata={}
            )
    
    def _build_command(self, target: str, options: DirbScanOptions) -> List[str]:
        """Build Dirb command"""
        command = ["dirb", target]
        
        # Wordlist
        if options.wordlist:
            command.append(options.wordlist)
        
        # Extensions
        if options.extensions:
            command.extend(["-X", ",".join(options.extensions)])
        
        # User agent
        if options.user_agent:
            command.extend(["-a", options.user_agent])
        
        # Authentication
        if options.username and options.password:
            command.extend(["-u", f"{options.username}:{options.password}"])
        
        # Proxy
        if options.proxy:
            command.extend(["-p", options.proxy])
        
        # Cookie
        if options.cookie:
            command.extend(["-c", options.cookie])
        
        # Delay
        if options.delay > 0:
            command.extend(["-z", str(options.delay)])
        
        # Case insensitive
        if options.case_insensitive:
            command.append("-i")
        
        # Silent mode
        if options.silent:
            command.append("-S")
        
        # Fine-tuning options
        command.extend(["-f"])  # Fine tunning of NOT_FOUND detection
        command.extend(["-l"])  # Print location header when found
        command.extend(["-r"])  # Don't search recursively
        
        return command
    
    async def _execute_command(self, command: List[str], progress_callback: Optional[Callable] = None) -> Dict[str, Any]:
        """Execute Dirb command"""
        try:
            process = await asyncio.create_subprocess_exec(
                *command,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            self.current_process = process
            
            # Monitor progress
            if progress_callback:
                monitor_task = asyncio.create_task(
                    self._monitor_progress(process, progress_callback)
                )
            
            stdout, stderr = await process.communicate()
            
            if progress_callback:
                monitor_task.cancel()
            
            return {
                "exit_code": process.returncode,
                "stdout": stdout.decode('utf-8', errors='ignore'),
                "stderr": stderr.decode('utf-8', errors='ignore')
            }
            
        except Exception as e:
            logger.error(f"Command execution failed: {e}")
            return {
                "exit_code": 1,
                "stdout": "",
                "stderr": str(e)
            }
        finally:
            self.current_process = None
    
    async def _monitor_progress(self, process, progress_callback):
        """Monitor scan progress"""
        try:
            progress = 0.2
            while process.returncode is None:
                await asyncio.sleep(3)
                progress = min(0.7, progress + 0.05)
                await progress_callback(progress, "Scanning", "Directory bruteforce in progress")
        except asyncio.CancelledError:
            pass
    
    def _parse_output(self, output: str, target: str) -> Dict[str, Any]:
        """Parse Dirb output"""
        parsed = {
            "discovered_paths": [],
            "vulnerabilities": [],
            "summary": {
                "total_requests": 0,
                "paths_found": 0,
                "target": target,
                "scan_time": datetime.now().isoformat()
            },
            "statistics": {
                "status_codes": {},
                "file_extensions": {},
                "directory_count": 0,
                "file_count": 0
            }
        }
        
        lines = output.split('\n')
        discovered_paths = []
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
            
            # Parse discovered URLs
            if line.startswith('==> DIRECTORY:') or line.startswith('+ '):
                path_info = self._parse_path_line(line, target)
                if path_info:
                    discovered_paths.append(path_info)
            
            # Parse statistics
            elif 'Total words:' in line:
                words_match = re.search(r'Total words: (\d+)', line)
                if words_match:
                    parsed['summary']['total_requests'] = int(words_match.group(1))
        
        # Process discovered paths
        parsed['discovered_paths'] = discovered_paths
        parsed['summary']['paths_found'] = len(discovered_paths)
        
        # Generate statistics
        self._generate_statistics(parsed, discovered_paths)
        
        # Generate vulnerabilities from interesting findings
        parsed['vulnerabilities'] = self._generate_vulnerabilities(discovered_paths, target)
        
        return parsed
    
    def _parse_path_line(self, line: str, target: str) -> Optional[Dict[str, Any]]:
        """Parse individual path discovery line"""
        try:
            if line.startswith('==> DIRECTORY:'):
                # Directory discovery
                url = line.replace('==> DIRECTORY:', '').strip()
                return {
                    "url": url,
                    "type": "directory",
                    "status_code": 200,
                    "size": 0,
                    "interesting": True
                }
            
            elif line.startswith('+ '):
                # File discovery with details
                # Format: + http://example.com/path (CODE:200|SIZE:1234)
                match = re.match(r'\+ (.+?) \(CODE:(\d+)\|SIZE:(\d+)\)', line)
                if match:
                    url = match.group(1)
                    status_code = int(match.group(2))
                    size = int(match.group(3))
                    
                    return {
                        "url": url,
                        "type": "file",
                        "status_code": status_code,
                        "size": size,
                        "interesting": status_code == 200
                    }
            
            return None
            
        except Exception as e:
            logger.warning(f"Failed to parse path line: {line} - {e}")
            return None
    
    def _generate_statistics(self, parsed: Dict[str, Any], discovered_paths: List[Dict[str, Any]]):
        """Generate statistics from discovered paths"""
        status_codes = {}
        file_extensions = {}
        directory_count = 0
        file_count = 0
        
        for path in discovered_paths:
            # Status codes
            status_code = path.get('status_code', 200)
            status_codes[status_code] = status_codes.get(status_code, 0) + 1
            
            # File extensions
            if path.get('type') == 'file':
                file_count += 1
                url = path.get('url', '')
                if '.' in url:
                    ext = url.split('.')[-1].lower()
                    file_extensions[ext] = file_extensions.get(ext, 0) + 1
            else:
                directory_count += 1
        
        parsed['statistics'] = {
            "status_codes": status_codes,
            "file_extensions": file_extensions,
            "directory_count": directory_count,
            "file_count": file_count
        }
    
    def _generate_vulnerabilities(self, discovered_paths: List[Dict[str, Any]], target: str) -> List[Dict[str, Any]]:
        """Generate vulnerability findings from discovered paths"""
        vulnerabilities = []
        
        for path in discovered_paths:
            url = path.get('url', '')
            path_lower = url.lower()
            
            # Check for interesting/sensitive files
            if any(keyword in path_lower for keyword in [
                'admin', 'config', 'backup', 'test', 'dev', 'debug',
                'log', 'tmp', 'temp', 'old', 'bak', 'phpinfo'
            ]):
                vulnerabilities.append({
                    "id": f"dirb_sensitive_{len(vulnerabilities)}",
                    "type": "Information Disclosure",
                    "severity": "medium",
                    "url": url,
                    "description": f"Potentially sensitive path discovered: {url}",
                    "recommendation": "Review if this path should be publicly accessible",
                    "status_code": path.get('status_code', 200)
                })
            
            # Check for common web application files
            if any(ext in path_lower for ext in ['.php', '.asp', '.jsp', '.py', '.pl']):
                if any(keyword in path_lower for keyword in ['test', 'debug', 'info']):
                    vulnerabilities.append({
                        "id": f"dirb_webapp_{len(vulnerabilities)}",
                        "type": "Web Application Exposure",
                        "severity": "low",
                        "url": url,
                        "description": f"Web application file discovered: {url}",
                        "recommendation": "Ensure this file doesn't expose sensitive information",
                        "status_code": path.get('status_code', 200)
                    })
        
        return vulnerabilities
    
    async def stop_scan(self) -> bool:
        """Stop current scan"""
        if self.current_process:
            try:
                self.current_process.terminate()
                await asyncio.sleep(2)
                if self.current_process.returncode is None:
                    self.current_process.kill()
                return True
            except Exception as e:
                logger.error(f"Failed to stop scan: {e}")
                return False
        return True
    
    def get_scan_profiles(self) -> Dict[str, Dict[str, Any]]:
        """Get predefined scan profiles"""
        return {
            "directory": {
                "name": "Directory Scan",
                "description": "Discover directories only",
                "options": {
                    "recursive": False,
                    "extensions": []
                }
            },
            "file": {
                "name": "File Discovery",
                "description": "Discover files with common extensions",
                "options": {
                    "extensions": ["php", "html", "txt", "js", "css", "xml", "json"]
                }
            },
            "recursive": {
                "name": "Recursive Scan",
                "description": "Deep recursive directory discovery",
                "options": {
                    "recursive": True,
                    "extensions": ["php", "html", "txt", "asp", "jsp"]
                }
            }
        }
    
    def get_frontend_interface_data(self) -> Dict[str, Any]:
        """Get data for frontend interface"""
        return {
            "tool_info": {
                "name": "Dirb Directory Scanner",
                "description": "Directory and file bruteforcer",
                "version": self._get_version(),
                "category": "Web Scanner",
                "status": "available" if self.is_available() else "unavailable"
            },
            "scan_options": {
                "target": {
                    "type": "url",
                    "required": True,
                    "placeholder": "https://example.com",
                    "validation": "url"
                },
                "wordlist": {
                    "type": "select",
                    "options": [
                        "/usr/share/dirb/wordlists/common.txt",
                        "/usr/share/dirb/wordlists/big.txt",
                        "/usr/share/dirb/wordlists/small.txt"
                    ],
                    "default": "/usr/share/dirb/wordlists/common.txt"
                },
                "extensions": {
                    "type": "multiselect",
                    "options": ["php", "html", "txt", "js", "css", "xml", "json", "asp", "jsp"],
                    "default": []
                },
                "recursive": {
                    "type": "boolean",
                    "default": False,
                    "label": "Recursive scan"
                },
                "case_insensitive": {
                    "type": "boolean",
                    "default": False,
                    "label": "Case insensitive"
                },
                "delay": {
                    "type": "number",
                    "default": 0,
                    "min": 0,
                    "max": 10,
                    "label": "Delay between requests (seconds)"
                }
            },
            "scan_profiles": self.get_scan_profiles(),
            "output_formats": ["txt", "json"],
            "capabilities": [
                "Directory enumeration",
                "File discovery",
                "Custom wordlists",
                "Extension-based scanning",
                "Recursive scanning",
                "Authentication support"
            ]
        }


# Register the tool
register_tool(DirbScanner)