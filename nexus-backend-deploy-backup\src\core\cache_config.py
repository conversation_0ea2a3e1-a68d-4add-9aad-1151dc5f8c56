#!/usr/bin/env python3
"""
Cache Configuration and Integration Module
Centralized configuration and setup for all caching components
"""

import os
import json
import logging
from typing import Dict, Any, Optional
from dataclasses import dataclass, asdict

from core.cache import <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>trate<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>
from security.cache_strategies import SecurityCacheManager
from ai.cache_optimization import AI<PERSON>acheOptimizer
from core.performance_cache import PerformanceCacheManager, PerformanceCacheType

logger = logging.getLogger(__name__)

@dataclass
class CacheConfiguration:
    """Centralized cache configuration"""
    
    # Global cache settings
    enabled: bool = True
    default_ttl: int = 3600  # 1 hour
    
    # Memory cache settings
    memory_cache_enabled: bool = True
    memory_max_size: int = 1000
    memory_max_mb: int = 100
    memory_strategy: str = "adaptive"
    
    # Redis cache settings
    redis_cache_enabled: bool = True
    redis_url: str = "redis://localhost:6379"
    redis_prefix: str = "nexusscan:"
    redis_max_connections: int = 10
    
    # Hybrid cache settings
    hybrid_cache_enabled: bool = True
    write_through: bool = True
    read_through: bool = True
    
    # Security cache settings
    security_cache_enabled: bool = True
    scan_result_ttl: int = 3600
    vulnerability_ttl: int = 86400
    ai_payload_ttl: int = 7200
    target_profile_ttl: int = 43200
    
    # AI cache optimization settings
    ai_cache_optimization_enabled: bool = True
    similarity_threshold: float = 0.8
    cost_savings_target: float = 0.3
    cache_warming_enabled: bool = True
    
    # Performance cache settings
    performance_cache_enabled: bool = True
    tiered_cache_enabled: bool = True
    hot_cache_size: int = 100
    warm_cache_size: int = 500
    cold_cache_size: int = 2000
    compression_enabled: bool = True
    prefetching_enabled: bool = True
    
    @classmethod
    def from_dict(cls, config_dict: Dict[str, Any]) -> 'CacheConfiguration':
        """Create configuration from dictionary"""
        return cls(**config_dict)
    
    @classmethod
    def from_env(cls) -> 'CacheConfiguration':
        """Create configuration from environment variables"""
        config = cls()
        
        # Global settings
        config.enabled = os.getenv('CACHE_ENABLED', 'true').lower() == 'true'
        config.default_ttl = int(os.getenv('CACHE_DEFAULT_TTL', '3600'))
        
        # Memory cache
        config.memory_cache_enabled = os.getenv('MEMORY_CACHE_ENABLED', 'true').lower() == 'true'
        config.memory_max_size = int(os.getenv('MEMORY_CACHE_MAX_SIZE', '1000'))
        config.memory_max_mb = int(os.getenv('MEMORY_CACHE_MAX_MB', '100'))
        config.memory_strategy = os.getenv('MEMORY_CACHE_STRATEGY', 'adaptive')
        
        # Redis cache
        config.redis_cache_enabled = os.getenv('REDIS_CACHE_ENABLED', 'true').lower() == 'true'
        config.redis_url = os.getenv('REDIS_URL', 'redis://localhost:6379')
        config.redis_prefix = os.getenv('REDIS_CACHE_PREFIX', 'nexusscan:')
        config.redis_max_connections = int(os.getenv('REDIS_MAX_CONNECTIONS', '10'))
        
        # Performance cache
        config.performance_cache_enabled = os.getenv('PERFORMANCE_CACHE_ENABLED', 'true').lower() == 'true'
        config.hot_cache_size = int(os.getenv('HOT_CACHE_SIZE', '100'))
        config.warm_cache_size = int(os.getenv('WARM_CACHE_SIZE', '500'))
        config.cold_cache_size = int(os.getenv('COLD_CACHE_SIZE', '2000'))
        
        return config
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return asdict(self)

class CacheSystem:
    """Centralized cache system manager"""
    
    def __init__(self, config: Optional[CacheConfiguration] = None):
        self.config = config or CacheConfiguration.from_env()
        
        # Cache managers
        self.base_cache_manager: Optional[CacheManager] = None
        self.security_cache_manager: Optional[SecurityCacheManager] = None
        self.ai_cache_optimizer: Optional[AICacheOptimizer] = None
        self.performance_cache_manager: Optional[PerformanceCacheManager] = None
        
        # Initialization status
        self.initialized = False
        self.redis_available = False
    
    async def initialize(self) -> bool:
        """Initialize all cache components"""
        if not self.config.enabled:
            logger.info("Caching disabled by configuration")
            return True
        
        try:
            # Initialize base cache manager
            await self._initialize_base_cache()
            
            # Initialize specialized cache managers
            if self.config.security_cache_enabled:
                await self._initialize_security_cache()
            
            if self.config.ai_cache_optimization_enabled:
                await self._initialize_ai_cache()
            
            if self.config.performance_cache_enabled:
                await self._initialize_performance_cache()
            
            self.initialized = True
            logger.info("Cache system initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize cache system: {e}")
            return False
    
    async def _initialize_base_cache(self):
        """Initialize base cache manager"""
        cache_config = {
            "type": "hybrid" if self.config.hybrid_cache_enabled else "memory",
            "memory": {
                "max_size": self.config.memory_max_size,
                "max_memory_mb": self.config.memory_max_mb,
                "strategy": getattr(CacheStrategy, self.config.memory_strategy.upper()),
                "default_ttl": self.config.default_ttl
            },
            "redis": {
                "redis_url": self.config.redis_url,
                "prefix": self.config.redis_prefix,
                "default_ttl": self.config.default_ttl,
                "max_connections": self.config.redis_max_connections
            },
            "auto_optimize": True
        }
        
        self.base_cache_manager = CacheManager(cache_config)
        
        # Try to connect to Redis if enabled
        if self.config.redis_cache_enabled:
            try:
                self.redis_available = await self.base_cache_manager.connect()
                if self.redis_available:
                    logger.info("Connected to Redis cache")
                else:
                    logger.warning("Redis cache not available, using memory-only caching")
            except Exception as e:
                logger.warning(f"Redis connection failed: {e}")
                self.redis_available = False
    
    async def _initialize_security_cache(self):
        """Initialize security cache manager"""
        self.security_cache_manager = SecurityCacheManager(self.base_cache_manager)
        
        # Update TTL configurations
        self.security_cache_manager.ttl_config.update({
            'scan_results': self.config.scan_result_ttl,
            'vulnerability_data': self.config.vulnerability_ttl,
            'ai_payloads': self.config.ai_payload_ttl,
            'target_profiles': self.config.target_profile_ttl
        })
        
        logger.debug("Security cache manager initialized")
    
    async def _initialize_ai_cache(self):
        """Initialize AI cache optimizer"""
        self.ai_cache_optimizer = AICacheOptimizer(self.base_cache_manager)
        
        # Update configuration
        self.ai_cache_optimizer.similarity_threshold = self.config.similarity_threshold
        self.ai_cache_optimizer.cost_savings_target = self.config.cost_savings_target
        self.ai_cache_optimizer.warm_cache_enabled = self.config.cache_warming_enabled
        
        logger.debug("AI cache optimizer initialized")
    
    async def _initialize_performance_cache(self):
        """Initialize performance cache manager"""
        tiered_config = {
            "hot_cache_size": self.config.hot_cache_size,
            "warm_cache_size": self.config.warm_cache_size,
            "cold_cache_size": self.config.cold_cache_size,
            "enable_compression": self.config.compression_enabled,
            "enable_prefetching": self.config.prefetching_enabled
        }
        
        self.performance_cache_manager = PerformanceCacheManager(
            base_cache_manager=self.base_cache_manager,
            enable_tiered_cache=self.config.tiered_cache_enabled,
            tiered_cache_config=tiered_config
        )
        
        logger.debug("Performance cache manager initialized")
    
    async def shutdown(self):
        """Shutdown cache system"""
        if self.base_cache_manager:
            await self.base_cache_manager.disconnect()
        
        logger.info("Cache system shutdown completed")
    
    def get_cache_manager(self) -> Optional[CacheManager]:
        """Get base cache manager"""
        return self.base_cache_manager
    
    def get_security_cache(self) -> Optional[SecurityCacheManager]:
        """Get security cache manager"""
        return self.security_cache_manager
    
    def get_ai_cache_optimizer(self) -> Optional[AICacheOptimizer]:
        """Get AI cache optimizer"""
        return self.ai_cache_optimizer
    
    def get_performance_cache(self) -> Optional[PerformanceCacheManager]:
        """Get performance cache manager"""
        return self.performance_cache_manager
    
    def get_system_stats(self) -> Dict[str, Any]:
        """Get comprehensive cache system statistics"""
        stats = {
            "initialized": self.initialized,
            "redis_available": self.redis_available,
            "configuration": self.config.to_dict()
        }
        
        if self.base_cache_manager:
            if hasattr(self.base_cache_manager.cache, 'get_stats'):
                stats["base_cache"] = self.base_cache_manager.cache.get_stats()
        
        if self.security_cache_manager:
            stats["security_cache"] = self.security_cache_manager.get_cache_stats()
        
        if self.ai_cache_optimizer:
            stats["ai_cache"] = self.ai_cache_optimizer.get_cache_stats()
        
        if self.performance_cache_manager:
            stats["performance_cache"] = self.performance_cache_manager.get_stats()
        
        return stats
    
    async def clear_all_caches(self):
        """Clear all cache layers"""
        if not self.initialized:
            return
        
        if self.base_cache_manager:
            await self.base_cache_manager.clear()
        
        logger.info("All caches cleared")
    
    async def warm_cache(self):
        """Warm up caches with frequently accessed data"""
        if not self.initialized:
            return
        
        # This would implement cache warming logic
        # For now, it's a placeholder
        logger.info("Cache warming completed")

# Global cache system instance
cache_system: Optional[CacheSystem] = None

async def initialize_cache_system(config: Optional[CacheConfiguration] = None) -> CacheSystem:
    """Initialize global cache system"""
    global cache_system
    
    if cache_system is None:
        cache_system = CacheSystem(config)
        await cache_system.initialize()
    
    return cache_system

def get_cache_system() -> Optional[CacheSystem]:
    """Get global cache system instance"""
    return cache_system

async def shutdown_cache_system():
    """Shutdown global cache system"""
    global cache_system
    
    if cache_system:
        await cache_system.shutdown()
        cache_system = None

# Convenience functions for accessing cache managers
def get_base_cache() -> Optional[CacheManager]:
    """Get base cache manager"""
    return cache_system.get_cache_manager() if cache_system else None

def get_security_cache() -> Optional[SecurityCacheManager]:
    """Get security cache manager"""
    return cache_system.get_security_cache() if cache_system else None

def get_ai_cache() -> Optional[AICacheOptimizer]:
    """Get AI cache optimizer"""
    return cache_system.get_ai_cache_optimizer() if cache_system else None

def get_performance_cache() -> Optional[PerformanceCacheManager]:
    """Get performance cache manager"""
    return cache_system.get_performance_cache() if cache_system else None