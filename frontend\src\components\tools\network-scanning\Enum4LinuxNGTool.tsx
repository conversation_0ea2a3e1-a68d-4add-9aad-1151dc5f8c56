/**
 * enum4linux-ng Tool Component
 * Modern SMB enumeration tool for comprehensive Windows network analysis
 */
import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { 
  Search,
  Play,
  Square,
  Download,
  Settings,
  Target,
  Users,
  Shield,
  Database,
  Terminal,
  FileText,
  Copy,
  RotateCcw,
  AlertTriangle,
  Info,
  Lock,
  Network,
  Activity,
  Eye,
  UserCheck,
  Share,
  Key,
  Clock,
  CheckCircle,
  Folder,
  Server,
  Zap
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useBackendStore } from '@/stores/backend-store';
import { apiClient, ToolExecution } from '@/services/api-client';
import { toast } from 'sonner';

/**
 * enum4linux-ng configuration interface
 */
interface Enum4LinuxNGConfig {
  target: string;
  modules: {
    shares: boolean;
    users: boolean;
    groups: boolean;
    policies: boolean;
    printers: boolean;
    machines: boolean;
    osinfo: boolean;
    ridCycling: boolean;
  };
  authentication: {
    username: string;
    password: string;
    domain: string;
    useNullSession: boolean;
    hash: string;
  };
  ridCycling: {
    enabled: boolean;
    startRid: number;
    endRid: number;
    step: number;
  };
  options: {
    timeout: number;
    threads: number;
    verbose: boolean;
    jsonOutput: boolean;
    fullOutput: boolean;
    keepLogging: boolean;
  };
  customArgs: string;
}

/**
 * Enumeration modules and their descriptions
 */
const ENUM_MODULES = {
  shares: {
    name: 'Share Enumeration',
    description: 'Enumerate SMB shares and their permissions',
    icon: <Share className="h-4 w-4" />,
    risk: 'low'
  },
  users: {
    name: 'User Enumeration',
    description: 'Discover user accounts and detailed information',
    icon: <Users className="h-4 w-4" />,
    risk: 'medium'
  },
  groups: {
    name: 'Group Enumeration',
    description: 'Enumerate security groups and memberships',
    icon: <UserCheck className="h-4 w-4" />,
    risk: 'medium'
  },
  policies: {
    name: 'Password Policies',
    description: 'Extract password and account policies',
    icon: <Shield className="h-4 w-4" />,
    risk: 'low'
  },
  printers: {
    name: 'Printer Enumeration',
    description: 'Discover shared printers and print queues',
    icon: <Activity className="h-4 w-4" />,
    risk: 'low'
  },
  machines: {
    name: 'Machine Accounts',
    description: 'Enumerate domain machine accounts',
    icon: <Server className="h-4 w-4" />,
    risk: 'medium'
  },
  osinfo: {
    name: 'OS Information',
    description: 'Gather operating system and version details',
    icon: <Info className="h-4 w-4" />,
    risk: 'low'
  },
  ridCycling: {
    name: 'RID Cycling',
    description: 'Bruteforce RIDs to discover users and groups',
    icon: <Search className="h-4 w-4" />,
    risk: 'high'
  }
};

/**
 * Common RID ranges for different object types
 */
const RID_PRESETS = {
  'users-standard': { start: 1000, end: 1100, description: 'Standard user accounts (1000-1100)' },
  'users-extended': { start: 1000, end: 2000, description: 'Extended user range (1000-2000)' },
  'builtin-groups': { start: 500, end: 600, description: 'Built-in groups (500-600)' },
  'domain-groups': { start: 1100, end: 1200, description: 'Domain groups (1100-1200)' },
  'comprehensive': { start: 500, end: 5000, description: 'Comprehensive scan (500-5000)' },
  'fast-scan': { start: 1000, end: 1050, description: 'Quick user scan (1000-1050)' }
};

/**
 * Risk level styling
 */
const getRiskColor = (risk: string) => {
  switch (risk) {
    case 'low': return 'text-green-600 bg-green-100 dark:bg-green-900/20';
    case 'medium': return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900/20';
    case 'high': return 'text-red-600 bg-red-100 dark:bg-red-900/20';
    default: return 'text-gray-600 bg-gray-100 dark:bg-gray-900/20';
  }
};

/**
 * enum4linux-ng Tool Component
 */
export function Enum4LinuxNGTool() {
  // State management
  const [config, setConfig] = React.useState<Enum4LinuxNGConfig>({
    target: '',
    modules: {
      shares: true,
      users: true,
      groups: true,
      policies: true,
      printers: false,
      machines: false,
      osinfo: true,
      ridCycling: false
    },
    authentication: {
      username: '',
      password: '',
      domain: '',
      useNullSession: true,
      hash: ''
    },
    ridCycling: {
      enabled: false,
      startRid: 1000,
      endRid: 1100,
      step: 1
    },
    options: {
      timeout: 5,
      threads: 4,
      verbose: false,
      jsonOutput: true,
      fullOutput: false,
      keepLogging: false
    },
    customArgs: ''
  });
  
  const [isEnumerating, setIsEnumerating] = React.useState(false);
  const [currentExecution, setCurrentExecution] = React.useState<ToolExecution | null>(null);
  const [progress, setProgress] = React.useState(0);
  const [output, setOutput] = React.useState<string[]>([]);
  const [results, setResults] = React.useState<any>(null);
  const [activeTab, setActiveTab] = React.useState('configure');
  const [enumerationStats, setEnumerationStats] = React.useState<any>(null);
  
  // Backend connection
  const { status: backendStatus } = useBackendStore();
  
  // Update configuration
  const updateConfig = (updates: Partial<Enum4LinuxNGConfig>) => {
    setConfig(prev => ({ ...prev, ...updates }));
  };
  
  // Update modules
  const updateModules = (updates: Partial<Enum4LinuxNGConfig['modules']>) => {
    setConfig(prev => ({
      ...prev,
      modules: { ...prev.modules, ...updates }
    }));
  };
  
  // Update authentication
  const updateAuth = (updates: Partial<Enum4LinuxNGConfig['authentication']>) => {
    setConfig(prev => ({
      ...prev,
      authentication: { ...prev.authentication, ...updates }
    }));
  };
  
  // Update RID cycling
  const updateRidCycling = (updates: Partial<Enum4LinuxNGConfig['ridCycling']>) => {
    setConfig(prev => ({
      ...prev,
      ridCycling: { ...prev.ridCycling, ...updates }
    }));
    
    // Enable RID cycling module if RID cycling is enabled
    if (updates.enabled) {
      updateModules({ ridCycling: true });
    }
  };
  
  // Update options
  const updateOptions = (updates: Partial<Enum4LinuxNGConfig['options']>) => {
    setConfig(prev => ({
      ...prev,
      options: { ...prev.options, ...updates }
    }));
  };
  
  // Apply RID preset
  const applyRidPreset = (presetKey: string) => {
    const preset = RID_PRESETS[presetKey as keyof typeof RID_PRESETS];
    if (preset) {
      updateRidCycling({
        startRid: preset.start,
        endRid: preset.end,
        enabled: true
      });
      toast.success(`Applied RID preset: ${preset.description}`);
    }
  };
  
  // Apply quick configuration presets
  const applyQuickPreset = (preset: string) => {
    switch (preset) {
      case 'basic':
        updateModules({
          shares: true,
          users: true,
          groups: false,
          policies: true,
          printers: false,
          machines: false,
          osinfo: true,
          ridCycling: false
        });
        break;
      case 'comprehensive':
        updateModules({
          shares: true,
          users: true,
          groups: true,
          policies: true,
          printers: true,
          machines: true,
          osinfo: true,
          ridCycling: false
        });
        break;
      case 'stealth':
        updateModules({
          shares: true,
          users: false,
          groups: false,
          policies: false,
          printers: false,
          machines: false,
          osinfo: true,
          ridCycling: false
        });
        updateOptions({ threads: 1, timeout: 10 });
        break;
      case 'aggressive':
        updateModules({
          shares: true,
          users: true,
          groups: true,
          policies: true,
          printers: true,
          machines: true,
          osinfo: true,
          ridCycling: true
        });
        updateRidCycling({ enabled: true, startRid: 500, endRid: 2000 });
        break;
    }
    toast.success(`Applied ${preset} configuration`);
  };
  
  // Generate enum4linux-ng command
  const generateCommand = (): string => {
    let command = 'enum4linux-ng';
    
    // Modules
    const enabledModules = Object.entries(config.modules)
      .filter(([_, enabled]) => enabled)
      .map(([module]) => module);
    
    if (enabledModules.length > 0) {
      command += ` -A`; // All modules, then we'll specify exclusions if needed
    }
    
    // Authentication
    if (!config.authentication.useNullSession && config.authentication.username) {
      command += ` -u ${config.authentication.username}`;
      if (config.authentication.password) {
        command += ` -p ${config.authentication.password}`;
      }
      if (config.authentication.domain) {
        command += ` -d ${config.authentication.domain}`;
      }
      if (config.authentication.hash) {
        command += ` -H ${config.authentication.hash}`;
      }
    }
    
    // RID cycling
    if (config.modules.ridCycling && config.ridCycling.enabled) {
      command += ` -r ${config.ridCycling.startRid}-${config.ridCycling.endRid}`;
    }
    
    // Options
    command += ` -t ${config.options.timeout}`;
    command += ` -th ${config.options.threads}`;
    
    if (config.options.verbose) {
      command += ` -v`;
    }
    
    if (config.options.jsonOutput) {
      command += ` -oJ enum4linux-ng.json`;
    }
    
    if (config.options.fullOutput) {
      command += ` -oA enum4linux-ng`;
    }
    
    // Custom arguments
    if (config.customArgs.trim()) {
      command += ` ${config.customArgs.trim()}`;
    }
    
    // Target
    command += ` ${config.target}`;
    
    return command;
  };
  
  // Start enumeration
  const startEnumeration = async () => {
    if (!config.target.trim()) {
      toast.error('Please specify a target host');
      return;
    }
    
    const enabledModules = Object.values(config.modules).filter(Boolean).length;
    if (enabledModules === 0) {
      toast.error('Please select at least one enumeration module');
      return;
    }
    
    if (!backendStatus.connected) {
      toast.error('Backend not connected');
      return;
    }
    
    try {
      setIsEnumerating(true);
      setProgress(0);
      setOutput([]);
      setResults(null);
      setEnumerationStats(null);
      setActiveTab('output');
      
      // Add warning about detection issues
      toast.info('Note: Tool functional but may show detection issues in backend registry', {
        duration: 5000
      });
      
      const execution = await apiClient.executeTool('enum4linux-ng', config, (progressUpdate) => {
        setCurrentExecution(progressUpdate);
        setProgress(progressUpdate.progress);
        if (progressUpdate.output) {
          setOutput(prev => [...prev, ...progressUpdate.output!]);
        }
        
        if (progressUpdate.status === 'completed') {
          setIsEnumerating(false);
          setResults(progressUpdate.results);
          
          // Parse enumeration statistics
          if (progressUpdate.results) {
            const stats = {
              users: progressUpdate.results.users?.length || 0,
              groups: progressUpdate.results.groups?.length || 0,
              shares: progressUpdate.results.shares?.length || 0,
              policies: progressUpdate.results.policies ? 1 : 0
            };
            setEnumerationStats(stats);
          }
          
          toast.success('enum4linux-ng enumeration completed');
        } else if (progressUpdate.status === 'failed') {
          setIsEnumerating(false);
          toast.error(`Enumeration failed: ${progressUpdate.error}`);
        }
      });
      
      setCurrentExecution(execution);
      toast.info('enum4linux-ng enumeration started');
    } catch (error) {
      setIsEnumerating(false);
      console.error('Failed to start enum4linux-ng:', error);
      toast.error('Failed to start enumeration');
    }
  };
  
  // Stop enumeration
  const stopEnumeration = async () => {
    if (currentExecution) {
      try {
        await apiClient.cancelExecution(currentExecution.id);
        setIsEnumerating(false);
        setCurrentExecution(null);
        toast.info('Enumeration cancelled');
      } catch (error) {
        console.error('Failed to stop enumeration:', error);
        toast.error('Failed to stop enumeration');
      }
    }
  };
  
  // Copy command to clipboard
  const copyCommand = async () => {
    try {
      await navigator.clipboard.writeText(generateCommand());
      toast.success('Command copied to clipboard');
    } catch (error) {
      toast.error('Failed to copy command');
    }
  };
  
  // Export results
  const exportResults = () => {
    if (!results && output.length === 0) {
      toast.error('No results to export');
      return;
    }
    
    const data = results || output.join('\n');
    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `enum4linux-ng-${config.target}-${new Date().toISOString().slice(0, 19)}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    
    toast.success('Results exported');
  };
  
  // Get estimated enumeration time
  const getEstimatedTime = (): string => {
    const enabledModules = Object.values(config.modules).filter(Boolean).length;
    const baseTime = enabledModules * 30; // 30 seconds per module
    const ridTime = config.modules.ridCycling ? (config.ridCycling.endRid - config.ridCycling.startRid) / 100 * 10 : 0;
    const totalSeconds = baseTime + ridTime;
    
    if (totalSeconds < 60) return `~${totalSeconds}s`;
    if (totalSeconds < 3600) return `~${Math.ceil(totalSeconds / 60)}m`;
    return `~${Math.ceil(totalSeconds / 3600)}h`;
  };
  
  return (
    <div className="container-desktop py-6 space-y-6">
      {/* Tool Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className="p-2 bg-purple-100 dark:bg-purple-900/20 rounded-lg">
            <Search className="h-6 w-6 text-purple-600 dark:text-purple-400" />
          </div>
          <div>
            <h1 className="text-2xl font-bold">enum4linux-ng</h1>
            <p className="text-muted-foreground">
              Modern SMB enumeration tool for comprehensive Windows network analysis
            </p>
          </div>
        </div>
        
        {/* Status indicators */}
        <div className="flex items-center gap-2">
          <Badge variant={backendStatus.connected ? 'default' : 'destructive'}>
            {backendStatus.connected ? 'Connected' : 'Offline'}
          </Badge>
          <Badge variant="secondary" className="flex items-center gap-1">
            <AlertTriangle className="h-3 w-3" />
            Detection Issues
          </Badge>
          {isEnumerating && (
            <Badge variant="secondary" className="animate-pulse">
              Enumerating...
            </Badge>
          )}
        </div>
      </div>
      
      {/* Detection Warning */}
      <Card className="border-yellow-200 bg-yellow-50 dark:border-yellow-800 dark:bg-yellow-900/10">
        <CardContent className="pt-6">
          <div className="flex items-start gap-3">
            <AlertTriangle className="h-5 w-5 text-yellow-600 dark:text-yellow-400 mt-0.5" />
            <div className="space-y-1">
              <p className="text-sm font-medium text-yellow-800 dark:text-yellow-200">
                Tool Status: Functional with Detection Issues
              </p>
              <p className="text-xs text-yellow-700 dark:text-yellow-300">
                enum4linux-ng is fully operational but may show platform detection issues in the backend registry. 
                All enumeration capabilities work correctly despite this cosmetic issue.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
      
      {/* Main Interface */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="configure" className="flex items-center gap-2">
            <Settings className="h-4 w-4" />
            Configure
          </TabsTrigger>
          <TabsTrigger value="output" className="flex items-center gap-2">
            <Terminal className="h-4 w-4" />
            Output
          </TabsTrigger>
          <TabsTrigger value="results" className="flex items-center gap-2">
            <FileText className="h-4 w-4" />
            Results
          </TabsTrigger>
          <TabsTrigger value="command" className="flex items-center gap-2">
            <Target className="h-4 w-4" />
            Command
          </TabsTrigger>
        </TabsList>
        
        {/* Configuration Tab */}
        <TabsContent value="configure" className="space-y-6">
          {/* Quick Presets */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Zap className="h-5 w-5" />
                Quick Configuration Presets
              </CardTitle>
              <CardDescription>
                Use predefined configurations for common enumeration scenarios
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-3 md:grid-cols-4">
                <Button
                  variant="outline"
                  onClick={() => applyQuickPreset('basic')}
                  className="flex flex-col h-auto p-4 text-left"
                >
                  <div className="font-medium text-sm">Basic Scan</div>
                  <div className="text-xs text-muted-foreground">
                    Shares, users, policies, OS info
                  </div>
                </Button>
                
                <Button
                  variant="outline"
                  onClick={() => applyQuickPreset('comprehensive')}
                  className="flex flex-col h-auto p-4 text-left"
                >
                  <div className="font-medium text-sm">Comprehensive</div>
                  <div className="text-xs text-muted-foreground">
                    All modules except RID cycling
                  </div>
                </Button>
                
                <Button
                  variant="outline"
                  onClick={() => applyQuickPreset('stealth')}
                  className="flex flex-col h-auto p-4 text-left"
                >
                  <div className="font-medium text-sm">Stealth Mode</div>
                  <div className="text-xs text-muted-foreground">
                    Minimal detection footprint
                  </div>
                </Button>
                
                <Button
                  variant="outline"
                  onClick={() => applyQuickPreset('aggressive')}
                  className="flex flex-col h-auto p-4 text-left"
                >
                  <div className="font-medium text-sm">Aggressive</div>
                  <div className="text-xs text-muted-foreground">
                    All modules including RID cycling
                  </div>
                </Button>
              </div>
            </CardContent>
          </Card>
          
          <div className="grid gap-6 lg:grid-cols-2">
            {/* Target Configuration */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Target className="h-5 w-5" />
                  Target Configuration
                </CardTitle>
                <CardDescription>
                  Specify the target host for SMB enumeration
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Target */}
                <div className="space-y-2">
                  <label className="text-sm font-medium">Target Host</label>
                  <Input
                    placeholder="e.g., *************, dc01.domain.local"
                    value={config.target}
                    onChange={(e) => updateConfig({ target: e.target.value })}
                  />
                  <p className="text-xs text-muted-foreground">
                    IP address or hostname of the Windows target
                  </p>
                </div>
                
                {/* Estimated time */}
                <div className="flex items-center gap-2 p-3 bg-muted rounded-lg">
                  <Clock className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm">
                    Estimated enumeration time: <strong>{getEstimatedTime()}</strong>
                  </span>
                </div>
              </CardContent>
            </Card>
            
            {/* Authentication */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Lock className="h-5 w-5" />
                  Authentication
                </CardTitle>
                <CardDescription>
                  Configure authentication credentials for authenticated enumeration
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Null Session Option */}
                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    checked={config.authentication.useNullSession}
                    onChange={(e) => updateAuth({ useNullSession: e.target.checked })}
                    className="rounded border-gray-300"
                  />
                  <span className="text-sm">Use Null Session (Anonymous)</span>
                </div>
                
                {!config.authentication.useNullSession && (
                  <>
                    <div className="space-y-2">
                      <label className="text-sm font-medium">Username</label>
                      <Input
                        placeholder="Username"
                        value={config.authentication.username}
                        onChange={(e) => updateAuth({ username: e.target.value })}
                      />
                    </div>
                    
                    <div className="space-y-2">
                      <label className="text-sm font-medium">Password</label>
                      <Input
                        type="password"
                        placeholder="Password"
                        value={config.authentication.password}
                        onChange={(e) => updateAuth({ password: e.target.value })}
                      />
                    </div>
                    
                    <div className="space-y-2">
                      <label className="text-sm font-medium">Domain (Optional)</label>
                      <Input
                        placeholder="DOMAIN or WORKGROUP"
                        value={config.authentication.domain}
                        onChange={(e) => updateAuth({ domain: e.target.value })}
                      />
                    </div>
                    
                    <div className="space-y-2">
                      <label className="text-sm font-medium">NTLM Hash (Optional)</label>
                      <Input
                        placeholder="NTLM hash for pass-the-hash"
                        value={config.authentication.hash}
                        onChange={(e) => updateAuth({ hash: e.target.value })}
                      />
                    </div>
                  </>
                )}
              </CardContent>
            </Card>
          </div>
          
          {/* Enumeration Modules */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Database className="h-5 w-5" />
                Enumeration Modules
              </CardTitle>
              <CardDescription>
                Select which information types to enumerate from the target
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                {Object.entries(ENUM_MODULES).map(([key, module]) => (
                  <div
                    key={key}
                    className={cn(
                      'p-4 border rounded-lg cursor-pointer transition-all',
                      config.modules[key as keyof typeof config.modules]
                        ? 'border-primary bg-primary/5'
                        : 'border-border hover:border-primary/50'
                    )}
                    onClick={() => updateModules({ [key]: !config.modules[key as keyof typeof config.modules] })}
                  >
                    <div className="flex items-start gap-3">
                      <div className={cn(
                        'p-2 rounded-md',
                        getRiskColor(module.risk)
                      )}>
                        {module.icon}
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2 mb-1">
                          <span className="text-sm font-medium">{module.name}</span>
                          <Badge variant="outline" className="text-xs">
                            {module.risk}
                          </Badge>
                        </div>
                        <p className="text-xs text-muted-foreground">{module.description}</p>
                        <div className="mt-2">
                          <input
                            type="checkbox"
                            checked={config.modules[key as keyof typeof config.modules]}
                            onChange={(e) => updateModules({ [key]: e.target.checked })}
                            className="rounded border-gray-300"
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
          
          {/* RID Cycling Configuration */}
          {config.modules.ridCycling && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Search className="h-5 w-5" />
                  RID Cycling Configuration
                </CardTitle>
                <CardDescription>
                  Configure RID bruteforcing to discover users and groups
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid gap-4 md:grid-cols-3">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Start RID</label>
                    <Input
                      type="number"
                      min="500"
                      max="10000"
                      value={config.ridCycling.startRid}
                      onChange={(e) => updateRidCycling({ startRid: parseInt(e.target.value) || 1000 })}
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <label className="text-sm font-medium">End RID</label>
                    <Input
                      type="number"
                      min="500"
                      max="10000"
                      value={config.ridCycling.endRid}
                      onChange={(e) => updateRidCycling({ endRid: parseInt(e.target.value) || 1100 })}
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Step Size</label>
                    <Input
                      type="number"
                      min="1"
                      max="10"
                      value={config.ridCycling.step}
                      onChange={(e) => updateRidCycling({ step: parseInt(e.target.value) || 1 })}
                    />
                  </div>
                </div>
                
                <div>
                  <h4 className="text-sm font-medium mb-3">RID Range Presets</h4>
                  <div className="flex flex-wrap gap-2">
                    {Object.entries(RID_PRESETS).map(([key, preset]) => (
                      <Button
                        key={key}
                        variant="outline"
                        size="sm"
                        onClick={() => applyRidPreset(key)}
                      >
                        {preset.description}
                      </Button>
                    ))}
                  </div>
                </div>
                
                <div className="p-3 bg-orange-50 dark:bg-orange-900/10 rounded-lg border border-orange-200 dark:border-orange-800">
                  <div className="flex items-start gap-2">
                    <AlertTriangle className="h-4 w-4 text-orange-600 dark:text-orange-400 mt-0.5" />
                    <div className="text-sm text-orange-700 dark:text-orange-300">
                      <strong>Warning:</strong> RID cycling can be detected by security monitoring systems. 
                      Use with caution in production environments.
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
          
          {/* Options */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5" />
                Enumeration Options
              </CardTitle>
              <CardDescription>
                Advanced options for performance tuning and output formatting
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid gap-4 md:grid-cols-3">
                <div className="space-y-2">
                  <label className="text-sm font-medium">Timeout (seconds)</label>
                  <Input
                    type="number"
                    min="1"
                    max="60"
                    value={config.options.timeout}
                    onChange={(e) => updateOptions({ timeout: parseInt(e.target.value) || 5 })}
                  />
                </div>
                
                <div className="space-y-2">
                  <label className="text-sm font-medium">Threads</label>
                  <Input
                    type="number"
                    min="1"
                    max="20"
                    value={config.options.threads}
                    onChange={(e) => updateOptions({ threads: parseInt(e.target.value) || 4 })}
                  />
                </div>
                
                <div className="space-y-2">
                  <label className="text-sm font-medium">Custom Arguments</label>
                  <Input
                    placeholder="Additional arguments"
                    value={config.customArgs}
                    onChange={(e) => updateConfig({ customArgs: e.target.value })}
                  />
                </div>
              </div>
              
              <div className="grid gap-3 md:grid-cols-2">
                <label className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    checked={config.options.verbose}
                    onChange={(e) => updateOptions({ verbose: e.target.checked })}
                    className="rounded border-gray-300"
                  />
                  <span className="text-sm">Verbose Output</span>
                </label>
                
                <label className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    checked={config.options.jsonOutput}
                    onChange={(e) => updateOptions({ jsonOutput: e.target.checked })}
                    className="rounded border-gray-300"
                  />
                  <span className="text-sm">JSON Output</span>
                </label>
                
                <label className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    checked={config.options.fullOutput}
                    onChange={(e) => updateOptions({ fullOutput: e.target.checked })}
                    className="rounded border-gray-300"
                  />
                  <span className="text-sm">Full Output Files</span>
                </label>
                
                <label className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    checked={config.options.keepLogging}
                    onChange={(e) => updateOptions({ keepLogging: e.target.checked })}
                    className="rounded border-gray-300"
                  />
                  <span className="text-sm">Keep Detailed Logs</span>
                </label>
              </div>
            </CardContent>
          </Card>
          
          {/* Actions */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Activity className="h-5 w-5" />
                Start Enumeration
              </CardTitle>
              <CardDescription>
                Execute the enumeration with your configured parameters
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between">
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={copyCommand}
                  >
                    <Copy className="h-4 w-4 mr-1" />
                    Copy Command
                  </Button>
                </div>
                
                <div className="flex gap-2">
                  {isEnumerating ? (
                    <Button
                      variant="destructive"
                      onClick={stopEnumeration}
                    >
                      <Square className="h-4 w-4 mr-2" />
                      Stop Enumeration
                    </Button>
                  ) : (
                    <Button
                      onClick={startEnumeration}
                      disabled={!config.target.trim() || !backendStatus.connected}
                    >
                      <Play className="h-4 w-4 mr-2" />
                      Start Enumeration
                    </Button>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        {/* Output Tab */}
        <TabsContent value="output" className="space-y-6">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="flex items-center gap-2">
                    <Terminal className="h-5 w-5" />
                    Real-time Output
                  </CardTitle>
                  <CardDescription>
                    Live output from the enum4linux-ng enumeration process
                  </CardDescription>
                </div>
                
                {/* Progress and controls */}
                <div className="flex items-center gap-4">
                  {isEnumerating && (
                    <div className="flex items-center gap-2">
                      <Progress value={progress} className="w-32" />
                      <span className="text-sm text-muted-foreground">
                        {progress}%
                      </span>
                    </div>
                  )}
                  
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setOutput([])}
                    disabled={isEnumerating}
                  >
                    <RotateCcw className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="bg-gray-900 text-gray-100 p-4 rounded-lg h-96 overflow-y-auto font-mono text-sm">
                {output.length === 0 ? (
                  <div className="text-gray-500 italic">
                    {isEnumerating ? 'Waiting for output...' : 'No output yet. Start enumeration to see results here.'}
                  </div>
                ) : (
                  output.map((line, index) => (
                    <div key={index} className="mb-1">
                      {line}
                    </div>
                  ))
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        {/* Results Tab */}
        <TabsContent value="results" className="space-y-6">
          {/* Enumeration Statistics */}
          {enumerationStats && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Eye className="h-5 w-5" />
                  Enumeration Summary
                </CardTitle>
                <CardDescription>
                  Overview of discovered information from the target
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid gap-4 md:grid-cols-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-600">{enumerationStats.users}</div>
                    <div className="text-sm text-muted-foreground">Users</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-600">{enumerationStats.groups}</div>
                    <div className="text-sm text-muted-foreground">Groups</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-purple-600">{enumerationStats.shares}</div>
                    <div className="text-sm text-muted-foreground">Shares</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-orange-600">{enumerationStats.policies}</div>
                    <div className="text-sm text-muted-foreground">Policies</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
          
          {/* Detailed Results */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="flex items-center gap-2">
                    <FileText className="h-5 w-5" />
                    Detailed Results
                  </CardTitle>
                  <CardDescription>
                    Comprehensive enumeration results and discovered information
                  </CardDescription>
                </div>
                
                <Button
                  variant="outline"
                  onClick={exportResults}
                  disabled={!results && output.length === 0}
                >
                  <Download className="h-4 w-4 mr-2" />
                  Export Results
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              {results ? (
                <div className="space-y-4">
                  <pre className="bg-muted p-4 rounded-lg overflow-auto text-sm max-h-96">
                    {JSON.stringify(results, null, 2)}
                  </pre>
                </div>
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  <Search className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>No enumeration results available yet.</p>
                  <p className="text-sm">Complete an enumeration to see detailed results here.</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
        
        {/* Command Tab */}
        <TabsContent value="command" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Target className="h-5 w-5" />
                Generated Command
              </CardTitle>
              <CardDescription>
                The enum4linux-ng command that will be executed based on your configuration
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="bg-muted p-4 rounded-lg">
                  <code className="text-sm font-mono break-all">
                    {generateCommand()}
                  </code>
                </div>
                
                <div className="flex justify-between items-center">
                  <p className="text-sm text-muted-foreground">
                    This command will be executed on the backend server
                  </p>
                  
                  <Button variant="outline" onClick={copyCommand}>
                    <Copy className="h-4 w-4 mr-2" />
                    Copy Command
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}