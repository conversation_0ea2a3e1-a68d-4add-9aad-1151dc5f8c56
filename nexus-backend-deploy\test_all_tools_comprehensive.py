#!/usr/bin/env python3
"""
Comprehensive Testing of ALL Available Tools
Tests every tool discovered from v1 UnifiedToolManager
Goal: Reach 100% tools working perfectly
"""

import requests
import json
import time
from datetime import datetime
import asyncio
from concurrent.futures import ThreadPoolExecutor

# Configuration
BACKEND_URL = "http://ec2-3-89-91-209.compute-1.amazonaws.com:8000"
SIMPLE_TOOLS_URL = f"{BACKEND_URL}/api/simple-tools"
MAIN_TOOLS_URL = f"{BACKEND_URL}/api/tools"

def test_tool_execution(tool_name, target="scanme.nmap.org", scan_type="basic", timeout=120, use_main_endpoint=False):
    """Test a single tool execution"""
    print(f"\n=== TESTING {tool_name.upper()} ===")
    
    # Choose endpoint
    if use_main_endpoint:
        url = f"{MAIN_TOOLS_URL}/{tool_name}/scan"
        endpoint_type = "MAIN"
    else:
        url = f"{SIMPLE_TOOLS_URL}/{tool_name}/scan"
        endpoint_type = "SIMPLE"
    
    # Adjust target based on tool type
    if tool_name in ["nuclei", "nikto", "wpscan", "ffuf", "feroxbuster", "whatweb"]:
        if not target.startswith("http"):
            target = f"http://{target}"
    elif tool_name in ["testssl", "sslyze"]:
        target = target.replace("http://", "").replace("https://", "")
    
    data = {
        "target": target,
        "timeout": timeout,
        "options": {"scan_type": scan_type}
    }
    
    result = {
        "tool": tool_name,
        "endpoint": endpoint_type,
        "target": target,
        "success": False,
        "status_code": 0,
        "execution_time": 0,
        "output_length": 0,
        "error": None
    }
    
    try:
        start_time = time.time()
        response = requests.post(url, json=data, timeout=timeout + 30)
        end_time = time.time()
        
        result["status_code"] = response.status_code
        result["execution_time"] = round(end_time - start_time, 2)
        
        if response.status_code == 200:
            json_result = response.json()
            result["success"] = json_result.get("success", False)
            
            if result["success"]:
                data_section = json_result.get("data", {})
                output = data_section.get("output", "")
                result["output_length"] = len(str(output))
                result["scan_id"] = data_section.get("scan_id")
                result["tool_status"] = data_section.get("status")
                
                print(f"✅ SUCCESS - {tool_name} ({endpoint_type})")
                print(f"   Execution time: {result['execution_time']}s")
                print(f"   Output length: {result['output_length']} chars")
                print(f"   Status: {result['tool_status']}")
                
                if output and len(str(output)) > 50:
                    print(f"   Output preview: {str(output)[:100]}...")
                    
            else:
                result["error"] = json_result.get("error", "Unknown error")
                print(f"❌ FAILED - {tool_name} ({endpoint_type})")
                print(f"   Error: {result['error']}")
        else:
            result["error"] = f"HTTP {response.status_code}: {response.text[:200]}"
            print(f"❌ HTTP ERROR - {tool_name} ({endpoint_type})")
            print(f"   Status: {response.status_code}")
            
    except Exception as e:
        result["error"] = str(e)
        print(f"❌ EXCEPTION - {tool_name} ({endpoint_type})")
        print(f"   Error: {e}")
    
    return result

def get_available_tools():
    """Get all available tools from the backend"""
    try:
        print("🔍 Discovering available tools...")
        response = requests.get(f"{SIMPLE_TOOLS_URL}/available", timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            if data.get("success"):
                tools = data.get("data", [])
                tool_names = [tool.get("name") for tool in tools if tool.get("name")]
                print(f"✅ Found {len(tool_names)} tools: {', '.join(tool_names)}")
                return tool_names
        
        print("❌ Failed to get tools list")
        return []
        
    except Exception as e:
        print(f"❌ Error getting tools: {e}")
        return []

def test_all_tools_systematic():
    """Test all available tools systematically"""
    print("🧪 COMPREHENSIVE TESTING OF ALL AVAILABLE TOOLS")
    print("=" * 80)
    print(f"Backend: {BACKEND_URL}")
    print(f"Time: {datetime.now()}")
    print()
    
    # Check backend health
    try:
        r = requests.get(f"{BACKEND_URL}/api/health", timeout=10)
        if r.status_code == 200:
            print("✅ Backend is healthy")
        else:
            print("❌ Backend health check failed")
            return False
    except Exception as e:
        print(f"❌ Backend not accessible: {e}")
        return False
    
    # Get all available tools
    available_tools = get_available_tools()
    if not available_tools:
        print("❌ No tools available for testing")
        return False
    
    print(f"\n📋 TESTING PLAN: {len(available_tools)} tools")
    print("=" * 80)
    
    # Test configuration for different tool types
    test_configs = {
        # Network Scanning
        "nmap": {"target": "scanme.nmap.org", "scan_type": "basic", "timeout": 60},
        
        # Vulnerability Scanning  
        "nuclei": {"target": "http://scanme.nmap.org", "scan_type": "critical", "timeout": 120},
        
        # Web Application Testing
        "sqlmap": {"target": "http://testphp.vulnweb.com/artists.php?artist=1", "scan_type": "detection", "timeout": 180},
        "gobuster": {"target": "http://scanme.nmap.org", "scan_type": "dir", "timeout": 120},
        "nikto": {"target": "http://scanme.nmap.org", "scan_type": "basic", "timeout": 180},
        "dirb": {"target": "http://scanme.nmap.org", "scan_type": "basic", "timeout": 120},
        "wpscan": {"target": "http://scanme.nmap.org", "scan_type": "basic", "timeout": 120},
        "ffuf": {"target": "http://scanme.nmap.org", "scan_type": "dir", "timeout": 120},
        "feroxbuster": {"target": "http://scanme.nmap.org", "scan_type": "dir", "timeout": 120},
        "whatweb": {"target": "http://scanme.nmap.org", "scan_type": "basic", "timeout": 60},
        
        # Network Enumeration
        "enum4linux-ng": {"target": "scanme.nmap.org", "scan_type": "basic", "timeout": 120},
        "smbclient": {"target": "scanme.nmap.org", "scan_type": "basic", "timeout": 60},
        
        # SSL/TLS Testing
        "testssl": {"target": "scanme.nmap.org:443", "scan_type": "basic", "timeout": 120},
        "sslyze": {"target": "scanme.nmap.org:443", "scan_type": "basic", "timeout": 120},
        
        # Default for any other tools
        "default": {"target": "scanme.nmap.org", "scan_type": "basic", "timeout": 120}
    }
    
    results = []
    successful_tools = []
    failed_tools = []
    
    print("\n🔥 TESTING ALL TOOLS - SIMPLE ENDPOINTS")
    print("=" * 80)
    
    # Test each tool with simple endpoints
    for tool in available_tools:
        config = test_configs.get(tool, test_configs["default"])
        
        result = test_tool_execution(
            tool, 
            config["target"], 
            config["scan_type"], 
            config["timeout"],
            use_main_endpoint=False
        )
        
        results.append(result)
        
        if result["success"]:
            successful_tools.append(tool)
        else:
            failed_tools.append(tool)
        
        # Small delay between tests
        time.sleep(2)
    
    print("\n🔥 TESTING ALL TOOLS - MAIN ENDPOINTS")
    print("=" * 80)
    
    # Test each tool with main endpoints (should now route to simple)
    main_results = []
    for tool in available_tools:
        config = test_configs.get(tool, test_configs["default"])
        
        result = test_tool_execution(
            tool, 
            config["target"], 
            config["scan_type"], 
            config["timeout"],
            use_main_endpoint=True
        )
        
        main_results.append(result)
        time.sleep(2)
    
    # Summary
    print("\n" + "=" * 80)
    print("📊 COMPREHENSIVE TESTING RESULTS")
    print("=" * 80)
    
    total_tools = len(available_tools)
    success_count = len(successful_tools)
    success_rate = (success_count / total_tools * 100) if total_tools > 0 else 0
    
    print(f"Tools discovered: {total_tools}")
    print(f"Tools tested: {len(results)}")
    print(f"Successful tools: {success_count}")
    print(f"Failed tools: {len(failed_tools)}")
    print(f"SUCCESS RATE: {success_rate:.1f}%")
    
    print(f"\n✅ WORKING TOOLS ({len(successful_tools)}):")
    for tool in successful_tools:
        result = next(r for r in results if r["tool"] == tool)
        print(f"   {tool}: {result['execution_time']}s, {result['output_length']} chars")
    
    if failed_tools:
        print(f"\n❌ FAILED TOOLS ({len(failed_tools)}):")
        for tool in failed_tools:
            result = next(r for r in results if r["tool"] == tool)
            print(f"   {tool}: {result['error']}")
    
    # Compare simple vs main endpoints
    print(f"\n🔄 ENDPOINT COMPARISON:")
    simple_success = len([r for r in results if r["success"]])
    main_success = len([r for r in main_results if r["success"]])
    
    print(f"Simple endpoints: {simple_success}/{total_tools} working")
    print(f"Main endpoints: {main_success}/{total_tools} working")
    
    if main_success >= simple_success:
        print("✅ Main endpoints now working correctly (routing to simple)")
    else:
        print("⚠️  Main endpoints still have issues")
    
    # Final assessment
    if success_rate >= 90:
        print(f"\n🎉 EXCELLENT: {success_rate:.1f}% success rate - Nearly all tools working!")
        print("✅ GOAL ACHIEVED: Close to 100% tools working perfectly")
        return True
    elif success_rate >= 70:
        print(f"\n✅ GOOD: {success_rate:.1f}% success rate - Most tools working")
        print("⚡ Significant improvement achieved")
        return True
    elif success_rate >= 50:
        print(f"\n⚠️  MODERATE: {success_rate:.1f}% success rate - Some improvement")
        return False
    else:
        print(f"\n❌ POOR: {success_rate:.1f}% success rate - Major issues remain")
        return False

if __name__ == "__main__":
    success = test_all_tools_systematic()
    print(f"\n{'='*80}")
    print(f"FINAL RESULT: {'SUCCESS' if success else 'NEEDS IMPROVEMENT'}")
    print(f"{'='*80}")
    exit(0 if success else 1)