-- NexusScan Database Schema
-- SQLite database for storing campaigns, scans, vulnerabilities, and configurations
-- Version: 1.0 (Week 4 Implementation)

-- Enable foreign key constraints
PRAGMA foreign_keys = ON;

-- =============================================================================
-- CORE TABLES
-- =============================================================================

-- Campaigns: Main scanning campaigns/projects
CREATE TABLE campaigns (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    target_scope TEXT NOT NULL, -- JSON array of targets
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    status VARCHAR(50) DEFAULT 'draft', -- draft, running, completed, failed, paused
    owner VARCHAR(100), -- User/team owner
    tags TEXT, -- JSON array of tags
    settings TEXT -- JSON campaign-specific settings
);

-- Scans: Individual scan sessions within campaigns
CREATE TABLE scans (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    campaign_id INTEGER NOT NULL,
    name VARCHAR(255) NOT NULL,
    scan_type VARCHAR(100) NOT NULL, -- network, web, api, mobile, etc.
    target VARCHAR(500) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    started_at TIMESTAMP NULL,
    completed_at TIMESTAMP NULL,
    status VARCHAR(50) DEFAULT 'pending', -- pending, running, completed, failed, cancelled
    priority VARCHAR(20) DEFAULT 'medium', -- low, medium, high, critical
    progress INTEGER DEFAULT 0, -- Progress percentage 0-100
    duration_seconds INTEGER DEFAULT 0,
    tools_used TEXT, -- JSON array of tools used
    scan_config TEXT, -- JSON configuration used
    raw_output TEXT, -- Raw tool output
    summary TEXT, -- Brief scan summary
    results TEXT, -- JSON scan results
    FOREIGN KEY (campaign_id) REFERENCES campaigns(id) ON DELETE CASCADE
);

-- Vulnerabilities: Discovered vulnerabilities
CREATE TABLE vulnerabilities (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    scan_id INTEGER NOT NULL,
    campaign_id INTEGER NOT NULL,
    vuln_type VARCHAR(100) NOT NULL, -- sql_injection, xss, rce, etc.
    severity VARCHAR(20) NOT NULL, -- critical, high, medium, low, info
    title VARCHAR(500) NOT NULL,
    description TEXT NOT NULL,
    affected_url VARCHAR(1000),
    affected_parameter VARCHAR(200),
    evidence TEXT, -- Proof of concept
    remediation TEXT, -- Fix recommendations
    cvss_score DECIMAL(3,1) DEFAULT 0.0,
    cve_id VARCHAR(50), -- CVE identifier if applicable
    discovered_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    status VARCHAR(50) DEFAULT 'open', -- open, fixed, false_positive, accepted_risk
    verified BOOLEAN DEFAULT FALSE,
    exploitable BOOLEAN DEFAULT FALSE,
    tags TEXT, -- JSON array of tags
    metadata TEXT, -- JSON additional metadata
    FOREIGN KEY (scan_id) REFERENCES scans(id) ON DELETE CASCADE,
    FOREIGN KEY (campaign_id) REFERENCES campaigns(id) ON DELETE CASCADE
);

-- AI Analysis Results: AI-generated analysis and insights
CREATE TABLE ai_analysis (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    vulnerability_id INTEGER,
    scan_id INTEGER,
    campaign_id INTEGER,
    analysis_type VARCHAR(100) NOT NULL, -- payload_generation, risk_assessment, remediation
    ai_provider VARCHAR(50), -- openai, deepseek, anthropic
    model_used VARCHAR(100),
    input_data TEXT NOT NULL, -- JSON input sent to AI
    output_data TEXT NOT NULL, -- JSON AI response
    confidence_score DECIMAL(3,2) DEFAULT 0.0,
    execution_time_ms INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    status VARCHAR(50) DEFAULT 'completed', -- pending, completed, failed
    error_message TEXT,
    FOREIGN KEY (vulnerability_id) REFERENCES vulnerabilities(id) ON DELETE CASCADE,
    FOREIGN KEY (scan_id) REFERENCES scans(id) ON DELETE CASCADE,
    FOREIGN KEY (campaign_id) REFERENCES campaigns(id) ON DELETE CASCADE
);

-- =============================================================================
-- CONFIGURATION TABLES
-- =============================================================================

-- Application Configuration
CREATE TABLE app_config (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    section VARCHAR(100) NOT NULL, -- ai, scanning, reporting, etc.
    key VARCHAR(200) NOT NULL,
    value TEXT NOT NULL,
    value_type VARCHAR(20) DEFAULT 'string', -- string, integer, boolean, json
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(section, key)
);

-- Scan Templates: Reusable scan configurations
CREATE TABLE scan_templates (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(255) NOT NULL UNIQUE,
    description TEXT,
    scan_type VARCHAR(100) NOT NULL,
    tools_config TEXT NOT NULL, -- JSON tools configuration
    ai_config TEXT, -- JSON AI settings
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_default BOOLEAN DEFAULT FALSE,
    category VARCHAR(100) -- web, network, api, mobile
);

-- =============================================================================
-- REPORTING & EXPORT TABLES
-- =============================================================================

-- Reports: Generated reports
CREATE TABLE reports (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    campaign_id INTEGER NOT NULL,
    name VARCHAR(255) NOT NULL,
    report_type VARCHAR(100) NOT NULL, -- executive, technical, compliance
    format VARCHAR(50) NOT NULL, -- pdf, html, json, csv
    template_used VARCHAR(200),
    generated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    file_path VARCHAR(500), -- Path to generated file
    file_size INTEGER DEFAULT 0,
    status VARCHAR(50) DEFAULT 'generated', -- generating, generated, failed
    metadata TEXT, -- JSON report metadata
    FOREIGN KEY (campaign_id) REFERENCES campaigns(id) ON DELETE CASCADE
);

-- Export History: Track data exports
CREATE TABLE export_history (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    campaign_id INTEGER,
    export_type VARCHAR(100) NOT NULL, -- vulnerabilities, scans, full_campaign
    format VARCHAR(50) NOT NULL, -- json, csv, xml
    file_path VARCHAR(500),
    exported_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    record_count INTEGER DEFAULT 0,
    file_size INTEGER DEFAULT 0,
    exported_by VARCHAR(100), -- User who initiated export
    FOREIGN KEY (campaign_id) REFERENCES campaigns(id) ON DELETE SET NULL
);

-- =============================================================================
-- SECURITY & AUDIT TABLES
-- =============================================================================

-- Activity Log: Audit trail of user actions
CREATE TABLE activity_log (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id VARCHAR(100),
    action VARCHAR(200) NOT NULL,
    resource_type VARCHAR(100), -- campaign, scan, vulnerability, etc.
    resource_id INTEGER,
    details TEXT, -- JSON additional details
    ip_address VARCHAR(45),
    user_agent TEXT,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    session_id VARCHAR(255)
);

-- File Attachments: Store evidence files, screenshots, etc.
CREATE TABLE file_attachments (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    vulnerability_id INTEGER,
    scan_id INTEGER,
    campaign_id INTEGER,
    filename VARCHAR(500) NOT NULL,
    original_filename VARCHAR(500) NOT NULL,
    file_path VARCHAR(1000) NOT NULL,
    file_size INTEGER NOT NULL,
    mime_type VARCHAR(200),
    file_hash VARCHAR(64), -- SHA256 hash
    uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    description TEXT,
    FOREIGN KEY (vulnerability_id) REFERENCES vulnerabilities(id) ON DELETE CASCADE,
    FOREIGN KEY (scan_id) REFERENCES scans(id) ON DELETE CASCADE,
    FOREIGN KEY (campaign_id) REFERENCES campaigns(id) ON DELETE CASCADE
);

-- =============================================================================
-- SCAN RESULTS TABLE (Referenced in indexes)
-- =============================================================================

-- Scan Results: Individual tool results within scans
CREATE TABLE scan_results (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    scan_id INTEGER NOT NULL,
    tool_name VARCHAR(100) NOT NULL,
    result_type VARCHAR(100), -- vulnerability, info, error
    raw_output TEXT,
    parsed_data TEXT, -- JSON structured result data
    execution_time_ms INTEGER DEFAULT 0,
    exit_code INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (scan_id) REFERENCES scans(id) ON DELETE CASCADE
);

-- =============================================================================
-- INDEXES FOR PERFORMANCE
-- =============================================================================

-- Campaign indexes
CREATE INDEX idx_campaigns_status ON campaigns(status);
CREATE INDEX idx_campaigns_created_at ON campaigns(created_at);
CREATE INDEX idx_campaigns_owner ON campaigns(owner);

-- Scan indexes
CREATE INDEX idx_scans_campaign_id ON scans(campaign_id);
CREATE INDEX idx_scans_status ON scans(status);
CREATE INDEX idx_scans_created_at ON scans(created_at);
CREATE INDEX idx_scans_started_at ON scans(started_at);
CREATE INDEX idx_scans_scan_type ON scans(scan_type);
CREATE INDEX idx_scans_priority ON scans(priority);

-- Vulnerability indexes
CREATE INDEX idx_vulnerabilities_scan_id ON vulnerabilities(scan_id);
CREATE INDEX idx_vulnerabilities_campaign_id ON vulnerabilities(campaign_id);
CREATE INDEX idx_vulnerabilities_severity ON vulnerabilities(severity);
CREATE INDEX idx_vulnerabilities_vuln_type ON vulnerabilities(vuln_type);
CREATE INDEX idx_vulnerabilities_status ON vulnerabilities(status);
CREATE INDEX idx_vulnerabilities_discovered_at ON vulnerabilities(discovered_at);
CREATE INDEX idx_vulnerabilities_cvss_score ON vulnerabilities(cvss_score);

-- AI Analysis indexes
CREATE INDEX idx_ai_analysis_vulnerability_id ON ai_analysis(vulnerability_id);
CREATE INDEX idx_ai_analysis_scan_id ON ai_analysis(scan_id);
CREATE INDEX idx_ai_analysis_campaign_id ON ai_analysis(campaign_id);
CREATE INDEX idx_ai_analysis_analysis_type ON ai_analysis(analysis_type);
CREATE INDEX idx_ai_analysis_ai_provider ON ai_analysis(ai_provider);

-- Configuration indexes
CREATE INDEX idx_app_config_section ON app_config(section);
CREATE INDEX idx_app_config_section_key ON app_config(section, key);

-- Activity log indexes
CREATE INDEX idx_activity_log_user_id ON activity_log(user_id);
CREATE INDEX idx_activity_log_timestamp ON activity_log(timestamp);
CREATE INDEX idx_activity_log_resource ON activity_log(resource_type, resource_id);

-- Scan Results indexes (for performance optimization)
CREATE INDEX idx_scan_results_scan ON scan_results(scan_id, tool_name);
CREATE INDEX idx_scan_results_tool ON scan_results(tool_name);
CREATE INDEX idx_scan_results_created ON scan_results(created_at);

-- Enhanced vulnerability indexes for complex searches
CREATE INDEX idx_vulnerabilities_severity_date ON vulnerabilities(severity, discovered_at);
CREATE INDEX idx_vulnerabilities_type_severity ON vulnerabilities(vuln_type, severity);
CREATE INDEX idx_vulnerabilities_status_severity ON vulnerabilities(status, severity);

-- Enhanced scan indexes for workload optimization
CREATE INDEX idx_scans_campaign_status ON scans(campaign_id, status);
CREATE INDEX idx_scans_type_status ON scans(scan_type, status);

-- =============================================================================
-- TRIGGERS FOR AUTO-UPDATE
-- =============================================================================

-- Update campaigns.updated_at on changes
CREATE TRIGGER update_campaigns_timestamp 
AFTER UPDATE ON campaigns
BEGIN
    UPDATE campaigns SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;

-- Update app_config.updated_at on changes
CREATE TRIGGER update_app_config_timestamp 
AFTER UPDATE ON app_config
BEGIN
    UPDATE app_config SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;

-- Update scan_templates.updated_at on changes
CREATE TRIGGER update_scan_templates_timestamp 
AFTER UPDATE ON scan_templates
BEGIN
    UPDATE scan_templates SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;

-- Auto-update scan duration when completed
CREATE TRIGGER update_scan_duration
AFTER UPDATE OF completed_at ON scans
WHEN NEW.completed_at IS NOT NULL AND OLD.completed_at IS NULL
BEGIN
    UPDATE scans 
    SET duration_seconds = CAST((julianday(NEW.completed_at) - julianday(NEW.started_at)) * 86400 AS INTEGER)
    WHERE id = NEW.id;
END;

-- =============================================================================
-- VIEWS FOR COMMON QUERIES
-- =============================================================================

-- Campaign summary view
CREATE VIEW campaign_summary AS
SELECT 
    c.id,
    c.name,
    c.description,
    c.status,
    c.created_at,
    c.updated_at,
    COUNT(DISTINCT s.id) as total_scans,
    COUNT(DISTINCT v.id) as total_vulnerabilities,
    COUNT(DISTINCT CASE WHEN v.severity = 'critical' THEN v.id END) as critical_vulns,
    COUNT(DISTINCT CASE WHEN v.severity = 'high' THEN v.id END) as high_vulns,
    COUNT(DISTINCT CASE WHEN v.severity = 'medium' THEN v.id END) as medium_vulns,
    COUNT(DISTINCT CASE WHEN v.severity = 'low' THEN v.id END) as low_vulns,
    AVG(v.cvss_score) as avg_cvss_score
FROM campaigns c
LEFT JOIN scans s ON c.id = s.campaign_id
LEFT JOIN vulnerabilities v ON c.id = v.campaign_id
GROUP BY c.id, c.name, c.description, c.status, c.created_at, c.updated_at;

-- Recent activity view
CREATE VIEW recent_activity AS
SELECT 
    'campaign' as resource_type,
    c.id as resource_id,
    c.name as resource_name,
    c.status,
    c.updated_at as activity_time,
    'Campaign ' || c.status as activity_description
FROM campaigns c
WHERE c.updated_at >= datetime('now', '-7 days')
UNION ALL
SELECT 
    'scan' as resource_type,
    s.id as resource_id,
    s.name as resource_name,
    s.status,
    COALESCE(s.completed_at, s.started_at) as activity_time,
    'Scan ' || s.status as activity_description
FROM scans s
WHERE COALESCE(s.completed_at, s.started_at) >= datetime('now', '-7 days')
ORDER BY activity_time DESC;

-- =============================================================================
-- INSERT DEFAULT DATA
-- =============================================================================

-- Default application configuration
INSERT INTO app_config (section, key, value, value_type, description) VALUES
('ai', 'openai_api_key', '', 'string', 'OpenAI API key for GPT-4o model'),
('ai', 'deepseek_api_key', '', 'string', 'DeepSeek API key for fallback'),
('ai', 'anthropic_api_key', '', 'string', 'Anthropic API key for backup'),
('ai', 'openai_model', 'gpt-4o', 'string', 'OpenAI model to use'),
('ai', 'deepseek_model', 'deepseek-chat', 'string', 'DeepSeek model to use'),
('ai', 'default_provider', 'openai', 'string', 'Default AI provider'),
('ai', 'request_timeout', '60', 'integer', 'AI request timeout in seconds'),
('ai', 'max_retries', '3', 'integer', 'Maximum retry attempts'),

('scanning', 'default_nmap_options', '-sV -sC --script=vuln', 'string', 'Default Nmap scan options'),
('scanning', 'nuclei_templates_path', './tools/nuclei-templates', 'string', 'Path to Nuclei templates'),
('scanning', 'max_concurrent_scans', '5', 'integer', 'Maximum concurrent scans'),
('scanning', 'scan_timeout', '3600', 'integer', 'Default scan timeout in seconds'),

('reporting', 'default_template', 'professional', 'string', 'Default report template'),
('reporting', 'output_directory', './reports', 'string', 'Report output directory'),
('reporting', 'include_screenshots', 'true', 'boolean', 'Include screenshots in reports'),
('reporting', 'logo_path', '', 'string', 'Custom logo for reports'),

('general', 'auto_save_interval', '300', 'integer', 'Auto-save interval in seconds'),
('general', 'max_log_file_size', '50', 'integer', 'Maximum log file size in MB'),
('general', 'debug_mode', 'false', 'boolean', 'Enable debug logging'),
('general', 'theme', 'dark', 'string', 'UI theme (dark/light)');

-- Default scan templates
INSERT INTO scan_templates (name, description, scan_type, tools_config, ai_config, is_default, category) VALUES
('Quick Web Scan', 'Fast web application security scan', 'web', 
 '{"nmap": {"options": "-sV --script=http-*"}, "nuclei": {"templates": ["http", "web"]}}',
 '{"generate_payloads": true, "analysis_level": "basic"}', 
 true, 'web'),

('Comprehensive Network Scan', 'Full network security assessment', 'network',
 '{"nmap": {"options": "-sS -sV -sC -O --script=vuln"}, "nuclei": {"templates": ["network", "services"]}}',
 '{"generate_payloads": true, "analysis_level": "comprehensive"}',
 true, 'network'),

('API Security Scan', 'REST API security testing', 'api',
 '{"nuclei": {"templates": ["api", "swagger"]}, "custom": {"tools": ["api_scanner"]}}',
 '{"generate_payloads": true, "analysis_level": "targeted"}',
 false, 'api'),

('Mobile App Scan', 'Mobile application security assessment', 'mobile',
 '{"custom": {"tools": ["mobile_scanner"]}}',
 '{"generate_payloads": false, "analysis_level": "basic"}',
 false, 'mobile');

-- Database version tracking
CREATE TABLE db_version (
    version INTEGER PRIMARY KEY,
    applied_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    description TEXT
);

INSERT INTO db_version (version, description) VALUES 
(1, 'Initial schema creation - Week 4 Database & Data Management');