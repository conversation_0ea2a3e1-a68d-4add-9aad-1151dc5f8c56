#!/usr/bin/env python3
"""
Sudo Execution Wrapper for Security Tools
Handles privilege escalation for tools requiring root access
"""

import subprocess
import logging
import os
import time
from typing import List, Dict, Any, Optional
from pathlib import Path

logger = logging.getLogger(__name__)

class SudoExecutor:
    """Manages sudo execution for security tools requiring root privileges"""
    
    def __init__(self, sudo_password: str = None):
        """Initialize sudo executor
        
        Args:
            sudo_password: Password for sudo authentication
        """
        self.sudo_password = sudo_password or os.getenv('SUDO_PASSWORD', '292827')
        self._cached_auth = False
        self._auth_timestamp = 0
        
    def authenticate_sudo(self) -> bool:
        """Authenticate sudo access and cache credentials
        
        Returns:
            True if authentication successful
        """
        try:
            # Check if sudo is available
            result = subprocess.run(['which', 'sudo'], capture_output=True, text=True)
            if result.returncode != 0:
                logger.error("sudo command not available")
                return False
            
            # Test sudo access with password
            cmd = ['sudo', '-S', 'echo', 'sudo_test']
            process = subprocess.Popen(
                cmd,
                stdin=subprocess.PIPE,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            stdout, stderr = process.communicate(input=f"{self.sudo_password}\n")
            
            if process.returncode == 0 and 'sudo_test' in stdout:
                self._cached_auth = True
                self._auth_timestamp = time.time()
                logger.info("Sudo authentication successful")
                return True
            else:
                logger.error(f"Sudo authentication failed: {stderr}")
                return False
                
        except Exception as e:
            logger.error(f"Sudo authentication error: {e}")
            return False
    
    def is_authenticated(self) -> bool:
        """Check if sudo is currently authenticated
        
        Returns:
            True if authenticated and cache valid
        """
        # Sudo cache typically expires after 15 minutes
        cache_timeout = 900  # 15 minutes
        current_time = time.time()
        
        if not self._cached_auth:
            return False
            
        if current_time - self._auth_timestamp > cache_timeout:
            self._cached_auth = False
            return False
            
        return True
    
    def execute_with_sudo(self, command: List[str], 
                         timeout: int = 300,
                         working_dir: str = None) -> subprocess.CompletedProcess:
        """Execute command with sudo privileges
        
        Args:
            command: Command and arguments to execute
            timeout: Command timeout in seconds
            working_dir: Working directory for command execution
            
        Returns:
            CompletedProcess result
        """
        if not self.is_authenticated():
            if not self.authenticate_sudo():
                raise PermissionError("Failed to authenticate sudo access")
        
        # Prepare sudo command
        sudo_cmd = ['sudo', '-S'] + command
        
        try:
            logger.info(f"Executing with sudo: {' '.join(command)}")
            
            # Execute command with sudo
            process = subprocess.Popen(
                sudo_cmd,
                stdin=subprocess.PIPE,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                cwd=working_dir
            )
            
            # Provide password to sudo
            stdout, stderr = process.communicate(
                input=f"{self.sudo_password}\n",
                timeout=timeout
            )
            
            # Create result object
            result = subprocess.CompletedProcess(
                args=sudo_cmd,
                returncode=process.returncode,
                stdout=stdout,
                stderr=stderr
            )
            
            if result.returncode == 0:
                logger.info(f"Sudo command executed successfully: {command[0]}")
            else:
                logger.error(f"Sudo command failed: {stderr}")
                
            return result
            
        except subprocess.TimeoutExpired:
            process.kill()
            logger.error(f"Sudo command timeout: {' '.join(command)}")
            raise
        except Exception as e:
            logger.error(f"Sudo execution error: {e}")
            raise
    
    def check_tool_sudo_requirement(self, tool_name: str, scan_type: str = None) -> bool:
        """Check if a tool requires sudo for specific scan type
        
        Args:
            tool_name: Name of the security tool
            scan_type: Type of scan (e.g., -sS, -sU)
            
        Returns:
            True if sudo is required
        """
        # Tools that always require root
        always_root_tools = {
            'masscan',
            'zmap'
        }
        
        # Tools with conditional root requirements
        conditional_root = {
            'nmap': {
                'requires_root': ['-sS', '-sU', '-sO', '-sN', '-sF', '-sX'],
                'no_root': ['-sT', '-sn', '-sV', '-sC']
            },
            'hping3': {
                'requires_root': ['*']  # Always needs root
            }
        }
        
        tool_name = tool_name.lower()
        
        # Check always root tools
        if tool_name in always_root_tools:
            return True
        
        # Check conditional tools
        if tool_name in conditional_root:
            tool_config = conditional_root[tool_name]
            
            if scan_type:
                # Check if scan type requires root
                if scan_type in tool_config.get('requires_root', []):
                    return True
                if scan_type in tool_config.get('no_root', []):
                    return False
            
            # Default behavior for tool
            return len(tool_config.get('requires_root', [])) > 0
        
        # Default: assume no root required
        return False
    
    def get_safe_scan_options(self, tool_name: str) -> Dict[str, Any]:
        """Get safe scan options that don't require root
        
        Args:
            tool_name: Name of the security tool
            
        Returns:
            Dictionary of safe scan options
        """
        safe_options = {
            'nmap': {
                'scan_type': '-sT',  # TCP connect scan
                'requires_root': False,
                'description': 'TCP connect scan (no root required)'
            },
            'nuclei': {
                'scan_type': 'basic',
                'requires_root': False,
                'description': 'Basic vulnerability scan'
            },
            'sqlmap': {
                'scan_type': 'basic',
                'requires_root': False,
                'description': 'SQL injection testing'
            },
            'nikto': {
                'scan_type': 'basic',
                'requires_root': False,
                'description': 'Web vulnerability scan'
            }
        }
        
        return safe_options.get(tool_name.lower(), {
            'scan_type': 'basic',
            'requires_root': False,
            'description': 'Basic scan mode'
        })
    
    def create_sudo_script(self, commands: List[str], script_path: str = None) -> str:
        """Create a script file for complex sudo operations
        
        Args:
            commands: List of commands to include in script
            script_path: Path for script file (optional)
            
        Returns:
            Path to created script file
        """
        if not script_path:
            script_path = f"/tmp/nexusscan_sudo_{int(time.time())}.sh"
        
        script_content = "#!/bin/bash\n"
        script_content += "# NexusScan Sudo Script\n"
        script_content += "set -e\n\n"
        
        for cmd in commands:
            if isinstance(cmd, list):
                script_content += " ".join(cmd) + "\n"
            else:
                script_content += cmd + "\n"
        
        # Write script file
        with open(script_path, 'w') as f:
            f.write(script_content)
        
        # Make executable
        os.chmod(script_path, 0o755)
        
        logger.info(f"Created sudo script: {script_path}")
        return script_path

# Global sudo executor instance
_sudo_executor = None

def get_sudo_executor() -> SudoExecutor:
    """Get global sudo executor instance
    
    Returns:
        SudoExecutor instance
    """
    global _sudo_executor
    if _sudo_executor is None:
        _sudo_executor = SudoExecutor()
    return _sudo_executor

def requires_sudo(tool_name: str, scan_options: Dict[str, Any] = None) -> bool:
    """Check if tool/scan requires sudo privileges
    
    Args:
        tool_name: Name of the security tool
        scan_options: Scan configuration options
        
    Returns:
        True if sudo is required
    """
    executor = get_sudo_executor()
    scan_type = scan_options.get('scan_type') if scan_options else None
    return executor.check_tool_sudo_requirement(tool_name, scan_type)

def execute_with_sudo(command: List[str], **kwargs) -> subprocess.CompletedProcess:
    """Execute command with sudo privileges
    
    Args:
        command: Command and arguments
        **kwargs: Additional arguments for execution
        
    Returns:
        CompletedProcess result
    """
    executor = get_sudo_executor()
    return executor.execute_with_sudo(command, **kwargs)