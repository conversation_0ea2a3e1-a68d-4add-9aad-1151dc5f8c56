#!/usr/bin/env python3
"""
🌐 Cross-Platform Compatibility Testing Framework 🌐
Advanced compatibility validation across different environments and platforms

This system provides:
- Multi-platform environment detection and validation
- WSL (Windows Subsystem for Linux) integration testing
- Docker container compatibility validation  
- Python environment and dependency compatibility
- Node.js and frontend build system compatibility
- File system path resolution testing
- Network configuration compatibility
- Security tool compatibility across platforms
- Shell command and script compatibility
- Environment variable and configuration compatibility
- Performance baseline comparison across platforms
- Automated platform-specific optimization recommendations
- Cross-platform deployment validation
"""

import asyncio
import json
import logging
import os
import sys
import time
import subprocess
import platform
import shutil
from datetime import datetime
from pathlib import Path, PureWindowsPath, PurePosixPath
from typing import Dict, List, Any, Optional, Tuple
import tempfile
import socket
import psutil
from dataclasses import dataclass, field

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root / 'src'))

# Platform-specific color codes
class Colors:
    PLATFORM = '\033[38;5;39m'    # Bright blue for platform
    WINDOWS = '\033[38;5;21m'     # Blue for Windows
    LINUX = '\033[38;5;28m'       # Green for Linux
    WSL = '\033[38;5;166m'        # Orange for WSL
    DOCKER = '\033[38;5;33m'      # Cyan for Docker
    MACOS = '\033[38;5;129m'      # Purple for macOS
    COMPAT = '\033[38;5;82m'      # Bright green for compatible
    INCOMPAT = '\033[38;5;196m'   # Bright red for incompatible
    PARTIAL = '\033[38;5;226m'    # Yellow for partial compatibility
    ENDC = '\033[0m'
    BOLD = '\033[1m'

@dataclass
class PlatformEnvironment:
    """Represents a platform environment for testing"""
    name: str
    type: str  # 'native', 'wsl', 'docker', 'vm'
    os_family: str  # 'windows', 'linux', 'darwin'
    architecture: str
    python_version: str
    shell: str
    available: bool = False
    tested: bool = False
    compatibility_score: float = 0.0
    issues: List[str] = field(default_factory=list)
    features: Dict[str, bool] = field(default_factory=dict)

@dataclass
class CompatibilityTest:
    """Individual compatibility test"""
    name: str
    description: str
    test_type: str  # 'file_system', 'network', 'process', 'dependency', 'command'
    platforms: List[str]  # Platforms this test applies to
    critical: bool = False
    auto_fix: bool = False
    
class PlatformDetector:
    """Detect and analyze available platforms"""
    
    def __init__(self):
        self.environments: Dict[str, PlatformEnvironment] = {}
        
    def detect_all_environments(self) -> Dict[str, PlatformEnvironment]:
        """Detect all available platform environments"""
        print(f"{Colors.PLATFORM}🔍 Detecting platform environments...{Colors.ENDC}")
        
        # Native platform
        self._detect_native_platform()
        
        # WSL environments (if on Windows)
        if platform.system() == 'Windows':
            self._detect_wsl_environments()
        
        # Docker environments
        self._detect_docker_environments()
        
        # Virtual environments
        self._detect_virtual_environments()
        
        return self.environments
    
    def _detect_native_platform(self):
        """Detect native platform environment"""
        system = platform.system()
        machine = platform.machine()
        python_version = platform.python_version()
        
        # Determine shell
        shell = os.environ.get('SHELL', '')
        if not shell:
            if system == 'Windows':
                shell = 'cmd' if 'COMSPEC' in os.environ else 'powershell'
            else:
                shell = 'bash'
        
        os_family = {
            'Windows': 'windows',
            'Linux': 'linux',
            'Darwin': 'darwin'
        }.get(system, 'unknown')
        
        env = PlatformEnvironment(
            name=f"Native {system}",
            type='native',
            os_family=os_family,
            architecture=machine,
            python_version=python_version,
            shell=shell,
            available=True
        )
        
        # Detect platform-specific features
        env.features = {
            'symlinks': self._test_symlink_support(),
            'long_paths': self._test_long_path_support(),
            'case_sensitive': self._test_case_sensitivity(),
            'executable_permissions': os_family != 'windows',
            'unix_sockets': os_family != 'windows'
        }
        
        self.environments['native'] = env
        print(f"  {Colors.COMPAT}✅ Native: {system} {machine} (Python {python_version}){Colors.ENDC}")
    
    def _detect_wsl_environments(self):
        """Detect WSL environments"""
        try:
            # Check if WSL is available
            result = subprocess.run(['wsl', '--status'], 
                                  capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0:
                # List WSL distributions
                list_result = subprocess.run(['wsl', '--list', '--verbose'], 
                                           capture_output=True, text=True, timeout=10)
                
                if list_result.returncode == 0:
                    lines = list_result.stdout.split('\n')
                    for line in lines[1:]:  # Skip header
                        if line.strip() and not line.startswith('Windows Subsystem'):
                            parts = line.split()
                            if len(parts) >= 3:
                                distro_name = parts[0].replace('*', '').strip()
                                state = parts[1]
                                version = parts[2] if len(parts) > 2 else 'unknown'
                                
                                if state.lower() in ['running', 'stopped']:
                                    env = PlatformEnvironment(
                                        name=f"WSL {distro_name}",
                                        type='wsl',
                                        os_family='linux',
                                        architecture='x86_64',  # WSL is typically x64
                                        python_version='unknown',
                                        shell='bash',
                                        available=True
                                    )
                                    
                                    # Test WSL-specific features
                                    env.features = self._test_wsl_features(distro_name)
                                    
                                    self.environments[f'wsl_{distro_name.lower()}'] = env
                                    print(f"  {Colors.WSL}✅ WSL: {distro_name} (Version {version}){Colors.ENDC}")
                
        except (subprocess.TimeoutExpired, FileNotFoundError):
            print(f"  {Colors.PARTIAL}⚠️ WSL not available or not responding{Colors.ENDC}")
    
    def _detect_docker_environments(self):
        """Detect Docker environments"""
        try:
            result = subprocess.run(['docker', '--version'], 
                                  capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0:
                # Check for common development containers
                containers = [
                    ('ubuntu:latest', 'linux'),
                    ('node:18', 'linux'),
                    ('python:3.11', 'linux')
                ]
                
                for container, os_family in containers:
                    try:
                        # Check if image is available
                        check_result = subprocess.run(
                            ['docker', 'image', 'inspect', container],
                            capture_output=True, text=True, timeout=5
                        )
                        
                        if check_result.returncode == 0:
                            env = PlatformEnvironment(
                                name=f"Docker {container}",
                                type='docker',
                                os_family=os_family,
                                architecture='x86_64',
                                python_version='unknown',
                                shell='bash',
                                available=True
                            )
                            
                            env.features = {
                                'isolated_network': True,
                                'ephemeral_storage': True,
                                'limited_resources': True
                            }
                            
                            self.environments[f'docker_{container.replace(":", "_")}'] = env
                            print(f"  {Colors.DOCKER}✅ Docker: {container}{Colors.ENDC}")
                    except:
                        continue
                
        except (subprocess.TimeoutExpired, FileNotFoundError):
            print(f"  {Colors.PARTIAL}⚠️ Docker not available{Colors.ENDC}")
    
    def _detect_virtual_environments(self):
        """Detect Python virtual environments"""
        # Check if currently in a virtual environment
        if hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
            venv_path = sys.prefix
            env = PlatformEnvironment(
                name=f"Virtual Environment",
                type='virtual',
                os_family=platform.system().lower(),
                architecture=platform.machine(),
                python_version=platform.python_version(),
                shell=os.environ.get('SHELL', 'bash'),
                available=True
            )
            
            env.features = {
                'isolated_python': True,
                'package_isolation': True
            }
            
            self.environments['venv'] = env
            print(f"  {Colors.COMPAT}✅ Virtual Environment: {venv_path}{Colors.ENDC}")
    
    def _test_symlink_support(self) -> bool:
        """Test symbolic link support"""
        try:
            with tempfile.TemporaryDirectory() as temp_dir:
                temp_path = Path(temp_dir)
                source = temp_path / 'source.txt'
                link = temp_path / 'link.txt'
                
                source.write_text('test')
                os.symlink(source, link)
                
                return link.read_text() == 'test'
        except:
            return False
    
    def _test_long_path_support(self) -> bool:
        """Test long path support"""
        try:
            with tempfile.TemporaryDirectory() as temp_dir:
                # Create a path longer than 260 characters (Windows limit)
                long_path = Path(temp_dir)
                for i in range(10):
                    long_path = long_path / f'very_long_directory_name_that_exceeds_normal_limits_{i}'
                
                long_path.mkdir(parents=True)
                test_file = long_path / 'test.txt'
                test_file.write_text('test')
                
                return test_file.read_text() == 'test'
        except:
            return False
    
    def _test_case_sensitivity(self) -> bool:
        """Test file system case sensitivity"""
        try:
            with tempfile.TemporaryDirectory() as temp_dir:
                temp_path = Path(temp_dir)
                file1 = temp_path / 'Test.txt'
                file2 = temp_path / 'test.txt'
                
                file1.write_text('upper')
                file2.write_text('lower')
                
                # If case sensitive, files should be different
                return file1.read_text() != file2.read_text()
        except:
            return False
    
    def _test_wsl_features(self, distro_name: str) -> Dict[str, bool]:
        """Test WSL-specific features"""
        features = {}
        
        try:
            # Test file system interop
            result = subprocess.run(
                ['wsl', '-d', distro_name, '--', 'ls', '/mnt/c'],
                capture_output=True, text=True, timeout=10
            )
            features['windows_fs_access'] = result.returncode == 0
            
            # Test network connectivity
            result = subprocess.run(
                ['wsl', '-d', distro_name, '--', 'ping', '-c', '1', 'google.com'],
                capture_output=True, text=True, timeout=10
            )
            features['network_access'] = result.returncode == 0
            
            # Test Python availability
            result = subprocess.run(
                ['wsl', '-d', distro_name, '--', 'python3', '--version'],
                capture_output=True, text=True, timeout=10
            )
            features['python_available'] = result.returncode == 0
            
        except:
            pass
        
        return features

class CompatibilityTestSuite:
    """Comprehensive compatibility test suite"""
    
    def __init__(self, project_root: Path, environments: Dict[str, PlatformEnvironment]):
        self.project_root = project_root
        self.environments = environments
        self.test_results = {}
        
        self.tests = self._initialize_test_suite()
    
    def _initialize_test_suite(self) -> List[CompatibilityTest]:
        """Initialize comprehensive test suite"""
        return [
            # File System Compatibility Tests
            CompatibilityTest(
                name='Path Resolution',
                description='Test path resolution across different file systems',
                test_type='file_system',
                platforms=['native', 'wsl', 'docker'],
                critical=True
            ),
            
            CompatibilityTest(
                name='File Permissions',
                description='Test file permission handling',
                test_type='file_system',
                platforms=['native', 'wsl', 'docker'],
                critical=True
            ),
            
            CompatibilityTest(
                name='Symbolic Links',
                description='Test symbolic link creation and resolution',
                test_type='file_system',
                platforms=['native', 'wsl', 'docker']
            ),
            
            # Python Environment Tests
            CompatibilityTest(
                name='Python Package Import',
                description='Test critical Python package imports',
                test_type='dependency',
                platforms=['native', 'wsl', 'docker', 'venv'],
                critical=True
            ),
            
            CompatibilityTest(
                name='SQLite Database Access',
                description='Test SQLite database operations',
                test_type='dependency',
                platforms=['native', 'wsl', 'docker'],
                critical=True
            ),
            
            # Network Compatibility Tests
            CompatibilityTest(
                name='Port Binding',
                description='Test network port binding and access',
                test_type='network',
                platforms=['native', 'wsl', 'docker'],
                critical=True
            ),
            
            CompatibilityTest(
                name='External API Access',
                description='Test external API connectivity',
                test_type='network',
                platforms=['native', 'wsl', 'docker'],
                critical=False
            ),
            
            # Command Execution Tests
            CompatibilityTest(
                name='Security Tool Execution',
                description='Test security tool command execution',
                test_type='command',
                platforms=['native', 'wsl'],
                critical=True
            ),
            
            CompatibilityTest(
                name='Shell Script Compatibility',
                description='Test shell script execution',
                test_type='command',
                platforms=['native', 'wsl', 'docker']
            ),
            
            # Process Management Tests
            CompatibilityTest(
                name='Process Monitoring',
                description='Test process monitoring capabilities',
                test_type='process',
                platforms=['native', 'wsl', 'docker'],
                critical=False
            ),
            
            # Frontend Build Tests
            CompatibilityTest(
                name='Node.js Build Process',
                description='Test Node.js and frontend build process',
                test_type='dependency',
                platforms=['native', 'wsl', 'docker']
            ),
            
            CompatibilityTest(
                name='Tauri Build Compatibility',
                description='Test Tauri desktop build process',
                test_type='dependency',
                platforms=['native']
            )
        ]
    
    async def run_all_tests(self) -> Dict[str, Any]:
        """Run all compatibility tests across all environments"""
        print(f"\n{Colors.PLATFORM}🧪 Running cross-platform compatibility tests...{Colors.ENDC}")
        
        test_session = {
            'session_id': f"compat_test_{int(time.time())}",
            'start_time': datetime.now().isoformat(),
            'environments_tested': len(self.environments),
            'tests_run': 0,
            'results': {},
            'summary': {}
        }
        
        total_tests = len(self.tests) * len(self.environments)
        current_test = 0
        
        for env_name, environment in self.environments.items():
            if not environment.available:
                continue
                
            test_session['results'][env_name] = {}
            
            print(f"\n{Colors.BOLD}🎯 Testing environment: {environment.name}{Colors.ENDC}")
            
            for test in self.tests:
                current_test += 1
                
                # Check if test applies to this environment
                if not any(platform in env_name for platform in test.platforms):
                    continue
                
                print(f"  [{current_test}/{total_tests}] {test.name}...")
                
                result = await self._run_compatibility_test(test, environment, env_name)
                test_session['results'][env_name][test.name] = result
                test_session['tests_run'] += 1
                
                self._print_test_result(test, result)
        
        # Generate summary
        test_session['summary'] = self._generate_compatibility_summary(test_session['results'])
        
        return test_session
    
    async def _run_compatibility_test(self, test: CompatibilityTest, 
                                    environment: PlatformEnvironment, 
                                    env_name: str) -> Dict[str, Any]:
        """Run a single compatibility test"""
        start_time = time.time()
        
        try:
            if test.test_type == 'file_system':
                result = await self._test_file_system_compatibility(test, environment, env_name)
            elif test.test_type == 'dependency':
                result = await self._test_dependency_compatibility(test, environment, env_name)
            elif test.test_type == 'network':
                result = await self._test_network_compatibility(test, environment, env_name)
            elif test.test_type == 'command':
                result = await self._test_command_compatibility(test, environment, env_name)
            elif test.test_type == 'process':
                result = await self._test_process_compatibility(test, environment, env_name)
            else:
                result = {
                    'status': 'unknown',
                    'score': 0.0,
                    'message': f'Unknown test type: {test.test_type}'
                }
            
            result['execution_time'] = time.time() - start_time
            return result
            
        except Exception as e:
            return {
                'status': 'error',
                'score': 0.0,
                'message': f'Test execution failed: {str(e)}',
                'execution_time': time.time() - start_time
            }
    
    async def _test_file_system_compatibility(self, test: CompatibilityTest, 
                                            environment: PlatformEnvironment, 
                                            env_name: str) -> Dict[str, Any]:
        """Test file system compatibility"""
        
        if test.name == 'Path Resolution':
            return await self._test_path_resolution(environment, env_name)
        elif test.name == 'File Permissions':
            return await self._test_file_permissions(environment, env_name)
        elif test.name == 'Symbolic Links':
            return await self._test_symbolic_links(environment, env_name)
        
        return {'status': 'unknown', 'score': 0.0, 'message': 'Test not implemented'}
    
    async def _test_path_resolution(self, environment: PlatformEnvironment, env_name: str) -> Dict[str, Any]:
        """Test path resolution compatibility"""
        
        test_paths = [
            'src/main.py',
            'frontend/package.json',
            'data/nexusscan.db',
            'logs/debug.log'
        ]
        
        resolved_paths = {}
        windows_style_paths = {}
        posix_style_paths = {}
        
        for test_path in test_paths:
            # Test different path styles
            original_path = self.project_root / test_path
            
            # Windows style
            if environment.os_family == 'windows':
                windows_path = PureWindowsPath(self.project_root) / test_path
                windows_style_paths[test_path] = str(windows_path)
            
            # POSIX style
            posix_path = PurePosixPath(self.project_root) / test_path
            posix_style_paths[test_path] = str(posix_path)
            
            # Check if path exists and is accessible
            resolved_paths[test_path] = {
                'exists': original_path.exists(),
                'is_file': original_path.is_file() if original_path.exists() else False,
                'is_dir': original_path.is_dir() if original_path.exists() else False,
                'absolute': str(original_path.absolute())
            }
        
        # Calculate compatibility score
        accessible_paths = len([p for p in resolved_paths.values() if p['exists']])
        total_paths = len(test_paths)
        score = accessible_paths / total_paths if total_paths > 0 else 0
        
        status = 'pass' if score > 0.8 else 'partial' if score > 0.5 else 'fail'
        
        return {
            'status': status,
            'score': score,
            'message': f'Path resolution: {accessible_paths}/{total_paths} paths accessible',
            'details': {
                'resolved_paths': resolved_paths,
                'windows_style': windows_style_paths,
                'posix_style': posix_style_paths
            }
        }
    
    async def _test_file_permissions(self, environment: PlatformEnvironment, env_name: str) -> Dict[str, Any]:
        """Test file permission compatibility"""
        
        try:
            with tempfile.TemporaryDirectory() as temp_dir:
                temp_path = Path(temp_dir)
                
                # Test file creation
                test_file = temp_path / 'permission_test.txt'
                test_file.write_text('test content')
                
                permissions_tested = {}
                
                # Test read permission
                try:
                    content = test_file.read_text()
                    permissions_tested['read'] = True
                except:
                    permissions_tested['read'] = False
                
                # Test write permission
                try:
                    test_file.write_text('modified content')
                    permissions_tested['write'] = True
                except:
                    permissions_tested['write'] = False
                
                # Test execute permission (Unix-like systems)
                if environment.os_family != 'windows':
                    try:
                        os.chmod(test_file, 0o755)
                        permissions_tested['execute'] = True
                    except:
                        permissions_tested['execute'] = False
                else:
                    permissions_tested['execute'] = True  # Not applicable on Windows
                
                working_permissions = len([p for p in permissions_tested.values() if p])
                total_permissions = len(permissions_tested)
                score = working_permissions / total_permissions
                
                status = 'pass' if score == 1.0 else 'partial' if score > 0.5 else 'fail'
                
                return {
                    'status': status,
                    'score': score,
                    'message': f'File permissions: {working_permissions}/{total_permissions} working',
                    'details': permissions_tested
                }
                
        except Exception as e:
            return {
                'status': 'error',
                'score': 0.0,
                'message': f'Permission test failed: {str(e)}'
            }
    
    async def _test_symbolic_links(self, environment: PlatformEnvironment, env_name: str) -> Dict[str, Any]:
        """Test symbolic link compatibility"""
        
        if not environment.features.get('symlinks', False):
            return {
                'status': 'skip',
                'score': 0.0,
                'message': 'Symbolic links not supported on this platform'
            }
        
        try:
            with tempfile.TemporaryDirectory() as temp_dir:
                temp_path = Path(temp_dir)
                
                # Create source file
                source_file = temp_path / 'source.txt'
                source_file.write_text('original content')
                
                # Create symbolic link
                link_file = temp_path / 'link.txt'
                os.symlink(source_file, link_file)
                
                # Test link functionality
                tests = {
                    'link_created': link_file.exists(),
                    'link_is_symlink': link_file.is_symlink(),
                    'content_accessible': link_file.read_text() == 'original content',
                    'link_points_to_source': os.readlink(link_file) == str(source_file)
                }
                
                working_tests = len([t for t in tests.values() if t])
                total_tests = len(tests)
                score = working_tests / total_tests
                
                status = 'pass' if score == 1.0 else 'partial' if score > 0.5 else 'fail'
                
                return {
                    'status': status,
                    'score': score,
                    'message': f'Symbolic links: {working_tests}/{total_tests} tests passed',
                    'details': tests
                }
                
        except Exception as e:
            return {
                'status': 'error',
                'score': 0.0,
                'message': f'Symbolic link test failed: {str(e)}'
            }
    
    async def _test_dependency_compatibility(self, test: CompatibilityTest, 
                                           environment: PlatformEnvironment, 
                                           env_name: str) -> Dict[str, Any]:
        """Test dependency compatibility"""
        
        if test.name == 'Python Package Import':
            return await self._test_python_imports(environment, env_name)
        elif test.name == 'SQLite Database Access':
            return await self._test_sqlite_access(environment, env_name)
        elif test.name == 'Node.js Build Process':
            return await self._test_nodejs_build(environment, env_name)
        elif test.name == 'Tauri Build Compatibility':
            return await self._test_tauri_build(environment, env_name)
        
        return {'status': 'unknown', 'score': 0.0, 'message': 'Test not implemented'}
    
    async def _test_python_imports(self, environment: PlatformEnvironment, env_name: str) -> Dict[str, Any]:
        """Test Python package imports"""
        
        critical_packages = [
            'asyncio', 'json', 'pathlib', 'sqlite3', 'requests',
            'psutil', 'datetime', 'subprocess', 'threading'
        ]
        
        import_results = {}
        
        for package in critical_packages:
            try:
                __import__(package)
                import_results[package] = True
            except ImportError:
                import_results[package] = False
        
        working_imports = len([r for r in import_results.values() if r])
        total_imports = len(critical_packages)
        score = working_imports / total_imports
        
        status = 'pass' if score == 1.0 else 'partial' if score > 0.8 else 'fail'
        
        return {
            'status': status,
            'score': score,
            'message': f'Python imports: {working_imports}/{total_imports} packages available',
            'details': import_results
        }
    
    async def _test_sqlite_access(self, environment: PlatformEnvironment, env_name: str) -> Dict[str, Any]:
        """Test SQLite database access"""
        
        try:
            import sqlite3
            
            # Test in-memory database
            conn = sqlite3.connect(':memory:')
            cursor = conn.cursor()
            
            # Test basic operations
            operations = {}
            
            try:
                cursor.execute('CREATE TABLE test (id INTEGER, name TEXT)')
                operations['create_table'] = True
            except:
                operations['create_table'] = False
            
            try:
                cursor.execute('INSERT INTO test VALUES (1, ?)', ('test',))
                operations['insert_data'] = True
            except:
                operations['insert_data'] = False
            
            try:
                cursor.execute('SELECT * FROM test')
                result = cursor.fetchone()
                operations['select_data'] = result is not None
            except:
                operations['select_data'] = False
            
            conn.close()
            
            working_ops = len([o for o in operations.values() if o])
            total_ops = len(operations)
            score = working_ops / total_ops
            
            status = 'pass' if score == 1.0 else 'partial' if score > 0.5 else 'fail'
            
            return {
                'status': status,
                'score': score,
                'message': f'SQLite access: {working_ops}/{total_ops} operations working',
                'details': operations
            }
            
        except ImportError:
            return {
                'status': 'fail',
                'score': 0.0,
                'message': 'SQLite module not available'
            }
    
    async def _test_nodejs_build(self, environment: PlatformEnvironment, env_name: str) -> Dict[str, Any]:
        """Test Node.js build process"""
        
        frontend_dir = self.project_root / 'frontend'
        if not frontend_dir.exists():
            return {
                'status': 'skip',
                'score': 0.0,
                'message': 'Frontend directory not found'
            }
        
        tests = {}
        
        # Check package.json
        package_json = frontend_dir / 'package.json'
        tests['package_json_exists'] = package_json.exists()
        
        # Check node_modules
        node_modules = frontend_dir / 'node_modules'
        tests['dependencies_installed'] = node_modules.exists()
        
        # Test build command availability
        try:
            if environment.type == 'wsl':
                # Test in WSL
                result = subprocess.run(
                    ['wsl', '--', 'which', 'npm'],
                    capture_output=True, text=True, timeout=10
                )
                tests['npm_available'] = result.returncode == 0
            else:
                # Test natively
                result = subprocess.run(['npm', '--version'], 
                                      capture_output=True, text=True, timeout=10)
                tests['npm_available'] = result.returncode == 0
        except:
            tests['npm_available'] = False
        
        working_tests = len([t for t in tests.values() if t])
        total_tests = len(tests)
        score = working_tests / total_tests
        
        status = 'pass' if score == 1.0 else 'partial' if score > 0.5 else 'fail'
        
        return {
            'status': status,
            'score': score,
            'message': f'Node.js build: {working_tests}/{total_tests} requirements met',
            'details': tests
        }
    
    async def _test_tauri_build(self, environment: PlatformEnvironment, env_name: str) -> Dict[str, Any]:
        """Test Tauri build compatibility"""
        
        if environment.os_family != 'windows' and environment.type != 'native':
            return {
                'status': 'skip',
                'score': 0.0,
                'message': 'Tauri builds require native environment'
            }
        
        tauri_dir = self.project_root / 'frontend/src-tauri'
        if not tauri_dir.exists():
            return {
                'status': 'skip',
                'score': 0.0,
                'message': 'Tauri directory not found'
            }
        
        tests = {}
        
        # Check Rust availability
        try:
            result = subprocess.run(['rustc', '--version'], 
                                  capture_output=True, text=True, timeout=10)
            tests['rust_available'] = result.returncode == 0
        except:
            tests['rust_available'] = False
        
        # Check Tauri config
        tauri_config = tauri_dir / 'tauri.conf.json'
        tests['tauri_config_exists'] = tauri_config.exists()
        
        # Check Cargo.toml
        cargo_toml = tauri_dir / 'Cargo.toml'
        tests['cargo_toml_exists'] = cargo_toml.exists()
        
        working_tests = len([t for t in tests.values() if t])
        total_tests = len(tests)
        score = working_tests / total_tests
        
        status = 'pass' if score == 1.0 else 'partial' if score > 0.5 else 'fail'
        
        return {
            'status': status,
            'score': score,
            'message': f'Tauri build: {working_tests}/{total_tests} requirements met',
            'details': tests
        }
    
    async def _test_network_compatibility(self, test: CompatibilityTest, 
                                        environment: PlatformEnvironment, 
                                        env_name: str) -> Dict[str, Any]:
        """Test network compatibility"""
        
        if test.name == 'Port Binding':
            return await self._test_port_binding(environment, env_name)
        elif test.name == 'External API Access':
            return await self._test_external_api_access(environment, env_name)
        
        return {'status': 'unknown', 'score': 0.0, 'message': 'Test not implemented'}
    
    async def _test_port_binding(self, environment: PlatformEnvironment, env_name: str) -> Dict[str, Any]:
        """Test port binding capability"""
        
        test_ports = [8000, 8001, 3000, 3001]
        port_results = {}
        
        for port in test_ports:
            try:
                # Try to bind to port
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
                sock.bind(('localhost', port))
                sock.listen(1)
                sock.close()
                
                port_results[port] = True
            except:
                port_results[port] = False
        
        working_ports = len([p for p in port_results.values() if p])
        total_ports = len(test_ports)
        score = working_ports / total_ports
        
        status = 'pass' if score > 0.8 else 'partial' if score > 0.5 else 'fail'
        
        return {
            'status': status,
            'score': score,
            'message': f'Port binding: {working_ports}/{total_ports} ports bindable',
            'details': port_results
        }
    
    async def _test_external_api_access(self, environment: PlatformEnvironment, env_name: str) -> Dict[str, Any]:
        """Test external API access"""
        
        test_endpoints = [
            'https://httpbin.org/status/200',
            'https://api.github.com',
            'https://jsonplaceholder.typicode.com/posts/1'
        ]
        
        access_results = {}
        
        for endpoint in test_endpoints:
            try:
                import requests
                response = requests.get(endpoint, timeout=10)
                access_results[endpoint] = response.status_code < 500
            except:
                access_results[endpoint] = False
        
        working_endpoints = len([r for r in access_results.values() if r])
        total_endpoints = len(test_endpoints)
        score = working_endpoints / total_endpoints
        
        status = 'pass' if score > 0.8 else 'partial' if score > 0.5 else 'fail'
        
        return {
            'status': status,
            'score': score,
            'message': f'External API access: {working_endpoints}/{total_endpoints} endpoints accessible',
            'details': access_results
        }
    
    async def _test_command_compatibility(self, test: CompatibilityTest, 
                                        environment: PlatformEnvironment, 
                                        env_name: str) -> Dict[str, Any]:
        """Test command execution compatibility"""
        
        if test.name == 'Security Tool Execution':
            return await self._test_security_tools(environment, env_name)
        elif test.name == 'Shell Script Compatibility':
            return await self._test_shell_scripts(environment, env_name)
        
        return {'status': 'unknown', 'score': 0.0, 'message': 'Test not implemented'}
    
    async def _test_security_tools(self, environment: PlatformEnvironment, env_name: str) -> Dict[str, Any]:
        """Test security tool execution"""
        
        security_tools = ['nmap', 'ping', 'curl']
        if environment.os_family == 'linux':
            security_tools.extend(['netcat', 'dig', 'whois'])
        
        tool_results = {}
        
        for tool in security_tools:
            try:
                if environment.type == 'wsl':
                    result = subprocess.run(
                        ['wsl', '--', 'which', tool],
                        capture_output=True, text=True, timeout=10
                    )
                else:
                    result = subprocess.run(
                        [tool, '--version'],
                        capture_output=True, text=True, timeout=10
                    )
                
                tool_results[tool] = result.returncode == 0
            except:
                tool_results[tool] = False
        
        working_tools = len([t for t in tool_results.values() if t])
        total_tools = len(security_tools)
        score = working_tools / total_tools
        
        status = 'pass' if score > 0.8 else 'partial' if score > 0.5 else 'fail'
        
        return {
            'status': status,
            'score': score,
            'message': f'Security tools: {working_tools}/{total_tools} tools available',
            'details': tool_results
        }
    
    async def _test_shell_scripts(self, environment: PlatformEnvironment, env_name: str) -> Dict[str, Any]:
        """Test shell script compatibility"""
        
        # Create simple test scripts
        try:
            with tempfile.TemporaryDirectory() as temp_dir:
                temp_path = Path(temp_dir)
                
                # Test bash script
                bash_script = temp_path / 'test.sh'
                bash_script.write_text('#!/bin/bash\necho "test successful"')
                
                script_results = {}
                
                if environment.os_family != 'windows':
                    try:
                        os.chmod(bash_script, 0o755)
                        
                        if environment.type == 'wsl':
                            result = subprocess.run(
                                ['wsl', '--', str(bash_script)],
                                capture_output=True, text=True, timeout=10
                            )
                        else:
                            result = subprocess.run(
                                [str(bash_script)],
                                capture_output=True, text=True, timeout=10
                            )
                        
                        script_results['bash'] = 'test successful' in result.stdout
                    except:
                        script_results['bash'] = False
                else:
                    # Test PowerShell script
                    ps_script = temp_path / 'test.ps1'
                    ps_script.write_text('Write-Output "test successful"')
                    
                    try:
                        result = subprocess.run(
                            ['powershell', '-File', str(ps_script)],
                            capture_output=True, text=True, timeout=10
                        )
                        script_results['powershell'] = 'test successful' in result.stdout
                    except:
                        script_results['powershell'] = False
                
                working_scripts = len([s for s in script_results.values() if s])
                total_scripts = len(script_results)
                score = working_scripts / total_scripts if total_scripts > 0 else 0
                
                status = 'pass' if score == 1.0 else 'partial' if score > 0.5 else 'fail'
                
                return {
                    'status': status,
                    'score': score,
                    'message': f'Shell scripts: {working_scripts}/{total_scripts} scripts working',
                    'details': script_results
                }
                
        except Exception as e:
            return {
                'status': 'error',
                'score': 0.0,
                'message': f'Shell script test failed: {str(e)}'
            }
    
    async def _test_process_compatibility(self, test: CompatibilityTest, 
                                        environment: PlatformEnvironment, 
                                        env_name: str) -> Dict[str, Any]:
        """Test process management compatibility"""
        
        if test.name == 'Process Monitoring':
            return await self._test_process_monitoring(environment, env_name)
        
        return {'status': 'unknown', 'score': 0.0, 'message': 'Test not implemented'}
    
    async def _test_process_monitoring(self, environment: PlatformEnvironment, env_name: str) -> Dict[str, Any]:
        """Test process monitoring capabilities"""
        
        try:
            import psutil
            
            monitoring_tests = {}
            
            # Test CPU monitoring
            try:
                cpu_percent = psutil.cpu_percent()
                monitoring_tests['cpu_monitoring'] = isinstance(cpu_percent, (int, float))
            except:
                monitoring_tests['cpu_monitoring'] = False
            
            # Test memory monitoring
            try:
                memory = psutil.virtual_memory()
                monitoring_tests['memory_monitoring'] = hasattr(memory, 'percent')
            except:
                monitoring_tests['memory_monitoring'] = False
            
            # Test process enumeration
            try:
                processes = list(psutil.process_iter(['pid', 'name']))
                monitoring_tests['process_enumeration'] = len(processes) > 0
            except:
                monitoring_tests['process_enumeration'] = False
            
            working_tests = len([t for t in monitoring_tests.values() if t])
            total_tests = len(monitoring_tests)
            score = working_tests / total_tests
            
            status = 'pass' if score == 1.0 else 'partial' if score > 0.5 else 'fail'
            
            return {
                'status': status,
                'score': score,
                'message': f'Process monitoring: {working_tests}/{total_tests} capabilities working',
                'details': monitoring_tests
            }
            
        except ImportError:
            return {
                'status': 'fail',
                'score': 0.0,
                'message': 'psutil module not available'
            }
    
    def _print_test_result(self, test: CompatibilityTest, result: Dict[str, Any]):
        """Print test result with color coding"""
        status_colors = {
            'pass': Colors.COMPAT,
            'partial': Colors.PARTIAL,
            'fail': Colors.INCOMPAT,
            'error': Colors.INCOMPAT,
            'skip': Colors.PARTIAL,
            'unknown': Colors.PLATFORM
        }
        
        status_symbols = {
            'pass': '✅',
            'partial': '🔶',
            'fail': '❌',
            'error': '💥',
            'skip': '⏭️',
            'unknown': '❓'
        }
        
        color = status_colors.get(result['status'], Colors.ENDC)
        symbol = status_symbols.get(result['status'], '?')
        
        print(f"    {symbol} {color}{result['message']} (Score: {result['score']:.2f}){Colors.ENDC}")
    
    def _generate_compatibility_summary(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """Generate compatibility summary"""
        
        total_environments = len(results)
        environment_scores = {}
        test_scores = {}
        
        # Calculate per-environment scores
        for env_name, env_results in results.items():
            if env_results:
                env_scores = [r['score'] for r in env_results.values()]
                environment_scores[env_name] = sum(env_scores) / len(env_scores) if env_scores else 0
        
        # Calculate per-test scores
        all_test_names = set()
        for env_results in results.values():
            all_test_names.update(env_results.keys())
        
        for test_name in all_test_names:
            test_results = [
                env_results.get(test_name, {}).get('score', 0)
                for env_results in results.values()
                if test_name in env_results
            ]
            test_scores[test_name] = sum(test_results) / len(test_results) if test_results else 0
        
        # Overall compatibility score
        all_scores = [score for score in environment_scores.values()]
        overall_score = sum(all_scores) / len(all_scores) if all_scores else 0
        
        # Compatibility level
        if overall_score > 0.9:
            compatibility_level = 'excellent'
        elif overall_score > 0.8:
            compatibility_level = 'good'
        elif overall_score > 0.6:
            compatibility_level = 'fair'
        else:
            compatibility_level = 'poor'
        
        return {
            'overall_score': overall_score,
            'compatibility_level': compatibility_level,
            'environment_scores': environment_scores,
            'test_scores': test_scores,
            'total_environments': total_environments,
            'fully_compatible_environments': len([s for s in environment_scores.values() if s > 0.9]),
            'problematic_environments': [env for env, score in environment_scores.items() if score < 0.6]
        }

class CrossPlatformCompatibilityTester:
    """🌐 Main cross-platform compatibility testing system"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.detector = PlatformDetector()
        self.environments = {}
        self.test_suite = None
        
        print(f"{Colors.PLATFORM}{Colors.BOLD}")
        print("🌐" * 50)
        print("    CROSS-PLATFORM COMPATIBILITY TESTER")
        print("    Advanced Multi-Environment Validation")
        print("🌐" * 50)
        print(f"{Colors.ENDC}")
    
    async def run_comprehensive_compatibility_analysis(self) -> Dict[str, Any]:
        """Run comprehensive cross-platform compatibility analysis"""
        print(f"{Colors.PLATFORM}🚀 Starting comprehensive compatibility analysis...{Colors.ENDC}")
        
        # Phase 1: Environment Detection
        print(f"\n{Colors.BOLD}Phase 1: Environment Detection{Colors.ENDC}")
        self.environments = self.detector.detect_all_environments()
        
        if not self.environments:
            return {
                'error': 'No compatible environments detected',
                'environments': {},
                'test_results': {}
            }
        
        # Phase 2: Initialize Test Suite
        print(f"\n{Colors.BOLD}Phase 2: Test Suite Initialization{Colors.ENDC}")
        self.test_suite = CompatibilityTestSuite(self.project_root, self.environments)
        
        # Phase 3: Run Compatibility Tests
        print(f"\n{Colors.BOLD}Phase 3: Compatibility Testing{Colors.ENDC}")
        test_results = await self.test_suite.run_all_tests()
        
        # Phase 4: Generate Report
        print(f"\n{Colors.BOLD}Phase 4: Analysis Report{Colors.ENDC}")
        report = await self._generate_comprehensive_report(test_results)
        
        return report
    
    async def _generate_comprehensive_report(self, test_results: Dict[str, Any]) -> Dict[str, Any]:
        """Generate comprehensive compatibility report"""
        
        print(f"\n{Colors.PLATFORM}{Colors.BOLD}")
        print("🌐" * 50)
        print("           CROSS-PLATFORM COMPATIBILITY REPORT")
        print("🌐" * 50)
        print(f"{Colors.ENDC}")
        
        summary = test_results['summary']
        
        # Overall compatibility assessment
        compat_color = {
            'excellent': Colors.COMPAT,
            'good': Colors.COMPAT,
            'fair': Colors.PARTIAL,
            'poor': Colors.INCOMPAT
        }.get(summary['compatibility_level'], Colors.PLATFORM)
        
        print(f"\n{Colors.BOLD}🎯 OVERALL COMPATIBILITY: {compat_color}{summary['compatibility_level'].upper()}{Colors.ENDC}")
        print(f"{Colors.BOLD}📊 COMPATIBILITY SCORE: {summary['overall_score']:.2f}/1.00{Colors.ENDC}")
        print(f"{Colors.BOLD}🏗️ ENVIRONMENTS TESTED: {summary['total_environments']}{Colors.ENDC}")
        print(f"{Colors.BOLD}✅ FULLY COMPATIBLE: {summary['fully_compatible_environments']}{Colors.ENDC}")
        
        # Environment-specific results
        print(f"\n{Colors.BOLD}🌐 ENVIRONMENT COMPATIBILITY BREAKDOWN:{Colors.ENDC}")
        for env_name, score in summary['environment_scores'].items():
            env_color = Colors.COMPAT if score > 0.8 else Colors.PARTIAL if score > 0.6 else Colors.INCOMPAT
            status = "Excellent" if score > 0.9 else "Good" if score > 0.8 else "Fair" if score > 0.6 else "Poor"
            print(f"  {env_color}{env_name:.<30} {score:.2f} ({status}){Colors.ENDC}")
        
        # Test-specific results
        print(f"\n{Colors.BOLD}🧪 TEST COMPATIBILITY BREAKDOWN:{Colors.ENDC}")
        for test_name, score in summary['test_scores'].items():
            test_color = Colors.COMPAT if score > 0.8 else Colors.PARTIAL if score > 0.6 else Colors.INCOMPAT
            print(f"  {test_color}{test_name:.<35} {score:.2f}{Colors.ENDC}")
        
        # Platform-specific recommendations
        print(f"\n{Colors.BOLD}💡 PLATFORM-SPECIFIC RECOMMENDATIONS:{Colors.ENDC}")
        
        recommendations = []
        
        if summary['problematic_environments']:
            recommendations.append(f"🔧 Address issues in: {', '.join(summary['problematic_environments'])}")
        
        if 'wsl' in str(summary['environment_scores']) and any('wsl' in env for env in summary['problematic_environments']):
            recommendations.append("🐧 Consider updating WSL distribution or installing missing packages")
        
        if summary['compatibility_level'] in ['poor', 'fair']:
            recommendations.append("⚠️ Consider focusing development on most compatible platform")
            recommendations.append("🔄 Implement platform-specific workarounds for critical functionality")
        
        if summary['overall_score'] < 0.8:
            recommendations.append("📋 Review and standardize cross-platform dependencies")
        
        for rec in recommendations[:5]:  # Show top 5 recommendations
            print(f"  {rec}")
        
        # Platform optimization suggestions
        print(f"\n{Colors.BOLD}⚡ OPTIMIZATION SUGGESTIONS:{Colors.ENDC}")
        
        best_environment = max(summary['environment_scores'].items(), key=lambda x: x[1])
        worst_environment = min(summary['environment_scores'].items(), key=lambda x: x[1])
        
        print(f"🏆 Best performing environment: {Colors.COMPAT}{best_environment[0]} ({best_environment[1]:.2f}){Colors.ENDC}")
        print(f"⚠️ Needs attention: {Colors.INCOMPAT}{worst_environment[0]} ({worst_environment[1]:.2f}){Colors.ENDC}")
        
        if best_environment[1] > 0.9:
            print(f"💡 Consider using {best_environment[0]} as primary development environment")
        
        print(f"\n{Colors.PLATFORM}🌐 Cross-platform compatibility analysis complete! 🌐{Colors.ENDC}")
        
        # Save detailed report
        report_file = self.project_root / f"cross_platform_compatibility_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        complete_report = {
            'environments': {name: env.__dict__ for name, env in self.environments.items()},
            'test_results': test_results,
            'summary': summary,
            'recommendations': recommendations
        }
        
        with open(report_file, 'w') as f:
            json.dump(complete_report, f, indent=2, default=str)
        
        print(f"\n📋 Detailed report saved to: {report_file}")
        
        return complete_report

async def main():
    """Main entry point"""
    tester = CrossPlatformCompatibilityTester()
    report = await tester.run_comprehensive_compatibility_analysis()
    return report

if __name__ == "__main__":
    asyncio.run(main())