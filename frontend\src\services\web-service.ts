/**
 * Web Service - Browser API integration
 * Replaces Electron service for web deployment
 * Provides unified interface to browser APIs in the web environment
 */

// ===== TYPE DEFINITIONS =====

export interface WebServiceAPI {
  // App info
  getName: () => string;
  getVersion: () => string;
  getPlatform: () => string;
  getArch: () => string;
  
  // Notifications
  showNotification: (title: string, options?: NotificationOptions) => Promise<void>;
  requestNotificationPermission: () => Promise<NotificationPermission>;
  
  // Storage
  getItem: (key: string) => string | null;
  setItem: (key: string, value: string) => void;
  removeItem: (key: string) => void;
  clear: () => void;
  
  // File operations
  downloadFile: (data: Blob, filename: string) => void;
  downloadText: (text: string, filename: string, mimeType?: string) => void;
  uploadFile: (accept?: string) => Promise<File | null>;
  uploadFiles: (accept?: string, multiple?: boolean) => Promise<FileList | null>;
  
  // Clipboard operations
  copyToClipboard: (text: string) => Promise<boolean>;
  readFromClipboard: () => Promise<string | null>;
  
  // URL operations
  openExternal: (url: string) => void;
  getCurrentUrl: () => string;
  
  // Browser detection
  getBrowserInfo: () => {
    name: string;
    version: string;
    userAgent: string;
  };
  
  // Feature detection
  supportsFeature: (feature: string) => boolean;
}

// ===== WEB SERVICE IMPLEMENTATION =====

class WebService implements WebServiceAPI {
  private readonly appName = 'NexusScan Web';
  private readonly appVersion = __APP_VERSION__ || '1.0.0';

  // ===== APP INFO =====

  getName(): string {
    return this.appName;
  }

  getVersion(): string {
    return this.appVersion;
  }

  getPlatform(): string {
    return navigator.platform || 'Web';
  }

  getArch(): string {
    // Detect architecture from user agent
    const ua = navigator.userAgent.toLowerCase();
    if (ua.includes('x64') || ua.includes('x86_64') || ua.includes('amd64')) {
      return 'x64';
    }
    if (ua.includes('arm64') || ua.includes('aarch64')) {
      return 'arm64';
    }
    if (ua.includes('arm')) {
      return 'arm';
    }
    return 'x86';
  }

  // ===== NOTIFICATIONS =====

  async requestNotificationPermission(): Promise<NotificationPermission> {
    if (!('Notification' in window)) {
      console.warn('This browser does not support notifications');
      return 'denied';
    }

    if (Notification.permission === 'granted') {
      return 'granted';
    }

    if (Notification.permission === 'denied') {
      return 'denied';
    }

    // Request permission
    const permission = await Notification.requestPermission();
    return permission;
  }

  async showNotification(title: string, options?: NotificationOptions): Promise<void> {
    if (!('Notification' in window)) {
      console.warn('Browser notifications not supported');
      return;
    }

    const permission = await this.requestNotificationPermission();
    
    if (permission === 'granted') {
      const notification = new Notification(title, {
        icon: '/icons/icon-192x192.png',
        badge: '/icons/icon-72x72.png',
        ...options
      });

      // Auto-close after 5 seconds
      setTimeout(() => {
        notification.close();
      }, 5000);
    } else {
      console.warn('Notification permission denied');
    }
  }

  // ===== STORAGE =====

  getItem(key: string): string | null {
    try {
      return localStorage.getItem(`nexusscan-${key}`);
    } catch (error) {
      console.error('Failed to get item from localStorage:', error);
      return null;
    }
  }

  setItem(key: string, value: string): void {
    try {
      localStorage.setItem(`nexusscan-${key}`, value);
    } catch (error) {
      console.error('Failed to set item in localStorage:', error);
    }
  }

  removeItem(key: string): void {
    try {
      localStorage.removeItem(`nexusscan-${key}`);
    } catch (error) {
      console.error('Failed to remove item from localStorage:', error);
    }
  }

  clear(): void {
    try {
      // Only clear NexusScan-specific items
      const keys = Object.keys(localStorage);
      keys.forEach(key => {
        if (key.startsWith('nexusscan-')) {
          localStorage.removeItem(key);
        }
      });
    } catch (error) {
      console.error('Failed to clear localStorage:', error);
    }
  }

  // ===== FILE OPERATIONS =====

  downloadFile(data: Blob, filename: string): void {
    try {
      const url = URL.createObjectURL(data);
      const a = document.createElement('a');
      a.href = url;
      a.download = filename;
      a.style.display = 'none';
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Failed to download file:', error);
    }
  }

  downloadText(text: string, filename: string, mimeType: string = 'text/plain'): void {
    const blob = new Blob([text], { type: mimeType });
    this.downloadFile(blob, filename);
  }

  async uploadFile(accept?: string): Promise<File | null> {
    return new Promise((resolve) => {
      try {
        const input = document.createElement('input');
        input.type = 'file';
        if (accept) {
          input.accept = accept;
        }
        input.style.display = 'none';
        
        input.onchange = (e) => {
          const file = (e.target as HTMLInputElement).files?.[0];
          resolve(file || null);
          document.body.removeChild(input);
        };
        
        input.oncancel = () => {
          resolve(null);
          document.body.removeChild(input);
        };
        
        document.body.appendChild(input);
        input.click();
      } catch (error) {
        console.error('Failed to upload file:', error);
        resolve(null);
      }
    });
  }

  async uploadFiles(accept?: string, multiple: boolean = false): Promise<FileList | null> {
    return new Promise((resolve) => {
      try {
        const input = document.createElement('input');
        input.type = 'file';
        input.multiple = multiple;
        if (accept) {
          input.accept = accept;
        }
        input.style.display = 'none';
        
        input.onchange = (e) => {
          const files = (e.target as HTMLInputElement).files;
          resolve(files);
          document.body.removeChild(input);
        };
        
        input.oncancel = () => {
          resolve(null);
          document.body.removeChild(input);
        };
        
        document.body.appendChild(input);
        input.click();
      } catch (error) {
        console.error('Failed to upload files:', error);
        resolve(null);
      }
    });
  }

  // ===== CLIPBOARD OPERATIONS =====

  async copyToClipboard(text: string): Promise<boolean> {
    try {
      if (navigator.clipboard && navigator.clipboard.writeText) {
        await navigator.clipboard.writeText(text);
        return true;
      } else {
        // Fallback for older browsers
        const textArea = document.createElement('textarea');
        textArea.value = text;
        textArea.style.position = 'fixed';
        textArea.style.left = '-999999px';
        textArea.style.top = '-999999px';
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();
        const result = document.execCommand('copy');
        document.body.removeChild(textArea);
        return result;
      }
    } catch (error) {
      console.error('Failed to copy to clipboard:', error);
      return false;
    }
  }

  async readFromClipboard(): Promise<string | null> {
    try {
      if (navigator.clipboard && navigator.clipboard.readText) {
        return await navigator.clipboard.readText();
      } else {
        console.warn('Clipboard read not supported in this browser');
        return null;
      }
    } catch (error) {
      console.error('Failed to read from clipboard:', error);
      return null;
    }
  }

  // ===== URL OPERATIONS =====

  openExternal(url: string): void {
    try {
      window.open(url, '_blank', 'noopener,noreferrer');
    } catch (error) {
      console.error('Failed to open external URL:', error);
    }
  }

  getCurrentUrl(): string {
    return window.location.href;
  }

  // ===== BROWSER DETECTION =====

  getBrowserInfo(): { name: string; version: string; userAgent: string } {
    const ua = navigator.userAgent;
    let name = 'Unknown';
    let version = 'Unknown';

    // Detect browser
    if (ua.includes('Chrome') && !ua.includes('Edg')) {
      name = 'Chrome';
      const match = ua.match(/Chrome\/(\d+)/);
      version = match ? match[1] : 'Unknown';
    } else if (ua.includes('Firefox')) {
      name = 'Firefox';
      const match = ua.match(/Firefox\/(\d+)/);
      version = match ? match[1] : 'Unknown';
    } else if (ua.includes('Safari') && !ua.includes('Chrome')) {
      name = 'Safari';
      const match = ua.match(/Version\/(\d+)/);
      version = match ? match[1] : 'Unknown';
    } else if (ua.includes('Edg')) {
      name = 'Edge';
      const match = ua.match(/Edg\/(\d+)/);
      version = match ? match[1] : 'Unknown';
    }

    return { name, version, userAgent: ua };
  }

  // ===== FEATURE DETECTION =====

  supportsFeature(feature: string): boolean {
    switch (feature) {
      case 'notifications':
        return 'Notification' in window;
      case 'serviceWorker':
        return 'serviceWorker' in navigator;
      case 'webGL':
        return !!document.createElement('canvas').getContext('webgl');
      case 'webGL2':
        return !!document.createElement('canvas').getContext('webgl2');
      case 'clipboard':
        return !!(navigator.clipboard && navigator.clipboard.writeText);
      case 'fullscreen':
        return !!(document.documentElement.requestFullscreen);
      case 'geolocation':
        return 'geolocation' in navigator;
      case 'localStorage':
        return 'localStorage' in window;
      case 'sessionStorage':
        return 'sessionStorage' in window;
      case 'indexedDB':
        return 'indexedDB' in window;
      case 'webWorkers':
        return 'Worker' in window;
      case 'webSockets':
        return 'WebSocket' in window;
      case 'fetch':
        return 'fetch' in window;
      default:
        return false;
    }
  }
}

// ===== EXPORT =====

export const webService = new WebService();
export default webService;
