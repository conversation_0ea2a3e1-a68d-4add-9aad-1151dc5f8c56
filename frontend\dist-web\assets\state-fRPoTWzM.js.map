{"version": 3, "file": "state-fRPoTWzM.js", "sources": ["../../node_modules/zustand/esm/vanilla.mjs", "../../node_modules/use-sync-external-store/cjs/use-sync-external-store-shim.production.js", "../../node_modules/use-sync-external-store/shim/index.js", "../../node_modules/use-sync-external-store/cjs/use-sync-external-store-shim/with-selector.production.js", "../../node_modules/use-sync-external-store/shim/with-selector.js", "../../node_modules/zustand/esm/index.mjs"], "sourcesContent": ["const createStoreImpl = (createState) => {\n  let state;\n  const listeners = /* @__PURE__ */ new Set();\n  const setState = (partial, replace) => {\n    const nextState = typeof partial === \"function\" ? partial(state) : partial;\n    if (!Object.is(nextState, state)) {\n      const previousState = state;\n      state = (replace != null ? replace : typeof nextState !== \"object\" || nextState === null) ? nextState : Object.assign({}, state, nextState);\n      listeners.forEach((listener) => listener(state, previousState));\n    }\n  };\n  const getState = () => state;\n  const getInitialState = () => initialState;\n  const subscribe = (listener) => {\n    listeners.add(listener);\n    return () => listeners.delete(listener);\n  };\n  const destroy = () => {\n    if ((import.meta.env ? import.meta.env.MODE : void 0) !== \"production\") {\n      console.warn(\n        \"[DEPRECATED] The `destroy` method will be unsupported in a future version. Instead use unsubscribe function returned by subscribe. Everything will be garbage-collected if store is garbage-collected.\"\n      );\n    }\n    listeners.clear();\n  };\n  const api = { setState, getState, getInitialState, subscribe, destroy };\n  const initialState = state = createState(setState, getState, api);\n  return api;\n};\nconst createStore = (createState) => createState ? createStoreImpl(createState) : createStoreImpl;\nvar vanilla = (createState) => {\n  if ((import.meta.env ? import.meta.env.MODE : void 0) !== \"production\") {\n    console.warn(\n      \"[DEPRECATED] Default export is deprecated. Instead use import { createStore } from 'zustand/vanilla'.\"\n    );\n  }\n  return createStore(createState);\n};\n\nexport { createStore, vanilla as default };\n", "/**\n * @license React\n * use-sync-external-store-shim.production.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\nvar React = require(\"react\");\nfunction is(x, y) {\n  return (x === y && (0 !== x || 1 / x === 1 / y)) || (x !== x && y !== y);\n}\nvar objectIs = \"function\" === typeof Object.is ? Object.is : is,\n  useState = React.useState,\n  useEffect = React.useEffect,\n  useLayoutEffect = React.useLayoutEffect,\n  useDebugValue = React.useDebugValue;\nfunction useSyncExternalStore$2(subscribe, getSnapshot) {\n  var value = getSnapshot(),\n    _useState = useState({ inst: { value: value, getSnapshot: getSnapshot } }),\n    inst = _useState[0].inst,\n    forceUpdate = _useState[1];\n  useLayoutEffect(\n    function () {\n      inst.value = value;\n      inst.getSnapshot = getSnapshot;\n      checkIfSnapshotChanged(inst) && forceUpdate({ inst: inst });\n    },\n    [subscribe, value, getSnapshot]\n  );\n  useEffect(\n    function () {\n      checkIfSnapshotChanged(inst) && forceUpdate({ inst: inst });\n      return subscribe(function () {\n        checkIfSnapshotChanged(inst) && forceUpdate({ inst: inst });\n      });\n    },\n    [subscribe]\n  );\n  useDebugValue(value);\n  return value;\n}\nfunction checkIfSnapshotChanged(inst) {\n  var latestGetSnapshot = inst.getSnapshot;\n  inst = inst.value;\n  try {\n    var nextValue = latestGetSnapshot();\n    return !objectIs(inst, nextValue);\n  } catch (error) {\n    return !0;\n  }\n}\nfunction useSyncExternalStore$1(subscribe, getSnapshot) {\n  return getSnapshot();\n}\nvar shim =\n  \"undefined\" === typeof window ||\n  \"undefined\" === typeof window.document ||\n  \"undefined\" === typeof window.document.createElement\n    ? useSyncExternalStore$1\n    : useSyncExternalStore$2;\nexports.useSyncExternalStore =\n  void 0 !== React.useSyncExternalStore ? React.useSyncExternalStore : shim;\n", "'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('../cjs/use-sync-external-store-shim.production.js');\n} else {\n  module.exports = require('../cjs/use-sync-external-store-shim.development.js');\n}\n", "/**\n * @license React\n * use-sync-external-store-shim/with-selector.production.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\nvar React = require(\"react\"),\n  shim = require(\"use-sync-external-store/shim\");\nfunction is(x, y) {\n  return (x === y && (0 !== x || 1 / x === 1 / y)) || (x !== x && y !== y);\n}\nvar objectIs = \"function\" === typeof Object.is ? Object.is : is,\n  useSyncExternalStore = shim.useSyncExternalStore,\n  useRef = React.useRef,\n  useEffect = React.useEffect,\n  useMemo = React.useMemo,\n  useDebugValue = React.useDebugValue;\nexports.useSyncExternalStoreWithSelector = function (\n  subscribe,\n  getSnapshot,\n  getServerSnapshot,\n  selector,\n  isEqual\n) {\n  var instRef = useRef(null);\n  if (null === instRef.current) {\n    var inst = { hasValue: !1, value: null };\n    instRef.current = inst;\n  } else inst = instRef.current;\n  instRef = useMemo(\n    function () {\n      function memoizedSelector(nextSnapshot) {\n        if (!hasMemo) {\n          hasMemo = !0;\n          memoizedSnapshot = nextSnapshot;\n          nextSnapshot = selector(nextSnapshot);\n          if (void 0 !== isEqual && inst.hasValue) {\n            var currentSelection = inst.value;\n            if (isEqual(currentSelection, nextSnapshot))\n              return (memoizedSelection = currentSelection);\n          }\n          return (memoizedSelection = nextSnapshot);\n        }\n        currentSelection = memoizedSelection;\n        if (objectIs(memoizedSnapshot, nextSnapshot)) return currentSelection;\n        var nextSelection = selector(nextSnapshot);\n        if (void 0 !== isEqual && isEqual(currentSelection, nextSelection))\n          return (memoizedSnapshot = nextSnapshot), currentSelection;\n        memoizedSnapshot = nextSnapshot;\n        return (memoizedSelection = nextSelection);\n      }\n      var hasMemo = !1,\n        memoizedSnapshot,\n        memoizedSelection,\n        maybeGetServerSnapshot =\n          void 0 === getServerSnapshot ? null : getServerSnapshot;\n      return [\n        function () {\n          return memoizedSelector(getSnapshot());\n        },\n        null === maybeGetServerSnapshot\n          ? void 0\n          : function () {\n              return memoizedSelector(maybeGetServerSnapshot());\n            }\n      ];\n    },\n    [getSnapshot, getServerSnapshot, selector, isEqual]\n  );\n  var value = useSyncExternalStore(subscribe, instRef[0], instRef[1]);\n  useEffect(\n    function () {\n      inst.hasValue = !0;\n      inst.value = value;\n    },\n    [value]\n  );\n  useDebugValue(value);\n  return value;\n};\n", "'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('../cjs/use-sync-external-store-shim/with-selector.production.js');\n} else {\n  module.exports = require('../cjs/use-sync-external-store-shim/with-selector.development.js');\n}\n", "import { createStore } from 'zustand/vanilla';\nexport * from 'zustand/vanilla';\nimport ReactExports from 'react';\nimport useSyncExternalStoreExports from 'use-sync-external-store/shim/with-selector.js';\n\nconst { useDebugValue } = ReactExports;\nconst { useSyncExternalStoreWithSelector } = useSyncExternalStoreExports;\nlet didWarnAboutEqualityFn = false;\nconst identity = (arg) => arg;\nfunction useStore(api, selector = identity, equalityFn) {\n  if ((import.meta.env ? import.meta.env.MODE : void 0) !== \"production\" && equalityFn && !didWarnAboutEqualityFn) {\n    console.warn(\n      \"[DEPRECATED] Use `createWithEqualityFn` instead of `create` or use `useStoreWithEqualityFn` instead of `useStore`. They can be imported from 'zustand/traditional'. https://github.com/pmndrs/zustand/discussions/1937\"\n    );\n    didWarnAboutEqualityFn = true;\n  }\n  const slice = useSyncExternalStoreWithSelector(\n    api.subscribe,\n    api.getState,\n    api.getServerState || api.getInitialState,\n    selector,\n    equalityFn\n  );\n  useDebugValue(slice);\n  return slice;\n}\nconst createImpl = (createState) => {\n  if ((import.meta.env ? import.meta.env.MODE : void 0) !== \"production\" && typeof createState !== \"function\") {\n    console.warn(\n      \"[DEPRECATED] Passing a vanilla store will be unsupported in a future version. Instead use `import { useStore } from 'zustand'`.\"\n    );\n  }\n  const api = typeof createState === \"function\" ? createStore(createState) : createState;\n  const useBoundStore = (selector, equalityFn) => useStore(api, selector, equalityFn);\n  Object.assign(useBoundStore, api);\n  return useBoundStore;\n};\nconst create = (createState) => createState ? createImpl(createState) : createImpl;\nvar react = (createState) => {\n  if ((import.meta.env ? import.meta.env.MODE : void 0) !== \"production\") {\n    console.warn(\n      \"[DEPRECATED] Default export is deprecated. Instead use `import { create } from 'zustand'`.\"\n    );\n  }\n  return create(createState);\n};\n\nexport { create, react as default, useStore };\n"], "names": ["createStoreImpl", "createState", "state", "listeners", "setState", "partial", "replace", "nextState", "previousState", "listener", "getState", "api", "initialState", "__vite_import_meta_env__", "createStore", "React", "require$$0", "is", "x", "y", "objectIs", "useState", "useEffect", "useLayoutEffect", "useDebugValue", "useSyncExternalStore$2", "subscribe", "getSnapshot", "value", "_useState", "inst", "forceUpdate", "checkIfSnapshotChanged", "latestGetSnapshot", "nextValue", "useSyncExternalStore$1", "shim", "useSyncExternalStoreShim_production", "shimModule", "require$$1", "useSyncExternalStore", "useRef", "useMemo", "withSelector_production", "getServerSnapshot", "selector", "isEqual", "instRef", "memoizedSelector", "nextSnapshot", "hasMemo", "memoizedSnapshot", "currentSelection", "memoizedSelection", "nextSelection", "maybeGetServerSnapshot", "withSelectorModule", "ReactExports", "useSyncExternalStoreWithSelector", "useSyncExternalStoreExports", "didWarnAboutEqualityFn", "identity", "arg", "useStore", "equalityFn", "slice", "createImpl", "useBoundStore", "create"], "mappings": "iEAAMA,EAAmBC,GAAgB,CACvC,IAAIC,EACJ,MAAMC,MAAgC,IAChCC,EAAW,CAACC,EAASC,IAAY,CACrC,MAAMC,EAAY,OAAOF,GAAY,WAAaA,EAAQH,CAAK,EAAIG,EACnE,GAAI,CAAC,OAAO,GAAGE,EAAWL,CAAK,EAAG,CAChC,MAAMM,EAAgBN,EACtBA,EAASI,IAA4B,OAAOC,GAAc,UAAYA,IAAc,MAAQA,EAAY,OAAO,OAAO,CAAA,EAAIL,EAAOK,CAAS,EAC1IJ,EAAU,QAASM,GAAaA,EAASP,EAAOM,CAAa,CAAC,CAChE,CACF,EACME,EAAW,IAAMR,EAcjBS,EAAM,CAAE,SAAAP,EAAU,SAAAM,EAAU,gBAbV,IAAME,EAaqB,UAZhCH,IACjBN,EAAU,IAAIM,CAAQ,EACf,IAAMN,EAAU,OAAOM,CAAQ,GAUsB,QAR9C,IAAM,EACfI,EAAkB,aAAuB,UAAY,cACxD,QAAQ,KACN,wMAAA,EAGJV,EAAU,MAAA,CACZ,CAC8D,EACxDS,EAAeV,EAAQD,EAAYG,EAAUM,EAAUC,CAAG,EAChE,OAAOA,CACT,EACMG,EAAeb,GAAgBA,EAAcD,EAAgBC,CAAW,EAAID;;;;;;;;wCClBlF,IAAIe,EAAQC,EAAA,EACZ,SAASC,EAAGC,EAAGC,EAAG,CAChB,OAAQD,IAAMC,IAAYD,IAAN,GAAW,EAAIA,IAAM,EAAIC,IAAQD,IAAMA,GAAKC,IAAMA,CACxE,CACA,IAAIC,EAA0B,OAAO,OAAO,IAA7B,WAAkC,OAAO,GAAKH,EAC3DI,EAAWN,EAAM,SACjBO,EAAYP,EAAM,UAClBQ,EAAkBR,EAAM,gBACxBS,EAAgBT,EAAM,cACxB,SAASU,EAAuBC,EAAWC,EAAa,CACtD,IAAIC,EAAQD,EAAW,EACrBE,EAAYR,EAAS,CAAE,KAAM,CAAE,MAAOO,EAAO,YAAaD,CAAW,EAAI,EACzEG,EAAOD,EAAU,CAAC,EAAE,KACpBE,EAAcF,EAAU,CAAC,EAC3B,OAAAN,EACE,UAAY,CACVO,EAAK,MAAQF,EACbE,EAAK,YAAcH,EACnBK,EAAuBF,CAAI,GAAKC,EAAY,CAAE,KAAMD,CAAI,CAAE,CAChE,EACI,CAACJ,EAAWE,EAAOD,CAAW,GAEhCL,EACE,UAAY,CACV,OAAAU,EAAuBF,CAAI,GAAKC,EAAY,CAAE,KAAMD,CAAI,CAAE,EACnDJ,EAAU,UAAY,CAC3BM,EAAuBF,CAAI,GAAKC,EAAY,CAAE,KAAMD,CAAI,CAAE,CAClE,CAAO,CACP,EACI,CAACJ,CAAS,GAEZF,EAAcI,CAAK,EACZA,CACT,CACA,SAASI,EAAuBF,EAAM,CACpC,IAAIG,EAAoBH,EAAK,YAC7BA,EAAOA,EAAK,MACZ,GAAI,CACF,IAAII,EAAYD,EAAiB,EACjC,MAAO,CAACb,EAASU,EAAMI,CAAS,CACpC,MAAkB,CACd,MAAO,EACX,CACA,CACA,SAASC,EAAuBT,EAAWC,EAAa,CACtD,OAAOA,EAAW,CACpB,CACA,IAAIS,EACc,OAAO,OAAvB,KACgB,OAAO,OAAO,SAA9B,KACgB,OAAO,OAAO,SAAS,cAAvC,IACID,EACAV,EACN,OAAAY,EAAA,qBACatB,EAAM,uBAAjB,OAAwCA,EAAM,qBAAuBqB,sCC9DrEE,EAAA,QAAiBtB,EAAA;;;;;;;;wCCQnB,IAAID,EAAQC,EAAA,EACVoB,EAAOG,EAAA,EACT,SAAStB,EAAGC,EAAGC,EAAG,CAChB,OAAQD,IAAMC,IAAYD,IAAN,GAAW,EAAIA,IAAM,EAAIC,IAAQD,IAAMA,GAAKC,IAAMA,CACxE,CACA,IAAIC,EAA0B,OAAO,OAAO,IAA7B,WAAkC,OAAO,GAAKH,EAC3DuB,EAAuBJ,EAAK,qBAC5BK,EAAS1B,EAAM,OACfO,EAAYP,EAAM,UAClB2B,EAAU3B,EAAM,QAChBS,EAAgBT,EAAM,cACxB,OAAA4B,EAAA,iCAA2C,SACzCjB,EACAC,EACAiB,EACAC,EACAC,EACA,CACA,IAAIC,EAAUN,EAAO,IAAI,EACzB,GAAaM,EAAQ,UAAjB,KAA0B,CAC5B,IAAIjB,EAAO,CAAE,SAAU,GAAI,MAAO,IAAI,EACtCiB,EAAQ,QAAUjB,CACtB,MAASA,EAAOiB,EAAQ,QACtBA,EAAUL,EACR,UAAY,CACV,SAASM,EAAiBC,EAAc,CACtC,GAAI,CAACC,EAAS,CAIZ,GAHAA,EAAU,GACVC,EAAmBF,EACnBA,EAAeJ,EAASI,CAAY,EACrBH,IAAX,QAAsBhB,EAAK,SAAU,CACvC,IAAIsB,EAAmBtB,EAAK,MAC5B,GAAIgB,EAAQM,EAAkBH,CAAY,EACxC,OAAQI,EAAoBD,CAC1C,CACU,OAAQC,EAAoBJ,CACtC,CAEQ,GADAG,EAAmBC,EACfjC,EAAS+B,EAAkBF,CAAY,EAAG,OAAOG,EACrD,IAAIE,EAAgBT,EAASI,CAAY,EACzC,OAAeH,IAAX,QAAsBA,EAAQM,EAAkBE,CAAa,GACvDH,EAAmBF,EAAeG,IAC5CD,EAAmBF,EACXI,EAAoBC,EACpC,CACM,IAAIJ,EAAU,GACZC,EACAE,EACAE,EACaX,IAAX,OAA+B,KAAOA,EAC1C,MAAO,CACL,UAAY,CACV,OAAOI,EAAiBrB,GAAa,CAC/C,EACiB4B,IAAT,KACI,OACA,UAAY,CACV,OAAOP,EAAiBO,GAAwB,CAC9D,EAEA,EACI,CAAC5B,EAAaiB,EAAmBC,EAAUC,CAAO,GAEpD,IAAIlB,EAAQY,EAAqBd,EAAWqB,EAAQ,CAAC,EAAGA,EAAQ,CAAC,CAAC,EAClE,OAAAzB,EACE,UAAY,CACVQ,EAAK,SAAW,GAChBA,EAAK,MAAQF,CACnB,EACI,CAACA,CAAK,GAERJ,EAAcI,CAAK,EACZA,CACT,sCCjFE4B,EAAA,QAAiBxC,EAAA,yCCEb,CAAE,cAAAQ,GAAkBiC,EACpB,CAAE,iCAAAC,GAAqCC,EAC7C,IAAIC,EAAyB,GAC7B,MAAMC,EAAYC,GAAQA,EAC1B,SAASC,EAASpD,EAAKkC,EAAWgB,EAAUG,EAAY,EACjDnD,EAAkB,aAAuB,UAAY,cAAgBmD,GAAc,CAACJ,IACvF,QAAQ,KACN,wNAAA,EAEFA,EAAyB,IAE3B,MAAMK,EAAQP,EACZ/C,EAAI,UACJA,EAAI,SACJA,EAAI,gBAAkBA,EAAI,gBAC1BkC,EACAmB,CAAA,EAEF,OAAAxC,EAAcyC,CAAK,EACZA,CACT,CACA,MAAMC,EAAcjE,GAAgB,EAC7BY,EAAkB,aAAuB,UAAY,cAAgB,OAAOZ,GAAgB,YAC/F,QAAQ,KACN,iIAAA,EAGJ,MAAMU,EAAM,OAAOV,GAAgB,WAAaa,EAAYb,CAAW,EAAIA,EACrEkE,EAAgB,CAACtB,EAAUmB,IAAeD,EAASpD,EAAKkC,EAAUmB,CAAU,EAClF,cAAO,OAAOG,EAAexD,CAAG,EACzBwD,CACT,EACMC,EAAUnE,GAAwDiE", "x_google_ignoreList": [0, 1, 2, 3, 4, 5]}