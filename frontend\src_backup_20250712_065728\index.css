@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

/* Base styles */
@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 210 40% 98%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
  
  /* Scrollbar styles for desktop */
  ::-webkit-scrollbar {
    @apply w-2 h-2;
  }
  
  ::-webkit-scrollbar-track {
    @apply bg-muted/30;
  }
  
  ::-webkit-scrollbar-thumb {
    @apply bg-muted-foreground/30 rounded-full;
  }
  
  ::-webkit-scrollbar-thumb:hover {
    @apply bg-muted-foreground/50;
  }
  
  /* Selection colors */
  ::selection {
    @apply bg-primary/20 text-primary-foreground;
  }
}

/* Component styles */
@layer components {
  /* Terminal styles */
  .terminal {
    @apply terminal-bg terminal-text terminal-scrollbar;
    min-height: 200px;
    resize: vertical;
    overflow-y: auto;
  }
  
  .terminal-output {
    @apply font-mono text-sm leading-relaxed;
    white-space: pre-wrap;
    word-break: break-word;
  }
  
  /* Code blocks */
  .code-block {
    @apply bg-muted/50 border border-border rounded-lg p-4 font-mono text-sm overflow-x-auto;
  }
  
  /* Status indicators */
  .status-indicator {
    @apply inline-flex items-center gap-2 px-2 py-1 rounded-full text-xs font-medium;
  }
  
  .status-online {
    @apply bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400;
  }
  
  .status-offline {
    @apply bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400;
  }
  
  .status-warning {
    @apply bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400;
  }
  
  .status-loading {
    @apply bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400;
  }
  
  /* Tool cards */
  .tool-card {
    @apply bg-card border border-border rounded-lg p-4 hover:border-primary/50 transition-colors;
  }
  
  .tool-card-active {
    @apply border-primary bg-primary/5;
  }
  
  /* Progress bars */
  .progress-bar {
    @apply w-full bg-muted rounded-full h-2 overflow-hidden;
  }
  
  .progress-fill {
    @apply h-full bg-primary transition-all duration-300 ease-out;
  }
  
  /* Severity badges */
  .severity-badge {
    @apply inline-flex items-center px-2 py-1 rounded-full text-xs font-medium;
  }
  
  /* Navigation styles */
  .nav-item {
    @apply flex items-center gap-3 px-3 py-2 rounded-lg text-sm font-medium transition-colors;
  }
  
  .nav-item-active {
    @apply bg-primary/10 text-primary;
  }
  
  .nav-item-inactive {
    @apply text-muted-foreground hover:text-foreground hover:bg-muted/50;
  }
  
  /* Form styles */
  .form-section {
    @apply space-y-4 p-4 border border-border rounded-lg;
  }
  
  .form-section-title {
    @apply text-sm font-semibold text-foreground border-b border-border pb-2;
  }
  
  /* Data tables */
  .data-table {
    @apply w-full border-collapse;
  }
  
  .data-table th {
    @apply bg-muted/50 border border-border px-4 py-3 text-left text-sm font-medium text-muted-foreground;
  }
  
  .data-table td {
    @apply border border-border px-4 py-3 text-sm;
  }
  
  .data-table tr:nth-child(even) {
    @apply bg-muted/20;
  }
  
  .data-table tr:hover {
    @apply bg-muted/40;
  }
  
  /* Loading animations */
  .loading-spinner {
    @apply animate-spin h-4 w-4 border-2 border-primary border-t-transparent rounded-full;
  }
  
  .loading-dots::after {
    content: '';
    animation: loading-dots 1.5s infinite;
  }
  
  @keyframes loading-dots {
    0%, 20% { content: '.'; }
    40% { content: '..'; }
    60%, 100% { content: '...'; }
  }
  
  /* Focus styles for accessibility */
  .focus-ring {
    @apply focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 focus:ring-offset-background;
  }
  
  /* Gradient backgrounds */
  .gradient-primary {
    background: linear-gradient(135deg, hsl(var(--primary)) 0%, hsl(var(--primary)) 100%);
  }
  
  .gradient-nexus {
    background: linear-gradient(135deg, #0ea5e9 0%, #3b82f7 50%, #8b5cf6 100%);
  }
  
  /* Glass morphism effect */
  .glass {
    @apply bg-background/80 backdrop-blur-sm border border-border/50;
  }
  
  /* Responsive utilities */
  .container-desktop {
    @apply max-w-7xl mx-auto px-6;
  }
  
  .grid-responsive {
    @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6;
  }
}

/* Utility classes */
@layer utilities {
  /* Text utilities */
  .text-balance {
    text-wrap: balance;
  }
  
  /* Animation utilities */
  .animate-in {
    animation: animate-in 0.3s ease-out;
  }
  
  .animate-out {
    animation: animate-out 0.3s ease-out;
  }
  
  @keyframes animate-in {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  @keyframes animate-out {
    from {
      opacity: 1;
      transform: translateY(0);
    }
    to {
      opacity: 0;
      transform: translateY(-10px);
    }
  }
  
  /* Layout utilities */
  .full-bleed {
    width: 100vw;
    margin-left: calc(50% - 50vw);
  }
  
  .safe-area-inset {
    padding-top: env(safe-area-inset-top);
    padding-right: env(safe-area-inset-right);
    padding-bottom: env(safe-area-inset-bottom);
    padding-left: env(safe-area-inset-left);
  }
  
  /* Hide scrollbar but keep functionality */
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }
  
  .print-break-before {
    break-before: page;
  }
  
  .print-break-after {
    break-after: page;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .border {
    @apply border-2;
  }
  
  .text-muted-foreground {
    @apply text-foreground;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .animate-spin {
    animation: none;
  }
  
  .animate-pulse {
    animation: none;
  }
  
  .animate-bounce {
    animation: none;
  }
  
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Theme-specific styles */
.dark .monaco-editor {
  background-color: rgb(var(--background)) !important;
}

.dark .monaco-editor .margin {
  background-color: rgb(var(--muted)) !important;
}

/* Custom component overrides */
.sonner-toaster {
  --normal-bg: hsl(var(--card));
  --normal-border: hsl(var(--border));
  --normal-text: hsl(var(--card-foreground));
}

/* Loading screen overrides (for better theming) */
.loading-container {
  background: linear-gradient(135deg, #0c4a6e 0%, #075985 50%, #0369a1 100%) !important;
}