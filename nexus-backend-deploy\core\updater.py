#!/usr/bin/env python3
"""
NexusScan Desktop Auto-Updater System
Handles application updates with security and verification
"""

import os
import sys
import json
import hashlib
import tempfile
import subprocess
from pathlib import Path
from typing import Dict, Optional, Tuple, List
import requests
from datetime import datetime, timedelta
import threading
import time
import logging
from dataclasses import dataclass

from .config import ConfigManager
from .events import EventSystem


@dataclass
class UpdateInfo:
    """Information about an available update"""
    version: str
    build_date: str
    download_url: str
    checksum: str
    changelog: str
    size: int
    critical: bool = False
    release_notes_url: Optional[str] = None


class UpdateChecker:
    """Checks for application updates"""
    
    def __init__(self, config_manager: ConfigManager):
        self.config = config_manager
        self.logger = logging.getLogger(__name__)
        
        # Update configuration
        self.update_server = "https://api.nexscn.com/updates"
        self.current_version = "1.0.0"  # Will be read from config
        self.check_interval = 24 * 3600  # 24 hours in seconds
        
        # Cache
        self.last_check = None
        self.cached_update_info = None
        
    def get_current_version(self) -> str:
        """Get current application version"""
        try:
            version = self.config.get("app.version", self.current_version)
            return version
        except Exception as e:
            self.logger.warning(f"Could not get version from config: {e}")
            return self.current_version
    
    def should_check_for_updates(self) -> bool:
        """Determine if we should check for updates now"""
        # Check if auto-update is enabled
        if not self.config.get("updates.auto_check", True):
            return False
        
        # Check if enough time has passed since last check
        if self.last_check:
            time_since_check = time.time() - self.last_check
            if time_since_check < self.check_interval:
                return False
        
        return True
    
    def check_for_updates(self) -> Optional[UpdateInfo]:
        """Check for available updates"""
        if not self.should_check_for_updates():
            return self.cached_update_info
        
        try:
            self.logger.info("Checking for updates...")
            
            # Prepare request
            current_version = self.get_current_version()
            platform = sys.platform
            
            headers = {
                "User-Agent": f"NexusScan-Desktop/{current_version} ({platform})",
                "X-Current-Version": current_version,
                "X-Platform": platform,
            }
            
            # Make request to update server
            response = requests.get(
                f"{self.update_server}/check",
                headers=headers,
                timeout=30
            )
            response.raise_for_status()
            
            update_data = response.json()
            self.last_check = time.time()
            
            # Parse update information
            if update_data.get("update_available", False):
                update_info = UpdateInfo(
                    version=update_data["version"],
                    build_date=update_data["build_date"],
                    download_url=update_data["download_url"],
                    checksum=update_data["checksum"],
                    changelog=update_data.get("changelog", ""),
                    size=update_data.get("size", 0),
                    critical=update_data.get("critical", False),
                    release_notes_url=update_data.get("release_notes_url")
                )
                
                self.cached_update_info = update_info
                self.logger.info(f"Update available: {update_info.version}")
                return update_info
            else:
                self.logger.info("No updates available")
                self.cached_update_info = None
                return None
                
        except requests.RequestException as e:
            self.logger.error(f"Failed to check for updates: {e}")
            return None
        except Exception as e:
            self.logger.error(f"Update check error: {e}")
            return None
    
    def is_version_newer(self, version1: str, version2: str) -> bool:
        """Compare two version strings"""
        try:
            def version_tuple(v):
                return tuple(map(int, v.split('.')))
            
            return version_tuple(version1) > version_tuple(version2)
        except:
            return False


class UpdateDownloader:
    """Downloads and verifies updates"""
    
    def __init__(self, config_manager: ConfigManager):
        self.config = config_manager
        self.logger = logging.getLogger(__name__)
        
        # Download configuration
        self.download_dir = Path(tempfile.gettempdir()) / "nexusscan_updates"
        self.download_dir.mkdir(exist_ok=True)
        
        # Progress tracking
        self.download_progress = 0.0
        self.download_speed = 0.0
        self.download_eta = 0
        
    def download_update(self, update_info: UpdateInfo, 
                       progress_callback=None) -> Optional[Path]:
        """Download update file with progress tracking"""
        try:
            self.logger.info(f"Downloading update: {update_info.version}")
            
            # Prepare download
            filename = f"nexusscan-{update_info.version}-update.exe"
            if sys.platform == "darwin":
                filename = f"nexusscan-{update_info.version}-update.dmg"
            elif sys.platform.startswith("linux"):
                filename = f"nexusscan-{update_info.version}-update.AppImage"
            
            download_path = self.download_dir / filename
            
            # Download with progress tracking
            response = requests.get(update_info.download_url, stream=True)
            response.raise_for_status()
            
            total_size = int(response.headers.get('content-length', 0))
            downloaded = 0
            start_time = time.time()
            
            with open(download_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)
                        downloaded += len(chunk)
                        
                        # Calculate progress
                        if total_size > 0:
                            self.download_progress = (downloaded / total_size) * 100
                        
                        # Calculate speed and ETA
                        elapsed = time.time() - start_time
                        if elapsed > 0:
                            self.download_speed = downloaded / elapsed
                            if self.download_speed > 0:
                                remaining = total_size - downloaded
                                self.download_eta = remaining / self.download_speed
                        
                        # Call progress callback
                        if progress_callback:
                            progress_callback(self.download_progress, 
                                            self.download_speed, 
                                            self.download_eta)
            
            # Verify checksum
            if not self.verify_checksum(download_path, update_info.checksum):
                self.logger.error("Checksum verification failed")
                download_path.unlink()
                return None
            
            self.logger.info(f"Update downloaded successfully: {download_path}")
            return download_path
            
        except Exception as e:
            self.logger.error(f"Failed to download update: {e}")
            return None
    
    def verify_checksum(self, file_path: Path, expected_checksum: str) -> bool:
        """Verify file checksum"""
        try:
            sha256_hash = hashlib.sha256()
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    sha256_hash.update(chunk)
            
            calculated_checksum = sha256_hash.hexdigest()
            return calculated_checksum.lower() == expected_checksum.lower()
            
        except Exception as e:
            self.logger.error(f"Checksum verification error: {e}")
            return False


class UpdateInstaller:
    """Installs downloaded updates"""
    
    def __init__(self, config_manager: ConfigManager):
        self.config = config_manager
        self.logger = logging.getLogger(__name__)
        
    def install_update(self, update_file: Path, update_info: UpdateInfo) -> bool:
        """Install the downloaded update"""
        try:
            self.logger.info(f"Installing update: {update_info.version}")
            
            if sys.platform == "win32":
                return self.install_windows_update(update_file, update_info)
            elif sys.platform == "darwin":
                return self.install_macos_update(update_file, update_info)
            else:  # Linux
                return self.install_linux_update(update_file, update_info)
                
        except Exception as e:
            self.logger.error(f"Failed to install update: {e}")
            return False
    
    def install_windows_update(self, update_file: Path, update_info: UpdateInfo) -> bool:
        """Install Windows update"""
        try:
            # Create update script
            script_path = update_file.parent / "update_script.bat"
            
            script_content = f"""@echo off
echo Installing NexusScan Desktop Update v{update_info.version}...
echo.

REM Wait for main application to close
timeout /t 5 /nobreak > nul

REM Run installer silently
"{update_file}" /S

REM Clean up
del "{update_file}"
del "%~f0"
"""
            
            with open(script_path, "w") as f:
                f.write(script_content)
            
            # Launch update script
            subprocess.Popen([str(script_path)], creationflags=subprocess.CREATE_NEW_CONSOLE)
            
            self.logger.info("Windows update script launched")
            return True
            
        except Exception as e:
            self.logger.error(f"Windows update installation failed: {e}")
            return False
    
    def install_macos_update(self, update_file: Path, update_info: UpdateInfo) -> bool:
        """Install macOS update"""
        try:
            # Mount DMG and copy application
            script_content = f"""#!/bin/bash
echo "Installing NexusScan Desktop Update v{update_info.version}..."

# Wait for main application to close
sleep 5

# Mount DMG
hdiutil attach "{update_file}"

# Copy application
cp -R "/Volumes/NexusScan Desktop/NexusScan.app" "/Applications/"

# Unmount DMG
hdiutil detach "/Volumes/NexusScan Desktop"

# Clean up
rm "{update_file}"
rm "$0"
"""
            
            script_path = update_file.parent / "update_script.sh"
            with open(script_path, "w") as f:
                f.write(script_content)
            
            os.chmod(script_path, 0o755)
            
            # Launch update script
            subprocess.Popen([str(script_path)])
            
            self.logger.info("macOS update script launched")
            return True
            
        except Exception as e:
            self.logger.error(f"macOS update installation failed: {e}")
            return False
    
    def install_linux_update(self, update_file: Path, update_info: UpdateInfo) -> bool:
        """Install Linux update"""
        try:
            # For AppImage, just replace the current executable
            current_exe = Path(sys.executable)
            
            script_content = f"""#!/bin/bash
echo "Installing NexusScan Desktop Update v{update_info.version}..."

# Wait for main application to close
sleep 5

# Make new version executable
chmod +x "{update_file}"

# Replace current executable
cp "{update_file}" "{current_exe}"

# Clean up
rm "{update_file}"
rm "$0"
"""
            
            script_path = update_file.parent / "update_script.sh"
            with open(script_path, "w") as f:
                f.write(script_content)
            
            os.chmod(script_path, 0o755)
            
            # Launch update script
            subprocess.Popen([str(script_path)])
            
            self.logger.info("Linux update script launched")
            return True
            
        except Exception as e:
            self.logger.error(f"Linux update installation failed: {e}")
            return False


class AutoUpdater:
    """Main auto-updater system"""
    
    def __init__(self, config_manager: ConfigManager, event_system: EventSystem):
        self.config = config_manager
        self.events = event_system
        self.logger = logging.getLogger(__name__)
        
        # Components
        self.checker = UpdateChecker(config_manager)
        self.downloader = UpdateDownloader(config_manager)
        self.installer = UpdateInstaller(config_manager)
        
        # State
        self.checking_for_updates = False
        self.downloading_update = False
        self.installing_update = False
        
        # Background thread
        self.background_thread = None
        self.stop_background_checks = False
        
    def start_background_checks(self):
        """Start background update checking"""
        if self.background_thread and self.background_thread.is_alive():
            return
        
        self.stop_background_checks = False
        self.background_thread = threading.Thread(target=self._background_check_loop)
        self.background_thread.daemon = True
        self.background_thread.start()
        
        self.logger.info("Background update checks started")
    
    def stop_background_checks(self):
        """Stop background update checking"""
        self.stop_background_checks = True
        if self.background_thread:
            self.background_thread.join(timeout=5)
        
        self.logger.info("Background update checks stopped")
    
    def _background_check_loop(self):
        """Background thread loop for checking updates"""
        while not self.stop_background_checks:
            try:
                # Check for updates
                if self.checker.should_check_for_updates():
                    update_info = self.checker.check_for_updates()
                    
                    if update_info:
                        # Emit update available event
                        self.events.emit("update_available", {
                            "version": update_info.version,
                            "critical": update_info.critical,
                            "changelog": update_info.changelog
                        })
                
                # Sleep for 1 hour before next check
                for _ in range(3600):  # 1 hour in seconds
                    if self.stop_background_checks:
                        break
                    time.sleep(1)
                    
            except Exception as e:
                self.logger.error(f"Background update check error: {e}")
                time.sleep(60)  # Wait 1 minute before retrying
    
    def check_for_updates_now(self) -> Optional[UpdateInfo]:
        """Manually check for updates"""
        if self.checking_for_updates:
            return None
        
        try:
            self.checking_for_updates = True
            self.events.emit("update_check_started")
            
            update_info = self.checker.check_for_updates()
            
            self.events.emit("update_check_completed", {
                "update_available": update_info is not None
            })
            
            return update_info
            
        finally:
            self.checking_for_updates = False
    
    def download_and_install_update(self, update_info: UpdateInfo, 
                                   auto_install: bool = False) -> bool:
        """Download and optionally install update"""
        if self.downloading_update or self.installing_update:
            return False
        
        try:
            # Download update
            self.downloading_update = True
            self.events.emit("update_download_started", {
                "version": update_info.version,
                "size": update_info.size
            })
            
            def progress_callback(progress, speed, eta):
                self.events.emit("update_download_progress", {
                    "progress": progress,
                    "speed": speed,
                    "eta": eta
                })
            
            update_file = self.downloader.download_update(update_info, progress_callback)
            
            if not update_file:
                self.events.emit("update_download_failed")
                return False
            
            self.events.emit("update_download_completed", {
                "file_path": str(update_file)
            })
            
            # Install update if requested
            if auto_install:
                self.installing_update = True
                self.events.emit("update_installation_started")
                
                success = self.installer.install_update(update_file, update_info)
                
                if success:
                    self.events.emit("update_installation_completed")
                    
                    # Schedule application restart
                    self.events.emit("application_restart_requested")
                else:
                    self.events.emit("update_installation_failed")
                
                return success
            
            return True
            
        except Exception as e:
            self.logger.error(f"Update download/install error: {e}")
            self.events.emit("update_error", {"error": str(e)})
            return False
            
        finally:
            self.downloading_update = False
            self.installing_update = False
    
    def get_update_settings(self) -> Dict:
        """Get current update settings"""
        return {
            "auto_check": self.config.get("updates.auto_check", True),
            "auto_download": self.config.get("updates.auto_download", False),
            "auto_install": self.config.get("updates.auto_install", False),
            "include_beta": self.config.get("updates.include_beta", False),
            "check_interval": self.config.get("updates.check_interval", 24),
        }
    
    def update_settings(self, settings: Dict):
        """Update auto-updater settings"""
        for key, value in settings.items():
            self.config.set(f"updates.{key}", value)
        
        self.config.save()
        self.logger.info("Update settings saved")
    
    def get_status(self) -> Dict:
        """Get current updater status"""
        return {
            "checking": self.checking_for_updates,
            "downloading": self.downloading_update,
            "installing": self.installing_update,
            "background_checks_enabled": not self.stop_background_checks,
            "last_check": self.checker.last_check,
            "current_version": self.checker.get_current_version(),
        }