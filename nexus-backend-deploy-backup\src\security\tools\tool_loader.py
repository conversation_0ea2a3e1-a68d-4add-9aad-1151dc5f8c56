#!/usr/bin/env python3
"""
Tool Loader for NexusScan Desktop
Automatically loads and registers all security tools
"""

import logging
import importlib
import os
from typing import List, Dict, Any

from security.tools.tool_registry import tool_registry

logger = logging.getLogger(__name__)


class ToolLoader:
    """Loads and registers all security tools"""
    
    def __init__(self):
        self.loaded_tools = []
        self.failed_imports = []
    
    def load_all_tools(self) -> Dict[str, Any]:
        """Load all available security tools"""
        logger.info("Loading security tools...")
        
        # Core scanners
        self._load_core_scanners()
        
        # AI tools
        self._load_ai_tools()
        
        # External tool wrappers
        self._load_external_tools()
        
        # Custom tools
        self._load_custom_tools()
        
        # Compliance tools
        self._load_compliance_tools()
        
        # Report loading results
        total_tools = len(self.loaded_tools)
        available_tools = len(tool_registry.get_available_tools())
        
        loading_report = {
            "total_loaded": total_tools,
            "available_tools": available_tools,
            "loaded_tools": self.loaded_tools,
            "failed_imports": self.failed_imports,
            "registry_status": "success" if total_tools > 0 else "failed"
        }
        
        logger.info(f"Tool loading complete: {total_tools} tools loaded, {available_tools} available")
        
        if self.failed_imports:
            logger.warning(f"Failed to import {len(self.failed_imports)} tools: {self.failed_imports}")
        
        return loading_report
    
    def _load_core_scanners(self):
        """Load core scanner tools"""
        core_scanners = [
            "security.tools.nmap_scanner",
            "security.tools.nuclei_scanner", 
            "security.tools.sqlmap_scanner",
            "security.tools.nikto_scanner",
            "security.tools.dirb_scanner",
            "security.tools.gobuster_scanner"
        ]
        
        for scanner_module in core_scanners:
            self._safe_import(scanner_module, "core_scanner")
    
    def _load_ai_tools(self):
        """Load AI-powered tools"""
        ai_tools = [
            "security.tools.custom.ai_tools.vulnerability_agent",
            "security.tools.attack_surface_mapper",
            "security.tools.threat_model_generator",
            "security.tools.risk_calculator",
            "security.tools.security_posture_analyzer",
            "security.tools.ai_payload_optimizer_wrapper",
            "security.tools.ai_vulnerability_discovery_wrapper",
            "security.tools.ai_exploit_chaining_wrapper",
            "security.tools.creative_exploit_generator_wrapper",
            "security.tools.behavioral_analysis_wrapper",
            "security.tools.multi_stage_orchestrator_wrapper"
        ]
        
        for ai_tool_module in ai_tools:
            self._safe_import(ai_tool_module, "ai_tool")
    
    def _load_external_tools(self):
        """Load external tool wrappers"""
        external_tools = [
            "security.tools.external_tool_wrapper",
            "security.tools.wpscan_wrapper",
            "security.tools.ffuf_wrapper",
            "security.tools.feroxbuster_wrapper",
            "security.tools.enum4linux_wrapper",
            "security.tools.sslyze_wrapper",
            "security.tools.metasploit_wrapper",
            "security.tools.searchsploit_wrapper",
            "security.tools.whatweb_wrapper",
            "security.tools.testssl_wrapper",
            "security.tools.hashcat_wrapper",
            "security.tools.infrastructure_tools"
        ]
        
        for external_tool_module in external_tools:
            self._safe_import(external_tool_module, "external_tool")
    
    def _load_custom_tools(self):
        """Load custom tool implementations"""
        custom_tools = [
            "security.tools.environment_detector",
            "security.tools.health_checker",
            "security.tools.performance_monitor",
            "security.tools.resource_optimizer",
            "security.tools.ai_reconnaissance_wrapper",
            "security.tools.tech_fingerprinter_wrapper"
        ]
        
        for custom_tool_module in custom_tools:
            self._safe_import(custom_tool_module, "custom_tool")
            
        logger.info("Custom tool loading completed")
    
    def _load_compliance_tools(self):
        """Load compliance checking tools"""
        compliance_tools = [
            "security.tools.pci_dss_checker",
            "security.tools.gdpr_compliance_checker",
            "security.tools.iso27001_checker",
            "security.tools.compliance_tester_wrapper"
        ]
        
        for compliance_tool_module in compliance_tools:
            self._safe_import(compliance_tool_module, "compliance_tool")
    
    def _safe_import(self, module_name: str, tool_type: str):
        """Safely import a tool module"""
        try:
            importlib.import_module(module_name)
            self.loaded_tools.append({
                "module": module_name,
                "type": tool_type,
                "status": "success"
            })
            logger.debug(f"Successfully loaded {tool_type}: {module_name}")
        except ImportError as e:
            self.failed_imports.append({
                "module": module_name,
                "type": tool_type,
                "error": str(e)
            })
            logger.warning(f"Failed to import {tool_type} {module_name}: {e}")
        except Exception as e:
            self.failed_imports.append({
                "module": module_name,
                "type": tool_type,
                "error": str(e)
            })
            logger.error(f"Error loading {tool_type} {module_name}: {e}")
    
    def get_tool_status_report(self) -> Dict[str, Any]:
        """Get comprehensive tool status report"""
        all_tools = tool_registry.list_all_tools()
        available_tools = tool_registry.get_available_tools()
        
        return {
            "registry_tools": len(all_tools),
            "available_tools": len(available_tools),
            "unavailable_tools": len(all_tools) - len(available_tools),
            "loaded_modules": len(self.loaded_tools),
            "failed_imports": len(self.failed_imports),
            "tools_by_category": self._get_tools_by_category(),
            "availability_details": {
                tool_name: tool_info.get("available", False)
                for tool_name, tool_info in all_tools.items()
            }
        }
    
    def _get_tools_by_category(self) -> Dict[str, List[str]]:
        """Get tools organized by category"""
        all_tools = tool_registry.list_all_tools()
        by_category = {}
        
        for tool_name, tool_info in all_tools.items():
            category = tool_info.get("metadata", {}).get("category", "unknown")
            if category not in by_category:
                by_category[category] = []
            by_category[category].append(tool_name)
        
        return by_category


# Global tool loader instance
tool_loader = ToolLoader()


def initialize_tools() -> Dict[str, Any]:
    """Initialize all security tools"""
    return tool_loader.load_all_tools()


def get_tools_status() -> Dict[str, Any]:
    """Get current tools status"""
    return tool_loader.get_tool_status_report()