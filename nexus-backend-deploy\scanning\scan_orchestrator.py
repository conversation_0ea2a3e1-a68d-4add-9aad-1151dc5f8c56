#!/usr/bin/env python3
"""
Scan Orchestrator for NexusScan Security Scanning Engine
Manages and coordinates multiple security scanning tools
"""

import asyncio
import logging
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Callable, Union
from dataclasses import dataclass, field
from enum import Enum
from concurrent.futures import Thr<PERSON><PERSON>oolExecutor, ProcessPoolExecutor
import json

# Note: ToolManager functionality provided by security.unified_tool_manager  
# from .tool_manager import <PERSON><PERSON>Mana<PERSON>, Tool<PERSON>onfig, <PERSON>lResult, ToolStatus
from .progress_tracker import ProgressTracker, ProgressUpdate, ProgressStatus
from .result_processor import ResultProcessor, ProcessedResult, ToolConfig, ToolResult, ToolManager

logger = logging.getLogger(__name__)


class ScanType(Enum):
    """Types of security scans"""
    NETWORK = "network"
    WEB = "web"
    API = "api"
    MOBILE = "mobile"
    INFRASTRUCTURE = "infrastructure"
    COMPREHENSIVE = "comprehensive"


class ScanStatus(Enum):
    """Scan execution status"""
    PENDING = "pending"
    INITIALIZING = "initializing"
    RUNNING = "running"
    PAUSED = "paused"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    TIMEOUT = "timeout"


class ScanPriority(Enum):
    """Scan priority levels"""
    LOW = "low"
    NORMAL = "normal"
    HIGH = "high"
    CRITICAL = "critical"


@dataclass
class ScanRequest:
    """Request structure for security scans"""
    scan_id: str
    name: str
    scan_type: ScanType
    targets: List[str]
    tools_config: Dict[str, ToolConfig]
    priority: ScanPriority = ScanPriority.NORMAL
    timeout_seconds: int = 3600
    max_concurrent_tools: int = 3
    scan_template: Optional[str] = None
    custom_options: Dict[str, Any] = field(default_factory=dict)
    callback_url: Optional[str] = None
    user_context: Dict[str, Any] = field(default_factory=dict)


@dataclass
class ScanProgress:
    """Real-time scan progress information"""
    scan_id: str
    status: ScanStatus
    overall_progress: float  # 0.0 to 1.0
    current_tool: Optional[str]
    current_target: Optional[str]
    tools_completed: int
    tools_total: int
    targets_completed: int
    targets_total: int
    start_time: datetime
    estimated_completion: Optional[datetime]
    elapsed_time: timedelta
    errors: List[str] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)


@dataclass
class ScanResult:
    """Complete scan execution result"""
    scan_id: str
    request: ScanRequest
    status: ScanStatus
    start_time: datetime
    end_time: Optional[datetime]
    execution_time: timedelta
    tool_results: Dict[str, ToolResult]
    processed_results: Optional[ProcessedResult]
    vulnerabilities_found: int
    success_rate: float
    errors: List[str] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)


class ScanOrchestrator:
    """Orchestrates security scans across multiple tools and targets"""

    def __init__(self, 
                 tool_manager: ToolManager,
                 result_processor: ResultProcessor,
                 max_concurrent_scans: int = 5,
                 default_timeout: int = 3600):
        """Initialize scan orchestrator
        
        Args:
            tool_manager: Tool manager instance
            result_processor: Result processor instance
            max_concurrent_scans: Maximum concurrent scans
            default_timeout: Default scan timeout in seconds
        """
        self.tool_manager = tool_manager
        self.result_processor = result_processor
        self.max_concurrent_scans = max_concurrent_scans
        self.default_timeout = default_timeout
        
        # Active scans tracking
        self.active_scans: Dict[str, ScanResult] = {}
        self.scan_progress: Dict[str, ScanProgress] = {}
        self.progress_trackers: Dict[str, ProgressTracker] = {}
        
        # Execution pools
        self.thread_pool = ThreadPoolExecutor(max_workers=max_concurrent_scans * 2)
        self.process_pool = ProcessPoolExecutor(max_workers=max_concurrent_scans)
        
        # Callbacks
        self.progress_callbacks: Dict[str, List[Callable]] = {}
        self.completion_callbacks: Dict[str, List[Callable]] = {}
        
        self.logger = logging.getLogger(__name__)
        
        # Statistics
        self.stats = {
            "total_scans": 0,
            "successful_scans": 0,
            "failed_scans": 0,
            "total_execution_time": 0.0,
            "average_scan_time": 0.0,
            "tools_executed": 0,
            "vulnerabilities_found": 0
        }

    async def submit_scan(self, request: ScanRequest) -> str:
        """Submit a new scan for execution
        
        Args:
            request: Scan request configuration
            
        Returns:
            Scan ID for tracking
            
        Raises:
            ValueError: If scan configuration is invalid
            RuntimeError: If scan queue is full
        """
        # Validate request
        self._validate_scan_request(request)
        
        # Check concurrency limits
        if len(self.active_scans) >= self.max_concurrent_scans:
            raise RuntimeError("Maximum concurrent scans exceeded")
        
        # Initialize scan tracking
        scan_result = ScanResult(
            scan_id=request.scan_id,
            request=request,
            status=ScanStatus.PENDING,
            start_time=datetime.now(),
            end_time=None,
            execution_time=timedelta(),
            tool_results={},
            processed_results=None,
            vulnerabilities_found=0,
            success_rate=0.0
        )
        
        scan_progress = ScanProgress(
            scan_id=request.scan_id,
            status=ScanStatus.PENDING,
            overall_progress=0.0,
            current_tool=None,
            current_target=None,
            tools_completed=0,
            tools_total=len(request.tools_config),
            targets_completed=0,
            targets_total=len(request.targets),
            start_time=datetime.now(),
            estimated_completion=None,
            elapsed_time=timedelta()
        )
        
        # Store in tracking dictionaries
        self.active_scans[request.scan_id] = scan_result
        self.scan_progress[request.scan_id] = scan_progress
        self.progress_trackers[request.scan_id] = ProgressTracker(request.scan_id)
        
        # Start scan execution asynchronously
        asyncio.create_task(self._execute_scan(request))
        
        self.logger.info(f"Submitted scan: {request.scan_id} ({request.scan_type.value})")
        return request.scan_id

    async def _execute_scan(self, request: ScanRequest):
        """Execute a complete security scan
        
        Args:
            request: Scan request to execute
        """
        scan_id = request.scan_id
        start_time = datetime.now()
        
        try:
            # Update status to initializing
            await self._update_scan_status(scan_id, ScanStatus.INITIALIZING)
            
            # Initialize scan environment
            await self._initialize_scan(request)
            
            # Update status to running
            await self._update_scan_status(scan_id, ScanStatus.RUNNING)
            
            # Execute scan phases
            await self._execute_scan_phases(request)
            
            # Process and aggregate results
            await self._process_scan_results(request)
            
            # Complete scan
            await self._complete_scan(request, ScanStatus.COMPLETED)
            
        except asyncio.TimeoutError:
            self.logger.error(f"Scan {scan_id} timed out")
            await self._complete_scan(request, ScanStatus.TIMEOUT)
            
        except asyncio.CancelledError:
            self.logger.warning(f"Scan {scan_id} was cancelled")
            await self._complete_scan(request, ScanStatus.CANCELLED)
            
        except Exception as e:
            self.logger.error(f"Scan {scan_id} failed: {e}")
            await self._complete_scan(request, ScanStatus.FAILED, str(e))

    async def _initialize_scan(self, request: ScanRequest):
        """Initialize scan environment and validate tools
        
        Args:
            request: Scan request to initialize
        """
        scan_id = request.scan_id
        
        # Validate all tools are available
        for tool_name, tool_config in request.tools_config.items():
            if not await self.tool_manager.is_tool_available(tool_name):
                raise RuntimeError(f"Tool '{tool_name}' is not available")
        
        # Initialize progress tracker
        tracker = self.progress_trackers[scan_id]
        await tracker.initialize(
            total_phases=len(request.tools_config),
            total_targets=len(request.targets)
        )
        
        # Setup scan workspace
        await self._setup_scan_workspace(request)
        
        self.logger.info(f"Initialized scan environment for {scan_id}")

    async def _execute_scan_phases(self, request: ScanRequest):
        """Execute all scan phases with tools
        
        Args:
            request: Scan request to execute
        """
        scan_id = request.scan_id
        tool_results = {}
        
        # Sort tools by execution order (if specified)
        tools_order = self._get_tools_execution_order(request.tools_config)
        
        for phase_idx, (tool_name, tool_config) in enumerate(tools_order):
            try:
                # Update progress
                await self._update_scan_progress(scan_id, current_tool=tool_name)
                
                # Execute tool across all targets
                tool_result = await self._execute_tool_phase(
                    request, tool_name, tool_config, phase_idx
                )
                
                tool_results[tool_name] = tool_result
                
                # Update completed tools count
                progress = self.scan_progress[scan_id]
                progress.tools_completed += 1
                progress.overall_progress = progress.tools_completed / progress.tools_total
                
                await self._notify_progress_callbacks(scan_id)
                
            except Exception as e:
                self.logger.error(f"Tool {tool_name} failed in scan {scan_id}: {e}")
                
                # Create failed result
                tool_results[tool_name] = ToolResult(
                    tool_name=tool_name,
                    status=ToolStatus.FAILED,
                    start_time=datetime.now(),
                    end_time=datetime.now(),
                    targets=[],
                    raw_output="",
                    error_message=str(e),
                    exit_code=-1,
                    findings=[]
                )
                
                # Continue with other tools unless it's critical
                if tool_config.critical:
                    raise
        
        # Store tool results
        self.active_scans[scan_id].tool_results = tool_results

    async def _execute_tool_phase(self, 
                                request: ScanRequest, 
                                tool_name: str, 
                                tool_config: ToolConfig,
                                phase_idx: int) -> ToolResult:
        """Execute a specific tool across targets
        
        Args:
            request: Scan request
            tool_name: Name of tool to execute
            tool_config: Tool configuration
            phase_idx: Phase index for progress tracking
            
        Returns:
            Tool execution result
        """
        scan_id = request.scan_id
        
        # Prepare tool execution parameters
        execution_params = {
            "scan_id": scan_id,
            "tool_config": tool_config,
            "targets": request.targets,
            "timeout": request.timeout_seconds,
            "user_context": request.user_context
        }
        
        # Execute tool with timeout
        try:
            tool_result = await asyncio.wait_for(
                self.tool_manager.execute_tool(tool_name, execution_params),
                timeout=tool_config.timeout or request.timeout_seconds
            )
            
            # Update target progress as tool completes targets
            await self._update_target_progress(scan_id, len(request.targets))
            
            return tool_result
            
        except asyncio.TimeoutError:
            raise RuntimeError(f"Tool {tool_name} execution timed out")

    async def _process_scan_results(self, request: ScanRequest):
        """Process and aggregate scan results
        
        Args:
            request: Scan request with results to process
        """
        scan_id = request.scan_id
        scan_result = self.active_scans[scan_id]
        
        # Process results through result processor
        processed_results = await self.result_processor.process_scan_results(
            scan_id=scan_id,
            tool_results=scan_result.tool_results,
            scan_type=request.scan_type,
            targets=request.targets
        )
        
        # Update scan result with processed data
        scan_result.processed_results = processed_results
        scan_result.vulnerabilities_found = len(processed_results.vulnerabilities)
        
        # Calculate success rate
        total_tools = len(request.tools_config)
        successful_tools = sum(1 for result in scan_result.tool_results.values() 
                             if result.status == ToolStatus.COMPLETED)
        scan_result.success_rate = successful_tools / total_tools if total_tools > 0 else 0.0
        
        self.logger.info(f"Processed results for scan {scan_id}: "
                        f"{scan_result.vulnerabilities_found} vulnerabilities found")

    async def _complete_scan(self, request: ScanRequest, status: ScanStatus, error: str = None):
        """Complete scan execution and cleanup
        
        Args:
            request: Scan request that completed
            status: Final scan status
            error: Error message if failed
        """
        scan_id = request.scan_id
        end_time = datetime.now()
        
        # Update scan result
        scan_result = self.active_scans[scan_id]
        scan_result.status = status
        scan_result.end_time = end_time
        scan_result.execution_time = end_time - scan_result.start_time
        
        if error:
            scan_result.errors.append(error)
        
        # Update progress
        progress = self.scan_progress[scan_id]
        progress.status = status
        progress.overall_progress = 1.0
        progress.elapsed_time = end_time - progress.start_time
        
        # Update statistics
        self._update_scan_statistics(scan_result)
        
        # Notify completion callbacks
        await self._notify_completion_callbacks(scan_id, scan_result)
        
        # Cleanup scan workspace
        await self._cleanup_scan_workspace(request)
        
        self.logger.info(f"Completed scan {scan_id} with status: {status.value}")

    async def get_scan_progress(self, scan_id: str) -> Optional[ScanProgress]:
        """Get current scan progress
        
        Args:
            scan_id: Scan ID to get progress for
            
        Returns:
            Current scan progress or None if not found
        """
        return self.scan_progress.get(scan_id)

    async def get_scan_result(self, scan_id: str) -> Optional[ScanResult]:
        """Get scan result
        
        Args:
            scan_id: Scan ID to get result for
            
        Returns:
            Scan result or None if not found
        """
        return self.active_scans.get(scan_id)

    async def cancel_scan(self, scan_id: str) -> bool:
        """Cancel a running scan
        
        Args:
            scan_id: Scan ID to cancel
            
        Returns:
            True if cancelled successfully
        """
        if scan_id not in self.active_scans:
            return False
        
        scan_result = self.active_scans[scan_id]
        if scan_result.status not in [ScanStatus.RUNNING, ScanStatus.INITIALIZING]:
            return False
        
        # Cancel all active tool executions
        await self.tool_manager.cancel_all_executions(scan_id)
        
        # Update status
        await self._update_scan_status(scan_id, ScanStatus.CANCELLED)
        
        self.logger.info(f"Cancelled scan: {scan_id}")
        return True

    async def pause_scan(self, scan_id: str) -> bool:
        """Pause a running scan
        
        Args:
            scan_id: Scan ID to pause
            
        Returns:
            True if paused successfully
        """
        if scan_id not in self.active_scans:
            return False
        
        scan_result = self.active_scans[scan_id]
        if scan_result.status != ScanStatus.RUNNING:
            return False
        
        # Pause tool executions
        await self.tool_manager.pause_executions(scan_id)
        
        # Update status
        await self._update_scan_status(scan_id, ScanStatus.PAUSED)
        
        self.logger.info(f"Paused scan: {scan_id}")
        return True

    async def resume_scan(self, scan_id: str) -> bool:
        """Resume a paused scan
        
        Args:
            scan_id: Scan ID to resume
            
        Returns:
            True if resumed successfully
        """
        if scan_id not in self.active_scans:
            return False
        
        scan_result = self.active_scans[scan_id]
        if scan_result.status != ScanStatus.PAUSED:
            return False
        
        # Resume tool executions
        await self.tool_manager.resume_executions(scan_id)
        
        # Update status
        await self._update_scan_status(scan_id, ScanStatus.RUNNING)
        
        self.logger.info(f"Resumed scan: {scan_id}")
        return True

    def add_progress_callback(self, scan_id: str, callback: Callable):
        """Add progress update callback
        
        Args:
            scan_id: Scan ID to add callback for
            callback: Callback function
        """
        if scan_id not in self.progress_callbacks:
            self.progress_callbacks[scan_id] = []
        self.progress_callbacks[scan_id].append(callback)

    def add_completion_callback(self, scan_id: str, callback: Callable):
        """Add scan completion callback
        
        Args:
            scan_id: Scan ID to add callback for
            callback: Callback function
        """
        if scan_id not in self.completion_callbacks:
            self.completion_callbacks[scan_id] = []
        self.completion_callbacks[scan_id].append(callback)

    def get_scan_statistics(self) -> Dict[str, Any]:
        """Get scanning engine statistics
        
        Returns:
            Dictionary containing scanning statistics
        """
        return self.stats.copy()

    def list_active_scans(self) -> List[str]:
        """Get list of active scan IDs
        
        Returns:
            List of active scan IDs
        """
        return list(self.active_scans.keys())

    # Private helper methods

    def _validate_scan_request(self, request: ScanRequest):
        """Validate scan request configuration"""
        if not request.scan_id:
            raise ValueError("Scan ID is required")
        
        if request.scan_id in self.active_scans:
            raise ValueError(f"Scan ID {request.scan_id} already exists")
        
        if not request.targets:
            raise ValueError("At least one target is required")
        
        if not request.tools_config:
            raise ValueError("At least one tool configuration is required")
        
        if request.timeout_seconds <= 0:
            raise ValueError("Timeout must be positive")

    def _get_tools_execution_order(self, tools_config: Dict[str, ToolConfig]) -> List[tuple]:
        """Get tools in execution order"""
        # Sort by priority (higher priority first), then by order if specified
        tools_list = list(tools_config.items())
        
        def sort_key(item):
            tool_name, config = item
            priority_order = {
                "high": 0,
                "medium": 1, 
                "low": 2
            }
            return (
                priority_order.get(getattr(config, 'priority', 'medium'), 1),
                getattr(config, 'execution_order', 999),
                tool_name
            )
        
        return sorted(tools_list, key=sort_key)

    async def _update_scan_status(self, scan_id: str, status: ScanStatus):
        """Update scan status"""
        if scan_id in self.active_scans:
            self.active_scans[scan_id].status = status
        
        if scan_id in self.scan_progress:
            self.scan_progress[scan_id].status = status

    async def _update_scan_progress(self, scan_id: str, **kwargs):
        """Update scan progress"""
        if scan_id not in self.scan_progress:
            return
        
        progress = self.scan_progress[scan_id]
        
        for key, value in kwargs.items():
            if hasattr(progress, key):
                setattr(progress, key, value)
        
        # Update elapsed time
        progress.elapsed_time = datetime.now() - progress.start_time
        
        # Update estimated completion
        if progress.overall_progress > 0:
            total_time = progress.elapsed_time.total_seconds() / progress.overall_progress
            remaining_time = total_time - progress.elapsed_time.total_seconds()
            progress.estimated_completion = datetime.now() + timedelta(seconds=remaining_time)

    async def _update_target_progress(self, scan_id: str, targets_completed: int):
        """Update target completion progress"""
        if scan_id in self.scan_progress:
            self.scan_progress[scan_id].targets_completed = targets_completed

    async def _notify_progress_callbacks(self, scan_id: str):
        """Notify progress callbacks"""
        if scan_id not in self.progress_callbacks:
            return
        
        progress = self.scan_progress.get(scan_id)
        if not progress:
            return
        
        for callback in self.progress_callbacks[scan_id]:
            try:
                await callback(progress)
            except Exception as e:
                self.logger.error(f"Progress callback error: {e}")

    async def _notify_completion_callbacks(self, scan_id: str, result: ScanResult):
        """Notify completion callbacks"""
        if scan_id not in self.completion_callbacks:
            return
        
        for callback in self.completion_callbacks[scan_id]:
            try:
                await callback(result)
            except Exception as e:
                self.logger.error(f"Completion callback error: {e}")

    async def _setup_scan_workspace(self, request: ScanRequest):
        """Setup workspace for scan execution"""
        # Create temporary directories, setup logging, etc.
        pass

    async def _cleanup_scan_workspace(self, request: ScanRequest):
        """Cleanup scan workspace"""
        # Remove temporary files, cleanup resources, etc.
        pass

    def _update_scan_statistics(self, scan_result: ScanResult):
        """Update global scanning statistics"""
        self.stats["total_scans"] += 1
        
        if scan_result.status == ScanStatus.COMPLETED:
            self.stats["successful_scans"] += 1
        else:
            self.stats["failed_scans"] += 1
        
        execution_seconds = scan_result.execution_time.total_seconds()
        self.stats["total_execution_time"] += execution_seconds
        self.stats["average_scan_time"] = (
            self.stats["total_execution_time"] / self.stats["total_scans"]
        )
        
        self.stats["tools_executed"] += len(scan_result.tool_results)
        self.stats["vulnerabilities_found"] += scan_result.vulnerabilities_found