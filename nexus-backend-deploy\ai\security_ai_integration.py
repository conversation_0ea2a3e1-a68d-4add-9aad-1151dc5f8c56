#!/usr/bin/env python3
"""
Security AI Integration
Advanced integration between AI orchestration and security tools,
providing AI-enhanced security analysis and automated workflow orchestration
"""

import asyncio
import logging
import json
import time
from typing import Any, Dict, List, Optional, Tuple, Union
from dataclasses import dataclass, field, asdict
from datetime import datetime, timed<PERSON>ta
from enum import Enum

from .orchestrator import AIOrchestrator, AIRequest, TaskType, get_ai_orchestrator
from .model_manager import ModelManager, get_model_manager
from ..security.unified_tool_manager import UnifiedToolManager
from ..core.events import EventManager, EventTypes

logger = logging.getLogger(__name__)

class SecurityAnalysisType(Enum):
    """Types of security analysis"""
    VULNERABILITY_ASSESSMENT = "vulnerability_assessment"
    THREAT_DETECTION = "threat_detection"
    MALWARE_ANALYSIS = "malware_analysis"
    NETWORK_ANALYSIS = "network_analysis"
    WEB_APPLICATION_SCAN = "web_application_scan"
    COMPLIANCE_CHECK = "compliance_check"
    INCIDENT_RESPONSE = "incident_response"
    PENETRATION_TEST = "penetration_test"

class AnalysisPriority(Enum):
    """Analysis priority levels"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

@dataclass
class SecurityContext:
    """Security analysis context"""
    target: str
    scan_type: SecurityAnalysisType
    priority: AnalysisPriority
    metadata: Dict[str, Any] = field(default_factory=dict)
    constraints: Dict[str, Any] = field(default_factory=dict)
    custom_prompts: Dict[str, str] = field(default_factory=dict)

@dataclass
class AISecurityResult:
    """AI-enhanced security analysis result"""
    analysis_id: str
    context: SecurityContext
    raw_results: Dict[str, Any]
    ai_analysis: str
    confidence_score: float
    recommendations: List[str]
    risk_score: float
    mitigation_steps: List[str]
    compliance_notes: List[str] = field(default_factory=list)
    timestamp: datetime = field(default_factory=datetime.now)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return asdict(self)

class SecurityAIIntegration:
    """AI-enhanced security analysis integration"""
    
    def __init__(self, 
                 tool_manager: Optional[UnifiedToolManager] = None,
                 ai_orchestrator: Optional[AIOrchestrator] = None,
                 model_manager: Optional[ModelManager] = None,
                 event_manager: Optional[EventManager] = None):
        
        self.tool_manager = tool_manager
        self.ai_orchestrator = ai_orchestrator or get_ai_orchestrator()
        self.model_manager = model_manager or get_model_manager()
        self.event_manager = event_manager
        
        # Analysis templates
        self.analysis_templates = self._load_analysis_templates()
        
        # Performance tracking
        self.analysis_metrics = {
            "total_analyses": 0,
            "successful_analyses": 0,
            "average_analysis_time": 0.0,
            "ai_enhancement_rate": 0.0
        }
    
    def _load_analysis_templates(self) -> Dict[SecurityAnalysisType, Dict[str, str]]:
        """Load AI analysis templates for different security analysis types"""
        return {
            SecurityAnalysisType.VULNERABILITY_ASSESSMENT: {
                "prompt_template": """
Analyze the following vulnerability scan results and provide a comprehensive security assessment:

Target: {target}
Scan Results: {scan_results}

Please provide:
1. Critical vulnerabilities and their potential impact
2. Risk prioritization with CVSS scoring
3. Exploitation scenarios and attack vectors
4. Detailed remediation recommendations
5. Compliance implications (if applicable)

Focus on actionable insights and business impact assessment.
""",
                "model_preference": "vulnerability_analyzer"
            },
            
            SecurityAnalysisType.THREAT_DETECTION: {
                "prompt_template": """
Analyze the following security events and network traffic for threat indicators:

Target: {target}
Events: {events}
Network Data: {network_data}

Please identify:
1. Potential threats and indicators of compromise (IoCs)
2. Attack patterns and tactics (MITRE ATT&CK mapping)
3. Threat actor attribution (if possible)
4. Recommended response actions
5. Prevention strategies

Provide confidence levels for each finding.
""",
                "model_preference": "threat_detector"
            },
            
            SecurityAnalysisType.MALWARE_ANALYSIS: {
                "prompt_template": """
Perform behavioral analysis on the following malware sample or suspicious file:

File Information: {file_info}
Behavioral Data: {behavioral_data}
Static Analysis: {static_analysis}

Please provide:
1. Malware family classification
2. Behavioral analysis summary
3. Potential impact and capabilities
4. IoCs and signatures
5. Containment and removal recommendations

Include confidence scores for classifications.
""",
                "model_preference": "malware_classifier"
            },
            
            SecurityAnalysisType.NETWORK_ANALYSIS: {
                "prompt_template": """
Analyze the following network scan and traffic data for security insights:

Network: {network}
Scan Results: {scan_results}
Traffic Patterns: {traffic_data}

Please analyze:
1. Network topology and exposed services
2. Security misconfigurations
3. Anomalous traffic patterns
4. Potential lateral movement paths
5. Network segmentation recommendations

Focus on identifying security gaps and defensive improvements.
""",
                "model_preference": "network_analyzer"
            },
            
            SecurityAnalysisType.WEB_APPLICATION_SCAN: {
                "prompt_template": """
Analyze the following web application security scan results:

Application: {target}
Scan Results: {scan_results}
Technology Stack: {tech_stack}

Please provide:
1. Critical web vulnerabilities (OWASP Top 10)
2. Business logic flaws
3. Authentication/authorization issues
4. Data exposure risks
5. Secure development recommendations

Prioritize findings by exploitability and business impact.
""",
                "model_preference": "web_app_analyzer"
            },
            
            SecurityAnalysisType.COMPLIANCE_CHECK: {
                "prompt_template": """
Evaluate the following system configuration against compliance requirements:

System: {target}
Framework: {compliance_framework}
Configuration: {configuration}
Scan Results: {scan_results}

Please assess:
1. Compliance gaps and violations
2. Control effectiveness evaluation
3. Remediation priorities
4. Documentation requirements
5. Continuous monitoring recommendations

Map findings to specific compliance controls.
""",
                "model_preference": "compliance_analyzer"
            },
            
            SecurityAnalysisType.INCIDENT_RESPONSE: {
                "prompt_template": """
Analyze the following security incident for response planning:

Incident Type: {incident_type}
Timeline: {timeline}
Evidence: {evidence}
System Impact: {impact}

Please provide:
1. Incident classification and severity
2. Root cause analysis
3. Containment strategies
4. Evidence preservation steps
5. Recovery recommendations
6. Lessons learned and improvements

Include timeline for response activities.
""",
                "model_preference": "incident_analyzer"
            },
            
            SecurityAnalysisType.PENETRATION_TEST: {
                "prompt_template": """
Analyze penetration testing results and provide comprehensive findings:

Target: {target}
Test Scope: {scope}
Findings: {findings}
Exploitation Results: {exploitation}

Please provide:
1. Executive summary of risks
2. Technical findings with proof-of-concept
3. Business impact assessment
4. Remediation roadmap with priorities
5. Retesting recommendations

Structure as a professional penetration test report.
""",
                "model_preference": "pentest_analyzer"
            }
        }
    
    async def analyze_security_results(self, 
                                     context: SecurityContext,
                                     raw_results: Dict[str, Any]) -> AISecurityResult:
        """Perform AI-enhanced analysis of security results"""
        
        analysis_id = f"analysis_{int(time.time())}_{hash(context.target) % 10000}"
        start_time = time.time()
        
        try:
            # Get analysis template
            template = self.analysis_templates.get(context.scan_type)
            if not template:
                raise ValueError(f"No analysis template for {context.scan_type}")
            
            # Prepare AI request
            prompt = self._prepare_analysis_prompt(template, context, raw_results)
            
            ai_request = AIRequest(
                task_type=TaskType.VULNERABILITY_ANALYSIS,
                prompt=prompt,
                max_tokens=2000,
                temperature=0.3,  # Lower temperature for more consistent analysis
                priority=self._map_priority_to_ai_priority(context.priority),
                quality_threshold=0.8
            )
            
            # Get AI analysis
            ai_response = await self.ai_orchestrator.process_request(ai_request)
            
            # Parse AI response
            analysis_result = self._parse_ai_analysis(ai_response.content)
            
            # Calculate metrics
            confidence_score = ai_response.confidence
            risk_score = self._calculate_risk_score(raw_results, analysis_result)
            
            # Generate recommendations
            recommendations = await self._generate_recommendations(context, analysis_result)
            
            # Generate mitigation steps
            mitigation_steps = await self._generate_mitigation_steps(context, analysis_result)
            
            # Check compliance if applicable
            compliance_notes = await self._check_compliance(context, analysis_result)
            
            # Create result
            result = AISecurityResult(
                analysis_id=analysis_id,
                context=context,
                raw_results=raw_results,
                ai_analysis=ai_response.content,
                confidence_score=confidence_score,
                recommendations=recommendations,
                risk_score=risk_score,
                mitigation_steps=mitigation_steps,
                compliance_notes=compliance_notes
            )
            
            # Update metrics
            analysis_time = time.time() - start_time
            self._update_metrics(True, analysis_time)
            
            # Emit event
            if self.event_manager:
                await self.event_manager.emit(
                    EventTypes.SCAN_COMPLETED,
                    {
                        "analysis_id": analysis_id,
                        "target": context.target,
                        "analysis_type": context.scan_type.value,
                        "risk_score": risk_score,
                        "confidence": confidence_score
                    }
                )
            
            logger.info(f"AI security analysis completed: {analysis_id}")
            return result
            
        except Exception as e:
            analysis_time = time.time() - start_time
            self._update_metrics(False, analysis_time)
            logger.error(f"AI security analysis failed: {e}")
            raise
    
    def _prepare_analysis_prompt(self, 
                               template: Dict[str, str], 
                               context: SecurityContext, 
                               raw_results: Dict[str, Any]) -> str:
        """Prepare AI analysis prompt from template and context"""
        
        prompt_template = template["prompt_template"]
        
        # Use custom prompt if provided
        if context.custom_prompts.get(context.scan_type.value):
            prompt_template = context.custom_prompts[context.scan_type.value]
        
        # Prepare template variables
        template_vars = {
            "target": context.target,
            "scan_results": json.dumps(raw_results, indent=2),
            **context.metadata
        }
        
        # Format prompt
        try:
            formatted_prompt = prompt_template.format(**template_vars)
        except KeyError as e:
            # Fallback for missing template variables
            logger.warning(f"Missing template variable {e}, using basic substitution")
            formatted_prompt = prompt_template.replace(f"{{{e.args[0]}}}", str(raw_results))
        
        return formatted_prompt
    
    def _parse_ai_analysis(self, ai_content: str) -> Dict[str, Any]:
        """Parse AI analysis content into structured data"""
        
        # Simple parsing logic - in production, this could be more sophisticated
        analysis = {
            "summary": "",
            "findings": [],
            "recommendations": [],
            "risk_level": "medium"
        }
        
        lines = ai_content.split('\n')
        current_section = None
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
                
            # Detect sections
            if "summary" in line.lower() or "overview" in line.lower():
                current_section = "summary"
            elif "finding" in line.lower() or "vulnerability" in line.lower():
                current_section = "findings"
            elif "recommendation" in line.lower() or "mitigation" in line.lower():
                current_section = "recommendations"
            elif "risk" in line.lower() and ("high" in line.lower() or "critical" in line.lower()):
                analysis["risk_level"] = "high"
            elif "risk" in line.lower() and "low" in line.lower():
                analysis["risk_level"] = "low"
            
            # Add content to appropriate section
            if current_section and not any(keyword in line.lower() for keyword in ["summary", "finding", "recommendation", "mitigation"]):
                if current_section == "summary":
                    analysis["summary"] += line + " "
                elif current_section in ["findings", "recommendations"]:
                    if line.startswith(("•", "-", "1.", "2.", "3.", "4.", "5.")):
                        analysis[current_section].append(line)
        
        return analysis
    
    def _calculate_risk_score(self, raw_results: Dict[str, Any], analysis: Dict[str, Any]) -> float:
        """Calculate numerical risk score based on results and AI analysis"""
        
        base_score = 5.0  # Medium risk baseline
        
        # Adjust based on AI analysis risk level
        risk_level = analysis.get("risk_level", "medium")
        if risk_level == "critical":
            base_score = 9.0
        elif risk_level == "high":
            base_score = 7.5
        elif risk_level == "low":
            base_score = 2.5
        
        # Adjust based on findings count
        findings_count = len(analysis.get("findings", []))
        if findings_count > 10:
            base_score += 1.0
        elif findings_count > 5:
            base_score += 0.5
        
        # Adjust based on raw results severity
        if raw_results.get("critical_vulnerabilities", 0) > 0:
            base_score += 2.0
        elif raw_results.get("high_vulnerabilities", 0) > 0:
            base_score += 1.0
        
        return min(10.0, max(0.0, base_score))
    
    async def _generate_recommendations(self, 
                                      context: SecurityContext, 
                                      analysis: Dict[str, Any]) -> List[str]:
        """Generate specific recommendations using AI"""
        
        recommendations_request = AIRequest(
            task_type=TaskType.REMEDIATION_ADVICE,
            prompt=f"""
Based on the following security analysis, provide specific, actionable recommendations:

Target: {context.target}
Analysis Type: {context.scan_type.value}
Key Findings: {json.dumps(analysis.get('findings', []))}
Risk Level: {analysis.get('risk_level', 'medium')}

Provide 5-7 prioritized recommendations that are:
1. Specific and actionable
2. Technically feasible
3. Cost-effective
4. Aligned with industry best practices

Format as numbered list.
""",
            max_tokens=1000,
            temperature=0.4
        )
        
        try:
            response = await self.ai_orchestrator.process_request(recommendations_request)
            
            # Parse recommendations from response
            recommendations = []
            for line in response.content.split('\n'):
                line = line.strip()
                if line and (line.startswith(tuple("123456789")) or line.startswith(("•", "-"))):
                    recommendations.append(line)
            
            return recommendations[:7]  # Limit to 7 recommendations
            
        except Exception as e:
            logger.warning(f"Failed to generate AI recommendations: {e}")
            return analysis.get("recommendations", [])
    
    async def _generate_mitigation_steps(self, 
                                       context: SecurityContext, 
                                       analysis: Dict[str, Any]) -> List[str]:
        """Generate detailed mitigation steps using AI"""
        
        mitigation_request = AIRequest(
            task_type=TaskType.REMEDIATION_ADVICE,
            prompt=f"""
Generate detailed step-by-step mitigation procedures for:

Target: {context.target}
Analysis Type: {context.scan_type.value}
Risk Level: {analysis.get('risk_level', 'medium')}
Key Issues: {json.dumps(analysis.get('findings', [])[:3])}

Provide specific implementation steps that include:
1. Immediate containment actions
2. Technical remediation steps
3. Verification procedures
4. Monitoring requirements

Format as actionable steps with timelines where appropriate.
""",
            max_tokens=1200,
            temperature=0.3
        )
        
        try:
            response = await self.ai_orchestrator.process_request(mitigation_request)
            
            # Parse mitigation steps
            steps = []
            for line in response.content.split('\n'):
                line = line.strip()
                if line and (line.startswith(tuple("123456789")) or line.startswith(("•", "-", "Step"))):
                    steps.append(line)
            
            return steps
            
        except Exception as e:
            logger.warning(f"Failed to generate mitigation steps: {e}")
            return ["Review findings and implement standard security controls"]
    
    async def _check_compliance(self, 
                              context: SecurityContext, 
                              analysis: Dict[str, Any]) -> List[str]:
        """Check compliance implications using AI"""
        
        # Only perform compliance check if relevant
        compliance_frameworks = context.metadata.get("compliance_frameworks", [])
        if not compliance_frameworks:
            return []
        
        compliance_request = AIRequest(
            task_type=TaskType.VULNERABILITY_ANALYSIS,
            prompt=f"""
Analyze compliance implications for the following security findings:

Compliance Frameworks: {', '.join(compliance_frameworks)}
Target System: {context.target}
Security Findings: {json.dumps(analysis.get('findings', []))}
Risk Level: {analysis.get('risk_level', 'medium')}

Identify:
1. Specific compliance violations or gaps
2. Required remediation for compliance
3. Documentation requirements
4. Audit implications

Focus on actionable compliance guidance.
""",
            max_tokens=800,
            temperature=0.2
        )
        
        try:
            response = await self.ai_orchestrator.process_request(compliance_request)
            
            # Parse compliance notes
            notes = []
            for line in response.content.split('\n'):
                line = line.strip()
                if line and len(line) > 20:  # Filter out short lines
                    notes.append(line)
            
            return notes[:5]  # Limit to 5 notes
            
        except Exception as e:
            logger.warning(f"Failed to check compliance: {e}")
            return []
    
    def _map_priority_to_ai_priority(self, priority: AnalysisPriority) -> int:
        """Map security priority to AI request priority"""
        mapping = {
            AnalysisPriority.LOW: 1,
            AnalysisPriority.MEDIUM: 2,
            AnalysisPriority.HIGH: 3,
            AnalysisPriority.CRITICAL: 3
        }
        return mapping.get(priority, 2)
    
    def _update_metrics(self, success: bool, analysis_time: float):
        """Update analysis metrics"""
        self.analysis_metrics["total_analyses"] += 1
        
        if success:
            self.analysis_metrics["successful_analyses"] += 1
        
        # Update average analysis time
        current_avg = self.analysis_metrics["average_analysis_time"]
        total_analyses = self.analysis_metrics["total_analyses"]
        
        self.analysis_metrics["average_analysis_time"] = (
            (current_avg * (total_analyses - 1) + analysis_time) / total_analyses
        )
        
        # Calculate AI enhancement rate
        if self.analysis_metrics["total_analyses"] > 0:
            self.analysis_metrics["ai_enhancement_rate"] = (
                self.analysis_metrics["successful_analyses"] / 
                self.analysis_metrics["total_analyses"] * 100
            )
    
    async def batch_analyze_results(self, 
                                  contexts_and_results: List[Tuple[SecurityContext, Dict[str, Any]]]) -> List[AISecurityResult]:
        """Perform batch AI analysis of multiple security results"""
        
        tasks = [
            self.analyze_security_results(context, results)
            for context, results in contexts_and_results
        ]
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Filter out exceptions and log them
        successful_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.error(f"Batch analysis {i} failed: {result}")
            else:
                successful_results.append(result)
        
        return successful_results
    
    async def continuous_analysis_workflow(self, 
                                         context: SecurityContext,
                                         tool_configs: Dict[str, Any]) -> AISecurityResult:
        """Run continuous analysis workflow with tool integration"""
        
        if not self.tool_manager:
            raise ValueError("Tool manager required for continuous workflow")
        
        try:
            # Step 1: Run security tools
            logger.info(f"Starting continuous analysis for {context.target}")
            
            # Map security analysis type to tools
            tool_mapping = {
                SecurityAnalysisType.VULNERABILITY_ASSESSMENT: ["nmap", "nuclei"],
                SecurityAnalysisType.NETWORK_ANALYSIS: ["nmap"],
                SecurityAnalysisType.WEB_APPLICATION_SCAN: ["nuclei", "sqlmap"],
                SecurityAnalysisType.MALWARE_ANALYSIS: ["custom_malware_scanner"],
            }
            
            tools_to_run = tool_mapping.get(context.scan_type, ["nmap"])
            raw_results = {}
            
            # Run each tool
            for tool_name in tools_to_run:
                try:
                    if tool_name == "nmap":
                        result = await self.tool_manager.run_nmap_scan(
                            context.target,
                            tool_configs.get("nmap", {})
                        )
                    elif tool_name == "nuclei":
                        result = await self.tool_manager.run_nuclei_scan(
                            context.target,
                            tool_configs.get("nuclei", {})
                        )
                    elif tool_name == "sqlmap":
                        result = await self.tool_manager.run_sqlmap_scan(
                            context.target,
                            tool_configs.get("sqlmap", {})
                        )
                    
                    raw_results[tool_name] = result
                    
                except Exception as e:
                    logger.error(f"Tool {tool_name} failed: {e}")
                    raw_results[tool_name] = {"error": str(e)}
            
            # Step 2: AI analysis of combined results
            ai_result = await self.analyze_security_results(context, raw_results)
            
            logger.info(f"Continuous analysis completed for {context.target}")
            return ai_result
            
        except Exception as e:
            logger.error(f"Continuous analysis workflow failed: {e}")
            raise
    
    def get_analysis_statistics(self) -> Dict[str, Any]:
        """Get AI analysis performance statistics"""
        return {
            "metrics": self.analysis_metrics.copy(),
            "ai_orchestrator_stats": self.ai_orchestrator.get_provider_stats(),
            "model_manager_stats": self.model_manager.get_system_stats()
        }
    
    def get_optimization_recommendations(self) -> List[Dict[str, Any]]:
        """Get optimization recommendations for AI security analysis"""
        recommendations = []
        
        # Check analysis success rate
        if self.analysis_metrics["ai_enhancement_rate"] < 90:
            recommendations.append({
                "type": "analysis_reliability",
                "priority": "medium",
                "title": "Improve AI Analysis Reliability",
                "description": f"Current success rate: {self.analysis_metrics['ai_enhancement_rate']:.1f}%",
                "suggestion": "Review prompt templates and error handling"
            })
        
        # Check analysis performance
        if self.analysis_metrics["average_analysis_time"] > 30:
            recommendations.append({
                "type": "performance",
                "priority": "low",
                "title": "Optimize Analysis Performance",
                "description": f"Average analysis time: {self.analysis_metrics['average_analysis_time']:.1f}s",
                "suggestion": "Consider caching or model optimization"
            })
        
        # Add AI orchestrator recommendations
        ai_recommendations = self.ai_orchestrator.get_optimization_recommendations()
        recommendations.extend(ai_recommendations)
        
        return recommendations

# Global security AI integration instance
security_ai_integration: Optional[SecurityAIIntegration] = None

def get_security_ai_integration() -> SecurityAIIntegration:
    """Get global security AI integration instance"""
    global security_ai_integration
    
    if security_ai_integration is None:
        security_ai_integration = SecurityAIIntegration()
    
    return security_ai_integration

def close_security_ai_integration():
    """Close global security AI integration"""
    global security_ai_integration
    security_ai_integration = None