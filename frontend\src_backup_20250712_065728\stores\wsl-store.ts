/**
 * WSL Store - Windows Subsystem for Linux Integration
 * Manages WSL distributions and Linux security tools
 */
import { create } from 'zustand'
import { persist, subscribeWithSelector } from 'zustand/middleware'
import { immer } from 'zustand/middleware/immer'
import { electronService } from '@/services/electron-service'
import { toast } from 'sonner'

// ===== TYPE DEFINITIONS =====

export interface WSLDistribution {
  name: string
  status: 'running' | 'stopped' | 'installing'
  isDefault: boolean
  version: string
}

export interface WSLStatus {
  status: 'available' | 'unavailable' | 'installing'
  version?: string
  error?: string
}

export interface ToolStatus {
  toolName: string
  installed: boolean
  path?: string
  version?: string
  lastChecked: Date
}

export interface CommandExecution {
  command: string
  startTime: Date
  output: string[]
  completed?: boolean
}

export interface InstallationProgress {
  step: string
  progress: number
  message: string
}

// ===== WSL STORE TYPES =====

export interface WSLState {
  // WSL status
  status: WSLStatus
  distributions: WSLDistribution[]
  defaultDistribution?: string
  
  // Tool management
  toolStatus: ToolStatus[]
  toolsChecking: boolean
  toolsLastChecked?: Date
  
  // Command execution
  activeExecutions: Map<string, CommandExecution>
  
  // Installation
  isInstalling: boolean
  installationProgress?: InstallationProgress | null
}

export interface WSLActions {
  // Status and initialization
  checkStatus: () => Promise<void>
  initialize: () => Promise<boolean>
  
  // Distribution management
  refreshDistributions: () => Promise<void>
  setDefaultDistribution: (name: string) => Promise<boolean>
  
  // Tool management
  checkToolStatus: () => Promise<void>
  installTool: (toolName: string) => Promise<boolean>
  installAllTools: () => Promise<void>
  
  // Command execution
  executeCommand: (command: string, distribution?: string) => Promise<{ success: boolean; output: string[]; error?: string }>
  
  // Installation
  installWSL: () => Promise<boolean>
  
  // Utility
  reset: () => void
}

export type WSLStore = WSLState & WSLActions

// ===== CONSTANTS =====

const REQUIRED_TOOLS = [
  'nmap',
  'nuclei', 
  'sqlmap',
  'gobuster',
  'nikto',
  'dirb',
  'wpscan',
  'ffuf',
  'feroxbuster',
  'whatweb',
  'enum4linux-ng',
  'metasploit-framework',
  'john',
  'hashcat',
  'hydra',
  'testssl.sh',
  'sslyze',
  'searchsploit',
]

// ===== INITIAL STATE =====

const initialState: WSLState = {
  status: {
    status: 'unavailable',
  },
  distributions: [],
  toolStatus: [],
  toolsChecking: false,
  activeExecutions: new Map(),
  isInstalling: false,
}

// ===== STORE IMPLEMENTATION =====

export const useWSLStore = create<WSLStore>()(
  persist(
    subscribeWithSelector(
      immer((set, get) => ({
        ...initialState,
        
        // ===== STATUS AND INITIALIZATION =====
        
        checkStatus: async (): Promise<void> => {
          if (!electronService.isElectron()) {
            set((draft) => {
              draft.status = {
                status: 'unavailable',
                error: 'Not running in Electron'
              }
            })
            return
          }
          
          try {
            console.log('🐧 Checking WSL status...')
            
            const wslStatus = await electronService.wsl.getStatus()
            
            set((draft) => {
              draft.status = wslStatus
            })
            
            console.log('✅ WSL status checked:', wslStatus)
          } catch (error) {
            console.error('❌ Failed to check WSL status:', error)
            
            set((draft) => {
              draft.status = {
                status: 'unavailable',
                error: error instanceof Error ? error.message : 'Unknown error'
              }
            })
          }
        },
        
        initialize: async (): Promise<boolean> => {
          console.log('🚀 Initializing WSL...')
          
          await get().checkStatus()
          
          const status = get().status
          
          if (status.status === 'available') {
            // Refresh distributions and tool status
            await get().refreshDistributions()
            await get().checkToolStatus()
            return true
          } else if (status.status === 'unavailable') {
            console.log('⚠️ WSL not available on this system')
            return false
          }
          
          return false
        },
        
        // ===== DISTRIBUTION MANAGEMENT =====
        
        refreshDistributions: async (): Promise<void> => {
          if (!electronService.isElectron() || get().status.status !== 'available') {
            return
          }
          
          try {
            console.log('📋 Refreshing WSL distributions...')
            
            const distributions = await electronService.wsl.getDistributions()
            
            set((draft) => {
              draft.distributions = distributions
              
              // Set default distribution if not set
              if (!draft.defaultDistribution && distributions.length > 0) {
                const defaultDist = distributions.find(d => d.isDefault) || distributions[0]
                draft.defaultDistribution = defaultDist.name
              }
            })
            
            console.log(`✅ Found ${distributions.length} WSL distributions`)
          } catch (error) {
            console.error('❌ Failed to refresh distributions:', error)
          }
        },
        
        setDefaultDistribution: async (name: string): Promise<boolean> => {
          try {
            console.log(`🔧 Setting default WSL distribution to: ${name}`)
            
            set((draft) => {
              draft.defaultDistribution = name
            })
            
            return true
          } catch (error) {
            console.error('❌ Failed to set default distribution:', error)
            return false
          }
        },
        
        // ===== TOOL MANAGEMENT =====
        
        checkToolStatus: async (): Promise<void> => {
          if (!electronService.isElectron() || get().status.status !== 'available') {
            return
          }
          
          set((draft) => {
            draft.toolsChecking = true
          })
          
          try {
            console.log('🔧 Checking WSL tool status...')
            
            const toolStatusPromises = REQUIRED_TOOLS.map(async (toolName) => {
              try {
                const result = await get().executeCommand(`which ${toolName}`)
                
                return {
                  toolName,
                  installed: result.success && result.output.length > 0,
                  path: result.success ? result.output[0]?.trim() : undefined,
                  lastChecked: new Date(),
                }
              } catch (error) {
                return {
                  toolName,
                  installed: false,
                  lastChecked: new Date(),
                }
              }
            })
            
            const toolStatus = await Promise.all(toolStatusPromises)
            
            set((draft) => {
              draft.toolStatus = toolStatus
              draft.toolsChecking = false
              draft.toolsLastChecked = new Date()
            })
            
            const installedCount = toolStatus.filter(t => t.installed).length
            console.log(`✅ WSL tool check complete: ${installedCount}/${REQUIRED_TOOLS.length} tools installed`)
          } catch (error) {
            console.error('❌ Failed to check tool status:', error)
            
            set((draft) => {
              draft.toolsChecking = false
            })
          }
        },
        
        installTool: async (toolName: string): Promise<boolean> => {
          if (!electronService.isElectron() || get().status.status !== 'available') {
            return false
          }
          
          try {
            console.log(`📦 Installing WSL tool: ${toolName}`)
            toast.info(`Installing ${toolName}...`)
            
            // Get the appropriate installation command for the tool
            const installCommand = getInstallCommand(toolName)
            
            if (!installCommand) {
              toast.error(`Unknown installation method for ${toolName}`)
              return false
            }
            
            const result = await get().executeCommand(installCommand)
            
            if (result.success) {
              console.log(`✅ Successfully installed ${toolName}`)
              toast.success(`${toolName} installed successfully`)
              
              // Refresh tool status
              await get().checkToolStatus()
              
              return true
            } else {
              console.error(`❌ Failed to install ${toolName}:`, result.error)
              toast.error(`Failed to install ${toolName}: ${result.error}`)
              return false
            }
          } catch (error) {
            console.error(`❌ Error installing ${toolName}:`, error)
            toast.error(`Error installing ${toolName}`)
            return false
          }
        },
        
        installAllTools: async (): Promise<void> => {
          const state = get()
          const missingTools = REQUIRED_TOOLS.filter(toolName => {
            const toolStatus = state.toolStatus.find(t => t.toolName === toolName)
            return !toolStatus || !toolStatus.installed
          })
          
          if (missingTools.length === 0) {
            toast.info('All tools are already installed')
            return
          }
          
          console.log(`📦 Installing ${missingTools.length} missing tools...`)
          toast.info(`Installing ${missingTools.length} security tools...`)
          
          let installed = 0
          let failed = 0
          
          for (const toolName of missingTools) {
            const success = await get().installTool(toolName)
            if (success) {
              installed++
            } else {
              failed++
            }
          }
          
          toast.success(`Installation complete: ${installed} installed, ${failed} failed`)
        },
        
        // ===== COMMAND EXECUTION =====
        
        executeCommand: async (
          command: string, 
          distribution?: string
        ): Promise<{ success: boolean; output: string[]; error?: string }> => {
          if (!electronService.isElectron()) {
            return {
              success: false,
              output: [],
              error: 'Not running in Electron'
            }
          }
          
          try {
            const executionId = crypto.randomUUID()
            
            set((draft) => {
              draft.activeExecutions.set(executionId, {
                command,
                startTime: new Date(),
                output: [],
              })
            })
            
            console.log(`🔧 Executing WSL command: ${command}`)
            
            const result = await electronService.wsl.executeCommand(command, distribution)
            
            set((draft) => {
              draft.activeExecutions.delete(executionId)
            })
            
            return result
          } catch (error) {
            console.error('❌ WSL command execution failed:', error)
            
            return {
              success: false,
              output: [],
              error: error instanceof Error ? error.message : 'Unknown error'
            }
          }
        },
        
        // ===== INSTALLATION =====
        
        installWSL: async (): Promise<boolean> => {
          if (!electronService.isElectron()) {
            return false
          }
          
          try {
            console.log('📦 Installing WSL...')
            
            set((draft) => {
              draft.isInstalling = true
              draft.installationProgress = {
                step: 'Initializing WSL installation',
                progress: 0,
                message: 'Starting WSL installation process...'
              }
            })
            
            toast.info('Starting WSL installation...')
            
            // This would require implementation in Electron main process
            const success = await electronService.wsl.install?.() || false
            
            set((draft) => {
              draft.isInstalling = false
              draft.installationProgress = null
            })
            
            if (success) {
              toast.success('WSL installed successfully')
              await get().initialize()
            } else {
              toast.error('WSL installation failed')
            }
            
            return success
          } catch (error) {
            console.error('❌ WSL installation failed:', error)
            
            set((draft) => {
              draft.isInstalling = false
              draft.installationProgress = null
            })
            
            toast.error('WSL installation failed')
            return false
          }
        },
        
        // ===== UTILITY ACTIONS =====
        
        reset: () => {
          set(() => ({ ...initialState }))
        },
      }))
    ),
    {
      name: 'nexusscan-wsl-state',
      version: 1,
      
      // Only persist certain parts of the state
      partialize: (state) => ({
        defaultDistribution: state.defaultDistribution,
        toolStatus: state.toolStatus,
        toolsLastChecked: state.toolsLastChecked,
      }),
    }
  )
)

// ===== UTILITY FUNCTIONS =====

/**
 * Get installation command for a tool
 */
function getInstallCommand(toolName: string): string | null {
  const commands: Record<string, string> = {
    'nmap': 'sudo apt update && sudo apt install -y nmap',
    'nuclei': 'go install -v github.com/projectdiscovery/nuclei/v3/cmd/nuclei@latest',
    'sqlmap': 'sudo apt update && sudo apt install -y sqlmap',
    'gobuster': 'sudo apt update && sudo apt install -y gobuster',
    'nikto': 'sudo apt update && sudo apt install -y nikto',
    'dirb': 'sudo apt update && sudo apt install -y dirb',
    'wpscan': 'sudo apt update && sudo apt install -y wpscan',
    'ffuf': 'go install github.com/ffuf/ffuf@latest',
    'feroxbuster': 'curl -sL https://raw.githubusercontent.com/epi052/feroxbuster/master/install-nix.sh | bash',
    'whatweb': 'sudo apt update && sudo apt install -y whatweb',
    'enum4linux-ng': 'pip3 install enum4linux-ng',
    'metasploit-framework': 'curl https://raw.githubusercontent.com/rapid7/metasploit-omnibus/master/config/templates/metasploit-framework-wrappers/msfupdate.erb > msfinstall && chmod 755 msfinstall && ./msfinstall',
    'john': 'sudo apt update && sudo apt install -y john',
    'hashcat': 'sudo apt update && sudo apt install -y hashcat',
    'hydra': 'sudo apt update && sudo apt install -y hydra',
    'testssl.sh': 'git clone --depth 1 https://github.com/drwetter/testssl.sh.git && sudo ln -s $(pwd)/testssl.sh/testssl.sh /usr/local/bin/testssl',
    'sslyze': 'pip3 install sslyze',
    'searchsploit': 'sudo apt update && sudo apt install -y exploitdb',
  }
  
  return commands[toolName] || null
}

// ===== STORE UTILITIES =====

/**
 * Initialize WSL store (Windows only)
 */
export const initializeWSLStore = async () => {
  try {
    if (!electronService.isElectron()) {
      console.log('📱 Not running in Electron - skipping WSL initialization')
      return
    }
    
    const platform = await electronService.getPlatform()
    
    if (platform !== 'win32') {
      console.log(`💻 Platform is ${platform} - WSL not needed`)
      return
    }
    
    console.log('🚀 Initializing WSL store...')
    
    const { initialize } = useWSLStore.getState()
    await initialize()
    
    console.log('✅ WSL store initialized')
  } catch (error) {
    console.error('❌ Failed to initialize WSL store:', error)
  }
}

/**
 * Get installed tools count
 */
export const useInstalledToolsCount = () => {
  return useWSLStore((state) => 
    state.toolStatus.filter(tool => tool.installed).length
  )
}

/**
 * Get missing tools list
 */
export const useMissingTools = () => {
  return useWSLStore((state) => {
    const installedTools = new Set(
      state.toolStatus
        .filter(tool => tool.installed)
        .map(tool => tool.toolName)
    )
    
    return REQUIRED_TOOLS.filter(tool => !installedTools.has(tool))
  })
}