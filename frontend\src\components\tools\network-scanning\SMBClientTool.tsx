/**
 * SMBClient Tool Component
 * SMB/CIFS client for accessing Windows shares and services
 */
import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { 
  Server,
  Play,
  Square,
  Download,
  Settings,
  Target,
  Folder,
  Users,
  Lock,
  Terminal,
  FileText,
  Copy,
  RotateCcw,
  Network,
  Eye,
  Key,
  Shield,
  FolderOpen,
  HardDrive,
  Activity,
  Search,
  CheckCircle,
  AlertTriangle,
  Info
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useBackendStore } from '@/stores/backend-store';
import { apiClient, ToolExecution } from '@/services/api-client';
import { toast } from 'sonner';

/**
 * SMBClient configuration interface
 */
interface SMBClientConfig {
  target: string;
  operation: 'enumerate' | 'list-shares' | 'connect-share' | 'download-file' | 'upload-file' | 'execute-command';
  authentication: {
    username: string;
    password: string;
    domain: string;
    useNullSession: boolean;
    useKerberos: boolean;
    hash: string;
  };
  share: string;
  remotePath: string;
  localPath: string;
  command: string;
  options: {
    port: number;
    timeout: number;
    maxProtocol: 'SMB1' | 'SMB2' | 'SMB3';
    encryption: boolean;
    signing: boolean;
    verbose: boolean;
    debugLevel: number;
  };
  customArgs: string;
}

/**
 * SMB operations and their descriptions
 */
const SMB_OPERATIONS = {
  'enumerate': {
    name: 'Enumerate Shares',
    description: 'List all available SMB shares on the target',
    icon: <Search className="h-4 w-4" />,
    requiresAuth: false
  },
  'list-shares': {
    name: 'List Share Contents',
    description: 'Browse files and directories in a specific share',
    icon: <FolderOpen className="h-4 w-4" />,
    requiresAuth: true
  },
  'connect-share': {
    name: 'Connect to Share',
    description: 'Establish connection to a specific SMB share',
    icon: <Network className="h-4 w-4" />,
    requiresAuth: true
  },
  'download-file': {
    name: 'Download File',
    description: 'Download a file from the remote SMB share',
    icon: <Download className="h-4 w-4" />,
    requiresAuth: true
  },
  'upload-file': {
    name: 'Upload File',
    description: 'Upload a file to the remote SMB share',
    icon: <HardDrive className="h-4 w-4" />,
    requiresAuth: true
  },
  'execute-command': {
    name: 'Execute Command',
    description: 'Execute a command on the remote system via SMB',
    icon: <Terminal className="h-4 w-4" />,
    requiresAuth: true
  }
};

/**
 * Common SMB shares and their typical purposes
 */
const COMMON_SHARES = [
  { name: 'C$', description: 'Administrative share for C: drive' },
  { name: 'ADMIN$', description: 'Administrative share for system root' },
  { name: 'IPC$', description: 'Inter-process communication share' },
  { name: 'NETLOGON', description: 'Domain controller logon scripts' },
  { name: 'SYSVOL', description: 'Domain controller shared folder' },
  { name: 'print$', description: 'Printer drivers share' },
  { name: 'Users', description: 'User profiles and home directories' },
  { name: 'Public', description: 'Public shared folder' }
];

/**
 * SMB protocol versions
 */
const SMB_PROTOCOLS = [
  { value: 'SMB1', label: 'SMB 1.0', description: 'Legacy protocol (not recommended)' },
  { value: 'SMB2', label: 'SMB 2.0/2.1', description: 'Modern protocol with better performance' },
  { value: 'SMB3', label: 'SMB 3.0+', description: 'Latest protocol with encryption support' }
];

/**
 * SMBClient Tool Component
 */
export function SMBClientTool() {
  // State management
  const [config, setConfig] = React.useState<SMBClientConfig>({
    target: '',
    operation: 'enumerate',
    authentication: {
      username: '',
      password: '',
      domain: '',
      useNullSession: false,
      useKerberos: false,
      hash: ''
    },
    share: '',
    remotePath: '',
    localPath: '',
    command: '',
    options: {
      port: 445,
      timeout: 30,
      maxProtocol: 'SMB3',
      encryption: false,
      signing: false,
      verbose: false,
      debugLevel: 0
    },
    customArgs: ''
  });
  
  const [isExecuting, setIsExecuting] = React.useState(false);
  const [currentExecution, setCurrentExecution] = React.useState<ToolExecution | null>(null);
  const [progress, setProgress] = React.useState(0);
  const [output, setOutput] = React.useState<string[]>([]);
  const [results, setResults] = React.useState<any>(null);
  const [activeTab, setActiveTab] = React.useState('configure');
  const [shareList, setShareList] = React.useState<any[]>([]);
  
  // Backend connection
  const { status: backendStatus } = useBackendStore();
  
  // Update configuration
  const updateConfig = (updates: Partial<SMBClientConfig>) => {
    setConfig(prev => ({ ...prev, ...updates }));
  };
  
  // Update authentication
  const updateAuth = (updates: Partial<SMBClientConfig['authentication']>) => {
    setConfig(prev => ({
      ...prev,
      authentication: { ...prev.authentication, ...updates }
    }));
  };
  
  // Update options
  const updateOptions = (updates: Partial<SMBClientConfig['options']>) => {
    setConfig(prev => ({
      ...prev,
      options: { ...prev.options, ...updates }
    }));
  };
  
  // Check if operation requires authentication
  const requiresAuth = () => {
    return SMB_OPERATIONS[config.operation]?.requiresAuth && !config.authentication.useNullSession;
  };
  
  // Generate SMBClient command
  const generateCommand = (): string => {
    let command = 'smbclient';
    
    // Target and share
    if (config.operation === 'enumerate' || config.operation === 'list-shares') {
      command += ` -L ${config.target}`;
    } else if (config.share) {
      command += ` //${config.target}/${config.share}`;
    } else {
      command += ` //${config.target}`;
    }
    
    // Authentication
    if (!config.authentication.useNullSession) {
      if (config.authentication.username) {
        command += ` -U ${config.authentication.domain ? config.authentication.domain + '\\' : ''}${config.authentication.username}`;
      }
      if (config.authentication.hash) {
        command += ` --pw-nt-hash`;
      }
    } else {
      command += ` -N`;
    }
    
    // Port
    if (config.options.port !== 445) {
      command += ` -p ${config.options.port}`;
    }
    
    // Protocol version
    if (config.options.maxProtocol !== 'SMB3') {
      command += ` --max-protocol=${config.options.maxProtocol}`;
    }
    
    // Encryption and signing
    if (config.options.encryption) {
      command += ` --encrypt`;
    }
    if (config.options.signing) {
      command += ` --sign`;
    }
    
    // Debug level
    if (config.options.debugLevel > 0) {
      command += ` -d ${config.options.debugLevel}`;
    }
    
    // Operation-specific options
    switch (config.operation) {
      case 'download-file':
        if (config.remotePath && config.localPath) {
          command += ` -c "get ${config.remotePath} ${config.localPath}"`;
        }
        break;
      case 'upload-file':
        if (config.localPath && config.remotePath) {
          command += ` -c "put ${config.localPath} ${config.remotePath}"`;
        }
        break;
      case 'execute-command':
        if (config.command) {
          command += ` -c "${config.command}"`;
        }
        break;
      case 'list-shares':
        if (config.remotePath) {
          command += ` -c "ls ${config.remotePath}"`;
        } else {
          command += ` -c "ls"`;
        }
        break;
    }
    
    // Custom arguments
    if (config.customArgs.trim()) {
      command += ` ${config.customArgs.trim()}`;
    }
    
    return command;
  };
  
  // Execute SMB operation
  const executeOperation = async () => {
    if (!config.target.trim()) {
      toast.error('Please specify a target host');
      return;
    }
    
    if (requiresAuth() && !config.authentication.username.trim()) {
      toast.error('This operation requires authentication');
      return;
    }
    
    if (!backendStatus.connected) {
      toast.error('Backend not connected');
      return;
    }
    
    try {
      setIsExecuting(true);
      setProgress(0);
      setOutput([]);
      setResults(null);
      setActiveTab('output');
      
      const execution = await apiClient.executeTool('smbclient', config, (progressUpdate) => {
        setCurrentExecution(progressUpdate);
        setProgress(progressUpdate.progress);
        if (progressUpdate.output) {
          setOutput(prev => [...prev, ...progressUpdate.output!]);
        }
        
        if (progressUpdate.status === 'completed') {
          setIsExecuting(false);
          setResults(progressUpdate.results);
          
          // Parse share list if enumeration
          if (config.operation === 'enumerate' && progressUpdate.results?.shares) {
            setShareList(progressUpdate.results.shares);
          }
          
          toast.success('SMB operation completed');
        } else if (progressUpdate.status === 'failed') {
          setIsExecuting(false);
          toast.error(`Operation failed: ${progressUpdate.error}`);
        }
      });
      
      setCurrentExecution(execution);
      toast.info('SMB operation started');
    } catch (error) {
      setIsExecuting(false);
      console.error('Failed to execute SMB operation:', error);
      toast.error('Failed to execute operation');
    }
  };
  
  // Stop execution
  const stopExecution = async () => {
    if (currentExecution) {
      try {
        await apiClient.cancelExecution(currentExecution.id);
        setIsExecuting(false);
        setCurrentExecution(null);
        toast.info('Operation cancelled');
      } catch (error) {
        console.error('Failed to stop operation:', error);
        toast.error('Failed to stop operation');
      }
    }
  };
  
  // Copy command to clipboard
  const copyCommand = async () => {
    try {
      await navigator.clipboard.writeText(generateCommand());
      toast.success('Command copied to clipboard');
    } catch (error) {
      toast.error('Failed to copy command');
    }
  };
  
  // Export results
  const exportResults = () => {
    if (!results && output.length === 0) {
      toast.error('No results to export');
      return;
    }
    
    const data = results || output.join('\n');
    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `smbclient-${config.operation}-${new Date().toISOString().slice(0, 19)}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    
    toast.success('Results exported');
  };
  
  // Select share from list
  const selectShare = (shareName: string) => {
    updateConfig({ share: shareName });
    toast.success(`Selected share: ${shareName}`);
  };
  
  return (
    <div className="container-desktop py-6 space-y-6">
      {/* Tool Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className="p-2 bg-indigo-100 dark:bg-indigo-900/20 rounded-lg">
            <Server className="h-6 w-6 text-indigo-600 dark:text-indigo-400" />
          </div>
          <div>
            <h1 className="text-2xl font-bold">SMBClient Tool</h1>
            <p className="text-muted-foreground">
              SMB/CIFS client for accessing Windows shares and services
            </p>
          </div>
        </div>
        
        {/* Status indicators */}
        <div className="flex items-center gap-2">
          <Badge variant={backendStatus.connected ? 'default' : 'destructive'}>
            {backendStatus.connected ? 'Connected' : 'Offline'}
          </Badge>
          {isExecuting && (
            <Badge variant="secondary" className="animate-pulse">
              Executing...
            </Badge>
          )}
        </div>
      </div>
      
      {/* Main Interface */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="configure" className="flex items-center gap-2">
            <Settings className="h-4 w-4" />
            Configure
          </TabsTrigger>
          <TabsTrigger value="output" className="flex items-center gap-2">
            <Terminal className="h-4 w-4" />
            Output
          </TabsTrigger>
          <TabsTrigger value="results" className="flex items-center gap-2">
            <FileText className="h-4 w-4" />
            Results
          </TabsTrigger>
          <TabsTrigger value="command" className="flex items-center gap-2">
            <Target className="h-4 w-4" />
            Command
          </TabsTrigger>
        </TabsList>
        
        {/* Configuration Tab */}
        <TabsContent value="configure" className="space-y-6">
          <div className="grid gap-6 lg:grid-cols-2">
            {/* Target and Operation */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Target className="h-5 w-5" />
                  Target & Operation
                </CardTitle>
                <CardDescription>
                  Specify the target host and SMB operation to perform
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Target */}
                <div className="space-y-2">
                  <label className="text-sm font-medium">Target Host</label>
                  <Input
                    placeholder="e.g., *************, server.domain.com"
                    value={config.target}
                    onChange={(e) => updateConfig({ target: e.target.value })}
                  />
                  <p className="text-xs text-muted-foreground">
                    IP address or hostname of the SMB server
                  </p>
                </div>
                
                {/* Operation */}
                <div className="space-y-2">
                  <label className="text-sm font-medium">Operation</label>
                  <Select 
                    value={config.operation} 
                    onValueChange={(value) => updateConfig({ operation: value as any })}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {Object.entries(SMB_OPERATIONS).map(([key, op]) => (
                        <SelectItem key={key} value={key}>
                          <div className="flex items-center gap-2">
                            {op.icon}
                            <div className="flex flex-col">
                              <span>{op.name}</span>
                              <span className="text-xs text-muted-foreground">
                                {op.description}
                              </span>
                            </div>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {requiresAuth() && (
                    <div className="flex items-center gap-2 text-xs text-orange-600">
                      <Lock className="h-3 w-3" />
                      <span>This operation requires authentication</span>
                    </div>
                  )}
                </div>
                
                {/* Share (for applicable operations) */}
                {['list-shares', 'connect-share', 'download-file', 'upload-file'].includes(config.operation) && (
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Share Name</label>
                    <div className="flex gap-2">
                      <Input
                        placeholder="e.g., C$, Users, Public"
                        value={config.share}
                        onChange={(e) => updateConfig({ share: e.target.value })}
                      />
                      <Select onValueChange={selectShare}>
                        <SelectTrigger className="w-32">
                          <SelectValue placeholder="Common" />
                        </SelectTrigger>
                        <SelectContent>
                          {COMMON_SHARES.map((share) => (
                            <SelectItem key={share.name} value={share.name}>
                              <div className="flex flex-col">
                                <span>{share.name}</span>
                                <span className="text-xs text-muted-foreground">
                                  {share.description}
                                </span>
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                )}
                
                {/* Path/File operations */}
                {config.operation === 'list-shares' && (
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Remote Path (Optional)</label>
                    <Input
                      placeholder="e.g., /folder, /folder/subfolder"
                      value={config.remotePath}
                      onChange={(e) => updateConfig({ remotePath: e.target.value })}
                    />
                  </div>
                )}
                
                {['download-file', 'upload-file'].includes(config.operation) && (
                  <>
                    <div className="space-y-2">
                      <label className="text-sm font-medium">Remote File Path</label>
                      <Input
                        placeholder="e.g., /folder/file.txt"
                        value={config.remotePath}
                        onChange={(e) => updateConfig({ remotePath: e.target.value })}
                      />
                    </div>
                    <div className="space-y-2">
                      <label className="text-sm font-medium">Local File Path</label>
                      <Input
                        placeholder="e.g., ./downloaded-file.txt"
                        value={config.localPath}
                        onChange={(e) => updateConfig({ localPath: e.target.value })}
                      />
                    </div>
                  </>
                )}
                
                {config.operation === 'execute-command' && (
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Command to Execute</label>
                    <Input
                      placeholder="e.g., dir, whoami, systeminfo"
                      value={config.command}
                      onChange={(e) => updateConfig({ command: e.target.value })}
                    />
                  </div>
                )}
              </CardContent>
            </Card>
            
            {/* Authentication */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Key className="h-5 w-5" />
                  Authentication
                </CardTitle>
                <CardDescription>
                  Configure authentication credentials for SMB access
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Null Session Option */}
                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    checked={config.authentication.useNullSession}
                    onChange={(e) => updateAuth({ useNullSession: e.target.checked })}
                    className="rounded border-gray-300"
                  />
                  <span className="text-sm">Use Null Session (Anonymous)</span>
                </div>
                
                {!config.authentication.useNullSession && (
                  <>
                    <div className="space-y-2">
                      <label className="text-sm font-medium">Username</label>
                      <Input
                        placeholder="Username"
                        value={config.authentication.username}
                        onChange={(e) => updateAuth({ username: e.target.value })}
                      />
                    </div>
                    
                    <div className="space-y-2">
                      <label className="text-sm font-medium">Password</label>
                      <Input
                        type="password"
                        placeholder="Password"
                        value={config.authentication.password}
                        onChange={(e) => updateAuth({ password: e.target.value })}
                      />
                    </div>
                    
                    <div className="space-y-2">
                      <label className="text-sm font-medium">Domain (Optional)</label>
                      <Input
                        placeholder="DOMAIN or WORKGROUP"
                        value={config.authentication.domain}
                        onChange={(e) => updateAuth({ domain: e.target.value })}
                      />
                    </div>
                    
                    <div className="space-y-2">
                      <label className="text-sm font-medium">NTLM Hash (Optional)</label>
                      <Input
                        placeholder="LM:NTLM hash for pass-the-hash attacks"
                        value={config.authentication.hash}
                        onChange={(e) => updateAuth({ hash: e.target.value })}
                      />
                      <p className="text-xs text-muted-foreground">
                        Use NTLM hash instead of password for authentication
                      </p>
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        checked={config.authentication.useKerberos}
                        onChange={(e) => updateAuth({ useKerberos: e.target.checked })}
                        className="rounded border-gray-300"
                      />
                      <span className="text-sm">Use Kerberos Authentication</span>
                    </div>
                  </>
                )}
              </CardContent>
            </Card>
          </div>
          
          {/* Options */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5" />
                Connection Options
              </CardTitle>
              <CardDescription>
                Advanced SMB connection and protocol options
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid gap-4 md:grid-cols-3">
                <div className="space-y-2">
                  <label className="text-sm font-medium">Port</label>
                  <Input
                    type="number"
                    min="1"
                    max="65535"
                    value={config.options.port}
                    onChange={(e) => updateOptions({ port: parseInt(e.target.value) || 445 })}
                  />
                </div>
                
                <div className="space-y-2">
                  <label className="text-sm font-medium">Timeout (seconds)</label>
                  <Input
                    type="number"
                    min="1"
                    max="300"
                    value={config.options.timeout}
                    onChange={(e) => updateOptions({ timeout: parseInt(e.target.value) || 30 })}
                  />
                </div>
                
                <div className="space-y-2">
                  <label className="text-sm font-medium">Max Protocol</label>
                  <Select 
                    value={config.options.maxProtocol} 
                    onValueChange={(value) => updateOptions({ maxProtocol: value as any })}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {SMB_PROTOCOLS.map((protocol) => (
                        <SelectItem key={protocol.value} value={protocol.value}>
                          <div className="flex flex-col">
                            <span>{protocol.label}</span>
                            <span className="text-xs text-muted-foreground">
                              {protocol.description}
                            </span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
              
              <div className="grid gap-3 md:grid-cols-2">
                <label className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    checked={config.options.encryption}
                    onChange={(e) => updateOptions({ encryption: e.target.checked })}
                    className="rounded border-gray-300"
                  />
                  <span className="text-sm">Force Encryption</span>
                </label>
                
                <label className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    checked={config.options.signing}
                    onChange={(e) => updateOptions({ signing: e.target.checked })}
                    className="rounded border-gray-300"
                  />
                  <span className="text-sm">Force Signing</span>
                </label>
                
                <label className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    checked={config.options.verbose}
                    onChange={(e) => updateOptions({ verbose: e.target.checked })}
                    className="rounded border-gray-300"
                  />
                  <span className="text-sm">Verbose Output</span>
                </label>
                
                <div className="space-y-2">
                  <label className="text-sm font-medium">Debug Level</label>
                  <Select 
                    value={config.options.debugLevel.toString()} 
                    onValueChange={(value) => updateOptions({ debugLevel: parseInt(value) })}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="0">0 - No Debug</SelectItem>
                      <SelectItem value="1">1 - Basic</SelectItem>
                      <SelectItem value="2">2 - Detailed</SelectItem>
                      <SelectItem value="3">3 - Verbose</SelectItem>
                      <SelectItem value="10">10 - Maximum</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              
              <div className="space-y-2">
                <label className="text-sm font-medium">Custom Arguments</label>
                <Input
                  placeholder="Additional smbclient arguments"
                  value={config.customArgs}
                  onChange={(e) => updateConfig({ customArgs: e.target.value })}
                />
              </div>
            </CardContent>
          </Card>
          
          {/* Actions */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Activity className="h-5 w-5" />
                Actions
              </CardTitle>
              <CardDescription>
                Execute SMB operations and manage results
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between">
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={copyCommand}
                  >
                    <Copy className="h-4 w-4 mr-1" />
                    Copy Command
                  </Button>
                </div>
                
                <div className="flex gap-2">
                  {isExecuting ? (
                    <Button
                      variant="destructive"
                      onClick={stopExecution}
                    >
                      <Square className="h-4 w-4 mr-2" />
                      Stop Operation
                    </Button>
                  ) : (
                    <Button
                      onClick={executeOperation}
                      disabled={!config.target.trim() || !backendStatus.connected}
                    >
                      <Play className="h-4 w-4 mr-2" />
                      Execute Operation
                    </Button>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        {/* Output Tab */}
        <TabsContent value="output" className="space-y-6">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="flex items-center gap-2">
                    <Terminal className="h-5 w-5" />
                    Real-time Output
                  </CardTitle>
                  <CardDescription>
                    Live output from the SMBClient operation
                  </CardDescription>
                </div>
                
                {/* Progress and controls */}
                <div className="flex items-center gap-4">
                  {isExecuting && (
                    <div className="flex items-center gap-2">
                      <Progress value={progress} className="w-32" />
                      <span className="text-sm text-muted-foreground">
                        {progress}%
                      </span>
                    </div>
                  )}
                  
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setOutput([])}
                    disabled={isExecuting}
                  >
                    <RotateCcw className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="bg-gray-900 text-gray-100 p-4 rounded-lg h-96 overflow-y-auto font-mono text-sm">
                {output.length === 0 ? (
                  <div className="text-gray-500 italic">
                    {isExecuting ? 'Waiting for output...' : 'No output yet. Execute an operation to see results here.'}
                  </div>
                ) : (
                  output.map((line, index) => (
                    <div key={index} className="mb-1">
                      {line}
                    </div>
                  ))
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        {/* Results Tab */}
        <TabsContent value="results" className="space-y-6">
          {/* Share List (if enumeration) */}
          {shareList.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Folder className="h-5 w-5" />
                  Discovered Shares
                </CardTitle>
                <CardDescription>
                  SMB shares found on the target system
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid gap-2">
                  {shareList.map((share, index) => (
                    <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex items-center gap-3">
                        <FolderOpen className="h-4 w-4 text-blue-500" />
                        <div>
                          <div className="font-medium">{share.name}</div>
                          <div className="text-sm text-muted-foreground">{share.description}</div>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge variant={share.type === 'Disk' ? 'default' : 'secondary'}>
                          {share.type}
                        </Badge>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => selectShare(share.name)}
                        >
                          Select
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
          
          {/* Detailed Results */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="flex items-center gap-2">
                    <FileText className="h-5 w-5" />
                    Operation Results
                  </CardTitle>
                  <CardDescription>
                    Detailed results from the SMB operation
                  </CardDescription>
                </div>
                
                <Button
                  variant="outline"
                  onClick={exportResults}
                  disabled={!results && output.length === 0}
                >
                  <Download className="h-4 w-4 mr-2" />
                  Export Results
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              {results ? (
                <div className="space-y-4">
                  <pre className="bg-muted p-4 rounded-lg overflow-auto text-sm max-h-96">
                    {JSON.stringify(results, null, 2)}
                  </pre>
                </div>
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  <Server className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>No results available yet.</p>
                  <p className="text-sm">Execute an SMB operation to see results here.</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
        
        {/* Command Tab */}
        <TabsContent value="command" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Target className="h-5 w-5" />
                Generated Command
              </CardTitle>
              <CardDescription>
                The SMBClient command that will be executed based on your configuration
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="bg-muted p-4 rounded-lg">
                  <code className="text-sm font-mono break-all">
                    {generateCommand()}
                  </code>
                </div>
                
                <div className="flex justify-between items-center">
                  <p className="text-sm text-muted-foreground">
                    This command will be executed on the backend server
                  </p>
                  
                  <Button variant="outline" onClick={copyCommand}>
                    <Copy className="h-4 w-4 mr-2" />
                    Copy Command
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}