#!/usr/bin/env python3
"""
<PERSON>ript to add missing abstract methods to all new tools
"""

import os
import re

def add_abstract_methods_to_file(file_path, class_name, options_class):
    """Add abstract methods to a tool file"""
    
    # Read the file
    with open(file_path, 'r') as f:
        content = f.read()
    
    # Find the is_available method
    is_available_pattern = r'(def is_available\(self\) -> bool:.*?return [^}]+)'
    is_available_match = re.search(is_available_pattern, content, re.DOTALL)
    
    if not is_available_match:
        print(f"Could not find is_available method in {file_path}")
        return
    
    # Define the methods to add
    methods_to_add = f'''
    
    def check_native_availability(self) -> bool:
        """Check if native tool is available"""
        return self.is_available()
    
    async def execute_native_scan(self, options: ScanOptions,
                                 progress_callback: Optional[Callable[[float, str], None]] = None) -> ScanResult:
        """Execute native scan"""
        if not isinstance(options, {options_class}):
            options = {options_class}(target=options.target)
        
        return await self.scan(options.target, options, progress_callback)'''
    
    # Find the end of the is_available method
    end_pos = is_available_match.end()
    
    # Insert the new methods after is_available
    new_content = content[:end_pos] + methods_to_add + content[end_pos:]
    
    # Write the modified content back
    with open(file_path, 'w') as f:
        f.write(new_content)
    
    print(f"✅ Added abstract methods to {file_path}")

# File mappings: (file_path, class_name, options_class)
files_to_update = [
    ("dirb_scanner.py", "DirbScanner", "DirbScanOptions"),
    ("gobuster_scanner.py", "GobusterScanner", "GobusterScanOptions"),
    ("attack_surface_mapper.py", "AttackSurfaceMapper", "AttackSurfaceOptions"),
    ("threat_model_generator.py", "ThreatModelGenerator", "ThreatModelOptions"),
    ("risk_calculator.py", "RiskCalculator", "RiskCalculationOptions"),
    ("security_posture_analyzer.py", "SecurityPostureAnalyzer", "SecurityPostureOptions"),
    ("pci_dss_checker.py", "PCIDSSChecker", "PCIDSSOptions"),
    ("gdpr_compliance_checker.py", "GDPRComplianceChecker", "GDPROptions"),
    ("iso27001_checker.py", "ISO27001Checker", "ISO27001Options")
]

for file_path, class_name, options_class in files_to_update:
    if os.path.exists(file_path):
        add_abstract_methods_to_file(file_path, class_name, options_class)
    else:
        print(f"❌ File not found: {file_path}")

print("\n✅ All files updated with abstract methods!")