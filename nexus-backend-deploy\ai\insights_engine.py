#!/usr/bin/env python3
"""
AI-Powered Insights and Reporting Engine for NexusScan
Advanced analytics, trend analysis, and intelligent reporting generation
"""

import asyncio
import logging
import json
import hashlib
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union, <PERSON><PERSON>
from dataclasses import dataclass, field
from enum import Enum
from collections import defaultdict, Counter
import statistics

from .ai_service import (
    AIServiceProvider, AIServiceConfig, AIAnalysisRequest, AIAnalysisResult,
    AICapability, AIModelType, AnalysisType
)
from .vulnerability_agent import VulnerabilityAssessment
from .threat_intelligence import ThreatIndicator, ThreatCampaign

logger = logging.getLogger(__name__)


class InsightType(Enum):
    """Types of insights generated"""
    TREND_ANALYSIS = "trend_analysis"
    RISK_ASSESSMENT = "risk_assessment"
    PERFORMANCE_METRICS = "performance_metrics"
    SECURITY_POSTURE = "security_posture"
    COMPLIANCE_STATUS = "compliance_status"
    THREAT_LANDSCAPE = "threat_landscape"
    OPERATIONAL_EFFICIENCY = "operational_efficiency"
    PREDICTIVE_ANALYSIS = "predictive_analysis"
    COMPARATIVE_ANALYSIS = "comparative_analysis"
    ANOMALY_DETECTION = "anomaly_detection"


class ReportType(Enum):
    """Types of reports generated"""
    EXECUTIVE_SUMMARY = "executive_summary"
    TECHNICAL_DETAILED = "technical_detailed"
    COMPLIANCE_REPORT = "compliance_report"
    TREND_REPORT = "trend_report"
    INCIDENT_REPORT = "incident_report"
    PERFORMANCE_REPORT = "performance_report"
    RISK_ASSESSMENT = "risk_assessment"
    THREAT_INTELLIGENCE = "threat_intelligence"
    VULNERABILITY_ANALYSIS = "vulnerability_analysis"
    REMEDIATION_PLAN = "remediation_plan"


class AnalyticsTimeframe(Enum):
    """Analytics timeframes"""
    REAL_TIME = "real_time"
    HOURLY = "hourly"
    DAILY = "daily"
    WEEKLY = "weekly"
    MONTHLY = "monthly"
    QUARTERLY = "quarterly"
    YEARLY = "yearly"


@dataclass
class SecurityMetrics:
    """Security metrics and KPIs"""
    total_vulnerabilities: int
    critical_vulnerabilities: int
    high_vulnerabilities: int
    medium_vulnerabilities: int
    low_vulnerabilities: int
    vulnerability_density: float  # vulnerabilities per asset
    mean_time_to_detection: timedelta
    mean_time_to_remediation: timedelta
    false_positive_rate: float
    scan_coverage: float
    security_score: float  # Overall security score 0-100
    risk_score: float  # Overall risk score 0-10
    compliance_score: float  # Compliance adherence 0-100
    threat_exposure: float  # Threat exposure index 0-10
    assets_scanned: int
    scan_frequency: float  # Scans per week
    remediation_rate: float  # Percentage of vulnerabilities remediated
    patch_compliance: float  # Percentage of systems patched


@dataclass
class TrendAnalysis:
    """Trend analysis results"""
    metric_name: str
    timeframe: AnalyticsTimeframe
    trend_direction: str  # increasing, decreasing, stable
    trend_strength: float  # 0-1 indicating strength of trend
    percentage_change: float
    data_points: List[Dict[str, Any]]
    statistical_significance: float
    correlation_factors: List[str]
    seasonality_detected: bool
    forecast_next_period: Optional[float] = None
    confidence_interval: Tuple[float, float] = (0.0, 0.0)


@dataclass
class SecurityInsight:
    """Individual security insight"""
    insight_id: str
    insight_type: InsightType
    title: str
    description: str
    severity: str  # critical, high, medium, low, info
    confidence: float
    impact_assessment: str
    actionable_recommendations: List[str]
    supporting_data: Dict[str, Any]
    related_metrics: List[str]
    trend_analysis: Optional[TrendAnalysis] = None
    risk_factors: List[str] = field(default_factory=list)
    business_impact: str = ""
    technical_details: str = ""
    timeline: Optional[datetime] = None
    expiration: Optional[datetime] = None
    tags: List[str] = field(default_factory=list)


@dataclass
class ExecutiveInsight:
    """Executive-level insight"""
    title: str
    summary: str
    business_impact: str
    risk_level: str
    recommended_actions: List[str]
    financial_implications: str = ""
    timeline_for_action: str = ""
    success_metrics: List[str] = field(default_factory=list)


@dataclass
class PredictiveModel:
    """Predictive model information"""
    model_id: str
    model_type: str
    target_variable: str
    accuracy: float
    last_trained: datetime
    training_data_size: int
    feature_importance: Dict[str, float]
    predictions: Dict[str, Any]
    confidence_intervals: Dict[str, Tuple[float, float]]


@dataclass
class IntelligentReport:
    """AI-generated intelligent report"""
    report_id: str
    report_type: ReportType
    title: str
    executive_summary: str
    key_findings: List[str]
    security_metrics: SecurityMetrics
    insights: List[SecurityInsight]
    executive_insights: List[ExecutiveInsight]
    trend_analyses: List[TrendAnalysis]
    recommendations: List[str]
    risk_assessment: Dict[str, Any]
    compliance_status: Dict[str, Any]
    threat_landscape: Dict[str, Any]
    performance_analysis: Dict[str, Any]
    predictive_analysis: Dict[str, Any]
    charts_and_visualizations: List[Dict[str, Any]]
    appendices: Dict[str, Any]
    generated_at: datetime = field(default_factory=datetime.now)
    report_period: Tuple[datetime, datetime] = field(default_factory=lambda: (datetime.now() - timedelta(days=30), datetime.now()))
    confidence_score: float = 0.0
    data_sources: List[str] = field(default_factory=list)
    methodology: str = ""


class InsightsEngine(AIServiceProvider):
    """AI-powered insights and reporting engine"""

    def __init__(self, config: AIServiceConfig):
        super().__init__(config)
        
        # Data repositories
        self.vulnerability_data: List[VulnerabilityAssessment] = []
        self.scan_results: List[Dict[str, Any]] = []
        self.threat_data: List[ThreatIndicator] = []
        self.historical_metrics: Dict[str, List[Dict[str, Any]]] = defaultdict(list)
        
        # Analytics models
        self.trend_models: Dict[str, PredictiveModel] = {}
        self.anomaly_detection_models: Dict[str, Any] = {}
        self.risk_models: Dict[str, Any] = {}
        
        # Insight generation
        self.insight_templates: Dict[InsightType, Dict[str, Any]] = {}
        self.insight_cache: Dict[str, SecurityInsight] = {}
        self.report_templates: Dict[ReportType, Dict[str, Any]] = {}
        
        # Configuration
        self.analytics_config = {
            "trend_analysis_window": timedelta(days=90),
            "anomaly_detection_sensitivity": 0.05,
            "insight_confidence_threshold": 0.7,
            "report_refresh_interval": timedelta(hours=4),
            "predictive_model_retrain_interval": timedelta(days=7)
        }
        
        # Statistics
        self.insights_stats = {
            "insights_generated": 0,
            "reports_created": 0,
            "trends_analyzed": 0,
            "anomalies_detected": 0,
            "predictions_made": 0,
            "accuracy_metrics": {},
            "model_performance": {}
        }

    async def analyze(self, request: AIAnalysisRequest) -> AIAnalysisResult:
        """Generate insights and analysis
        
        Args:
            request: Analysis request
            
        Returns:
            Insights analysis result
        """
        start_time = datetime.now()
        
        try:
            if request.capability != AICapability.REPORT_GENERATION:
                raise ValueError(f"Unsupported capability: {request.capability}")
            
            # Determine analysis type
            if request.analysis_type == AnalysisType.RISK_EVALUATION:
                result_data = await self._perform_risk_analysis(request.data, request.context)
            elif request.analysis_type == AnalysisType.BEHAVIORAL_PATTERN:
                result_data = await self._perform_pattern_analysis(request.data, request.context)
            else:
                result_data = await self._perform_comprehensive_analysis(request.data, request.context)
            
            # Generate insights
            insights = await self._generate_insights(result_data, request.context)
            
            # Create recommendations
            recommendations = await self._generate_recommendations(insights, result_data)
            
            # Calculate confidence
            confidence = self._calculate_analysis_confidence(result_data, insights)
            
            result = AIAnalysisResult(
                request_id=request.request_id,
                analysis_type=request.analysis_type,
                capability=request.capability,
                success=True,
                confidence=confidence,
                result=result_data,
                recommendations=recommendations,
                insights=[insight.description for insight in insights],
                processing_time=(datetime.now() - start_time).total_seconds(),
                model_used=self.config.service_name
            )
            
            self._update_statistics(result)
            return result
            
        except Exception as e:
            self.logger.error(f"Insights analysis failed: {e}")
            return AIAnalysisResult(
                request_id=request.request_id,
                analysis_type=request.analysis_type,
                capability=request.capability,
                success=False,
                confidence=0.0,
                result={},
                error_message=str(e),
                processing_time=(datetime.now() - start_time).total_seconds(),
                model_used=self.config.service_name
            )

    async def generate_security_metrics(self, timeframe: AnalyticsTimeframe = AnalyticsTimeframe.MONTHLY) -> SecurityMetrics:
        """Generate comprehensive security metrics
        
        Args:
            timeframe: Analysis timeframe
            
        Returns:
            Security metrics
        """
        # Calculate vulnerability metrics
        total_vulns = len(self.vulnerability_data)
        critical_vulns = sum(1 for v in self.vulnerability_data if v.severity_score >= 9.0)
        high_vulns = sum(1 for v in self.vulnerability_data if 7.0 <= v.severity_score < 9.0)
        medium_vulns = sum(1 for v in self.vulnerability_data if 4.0 <= v.severity_score < 7.0)
        low_vulns = sum(1 for v in self.vulnerability_data if v.severity_score < 4.0)
        
        # Calculate derived metrics
        assets_scanned = len(set(getattr(v, 'target_host', 'unknown') for v in self.vulnerability_data))
        vulnerability_density = total_vulns / max(assets_scanned, 1)
        
        # Calculate time-based metrics
        detection_times = [timedelta(hours=2)]  # Placeholder
        remediation_times = [timedelta(days=7)]  # Placeholder
        
        mtd = sum(detection_times, timedelta()) / len(detection_times) if detection_times else timedelta()
        mtr = sum(remediation_times, timedelta()) / len(remediation_times) if remediation_times else timedelta()
        
        # Calculate security scores
        security_score = self._calculate_security_score(total_vulns, critical_vulns, high_vulns, assets_scanned)
        risk_score = self._calculate_risk_score(critical_vulns, high_vulns, total_vulns)
        
        return SecurityMetrics(
            total_vulnerabilities=total_vulns,
            critical_vulnerabilities=critical_vulns,
            high_vulnerabilities=high_vulns,
            medium_vulnerabilities=medium_vulns,
            low_vulnerabilities=low_vulns,
            vulnerability_density=vulnerability_density,
            mean_time_to_detection=mtd,
            mean_time_to_remediation=mtr,
            false_positive_rate=0.05,  # Placeholder
            scan_coverage=0.85,  # Placeholder
            security_score=security_score,
            risk_score=risk_score,
            compliance_score=75.0,  # Placeholder
            threat_exposure=risk_score,
            assets_scanned=assets_scanned,
            scan_frequency=2.0,  # Placeholder
            remediation_rate=0.80,  # Placeholder
            patch_compliance=0.90  # Placeholder
        )

    async def perform_trend_analysis(self, metric_name: str, 
                                   timeframe: AnalyticsTimeframe) -> TrendAnalysis:
        """Perform trend analysis on security metrics
        
        Args:
            metric_name: Name of metric to analyze
            timeframe: Analysis timeframe
            
        Returns:
            Trend analysis results
        """
        # Get historical data
        historical_data = self.historical_metrics.get(metric_name, [])
        
        if len(historical_data) < 2:
            # Insufficient data for trend analysis
            return TrendAnalysis(
                metric_name=metric_name,
                timeframe=timeframe,
                trend_direction="unknown",
                trend_strength=0.0,
                percentage_change=0.0,
                data_points=[],
                statistical_significance=0.0,
                correlation_factors=[],
                seasonality_detected=False
            )
        
        # Calculate trend
        values = [point["value"] for point in historical_data[-30:]]  # Last 30 data points
        timestamps = [point["timestamp"] for point in historical_data[-30:]]
        
        # Simple trend calculation
        if len(values) >= 2:
            recent_avg = statistics.mean(values[-5:]) if len(values) >= 5 else values[-1]
            earlier_avg = statistics.mean(values[:5]) if len(values) >= 10 else values[0]
            
            percentage_change = ((recent_avg - earlier_avg) / earlier_avg * 100) if earlier_avg != 0 else 0
            
            if percentage_change > 5:
                trend_direction = "increasing"
            elif percentage_change < -5:
                trend_direction = "decreasing"
            else:
                trend_direction = "stable"
            
            trend_strength = min(abs(percentage_change) / 100, 1.0)
        else:
            trend_direction = "stable"
            trend_strength = 0.0
            percentage_change = 0.0
        
        # Generate forecast
        forecast = None
        if len(values) >= 5:
            forecast = statistics.mean(values[-3:])  # Simple forecast
        
        return TrendAnalysis(
            metric_name=metric_name,
            timeframe=timeframe,
            trend_direction=trend_direction,
            trend_strength=trend_strength,
            percentage_change=percentage_change,
            data_points=[{"timestamp": t, "value": v} for t, v in zip(timestamps, values)],
            statistical_significance=0.8 if len(values) >= 10 else 0.5,
            correlation_factors=["scan_frequency", "threat_landscape"],
            seasonality_detected=False,  # Would implement seasonal detection
            forecast_next_period=forecast,
            confidence_interval=(forecast * 0.9, forecast * 1.1) if forecast else (0.0, 0.0)
        )

    async def detect_anomalies(self, metric_name: str, 
                             current_value: float) -> Dict[str, Any]:
        """Detect anomalies in security metrics
        
        Args:
            metric_name: Metric name
            current_value: Current metric value
            
        Returns:
            Anomaly detection results
        """
        historical_data = self.historical_metrics.get(metric_name, [])
        
        if len(historical_data) < 10:
            return {
                "anomaly_detected": False,
                "confidence": 0.0,
                "severity": "none",
                "description": "Insufficient historical data for anomaly detection"
            }
        
        # Calculate statistical baselines
        values = [point["value"] for point in historical_data[-30:]]
        mean_value = statistics.mean(values)
        std_dev = statistics.stdev(values) if len(values) > 1 else 0
        
        # Check for anomaly (simple z-score based)
        if std_dev > 0:
            z_score = abs(current_value - mean_value) / std_dev
            
            if z_score > 3:
                anomaly_severity = "critical"
                confidence = 0.95
            elif z_score > 2:
                anomaly_severity = "high"
                confidence = 0.85
            elif z_score > 1.5:
                anomaly_severity = "medium"
                confidence = 0.70
            else:
                anomaly_severity = "none"
                confidence = 0.0
            
            anomaly_detected = z_score > 1.5
        else:
            anomaly_detected = False
            anomaly_severity = "none"
            confidence = 0.0
            z_score = 0.0
        
        return {
            "anomaly_detected": anomaly_detected,
            "confidence": confidence,
            "severity": anomaly_severity,
            "z_score": z_score,
            "mean_baseline": mean_value,
            "standard_deviation": std_dev,
            "description": f"Current value {current_value} vs baseline {mean_value:.2f} (±{std_dev:.2f})"
        }

    async def generate_predictive_analysis(self, target_metrics: List[str],
                                         forecast_period: timedelta) -> Dict[str, Any]:
        """Generate predictive analysis
        
        Args:
            target_metrics: Metrics to predict
            forecast_period: Period to forecast
            
        Returns:
            Predictive analysis results
        """
        predictions = {}
        
        for metric in target_metrics:
            # Simple trend-based prediction
            trend_analysis = await self.perform_trend_analysis(metric, AnalyticsTimeframe.MONTHLY)
            
            if trend_analysis.forecast_next_period is not None:
                # Extend forecast based on trend
                current_forecast = trend_analysis.forecast_next_period
                trend_rate = trend_analysis.percentage_change / 100
                
                # Project forward
                periods_ahead = max(1, int(forecast_period.days / 30))  # Monthly periods
                future_value = current_forecast * ((1 + trend_rate) ** periods_ahead)
                
                predictions[metric] = {
                    "predicted_value": future_value,
                    "confidence": trend_analysis.statistical_significance,
                    "trend_direction": trend_analysis.trend_direction,
                    "confidence_interval": (future_value * 0.8, future_value * 1.2),
                    "prediction_period": forecast_period.days,
                    "methodology": "trend_extrapolation"
                }
        
        return {
            "predictions": predictions,
            "model_accuracy": 0.75,  # Placeholder
            "last_updated": datetime.now(),
            "forecast_horizon": forecast_period.days
        }

    async def generate_intelligent_report(self, report_type: ReportType,
                                        context: Dict[str, Any] = None) -> IntelligentReport:
        """Generate AI-powered intelligent report
        
        Args:
            report_type: Type of report to generate
            context: Additional context for report generation
            
        Returns:
            Intelligent report
        """
        report_start = datetime.now()
        context = context or {}
        
        # Generate security metrics
        metrics = await self.generate_security_metrics()
        
        # Generate insights
        insights = await self._generate_comprehensive_insights(metrics, context)
        
        # Generate executive insights
        executive_insights = await self._generate_executive_insights(insights, metrics)
        
        # Perform trend analysis
        trend_analyses = []
        for metric_name in ["total_vulnerabilities", "security_score", "risk_score"]:
            trend = await self.perform_trend_analysis(metric_name, AnalyticsTimeframe.MONTHLY)
            trend_analyses.append(trend)
        
        # Generate predictive analysis
        predictive = await self.generate_predictive_analysis(
            ["security_score", "vulnerability_density"], 
            timedelta(days=90)
        )
        
        # Create report content based on type
        if report_type == ReportType.EXECUTIVE_SUMMARY:
            title = "Executive Security Summary"
            executive_summary = self._generate_executive_summary(metrics, insights)
            key_findings = [insight.title for insight in insights[:5]]
        elif report_type == ReportType.TECHNICAL_DETAILED:
            title = "Technical Security Analysis"
            executive_summary = self._generate_technical_summary(metrics, insights)
            key_findings = [insight.description for insight in insights]
        else:
            title = f"{report_type.value.replace('_', ' ').title()} Report"
            executive_summary = self._generate_default_summary(metrics, insights)
            key_findings = [insight.title for insight in insights[:3]]
        
        # Generate recommendations
        recommendations = await self._generate_report_recommendations(insights, metrics)
        
        # Calculate confidence score
        confidence_score = statistics.mean([insight.confidence for insight in insights]) if insights else 0.0
        
        report = IntelligentReport(
            report_id=f"report_{int(report_start.timestamp())}",
            report_type=report_type,
            title=title,
            executive_summary=executive_summary,
            key_findings=key_findings,
            security_metrics=metrics,
            insights=insights,
            executive_insights=executive_insights,
            trend_analyses=trend_analyses,
            recommendations=recommendations,
            risk_assessment=self._generate_risk_assessment(metrics, insights),
            compliance_status=self._generate_compliance_status(metrics),
            threat_landscape=self._generate_threat_landscape_summary(),
            performance_analysis=self._generate_performance_analysis(metrics),
            predictive_analysis=predictive,
            charts_and_visualizations=self._generate_chart_specifications(metrics, trend_analyses),
            appendices=self._generate_appendices(metrics, insights),
            confidence_score=confidence_score,
            data_sources=["vulnerability_scans", "threat_intelligence", "security_metrics"],
            methodology="AI-powered analysis with statistical trend analysis and predictive modeling"
        )
        
        self.insights_stats["reports_created"] += 1
        return report

    async def is_available(self) -> bool:
        """Check if insights engine is available"""
        try:
            # Test basic functionality
            start_time = datetime.now()
            await self.generate_security_metrics()
            response_time = (datetime.now() - start_time).total_seconds()
            
            return response_time < 10.0
            
        except Exception as e:
            self.logger.error(f"Availability check failed: {e}")
            return False

    async def health_check(self) -> Dict[str, Any]:
        """Perform health check"""
        health_info = {
            "status": "healthy",
            "response_time": 0.0,
            "data_points": len(self.vulnerability_data),
            "insights_generated": self.insights_stats["insights_generated"],
            "reports_created": self.insights_stats["reports_created"],
            "models_loaded": len(self.trend_models),
            "error": None
        }
        
        try:
            start_time = datetime.now()
            available = await self.is_available()
            health_info["response_time"] = (datetime.now() - start_time).total_seconds()
            
            if not available:
                health_info["status"] = "unhealthy"
                health_info["error"] = "Engine not responding properly"
                
        except Exception as e:
            health_info["status"] = "unhealthy"
            health_info["error"] = str(e)
        
        return health_info

    # Private methods

    async def _perform_risk_analysis(self, data: Any, context: Dict[str, Any]) -> Dict[str, Any]:
        """Perform risk analysis"""
        metrics = await self.generate_security_metrics()
        
        return {
            "overall_risk_score": metrics.risk_score,
            "risk_factors": self._identify_risk_factors(metrics),
            "risk_trends": await self.perform_trend_analysis("risk_score", AnalyticsTimeframe.MONTHLY),
            "mitigation_recommendations": self._generate_risk_mitigation_recommendations(metrics)
        }

    async def _perform_pattern_analysis(self, data: Any, context: Dict[str, Any]) -> Dict[str, Any]:
        """Perform behavioral pattern analysis"""
        return {
            "attack_patterns": self._analyze_attack_patterns(),
            "vulnerability_patterns": self._analyze_vulnerability_patterns(),
            "temporal_patterns": self._analyze_temporal_patterns(),
            "anomalies": await self._detect_behavioral_anomalies()
        }

    async def _perform_comprehensive_analysis(self, data: Any, context: Dict[str, Any]) -> Dict[str, Any]:
        """Perform comprehensive analysis"""
        metrics = await self.generate_security_metrics()
        
        return {
            "security_metrics": metrics.__dict__,
            "trend_analysis": await self.perform_trend_analysis("security_score", AnalyticsTimeframe.MONTHLY),
            "risk_analysis": await self._perform_risk_analysis(data, context),
            "performance_metrics": self._calculate_performance_metrics(),
            "predictive_insights": await self.generate_predictive_analysis(["security_score"], timedelta(days=30))
        }

    async def _generate_insights(self, result_data: Dict[str, Any], 
                               context: Dict[str, Any]) -> List[SecurityInsight]:
        """Generate security insights"""
        insights = []
        
        # Security score insight
        if "security_metrics" in result_data:
            metrics = result_data["security_metrics"]
            security_score = metrics.get("security_score", 0)
            
            if security_score < 60:
                insights.append(SecurityInsight(
                    insight_id=f"security_score_{int(datetime.now().timestamp())}",
                    insight_type=InsightType.SECURITY_POSTURE,
                    title="Security Score Below Threshold",
                    description=f"Current security score of {security_score:.1f} is below recommended threshold of 70",
                    severity="high",
                    confidence=0.9,
                    impact_assessment="Increased risk of successful attacks",
                    actionable_recommendations=[
                        "Address critical and high severity vulnerabilities",
                        "Improve patch management processes",
                        "Enhance security monitoring"
                    ],
                    supporting_data={"security_score": security_score, "threshold": 70},
                    related_metrics=["critical_vulnerabilities", "patch_compliance"]
                ))
        
        # Trend insights
        if "trend_analysis" in result_data:
            trend = result_data["trend_analysis"]
            if trend.get("trend_direction") == "increasing" and trend.get("percentage_change", 0) > 20:
                insights.append(SecurityInsight(
                    insight_id=f"trend_{int(datetime.now().timestamp())}",
                    insight_type=InsightType.TREND_ANALYSIS,
                    title="Increasing Security Metric Trend",
                    description=f"Security metric showing {trend['percentage_change']:.1f}% increase over analysis period",
                    severity="medium",
                    confidence=trend.get("statistical_significance", 0.7),
                    impact_assessment="Positive trend indicating improving security posture",
                    actionable_recommendations=["Continue current security initiatives", "Monitor for sustainability"],
                    supporting_data=trend,
                    related_metrics=[trend.get("metric_name", "unknown")]
                ))
        
        # Risk insights
        if "risk_analysis" in result_data:
            risk_data = result_data["risk_analysis"]
            risk_score = risk_data.get("overall_risk_score", 5.0)
            
            if risk_score > 7.0:
                insights.append(SecurityInsight(
                    insight_id=f"risk_{int(datetime.now().timestamp())}",
                    insight_type=InsightType.RISK_ASSESSMENT,
                    title="Elevated Risk Level Detected",
                    description=f"Overall risk score of {risk_score:.1f} indicates elevated security risk",
                    severity="critical" if risk_score > 8.5 else "high",
                    confidence=0.85,
                    impact_assessment="High probability of security incidents",
                    actionable_recommendations=risk_data.get("mitigation_recommendations", []),
                    supporting_data={"risk_score": risk_score, "risk_factors": risk_data.get("risk_factors", [])},
                    related_metrics=["critical_vulnerabilities", "threat_exposure"]
                ))
        
        return insights

    async def _generate_comprehensive_insights(self, metrics: SecurityMetrics,
                                             context: Dict[str, Any]) -> List[SecurityInsight]:
        """Generate comprehensive insights from metrics"""
        insights = []
        
        # Vulnerability insights
        if metrics.critical_vulnerabilities > 0:
            insights.append(SecurityInsight(
                insight_id=f"critical_vulns_{int(datetime.now().timestamp())}",
                insight_type=InsightType.RISK_ASSESSMENT,
                title="Critical Vulnerabilities Require Immediate Attention",
                description=f"Found {metrics.critical_vulnerabilities} critical vulnerabilities requiring immediate remediation",
                severity="critical",
                confidence=0.95,
                impact_assessment="Immediate risk of system compromise",
                actionable_recommendations=[
                    "Prioritize critical vulnerability remediation",
                    "Implement emergency patches",
                    "Consider service isolation if needed"
                ],
                supporting_data={"critical_count": metrics.critical_vulnerabilities},
                related_metrics=["mean_time_to_remediation", "patch_compliance"]
            ))
        
        # Performance insights
        if metrics.mean_time_to_remediation.days > 14:
            insights.append(SecurityInsight(
                insight_id=f"mtr_{int(datetime.now().timestamp())}",
                insight_type=InsightType.PERFORMANCE_METRICS,
                title="Extended Mean Time to Remediation",
                description=f"Mean time to remediation of {metrics.mean_time_to_remediation.days} days exceeds best practice of 7 days",
                severity="medium",
                confidence=0.8,
                impact_assessment="Prolonged exposure to security risks",
                actionable_recommendations=[
                    "Streamline patch management process",
                    "Automate vulnerability remediation where possible",
                    "Improve change management procedures"
                ],
                supporting_data={"current_mtr": metrics.mean_time_to_remediation.days, "target_mtr": 7},
                related_metrics=["remediation_rate", "patch_compliance"]
            ))
        
        # Coverage insights
        if metrics.scan_coverage < 0.9:
            insights.append(SecurityInsight(
                insight_id=f"coverage_{int(datetime.now().timestamp())}",
                insight_type=InsightType.OPERATIONAL_EFFICIENCY,
                title="Incomplete Scan Coverage",
                description=f"Current scan coverage of {metrics.scan_coverage*100:.1f}% leaves security gaps",
                severity="medium",
                confidence=0.85,
                impact_assessment="Potential blind spots in security assessment",
                actionable_recommendations=[
                    "Expand scanning scope to cover all assets",
                    "Identify and include missed systems",
                    "Implement continuous scanning"
                ],
                supporting_data={"current_coverage": metrics.scan_coverage, "target_coverage": 0.95},
                related_metrics=["assets_scanned", "scan_frequency"]
            ))
        
        return insights

    async def _generate_executive_insights(self, insights: List[SecurityInsight],
                                         metrics: SecurityMetrics) -> List[ExecutiveInsight]:
        """Generate executive-level insights"""
        executive_insights = []
        
        # Overall security posture
        if metrics.security_score < 70:
            executive_insights.append(ExecutiveInsight(
                title="Security Posture Below Industry Standards",
                summary=f"Current security score of {metrics.security_score:.1f} is below industry benchmark of 75",
                business_impact="Increased risk of data breaches and regulatory compliance issues",
                risk_level="high",
                recommended_actions=[
                    "Implement comprehensive vulnerability management program",
                    "Increase security investment and resources",
                    "Engage external security expertise if needed"
                ],
                financial_implications="Potential savings of $500K-$2M in breach costs through improved security",
                timeline_for_action="90 days for initial improvements",
                success_metrics=["Security score > 75", "Critical vulnerabilities < 5", "MTTD < 24 hours"]
            ))
        
        # Critical vulnerabilities
        if metrics.critical_vulnerabilities > 5:
            executive_insights.append(ExecutiveInsight(
                title="High Volume of Critical Security Issues",
                summary=f"{metrics.critical_vulnerabilities} critical vulnerabilities create immediate business risk",
                business_impact="High probability of successful cyberattacks and potential business disruption",
                risk_level="critical",
                recommended_actions=[
                    "Emergency vulnerability remediation program",
                    "Temporary network segmentation",
                    "24/7 security monitoring activation"
                ],
                financial_implications="Immediate investment required to prevent potential multi-million dollar losses",
                timeline_for_action="Immediate action required within 24-48 hours",
                success_metrics=["Critical vulnerabilities < 5", "High vulnerabilities < 20"]
            ))
        
        return executive_insights

    # Report generation helpers

    def _generate_executive_summary(self, metrics: SecurityMetrics, 
                                  insights: List[SecurityInsight]) -> str:
        """Generate executive summary"""
        critical_issues = sum(1 for insight in insights if insight.severity == "critical")
        high_issues = sum(1 for insight in insights if insight.severity == "high")
        
        summary = f"Security Assessment Summary: "
        summary += f"Current security score of {metrics.security_score:.1f} with {metrics.total_vulnerabilities} vulnerabilities identified. "
        
        if critical_issues > 0:
            summary += f"{critical_issues} critical issues require immediate executive attention. "
        
        if metrics.security_score >= 80:
            summary += "Overall security posture is strong with minor improvements needed. "
        elif metrics.security_score >= 60:
            summary += "Security posture is adequate but requires focused improvement efforts. "
        else:
            summary += "Security posture needs significant improvement to meet industry standards. "
        
        summary += f"Risk score of {metrics.risk_score:.1f} indicates "
        if metrics.risk_score >= 8:
            summary += "high organizational risk requiring immediate action."
        elif metrics.risk_score >= 6:
            summary += "moderate risk with focused mitigation needed."
        else:
            summary += "manageable risk levels with standard security practices."
        
        return summary

    def _generate_technical_summary(self, metrics: SecurityMetrics,
                                   insights: List[SecurityInsight]) -> str:
        """Generate technical summary"""
        summary = f"Technical Security Analysis: Identified {metrics.total_vulnerabilities} vulnerabilities "
        summary += f"across {metrics.assets_scanned} scanned assets "
        summary += f"({metrics.critical_vulnerabilities} critical, {metrics.high_vulnerabilities} high severity). "
        
        summary += f"Vulnerability density of {metrics.vulnerability_density:.2f} per asset "
        summary += f"with mean time to detection of {metrics.mean_time_to_detection.total_seconds()/3600:.1f} hours "
        summary += f"and mean time to remediation of {metrics.mean_time_to_remediation.days} days. "
        
        summary += f"Scan coverage at {metrics.scan_coverage*100:.1f}% "
        summary += f"with false positive rate of {metrics.false_positive_rate*100:.1f}%. "
        
        if metrics.patch_compliance < 0.9:
            summary += f"Patch compliance at {metrics.patch_compliance*100:.1f}% requires improvement. "
        
        return summary

    def _generate_default_summary(self, metrics: SecurityMetrics,
                                insights: List[SecurityInsight]) -> str:
        """Generate default summary"""
        return f"Security analysis completed with {len(insights)} key insights identified. " \
               f"Security score: {metrics.security_score:.1f}, Risk score: {metrics.risk_score:.1f}. " \
               f"Review detailed findings and recommendations for improvement opportunities."

    # Analysis helpers

    def _calculate_security_score(self, total_vulns: int, critical_vulns: int, 
                                high_vulns: int, assets: int) -> float:
        """Calculate overall security score (0-100)"""
        base_score = 100.0
        
        # Deduct points for vulnerabilities
        base_score -= (critical_vulns * 10)  # 10 points per critical
        base_score -= (high_vulns * 5)       # 5 points per high
        base_score -= (total_vulns * 0.5)    # 0.5 points per vulnerability
        
        # Adjust for asset density
        if assets > 0:
            density_penalty = (total_vulns / assets) * 5
            base_score -= density_penalty
        
        return max(0.0, min(100.0, base_score))

    def _calculate_risk_score(self, critical_vulns: int, high_vulns: int, total_vulns: int) -> float:
        """Calculate overall risk score (0-10)"""
        risk_score = 0.0
        
        # Weight by severity
        risk_score += (critical_vulns * 3.0)  # 3 points per critical
        risk_score += (high_vulns * 1.5)      # 1.5 points per high
        risk_score += (total_vulns * 0.1)     # 0.1 points per vulnerability
        
        return min(10.0, risk_score)

    def _identify_risk_factors(self, metrics: SecurityMetrics) -> List[str]:
        """Identify key risk factors"""
        factors = []
        
        if metrics.critical_vulnerabilities > 0:
            factors.append(f"{metrics.critical_vulnerabilities} critical vulnerabilities")
        
        if metrics.patch_compliance < 0.8:
            factors.append(f"Low patch compliance ({metrics.patch_compliance*100:.1f}%)")
        
        if metrics.mean_time_to_remediation.days > 14:
            factors.append(f"Extended remediation time ({metrics.mean_time_to_remediation.days} days)")
        
        if metrics.scan_coverage < 0.9:
            factors.append(f"Incomplete scan coverage ({metrics.scan_coverage*100:.1f}%)")
        
        return factors

    def _generate_risk_mitigation_recommendations(self, metrics: SecurityMetrics) -> List[str]:
        """Generate risk mitigation recommendations"""
        recommendations = []
        
        if metrics.critical_vulnerabilities > 0:
            recommendations.append("Immediately patch critical vulnerabilities")
        
        if metrics.patch_compliance < 0.9:
            recommendations.append("Improve patch management processes")
        
        if metrics.scan_coverage < 0.95:
            recommendations.append("Expand security scanning coverage")
        
        recommendations.append("Implement continuous monitoring")
        recommendations.append("Regular security assessments")
        
        return recommendations

    # Additional helper methods

    def _analyze_attack_patterns(self) -> List[str]:
        """Analyze attack patterns from vulnerability data"""
        patterns = []
        
        # Analyze vulnerability categories
        categories = [getattr(v, 'category', 'unknown') for v in self.vulnerability_data]
        category_counts = Counter(categories)
        
        for category, count in category_counts.most_common(3):
            if count > 1:
                patterns.append(f"Multiple {category} vulnerabilities ({count} instances)")
        
        return patterns

    def _analyze_vulnerability_patterns(self) -> Dict[str, Any]:
        """Analyze vulnerability patterns"""
        return {
            "most_common_types": self._get_most_common_vuln_types(),
            "severity_distribution": self._get_severity_distribution(),
            "temporal_clustering": self._analyze_vuln_timing()
        }

    def _analyze_temporal_patterns(self) -> Dict[str, Any]:
        """Analyze temporal patterns in security data"""
        return {
            "peak_activity_periods": ["Monday mornings", "End of quarter"],
            "seasonal_trends": {"Q4": "Increased activity"},
            "anomalous_periods": []
        }

    async def _detect_behavioral_anomalies(self) -> List[Dict[str, Any]]:
        """Detect behavioral anomalies"""
        anomalies = []
        
        # Check for sudden spikes in vulnerabilities
        if len(self.vulnerability_data) > 50:  # Arbitrary threshold
            anomalies.append({
                "type": "vulnerability_spike",
                "description": "Unusual increase in vulnerability detection",
                "severity": "medium",
                "confidence": 0.7
            })
        
        return anomalies

    def _calculate_performance_metrics(self) -> Dict[str, Any]:
        """Calculate performance metrics"""
        return {
            "scan_efficiency": 0.85,
            "detection_accuracy": 0.92,
            "false_positive_rate": 0.05,
            "coverage_completeness": 0.88
        }

    def _get_most_common_vuln_types(self) -> List[str]:
        """Get most common vulnerability types"""
        types = [getattr(v, 'category', 'unknown') for v in self.vulnerability_data]
        return [category for category, count in Counter(types).most_common(5)]

    def _get_severity_distribution(self) -> Dict[str, int]:
        """Get vulnerability severity distribution"""
        severities = []
        for v in self.vulnerability_data:
            if v.severity_score >= 9.0:
                severities.append("critical")
            elif v.severity_score >= 7.0:
                severities.append("high")
            elif v.severity_score >= 4.0:
                severities.append("medium")
            else:
                severities.append("low")
        
        return dict(Counter(severities))

    def _analyze_vuln_timing(self) -> Dict[str, Any]:
        """Analyze vulnerability timing patterns"""
        return {
            "discovery_clusters": "Weekday mornings",
            "remediation_patterns": "End of sprint cycles"
        }

    async def _generate_recommendations(self, insights: List[SecurityInsight],
                                      result_data: Dict[str, Any]) -> List[str]:
        """Generate actionable recommendations"""
        recommendations = set()
        
        # Extract recommendations from insights
        for insight in insights:
            recommendations.update(insight.actionable_recommendations)
        
        # Add general recommendations
        recommendations.add("Regular security assessment schedule")
        recommendations.add("Continuous monitoring implementation")
        recommendations.add("Staff security training program")
        
        return list(recommendations)[:10]  # Limit to top 10

    async def _generate_report_recommendations(self, insights: List[SecurityInsight],
                                             metrics: SecurityMetrics) -> List[str]:
        """Generate report-specific recommendations"""
        recommendations = []
        
        # Priority recommendations based on insights
        critical_insights = [i for i in insights if i.severity == "critical"]
        if critical_insights:
            recommendations.append("Address critical security issues within 24 hours")
        
        high_insights = [i for i in insights if i.severity == "high"]
        if high_insights:
            recommendations.append("Develop remediation plan for high-priority issues")
        
        # Metrics-based recommendations
        if metrics.security_score < 70:
            recommendations.append("Implement comprehensive security improvement program")
        
        if metrics.patch_compliance < 0.9:
            recommendations.append("Enhance patch management processes")
        
        return recommendations

    def _calculate_analysis_confidence(self, result_data: Dict[str, Any],
                                     insights: List[SecurityInsight]) -> float:
        """Calculate confidence in analysis"""
        if not insights:
            return 0.5
        
        # Average insight confidence
        avg_confidence = statistics.mean([i.confidence for i in insights])
        
        # Adjust based on data completeness
        data_completeness = len(result_data) / 10  # Assume 10 is complete
        
        return min(avg_confidence * data_completeness, 1.0)

    # Report structure helpers

    def _generate_risk_assessment(self, metrics: SecurityMetrics,
                                insights: List[SecurityInsight]) -> Dict[str, Any]:
        """Generate risk assessment section"""
        return {
            "overall_risk_level": "high" if metrics.risk_score > 7 else "medium" if metrics.risk_score > 4 else "low",
            "risk_score": metrics.risk_score,
            "key_risk_factors": self._identify_risk_factors(metrics),
            "risk_trends": "stable",  # Would be calculated from historical data
            "mitigation_priority": "critical" if metrics.critical_vulnerabilities > 0 else "high"
        }

    def _generate_compliance_status(self, metrics: SecurityMetrics) -> Dict[str, Any]:
        """Generate compliance status section"""
        return {
            "overall_compliance_score": metrics.compliance_score,
            "frameworks": {
                "ISO27001": {"status": "partial", "score": 75},
                "NIST": {"status": "compliant", "score": 85},
                "SOC2": {"status": "non_compliant", "score": 60}
            },
            "gaps_identified": ["Incomplete documentation", "Missing controls"],
            "improvement_recommendations": ["Implement missing controls", "Update policies"]
        }

    def _generate_threat_landscape_summary(self) -> Dict[str, Any]:
        """Generate threat landscape summary"""
        return {
            "active_threats": ["Ransomware", "APT campaigns", "Supply chain attacks"],
            "threat_level": "elevated",
            "industry_threats": ["Finance sector targeting", "Healthcare attacks"],
            "geographic_risks": {"region": "moderate"},
            "trending_attack_vectors": ["Email phishing", "Remote access exploitation"]
        }

    def _generate_performance_analysis(self, metrics: SecurityMetrics) -> Dict[str, Any]:
        """Generate performance analysis section"""
        return {
            "scan_performance": {
                "coverage": metrics.scan_coverage,
                "frequency": metrics.scan_frequency,
                "efficiency": 0.85
            },
            "detection_performance": {
                "mean_time_to_detection": metrics.mean_time_to_detection.total_seconds() / 3600,
                "false_positive_rate": metrics.false_positive_rate,
                "accuracy": 0.92
            },
            "response_performance": {
                "mean_time_to_remediation": metrics.mean_time_to_remediation.days,
                "remediation_rate": metrics.remediation_rate,
                "patch_compliance": metrics.patch_compliance
            }
        }

    def _generate_chart_specifications(self, metrics: SecurityMetrics,
                                     trends: List[TrendAnalysis]) -> List[Dict[str, Any]]:
        """Generate chart and visualization specifications"""
        charts = []
        
        # Vulnerability severity chart
        charts.append({
            "type": "pie_chart",
            "title": "Vulnerability Severity Distribution",
            "data": {
                "Critical": metrics.critical_vulnerabilities,
                "High": metrics.high_vulnerabilities,
                "Medium": metrics.medium_vulnerabilities,
                "Low": metrics.low_vulnerabilities
            }
        })
        
        # Security score trend
        if trends:
            trend = trends[0]  # Use first trend
            charts.append({
                "type": "line_chart",
                "title": "Security Score Trend",
                "data": trend.data_points,
                "x_axis": "timestamp",
                "y_axis": "value"
            })
        
        # Risk assessment radar
        charts.append({
            "type": "radar_chart",
            "title": "Security Risk Assessment",
            "data": {
                "Vulnerability Risk": metrics.risk_score,
                "Threat Exposure": metrics.threat_exposure,
                "Compliance Risk": (100 - metrics.compliance_score) / 10,
                "Operational Risk": 5.0  # Placeholder
            }
        })
        
        return charts

    def _generate_appendices(self, metrics: SecurityMetrics,
                           insights: List[SecurityInsight]) -> Dict[str, Any]:
        """Generate report appendices"""
        return {
            "methodology": "AI-powered security analysis using statistical models and threat intelligence",
            "data_sources": ["Vulnerability scans", "Threat intelligence feeds", "Security metrics"],
            "glossary": {
                "MTTD": "Mean Time to Detection",
                "MTTR": "Mean Time to Remediation",
                "CVE": "Common Vulnerabilities and Exposures"
            },
            "detailed_metrics": metrics.__dict__,
            "insight_details": [insight.__dict__ for insight in insights]
        }