#!/usr/bin/env python3
"""
Quick AWS connectivity test
"""

import requests
import socket

def test_connectivity():
    url = "http://ec2-3-89-91-209.compute-1.amazonaws.com:8000"
    
    print("🔍 Testing AWS EC2 connectivity...")
    
    # Test 1: DNS resolution
    try:
        hostname = "ec2-3-89-91-209.compute-1.amazonaws.com"
        ip = socket.gethostbyname(hostname)
        print(f"✅ DNS resolution: {hostname} -> {ip}")
    except Exception as e:
        print(f"❌ DNS resolution failed: {e}")
        return
    
    # Test 2: Port connectivity
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(10)
        result = sock.connect_ex((ip, 8000))
        sock.close()
        
        if result == 0:
            print(f"✅ Port 8000 is open on {ip}")
        else:
            print(f"❌ Port 8000 is closed on {ip}")
            print("🔧 Check AWS Security Group settings!")
            return
    except Exception as e:
        print(f"❌ Port test failed: {e}")
        return
    
    # Test 3: HTTP request
    try:
        response = requests.get(f"{url}/api/health", timeout=10)
        print(f"✅ HTTP request successful: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   Backend status: {data.get('status', 'unknown')}")
    except requests.exceptions.Timeout:
        print(f"❌ HTTP request timed out")
        print("🔧 Backend may not be running or firewall blocking")
    except Exception as e:
        print(f"❌ HTTP request failed: {e}")

if __name__ == "__main__":
    test_connectivity()