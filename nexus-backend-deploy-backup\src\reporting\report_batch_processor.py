#!/usr/bin/env python3
"""
Report Batch Processor for NexusScan Desktop Application
Automated report generation, scheduling, and batch processing capabilities
"""

import asyncio
import logging
import json
import os
import shutil
import zipfile
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass, field
from enum import Enum
from pathlib import Path
import schedule
import threading
import smtplib
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from email.mime.base import MIMEBase
from email import encoders

from core.config import Config
from core.database import DatabaseManager
from core.events import EventManager, EventTypes
from reporting.advanced_report_generator import AdvancedReportGenerator, ReportData, ReportConfiguration, ReportFormat, ReportType

logger = logging.getLogger(__name__)


class BatchJobStatus(Enum):
    """Batch job status"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class DeliveryMethod(Enum):
    """Report delivery methods"""
    FILE_SYSTEM = "file_system"
    EMAIL = "email"
    FTP = "ftp"
    SFTP = "sftp"
    WEBHOOK = "webhook"
    CLOUD_STORAGE = "cloud_storage"


@dataclass
class BatchJob:
    """Batch processing job"""
    job_id: str
    name: str
    description: str
    report_configs: List[ReportConfiguration]
    schedule_config: Dict[str, Any]
    delivery_config: Dict[str, Any]
    created_at: datetime
    last_run: Optional[datetime] = None
    next_run: Optional[datetime] = None
    status: BatchJobStatus = BatchJobStatus.PENDING
    run_count: int = 0
    success_count: int = 0
    failure_count: int = 0
    last_error: Optional[str] = None


@dataclass
class DeliveryConfiguration:
    """Report delivery configuration"""
    method: DeliveryMethod
    destination: str
    credentials: Dict[str, Any] = field(default_factory=dict)
    format_options: Dict[str, Any] = field(default_factory=dict)
    notification_settings: Dict[str, Any] = field(default_factory=dict)


@dataclass
class BatchProcessingResult:
    """Batch processing result"""
    job_id: str
    start_time: datetime
    end_time: datetime
    total_reports: int
    successful_reports: int
    failed_reports: int
    generated_files: List[str] = field(default_factory=list)
    delivered_reports: List[str] = field(default_factory=list)
    errors: List[str] = field(default_factory=list)


class ReportBatchProcessor:
    """Advanced report batch processor with scheduling and delivery"""
    
    def __init__(self, config: Config, db_manager: DatabaseManager, event_manager: EventManager):
        """Initialize batch processor"""
        self.config = config
        self.db_manager = db_manager
        self.event_manager = event_manager
        self.report_generator = AdvancedReportGenerator(config, db_manager)
        
        # Batch jobs
        self.batch_jobs: Dict[str, BatchJob] = {}
        self.active_jobs: Dict[str, asyncio.Task] = {}
        
        # Processing queues
        self.job_queue: asyncio.Queue = asyncio.Queue()
        self.processing_workers: List[asyncio.Task] = []
        
        # Scheduling
        self.scheduler_running = False
        self.scheduler_thread: Optional[threading.Thread] = None
        
        # Output and archive directories
        self.output_dir = Path(config.get("reporting.output_directory", "./reports"))
        self.archive_dir = Path(config.get("reporting.archive_directory", "./reports/archive"))
        self.temp_dir = Path(config.get("reporting.temp_directory", "./reports/temp"))
        
        # Create directories
        for directory in [self.output_dir, self.archive_dir, self.temp_dir]:
            directory.mkdir(parents=True, exist_ok=True)
        
        # Configuration
        self.max_concurrent_jobs = config.get("reporting.max_concurrent_jobs", 3)
        self.job_timeout = config.get("reporting.job_timeout_seconds", 1800)  # 30 minutes
        self.cleanup_interval = config.get("reporting.cleanup_interval_days", 30)
        
        # Start processing workers
        self._start_processing_workers()
        
        # Load existing jobs
        asyncio.create_task(self._load_batch_jobs())
    
    async def create_batch_job(self, 
                             name: str,
                             description: str,
                             report_configs: List[ReportConfiguration],
                             schedule_config: Dict[str, Any],
                             delivery_config: Dict[str, Any]) -> str:
        """Create new batch processing job"""
        
        job_id = f"batch_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{len(self.batch_jobs)}"
        
        # Calculate next run time
        next_run = self._calculate_next_run(schedule_config)
        
        job = BatchJob(
            job_id=job_id,
            name=name,
            description=description,
            report_configs=report_configs,
            schedule_config=schedule_config,
            delivery_config=delivery_config,
            created_at=datetime.now(),
            next_run=next_run
        )
        
        self.batch_jobs[job_id] = job
        
        # Schedule the job
        if schedule_config.get("enabled", True):
            self._schedule_job(job)
        
        # Save job configuration
        await self._save_batch_job(job)
        
        logger.info(f"Batch job created: {name} ({job_id})")
        
        # Emit event
        self.event_manager.emit(EventTypes.BATCH_JOB_CREATED, {
            "job_id": job_id,
            "name": name,
            "next_run": next_run.isoformat() if next_run else None
        })
        
        return job_id
    
    async def execute_batch_job(self, job_id: str) -> BatchProcessingResult:
        """Execute batch job immediately"""
        
        if job_id not in self.batch_jobs:
            raise ValueError(f"Batch job not found: {job_id}")
        
        job = self.batch_jobs[job_id]
        
        if job_id in self.active_jobs:
            raise RuntimeError(f"Batch job already running: {job_id}")
        
        logger.info(f"Executing batch job: {job.name} ({job_id})")
        
        # Create processing task
        task = asyncio.create_task(self._process_batch_job(job))
        self.active_jobs[job_id] = task
        
        try:
            result = await task
            return result
        finally:
            # Cleanup
            if job_id in self.active_jobs:
                del self.active_jobs[job_id]
    
    async def _process_batch_job(self, job: BatchJob) -> BatchProcessingResult:
        """Process batch job"""
        
        start_time = datetime.now()
        result = BatchProcessingResult(
            job_id=job.job_id,
            start_time=start_time,
            end_time=start_time,  # Will be updated
            total_reports=len(job.report_configs),
            successful_reports=0,
            failed_reports=0
        )
        
        try:
            job.status = BatchJobStatus.RUNNING
            job.run_count += 1
            job.last_run = start_time
            
            # Emit start event
            self.event_manager.emit(EventTypes.BATCH_JOB_STARTED, {
                "job_id": job.job_id,
                "name": job.name
            })
            
            # Process each report configuration
            for i, report_config in enumerate(job.report_configs):
                try:
                    logger.info(f"Processing report {i+1}/{len(job.report_configs)} for job {job.job_id}")
                    
                    # Generate sample report data (in real implementation, this would come from scan results)
                    report_data = await self._create_sample_report_data(report_config)
                    
                    # Generate report
                    report_path = await self.report_generator.generate_report(report_data, report_config)
                    result.generated_files.append(report_path)
                    
                    # Deliver report
                    delivery_success = await self._deliver_report(report_path, job.delivery_config)
                    
                    if delivery_success:
                        result.delivered_reports.append(report_path)
                        result.successful_reports += 1
                    else:
                        result.failed_reports += 1
                        result.errors.append(f"Delivery failed for report: {report_path}")
                    
                except Exception as e:
                    error_msg = f"Failed to process report {i+1}: {str(e)}"
                    logger.error(error_msg)
                    result.errors.append(error_msg)
                    result.failed_reports += 1
            
            # Update job statistics
            if result.failed_reports == 0:
                job.status = BatchJobStatus.COMPLETED
                job.success_count += 1
                job.last_error = None
            else:
                job.status = BatchJobStatus.FAILED
                job.failure_count += 1
                job.last_error = "; ".join(result.errors[-3:])  # Last 3 errors
            
            # Create batch summary if multiple reports
            if len(result.generated_files) > 1:
                archive_path = await self._create_batch_archive(job, result.generated_files)
                if archive_path:
                    result.generated_files.append(archive_path)
            
        except Exception as e:
            error_msg = f"Batch job failed: {str(e)}"
            logger.error(error_msg)
            job.status = BatchJobStatus.FAILED
            job.failure_count += 1
            job.last_error = error_msg
            result.errors.append(error_msg)
        
        finally:
            result.end_time = datetime.now()
            
            # Calculate next run
            if job.schedule_config.get("enabled", True):
                job.next_run = self._calculate_next_run(job.schedule_config)
            
            # Save job state
            await self._save_batch_job(job)
            
            # Emit completion event
            self.event_manager.emit(EventTypes.BATCH_JOB_COMPLETED, {
                "job_id": job.job_id,
                "name": job.name,
                "status": job.status.value,
                "successful_reports": result.successful_reports,
                "failed_reports": result.failed_reports,
                "next_run": job.next_run.isoformat() if job.next_run else None
            })
            
            logger.info(f"Batch job completed: {job.name} ({job.job_id}) - "
                       f"Success: {result.successful_reports}, Failed: {result.failed_reports}")
        
        return result
    
    async def _create_sample_report_data(self, config: ReportConfiguration) -> ReportData:
        """Create sample report data for demonstration"""
        
        # In real implementation, this would query actual scan results from database
        scan_id = f"scan_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        # Sample vulnerability data
        sample_vulnerabilities = [
            {
                "title": "SQL Injection Vulnerability",
                "description": "SQL injection vulnerability found in login form",
                "severity": "critical",
                "severity_score": 9.0,
                "cvss_score": "9.8",
                "target": "https://example.com/login",
                "tool": "sqlmap"
            },
            {
                "title": "Cross-Site Scripting (XSS)",
                "description": "Reflected XSS vulnerability in search parameter",
                "severity": "high",
                "severity_score": 7.5,
                "cvss_score": "7.4",
                "target": "https://example.com/search",
                "tool": "nuclei"
            },
            {
                "title": "Information Disclosure",
                "description": "Server version information exposed in HTTP headers",
                "severity": "medium",
                "severity_score": 5.0,
                "cvss_score": "5.3",
                "target": "https://example.com",
                "tool": "nmap"
            }
        ]
        
        # Sample AI insights
        sample_insights = [
            {
                "title": "Critical Security Gap Identified",
                "summary": "The combination of SQL injection and weak authentication creates a high-risk attack vector",
                "type": "executive_summary",
                "confidence": 0.95,
                "recommendations": [
                    "Implement parameterized queries",
                    "Add input validation",
                    "Enable SQL injection protection"
                ]
            },
            {
                "title": "Attack Chain Analysis",
                "summary": "Attackers could chain XSS and information disclosure to escalate privileges",
                "type": "technical_analysis",
                "confidence": 0.87,
                "recommendations": [
                    "Implement Content Security Policy",
                    "Remove server version headers",
                    "Add XSS protection headers"
                ]
            }
        ]
        
        # Sample remediation plans
        sample_remediation = [
            {
                "title": "SQL Injection Remediation",
                "description": "Implement secure coding practices to prevent SQL injection",
                "priority": "critical",
                "effort_estimate": "2-3 days",
                "steps": [
                    "Review all database queries",
                    "Implement parameterized queries",
                    "Add input validation",
                    "Test with security tools"
                ]
            }
        ]
        
        target = config.output_path.split('/')[-1] if '/' in config.output_path else "example.com"
        
        return ReportData(
            scan_id=scan_id,
            target=target,
            scan_type="comprehensive",
            start_time=datetime.now() - timedelta(hours=2),
            end_time=datetime.now(),
            duration=timedelta(hours=2),
            vulnerabilities=sample_vulnerabilities,
            scan_summary={
                "total_vulnerabilities": len(sample_vulnerabilities),
                "critical_count": 1,
                "high_count": 1,
                "medium_count": 1,
                "low_count": 0
            },
            tool_results={
                "nmap": {"ports_scanned": 1000, "open_ports": 5},
                "nuclei": {"templates_run": 500, "matches": 2},
                "sqlmap": {"injections_found": 1}
            },
            ai_risk_score=7.8,
            ai_confidence=0.91,
            ai_insights=sample_insights,
            threat_intelligence=[],
            remediation_plans=sample_remediation,
            compliance_status={
                "owasp_top10": {"status": "partial", "score": 6.5},
                "nist_csf": {"status": "needs_improvement", "score": 5.2}
            },
            analyst_notes="Automated batch report generation"
        )
    
    async def _deliver_report(self, report_path: str, delivery_config: Dict[str, Any]) -> bool:
        """Deliver report using configured method"""
        
        try:
            method = DeliveryMethod(delivery_config.get("method", "file_system"))
            
            if method == DeliveryMethod.FILE_SYSTEM:
                return await self._deliver_to_filesystem(report_path, delivery_config)
            elif method == DeliveryMethod.EMAIL:
                return await self._deliver_via_email(report_path, delivery_config)
            elif method == DeliveryMethod.WEBHOOK:
                return await self._deliver_via_webhook(report_path, delivery_config)
            else:
                logger.warning(f"Delivery method not implemented: {method}")
                return True  # Assume success for unimplemented methods
                
        except Exception as e:
            logger.error(f"Report delivery failed: {e}")
            return False
    
    async def _deliver_to_filesystem(self, report_path: str, delivery_config: Dict[str, Any]) -> bool:
        """Deliver report to filesystem location"""
        try:
            destination = delivery_config.get("destination", str(self.output_dir))
            destination_path = Path(destination)
            destination_path.mkdir(parents=True, exist_ok=True)
            
            filename = Path(report_path).name
            dest_file = destination_path / filename
            
            shutil.copy2(report_path, dest_file)
            logger.info(f"Report delivered to: {dest_file}")
            return True
            
        except Exception as e:
            logger.error(f"Filesystem delivery failed: {e}")
            return False
    
    async def _deliver_via_email(self, report_path: str, delivery_config: Dict[str, Any]) -> bool:
        """Deliver report via email"""
        try:
            smtp_config = delivery_config.get("smtp", {})
            recipients = delivery_config.get("recipients", [])
            
            if not recipients:
                logger.warning("No email recipients configured")
                return False
            
            # Create message
            msg = MIMEMultipart()
            msg['From'] = smtp_config.get("from_address", "<EMAIL>")
            msg['To'] = ", ".join(recipients)
            msg['Subject'] = delivery_config.get("subject", "NexusScan Security Report")
            
            # Add body
            body = delivery_config.get("body", "Please find the attached security report.")
            msg.attach(MIMEText(body, 'plain'))
            
            # Attach report
            with open(report_path, "rb") as attachment:
                part = MIMEBase('application', 'octet-stream')
                part.set_payload(attachment.read())
                encoders.encode_base64(part)
                part.add_header(
                    'Content-Disposition',
                    f'attachment; filename= {Path(report_path).name}'
                )
                msg.attach(part)
            
            # Send email
            server = smtplib.SMTP(smtp_config.get("host", "localhost"), smtp_config.get("port", 587))
            
            if smtp_config.get("use_tls", True):
                server.starttls()
            
            if smtp_config.get("username") and smtp_config.get("password"):
                server.login(smtp_config["username"], smtp_config["password"])
            
            server.sendmail(msg['From'], recipients, msg.as_string())
            server.quit()
            
            logger.info(f"Report emailed to {len(recipients)} recipients")
            return True
            
        except Exception as e:
            logger.error(f"Email delivery failed: {e}")
            return False
    
    async def _deliver_via_webhook(self, report_path: str, delivery_config: Dict[str, Any]) -> bool:
        """Deliver report via webhook"""
        try:
            import aiohttp
            
            webhook_url = delivery_config.get("url")
            if not webhook_url:
                logger.warning("No webhook URL configured")
                return False
            
            # Read report data
            with open(report_path, 'rb') as f:
                report_data = f.read()
            
            # Prepare payload
            payload = {
                "report_name": Path(report_path).name,
                "report_data": report_data.hex(),  # Hex encode binary data
                "timestamp": datetime.now().isoformat(),
                "metadata": delivery_config.get("metadata", {})
            }
            
            # Send webhook
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    webhook_url,
                    json=payload,
                    headers=delivery_config.get("headers", {}),
                    timeout=aiohttp.ClientTimeout(total=30)
                ) as response:
                    if response.status == 200:
                        logger.info(f"Report delivered via webhook: {webhook_url}")
                        return True
                    else:
                        logger.error(f"Webhook delivery failed: HTTP {response.status}")
                        return False
                        
        except Exception as e:
            logger.error(f"Webhook delivery failed: {e}")
            return False
    
    async def _create_batch_archive(self, job: BatchJob, report_files: List[str]) -> Optional[str]:
        """Create ZIP archive of batch reports"""
        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            archive_name = f"{job.name}_{job.job_id}_{timestamp}.zip"
            archive_path = self.archive_dir / archive_name
            
            with zipfile.ZipFile(archive_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                for report_file in report_files:
                    if os.path.exists(report_file):
                        zipf.write(report_file, os.path.basename(report_file))
                
                # Add batch summary
                summary = {
                    "job_id": job.job_id,
                    "job_name": job.name,
                    "generated_at": datetime.now().isoformat(),
                    "reports_included": len(report_files),
                    "report_files": [os.path.basename(f) for f in report_files]
                }
                
                zipf.writestr("batch_summary.json", json.dumps(summary, indent=2))
            
            logger.info(f"Batch archive created: {archive_path}")
            return str(archive_path)
            
        except Exception as e:
            logger.error(f"Failed to create batch archive: {e}")
            return None
    
    def _calculate_next_run(self, schedule_config: Dict[str, Any]) -> Optional[datetime]:
        """Calculate next run time based on schedule configuration"""
        try:
            schedule_type = schedule_config.get("type", "once")
            
            if schedule_type == "once":
                run_at = schedule_config.get("run_at")
                if run_at:
                    return datetime.fromisoformat(run_at)
                else:
                    return datetime.now() + timedelta(minutes=1)  # Run in 1 minute
            
            elif schedule_type == "recurring":
                interval = schedule_config.get("interval", "daily")
                time_str = schedule_config.get("time", "00:00")
                
                now = datetime.now()
                hour, minute = map(int, time_str.split(':'))
                
                if interval == "daily":
                    next_run = now.replace(hour=hour, minute=minute, second=0, microsecond=0)
                    if next_run <= now:
                        next_run += timedelta(days=1)
                    return next_run
                
                elif interval == "weekly":
                    day_of_week = schedule_config.get("day_of_week", 0)  # Monday = 0
                    days_ahead = day_of_week - now.weekday()
                    if days_ahead <= 0:
                        days_ahead += 7
                    next_run = now + timedelta(days=days_ahead)
                    return next_run.replace(hour=hour, minute=minute, second=0, microsecond=0)
                
                elif interval == "monthly":
                    day_of_month = schedule_config.get("day_of_month", 1)
                    next_run = now.replace(day=day_of_month, hour=hour, minute=minute, second=0, microsecond=0)
                    if next_run <= now:
                        # Move to next month
                        if now.month == 12:
                            next_run = next_run.replace(year=now.year + 1, month=1)
                        else:
                            next_run = next_run.replace(month=now.month + 1)
                    return next_run
            
            return None
            
        except Exception as e:
            logger.error(f"Failed to calculate next run time: {e}")
            return None
    
    def _schedule_job(self, job: BatchJob):
        """Schedule job using schedule library"""
        try:
            schedule_config = job.schedule_config
            schedule_type = schedule_config.get("type", "once")
            
            if schedule_type == "recurring":
                interval = schedule_config.get("interval", "daily")
                time_str = schedule_config.get("time", "00:00")
                
                if interval == "daily":
                    schedule.every().day.at(time_str).do(self._trigger_scheduled_job, job.job_id)
                elif interval == "weekly":
                    day_name = ["monday", "tuesday", "wednesday", "thursday", "friday", "saturday", "sunday"][
                        schedule_config.get("day_of_week", 0)
                    ]
                    getattr(schedule.every(), day_name).at(time_str).do(self._trigger_scheduled_job, job.job_id)
                
                logger.info(f"Job scheduled: {job.name} - {interval} at {time_str}")
        
        except Exception as e:
            logger.error(f"Failed to schedule job {job.job_id}: {e}")
    
    def _trigger_scheduled_job(self, job_id: str):
        """Trigger scheduled job execution"""
        asyncio.create_task(self.execute_batch_job(job_id))
    
    def _start_processing_workers(self):
        """Start background processing workers"""
        for i in range(self.max_concurrent_jobs):
            worker = asyncio.create_task(self._processing_worker(f"worker-{i}"))
            self.processing_workers.append(worker)
        
        # Start scheduler
        self.scheduler_running = True
        self.scheduler_thread = threading.Thread(target=self._run_scheduler, daemon=True)
        self.scheduler_thread.start()
    
    async def _processing_worker(self, worker_name: str):
        """Background processing worker"""
        logger.info(f"Processing worker started: {worker_name}")
        
        while True:
            try:
                # Wait for job from queue
                job_data = await self.job_queue.get()
                
                if job_data is None:  # Shutdown signal
                    break
                
                job_id = job_data["job_id"]
                
                if job_id in self.batch_jobs:
                    await self.execute_batch_job(job_id)
                
                self.job_queue.task_done()
                
            except Exception as e:
                logger.error(f"Worker {worker_name} error: {e}")
                await asyncio.sleep(1)
    
    def _run_scheduler(self):
        """Run the job scheduler"""
        import time
        
        logger.info("Job scheduler started")
        
        while self.scheduler_running:
            try:
                schedule.run_pending()
                time.sleep(60)  # Check every minute
            except Exception as e:
                logger.error(f"Scheduler error: {e}")
                time.sleep(60)
    
    async def _load_batch_jobs(self):
        """Load saved batch jobs from storage"""
        try:
            jobs_file = self.output_dir / "batch_jobs.json"
            
            if jobs_file.exists():
                with open(jobs_file, 'r') as f:
                    jobs_data = json.load(f)
                
                for job_data in jobs_data:
                    job = BatchJob(**job_data)
                    self.batch_jobs[job.job_id] = job
                    
                    # Reschedule active jobs
                    if job.schedule_config.get("enabled", True) and job.status != BatchJobStatus.CANCELLED:
                        self._schedule_job(job)
                
                logger.info(f"Loaded {len(self.batch_jobs)} batch jobs")
                
        except Exception as e:
            logger.warning(f"Failed to load batch jobs: {e}")
    
    async def _save_batch_job(self, job: BatchJob):
        """Save batch job configuration"""
        try:
            jobs_file = self.output_dir / "batch_jobs.json"
            
            # Load existing jobs
            jobs_data = []
            if jobs_file.exists():
                with open(jobs_file, 'r') as f:
                    jobs_data = json.load(f)
            
            # Update or add job
            job_dict = {
                "job_id": job.job_id,
                "name": job.name,
                "description": job.description,
                "schedule_config": job.schedule_config,
                "delivery_config": job.delivery_config,
                "created_at": job.created_at.isoformat(),
                "last_run": job.last_run.isoformat() if job.last_run else None,
                "next_run": job.next_run.isoformat() if job.next_run else None,
                "status": job.status.value,
                "run_count": job.run_count,
                "success_count": job.success_count,
                "failure_count": job.failure_count,
                "last_error": job.last_error
            }
            
            # Find and update existing job or add new one
            found = False
            for i, existing_job in enumerate(jobs_data):
                if existing_job["job_id"] == job.job_id:
                    jobs_data[i] = job_dict
                    found = True
                    break
            
            if not found:
                jobs_data.append(job_dict)
            
            # Save to file
            with open(jobs_file, 'w') as f:
                json.dump(jobs_data, f, indent=2)
                
        except Exception as e:
            logger.error(f"Failed to save batch job: {e}")
    
    async def get_batch_jobs(self) -> List[BatchJob]:
        """Get list of all batch jobs"""
        return list(self.batch_jobs.values())
    
    async def get_batch_job(self, job_id: str) -> Optional[BatchJob]:
        """Get specific batch job"""
        return self.batch_jobs.get(job_id)
    
    async def cancel_batch_job(self, job_id: str) -> bool:
        """Cancel batch job"""
        try:
            if job_id in self.batch_jobs:
                job = self.batch_jobs[job_id]
                job.status = BatchJobStatus.CANCELLED
                await self._save_batch_job(job)
                
                # Cancel active task if running
                if job_id in self.active_jobs:
                    self.active_jobs[job_id].cancel()
                
                logger.info(f"Batch job cancelled: {job.name} ({job_id})")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Failed to cancel batch job {job_id}: {e}")
            return False
    
    async def cleanup_old_reports(self):
        """Clean up old report files"""
        try:
            cutoff_date = datetime.now() - timedelta(days=self.cleanup_interval)
            
            cleaned_count = 0
            
            # Clean output directory
            for file_path in self.output_dir.iterdir():
                if file_path.is_file() and file_path.stat().st_mtime < cutoff_date.timestamp():
                    file_path.unlink()
                    cleaned_count += 1
            
            # Clean archive directory
            for file_path in self.archive_dir.iterdir():
                if file_path.is_file() and file_path.stat().st_mtime < cutoff_date.timestamp():
                    file_path.unlink()
                    cleaned_count += 1
            
            # Clean temp directory
            for file_path in self.temp_dir.iterdir():
                if file_path.is_file():
                    file_path.unlink()
                    cleaned_count += 1
            
            if cleaned_count > 0:
                logger.info(f"Cleaned up {cleaned_count} old report files")
            
        except Exception as e:
            logger.error(f"Cleanup failed: {e}")
    
    async def get_processing_statistics(self) -> Dict[str, Any]:
        """Get processing statistics"""
        total_jobs = len(self.batch_jobs)
        active_jobs = len(self.active_jobs)
        
        status_counts = {}
        for job in self.batch_jobs.values():
            status = job.status.value
            status_counts[status] = status_counts.get(status, 0) + 1
        
        total_runs = sum(job.run_count for job in self.batch_jobs.values())
        total_successes = sum(job.success_count for job in self.batch_jobs.values())
        total_failures = sum(job.failure_count for job in self.batch_jobs.values())
        
        return {
            "total_jobs": total_jobs,
            "active_jobs": active_jobs,
            "status_distribution": status_counts,
            "total_runs": total_runs,
            "total_successes": total_successes,
            "total_failures": total_failures,
            "success_rate": (total_successes / total_runs * 100) if total_runs > 0 else 0
        }
    
    async def shutdown(self):
        """Shutdown batch processor"""
        logger.info("Shutting down batch processor")
        
        # Stop scheduler
        self.scheduler_running = False
        if self.scheduler_thread:
            self.scheduler_thread.join(timeout=5)
        
        # Cancel active jobs
        for task in self.active_jobs.values():
            task.cancel()
        
        # Stop workers
        for _ in self.processing_workers:
            await self.job_queue.put(None)
        
        for worker in self.processing_workers:
            worker.cancel()
        
        # Final cleanup
        await self.cleanup_old_reports()