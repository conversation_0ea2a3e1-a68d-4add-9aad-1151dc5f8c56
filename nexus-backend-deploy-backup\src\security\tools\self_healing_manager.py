#!/usr/bin/env python3
"""
Self-Healing Tool Manager
Autonomous tool installation, dependency management, and system repair
"""

import asyncio
import logging
import json
import os
import platform
import subprocess
import shutil
import tempfile
import hashlib
from typing import Dict, Any, List, Optional, Tuple, Set
from pathlib import Path
from dataclasses import dataclass, field
from enum import Enum

from .environment_detector import environment_detector, get_capabilities
from .hybrid_execution_engine import ToolExecutionResult, ExecutionResult

logger = logging.getLogger(__name__)

class InstallationMethod(Enum):
    """Available installation methods"""
    PACKAGE_MANAGER = "package_manager"
    DIRECT_DOWNLOAD = "direct_download"
    SOURCE_COMPILE = "source_compile"
    CONTAINER_PULL = "container_pull"
    LANGUAGE_PACKAGE = "language_package"
    BINARY_RELEASE = "binary_release"

class InstallationStatus(Enum):
    """Installation status"""
    SUCCESS = "success"
    FAILED = "failed"
    PARTIAL = "partial"
    SKIPPED = "skipped"
    ALREADY_INSTALLED = "already_installed"
    DEPENDENCY_MISSING = "dependency_missing"

@dataclass
class ToolInstallation:
    """Tool installation definition"""
    name: str
    version: Optional[str] = None
    methods: List[InstallationMethod] = field(default_factory=list)
    dependencies: List[str] = field(default_factory=list)
    verify_commands: List[str] = field(default_factory=list)
    post_install_commands: List[str] = field(default_factory=list)
    installation_notes: str = ""

@dataclass
class InstallationResult:
    """Result of tool installation"""
    tool_name: str
    status: InstallationStatus
    method_used: Optional[InstallationMethod] = None
    version_installed: Optional[str] = None
    execution_time: float = 0.0
    error_message: str = ""
    warnings: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)

class SelfHealingManager:
    """Autonomous tool installation and system repair manager"""
    
    def __init__(self):
        self.environment = environment_detector.environment
        self.capabilities = get_capabilities()
        
        # Installation definitions
        self.tool_installations = self._load_installation_definitions()
        
        # Package manager commands
        self.package_managers = self._detect_package_managers()
        
        # Installation cache
        self.installation_cache = {}
        self.failed_installations = set()
        
        # System paths
        self.local_bin_path = Path.home() / ".local" / "bin"
        self.tool_cache_path = Path.home() / ".nexusscan" / "tools"
        
        # Ensure directories exist
        self.local_bin_path.mkdir(parents=True, exist_ok=True)
        self.tool_cache_path.mkdir(parents=True, exist_ok=True)
        
        logger.info("🔧 Self-Healing Manager initialized")
        logger.info(f"🌍 Environment: {self.environment.value}")
        logger.info(f"📦 Package managers: {list(self.package_managers.keys())}")
    
    def _detect_package_managers(self) -> Dict[str, Dict[str, str]]:
        """Detect available package managers and their commands"""
        
        managers = {}
        
        # APT (Debian/Ubuntu)
        if self.capabilities.get('apt'):
            managers['apt'] = {
                'install': 'sudo apt-get update && sudo apt-get install -y {}',
                'search': 'apt-cache search {}',
                'remove': 'sudo apt-get remove -y {}',
                'update': 'sudo apt-get update'
            }
        
        # YUM (RedHat/CentOS)
        if self.capabilities.get('yum'):
            managers['yum'] = {
                'install': 'sudo yum install -y {}',
                'search': 'yum search {}',
                'remove': 'sudo yum remove -y {}',
                'update': 'sudo yum update'
            }
        
        # Pacman (Arch)
        if self.capabilities.get('pacman'):
            managers['pacman'] = {
                'install': 'sudo pacman -S --noconfirm {}',
                'search': 'pacman -Ss {}',
                'remove': 'sudo pacman -R --noconfirm {}',
                'update': 'sudo pacman -Sy'
            }
        
        # Homebrew (macOS/Linux)
        if self.capabilities.get('brew'):
            managers['brew'] = {
                'install': 'brew install {}',
                'search': 'brew search {}',
                'remove': 'brew uninstall {}',
                'update': 'brew update'
            }
        
        # Chocolatey (Windows)
        if self.capabilities.get('chocolatey'):
            managers['chocolatey'] = {
                'install': 'choco install -y {}',
                'search': 'choco search {}',
                'remove': 'choco uninstall -y {}',
                'update': 'choco upgrade all'
            }
        
        # Language-specific package managers
        if self.capabilities.get('pip'):
            managers['pip'] = {
                'install': 'pip install {}',
                'search': 'pip search {}',
                'remove': 'pip uninstall -y {}',
                'update': 'pip install --upgrade {}'
            }
        
        if self.capabilities.get('npm'):
            managers['npm'] = {
                'install': 'npm install -g {}',
                'search': 'npm search {}',
                'remove': 'npm uninstall -g {}',
                'update': 'npm update -g {}'
            }
        
        if self.capabilities.get('gem'):
            managers['gem'] = {
                'install': 'gem install {}',
                'search': 'gem search {}',
                'remove': 'gem uninstall {}',
                'update': 'gem update {}'
            }
        
        if self.capabilities.get('cargo'):
            managers['cargo'] = {
                'install': 'cargo install {}',
                'search': 'cargo search {}',
                'remove': 'cargo uninstall {}',
                'update': 'cargo install --force {}'
            }
        
        if self.capabilities.get('go'):
            managers['go'] = {
                'install': 'go install {}@latest',
                'search': 'go list -m {}',
                'remove': 'rm -rf $(go env GOPATH)/bin/{}',
                'update': 'go install {}@latest'
            }
        
        return managers
    
    def _load_installation_definitions(self) -> Dict[str, ToolInstallation]:
        """Load comprehensive tool installation definitions"""
        
        installations = {}
        
        # Network scanning tools
        installations['nmap'] = ToolInstallation(
            name='nmap',
            methods=[InstallationMethod.PACKAGE_MANAGER, InstallationMethod.BINARY_RELEASE],
            verify_commands=['nmap --version'],
            installation_notes="Network discovery and security auditing"
        )
        
        installations['masscan'] = ToolInstallation(
            name='masscan',
            methods=[InstallationMethod.PACKAGE_MANAGER, InstallationMethod.SOURCE_COMPILE],
            dependencies=['gcc', 'make', 'libpcap-dev'],
            verify_commands=['masscan --version'],
            installation_notes="Fast port scanner"
        )
        
        # Vulnerability scanners
        installations['nuclei'] = ToolInstallation(
            name='nuclei',
            methods=[InstallationMethod.LANGUAGE_PACKAGE, InstallationMethod.BINARY_RELEASE],
            verify_commands=['nuclei -version'],
            installation_notes="Vulnerability scanner based on templates"
        )
        
        installations['nikto'] = ToolInstallation(
            name='nikto',
            methods=[InstallationMethod.PACKAGE_MANAGER, InstallationMethod.DIRECT_DOWNLOAD],
            dependencies=['perl', 'libnet-ssleay-perl'],
            verify_commands=['nikto -Version'],
            installation_notes="Web server scanner"
        )
        
        # Web application tools
        installations['sqlmap'] = ToolInstallation(
            name='sqlmap',
            methods=[InstallationMethod.PACKAGE_MANAGER, InstallationMethod.DIRECT_DOWNLOAD],
            dependencies=['python3', 'python3-requests'],
            verify_commands=['sqlmap --version'],
            installation_notes="SQL injection detection and exploitation"
        )
        
        installations['wpscan'] = ToolInstallation(
            name='wpscan',
            methods=[InstallationMethod.LANGUAGE_PACKAGE, InstallationMethod.PACKAGE_MANAGER],
            dependencies=['ruby', 'ruby-dev'],
            verify_commands=['wpscan --version'],
            installation_notes="WordPress vulnerability scanner"
        )
        
        # Directory/file enumeration
        installations['gobuster'] = ToolInstallation(
            name='gobuster',
            methods=[InstallationMethod.LANGUAGE_PACKAGE, InstallationMethod.BINARY_RELEASE],
            verify_commands=['gobuster version'],
            installation_notes="Directory/file brute-forcer"
        )
        
        installations['dirb'] = ToolInstallation(
            name='dirb',
            methods=[InstallationMethod.PACKAGE_MANAGER],
            verify_commands=['dirb'],
            installation_notes="Web content scanner"
        )
        
        installations['ffuf'] = ToolInstallation(
            name='ffuf',
            methods=[InstallationMethod.LANGUAGE_PACKAGE, InstallationMethod.BINARY_RELEASE],
            verify_commands=['ffuf -V'],
            installation_notes="Fast web fuzzer"
        )
        
        installations['feroxbuster'] = ToolInstallation(
            name='feroxbuster',
            methods=[InstallationMethod.LANGUAGE_PACKAGE, InstallationMethod.BINARY_RELEASE],
            verify_commands=['feroxbuster --version'],
            installation_notes="Content discovery tool"
        )
        
        # SSL/TLS tools
        installations['testssl'] = ToolInstallation(
            name='testssl.sh',
            methods=[InstallationMethod.DIRECT_DOWNLOAD, InstallationMethod.PACKAGE_MANAGER],
            dependencies=['openssl', 'bash'],
            verify_commands=['testssl.sh --version'],
            post_install_commands=['chmod +x testssl.sh'],
            installation_notes="SSL/TLS configuration tester"
        )
        
        installations['sslyze'] = ToolInstallation(
            name='sslyze',
            methods=[InstallationMethod.LANGUAGE_PACKAGE],
            dependencies=['python3', 'python3-pip'],
            verify_commands=['sslyze --version'],
            installation_notes="SSL configuration analyzer"
        )
        
        # SMB/NetBIOS tools
        installations['enum4linux'] = ToolInstallation(
            name='enum4linux',
            methods=[InstallationMethod.PACKAGE_MANAGER, InstallationMethod.DIRECT_DOWNLOAD],
            dependencies=['samba-common', 'smbclient', 'perl'],
            verify_commands=['enum4linux'],
            installation_notes="SMB enumeration tool"
        )
        
        installations['smbclient'] = ToolInstallation(
            name='smbclient',
            methods=[InstallationMethod.PACKAGE_MANAGER],
            verify_commands=['smbclient --version'],
            installation_notes="SMB/CIFS client"
        )
        
        # Web content analysis
        installations['whatweb'] = ToolInstallation(
            name='whatweb',
            methods=[InstallationMethod.PACKAGE_MANAGER, InstallationMethod.LANGUAGE_PACKAGE],
            dependencies=['ruby'],
            verify_commands=['whatweb --version'],
            installation_notes="Web application fingerprinter"
        )
        
        # Exploitation frameworks
        installations['metasploit'] = ToolInstallation(
            name='msfconsole',
            methods=[InstallationMethod.DIRECT_DOWNLOAD, InstallationMethod.CONTAINER_PULL],
            dependencies=['ruby', 'postgresql'],
            verify_commands=['msfconsole --version'],
            installation_notes="Penetration testing framework"
        )
        
        installations['searchsploit'] = ToolInstallation(
            name='searchsploit',
            methods=[InstallationMethod.DIRECT_DOWNLOAD, InstallationMethod.PACKAGE_MANAGER],
            verify_commands=['searchsploit --version'],
            installation_notes="Exploit database search tool"
        )
        
        # Password tools
        installations['hashcat'] = ToolInstallation(
            name='hashcat',
            methods=[InstallationMethod.PACKAGE_MANAGER, InstallationMethod.BINARY_RELEASE],
            verify_commands=['hashcat --version'],
            installation_notes="Advanced password recovery"
        )
        
        installations['john'] = ToolInstallation(
            name='john',
            methods=[InstallationMethod.PACKAGE_MANAGER, InstallationMethod.SOURCE_COMPILE],
            verify_commands=['john --version'],
            installation_notes="Password cracker"
        )
        
        return installations
    
    async def auto_install_tool(self, tool_name: str, force_reinstall: bool = False) -> InstallationResult:
        """Automatically install a security tool using best available method"""
        
        start_time = asyncio.get_event_loop().time()
        
        # Check if already failed
        if tool_name in self.failed_installations and not force_reinstall:
            return InstallationResult(
                tool_name=tool_name,
                status=InstallationStatus.SKIPPED,
                error_message="Previous installation failed"
            )
        
        # Check if already installed
        if not force_reinstall and await self._is_tool_installed(tool_name):
            return InstallationResult(
                tool_name=tool_name,
                status=InstallationStatus.ALREADY_INSTALLED,
                execution_time=asyncio.get_event_loop().time() - start_time
            )
        
        if tool_name not in self.tool_installations:
            return InstallationResult(
                tool_name=tool_name,
                status=InstallationStatus.FAILED,
                error_message="Tool installation definition not found",
                execution_time=asyncio.get_event_loop().time() - start_time
            )
        
        tool_def = self.tool_installations[tool_name]
        
        logger.info(f"🔧 Auto-installing {tool_name}...")
        
        # Install dependencies first
        dependency_results = []
        for dependency in tool_def.dependencies:
            if not await self._is_tool_installed(dependency):
                dep_result = await self._install_dependency(dependency)
                dependency_results.append(dep_result)
                if not dep_result:
                    return InstallationResult(
                        tool_name=tool_name,
                        status=InstallationStatus.DEPENDENCY_MISSING,
                        error_message=f"Failed to install dependency: {dependency}",
                        execution_time=asyncio.get_event_loop().time() - start_time
                    )
        
        # Try installation methods in order of preference
        last_error = ""
        warnings = []
        
        for method in tool_def.methods:
            try:
                logger.info(f"🔄 Trying {method.value} installation for {tool_name}")
                
                success, error_msg, version = await self._install_with_method(tool_name, method, tool_def)
                
                if success:
                    # Verify installation
                    if await self._verify_installation(tool_name, tool_def):
                        # Run post-install commands
                        await self._run_post_install_commands(tool_def)
                        
                        execution_time = asyncio.get_event_loop().time() - start_time
                        
                        result = InstallationResult(
                            tool_name=tool_name,
                            status=InstallationStatus.SUCCESS,
                            method_used=method,
                            version_installed=version,
                            execution_time=execution_time,
                            warnings=warnings,
                            metadata={
                                'dependencies_installed': dependency_results,
                                'installation_notes': tool_def.installation_notes
                            }
                        )
                        
                        # Cache successful installation
                        self.installation_cache[tool_name] = result
                        
                        logger.info(f"✅ Successfully installed {tool_name} via {method.value}")
                        return result
                    else:
                        warnings.append(f"Installation via {method.value} succeeded but verification failed")
                else:
                    last_error = error_msg
                    warnings.append(f"Installation via {method.value} failed: {error_msg}")
                    
            except Exception as e:
                last_error = str(e)
                warnings.append(f"Installation via {method.value} threw exception: {str(e)}")
        
        # All methods failed
        self.failed_installations.add(tool_name)
        
        return InstallationResult(
            tool_name=tool_name,
            status=InstallationStatus.FAILED,
            error_message=f"All installation methods failed. Last error: {last_error}",
            warnings=warnings,
            execution_time=asyncio.get_event_loop().time() - start_time
        )
    
    async def _install_with_method(
        self, 
        tool_name: str, 
        method: InstallationMethod, 
        tool_def: ToolInstallation
    ) -> Tuple[bool, str, Optional[str]]:
        """Install tool using specific method"""
        
        if method == InstallationMethod.PACKAGE_MANAGER:
            return await self._install_via_package_manager(tool_name)
        elif method == InstallationMethod.DIRECT_DOWNLOAD:
            return await self._install_via_direct_download(tool_name, tool_def)
        elif method == InstallationMethod.BINARY_RELEASE:
            return await self._install_via_binary_release(tool_name, tool_def)
        elif method == InstallationMethod.LANGUAGE_PACKAGE:
            return await self._install_via_language_package(tool_name, tool_def)
        elif method == InstallationMethod.SOURCE_COMPILE:
            return await self._install_via_source_compile(tool_name, tool_def)
        elif method == InstallationMethod.CONTAINER_PULL:
            return await self._install_via_container_pull(tool_name, tool_def)
        else:
            return False, f"Unsupported installation method: {method}", None
    
    async def _install_via_package_manager(self, tool_name: str) -> Tuple[bool, str, Optional[str]]:
        """Install via system package manager"""
        
        # Try each available package manager
        for pm_name, pm_commands in self.package_managers.items():
            if pm_name in ['pip', 'npm', 'gem', 'cargo', 'go']:
                continue  # Skip language package managers for this method
            
            try:
                install_cmd = pm_commands['install'].format(tool_name)
                
                logger.info(f"📦 Installing {tool_name} via {pm_name}: {install_cmd}")
                
                # Execute installation command
                process = await asyncio.create_subprocess_shell(
                    install_cmd,
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )
                
                stdout, stderr = await process.communicate()
                
                if process.returncode == 0:
                    # Try to get version
                    version = await self._get_tool_version(tool_name)
                    return True, "", version
                else:
                    error_msg = stderr.decode('utf-8', errors='replace')
                    return False, f"{pm_name} installation failed: {error_msg}", None
                    
            except Exception as e:
                continue  # Try next package manager
        
        return False, "No suitable package manager found", None
    
    async def _install_via_language_package(self, tool_name: str, tool_def: ToolInstallation) -> Tuple[bool, str, Optional[str]]:
        """Install via language-specific package manager"""
        
        # Define language package mappings
        language_packages = {
            'nuclei': ('go', 'github.com/projectdiscovery/nuclei/v2/cmd/nuclei'),
            'gobuster': ('go', 'github.com/OJ/gobuster/v3'),
            'ffuf': ('go', 'github.com/ffuf/ffuf'),
            'feroxbuster': ('cargo', 'feroxbuster'),
            'wpscan': ('gem', 'wpscan'),
            'sslyze': ('pip', 'sslyze'),
            'whatweb': ('gem', 'whatweb')
        }
        
        if tool_name not in language_packages:
            return False, "No language package mapping found", None
        
        lang_manager, package_name = language_packages[tool_name]
        
        if lang_manager not in self.package_managers:
            return False, f"Language package manager {lang_manager} not available", None
        
        try:
            install_cmd = self.package_managers[lang_manager]['install'].format(package_name)
            
            logger.info(f"🔧 Installing {tool_name} via {lang_manager}: {install_cmd}")
            
            process = await asyncio.create_subprocess_shell(
                install_cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await process.communicate()
            
            if process.returncode == 0:
                version = await self._get_tool_version(tool_name)
                return True, "", version
            else:
                error_msg = stderr.decode('utf-8', errors='replace')
                return False, f"{lang_manager} installation failed: {error_msg}", None
                
        except Exception as e:
            return False, f"Language package installation failed: {str(e)}", None
    
    async def _install_via_binary_release(self, tool_name: str, tool_def: ToolInstallation) -> Tuple[bool, str, Optional[str]]:
        """Install via binary release download"""
        
        # Define binary release URLs
        binary_releases = {
            'nuclei': 'https://github.com/projectdiscovery/nuclei/releases/latest/download/nuclei_{version}_linux_amd64.zip',
            'gobuster': 'https://github.com/OJ/gobuster/releases/latest/download/gobuster-linux-amd64.7z',
            'ffuf': 'https://github.com/ffuf/ffuf/releases/latest/download/ffuf_{version}_linux_amd64.tar.gz',
            'feroxbuster': 'https://github.com/epi052/feroxbuster/releases/latest/download/feroxbuster-{version}-x86_64-linux.tar.gz'
        }
        
        if tool_name not in binary_releases:
            return False, "No binary release URL defined", None
        
        try:
            # For now, fallback to package manager method
            # In a full implementation, this would download and install binaries
            return await self._install_via_package_manager(tool_name)
            
        except Exception as e:
            return False, f"Binary release installation failed: {str(e)}", None
    
    async def _install_via_direct_download(self, tool_name: str, tool_def: ToolInstallation) -> Tuple[bool, str, Optional[str]]:
        """Install via direct download"""
        
        # Define direct download commands
        download_commands = {
            'testssl.sh': [
                'git clone https://github.com/drwetter/testssl.sh.git /tmp/testssl',
                f'cp /tmp/testssl/testssl.sh {self.local_bin_path}/testssl.sh',
                f'chmod +x {self.local_bin_path}/testssl.sh'
            ],
            'sqlmap': [
                'git clone https://github.com/sqlmapproject/sqlmap.git /tmp/sqlmap',
                f'cp -r /tmp/sqlmap {self.tool_cache_path}/',
                f'ln -sf {self.tool_cache_path}/sqlmap/sqlmap.py {self.local_bin_path}/sqlmap'
            ],
            'searchsploit': [
                'git clone https://github.com/offensive-security/exploitdb.git /tmp/exploitdb',
                f'cp -r /tmp/exploitdb {self.tool_cache_path}/',
                f'ln -sf {self.tool_cache_path}/exploitdb/searchsploit {self.local_bin_path}/searchsploit'
            ],
            'metasploit': [
                'curl -L https://raw.githubusercontent.com/rapid7/metasploit-omnibus/master/config/templates/metasploit-framework-wrappers/msfupdate.erb -o /tmp/install_metasploit.sh',
                'bash /tmp/install_metasploit.sh'
            ]
        }
        
        if tool_name not in download_commands:
            return False, "No direct download commands defined", None
        
        try:
            commands = download_commands[tool_name]
            
            for cmd in commands:
                logger.info(f"🔽 Executing: {cmd}")
                
                process = await asyncio.create_subprocess_shell(
                    cmd,
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )
                
                stdout, stderr = await process.communicate()
                
                if process.returncode != 0:
                    error_msg = stderr.decode('utf-8', errors='replace')
                    return False, f"Download command failed: {error_msg}", None
            
            version = await self._get_tool_version(tool_name)
            return True, "", version
            
        except Exception as e:
            return False, f"Direct download failed: {str(e)}", None
    
    async def _install_via_source_compile(self, tool_name: str, tool_def: ToolInstallation) -> Tuple[bool, str, Optional[str]]:
        """Install via source compilation"""
        
        # For now, fallback to package manager
        # In a full implementation, this would download source and compile
        return await self._install_via_package_manager(tool_name)
    
    async def _install_via_container_pull(self, tool_name: str, tool_def: ToolInstallation) -> Tuple[bool, str, Optional[str]]:
        """Install via container pull"""
        
        if not self.capabilities.get('docker'):
            return False, "Docker not available", None
        
        # Container images
        images = {
            'metasploit': 'metasploitframework/metasploit-framework:latest',
            'sqlmap': 'paolobasso/sqlmap:latest',
            'nuclei': 'projectdiscovery/nuclei:latest'
        }
        
        if tool_name not in images:
            return False, "No container image defined", None
        
        try:
            image = images[tool_name]
            
            logger.info(f"🐳 Pulling container image: {image}")
            
            process = await asyncio.create_subprocess_shell(
                f"docker pull {image}",
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await process.communicate()
            
            if process.returncode == 0:
                return True, "", "container"
            else:
                error_msg = stderr.decode('utf-8', errors='replace')
                return False, f"Container pull failed: {error_msg}", None
                
        except Exception as e:
            return False, f"Container installation failed: {str(e)}", None
    
    async def _install_dependency(self, dependency: str) -> bool:
        """Install a dependency"""
        
        try:
            # Try package manager installation
            for pm_name, pm_commands in self.package_managers.items():
                if pm_name in ['pip', 'npm', 'gem', 'cargo', 'go']:
                    continue
                
                install_cmd = pm_commands['install'].format(dependency)
                
                process = await asyncio.create_subprocess_shell(
                    install_cmd,
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )
                
                await process.communicate()
                
                if process.returncode == 0:
                    return True
            
            return False
            
        except Exception as e:
            logger.warning(f"Failed to install dependency {dependency}: {e}")
            return False
    
    async def _is_tool_installed(self, tool_name: str) -> bool:
        """Check if a tool is installed and available"""
        
        try:
            # Check PATH
            if shutil.which(tool_name):
                return True
            
            # Check local bin path
            local_tool_path = self.local_bin_path / tool_name
            if local_tool_path.exists() and local_tool_path.is_file():
                return True
            
            # Check for alternative names
            alternatives = {
                'testssl.sh': ['testssl'],
                'msfconsole': ['msfconsole', 'metasploit']
            }
            
            if tool_name in alternatives:
                for alt in alternatives[tool_name]:
                    if shutil.which(alt):
                        return True
            
            return False
            
        except Exception:
            return False
    
    async def _verify_installation(self, tool_name: str, tool_def: ToolInstallation) -> bool:
        """Verify tool installation"""
        
        try:
            for verify_cmd in tool_def.verify_commands:
                process = await asyncio.create_subprocess_shell(
                    verify_cmd,
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )
                
                await process.communicate()
                
                if process.returncode == 0:
                    return True
            
            # Fallback to basic availability check
            return await self._is_tool_installed(tool_name)
            
        except Exception:
            return False
    
    async def _get_tool_version(self, tool_name: str) -> Optional[str]:
        """Get tool version"""
        
        version_commands = [
            f"{tool_name} --version",
            f"{tool_name} -version",
            f"{tool_name} -V",
            f"{tool_name} version"
        ]
        
        for cmd in version_commands:
            try:
                process = await asyncio.create_subprocess_shell(
                    cmd,
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )
                
                stdout, stderr = await process.communicate()
                
                if process.returncode == 0:
                    output = stdout.decode('utf-8', errors='replace')
                    # Extract version from output (simplified)
                    return output.split('\n')[0][:100]
                    
            except Exception:
                continue
        
        return None
    
    async def _run_post_install_commands(self, tool_def: ToolInstallation):
        """Run post-installation commands"""
        
        for cmd in tool_def.post_install_commands:
            try:
                process = await asyncio.create_subprocess_shell(
                    cmd,
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )
                
                await process.communicate()
                
            except Exception as e:
                logger.warning(f"Post-install command failed: {cmd} - {e}")
    
    async def install_missing_tools(self, required_tools: List[str]) -> Dict[str, InstallationResult]:
        """Install multiple missing tools"""
        
        results = {}
        
        for tool_name in required_tools:
            result = await self.auto_install_tool(tool_name)
            results[tool_name] = result
            
            if result.status == InstallationStatus.SUCCESS:
                logger.info(f"✅ {tool_name} installation completed")
            else:
                logger.warning(f"⚠️ {tool_name} installation failed: {result.error_message}")
        
        return results
    
    async def system_health_check(self) -> Dict[str, Any]:
        """Perform comprehensive system health check"""
        
        health_report = {
            'environment': self.environment.value,
            'capabilities': self.capabilities,
            'package_managers': list(self.package_managers.keys()),
            'installed_tools': {},
            'missing_tools': [],
            'failed_tools': list(self.failed_installations),
            'recommendations': []
        }
        
        # Check all known tools
        for tool_name in self.tool_installations.keys():
            is_installed = await self._is_tool_installed(tool_name)
            health_report['installed_tools'][tool_name] = is_installed
            
            if not is_installed:
                health_report['missing_tools'].append(tool_name)
        
        # Generate recommendations
        if health_report['missing_tools']:
            health_report['recommendations'].append(
                f"Install missing tools: {', '.join(health_report['missing_tools'][:5])}"
            )
        
        if not self.package_managers:
            health_report['recommendations'].append(
                "No package managers detected - manual installation required"
            )
        
        return health_report
    
    def get_installation_cache(self) -> Dict[str, InstallationResult]:
        """Get installation cache"""
        return self.installation_cache.copy()
    
    def clear_failed_installations(self):
        """Clear failed installation cache"""
        self.failed_installations.clear()
        logger.info("🧹 Cleared failed installation cache")

# Global self-healing manager
self_healing_manager = None

async def get_self_healing_manager() -> SelfHealingManager:
    """Get or create self-healing manager"""
    global self_healing_manager
    
    if self_healing_manager is None:
        self_healing_manager = SelfHealingManager()
    
    return self_healing_manager

async def auto_install_tool(tool_name: str) -> InstallationResult:
    """Convenience function to auto-install a tool"""
    manager = await get_self_healing_manager()
    return await manager.auto_install_tool(tool_name)