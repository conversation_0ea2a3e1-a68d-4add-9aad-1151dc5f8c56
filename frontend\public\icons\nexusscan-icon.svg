<svg width="512" height="512" viewBox="0 0 512 512" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background Circle -->
  <circle cx="256" cy="256" r="240" fill="#3b82f6" stroke="#1e40af" stroke-width="8"/>
  
  <!-- Shield Background -->
  <path d="M256 80L180 120V200C180 280 220 340 256 360C292 340 332 280 332 200V120L256 80Z" fill="#1e40af"/>
  
  <!-- Shield Outline -->
  <path d="M256 80L180 120V200C180 280 220 340 256 360C292 340 332 280 332 200V120L256 80Z" stroke="#ffffff" stroke-width="4" fill="none"/>
  
  <!-- Scan Lines -->
  <g opacity="0.8">
    <!-- Horizontal scan lines -->
    <line x1="200" y1="140" x2="312" y2="140" stroke="#60a5fa" stroke-width="3"/>
    <line x1="200" y1="160" x2="312" y2="160" stroke="#60a5fa" stroke-width="3"/>
    <line x1="200" y1="180" x2="312" y2="180" stroke="#60a5fa" stroke-width="3"/>
    <line x1="200" y1="200" x2="312" y2="200" stroke="#60a5fa" stroke-width="3"/>
    <line x1="200" y1="220" x2="312" y2="220" stroke="#60a5fa" stroke-width="3"/>
    <line x1="200" y1="240" x2="312" y2="240" stroke="#60a5fa" stroke-width="3"/>
    <line x1="200" y1="260" x2="312" y2="260" stroke="#60a5fa" stroke-width="3"/>
    <line x1="200" y1="280" x2="312" y2="280" stroke="#60a5fa" stroke-width="3"/>
    <line x1="200" y1="300" x2="312" y2="300" stroke="#60a5fa" stroke-width="3"/>
  </g>
  
  <!-- Central Scanner Element -->
  <circle cx="256" cy="220" r="20" fill="#ffffff" opacity="0.9"/>
  <circle cx="256" cy="220" r="12" fill="#3b82f6"/>
  <circle cx="256" cy="220" r="6" fill="#ffffff"/>
  
  <!-- Radar Sweep Animation -->
  <g opacity="0.6">
    <path d="M256 220 L276 200 A20 20 0 0 1 276 240 Z" fill="#10b981">
      <animateTransform
        attributeName="transform"
        attributeType="XML"
        type="rotate"
        from="0 256 220"
        to="360 256 220"
        dur="3s"
        repeatCount="indefinite"/>
    </path>
  </g>
  
  <!-- Network Nodes -->
  <circle cx="220" cy="180" r="4" fill="#ffffff"/>
  <circle cx="292" cy="180" r="4" fill="#ffffff"/>
  <circle cx="220" cy="260" r="4" fill="#ffffff"/>
  <circle cx="292" cy="260" r="4" fill="#ffffff"/>
  
  <!-- Connection Lines -->
  <line x1="256" y1="220" x2="220" y2="180" stroke="#ffffff" stroke-width="2" opacity="0.7"/>
  <line x1="256" y1="220" x2="292" y2="180" stroke="#ffffff" stroke-width="2" opacity="0.7"/>
  <line x1="256" y1="220" x2="220" y2="260" stroke="#ffffff" stroke-width="2" opacity="0.7"/>
  <line x1="256" y1="220" x2="292" y2="260" stroke="#ffffff" stroke-width="2" opacity="0.7"/>
  
  <!-- Text -->
  <text x="256" y="420" text-anchor="middle" fill="#ffffff" font-family="Arial, sans-serif" font-size="32" font-weight="bold">NexusScan</text>
  <text x="256" y="450" text-anchor="middle" fill="#93c5fd" font-family="Arial, sans-serif" font-size="18">Security Scanner</text>
</svg>
