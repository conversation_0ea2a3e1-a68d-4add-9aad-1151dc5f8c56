#!/usr/bin/env python3
"""
Health Check System for NexusScan Platform
Provides comprehensive service health monitoring
"""

import asyncio
import time
import json
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from enum import Enum
from pathlib import Path

from core.metrics import metrics, set_system_resource_usage

logger = logging.getLogger(__name__)

class HealthStatus(Enum):
    HEALTHY = "healthy"
    DEGRADED = "degraded"
    UNHEALTHY = "unhealthy"
    UNKNOWN = "unknown"

@dataclass
class ComponentHealth:
    name: str
    status: HealthStatus
    response_time_ms: float
    last_check: datetime
    error_message: Optional[str] = None
    metrics: Dict[str, Any] = None

    def __post_init__(self):
        if self.metrics is None:
            self.metrics = {}

@dataclass
class SystemHealth:
    overall_status: HealthStatus
    timestamp: datetime
    components: List[ComponentHealth]
    system_metrics: Dict[str, Any]
    uptime_seconds: float

class HealthChecker:
    def __init__(self, config=None):
        self.config = config
        self.start_time = time.time()
        self.health_history: List[SystemHealth] = []
        
    async def check_system_health(self) -> SystemHealth:
        """Perform comprehensive system health check"""
        components = []
        
        # Check database health
        components.append(await self._check_database_health())
        
        # Check AI services health
        components.append(await self._check_ai_services_health())
        
        # Check security tools health
        components.append(await self._check_security_tools_health())
        
        # Check WSL integration health (if applicable)
        components.append(await self._check_wsl_health())
        
        # Check system resources
        components.append(await self._check_system_resources())
        
        # Check file system health
        components.append(await self._check_filesystem_health())
        
        # Determine overall status
        overall_status = self._determine_overall_status(components)
        
        system_health = SystemHealth(
            overall_status=overall_status,
            timestamp=datetime.now(),
            components=components,
            system_metrics=self._get_system_metrics(),
            uptime_seconds=time.time() - self.start_time
        )
        
        # Store in history (keep last 100 checks)
        self.health_history.append(system_health)
        if len(self.health_history) > 100:
            self.health_history.pop(0)
            
        return system_health
    
    async def _check_database_health(self) -> ComponentHealth:
        """Check database connectivity and performance"""
        start_time = time.time()
        
        try:
            # Import here to avoid circular dependencies
            from core.database import DatabaseManager
            from core.config import Config
            
            # Test database connection
            config = self.config or Config()
            db_manager = DatabaseManager(config.database)
            
            # Simple query test
            session = db_manager.get_session()
            try:
                # Test basic database operations
                result = session.execute("SELECT 1").fetchone()
                if result is None:
                    raise Exception("Database query returned no result")
                
                # Test table existence
                tables_query = "SELECT name FROM sqlite_master WHERE type='table'"
                tables = session.execute(tables_query).fetchall()
                table_count = len(tables)
                
            finally:
                session.close()
                
            response_time = (time.time() - start_time) * 1000
            
            return ComponentHealth(
                name="database",
                status=HealthStatus.HEALTHY,
                response_time_ms=response_time,
                last_check=datetime.now(),
                metrics={
                    "connection_successful": True,
                    "query_response_time_ms": response_time,
                    "table_count": table_count,
                    "database_file_exists": Path(config.database.database_path).exists()
                }
            )
            
        except Exception as e:
            response_time = (time.time() - start_time) * 1000
            logger.error(f"Database health check failed: {e}")
            return ComponentHealth(
                name="database",
                status=HealthStatus.UNHEALTHY,
                response_time_ms=response_time,
                last_check=datetime.now(),
                error_message=str(e)
            )
    
    async def _check_ai_services_health(self) -> ComponentHealth:
        """Check AI services availability"""
        start_time = time.time()
        
        try:
            # Import here to avoid circular dependencies
            from ai.services import AIServiceManager
            from core.config import Config
            
            config = self.config or Config()
            ai_manager = AIServiceManager(config)
            available_providers = ai_manager.get_available_providers()
            cache_stats = ai_manager.get_cache_stats()
            
            response_time = (time.time() - start_time) * 1000
            
            # Determine status based on available providers
            if len(available_providers) >= 2:
                status = HealthStatus.HEALTHY
            elif len(available_providers) == 1:
                status = HealthStatus.DEGRADED
            else:
                status = HealthStatus.UNHEALTHY
            
            return ComponentHealth(
                name="ai_services",
                status=status,
                response_time_ms=response_time,
                last_check=datetime.now(),
                metrics={
                    "available_providers": len(available_providers),
                    "providers_list": available_providers,
                    "cache_stats": cache_stats,
                    "cache_size": cache_stats.get("size", 0),
                    "cache_hit_ratio": cache_stats.get("hit_ratio", 0.0)
                }
            )
            
        except Exception as e:
            response_time = (time.time() - start_time) * 1000
            logger.error(f"AI services health check failed: {e}")
            return ComponentHealth(
                name="ai_services",
                status=HealthStatus.UNHEALTHY,
                response_time_ms=response_time,
                last_check=datetime.now(),
                error_message=str(e)
            )
    
    async def _check_security_tools_health(self) -> ComponentHealth:
        """Check security tools availability"""
        start_time = time.time()
        
        try:
            # Import here to avoid circular dependencies
            from security.unified_tool_manager import UnifiedToolManager
            from core.config import Config
            
            config = self.config or Config()
            tool_manager = UnifiedToolManager(config)
            
            # Check available tools
            available_tools = tool_manager.get_available_tools()
            tool_count = len(available_tools)
            
            response_time = (time.time() - start_time) * 1000
            
            # Determine status based on available tools
            if tool_count >= 5:
                status = HealthStatus.HEALTHY
            elif tool_count >= 2:
                status = HealthStatus.DEGRADED
            else:
                status = HealthStatus.UNHEALTHY
            
            return ComponentHealth(
                name="security_tools",
                status=status,
                response_time_ms=response_time,
                last_check=datetime.now(),
                metrics={
                    "available_tools": tool_count,
                    "tools_list": available_tools,
                    "windows_tools_available": hasattr(tool_manager, 'windows_manager'),
                    "wsl_available": hasattr(tool_manager, 'wsl_manager')
                }
            )
            
        except Exception as e:
            response_time = (time.time() - start_time) * 1000
            logger.error(f"Security tools health check failed: {e}")
            return ComponentHealth(
                name="security_tools",
                status=HealthStatus.UNHEALTHY,
                response_time_ms=response_time,
                last_check=datetime.now(),
                error_message=str(e)
            )
    
    async def _check_wsl_health(self) -> ComponentHealth:
        """Check WSL integration health"""
        start_time = time.time()
        
        try:
            import subprocess
            import platform
            
            if platform.system() != "Windows":
                # WSL only relevant on Windows
                return ComponentHealth(
                    name="wsl_integration",
                    status=HealthStatus.HEALTHY,
                    response_time_ms=(time.time() - start_time) * 1000,
                    last_check=datetime.now(),
                    metrics={"platform": platform.system(), "wsl_applicable": False}
                )
            
            # Test WSL availability
            result = subprocess.run(
                ["wsl", "--status"],
                capture_output=True,
                text=True,
                timeout=5
            )
            
            wsl_available = result.returncode == 0
            response_time = (time.time() - start_time) * 1000
            
            status = HealthStatus.HEALTHY if wsl_available else HealthStatus.DEGRADED
            
            return ComponentHealth(
                name="wsl_integration",
                status=status,
                response_time_ms=response_time,
                last_check=datetime.now(),
                metrics={
                    "wsl_available": wsl_available,
                    "wsl_status": result.stdout if wsl_available else result.stderr,
                    "platform": platform.system()
                }
            )
            
        except Exception as e:
            response_time = (time.time() - start_time) * 1000
            logger.warning(f"WSL health check failed: {e}")
            return ComponentHealth(
                name="wsl_integration",
                status=HealthStatus.DEGRADED,
                response_time_ms=response_time,
                last_check=datetime.now(),
                error_message=str(e)
            )
    
    async def _check_system_resources(self) -> ComponentHealth:
        """Check system resource availability"""
        start_time = time.time()
        
        try:
            # Try to import psutil for detailed system metrics
            try:
                import psutil
                
                memory = psutil.virtual_memory()
                disk = psutil.disk_usage('.')
                cpu_percent = psutil.cpu_percent(interval=0.1)
                
                # Record system metrics
                set_system_resource_usage(cpu_percent, memory.percent, (disk.used / disk.total) * 100)
                
                component_metrics = {
                    "memory_available_gb": memory.available / (1024**3),
                    "memory_used_percent": memory.percent,
                    "disk_free_gb": disk.free / (1024**3),
                    "disk_used_percent": (disk.used / disk.total) * 100,
                    "cpu_percent": cpu_percent,
                    "detailed_metrics": True
                }
                
                # Determine status based on resource usage
                if memory.percent > 90 or disk.used / disk.total > 0.95 or cpu_percent > 95:
                    status = HealthStatus.UNHEALTHY
                elif memory.percent > 80 or disk.used / disk.total > 0.85 or cpu_percent > 80:
                    status = HealthStatus.DEGRADED
                else:
                    status = HealthStatus.HEALTHY
                    
            except ImportError:
                # Fallback without psutil
                import shutil
                disk_usage = shutil.disk_usage('.')
                
                component_metrics = {
                    "disk_free_gb": disk_usage.free / (1024**3),
                    "disk_total_gb": disk_usage.total / (1024**3),
                    "detailed_metrics": False
                }
                
                if disk_usage.free < 1024**3:  # Less than 1GB free
                    status = HealthStatus.UNHEALTHY
                elif disk_usage.free < 5 * 1024**3:  # Less than 5GB free
                    status = HealthStatus.DEGRADED
                else:
                    status = HealthStatus.HEALTHY
            
            response_time = (time.time() - start_time) * 1000
            
            return ComponentHealth(
                name="system_resources",
                status=status,
                response_time_ms=response_time,
                last_check=datetime.now(),
                metrics=component_metrics
            )
            
        except Exception as e:
            response_time = (time.time() - start_time) * 1000
            logger.error(f"System resources health check failed: {e}")
            return ComponentHealth(
                name="system_resources",
                status=HealthStatus.UNKNOWN,
                response_time_ms=response_time,
                last_check=datetime.now(),
                error_message=str(e)
            )
    
    async def _check_filesystem_health(self) -> ComponentHealth:
        """Check file system health and permissions"""
        start_time = time.time()
        
        try:
            from pathlib import Path
            
            # Check critical directories
            critical_paths = [
                Path("src"),
                Path("data"),
                Path("logs") if Path("logs").exists() else None,
                Path("scripts")
            ]
            
            critical_paths = [p for p in critical_paths if p is not None]
            
            readable_count = 0
            writable_count = 0
            
            for path in critical_paths:
                if path.exists():
                    if path.is_readable():
                        readable_count += 1
                    if path.is_writable():
                        writable_count += 1
            
            # Test write permissions in data directory
            test_file = Path("data") / ".health_test"
            write_test_success = False
            try:
                test_file.write_text("test")
                test_file.unlink()
                write_test_success = True
            except Exception:
                pass
            
            response_time = (time.time() - start_time) * 1000
            
            # Determine status
            if readable_count == len(critical_paths) and write_test_success:
                status = HealthStatus.HEALTHY
            elif readable_count >= len(critical_paths) * 0.8:
                status = HealthStatus.DEGRADED
            else:
                status = HealthStatus.UNHEALTHY
            
            return ComponentHealth(
                name="filesystem",
                status=status,
                response_time_ms=response_time,
                last_check=datetime.now(),
                metrics={
                    "critical_paths_count": len(critical_paths),
                    "readable_paths": readable_count,
                    "writable_paths": writable_count,
                    "write_test_success": write_test_success,
                    "paths_checked": [str(p) for p in critical_paths]
                }
            )
            
        except Exception as e:
            response_time = (time.time() - start_time) * 1000
            logger.error(f"Filesystem health check failed: {e}")
            return ComponentHealth(
                name="filesystem",
                status=HealthStatus.UNKNOWN,
                response_time_ms=response_time,
                last_check=datetime.now(),
                error_message=str(e)
            )
    
    def _determine_overall_status(self, components: List[ComponentHealth]) -> HealthStatus:
        """Determine overall system status from component statuses"""
        if not components:
            return HealthStatus.UNKNOWN
        
        unhealthy_count = sum(1 for c in components if c.status == HealthStatus.UNHEALTHY)
        degraded_count = sum(1 for c in components if c.status == HealthStatus.DEGRADED)
        healthy_count = sum(1 for c in components if c.status == HealthStatus.HEALTHY)
        
        # System is unhealthy if any critical component is unhealthy
        critical_components = {"database", "filesystem"}
        for component in components:
            if component.name in critical_components and component.status == HealthStatus.UNHEALTHY:
                return HealthStatus.UNHEALTHY
        
        # System is unhealthy if majority of components are unhealthy
        if unhealthy_count > len(components) / 2:
            return HealthStatus.UNHEALTHY
        
        # System is degraded if any component is unhealthy or many are degraded
        if unhealthy_count > 0 or degraded_count > len(components) / 3:
            return HealthStatus.DEGRADED
        
        # System is healthy if all components are healthy
        if healthy_count == len(components):
            return HealthStatus.HEALTHY
        
        # Default to degraded for mixed states
        return HealthStatus.DEGRADED
    
    def _get_system_metrics(self) -> Dict[str, Any]:
        """Get system-level metrics"""
        import platform
        
        metrics = {
            "platform": platform.system(),
            "platform_release": platform.release(),
            "python_version": platform.python_version(),
            "uptime_seconds": time.time() - self.start_time,
            "health_checks_performed": len(self.health_history),
            "timestamp": datetime.now().isoformat()
        }
        
        try:
            import psutil
            metrics.update({
                "process_count": len(psutil.pids()),
                "boot_time": psutil.boot_time()
            })
        except ImportError:
            pass
        
        return metrics
    
    def get_health_history(self, hours: int = 24) -> List[SystemHealth]:
        """Get health check history for the specified time period"""
        cutoff = datetime.now() - timedelta(hours=hours)
        return [h for h in self.health_history if h.timestamp >= cutoff]
    
    def to_dict(self, health: SystemHealth) -> Dict[str, Any]:
        """Convert SystemHealth to dictionary for JSON serialization"""
        return {
            "overall_status": health.overall_status.value,
            "timestamp": health.timestamp.isoformat(),
            "uptime_seconds": health.uptime_seconds,
            "system_metrics": health.system_metrics,
            "components": [
                {
                    "name": comp.name,
                    "status": comp.status.value,
                    "response_time_ms": comp.response_time_ms,
                    "last_check": comp.last_check.isoformat(),
                    "error_message": comp.error_message,
                    "metrics": comp.metrics
                }
                for comp in health.components
            ]
        }

# Convenience function for quick health checks
async def quick_health_check(config=None) -> Dict[str, Any]:
    """Perform a quick health check and return results as dictionary"""
    checker = HealthChecker(config)
    health = await checker.check_system_health()
    return checker.to_dict(health)