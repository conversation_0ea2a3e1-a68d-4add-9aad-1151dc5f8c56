"""
Post-Exploitation Simulation Framework for NexusScan Desktop
Advanced simulation of post-exploitation scenarios with AI-guided execution.
"""

import asyncio
import logging
import json
import time
import os
from typing import Dict, List, Optional, Any, Set, Tuple
from enum import Enum
from datetime import datetime, timed<PERSON>ta
from dataclasses import dataclass, asdict
from pathlib import Path

from core.config import Config
from core.database import DatabaseManager
from core.events import EventManager, EventTypes
from ai.ai_service import AIServiceManager
from security.unified_tool_manager import UnifiedToolManager

logger = logging.getLogger(__name__)


class ExploitationPhase(Enum):
    """Post-exploitation phases"""
    INITIAL_ACCESS = "initial_access"
    DISCOVERY = "discovery"
    PRIVILEGE_ESCALATION = "privilege_escalation"
    PERSISTENCE = "persistence"
    DEFENSE_EVASION = "defense_evasion"
    CREDENTIAL_ACCESS = "credential_access"
    LATERAL_MOVEMENT = "lateral_movement"
    COLLECTION = "collection"
    COMMAND_CONTROL = "command_control"
    EXFILTRATION = "exfiltration"
    IMPACT = "impact"


class SimulationStatus(Enum):
    """Simulation execution status"""
    PENDING = "pending"
    RUNNING = "running"
    PAUSED = "paused"
    COMPLETED = "completed"
    FAILED = "failed"
    STOPPED = "stopped"


@dataclass
class ExploitationStep:
    """Individual exploitation step"""
    step_id: str
    phase: ExploitationPhase
    technique_id: str  # MITRE ATT&CK technique ID
    technique_name: str
    description: str
    command: str
    expected_output: str
    success_indicators: List[str]
    failure_indicators: List[str]
    prerequisites: List[str]
    risk_level: str  # low, medium, high, critical
    estimated_duration: int  # seconds
    ai_guidance: Optional[str] = None


@dataclass
class SimulationResult:
    """Result of simulation step execution"""
    step_id: str
    status: str  # success, failure, partial, skipped
    output: str
    duration: float
    timestamp: datetime
    artifacts_collected: List[str]
    indicators_found: List[str]
    next_steps: List[str]
    ai_analysis: Optional[str] = None


@dataclass
class SimulationScenario:
    """Complete post-exploitation scenario"""
    scenario_id: str
    name: str
    description: str
    target_type: str  # windows, linux, web_app, network
    difficulty_level: str  # beginner, intermediate, advanced, expert
    estimated_duration: int  # minutes
    steps: List[ExploitationStep]
    success_criteria: List[str]
    learning_objectives: List[str]
    mitre_techniques: List[str]
    compliance_frameworks: List[str]


class PostExploitationSimulator:
    """Advanced post-exploitation simulation framework"""
    
    def __init__(self, config: Config, db_manager: DatabaseManager, 
                 event_manager: EventManager, ai_service: AIServiceManager):
        """Initialize post-exploitation simulator"""
        self.config = config
        self.db_manager = db_manager
        self.event_manager = event_manager
        self.ai_service = ai_service
        
        # Simulation state
        self.active_simulations: Dict[str, Dict[str, Any]] = {}
        self.scenario_templates: Dict[str, SimulationScenario] = {}
        self.execution_history: List[SimulationResult] = []
        
        # Load built-in scenarios
        self._load_builtin_scenarios()
        
        logger.info("Post-exploitation simulator initialized")
    
    def _load_builtin_scenarios(self):
        """Load built-in simulation scenarios"""
        # Windows Domain Enumeration Scenario
        windows_enum = SimulationScenario(
            scenario_id="win_domain_enum",
            name="Windows Domain Enumeration",
            description="Comprehensive Active Directory reconnaissance and enumeration",
            target_type="windows",
            difficulty_level="intermediate",
            estimated_duration=45,
            steps=[
                ExploitationStep(
                    step_id="domain_info",
                    phase=ExploitationPhase.DISCOVERY,
                    technique_id="T1087.002",
                    technique_name="Domain Account Discovery",
                    description="Enumerate domain information and trust relationships",
                    command="nltest /domain_trusts /all_trusts",
                    expected_output="Domain trust information",
                    success_indicators=["Domain Name", "Trust Type", "Trust Direction"],
                    failure_indicators=["Access denied", "Command not found"],
                    prerequisites=["Domain-joined system", "User credentials"],
                    risk_level="low",
                    estimated_duration=30
                ),
                ExploitationStep(
                    step_id="user_enum",
                    phase=ExploitationPhase.DISCOVERY,
                    technique_id="T1087.002",
                    technique_name="Domain Account Discovery",
                    description="Enumerate domain users and groups",
                    command="net user /domain",
                    expected_output="List of domain users",
                    success_indicators=["User accounts", "Account active"],
                    failure_indicators=["System error", "Access denied"],
                    prerequisites=["Domain connectivity"],
                    risk_level="low",
                    estimated_duration=45
                ),
                ExploitationStep(
                    step_id="admin_enum",
                    phase=ExploitationPhase.DISCOVERY,
                    technique_id="T1069.002",
                    technique_name="Domain Groups Discovery",
                    description="Identify domain administrators and privileged accounts",
                    command="net group \"Domain Admins\" /domain",
                    expected_output="Domain administrator accounts",
                    success_indicators=["Administrator accounts", "Group members"],
                    failure_indicators=["Group does not exist", "Access denied"],
                    prerequisites=["Domain user access"],
                    risk_level="medium",
                    estimated_duration=20
                )
            ],
            success_criteria=[
                "Domain structure mapped",
                "User accounts enumerated",
                "Privileged accounts identified"
            ],
            learning_objectives=[
                "Understand Active Directory enumeration",
                "Identify privilege escalation targets",
                "Map attack surface"
            ],
            mitre_techniques=["T1087.002", "T1069.002", "T1482"],
            compliance_frameworks=["NIST", "CIS", "SOC2"]
        )
        
        # Linux Privilege Escalation Scenario
        linux_privesc = SimulationScenario(
            scenario_id="linux_privesc",
            name="Linux Privilege Escalation",
            description="Systematic Linux privilege escalation techniques",
            target_type="linux",
            difficulty_level="advanced",
            estimated_duration=60,
            steps=[
                ExploitationStep(
                    step_id="system_enum",
                    phase=ExploitationPhase.DISCOVERY,
                    technique_id="T1082",
                    technique_name="System Information Discovery",
                    description="Gather system information and kernel version",
                    command="uname -a && cat /etc/os-release",
                    expected_output="System and kernel information",
                    success_indicators=["Kernel version", "OS distribution"],
                    failure_indicators=["Permission denied", "File not found"],
                    prerequisites=["Shell access"],
                    risk_level="low",
                    estimated_duration=15
                ),
                ExploitationStep(
                    step_id="suid_search",
                    phase=ExploitationPhase.PRIVILEGE_ESCALATION,
                    technique_id="T1548.001",
                    technique_name="Setuid and Setgid",
                    description="Search for SUID/SGID binaries for privilege escalation",
                    command="find / -perm -4000 -type f 2>/dev/null",
                    expected_output="List of SUID binaries",
                    success_indicators=["SUID binaries found", "Potential exploits"],
                    failure_indicators=["No results", "Permission errors"],
                    prerequisites=["File system access"],
                    risk_level="medium",
                    estimated_duration=30
                ),
                ExploitationStep(
                    step_id="sudo_check",
                    phase=ExploitationPhase.PRIVILEGE_ESCALATION,
                    technique_id="T1548.003",
                    technique_name="Sudo and Sudo Caching",
                    description="Check sudo permissions and cached credentials",
                    command="sudo -l",
                    expected_output="Sudo permissions listing",
                    success_indicators=["NOPASSWD entries", "Sudo permissions"],
                    failure_indicators=["Password required", "No sudo access"],
                    prerequisites=["User account access"],
                    risk_level="high",
                    estimated_duration=20
                )
            ],
            success_criteria=[
                "System thoroughly enumerated",
                "Privilege escalation paths identified",
                "Root access achieved (simulation)"
            ],
            learning_objectives=[
                "Master Linux enumeration techniques",
                "Identify privilege escalation vectors",
                "Understand SUID/sudo vulnerabilities"
            ],
            mitre_techniques=["T1082", "T1548.001", "T1548.003"],
            compliance_frameworks=["NIST", "CIS", "PCI-DSS"]
        )
        
        self.scenario_templates = {
            "win_domain_enum": windows_enum,
            "linux_privesc": linux_privesc
        }
        
        logger.info(f"Loaded {len(self.scenario_templates)} built-in scenarios")
    
    async def start_simulation(self, scenario_id: str, target_config: Dict[str, Any], 
                             simulation_mode: str = "simulation") -> str:
        """Start post-exploitation simulation"""
        try:
            if scenario_id not in self.scenario_templates:
                raise ValueError(f"Unknown scenario: {scenario_id}")
            
            scenario = self.scenario_templates[scenario_id]
            sim_id = f"sim_{int(time.time())}"
            
            # Initialize simulation state
            simulation_state = {
                "simulation_id": sim_id,
                "scenario": scenario,
                "target_config": target_config,
                "mode": simulation_mode,
                "status": SimulationStatus.PENDING,
                "current_step": 0,
                "start_time": datetime.now(),
                "results": [],
                "artifacts": [],
                "ai_insights": []
            }
            
            self.active_simulations[sim_id] = simulation_state
            
            # Get AI pre-simulation analysis
            ai_analysis = await self._get_ai_simulation_guidance(scenario, target_config)
            simulation_state["ai_pre_analysis"] = ai_analysis
            
            # Emit simulation started event
            await self.event_manager.emit(
                EventTypes.SECURITY_SCAN_STARTED,
                {
                    "simulation_id": sim_id,
                    "scenario": scenario_id,
                    "target": target_config.get("target_name", "unknown"),
                    "mode": simulation_mode
                },
                "post_exploitation"
            )
            
            logger.info(f"Started simulation {sim_id} for scenario {scenario_id}")
            return sim_id
            
        except Exception as e:
            logger.error(f"Failed to start simulation: {e}")
            raise
    
    async def execute_simulation(self, simulation_id: str) -> Dict[str, Any]:
        """Execute post-exploitation simulation"""
        try:
            if simulation_id not in self.active_simulations:
                raise ValueError(f"Unknown simulation: {simulation_id}")
            
            simulation = self.active_simulations[simulation_id]
            simulation["status"] = SimulationStatus.RUNNING
            scenario = simulation["scenario"]
            
            logger.info(f"Executing simulation {simulation_id}")
            
            # Execute each step
            for i, step in enumerate(scenario.steps):
                simulation["current_step"] = i
                
                # Execute step with AI guidance
                result = await self._execute_simulation_step(step, simulation)
                simulation["results"].append(result)
                
                # Get AI analysis of step result
                ai_step_analysis = await self._get_ai_step_analysis(step, result)
                result.ai_analysis = ai_step_analysis
                
                # Check if step failed critically
                if result.status == "failure" and step.risk_level == "critical":
                    simulation["status"] = SimulationStatus.FAILED
                    break
                
                # Brief pause between steps
                await asyncio.sleep(1)
            
            # Finalize simulation
            simulation["status"] = SimulationStatus.COMPLETED
            simulation["end_time"] = datetime.now()
            simulation["duration"] = (simulation["end_time"] - simulation["start_time"]).total_seconds()
            
            # Get final AI analysis
            final_analysis = await self._get_ai_final_analysis(simulation)
            simulation["ai_final_analysis"] = final_analysis
            
            # Generate simulation report
            report = await self._generate_simulation_report(simulation)
            simulation["report"] = report
            
            # Emit simulation completed event
            await self.event_manager.emit(
                EventTypes.SECURITY_SCAN_COMPLETED,
                {
                    "simulation_id": simulation_id,
                    "status": simulation["status"].value,
                    "duration": simulation["duration"],
                    "steps_completed": len(simulation["results"]),
                    "success_rate": self._calculate_success_rate(simulation["results"])
                },
                "post_exploitation"
            )
            
            logger.info(f"Simulation {simulation_id} completed successfully")
            return simulation
            
        except Exception as e:
            logger.error(f"Simulation execution failed: {e}")
            if simulation_id in self.active_simulations:
                self.active_simulations[simulation_id]["status"] = SimulationStatus.FAILED
            raise
    
    async def _execute_simulation_step(self, step: ExploitationStep, 
                                     simulation: Dict[str, Any]) -> SimulationResult:
        """Execute individual simulation step"""
        start_time = time.time()
        
        try:
            # Execute based on mode - simulation or real
            if simulation["mode"] == "simulation":
                output = await self._simulate_command_output(step, simulation["target_config"])
                status = self._evaluate_simulated_output(step, output)
            elif simulation["mode"] == "real":
                output = await self._execute_real_command(step, simulation["target_config"])
                status = self._evaluate_real_output(step, output)
            else:
                raise ValueError(f"Unknown execution mode: {simulation['mode']}")
            
            duration = time.time() - start_time
            
            # Analyze output for artifacts and indicators
            artifacts = self._extract_artifacts(step, output)
            indicators = self._find_success_indicators(step, output)
            next_steps = self._suggest_next_steps(step, output)
            
            result = SimulationResult(
                step_id=step.step_id,
                status=status,
                output=output,
                duration=duration,
                timestamp=datetime.now(),
                artifacts_collected=artifacts,
                indicators_found=indicators,
                next_steps=next_steps
            )
            
            logger.info(f"Executed step {step.step_id}: {status}")
            return result
            
        except Exception as e:
            logger.error(f"Step execution failed: {e}")
            return SimulationResult(
                step_id=step.step_id,
                status="failure",
                output=f"Execution error: {str(e)}",
                duration=time.time() - start_time,
                timestamp=datetime.now(),
                artifacts_collected=[],
                indicators_found=[],
                next_steps=[]
            )
    
    async def _simulate_command_output(self, step: ExploitationStep, 
                                     target_config: Dict[str, Any]) -> str:
        """Simulate realistic command output for safe mode"""
        # Generate realistic output based on step type and target
        target_type = target_config.get("type", "unknown")
        
        if step.technique_id == "T1087.002":  # Domain Account Discovery
            return """Domain Name: EXAMPLE
Domain SID: S-1-5-21-*********-*********-*********
Trust(s):
    EXAMPLE.COM (NT 5) (Forest: EXAMPLE.COM) (Direct Outbound) (Direct Inbound) ( Attr: )"""
        
        elif step.technique_id == "T1069.002":  # Domain Groups Discovery
            return """Group name     Domain Admins
Comment        Designated administrators of the domain
Members
Administrator            john.doe            jane.smith"""
        
        elif step.technique_id == "T1082":  # System Information Discovery
            return """Linux hostname 5.4.0-74-generic #83-Ubuntu SMP Sat May 8 02:35:39 UTC 2021 x86_64 x86_64 x86_64 GNU/Linux
NAME="Ubuntu"
VERSION="20.04.2 LTS (Focal Fossa)" """
        
        elif step.technique_id == "T1548.001":  # SUID Discovery
            return """/usr/bin/sudo
/usr/bin/passwd
/usr/bin/gpasswd
/usr/bin/newgrp
/usr/bin/su
/usr/lib/openssh/ssh-keysign"""
        
        else:
            return step.expected_output
    
    async def _execute_real_command(self, step: ExploitationStep, 
                                  target_config: Dict[str, Any]) -> str:
        """Execute real command for actual penetration testing"""
        try:
            # WARNING: This executes real commands - use with extreme caution
            logger.warning(f"REAL EXECUTION MODE: Executing {step.technique_id}")
            
            # Get target connection details
            target_host = target_config.get("host", "localhost")
            target_user = target_config.get("username")
            target_password = target_config.get("password")
            target_key = target_config.get("ssh_key")
            
            # Execute command based on technique type
            if step.technique_id in ["T1087.002", "T1069.002"]:  # AD Discovery
                return await self._execute_windows_command(step.command, target_config)
            elif step.technique_id in ["T1082", "T1548.001"]:  # Linux Discovery
                return await self._execute_linux_command(step.command, target_config)
            else:
                return await self._execute_generic_command(step.command, target_config)
                
        except Exception as e:
            logger.error(f"Real command execution failed: {e}")
            return f"Command execution failed: {str(e)}"
    
    async def _execute_windows_command(self, command: str, target_config: Dict[str, Any]) -> str:
        """Execute Windows command via WinRM or RDP"""
        try:
            # Use WinRM for Windows command execution
            # This would require winrm-py or similar library
            logger.info(f"Executing Windows command: {command}")
            
            # For now, return a warning that this needs WinRM setup
            return f"Windows command execution requires WinRM configuration: {command}"
            
        except Exception as e:
            return f"Windows command failed: {str(e)}"
    
    async def _execute_linux_command(self, command: str, target_config: Dict[str, Any]) -> str:
        """Execute Linux command via SSH"""
        try:
            import asyncio
            import subprocess
            
            host = target_config.get("host", "localhost")
            username = target_config.get("username")
            password = target_config.get("password")
            ssh_key = target_config.get("ssh_key")
            
            if host == "localhost":
                # Local execution
                process = await asyncio.create_subprocess_shell(
                    command,
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )
                stdout, stderr = await process.communicate()
                
                if process.returncode == 0:
                    return stdout.decode('utf-8', errors='ignore')
                else:
                    return f"Command failed: {stderr.decode('utf-8', errors='ignore')}"
            
            else:
                # SSH execution - would require paramiko or similar
                ssh_command = f"ssh {username}@{host} '{command}'"
                logger.info(f"Executing SSH command: {ssh_command}")
                return f"SSH execution requires paramiko setup: {command}"
                
        except Exception as e:
            return f"Linux command failed: {str(e)}"
    
    async def _execute_generic_command(self, command: str, target_config: Dict[str, Any]) -> str:
        """Execute generic command"""
        try:
            import asyncio
            
            # Execute locally with safety checks
            if any(dangerous in command.lower() for dangerous in ['rm -rf', 'del ', 'format', 'mkfs']):
                return "Dangerous command blocked for safety"
            
            process = await asyncio.create_subprocess_shell(
                command,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            stdout, stderr = await process.communicate()
            
            if process.returncode == 0:
                return stdout.decode('utf-8', errors='ignore')
            else:
                return f"Command failed: {stderr.decode('utf-8', errors='ignore')}"
                
        except Exception as e:
            return f"Generic command failed: {str(e)}"
    
    def _evaluate_real_output(self, step: ExploitationStep, output: str) -> str:
        """Evaluate real command output for success/failure"""
        # Check for error indicators first
        error_indicators = ["error", "failed", "denied", "not found", "command not found"]
        if any(indicator in output.lower() for indicator in error_indicators):
            return "failure"
        
        # Check success indicators
        success_count = sum(1 for indicator in step.success_indicators 
                          if indicator.lower() in output.lower())
        
        # Check failure indicators
        failure_count = sum(1 for indicator in step.failure_indicators 
                          if indicator.lower() in output.lower())
        
        if failure_count > 0:
            return "failure"
        elif success_count >= len(step.success_indicators) * 0.5:  # 50% threshold
            return "success"
        elif success_count > 0:
            return "partial"
        elif output.strip():  # Got some output
            return "partial"
        else:
            return "failure"
    
    def _evaluate_simulated_output(self, step: ExploitationStep, output: str) -> str:
        """Evaluate simulated output for success/failure"""
        # Check success indicators
        success_count = sum(1 for indicator in step.success_indicators 
                          if indicator.lower() in output.lower())
        
        # Check failure indicators
        failure_count = sum(1 for indicator in step.failure_indicators 
                          if indicator.lower() in output.lower())
        
        if failure_count > 0:
            return "failure"
        elif success_count >= len(step.success_indicators) * 0.5:  # 50% threshold
            return "success"
        elif success_count > 0:
            return "partial"
        else:
            return "skipped"
    
    def _extract_artifacts(self, step: ExploitationStep, output: str) -> List[str]:
        """Extract security artifacts from step output"""
        artifacts = []
        
        # Extract common artifacts based on technique
        if "T1087" in step.technique_id:  # Account Discovery
            # Extract usernames
            import re
            usernames = re.findall(r'\b[a-zA-Z][a-zA-Z0-9._-]{2,20}\b', output)
            artifacts.extend([f"username:{name}" for name in usernames[:10]])
        
        elif "T1069" in step.technique_id:  # Group Discovery
            # Extract group information
            if "Members" in output:
                artifacts.append("group_membership_discovered")
        
        elif "T1082" in step.technique_id:  # System Information
            # Extract system details
            if "Linux" in output:
                artifacts.append("linux_system_identified")
            if "Ubuntu" in output:
                artifacts.append("ubuntu_distribution_identified")
        
        return artifacts
    
    def _find_success_indicators(self, step: ExploitationStep, output: str) -> List[str]:
        """Find success indicators in step output"""
        found_indicators = []
        
        for indicator in step.success_indicators:
            if indicator.lower() in output.lower():
                found_indicators.append(indicator)
        
        return found_indicators
    
    def _suggest_next_steps(self, step: ExploitationStep, output: str) -> List[str]:
        """Suggest next steps based on step results"""
        suggestions = []
        
        if step.phase == ExploitationPhase.DISCOVERY:
            suggestions.append("Analyze discovered assets for vulnerabilities")
            suggestions.append("Identify privilege escalation opportunities")
        
        elif step.phase == ExploitationPhase.PRIVILEGE_ESCALATION:
            if "success" in output:
                suggestions.append("Establish persistence mechanisms")
                suggestions.append("Begin lateral movement")
            else:
                suggestions.append("Try alternative privilege escalation methods")
        
        return suggestions
    
    async def _get_ai_simulation_guidance(self, scenario: SimulationScenario, 
                                        target_config: Dict[str, Any]) -> str:
        """Get AI guidance for simulation execution"""
        try:
            prompt = f"""
Analyze the following post-exploitation simulation scenario and provide strategic guidance:

Scenario: {scenario.name}
Description: {scenario.description}
Target Type: {scenario.target_type}
Difficulty: {scenario.difficulty_level}
Steps: {len(scenario.steps)}

Target Configuration:
{json.dumps(target_config, indent=2)}

Provide guidance on:
1. Potential challenges and risks
2. Alternative approaches if steps fail
3. Key indicators to watch for
4. Real-world applicability
5. Defensive countermeasures to consider

Keep response concise and actionable.
"""
            
            response = await self.ai_service.analyze(
                prompt,
                context="post_exploitation_simulation",
                provider_preference=["openai", "deepseek"]
            )
            
            return response.get("analysis", "No AI guidance available")
            
        except Exception as e:
            logger.error(f"Failed to get AI simulation guidance: {e}")
            return "AI guidance unavailable"
    
    async def _get_ai_step_analysis(self, step: ExploitationStep, 
                                  result: SimulationResult) -> str:
        """Get AI analysis of step execution result"""
        try:
            prompt = f"""
Analyze this post-exploitation simulation step result:

Step: {step.technique_name} ({step.technique_id})
Description: {step.description}
Command: {step.command}
Expected: {step.expected_output}

Result:
Status: {result.status}
Output: {result.output}
Duration: {result.duration:.2f}s
Artifacts: {result.artifacts_collected}
Indicators: {result.indicators_found}

Provide brief analysis covering:
1. Success/failure assessment
2. Output interpretation
3. Security implications
4. Next recommended actions

Keep response concise and technical.
"""
            
            response = await self.ai_service.analyze(
                prompt,
                context="step_analysis",
                provider_preference=["openai"]
            )
            
            return response.get("analysis", "No AI analysis available")
            
        except Exception as e:
            logger.error(f"Failed to get AI step analysis: {e}")
            return "AI analysis unavailable"
    
    async def _get_ai_final_analysis(self, simulation: Dict[str, Any]) -> str:
        """Get comprehensive AI analysis of completed simulation"""
        try:
            scenario = simulation["scenario"]
            results = simulation["results"]
            
            success_rate = self._calculate_success_rate(results)
            total_artifacts = sum(len(r.artifacts_collected) for r in results)
            
            prompt = f"""
Analyze this completed post-exploitation simulation:

Scenario: {scenario.name}
Target: {simulation["target_config"].get("target_name", "unknown")}
Duration: {simulation.get("duration", 0):.1f} seconds
Steps Executed: {len(results)}
Success Rate: {success_rate:.1f}%
Artifacts Collected: {total_artifacts}

Step Results Summary:
{self._format_results_summary(results)}

Provide comprehensive analysis covering:
1. Overall simulation effectiveness
2. Key findings and insights
3. Security posture assessment
4. Recommended defensive improvements
5. Learning outcomes achieved

Provide actionable insights for both red and blue team perspectives.
"""
            
            response = await self.ai_service.analyze(
                prompt,
                context="simulation_final_analysis",
                provider_preference=["openai", "claude"]
            )
            
            return response.get("analysis", "No final analysis available")
            
        except Exception as e:
            logger.error(f"Failed to get AI final analysis: {e}")
            return "AI final analysis unavailable"
    
    def _calculate_success_rate(self, results: List[SimulationResult]) -> float:
        """Calculate simulation success rate"""
        if not results:
            return 0.0
        
        successful = sum(1 for r in results if r.status == "success")
        return (successful / len(results)) * 100.0
    
    def _format_results_summary(self, results: List[SimulationResult]) -> str:
        """Format results summary for AI analysis"""
        summary_lines = []
        
        for i, result in enumerate(results, 1):
            summary_lines.append(
                f"{i}. {result.step_id}: {result.status} "
                f"({result.duration:.1f}s, {len(result.artifacts_collected)} artifacts)"
            )
        
        return "\n".join(summary_lines)
    
    async def _generate_simulation_report(self, simulation: Dict[str, Any]) -> Dict[str, Any]:
        """Generate comprehensive simulation report"""
        scenario = simulation["scenario"]
        results = simulation["results"]
        
        report = {
            "simulation_id": simulation["simulation_id"],
            "scenario_name": scenario.name,
            "execution_time": simulation["start_time"].isoformat(),
            "duration": simulation.get("duration", 0),
            "status": simulation["status"].value,
            "target_config": simulation["target_config"],
            "success_rate": self._calculate_success_rate(results),
            "steps_executed": len(results),
            "artifacts_total": sum(len(r.artifacts_collected) for r in results),
            "mitre_techniques_covered": scenario.mitre_techniques,
            "compliance_frameworks": scenario.compliance_frameworks,
            "step_results": [asdict(result) for result in results],
            "ai_insights": {
                "pre_analysis": simulation.get("ai_pre_analysis", ""),
                "final_analysis": simulation.get("ai_final_analysis", "")
            },
            "recommendations": self._generate_recommendations(simulation),
            "learning_outcomes": scenario.learning_objectives
        }
        
        return report
    
    def _generate_recommendations(self, simulation: Dict[str, Any]) -> List[str]:
        """Generate security recommendations based on simulation results"""
        recommendations = []
        results = simulation["results"]
        scenario = simulation["scenario"]
        
        # Analyze success patterns
        successful_techniques = [r.step_id for r in results if r.status == "success"]
        failed_techniques = [r.step_id for r in results if r.status == "failure"]
        
        if successful_techniques:
            recommendations.append(
                f"Implement monitoring for {len(successful_techniques)} successful attack techniques"
            )
        
        if failed_techniques:
            recommendations.append(
                f"Review defenses that successfully blocked {len(failed_techniques)} attack attempts"
            )
        
        # Add scenario-specific recommendations
        if scenario.target_type == "windows":
            recommendations.extend([
                "Implement advanced Windows logging and monitoring",
                "Review Active Directory security configurations",
                "Enable PowerShell script block logging"
            ])
        elif scenario.target_type == "linux":
            recommendations.extend([
                "Implement comprehensive Linux audit logging",
                "Review sudo and SUID configurations",
                "Deploy endpoint detection and response (EDR)"
            ])
        
        return recommendations[:10]  # Limit to top 10 recommendations
    
    async def get_simulation_status(self, simulation_id: str) -> Dict[str, Any]:
        """Get current simulation status"""
        if simulation_id not in self.active_simulations:
            raise ValueError(f"Unknown simulation: {simulation_id}")
        
        simulation = self.active_simulations[simulation_id]
        
        return {
            "simulation_id": simulation_id,
            "status": simulation["status"].value,
            "current_step": simulation["current_step"],
            "total_steps": len(simulation["scenario"].steps),
            "progress": (simulation["current_step"] / len(simulation["scenario"].steps)) * 100,
            "elapsed_time": (datetime.now() - simulation["start_time"]).total_seconds(),
            "results_count": len(simulation.get("results", [])),
            "artifacts_count": sum(len(r.artifacts_collected) for r in simulation.get("results", []))
        }
    
    async def stop_simulation(self, simulation_id: str) -> bool:
        """Stop running simulation"""
        if simulation_id not in self.active_simulations:
            return False
        
        simulation = self.active_simulations[simulation_id]
        simulation["status"] = SimulationStatus.STOPPED
        simulation["end_time"] = datetime.now()
        
        await self.event_manager.emit(
            EventTypes.SECURITY_SCAN_STOPPED,
            {"simulation_id": simulation_id},
            "post_exploitation"
        )
        
        logger.info(f"Simulation {simulation_id} stopped")
        return True
    
    def list_available_scenarios(self) -> List[Dict[str, Any]]:
        """List all available simulation scenarios"""
        scenarios = []
        
        for scenario_id, scenario in self.scenario_templates.items():
            scenarios.append({
                "scenario_id": scenario_id,
                "name": scenario.name,
                "description": scenario.description,
                "target_type": scenario.target_type,
                "difficulty_level": scenario.difficulty_level,
                "estimated_duration": scenario.estimated_duration,
                "steps_count": len(scenario.steps),
                "mitre_techniques": scenario.mitre_techniques,
                "compliance_frameworks": scenario.compliance_frameworks
            })
        
        return scenarios
    
    def get_scenario_details(self, scenario_id: str) -> Optional[Dict[str, Any]]:
        """Get detailed information about a specific scenario"""
        if scenario_id not in self.scenario_templates:
            return None
        
        scenario = self.scenario_templates[scenario_id]
        return asdict(scenario)
    
    async def create_custom_scenario(self, scenario_data: Dict[str, Any]) -> str:
        """Create custom simulation scenario"""
        try:
            # Validate scenario data
            required_fields = ["name", "description", "target_type", "steps"]
            for field in required_fields:
                if field not in scenario_data:
                    raise ValueError(f"Missing required field: {field}")
            
            # Create scenario object
            scenario = SimulationScenario(**scenario_data)
            scenario_id = f"custom_{int(time.time())}"
            scenario.scenario_id = scenario_id
            
            # Store scenario
            self.scenario_templates[scenario_id] = scenario
            
            logger.info(f"Created custom scenario: {scenario_id}")
            return scenario_id
            
        except Exception as e:
            logger.error(f"Failed to create custom scenario: {e}")
            raise
    
    def get_simulation_history(self, limit: int = 50) -> List[Dict[str, Any]]:
        """Get simulation execution history"""
        history = []
        
        for sim_id, simulation in list(self.active_simulations.items())[-limit:]:
            if simulation["status"] in [SimulationStatus.COMPLETED, SimulationStatus.FAILED]:
                history.append({
                    "simulation_id": sim_id,
                    "scenario_name": simulation["scenario"].name,
                    "status": simulation["status"].value,
                    "start_time": simulation["start_time"].isoformat(),
                    "duration": simulation.get("duration", 0),
                    "success_rate": self._calculate_success_rate(simulation.get("results", [])),
                    "target": simulation["target_config"].get("target_name", "unknown")
                })
        
        return sorted(history, key=lambda x: x["start_time"], reverse=True)

class PostExploitationFramework:
    """Post-exploitation framework for advanced testing"""
    def __init__(self, config=None):
        self.config = config
        self.techniques = []
    
    async def execute_technique(self, technique_name, target, options=None):
        """Execute a post-exploitation technique"""
        return {"technique": technique_name, "target": target, "success": True}
    
    def list_techniques(self):
        """List available post-exploitation techniques"""
        return ["privilege_escalation", "lateral_movement", "data_exfiltration", "persistence"]
