#!/usr/bin/env python3
"""
Environment Detection and Adaptive Execution Strategy
Intelligent detection of execution environment for optimal tool execution
"""

import os
import sys
import platform
import subprocess
import logging
from enum import Enum
from typing import Dict, Optional, List, Tuple
from pathlib import Path

logger = logging.getLogger(__name__)

class ExecutionEnvironment(Enum):
    """Supported execution environments"""
    WINDOWS = "windows"
    WSL_GUEST = "wsl_guest"  # Inside WSL
    WSL_HOST = "wsl_host"    # Windows with WSL available
    LINUX_NATIVE = "linux_native"
    CONTAINER = "container"
    DOCKER_HOST = "docker_host"
    UNKNOWN = "unknown"

class ExecutionStrategy(Enum):
    """Tool execution strategies"""
    DOCKER_FIRST = "docker_first"        # Prioritize containerized tools
    NATIVE_FIRST = "native_first"        # Direct OS execution
    WSL_BRIDGE = "wsl_bridge"           # Windows→WSL bridge
    HYBRID_CASCADE = "hybrid_cascade"    # Multi-strategy fallback
    SIMULATION_ONLY = "simulation_only"  # Last resort simulation

class EnvironmentDetector:
    """Intelligent environment detection and strategy selection"""
    
    def __init__(self):
        self.environment = self._detect_environment()
        self.capabilities = self._detect_capabilities()
        self.strategy = self._select_optimal_strategy()
        
        logger.info(f"Environment detected: {self.environment.value}")
        logger.info(f"Selected strategy: {self.strategy.value}")
        logger.info(f"Available capabilities: {list(self.capabilities.keys())}")
    
    def _detect_environment(self) -> ExecutionEnvironment:
        """Comprehensive environment detection"""
        
        # Check for WSL Guest (running inside WSL)
        if self._is_wsl_guest():
            return ExecutionEnvironment.WSL_GUEST
        
        # Check for Container environment
        if self._is_container():
            return ExecutionEnvironment.CONTAINER
        
        # Check for Windows
        if self._is_windows():
            # Check if WSL is available on Windows
            if self._is_wsl_available():
                return ExecutionEnvironment.WSL_HOST
            else:
                return ExecutionEnvironment.WINDOWS
        
        # Check for Linux with Docker
        if self._is_linux():
            if self._is_docker_available():
                return ExecutionEnvironment.DOCKER_HOST
            else:
                return ExecutionEnvironment.LINUX_NATIVE
        
        return ExecutionEnvironment.UNKNOWN
    
    def _is_wsl_guest(self) -> bool:
        """Detect if running inside WSL"""
        # Check WSL environment variables
        wsl_indicators = [
            os.getenv('WSL_DISTRO_NAME'),
            os.getenv('WSLENV'),
            os.getenv('WSL_INTEROP')
        ]
        
        if any(wsl_indicators):
            return True
        
        # Check for WSL-specific file system markers
        wsl_markers = [
            '/proc/sys/fs/binfmt_misc/WSLInterop',
            '/run/WSL'
        ]
        
        return any(Path(marker).exists() for marker in wsl_markers)
    
    def _is_container(self) -> bool:
        """Detect if running in a container"""
        container_indicators = [
            os.path.exists('/.dockerenv'),
            os.getenv('container') is not None,
            os.getenv('DOCKER_CONTAINER') is not None,
            Path('/proc/1/cgroup').exists() and 'docker' in Path('/proc/1/cgroup').read_text()
        ]
        
        return any(container_indicators)
    
    def _is_windows(self) -> bool:
        """Detect Windows operating system"""
        return os.name == 'nt' or sys.platform.startswith('win') or platform.system() == 'Windows'
    
    def _is_linux(self) -> bool:
        """Detect Linux operating system"""
        return platform.system() == 'Linux'
    
    def _is_wsl_available(self) -> bool:
        """Check if WSL is available on Windows"""
        try:
            result = subprocess.run(
                ["wsl", "--status"],
                capture_output=True,
                text=True,
                timeout=10
            )
            return result.returncode == 0
        except Exception:
            return False
    
    def _is_docker_available(self) -> bool:
        """Check if Docker is available"""
        try:
            result = subprocess.run(
                ["docker", "--version"],
                capture_output=True,
                text=True,
                timeout=10
            )
            return result.returncode == 0
        except Exception:
            return False
    
    def _detect_capabilities(self) -> Dict[str, bool]:
        """Detect available execution capabilities"""
        capabilities = {}
        
        # Docker availability
        capabilities['docker'] = self._is_docker_available()
        
        # WSL availability
        if self.environment == ExecutionEnvironment.WSL_HOST:
            capabilities['wsl'] = True
        elif self.environment == ExecutionEnvironment.WINDOWS:
            capabilities['wsl'] = self._is_wsl_available()
        else:
            capabilities['wsl'] = False
        
        # Native execution
        capabilities['native_linux'] = self.environment in [
            ExecutionEnvironment.LINUX_NATIVE,
            ExecutionEnvironment.WSL_GUEST,
            ExecutionEnvironment.CONTAINER,
            ExecutionEnvironment.DOCKER_HOST
        ]
        
        capabilities['native_windows'] = self.environment in [
            ExecutionEnvironment.WINDOWS,
            ExecutionEnvironment.WSL_HOST
        ]
        
        # Package managers
        capabilities['apt'] = self._command_available('apt-get')
        capabilities['yum'] = self._command_available('yum')
        capabilities['pacman'] = self._command_available('pacman')
        capabilities['brew'] = self._command_available('brew')
        capabilities['chocolatey'] = self._command_available('choco')
        capabilities['pip'] = self._command_available('pip') or self._command_available('pip3')
        capabilities['npm'] = self._command_available('npm')
        capabilities['gem'] = self._command_available('gem')
        capabilities['cargo'] = self._command_available('cargo')
        capabilities['go'] = self._command_available('go')
        
        return capabilities
    
    def _command_available(self, command: str) -> bool:
        """Check if a command is available in PATH"""
        try:
            subprocess.run(
                [command, "--version"],
                capture_output=True,
                text=True,
                timeout=5
            )
            return True
        except Exception:
            return False
    
    def _select_optimal_strategy(self) -> ExecutionStrategy:
        """Select optimal execution strategy based on environment"""
        
        strategy_map = {
            ExecutionEnvironment.CONTAINER: ExecutionStrategy.DOCKER_FIRST,
            ExecutionEnvironment.DOCKER_HOST: ExecutionStrategy.DOCKER_FIRST,
            ExecutionEnvironment.LINUX_NATIVE: ExecutionStrategy.NATIVE_FIRST,
            ExecutionEnvironment.WSL_GUEST: ExecutionStrategy.NATIVE_FIRST,
            ExecutionEnvironment.WSL_HOST: ExecutionStrategy.HYBRID_CASCADE,
            ExecutionEnvironment.WINDOWS: ExecutionStrategy.NATIVE_FIRST,
            ExecutionEnvironment.UNKNOWN: ExecutionStrategy.SIMULATION_ONLY
        }
        
        return strategy_map.get(self.environment, ExecutionStrategy.HYBRID_CASCADE)
    
    def get_execution_priority(self) -> List[str]:
        """Get execution method priority order"""
        
        priority_map = {
            ExecutionStrategy.DOCKER_FIRST: ["docker", "native", "wsl", "simulation"],
            ExecutionStrategy.NATIVE_FIRST: ["native", "docker", "wsl", "simulation"],
            ExecutionStrategy.WSL_BRIDGE: ["wsl", "native", "docker", "simulation"],
            ExecutionStrategy.HYBRID_CASCADE: ["docker", "native", "wsl", "simulation"],
            ExecutionStrategy.SIMULATION_ONLY: ["simulation"]
        }
        
        return priority_map.get(self.strategy, ["simulation"])
    
    def get_recommended_package_manager(self) -> Optional[str]:
        """Get recommended package manager for the environment"""
        
        if self.capabilities.get('apt'):
            return 'apt'
        elif self.capabilities.get('yum'):
            return 'yum'
        elif self.capabilities.get('pacman'):
            return 'pacman'
        elif self.capabilities.get('brew'):
            return 'brew'
        elif self.capabilities.get('chocolatey'):
            return 'chocolatey'
        else:
            return None
    
    def get_environment_info(self) -> Dict:
        """Get comprehensive environment information"""
        return {
            'environment': self.environment.value,
            'strategy': self.strategy.value,
            'capabilities': self.capabilities,
            'execution_priority': self.get_execution_priority(),
            'package_manager': self.get_recommended_package_manager(),
            'platform': {
                'system': platform.system(),
                'release': platform.release(),
                'version': platform.version(),
                'machine': platform.machine(),
                'processor': platform.processor()
            }
        }

# Global environment detector instance
environment_detector = EnvironmentDetector()

def get_environment() -> ExecutionEnvironment:
    """Get current execution environment"""
    return environment_detector.environment

def get_strategy() -> ExecutionStrategy:
    """Get current execution strategy"""
    return environment_detector.strategy

def get_capabilities() -> Dict[str, bool]:
    """Get available capabilities"""
    return environment_detector.capabilities

def get_execution_priority() -> List[str]:
    """Get execution method priority"""
    return environment_detector.get_execution_priority()

def get_environment_info() -> Dict:
    """Get comprehensive environment information"""
    return environment_detector.get_environment_info()