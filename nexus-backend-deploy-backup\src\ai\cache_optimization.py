#!/usr/bin/env python3
"""
AI Service Cache Optimization
Advanced caching strategies for AI service calls with intelligent cache warming,
result similarity detection, and cost optimization
"""

import asyncio
import hashlib
import json
import time
import logging
import re
from typing import Any, Dict, List, Optional, Tuple, Union, Callable
from dataclasses import dataclass, asdict
from datetime import datetime, timed<PERSON><PERSON>
from enum import Enum
from difflib import SequenceMatcher

from core.cache import CacheManager, get_cache_manager
from security.cache_strategies import SecurityCacheManager, get_security_cache_manager

logger = logging.getLogger(__name__)

class AICacheStrategy(Enum):
    """AI-specific cache strategies"""
    EXACT_MATCH = "exact_match"
    SIMILARITY_MATCH = "similarity_match"
    TEMPLATE_BASED = "template_based"
    COST_OPTIMIZED = "cost_optimized"
    ADAPTIVE = "adaptive"

@dataclass
class AIRequestSignature:
    """Signature for AI requests to enable intelligent caching"""
    function_name: str
    prompt_template: str
    prompt_variables: Dict[str, Any]
    model: str
    temperature: float
    max_tokens: int
    system_prompt: Optional[str] = None
    
    def create_cache_key(self, strategy: AICacheStrategy = AICacheStrategy.EXACT_MATCH) -> str:
        """Create cache key based on strategy"""
        if strategy == AICacheStrategy.EXACT_MATCH:
            return self._exact_match_key()
        elif strategy == AICacheStrategy.SIMILARITY_MATCH:
            return self._similarity_key()
        elif strategy == AICacheStrategy.TEMPLATE_BASED:
            return self._template_key()
        else:
            return self._exact_match_key()
    
    def _exact_match_key(self) -> str:
        """Create exact match cache key"""
        key_data = {
            "function": self.function_name,
            "template": self.prompt_template,
            "variables": sorted(self.prompt_variables.items()),
            "model": self.model,
            "temperature": self.temperature,
            "max_tokens": self.max_tokens,
            "system": self.system_prompt
        }
        key_str = json.dumps(key_data, sort_keys=True)
        return f"ai_exact:{hashlib.md5(key_str.encode()).hexdigest()}"
    
    def _similarity_key(self) -> str:
        """Create similarity-based cache key (normalized)"""
        # Normalize variables for similarity matching
        normalized_vars = self._normalize_variables(self.prompt_variables)
        
        key_data = {
            "function": self.function_name,
            "template": self.prompt_template,
            "normalized_vars": sorted(normalized_vars.items()),
            "model": self.model,
            "temp_range": round(self.temperature, 1),  # Round temperature for fuzzy matching
            "token_range": (self.max_tokens // 100) * 100,  # Round to nearest 100
            "system": self.system_prompt
        }
        key_str = json.dumps(key_data, sort_keys=True)
        return f"ai_sim:{hashlib.md5(key_str.encode()).hexdigest()}"
    
    def _template_key(self) -> str:
        """Create template-based cache key"""
        # Extract template structure without specific values
        template_structure = self._extract_template_structure(self.prompt_template)
        
        key_data = {
            "function": self.function_name,
            "template_structure": template_structure,
            "model": self.model,
            "system": self.system_prompt
        }
        key_str = json.dumps(key_data, sort_keys=True)
        return f"ai_template:{hashlib.md5(key_str.encode()).hexdigest()}"
    
    def _normalize_variables(self, variables: Dict[str, Any]) -> Dict[str, Any]:
        """Normalize variables for similarity matching"""
        normalized = {}
        for key, value in variables.items():
            if isinstance(value, str):
                # Normalize common patterns
                normalized_value = value.lower().strip()
                # Remove specific IPs, domains, etc. for broader matching
                normalized_value = re.sub(r'\d+\.\d+\.\d+\.\d+', '[IP]', normalized_value)
                normalized_value = re.sub(r'[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}', '[DOMAIN]', normalized_value)
                normalized_value = re.sub(r'\d+', '[NUMBER]', normalized_value)
                normalized[key] = normalized_value
            else:
                normalized[key] = value
        return normalized
    
    def _extract_template_structure(self, template: str) -> str:
        """Extract template structure for template-based caching"""
        # Replace variable placeholders with generic markers
        structure = re.sub(r'\{[^}]+\}', '{VAR}', template)
        structure = re.sub(r'\s+', ' ', structure).strip()
        return structure

@dataclass
class AICacheEntry:
    """AI cache entry with metadata"""
    request_signature: AIRequestSignature
    response: Any
    tokens_used: int
    cost: float
    model_version: str
    cached_at: float
    access_count: int = 0
    last_accessed: float = 0
    similarity_score: float = 1.0
    
    def __post_init__(self):
        if self.last_accessed == 0:
            self.last_accessed = self.cached_at

class AICacheOptimizer:
    """Advanced AI cache optimizer with intelligent strategies"""
    
    def __init__(self, cache_manager: Optional[CacheManager] = None):
        self.cache = cache_manager or get_cache_manager()
        self.security_cache = get_security_cache_manager()
        
        # Cache configuration
        self.default_ttl = 7200  # 2 hours
        self.similarity_threshold = 0.8
        self.cost_savings_target = 0.3  # 30% cost reduction target
        
        # Performance tracking
        self.stats = {
            "cache_hits": 0,
            "cache_misses": 0,
            "similarity_matches": 0,
            "cost_saved": 0.0,
            "tokens_saved": 0
        }
        
        # Cache warming configuration
        self.warm_cache_enabled = True
        self.popular_requests = {}  # Track popular request patterns
        
    async def get_cached_response(self, 
                                request_sig: AIRequestSignature,
                                strategy: AICacheStrategy = AICacheStrategy.ADAPTIVE) -> Optional[Tuple[Any, AICacheEntry]]:
        """Get cached AI response using specified strategy"""
        
        # Try exact match first
        exact_key = request_sig.create_cache_key(AICacheStrategy.EXACT_MATCH)
        cached_entry = await self._get_cache_entry(exact_key)
        
        if cached_entry:
            self.stats["cache_hits"] += 1
            cached_entry.access_count += 1
            cached_entry.last_accessed = time.time()
            await self._update_cache_entry(exact_key, cached_entry)
            logger.debug(f"Exact cache hit for AI request: {request_sig.function_name}")
            return cached_entry.response, cached_entry
        
        # Try similarity matching if enabled
        if strategy in [AICacheStrategy.SIMILARITY_MATCH, AICacheStrategy.ADAPTIVE]:
            similar_response = await self._find_similar_response(request_sig)
            if similar_response:
                self.stats["similarity_matches"] += 1
                logger.debug(f"Similarity cache hit for AI request: {request_sig.function_name}")
                return similar_response
        
        # Try template-based matching
        if strategy in [AICacheStrategy.TEMPLATE_BASED, AICacheStrategy.ADAPTIVE]:
            template_response = await self._find_template_response(request_sig)
            if template_response:
                logger.debug(f"Template cache hit for AI request: {request_sig.function_name}")
                return template_response
        
        self.stats["cache_misses"] += 1
        return None
    
    async def cache_response(self,
                           request_sig: AIRequestSignature,
                           response: Any,
                           tokens_used: int,
                           cost: float,
                           model_version: str,
                           custom_ttl: Optional[int] = None) -> bool:
        """Cache AI response with metadata"""
        
        cache_entry = AICacheEntry(
            request_signature=request_sig,
            response=response,
            tokens_used=tokens_used,
            cost=cost,
            model_version=model_version,
            cached_at=time.time()
        )
        
        # Cache with exact match key
        exact_key = request_sig.create_cache_key(AICacheStrategy.EXACT_MATCH)
        ttl = custom_ttl or self.default_ttl
        
        success = await self._store_cache_entry(exact_key, cache_entry, ttl)
        
        if success:
            # Track popular requests for cache warming
            await self._track_request_popularity(request_sig)
            
            # Store additional similarity and template keys for faster lookup
            await self._store_auxiliary_keys(request_sig, exact_key)
        
        return success
    
    async def _find_similar_response(self, request_sig: AIRequestSignature) -> Optional[Tuple[Any, AICacheEntry]]:
        """Find similar cached response using semantic similarity"""
        
        # Get similarity key for faster lookup
        sim_key = request_sig.create_cache_key(AICacheStrategy.SIMILARITY_MATCH)
        cached_entry = await self._get_cache_entry(sim_key)
        
        if cached_entry and cached_entry.similarity_score >= self.similarity_threshold:
            return cached_entry.response, cached_entry
        
        # If no direct similarity match, search through related entries
        # This is a simplified version - in production, you might use vector similarity
        return None
    
    async def _find_template_response(self, request_sig: AIRequestSignature) -> Optional[Tuple[Any, AICacheEntry]]:
        """Find cached response for similar template structure"""
        
        template_key = request_sig.create_cache_key(AICacheStrategy.TEMPLATE_BASED)
        cached_entry = await self._get_cache_entry(template_key)
        
        if cached_entry:
            # Adapt the cached response to current variables
            adapted_response = await self._adapt_template_response(
                cached_entry.response,
                cached_entry.request_signature.prompt_variables,
                request_sig.prompt_variables
            )
            
            if adapted_response:
                return adapted_response, cached_entry
        
        return None
    
    async def _adapt_template_response(self,
                                     cached_response: Any,
                                     cached_variables: Dict[str, Any],
                                     current_variables: Dict[str, Any]) -> Optional[Any]:
        """Adapt cached template response to current variables"""
        
        # This is a simplified adaptation - in production, you might use
        # more sophisticated template variable replacement
        if isinstance(cached_response, str):
            adapted = cached_response
            for key, value in current_variables.items():
                if key in cached_variables:
                    old_value = str(cached_variables[key])
                    new_value = str(value)
                    adapted = adapted.replace(old_value, new_value)
            return adapted
        
        return None
    
    async def _store_cache_entry(self, key: str, entry: AICacheEntry, ttl: int) -> bool:
        """Store cache entry with metadata"""
        return await self.cache.set(key, entry, ttl, ["ai", "responses", entry.request_signature.function_name])
    
    async def _get_cache_entry(self, key: str) -> Optional[AICacheEntry]:
        """Get cache entry with metadata"""
        return await self.cache.get(key)
    
    async def _update_cache_entry(self, key: str, entry: AICacheEntry) -> bool:
        """Update existing cache entry"""
        return await self.cache.set(key, entry, None)  # Keep existing TTL
    
    async def _store_auxiliary_keys(self, request_sig: AIRequestSignature, primary_key: str):
        """Store auxiliary keys for faster similarity and template lookup"""
        
        # Store similarity key mapping
        sim_key = request_sig.create_cache_key(AICacheStrategy.SIMILARITY_MATCH)
        await self.cache.set(f"aux_sim:{sim_key}", primary_key, self.default_ttl)
        
        # Store template key mapping
        template_key = request_sig.create_cache_key(AICacheStrategy.TEMPLATE_BASED)
        await self.cache.set(f"aux_template:{template_key}", primary_key, self.default_ttl)
    
    async def _track_request_popularity(self, request_sig: AIRequestSignature):
        """Track request popularity for cache warming"""
        
        pattern_key = f"{request_sig.function_name}:{request_sig.prompt_template[:50]}"
        self.popular_requests[pattern_key] = self.popular_requests.get(pattern_key, 0) + 1
        
        # If this pattern becomes popular, consider pre-warming similar requests
        if self.popular_requests[pattern_key] > 5 and self.warm_cache_enabled:
            asyncio.create_task(self._warm_cache_for_pattern(request_sig))
    
    async def _warm_cache_for_pattern(self, request_sig: AIRequestSignature):
        """Pre-warm cache for popular request patterns"""
        
        # This is a placeholder for cache warming logic
        # In production, you might pre-generate responses for common variations
        logger.debug(f"Cache warming triggered for pattern: {request_sig.function_name}")
    
    async def calculate_cost_savings(self) -> Dict[str, float]:
        """Calculate cost savings from caching"""
        
        # Get all cached AI entries and calculate potential cost savings
        total_saved_cost = 0.0
        total_saved_tokens = 0
        
        # This would iterate through cached entries to calculate savings
        # Simplified for this implementation
        
        return {
            "total_cost_saved": self.stats["cost_saved"],
            "total_tokens_saved": self.stats["tokens_saved"],
            "cache_hit_rate": self._calculate_hit_rate(),
            "similarity_match_rate": self._calculate_similarity_rate()
        }
    
    def _calculate_hit_rate(self) -> float:
        """Calculate cache hit rate"""
        total_requests = self.stats["cache_hits"] + self.stats["cache_misses"]
        return (self.stats["cache_hits"] / total_requests * 100) if total_requests > 0 else 0.0
    
    def _calculate_similarity_rate(self) -> float:
        """Calculate similarity match rate"""
        total_hits = self.stats["cache_hits"] + self.stats["similarity_matches"]
        return (self.stats["similarity_matches"] / total_hits * 100) if total_hits > 0 else 0.0
    
    async def optimize_cache_strategy(self) -> AICacheStrategy:
        """Automatically optimize cache strategy based on usage patterns"""
        
        hit_rate = self._calculate_hit_rate()
        similarity_rate = self._calculate_similarity_rate()
        
        # Simple heuristic for strategy optimization
        if hit_rate > 80:
            return AICacheStrategy.EXACT_MATCH
        elif similarity_rate > 20:
            return AICacheStrategy.SIMILARITY_MATCH
        elif len(self.popular_requests) > 10:
            return AICacheStrategy.TEMPLATE_BASED
        else:
            return AICacheStrategy.ADAPTIVE
    
    async def cleanup_expired_entries(self):
        """Clean up expired cache entries and optimize storage"""
        
        # This would implement intelligent cache cleanup
        # For now, we rely on the underlying cache TTL mechanism
        logger.info("AI cache cleanup completed")
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """Get comprehensive AI cache statistics"""
        
        return {
            "performance": self.stats,
            "configuration": {
                "default_ttl": self.default_ttl,
                "similarity_threshold": self.similarity_threshold,
                "cost_savings_target": self.cost_savings_target
            },
            "popular_patterns": dict(list(self.popular_requests.items())[:10]),  # Top 10
            "recommendations": self._get_optimization_recommendations()
        }
    
    def _get_optimization_recommendations(self) -> List[str]:
        """Get cache optimization recommendations"""
        
        recommendations = []
        hit_rate = self._calculate_hit_rate()
        
        if hit_rate < 50:
            recommendations.append("Consider increasing cache TTL or improving similarity matching")
        
        if self.stats["similarity_matches"] > self.stats["cache_hits"] * 0.3:
            recommendations.append("Similarity matching is effective - consider using SIMILARITY_MATCH strategy")
        
        if len(self.popular_requests) > 20:
            recommendations.append("Enable cache warming for popular request patterns")
        
        return recommendations

# Decorator for AI functions with intelligent caching
def cached_ai_call(function_name: str, 
                  model: str,
                  strategy: AICacheStrategy = AICacheStrategy.ADAPTIVE,
                  ttl: Optional[int] = None):
    """Decorator for caching AI function calls with optimization"""
    
    def decorator(func: Callable):
        async def wrapper(*args, **kwargs):
            optimizer = AICacheOptimizer()
            
            # Extract AI call parameters from function arguments
            # This is simplified - in practice, you'd extract prompt, temperature, etc.
            prompt_template = kwargs.get('prompt', str(args[0]) if args else '')
            temperature = kwargs.get('temperature', 0.7)
            max_tokens = kwargs.get('max_tokens', 1000)
            system_prompt = kwargs.get('system_prompt')
            
            # Create request signature
            request_sig = AIRequestSignature(
                function_name=function_name,
                prompt_template=prompt_template,
                prompt_variables=kwargs,
                model=model,
                temperature=temperature,
                max_tokens=max_tokens,
                system_prompt=system_prompt
            )
            
            # Try to get cached response
            cached_result = await optimizer.get_cached_response(request_sig, strategy)
            if cached_result:
                response, cache_entry = cached_result
                logger.debug(f"Using cached AI response for {function_name}")
                return response
            
            # Execute function and cache result
            logger.debug(f"Executing AI function {function_name}")
            if asyncio.iscoroutinefunction(func):
                result = await func(*args, **kwargs)
            else:
                result = func(*args, **kwargs)
            
            # Extract metadata from result (simplified)
            if isinstance(result, dict):
                response = result.get('response', result)
                tokens_used = result.get('tokens_used', 0)
                cost = result.get('cost', 0.0)
                model_version = result.get('model_version', model)
            else:
                response = result
                tokens_used = 0
                cost = 0.0
                model_version = model
            
            # Cache the result
            await optimizer.cache_response(
                request_sig, response, tokens_used, cost, model_version, ttl
            )
            
            return response
        
        return wrapper
    return decorator

# Global AI cache optimizer instance
ai_cache_optimizer: Optional[AICacheOptimizer] = None

def get_ai_cache_optimizer() -> AICacheOptimizer:
    """Get global AI cache optimizer instance"""
    global ai_cache_optimizer
    if ai_cache_optimizer is None:
        ai_cache_optimizer = AICacheOptimizer()
    return ai_cache_optimizer