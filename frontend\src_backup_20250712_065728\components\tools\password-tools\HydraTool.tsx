/**
 * Hydra Tool Component
 * Fast network login cracker which supports many different services
 */
import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { 
  Key,
  Play,
  Square,
  Download,
  Settings,
  Target,
  Globe,
  FileText,
  Terminal,
  Copy,
  RotateCcw,
  Clock,
  Zap,
  Filter,
  Eye,
  AlertTriangle,
  CheckCircle,
  TrendingUp,
  Activity,
  Lock,
  Search,
  List,
  BarChart3,
  Database,
  Server,
  Bug,
  Info,
  Code,
  Package,
  Layers,
  Shield,
  Crosshair,
  Cpu,
  Network,
  Gauge,
  Tag,
  Hash,
  Workflow,
  GitBranch,
  Book,
  Users,
  Table,
  FileX,
  Skull,
  Unlock,
  HardDrive,
  Flame,
  Binary,
  Braces,
  FileKey,
  History,
  Folder,
  Star,
  Crown,
  Award,
  Sparkles,
  Wifi,
  Router,
  Mail,
  CloudRain,
  Rss,
  UserCheck,
  UserX,
  Timer,
  Pause,
  FastForward,
  Rewind,
  SkipForward
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useBackendStore } from '@/stores/backend-store';
import { apiClient, ToolExecution } from '@/services/api-client';
import { toast } from 'sonner';

/**
 * Hydra configuration interface
 */
interface HydraConfig {
  target: {
    host: string;
    port: number;
    service: string;
    targetList: string;
    hostFile: string;
  };
  credentials: {
    username: string;
    userList: string;
    userFile: string;
    password: string;
    passList: string;
    passFile: string;
    comboFile: string;
    nullPassword: boolean;
    loginAsPassword: boolean;
    reverseLogin: boolean;
  };
  attack: {
    parallel: number;
    waitTime: number;
    timeout: number;
    retries: number;
    exitAfterFirst: boolean;
    continueOnSuccess: boolean;
    showAttempts: boolean;
    colon: boolean;
  };
  output: {
    verbose: boolean;
    debug: boolean;
    outputFile: string;
    format: 'default' | 'json' | 'jsonv1';
    resume: string;
  };
  advanced: {
    useSSL: boolean;
    ignoreSslErrors: boolean;
    customOptions: string;
    moduleOptions: string;
    proxyUrl: string;
    userAgent: string;
  };
}

/**
 * Supported services in Hydra
 */
const HYDRA_SERVICES = {
  // Network protocols
  'ftp': { name: 'FTP', category: 'Network', port: 21, description: 'File Transfer Protocol' },
  'ssh': { name: 'SSH', category: 'Network', port: 22, description: 'Secure Shell' },
  'telnet': { name: 'Telnet', category: 'Network', port: 23, description: 'Telnet remote access' },
  'smtp': { name: 'SMTP', category: 'Network', port: 25, description: 'Simple Mail Transfer Protocol' },
  'pop3': { name: 'POP3', category: 'Network', port: 110, description: 'Post Office Protocol v3' },
  'imap': { name: 'IMAP', category: 'Network', port: 143, description: 'Internet Message Access Protocol' },
  'snmp': { name: 'SNMP', category: 'Network', port: 161, description: 'Simple Network Management Protocol' },
  'ldap': { name: 'LDAP', category: 'Network', port: 389, description: 'Lightweight Directory Access Protocol' },
  'smb': { name: 'SMB', category: 'Network', port: 445, description: 'Server Message Block' },
  'rdp': { name: 'RDP', category: 'Network', port: 3389, description: 'Remote Desktop Protocol' },
  
  // Web protocols
  'http-get': { name: 'HTTP GET', category: 'Web', port: 80, description: 'HTTP GET form login' },
  'http-post': { name: 'HTTP POST', category: 'Web', port: 80, description: 'HTTP POST form login' },
  'https-get': { name: 'HTTPS GET', category: 'Web', port: 443, description: 'HTTPS GET form login' },
  'https-post': { name: 'HTTPS POST', category: 'Web', port: 443, description: 'HTTPS POST form login' },
  'http-proxy': { name: 'HTTP Proxy', category: 'Web', port: 8080, description: 'HTTP proxy authentication' },
  
  // Database protocols
  'mysql': { name: 'MySQL', category: 'Database', port: 3306, description: 'MySQL database' },
  'mssql': { name: 'MS SQL', category: 'Database', port: 1433, description: 'Microsoft SQL Server' },
  'postgres': { name: 'PostgreSQL', category: 'Database', port: 5432, description: 'PostgreSQL database' },
  'oracle-listener': { name: 'Oracle TNS', category: 'Database', port: 1521, description: 'Oracle TNS Listener' },
  'redis': { name: 'Redis', category: 'Database', port: 6379, description: 'Redis key-value store' },
  
  // VPN/Security
  'cisco': { name: 'Cisco', category: 'VPN', port: 23, description: 'Cisco device authentication' },
  'cisco-enable': { name: 'Cisco Enable', category: 'VPN', port: 23, description: 'Cisco enable password' },
  'ipsec-ike': { name: 'IPSec IKE', category: 'VPN', port: 500, description: 'IPSec IKE pre-shared key' },
  
  // Other protocols
  'vnc': { name: 'VNC', category: 'Remote', port: 5900, description: 'Virtual Network Computing' },
  'rsh': { name: 'RSH', category: 'Remote', port: 514, description: 'Remote Shell' },
  'rlogin': { name: 'RLogin', category: 'Remote', port: 513, description: 'Remote Login' },
  'adam6500': { name: 'Adam6500', category: 'IoT', port: 502, description: 'Adam 6500 series devices' },
  'afp': { name: 'AFP', category: 'Network', port: 548, description: 'Apple Filing Protocol' }
};

/**
 * Performance presets for different attack scenarios
 */
const PERFORMANCE_PRESETS = {
  'stealth': {
    name: 'Stealth Mode',
    description: 'Slow and low-profile attack',
    parallel: 1,
    waitTime: 5,
    timeout: 30,
    retries: 1
  },
  'balanced': {
    name: 'Balanced',
    description: 'Good balance of speed and stealth',
    parallel: 4,
    waitTime: 2,
    timeout: 20,
    retries: 2
  },
  'aggressive': {
    name: 'Aggressive',
    description: 'Fast attack with higher risk of detection',
    parallel: 16,
    waitTime: 0,
    timeout: 10,
    retries: 3
  },
  'maximum': {
    name: 'Maximum Speed',
    description: 'Fastest possible attack',
    parallel: 64,
    waitTime: 0,
    timeout: 5,
    retries: 4
  }
};

/**
 * Common wordlists for Hydra attacks
 */
const COMMON_WORDLISTS = {
  'usernames': [
    { name: 'Common Usernames', path: '/usr/share/hydra/dpl4hydra_full.csv', size: '1000+' },
    { name: 'Top 1000 Usernames', path: '/usr/share/seclists/Usernames/top-usernames-shortlist.txt', size: '25' },
    { name: 'Default Users', path: '/usr/share/seclists/Usernames/default-usernames.txt', size: '900+' },
    { name: 'Admin Users', path: '/usr/share/seclists/Usernames/admin-usernames.txt', size: '50+' }
  ],
  'passwords': [
    { name: 'RockYou Top 1000', path: '/usr/share/wordlists/rockyou.txt', size: '14M' },
    { name: 'Common Passwords', path: '/usr/share/seclists/Passwords/Common-Credentials/10-million-password-list-top-1000.txt', size: '1000' },
    { name: 'Default Passwords', path: '/usr/share/seclists/Passwords/Default-Credentials/default-passwords.csv', size: '3000+' },
    { name: 'Weak Passwords', path: '/usr/share/seclists/Passwords/darkweb2017-top1000.txt', size: '1000' }
  ]
};

/**
 * Attack scenarios for quick setup
 */
const ATTACK_SCENARIOS = {
  'ssh_brute': {
    name: 'SSH Brute Force',
    description: 'Standard SSH login brute force',
    service: 'ssh',
    port: 22,
    parallel: 4,
    exitAfterFirst: true
  },
  'ftp_default': {
    name: 'FTP Default Credentials',
    description: 'Test for default FTP credentials',
    service: 'ftp',
    port: 21,
    nullPassword: true,
    loginAsPassword: true
  },
  'web_form': {
    name: 'Web Form Attack',
    description: 'HTTP POST form brute force',
    service: 'http-post',
    port: 80,
    parallel: 8
  },
  'smb_shares': {
    name: 'SMB Share Access',
    description: 'SMB/CIFS share authentication',
    service: 'smb',
    port: 445,
    nullPassword: true
  },
  'database_mysql': {
    name: 'MySQL Database',
    description: 'MySQL root account brute force',
    service: 'mysql',
    port: 3306,
    username: 'root'
  },
  'rdp_admin': {
    name: 'RDP Administrator',
    description: 'Windows RDP admin account',
    service: 'rdp',
    port: 3389,
    username: 'administrator'
  }
};

export default function HydraTool() {
  const { isConnected } = useBackendStore();
  const [config, setConfig] = React.useState<HydraConfig>({
    target: {
      host: '',
      port: 22,
      service: 'ssh',
      targetList: '',
      hostFile: ''
    },
    credentials: {
      username: '',
      userList: '',
      userFile: '',
      password: '',
      passList: '',
      passFile: '',
      comboFile: '',
      nullPassword: false,
      loginAsPassword: false,
      reverseLogin: false
    },
    attack: {
      parallel: 16,
      waitTime: 1,
      timeout: 30,
      retries: 3,
      exitAfterFirst: true,
      continueOnSuccess: false,
      showAttempts: true,
      colon: false
    },
    output: {
      verbose: true,
      debug: false,
      outputFile: '',
      format: 'default',
      resume: ''
    },
    advanced: {
      useSSL: false,
      ignoreSslErrors: false,
      customOptions: '',
      moduleOptions: '',
      proxyUrl: '',
      userAgent: ''
    }
  });

  const [isRunning, setIsRunning] = React.useState(false);
  const [output, setOutput] = React.useState<string[]>([]);
  const [results, setResults] = React.useState<any[]>([]);
  const [currentExecution, setCurrentExecution] = React.useState<ToolExecution | null>(null);
  const [progress, setProgress] = React.useState(0);
  const [statistics, setStatistics] = React.useState({
    totalAttempts: 0,
    successfulLogins: 0,
    failedAttempts: 0,
    averageTime: 0
  });

  /**
   * Apply attack scenario preset
   */
  const applyScenario = (scenario: keyof typeof ATTACK_SCENARIOS) => {
    const scenarioConfig = ATTACK_SCENARIOS[scenario];
    setConfig(prev => ({
      ...prev,
      target: {
        ...prev.target,
        service: scenarioConfig.service,
        port: scenarioConfig.port
      },
      credentials: {
        ...prev.credentials,
        username: scenarioConfig.username || '',
        nullPassword: scenarioConfig.nullPassword || false,
        loginAsPassword: scenarioConfig.loginAsPassword || false
      },
      attack: {
        ...prev.attack,
        parallel: scenarioConfig.parallel || prev.attack.parallel,
        exitAfterFirst: scenarioConfig.exitAfterFirst !== undefined ? scenarioConfig.exitAfterFirst : prev.attack.exitAfterFirst
      }
    }));
    toast.success(`Applied ${scenarioConfig.name} scenario`);
  };

  /**
   * Apply performance preset
   */
  const applyPreset = (preset: keyof typeof PERFORMANCE_PRESETS) => {
    const presetConfig = PERFORMANCE_PRESETS[preset];
    setConfig(prev => ({
      ...prev,
      attack: {
        ...prev.attack,
        parallel: presetConfig.parallel,
        waitTime: presetConfig.waitTime,
        timeout: presetConfig.timeout,
        retries: presetConfig.retries
      }
    }));
    toast.success(`Applied ${presetConfig.name} preset`);
  };

  /**
   * Update port based on service selection
   */
  const updateService = (service: string) => {
    const serviceInfo = HYDRA_SERVICES[service as keyof typeof HYDRA_SERVICES];
    setConfig(prev => ({
      ...prev,
      target: {
        ...prev.target,
        service,
        port: serviceInfo?.port || prev.target.port
      },
      advanced: {
        ...prev.advanced,
        useSSL: service.includes('https') || service.includes('ssl')
      }
    }));
  };

  /**
   * Generate Hydra command
   */
  const generateCommand = (): string => {
    const parts = ['hydra'];

    // Credentials
    if (config.credentials.username) {
      parts.push(`-l ${config.credentials.username}`);
    } else if (config.credentials.userList) {
      parts.push(`-L ${config.credentials.userList}`);
    } else if (config.credentials.userFile) {
      parts.push(`-L ${config.credentials.userFile}`);
    }

    if (config.credentials.password) {
      parts.push(`-p ${config.credentials.password}`);
    } else if (config.credentials.passList) {
      parts.push(`-P ${config.credentials.passList}`);
    } else if (config.credentials.passFile) {
      parts.push(`-P ${config.credentials.passFile}`);
    }

    if (config.credentials.comboFile) {
      parts.push(`-C ${config.credentials.comboFile}`);
    }

    // Special credential options
    if (config.credentials.nullPassword) {
      parts.push('-e n');
    }
    if (config.credentials.loginAsPassword) {
      parts.push('-e s');
    }
    if (config.credentials.reverseLogin) {
      parts.push('-e r');
    }

    // Attack options
    parts.push(`-t ${config.attack.parallel}`);
    if (config.attack.waitTime > 0) {
      parts.push(`-w ${config.attack.waitTime}`);
    }
    parts.push(`-W ${config.attack.timeout}`);
    if (config.attack.retries > 1) {
      parts.push(`-M ${config.attack.retries}`);
    }
    if (config.attack.exitAfterFirst) {
      parts.push('-f');
    }
    if (config.attack.continueOnSuccess) {
      parts.push('-F');
    }
    if (config.attack.showAttempts) {
      parts.push('-V');
    }
    if (config.attack.colon) {
      parts.push('-C');
    }

    // Output options
    if (config.output.verbose) {
      parts.push('-v');
    }
    if (config.output.debug) {
      parts.push('-d');
    }
    if (config.output.outputFile) {
      parts.push(`-o ${config.output.outputFile}`);
    }
    if (config.output.resume) {
      parts.push(`-R ${config.output.resume}`);
    }

    // Advanced options
    if (config.advanced.useSSL) {
      parts.push('-S');
    }
    if (config.advanced.ignoreSslErrors) {
      parts.push('-I');
    }
    if (config.advanced.proxyUrl) {
      parts.push(`-P ${config.advanced.proxyUrl}`);
    }
    if (config.advanced.userAgent) {
      parts.push(`-U "${config.advanced.userAgent}"`);
    }
    if (config.advanced.customOptions) {
      parts.push(config.advanced.customOptions);
    }

    // Target and service
    if (config.target.hostFile) {
      parts.push(`-M ${config.target.hostFile}`);
    } else if (config.target.targetList) {
      // Multiple targets
      parts.push(config.target.targetList);
    } else {
      parts.push(config.target.host);
    }

    // Service specification
    if (config.target.port !== HYDRA_SERVICES[config.target.service as keyof typeof HYDRA_SERVICES]?.port) {
      parts.push(`-s ${config.target.port}`);
    }
    parts.push(config.target.service);

    // Module options
    if (config.advanced.moduleOptions) {
      parts.push(`"${config.advanced.moduleOptions}"`);
    }

    return parts.join(' ');
  };

  /**
   * Start Hydra execution
   */
  const startExecution = async () => {
    if (!isConnected) {
      toast.error('Backend not connected');
      return;
    }

    // Validation
    if (!config.target.host && !config.target.targetList && !config.target.hostFile) {
      toast.error('Please specify a target host, target list, or host file');
      return;
    }

    if (!config.credentials.username && !config.credentials.userList && !config.credentials.userFile && !config.credentials.comboFile) {
      toast.error('Please specify username(s) or combo file');
      return;
    }

    if (!config.credentials.password && !config.credentials.passList && !config.credentials.passFile && !config.credentials.comboFile && !config.credentials.nullPassword && !config.credentials.loginAsPassword) {
      toast.error('Please specify password(s), enable null passwords, or use login-as-password option');
      return;
    }

    try {
      setIsRunning(true);
      setOutput([]);
      setResults([]);
      setProgress(0);
      setStatistics({
        totalAttempts: 0,
        successfulLogins: 0,
        failedAttempts: 0,
        averageTime: 0
      });

      const execution = await apiClient.startTool('hydra', {
        ...config,
        command: generateCommand()
      });

      setCurrentExecution(execution);
      toast.success('Hydra started successfully');

      // Listen for progress updates
      const eventSource = new EventSource(`/api/tools/hydra/execution/${execution.id}/stream`);
      
      eventSource.onmessage = (event) => {
        const data = JSON.parse(event.data);
        
        if (data.type === 'output') {
          setOutput(prev => [...prev, data.content]);
        } else if (data.type === 'progress') {
          setProgress(data.progress);
        } else if (data.type === 'statistics') {
          setStatistics(data.stats);
        } else if (data.type === 'result') {
          setResults(prev => [...prev, data.result]);
        } else if (data.type === 'complete') {
          setIsRunning(false);
          eventSource.close();
          toast.success('Hydra attack completed');
        } else if (data.type === 'error') {
          setIsRunning(false);
          eventSource.close();
          toast.error(`Error: ${data.message}`);
        }
      };

      eventSource.onerror = () => {
        setIsRunning(false);
        eventSource.close();
        toast.error('Connection to execution stream lost');
      };

    } catch (error) {
      setIsRunning(false);
      console.error('Failed to start Hydra:', error);
      toast.error('Failed to start Hydra');
    }
  };

  /**
   * Stop execution
   */
  const stopExecution = async () => {
    if (!currentExecution) return;

    try {
      await apiClient.stopTool('hydra', currentExecution.id);
      setIsRunning(false);
      toast.success('Hydra stopped');
    } catch (error) {
      console.error('Failed to stop Hydra:', error);
      toast.error('Failed to stop Hydra');
    }
  };

  /**
   * Export results
   */
  const exportResults = () => {
    const exportData = {
      config,
      results,
      statistics,
      command: generateCommand(),
      timestamp: new Date().toISOString()
    };

    const blob = new Blob([JSON.stringify(exportData, null, 2)], {
      type: 'application/json'
    });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `hydra-results-${Date.now()}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    
    toast.success('Results exported successfully');
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className="flex items-center justify-center w-10 h-10 bg-blue-100 rounded-lg">
            <Network className="w-5 h-5 text-blue-600" />
          </div>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Hydra</h1>
            <p className="text-sm text-gray-500">Fast network login cracker supporting many different services</p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Badge variant={isConnected ? "default" : "secondary"}>
            {isConnected ? "Connected" : "Disconnected"}
          </Badge>
          <Badge variant="outline" className="font-mono">v9.5</Badge>
        </div>
      </div>

      {/* Warning Notice */}
      <Card className="border-red-200 bg-red-50">
        <CardContent className="pt-4">
          <div className="flex items-start space-x-2">
            <AlertTriangle className="w-5 h-5 text-red-600 mt-0.5" />
            <div className="space-y-1">
              <p className="text-sm font-medium text-red-800">
                Authorized Testing Only
              </p>
              <p className="text-xs text-red-700">
                Use Hydra only on systems you own or have explicit written permission to test. 
                Unauthorized access attempts are illegal and may result in serious legal consequences.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Quick Start Scenarios */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Zap className="w-5 h-5" />
            <span>Quick Start Scenarios</span>
          </CardTitle>
          <CardDescription>
            Pre-configured attack scenarios for common services
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 lg:grid-cols-3 gap-3">
            {Object.entries(ATTACK_SCENARIOS).map(([key, scenario]) => (
              <Button
                key={key}
                variant="outline"
                size="sm"
                onClick={() => applyScenario(key as keyof typeof ATTACK_SCENARIOS)}
                className="h-auto p-3 text-left justify-start"
              >
                <div>
                  <div className="font-medium text-sm">{scenario.name}</div>
                  <div className="text-xs text-gray-500">{scenario.description}</div>
                </div>
              </Button>
            ))}
          </div>
        </CardContent>
      </Card>

      <Tabs defaultValue="configure" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="configure" className="flex items-center space-x-2">
            <Settings className="w-4 h-4" />
            <span>Configure</span>
          </TabsTrigger>
          <TabsTrigger value="output" className="flex items-center space-x-2">
            <Terminal className="w-4 h-4" />
            <span>Output</span>
          </TabsTrigger>
          <TabsTrigger value="results" className="flex items-center space-x-2">
            <Target className="w-4 h-4" />
            <span>Results</span>
          </TabsTrigger>
          <TabsTrigger value="command" className="flex items-center space-x-2">
            <Code className="w-4 h-4" />
            <span>Command</span>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="configure" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Target Configuration */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Target className="w-5 h-5" />
                  <span>Target Configuration</span>
                </CardTitle>
                <CardDescription>
                  Specify the target hosts and service to attack
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium">Target Host</label>
                  <Input
                    placeholder="************* or example.com"
                    value={config.target.host}
                    onChange={(e) => setConfig(prev => ({
                      ...prev,
                      target: { ...prev.target, host: e.target.value }
                    }))}
                  />
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">Service</label>
                  <Select 
                    value={config.target.service} 
                    onValueChange={updateService}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {Object.entries(
                        Object.entries(HYDRA_SERVICES).reduce((acc, [key, value]) => {
                          if (!acc[value.category]) acc[value.category] = [];
                          acc[value.category].push({ key, ...value });
                          return acc;
                        }, {} as Record<string, any>)
                      ).map(([category, services]) => (
                        <div key={category}>
                          <div className="px-2 py-1 text-xs font-semibold text-gray-500 uppercase">
                            {category}
                          </div>
                          {services.map((service: any) => (
                            <SelectItem key={service.key} value={service.key}>
                              <div className="flex items-center justify-between w-full">
                                <span>{service.name}</span>
                                <span className="text-xs text-gray-500 ml-2">:{service.port}</span>
                              </div>
                            </SelectItem>
                          ))}
                        </div>
                      ))}
                    </SelectContent>
                  </Select>
                  <p className="text-xs text-gray-500">
                    {HYDRA_SERVICES[config.target.service as keyof typeof HYDRA_SERVICES]?.description}
                  </p>
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">Port</label>
                  <Input
                    type="number"
                    min="1"
                    max="65535"
                    value={config.target.port}
                    onChange={(e) => setConfig(prev => ({
                      ...prev,
                      target: { ...prev.target, port: parseInt(e.target.value) || 22 }
                    }))}
                  />
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">Multiple Targets (Alternative)</label>
                  <Textarea
                    placeholder="*************&#10;*************&#10;example.com"
                    value={config.target.targetList}
                    onChange={(e) => setConfig(prev => ({
                      ...prev,
                      target: { ...prev.target, targetList: e.target.value }
                    }))}
                    rows={3}
                  />
                  <p className="text-xs text-gray-500">One target per line</p>
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">Host File (Alternative)</label>
                  <Input
                    placeholder="/path/to/hosts.txt"
                    value={config.target.hostFile}
                    onChange={(e) => setConfig(prev => ({
                      ...prev,
                      target: { ...prev.target, hostFile: e.target.value }
                    }))}
                  />
                </div>
              </CardContent>
            </Card>

            {/* Credentials Configuration */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Key className="w-5 h-5" />
                  <span>Credentials Configuration</span>
                </CardTitle>
                <CardDescription>
                  Configure usernames and passwords for the attack
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Username</label>
                    <Input
                      placeholder="admin"
                      value={config.credentials.username}
                      onChange={(e) => setConfig(prev => ({
                        ...prev,
                        credentials: { ...prev.credentials, username: e.target.value }
                      }))}
                    />
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Password</label>
                    <Input
                      type="password"
                      placeholder="password123"
                      value={config.credentials.password}
                      onChange={(e) => setConfig(prev => ({
                        ...prev,
                        credentials: { ...prev.credentials, password: e.target.value }
                      }))}
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">Username List</label>
                  <Textarea
                    placeholder="admin&#10;administrator&#10;root&#10;user"
                    value={config.credentials.userList}
                    onChange={(e) => setConfig(prev => ({
                      ...prev,
                      credentials: { ...prev.credentials, userList: e.target.value }
                    }))}
                    rows={3}
                  />
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">Password List</label>
                  <Textarea
                    placeholder="password&#10;123456&#10;admin&#10;letmein"
                    value={config.credentials.passList}
                    onChange={(e) => setConfig(prev => ({
                      ...prev,
                      credentials: { ...prev.credentials, passList: e.target.value }
                    }))}
                    rows={3}
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Username File</label>
                    <Input
                      placeholder="/path/to/usernames.txt"
                      value={config.credentials.userFile}
                      onChange={(e) => setConfig(prev => ({
                        ...prev,
                        credentials: { ...prev.credentials, userFile: e.target.value }
                      }))}
                    />
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Password File</label>
                    <Input
                      placeholder="/path/to/passwords.txt"
                      value={config.credentials.passFile}
                      onChange={(e) => setConfig(prev => ({
                        ...prev,
                        credentials: { ...prev.credentials, passFile: e.target.value }
                      }))}
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">Combo File (user:pass format)</label>
                  <Input
                    placeholder="/path/to/combo.txt"
                    value={config.credentials.comboFile}
                    onChange={(e) => setConfig(prev => ({
                      ...prev,
                      credentials: { ...prev.credentials, comboFile: e.target.value }
                    }))}
                  />
                </div>

                <div className="space-y-3">
                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="null-password"
                      checked={config.credentials.nullPassword}
                      onChange={(e) => setConfig(prev => ({
                        ...prev,
                        credentials: { ...prev.credentials, nullPassword: e.target.checked }
                      }))}
                      className="rounded"
                    />
                    <label htmlFor="null-password" className="text-sm font-medium">
                      Try null password
                    </label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="login-as-pass"
                      checked={config.credentials.loginAsPassword}
                      onChange={(e) => setConfig(prev => ({
                        ...prev,
                        credentials: { ...prev.credentials, loginAsPassword: e.target.checked }
                      }))}
                      className="rounded"
                    />
                    <label htmlFor="login-as-pass" className="text-sm font-medium">
                      Try login as password
                    </label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="reverse-login"
                      checked={config.credentials.reverseLogin}
                      onChange={(e) => setConfig(prev => ({
                        ...prev,
                        credentials: { ...prev.credentials, reverseLogin: e.target.checked }
                      }))}
                      className="rounded"
                    />
                    <label htmlFor="reverse-login" className="text-sm font-medium">
                      Try reverse login (password as username)
                    </label>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Attack Configuration */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Zap className="w-5 h-5" />
                  <span>Attack Configuration</span>
                </CardTitle>
                <CardDescription>
                  Configure attack parameters and performance
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium">Performance Presets</label>
                  <div className="grid grid-cols-2 gap-2">
                    {Object.entries(PERFORMANCE_PRESETS).map(([key, preset]) => (
                      <Button
                        key={key}
                        variant="outline"
                        size="sm"
                        onClick={() => applyPreset(key as keyof typeof PERFORMANCE_PRESETS)}
                        className="h-auto p-3 text-left"
                      >
                        <div>
                          <div className="font-medium text-xs">{preset.name}</div>
                          <div className="text-xs text-gray-500">{preset.description}</div>
                        </div>
                      </Button>
                    ))}
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Parallel Connections</label>
                    <Input
                      type="number"
                      min="1"
                      max="256"
                      value={config.attack.parallel}
                      onChange={(e) => setConfig(prev => ({
                        ...prev,
                        attack: { ...prev.attack, parallel: parseInt(e.target.value) || 16 }
                      }))}
                    />
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Wait Time (seconds)</label>
                    <Input
                      type="number"
                      min="0"
                      max="60"
                      value={config.attack.waitTime}
                      onChange={(e) => setConfig(prev => ({
                        ...prev,
                        attack: { ...prev.attack, waitTime: parseInt(e.target.value) || 0 }
                      }))}
                    />
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Timeout (seconds)</label>
                    <Input
                      type="number"
                      min="1"
                      max="300"
                      value={config.attack.timeout}
                      onChange={(e) => setConfig(prev => ({
                        ...prev,
                        attack: { ...prev.attack, timeout: parseInt(e.target.value) || 30 }
                      }))}
                    />
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Retries</label>
                    <Input
                      type="number"
                      min="1"
                      max="10"
                      value={config.attack.retries}
                      onChange={(e) => setConfig(prev => ({
                        ...prev,
                        attack: { ...prev.attack, retries: parseInt(e.target.value) || 3 }
                      }))}
                    />
                  </div>
                </div>

                <div className="space-y-3">
                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="exit-after-first"
                      checked={config.attack.exitAfterFirst}
                      onChange={(e) => setConfig(prev => ({
                        ...prev,
                        attack: { ...prev.attack, exitAfterFirst: e.target.checked }
                      }))}
                      className="rounded"
                    />
                    <label htmlFor="exit-after-first" className="text-sm font-medium">
                      Exit after first successful login per host
                    </label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="continue-on-success"
                      checked={config.attack.continueOnSuccess}
                      onChange={(e) => setConfig(prev => ({
                        ...prev,
                        attack: { ...prev.attack, continueOnSuccess: e.target.checked }
                      }))}
                      className="rounded"
                    />
                    <label htmlFor="continue-on-success" className="text-sm font-medium">
                      Continue attack after successful login
                    </label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="show-attempts"
                      checked={config.attack.showAttempts}
                      onChange={(e) => setConfig(prev => ({
                        ...prev,
                        attack: { ...prev.attack, showAttempts: e.target.checked }
                      }))}
                      className="rounded"
                    />
                    <label htmlFor="show-attempts" className="text-sm font-medium">
                      Show login attempts
                    </label>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Advanced Options */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Settings className="w-5 h-5" />
                  <span>Advanced Options</span>
                </CardTitle>
                <CardDescription>
                  SSL, proxy, and other advanced configuration
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="use-ssl"
                      checked={config.advanced.useSSL}
                      onChange={(e) => setConfig(prev => ({
                        ...prev,
                        advanced: { ...prev.advanced, useSSL: e.target.checked }
                      }))}
                      className="rounded"
                    />
                    <label htmlFor="use-ssl" className="text-sm font-medium">
                      Use SSL/TLS encryption
                    </label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="ignore-ssl"
                      checked={config.advanced.ignoreSslErrors}
                      onChange={(e) => setConfig(prev => ({
                        ...prev,
                        advanced: { ...prev.advanced, ignoreSslErrors: e.target.checked }
                      }))}
                      className="rounded"
                    />
                    <label htmlFor="ignore-ssl" className="text-sm font-medium">
                      Ignore SSL certificate errors
                    </label>
                  </div>
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">Proxy URL</label>
                  <Input
                    placeholder="socks4://127.0.0.1:1080"
                    value={config.advanced.proxyUrl}
                    onChange={(e) => setConfig(prev => ({
                      ...prev,
                      advanced: { ...prev.advanced, proxyUrl: e.target.value }
                    }))}
                  />
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">User Agent</label>
                  <Input
                    placeholder="Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36"
                    value={config.advanced.userAgent}
                    onChange={(e) => setConfig(prev => ({
                      ...prev,
                      advanced: { ...prev.advanced, userAgent: e.target.value }
                    }))}
                  />
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">Module Options</label>
                  <Input
                    placeholder="/login.php:username=^USER^&password=^PASS^:Invalid"
                    value={config.advanced.moduleOptions}
                    onChange={(e) => setConfig(prev => ({
                      ...prev,
                      advanced: { ...prev.advanced, moduleOptions: e.target.value }
                    }))}
                  />
                  <p className="text-xs text-gray-500">
                    Service-specific options (e.g., HTTP form parameters)
                  </p>
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">Custom Options</label>
                  <Input
                    placeholder="-6 -q -x"
                    value={config.advanced.customOptions}
                    onChange={(e) => setConfig(prev => ({
                      ...prev,
                      advanced: { ...prev.advanced, customOptions: e.target.value }
                    }))}
                  />
                  <p className="text-xs text-gray-500">
                    Additional command line options
                  </p>
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">Output File</label>
                  <Input
                    placeholder="/tmp/hydra_results.txt"
                    value={config.output.outputFile}
                    onChange={(e) => setConfig(prev => ({
                      ...prev,
                      output: { ...prev.output, outputFile: e.target.value }
                    }))}
                  />
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Execution Controls */}
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <Button
                    onClick={startExecution}
                    disabled={!isConnected || isRunning}
                    size="lg"
                    className="bg-blue-600 hover:bg-blue-700"
                  >
                    <Play className="w-4 h-4 mr-2" />
                    Start Hydra Attack
                  </Button>
                  
                  {isRunning && (
                    <Button
                      onClick={stopExecution}
                      variant="outline"
                      size="lg"
                    >
                      <Square className="w-4 h-4 mr-2" />
                      Stop
                    </Button>
                  )}
                </div>

                <div className="flex items-center space-x-2">
                  {isRunning && (
                    <div className="flex items-center space-x-2">
                      <Activity className="w-4 h-4 text-green-600 animate-pulse" />
                      <span className="text-sm text-green-600">Running...</span>
                    </div>
                  )}
                  <Badge variant="outline">
                    {results.length} successful logins
                  </Badge>
                </div>
              </div>

              {isRunning && (
                <div className="mt-4 space-y-3">
                  <div className="flex items-center justify-between text-sm text-gray-600 mb-2">
                    <span>Progress</span>
                    <span>{progress.toFixed(1)}%</span>
                  </div>
                  <Progress value={progress} className="h-2" />
                  
                  <div className="grid grid-cols-4 gap-4 text-sm">
                    <div className="text-center">
                      <div className="font-semibold text-lg">{statistics.totalAttempts}</div>
                      <div className="text-gray-600">Total Attempts</div>
                    </div>
                    <div className="text-center">
                      <div className="font-semibold text-lg text-green-600">{statistics.successfulLogins}</div>
                      <div className="text-gray-600">Successful</div>
                    </div>
                    <div className="text-center">
                      <div className="font-semibold text-lg text-red-600">{statistics.failedAttempts}</div>
                      <div className="text-gray-600">Failed</div>
                    </div>
                    <div className="text-center">
                      <div className="font-semibold text-lg">{statistics.averageTime.toFixed(1)}s</div>
                      <div className="text-gray-600">Avg Time</div>
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="output" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Terminal className="w-5 h-5" />
                <span>Real-time Output</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="bg-black rounded-lg p-4 h-96 overflow-y-auto font-mono text-sm">
                {output.length === 0 ? (
                  <div className="text-gray-500 italic">
                    No output yet. Start Hydra to see attack progress.
                  </div>
                ) : (
                  output.map((line, index) => (
                    <div key={index} className={cn(
                      "text-green-400",
                      line.includes('[ERROR]') && "text-red-400",
                      line.includes('[SUCCESS]') && "text-yellow-400",
                      line.includes('[INFO]') && "text-blue-400"
                    )}>
                      {line}
                    </div>
                  ))
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="results" className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold">Successful Logins</h3>
            {results.length > 0 && (
              <Button onClick={exportResults} variant="outline">
                <Download className="w-4 h-4 mr-2" />
                Export Results
              </Button>
            )}
          </div>

          {results.length === 0 ? (
            <Card>
              <CardContent className="pt-6">
                <div className="text-center py-8">
                  <UserX className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500">No successful logins found</p>
                  <p className="text-sm text-gray-400">Results will appear here as credentials are discovered</p>
                </div>
              </CardContent>
            </Card>
          ) : (
            <div className="grid gap-4">
              {results.map((result, index) => (
                <Card key={index} className="border-green-200 bg-green-50">
                  <CardContent className="pt-4">
                    <div className="flex items-center justify-between">
                      <div className="space-y-2">
                        <div className="flex items-center space-x-2">
                          <CheckCircle className="w-5 h-5 text-green-600" />
                          <Badge variant="outline" className="bg-green-100">
                            {result.service}
                          </Badge>
                          <span className="font-mono text-sm text-gray-600">
                            {result.host}:{result.port}
                          </span>
                        </div>
                        <div className="space-y-1">
                          <div className="flex items-center space-x-2">
                            <span className="text-sm text-gray-600">Username:</span>
                            <span className="font-mono font-semibold">{result.username}</span>
                          </div>
                          <div className="flex items-center space-x-2">
                            <span className="text-sm text-gray-600">Password:</span>
                            <span className="font-mono font-semibold text-green-600">{result.password}</span>
                          </div>
                        </div>
                        {result.additional && (
                          <div className="text-xs text-gray-500">
                            Additional info: {result.additional}
                          </div>
                        )}
                      </div>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          navigator.clipboard.writeText(`${result.username}:${result.password}`);
                          toast.success('Credentials copied to clipboard');
                        }}
                      >
                        <Copy className="w-4 h-4" />
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>

        <TabsContent value="command" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Code className="w-5 h-5" />
                <span>Generated Command</span>
              </CardTitle>
              <CardDescription>
                Review and copy the Hydra command
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="bg-gray-900 rounded-lg p-4">
                  <code className="text-green-400 font-mono text-sm whitespace-pre-wrap">
                    {generateCommand()}
                  </code>
                </div>
                <div className="flex space-x-2">
                  <Button
                    variant="outline"
                    onClick={() => {
                      navigator.clipboard.writeText(generateCommand());
                      toast.success('Command copied to clipboard');
                    }}
                  >
                    <Copy className="w-4 h-4 mr-2" />
                    Copy Command
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Command Explanation */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Book className="w-5 h-5" />
                <span>Command Explanation</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3 text-sm">
                <div>
                  <span className="font-semibold">hydra</span>
                  <span className="text-gray-600 ml-2">- Hydra network login cracker</span>
                </div>
                
                {config.credentials.username && (
                  <div>
                    <span className="font-semibold">-l {config.credentials.username}</span>
                    <span className="text-gray-600 ml-2">- Single username</span>
                  </div>
                )}
                
                {config.credentials.password && (
                  <div>
                    <span className="font-semibold">-p {config.credentials.password}</span>
                    <span className="text-gray-600 ml-2">- Single password</span>
                  </div>
                )}
                
                <div>
                  <span className="font-semibold">-t {config.attack.parallel}</span>
                  <span className="text-gray-600 ml-2">- Number of parallel connections</span>
                </div>
                
                <div>
                  <span className="font-semibold">-W {config.attack.timeout}</span>
                  <span className="text-gray-600 ml-2">- Connection timeout in seconds</span>
                </div>
                
                {config.attack.exitAfterFirst && (
                  <div>
                    <span className="font-semibold">-f</span>
                    <span className="text-gray-600 ml-2">- Exit after first successful login</span>
                  </div>
                )}
                
                <div>
                  <span className="font-semibold">{config.target.host} {config.target.service}</span>
                  <span className="text-gray-600 ml-2">- Target host and service</span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Wordlist Recommendations */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <List className="w-5 h-5" />
                <span>Common Wordlists</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h4 className="font-semibold mb-2">Usernames</h4>
                  <div className="space-y-2">
                    {COMMON_WORDLISTS.usernames.map((list, index) => (
                      <div key={index} className="flex items-center justify-between text-sm">
                        <div>
                          <div className="font-medium">{list.name}</div>
                          <div className="text-gray-500 font-mono text-xs">{list.path}</div>
                        </div>
                        <Badge variant="outline" className="text-xs">{list.size}</Badge>
                      </div>
                    ))}
                  </div>
                </div>
                
                <div>
                  <h4 className="font-semibold mb-2">Passwords</h4>
                  <div className="space-y-2">
                    {COMMON_WORDLISTS.passwords.map((list, index) => (
                      <div key={index} className="flex items-center justify-between text-sm">
                        <div>
                          <div className="font-medium">{list.name}</div>
                          <div className="text-gray-500 font-mono text-xs">{list.path}</div>
                        </div>
                        <Badge variant="outline" className="text-xs">{list.size}</Badge>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}