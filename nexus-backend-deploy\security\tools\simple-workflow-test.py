#!/usr/bin/env python3
"""
Simple System Workflow Test
Tests core functionality without complex async operations
"""

import subprocess
import requests
import time
import signal
import os
import json

def print_step(step, message):
    print(f"\n[STEP {step}] {message}")
    print("-" * 50)

def cleanup():
    """Kill any existing backend processes"""
    try:
        subprocess.run("pkill -f 'python.*main.py'", shell=True, capture_output=True)
        subprocess.run("lsof -ti:8000 | xargs kill -9", shell=True, capture_output=True)
        subprocess.run("lsof -ti:8002 | xargs kill -9", shell=True, capture_output=True)
    except:
        pass
    time.sleep(2)

def test_backend():
    """Test backend startup and API"""
    print_step(1, "Testing Backend Startup")
    
    # Set environment
    env = os.environ.copy()
    env['METRICS_PORT'] = '8002'
    
    # Start backend
    print("Starting backend...")
    proc = subprocess.Popen(
        ['bash', '-c', 'source venv/bin/activate && python src/main.py'],
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        env=env,
        preexec_fn=os.setsid
    )
    
    # Wait for startup
    print("Waiting for backend startup...")
    for i in range(30):
        try:
            response = requests.get('http://localhost:8000/api/health', timeout=1)
            if response.status_code == 200:
                print(f"✅ Backend started successfully after {i+1} seconds")
                break
        except:
            pass
        time.sleep(1)
        print(f"  Waiting... {i+1}/30")
    else:
        print("❌ Backend failed to start")
        proc.terminate()
        return False, None
    
    return True, proc

def test_api_endpoints(base_url):
    """Test API endpoints"""
    print_step(2, "Testing API Endpoints")
    
    endpoints = [
        "/health",
        "/campaigns", 
        "/scans",
        "/vulnerabilities",
        "/tools",
        "/ai/providers"
    ]
    
    results = {}
    for endpoint in endpoints:
        try:
            url = f"{base_url}{endpoint}"
            response = requests.get(url, timeout=5)
            status = "✅ PASS" if response.status_code == 200 else f"❌ FAIL ({response.status_code})"
            print(f"  {endpoint}: {status}")
            results[endpoint] = response.status_code == 200
        except Exception as e:
            print(f"  {endpoint}: ❌ ERROR ({str(e)})")
            results[endpoint] = False
    
    return results

def test_campaign_creation(base_url):
    """Test campaign creation"""
    print_step(3, "Testing Campaign Creation")
    
    campaign_data = {
        "name": f"Test Campaign {int(time.time())}",
        "description": "Automated test campaign",
        "targets": ["test.example.com"],
        "scan_types": ["port_scan"]
    }
    
    try:
        response = requests.post(
            f"{base_url}/campaigns",
            json=campaign_data,
            timeout=10
        )
        
        if response.status_code in [200, 201]:
            data = response.json()
            campaign_id = data.get('data', {}).get('id')
            print(f"✅ Campaign created successfully (ID: {campaign_id})")
            return True, campaign_id
        else:
            print(f"❌ Campaign creation failed ({response.status_code})")
            try:
                error = response.json()
                print(f"   Error: {error}")
            except:
                pass
            return False, None
    except Exception as e:
        print(f"❌ Campaign creation error: {str(e)}")
        return False, None

def test_frontend():
    """Test frontend build and startup"""
    print_step(4, "Testing Frontend")
    
    # Test build
    print("Testing frontend build...")
    result = subprocess.run(
        ['npm', 'run', 'build'],
        cwd='frontend',
        capture_output=True,
        text=True
    )
    
    if result.returncode == 0:
        print("✅ Frontend build successful")
    else:
        print("❌ Frontend build failed")
        print(f"Error: {result.stderr}")
        return False
    
    # Test dev server startup (brief test)
    print("Testing frontend dev server...")
    proc = subprocess.Popen(
        ['npm', 'run', 'dev'],
        cwd='frontend',
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        preexec_fn=os.setsid
    )
    
    # Check if it starts
    time.sleep(10)
    try:
        response = requests.get('http://localhost:1420', timeout=2)
        if response.status_code == 200:
            print("✅ Frontend dev server accessible")
            frontend_ok = True
        else:
            print(f"❌ Frontend not accessible ({response.status_code})")
            frontend_ok = False
    except Exception as e:
        print(f"❌ Frontend connection error: {str(e)}")
        frontend_ok = False
    
    # Stop frontend
    try:
        os.killpg(os.getpgid(proc.pid), signal.SIGTERM)
    except:
        pass
    
    return frontend_ok

def main():
    """Main test execution"""
    print("🚀 NexusScan Desktop - Simple Workflow Test")
    print("=" * 60)
    
    cleanup()
    
    backend_proc = None
    test_results = {}
    
    try:
        # Test backend
        backend_ok, backend_proc = test_backend()
        test_results['backend_startup'] = backend_ok
        
        if not backend_ok:
            print("\n❌ Backend failed - stopping tests")
            return
        
        # Test API endpoints
        api_results = test_api_endpoints("http://localhost:8000/api")
        test_results['api_endpoints'] = api_results
        
        # Test campaign creation
        campaign_ok, campaign_id = test_campaign_creation("http://localhost:8000/api")
        test_results['campaign_creation'] = campaign_ok
        
        # Test frontend (change back to root directory first)
        os.chdir('/mnt/e/dev/nexusscan-desktop')
        frontend_ok = test_frontend()
        test_results['frontend'] = frontend_ok
        
        # Summary
        print_step(5, "Test Summary")
        print(f"Backend Startup: {'✅' if test_results['backend_startup'] else '❌'}")
        
        api_passed = sum(1 for v in test_results['api_endpoints'].values() if v)
        api_total = len(test_results['api_endpoints'])
        print(f"API Endpoints: {api_passed}/{api_total} passed")
        
        print(f"Campaign Creation: {'✅' if test_results['campaign_creation'] else '❌'}")
        print(f"Frontend: {'✅' if test_results['frontend'] else '❌'}")
        
        # Overall result
        overall_score = (
            (1 if test_results['backend_startup'] else 0) +
            (api_passed / api_total) +
            (1 if test_results['campaign_creation'] else 0) +
            (1 if test_results['frontend'] else 0)
        ) / 4 * 100
        
        print(f"\nOverall System Health: {overall_score:.1f}%")
        
        if overall_score >= 80:
            print("🎉 SYSTEM INTEGRATION: EXCELLENT")
        elif overall_score >= 60:
            print("⚠️  SYSTEM INTEGRATION: GOOD (some issues)")
        else:
            print("❌ SYSTEM INTEGRATION: NEEDS WORK")
        
        print(f"\nSystem is running at http://localhost:8000/api/")
        print("Press Ctrl+C to stop...")
        
        # Keep running for manual testing
        while True:
            time.sleep(1)
        
    except KeyboardInterrupt:
        print("\n\n🛑 Stopping system...")
    finally:
        if backend_proc:
            try:
                os.killpg(os.getpgid(backend_proc.pid), signal.SIGTERM)
                print("Backend stopped")
            except:
                pass
        cleanup()

if __name__ == "__main__":
    main()