#!/usr/bin/env python3
"""
AI-Powered Wordlist Management Suite for NexusScan Desktop
Intelligent wordlist generation, mutation, and optimization for security testing.
"""

import asyncio
import logging
import json
import re
import hashlib
import sqlite3
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Set, Tuple
from dataclasses import dataclass, asdict
from enum import Enum
from pathlib import Path
import aiohttp
import itertools
import string
import random

from core.config import Config
from core.database import DatabaseManager
from ai.ai_service import AIServiceManager
from ai.services import AnalysisRequest

logger = logging.getLogger(__name__)


class WordlistCategory(Enum):
    """Categories of wordlists"""
    GENERAL = "general"
    PASSWORDS = "passwords"
    DIRECTORIES = "directories"
    FILES = "files"
    SUBDOMAINS = "subdomains"
    PARAMETERS = "parameters"
    USERNAMES = "usernames"
    TECHNOLOGIES = "technologies"
    CUSTOM = "custom"


class MutationRule(Enum):
    """Mutation rules for password generation"""
    APPEND_NUMBERS = "append_numbers"
    PREPEND_NUMBERS = "prepend_numbers"
    CASE_VARIATIONS = "case_variations"
    LEET_SPEAK = "leet_speak"
    YEARS = "years"
    COMMON_SUFFIXES = "common_suffixes"
    KEYBOARD_PATTERNS = "keyboard_patterns"
    COMPANY_VARIATIONS = "company_variations"


@dataclass
class WordlistSource:
    """Wordlist source configuration"""
    name: str
    url: Optional[str]
    category: WordlistCategory
    description: str
    size: int
    last_updated: datetime
    checksum: str
    active: bool = True


@dataclass
class WordlistMetadata:
    """Wordlist metadata"""
    id: str
    name: str
    category: WordlistCategory
    source: str
    target_context: Dict[str, Any]
    generation_method: str
    word_count: int
    unique_words: int
    ai_generated: bool
    success_rate: float
    last_used: datetime
    created_at: datetime


@dataclass
class OSINTData:
    """OSINT data structure"""
    company_name: str
    domain: str
    employees: List[str]
    technologies: List[str]
    social_media: Dict[str, List[str]]
    public_documents: List[str]
    breach_data: List[str]
    keywords: List[str]


class AIWordlistGenerator:
    """AI-powered wordlist generation engine"""

    def __init__(self, config: Config, ai_service: AIServiceManager, database: DatabaseManager):
        """Initialize AI wordlist generator"""
        self.config = config
        self.ai_service = ai_service
        self.database = database
        
        # Wordlist storage
        self.wordlist_storage = Path("data/wordlists")
        self.wordlist_storage.mkdir(parents=True, exist_ok=True)
        
        # Built-in wordlist sources
        self.built_in_sources = self._initialize_built_in_sources()
        
        # Mutation engines
        self.mutation_engine = MutationEngine()
        self.osint_collector = OSINTCollector()
        
        # Success tracking
        self.success_tracker = WordlistSuccessTracker(database)
        
        # Cache for frequently used wordlists
        self.wordlist_cache: Dict[str, List[str]] = {}
        self.cache_ttl = timedelta(hours=6)
        
        logger.info("AI Wordlist Generator initialized")

    def _initialize_built_in_sources(self) -> List[WordlistSource]:
        """Initialize built-in wordlist sources"""
        return [
            WordlistSource(
                name="SecLists Common Passwords",
                url="https://raw.githubusercontent.com/danielmiessler/SecLists/master/Passwords/Common-Credentials/10-million-password-list-top-1000000.txt",
                category=WordlistCategory.PASSWORDS,
                description="Top 1M passwords from SecLists",
                size=1000000,
                last_updated=datetime.now(),
                checksum="",
                active=True
            ),
            WordlistSource(
                name="DirBuster Directory List",
                url="https://raw.githubusercontent.com/daviddias/node-dirbuster/master/lists/directory-list-2.3-medium.txt",
                category=WordlistCategory.DIRECTORIES,
                description="Medium directory wordlist",
                size=220560,
                last_updated=datetime.now(),
                checksum="",
                active=True
            ),
            WordlistSource(
                name="Common Subdomains",
                url="https://raw.githubusercontent.com/rbsec/dnscan/master/subdomains-10000.txt",
                category=WordlistCategory.SUBDOMAINS,
                description="Top 10K subdomains",
                size=10000,
                last_updated=datetime.now(),
                checksum="",
                active=True
            ),
            WordlistSource(
                name="Web Parameters",
                url="https://raw.githubusercontent.com/danielmiessler/SecLists/master/Discovery/Web-Content/burp-parameter-names.txt",
                category=WordlistCategory.PARAMETERS,
                description="Common web parameters",
                size=2588,
                last_updated=datetime.now(),
                checksum="",
                active=True
            )
        ]

    async def generate_context_aware_wordlists(self, target_info: Dict[str, Any]) -> Dict[str, List[str]]:
        """Generate wordlists based on target context and technology"""
        
        try:
            # Extract context information
            domain = target_info.get("domain", "")
            company = target_info.get("company", "")
            technologies = target_info.get("technologies", [])
            services = target_info.get("services", [])
            
            wordlists = {}
            
            # Generate company-specific wordlists
            if company:
                wordlists["company_passwords"] = await self._generate_company_passwords(company)
                wordlists["company_directories"] = await self._generate_company_directories(company)
                wordlists["company_usernames"] = await self._generate_company_usernames(company)
            
            # Generate technology-specific wordlists
            if technologies:
                wordlists["tech_directories"] = await self._generate_technology_directories(technologies)
                wordlists["tech_parameters"] = await self._generate_technology_parameters(technologies)
                wordlists["tech_files"] = await self._generate_technology_files(technologies)
            
            # Generate domain-specific wordlists
            if domain:
                wordlists["subdomain_variants"] = await self._generate_subdomain_variants(domain)
                wordlists["domain_passwords"] = await self._generate_domain_passwords(domain)
            
            # Generate service-specific wordlists
            if services:
                wordlists["service_paths"] = await self._generate_service_paths(services)
                wordlists["service_credentials"] = await self._generate_service_credentials(services)
            
            # AI-enhanced wordlist generation
            if self.ai_service:
                ai_wordlists = await self._generate_ai_wordlists(target_info)
                wordlists.update(ai_wordlists)
            
            # Store generated wordlists
            for category, wordlist in wordlists.items():
                await self._store_wordlist(category, wordlist, target_info)
            
            return wordlists
            
        except Exception as e:
            logger.error(f"Context-aware wordlist generation failed: {e}")
            return {}

    async def _generate_company_passwords(self, company: str) -> List[str]:
        """Generate company-specific password wordlist"""
        passwords = []
        
        # Base company variations
        company_clean = re.sub(r'[^a-zA-Z0-9]', '', company.lower())
        company_variations = [
            company.lower(),
            company.upper(),
            company.capitalize(),
            company_clean,
            company_clean.upper(),
            company_clean.capitalize()
        ]
        
        # Common password patterns
        years = [str(year) for year in range(2015, 2026)]
        numbers = ['1', '12', '123', '1234', '12345', '123456', '01', '001']
        symbols = ['!', '@', '#', '$', '!@', '123']
        
        # Generate combinations
        for variation in company_variations:
            passwords.append(variation)
            
            # Add years
            for year in years:
                passwords.extend([
                    f"{variation}{year}",
                    f"{year}{variation}",
                    f"{variation}_{year}",
                    f"{variation}-{year}"
                ])
            
            # Add numbers
            for num in numbers:
                passwords.extend([
                    f"{variation}{num}",
                    f"{num}{variation}",
                    f"{variation}_{num}"
                ])
            
            # Add symbols
            for symbol in symbols:
                passwords.extend([
                    f"{variation}{symbol}",
                    f"{symbol}{variation}"
                ])
        
        # Remove duplicates and sort by likelihood
        unique_passwords = list(set(passwords))
        return self._rank_by_likelihood(unique_passwords, "password")

    async def _generate_company_directories(self, company: str) -> List[str]:
        """Generate company-specific directory wordlist"""
        directories = []
        
        company_clean = re.sub(r'[^a-zA-Z0-9]', '', company.lower())
        
        # Base directories
        base_dirs = [
            company.lower(), company_clean, company.upper(),
            f"{company_clean}app", f"{company_clean}web", f"{company_clean}site",
            f"{company_clean}admin", f"{company_clean}api", f"{company_clean}portal",
            f"admin{company_clean}", f"api{company_clean}", f"portal{company_clean}"
        ]
        
        directories.extend(base_dirs)
        
        # Common business directories
        business_terms = [
            "about", "contact", "careers", "jobs", "team", "staff",
            "products", "services", "solutions", "support", "help",
            "news", "blog", "press", "media", "investor", "legal"
        ]
        
        for term in business_terms:
            directories.extend([
                f"{company_clean}{term}",
                f"{term}{company_clean}",
                f"{company_clean}_{term}",
                f"{company_clean}-{term}"
            ])
        
        return list(set(directories))

    async def _generate_company_usernames(self, company: str) -> List[str]:
        """Generate company-specific username wordlist"""
        usernames = []
        
        company_clean = re.sub(r'[^a-zA-Z0-9]', '', company.lower())
        
        # Administrative usernames
        admin_patterns = [
            f"{company_clean}admin", f"admin{company_clean}", f"{company_clean}_admin",
            f"{company_clean}user", f"user{company_clean}", f"{company_clean}_user",
            f"{company_clean}test", f"test{company_clean}", f"{company_clean}_test"
        ]
        
        usernames.extend(admin_patterns)
        
        # Service account patterns
        service_patterns = [
            f"{company_clean}service", f"service{company_clean}",
            f"{company_clean}app", f"app{company_clean}",
            f"{company_clean}api", f"api{company_clean}"
        ]
        
        usernames.extend(service_patterns)
        
        return list(set(usernames))

    async def _generate_technology_directories(self, technologies: List[str]) -> List[str]:
        """Generate technology-specific directory wordlist"""
        directories = []
        
        tech_directories = {
            "apache": ["htdocs", "www", "html", "public_html", "web"],
            "nginx": ["html", "www", "web", "public"],
            "iis": ["wwwroot", "inetpub", "web"],
            "tomcat": ["webapps", "ROOT", "manager", "host-manager"],
            "wordpress": ["wp-admin", "wp-content", "wp-includes", "wp-config"],
            "drupal": ["sites", "modules", "themes", "core"],
            "joomla": ["administrator", "components", "modules", "templates"],
            "php": ["includes", "lib", "libs", "vendor", "composer"],
            "node": ["node_modules", "public", "views", "routes"],
            "python": ["static", "templates", "media", "venv"],
            "java": ["WEB-INF", "META-INF", "classes", "lib"],
            "asp": ["bin", "App_Code", "App_Data", "App_Themes"]
        }
        
        for tech in technologies:
            tech_lower = tech.lower()
            if tech_lower in tech_directories:
                directories.extend(tech_directories[tech_lower])
                
                # Add versioned directories
                for version in ["v1", "v2", "v3", "2.0", "3.0"]:
                    directories.extend([
                        f"{tech_lower}{version}",
                        f"{tech_lower}_{version}",
                        f"{tech_lower}-{version}"
                    ])
        
        return list(set(directories))

    async def _generate_technology_parameters(self, technologies: List[str]) -> List[str]:
        """Generate technology-specific parameter wordlist"""
        parameters = []
        
        tech_parameters = {
            "php": ["page", "file", "include", "require", "id", "user", "pass"],
            "asp": ["page", "file", "id", "user", "password", "cmd"],
            "jsp": ["page", "file", "id", "user", "password", "action"],
            "python": ["page", "file", "id", "user", "password", "data"],
            "node": ["page", "file", "id", "user", "password", "query"],
            "sql": ["id", "user", "username", "password", "query", "search"],
            "mysql": ["id", "user", "password", "table", "database"],
            "postgresql": ["id", "user", "password", "table", "schema"]
        }
        
        for tech in technologies:
            tech_lower = tech.lower()
            if tech_lower in tech_parameters:
                parameters.extend(tech_parameters[tech_lower])
        
        # Add common web parameters
        common_params = [
            "q", "query", "search", "s", "keyword", "term",
            "page", "p", "pagenum", "offset", "limit",
            "id", "uid", "userid", "user_id",
            "action", "act", "cmd", "command",
            "file", "filename", "path", "dir",
            "callback", "jsonp", "format", "type"
        ]
        
        parameters.extend(common_params)
        return list(set(parameters))

    async def _generate_technology_files(self, technologies: List[str]) -> List[str]:
        """Generate technology-specific file wordlist"""
        files = []
        
        tech_files = {
            "php": [".php", ".php3", ".php4", ".php5", ".phtml"],
            "asp": [".asp", ".aspx", ".asa", ".cer"],
            "jsp": [".jsp", ".jspx", ".jsw", ".jsv"],
            "python": [".py", ".pyc", ".pyo", ".wsgi"],
            "perl": [".pl", ".pm", ".cgi"],
            "ruby": [".rb", ".rhtml", ".erb"],
            "javascript": [".js", ".json", ".jsx"],
            "config": [".conf", ".config", ".cfg", ".ini"],
            "backup": [".bak", ".backup", ".old", ".orig", ".save"],
            "log": [".log", ".logs", ".txt"]
        }
        
        # Common filenames
        common_files = [
            "index", "default", "home", "main", "admin", "login",
            "config", "settings", "database", "db", "connection",
            "test", "demo", "example", "sample", "backup"
        ]
        
        # Generate file combinations
        for tech in technologies:
            tech_lower = tech.lower()
            if tech_lower in tech_files:
                extensions = tech_files[tech_lower]
                for filename in common_files:
                    for ext in extensions:
                        files.append(f"{filename}{ext}")
        
        # Add common config files
        config_files = [
            "web.config", "app.config", "database.config",
            "wp-config.php", "config.php", "settings.php",
            ".htaccess", ".htpasswd", "robots.txt", "sitemap.xml"
        ]
        
        files.extend(config_files)
        return list(set(files))

    async def _generate_subdomain_variants(self, domain: str) -> List[str]:
        """Generate subdomain variants for a domain"""
        subdomains = []
        
        # Extract base domain parts
        domain_parts = domain.split('.')
        if len(domain_parts) >= 2:
            company = domain_parts[0]
            
            # Common subdomain patterns
            common_subs = [
                "www", "mail", "ftp", "admin", "api", "app", "blog",
                "dev", "test", "staging", "prod", "beta", "demo",
                "secure", "portal", "login", "auth", "sso",
                "cdn", "static", "assets", "img", "images",
                "docs", "help", "support", "wiki", "kb"
            ]
            
            subdomains.extend(common_subs)
            
            # Company-specific subdomains
            company_subs = [
                f"{company}admin", f"{company}api", f"{company}app",
                f"admin{company}", f"api{company}", f"app{company}",
                f"{company}dev", f"{company}test", f"{company}prod"
            ]
            
            subdomains.extend(company_subs)
            
            # Geographic subdomains
            geo_subs = [
                "us", "eu", "asia", "na", "emea", "apac",
                "east", "west", "north", "south",
                "ny", "ca", "tx", "london", "paris", "tokyo"
            ]
            
            subdomains.extend(geo_subs)
        
        return list(set(subdomains))

    async def _generate_domain_passwords(self, domain: str) -> List[str]:
        """Generate domain-specific passwords"""
        passwords = []
        
        domain_parts = domain.split('.')
        if domain_parts:
            base_domain = domain_parts[0]
            passwords.extend(await self._generate_company_passwords(base_domain))
        
        return passwords

    async def _generate_service_paths(self, services: List[str]) -> List[str]:
        """Generate service-specific paths"""
        paths = []
        
        service_paths = {
            "ssh": ["/home", "/root", "/.ssh", "/etc/ssh"],
            "ftp": ["/var/ftp", "/home/<USER>", "/etc/vsftpd"],
            "http": ["/var/www", "/htdocs", "/public_html"],
            "https": ["/var/www", "/htdocs", "/public_html"],
            "smtp": ["/var/mail", "/etc/postfix"],
            "mysql": ["/var/lib/mysql", "/etc/mysql"],
            "postgresql": ["/var/lib/postgresql", "/etc/postgresql"]
        }
        
        for service in services:
            service_lower = service.lower()
            if service_lower in service_paths:
                paths.extend(service_paths[service_lower])
        
        return list(set(paths))

    async def _generate_service_credentials(self, services: List[str]) -> List[str]:
        """Generate service-specific credentials"""
        credentials = []
        
        service_creds = {
            "mysql": ["root:password", "admin:admin", "mysql:mysql"],
            "postgresql": ["postgres:password", "admin:admin"],
            "ftp": ["ftp:ftp", "anonymous:anonymous", "admin:admin"],
            "ssh": ["root:password", "admin:admin", "user:user"],
            "rdp": ["administrator:password", "admin:admin"],
            "vnc": ["admin:admin", "vnc:vnc"]
        }
        
        for service in services:
            service_lower = service.lower()
            if service_lower in service_creds:
                credentials.extend(service_creds[service_lower])
        
        return list(set(credentials))

    async def _generate_ai_wordlists(self, target_info: Dict[str, Any]) -> Dict[str, List[str]]:
        """Generate AI-enhanced wordlists"""
        try:
            if not self.ai_service:
                return {}
            
            # Create analysis request for AI wordlist generation
            analysis_request = AnalysisRequest(
                scan_results=target_info,
                analysis_type="wordlist_generation",
                include_remediation=False,
                include_risk_scoring=False,
                custom_context={
                    "wordlist_categories": ["passwords", "directories", "parameters"],
                    "target_type": target_info.get("type", "web_application"),
                    "industry": target_info.get("industry", "general")
                }
            )
            
            response = await self.ai_service.analyze_vulnerabilities(analysis_request)
            
            if response.success:
                # Parse AI response for wordlist suggestions
                ai_wordlists = self._parse_ai_wordlist_response(response.executive_summary)
                return ai_wordlists
            
        except Exception as e:
            logger.error(f"AI wordlist generation failed: {e}")
        
        return {}

    def _parse_ai_wordlist_response(self, ai_response: str) -> Dict[str, List[str]]:
        """Parse AI response to extract wordlist suggestions"""
        wordlists = {}
        
        # Simple parsing for demonstration - in production would use more sophisticated NLP
        words = re.findall(r'\b[a-zA-Z][a-zA-Z0-9_-]*\b', ai_response.lower())
        
        # Categorize words
        password_words = [w for w in words if len(w) >= 4 and len(w) <= 20]
        directory_words = [w for w in words if len(w) >= 3 and len(w) <= 15]
        parameter_words = [w for w in words if len(w) >= 2 and len(w) <= 10]
        
        wordlists["ai_passwords"] = password_words[:100]
        wordlists["ai_directories"] = directory_words[:50]
        wordlists["ai_parameters"] = parameter_words[:30]
        
        return wordlists

    async def create_mutation_wordlists(self, base_words: List[str], ai_rules: Dict[str, Any]) -> List[str]:
        """Create mutation wordlists with AI-powered rules"""
        
        try:
            mutated_words = []
            
            # Apply standard mutation rules
            for word in base_words:
                mutations = self.mutation_engine.apply_mutations(word, list(MutationRule))
                mutated_words.extend(mutations)
            
            # Apply AI-suggested mutations
            if ai_rules and self.ai_service:
                ai_mutations = await self._apply_ai_mutations(base_words, ai_rules)
                mutated_words.extend(ai_mutations)
            
            # Remove duplicates and rank
            unique_mutations = list(set(mutated_words))
            return self._rank_by_likelihood(unique_mutations, "mutation")
            
        except Exception as e:
            logger.error(f"Mutation wordlist creation failed: {e}")
            return base_words

    async def _apply_ai_mutations(self, base_words: List[str], ai_rules: Dict[str, Any]) -> List[str]:
        """Apply AI-suggested mutations to base words"""
        mutations = []
        
        # AI-suggested patterns from rules
        patterns = ai_rules.get("patterns", [])
        transformations = ai_rules.get("transformations", [])
        
        for word in base_words[:10]:  # Limit for API efficiency
            # Apply patterns
            for pattern in patterns[:5]:
                if "{word}" in pattern:
                    mutations.append(pattern.replace("{word}", word))
            
            # Apply transformations
            for transform in transformations[:3]:
                if transform == "reverse":
                    mutations.append(word[::-1])
                elif transform == "double":
                    mutations.append(word + word)
                elif transform == "capitalize_random":
                    mutations.append(self._random_capitalize(word))
        
        return mutations

    def _random_capitalize(self, word: str) -> str:
        """Randomly capitalize letters in word"""
        return ''.join(c.upper() if random.random() > 0.5 else c.lower() for c in word)

    async def build_social_engineering_wordlists(self, osint_data: OSINTData) -> Dict[str, List[str]]:
        """Build wordlists from social engineering and OSINT data"""
        
        wordlists = {}
        
        try:
            # Employee-based wordlists
            if osint_data.employees:
                wordlists["employee_usernames"] = self._generate_employee_usernames(osint_data.employees)
                wordlists["employee_passwords"] = self._generate_employee_passwords(osint_data.employees)
            
            # Company-based wordlists
            company_words = self._extract_company_keywords(osint_data)
            wordlists["company_keywords"] = company_words
            
            # Technology-based wordlists
            if osint_data.technologies:
                wordlists["tech_passwords"] = await self._generate_technology_passwords(osint_data.technologies)
            
            # Social media wordlists
            if osint_data.social_media:
                wordlists["social_keywords"] = self._extract_social_keywords(osint_data.social_media)
            
            # Breach data wordlists
            if osint_data.breach_data:
                wordlists["breach_passwords"] = self._process_breach_data(osint_data.breach_data)
            
            return wordlists
            
        except Exception as e:
            logger.error(f"Social engineering wordlist creation failed: {e}")
            return {}

    def _generate_employee_usernames(self, employees: List[str]) -> List[str]:
        """Generate usernames from employee names"""
        usernames = []
        
        for employee in employees:
            name_parts = employee.lower().split()
            if len(name_parts) >= 2:
                first, last = name_parts[0], name_parts[-1]
                
                # Common username patterns
                patterns = [
                    first,
                    last,
                    f"{first}{last}",
                    f"{first}.{last}",
                    f"{first}_{last}",
                    f"{first[0]}{last}",
                    f"{first}{last[0]}",
                    f"{last}{first}",
                    f"{last}.{first}",
                    f"{last}_{first}"
                ]
                
                usernames.extend(patterns)
        
        return list(set(usernames))

    def _generate_employee_passwords(self, employees: List[str]) -> List[str]:
        """Generate passwords from employee names"""
        passwords = []
        
        for employee in employees:
            name_parts = employee.split()
            if name_parts:
                base_name = name_parts[0].lower()
                
                # Apply password mutations
                mutations = self.mutation_engine.apply_mutations(base_name, [
                    MutationRule.APPEND_NUMBERS,
                    MutationRule.CASE_VARIATIONS,
                    MutationRule.YEARS
                ])
                passwords.extend(mutations)
        
        return list(set(passwords))

    def _extract_company_keywords(self, osint_data: OSINTData) -> List[str]:
        """Extract keywords from company OSINT data"""
        keywords = []
        
        # Add company name variations
        if osint_data.company_name:
            company_variations = self.mutation_engine.generate_company_variations(osint_data.company_name)
            keywords.extend(company_variations)
        
        # Add domain keywords
        if osint_data.domain:
            domain_parts = osint_data.domain.split('.')
            keywords.extend(domain_parts)
        
        # Add technology keywords
        keywords.extend(osint_data.technologies)
        
        # Add custom keywords
        keywords.extend(osint_data.keywords)
        
        return list(set(keywords))

    def _extract_social_keywords(self, social_media: Dict[str, List[str]]) -> List[str]:
        """Extract keywords from social media data"""
        keywords = []
        
        for platform, posts in social_media.items():
            for post in posts:
                # Extract hashtags
                hashtags = re.findall(r'#(\w+)', post)
                keywords.extend(hashtags)
                
                # Extract mentions
                mentions = re.findall(r'@(\w+)', post)
                keywords.extend(mentions)
                
                # Extract common words
                words = re.findall(r'\b[a-zA-Z]{4,}\b', post.lower())
                keywords.extend(words)
        
        return list(set(keywords))

    def _process_breach_data(self, breach_data: List[str]) -> List[str]:
        """Process breach data for password patterns"""
        passwords = []
        
        for breach_entry in breach_data:
            # Extract password if format is email:password
            if ':' in breach_entry:
                parts = breach_entry.split(':')
                if len(parts) >= 2:
                    password = parts[1].strip()
                    if len(password) >= 4:
                        passwords.append(password)
        
        # Analyze patterns and generate similar passwords
        pattern_passwords = self._analyze_password_patterns(passwords)
        passwords.extend(pattern_passwords)
        
        return list(set(passwords))

    def _analyze_password_patterns(self, passwords: List[str]) -> List[str]:
        """Analyze password patterns and generate similar ones"""
        pattern_passwords = []
        
        # Common patterns found in breaches
        for password in passwords[:100]:  # Limit for performance
            # Number at end pattern
            if password[-1].isdigit():
                base = password[:-1]
                for i in range(10):
                    pattern_passwords.append(f"{base}{i}")
            
            # Year pattern
            year_match = re.search(r'(19|20)\d{2}', password)
            if year_match:
                base = password.replace(year_match.group(), "")
                for year in range(2015, 2026):
                    pattern_passwords.append(f"{base}{year}")
        
        return pattern_passwords

    async def _generate_technology_passwords(self, technologies: List[str]) -> List[str]:
        """Generate technology-specific passwords"""
        passwords = []
        
        for tech in technologies:
            tech_mutations = self.mutation_engine.apply_mutations(tech.lower(), [
                MutationRule.APPEND_NUMBERS,
                MutationRule.CASE_VARIATIONS,
                MutationRule.YEARS,
                MutationRule.COMMON_SUFFIXES
            ])
            passwords.extend(tech_mutations)
        
        return list(set(passwords))

    async def optimize_wordlists_from_results(self, scan_results: Dict[str, Any]) -> Dict[str, List[str]]:
        """Optimize wordlists based on scan success rates"""
        
        try:
            optimized_wordlists = {}
            
            # Get success data from scan results
            successful_words = scan_results.get("successful_words", [])
            failed_words = scan_results.get("failed_words", [])
            tool_used = scan_results.get("tool", "unknown")
            
            # Update success tracking
            await self.success_tracker.update_success_rates(successful_words, failed_words, tool_used)
            
            # Get optimized wordlists based on success rates
            if successful_words:
                # Generate similar words to successful ones
                optimized_words = []
                for word in successful_words:
                    similar_words = self.mutation_engine.generate_similar_words(word)
                    optimized_words.extend(similar_words)
                
                optimized_wordlists["optimized_based_on_success"] = list(set(optimized_words))
            
            # Get historically successful words
            historical_success = await self.success_tracker.get_top_successful_words(tool_used, limit=1000)
            if historical_success:
                optimized_wordlists["historical_success"] = historical_success
            
            return optimized_wordlists
            
        except Exception as e:
            logger.error(f"Wordlist optimization failed: {e}")
            return {}

    def _rank_by_likelihood(self, words: List[str], category: str) -> List[str]:
        """Rank words by likelihood of success"""
        
        # Scoring criteria
        scored_words = []
        
        for word in words:
            score = 0
            
            # Length scoring
            if category == "password":
                if 6 <= len(word) <= 12:
                    score += 10
                elif 4 <= len(word) <= 15:
                    score += 5
            elif category == "directory":
                if 3 <= len(word) <= 10:
                    score += 10
            
            # Common patterns
            if category == "password":
                if any(char.isdigit() for char in word):
                    score += 5
                if any(char.isupper() for char in word):
                    score += 3
                if word.endswith(('123', '1', '01')):
                    score += 8
            
            # Frequency in common wordlists (simplified)
            common_words = ["admin", "password", "test", "user", "login", "root"]
            if word.lower() in common_words:
                score += 15
            
            scored_words.append((word, score))
        
        # Sort by score descending
        scored_words.sort(key=lambda x: x[1], reverse=True)
        
        return [word for word, score in scored_words]

    async def _store_wordlist(self, category: str, wordlist: List[str], target_info: Dict[str, Any]):
        """Store generated wordlist for future use"""
        try:
            # Create wordlist file
            wordlist_id = f"{category}_{hashlib.md5(str(target_info).encode()).hexdigest()[:8]}"
            wordlist_file = self.wordlist_storage / f"{wordlist_id}.txt"
            
            # Write wordlist to file
            with open(wordlist_file, 'w') as f:
                for word in wordlist:
                    f.write(f"{word}\n")
            
            # Store metadata
            metadata = WordlistMetadata(
                id=wordlist_id,
                name=f"{category.title()} Wordlist",
                category=WordlistCategory.CUSTOM,
                source="ai_generated",
                target_context=target_info,
                generation_method="context_aware",
                word_count=len(wordlist),
                unique_words=len(set(wordlist)),
                ai_generated=True,
                success_rate=0.0,
                last_used=datetime.now(),
                created_at=datetime.now()
            )
            
            # Store in database (implementation would depend on database schema)
            logger.info(f"Stored wordlist {wordlist_id} with {len(wordlist)} words")
            
        except Exception as e:
            logger.error(f"Failed to store wordlist: {e}")

    async def download_wordlist_source(self, source: WordlistSource) -> bool:
        """Download wordlist from external source"""
        try:
            if not source.url:
                return False
            
            async with aiohttp.ClientSession() as session:
                async with session.get(source.url) as response:
                    if response.status == 200:
                        content = await response.text()
                        
                        # Save to local file
                        source_file = self.wordlist_storage / f"{source.name.lower().replace(' ', '_')}.txt"
                        with open(source_file, 'w') as f:
                            f.write(content)
                        
                        # Update checksum
                        source.checksum = hashlib.md5(content.encode()).hexdigest()
                        source.last_updated = datetime.now()
                        
                        logger.info(f"Downloaded wordlist source: {source.name}")
                        return True
            
        except Exception as e:
            logger.error(f"Failed to download wordlist source {source.name}: {e}")
        
        return False

    def get_available_wordlists(self) -> List[WordlistMetadata]:
        """Get list of available wordlists"""
        wordlists = []
        
        try:
            # Get built-in wordlists
            for source in self.built_in_sources:
                if source.active:
                    metadata = WordlistMetadata(
                        id=source.name.lower().replace(' ', '_'),
                        name=source.name,
                        category=source.category,
                        source="built_in",
                        target_context={},
                        generation_method="external",
                        word_count=source.size,
                        unique_words=source.size,
                        ai_generated=False,
                        success_rate=0.0,
                        last_used=datetime.now(),
                        created_at=source.last_updated
                    )
                    wordlists.append(metadata)
            
            # Get custom wordlists
            for wordlist_file in self.wordlist_storage.glob("*.txt"):
                try:
                    with open(wordlist_file, 'r') as f:
                        word_count = sum(1 for line in f)
                    
                    metadata = WordlistMetadata(
                        id=wordlist_file.stem,
                        name=wordlist_file.stem.replace('_', ' ').title(),
                        category=WordlistCategory.CUSTOM,
                        source="local",
                        target_context={},
                        generation_method="custom",
                        word_count=word_count,
                        unique_words=word_count,
                        ai_generated=False,
                        success_rate=0.0,
                        last_used=datetime.now(),
                        created_at=datetime.fromtimestamp(wordlist_file.stat().st_mtime)
                    )
                    wordlists.append(metadata)
                except:
                    continue
        
        except Exception as e:
            logger.error(f"Failed to get available wordlists: {e}")
        
        return wordlists

    async def load_wordlist(self, wordlist_id: str) -> List[str]:
        """Load wordlist by ID"""
        try:
            # Check cache first
            if wordlist_id in self.wordlist_cache:
                return self.wordlist_cache[wordlist_id]
            
            # Try to load from file
            wordlist_file = self.wordlist_storage / f"{wordlist_id}.txt"
            if wordlist_file.exists():
                with open(wordlist_file, 'r') as f:
                    words = [line.strip() for line in f if line.strip()]
                
                # Cache wordlist
                self.wordlist_cache[wordlist_id] = words
                return words
            
            # Try built-in sources
            for source in self.built_in_sources:
                if source.name.lower().replace(' ', '_') == wordlist_id:
                    if await self.download_wordlist_source(source):
                        return await self.load_wordlist(wordlist_id)
            
        except Exception as e:
            logger.error(f"Failed to load wordlist {wordlist_id}: {e}")
        
        return []


class MutationEngine:
    """Engine for applying password and wordlist mutations"""
    
    def apply_mutations(self, word: str, rules: List[MutationRule]) -> List[str]:
        """Apply mutation rules to a word"""
        mutations = [word]  # Include original
        
        for rule in rules:
            new_mutations = []
            
            if rule == MutationRule.APPEND_NUMBERS:
                new_mutations.extend([f"{word}{i}" for i in range(10)])
                new_mutations.extend([f"{word}{i:02d}" for i in range(1, 13)])
                new_mutations.extend([f"{word}{year}" for year in range(2015, 2026)])
            
            elif rule == MutationRule.PREPEND_NUMBERS:
                new_mutations.extend([f"{i}{word}" for i in range(10)])
                new_mutations.extend([f"{year}{word}" for year in range(2015, 2026)])
            
            elif rule == MutationRule.CASE_VARIATIONS:
                new_mutations.extend([
                    word.upper(),
                    word.lower(),
                    word.capitalize(),
                    word.swapcase()
                ])
            
            elif rule == MutationRule.LEET_SPEAK:
                leet_map = {'a': '4', 'e': '3', 'i': '1', 'o': '0', 's': '5', 't': '7'}
                leet_word = word.lower()
                for char, replacement in leet_map.items():
                    leet_word = leet_word.replace(char, replacement)
                new_mutations.append(leet_word)
            
            elif rule == MutationRule.YEARS:
                for year in range(2015, 2026):
                    new_mutations.extend([
                        f"{word}{year}",
                        f"{word}_{year}",
                        f"{word}-{year}"
                    ])
            
            elif rule == MutationRule.COMMON_SUFFIXES:
                suffixes = ['!', '@', '#', '$', '123', '12', '1', '01', '001']
                new_mutations.extend([f"{word}{suffix}" for suffix in suffixes])
            
            elif rule == MutationRule.KEYBOARD_PATTERNS:
                patterns = ['123', 'qwe', 'asd', 'zxc', '!@#', 'qwerty']
                new_mutations.extend([f"{word}{pattern}" for pattern in patterns])
            
            elif rule == MutationRule.COMPANY_VARIATIONS:
                new_mutations.extend([
                    f"{word}corp",
                    f"{word}inc",
                    f"{word}ltd",
                    f"{word}llc",
                    f"{word}co"
                ])
            
            mutations.extend(new_mutations)
        
        return list(set(mutations))  # Remove duplicates
    
    def generate_company_variations(self, company: str) -> List[str]:
        """Generate company name variations"""
        variations = []
        
        # Basic variations
        variations.extend([
            company.lower(),
            company.upper(),
            company.capitalize(),
            re.sub(r'[^a-zA-Z0-9]', '', company.lower())
        ])
        
        # Business suffixes
        suffixes = ['corp', 'inc', 'ltd', 'llc', 'co', 'group', 'holdings']
        for suffix in suffixes:
            variations.extend([
                f"{company.lower()}{suffix}",
                f"{company.lower()}_{suffix}",
                f"{company.lower()}-{suffix}"
            ])
        
        return list(set(variations))
    
    def generate_similar_words(self, word: str) -> List[str]:
        """Generate words similar to the input word"""
        similar = []
        
        # Character substitutions
        substitutions = {'a': ['@', '4'], 'e': ['3'], 'i': ['1', '!'], 'o': ['0'], 's': ['5', '$']}
        
        for char, replacements in substitutions.items():
            for replacement in replacements:
                if char in word.lower():
                    similar.append(word.lower().replace(char, replacement))
        
        # Append/prepend common elements
        common_elements = ['1', '123', '12', '01', '2024', '2025']
        for element in common_elements:
            similar.extend([f"{word}{element}", f"{element}{word}"])
        
        return list(set(similar))


class OSINTCollector:
    """OSINT data collection for social engineering wordlists"""
    
    async def collect_company_data(self, company: str, domain: str) -> OSINTData:
        """Collect OSINT data for a company"""
        
        # Placeholder implementation - in production would integrate with real OSINT APIs
        osint_data = OSINTData(
            company_name=company,
            domain=domain,
            employees=[],
            technologies=[],
            social_media={},
            public_documents=[],
            breach_data=[],
            keywords=[]
        )
        
        # Simulate data collection
        if domain:
            # Would use real APIs like Hunter.io, Clearbit, etc.
            osint_data.employees = [
                "John Smith", "Jane Doe", "Mike Johnson", "Sarah Wilson"
            ]
            osint_data.technologies = [
                "WordPress", "Apache", "MySQL", "PHP", "JavaScript"
            ]
            osint_data.social_media = {
                "twitter": ["Great product launch today! #innovation"],
                "linkedin": ["Hiring software engineers in our NYC office"]
            }
        
        return osint_data


class WordlistSuccessTracker:
    """Track success rates of wordlist entries"""
    
    def __init__(self, database: DatabaseManager):
        self.database = database
    
    async def update_success_rates(self, successful_words: List[str], 
                                 failed_words: List[str], tool: str):
        """Update success rates based on scan results"""
        try:
            # Implementation would update database with success/failure rates
            logger.info(f"Updated success rates: {len(successful_words)} successful, {len(failed_words)} failed for {tool}")
        except Exception as e:
            logger.error(f"Failed to update success rates: {e}")
    
    async def get_top_successful_words(self, tool: str, limit: int = 1000) -> List[str]:
        """Get historically successful words for a tool"""
        try:
            # Implementation would query database for top successful words
            # Placeholder return
            return ["admin", "password", "test", "user", "login", "root"][:limit]
        except Exception as e:
            logger.error(f"Failed to get successful words: {e}")
            return []