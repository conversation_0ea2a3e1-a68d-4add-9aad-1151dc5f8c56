/**
 * Web Testing Page - Web application security testing tools
 */
import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { 
  Globe,
  Search,
  Database,
  Code,
  AlertTriangle,
  CheckCircle,
  Play,
  Settings
} from 'lucide-react';

export function WebTestingPage() {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Web Application Testing</h1>
          <p className="text-muted-foreground">Web security testing and analysis tools</p>
        </div>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <div className="flex items-center gap-3">
              <Database className="h-5 w-5" />
              <CardTitle>SQL Injection Testing</CardTitle>
            </div>
            <CardDescription>Automated SQL injection detection and exploitation</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <Input placeholder="Target URL" />
              <div className="flex gap-2">
                <Button><Play className="mr-2 h-4 w-4" />Test SQLi</Button>
                <Button variant="outline"><Settings className="mr-2 h-4 w-4" />Configure</Button>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <div className="flex items-center gap-3">
              <Search className="h-5 w-5" />
              <CardTitle>Directory Discovery</CardTitle>
            </div>
            <CardDescription>Find hidden files and directories</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <Input placeholder="Website URL" />
              <div className="flex gap-2">
                <Button><Play className="mr-2 h-4 w-4" />Discover</Button>
                <Button variant="outline"><Settings className="mr-2 h-4 w-4" />Configure</Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}