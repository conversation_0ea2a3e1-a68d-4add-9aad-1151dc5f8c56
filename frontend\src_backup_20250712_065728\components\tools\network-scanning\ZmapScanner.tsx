/**
 * Zmap Scanner Component
 * Internet-wide network scanner for research and large-scale security assessment
 */
import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { 
  Globe,
  Play,
  Square,
  Download,
  Settings,
  Target,
  MapPin,
  BarChart3,
  Clock,
  Terminal,
  FileText,
  AlertTriangle,
  CheckCircle,
  Copy,
  RotateCcw,
  Shield,
  Wifi,
  Database
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useBackendStore } from '@/stores/backend-store';
import { apiClient, ToolExecution } from '@/services/api-client';
import { toast } from 'sonner';

/**
 * Zmap configuration interface
 */
interface ZmapConfig {
  port: number;
  targetSubnet: string;
  bandwidth: string;
  sourcePort: string;
  sourceIP: string;
  interface: string;
  probeModule: 'tcp_synscan' | 'icmp_echoscan' | 'udp' | 'tcp_ack' | 'dns';
  outputFields: string[];
  outputFilter: string;
  blacklistFile: string;
  whitelistFile: string;
  maxTargets: number;
  maxRuntime: number;
  maxResults: number;
  cooldownTime: number;
  seed: string;
  verbosity: number;
  dryRun: boolean;
  summaryStats: boolean;
  metadataFile: string;
  userAgent: string;
  customArgs: string;
}

/**
 * Probe module configurations
 */
const PROBE_MODULES = {
  'tcp_synscan': {
    name: 'TCP SYN Scan',
    description: 'Standard TCP SYN port scanning',
    defaultPort: 80,
    fields: ['saddr', 'daddr', 'sport', 'dport', 'seqnum', 'acknum', 'window']
  },
  'icmp_echoscan': {
    name: 'ICMP Echo Scan',
    description: 'ICMP ping sweep for host discovery',
    defaultPort: 0,
    fields: ['saddr', 'daddr', 'type', 'code', 'icmp-id', 'seq']
  },
  'udp': {
    name: 'UDP Scan',
    description: 'UDP port scanning',
    defaultPort: 53,
    fields: ['saddr', 'daddr', 'sport', 'dport', 'udp-plen', 'data']
  },
  'tcp_ack': {
    name: 'TCP ACK Scan',
    description: 'TCP ACK scanning for firewall detection',
    defaultPort: 80,
    fields: ['saddr', 'daddr', 'sport', 'dport', 'seqnum', 'acknum', 'window']
  },
  'dns': {
    name: 'DNS Scan',
    description: 'DNS resolver scanning',
    defaultPort: 53,
    fields: ['saddr', 'daddr', 'sport', 'dport', 'dns-qname', 'dns-qtype', 'dns-rcode']
  }
};

/**
 * Common output fields
 */
const OUTPUT_FIELDS = [
  'saddr', 'daddr', 'sport', 'dport', 'seqnum', 'acknum', 'window',
  'classification', 'success', 'repeat', 'cooldown', 'timestamp-str',
  'timestamp-ts', 'timestamp-us'
];

/**
 * Bandwidth presets
 */
const BANDWIDTH_PRESETS = {
  'conservative': { value: '1M', label: '1 Mbps', description: 'Safe for most networks' },
  'moderate': { value: '10M', label: '10 Mbps', description: 'Moderate scanning speed' },
  'aggressive': { value: '100M', label: '100 Mbps', description: 'Fast scanning' },
  'maximum': { value: '1G', label: '1 Gbps', description: 'Maximum bandwidth' },
  'research': { value: '10G', label: '10 Gbps', description: 'Research-grade scanning' }
};

/**
 * Target size presets for different scanning scenarios
 */
const TARGET_PRESETS = {
  'single': { subnet: '', max: 1, description: 'Single host' },
  'local': { subnet: '***********/24', max: 256, description: 'Local subnet' },
  'enterprise': { subnet: '10.0.0.0/16', max: 65536, description: 'Enterprise network' },
  'research': { subnet: '0.0.0.0/0', max: 1000000, description: 'Internet research (1M hosts)' },
  'survey': { subnet: '0.0.0.0/0', max: 10000000, description: 'Internet survey (10M hosts)' }
};

/**
 * Zmap Scanner Component
 */
export function ZmapScanner() {
  // State management
  const [config, setConfig] = React.useState<ZmapConfig>({
    port: 80,
    targetSubnet: '',
    bandwidth: '10M',
    sourcePort: '',
    sourceIP: '',
    interface: '',
    probeModule: 'tcp_synscan',
    outputFields: ['saddr', 'daddr', 'sport', 'dport', 'classification'],
    outputFilter: '',
    blacklistFile: '',
    whitelistFile: '',
    maxTargets: 1000,
    maxRuntime: 3600,
    maxResults: 1000000,
    cooldownTime: 8,
    seed: '',
    verbosity: 1,
    dryRun: false,
    summaryStats: true,
    metadataFile: '',
    userAgent: '',
    customArgs: ''
  });
  
  const [isScanning, setIsScanning] = React.useState(false);
  const [currentExecution, setCurrentExecution] = React.useState<ToolExecution | null>(null);
  const [progress, setProgress] = React.useState(0);
  const [output, setOutput] = React.useState<string[]>([]);
  const [results, setResults] = React.useState<any>(null);
  const [activeTab, setActiveTab] = React.useState('configure');
  const [scanStats, setScanStats] = React.useState({
    estimatedHosts: 0,
    estimatedTime: 0,
    packetsPerSecond: 0,
    totalPackets: 0
  });
  
  // Backend connection
  const { status: backendStatus } = useBackendStore();
  
  // Calculate scan statistics
  React.useEffect(() => {
    const calculateStats = () => {
      let estimatedHosts = config.maxTargets;
      
      // Calculate based on subnet if provided
      if (config.targetSubnet.includes('/')) {
        const cidr = parseInt(config.targetSubnet.split('/')[1]);
        if (cidr) {
          const totalHosts = Math.pow(2, 32 - cidr);
          estimatedHosts = Math.min(totalHosts, config.maxTargets);
        }
      }
      
      // Calculate packets per second based on bandwidth
      const bandwidthBps = parseBandwidth(config.bandwidth);
      const packetsPerSecond = bandwidthBps / (64 * 8); // Assuming 64-byte packets
      
      const totalPackets = estimatedHosts;
      const estimatedTime = totalPackets / packetsPerSecond;
      
      setScanStats({
        estimatedHosts,
        estimatedTime,
        packetsPerSecond,
        totalPackets
      });
    };
    
    calculateStats();
  }, [config.targetSubnet, config.maxTargets, config.bandwidth]);
  
  // Parse bandwidth string to bits per second
  const parseBandwidth = (bandwidth: string): number => {
    const value = parseFloat(bandwidth);
    const unit = bandwidth.replace(/[0-9.]/g, '').toLowerCase();
    
    switch (unit) {
      case 'k': case 'kbps': return value * 1000;
      case 'm': case 'mbps': return value * 1000000;
      case 'g': case 'gbps': return value * 1000000000;
      default: return value;
    }
  };
  
  // Update configuration
  const updateConfig = (updates: Partial<ZmapConfig>) => {
    setConfig(prev => ({ ...prev, ...updates }));
  };
  
  // Apply target preset
  const applyTargetPreset = (presetName: string) => {
    const preset = TARGET_PRESETS[presetName as keyof typeof TARGET_PRESETS];
    if (preset) {
      updateConfig({ 
        targetSubnet: preset.subnet,
        maxTargets: preset.max
      });
      toast.success(`Applied ${presetName} target preset`);
    }
  };
  
  // Apply bandwidth preset
  const applyBandwidthPreset = (presetName: string) => {
    const preset = BANDWIDTH_PRESETS[presetName as keyof typeof BANDWIDTH_PRESETS];
    if (preset) {
      updateConfig({ bandwidth: preset.value });
      toast.success(`Applied ${preset.label} bandwidth`);
    }
  };
  
  // Update probe module and set appropriate defaults
  const updateProbeModule = (module: string) => {
    const moduleConfig = PROBE_MODULES[module as keyof typeof PROBE_MODULES];
    if (moduleConfig) {
      updateConfig({
        probeModule: module as any,
        port: moduleConfig.defaultPort,
        outputFields: moduleConfig.fields.slice(0, 5) // First 5 fields by default
      });
    }
  };
  
  // Generate Zmap command
  const generateCommand = (): string => {
    let command = 'zmap';
    
    // Port (required for most probe modules)
    if (config.port > 0) {
      command += ` -p ${config.port}`;
    }
    
    // Probe module
    command += ` -M ${config.probeModule}`;
    
    // Target subnet
    if (config.targetSubnet.trim()) {
      command += ` ${config.targetSubnet}`;
    }
    
    // Bandwidth
    if (config.bandwidth.trim()) {
      command += ` -B ${config.bandwidth}`;
    }
    
    // Source configuration
    if (config.sourcePort.trim()) {
      command += ` -s ${config.sourcePort}`;
    }
    
    if (config.sourceIP.trim()) {
      command += ` -S ${config.sourceIP}`;
    }
    
    if (config.interface.trim()) {
      command += ` -i ${config.interface}`;
    }
    
    // Output fields
    if (config.outputFields.length > 0) {
      command += ` -f \"${config.outputFields.join(',')}\"`;
    }
    
    // Output filter
    if (config.outputFilter.trim()) {
      command += ` --output-filter=\"${config.outputFilter}\"`;
    }
    
    // List files
    if (config.blacklistFile.trim()) {
      command += ` -b ${config.blacklistFile}`;
    }
    
    if (config.whitelistFile.trim()) {
      command += ` -w ${config.whitelistFile}`;
    }
    
    // Limits
    if (config.maxTargets > 0) {
      command += ` -n ${config.maxTargets}`;
    }
    
    if (config.maxRuntime > 0) {
      command += ` --max-runtime=${config.maxRuntime}`;
    }
    
    if (config.maxResults > 0) {
      command += ` --max-results=${config.maxResults}`;
    }
    
    // Timing
    if (config.cooldownTime !== 8) {
      command += ` -c ${config.cooldownTime}`;
    }
    
    // Misc options
    if (config.seed.trim()) {
      command += ` --seed=${config.seed}`;
    }
    
    if (config.verbosity !== 1) {
      command += ` -v ${config.verbosity}`;
    }
    
    if (config.dryRun) {
      command += ' --dryrun';
    }
    
    if (!config.summaryStats) {
      command += ' --disable-syslog';
    }
    
    if (config.metadataFile.trim()) {
      command += ` --metadata-file=${config.metadataFile}`;
    }
    
    if (config.userAgent.trim()) {
      command += ` --user-agent=\"${config.userAgent}\"`;
    }
    
    // Custom arguments
    if (config.customArgs.trim()) {
      command += ` ${config.customArgs.trim()}`;
    }
    
    return command;
  };
  
  // Format duration
  const formatDuration = (seconds: number): string => {
    if (seconds < 60) return `${Math.round(seconds)}s`;
    if (seconds < 3600) return `${Math.round(seconds / 60)}m`;
    if (seconds < 86400) return `${Math.round(seconds / 3600)}h`;
    return `${Math.round(seconds / 86400)}d`;
  };
  
  // Format number with units
  const formatNumber = (num: number): string => {
    if (num >= 1000000000) return `${(num / 1000000000).toFixed(1)}B`;
    if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`;
    if (num >= 1000) return `${(num / 1000).toFixed(1)}K`;
    return num.toString();
  };
  
  // Start scan
  const startScan = async () => {
    if (!config.targetSubnet.trim() && !config.whitelistFile.trim()) {
      toast.error('Please specify targets or whitelist file');
      return;
    }
    
    if (config.port <= 0 && config.probeModule !== 'icmp_echoscan') {
      toast.error('Please specify a valid port');
      return;
    }
    
    if (!backendStatus.connected) {
      toast.error('Backend not connected');
      return;
    }
    
    // Warning for large scans
    if (config.maxTargets > 100000) {
      const confirmed = window.confirm(
        `You are about to scan ${formatNumber(config.maxTargets)} hosts. This is a large-scale scan that may:\n\n` +
        `• Take ${formatDuration(scanStats.estimatedTime)} to complete\n` +
        `• Generate significant network traffic\n` +
        `• Trigger security monitoring systems\n\n` +
        `Are you sure you want to proceed?`
      );
      
      if (!confirmed) return;
    }
    
    try {
      setIsScanning(true);
      setProgress(0);
      setOutput([]);
      setResults(null);
      setActiveTab('output');
      
      const execution = await apiClient.executeTool('zmap', config, (progressUpdate) => {
        setCurrentExecution(progressUpdate);
        setProgress(progressUpdate.progress);
        if (progressUpdate.output) {
          setOutput(prev => [...prev, ...progressUpdate.output!]);
        }
        
        if (progressUpdate.status === 'completed') {
          setIsScanning(false);
          setResults(progressUpdate.results);
          toast.success('Zmap scan completed');
        } else if (progressUpdate.status === 'failed') {
          setIsScanning(false);
          toast.error(`Scan failed: ${progressUpdate.error}`);
        }
      });
      
      setCurrentExecution(execution);
      toast.info('Zmap scan started');
    } catch (error) {
      setIsScanning(false);
      console.error('Failed to start Zmap scan:', error);
      toast.error('Failed to start scan');
    }
  };
  
  // Stop scan
  const stopScan = async () => {
    if (currentExecution) {
      try {
        await apiClient.cancelExecution(currentExecution.id);
        setIsScanning(false);
        setCurrentExecution(null);
        toast.info('Scan cancelled');
      } catch (error) {
        console.error('Failed to stop scan:', error);
        toast.error('Failed to stop scan');
      }
    }
  };
  
  // Copy command to clipboard
  const copyCommand = async () => {
    try {
      await navigator.clipboard.writeText(generateCommand());
      toast.success('Command copied to clipboard');
    } catch (error) {
      toast.error('Failed to copy command');
    }
  };
  
  // Export results
  const exportResults = () => {
    if (!results && output.length === 0) {
      toast.error('No results to export');
      return;
    }
    
    const data = results || output.join('\n');
    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `zmap-scan-${new Date().toISOString().slice(0, 19)}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    
    toast.success('Results exported');
  };
  
  return (
    <div className="container-desktop py-6 space-y-6">
      {/* Tool Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className="p-2 bg-purple-100 dark:bg-purple-900/20 rounded-lg">
            <Globe className="h-6 w-6 text-purple-600 dark:text-purple-400" />
          </div>
          <div>
            <h1 className="text-2xl font-bold">Zmap Scanner</h1>
            <p className="text-muted-foreground">
              Internet-wide network scanner for research and large-scale security assessment
            </p>
          </div>
        </div>
        
        {/* Status indicators */}
        <div className="flex items-center gap-2">
          <Badge variant={backendStatus.connected ? 'default' : 'destructive'}>
            {backendStatus.connected ? 'Connected' : 'Offline'}
          </Badge>
          {isScanning && (
            <Badge variant="secondary" className="animate-pulse">
              Scanning {formatNumber(config.maxTargets)} hosts
            </Badge>
          )}
        </div>
      </div>
      
      {/* Research Warning */}
      <Card className="border-yellow-200 dark:border-yellow-800 bg-yellow-50/50 dark:bg-yellow-900/10">
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 text-yellow-600 dark:text-yellow-400">
            <Shield className="h-5 w-5" />
            Research Tool Notice
          </CardTitle>
        </CardHeader>
        <CardContent className="text-sm space-y-2">
          <p>Zmap is designed for Internet-wide research and large-scale security studies. Please ensure:</p>
          <ul className="list-disc list-inside space-y-1 text-muted-foreground">
            <li>You have permission to scan the target networks</li>
            <li>Your scans comply with your organization's policies and local laws</li>
            <li>You use appropriate rate limiting to avoid network disruption</li>
            <li>You consider the ethical implications of large-scale scanning</li>
          </ul>
        </CardContent>
      </Card>
      
      {/* Scan Statistics */}
      {config.targetSubnet && (
        <Card className="border-purple-200 dark:border-purple-800">
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-purple-600 dark:text-purple-400">
              <BarChart3 className="h-5 w-5" />
              Scan Projection
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-600 dark:text-purple-400">
                  {formatNumber(scanStats.estimatedHosts)}
                </div>
                <div className="text-xs text-muted-foreground">Target Hosts</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-600 dark:text-purple-400">
                  {formatNumber(scanStats.packetsPerSecond)}
                </div>
                <div className="text-xs text-muted-foreground">Packets/sec</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-600 dark:text-purple-400">
                  {formatDuration(scanStats.estimatedTime)}
                </div>
                <div className="text-xs text-muted-foreground">Est. Duration</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-600 dark:text-purple-400">
                  {config.bandwidth}
                </div>
                <div className="text-xs text-muted-foreground">Bandwidth</div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
      
      {/* Main Interface */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="configure" className="flex items-center gap-2">
            <Settings className="h-4 w-4" />
            Configure
          </TabsTrigger>
          <TabsTrigger value="output" className="flex items-center gap-2">
            <Terminal className="h-4 w-4" />
            Output
          </TabsTrigger>
          <TabsTrigger value="results" className="flex items-center gap-2">
            <FileText className="h-4 w-4" />
            Results
          </TabsTrigger>
          <TabsTrigger value="command" className="flex items-center gap-2">
            <Target className="h-4 w-4" />
            Command
          </TabsTrigger>
        </TabsList>
        
        {/* Configuration Tab */}
        <TabsContent value="configure" className="space-y-6">
          <div className="grid gap-6 lg:grid-cols-2">
            {/* Target Configuration */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Target className="h-5 w-5" />
                  Target Configuration
                </CardTitle>
                <CardDescription>
                  Specify scan targets and basic parameters
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Target Presets */}
                <div className="space-y-2">
                  <label className="text-sm font-medium">Target Presets</label>
                  <div className="grid grid-cols-2 gap-2">
                    {Object.entries(TARGET_PRESETS).map(([name, preset]) => (
                      <Button
                        key={name}
                        variant="outline"
                        size="sm"
                        onClick={() => applyTargetPreset(name)}
                        className="h-auto p-3 flex flex-col items-start"
                      >
                        <span className="font-medium capitalize">{name}</span>
                        <span className="text-xs text-muted-foreground">{preset.description}</span>
                      </Button>
                    ))}
                  </div>
                </div>
                
                {/* Target Subnet */}
                <div className="space-y-2">
                  <label className="text-sm font-medium">Target Subnet</label>
                  <Input
                    placeholder="e.g., ***********/24, 0.0.0.0/0"
                    value={config.targetSubnet}
                    onChange={(e) => updateConfig({ targetSubnet: e.target.value })}
                  />
                  <p className="text-xs text-muted-foreground">
                    CIDR notation subnet to scan (0.0.0.0/0 for Internet-wide)
                  </p>
                </div>
                
                {/* Port and Probe Module */}
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Port</label>
                    <Input
                      type="number"
                      placeholder="80"
                      value={config.port}
                      onChange={(e) => updateConfig({ port: parseInt(e.target.value) || 0 })}
                    />
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Probe Module</label>
                    <Select 
                      value={config.probeModule} 
                      onValueChange={updateProbeModule}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {Object.entries(PROBE_MODULES).map(([key, module]) => (
                          <SelectItem key={key} value={key}>
                            {module.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                
                {/* Probe Module Description */}
                <div className="text-xs text-muted-foreground p-2 bg-muted/50 rounded">
                  {PROBE_MODULES[config.probeModule].description}
                </div>
                
                {/* Max Targets */}
                <div className="space-y-2">
                  <label className="text-sm font-medium">
                    Max Targets: {formatNumber(config.maxTargets)}
                  </label>
                  <input
                    type="range"
                    min="1"
                    max="10000000"
                    step="1000"
                    value={config.maxTargets}
                    onChange={(e) => updateConfig({ maxTargets: parseInt(e.target.value) })}
                    className="w-full"
                  />
                  <div className="flex justify-between text-xs text-muted-foreground">
                    <span>1</span>
                    <span>1K</span>
                    <span>100K</span>
                    <span>1M</span>
                    <span>10M</span>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            {/* Performance Configuration */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Wifi className="h-5 w-5" />
                  Performance Settings
                </CardTitle>
                <CardDescription>
                  Configure bandwidth and timing parameters
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Bandwidth Presets */}
                <div className="space-y-2">
                  <label className="text-sm font-medium">Bandwidth Presets</label>
                  <div className="grid grid-cols-2 gap-2">
                    {Object.entries(BANDWIDTH_PRESETS).map(([name, preset]) => (
                      <Button
                        key={name}
                        variant="outline"
                        size="sm"
                        onClick={() => applyBandwidthPreset(name)}
                        className="h-auto p-3 flex flex-col items-start"
                      >
                        <span className="font-medium">{preset.label}</span>
                        <span className="text-xs text-muted-foreground">{preset.description}</span>
                      </Button>
                    ))}
                  </div>
                </div>
                
                {/* Custom Bandwidth */}
                <div className="space-y-2">
                  <label className="text-sm font-medium">Bandwidth</label>
                  <Input
                    placeholder="e.g., 10M, 1G"
                    value={config.bandwidth}
                    onChange={(e) => updateConfig({ bandwidth: e.target.value })}
                  />
                  <p className="text-xs text-muted-foreground">
                    Bandwidth limit (K/M/G suffix for Kbps/Mbps/Gbps)
                  </p>
                </div>
                
                {/* Source Configuration */}
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Source Port</label>
                    <Input
                      placeholder="Random"
                      value={config.sourcePort}
                      onChange={(e) => updateConfig({ sourcePort: e.target.value })}
                    />
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Source IP</label>
                    <Input
                      placeholder="Auto-detect"
                      value={config.sourceIP}
                      onChange={(e) => updateConfig({ sourceIP: e.target.value })}
                    />
                  </div>
                </div>
                
                {/* Interface */}
                <div className="space-y-2">
                  <label className="text-sm font-medium">Network Interface</label>
                  <Input
                    placeholder="Auto-detect (eth0, wlan0, etc.)"
                    value={config.interface}
                    onChange={(e) => updateConfig({ interface: e.target.value })}
                  />
                </div>
                
                {/* Runtime Limits */}
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Max Runtime (sec)</label>
                    <Input
                      type="number"
                      placeholder="3600"
                      value={config.maxRuntime}
                      onChange={(e) => updateConfig({ maxRuntime: parseInt(e.target.value) || 0 })}
                    />
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Cooldown Time</label>
                    <Input
                      type="number"
                      placeholder="8"
                      value={config.cooldownTime}
                      onChange={(e) => updateConfig({ cooldownTime: parseInt(e.target.value) || 8 })}
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
          
          {/* Advanced Configuration */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Database className="h-5 w-5" />
                Output & Advanced Options
              </CardTitle>
              <CardDescription>
                Configure output fields, filters, and advanced scanning options
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Output Fields */}
              <div className="space-y-2">
                <label className="text-sm font-medium">Output Fields</label>
                <div className="grid grid-cols-3 gap-2">
                  {OUTPUT_FIELDS.map((field) => (
                    <label key={field} className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        checked={config.outputFields.includes(field)}
                        onChange={(e) => {
                          if (e.target.checked) {
                            updateConfig({
                              outputFields: [...config.outputFields, field]
                            });
                          } else {
                            updateConfig({
                              outputFields: config.outputFields.filter(f => f !== field)
                            });
                          }
                        }}
                        className="rounded border-gray-300"
                      />
                      <span className="text-xs font-mono">{field}</span>
                    </label>
                  ))}
                </div>
              </div>
              
              {/* Output Filter */}
              <div className="space-y-2">
                <label className="text-sm font-medium">Output Filter</label>
                <Input
                  placeholder="e.g., success = 1 && repeat = 0"
                  value={config.outputFilter}
                  onChange={(e) => updateConfig({ outputFilter: e.target.value })}
                />
                <p className="text-xs text-muted-foreground">
                  Filter expression for output results
                </p>
              </div>
              
              {/* List Files */}
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium">Blacklist File</label>
                  <Input
                    placeholder="Path to blacklist file"
                    value={config.blacklistFile}
                    onChange={(e) => updateConfig({ blacklistFile: e.target.value })}
                  />
                </div>
                <div className="space-y-2">
                  <label className="text-sm font-medium">Whitelist File</label>
                  <Input
                    placeholder="Path to whitelist file"
                    value={config.whitelistFile}
                    onChange={(e) => updateConfig({ whitelistFile: e.target.value })}
                  />
                </div>
              </div>
              
              {/* Boolean Options */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <label className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    checked={config.dryRun}
                    onChange={(e) => updateConfig({ dryRun: e.target.checked })}
                    className="rounded border-gray-300"
                  />
                  <span className="text-sm">Dry Run</span>
                </label>
                
                <label className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    checked={config.summaryStats}
                    onChange={(e) => updateConfig({ summaryStats: e.target.checked })}
                    className="rounded border-gray-300"
                  />
                  <span className="text-sm">Summary Stats</span>
                </label>
              </div>
              
              {/* Custom Arguments */}
              <div className="space-y-2">
                <label className="text-sm font-medium">Custom Arguments</label>
                <Input
                  placeholder="Additional zmap arguments"
                  value={config.customArgs}
                  onChange={(e) => updateConfig({ customArgs: e.target.value })}
                />
              </div>
              
              {/* Action buttons */}
              <div className="flex justify-between items-center pt-4">
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={copyCommand}
                  >
                    <Copy className="h-4 w-4 mr-1" />
                    Copy Command
                  </Button>
                </div>
                
                <div className="flex gap-2">
                  {isScanning ? (
                    <Button
                      variant="destructive"
                      onClick={stopScan}
                    >
                      <Square className="h-4 w-4 mr-2" />
                      Stop Scan
                    </Button>
                  ) : (
                    <Button
                      onClick={startScan}
                      disabled={(!config.targetSubnet.trim() && !config.whitelistFile.trim()) || !backendStatus.connected}
                    >
                      <Play className="h-4 w-4 mr-2" />
                      Start Scan
                    </Button>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        {/* Output Tab */}
        <TabsContent value="output" className="space-y-6">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="flex items-center gap-2">
                    <Terminal className="h-5 w-5" />
                    Real-time Output
                  </CardTitle>
                  <CardDescription>
                    Live output from the Zmap scanning process
                  </CardDescription>
                </div>
                
                {/* Progress and controls */}
                <div className="flex items-center gap-4">
                  {isScanning && (
                    <div className="flex items-center gap-2">
                      <Progress value={progress} className="w-32" />
                      <span className="text-sm text-muted-foreground">
                        {progress}%
                      </span>
                    </div>
                  )}
                  
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setOutput([])}
                    disabled={isScanning}
                  >
                    <RotateCcw className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="bg-gray-900 text-gray-100 p-4 rounded-lg h-96 overflow-y-auto font-mono text-sm">
                {output.length === 0 ? (
                  <div className="text-gray-500 italic">
                    {isScanning ? 'Waiting for output...' : 'No output yet. Start a scan to see results here.'}
                  </div>
                ) : (
                  output.map((line, index) => (
                    <div key={index} className="mb-1">
                      {line}
                    </div>
                  ))
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        {/* Results Tab */}
        <TabsContent value="results" className="space-y-6">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="flex items-center gap-2">
                    <FileText className="h-5 w-5" />
                    Scan Results
                  </CardTitle>
                  <CardDescription>
                    Structured results and analysis from the completed scan
                  </CardDescription>
                </div>
                
                <Button
                  variant="outline"
                  onClick={exportResults}
                  disabled={!results && output.length === 0}
                >
                  <Download className="h-4 w-4 mr-2" />
                  Export Results
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              {results ? (
                <div className="space-y-4">
                  <pre className="bg-muted p-4 rounded-lg overflow-auto text-sm">
                    {JSON.stringify(results, null, 2)}
                  </pre>
                </div>
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  <FileText className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>No structured results available yet.</p>
                  <p className="text-sm">Complete a scan to see parsed results here.</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
        
        {/* Command Tab */}
        <TabsContent value="command" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Target className="h-5 w-5" />
                Generated Command
              </CardTitle>
              <CardDescription>
                The Zmap command that will be executed based on your configuration
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="bg-muted p-4 rounded-lg">
                  <code className="text-sm font-mono break-all">
                    {generateCommand()}
                  </code>
                </div>
                
                <div className="flex justify-between items-center">
                  <p className="text-sm text-muted-foreground">
                    This command will be executed on the backend server
                  </p>
                  
                  <Button variant="outline" onClick={copyCommand}>
                    <Copy className="h-4 w-4 mr-2" />
                    Copy Command
                  </Button>
                </div>
                
                {/* Large scale warning */}
                {config.maxTargets > 100000 && (
                  <div className="flex items-start gap-2 p-3 bg-red-50 dark:bg-red-900/10 rounded-md border border-red-200 dark:border-red-800">
                    <AlertTriangle className="h-4 w-4 text-red-600 dark:text-red-400 mt-0.5 flex-shrink-0" />
                    <div className="text-sm text-red-700 dark:text-red-300">
                      <p className="font-medium">Large Scale Scan Warning</p>
                      <p>This scan targets {formatNumber(config.maxTargets)} hosts and may:</p>
                      <ul className="list-disc list-inside mt-1 space-y-1">
                        <li>Generate significant network traffic ({config.bandwidth} bandwidth)</li>
                        <li>Take {formatDuration(scanStats.estimatedTime)} to complete</li>
                        <li>Trigger security monitoring and intrusion detection systems</li>
                        <li>Require compliance with responsible disclosure practices</li>
                      </ul>
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}"