#!/usr/bin/env python3
"""
⚡ Real-time Performance Monitoring Dashboard ⚡
Comprehensive performance analysis and monitoring for all NexusScan components

This script provides:
- Real-time system resource monitoring (CPU, Memory, Disk, Network)
- Application-specific performance metrics collection
- API response time tracking and analysis
- Database query performance monitoring
- Security tool execution performance profiling
- AI service response time analysis
- Frontend rendering performance tracking
- Memory leak detection and analysis
- Performance bottleneck identification
- Historical performance trend analysis
- Automated performance alerts and notifications
"""

import asyncio
import json
import logging
import os
import sys
import time
import threading
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
import psutil
import requests
import sqlite3
from collections import deque, defaultdict
import statistics
from dataclasses import dataclass, asdict
import subprocess

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root / 'src'))

# Color codes for dashboard output
class Colors:
    HEADER = '\033[95m'
    OKBLUE = '\033[94m'
    OKCYAN = '\033[96m'
    OKGREEN = '\033[92m'
    WARNING = '\033[93m'
    FAIL = '\033[91m'
    ENDC = '\033[0m'
    BOLD = '\033[1m'
    UNDERLINE = '\033[4m'

@dataclass
class PerformanceMetric:
    """Individual performance metric"""
    component: str
    metric_type: str
    value: float
    unit: str
    timestamp: datetime
    context: Dict[str, Any] = None
    
    def to_dict(self):
        """Convert to dictionary for JSON serialization"""
        return {
            'component': self.component,
            'metric_type': self.metric_type,
            'value': self.value,
            'unit': self.unit,
            'timestamp': self.timestamp.isoformat(),
            'context': self.context or {}
        }

@dataclass
class PerformanceAlert:
    """Performance alert when thresholds are exceeded"""
    component: str
    metric_type: str
    threshold: float
    actual_value: float
    severity: str  # 'warning', 'critical'
    message: str
    timestamp: datetime
    
    def to_dict(self):
        """Convert to dictionary for JSON serialization"""
        return {
            'component': self.component,
            'metric_type': self.metric_type,
            'threshold': self.threshold,
            'actual_value': self.actual_value,
            'severity': self.severity,
            'message': self.message,
            'timestamp': self.timestamp.isoformat()
        }

class SystemResourceMonitor:
    """Monitor system-level resource usage"""
    
    def __init__(self):
        self.metrics = deque(maxlen=1000)  # Keep last 1000 readings
        self.monitoring = False
        self.monitor_thread = None
    
    def start_monitoring(self, interval: float = 1.0):
        """Start continuous system monitoring"""
        self.monitoring = True
        self.monitor_thread = threading.Thread(target=self._monitor_loop, args=(interval,))
        self.monitor_thread.daemon = True
        self.monitor_thread.start()
    
    def stop_monitoring(self):
        """Stop monitoring"""
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5)
    
    def _monitor_loop(self, interval: float):
        """Main monitoring loop"""
        while self.monitoring:
            try:
                timestamp = datetime.now()
                
                # CPU metrics
                cpu_percent = psutil.cpu_percent(interval=0.1)
                cpu_freq = psutil.cpu_freq()
                cpu_count = psutil.cpu_count()
                
                self.metrics.append(PerformanceMetric(
                    'system', 'cpu_usage', cpu_percent, 'percent', timestamp,
                    {'cores': cpu_count, 'frequency_mhz': cpu_freq.current if cpu_freq else None}
                ))
                
                # Memory metrics
                memory = psutil.virtual_memory()
                self.metrics.append(PerformanceMetric(
                    'system', 'memory_usage', memory.percent, 'percent', timestamp,
                    {'total_gb': memory.total / 1024**3, 'available_gb': memory.available / 1024**3}
                ))
                
                # Disk metrics
                disk = psutil.disk_usage('/')
                disk_percent = (disk.used / disk.total) * 100
                self.metrics.append(PerformanceMetric(
                    'system', 'disk_usage', disk_percent, 'percent', timestamp,
                    {'total_gb': disk.total / 1024**3, 'free_gb': disk.free / 1024**3}
                ))
                
                # Network metrics
                net_io = psutil.net_io_counters()
                self.metrics.append(PerformanceMetric(
                    'system', 'network_bytes_sent', net_io.bytes_sent, 'bytes', timestamp
                ))
                self.metrics.append(PerformanceMetric(
                    'system', 'network_bytes_recv', net_io.bytes_recv, 'bytes', timestamp
                ))
                
                # Process-specific metrics for NexusScan
                for proc in psutil.process_iter(['pid', 'name', 'cmdline', 'memory_info', 'cpu_percent']):
                    try:
                        if proc.info['cmdline']:
                            cmdline = ' '.join(proc.info['cmdline'])
                            if 'nexusscan' in cmdline.lower() or 'main.py' in cmdline:
                                memory_mb = proc.info['memory_info'].rss / 1024 / 1024
                                self.metrics.append(PerformanceMetric(
                                    'nexusscan_process', 'memory_usage', memory_mb, 'mb', timestamp,
                                    {'pid': proc.info['pid'], 'cmdline': cmdline[:100]}
                                ))
                                
                                cpu_percent = proc.info['cpu_percent']
                                if cpu_percent is not None:
                                    self.metrics.append(PerformanceMetric(
                                        'nexusscan_process', 'cpu_usage', cpu_percent, 'percent', timestamp,
                                        {'pid': proc.info['pid']}
                                    ))
                    except (psutil.NoSuchProcess, psutil.AccessDenied):
                        continue
                
                time.sleep(interval)
                
            except Exception as e:
                # Continue monitoring even if individual readings fail
                time.sleep(interval)
    
    def get_recent_metrics(self, component: str = None, metric_type: str = None, 
                          minutes: int = 5) -> List[PerformanceMetric]:
        """Get recent metrics with optional filtering"""
        cutoff_time = datetime.now() - timedelta(minutes=minutes)
        
        filtered_metrics = []
        for metric in self.metrics:
            if metric.timestamp >= cutoff_time:
                if component and metric.component != component:
                    continue
                if metric_type and metric.metric_type != metric_type:
                    continue
                filtered_metrics.append(metric)
        
        return filtered_metrics
    
    def get_current_snapshot(self) -> Dict[str, Any]:
        """Get current system performance snapshot"""
        if not self.metrics:
            return {}
        
        # Get latest metrics for each type
        latest_metrics = {}
        for metric in reversed(self.metrics):
            key = f"{metric.component}_{metric.metric_type}"
            if key not in latest_metrics:
                latest_metrics[key] = metric
        
        return {key: metric.to_dict() for key, metric in latest_metrics.items()}

class APIPerformanceTracker:
    """Track API endpoint performance"""
    
    def __init__(self, backend_url: str):
        self.backend_url = backend_url
        self.response_times = defaultdict(list)
        self.error_counts = defaultdict(int)
        self.request_counts = defaultdict(int)
    
    async def measure_endpoint_performance(self, endpoint: str, method: str = 'GET', 
                                         data: Dict = None) -> Dict[str, Any]:
        """Measure performance of a specific endpoint"""
        url = f"{self.backend_url}{endpoint}"
        
        start_time = time.time()
        try:
            if method.upper() == 'GET':
                response = requests.get(url, timeout=10)
            elif method.upper() == 'POST':
                response = requests.post(url, json=data, timeout=10)
            else:
                response = requests.request(method, url, json=data, timeout=10)
            
            end_time = time.time()
            response_time = (end_time - start_time) * 1000  # Convert to ms
            
            self.response_times[endpoint].append(response_time)
            self.request_counts[endpoint] += 1
            
            if response.status_code >= 400:
                self.error_counts[endpoint] += 1
            
            return {
                'endpoint': endpoint,
                'method': method,
                'response_time_ms': response_time,
                'status_code': response.status_code,
                'success': response.status_code < 400,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            end_time = time.time()
            response_time = (end_time - start_time) * 1000
            
            self.error_counts[endpoint] += 1
            self.request_counts[endpoint] += 1
            
            return {
                'endpoint': endpoint,
                'method': method,
                'response_time_ms': response_time,
                'error': str(e),
                'success': False,
                'timestamp': datetime.now().isoformat()
            }
    
    async def benchmark_all_endpoints(self) -> List[Dict[str, Any]]:
        """Benchmark all known API endpoints"""
        endpoints = [
            '/api/health',
            '/api/campaigns',
            '/api/scans',
            '/api/dashboard/metrics',
            '/api/tools/status',
            '/api/ai/status',
            '/api/config'
        ]
        
        results = []
        for endpoint in endpoints:
            result = await self.measure_endpoint_performance(endpoint)
            results.append(result)
            
            # Small delay between requests
            await asyncio.sleep(0.1)
        
        return results
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """Get performance summary for all tracked endpoints"""
        summary = {}
        
        for endpoint in self.response_times:
            times = self.response_times[endpoint]
            if times:
                summary[endpoint] = {
                    'avg_response_time_ms': statistics.mean(times),
                    'min_response_time_ms': min(times),
                    'max_response_time_ms': max(times),
                    'median_response_time_ms': statistics.median(times),
                    'request_count': self.request_counts[endpoint],
                    'error_count': self.error_counts[endpoint],
                    'error_rate': self.error_counts[endpoint] / self.request_counts[endpoint],
                    'sample_count': len(times)
                }
        
        return summary

class DatabasePerformanceMonitor:
    """Monitor database query performance"""
    
    def __init__(self, db_path: Path):
        self.db_path = db_path
        self.query_times = []
    
    def measure_query_performance(self, query: str, params: Tuple = None) -> Dict[str, Any]:
        """Measure performance of a database query"""
        if not self.db_path.exists():
            return {'error': 'Database file not found'}
        
        start_time = time.time()
        try:
            conn = sqlite3.connect(str(self.db_path))
            cursor = conn.cursor()
            
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)
            
            results = cursor.fetchall()
            conn.close()
            
            end_time = time.time()
            query_time = (end_time - start_time) * 1000  # Convert to ms
            
            self.query_times.append(query_time)
            
            return {
                'query': query[:100] + '...' if len(query) > 100 else query,
                'query_time_ms': query_time,
                'result_count': len(results),
                'success': True,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            end_time = time.time()
            query_time = (end_time - start_time) * 1000
            
            return {
                'query': query[:100] + '...' if len(query) > 100 else query,
                'query_time_ms': query_time,
                'error': str(e),
                'success': False,
                'timestamp': datetime.now().isoformat()
            }
    
    def benchmark_common_queries(self) -> List[Dict[str, Any]]:
        """Benchmark common database queries"""
        common_queries = [
            "SELECT COUNT(*) FROM campaigns",
            "SELECT COUNT(*) FROM scans",
            "SELECT COUNT(*) FROM vulnerabilities",
            "SELECT * FROM campaigns ORDER BY created_at DESC LIMIT 10",
            "SELECT * FROM vulnerabilities WHERE severity = 'critical' LIMIT 10",
            "SELECT name FROM sqlite_master WHERE type='table'"
        ]
        
        results = []
        for query in common_queries:
            result = self.measure_query_performance(query)
            results.append(result)
        
        return results

class SecurityToolPerformanceProfiler:
    """Profile security tool execution performance"""
    
    def __init__(self):
        self.tool_metrics = defaultdict(list)
    
    def profile_tool_execution(self, tool_name: str, command: List[str], 
                             timeout: int = 30) -> Dict[str, Any]:
        """Profile the execution of a security tool"""
        start_time = time.time()
        
        try:
            # Monitor process during execution
            process = subprocess.Popen(
                command,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            # Monitor resource usage
            psutil_process = psutil.Process(process.pid)
            max_memory = 0
            cpu_samples = []
            
            while process.poll() is None:
                try:
                    memory_mb = psutil_process.memory_info().rss / 1024 / 1024
                    cpu_percent = psutil_process.cpu_percent()
                    
                    max_memory = max(max_memory, memory_mb)
                    if cpu_percent > 0:
                        cpu_samples.append(cpu_percent)
                    
                    time.sleep(0.1)
                    
                    # Check for timeout
                    if time.time() - start_time > timeout:
                        process.terminate()
                        break
                        
                except psutil.NoSuchProcess:
                    break
            
            stdout, stderr = process.communicate()
            end_time = time.time()
            
            execution_time = end_time - start_time
            avg_cpu = statistics.mean(cpu_samples) if cpu_samples else 0
            
            metric = {
                'tool_name': tool_name,
                'execution_time_s': execution_time,
                'max_memory_mb': max_memory,
                'avg_cpu_percent': avg_cpu,
                'exit_code': process.returncode,
                'stdout_lines': len(stdout.splitlines()) if stdout else 0,
                'stderr_lines': len(stderr.splitlines()) if stderr else 0,
                'success': process.returncode == 0,
                'timestamp': datetime.now().isoformat()
            }
            
            self.tool_metrics[tool_name].append(metric)
            return metric
            
        except Exception as e:
            end_time = time.time()
            execution_time = end_time - start_time
            
            return {
                'tool_name': tool_name,
                'execution_time_s': execution_time,
                'error': str(e),
                'success': False,
                'timestamp': datetime.now().isoformat()
            }
    
    def get_tool_performance_summary(self, tool_name: str) -> Dict[str, Any]:
        """Get performance summary for a specific tool"""
        metrics = self.tool_metrics.get(tool_name, [])
        if not metrics:
            return {'error': f'No metrics available for {tool_name}'}
        
        successful_runs = [m for m in metrics if m.get('success', False)]
        
        if not successful_runs:
            return {'error': f'No successful runs for {tool_name}'}
        
        execution_times = [m['execution_time_s'] for m in successful_runs]
        memory_usage = [m['max_memory_mb'] for m in successful_runs]
        cpu_usage = [m['avg_cpu_percent'] for m in successful_runs]
        
        return {
            'tool_name': tool_name,
            'total_runs': len(metrics),
            'successful_runs': len(successful_runs),
            'success_rate': len(successful_runs) / len(metrics),
            'avg_execution_time_s': statistics.mean(execution_times),
            'min_execution_time_s': min(execution_times),
            'max_execution_time_s': max(execution_times),
            'avg_memory_mb': statistics.mean(memory_usage),
            'max_memory_mb': max(memory_usage),
            'avg_cpu_percent': statistics.mean(cpu_usage)
        }

class PerformanceAlertManager:
    """Manage performance alerts and thresholds"""
    
    def __init__(self):
        self.thresholds = {
            'cpu_usage': {'warning': 70, 'critical': 90},
            'memory_usage': {'warning': 80, 'critical': 95},
            'disk_usage': {'warning': 85, 'critical': 95},
            'api_response_time_ms': {'warning': 1000, 'critical': 5000},
            'query_time_ms': {'warning': 500, 'critical': 2000},
            'error_rate': {'warning': 0.05, 'critical': 0.1}
        }
        self.alerts = []
        self.alert_history = deque(maxlen=1000)
    
    def check_metric_thresholds(self, metric: PerformanceMetric) -> Optional[PerformanceAlert]:
        """Check if a metric exceeds defined thresholds"""
        thresholds = self.thresholds.get(metric.metric_type)
        if not thresholds:
            return None
        
        alert = None
        if metric.value >= thresholds.get('critical', float('inf')):
            alert = PerformanceAlert(
                component=metric.component,
                metric_type=metric.metric_type,
                threshold=thresholds['critical'],
                actual_value=metric.value,
                severity='critical',
                message=f"{metric.component} {metric.metric_type} is critical: {metric.value}{metric.unit}",
                timestamp=metric.timestamp
            )
        elif metric.value >= thresholds.get('warning', float('inf')):
            alert = PerformanceAlert(
                component=metric.component,
                metric_type=metric.metric_type,
                threshold=thresholds['warning'],
                actual_value=metric.value,
                severity='warning',
                message=f"{metric.component} {metric.metric_type} is high: {metric.value}{metric.unit}",
                timestamp=metric.timestamp
            )
        
        if alert:
            self.alerts.append(alert)
            self.alert_history.append(alert)
        
        return alert
    
    def get_active_alerts(self, severity: str = None) -> List[PerformanceAlert]:
        """Get active alerts, optionally filtered by severity"""
        if severity:
            return [alert for alert in self.alerts if alert.severity == severity]
        return self.alerts.copy()
    
    def clear_old_alerts(self, max_age_minutes: int = 60):
        """Clear alerts older than specified age"""
        cutoff_time = datetime.now() - timedelta(minutes=max_age_minutes)
        self.alerts = [alert for alert in self.alerts if alert.timestamp >= cutoff_time]

class PerformanceMonitoringDashboard:
    """⚡ Main performance monitoring dashboard"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.backend_url = "http://localhost:8000"
        
        # Initialize components
        self.system_monitor = SystemResourceMonitor()
        self.api_tracker = APIPerformanceTracker(self.backend_url)
        self.alert_manager = PerformanceAlertManager()
        self.tool_profiler = SecurityToolPerformanceProfiler()
        
        # Find database
        self.db_monitor = None
        db_paths = [
            self.project_root / 'data/nexusscan.db',
            self.project_root / 'nexusscan.db'
        ]
        for db_path in db_paths:
            if db_path.exists():
                self.db_monitor = DatabasePerformanceMonitor(db_path)
                break
        
        print(f"{Colors.HEADER}{Colors.BOLD}")
        print("⚡" * 50)
        print("    REAL-TIME PERFORMANCE MONITORING DASHBOARD")
        print("    Comprehensive Performance Analysis")
        print("⚡" * 50)
        print(f"{Colors.ENDC}")
    
    async def start_comprehensive_monitoring(self, duration_minutes: int = 10) -> Dict[str, Any]:
        """Start comprehensive performance monitoring for specified duration"""
        print(f"{Colors.OKCYAN}🚀 Starting {duration_minutes}-minute performance monitoring session...{Colors.ENDC}\n")
        
        # Start system monitoring
        self.system_monitor.start_monitoring(interval=2.0)
        
        monitoring_results = {
            'session_info': {
                'start_time': datetime.now().isoformat(),
                'duration_minutes': duration_minutes,
                'monitoring_interval_seconds': 2.0
            },
            'system_metrics': [],
            'api_performance': [],
            'database_performance': [],
            'security_tools_performance': [],
            'alerts': [],
            'summary': {}
        }
        
        try:
            # Run monitoring for specified duration
            end_time = time.time() + (duration_minutes * 60)
            
            while time.time() < end_time:
                await self._collect_performance_data(monitoring_results)
                await asyncio.sleep(30)  # Collect data every 30 seconds
                
                # Update dashboard display
                await self._display_dashboard_update(monitoring_results)
        
        finally:
            # Stop system monitoring
            self.system_monitor.stop_monitoring()
        
        # Generate final summary
        monitoring_results['summary'] = await self._generate_performance_summary(monitoring_results)
        
        return monitoring_results
    
    async def _collect_performance_data(self, results: Dict[str, Any]):
        """Collect performance data from all sources"""
        
        # Collect API performance data
        try:
            api_results = await self.api_tracker.benchmark_all_endpoints()
            results['api_performance'].extend(api_results)
            
            # Check for API performance alerts
            for api_result in api_results:
                if api_result.get('response_time_ms', 0) > 0:
                    metric = PerformanceMetric(
                        'api', 'api_response_time_ms', api_result['response_time_ms'], 
                        'ms', datetime.now(), {'endpoint': api_result['endpoint']}
                    )
                    alert = self.alert_manager.check_metric_thresholds(metric)
                    if alert:
                        results['alerts'].append(alert.to_dict())
                        
        except Exception as e:
            print(f"  ⚠️ API monitoring error: {str(e)}")
        
        # Collect database performance data
        if self.db_monitor:
            try:
                db_results = self.db_monitor.benchmark_common_queries()
                results['database_performance'].extend(db_results)
                
                # Check for database performance alerts
                for db_result in db_results:
                    if db_result.get('query_time_ms', 0) > 0:
                        metric = PerformanceMetric(
                            'database', 'query_time_ms', db_result['query_time_ms'],
                            'ms', datetime.now(), {'query': db_result.get('query', 'unknown')}
                        )
                        alert = self.alert_manager.check_metric_thresholds(metric)
                        if alert:
                            results['alerts'].append(alert.to_dict())
                            
            except Exception as e:
                print(f"  ⚠️ Database monitoring error: {str(e)}")
        
        # Collect system metrics and check thresholds
        recent_metrics = self.system_monitor.get_recent_metrics(minutes=1)
        for metric in recent_metrics:
            alert = self.alert_manager.check_metric_thresholds(metric)
            if alert:
                results['alerts'].append(alert.to_dict())
        
        results['system_metrics'].extend([m.to_dict() for m in recent_metrics])
    
    async def _display_dashboard_update(self, results: Dict[str, Any]):
        """Display real-time dashboard update"""
        current_snapshot = self.system_monitor.get_current_snapshot()
        
        print(f"\n{Colors.BOLD}📊 Performance Dashboard Update - {datetime.now().strftime('%H:%M:%S')}{Colors.ENDC}")
        print("-" * 60)
        
        # System metrics
        if 'system_cpu_usage' in current_snapshot:
            cpu_metric = current_snapshot['system_cpu_usage']
            cpu_value = cpu_metric['value']
            cpu_color = Colors.OKGREEN if cpu_value < 70 else Colors.WARNING if cpu_value < 90 else Colors.FAIL
            print(f"🖥️  CPU Usage: {cpu_color}{cpu_value:.1f}%{Colors.ENDC}")
        
        if 'system_memory_usage' in current_snapshot:
            mem_metric = current_snapshot['system_memory_usage']
            mem_value = mem_metric['value']
            mem_color = Colors.OKGREEN if mem_value < 80 else Colors.WARNING if mem_value < 95 else Colors.FAIL
            print(f"💾 Memory Usage: {mem_color}{mem_value:.1f}%{Colors.ENDC}")
        
        # Recent API performance
        recent_api = results['api_performance'][-5:] if results['api_performance'] else []
        if recent_api:
            avg_response_time = statistics.mean([r.get('response_time_ms', 0) for r in recent_api if r.get('response_time_ms')])
            response_color = Colors.OKGREEN if avg_response_time < 500 else Colors.WARNING if avg_response_time < 2000 else Colors.FAIL
            print(f"🔌 API Avg Response: {response_color}{avg_response_time:.0f}ms{Colors.ENDC}")
        
        # Recent alerts
        recent_alerts = [alert for alert in results['alerts'] if 
                        datetime.fromisoformat(alert['timestamp']) > datetime.now() - timedelta(minutes=5)]
        
        if recent_alerts:
            critical_alerts = [a for a in recent_alerts if a['severity'] == 'critical']
            warning_alerts = [a for a in recent_alerts if a['severity'] == 'warning']
            
            if critical_alerts:
                print(f"{Colors.FAIL}🚨 Critical Alerts: {len(critical_alerts)}{Colors.ENDC}")
            if warning_alerts:
                print(f"{Colors.WARNING}⚠️  Warning Alerts: {len(warning_alerts)}{Colors.ENDC}")
        else:
            print(f"{Colors.OKGREEN}✅ No Recent Alerts{Colors.ENDC}")
    
    async def _generate_performance_summary(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """Generate comprehensive performance summary"""
        
        summary = {
            'session_duration': results['session_info']['duration_minutes'],
            'total_data_points': len(results['system_metrics']),
            'total_alerts': len(results['alerts']),
            'system_performance': {},
            'api_performance': {},
            'database_performance': {},
            'overall_health_score': 0
        }
        
        # System performance summary
        if results['system_metrics']:
            cpu_metrics = [m for m in results['system_metrics'] if m['metric_type'] == 'cpu_usage' and m['component'] == 'system']
            memory_metrics = [m for m in results['system_metrics'] if m['metric_type'] == 'memory_usage' and m['component'] == 'system']
            
            if cpu_metrics:
                cpu_values = [m['value'] for m in cpu_metrics]
                summary['system_performance']['cpu'] = {
                    'avg': statistics.mean(cpu_values),
                    'max': max(cpu_values),
                    'min': min(cpu_values)
                }
            
            if memory_metrics:
                memory_values = [m['value'] for m in memory_metrics]
                summary['system_performance']['memory'] = {
                    'avg': statistics.mean(memory_values),
                    'max': max(memory_values),
                    'min': min(memory_values)
                }
        
        # API performance summary
        if results['api_performance']:
            successful_requests = [r for r in results['api_performance'] if r.get('success', False)]
            if successful_requests:
                response_times = [r['response_time_ms'] for r in successful_requests]
                summary['api_performance'] = {
                    'total_requests': len(results['api_performance']),
                    'successful_requests': len(successful_requests),
                    'success_rate': len(successful_requests) / len(results['api_performance']),
                    'avg_response_time_ms': statistics.mean(response_times),
                    'max_response_time_ms': max(response_times),
                    'min_response_time_ms': min(response_times)
                }
        
        # Database performance summary
        if results['database_performance']:
            successful_queries = [q for q in results['database_performance'] if q.get('success', False)]
            if successful_queries:
                query_times = [q['query_time_ms'] for q in successful_queries]
                summary['database_performance'] = {
                    'total_queries': len(results['database_performance']),
                    'successful_queries': len(successful_queries),
                    'success_rate': len(successful_queries) / len(results['database_performance']),
                    'avg_query_time_ms': statistics.mean(query_times),
                    'max_query_time_ms': max(query_times),
                    'min_query_time_ms': min(query_times)
                }
        
        # Calculate overall health score
        health_factors = []
        
        # System health (lower is better for usage metrics)
        if 'cpu' in summary['system_performance']:
            cpu_health = max(0, 100 - summary['system_performance']['cpu']['avg'])
            health_factors.append(cpu_health)
        
        if 'memory' in summary['system_performance']:
            memory_health = max(0, 100 - summary['system_performance']['memory']['avg'])
            health_factors.append(memory_health)
        
        # API health (lower response time is better)
        if 'avg_response_time_ms' in summary['api_performance']:
            api_health = max(0, 100 - (summary['api_performance']['avg_response_time_ms'] / 50))  # 5000ms = 0 health
            health_factors.append(api_health)
        
        # Alert penalty
        critical_alerts = len([a for a in results['alerts'] if a['severity'] == 'critical'])
        warning_alerts = len([a for a in results['alerts'] if a['severity'] == 'warning'])
        alert_penalty = (critical_alerts * 20) + (warning_alerts * 5)
        
        if health_factors:
            base_health = statistics.mean(health_factors)
            summary['overall_health_score'] = max(0, min(100, base_health - alert_penalty))
        
        return summary
    
    async def generate_performance_report(self, monitoring_results: Dict[str, Any]) -> Dict[str, Any]:
        """Generate comprehensive performance report"""
        
        print(f"\n{Colors.HEADER}{Colors.BOLD}")
        print("⚡" * 50)
        print("           PERFORMANCE MONITORING REPORT")
        print("⚡" * 50)
        print(f"{Colors.ENDC}")
        
        summary = monitoring_results.get('summary', {})
        
        # Overall health score
        health_score = summary.get('overall_health_score', 0)
        health_color = Colors.OKGREEN if health_score > 80 else Colors.WARNING if health_score > 60 else Colors.FAIL
        
        print(f"\n{Colors.BOLD}⚡ OVERALL PERFORMANCE HEALTH: {health_color}{health_score:.1f}%{Colors.ENDC}")
        print(f"{Colors.BOLD}⏱️  MONITORING DURATION: {summary.get('session_duration', 0)} minutes{Colors.ENDC}")
        print(f"{Colors.BOLD}📊 DATA POINTS COLLECTED: {summary.get('total_data_points', 0)}{Colors.ENDC}")
        print(f"{Colors.BOLD}🚨 TOTAL ALERTS: {summary.get('total_alerts', 0)}{Colors.ENDC}")
        
        # System performance breakdown
        system_perf = summary.get('system_performance', {})
        if system_perf:
            print(f"\n{Colors.BOLD}🖥️ SYSTEM PERFORMANCE:{Colors.ENDC}")
            if 'cpu' in system_perf:
                cpu = system_perf['cpu']
                print(f"   CPU Usage: Avg {cpu['avg']:.1f}%, Max {cpu['max']:.1f}%, Min {cpu['min']:.1f}%")
            if 'memory' in system_perf:
                memory = system_perf['memory']
                print(f"   Memory Usage: Avg {memory['avg']:.1f}%, Max {memory['max']:.1f}%, Min {memory['min']:.1f}%")
        
        # API performance breakdown
        api_perf = summary.get('api_performance', {})
        if api_perf:
            print(f"\n{Colors.BOLD}🔌 API PERFORMANCE:{Colors.ENDC}")
            print(f"   Total Requests: {api_perf.get('total_requests', 0)}")
            print(f"   Success Rate: {api_perf.get('success_rate', 0):.1%}")
            print(f"   Avg Response Time: {api_perf.get('avg_response_time_ms', 0):.0f}ms")
            print(f"   Max Response Time: {api_perf.get('max_response_time_ms', 0):.0f}ms")
        
        # Database performance breakdown
        db_perf = summary.get('database_performance', {})
        if db_perf:
            print(f"\n{Colors.BOLD}🗄️ DATABASE PERFORMANCE:{Colors.ENDC}")
            print(f"   Total Queries: {db_perf.get('total_queries', 0)}")
            print(f"   Success Rate: {db_perf.get('success_rate', 0):.1%}")
            print(f"   Avg Query Time: {db_perf.get('avg_query_time_ms', 0):.0f}ms")
            print(f"   Max Query Time: {db_perf.get('max_query_time_ms', 0):.0f}ms")
        
        # Alert summary
        alerts = monitoring_results.get('alerts', [])
        if alerts:
            critical_alerts = [a for a in alerts if a['severity'] == 'critical']
            warning_alerts = [a for a in alerts if a['severity'] == 'warning']
            
            print(f"\n{Colors.BOLD}🚨 ALERT SUMMARY:{Colors.ENDC}")
            if critical_alerts:
                print(f"{Colors.FAIL}   Critical Alerts: {len(critical_alerts)}{Colors.ENDC}")
                for alert in critical_alerts[-3:]:  # Show last 3
                    print(f"     • {alert['component']} {alert['metric_type']}: {alert['actual_value']}")
            
            if warning_alerts:
                print(f"{Colors.WARNING}   Warning Alerts: {len(warning_alerts)}{Colors.ENDC}")
        
        print(f"\n{Colors.HEADER}⚡ Performance monitoring complete! ⚡{Colors.ENDC}")
        
        # Save detailed report
        report_file = self.project_root / f"performance_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w') as f:
            json.dump(monitoring_results, f, indent=2, default=str)
        
        print(f"\n📋 Detailed report saved to: {report_file}")
        
        return monitoring_results

    async def quick_performance_check(self) -> Dict[str, Any]:
        """Perform a quick performance health check"""
        print(f"{Colors.OKCYAN}🔍 Performing quick performance check...{Colors.ENDC}\n")
        
        # Start monitoring briefly
        self.system_monitor.start_monitoring(interval=1.0)
        await asyncio.sleep(5)  # Monitor for 5 seconds
        
        # Get current metrics
        current_metrics = self.system_monitor.get_current_snapshot()
        
        # Test API performance
        api_results = await self.api_tracker.benchmark_all_endpoints()
        
        # Test database performance
        db_results = []
        if self.db_monitor:
            db_results = self.db_monitor.benchmark_common_queries()
        
        # Stop monitoring
        self.system_monitor.stop_monitoring()
        
        # Generate quick summary
        quick_summary = {
            'timestamp': datetime.now().isoformat(),
            'system_snapshot': current_metrics,
            'api_performance': api_results,
            'database_performance': db_results,
            'health_status': 'unknown'
        }
        
        # Determine health status
        issues = []
        
        # Check system metrics
        if 'system_cpu_usage' in current_metrics:
            cpu_usage = current_metrics['system_cpu_usage']['value']
            if cpu_usage > 90:
                issues.append(f"High CPU usage: {cpu_usage:.1f}%")
        
        if 'system_memory_usage' in current_metrics:
            memory_usage = current_metrics['system_memory_usage']['value']
            if memory_usage > 95:
                issues.append(f"High memory usage: {memory_usage:.1f}%")
        
        # Check API performance
        api_failures = [r for r in api_results if not r.get('success', False)]
        if len(api_failures) > len(api_results) / 2:
            issues.append(f"High API failure rate: {len(api_failures)}/{len(api_results)}")
        
        slow_apis = [r for r in api_results if r.get('response_time_ms', 0) > 2000]
        if slow_apis:
            issues.append(f"Slow API responses: {len(slow_apis)} endpoints > 2s")
        
        # Check database performance
        slow_queries = [q for q in db_results if q.get('query_time_ms', 0) > 1000]
        if slow_queries:
            issues.append(f"Slow database queries: {len(slow_queries)} > 1s")
        
        # Determine overall health
        if not issues:
            quick_summary['health_status'] = 'healthy'
            print(f"{Colors.OKGREEN}✅ System appears healthy{Colors.ENDC}")
        elif len(issues) <= 2:
            quick_summary['health_status'] = 'warning'
            print(f"{Colors.WARNING}⚠️ Some performance issues detected{Colors.ENDC}")
        else:
            quick_summary['health_status'] = 'critical'
            print(f"{Colors.FAIL}❌ Multiple performance issues detected{Colors.ENDC}")
        
        # Show issues
        if issues:
            print(f"\n{Colors.BOLD}Issues detected:{Colors.ENDC}")
            for issue in issues:
                print(f"  • {issue}")
        
        quick_summary['issues'] = issues
        return quick_summary

async def main():
    """Main entry point"""
    dashboard = PerformanceMonitoringDashboard()
    
    # You can run either a quick check or comprehensive monitoring
    # For demo purposes, let's do a quick check
    results = await dashboard.quick_performance_check()
    
    # Uncomment below for comprehensive monitoring
    # results = await dashboard.start_comprehensive_monitoring(duration_minutes=5)
    # await dashboard.generate_performance_report(results)
    
    return results

if __name__ == "__main__":
    asyncio.run(main())