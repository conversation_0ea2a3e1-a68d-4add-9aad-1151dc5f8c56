import { ApiClient } from './api-client';
import { BackendStore } from '../stores/backend-store';

export interface WorkflowTemplate {
  id: string;
  name: string;
  description: string;
  category: 'reconnaissance' | 'enumeration' | 'vulnerability_scanning' | 'exploitation' | 'post_exploitation';
  tools: WorkflowTool[];
  estimatedDuration: number; // minutes
  difficulty: 'beginner' | 'intermediate' | 'advanced' | 'expert';
  prerequisites: string[];
}

export interface WorkflowTool {
  toolId: string;
  name: string;
  order: number;
  config: Record<string, any>;
  dependencies: string[]; // tool IDs this tool depends on
  optional: boolean;
  condition?: string; // condition to run this tool
}

export interface WorkflowExecution {
  id: string;
  templateId: string;
  campaignId: string;
  status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';
  currentStep: number;
  totalSteps: number;
  startedAt: Date;
  completedAt?: Date;
  results: WorkflowStepResult[];
  metadata: Record<string, any>;
}

export interface WorkflowStepResult {
  toolId: string;
  stepNumber: number;
  status: 'pending' | 'running' | 'completed' | 'failed' | 'skipped';
  startedAt?: Date;
  completedAt?: Date;
  output?: any;
  error?: string;
  vulnerabilities?: any[];
  artifacts?: string[];
}

export class AssessmentWorkflows {
  private apiClient: ApiClient;
  private backendStore: BackendStore;
  private activeExecutions: Map<string, WorkflowExecution> = new Map();

  constructor(apiClient: ApiClient, backendStore: BackendStore) {
    this.apiClient = apiClient;
    this.backendStore = backendStore;
  }

  // Built-in workflow templates for real-world penetration testing
  getWorkflowTemplates(): WorkflowTemplate[] {
    return [
      {
        id: 'external-network-assessment',
        name: 'External Network Assessment',
        description: 'Comprehensive external network penetration test following industry standard methodology',
        category: 'reconnaissance',
        estimatedDuration: 180, // 3 hours
        difficulty: 'intermediate',
        prerequisites: ['Valid target IP ranges', 'Authorization documentation'],
        tools: [
          {
            toolId: 'nmap',
            name: 'Network Discovery',
            order: 1,
            config: { 
              scan_type: 'discovery',
              timing: 'normal',
              scripts: ['default', 'vuln']
            },
            dependencies: [],
            optional: false
          },
          {
            toolId: 'nuclei',
            name: 'Vulnerability Scanning',
            order: 2,
            config: {
              severity: ['medium', 'high', 'critical'],
              templates: 'all',
              rate_limit: 150
            },
            dependencies: ['nmap'],
            optional: false
          },
          {
            toolId: 'nikto',
            name: 'Web Server Scanning',
            order: 3,
            config: {
              comprehensive: true,
              evasion: 'basic'
            },
            dependencies: ['nmap'],
            optional: true,
            condition: 'http_services_found'
          },
          {
            toolId: 'sqlmap',
            name: 'SQL Injection Testing',
            order: 4,
            config: {
              level: 3,
              risk: 2,
              batch: true
            },
            dependencies: ['nikto'],
            optional: true,
            condition: 'web_applications_found'
          }
        ]
      },
      {
        id: 'web-application-assessment',
        name: 'Web Application Security Assessment',
        description: 'Focused web application penetration test covering OWASP Top 10 and beyond',
        category: 'vulnerability_scanning',
        estimatedDuration: 240, // 4 hours
        difficulty: 'intermediate',
        prerequisites: ['Target web application URLs', 'Test accounts if available'],
        tools: [
          {
            toolId: 'dirb',
            name: 'Directory Enumeration',
            order: 1,
            config: {
              wordlist: 'common',
              recursive: true,
              extensions: ['php', 'asp', 'aspx', 'jsp', 'html', 'js']
            },
            dependencies: [],
            optional: false
          },
          {
            toolId: 'gobuster',
            name: 'Advanced Directory Brute Force',
            order: 2,
            config: {
              mode: 'dir',
              threads: 50,
              wordlist: 'medium',
              extensions: ['php', 'asp', 'aspx', 'jsp']
            },
            dependencies: ['dirb'],
            optional: false
          },
          {
            toolId: 'nikto',
            name: 'Web Vulnerability Scanning',
            order: 3,
            config: {
              comprehensive: true,
              plugins: 'all',
              evasion: 'advanced'
            },
            dependencies: [],
            optional: false
          },
          {
            toolId: 'sqlmap',
            name: 'SQL Injection Assessment',
            order: 4,
            config: {
              level: 5,
              risk: 3,
              tamper: 'space2comment,charencode',
              batch: false
            },
            dependencies: ['nikto'],
            optional: false
          },
          {
            toolId: 'wpscan',
            name: 'WordPress Security Scan',
            order: 5,
            config: {
              enumerate: 'all',
              plugins_detection: 'aggressive',
              themes_detection: 'aggressive'
            },
            dependencies: [],
            optional: true,
            condition: 'wordpress_detected'
          }
        ]
      },
      {
        id: 'internal-network-assessment',
        name: 'Internal Network Assessment',
        description: 'Internal network penetration test simulating insider threat scenarios',
        category: 'enumeration',
        estimatedDuration: 300, // 5 hours
        difficulty: 'advanced',
        prerequisites: ['Internal network access', 'Domain information'],
        tools: [
          {
            toolId: 'nmap',
            name: 'Internal Network Discovery',
            order: 1,
            config: {
              scan_type: 'comprehensive',
              timing: 'aggressive',
              scripts: ['smb-enum-shares', 'smb-vuln-*', 'ssh-enum-algos']
            },
            dependencies: [],
            optional: false
          },
          {
            toolId: 'enum4linux-ng',
            name: 'SMB Enumeration',
            order: 2,
            config: {
              comprehensive: true,
              shares: true,
              users: true,
              groups: true
            },
            dependencies: ['nmap'],
            optional: false,
            condition: 'smb_services_found'
          },
          {
            toolId: 'smbclient',
            name: 'SMB Share Analysis',
            order: 3,
            config: {
              list_shares: true,
              anonymous_access: true,
              enumerate_files: true
            },
            dependencies: ['enum4linux-ng'],
            optional: false,
            condition: 'smb_shares_found'
          },
          {
            toolId: 'nuclei',
            name: 'Internal Vulnerability Scanning',
            order: 4,
            config: {
              severity: ['medium', 'high', 'critical'],
              templates: 'network,misc,file',
              rate_limit: 100
            },
            dependencies: ['nmap'],
            optional: false
          }
        ]
      },
      {
        id: 'ssl-tls-assessment',
        name: 'SSL/TLS Security Assessment',
        description: 'Comprehensive SSL/TLS configuration and vulnerability assessment',
        category: 'vulnerability_scanning',
        estimatedDuration: 90, // 1.5 hours
        difficulty: 'beginner',
        prerequisites: ['HTTPS services identified'],
        tools: [
          {
            toolId: 'testssl',
            name: 'SSL/TLS Configuration Analysis',
            order: 1,
            config: {
              comprehensive: true,
              check_ciphers: true,
              check_protocols: true,
              check_vulnerabilities: true
            },
            dependencies: [],
            optional: false
          },
          {
            toolId: 'sslyze',
            name: 'SSL/TLS Vulnerability Scanning',
            order: 2,
            config: {
              scan_commands: ['sslv2', 'sslv3', 'tlsv1', 'tlsv1_1', 'tlsv1_2', 'tlsv1_3'],
              check_certificates: true,
              check_heartbleed: true
            },
            dependencies: [],
            optional: false
          }
        ]
      },
      {
        id: 'reconnaissance-osint',
        name: 'OSINT Reconnaissance',
        description: 'Open Source Intelligence gathering for target organization',
        category: 'reconnaissance',
        estimatedDuration: 120, // 2 hours
        difficulty: 'beginner',
        prerequisites: ['Target organization name', 'Domain information'],
        tools: [
          {
            toolId: 'nmap',
            name: 'DNS Enumeration',
            order: 1,
            config: {
              scan_type: 'dns_enum',
              scripts: ['dns-brute', 'dns-zone-transfer']
            },
            dependencies: [],
            optional: false
          },
          {
            toolId: 'whatweb',
            name: 'Web Technology Fingerprinting',
            order: 2,
            config: {
              aggression: 3,
              verbose: true,
              follow_redirects: true
            },
            dependencies: [],
            optional: false
          }
        ]
      }
    ];
  }

  async executeWorkflow(templateId: string, campaignId: string, targets: string[]): Promise<string> {
    const template = this.getWorkflowTemplates().find(t => t.id === templateId);
    if (!template) {
      throw new Error(`Workflow template ${templateId} not found`);
    }

    // Create workflow execution
    const execution: WorkflowExecution = {
      id: this.generateExecutionId(),
      templateId,
      campaignId,
      status: 'pending',
      currentStep: 0,
      totalSteps: template.tools.length,
      startedAt: new Date(),
      results: [],
      metadata: { targets, template: template.name }
    };

    this.activeExecutions.set(execution.id, execution);

    // Start workflow execution
    this.runWorkflowSteps(execution, template, targets);

    return execution.id;
  }

  private async runWorkflowSteps(execution: WorkflowExecution, template: WorkflowTemplate, targets: string[]) {
    execution.status = 'running';
    this.notifyExecutionUpdate(execution);

    for (let i = 0; i < template.tools.length; i++) {
      const tool = template.tools[i];
      execution.currentStep = i + 1;

      // Check if tool should be skipped based on conditions
      if (tool.optional && tool.condition && !this.evaluateCondition(tool.condition, execution.results)) {
        const stepResult: WorkflowStepResult = {
          toolId: tool.toolId,
          stepNumber: i + 1,
          status: 'skipped',
          startedAt: new Date(),
          completedAt: new Date()
        };
        execution.results.push(stepResult);
        continue;
      }

      // Check dependencies
      if (!this.checkDependencies(tool.dependencies, execution.results)) {
        const stepResult: WorkflowStepResult = {
          toolId: tool.toolId,
          stepNumber: i + 1,
          status: 'failed',
          error: `Dependencies not met: ${tool.dependencies.join(', ')}`,
          startedAt: new Date(),
          completedAt: new Date()
        };
        execution.results.push(stepResult);
        
        if (!tool.optional) {
          execution.status = 'failed';
          execution.completedAt = new Date();
          this.notifyExecutionUpdate(execution);
          return;
        }
        continue;
      }

      // Execute tool
      const stepResult = await this.executeWorkflowStep(tool, targets, execution);
      execution.results.push(stepResult);

      if (stepResult.status === 'failed' && !tool.optional) {
        execution.status = 'failed';
        execution.completedAt = new Date();
        this.notifyExecutionUpdate(execution);
        return;
      }

      this.notifyExecutionUpdate(execution);
    }

    execution.status = 'completed';
    execution.completedAt = new Date();
    this.notifyExecutionUpdate(execution);
  }

  private async executeWorkflowStep(tool: WorkflowTool, targets: string[], execution: WorkflowExecution): Promise<WorkflowStepResult> {
    const stepResult: WorkflowStepResult = {
      toolId: tool.toolId,
      stepNumber: tool.order,
      status: 'running',
      startedAt: new Date()
    };

    try {
      // Prepare tool configuration with targets
      const config = {
        ...tool.config,
        targets: targets
      };

      // Execute tool via workflow API (using working backend)
      const toolExecution = await this.apiClient.executeToolViaWorkflow(tool.toolId, config);
      
      stepResult.status = toolExecution.status as any;
      stepResult.output = toolExecution.output;
      stepResult.completedAt = new Date();

      // Parse vulnerabilities if present
      if (toolExecution.output && typeof toolExecution.output === 'object') {
        stepResult.vulnerabilities = this.extractVulnerabilities(toolExecution.output);
        stepResult.artifacts = this.extractArtifacts(toolExecution.output);
      }

    } catch (error) {
      stepResult.status = 'failed';
      stepResult.error = error instanceof Error ? error.message : 'Unknown error';
      stepResult.completedAt = new Date();
    }

    return stepResult;
  }

  private evaluateCondition(condition: string, results: WorkflowStepResult[]): boolean {
    // Simple condition evaluation - can be expanded
    switch (condition) {
      case 'http_services_found':
        return results.some(r => r.output && JSON.stringify(r.output).includes('http'));
      case 'web_applications_found':
        return results.some(r => r.output && JSON.stringify(r.output).includes('web'));
      case 'wordpress_detected':
        return results.some(r => r.output && JSON.stringify(r.output).toLowerCase().includes('wordpress'));
      case 'smb_services_found':
        return results.some(r => r.output && JSON.stringify(r.output).includes('445') || JSON.stringify(r.output).includes('smb'));
      case 'smb_shares_found':
        return results.some(r => r.output && JSON.stringify(r.output).includes('share'));
      default:
        return true;
    }
  }

  private checkDependencies(dependencies: string[], results: WorkflowStepResult[]): boolean {
    if (dependencies.length === 0) return true;
    
    return dependencies.every(dep => 
      results.some(r => r.toolId === dep && (r.status === 'completed' || r.status === 'skipped'))
    );
  }

  private extractVulnerabilities(output: any): any[] {
    // Extract vulnerabilities from tool output - can be enhanced per tool
    if (Array.isArray(output.vulnerabilities)) {
      return output.vulnerabilities;
    }
    if (Array.isArray(output.results)) {
      return output.results.filter((r: any) => r.severity && r.severity !== 'info');
    }
    return [];
  }

  private extractArtifacts(output: any): string[] {
    // Extract file paths and artifacts from output
    const artifacts: string[] = [];
    if (output.files) artifacts.push(...output.files);
    if (output.screenshots) artifacts.push(...output.screenshots);
    if (output.reports) artifacts.push(...output.reports);
    return artifacts;
  }

  private generateExecutionId(): string {
    return `workflow_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private notifyExecutionUpdate(execution: WorkflowExecution) {
    // Notify backend store and UI of execution updates
    this.backendStore.updateWorkflowExecution(execution);
  }

  // Public API methods
  getActiveExecutions(): WorkflowExecution[] {
    return Array.from(this.activeExecutions.values());
  }

  getExecution(executionId: string): WorkflowExecution | undefined {
    return this.activeExecutions.get(executionId);
  }

  async cancelExecution(executionId: string): Promise<void> {
    const execution = this.activeExecutions.get(executionId);
    if (execution && execution.status === 'running') {
      execution.status = 'cancelled';
      execution.completedAt = new Date();
      this.notifyExecutionUpdate(execution);
    }
  }

  getWorkflowsByCategory(category: string): WorkflowTemplate[] {
    return this.getWorkflowTemplates().filter(t => t.category === category);
  }

  getRecommendedWorkflows(targets: string[]): WorkflowTemplate[] {
    // Simple recommendation logic - can be enhanced with AI
    const hasWebTargets = targets.some(t => t.includes('http') || t.includes(':80') || t.includes(':443'));
    const hasInternalTargets = targets.some(t => 
      t.startsWith('192.168.') || t.startsWith('10.') || t.startsWith('172.')
    );

    const recommended: WorkflowTemplate[] = [];
    
    if (hasWebTargets) {
      recommended.push(...this.getWorkflowsByCategory('vulnerability_scanning'));
    }
    
    if (hasInternalTargets) {
      recommended.push(...this.getWorkflowsByCategory('enumeration'));
    }
    
    // Always recommend reconnaissance
    recommended.push(...this.getWorkflowsByCategory('reconnaissance'));

    return recommended.slice(0, 3); // Top 3 recommendations
  }
}