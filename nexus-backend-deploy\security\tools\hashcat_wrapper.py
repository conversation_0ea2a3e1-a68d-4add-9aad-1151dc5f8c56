"""
Hashcat Wrapper for NexusScan Desktop
Advanced password recovery tool wrapper.
"""

import logging
from typing import Dict, List, Optional, Callable, Any
from datetime import datetime
import os

from security.tools.tool_registry import (
    ToolMetadata, ToolCategory, ToolCapabilities,
    ScanOptions, ScanResult, ToolStatus, register_tool
)
from security.tools.external_tool_wrapper import ExternalToolWrapper

logger = logging.getLogger(__name__)


@register_tool
class HashcatScanner(ExternalToolWrapper):
    """Hashcat advanced password recovery wrapper"""
    
    def get_metadata(self) -> ToolMetadata:
        """Get tool metadata"""
        return ToolMetadata(
            name="hashcat",
            display_name="Hashcat Password Cracker",
            description="World's fastest and most advanced password recovery utility",
            version="6.2.6",
            category=ToolCategory.EXPLOITATION,
            author="Jens 'atom' Steube",
            website="https://hashcat.net/hashcat/",
            capabilities=ToolCapabilities(
                supports_async=True,
                supports_progress=True,
                supports_cancellation=True,
                requires_root=False,
                network_access_required=False,
                output_formats=["text", "json"],
                supported_targets=["hash", "hashfile"]
            ),
            default_options={
                "attack_mode": 0,  # 0=dictionary, 1=combinator, 3=bruteforce, 6=hybrid
                "hash_type": 0,    # 0=MD5, 100=SHA1, 1400=SHA256, etc.
                "workload_profile": 2,  # 1=low, 2=default, 3=high, 4=nightmare
                "optimized_kernel": True,
                "remove_cracked": True,
                "wordlist": "/usr/share/wordlists/rockyou.txt",
                "rules": None,
                "mask": None
            }
        )
    
    def get_command_path(self) -> str:
        """Get Hashcat command path"""
        return "hashcat"
    
    def build_command(self, options: ScanOptions) -> List[str]:
        """Build Hashcat command"""
        cmd = [self.get_command_path()]
        
        # Attack mode
        attack_mode = options.custom_options.get("attack_mode", 0)
        cmd.extend(["-a", str(attack_mode)])
        
        # Hash type
        hash_type = options.custom_options.get("hash_type", 0)
        cmd.extend(["-m", str(hash_type)])
        
        # Workload profile
        workload = options.custom_options.get("workload_profile", 2)
        cmd.extend(["-w", str(workload)])
        
        # Optimized kernels
        if options.custom_options.get("optimized_kernel", True):
            cmd.append("-O")
        
        # Remove cracked hashes
        if options.custom_options.get("remove_cracked", True):
            cmd.append("--remove")
        
        # Output format
        if options.output_format == "json":
            cmd.extend(["--status-json"])
        
        # Status timer
        cmd.extend(["--status-timer", "10"])
        
        # Target (hash or file)
        cmd.append(options.target)
        
        # Attack-specific options
        if attack_mode == 0:  # Dictionary attack
            wordlist = options.custom_options.get("wordlist")
            if wordlist and os.path.exists(wordlist):
                cmd.append(wordlist)
            
            # Rules file
            rules = options.custom_options.get("rules")
            if rules:
                cmd.extend(["-r", rules])
                
        elif attack_mode == 3:  # Brute-force
            mask = options.custom_options.get("mask", "?a?a?a?a?a?a")
            cmd.append(mask)
            
        elif attack_mode == 6:  # Hybrid
            wordlist = options.custom_options.get("wordlist")
            if wordlist and os.path.exists(wordlist):
                cmd.append(wordlist)
            mask = options.custom_options.get("mask", "?d?d?d?d")
            cmd.append(mask)
        
        # Session name
        session = f"nexusscan_{int(datetime.now().timestamp())}"
        cmd.extend(["--session", session])
        
        # Force output
        cmd.append("--force")
        
        return cmd
    
    def parse_output(self, output: str, options: ScanOptions) -> Dict[str, Any]:
        """Parse Hashcat output"""
        parsed_results = {
            "target": options.target,
            "attack_mode": options.custom_options.get("attack_mode", 0),
            "hash_type": options.custom_options.get("hash_type", 0),
            "cracked_hashes": [],
            "statistics": {},
            "status": "running",
            "summary": {}
        }
        
        try:
            lines = output.strip().split('\n')
            
            for line in lines:
                line = line.strip()
                
                # Cracked hash format: hash:plaintext
                if ":" in line and not line.startswith("Status"):
                    parts = line.split(":", 1)
                    if len(parts) == 2:
                        parsed_results["cracked_hashes"].append({
                            "hash": parts[0],
                            "plaintext": parts[1],
                            "cracked_time": datetime.now().isoformat()
                        })
                
                # Parse status lines
                elif "Status" in line:
                    if "Exhausted" in line:
                        parsed_results["status"] = "exhausted"
                    elif "Cracked" in line:
                        parsed_results["status"] = "cracked"
                    elif "Running" in line:
                        parsed_results["status"] = "running"
                
                # Progress information
                elif "Progress" in line:
                    if "(" in line and ")" in line:
                        progress_str = line[line.find("(")+1:line.find(")")]
                        if "/" in progress_str:
                            current, total = progress_str.split("/")
                            parsed_results["statistics"]["progress"] = {
                                "current": int(current.strip()),
                                "total": int(total.strip()),
                                "percentage": 0
                            }
                            if parsed_results["statistics"]["progress"]["total"] > 0:
                                percentage = (parsed_results["statistics"]["progress"]["current"] / 
                                            parsed_results["statistics"]["progress"]["total"] * 100)
                                parsed_results["statistics"]["progress"]["percentage"] = round(percentage, 2)
                
                # Speed information
                elif "Speed" in line:
                    speed_parts = line.split(":")
                    if len(speed_parts) > 1:
                        parsed_results["statistics"]["speed"] = speed_parts[1].strip()
                
                # Time information
                elif "Time.Estimated" in line:
                    time_parts = line.split(":")
                    if len(time_parts) > 1:
                        parsed_results["statistics"]["estimated_time"] = ":".join(time_parts[1:]).strip()
            
            # Generate summary
            parsed_results["summary"] = {
                "total_cracked": len(parsed_results["cracked_hashes"]),
                "attack_mode_name": self._get_attack_mode_name(parsed_results["attack_mode"]),
                "hash_type_name": self._get_hash_type_name(parsed_results["hash_type"]),
                "success_rate": self._calculate_success_rate(parsed_results)
            }
            
        except Exception as e:
            logger.error(f"Failed to parse Hashcat output: {e}")
            parsed_results["parse_error"] = str(e)
            parsed_results["raw_output"] = output[:1000]
        
        return parsed_results
    
    def _get_attack_mode_name(self, mode: int) -> str:
        """Get human-readable attack mode name"""
        modes = {
            0: "Dictionary Attack",
            1: "Combinator Attack",
            3: "Brute-force Attack",
            6: "Hybrid Wordlist + Mask",
            7: "Hybrid Mask + Wordlist"
        }
        return modes.get(mode, f"Mode {mode}")
    
    def _get_hash_type_name(self, hash_type: int) -> str:
        """Get human-readable hash type name"""
        # Common hash types
        types = {
            0: "MD5",
            100: "SHA1",
            1400: "SHA256",
            1700: "SHA512",
            1000: "NTLM",
            3000: "LM",
            5500: "NetNTLMv1",
            5600: "NetNTLMv2",
            13100: "Kerberos 5 TGS-REP",
            1800: "sha512crypt",
            500: "md5crypt",
            3200: "bcrypt",
            7900: "Drupal7",
            400: "phpass"
        }
        return types.get(hash_type, f"Type {hash_type}")
    
    def _calculate_success_rate(self, results: Dict[str, Any]) -> float:
        """Calculate password cracking success rate"""
        total_cracked = results["summary"]["total_cracked"]
        if "progress" in results.get("statistics", {}):
            total = results["statistics"]["progress"].get("total", 0)
            if total > 0:
                return round((total_cracked / total) * 100, 2)
        return 0.0
    
    def get_tool_command(self, options: ScanOptions) -> List[str]:
        """Get tool command - required by base class"""
        return self.build_command(options)
    
    def parse_tool_output(self, output: str, options: ScanOptions) -> Dict[str, Any]:
        """Parse tool output - required by base class"""
        return self.parse_output(output, options)