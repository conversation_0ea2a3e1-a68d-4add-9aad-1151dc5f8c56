#!/usr/bin/env python3
"""
Comprehensive Backend Verification Test for NexusScan Desktop
Tests all tools listed in tools-based-frontend.md to verify they exist and function properly.
"""

import sys
import os
import asyncio
import importlib
import inspect
import logging
from pathlib import Path
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
import json
from datetime import datetime

# Add project paths to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root / "src"))
sys.path.insert(0, str(project_root / "nexus-backend-deploy" / "src"))

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

@dataclass
class ToolTestResult:
    """Result of tool verification test"""
    tool_name: str
    tool_path: str
    category: str
    exists: bool = False
    importable: bool = False
    has_frontend_interface: bool = False
    has_required_methods: bool = False
    frontend_data_complete: bool = False
    error_message: str = ""
    test_details: Dict[str, Any] = None

class ComprehensiveBackendTester:
    """Comprehensive tester for all backend tools"""
    
    def __init__(self):
        self.test_results: List[ToolTestResult] = []
        self.tools_from_frontend_md = self._load_tools_from_spec()
        
    def _load_tools_from_spec(self) -> Dict[str, List[Dict[str, str]]]:
        """Load all tools specified in tools-based-frontend.md"""
        return {
            "Core Security Scanners": [
                {"name": "Nmap Scanner", "path": "security.tools.nmap_scanner", "class": "NmapScanner"},
                {"name": "Nuclei Scanner", "path": "security.tools.nuclei_scanner", "class": "NucleiScanner"},
                {"name": "SQLMap Scanner", "path": "security.tools.sqlmap_scanner", "class": "SQLMapScanner"},
                {"name": "Nikto Scanner", "path": "security.tools.nikto_scanner", "class": "NiktoScanner"},
                {"name": "Dirb Scanner", "path": "security.tools.dirb_scanner", "class": "DirbScanner"},
                {"name": "Gobuster Scanner", "path": "security.tools.gobuster_scanner", "class": "GobusterScanner"}
            ],
            "AI-Enhanced Tools": [
                {"name": "Creative Exploit Engine", "path": "ai.creative_exploit_engine", "class": "CreativeExploitEngine"},
                {"name": "Multi-Stage Orchestrator", "path": "ai.multi_stage_orchestrator", "class": "MultiStageAttackOrchestrator"},
                {"name": "Behavioral Analysis Engine", "path": "ai.behavioral_analysis_engine", "class": "BehavioralAnalysisEngine"},
                {"name": "Adaptive Exploit Modifier", "path": "ai.adaptive_exploit_modifier", "class": "AdaptiveExploitModifier"},
                {"name": "Evasion Technique Generator", "path": "ai.evasion_technique_generator", "class": "EvasionTechniqueGenerator"},
                {"name": "AI-Powered Proxy", "path": "ai.ai_powered_proxy", "class": "AIPoweredProxy"}
            ],
            "Advanced Analyzers": [
                {"name": "Attack Surface Mapper", "path": "security.tools.custom.ai_analyzers.attack_surface_mapper", "class": "AttackSurfaceMapper"},
                {"name": "Threat Model Generator", "path": "security.tools.custom.ai_analyzers.threat_model_generator", "class": "ThreatModelGenerator"},
                {"name": "Risk Calculator", "path": "security.tools.custom.ai_analyzers.risk_calculator", "class": "RiskCalculator"},
                {"name": "Security Posture Analyzer", "path": "security.tools.custom.ai_analyzers.security_posture_analyzer", "class": "SecurityPostureAnalyzer"}
            ],
            "External Tool Gateway": [
                {"name": "External Tool Gateway", "path": "security.tools.external_tool_gateway", "class": "ExternalToolGateway"}
            ],
            "Utility Tools": [
                {"name": "Universal Parser", "path": "security.tools.custom.utilities.universal_parser", "class": "UniversalParser"},
                {"name": "Evidence Collector", "path": "security.tools.custom.utilities.evidence_collector", "class": "EvidenceCollector"},
                {"name": "Proxy Engine", "path": "security.tools.custom.utilities.proxy_engine", "class": "ProxyEngine"},
                {"name": "Parameter Fuzzer", "path": "security.tools.custom.utilities.parameter_fuzzer", "class": "ParameterFuzzer"},
                {"name": "Wordlist Manager", "path": "security.tools.custom.utilities.wordlist_manager", "class": "WordlistManager"}
            ],
            "Compliance Framework": [
                {"name": "Automated Compliance Tester", "path": "security.tools.custom.compliance.automated_compliance_tester", "class": "AutomatedComplianceTester"},
                {"name": "SOC2 Compliance Checker", "path": "security.tools.custom.compliance.soc2_compliance_checker", "class": "SOC2ComplianceChecker"},
                {"name": "PCI DSS Scanner", "path": "security.tools.custom.compliance.pci_dss_scanner", "class": "PCIDSSScanner"},
                {"name": "HIPAA Compliance Validator", "path": "security.tools.custom.compliance.hipaa_compliance_validator", "class": "HIPAAComplianceValidator"}
            ]
        }
    
    async def run_comprehensive_test(self) -> Dict[str, Any]:
        """Run comprehensive test of all backend tools"""
        logger.info("🚀 Starting Comprehensive Backend Verification Test")
        logger.info("="*80)
        
        start_time = datetime.now()
        total_tools = sum(len(tools) for tools in self.tools_from_frontend_md.values())
        logger.info(f"Testing {total_tools} tools across {len(self.tools_from_frontend_md)} categories")
        
        category_results = {}
        
        for category, tools in self.tools_from_frontend_md.items():
            logger.info(f"\n📂 Testing Category: {category}")
            logger.info("-" * 60)
            
            category_results[category] = await self._test_category(category, tools)
        
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        # Generate comprehensive report
        report = self._generate_comprehensive_report(category_results, duration)
        
        # Save detailed results
        await self._save_test_results(report)
        
        return report
    
    async def _test_category(self, category: str, tools: List[Dict[str, str]]) -> Dict[str, Any]:
        """Test all tools in a category"""
        category_results = {
            "tools_tested": len(tools),
            "tools_passed": 0,
            "tools_failed": 0,
            "tool_results": [],
            "category_status": "PENDING"
        }
        
        for tool in tools:
            logger.info(f"  🔍 Testing: {tool['name']}")
            result = await self._test_single_tool(tool['name'], tool['path'], tool['class'], category)
            
            self.test_results.append(result)
            category_results["tool_results"].append(result)
            
            if result.exists and result.importable and result.has_frontend_interface:
                category_results["tools_passed"] += 1
                logger.info(f"    ✅ {tool['name']}: PASSED")
            else:
                category_results["tools_failed"] += 1
                logger.info(f"    ❌ {tool['name']}: FAILED - {result.error_message}")
        
        # Determine category status
        if category_results["tools_passed"] == category_results["tools_tested"]:
            category_results["category_status"] = "PASSED"
        elif category_results["tools_passed"] > 0:
            category_results["category_status"] = "PARTIAL"
        else:
            category_results["category_status"] = "FAILED"
        
        logger.info(f"  📊 Category Summary: {category_results['tools_passed']}/{category_results['tools_tested']} tools passed")
        
        return category_results
    
    async def _test_single_tool(self, tool_name: str, tool_path: str, class_name: str, category: str) -> ToolTestResult:
        """Test a single tool comprehensively"""
        result = ToolTestResult(
            tool_name=tool_name,
            tool_path=tool_path,
            category=category,
            test_details={}
        )
        
        try:
            # Test 1: Check if file exists
            result.test_details["file_existence"] = await self._test_file_existence(tool_path)
            result.exists = result.test_details["file_existence"]["exists"]
            
            if not result.exists:
                result.error_message = f"File not found: {tool_path}"
                return result
            
            # Test 2: Check if module is importable
            result.test_details["import_test"] = await self._test_module_import(tool_path, class_name)
            result.importable = result.test_details["import_test"]["importable"]
            
            if not result.importable:
                result.error_message = result.test_details["import_test"]["error"]
                return result
            
            # Test 3: Check for frontend interface method
            result.test_details["frontend_interface"] = await self._test_frontend_interface(tool_path, class_name)
            result.has_frontend_interface = result.test_details["frontend_interface"]["has_method"]
            
            # Test 4: Check for required methods
            result.test_details["required_methods"] = await self._test_required_methods(tool_path, class_name)
            result.has_required_methods = result.test_details["required_methods"]["all_present"]
            
            # Test 5: Test frontend data completeness
            if result.has_frontend_interface:
                result.test_details["frontend_data"] = await self._test_frontend_data_completeness(tool_path, class_name)
                result.frontend_data_complete = result.test_details["frontend_data"]["complete"]
            
            # Set overall error message if any tests failed
            if not all([result.exists, result.importable, result.has_frontend_interface]):
                failed_tests = []
                if not result.exists:
                    failed_tests.append("file missing")
                if not result.importable:
                    failed_tests.append("import failed")
                if not result.has_frontend_interface:
                    failed_tests.append("no frontend interface")
                result.error_message = f"Failed tests: {', '.join(failed_tests)}"
            
        except Exception as e:
            result.error_message = f"Test exception: {str(e)}"
            result.test_details["exception"] = str(e)
        
        return result
    
    async def _test_file_existence(self, tool_path: str) -> Dict[str, Any]:
        """Test if tool file exists"""
        # Try both src/ and nexus-backend-deploy/src/ paths
        tool_file_path = tool_path.replace(".", "/") + ".py"
        paths_to_check = [
            project_root / "src" / tool_file_path,
            project_root / "nexus-backend-deploy" / "src" / tool_file_path
        ]
        
        for path in paths_to_check:
            if path.exists():
                return {
                    "exists": True,
                    "full_path": str(path),
                    "file_size": path.stat().st_size
                }
        
        return {
            "exists": False,
            "checked_paths": [str(p) for p in paths_to_check]
        }
    
    async def _test_module_import(self, tool_path: str, class_name: str) -> Dict[str, Any]:
        """Test if module can be imported and class exists"""
        try:
            module = importlib.import_module(tool_path)
            
            if hasattr(module, class_name):
                tool_class = getattr(module, class_name)
                return {
                    "importable": True,
                    "class_exists": True,
                    "module_file": getattr(module, "__file__", "unknown"),
                    "class_type": str(type(tool_class))
                }
            else:
                return {
                    "importable": True,
                    "class_exists": False,
                    "available_classes": [name for name, obj in inspect.getmembers(module, inspect.isclass)],
                    "error": f"Class {class_name} not found in module"
                }
                
        except ImportError as e:
            return {
                "importable": False,
                "error": f"Import error: {str(e)}"
            }
        except Exception as e:
            return {
                "importable": False,
                "error": f"Unexpected error: {str(e)}"
            }
    
    async def _test_frontend_interface(self, tool_path: str, class_name: str) -> Dict[str, Any]:
        """Test if tool has get_frontend_interface_data method"""
        try:
            module = importlib.import_module(tool_path)
            tool_class = getattr(module, class_name)
            
            has_method = hasattr(tool_class, 'get_frontend_interface_data')
            
            if has_method:
                # Try to get method signature
                method = getattr(tool_class, 'get_frontend_interface_data')
                method_signature = str(inspect.signature(method))
                
                return {
                    "has_method": True,
                    "method_signature": method_signature,
                    "is_callable": callable(method)
                }
            else:
                # Check what methods are available
                methods = [name for name, method in inspect.getmembers(tool_class, predicate=inspect.ismethod)]
                return {
                    "has_method": False,
                    "available_methods": methods
                }
                
        except Exception as e:
            return {
                "has_method": False,
                "error": f"Error checking frontend interface: {str(e)}"
            }
    
    async def _test_required_methods(self, tool_path: str, class_name: str) -> Dict[str, Any]:
        """Test if tool has required methods for proper functioning"""
        required_methods = ['__init__', 'get_metadata']  # Basic required methods
        
        try:
            module = importlib.import_module(tool_path)
            tool_class = getattr(module, class_name)
            
            present_methods = []
            missing_methods = []
            
            for method_name in required_methods:
                if hasattr(tool_class, method_name):
                    present_methods.append(method_name)
                else:
                    missing_methods.append(method_name)
            
            return {
                "all_present": len(missing_methods) == 0,
                "present_methods": present_methods,
                "missing_methods": missing_methods,
                "total_methods": len([name for name, method in inspect.getmembers(tool_class, predicate=inspect.ismethod)])
            }
            
        except Exception as e:
            return {
                "all_present": False,
                "error": f"Error checking required methods: {str(e)}"
            }
    
    async def _test_frontend_data_completeness(self, tool_path: str, class_name: str) -> Dict[str, Any]:
        """Test if frontend interface data is complete and well-formed"""
        try:
            module = importlib.import_module(tool_path)
            tool_class = getattr(module, class_name)
            
            # Try to instantiate and call get_frontend_interface_data
            instance = tool_class()
            frontend_data = instance.get_frontend_interface_data()
            
            # Check for required top-level keys
            required_keys = ['header', 'educational_content']
            present_keys = []
            missing_keys = []
            
            for key in required_keys:
                if key in frontend_data:
                    present_keys.append(key)
                else:
                    missing_keys.append(key)
            
            # Check header structure
            header_complete = False
            if 'header' in frontend_data:
                header = frontend_data['header']
                required_header_keys = ['title', 'subtitle']
                header_complete = all(key in header for key in required_header_keys)
            
            return {
                "complete": len(missing_keys) == 0 and header_complete,
                "present_keys": present_keys,
                "missing_keys": missing_keys,
                "header_complete": header_complete,
                "total_keys": len(frontend_data.keys()),
                "data_size": len(str(frontend_data))
            }
            
        except Exception as e:
            return {
                "complete": False,
                "error": f"Error testing frontend data: {str(e)}"
            }
    
    def _generate_comprehensive_report(self, category_results: Dict[str, Any], duration: float) -> Dict[str, Any]:
        """Generate comprehensive test report"""
        total_tools = len(self.test_results)
        passed_tools = len([r for r in self.test_results if r.exists and r.importable and r.has_frontend_interface])
        failed_tools = total_tools - passed_tools
        
        # Category summary
        category_summary = {}
        for category, results in category_results.items():
            category_summary[category] = {
                "status": results["category_status"],
                "passed": results["tools_passed"],
                "total": results["tools_tested"],
                "pass_rate": results["tools_passed"] / results["tools_tested"] * 100 if results["tools_tested"] > 0 else 0
            }
        
        # Tool status breakdown
        status_breakdown = {
            "fully_functional": len([r for r in self.test_results if r.exists and r.importable and r.has_frontend_interface and r.frontend_data_complete]),
            "partially_functional": len([r for r in self.test_results if r.exists and r.importable and not (r.has_frontend_interface and r.frontend_data_complete)]),
            "import_issues": len([r for r in self.test_results if r.exists and not r.importable]),
            "missing_files": len([r for r in self.test_results if not r.exists])
        }
        
        # Failed tools details
        failed_tools_details = [
            {
                "name": r.tool_name,
                "category": r.category,
                "path": r.tool_path,
                "error": r.error_message,
                "exists": r.exists,
                "importable": r.importable,
                "has_frontend": r.has_frontend_interface
            }
            for r in self.test_results 
            if not (r.exists and r.importable and r.has_frontend_interface)
        ]
        
        # Success analysis
        success_rate = (passed_tools / total_tools * 100) if total_tools > 0 else 0
        
        # Determine overall status
        if success_rate >= 90:
            overall_status = "EXCELLENT"
        elif success_rate >= 75:
            overall_status = "GOOD"
        elif success_rate >= 50:
            overall_status = "PARTIAL"
        else:
            overall_status = "POOR"
        
        return {
            "test_summary": {
                "timestamp": datetime.now().isoformat(),
                "duration_seconds": duration,
                "overall_status": overall_status,
                "success_rate": success_rate,
                "total_tools_tested": total_tools,
                "tools_passed": passed_tools,
                "tools_failed": failed_tools
            },
            "category_results": category_summary,
            "status_breakdown": status_breakdown,
            "failed_tools": failed_tools_details,
            "detailed_results": [
                {
                    "tool_name": r.tool_name,
                    "category": r.category,
                    "path": r.tool_path,
                    "status": "PASSED" if (r.exists and r.importable and r.has_frontend_interface) else "FAILED",
                    "exists": r.exists,
                    "importable": r.importable,
                    "has_frontend_interface": r.has_frontend_interface,
                    "frontend_data_complete": r.frontend_data_complete,
                    "error_message": r.error_message,
                    "test_details": r.test_details
                }
                for r in self.test_results
            ]
        }
    
    async def _save_test_results(self, report: Dict[str, Any]):
        """Save test results to file"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"backend_verification_report_{timestamp}.json"
        
        with open(filename, 'w') as f:
            json.dump(report, f, indent=2, default=str)
        
        logger.info(f"\n📄 Detailed report saved to: {filename}")

async def main():
    """Main test execution"""
    print("🔬 NexusScan Desktop - Comprehensive Backend Verification Test")
    print("=" * 80)
    print("Testing all tools listed in tools-based-frontend.md for:")
    print("  ✓ File existence")
    print("  ✓ Module importability") 
    print("  ✓ Class availability")
    print("  ✓ Frontend interface methods")
    print("  ✓ Required method implementation")
    print("  ✓ Frontend data completeness")
    print()
    
    tester = ComprehensiveBackendTester()
    report = await tester.run_comprehensive_test()
    
    # Print summary results
    print("\n" + "="*80)
    print("🏁 COMPREHENSIVE TEST RESULTS SUMMARY")
    print("="*80)
    
    summary = report["test_summary"]
    print(f"Overall Status: {summary['overall_status']}")
    print(f"Success Rate: {summary['success_rate']:.1f}%")
    print(f"Tools Tested: {summary['total_tools_tested']}")
    print(f"Tools Passed: {summary['tools_passed']}")
    print(f"Tools Failed: {summary['tools_failed']}")
    print(f"Test Duration: {summary['duration_seconds']:.2f} seconds")
    
    print(f"\n📊 STATUS BREAKDOWN:")
    status = report["status_breakdown"]
    print(f"  ✅ Fully Functional: {status['fully_functional']}")
    print(f"  ⚠️  Partially Functional: {status['partially_functional']}")
    print(f"  🔧 Import Issues: {status['import_issues']}")
    print(f"  📂 Missing Files: {status['missing_files']}")
    
    print(f"\n📂 CATEGORY RESULTS:")
    for category, results in report["category_results"].items():
        status_emoji = "✅" if results["status"] == "PASSED" else "⚠️" if results["status"] == "PARTIAL" else "❌"
        print(f"  {status_emoji} {category}: {results['passed']}/{results['total']} ({results['pass_rate']:.1f}%)")
    
    if report["failed_tools"]:
        print(f"\n❌ FAILED TOOLS ({len(report['failed_tools'])}):")
        for tool in report["failed_tools"]:
            print(f"  • {tool['name']} ({tool['category']}): {tool['error']}")
    
    # Overall assessment
    print(f"\n🔍 BACKEND ASSESSMENT:")
    if summary['success_rate'] >= 90:
        print("🎉 EXCELLENT: Backend is nearly complete and ready for frontend integration!")
    elif summary['success_rate'] >= 75:
        print("👍 GOOD: Backend is mostly ready with minor issues to address.")
    elif summary['success_rate'] >= 50:
        print("⚠️  PARTIAL: Backend has significant gaps that need attention.")
    else:
        print("🚫 POOR: Backend requires major development work before frontend integration.")
    
    print("\nSee detailed JSON report for complete analysis.")
    return report

if __name__ == "__main__":
    asyncio.run(main())