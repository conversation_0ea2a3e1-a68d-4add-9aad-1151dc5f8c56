name: NexusScan Desktop CI/CD

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]
  release:
    types: [ published ]

env:
  NODE_VERSION: '18'
  ELECTRON_CACHE: ~/.cache/electron
  ELECTRON_BUILDER_CACHE: ~/.cache/electron-builder

jobs:
  test-unit:
    name: Unit Tests
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: frontend/package-lock.json
        
    - name: Install dependencies
      working-directory: frontend
      run: npm ci
      
    - name: Run type checking
      working-directory: frontend
      run: npm run type-check
      
    - name: Run linting
      working-directory: frontend
      run: npm run lint
      
    - name: Run unit tests
      working-directory: frontend
      run: npm run test:run
      
    - name: Run tests with coverage
      working-directory: frontend
      run: npm run test:coverage
      
    - name: Upload coverage reports
      uses: codecov/codecov-action@v3
      with:
        file: frontend/coverage/coverage-final.json
        flags: frontend
        name: frontend-coverage
        fail_ci_if_error: true

  test-integration:
    name: Integration Tests
    runs-on: ubuntu-latest
    needs: test-unit
    
    services:
      backend:
        image: nexusscan/backend:latest
        ports:
          - 8000:8000
        env:
          TESTING: true
          DATABASE_URL: sqlite:///tmp/test.db
        options: >-
          --health-cmd "curl -f http://localhost:8000/api/health || exit 1"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: frontend/package-lock.json
        
    - name: Install dependencies
      working-directory: frontend
      run: npm ci
      
    - name: Wait for backend
      run: |
        timeout 60 bash -c 'until curl -f http://localhost:8000/api/health; do sleep 2; done'
        
    - name: Run integration tests
      working-directory: frontend
      run: npm run test:integration
      env:
        API_BASE_URL: http://localhost:8000
        
    - name: Upload integration test results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: integration-test-results
        path: frontend/test-results/

  test-e2e:
    name: E2E Tests
    runs-on: ${{ matrix.os }}
    needs: test-unit
    
    strategy:
      matrix:
        os: [ubuntu-latest, windows-latest, macos-latest]
        browser: [chromium, firefox, webkit]
        exclude:
          # WebKit is not available on Linux in Playwright
          - os: ubuntu-latest
            browser: webkit
          # Firefox has issues on Windows in CI
          - os: windows-latest
            browser: firefox
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: frontend/package-lock.json
        
    - name: Install dependencies
      working-directory: frontend
      run: npm ci
      
    - name: Install Playwright browsers
      working-directory: frontend
      run: npx playwright install ${{ matrix.browser }} --with-deps
      
    - name: Build application
      working-directory: frontend
      run: npm run build
      
    - name: Run E2E tests
      working-directory: frontend
      run: npx playwright test --project=${{ matrix.browser }}
      env:
        CI: true
        
    - name: Upload E2E test results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: e2e-results-${{ matrix.os }}-${{ matrix.browser }}
        path: |
          frontend/test-results/
          frontend/playwright-report/

  test-electron:
    name: Electron Tests
    runs-on: ${{ matrix.os }}
    needs: test-unit
    
    strategy:
      matrix:
        os: [ubuntu-latest, windows-latest, macos-latest]
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: frontend/package-lock.json
        
    - name: Install dependencies
      working-directory: frontend
      run: npm ci
      
    - name: Install Electron dependencies
      working-directory: frontend
      run: npm run postinstall
      
    - name: Cache Electron binaries
      uses: actions/cache@v3
      with:
        path: ${{ env.ELECTRON_CACHE }}
        key: ${{ runner.os }}-electron-${{ hashFiles('**/package-lock.json') }}
        
    - name: Build Electron app
      working-directory: frontend
      run: npm run build:electron
      
    - name: Run Electron tests
      working-directory: frontend
      run: npm run test:electron
      env:
        CI: true
        DISPLAY: :99.0
      
    - name: Upload Electron test results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: electron-test-results-${{ matrix.os }}
        path: frontend/electron-test-results/

  security-scan:
    name: Security Scanning
    runs-on: ubuntu-latest
    needs: test-unit
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: frontend/package-lock.json
        
    - name: Install dependencies
      working-directory: frontend
      run: npm ci
      
    - name: Run security audit
      working-directory: frontend
      run: npm audit --audit-level high
      
    - name: Run dependency vulnerability scan
      uses: securecodewarrior/github-action-add-sarif@v1
      with:
        sarif-file: 'security-scan-results.sarif'
        
    - name: Run SAST scan
      uses: github/codeql-action/analyze@v2
      with:
        languages: javascript,typescript
        
    - name: Upload security scan results
      uses: github/codeql-action/upload-sarif@v2
      if: always()
      with:
        sarif_file: security-scan-results.sarif

  performance-test:
    name: Performance Testing
    runs-on: ubuntu-latest
    needs: [test-integration]
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: frontend/package-lock.json
        
    - name: Install dependencies
      working-directory: frontend
      run: npm ci
      
    - name: Build application
      working-directory: frontend
      run: npm run build
      
    - name: Run performance tests
      working-directory: frontend
      run: npm run test:performance
      
    - name: Run bundle size analysis
      working-directory: frontend
      run: npm run analyze:bundle
      
    - name: Upload performance results
      uses: actions/upload-artifact@v3
      with:
        name: performance-results
        path: |
          frontend/performance-results/
          frontend/bundle-analysis/

  build:
    name: Build Application
    runs-on: ${{ matrix.os }}
    needs: [test-unit, test-integration]
    
    strategy:
      matrix:
        os: [ubuntu-latest, windows-latest, macos-latest]
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: frontend/package-lock.json
        
    - name: Install dependencies
      working-directory: frontend
      run: npm ci
      
    - name: Cache Electron
      uses: actions/cache@v3
      with:
        path: ${{ env.ELECTRON_CACHE }}
        key: ${{ runner.os }}-electron-${{ hashFiles('**/package-lock.json') }}
        
    - name: Cache Electron Builder
      uses: actions/cache@v3
      with:
        path: ${{ env.ELECTRON_BUILDER_CACHE }}
        key: ${{ runner.os }}-electron-builder-${{ hashFiles('**/package-lock.json') }}
        
    - name: Build application
      working-directory: frontend
      run: npm run build:prod
      env:
        CI: true
        GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        
    - name: Upload build artifacts
      uses: actions/upload-artifact@v3
      with:
        name: build-${{ matrix.os }}
        path: |
          frontend/dist-electron/
          frontend/dist/

  release:
    name: Release
    runs-on: ${{ matrix.os }}
    needs: [test-e2e, test-electron, security-scan, performance-test, build]
    if: github.event_name == 'release'
    
    strategy:
      matrix:
        os: [ubuntu-latest, windows-latest, macos-latest]
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: frontend/package-lock.json
        
    - name: Install dependencies
      working-directory: frontend
      run: npm ci
      
    - name: Setup code signing (Windows)
      if: matrix.os == 'windows-latest'
      env:
        WINDOWS_CERTIFICATE: ${{ secrets.WINDOWS_CERTIFICATE }}
        WINDOWS_CERTIFICATE_PASSWORD: ${{ secrets.WINDOWS_CERTIFICATE_PASSWORD }}
      run: |
        echo "$WINDOWS_CERTIFICATE" | base64 --decode > certificate.p12
        echo "CSC_LINK=$PWD/certificate.p12" >> $GITHUB_ENV
        echo "CSC_KEY_PASSWORD=$WINDOWS_CERTIFICATE_PASSWORD" >> $GITHUB_ENV
        
    - name: Setup code signing (macOS)
      if: matrix.os == 'macos-latest'
      env:
        APPLE_CERTIFICATE: ${{ secrets.APPLE_CERTIFICATE }}
        APPLE_CERTIFICATE_PASSWORD: ${{ secrets.APPLE_CERTIFICATE_PASSWORD }}
        APPLE_ID: ${{ secrets.APPLE_ID }}
        APPLE_ID_PASSWORD: ${{ secrets.APPLE_ID_PASSWORD }}
        APPLE_TEAM_ID: ${{ secrets.APPLE_TEAM_ID }}
      run: |
        echo "$APPLE_CERTIFICATE" | base64 --decode > certificate.p12
        security create-keychain -p temp build.keychain
        security default-keychain -s build.keychain
        security unlock-keychain -p temp build.keychain
        security import certificate.p12 -k build.keychain -P "$APPLE_CERTIFICATE_PASSWORD" -T /usr/bin/codesign
        security set-key-partition-list -S apple-tool:,apple:,codesign: -s -k temp build.keychain
        echo "CSC_LINK=$PWD/certificate.p12" >> $GITHUB_ENV
        echo "CSC_KEY_PASSWORD=$APPLE_CERTIFICATE_PASSWORD" >> $GITHUB_ENV
        echo "APPLE_ID=$APPLE_ID" >> $GITHUB_ENV
        echo "APPLE_ID_PASSWORD=$APPLE_ID_PASSWORD" >> $GITHUB_ENV
        echo "APPLE_TEAM_ID=$APPLE_TEAM_ID" >> $GITHUB_ENV
        
    - name: Build and release
      working-directory: frontend
      run: npm run dist
      env:
        CI: true
        GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        
    - name: Upload release artifacts
      uses: actions/upload-artifact@v3
      with:
        name: release-${{ matrix.os }}
        path: frontend/dist-electron/

  deploy-staging:
    name: Deploy to Staging
    runs-on: ubuntu-latest
    needs: [build]
    if: github.ref == 'refs/heads/develop'
    
    environment:
      name: staging
      url: https://staging.nexusscan.ai
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Download build artifacts
      uses: actions/download-artifact@v3
      with:
        name: build-ubuntu-latest
        path: ./dist
        
    - name: Deploy to staging
      env:
        STAGING_DEPLOY_KEY: ${{ secrets.STAGING_DEPLOY_KEY }}
        STAGING_HOST: ${{ secrets.STAGING_HOST }}
      run: |
        echo "$STAGING_DEPLOY_KEY" > deploy_key
        chmod 600 deploy_key
        rsync -avz -e "ssh -i deploy_key -o StrictHostKeyChecking=no" \
          ./dist/ nexusscan@$STAGING_HOST:/var/www/staging/
        
    - name: Run smoke tests
      run: |
        sleep 30
        curl -f https://staging.nexusscan.ai/health || exit 1

  deploy-production:
    name: Deploy to Production
    runs-on: ubuntu-latest
    needs: [release]
    if: github.event_name == 'release'
    
    environment:
      name: production
      url: https://nexusscan.ai
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Download release artifacts
      uses: actions/download-artifact@v3
      with:
        pattern: release-*
        path: ./releases
        
    - name: Create release manifest
      run: |
        echo "Release: ${{ github.event.release.tag_name }}" > release-manifest.txt
        echo "Commit: ${{ github.sha }}" >> release-manifest.txt
        echo "Date: $(date -Iseconds)" >> release-manifest.txt
        echo "Artifacts:" >> release-manifest.txt
        find ./releases -name "*.exe" -o -name "*.dmg" -o -name "*.AppImage" -o -name "*.deb" | \
          xargs -I {} sh -c 'echo "  - $(basename {}): $(sha256sum {} | cut -d" " -f1)"' >> release-manifest.txt
        
    - name: Update release with artifacts
      uses: softprops/action-gh-release@v1
      with:
        files: |
          ./releases/**/*.exe
          ./releases/**/*.dmg
          ./releases/**/*.AppImage
          ./releases/**/*.deb
          ./releases/**/*.rpm
          release-manifest.txt
        body_path: release-manifest.txt
        
    - name: Deploy auto-updater
      env:
        PRODUCTION_DEPLOY_KEY: ${{ secrets.PRODUCTION_DEPLOY_KEY }}
        PRODUCTION_HOST: ${{ secrets.PRODUCTION_HOST }}
      run: |
        echo "$PRODUCTION_DEPLOY_KEY" > deploy_key
        chmod 600 deploy_key
        scp -i deploy_key -o StrictHostKeyChecking=no \
          ./releases/latest*.yml nexusscan@$PRODUCTION_HOST:/var/www/updates/
        
    - name: Notify deployment
      uses: 8398a7/action-slack@v3
      with:
        status: success
        channel: '#deployments'
        text: "NexusScan Desktop ${{ github.event.release.tag_name }} deployed to production"
      env:
        SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}

  notify:
    name: Notify Results
    runs-on: ubuntu-latest
    needs: [test-unit, test-integration, test-e2e, test-electron, security-scan, performance-test]
    if: always()
    
    steps:
    - name: Notify Slack on failure
      if: contains(needs.*.result, 'failure')
      uses: 8398a7/action-slack@v3
      with:
        status: failure
        channel: '#ci-cd'
        text: "NexusScan Desktop CI/CD pipeline failed"
      env:
        SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
        
    - name: Notify Slack on success
      if: ${{ !contains(needs.*.result, 'failure') && !contains(needs.*.result, 'cancelled') }}
      uses: 8398a7/action-slack@v3
      with:
        status: success
        channel: '#ci-cd'
        text: "NexusScan Desktop CI/CD pipeline completed successfully"
      env:
        SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}