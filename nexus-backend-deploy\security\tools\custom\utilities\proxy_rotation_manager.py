#!/usr/bin/env python3
"""
Live Proxy Rotation System for NexusScan Desktop
Advanced proxy management with global network access, quality assessment, and intelligent rotation.
"""

import asyncio
import logging
import json
import time
import random
import hashlib
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple, Set
from dataclasses import dataclass, asdict
from enum import Enum
import aiohttp
import socket
import ssl
from urllib.parse import urlparse
import ipaddress
import re

from core.config import Config
from core.database import DatabaseManager

logger = logging.getLogger(__name__)


class ProxyType(Enum):
    """Types of proxy protocols"""
    HTTP = "http"
    HTTPS = "https"
    SOCKS4 = "socks4"
    SOCKS5 = "socks5"


class ProxyAnonymity(Enum):
    """Proxy anonymity levels"""
    TRANSPARENT = "transparent"
    ANONYMOUS = "anonymous"
    ELITE = "elite"


class ProxyStatus(Enum):
    """Proxy status"""
    ACTIVE = "active"
    INACTIVE = "inactive"
    TESTING = "testing"
    FAILED = "failed"
    BANNED = "banned"


@dataclass
class ProxyInfo:
    """Proxy information"""
    id: str
    host: str
    port: int
    proxy_type: ProxyType
    anonymity: ProxyAnonymity
    country: str
    city: str
    provider: str
    username: Optional[str] = None
    password: Optional[str] = None
    last_tested: Optional[datetime] = None
    response_time: float = 0.0
    success_rate: float = 0.0
    status: ProxyStatus = ProxyStatus.INACTIVE
    failure_count: int = 0
    detection_score: float = 0.0  # Higher = more likely to be detected
    bandwidth_score: float = 0.0  # Higher = better bandwidth
    reliability_score: float = 0.0  # Higher = more reliable


@dataclass
class ProxyTestResult:
    """Result of proxy testing"""
    proxy_id: str
    success: bool
    response_time: float
    anonymity_verified: bool
    ip_leaked: bool
    detected_headers: List[str]
    error_message: Optional[str]
    test_timestamp: datetime


@dataclass
class RotationStrategy:
    """Proxy rotation strategy configuration"""
    rotation_interval: int  # seconds
    max_requests_per_proxy: int
    failure_threshold: int
    detection_threshold: float
    geographic_preference: List[str]  # Preferred countries
    anonymity_requirement: ProxyAnonymity
    auto_ban_on_detection: bool


class ProxyRotationManager:
    """Advanced proxy rotation and management system"""

    def __init__(self, config: Config, database: DatabaseManager):
        """Initialize proxy rotation manager"""
        self.config = config
        self.database = database
        
        # Proxy storage
        self.proxies: Dict[str, ProxyInfo] = {}
        self.active_proxies: List[str] = []
        self.current_proxy_index = 0
        self.current_proxy: Optional[ProxyInfo] = None
        
        # Proxy sources
        self.proxy_sources = self._initialize_proxy_sources()
        
        # Testing configuration
        self.test_urls = [
            "http://httpbin.org/ip",
            "https://api.ipify.org?format=json",
            "http://icanhazip.com",
            "https://checkip.amazonaws.com"
        ]
        
        # Statistics tracking
        self.usage_stats = {
            "total_requests": 0,
            "successful_requests": 0,
            "failed_requests": 0,
            "proxies_tested": 0,
            "proxies_active": 0,
            "rotation_count": 0
        }
        
        # Rotation strategy
        self.rotation_strategy = RotationStrategy(
            rotation_interval=300,  # 5 minutes
            max_requests_per_proxy=100,
            failure_threshold=5,
            detection_threshold=0.7,
            geographic_preference=["US", "UK", "CA", "DE"],
            anonymity_requirement=ProxyAnonymity.ANONYMOUS,
            auto_ban_on_detection=True
        )
        
        # Background tasks
        self.rotation_task: Optional[asyncio.Task] = None
        self.testing_task: Optional[asyncio.Task] = None
        self.harvesting_task: Optional[asyncio.Task] = None
        
        logger.info("Proxy Rotation Manager initialized")

    def _initialize_proxy_sources(self) -> List[Dict[str, Any]]:
        """Initialize proxy source configurations"""
        return [
            {
                "name": "Free Proxy List",
                "url": "https://www.proxy-list.download/api/v1/get?type=http",
                "type": "api",
                "format": "json",
                "rate_limit": 60,  # seconds between requests
                "anonymity": ProxyAnonymity.ANONYMOUS
            },
            {
                "name": "ProxyScrape",
                "url": "https://api.proxyscrape.com/v2/?request=get&protocol=http&timeout=10000&country=all",
                "type": "api",
                "format": "text",
                "rate_limit": 300,
                "anonymity": ProxyAnonymity.ANONYMOUS
            },
            {
                "name": "GitHub Proxy Lists",
                "url": "https://raw.githubusercontent.com/TheSpeedX/PROXY-List/master/http.txt",
                "type": "github",
                "format": "text",
                "rate_limit": 3600,
                "anonymity": ProxyAnonymity.TRANSPARENT
            },
            {
                "name": "PubProxy",
                "url": "http://pubproxy.com/api/proxy?limit=20&format=json&type=http",
                "type": "api",
                "format": "json",
                "rate_limit": 120,
                "anonymity": ProxyAnonymity.ANONYMOUS
            }
        ]

    async def start_rotation_system(self):
        """Start the proxy rotation system"""
        try:
            # Start background tasks
            self.rotation_task = asyncio.create_task(self._rotation_loop())
            self.testing_task = asyncio.create_task(self._testing_loop())
            self.harvesting_task = asyncio.create_task(self._harvesting_loop())
            
            # Initial proxy harvest
            await self.fetch_live_proxies()
            
            logger.info("Proxy rotation system started")
            
        except Exception as e:
            logger.error(f"Failed to start proxy rotation system: {e}")

    async def stop_rotation_system(self):
        """Stop the proxy rotation system"""
        try:
            # Cancel background tasks
            if self.rotation_task:
                self.rotation_task.cancel()
            if self.testing_task:
                self.testing_task.cancel()
            if self.harvesting_task:
                self.harvesting_task.cancel()
            
            logger.info("Proxy rotation system stopped")
            
        except Exception as e:
            logger.error(f"Failed to stop proxy rotation system: {e}")

    async def fetch_live_proxies(self, sources: List[str] = None) -> Dict[str, int]:
        """Fetch live proxies from multiple sources"""
        
        if sources is None:
            sources = ['free', 'api', 'github']
        
        results = {}
        
        try:
            # Fetch from each source
            for source_config in self.proxy_sources:
                source_name = source_config["name"]
                
                try:
                    if source_config["type"] in sources:
                        proxies = await self._fetch_from_source(source_config)
                        
                        # Add proxies to collection
                        added_count = 0
                        for proxy in proxies:
                            if await self._add_proxy(proxy):
                                added_count += 1
                        
                        results[source_name] = added_count
                        logger.info(f"Fetched {added_count} proxies from {source_name}")
                        
                except Exception as e:
                    logger.error(f"Failed to fetch from {source_name}: {e}")
                    results[source_name] = 0
            
            # Update statistics
            self.usage_stats["proxies_tested"] += sum(results.values())
            
            return results
            
        except Exception as e:
            logger.error(f"Proxy fetching failed: {e}")
            return {}

    async def _fetch_from_source(self, source_config: Dict[str, Any]) -> List[ProxyInfo]:
        """Fetch proxies from a specific source"""
        proxies = []
        
        try:
            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=30)) as session:
                async with session.get(source_config["url"]) as response:
                    if response.status == 200:
                        content = await response.text()
                        
                        if source_config["format"] == "json":
                            proxies = self._parse_json_proxies(content, source_config)
                        elif source_config["format"] == "text":
                            proxies = self._parse_text_proxies(content, source_config)
            
        except Exception as e:
            logger.error(f"Failed to fetch from source: {e}")
        
        return proxies

    def _parse_json_proxies(self, content: str, source_config: Dict[str, Any]) -> List[ProxyInfo]:
        """Parse JSON format proxy response"""
        proxies = []
        
        try:
            data = json.loads(content)
            
            # Handle different JSON structures
            if isinstance(data, list):
                proxy_list = data
            elif "data" in data:
                proxy_list = data["data"]
            elif "proxies" in data:
                proxy_list = data["proxies"]
            else:
                proxy_list = [data]
            
            for proxy_data in proxy_list:
                try:
                    # Extract proxy information
                    if isinstance(proxy_data, str):
                        # Format: "host:port"
                        host, port = proxy_data.split(':')
                        proxy_info = ProxyInfo(
                            id=self._generate_proxy_id(host, port),
                            host=host.strip(),
                            port=int(port.strip()),
                            proxy_type=ProxyType.HTTP,
                            anonymity=source_config.get("anonymity", ProxyAnonymity.TRANSPARENT),
                            country="Unknown",
                            city="Unknown",
                            provider=source_config["name"]
                        )
                    else:
                        # JSON object with detailed info
                        proxy_info = ProxyInfo(
                            id=self._generate_proxy_id(proxy_data.get("ip", ""), proxy_data.get("port", "")),
                            host=proxy_data.get("ip", proxy_data.get("host", "")),
                            port=int(proxy_data.get("port", 0)),
                            proxy_type=ProxyType(proxy_data.get("type", "http").lower()),
                            anonymity=ProxyAnonymity(proxy_data.get("anonymity", "transparent").lower()),
                            country=proxy_data.get("country", "Unknown"),
                            city=proxy_data.get("city", "Unknown"),
                            provider=source_config["name"]
                        )
                    
                    if proxy_info.host and proxy_info.port:
                        proxies.append(proxy_info)
                        
                except Exception as e:
                    logger.debug(f"Failed to parse proxy data: {e}")
                    continue
                    
        except Exception as e:
            logger.error(f"JSON proxy parsing failed: {e}")
        
        return proxies

    def _parse_text_proxies(self, content: str, source_config: Dict[str, Any]) -> List[ProxyInfo]:
        """Parse text format proxy response"""
        proxies = []
        
        try:
            lines = content.strip().split('\n')
            
            for line in lines:
                line = line.strip()
                if not line or line.startswith('#'):
                    continue
                
                try:
                    # Format: "host:port" or "host:port:username:password"
                    parts = line.split(':')
                    if len(parts) >= 2:
                        host = parts[0].strip()
                        port = int(parts[1].strip())
                        username = parts[2].strip() if len(parts) > 2 else None
                        password = parts[3].strip() if len(parts) > 3 else None
                        
                        proxy_info = ProxyInfo(
                            id=self._generate_proxy_id(host, port),
                            host=host,
                            port=port,
                            proxy_type=ProxyType.HTTP,
                            anonymity=source_config.get("anonymity", ProxyAnonymity.TRANSPARENT),
                            country="Unknown",
                            city="Unknown",
                            provider=source_config["name"],
                            username=username,
                            password=password
                        )
                        
                        proxies.append(proxy_info)
                        
                except Exception as e:
                    logger.debug(f"Failed to parse proxy line '{line}': {e}")
                    continue
                    
        except Exception as e:
            logger.error(f"Text proxy parsing failed: {e}")
        
        return proxies

    def _generate_proxy_id(self, host: str, port: Any) -> str:
        """Generate unique proxy ID"""
        return hashlib.md5(f"{host}:{port}".encode()).hexdigest()[:12]

    async def _add_proxy(self, proxy: ProxyInfo) -> bool:
        """Add proxy to collection if valid"""
        try:
            # Validate proxy
            if not self._validate_proxy(proxy):
                return False
            
            # Check if already exists
            if proxy.id in self.proxies:
                return False
            
            # Add to collection
            self.proxies[proxy.id] = proxy
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to add proxy: {e}")
            return False

    def _validate_proxy(self, proxy: ProxyInfo) -> bool:
        """Validate proxy information"""
        try:
            # Check host format
            if not proxy.host:
                return False
            
            # Validate IP address or hostname
            try:
                ipaddress.ip_address(proxy.host)
            except ValueError:
                # Not an IP, check if valid hostname
                if not re.match(r'^[a-zA-Z0-9.-]+$', proxy.host):
                    return False
            
            # Check port range
            if not (1 <= proxy.port <= 65535):
                return False
            
            return True
            
        except Exception:
            return False

    async def test_proxy_anonymity(self, proxy_list: List[str]) -> Dict[str, ProxyTestResult]:
        """Test proxy quality and anonymity"""
        results = {}
        
        for proxy_id in proxy_list[:20]:  # Limit concurrent tests
            if proxy_id in self.proxies:
                proxy = self.proxies[proxy_id]
                result = await self._test_single_proxy(proxy)
                results[proxy_id] = result
                
                # Update proxy based on test result
                await self._update_proxy_from_test(proxy, result)
        
        return results

    async def _test_single_proxy(self, proxy: ProxyInfo) -> ProxyTestResult:
        """Test a single proxy for functionality and anonymity"""
        
        start_time = time.time()
        
        try:
            # Create proxy connector
            connector = aiohttp.ProxyConnector(
                proxy_url=f"{proxy.proxy_type.value}://{proxy.host}:{proxy.port}",
                proxy_auth=aiohttp.BasicAuth(proxy.username, proxy.password) if proxy.username else None
            )
            
            async with aiohttp.ClientSession(
                connector=connector,
                timeout=aiohttp.ClientTimeout(total=10)
            ) as session:
                
                # Test basic connectivity
                test_url = random.choice(self.test_urls)
                async with session.get(test_url) as response:
                    if response.status != 200:
                        raise Exception(f"HTTP {response.status}")
                    
                    response_data = await response.text()
                    response_time = time.time() - start_time
                    
                    # Check anonymity
                    anonymity_result = await self._check_anonymity(session, response_data)
                    
                    return ProxyTestResult(
                        proxy_id=proxy.id,
                        success=True,
                        response_time=response_time,
                        anonymity_verified=anonymity_result["anonymous"],
                        ip_leaked=anonymity_result["ip_leaked"],
                        detected_headers=anonymity_result["detected_headers"],
                        error_message=None,
                        test_timestamp=datetime.now()
                    )
                    
        except Exception as e:
            response_time = time.time() - start_time
            return ProxyTestResult(
                proxy_id=proxy.id,
                success=False,
                response_time=response_time,
                anonymity_verified=False,
                ip_leaked=True,
                detected_headers=[],
                error_message=str(e),
                test_timestamp=datetime.now()
            )

    async def _check_anonymity(self, session: aiohttp.ClientSession, response_data: str) -> Dict[str, Any]:
        """Check proxy anonymity level"""
        
        try:
            # Get real IP for comparison
            real_ip = await self._get_real_ip()
            
            # Extract IP from response
            proxy_ip = self._extract_ip_from_response(response_data)
            
            # Check for IP leakage
            ip_leaked = real_ip == proxy_ip
            
            # Check for proxy detection headers
            detected_headers = []
            
            # Test for common proxy detection methods
            try:
                async with session.get("http://httpbin.org/headers") as response:
                    headers_data = await response.json()
                    headers = headers_data.get("headers", {})
                    
                    # Look for proxy-related headers
                    proxy_headers = [
                        "X-Forwarded-For", "X-Real-IP", "X-Proxy-ID",
                        "Via", "X-Forwarded-Proto", "Proxy-Connection"
                    ]
                    
                    for header in proxy_headers:
                        if header in headers:
                            detected_headers.append(header)
                            
            except Exception:
                pass
            
            # Determine anonymity level
            anonymous = not ip_leaked and len(detected_headers) == 0
            
            return {
                "anonymous": anonymous,
                "ip_leaked": ip_leaked,
                "detected_headers": detected_headers,
                "proxy_ip": proxy_ip,
                "real_ip": real_ip
            }
            
        except Exception as e:
            logger.error(f"Anonymity check failed: {e}")
            return {
                "anonymous": False,
                "ip_leaked": True,
                "detected_headers": [],
                "proxy_ip": None,
                "real_ip": None
            }

    async def _get_real_ip(self) -> str:
        """Get real IP address"""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get("https://api.ipify.org?format=json") as response:
                    data = await response.json()
                    return data.get("ip", "unknown")
        except Exception:
            return "unknown"

    def _extract_ip_from_response(self, response_data: str) -> str:
        """Extract IP address from response data"""
        try:
            # Try JSON format first
            try:
                data = json.loads(response_data)
                if "ip" in data:
                    return data["ip"]
                elif "origin" in data:
                    return data["origin"]
            except json.JSONDecodeError:
                pass
            
            # Try to find IP in text
            ip_pattern = r'\b(?:[0-9]{1,3}\.){3}[0-9]{1,3}\b'
            matches = re.findall(ip_pattern, response_data)
            if matches:
                return matches[0]
            
            return "unknown"
            
        except Exception:
            return "unknown"

    async def _update_proxy_from_test(self, proxy: ProxyInfo, result: ProxyTestResult):
        """Update proxy information based on test result"""
        try:
            proxy.last_tested = result.test_timestamp
            proxy.response_time = result.response_time
            
            if result.success:
                proxy.status = ProxyStatus.ACTIVE
                proxy.failure_count = 0
                
                # Update success rate
                if proxy.success_rate == 0.0:
                    proxy.success_rate = 1.0
                else:
                    proxy.success_rate = (proxy.success_rate + 1.0) / 2.0
                
                # Update anonymity
                if result.anonymity_verified:
                    proxy.anonymity = ProxyAnonymity.ANONYMOUS
                elif result.ip_leaked:
                    proxy.anonymity = ProxyAnonymity.TRANSPARENT
                else:
                    proxy.anonymity = ProxyAnonymity.ELITE
                
                # Calculate scores
                proxy.reliability_score = proxy.success_rate
                proxy.bandwidth_score = max(0.0, 1.0 - (result.response_time / 10.0))
                proxy.detection_score = len(result.detected_headers) / 10.0
                
                # Add to active proxies if not already there
                if proxy.id not in self.active_proxies:
                    self.active_proxies.append(proxy.id)
                    
            else:
                proxy.failure_count += 1
                proxy.success_rate = max(0.0, proxy.success_rate - 0.1)
                
                if proxy.failure_count >= self.rotation_strategy.failure_threshold:
                    proxy.status = ProxyStatus.FAILED
                    # Remove from active proxies
                    if proxy.id in self.active_proxies:
                        self.active_proxies.remove(proxy.id)
            
            # Update statistics
            self.usage_stats["proxies_active"] = len(self.active_proxies)
            
        except Exception as e:
            logger.error(f"Failed to update proxy from test: {e}")

    async def rotate_based_on_detection(self, current_scan_results: Dict[str, Any]) -> bool:
        """Rotate proxy based on detection indicators"""
        
        try:
            if not self.current_proxy:
                return await self.rotate_to_next_proxy()
            
            # Check for detection indicators
            detection_indicators = current_scan_results.get("detection_indicators", [])
            
            detection_score = 0.0
            
            # Check common detection indicators
            if "403" in str(current_scan_results.get("status_codes", [])):
                detection_score += 0.3
            
            if "429" in str(current_scan_results.get("status_codes", [])):
                detection_score += 0.4
            
            if any(indicator in str(current_scan_results).lower() 
                   for indicator in ["blocked", "banned", "suspicious"]):
                detection_score += 0.5
            
            # Update proxy detection score
            self.current_proxy.detection_score = max(
                self.current_proxy.detection_score, 
                detection_score
            )
            
            # Rotate if detection threshold exceeded
            if detection_score >= self.rotation_strategy.detection_threshold:
                logger.warning(f"Proxy {self.current_proxy.id} detected, rotating")
                
                if self.rotation_strategy.auto_ban_on_detection:
                    self.current_proxy.status = ProxyStatus.BANNED
                    if self.current_proxy.id in self.active_proxies:
                        self.active_proxies.remove(self.current_proxy.id)
                
                return await self.rotate_to_next_proxy()
            
            return False
            
        except Exception as e:
            logger.error(f"Detection-based rotation failed: {e}")
            return False

    async def geo_locate_and_select(self, target_country: str) -> bool:
        """Select proxy based on geographic location"""
        
        try:
            # Find proxies in target country
            country_proxies = [
                proxy_id for proxy_id, proxy in self.proxies.items()
                if proxy.country.upper() == target_country.upper() and proxy.status == ProxyStatus.ACTIVE
            ]
            
            if not country_proxies:
                logger.warning(f"No active proxies found in {target_country}")
                return False
            
            # Select best proxy in country
            best_proxy_id = max(country_proxies, key=lambda pid: self._calculate_proxy_score(self.proxies[pid]))
            
            # Switch to selected proxy
            self.current_proxy = self.proxies[best_proxy_id]
            self.current_proxy_index = self.active_proxies.index(best_proxy_id) if best_proxy_id in self.active_proxies else 0
            
            logger.info(f"Selected proxy in {target_country}: {self.current_proxy.host}:{self.current_proxy.port}")
            return True
            
        except Exception as e:
            logger.error(f"Geographic proxy selection failed: {e}")
            return False

    def _calculate_proxy_score(self, proxy: ProxyInfo) -> float:
        """Calculate overall proxy quality score"""
        try:
            # Weighted scoring
            reliability_weight = 0.4
            bandwidth_weight = 0.3
            anonymity_weight = 0.2
            detection_weight = 0.1
            
            anonymity_score = {
                ProxyAnonymity.TRANSPARENT: 0.3,
                ProxyAnonymity.ANONYMOUS: 0.7,
                ProxyAnonymity.ELITE: 1.0
            }.get(proxy.anonymity, 0.0)
            
            # Invert detection score (lower is better)
            detection_score = max(0.0, 1.0 - proxy.detection_score)
            
            total_score = (
                proxy.reliability_score * reliability_weight +
                proxy.bandwidth_score * bandwidth_weight +
                anonymity_score * anonymity_weight +
                detection_score * detection_weight
            )
            
            return total_score
            
        except Exception:
            return 0.0

    async def rotate_to_next_proxy(self) -> bool:
        """Rotate to the next available proxy"""
        
        try:
            if not self.active_proxies:
                logger.warning("No active proxies available for rotation")
                return False
            
            # Move to next proxy
            self.current_proxy_index = (self.current_proxy_index + 1) % len(self.active_proxies)
            proxy_id = self.active_proxies[self.current_proxy_index]
            self.current_proxy = self.proxies[proxy_id]
            
            # Update statistics
            self.usage_stats["rotation_count"] += 1
            
            logger.info(f"Rotated to proxy: {self.current_proxy.host}:{self.current_proxy.port}")
            return True
            
        except Exception as e:
            logger.error(f"Proxy rotation failed: {e}")
            return False

    async def get_current_proxy(self) -> Optional[ProxyInfo]:
        """Get current active proxy"""
        return self.current_proxy

    async def get_proxy_for_request(self) -> Optional[Dict[str, str]]:
        """Get proxy configuration for HTTP request"""
        
        try:
            if not self.current_proxy or self.current_proxy.status != ProxyStatus.ACTIVE:
                await self.rotate_to_next_proxy()
            
            if not self.current_proxy:
                return None
            
            proxy_url = f"{self.current_proxy.proxy_type.value}://{self.current_proxy.host}:{self.current_proxy.port}"
            
            proxy_config = {
                "http": proxy_url,
                "https": proxy_url
            }
            
            # Add authentication if available
            if self.current_proxy.username and self.current_proxy.password:
                auth_url = f"{self.current_proxy.proxy_type.value}://{self.current_proxy.username}:{self.current_proxy.password}@{self.current_proxy.host}:{self.current_proxy.port}"
                proxy_config = {
                    "http": auth_url,
                    "https": auth_url
                }
            
            return proxy_config
            
        except Exception as e:
            logger.error(f"Failed to get proxy for request: {e}")
            return None

    async def _rotation_loop(self):
        """Background loop for automatic proxy rotation"""
        
        while True:
            try:
                await asyncio.sleep(self.rotation_strategy.rotation_interval)
                
                if self.active_proxies:
                    await self.rotate_to_next_proxy()
                    
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Rotation loop error: {e}")

    async def _testing_loop(self):
        """Background loop for proxy testing"""
        
        while True:
            try:
                await asyncio.sleep(600)  # Test every 10 minutes
                
                # Test random subset of proxies
                proxy_ids = list(self.proxies.keys())
                if proxy_ids:
                    sample_size = min(20, len(proxy_ids))
                    test_proxies = random.sample(proxy_ids, sample_size)
                    await self.test_proxy_anonymity(test_proxies)
                    
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Testing loop error: {e}")

    async def _harvesting_loop(self):
        """Background loop for proxy harvesting"""
        
        while True:
            try:
                await asyncio.sleep(3600)  # Harvest every hour
                
                await self.fetch_live_proxies()
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Harvesting loop error: {e}")

    def get_proxy_statistics(self) -> Dict[str, Any]:
        """Get proxy system statistics"""
        
        active_count = len([p for p in self.proxies.values() if p.status == ProxyStatus.ACTIVE])
        failed_count = len([p for p in self.proxies.values() if p.status == ProxyStatus.FAILED])
        banned_count = len([p for p in self.proxies.values() if p.status == ProxyStatus.BANNED])
        
        countries = {}
        for proxy in self.proxies.values():
            countries[proxy.country] = countries.get(proxy.country, 0) + 1
        
        anonymity_levels = {}
        for proxy in self.proxies.values():
            level = proxy.anonymity.value
            anonymity_levels[level] = anonymity_levels.get(level, 0) + 1
        
        return {
            "total_proxies": len(self.proxies),
            "active_proxies": active_count,
            "failed_proxies": failed_count,
            "banned_proxies": banned_count,
            "current_proxy": f"{self.current_proxy.host}:{self.current_proxy.port}" if self.current_proxy else None,
            "rotation_count": self.usage_stats["rotation_count"],
            "countries": countries,
            "anonymity_levels": anonymity_levels,
            "average_response_time": sum(p.response_time for p in self.proxies.values()) / len(self.proxies) if self.proxies else 0,
            "usage_stats": self.usage_stats
        }

    def get_active_proxies(self) -> List[ProxyInfo]:
        """Get list of active proxies"""
        return [self.proxies[proxy_id] for proxy_id in self.active_proxies if proxy_id in self.proxies]

    def update_rotation_strategy(self, strategy: RotationStrategy):
        """Update rotation strategy"""
        self.rotation_strategy = strategy
        logger.info("Proxy rotation strategy updated")

    async def clear_failed_proxies(self):
        """Remove failed and banned proxies"""
        
        try:
            removed_count = 0
            
            for proxy_id in list(self.proxies.keys()):
                proxy = self.proxies[proxy_id]
                if proxy.status in [ProxyStatus.FAILED, ProxyStatus.BANNED]:
                    del self.proxies[proxy_id]
                    if proxy_id in self.active_proxies:
                        self.active_proxies.remove(proxy_id)
                    removed_count += 1
            
            logger.info(f"Cleared {removed_count} failed/banned proxies")
            
        except Exception as e:
            logger.error(f"Failed to clear failed proxies: {e}")

    async def export_proxy_list(self, format: str = "json") -> str:
        """Export proxy list in specified format"""
        
        try:
            active_proxies = self.get_active_proxies()
            
            if format.lower() == "json":
                return json.dumps([asdict(proxy) for proxy in active_proxies], indent=2, default=str)
            
            elif format.lower() == "text":
                lines = []
                for proxy in active_proxies:
                    if proxy.username and proxy.password:
                        lines.append(f"{proxy.host}:{proxy.port}:{proxy.username}:{proxy.password}")
                    else:
                        lines.append(f"{proxy.host}:{proxy.port}")
                return "\n".join(lines)
            
            elif format.lower() == "csv":
                lines = ["host,port,type,anonymity,country,response_time,success_rate"]
                for proxy in active_proxies:
                    lines.append(f"{proxy.host},{proxy.port},{proxy.proxy_type.value},{proxy.anonymity.value},{proxy.country},{proxy.response_time:.2f},{proxy.success_rate:.2f}")
                return "\n".join(lines)
            
            else:
                raise ValueError(f"Unsupported format: {format}")
                
        except Exception as e:
            logger.error(f"Proxy export failed: {e}")
            return ""