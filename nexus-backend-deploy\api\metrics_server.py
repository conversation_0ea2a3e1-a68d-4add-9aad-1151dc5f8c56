#!/usr/bin/env python3
"""
Metrics HTTP Server for NexusScan Platform
Provides /metrics endpoint for Prometheus scraping
"""

import asyncio
import logging
import socket
from typing import Optional
from aiohttp import web, web_request, web_response
import json

from core.metrics import metrics
from core.health import HealthChecker, quick_health_check

logger = logging.getLogger(__name__)

def find_free_port(start_port: int = 8001, max_attempts: int = 10) -> int:
    """Find a free port starting from the given port"""
    for port in range(start_port, start_port + max_attempts):
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.bind(('', port))
                return port
        except OSError:
            continue
    raise RuntimeError(f"No free ports found in range {start_port}-{start_port + max_attempts - 1}")

class MetricsServer:
    """HTTP server for metrics and health endpoints"""
    
    def __init__(self, host: str = "0.0.0.0", port: int = 8001, config=None):
        self.host = host
        self.port = port
        self.config = config
        self.app: Optional[web.Application] = None
        self.runner: Optional[web.AppRunner] = None
        self.site: Optional[web.TCPSite] = None
        self.health_checker = HealthChecker(config)
    
    async def metrics_handler(self, request: web_request.Request) -> web_response.Response:
        """Handle /metrics endpoint for Prometheus scraping"""
        try:
            prometheus_format = metrics.get_prometheus_format()
            return web_response.Response(
                text=prometheus_format,
                content_type="text/plain; version=0.0.4"
            )
        except Exception as e:
            logger.error(f"Error generating metrics: {e}")
            return web_response.Response(
                text=f"# Error generating metrics: {e}",
                status=500,
                content_type="text/plain"
            )
    
    async def health_handler(self, request: web_request.Request) -> web_response.Response:
        """Handle /health endpoint"""
        try:
            health = await self.health_checker.check_system_health()
            health_dict = self.health_checker.to_dict(health)
            
            status_code = 200
            if health.overall_status.value == "unhealthy":
                status_code = 503
            elif health.overall_status.value == "degraded":
                status_code = 200  # Still OK, but degraded
                
            return web_response.Response(
                text=json.dumps(health_dict, indent=2),
                status=status_code,
                content_type="application/json"
            )
        except Exception as e:
            logger.error(f"Error in health check: {e}")
            return web_response.Response(
                text=json.dumps({
                    "error": str(e),
                    "overall_status": "unknown"
                }),
                status=500,
                content_type="application/json"
            )
    
    async def stats_handler(self, request: web_request.Request) -> web_response.Response:
        """Handle /stats endpoint for metrics statistics"""
        try:
            stats = metrics.get_stats()
            return web_response.Response(
                text=json.dumps(stats, indent=2),
                content_type="application/json"
            )
        except Exception as e:
            logger.error(f"Error generating stats: {e}")
            return web_response.Response(
                text=json.dumps({"error": str(e)}),
                status=500,
                content_type="application/json"
            )
    
    async def root_handler(self, request: web_request.Request) -> web_response.Response:
        """Handle root endpoint with available endpoints"""
        endpoints = {
            "service": "NexusScan Metrics Server",
            "version": "1.0.0",
            "endpoints": {
                "/metrics": "Prometheus metrics (text/plain)",
                "/health": "System health check (application/json)",
                "/stats": "Metrics collection statistics (application/json)"
            }
        }
        return web_response.Response(
            text=json.dumps(endpoints, indent=2),
            content_type="application/json"
        )
    
    def setup_routes(self):
        """Setup HTTP routes"""
        self.app.router.add_get("/", self.root_handler)
        self.app.router.add_get("/metrics", self.metrics_handler)
        self.app.router.add_get("/health", self.health_handler)
        self.app.router.add_get("/stats", self.stats_handler)
    
    async def start(self):
        """Start the metrics server"""
        try:
            # Create application
            self.app = web.Application()
            self.setup_routes()
            
            # Create runner
            self.runner = web.AppRunner(self.app)
            await self.runner.setup()
            
            # Try to create site with configured port
            try:
                self.site = web.TCPSite(self.runner, self.host, self.port)
                await self.site.start()
            except OSError as e:
                if "Address already in use" in str(e):
                    # Port is already in use, try to find a free port
                    logger.warning(f"Port {self.port} is already in use, finding a free port...")
                    free_port = find_free_port(self.port, 10)
                    self.port = free_port
                    self.site = web.TCPSite(self.runner, self.host, self.port)
                    await self.site.start()
                    logger.info(f"Using alternative port {self.port}")
                else:
                    raise
            
            logger.info(f"Metrics server started on http://{self.host}:{self.port}")
            logger.info("Available endpoints:")
            logger.info(f"  - http://{self.host}:{self.port}/metrics (Prometheus)")
            logger.info(f"  - http://{self.host}:{self.port}/health (Health check)")
            logger.info(f"  - http://{self.host}:{self.port}/stats (Statistics)")
            
        except Exception as e:
            logger.error(f"Failed to start metrics server: {e}")
            raise
    
    async def stop(self):
        """Stop the metrics server"""
        try:
            if self.site:
                await self.site.stop()
            if self.runner:
                await self.runner.cleanup()
            logger.info("Metrics server stopped")
        except Exception as e:
            logger.error(f"Error stopping metrics server: {e}")

async def run_metrics_server(host: str = "0.0.0.0", port: int = 8001, config=None):
    """Run metrics server as standalone service"""
    server = MetricsServer(host, port, config)
    
    try:
        await server.start()
        
        # Keep server running
        while True:
            await asyncio.sleep(1)
            
    except KeyboardInterrupt:
        logger.info("Shutting down metrics server...")
    except Exception as e:
        logger.error(f"Metrics server error: {e}")
    finally:
        await server.stop()

if __name__ == "__main__":
    # Run as standalone server
    logging.basicConfig(level=logging.INFO)
    asyncio.run(run_metrics_server())