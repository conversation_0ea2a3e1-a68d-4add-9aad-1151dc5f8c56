/**
 * Campaigns Page - Security testing campaign management
 */
import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { 
  Search,
  Plus,
  Play,
  Pause,
  StopCircle,
  Calendar,
  Target,
  Shield,
  AlertTriangle,
  CheckCircle
} from 'lucide-react';

interface Campaign {
  id: string;
  name: string;
  description: string;
  status: 'active' | 'paused' | 'completed' | 'failed';
  target: string;
  createdAt: string;
  lastRun: string;
  progress: number;
  vulnerabilities: number;
  toolsUsed: string[];
}

export function CampaignsPage() {
  const [searchTerm, setSearchTerm] = React.useState('');
  const [campaigns, setCampaigns] = React.useState<Campaign[]>([
    {
      id: '1',
      name: 'Production Environment Audit',
      description: 'Comprehensive security assessment of production systems',
      status: 'active',
      target: '***********/24',
      createdAt: '2024-01-15',
      lastRun: '2024-01-20',
      progress: 75,
      vulnerabilities: 3,
      toolsUsed: ['nmap', 'nuclei', 'sqlmap']
    },
    {
      id: '2',
      name: 'Web Application Security Test',
      description: 'OWASP Top 10 vulnerability assessment',
      status: 'completed',
      target: 'https://example.com',
      createdAt: '2024-01-10',
      lastRun: '2024-01-18',
      progress: 100,
      vulnerabilities: 7,
      toolsUsed: ['dirb', 'nuclei', 'nikto']
    }
  ]);

  const getStatusColor = (status: Campaign['status']) => {
    switch (status) {
      case 'active': return 'bg-green-500';
      case 'paused': return 'bg-yellow-500';
      case 'completed': return 'bg-blue-500';
      case 'failed': return 'bg-red-500';
      default: return 'bg-gray-500';
    }
  };

  const getStatusIcon = (status: Campaign['status']) => {
    switch (status) {
      case 'active': return <Play className="h-3 w-3" />;
      case 'paused': return <Pause className="h-3 w-3" />;
      case 'completed': return <CheckCircle className="h-3 w-3" />;
      case 'failed': return <AlertTriangle className="h-3 w-3" />;
      default: return <StopCircle className="h-3 w-3" />;
    }
  };

  const filteredCampaigns = campaigns.filter(campaign =>
    campaign.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    campaign.target.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleCreateCampaign = () => {
    // Navigate to campaign creation
    console.log('Creating new campaign...');
  };

  const handleRunCampaign = (campaignId: string) => {
    console.log(`Running campaign ${campaignId}...`);
  };

  const handlePauseCampaign = (campaignId: string) => {
    console.log(`Pausing campaign ${campaignId}...`);
  };

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Security Campaigns</h1>
          <p className="text-muted-foreground">
            Manage and monitor your security testing campaigns
          </p>
        </div>
        <Button onClick={handleCreateCampaign}>
          <Plus className="mr-2 h-4 w-4" />
          New Campaign
        </Button>
      </div>

      {/* Search and Filters */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex items-center gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search campaigns..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Button variant="outline">Filter</Button>
            <Button variant="outline">Sort</Button>
          </div>
        </CardContent>
      </Card>

      {/* Campaigns Grid */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {filteredCampaigns.map((campaign) => (
          <Card key={campaign.id} className="hover:shadow-lg transition-shadow">
            <CardHeader>
              <div className="flex items-start justify-between">
                <div className="space-y-1">
                  <CardTitle className="text-lg">{campaign.name}</CardTitle>
                  <CardDescription>{campaign.description}</CardDescription>
                </div>
                <Badge variant="outline" className={`${getStatusColor(campaign.status)} text-white`}>
                  {getStatusIcon(campaign.status)}
                  {campaign.status}
                </Badge>
              </div>
            </CardHeader>
            
            <CardContent className="space-y-4">
              {/* Target */}
              <div className="flex items-center gap-2">
                <Target className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm font-medium">Target:</span>
                <span className="text-sm text-muted-foreground">{campaign.target}</span>
              </div>

              {/* Progress */}
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Progress</span>
                  <span className="text-sm text-muted-foreground">{campaign.progress}%</span>
                </div>
                <div className="w-full bg-secondary rounded-full h-2">
                  <div 
                    className="bg-primary h-2 rounded-full transition-all" 
                    style={{ width: `${campaign.progress}%` }}
                  />
                </div>
              </div>

              {/* Stats */}
              <div className="grid grid-cols-2 gap-4">
                <div className="flex items-center gap-2">
                  <AlertTriangle className="h-4 w-4 text-yellow-500" />
                  <div>
                    <p className="text-sm font-medium">{campaign.vulnerabilities}</p>
                    <p className="text-xs text-muted-foreground">Vulnerabilities</p>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Shield className="h-4 w-4 text-blue-500" />
                  <div>
                    <p className="text-sm font-medium">{campaign.toolsUsed.length}</p>
                    <p className="text-xs text-muted-foreground">Tools Used</p>
                  </div>
                </div>
              </div>

              {/* Tools Used */}
              <div className="space-y-2">
                <span className="text-sm font-medium">Tools:</span>
                <div className="flex flex-wrap gap-1">
                  {campaign.toolsUsed.map((tool) => (
                    <Badge key={tool} variant="secondary" className="text-xs">
                      {tool}
                    </Badge>
                  ))}
                </div>
              </div>

              {/* Dates */}
              <div className="flex items-center gap-4 text-xs text-muted-foreground">
                <div className="flex items-center gap-1">
                  <Calendar className="h-3 w-3" />
                  Created: {campaign.createdAt}
                </div>
              </div>

              {/* Actions */}
              <div className="flex gap-2 pt-2">
                {campaign.status === 'active' ? (
                  <Button 
                    size="sm" 
                    variant="outline"
                    onClick={() => handlePauseCampaign(campaign.id)}
                  >
                    <Pause className="mr-1 h-3 w-3" />
                    Pause
                  </Button>
                ) : (
                  <Button 
                    size="sm"
                    onClick={() => handleRunCampaign(campaign.id)}
                  >
                    <Play className="mr-1 h-3 w-3" />
                    Run
                  </Button>
                )}
                <Button size="sm" variant="outline">
                  View Details
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Empty State */}
      {filteredCampaigns.length === 0 && (
        <Card>
          <CardContent className="pt-6">
            <div className="text-center py-8">
              <Shield className="mx-auto h-12 w-12 text-muted-foreground" />
              <h3 className="mt-2 text-sm font-semibold">No campaigns found</h3>
              <p className="mt-1 text-sm text-muted-foreground">
                {searchTerm ? 'Try adjusting your search terms.' : 'Get started by creating a new security campaign.'}
              </p>
              {!searchTerm && (
                <div className="mt-6">
                  <Button onClick={handleCreateCampaign}>
                    <Plus className="mr-2 h-4 w-4" />
                    Create Campaign
                  </Button>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}