#!/usr/bin/env python3
"""
Configuration Management System for NexusScan Desktop Application
Handles application configuration, settings, and preferences
"""

import json
import logging
from typing import Dict, List, Optional, Any, Union, Type, Tuple
from dataclasses import dataclass
from datetime import datetime
from sqlalchemy.orm import Session
from sqlalchemy import and_

from .models import AppConfig, ScanTemplate, DatabaseManager

logger = logging.getLogger(__name__)


@dataclass
class ConfigSection:
    """Configuration section definition"""
    name: str
    description: str
    settings: Dict[str, Any]


@dataclass
class SettingDefinition:
    """Definition of a configuration setting"""
    key: str
    value_type: str  # string, integer, boolean, json
    default_value: Any
    description: str
    required: bool = False
    validation_rules: Optional[Dict[str, Any]] = None


class ConfigManager:
    """Manages application configuration and settings"""

    def __init__(self, db_manager: DatabaseManager):
        """Initialize configuration manager
        
        Args:
            db_manager: Database manager instance
        """
        self.db_manager = db_manager
        self.logger = logging.getLogger(__name__)
        self._config_cache = {}
        self._cache_timestamp = None
        self.cache_ttl = 300  # 5 minutes

    # =============================================================================
    # CONFIGURATION MANAGEMENT
    # =============================================================================

    def get_config(self, section: str, key: str, default: Any = None) -> Any:
        """Get configuration value
        
        Args:
            section: Configuration section
            key: Configuration key
            default: Default value if not found
            
        Returns:
            Configuration value with proper type conversion
        """
        try:
            # Check cache first
            cache_key = f"{section}.{key}"
            if self._is_cache_valid() and cache_key in self._config_cache:
                return self._config_cache[cache_key]
            
            session = self.db_manager.get_session()
            try:
                config = session.query(AppConfig).filter(
                    and_(AppConfig.section == section, AppConfig.key == key)
                ).first()
                
                if config:
                    value = config.typed_value
                    self._config_cache[cache_key] = value
                    return value
                else:
                    return default
                    
            finally:
                session.close()
                
        except Exception as e:
            self.logger.error(f"Failed to get config {section}.{key}: {e}")
            return default

    def set_config(self, section: str, key: str, value: Any, 
                   value_type: Optional[str] = None, 
                   description: str = "") -> bool:
        """Set configuration value
        
        Args:
            section: Configuration section
            key: Configuration key
            value: Configuration value
            value_type: Value type (auto-detected if None)
            description: Setting description
            
        Returns:
            True if set successfully
        """
        session = self.db_manager.get_session()
        try:
            # Auto-detect value type if not specified
            if value_type is None:
                value_type = self._detect_value_type(value)
            
            # Convert value to string for storage
            if value_type == 'json':
                string_value = json.dumps(value)
            elif value_type == 'boolean':
                string_value = str(value).lower()
            else:
                string_value = str(value)
            
            # Check if config exists
            config = session.query(AppConfig).filter(
                and_(AppConfig.section == section, AppConfig.key == key)
            ).first()
            
            if config:
                # Update existing
                config.value = string_value
                config.value_type = value_type
                config.description = description
                config.updated_at = datetime.utcnow()
            else:
                # Create new
                config = AppConfig(
                    section=section,
                    key=key,
                    value=string_value,
                    value_type=value_type,
                    description=description
                )
                session.add(config)
            
            session.commit()
            
            # Update cache
            cache_key = f"{section}.{key}"
            self._config_cache[cache_key] = value
            
            self.logger.info(f"Set config {section}.{key} = {value}")
            return True
            
        except Exception as e:
            session.rollback()
            self.logger.error(f"Failed to set config {section}.{key}: {e}")
            return False
        finally:
            session.close()

    def delete_config(self, section: str, key: str) -> bool:
        """Delete configuration setting
        
        Args:
            section: Configuration section
            key: Configuration key
            
        Returns:
            True if deleted successfully
        """
        session = self.db_manager.get_session()
        try:
            config = session.query(AppConfig).filter(
                and_(AppConfig.section == section, AppConfig.key == key)
            ).first()
            
            if config:
                session.delete(config)
                session.commit()
                
                # Remove from cache
                cache_key = f"{section}.{key}"
                self._config_cache.pop(cache_key, None)
                
                self.logger.info(f"Deleted config {section}.{key}")
                return True
            else:
                return False
                
        except Exception as e:
            session.rollback()
            self.logger.error(f"Failed to delete config {section}.{key}: {e}")
            return False
        finally:
            session.close()

    def get_section(self, section: str) -> Dict[str, Any]:
        """Get all configuration values for a section
        
        Args:
            section: Configuration section name
            
        Returns:
            Dictionary of configuration values
        """
        session = self.db_manager.get_session()
        try:
            configs = session.query(AppConfig).filter(AppConfig.section == section).all()
            
            result = {}
            for config in configs:
                result[config.key] = config.typed_value
                
                # Update cache
                cache_key = f"{section}.{config.key}"
                self._config_cache[cache_key] = config.typed_value
            
            return result
            
        finally:
            session.close()

    def get_all_sections(self) -> Dict[str, Dict[str, Any]]:
        """Get all configuration sections and their values
        
        Returns:
            Nested dictionary of all configuration
        """
        session = self.db_manager.get_session()
        try:
            configs = session.query(AppConfig).all()
            
            result = {}
            for config in configs:
                if config.section not in result:
                    result[config.section] = {}
                
                result[config.section][config.key] = config.typed_value
                
                # Update cache
                cache_key = f"{config.section}.{config.key}"
                self._config_cache[cache_key] = config.typed_value
            
            return result
            
        finally:
            session.close()

    def update_section(self, section: str, settings: Dict[str, Any]) -> bool:
        """Update multiple settings in a section
        
        Args:
            section: Configuration section
            settings: Dictionary of key-value pairs to update
            
        Returns:
            True if all updates successful
        """
        success = True
        for key, value in settings.items():
            if not self.set_config(section, key, value):
                success = False
        
        return success

    def reset_section(self, section: str) -> bool:
        """Reset a configuration section to defaults
        
        Args:
            section: Configuration section to reset
            
        Returns:
            True if reset successfully
        """
        defaults = self._get_default_config()
        if section not in defaults:
            self.logger.warning(f"No defaults found for section: {section}")
            return False
        
        # Delete existing settings in section
        session = self.db_manager.get_session()
        try:
            session.query(AppConfig).filter(AppConfig.section == section).delete()
            session.commit()
            
            # Clear cache for this section
            cache_keys_to_remove = [k for k in self._config_cache.keys() if k.startswith(f"{section}.")]
            for cache_key in cache_keys_to_remove:
                del self._config_cache[cache_key]
            
        except Exception as e:
            session.rollback()
            self.logger.error(f"Failed to reset section {section}: {e}")
            return False
        finally:
            session.close()
        
        # Apply defaults
        return self.update_section(section, defaults[section])

    # =============================================================================
    # SCAN TEMPLATE MANAGEMENT
    # =============================================================================

    def get_scan_template(self, template_id: int) -> Optional[ScanTemplate]:
        """Get scan template by ID
        
        Args:
            template_id: Template ID
            
        Returns:
            Scan template or None if not found
        """
        session = self.db_manager.get_session()
        try:
            return session.query(ScanTemplate).filter_by(id=template_id).first()
        finally:
            session.close()

    def get_scan_template_by_name(self, name: str) -> Optional[ScanTemplate]:
        """Get scan template by name
        
        Args:
            name: Template name
            
        Returns:
            Scan template or None if not found
        """
        session = self.db_manager.get_session()
        try:
            return session.query(ScanTemplate).filter_by(name=name).first()
        finally:
            session.close()

    def list_scan_templates(self, 
                           scan_type: Optional[str] = None,
                           category: Optional[str] = None,
                           is_default: Optional[bool] = None) -> List[ScanTemplate]:
        """List scan templates with filtering
        
        Args:
            scan_type: Filter by scan type
            category: Filter by category
            is_default: Filter by default status
            
        Returns:
            List of scan templates
        """
        session = self.db_manager.get_session()
        try:
            query = session.query(ScanTemplate)
            
            if scan_type:
                query = query.filter(ScanTemplate.scan_type == scan_type)
            
            if category:
                query = query.filter(ScanTemplate.category == category)
            
            if is_default is not None:
                query = query.filter(ScanTemplate.is_default == is_default)
            
            return query.order_by(ScanTemplate.name).all()
            
        finally:
            session.close()

    def create_scan_template(self, 
                           name: str,
                           scan_type: str,
                           tools_config: Dict[str, Any],
                           description: str = "",
                           ai_config: Optional[Dict[str, Any]] = None,
                           category: str = "",
                           is_default: bool = False) -> Optional[ScanTemplate]:
        """Create new scan template
        
        Args:
            name: Template name
            scan_type: Type of scan
            tools_config: Tools configuration
            description: Template description
            ai_config: AI configuration
            category: Template category
            is_default: Whether this is a default template
            
        Returns:
            Created template or None if failed
        """
        session = self.db_manager.get_session()
        try:
            # Check if name already exists
            existing = session.query(ScanTemplate).filter_by(name=name).first()
            if existing:
                self.logger.error(f"Scan template name '{name}' already exists")
                return None
            
            template = ScanTemplate(
                name=name,
                description=description,
                scan_type=scan_type,
                tools_config=tools_config,
                ai_config=ai_config or {},
                is_default=is_default,
                category=category
            )
            
            session.add(template)
            session.commit()
            session.refresh(template)
            
            self.logger.info(f"Created scan template: {name}")
            return template
            
        except Exception as e:
            session.rollback()
            self.logger.error(f"Failed to create scan template: {e}")
            return None
        finally:
            session.close()

    def update_scan_template(self, 
                           template_id: int,
                           name: Optional[str] = None,
                           description: Optional[str] = None,
                           tools_config: Optional[Dict[str, Any]] = None,
                           ai_config: Optional[Dict[str, Any]] = None,
                           category: Optional[str] = None,
                           is_default: Optional[bool] = None) -> bool:
        """Update scan template
        
        Args:
            template_id: Template ID to update
            name: New name
            description: New description
            tools_config: New tools configuration
            ai_config: New AI configuration
            category: New category
            is_default: New default status
            
        Returns:
            True if updated successfully
        """
        session = self.db_manager.get_session()
        try:
            template = session.query(ScanTemplate).filter_by(id=template_id).first()
            if not template:
                return False
            
            # Update provided fields
            if name is not None:
                # Check name uniqueness
                existing = session.query(ScanTemplate).filter(
                    and_(ScanTemplate.name == name, ScanTemplate.id != template_id)
                ).first()
                if existing:
                    self.logger.error(f"Scan template name '{name}' already exists")
                    return False
                template.name = name
            
            if description is not None:
                template.description = description
            
            if tools_config is not None:
                template.tools_config = tools_config
            
            if ai_config is not None:
                template.ai_config = ai_config
            
            if category is not None:
                template.category = category
            
            if is_default is not None:
                template.is_default = is_default
            
            template.updated_at = datetime.utcnow()
            session.commit()
            
            self.logger.info(f"Updated scan template: {template.name}")
            return True
            
        except Exception as e:
            session.rollback()
            self.logger.error(f"Failed to update scan template {template_id}: {e}")
            return False
        finally:
            session.close()

    def delete_scan_template(self, template_id: int) -> bool:
        """Delete scan template
        
        Args:
            template_id: Template ID to delete
            
        Returns:
            True if deleted successfully
        """
        session = self.db_manager.get_session()
        try:
            template = session.query(ScanTemplate).filter_by(id=template_id).first()
            if not template:
                return False
            
            session.delete(template)
            session.commit()
            
            self.logger.info(f"Deleted scan template: {template.name}")
            return True
            
        except Exception as e:
            session.rollback()
            self.logger.error(f"Failed to delete scan template {template_id}: {e}")
            return False
        finally:
            session.close()

    # =============================================================================
    # CONFIGURATION VALIDATION & DEFAULTS
    # =============================================================================

    def validate_config(self, section: str, key: str, value: Any) -> Tuple[bool, str]:
        """Validate configuration value
        
        Args:
            section: Configuration section
            key: Configuration key
            value: Value to validate
            
        Returns:
            Tuple of (is_valid, error_message)
        """
        try:
            validation_rules = self._get_validation_rules()
            
            section_rules = validation_rules.get(section, {})
            key_rules = section_rules.get(key, {})
            
            if not key_rules:
                return True, ""  # No validation rules defined
            
            # Check type
            expected_type = key_rules.get('type')
            if expected_type:
                if expected_type == 'integer' and not isinstance(value, int):
                    try:
                        int(value)
                    except (ValueError, TypeError):
                        return False, f"Value must be an integer"
                
                elif expected_type == 'float' and not isinstance(value, (int, float)):
                    try:
                        float(value)
                    except (ValueError, TypeError):
                        return False, f"Value must be a number"
                
                elif expected_type == 'boolean' and not isinstance(value, bool):
                    if str(value).lower() not in ['true', 'false', '1', '0', 'yes', 'no']:
                        return False, f"Value must be a boolean"
            
            # Check range
            if 'min' in key_rules or 'max' in key_rules:
                try:
                    num_value = float(value)
                    if 'min' in key_rules and num_value < key_rules['min']:
                        return False, f"Value must be >= {key_rules['min']}"
                    if 'max' in key_rules and num_value > key_rules['max']:
                        return False, f"Value must be <= {key_rules['max']}"
                except (ValueError, TypeError):
                    return False, f"Value must be numeric for range validation"
            
            # Check allowed values
            if 'allowed_values' in key_rules:
                if value not in key_rules['allowed_values']:
                    return False, f"Value must be one of: {key_rules['allowed_values']}"
            
            # Check regex pattern
            if 'pattern' in key_rules:
                import re
                if not re.match(key_rules['pattern'], str(value)):
                    return False, f"Value does not match required pattern"
            
            return True, ""
            
        except Exception as e:
            return False, f"Validation error: {e}"

    def _get_validation_rules(self) -> Dict[str, Dict[str, Dict[str, Any]]]:
        """Get validation rules for configuration settings"""
        return {
            'ai': {
                'request_timeout': {'type': 'integer', 'min': 1, 'max': 300},
                'max_retries': {'type': 'integer', 'min': 0, 'max': 10},
                'default_provider': {'allowed_values': ['openai_o3', 'deepseek', 'anthropic']},
                'openai_model': {'pattern': r'^[a-zA-Z0-9\-_]+$'},
                'deepseek_model': {'pattern': r'^[a-zA-Z0-9\-_]+$'}
            },
            'scanning': {
                'max_concurrent_scans': {'type': 'integer', 'min': 1, 'max': 20},
                'scan_timeout': {'type': 'integer', 'min': 60, 'max': 7200},
                'default_nmap_options': {'type': 'string'}
            },
            'reporting': {
                'include_screenshots': {'type': 'boolean'},
                'default_template': {'allowed_values': ['professional', 'executive', 'technical', 'compliance']}
            },
            'general': {
                'auto_save_interval': {'type': 'integer', 'min': 30, 'max': 3600},
                'max_log_file_size': {'type': 'integer', 'min': 1, 'max': 1000},
                'debug_mode': {'type': 'boolean'},
                'theme': {'allowed_values': ['dark', 'light', 'auto']}
            }
        }

    def _get_default_config(self) -> Dict[str, Dict[str, Any]]:
        """Get default configuration values"""
        return {
            'ai': {
                'openai_api_key': '',
                'deepseek_api_key': '',
                'anthropic_api_key': '',
                'openai_model': 'gpt-4',
                'deepseek_model': 'deepseek-chat',
                'default_provider': 'openai_o3',
                'request_timeout': 60,
                'max_retries': 3
            },
            'scanning': {
                'default_nmap_options': '-sV -sC --script=vuln',
                'nuclei_templates_path': './tools/nuclei-templates',
                'max_concurrent_scans': 5,
                'scan_timeout': 3600
            },
            'reporting': {
                'default_template': 'professional',
                'output_directory': './reports',
                'include_screenshots': True,
                'logo_path': ''
            },
            'general': {
                'auto_save_interval': 300,
                'max_log_file_size': 50,
                'debug_mode': False,
                'theme': 'dark'
            }
        }

    def _detect_value_type(self, value: Any) -> str:
        """Auto-detect value type"""
        if isinstance(value, bool):
            return 'boolean'
        elif isinstance(value, int):
            return 'integer'
        elif isinstance(value, float):
            return 'float'
        elif isinstance(value, (dict, list)):
            return 'json'
        else:
            return 'string'

    def _is_cache_valid(self) -> bool:
        """Check if configuration cache is still valid"""
        if self._cache_timestamp is None:
            self._cache_timestamp = datetime.utcnow()
            return False
        
        age = (datetime.utcnow() - self._cache_timestamp).total_seconds()
        return age < self.cache_ttl

    def clear_cache(self):
        """Clear configuration cache"""
        self._config_cache.clear()
        self._cache_timestamp = None

    def export_config(self, sections: Optional[List[str]] = None) -> Dict[str, Any]:
        """Export configuration to dictionary
        
        Args:
            sections: List of sections to export (all if None)
            
        Returns:
            Configuration dictionary
        """
        if sections:
            config = {}
            for section in sections:
                config[section] = self.get_section(section)
            return config
        else:
            return self.get_all_sections()

    def import_config(self, config_data: Dict[str, Dict[str, Any]], 
                     validate: bool = True) -> Tuple[bool, List[str]]:
        """Import configuration from dictionary
        
        Args:
            config_data: Configuration dictionary to import
            validate: Whether to validate values
            
        Returns:
            Tuple of (success, error_messages)
        """
        errors = []
        
        for section, settings in config_data.items():
            for key, value in settings.items():
                if validate:
                    is_valid, error_msg = self.validate_config(section, key, value)
                    if not is_valid:
                        errors.append(f"{section}.{key}: {error_msg}")
                        continue
                
                success = self.set_config(section, key, value)
                if not success:
                    errors.append(f"Failed to set {section}.{key}")
        
        return len(errors) == 0, errors

    def initialize_default_config(self) -> bool:
        """Initialize database with default configuration values
        
        Returns:
            True if initialization successful
        """
        try:
            defaults = self._get_default_config()
            
            for section, settings in defaults.items():
                for key, value in settings.items():
                    # Only set if doesn't exist
                    existing_value = self.get_config(section, key)
                    if existing_value is None:
                        self.set_config(section, key, value, description=f"Default {section} setting")
            
            self.logger.info("Default configuration initialized")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to initialize default config: {e}")
            return False