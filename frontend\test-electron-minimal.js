/**
 * Minimal Electron test to verify basic functionality
 */
const { app, BrowserWindow } = require('electron');
const path = require('path');

let mainWindow;

function createWindow() {
  console.log('Creating window...');
  
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: path.join(__dirname, 'dist/electron/preload/api.js')
    }
  });

  // Create a simple HTML page to test
  const html = `
    <!DOCTYPE html>
    <html>
    <head>
      <title>NexusScan Test</title>
      <style>
        body { 
          font-family: Arial, sans-serif; 
          padding: 20px; 
          background: #1a1a1a; 
          color: #fff;
        }
        button { 
          padding: 10px 20px; 
          margin: 10px; 
          cursor: pointer;
          background: #3b82f6;
          color: white;
          border: none;
          border-radius: 5px;
        }
        pre { 
          background: #000; 
          padding: 10px; 
          border-radius: 5px;
          overflow: auto;
        }
      </style>
    </head>
    <body>
      <h1>NexusScan Electron Test</h1>
      
      <button onclick="testBackend()">Test Backend Connection</button>
      <button onclick="testWSL()">Test WSL</button>
      <button onclick="testElectronAPI()">Test Electron API</button>
      
      <pre id="output">Ready for testing...</pre>
      
      <script>
        const output = document.getElementById('output');
        
        function log(msg) {
          output.innerHTML += msg + '\\n';
        }
        
        async function testBackend() {
          log('\\nTesting backend connection...');
          try {
            const response = await fetch('http://ec2-3-89-91-209.compute-1.amazonaws.com:8000/api/health');
            const data = await response.json();
            log('✅ Backend connected: ' + JSON.stringify(data, null, 2));
          } catch (error) {
            log('❌ Backend error: ' + error.message);
          }
        }
        
        async function testWSL() {
          log('\\nTesting WSL through Electron API...');
          if (window.electronAPI && window.electronAPI.wsl) {
            try {
              const status = await window.electronAPI.wsl.getStatus();
              log('✅ WSL Status: ' + JSON.stringify(status, null, 2));
            } catch (error) {
              log('❌ WSL error: ' + error.message);
            }
          } else {
            log('❌ Electron API not available');
          }
        }
        
        function testElectronAPI() {
          log('\\nTesting Electron API availability...');
          if (window.electronAPI) {
            log('✅ Electron API is available');
            log('Available methods:');
            Object.keys(window.electronAPI).forEach(key => {
              log('  - ' + key);
            });
          } else {
            log('❌ Electron API is NOT available');
          }
        }
        
        // Auto-test on load
        testElectronAPI();
      </script>
    </body>
    </html>
  `;

  mainWindow.loadURL(`data:text/html;charset=utf-8,${encodeURIComponent(html)}`);
  
  mainWindow.webContents.openDevTools();
}

app.on('ready', () => {
  console.log('Electron ready!');
  createWindow();
});

app.on('window-all-closed', () => {
  app.quit();
});

console.log('Starting minimal Electron test...');