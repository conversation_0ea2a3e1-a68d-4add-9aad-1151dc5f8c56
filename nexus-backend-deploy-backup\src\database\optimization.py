#!/usr/bin/env python3
"""
Database Optimization for Enterprise Scale
Advanced SQLite optimization, connection pooling, query optimization,
and data management strategies for high-volume operations
"""

import sqlite3
import asyncio
import threading
import time
import logging
import os
import json
import gzip
from typing import Any, Dict, List, Optional, Tuple, Union, Callable
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from pathlib import Path
from contextlib import asynccontextmanager, contextmanager
from concurrent.futures import ThreadPoolExecutor
import queue

logger = logging.getLogger(__name__)

@dataclass
class DatabaseMetrics:
    """Database performance metrics"""
    total_queries: int = 0
    slow_queries: int = 0
    avg_query_time: float = 0.0
    connection_pool_hits: int = 0
    connection_pool_misses: int = 0
    cache_hits: int = 0
    cache_misses: int = 0
    database_size_mb: float = 0.0
    vacuum_count: int = 0
    last_vacuum: Optional[datetime] = None
    
    def get_hit_rate(self) -> float:
        """Calculate cache hit rate"""
        total = self.cache_hits + self.cache_misses
        return (self.cache_hits / total * 100) if total > 0 else 0.0

@dataclass
class OptimizationConfig:
    """Database optimization configuration"""
    # Connection pool settings
    max_connections: int = 20
    min_connections: int = 5
    connection_timeout: float = 30.0
    idle_timeout: float = 300.0
    
    # Performance settings
    enable_wal_mode: bool = True
    enable_foreign_keys: bool = True
    cache_size_kb: int = 64000  # 64MB
    temp_store: str = "memory"
    synchronous: str = "normal"
    journal_mode: str = "wal"
    
    # Query optimization
    enable_query_cache: bool = True
    query_cache_size: int = 1000
    slow_query_threshold: float = 1.0
    enable_query_logging: bool = True
    
    # Maintenance settings
    auto_vacuum_enabled: bool = True
    vacuum_threshold_mb: int = 100
    analyze_frequency_hours: int = 24
    checkpoint_frequency_seconds: int = 300
    
    # Compression settings
    enable_blob_compression: bool = True
    compression_threshold_bytes: int = 1024
    
    # Backup settings
    enable_incremental_backup: bool = True
    backup_page_size: int = 4096

class ConnectionPool:
    """Advanced SQLite connection pool with optimization"""
    
    def __init__(self, database_path: str, config: OptimizationConfig):
        self.database_path = database_path
        self.config = config
        
        # Connection pool
        self._pool = queue.Queue(maxsize=config.max_connections)
        self._active_connections = set()
        self._connection_count = 0
        self._lock = threading.RLock()
        
        # Metrics
        self.metrics = DatabaseMetrics()
        
        # Maintenance
        self._last_maintenance = time.time()
        self._maintenance_executor = ThreadPoolExecutor(max_workers=1)
        
        # Initialize pool
        self._initialize_pool()
        
        # Start maintenance task
        asyncio.create_task(self._maintenance_task())
    
    def _initialize_pool(self):
        """Initialize connection pool with minimum connections"""
        for _ in range(self.config.min_connections):
            conn = self._create_optimized_connection()
            self._pool.put(conn)
            self._connection_count += 1
    
    def _create_optimized_connection(self) -> sqlite3.Connection:
        """Create optimized SQLite connection"""
        conn = sqlite3.connect(
            self.database_path,
            check_same_thread=False,
            timeout=self.config.connection_timeout,
            isolation_level=None  # Autocommit mode
        )
        
        # Apply optimizations
        self._apply_optimizations(conn)
        return conn
    
    def _apply_optimizations(self, conn: sqlite3.Connection):
        """Apply SQLite optimizations to connection"""
        cursor = conn.cursor()
        
        try:
            # Enable WAL mode for better concurrency
            if self.config.enable_wal_mode:
                cursor.execute(f"PRAGMA journal_mode = {self.config.journal_mode}")
            
            # Set synchronous mode
            cursor.execute(f"PRAGMA synchronous = {self.config.synchronous}")
            
            # Set cache size
            cursor.execute(f"PRAGMA cache_size = -{self.config.cache_size_kb}")
            
            # Set temp store
            cursor.execute(f"PRAGMA temp_store = {self.config.temp_store}")
            
            # Enable foreign keys
            if self.config.enable_foreign_keys:
                cursor.execute("PRAGMA foreign_keys = ON")
            
            # Set auto vacuum
            if self.config.auto_vacuum_enabled:
                cursor.execute("PRAGMA auto_vacuum = INCREMENTAL")
            
            # Additional optimizations
            cursor.execute("PRAGMA optimize")
            cursor.execute("PRAGMA page_size = 4096")
            cursor.execute("PRAGMA mmap_size = 268435456")  # 256MB mmap
            
        except Exception as e:
            logger.warning(f"Failed to apply some optimizations: {e}")
        finally:
            cursor.close()
    
    @contextmanager
    def get_connection(self):
        """Get connection from pool with automatic return"""
        conn = None
        try:
            conn = self._acquire_connection()
            self.metrics.connection_pool_hits += 1
            yield conn
        except Exception as e:
            self.metrics.connection_pool_misses += 1
            logger.error(f"Connection error: {e}")
            raise
        finally:
            if conn:
                self._release_connection(conn)
    
    def _acquire_connection(self) -> sqlite3.Connection:
        """Acquire connection from pool"""
        try:
            # Try to get from pool first
            conn = self._pool.get_nowait()
            self._active_connections.add(id(conn))
            return conn
        except queue.Empty:
            # Pool empty, create new connection if under limit
            with self._lock:
                if self._connection_count < self.config.max_connections:
                    conn = self._create_optimized_connection()
                    self._connection_count += 1
                    self._active_connections.add(id(conn))
                    return conn
                else:
                    # Wait for connection with timeout
                    conn = self._pool.get(timeout=self.config.connection_timeout)
                    self._active_connections.add(id(conn))
                    return conn
    
    def _release_connection(self, conn: sqlite3.Connection):
        """Release connection back to pool"""
        try:
            # Check if connection is still valid
            conn.execute("SELECT 1")
            
            # Return to pool
            self._pool.put_nowait(conn)
            self._active_connections.discard(id(conn))
        except (queue.Full, sqlite3.Error):
            # Pool full or connection invalid, close it
            self._close_connection(conn)
    
    def _close_connection(self, conn: sqlite3.Connection):
        """Close connection and update count"""
        try:
            conn.close()
        except:
            pass
        
        with self._lock:
            self._connection_count -= 1
            self._active_connections.discard(id(conn))
    
    async def _maintenance_task(self):
        """Background maintenance task"""
        while True:
            try:
                await asyncio.sleep(60)  # Check every minute
                await self._perform_maintenance()
            except Exception as e:
                logger.error(f"Maintenance task error: {e}")
    
    async def _perform_maintenance(self):
        """Perform database maintenance"""
        current_time = time.time()
        
        # Check if maintenance is needed
        if current_time - self._last_maintenance < self.config.checkpoint_frequency_seconds:
            return
        
        self._last_maintenance = current_time
        
        # Perform maintenance in background thread
        loop = asyncio.get_event_loop()
        await loop.run_in_executor(self._maintenance_executor, self._do_maintenance)
    
    def _do_maintenance(self):
        """Perform actual maintenance operations"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                # WAL checkpoint
                if self.config.enable_wal_mode:
                    cursor.execute("PRAGMA wal_checkpoint(TRUNCATE)")
                
                # Update database size metric
                cursor.execute("PRAGMA page_count")
                page_count = cursor.fetchone()[0]
                cursor.execute("PRAGMA page_size")
                page_size = cursor.fetchone()[0]
                self.metrics.database_size_mb = (page_count * page_size) / (1024 * 1024)
                
                # Check if vacuum is needed
                if (self.metrics.database_size_mb > self.config.vacuum_threshold_mb and
                    (self.metrics.last_vacuum is None or 
                     datetime.now() - self.metrics.last_vacuum > timedelta(days=7))):
                    
                    logger.info("Performing incremental vacuum")
                    cursor.execute("PRAGMA incremental_vacuum")
                    self.metrics.vacuum_count += 1
                    self.metrics.last_vacuum = datetime.now()
                
                # Analyze for query optimization
                cursor.execute("PRAGMA optimize")
                
                cursor.close()
                
        except Exception as e:
            logger.error(f"Maintenance error: {e}")
    
    def close(self):
        """Close all connections in pool"""
        self._maintenance_executor.shutdown(wait=True)
        
        # Close all pooled connections
        while not self._pool.empty():
            try:
                conn = self._pool.get_nowait()
                conn.close()
            except (queue.Empty, sqlite3.Error):
                break
        
        # Clear active connections tracking
        self._active_connections.clear()
    
    def get_stats(self) -> Dict[str, Any]:
        """Get connection pool statistics"""
        return {
            "pool_size": self._pool.qsize(),
            "active_connections": len(self._active_connections),
            "total_connections": self._connection_count,
            "max_connections": self.config.max_connections,
            "metrics": self.metrics
        }

class QueryOptimizer:
    """Query optimization and caching"""
    
    def __init__(self, config: OptimizationConfig):
        self.config = config
        self.query_cache = {}
        self.query_stats = {}
        self._lock = threading.RLock()
    
    def optimize_query(self, query: str, params: Optional[Tuple] = None) -> str:
        """Optimize SQL query"""
        # Remove extra whitespace and normalize
        optimized = " ".join(query.split())
        
        # Add query hints for common patterns
        if "SELECT" in optimized.upper() and "ORDER BY" in optimized.upper():
            # Suggest index usage for ORDER BY
            if "LIMIT" not in optimized.upper():
                logger.debug("Consider adding LIMIT for ORDER BY queries")
        
        return optimized
    
    def cache_query_result(self, query: str, params: Optional[Tuple], result: Any):
        """Cache query result if caching is enabled"""
        if not self.config.enable_query_cache:
            return
        
        cache_key = self._create_cache_key(query, params)
        
        with self._lock:
            if len(self.query_cache) >= self.config.query_cache_size:
                # Remove oldest entry (simple FIFO)
                oldest_key = next(iter(self.query_cache))
                del self.query_cache[oldest_key]
            
            self.query_cache[cache_key] = {
                "result": result,
                "timestamp": time.time()
            }
    
    def get_cached_result(self, query: str, params: Optional[Tuple]) -> Optional[Any]:
        """Get cached query result"""
        if not self.config.enable_query_cache:
            return None
        
        cache_key = self._create_cache_key(query, params)
        
        with self._lock:
            cached = self.query_cache.get(cache_key)
            if cached:
                # Check if cache entry is still valid (5 minutes TTL)
                if time.time() - cached["timestamp"] < 300:
                    return cached["result"]
                else:
                    del self.query_cache[cache_key]
        
        return None
    
    def _create_cache_key(self, query: str, params: Optional[Tuple]) -> str:
        """Create cache key for query and parameters"""
        query_normalized = " ".join(query.split())
        params_str = str(params) if params else ""
        return f"{query_normalized}|{params_str}"
    
    def record_query_stats(self, query: str, execution_time: float):
        """Record query execution statistics"""
        if not self.config.enable_query_logging:
            return
        
        query_type = query.strip().split()[0].upper()
        
        with self._lock:
            if query_type not in self.query_stats:
                self.query_stats[query_type] = {
                    "count": 0,
                    "total_time": 0.0,
                    "slow_queries": 0
                }
            
            stats = self.query_stats[query_type]
            stats["count"] += 1
            stats["total_time"] += execution_time
            
            if execution_time > self.config.slow_query_threshold:
                stats["slow_queries"] += 1
                logger.warning(f"Slow query detected ({execution_time:.3f}s): {query[:100]}...")
    
    def get_query_stats(self) -> Dict[str, Any]:
        """Get query execution statistics"""
        with self._lock:
            stats = {}
            for query_type, data in self.query_stats.items():
                if data["count"] > 0:
                    stats[query_type] = {
                        "count": data["count"],
                        "avg_time": data["total_time"] / data["count"],
                        "slow_queries": data["slow_queries"],
                        "slow_query_rate": data["slow_queries"] / data["count"] * 100
                    }
            return stats

class DataCompressor:
    """Data compression for large fields"""
    
    def __init__(self, config: OptimizationConfig):
        self.config = config
    
    def compress_data(self, data: Any) -> bytes:
        """Compress data if it exceeds threshold"""
        if isinstance(data, str):
            data_bytes = data.encode('utf-8')
        elif isinstance(data, (dict, list)):
            data_bytes = json.dumps(data, separators=(',', ':')).encode('utf-8')
        else:
            data_bytes = str(data).encode('utf-8')
        
        if len(data_bytes) > self.config.compression_threshold_bytes:
            compressed = gzip.compress(data_bytes)
            # Only use compression if it actually reduces size
            if len(compressed) < len(data_bytes):
                return b'COMPRESSED:' + compressed
        
        return data_bytes
    
    def decompress_data(self, data: bytes) -> str:
        """Decompress data if it was compressed"""
        if data.startswith(b'COMPRESSED:'):
            compressed_data = data[11:]  # Remove prefix
            decompressed = gzip.decompress(compressed_data)
            return decompressed.decode('utf-8')
        else:
            return data.decode('utf-8')

class OptimizedDatabase:
    """High-performance optimized database interface"""
    
    def __init__(self, database_path: str, config: Optional[OptimizationConfig] = None):
        self.database_path = database_path
        self.config = config or OptimizationConfig()
        
        # Components
        self.connection_pool = ConnectionPool(database_path, self.config)
        self.query_optimizer = QueryOptimizer(self.config)
        self.compressor = DataCompressor(self.config)
        
        # Metrics
        self.metrics = DatabaseMetrics()
        
        # Ensure database directory exists
        Path(database_path).parent.mkdir(parents=True, exist_ok=True)
        
        # Initialize database if needed
        self._initialize_database()
    
    def _initialize_database(self):
        """Initialize database with optimizations"""
        with self.connection_pool.get_connection() as conn:
            cursor = conn.cursor()
            
            # Create performance monitoring table if it doesn't exist
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS performance_metrics (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    metric_name TEXT NOT NULL,
                    metric_value REAL NOT NULL,
                    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # Create index for performance metrics
            cursor.execute("""
                CREATE INDEX IF NOT EXISTS idx_performance_metrics_timestamp 
                ON performance_metrics(timestamp)
            """)
            
            cursor.close()
    
    def execute_query(self, 
                     query: str, 
                     params: Optional[Tuple] = None,
                     fetch_one: bool = False,
                     fetch_all: bool = False) -> Optional[Any]:
        """Execute optimized database query"""
        
        # Check cache first
        cached_result = self.query_optimizer.get_cached_result(query, params)
        if cached_result is not None:
            self.metrics.cache_hits += 1
            return cached_result
        
        self.metrics.cache_misses += 1
        
        # Optimize query
        optimized_query = self.query_optimizer.optimize_query(query, params)
        
        # Execute query with timing
        start_time = time.time()
        result = None
        
        try:
            with self.connection_pool.get_connection() as conn:
                cursor = conn.cursor()
                
                if params:
                    cursor.execute(optimized_query, params)
                else:
                    cursor.execute(optimized_query)
                
                if fetch_one:
                    result = cursor.fetchone()
                elif fetch_all:
                    result = cursor.fetchall()
                else:
                    result = cursor.rowcount
                
                cursor.close()
            
            execution_time = time.time() - start_time
            
            # Record metrics
            self.metrics.total_queries += 1
            self.metrics.avg_query_time = (
                (self.metrics.avg_query_time * (self.metrics.total_queries - 1) + execution_time) /
                self.metrics.total_queries
            )
            
            # Record query stats
            self.query_optimizer.record_query_stats(query, execution_time)
            
            # Cache result if it's a SELECT query
            if query.strip().upper().startswith('SELECT') and result is not None:
                self.query_optimizer.cache_query_result(query, params, result)
            
            return result
            
        except Exception as e:
            logger.error(f"Query execution failed: {e}")
            raise
    
    def execute_many(self, query: str, params_list: List[Tuple]) -> int:
        """Execute query with multiple parameter sets"""
        optimized_query = self.query_optimizer.optimize_query(query)
        total_affected = 0
        
        with self.connection_pool.get_connection() as conn:
            cursor = conn.cursor()
            
            try:
                cursor.execute("BEGIN TRANSACTION")
                
                for params in params_list:
                    cursor.execute(optimized_query, params)
                    total_affected += cursor.rowcount
                
                cursor.execute("COMMIT")
                
            except Exception as e:
                cursor.execute("ROLLBACK")
                logger.error(f"Batch execution failed: {e}")
                raise
            finally:
                cursor.close()
        
        return total_affected
    
    def insert_large_data(self, table: str, data: Dict[str, Any], compress_fields: Optional[List[str]] = None) -> int:
        """Insert data with optional compression for large fields"""
        compress_fields = compress_fields or []
        
        # Compress large fields if enabled
        if self.config.enable_blob_compression:
            for field in compress_fields:
                if field in data:
                    data[field] = self.compressor.compress_data(data[field])
        
        # Build insert query
        columns = ', '.join(data.keys())
        placeholders = ', '.join(['?' for _ in data])
        query = f"INSERT INTO {table} ({columns}) VALUES ({placeholders})"
        
        return self.execute_query(query, tuple(data.values()))
    
    def select_large_data(self, query: str, params: Optional[Tuple] = None, 
                         decompress_fields: Optional[List[str]] = None) -> List[Dict[str, Any]]:
        """Select data with optional decompression"""
        decompress_fields = decompress_fields or []
        
        with self.connection_pool.get_connection() as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)
            
            rows = cursor.fetchall()
            result = []
            
            for row in rows:
                row_dict = dict(row)
                
                # Decompress fields if enabled
                if self.config.enable_blob_compression:
                    for field in decompress_fields:
                        if field in row_dict and row_dict[field]:
                            try:
                                row_dict[field] = self.compressor.decompress_data(row_dict[field])
                            except Exception as e:
                                logger.warning(f"Failed to decompress field {field}: {e}")
                
                result.append(row_dict)
            
            cursor.close()
        
        return result
    
    def create_index(self, table: str, columns: List[str], unique: bool = False, if_not_exists: bool = True) -> bool:
        """Create optimized index"""
        index_name = f"idx_{table}_{'_'.join(columns)}"
        unique_clause = "UNIQUE " if unique else ""
        exists_clause = "IF NOT EXISTS " if if_not_exists else ""
        columns_str = ', '.join(columns)
        
        query = f"CREATE {unique_clause}INDEX {exists_clause}{index_name} ON {table}({columns_str})"
        
        try:
            self.execute_query(query)
            logger.info(f"Created index: {index_name}")
            return True
        except Exception as e:
            logger.error(f"Failed to create index {index_name}: {e}")
            return False
    
    def analyze_table(self, table: str) -> Dict[str, Any]:
        """Analyze table for optimization recommendations"""
        analysis = {}
        
        with self.connection_pool.get_connection() as conn:
            cursor = conn.cursor()
            
            # Get table info
            cursor.execute(f"PRAGMA table_info({table})")
            columns = cursor.fetchall()
            analysis["columns"] = len(columns)
            
            # Get row count
            cursor.execute(f"SELECT COUNT(*) FROM {table}")
            analysis["row_count"] = cursor.fetchone()[0]
            
            # Get index info
            cursor.execute(f"PRAGMA index_list({table})")
            indexes = cursor.fetchall()
            analysis["indexes"] = len(indexes)
            
            # Check for missing indexes on foreign keys
            foreign_keys = []
            cursor.execute(f"PRAGMA foreign_key_list({table})")
            fks = cursor.fetchall()
            for fk in fks:
                foreign_keys.append(fk[3])  # from column
            
            analysis["foreign_keys"] = foreign_keys
            analysis["recommendations"] = []
            
            # Generate recommendations
            if analysis["row_count"] > 1000 and analysis["indexes"] == 0:
                analysis["recommendations"].append("Consider adding indexes for better query performance")
            
            if foreign_keys and analysis["indexes"] < len(foreign_keys):
                analysis["recommendations"].append("Consider adding indexes on foreign key columns")
            
            cursor.close()
        
        return analysis
    
    def vacuum_database(self, full: bool = False) -> bool:
        """Vacuum database to reclaim space"""
        try:
            with self.connection_pool.get_connection() as conn:
                cursor = conn.cursor()
                
                if full:
                    cursor.execute("VACUUM")
                    logger.info("Full vacuum completed")
                else:
                    cursor.execute("PRAGMA incremental_vacuum")
                    logger.info("Incremental vacuum completed")
                
                cursor.close()
                self.metrics.vacuum_count += 1
                self.metrics.last_vacuum = datetime.now()
                return True
                
        except Exception as e:
            logger.error(f"Vacuum failed: {e}")
            return False
    
    def backup_database(self, backup_path: str, incremental: bool = True) -> bool:
        """Create database backup"""
        try:
            if incremental and self.config.enable_incremental_backup:
                # Use SQLite's backup API for incremental backup
                with self.connection_pool.get_connection() as source_conn:
                    backup_conn = sqlite3.connect(backup_path)
                    source_conn.backup(backup_conn, pages=self.config.backup_page_size)
                    backup_conn.close()
            else:
                # Simple file copy for full backup
                import shutil
                shutil.copy2(self.database_path, backup_path)
            
            logger.info(f"Database backup created: {backup_path}")
            return True
            
        except Exception as e:
            logger.error(f"Backup failed: {e}")
            return False
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """Get comprehensive performance statistics"""
        stats = {
            "database_metrics": self.metrics,
            "connection_pool": self.connection_pool.get_stats(),
            "query_stats": self.query_optimizer.get_query_stats(),
            "database_size_mb": self.metrics.database_size_mb,
            "cache_hit_rate": self.metrics.get_hit_rate()
        }
        
        return stats
    
    def close(self):
        """Close database and cleanup resources"""
        self.connection_pool.close()
        logger.info("Database closed")

# Global optimized database instance
optimized_db: Optional[OptimizedDatabase] = None

def get_optimized_database(database_path: Optional[str] = None, 
                         config: Optional[OptimizationConfig] = None) -> OptimizedDatabase:
    """Get global optimized database instance"""
    global optimized_db
    
    if optimized_db is None:
        if database_path is None:
            database_path = os.getenv('DATABASE_PATH', 'data/nexusscan.db')
        
        optimized_db = OptimizedDatabase(database_path, config)
    
    return optimized_db

def close_optimized_database():
    """Close global optimized database"""
    global optimized_db
    
    if optimized_db:
        optimized_db.close()
        optimized_db = None