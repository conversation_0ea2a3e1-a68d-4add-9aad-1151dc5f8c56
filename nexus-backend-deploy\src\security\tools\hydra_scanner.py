"""
Hydra Scanner Integration for NexusScan Desktop Application
Network logon cracker for password attacks.
"""

import asyncio
import logging
from datetime import datetime
from typing import Dict, List, Optional, Any
from dataclasses import dataclass

from security.tools.tool_registry import (
    ToolMetadata, ToolCategory, ToolCapabilities,
    ScanOptions, ScanResult, ToolStatus, register_tool
)
from security.tools.base_scanner import BaseScanner

logger = logging.getLogger(__name__)


@dataclass
class HydraScanOptions(ScanOptions):
    """Hydra-specific scan options"""
    service: str = "ssh"
    username: str = "admin"
    password_list: str = "/usr/share/wordlists/rockyou.txt"


@register_tool
class HydraScanner(BaseScanner):
    """Network logon cracker for password attacks"""
    
    def __init__(self):
        super().__init__(
            capabilities=ToolCapabilities(
                supports_async=True,
                requires_root=False,
                network_access_required=True,
                supported_targets=["ip", "domain"]
            )
        )
    
    def get_metadata(self) -> ToolMetadata:
        """Get tool metadata"""
        return ToolMetadata(
            name="hydra",
            display_name="Hydra",
            category=ToolCategory.PAYLOAD_GENERATOR,
            description="Network logon cracker for password attacks",
            author="van Hauser",
            version="9.5"
        )

    async def check_native_availability(self) -> bool:
        try:
            result = await asyncio.create_subprocess_exec(
                "hydra", "-h",
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            stdout, stderr = await result.communicate()
            return result.returncode == 0
        except FileNotFoundError:
            return False
    
    async def execute_native_scan(self, target: str, options: HydraScanOptions) -> ScanResult:
        try:
            cmd = ["hydra", "-l", options.username, "-P", options.password_list, target, options.service]
            
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await process.communicate()
            
            if process.returncode == 0:
                return ScanResult(
                    success=True,
                    data={"raw_output": stdout.decode()},
                    scan_time=datetime.now(),
                    targets_scanned=[target]
                )
            else:
                return ScanResult(
                    success=False,
                    error=f"Hydra failed: {stderr.decode()}",
                    scan_time=datetime.now(),
                    targets_scanned=[target]
                )
                
        except Exception as e:
            return ScanResult(
                success=False,
                error=str(e),
                scan_time=datetime.now(),
                targets_scanned=[target]
            )

hydra_scanner = HydraScanner()