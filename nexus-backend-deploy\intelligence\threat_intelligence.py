#!/usr/bin/env python3
"""
Real-time Threat Intelligence Integration
Advanced threat intelligence collection, analysis, and correlation system
with multiple intelligence sources and AI-enhanced threat detection
"""

import asyncio
import aiohttp
import json
import logging
import hashlib
import time
from typing import Any, Dict, List, Optional, Set, Tuple, Union
from dataclasses import dataclass, field, asdict
from datetime import datetime, timed<PERSON><PERSON>
from enum import Enum
from pathlib import Path
import sqlite3
import threading

try:
    from ..ai.orchestrator import AIOrchestrator, AIRequest, TaskType, get_ai_orchestrator
    from ..core.events import EventManager, EventTypes
except ImportError:
    # Handle import for standalone testing
    import sys
    from pathlib import Path
    sys.path.append(str(Path(__file__).parent.parent))
    from ai.orchestrator import AIOrchestrator, AIRequest, TaskType, get_ai_orchestrator
    from core.events import EventManager, EventTypes

logger = logging.getLogger(__name__)

class ThreatLevel(Enum):
    """Threat severity levels"""
    INFO = "info"
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

class IndicatorType(Enum):
    """Types of threat indicators"""
    IP_ADDRESS = "ip_address"
    DOMAIN = "domain"
    URL = "url"
    FILE_HASH = "file_hash"
    EMAIL = "email"
    CVE = "cve"
    MALWARE_FAMILY = "malware_family"
    ATTACK_PATTERN = "attack_pattern"
    YARA_RULE = "yara_rule"

class IntelligenceSource(Enum):
    """Threat intelligence sources"""
    MISP = "misp"
    VIRUSTOTAL = "virustotal"
    SHODAN = "shodan"
    ABUSE_CH = "abuse_ch"
    OTXALIENVAULT = "otx_alienvault"
    THREATCROWD = "threatcrowd"
    HYBRID_ANALYSIS = "hybrid_analysis"
    URLVOID = "urlvoid"
    INTERNAL = "internal"
    AI_GENERATED = "ai_generated"

@dataclass
class ThreatIndicator:
    """Threat indicator of compromise (IoC)"""
    indicator_id: str
    indicator_type: IndicatorType
    value: str
    threat_level: ThreatLevel
    confidence: float
    source: IntelligenceSource
    first_seen: datetime
    last_seen: datetime
    description: str = ""
    tags: List[str] = field(default_factory=list)
    context: Dict[str, Any] = field(default_factory=dict)
    related_indicators: List[str] = field(default_factory=list)
    ttl_hours: int = 24
    
    def is_expired(self) -> bool:
        """Check if indicator has expired"""
        return datetime.now() - self.last_seen > timedelta(hours=self.ttl_hours)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        result = asdict(self)
        result['first_seen'] = self.first_seen.isoformat()
        result['last_seen'] = self.last_seen.isoformat()
        result['indicator_type'] = self.indicator_type.value
        result['threat_level'] = self.threat_level.value
        result['source'] = self.source.value
        return result

@dataclass
class ThreatIntelligenceReport:
    """Comprehensive threat intelligence report"""
    report_id: str
    target: str
    analysis_timestamp: datetime
    threat_indicators: List[ThreatIndicator]
    risk_score: float
    threat_summary: str
    ai_analysis: str
    recommendations: List[str]
    mitre_tactics: List[str] = field(default_factory=list)
    attribution: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        result = asdict(self)
        result['analysis_timestamp'] = self.analysis_timestamp.isoformat()
        result['threat_indicators'] = [ti.to_dict() for ti in self.threat_indicators]
        return result

class ThreatIntelligenceFeed:
    """Base class for threat intelligence feeds"""
    
    def __init__(self, source: IntelligenceSource, config: Dict[str, Any]):
        self.source = source
        self.config = config
        self.session: Optional[aiohttp.ClientSession] = None
        self.rate_limit_delay = config.get('rate_limit_delay', 1.0)
        self.last_request_time = 0.0
    
    async def initialize(self):
        """Initialize the feed"""
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=30),
            headers={'User-Agent': 'NexusScan-ThreatIntel/1.0'}
        )
    
    async def cleanup(self):
        """Cleanup resources"""
        if self.session:
            await self.session.close()
    
    async def _rate_limit(self):
        """Apply rate limiting"""
        current_time = time.time()
        time_since_last = current_time - self.last_request_time
        if time_since_last < self.rate_limit_delay:
            await asyncio.sleep(self.rate_limit_delay - time_since_last)
        self.last_request_time = time.time()
    
    async def query_indicator(self, indicator: str, indicator_type: IndicatorType) -> List[ThreatIndicator]:
        """Query for threat indicators"""
        raise NotImplementedError

class VirusTotalFeed(ThreatIntelligenceFeed):
    """VirusTotal threat intelligence feed"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(IntelligenceSource.VIRUSTOTAL, config)
        self.api_key = config.get('api_key')
        self.base_url = "https://www.virustotal.com/vtapi/v2"
    
    async def query_indicator(self, indicator: str, indicator_type: IndicatorType) -> List[ThreatIndicator]:
        """Query VirusTotal for indicator information"""
        if not self.api_key:
            return []
        
        await self._rate_limit()
        
        try:
            if indicator_type == IndicatorType.IP_ADDRESS:
                url = f"{self.base_url}/ip-address/report"
                params = {'apikey': self.api_key, 'ip': indicator}
            elif indicator_type == IndicatorType.DOMAIN:
                url = f"{self.base_url}/domain/report"
                params = {'apikey': self.api_key, 'domain': indicator}
            elif indicator_type == IndicatorType.FILE_HASH:
                url = f"{self.base_url}/file/report"
                params = {'apikey': self.api_key, 'resource': indicator}
            elif indicator_type == IndicatorType.URL:
                url = f"{self.base_url}/url/report"
                params = {'apikey': self.api_key, 'resource': indicator}
            else:
                return []
            
            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    return self._parse_virustotal_response(indicator, indicator_type, data)
                else:
                    logger.warning(f"VirusTotal API error: {response.status}")
                    return []
                    
        except Exception as e:
            logger.error(f"VirusTotal query failed: {e}")
            return []
    
    def _parse_virustotal_response(self, indicator: str, indicator_type: IndicatorType, data: Dict[str, Any]) -> List[ThreatIndicator]:
        """Parse VirusTotal API response"""
        indicators = []
        
        if data.get('response_code') == 1:
            positives = data.get('positives', 0)
            total = data.get('total', 0)
            
            if positives > 0:
                confidence = (positives / total) * 100 if total > 0 else 0
                
                # Determine threat level based on detection ratio
                if confidence >= 80:
                    threat_level = ThreatLevel.CRITICAL
                elif confidence >= 60:
                    threat_level = ThreatLevel.HIGH
                elif confidence >= 40:
                    threat_level = ThreatLevel.MEDIUM
                elif confidence >= 20:
                    threat_level = ThreatLevel.LOW
                else:
                    threat_level = ThreatLevel.INFO
                
                indicator_obj = ThreatIndicator(
                    indicator_id=hashlib.md5(f"vt_{indicator}".encode()).hexdigest(),
                    indicator_type=indicator_type,
                    value=indicator,
                    threat_level=threat_level,
                    confidence=confidence,
                    source=self.source,
                    first_seen=datetime.now(),
                    last_seen=datetime.now(),
                    description=f"Detected by {positives}/{total} engines",
                    context={
                        'positives': positives,
                        'total': total,
                        'scan_date': data.get('scan_date'),
                        'permalink': data.get('permalink')
                    }
                )
                
                indicators.append(indicator_obj)
        
        return indicators

class AbuseChFeed(ThreatIntelligenceFeed):
    """Abuse.ch threat intelligence feed"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(IntelligenceSource.ABUSE_CH, config)
        self.base_url = "https://urlhaus-api.abuse.ch/v1"
    
    async def query_indicator(self, indicator: str, indicator_type: IndicatorType) -> List[ThreatIndicator]:
        """Query Abuse.ch for indicator information"""
        await self._rate_limit()
        
        try:
            if indicator_type == IndicatorType.URL:
                url = f"{self.base_url}/url/"
                data = {'url': indicator}
            elif indicator_type == IndicatorType.DOMAIN:
                url = f"{self.base_url}/host/"
                data = {'host': indicator}
            else:
                return []
            
            async with self.session.post(url, data=data) as response:
                if response.status == 200:
                    result = await response.json()
                    return self._parse_abuse_ch_response(indicator, indicator_type, result)
                else:
                    logger.warning(f"Abuse.ch API error: {response.status}")
                    return []
                    
        except Exception as e:
            logger.error(f"Abuse.ch query failed: {e}")
            return []
    
    def _parse_abuse_ch_response(self, indicator: str, indicator_type: IndicatorType, data: Dict[str, Any]) -> List[ThreatIndicator]:
        """Parse Abuse.ch API response"""
        indicators = []
        
        if data.get('query_status') == 'ok':
            urls = data.get('urls', [])
            
            for url_data in urls:
                if url_data.get('url_status') == 'online':
                    threat_level = ThreatLevel.HIGH
                elif url_data.get('url_status') == 'offline':
                    threat_level = ThreatLevel.MEDIUM
                else:
                    threat_level = ThreatLevel.LOW
                
                indicator_obj = ThreatIndicator(
                    indicator_id=hashlib.md5(f"abuse_ch_{indicator}".encode()).hexdigest(),
                    indicator_type=indicator_type,
                    value=indicator,
                    threat_level=threat_level,
                    confidence=85.0,  # High confidence for abuse.ch
                    source=self.source,
                    first_seen=datetime.fromisoformat(url_data.get('date_added', datetime.now().isoformat())),
                    last_seen=datetime.now(),
                    description=f"Malicious URL reported to abuse.ch",
                    tags=url_data.get('tags', []),
                    context={
                        'url_status': url_data.get('url_status'),
                        'threat': url_data.get('threat'),
                        'malware': url_data.get('malware')
                    }
                )
                
                indicators.append(indicator_obj)
        
        return indicators

class InternalThreatFeed(ThreatIntelligenceFeed):
    """Internal threat intelligence feed for custom indicators"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(IntelligenceSource.INTERNAL, config)
        self.db_path = config.get('db_path', 'data/threat_intel.db')
        self.indicators: Dict[str, ThreatIndicator] = {}
        self._lock = threading.RLock()
        self._initialize_db()
    
    def _initialize_db(self):
        """Initialize internal threat intelligence database"""
        Path(self.db_path).parent.mkdir(parents=True, exist_ok=True)
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS threat_indicators (
                indicator_id TEXT PRIMARY KEY,
                indicator_type TEXT NOT NULL,
                value TEXT NOT NULL,
                threat_level TEXT NOT NULL,
                confidence REAL NOT NULL,
                source TEXT NOT NULL,
                first_seen TEXT NOT NULL,
                last_seen TEXT NOT NULL,
                description TEXT,
                tags TEXT,
                context TEXT,
                related_indicators TEXT,
                ttl_hours INTEGER DEFAULT 24
            )
        """)
        
        conn.commit()
        conn.close()
        
        # Load existing indicators
        self._load_indicators()
    
    def _load_indicators(self):
        """Load indicators from database"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute("SELECT * FROM threat_indicators")
        rows = cursor.fetchall()
        
        for row in rows:
            try:
                indicator = ThreatIndicator(
                    indicator_id=row[0],
                    indicator_type=IndicatorType(row[1]),
                    value=row[2],
                    threat_level=ThreatLevel(row[3]),
                    confidence=row[4],
                    source=IntelligenceSource(row[5]),
                    first_seen=datetime.fromisoformat(row[6]),
                    last_seen=datetime.fromisoformat(row[7]),
                    description=row[8] or "",
                    tags=json.loads(row[9]) if row[9] else [],
                    context=json.loads(row[10]) if row[10] else {},
                    related_indicators=json.loads(row[11]) if row[11] else [],
                    ttl_hours=row[12]
                )
                
                # Only load non-expired indicators
                if not indicator.is_expired():
                    self.indicators[indicator.value] = indicator
                    
            except Exception as e:
                logger.warning(f"Failed to load indicator from DB: {e}")
        
        conn.close()
        logger.info(f"Loaded {len(self.indicators)} internal threat indicators")
    
    async def query_indicator(self, indicator: str, indicator_type: IndicatorType) -> List[ThreatIndicator]:
        """Query internal indicators"""
        with self._lock:
            if indicator in self.indicators:
                return [self.indicators[indicator]]
            return []
    
    def add_indicator(self, indicator: ThreatIndicator):
        """Add indicator to internal feed"""
        with self._lock:
            self.indicators[indicator.value] = indicator
            self._save_indicator(indicator)
    
    def _save_indicator(self, indicator: ThreatIndicator):
        """Save indicator to database"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute("""
            INSERT OR REPLACE INTO threat_indicators 
            (indicator_id, indicator_type, value, threat_level, confidence, source,
             first_seen, last_seen, description, tags, context, related_indicators, ttl_hours)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            indicator.indicator_id,
            indicator.indicator_type.value,
            indicator.value,
            indicator.threat_level.value,
            indicator.confidence,
            indicator.source.value,
            indicator.first_seen.isoformat(),
            indicator.last_seen.isoformat(),
            indicator.description,
            json.dumps(indicator.tags),
            json.dumps(indicator.context),
            json.dumps(indicator.related_indicators),
            indicator.ttl_hours
        ))
        
        conn.commit()
        conn.close()

class ThreatIntelligenceEngine:
    """Main threat intelligence engine with multi-source aggregation"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        self.config = config or {}
        self.feeds: Dict[IntelligenceSource, ThreatIntelligenceFeed] = {}
        self.ai_orchestrator = get_ai_orchestrator()
        self.cache: Dict[str, List[ThreatIndicator]] = {}
        self.cache_ttl = self.config.get('cache_ttl_minutes', 30)
        self._lock = threading.RLock()
        
        # Analytics
        self.query_metrics = {
            'total_queries': 0,
            'cache_hits': 0,
            'source_queries': {},
            'threat_levels': {}
        }
        
        # Initialize feeds
        self._initialize_feeds()
    
    def _initialize_feeds(self):
        """Initialize configured threat intelligence feeds"""
        feed_configs = self.config.get('feeds', {})
        
        # VirusTotal
        if 'virustotal' in feed_configs:
            self.feeds[IntelligenceSource.VIRUSTOTAL] = VirusTotalFeed(feed_configs['virustotal'])
        
        # Abuse.ch
        if 'abuse_ch' in feed_configs:
            self.feeds[IntelligenceSource.ABUSE_CH] = AbuseChFeed(feed_configs['abuse_ch'])
        
        # Internal feed (always enabled)
        self.feeds[IntelligenceSource.INTERNAL] = InternalThreatFeed(
            feed_configs.get('internal', {'db_path': 'data/threat_intel.db'})
        )
        
        logger.info(f"Initialized {len(self.feeds)} threat intelligence feeds")
    
    async def initialize(self):
        """Initialize all feeds"""
        for feed in self.feeds.values():
            await feed.initialize()
    
    async def cleanup(self):
        """Cleanup all feeds"""
        for feed in self.feeds.values():
            await feed.cleanup()
    
    async def query_threat_intelligence(self, 
                                      indicators: List[Tuple[str, IndicatorType]],
                                      use_ai_analysis: bool = True) -> ThreatIntelligenceReport:
        """Query multiple indicators and generate comprehensive threat report"""
        
        report_id = hashlib.md5(f"{time.time()}_{len(indicators)}".encode()).hexdigest()
        all_threat_indicators = []
        
        # Query each indicator across all feeds
        for indicator_value, indicator_type in indicators:
            threat_indicators = await self._query_single_indicator(indicator_value, indicator_type)
            all_threat_indicators.extend(threat_indicators)
        
        # Deduplicate and merge indicators
        merged_indicators = self._merge_indicators(all_threat_indicators)
        
        # Calculate overall risk score
        risk_score = self._calculate_risk_score(merged_indicators)
        
        # Generate AI analysis if enabled
        ai_analysis = ""
        recommendations = []
        mitre_tactics = []
        
        if use_ai_analysis and merged_indicators:
            ai_result = await self._generate_ai_analysis(merged_indicators)
            ai_analysis = ai_result.get('analysis', '')
            recommendations = ai_result.get('recommendations', [])
            mitre_tactics = ai_result.get('mitre_tactics', [])
        
        # Generate threat summary
        threat_summary = self._generate_threat_summary(merged_indicators)
        
        # Create report
        report = ThreatIntelligenceReport(
            report_id=report_id,
            target=", ".join([ind[0] for ind in indicators]),
            analysis_timestamp=datetime.now(),
            threat_indicators=merged_indicators,
            risk_score=risk_score,
            threat_summary=threat_summary,
            ai_analysis=ai_analysis,
            recommendations=recommendations,
            mitre_tactics=mitre_tactics
        )
        
        # Update metrics
        self._update_metrics(indicators, merged_indicators)
        
        return report
    
    async def _query_single_indicator(self, indicator: str, indicator_type: IndicatorType) -> List[ThreatIndicator]:
        """Query single indicator across all feeds"""
        cache_key = f"{indicator}_{indicator_type.value}"
        
        # Check cache first
        with self._lock:
            if cache_key in self.cache:
                cached_time, cached_indicators = self.cache[cache_key]
                if datetime.now() - cached_time < timedelta(minutes=self.cache_ttl):
                    self.query_metrics['cache_hits'] += 1
                    return cached_indicators
        
        # Query all feeds
        all_indicators = []
        tasks = []
        
        for source, feed in self.feeds.items():
            task = feed.query_indicator(indicator, indicator_type)
            tasks.append((source, task))
        
        # Execute queries concurrently
        for source, task in tasks:
            try:
                indicators = await task
                all_indicators.extend(indicators)
                
                # Update source metrics
                if source.value not in self.query_metrics['source_queries']:
                    self.query_metrics['source_queries'][source.value] = 0
                self.query_metrics['source_queries'][source.value] += 1
                
            except Exception as e:
                logger.error(f"Feed {source.value} query failed: {e}")
        
        # Cache results
        with self._lock:
            self.cache[cache_key] = (datetime.now(), all_indicators)
        
        return all_indicators
    
    def _merge_indicators(self, indicators: List[ThreatIndicator]) -> List[ThreatIndicator]:
        """Merge duplicate indicators from different sources"""
        merged = {}
        
        for indicator in indicators:
            key = f"{indicator.value}_{indicator.indicator_type.value}"
            
            if key in merged:
                # Merge with existing indicator
                existing = merged[key]
                
                # Take highest threat level
                if indicator.threat_level.value == 'critical' or existing.threat_level.value == 'critical':
                    existing.threat_level = ThreatLevel.CRITICAL
                elif indicator.threat_level.value == 'high' or existing.threat_level.value == 'high':
                    existing.threat_level = ThreatLevel.HIGH
                elif indicator.threat_level.value == 'medium' or existing.threat_level.value == 'medium':
                    existing.threat_level = ThreatLevel.MEDIUM
                
                # Average confidence scores
                existing.confidence = (existing.confidence + indicator.confidence) / 2
                
                # Merge tags and context
                existing.tags = list(set(existing.tags + indicator.tags))
                existing.context.update(indicator.context)
                
                # Update last seen
                if indicator.last_seen > existing.last_seen:
                    existing.last_seen = indicator.last_seen
                
                # Append to description
                if indicator.description and indicator.description not in existing.description:
                    existing.description += f"; {indicator.description}"
                
            else:
                merged[key] = indicator
        
        return list(merged.values())
    
    def _calculate_risk_score(self, indicators: List[ThreatIndicator]) -> float:
        """Calculate overall risk score from indicators"""
        if not indicators:
            return 0.0
        
        threat_weights = {
            ThreatLevel.INFO: 1.0,
            ThreatLevel.LOW: 2.5,
            ThreatLevel.MEDIUM: 5.0,
            ThreatLevel.HIGH: 7.5,
            ThreatLevel.CRITICAL: 10.0
        }
        
        total_score = 0.0
        total_weight = 0.0
        
        for indicator in indicators:
            weight = threat_weights.get(indicator.threat_level, 1.0)
            confidence_factor = indicator.confidence / 100.0
            
            score = weight * confidence_factor
            total_score += score
            total_weight += weight
        
        # Normalize to 0-10 scale
        if total_weight > 0:
            normalized_score = min(10.0, (total_score / len(indicators)) * 1.2)
        else:
            normalized_score = 0.0
        
        return round(normalized_score, 2)
    
    async def _generate_ai_analysis(self, indicators: List[ThreatIndicator]) -> Dict[str, Any]:
        """Generate AI-powered threat analysis"""
        
        # Prepare indicators summary for AI
        indicators_summary = []
        for indicator in indicators:
            indicators_summary.append({
                'type': indicator.indicator_type.value,
                'value': indicator.value,
                'threat_level': indicator.threat_level.value,
                'confidence': indicator.confidence,
                'source': indicator.source.value,
                'description': indicator.description,
                'tags': indicator.tags
            })
        
        prompt = f"""
Analyze the following threat intelligence indicators and provide comprehensive security insights:

Threat Indicators: {json.dumps(indicators_summary, indent=2)}

Please provide:
1. Overall threat assessment and attack scenario analysis
2. Potential impact and business risk evaluation
3. Attribution and threat actor analysis (if possible)
4. MITRE ATT&CK tactics and techniques mapping
5. Specific security recommendations and countermeasures
6. Incident response priorities and actions

Focus on actionable intelligence and strategic security implications.
Format the response with clear sections for each analysis area.
"""
        
        ai_request = AIRequest(
            task_type=TaskType.THREAT_INTELLIGENCE,
            prompt=prompt,
            max_tokens=2000,
            temperature=0.3,
            priority=2
        )
        
        try:
            response = await self.ai_orchestrator.process_request(ai_request)
            
            # Parse AI response
            analysis_text = response.content
            
            # Extract structured information
            recommendations = []
            mitre_tactics = []
            
            lines = analysis_text.split('\n')
            current_section = None
            
            for line in lines:
                line = line.strip()
                if 'recommendation' in line.lower():
                    current_section = 'recommendations'
                elif 'mitre' in line.lower() or 'tactic' in line.lower():
                    current_section = 'mitre'
                elif current_section == 'recommendations' and line.startswith(('•', '-', '1.', '2.', '3.')):
                    recommendations.append(line)
                elif current_section == 'mitre' and ('T' in line and any(char.isdigit() for char in line)):
                    mitre_tactics.append(line)
            
            return {
                'analysis': analysis_text,
                'recommendations': recommendations[:5],  # Top 5 recommendations
                'mitre_tactics': mitre_tactics[:10]  # Top 10 tactics
            }
            
        except Exception as e:
            logger.error(f"AI threat analysis failed: {e}")
            return {
                'analysis': 'AI analysis unavailable due to processing error.',
                'recommendations': ['Manually review threat indicators', 'Implement monitoring for detected IoCs'],
                'mitre_tactics': []
            }
    
    def _generate_threat_summary(self, indicators: List[ThreatIndicator]) -> str:
        """Generate human-readable threat summary"""
        if not indicators:
            return "No threat indicators identified."
        
        # Count by threat level
        level_counts = {}
        for indicator in indicators:
            level = indicator.threat_level.value
            level_counts[level] = level_counts.get(level, 0) + 1
        
        # Count by type
        type_counts = {}
        for indicator in indicators:
            itype = indicator.indicator_type.value
            type_counts[itype] = type_counts.get(itype, 0) + 1
        
        summary_parts = []
        
        # Threat level summary
        if level_counts.get('critical', 0) > 0:
            summary_parts.append(f"{level_counts['critical']} critical threats")
        if level_counts.get('high', 0) > 0:
            summary_parts.append(f"{level_counts['high']} high-risk threats")
        if level_counts.get('medium', 0) > 0:
            summary_parts.append(f"{level_counts['medium']} medium-risk threats")
        
        # Type summary
        type_summary = ", ".join([f"{count} {itype.replace('_', ' ')}" for itype, count in type_counts.items()])
        
        if summary_parts:
            threat_summary = f"Identified {', '.join(summary_parts)} including {type_summary}"
        else:
            threat_summary = f"Identified {len(indicators)} threat indicators: {type_summary}"
        
        return threat_summary
    
    def _update_metrics(self, queried_indicators: List[Tuple[str, IndicatorType]], found_indicators: List[ThreatIndicator]):
        """Update query and threat metrics"""
        self.query_metrics['total_queries'] += len(queried_indicators)
        
        for indicator in found_indicators:
            level = indicator.threat_level.value
            if level not in self.query_metrics['threat_levels']:
                self.query_metrics['threat_levels'][level] = 0
            self.query_metrics['threat_levels'][level] += 1
    
    async def add_custom_indicator(self, 
                                 indicator_value: str,
                                 indicator_type: IndicatorType,
                                 threat_level: ThreatLevel,
                                 description: str,
                                 tags: Optional[List[str]] = None,
                                 ttl_hours: int = 24) -> ThreatIndicator:
        """Add custom threat indicator to internal feed"""
        
        indicator = ThreatIndicator(
            indicator_id=hashlib.md5(f"custom_{indicator_value}_{time.time()}".encode()).hexdigest(),
            indicator_type=indicator_type,
            value=indicator_value,
            threat_level=threat_level,
            confidence=95.0,  # High confidence for manual additions
            source=IntelligenceSource.INTERNAL,
            first_seen=datetime.now(),
            last_seen=datetime.now(),
            description=description,
            tags=tags or [],
            ttl_hours=ttl_hours
        )
        
        # Add to internal feed
        internal_feed = self.feeds.get(IntelligenceSource.INTERNAL)
        if internal_feed:
            internal_feed.add_indicator(indicator)
        
        logger.info(f"Added custom threat indicator: {indicator_value}")
        return indicator
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get threat intelligence statistics"""
        return {
            'query_metrics': self.query_metrics.copy(),
            'cache_size': len(self.cache),
            'active_feeds': list(self.feeds.keys()),
            'cache_hit_rate': (
                self.query_metrics['cache_hits'] / max(1, self.query_metrics['total_queries']) * 100
            )
        }
    
    def clear_cache(self):
        """Clear the intelligence cache"""
        with self._lock:
            self.cache.clear()
        logger.info("Threat intelligence cache cleared")

# Global threat intelligence engine instance
threat_intelligence_engine: Optional[ThreatIntelligenceEngine] = None

def get_threat_intelligence_engine() -> ThreatIntelligenceEngine:
    """Get global threat intelligence engine instance"""
    global threat_intelligence_engine
    
    if threat_intelligence_engine is None:
        # Default configuration
        config = {
            'cache_ttl_minutes': 30,
            'feeds': {
                'internal': {'db_path': 'data/threat_intel.db'},
                # Additional feeds can be configured here
            }
        }
        threat_intelligence_engine = ThreatIntelligenceEngine(config)
    
    return threat_intelligence_engine

async def initialize_threat_intelligence():
    """Initialize global threat intelligence engine"""
    engine = get_threat_intelligence_engine()
    await engine.initialize()
    return engine

async def close_threat_intelligence():
    """Close global threat intelligence engine"""
    global threat_intelligence_engine
    
    if threat_intelligence_engine:
        await threat_intelligence_engine.cleanup()
        threat_intelligence_engine = None