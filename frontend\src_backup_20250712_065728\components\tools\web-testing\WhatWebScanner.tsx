/**
 * WhatWeb Scanner Component
 * Web technology identification tool for comprehensive web application fingerprinting
 */
import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { 
  Globe,
  Play,
  Square,
  Download,
  Settings,
  Target,
  Eye,
  FileText,
  Terminal,
  Copy,
  RotateCcw,
  Clock,
  Zap,
  Filter,
  AlertTriangle,
  CheckCircle,
  TrendingUp,
  Activity,
  Lock,
  Search,
  List,
  BarChart3,
  Database,
  Server,
  Bug,
  Info,
  Code,
  Package,
  Layers,
  Shield,
  Fingerprint,
  Cpu,
  Network,
  Gauge,
  Tag,
  Link,
  Hash
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useBackendStore } from '@/stores/backend-store';
import { apiClient, ToolExecution } from '@/services/api-client';
import { toast } from 'sonner';

/**
 * WhatWeb configuration interface
 */
interface WhatWebConfig {
  targets: string[];
  inputFile: string;
  aggression: number;
  plugins: {
    enabled: string[];
    disabled: string[];
    categories: string[];
  };
  output: {
    format: 'brief' | 'verbose' | 'xml' | 'json' | 'mongo' | 'logbrief' | 'csv';
    file: string;
    noColour: boolean;
    noErrors: boolean;
    userAgent: string;
    maxDepth: number;
    maxUrls: number;
  };
  http: {
    userAgent: string;
    customHeaders: { [key: string]: string };
    cookies: string;
    authentication: {
      enabled: boolean;
      username: string;
      password: string;
      type: 'basic' | 'digest' | 'ntlm';
    };
    proxy: {
      enabled: boolean;
      url: string;
      credentials: string;
    };
    timeout: number;
    maxRedirects: number;
    openTimeout: number;
    readTimeout: number;
  };
  crawling: {
    followRedirects: boolean;
    maxDepth: number;
    waitTime: number;
    urlPattern: string;
    urlFilter: string;
  };
  performance: {
    threads: number;
    maxThreads: number;
    wait: number;
  };
  logging: {
    verbose: boolean;
    debug: boolean;
    quiet: boolean;
    logErrors: string;
    logVerbose: string;
    logBrief: string;
  };
  customArgs: string;
}

/**
 * WhatWeb aggression levels
 */
const AGGRESSION_LEVELS = {
  1: {
    name: 'Passive',
    description: 'Makes one HTTP request, follows redirects',
    risk: 'low'
  },
  2: {
    name: 'Polite',
    description: 'Makes a few HTTP requests when necessary',
    risk: 'low'
  },
  3: {
    name: 'Aggressive',
    description: 'Can make many HTTP requests, uses common directory names',
    risk: 'medium'
  },
  4: {
    name: 'Heavy',
    description: 'Makes many HTTP requests, uses large lists of common files and directories',
    risk: 'high'
  }
};

/**
 * Common WhatWeb plugin categories
 */
const PLUGIN_CATEGORIES = {
  'cms': 'Content Management Systems',
  'ecommerce': 'E-commerce Platforms',
  'javascript': 'JavaScript Frameworks',
  'analytics': 'Analytics and Tracking',
  'advertising': 'Advertising Networks',
  'blogs': 'Blogging Platforms',
  'databases': 'Database Systems',
  'framework': 'Web Frameworks',
  'language': 'Programming Languages',
  'os': 'Operating Systems',
  'server': 'Web Servers',
  'security': 'Security Tools',
  'cdn': 'Content Delivery Networks',
  'caching': 'Caching Systems',
  'payment': 'Payment Processors',
  'social': 'Social Media'
};

/**
 * Output formats
 */
const OUTPUT_FORMATS = {
  'brief': { name: 'Brief', description: 'Default brief output' },
  'verbose': { name: 'Verbose', description: 'Detailed verbose output' },
  'xml': { name: 'XML', description: 'XML structured output' },
  'json': { name: 'JSON', description: 'JSON structured output' },
  'mongo': { name: 'MongoDB', description: 'MongoDB insert format' },
  'logbrief': { name: 'Log Brief', description: 'Greppable log format' },
  'csv': { name: 'CSV', description: 'Comma-separated values' }
};

/**
 * Common user agents
 */
const USER_AGENTS = {
  'default': 'WhatWeb/0.5.5',
  'chrome': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
  'firefox': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0',
  'safari': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15',
  'edge': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36 Edg/91.0.864.59',
  'mobile': 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1'
};

export default function WhatWebScanner() {
  const { connectionStatus } = useBackendStore();
  const [config, setConfig] = React.useState<WhatWebConfig>({
    targets: [''],
    inputFile: '',
    aggression: 1,
    plugins: {
      enabled: [],
      disabled: [],
      categories: []
    },
    output: {
      format: 'json',
      file: '',
      noColour: false,
      noErrors: false,
      userAgent: '',
      maxDepth: 3,
      maxUrls: 25
    },
    http: {
      userAgent: 'default',
      customHeaders: {},
      cookies: '',
      authentication: {
        enabled: false,
        username: '',
        password: '',
        type: 'basic'
      },
      proxy: {
        enabled: false,
        url: '',
        credentials: ''
      },
      timeout: 60,
      maxRedirects: 2,
      openTimeout: 15,
      readTimeout: 30
    },
    crawling: {
      followRedirects: true,
      maxDepth: 3,
      waitTime: 0,
      urlPattern: '',
      urlFilter: ''
    },
    performance: {
      threads: 25,
      maxThreads: 25,
      wait: 0
    },
    logging: {
      verbose: false,
      debug: false,
      quiet: false,
      logErrors: '',
      logVerbose: '',
      logBrief: ''
    },
    customArgs: ''
  });

  const [execution, setExecution] = React.useState<ToolExecution | null>(null);
  const [activeTab, setActiveTab] = React.useState('configure');

  /**
   * Handle configuration changes
   */
  const updateConfig = (path: string, value: any) => {
    setConfig(prev => {
      const keys = path.split('.');
      const newConfig = { ...prev };
      let current = newConfig;
      
      for (let i = 0; i < keys.length - 1; i++) {
        current[keys[i]] = { ...current[keys[i]] };
        current = current[keys[i]];
      }
      
      current[keys[keys.length - 1]] = value;
      return newConfig;
    });
  };

  /**
   * Handle target list changes
   */
  const updateTargets = (targets: string) => {
    const targetList = targets.split('\n').map(t => t.trim()).filter(t => t);
    updateConfig('targets', targetList);
  };

  /**
   * Handle preset configurations
   */
  const applyPreset = (preset: string) => {
    const presets = {
      'quick_scan': {
        aggression: 1,
        output: { ...config.output, format: 'json' },
        performance: { threads: 10, maxThreads: 10, wait: 0 },
        crawling: { ...config.crawling, maxDepth: 1 }
      },
      'comprehensive_scan': {
        aggression: 3,
        output: { ...config.output, format: 'json', maxDepth: 5, maxUrls: 100 },
        performance: { threads: 25, maxThreads: 25, wait: 0 },
        crawling: { ...config.crawling, maxDepth: 5 },
        plugins: { ...config.plugins, categories: ['cms', 'framework', 'server', 'language'] }
      },
      'stealth_scan': {
        aggression: 1,
        output: { ...config.output, format: 'json' },
        performance: { threads: 5, maxThreads: 5, wait: 2000 },
        crawling: { ...config.crawling, maxDepth: 2, waitTime: 2000 },
        http: { ...config.http, userAgent: 'chrome' }
      },
      'aggressive_scan': {
        aggression: 4,
        output: { ...config.output, format: 'verbose', maxDepth: 10, maxUrls: 500 },
        performance: { threads: 50, maxThreads: 50, wait: 0 },
        crawling: { ...config.crawling, maxDepth: 10 },
        plugins: { ...config.plugins, categories: Object.keys(PLUGIN_CATEGORIES) }
      },
      'technology_focus': {
        aggression: 2,
        output: { ...config.output, format: 'json' },
        plugins: { 
          ...config.plugins, 
          categories: ['cms', 'framework', 'javascript', 'server', 'language', 'databases'] 
        },
        performance: { threads: 20, maxThreads: 20, wait: 0 }
      },
      'security_focus': {
        aggression: 3,
        output: { ...config.output, format: 'verbose' },
        plugins: { ...config.plugins, categories: ['security', 'server', 'framework'] },
        performance: { threads: 15, maxThreads: 15, wait: 1000 }
      }
    };

    if (presets[preset]) {
      setConfig(prev => ({ ...prev, ...presets[preset] }));
    }
  };

  /**
   * Toggle plugin category
   */
  const togglePluginCategory = (category: string) => {
    const categories = config.plugins.categories.includes(category)
      ? config.plugins.categories.filter(c => c !== category)
      : [...config.plugins.categories, category];
    updateConfig('plugins.categories', categories);
  };

  /**
   * Start WhatWeb scan
   */
  const startScan = async () => {
    const validTargets = config.targets.filter(t => t.trim());
    if (validTargets.length === 0 && !config.inputFile) {
      toast.error('Please provide at least one target URL or input file');
      return;
    }

    try {
      const execution = await apiClient.executeSecurityTool('whatweb', config);
      setExecution(execution);
      setActiveTab('output');
      toast.success('WhatWeb scan started successfully');
    } catch (error) {
      toast.error(`Failed to start WhatWeb scan: ${error.message}`);
    }
  };

  /**
   * Stop scan
   */
  const stopScan = async () => {
    if (execution) {
      try {
        await apiClient.stopToolExecution(execution.id);
        toast.success('WhatWeb scan stopped');
      } catch (error) {
        toast.error(`Failed to stop scan: ${error.message}`);
      }
    }
  };

  /**
   * Generate command line
   */
  const generateCommand = () => {
    let cmd = 'whatweb';
    
    // Aggression level
    cmd += ` -a ${config.aggression}`;
    
    // Output format
    if (config.output.format !== 'brief') {
      cmd += ` --log-${config.output.format}=${config.output.file || '/dev/stdout'}`;
    }
    
    // User agent
    if (config.http.userAgent !== 'default') {
      const userAgent = USER_AGENTS[config.http.userAgent] || config.http.userAgent;
      cmd += ` --user-agent="${userAgent}"`;
    }
    
    // Custom headers
    Object.entries(config.http.customHeaders).forEach(([key, value]) => {
      cmd += ` --header="${key}: ${value}"`;
    });
    
    // Cookies
    if (config.http.cookies) {
      cmd += ` --cookie="${config.http.cookies}"`;
    }
    
    // Authentication
    if (config.http.authentication.enabled) {
      cmd += ` --user="${config.http.authentication.username}"`;
      cmd += ` --password="${config.http.authentication.password}"`;
    }
    
    // Proxy
    if (config.http.proxy.enabled && config.http.proxy.url) {
      cmd += ` --proxy="${config.http.proxy.url}"`;
    }
    
    // HTTP settings
    if (config.http.timeout !== 60) {
      cmd += ` --read-timeout=${config.http.timeout}`;
    }
    
    if (config.http.maxRedirects !== 2) {
      cmd += ` --max-redirects=${config.http.maxRedirects}`;
    }
    
    if (config.http.openTimeout !== 15) {
      cmd += ` --open-timeout=${config.http.openTimeout}`;
    }
    
    // Performance
    if (config.performance.threads !== 25) {
      cmd += ` -t ${config.performance.threads}`;
    }
    
    if (config.performance.wait > 0) {
      cmd += ` --wait=${config.performance.wait}`;
    }
    
    // Crawling
    if (config.crawling.maxDepth !== 3) {
      cmd += ` --max-depth=${config.crawling.maxDepth}`;
    }
    
    if (!config.crawling.followRedirects) {
      cmd += ' --no-redirect';
    }
    
    if (config.crawling.urlPattern) {
      cmd += ` --url-pattern="${config.crawling.urlPattern}"`;
    }
    
    if (config.crawling.urlFilter) {
      cmd += ` --url-filter="${config.crawling.urlFilter}"`;
    }
    
    // Plugin categories
    if (config.plugins.categories.length > 0) {
      cmd += ` --plugins="${config.plugins.categories.join(',')}"`;
    }
    
    // Enabled plugins
    if (config.plugins.enabled.length > 0) {
      cmd += ` --plugins="+${config.plugins.enabled.join(',+')}"`;
    }
    
    // Disabled plugins
    if (config.plugins.disabled.length > 0) {
      cmd += ` --plugins="-${config.plugins.disabled.join(',-')}"`;
    }
    
    // Logging
    if (config.logging.verbose) {
      cmd += ' --verbose';
    }
    
    if (config.logging.quiet) {
      cmd += ' --quiet';
    }
    
    if (config.output.noColour) {
      cmd += ' --colour=never';
    }
    
    if (config.output.noErrors) {
      cmd += ' --no-errors';
    }
    
    // Custom arguments
    if (config.customArgs) {
      cmd += ` ${config.customArgs}`;
    }
    
    // Input file or targets
    if (config.inputFile) {
      cmd += ` -i "${config.inputFile}"`;
    } else {
      config.targets.filter(t => t.trim()).forEach(target => {
        cmd += ` "${target}"`;
      });
    }
    
    return cmd;
  };

  const isScanning = execution?.status === 'running';
  const canStart = connectionStatus === 'connected' && 
    ((config.targets.some(t => t.trim()) || config.inputFile)) && !isScanning;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center gap-3">
        <div className="flex items-center justify-center w-10 h-10 rounded-lg bg-gradient-to-br from-green-500 to-teal-600">
          <Fingerprint className="h-5 w-5 text-white" />
        </div>
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100">WhatWeb Scanner</h2>
          <p className="text-gray-600 dark:text-gray-400">Web technology identification and fingerprinting tool</p>
        </div>
      </div>

      {/* Status Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Status</p>
                <p className="text-2xl font-bold">
                  {isScanning ? 'Scanning' : 'Ready'}
                </p>
              </div>
              <Activity className={cn("h-8 w-8", isScanning ? "text-green-600" : "text-gray-400")} />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Aggression</p>
                <p className="text-2xl font-bold">{AGGRESSION_LEVELS[config.aggression].name}</p>
              </div>
              <Gauge className={cn("h-8 w-8", 
                config.aggression === 1 ? "text-green-600" :
                config.aggression === 2 ? "text-yellow-600" :
                config.aggression === 3 ? "text-orange-600" : "text-red-600"
              )} />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Targets</p>
                <p className="text-2xl font-bold">{config.targets.filter(t => t.trim()).length}</p>
              </div>
              <Target className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Progress</p>
                <p className="text-2xl font-bold">{execution ? `${execution.progress}%` : '0%'}</p>
              </div>
              <BarChart3 className="h-8 w-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Interface */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Globe className="h-5 w-5" />
                WhatWeb Configuration
              </CardTitle>
              <CardDescription>
                Configure web technology identification parameters and start fingerprinting
              </CardDescription>
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => navigator.clipboard.writeText(generateCommand())}
              >
                <Copy className="h-4 w-4 mr-2" />
                Copy Command
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setConfig(prev => ({ 
                  ...prev, 
                  targets: [''], 
                  inputFile: '', 
                  http: { ...prev.http, customHeaders: {}, cookies: '' },
                  customArgs: ''
                }))}
              >
                <RotateCcw className="h-4 w-4 mr-2" />
                Reset
              </Button>
              {isScanning ? (
                <Button variant="destructive" size="sm" onClick={stopScan}>
                  <Square className="h-4 w-4 mr-2" />
                  Stop Scan
                </Button>
              ) : (
                <Button 
                  size="sm" 
                  onClick={startScan}
                  disabled={!canStart}
                >
                  <Play className="h-4 w-4 mr-2" />
                  Start Scan
                </Button>
              )}
            </div>
          </div>
        </CardHeader>

        <CardContent>
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="configure">Configure</TabsTrigger>
              <TabsTrigger value="output">Output</TabsTrigger>
              <TabsTrigger value="results">Results</TabsTrigger>
              <TabsTrigger value="command">Command</TabsTrigger>
            </TabsList>

            <TabsContent value="configure" className="space-y-6">
              {/* Quick Presets */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Quick Presets</h3>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                  <Button variant="outline" onClick={() => applyPreset('quick_scan')}>
                    <Zap className="h-4 w-4 mr-2" />
                    Quick Scan
                  </Button>
                  <Button variant="outline" onClick={() => applyPreset('comprehensive_scan')}>
                    <Search className="h-4 w-4 mr-2" />
                    Comprehensive
                  </Button>
                  <Button variant="outline" onClick={() => applyPreset('stealth_scan')}>
                    <Shield className="h-4 w-4 mr-2" />
                    Stealth Scan
                  </Button>
                  <Button variant="outline" onClick={() => applyPreset('aggressive_scan')}>
                    <Fingerprint className="h-4 w-4 mr-2" />
                    Aggressive Scan
                  </Button>
                  <Button variant="outline" onClick={() => applyPreset('technology_focus')}>
                    <Code className="h-4 w-4 mr-2" />
                    Technology Focus
                  </Button>
                  <Button variant="outline" onClick={() => applyPreset('security_focus')}>
                    <Lock className="h-4 w-4 mr-2" />
                    Security Focus
                  </Button>
                </div>
              </div>

              {/* Target Configuration */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Target Configuration</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Target URLs</label>
                    <Textarea
                      value={config.targets.join('\n')}
                      onChange={(e) => updateTargets(e.target.value)}
                      placeholder="https://example.com&#10;https://test.com&#10;192.168.1.1"
                      className="min-h-[120px]"
                    />
                    <p className="text-xs text-gray-500">Enter one URL per line</p>
                  </div>

                  <div className="space-y-2">
                    <label className="text-sm font-medium">Input File (Alternative)</label>
                    <Input
                      value={config.inputFile}
                      onChange={(e) => updateConfig('inputFile', e.target.value)}
                      placeholder="/path/to/urls.txt"
                    />
                    <p className="text-xs text-gray-500">File containing URLs to scan</p>
                  </div>
                </div>
              </div>

              {/* Aggression Level */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Aggression Level</h3>
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                  {Object.entries(AGGRESSION_LEVELS).map(([level, info]) => (
                    <Card 
                      key={level}
                      className={cn(
                        "cursor-pointer transition-colors",
                        config.aggression === parseInt(level) ? "border-blue-500 bg-blue-50 dark:bg-blue-950" : ""
                      )}
                      onClick={() => updateConfig('aggression', parseInt(level))}
                    >
                      <CardContent className="pt-6">
                        <div className="text-center space-y-2">
                          <div className="text-2xl font-bold">{level}</div>
                          <div className="font-medium">{info.name}</div>
                          <div className="text-xs text-gray-500">{info.description}</div>
                          <Badge 
                            variant={info.risk === 'low' ? 'secondary' : 
                                    info.risk === 'medium' ? 'default' : 'destructive'}
                          >
                            {info.risk.toUpperCase()} RISK
                          </Badge>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>

              {/* Plugin Categories */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Plugin Categories</h3>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                  {Object.entries(PLUGIN_CATEGORIES).map(([category, description]) => (
                    <Button
                      key={category}
                      variant={config.plugins.categories.includes(category) ? "default" : "outline"}
                      size="sm"
                      onClick={() => togglePluginCategory(category)}
                      className="h-auto p-3 flex-col"
                    >
                      <div className="font-medium">{category.toUpperCase()}</div>
                      <div className="text-xs text-center">{description}</div>
                    </Button>
                  ))}
                </div>
              </div>

              {/* HTTP Configuration */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">HTTP Configuration</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">User Agent</label>
                    <Select value={config.http.userAgent} onValueChange={(value) => updateConfig('http.userAgent', value)}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="default">WhatWeb Default</SelectItem>
                        <SelectItem value="chrome">Chrome (Windows)</SelectItem>
                        <SelectItem value="firefox">Firefox (Windows)</SelectItem>
                        <SelectItem value="safari">Safari (macOS)</SelectItem>
                        <SelectItem value="edge">Edge (Windows)</SelectItem>
                        <SelectItem value="mobile">Mobile Safari (iOS)</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <label className="text-sm font-medium">Timeout (seconds)</label>
                    <Input
                      type="number"
                      value={config.http.timeout}
                      onChange={(e) => updateConfig('http.timeout', parseInt(e.target.value) || 60)}
                      min="1"
                      max="300"
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">Cookies</label>
                  <Input
                    value={config.http.cookies}
                    onChange={(e) => updateConfig('http.cookies', e.target.value)}
                    placeholder="session=abc123; token=xyz789"
                  />
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">Custom Headers</label>
                  <Textarea
                    value={Object.entries(config.http.customHeaders).map(([k, v]) => `${k}: ${v}`).join('\n')}
                    onChange={(e) => {
                      const headers = {};
                      e.target.value.split('\n').forEach(line => {
                        const [key, ...valueParts] = line.split(':');
                        if (key && valueParts.length > 0) {
                          headers[key.trim()] = valueParts.join(':').trim();
                        }
                      });
                      updateConfig('http.customHeaders', headers);
                    }}
                    placeholder="Authorization: Bearer token&#10;X-Custom-Header: value"
                    className="min-h-[80px]"
                  />
                </div>
              </div>

              {/* Performance Configuration */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Performance Configuration</h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Threads</label>
                    <Input
                      type="number"
                      value={config.performance.threads}
                      onChange={(e) => updateConfig('performance.threads', parseInt(e.target.value) || 25)}
                      min="1"
                      max="100"
                    />
                  </div>

                  <div className="space-y-2">
                    <label className="text-sm font-medium">Wait Time (ms)</label>
                    <Input
                      type="number"
                      value={config.performance.wait}
                      onChange={(e) => updateConfig('performance.wait', parseInt(e.target.value) || 0)}
                      min="0"
                      max="10000"
                    />
                  </div>

                  <div className="space-y-2">
                    <label className="text-sm font-medium">Max Depth</label>
                    <Input
                      type="number"
                      value={config.crawling.maxDepth}
                      onChange={(e) => updateConfig('crawling.maxDepth', parseInt(e.target.value) || 3)}
                      min="1"
                      max="10"
                    />
                  </div>
                </div>
              </div>

              {/* Output Configuration */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Output Configuration</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Output Format</label>
                    <Select value={config.output.format} onValueChange={(value) => updateConfig('output.format', value)}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {Object.entries(OUTPUT_FORMATS).map(([format, info]) => (
                          <SelectItem key={format} value={format}>
                            <div>
                              <div className="font-medium">{info.name}</div>
                              <div className="text-xs text-gray-500">{info.description}</div>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <label className="text-sm font-medium">Output File</label>
                    <Input
                      value={config.output.file}
                      onChange={(e) => updateConfig('output.file', e.target.value)}
                      placeholder="/path/to/output.json"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="verbose"
                      checked={config.logging.verbose}
                      onChange={(e) => updateConfig('logging.verbose', e.target.checked)}
                      className="rounded"
                    />
                    <label htmlFor="verbose" className="text-sm font-medium">Verbose</label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="quiet"
                      checked={config.logging.quiet}
                      onChange={(e) => updateConfig('logging.quiet', e.target.checked)}
                      className="rounded"
                    />
                    <label htmlFor="quiet" className="text-sm font-medium">Quiet</label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="noColour"
                      checked={config.output.noColour}
                      onChange={(e) => updateConfig('output.noColour', e.target.checked)}
                      className="rounded"
                    />
                    <label htmlFor="noColour" className="text-sm font-medium">No Color</label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="noErrors"
                      checked={config.output.noErrors}
                      onChange={(e) => updateConfig('output.noErrors', e.target.checked)}
                      className="rounded"
                    />
                    <label htmlFor="noErrors" className="text-sm font-medium">No Errors</label>
                  </div>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="output" className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold">Real-time Output</h3>
                <div className="flex items-center gap-2">
                  {isScanning && (
                    <Badge variant="secondary" className="animate-pulse">
                      <Activity className="h-3 w-3 mr-1" />
                      Scanning in progress...
                    </Badge>
                  )}
                  <Button variant="outline" size="sm">
                    <Download className="h-4 w-4 mr-2" />
                    Export Output
                  </Button>
                </div>
              </div>
              
              {execution?.progress !== undefined && (
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Progress</span>
                    <span>{execution.progress}%</span>
                  </div>
                  <Progress value={execution.progress} className="w-full" />
                </div>
              )}

              <Card>
                <CardContent className="p-4">
                  <div className="bg-gray-900 dark:bg-gray-800 rounded-lg p-4 min-h-[400px] font-mono text-sm overflow-auto">
                    <div className="text-green-400">
                      {execution?.output || 'WhatWeb output will appear here when scanning starts...'}
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="results" className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold">Identification Results</h3>
                <div className="flex items-center gap-2">
                  <Button variant="outline" size="sm">
                    <Download className="h-4 w-4 mr-2" />
                    Export Results
                  </Button>
                  <Button variant="outline" size="sm">
                    <Filter className="h-4 w-4 mr-2" />
                    Filter Results
                  </Button>
                </div>
              </div>

              {execution?.results ? (
                <div className="space-y-4">
                  {/* Results Summary */}
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <Card>
                      <CardContent className="pt-6">
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Targets Scanned</p>
                            <p className="text-2xl font-bold">{execution.results.targets_scanned || 0}</p>
                          </div>
                          <Target className="h-8 w-8 text-blue-600" />
                        </div>
                      </CardContent>
                    </Card>

                    <Card>
                      <CardContent className="pt-6">
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Technologies</p>
                            <p className="text-2xl font-bold">{execution.results.technologies || 0}</p>
                          </div>
                          <Code className="h-8 w-8 text-green-600" />
                        </div>
                      </CardContent>
                    </Card>

                    <Card>
                      <CardContent className="pt-6">
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Plugins Matched</p>
                            <p className="text-2xl font-bold">{execution.results.plugins_matched || 0}</p>
                          </div>
                          <Package className="h-8 w-8 text-purple-600" />
                        </div>
                      </CardContent>
                    </Card>

                    <Card>
                      <CardContent className="pt-6">
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Certainty</p>
                            <p className="text-2xl font-bold">{execution.results.avg_certainty || 0}%</p>
                          </div>
                          <CheckCircle className="h-8 w-8 text-green-600" />
                        </div>
                      </CardContent>
                    </Card>
                  </div>

                  {/* Results Table */}
                  <Card>
                    <CardContent className="p-0">
                      <div className="overflow-x-auto">
                        <table className="w-full">
                          <thead className="border-b">
                            <tr className="text-left">
                              <th className="p-4 font-medium">Target</th>
                              <th className="p-4 font-medium">Technology</th>
                              <th className="p-4 font-medium">Version</th>
                              <th className="p-4 font-medium">Certainty</th>
                              <th className="p-4 font-medium">Category</th>
                            </tr>
                          </thead>
                          <tbody>
                            {execution.results.findings?.map((finding, index) => (
                              <tr key={index} className="border-b hover:bg-gray-50 dark:hover:bg-gray-800">
                                <td className="p-4 font-mono text-sm">{finding.target}</td>
                                <td className="p-4 font-medium">{finding.technology}</td>
                                <td className="p-4 text-sm">{finding.version || 'N/A'}</td>
                                <td className="p-4">
                                  <Badge 
                                    variant={finding.certainty >= 80 ? 'default' : 
                                            finding.certainty >= 60 ? 'secondary' : 'destructive'}
                                  >
                                    {finding.certainty}%
                                  </Badge>
                                </td>
                                <td className="p-4">
                                  <Badge variant="outline">
                                    {finding.category}
                                  </Badge>
                                </td>
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              ) : (
                <Card>
                  <CardContent className="p-8 text-center">
                    <Fingerprint className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">No Results Yet</h3>
                    <p className="text-gray-600 dark:text-gray-400">
                      Start a web technology identification scan to see results here. Technologies will be identified and categorized automatically.
                    </p>
                  </CardContent>
                </Card>
              )}
            </TabsContent>

            <TabsContent value="command" className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold">Generated Command</h3>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => navigator.clipboard.writeText(generateCommand())}
                >
                  <Copy className="h-4 w-4 mr-2" />
                  Copy Command
                </Button>
              </div>

              <Card>
                <CardContent className="p-4">
                  <div className="bg-gray-900 dark:bg-gray-800 rounded-lg p-4 overflow-x-auto">
                    <code className="text-green-400 whitespace-pre-wrap break-all">
                      {generateCommand()}
                    </code>
                  </div>
                </CardContent>
              </Card>

              <div className="space-y-4">
                <h4 className="font-semibold">Command Explanation</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                  <div>
                    <h5 className="font-medium mb-2">Basic Options</h5>
                    <ul className="space-y-1 text-gray-600 dark:text-gray-400">
                      <li>• <code>-a</code>: Aggression level (1-4)</li>
                      <li>• <code>--log-FORMAT</code>: Output format</li>
                      <li>• <code>--user-agent</code>: Custom user agent</li>
                      <li>• <code>-t</code>: Number of threads</li>
                    </ul>
                  </div>
                  <div>
                    <h5 className="font-medium mb-2">HTTP Options</h5>
                    <ul className="space-y-1 text-gray-600 dark:text-gray-400">
                      <li>• <code>--header</code>: Custom HTTP headers</li>
                      <li>• <code>--cookie</code>: HTTP cookies</li>
                      <li>• <code>--proxy</code>: Proxy server</li>
                      <li>• <code>--read-timeout</code>: Request timeout</li>
                    </ul>
                  </div>
                  <div>
                    <h5 className="font-medium mb-2">Plugin Control</h5>
                    <ul className="space-y-1 text-gray-600 dark:text-gray-400">
                      <li>• <code>--plugins</code>: Enable plugin categories</li>
                      <li>• <code>+plugin</code>: Enable specific plugin</li>
                      <li>• <code>-plugin</code>: Disable specific plugin</li>
                      <li>• Categories: cms, framework, server, etc.</li>
                    </ul>
                  </div>
                  <div>
                    <h5 className="font-medium mb-2">Output Control</h5>
                    <ul className="space-y-1 text-gray-600 dark:text-gray-400">
                      <li>• <code>--verbose</code>: Detailed output</li>
                      <li>• <code>--quiet</code>: Minimal output</li>
                      <li>• <code>--colour=never</code>: No color output</li>
                      <li>• <code>--no-errors</code>: Hide error messages</li>
                    </ul>
                  </div>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}