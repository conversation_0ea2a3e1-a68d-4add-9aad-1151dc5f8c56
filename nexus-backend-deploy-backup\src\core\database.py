"""
SQLite Database Manager for NexusScan Desktop Application
Handles database schema, migrations, and data operations.
"""

import sqlite3
import logging
import json
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple, TYPE_CHECKING

if TYPE_CHECKING:
    from sqlalchemy.orm import Session
from contextlib import contextmanager
from dataclasses import dataclass, asdict

# SQLAlchemy imports for ORM support
try:
    from sqlalchemy import create_engine, event
    from sqlalchemy.orm import sessionmaker, Session
    from sqlalchemy.pool import StaticPool
    HAS_SQLALCHEMY = True
except ImportError:
    HAS_SQLALCHEMY = False

logger = logging.getLogger(__name__)


@dataclass
class User:
    """User data model"""
    id: str
    username: str
    email: Optional[str] = None
    password_hash: Optional[str] = None
    role: str = "analyst"
    preferences: Optional[Dict] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None


@dataclass
class Campaign:
    """Campaign data model"""
    id: str
    name: str
    description: Optional[str] = None
    targets: List[str] = None
    scan_types: List[str] = None
    configuration: Optional[Dict] = None
    status: str = "draft"
    user_id: Optional[str] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None


@dataclass
class ScanSession:
    """Scan session data model"""
    id: str
    campaign_id: str
    target: str
    tool: str
    status: str = "pending"
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    raw_output: Optional[str] = None
    parsed_results: Optional[Dict] = None
    created_at: Optional[datetime] = None


@dataclass
class Vulnerability:
    """Vulnerability data model"""
    id: str
    session_id: str
    campaign_id: str
    target: str
    type: str
    severity: str
    title: str
    description: Optional[str] = None
    payload: Optional[str] = None
    evidence: Optional[Dict] = None
    remediation: Optional[str] = None
    cvss_score: Optional[float] = None
    cve_id: Optional[str] = None
    discovered_at: Optional[datetime] = None


class DatabaseManager:
    """SQLite database manager for NexusScan"""
    
    def __init__(self, database_path: str):
        """Initialize database manager"""
        self.database_path = Path(database_path)
        self.database_path.parent.mkdir(parents=True, exist_ok=True)
        
        # Initialize SQLAlchemy components if available
        self.engine = None
        self.SessionLocal = None
        self._init_sqlalchemy()
        
        logger.info(f"Database manager initialized: {self.database_path}")
    
    def _init_sqlalchemy(self):
        """Initialize SQLAlchemy engine and session factory"""
        if HAS_SQLALCHEMY:
            try:
                # Create SQLAlchemy engine
                self.engine = create_engine(
                    f"sqlite:///{self.database_path}",
                    poolclass=StaticPool,
                    connect_args={"check_same_thread": False},
                    echo=False
                )
                
                # Create session factory
                self.SessionLocal = sessionmaker(
                    autocommit=False,
                    autoflush=False,
                    bind=self.engine
                )
                
                logger.info("SQLAlchemy ORM support initialized")
            except Exception as e:
                logger.warning(f"Failed to initialize SQLAlchemy: {e}")
                self.engine = None
                self.SessionLocal = None
        else:
            logger.warning("SQLAlchemy not available - ORM features disabled")
    
    def get_session(self) -> Optional["Session"]:
        """Get SQLAlchemy session for ORM operations"""
        if self.SessionLocal:
            return self.SessionLocal()
        else:
            logger.warning("SQLAlchemy session not available")
            return None
    
    @contextmanager
    def get_connection(self):
        """Get database connection with context manager"""
        conn = None
        try:
            conn = sqlite3.connect(
                self.database_path,
                detect_types=sqlite3.PARSE_DECLTYPES | sqlite3.PARSE_COLNAMES
            )
            conn.row_factory = sqlite3.Row  # Enable dict-like access
            
            # Enable WAL mode for better performance
            conn.execute("PRAGMA journal_mode=WAL")
            conn.execute("PRAGMA synchronous=NORMAL")
            conn.execute("PRAGMA cache_size=10000")
            conn.execute("PRAGMA temp_store=MEMORY")
            
            yield conn
            
        except Exception as e:
            if conn:
                conn.rollback()
            logger.error(f"Database error: {e}")
            raise
        finally:
            if conn:
                conn.close()
    
    async def initialize(self):
        """Initialize database - async version for compatibility"""
        return self.initialize_database()
    
    def initialize_database(self):
        """Initialize database with schema"""
        # Try SQLAlchemy first if available
        if self.engine is not None:
            try:
                from database.models import Base
                Base.metadata.create_all(bind=self.engine)
                logger.info("Database schema initialized with SQLAlchemy")
                return True
            except Exception as e:
                logger.warning(f"SQLAlchemy schema creation failed: {e}")
        
        # Fallback to raw SQL
        try:
            with self.get_connection() as conn:
                # Create users table
                conn.execute("""
                    CREATE TABLE IF NOT EXISTS users (
                        id TEXT PRIMARY KEY,
                        username TEXT UNIQUE NOT NULL,
                        email TEXT UNIQUE,
                        password_hash TEXT,
                        role TEXT DEFAULT 'analyst',
                        preferences TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                """)
                
                # Create campaigns table
                conn.execute("""
                    CREATE TABLE IF NOT EXISTS campaigns (
                        id TEXT PRIMARY KEY,
                        name TEXT NOT NULL,
                        description TEXT,
                        targets TEXT NOT NULL,
                        scan_types TEXT,
                        configuration TEXT,
                        status TEXT DEFAULT 'draft',
                        user_id TEXT REFERENCES users(id),
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                """)
                
                # Create scan_sessions table
                conn.execute("""
                    CREATE TABLE IF NOT EXISTS scan_sessions (
                        id TEXT PRIMARY KEY,
                        campaign_id TEXT REFERENCES campaigns(id),
                        target TEXT NOT NULL,
                        tool TEXT NOT NULL,
                        status TEXT DEFAULT 'pending',
                        started_at TIMESTAMP,
                        completed_at TIMESTAMP,
                        raw_output TEXT,
                        parsed_results TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                """)
                
                # Create vulnerabilities table
                conn.execute("""
                    CREATE TABLE IF NOT EXISTS vulnerabilities (
                        id TEXT PRIMARY KEY,
                        session_id TEXT REFERENCES scan_sessions(id),
                        campaign_id TEXT REFERENCES campaigns(id),
                        target TEXT NOT NULL,
                        type TEXT NOT NULL,
                        severity TEXT NOT NULL,
                        title TEXT NOT NULL,
                        description TEXT,
                        payload TEXT,
                        evidence TEXT,
                        remediation TEXT,
                        cvss_score REAL,
                        cve_id TEXT,
                        discovered_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                """)
                
                # Create ai_payloads table
                conn.execute("""
                    CREATE TABLE IF NOT EXISTS ai_payloads (
                        id TEXT PRIMARY KEY,
                        user_id TEXT REFERENCES users(id),
                        vulnerability_type TEXT NOT NULL,
                        target_context TEXT,
                        difficulty_level TEXT DEFAULT 'intermediate',
                        payload TEXT NOT NULL,
                        explanation TEXT,
                        mitigation TEXT,
                        success_probability REAL,
                        generated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                """)
                
                # Create reports table
                conn.execute("""
                    CREATE TABLE IF NOT EXISTS reports (
                        id TEXT PRIMARY KEY,
                        campaign_id TEXT REFERENCES campaigns(id),
                        name TEXT NOT NULL,
                        type TEXT NOT NULL,
                        format TEXT NOT NULL,
                        content TEXT,
                        file_path TEXT,
                        generated_by TEXT REFERENCES users(id),
                        generated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                """)
                
                # Create configurations table
                conn.execute("""
                    CREATE TABLE IF NOT EXISTS configurations (
                        id TEXT PRIMARY KEY,
                        category TEXT NOT NULL,
                        key TEXT NOT NULL,
                        value TEXT,
                        user_id TEXT REFERENCES users(id),
                        is_global BOOLEAN DEFAULT FALSE,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                """)
                
                # Create audit_logs table
                conn.execute("""
                    CREATE TABLE IF NOT EXISTS audit_logs (
                        id TEXT PRIMARY KEY,
                        user_id TEXT REFERENCES users(id),
                        action TEXT NOT NULL,
                        resource_type TEXT,
                        resource_id TEXT,
                        details TEXT,
                        ip_address TEXT,
                        user_agent TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                """)
                
                # Create execution_logs table for advanced execution tracking
                conn.execute("""
                    CREATE TABLE IF NOT EXISTS execution_logs (
                        id TEXT PRIMARY KEY,
                        execution_id TEXT UNIQUE NOT NULL,
                        user_id TEXT REFERENCES users(id),
                        command TEXT NOT NULL,
                        target_info TEXT,
                        execution_mode TEXT NOT NULL,
                        risk_level TEXT NOT NULL,
                        safety_score REAL,
                        authorization_data TEXT,
                        ai_guidance TEXT,
                        status TEXT NOT NULL,
                        exit_code INTEGER,
                        stdout TEXT,
                        stderr TEXT,
                        execution_time REAL,
                        evidence_files TEXT,
                        ai_analysis TEXT,
                        safety_assessment TEXT,
                        cleanup_performed BOOLEAN DEFAULT FALSE,
                        emergency_stopped BOOLEAN DEFAULT FALSE,
                        started_at TIMESTAMP,
                        completed_at TIMESTAMP,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                """)
                
                # Create evidence_packages table for evidence collection
                conn.execute("""
                    CREATE TABLE IF NOT EXISTS evidence_packages (
                        id TEXT PRIMARY KEY,
                        execution_id TEXT REFERENCES execution_logs(execution_id),
                        campaign_id TEXT REFERENCES campaigns(id),
                        evidence_type TEXT NOT NULL,
                        file_path TEXT,
                        file_size INTEGER,
                        file_hash TEXT,
                        metadata TEXT,
                        chain_of_custody TEXT,
                        collected_by TEXT REFERENCES users(id),
                        collection_method TEXT,
                        integrity_verified BOOLEAN DEFAULT FALSE,
                        legal_hold BOOLEAN DEFAULT FALSE,
                        retention_date DATE,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                """)
                
                # Create ai_confidence_data table for confidence tracking
                conn.execute("""
                    CREATE TABLE IF NOT EXISTS ai_confidence_data (
                        id TEXT PRIMARY KEY,
                        vulnerability_type TEXT NOT NULL,
                        exploit_hash TEXT,
                        original_confidence REAL NOT NULL,
                        enhanced_confidence REAL NOT NULL,
                        boost_amount REAL NOT NULL,
                        validation_methods TEXT,
                        boost_reasons TEXT,
                        validation_evidence TEXT,
                        success_rate REAL,
                        execution_count INTEGER DEFAULT 0,
                        avg_execution_time REAL,
                        provider_used TEXT,
                        learning_data TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                """)
                
                # Create safety_assessments table for safety analysis tracking
                conn.execute("""
                    CREATE TABLE IF NOT EXISTS safety_assessments (
                        id TEXT PRIMARY KEY,
                        execution_id TEXT REFERENCES execution_logs(execution_id),
                        command_hash TEXT NOT NULL,
                        risk_level TEXT NOT NULL,
                        safety_score REAL NOT NULL,
                        attack_category TEXT NOT NULL,
                        technical_risks TEXT,
                        business_risks TEXT,
                        legal_implications TEXT,
                        mitigation_strategies TEXT,
                        required_authorizations TEXT,
                        estimated_impact TEXT,
                        reversibility BOOLEAN,
                        stealth_level TEXT,
                        detection_probability REAL,
                        ai_confidence REAL,
                        jurisdiction TEXT,
                        assessment_version TEXT DEFAULT '1.0',
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                """)
                
                # Create authorization_trail table for comprehensive audit trail
                conn.execute("""
                    CREATE TABLE IF NOT EXISTS authorization_trail (
                        id TEXT PRIMARY KEY,
                        execution_id TEXT REFERENCES execution_logs(execution_id),
                        user_id TEXT REFERENCES users(id),
                        authorization_level TEXT NOT NULL,
                        authorization_code TEXT,
                        confirmation_steps TEXT,
                        legal_disclaimer_accepted BOOLEAN DEFAULT FALSE,
                        risk_acknowledgment TEXT,
                        emergency_contact_notified BOOLEAN DEFAULT FALSE,
                        session_duration INTEGER,
                        ip_address TEXT,
                        user_agent TEXT,
                        geolocation TEXT,
                        authorization_method TEXT,
                        witness_user_id TEXT REFERENCES users(id),
                        approval_chain TEXT,
                        revocation_reason TEXT,
                        revoked_at TIMESTAMP,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                """)
                
                # Create indexes for performance
                self._create_indexes(conn)
                
                conn.commit()
                logger.info("Database schema initialized successfully")
                
        except Exception as e:
            logger.error(f"Failed to initialize database: {e}")
            raise
    
    def _create_indexes(self, conn):
        """Create database indexes for performance"""
        indexes = [
            # Original indexes
            "CREATE INDEX IF NOT EXISTS idx_campaigns_user_id ON campaigns(user_id)",
            "CREATE INDEX IF NOT EXISTS idx_campaigns_status ON campaigns(status)",
            "CREATE INDEX IF NOT EXISTS idx_scan_sessions_campaign_id ON scan_sessions(campaign_id)",
            "CREATE INDEX IF NOT EXISTS idx_scan_sessions_status ON scan_sessions(status)",
            "CREATE INDEX IF NOT EXISTS idx_vulnerabilities_session_id ON vulnerabilities(session_id)",
            "CREATE INDEX IF NOT EXISTS idx_vulnerabilities_campaign_id ON vulnerabilities(campaign_id)",
            "CREATE INDEX IF NOT EXISTS idx_vulnerabilities_severity ON vulnerabilities(severity)",
            "CREATE INDEX IF NOT EXISTS idx_ai_payloads_user_id ON ai_payloads(user_id)",
            "CREATE INDEX IF NOT EXISTS idx_ai_payloads_vulnerability_type ON ai_payloads(vulnerability_type)",
            "CREATE INDEX IF NOT EXISTS idx_reports_campaign_id ON reports(campaign_id)",
            "CREATE INDEX IF NOT EXISTS idx_audit_logs_user_id ON audit_logs(user_id)",
            "CREATE INDEX IF NOT EXISTS idx_audit_logs_created_at ON audit_logs(created_at)",
            
            # New advanced execution indexes
            "CREATE INDEX IF NOT EXISTS idx_execution_logs_user_id ON execution_logs(user_id)",
            "CREATE INDEX IF NOT EXISTS idx_execution_logs_execution_id ON execution_logs(execution_id)",
            "CREATE INDEX IF NOT EXISTS idx_execution_logs_status ON execution_logs(status)",
            "CREATE INDEX IF NOT EXISTS idx_execution_logs_risk_level ON execution_logs(risk_level)",
            "CREATE INDEX IF NOT EXISTS idx_execution_logs_started_at ON execution_logs(started_at)",
            "CREATE INDEX IF NOT EXISTS idx_execution_logs_emergency_stopped ON execution_logs(emergency_stopped)",
            
            # Evidence packages indexes
            "CREATE INDEX IF NOT EXISTS idx_evidence_packages_execution_id ON evidence_packages(execution_id)",
            "CREATE INDEX IF NOT EXISTS idx_evidence_packages_campaign_id ON evidence_packages(campaign_id)",
            "CREATE INDEX IF NOT EXISTS idx_evidence_packages_evidence_type ON evidence_packages(evidence_type)",
            "CREATE INDEX IF NOT EXISTS idx_evidence_packages_collected_by ON evidence_packages(collected_by)",
            "CREATE INDEX IF NOT EXISTS idx_evidence_packages_legal_hold ON evidence_packages(legal_hold)",
            
            # AI confidence data indexes
            "CREATE INDEX IF NOT EXISTS idx_ai_confidence_vulnerability_type ON ai_confidence_data(vulnerability_type)",
            "CREATE INDEX IF NOT EXISTS idx_ai_confidence_exploit_hash ON ai_confidence_data(exploit_hash)",
            "CREATE INDEX IF NOT EXISTS idx_ai_confidence_enhanced_confidence ON ai_confidence_data(enhanced_confidence)",
            "CREATE INDEX IF NOT EXISTS idx_ai_confidence_provider_used ON ai_confidence_data(provider_used)",
            
            # Safety assessments indexes
            "CREATE INDEX IF NOT EXISTS idx_safety_assessments_execution_id ON safety_assessments(execution_id)",
            "CREATE INDEX IF NOT EXISTS idx_safety_assessments_command_hash ON safety_assessments(command_hash)",
            "CREATE INDEX IF NOT EXISTS idx_safety_assessments_risk_level ON safety_assessments(risk_level)",
            "CREATE INDEX IF NOT EXISTS idx_safety_assessments_attack_category ON safety_assessments(attack_category)",
            
            # Authorization trail indexes
            "CREATE INDEX IF NOT EXISTS idx_authorization_trail_execution_id ON authorization_trail(execution_id)",
            "CREATE INDEX IF NOT EXISTS idx_authorization_trail_user_id ON authorization_trail(user_id)",
            "CREATE INDEX IF NOT EXISTS idx_authorization_trail_authorization_level ON authorization_trail(authorization_level)",
            "CREATE INDEX IF NOT EXISTS idx_authorization_trail_created_at ON authorization_trail(created_at)"
        ]
        
        for index_sql in indexes:
            conn.execute(index_sql)
    
    def create_user(self, user: User) -> bool:
        """Create new user"""
        try:
            with self.get_connection() as conn:
                conn.execute("""
                    INSERT INTO users (id, username, email, password_hash, role, preferences)
                    VALUES (?, ?, ?, ?, ?, ?)
                """, (
                    user.id, user.username, user.email, user.password_hash,
                    user.role, json.dumps(user.preferences) if user.preferences else None
                ))
                conn.commit()
                return True
        except Exception as e:
            logger.error(f"Failed to create user: {e}")
            return False
    
    def get_user(self, user_id: str) -> Optional[User]:
        """Get user by ID"""
        try:
            with self.get_connection() as conn:
                cursor = conn.execute(
                    "SELECT * FROM users WHERE id = ?", (user_id,)
                )
                row = cursor.fetchone()
                if row:
                    return User(
                        id=row['id'],
                        username=row['username'],
                        email=row['email'],
                        password_hash=row['password_hash'],
                        role=row['role'],
                        preferences=json.loads(row['preferences'] or '{}'),
                        created_at=row['created_at'],
                        updated_at=row['updated_at']
                    )
        except Exception as e:
            logger.error(f"Failed to get user: {e}")
        return None
    
    def create_campaign(self, campaign: Campaign) -> bool:
        """Create new campaign"""
        try:
            with self.get_connection() as conn:
                conn.execute("""
                    INSERT INTO campaigns (id, name, description, targets, scan_types, 
                                         configuration, status, user_id)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    campaign.id, campaign.name, campaign.description,
                    json.dumps(campaign.targets) if campaign.targets else None,
                    json.dumps(campaign.scan_types) if campaign.scan_types else None,
                    json.dumps(campaign.configuration) if campaign.configuration else None,
                    campaign.status, campaign.user_id
                ))
                conn.commit()
                return True
        except Exception as e:
            logger.error(f"Failed to create campaign: {e}")
            return False
    
    def get_campaigns(self, user_id: Optional[str] = None) -> List[Campaign]:
        """Get campaigns, optionally filtered by user"""
        campaigns = []
        try:
            with self.get_connection() as conn:
                if user_id:
                    cursor = conn.execute(
                        "SELECT * FROM campaigns WHERE user_id = ? ORDER BY created_at DESC",
                        (user_id,)
                    )
                else:
                    cursor = conn.execute(
                        "SELECT * FROM campaigns ORDER BY created_at DESC"
                    )
                
                for row in cursor.fetchall():
                    campaigns.append(Campaign(
                        id=row['id'],
                        name=row['name'],
                        description=row['description'],
                        targets=json.loads(row['targets'] or '[]'),
                        scan_types=json.loads(row['scan_types'] or '[]'),
                        configuration=json.loads(row['configuration'] or '{}'),
                        status=row['status'],
                        user_id=row['user_id'],
                        created_at=row['created_at'],
                        updated_at=row['updated_at']
                    ))
        except Exception as e:
            logger.error(f"Failed to get campaigns: {e}")
        return campaigns
    
    def create_vulnerability(self, vulnerability: Vulnerability) -> bool:
        """Create new vulnerability"""
        try:
            with self.get_connection() as conn:
                conn.execute("""
                    INSERT INTO vulnerabilities (id, session_id, campaign_id, target, type,
                                               severity, title, description, payload, evidence,
                                               remediation, cvss_score, cve_id)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    vulnerability.id, vulnerability.session_id, vulnerability.campaign_id,
                    vulnerability.target, vulnerability.type, vulnerability.severity,
                    vulnerability.title, vulnerability.description, vulnerability.payload,
                    json.dumps(vulnerability.evidence) if vulnerability.evidence else None,
                    vulnerability.remediation, vulnerability.cvss_score, vulnerability.cve_id
                ))
                conn.commit()
                return True
        except Exception as e:
            logger.error(f"Failed to create vulnerability: {e}")
            return False
    
    def get_vulnerabilities(self, campaign_id: str) -> List[Vulnerability]:
        """Get vulnerabilities for a campaign"""
        vulnerabilities = []
        try:
            with self.get_connection() as conn:
                cursor = conn.execute(
                    "SELECT * FROM vulnerabilities WHERE campaign_id = ? ORDER BY discovered_at DESC",
                    (campaign_id,)
                )
                
                for row in cursor.fetchall():
                    vulnerabilities.append(Vulnerability(
                        id=row['id'],
                        session_id=row['session_id'],
                        campaign_id=row['campaign_id'],
                        target=row['target'],
                        type=row['type'],
                        severity=row['severity'],
                        title=row['title'],
                        description=row['description'],
                        payload=row['payload'],
                        evidence=json.loads(row['evidence'] or '{}'),
                        remediation=row['remediation'],
                        cvss_score=row['cvss_score'],
                        cve_id=row['cve_id'],
                        discovered_at=row['discovered_at']
                    ))
        except Exception as e:
            logger.error(f"Failed to get vulnerabilities: {e}")
        return vulnerabilities
    
    def log_audit_event(self, user_id: str, action: str, resource_type: str = None,
                       resource_id: str = None, details: Dict = None):
        """Log audit event"""
        try:
            with self.get_connection() as conn:
                conn.execute("""
                    INSERT INTO audit_logs (id, user_id, action, resource_type, 
                                          resource_id, details)
                    VALUES (?, ?, ?, ?, ?, ?)
                """, (
                    f"audit_{datetime.now().timestamp()}", user_id, action,
                    resource_type, resource_id,
                    json.dumps(details) if details else None
                ))
                conn.commit()
        except Exception as e:
            logger.error(f"Failed to log audit event: {e}")
    
    def log_execution(self, execution_data: Dict[str, Any]) -> bool:
        """Log advanced execution details"""
        try:
            with self.get_connection() as conn:
                conn.execute("""
                    INSERT INTO execution_logs (
                        id, execution_id, user_id, command, target_info, execution_mode,
                        risk_level, safety_score, authorization_data, ai_guidance, status,
                        exit_code, stdout, stderr, execution_time, evidence_files,
                        ai_analysis, safety_assessment, cleanup_performed, 
                        emergency_stopped, started_at, completed_at
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    f"exec_log_{execution_data['execution_id']}",
                    execution_data['execution_id'],
                    execution_data['user_id'],
                    execution_data['command'],
                    json.dumps(execution_data.get('target_info', {})),
                    execution_data['execution_mode'],
                    execution_data['risk_level'],
                    execution_data.get('safety_score', 0.0),
                    json.dumps(execution_data.get('authorization_data', {})),
                    json.dumps(execution_data.get('ai_guidance', {})),
                    execution_data['status'],
                    execution_data.get('exit_code'),
                    execution_data.get('stdout', ''),
                    execution_data.get('stderr', ''),
                    execution_data.get('execution_time', 0.0),
                    json.dumps(execution_data.get('evidence_files', [])),
                    json.dumps(execution_data.get('ai_analysis', {})),
                    json.dumps(execution_data.get('safety_assessment', {})),
                    execution_data.get('cleanup_performed', False),
                    execution_data.get('emergency_stopped', False),
                    execution_data.get('started_at'),
                    execution_data.get('completed_at')
                ))
                conn.commit()
                return True
        except Exception as e:
            logger.error(f"Failed to log execution: {e}")
            return False

    def log_evidence_package(self, evidence_data: Dict[str, Any]) -> bool:
        """Log evidence package details"""
        try:
            with self.get_connection() as conn:
                conn.execute("""
                    INSERT INTO evidence_packages (
                        id, execution_id, campaign_id, evidence_type, file_path,
                        file_size, file_hash, metadata, chain_of_custody, collected_by,
                        collection_method, integrity_verified, legal_hold, retention_date
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    f"evidence_{int(datetime.now().timestamp())}",
                    evidence_data['execution_id'],
                    evidence_data.get('campaign_id'),
                    evidence_data['evidence_type'],
                    evidence_data.get('file_path'),
                    evidence_data.get('file_size'),
                    evidence_data.get('file_hash'),
                    json.dumps(evidence_data.get('metadata', {})),
                    json.dumps(evidence_data.get('chain_of_custody', [])),
                    evidence_data['collected_by'],
                    evidence_data.get('collection_method', 'automated'),
                    evidence_data.get('integrity_verified', False),
                    evidence_data.get('legal_hold', False),
                    evidence_data.get('retention_date')
                ))
                conn.commit()
                return True
        except Exception as e:
            logger.error(f"Failed to log evidence package: {e}")
            return False

    def log_confidence_enhancement(self, confidence_data: Dict[str, Any]) -> bool:
        """Log AI confidence enhancement data"""
        try:
            with self.get_connection() as conn:
                conn.execute("""
                    INSERT INTO ai_confidence_data (
                        id, vulnerability_type, exploit_hash, original_confidence,
                        enhanced_confidence, boost_amount, validation_methods,
                        boost_reasons, validation_evidence, success_rate,
                        execution_count, avg_execution_time, provider_used, learning_data
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    f"confidence_{int(datetime.now().timestamp())}",
                    confidence_data['vulnerability_type'],
                    confidence_data.get('exploit_hash'),
                    confidence_data['original_confidence'],
                    confidence_data['enhanced_confidence'],
                    confidence_data['boost_amount'],
                    json.dumps(confidence_data.get('validation_methods', [])),
                    json.dumps(confidence_data.get('boost_reasons', [])),
                    json.dumps(confidence_data.get('validation_evidence', {})),
                    confidence_data.get('success_rate'),
                    confidence_data.get('execution_count', 0),
                    confidence_data.get('avg_execution_time'),
                    confidence_data.get('provider_used'),
                    json.dumps(confidence_data.get('learning_data', {}))
                ))
                conn.commit()
                return True
        except Exception as e:
            logger.error(f"Failed to log confidence enhancement: {e}")
            return False

    def log_safety_assessment(self, assessment_data: Dict[str, Any]) -> bool:
        """Log safety assessment details"""
        try:
            with self.get_connection() as conn:
                conn.execute("""
                    INSERT INTO safety_assessments (
                        id, execution_id, command_hash, risk_level, safety_score,
                        attack_category, technical_risks, business_risks, legal_implications,
                        mitigation_strategies, required_authorizations, estimated_impact,
                        reversibility, stealth_level, detection_probability, ai_confidence,
                        jurisdiction, assessment_version
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    f"safety_{int(datetime.now().timestamp())}",
                    assessment_data.get('execution_id'),
                    assessment_data['command_hash'],
                    assessment_data['risk_level'],
                    assessment_data['safety_score'],
                    assessment_data['attack_category'],
                    json.dumps(assessment_data.get('technical_risks', [])),
                    json.dumps(assessment_data.get('business_risks', [])),
                    json.dumps(assessment_data.get('legal_implications', [])),
                    json.dumps(assessment_data.get('mitigation_strategies', [])),
                    json.dumps(assessment_data.get('required_authorizations', [])),
                    assessment_data.get('estimated_impact'),
                    assessment_data.get('reversibility', False),
                    assessment_data.get('stealth_level'),
                    assessment_data.get('detection_probability'),
                    assessment_data.get('ai_confidence'),
                    assessment_data.get('jurisdiction'),
                    assessment_data.get('assessment_version', '1.0')
                ))
                conn.commit()
                return True
        except Exception as e:
            logger.error(f"Failed to log safety assessment: {e}")
            return False

    def log_authorization_trail(self, authorization_data: Dict[str, Any]) -> bool:
        """Log authorization trail for audit purposes"""
        try:
            with self.get_connection() as conn:
                conn.execute("""
                    INSERT INTO authorization_trail (
                        id, execution_id, user_id, authorization_level, authorization_code,
                        confirmation_steps, legal_disclaimer_accepted, risk_acknowledgment,
                        emergency_contact_notified, session_duration, ip_address, user_agent,
                        geolocation, authorization_method, witness_user_id, approval_chain
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    f"auth_{int(datetime.now().timestamp())}",
                    authorization_data['execution_id'],
                    authorization_data['user_id'],
                    authorization_data['authorization_level'],
                    authorization_data.get('authorization_code'),
                    json.dumps(authorization_data.get('confirmation_steps', {})),
                    authorization_data.get('legal_disclaimer_accepted', False),
                    authorization_data.get('risk_acknowledgment'),
                    authorization_data.get('emergency_contact_notified', False),
                    authorization_data.get('session_duration'),
                    authorization_data.get('ip_address'),
                    authorization_data.get('user_agent'),
                    authorization_data.get('geolocation'),
                    authorization_data.get('authorization_method', 'manual'),
                    authorization_data.get('witness_user_id'),
                    json.dumps(authorization_data.get('approval_chain', []))
                ))
                conn.commit()
                return True
        except Exception as e:
            logger.error(f"Failed to log authorization trail: {e}")
            return False

    def get_execution_history(self, user_id: Optional[str] = None, limit: int = 100) -> List[Dict[str, Any]]:
        """Get execution history with optional user filtering"""
        executions = []
        try:
            with self.get_connection() as conn:
                if user_id:
                    cursor = conn.execute("""
                        SELECT * FROM execution_logs 
                        WHERE user_id = ? 
                        ORDER BY created_at DESC 
                        LIMIT ?
                    """, (user_id, limit))
                else:
                    cursor = conn.execute("""
                        SELECT * FROM execution_logs 
                        ORDER BY created_at DESC 
                        LIMIT ?
                    """, (limit,))
                
                for row in cursor.fetchall():
                    executions.append(dict(row))
        except Exception as e:
            logger.error(f"Failed to get execution history: {e}")
        return executions

    def get_confidence_analytics(self, vulnerability_type: Optional[str] = None) -> Dict[str, Any]:
        """Get AI confidence analytics"""
        analytics = {}
        try:
            with self.get_connection() as conn:
                base_query = "SELECT * FROM ai_confidence_data"
                params = ()
                
                if vulnerability_type:
                    base_query += " WHERE vulnerability_type = ?"
                    params = (vulnerability_type,)
                
                cursor = conn.execute(base_query + " ORDER BY created_at DESC", params)
                records = cursor.fetchall()
                
                if records:
                    confidences = [row['enhanced_confidence'] for row in records]
                    boosts = [row['boost_amount'] for row in records]
                    
                    analytics = {
                        "total_enhancements": len(records),
                        "avg_enhanced_confidence": sum(confidences) / len(confidences),
                        "avg_boost_amount": sum(boosts) / len(boosts),
                        "max_confidence": max(confidences),
                        "min_confidence": min(confidences),
                        "records": [dict(row) for row in records]
                    }
        except Exception as e:
            logger.error(f"Failed to get confidence analytics: {e}")
        return analytics

    def get_database_stats(self) -> Dict[str, int]:
        """Get database statistics"""
        stats = {}
        try:
            with self.get_connection() as conn:
                tables = [
                    'users', 'campaigns', 'scan_sessions', 'vulnerabilities', 
                    'ai_payloads', 'reports', 'audit_logs', 'execution_logs',
                    'evidence_packages', 'ai_confidence_data', 'safety_assessments',
                    'authorization_trail'
                ]
                
                for table in tables:
                    cursor = conn.execute(f"SELECT COUNT(*) FROM {table}")
                    stats[table] = cursor.fetchone()[0]
        except Exception as e:
            logger.error(f"Failed to get database stats: {e}")
        return stats
    
    def is_connected(self) -> bool:
        """Check if database is connected"""
        try:
            with self.get_connection() as conn:
                conn.execute("SELECT 1")
                return True
        except Exception:
            return False
    
    def close(self):
        """Close database connections"""
        try:
            if hasattr(self, 'engine') and self.engine:
                self.engine.dispose()
            logger.info("Database connections closed")
        except Exception as e:
            logger.error(f"Error closing database: {e}")