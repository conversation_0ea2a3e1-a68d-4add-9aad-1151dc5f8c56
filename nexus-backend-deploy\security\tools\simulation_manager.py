#!/usr/bin/env python3
"""
Simulation Manager
High-quality simulation for security tools when real execution is not possible
"""

import json
import time
import random
import logging
import asyncio
from typing import Dict, Any, List, Optional
from datetime import datetime, timezone

from .hybrid_execution_engine import ToolExecutionResult, ExecutionResult

logger = logging.getLogger(__name__)

class SimulationManager:
    """Intelligent simulation manager for security tools"""
    
    def __init__(self):
        # Simulation templates for realistic results
        self.simulation_templates = self._load_simulation_templates()
        
        # Common ports and services for realistic simulation
        self.common_ports = {
            21: "ftp", 22: "ssh", 23: "telnet", 25: "smtp", 53: "dns",
            80: "http", 110: "pop3", 143: "imap", 443: "https", 993: "imaps",
            995: "pop3s", 3389: "rdp", 5432: "postgresql", 3306: "mysql"
        }
        
        # Common vulnerabilities for simulation
        self.common_vulns = [
            {
                "id": "CVE-2023-0001",
                "severity": "high",
                "title": "SQL Injection in Login Form",
                "description": "SQL injection vulnerability in user authentication",
                "cvss": 8.1
            },
            {
                "id": "CVE-2023-0002", 
                "severity": "medium",
                "title": "Cross-Site Scripting (XSS)",
                "description": "Reflected XSS in search parameter",
                "cvss": 6.1
            },
            {
                "id": "CVE-2023-0003",
                "severity": "low",
                "title": "Information Disclosure",
                "description": "Server version information disclosure",
                "cvss": 3.1
            }
        ]
        
        logger.info("🎭 Simulation Manager initialized")
    
    def _load_simulation_templates(self) -> Dict[str, Dict]:
        """Load simulation templates for different tools"""
        
        return {
            "nmap": {
                "scan_types": ["tcp", "syn", "udp"],
                "common_results": {
                    "open_ports": [22, 80, 443, 8080],
                    "services": ["ssh", "http", "https", "http-proxy"],
                    "os_detection": ["Linux 3.x-4.x", "Windows 10", "Windows Server 2019"]
                }
            },
            "nuclei": {
                "templates": ["cves", "exposures", "technologies", "vulnerabilities"],
                "severities": ["info", "low", "medium", "high", "critical"],
                "common_findings": [
                    "HTTP Security Headers Missing",
                    "SSL/TLS Configuration Issues", 
                    "Directory Traversal Vulnerability",
                    "SQL Injection Detected"
                ]
            },
            "sqlmap": {
                "injection_types": ["boolean-based", "time-based", "union-based", "error-based"],
                "databases": ["MySQL", "PostgreSQL", "MSSQL", "Oracle"],
                "techniques": ["B", "T", "U", "E", "S"]
            },
            "gobuster": {
                "modes": ["dir", "dns", "vhost"],
                "common_directories": [
                    "/admin", "/api", "/backup", "/config", "/uploads",
                    "/images", "/css", "/js", "/login", "/dashboard"
                ],
                "status_codes": [200, 301, 302, 403, 404]
            },
            "nikto": {
                "scan_types": ["standard", "full", "quick"],
                "common_issues": [
                    "Server version disclosure",
                    "Outdated software versions",
                    "Default files found",
                    "Potential backup files"
                ]
            },
            "masscan": {
                "scan_rates": ["100", "1000", "10000"],
                "port_ranges": ["1-1000", "1-65535", "top-ports"],
                "protocols": ["tcp", "udp"]
            },
            "testssl": {
                "protocols": ["SSLv2", "SSLv3", "TLS1.0", "TLS1.1", "TLS1.2", "TLS1.3"],
                "ciphers": ["AES256-GCM-SHA384", "CHACHA20-POLY1305", "AES128-GCM-SHA256"],
                "vulnerabilities": ["BEAST", "CRIME", "HEARTBLEED", "POODLE"]
            },
            "hashcat": {
                "attack_modes": ["dictionary", "brute-force", "combinator", "hybrid"],
                "hash_types": ["MD5", "SHA1", "SHA256", "NTLM", "bcrypt"],
                "success_rates": [15, 35, 60, 85]  # Percentage success simulation
            }
        }
    
    async def simulate_tool(
        self,
        tool_name: str,
        target: str,
        options: Dict[str, Any]
    ) -> ToolExecutionResult:
        """Simulate tool execution with realistic results"""
        
        start_time = time.time()
        
        logger.info(f"🎭 Simulating {tool_name} execution on {target}")
        
        # Add realistic delay
        simulation_delay = random.uniform(2.0, 8.0)
        await asyncio.sleep(simulation_delay)
        
        try:
            if tool_name == "nmap":
                output = self._simulate_nmap(target, options)
            elif tool_name == "nuclei":
                output = self._simulate_nuclei(target, options)
            elif tool_name == "sqlmap":
                output = self._simulate_sqlmap(target, options)
            elif tool_name == "gobuster":
                output = self._simulate_gobuster(target, options)
            elif tool_name == "nikto":
                output = self._simulate_nikto(target, options)
            elif tool_name == "masscan":
                output = self._simulate_masscan(target, options)
            elif tool_name == "testssl":
                output = self._simulate_testssl(target, options)
            elif tool_name == "hashcat":
                output = self._simulate_hashcat(target, options)
            elif tool_name == "john":
                output = self._simulate_john(target, options)
            elif tool_name == "metasploit":
                output = self._simulate_metasploit(target, options)
            else:
                output = self._simulate_generic_tool(tool_name, target, options)
            
            execution_time = time.time() - start_time
            
            return ToolExecutionResult(
                status=ExecutionResult.SUCCESS,
                method="simulation",
                output=output,
                execution_time=execution_time,
                metadata={
                    "simulated": True,
                    "tool": tool_name,
                    "target": target,
                    "simulation_delay": simulation_delay,
                    "timestamp": datetime.now(timezone.utc).isoformat()
                }
            )
            
        except Exception as e:
            return ToolExecutionResult(
                status=ExecutionResult.FAILED,
                method="simulation",
                error=f"Simulation failed: {str(e)}",
                execution_time=time.time() - start_time
            )
    
    def _simulate_nmap(self, target: str, options: Dict[str, Any]) -> str:
        """Simulate Nmap scan results"""
        
        scan_type = options.get('scan_type', 'tcp')
        ports = options.get('ports', '1-1000')
        
        # Generate realistic port scan results
        open_ports = random.sample(list(self.common_ports.keys()), random.randint(3, 8))
        
        output_lines = [
            f"# Nmap simulation scan results for {target}",
            f"# Scan type: {scan_type}, Ports: {ports}",
            "",
            f"Nmap scan report for {target}",
            f"Host is up (0.{random.randint(10, 99)}s latency).",
            "Not shown: " + str(1000 - len(open_ports)) + " closed ports",
            "PORT     STATE SERVICE",
        ]
        
        for port in sorted(open_ports):
            service = self.common_ports.get(port, "unknown")
            output_lines.append(f"{port}/tcp  open  {service}")
        
        if options.get('version_detection'):
            output_lines.append("")
            output_lines.append("Service detection performed. Please report any incorrect results.")
        
        if options.get('os_detection'):
            os_guess = random.choice(self.simulation_templates["nmap"]["common_results"]["os_detection"])
            output_lines.append("")
            output_lines.append(f"OS details: {os_guess}")
        
        output_lines.append("")
        output_lines.append(f"Nmap done: 1 IP address (1 host up) scanned in {random.uniform(5.0, 30.0):.2f} seconds")
        
        return "\n".join(output_lines)
    
    def _simulate_nuclei(self, target: str, options: Dict[str, Any]) -> str:
        """Simulate Nuclei vulnerability scan results"""
        
        templates = options.get('templates', 'cves,exposures')
        severity = options.get('severity', 'all')
        
        # Generate realistic vulnerability findings
        num_findings = random.randint(2, 6)
        findings = []
        
        for i in range(num_findings):
            vuln = random.choice(self.common_vulns)
            finding = {
                "template": f"nuclei-templates/{vuln['id'].lower()}",
                "template-id": vuln['id'].lower(),
                "info": {
                    "name": vuln['title'],
                    "author": "simulation",
                    "severity": vuln['severity'],
                    "description": vuln['description']
                },
                "host": target,
                "matched": target,
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
            findings.append(finding)
        
        if options.get('json_output'):
            return "\n".join(json.dumps(finding) for finding in findings)
        else:
            output_lines = [f"[{f['info']['severity'].upper()}] {f['info']['name']} - {target}" for f in findings]
            return "\n".join(output_lines)
    
    def _simulate_sqlmap(self, target: str, options: Dict[str, Any]) -> str:
        """Simulate SQLMap injection testing results"""
        
        # Simulate SQL injection detection
        injection_found = random.choice([True, False, False])  # 33% chance
        
        output_lines = [
            f"[*] starting @ {datetime.now().strftime('%H:%M:%S')}",
            f"[*] testing URL '{target}'",
            "",
            "[*] testing parameter 'id'",
        ]
        
        if injection_found:
            injection_type = random.choice(self.simulation_templates["sqlmap"]["injection_types"])
            database = random.choice(self.simulation_templates["sqlmap"]["databases"])
            
            output_lines.extend([
                f"[+] SQL injection vulnerability found!",
                f"[+] Type: {injection_type}",
                f"[+] Database: {database}",
                "",
                "[*] fetching database names",
                "[*] available databases [3]:",
                "[*] information_schema",
                "[*] mysql", 
                "[*] test_db"
            ])
        else:
            output_lines.extend([
                "[-] parameter 'id' does not seem to be injectable",
                "",
                "[*] testing parameter 'username'",
                "[-] parameter 'username' does not seem to be injectable"
            ])
        
        output_lines.append(f"[*] shutting down @ {datetime.now().strftime('%H:%M:%S')}")
        
        return "\n".join(output_lines)
    
    def _simulate_gobuster(self, target: str, options: Dict[str, Any]) -> str:
        """Simulate Gobuster directory enumeration results"""
        
        mode = options.get('mode', 'dir')
        wordlist = options.get('wordlist', 'common.txt')
        
        # Generate realistic directory findings
        directories = random.sample(
            self.simulation_templates["gobuster"]["common_directories"],
            random.randint(3, 7)
        )
        
        output_lines = [
            f"Gobuster v3.1.0",
            f"by OJ Reeves (@TheColonial) & Christian Mehlmauer (@firefart)",
            "",
            f"[+] Url:                     {target}",
            f"[+] Method:                  GET",
            f"[+] Threads:                 {options.get('threads', '10')}",
            f"[+] Wordlist:                {wordlist}",
            f"[+] Status codes:            200,204,301,302,307,401,403",
            f"[+] User Agent:              gobuster/3.1.0",
            f"[+] Timeout:                 10s",
            "",
            "===============================================================",
            f"{datetime.now().strftime('%Y/%m/%d %H:%M:%S')} Starting gobuster",
            "==============================================================="
        ]
        
        for directory in directories:
            status_code = random.choice([200, 301, 302, 403])
            size = random.randint(100, 5000)
            output_lines.append(f"{directory:<20} (Status: {status_code}) [Size: {size}]")
        
        output_lines.append("")
        output_lines.append("===============================================================")
        output_lines.append(f"{datetime.now().strftime('%Y/%m/%d %H:%M:%S')} Finished")
        output_lines.append("===============================================================")
        
        return "\n".join(output_lines)
    
    def _simulate_nikto(self, target: str, options: Dict[str, Any]) -> str:
        """Simulate Nikto web vulnerability scan results"""
        
        # Generate realistic web vulnerabilities
        issues = random.sample(
            self.simulation_templates["nikto"]["common_issues"],
            random.randint(2, 4)
        )
        
        output_lines = [
            f"- Nikto v2.1.6",
            f"+ Target IP:          {target}",
            f"+ Target Hostname:    {target}",
            f"+ Target Port:        {options.get('port', '80')}",
            f"+ Start Time:         {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            "---------------------------------------------------------------------------",
            f"+ Server: Apache/2.4.41 (Ubuntu)",
            ""
        ]
        
        for i, issue in enumerate(issues, 1):
            output_lines.append(f"+ {issue}")
        
        output_lines.extend([
            "",
            f"+ {len(issues)} host(s) tested"
        ])
        
        return "\n".join(output_lines)
    
    def _simulate_masscan(self, target: str, options: Dict[str, Any]) -> str:
        """Simulate Masscan port scan results"""
        
        ports = options.get('ports', '1-1000')
        rate = options.get('rate', '1000')
        
        # Generate realistic fast scan results
        open_ports = random.sample(range(1, 1001), random.randint(5, 12))
        
        output_lines = [
            f"Starting masscan 1.0.5 at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} GMT",
            f"-- forced options: -sS -Pn -n --randomize-hosts -v --send-eth",
            f"Initiating SYN Stealth Scan",
            f"Scanning {target} [ports {ports}]",
            ""
        ]
        
        for port in sorted(open_ports):
            protocol = random.choice(["tcp", "udp"])
            output_lines.append(f"Discovered open port {port}/{protocol} on {target}")
        
        return "\n".join(output_lines)
    
    def _simulate_testssl(self, target: str, options: Dict[str, Any]) -> str:
        """Simulate TestSSL SSL/TLS analysis results"""
        
        protocols = self.simulation_templates["testssl"]["protocols"]
        vulnerabilities = self.simulation_templates["testssl"]["vulnerabilities"]
        
        output_lines = [
            f"",
            f"###########################################################",
            f"    testssl.sh       3.0.8",
            f"",
            f"      This program is free software. Distribution and",
            f"           modification under GPLv2 permitted.",
            f"      USAGE w/o ANY WARRANTY. USE IT AT YOUR OWN RISK!",
            f"",
            f"       Please file bugs @ https://testssl.sh/bugs/",
            f"",
            f"###########################################################",
            f"",
            f" Using \"OpenSSL 1.0.2-chacha (1.0.2k-dev)\" [~183 ciphers]",
            f" on host:/usr/bin/openssl",
            f" (built: \"Jan 18 17:12:17 2019\", platform: \"linux-x86_64\")",
            f"",
            f"",
            f" Start {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}        -->> {target}:443 ({target}) <<--",
            f"",
            f" Further IP addresses:   N/A",
            f" rDNS ({target}):        N/A",
            f" Service detected:       HTTP",
            f"",
        ]
        
        # Protocol support
        output_lines.append(" Protocols")
        for protocol in protocols:
            status = random.choice(["offered", "not offered", "not offered"])
            output_lines.append(f" {protocol:<12} {status}")
        
        output_lines.append("")
        
        # Vulnerabilities
        output_lines.append(" Testing vulnerabilities")
        for vuln in random.sample(vulnerabilities, 3):
            status = random.choice(["not vulnerable", "VULNERABLE", "not tested"])
            output_lines.append(f" {vuln:<12} {status}")
        
        return "\n".join(output_lines)
    
    def _simulate_hashcat(self, target: str, options: Dict[str, Any]) -> str:
        """Simulate Hashcat password cracking results"""
        
        hash_type = options.get('hash_mode', 'MD5')
        attack_mode = options.get('attack_mode', 'dictionary')
        
        # Simulate cracking progress
        success_rate = random.choice(self.simulation_templates["hashcat"]["success_rates"])
        
        output_lines = [
            f"hashcat (v6.2.5) starting...",
            f"",
            f"Hash-mode was not specified with -m. Attempting to auto-detect hash mode.",
            f"The following mode was auto-detected as the only one matching your input hash:",
            f"",
            f"0 | MD5 | Raw Hash",
            f"",
            f"Approaching final keyspace - workload adjusted.",
            f"",
        ]
        
        if success_rate > 50:  # Simulate some success
            num_cracked = random.randint(1, 3)
            for i in range(num_cracked):
                hash_value = "5d41402abc4b2a76b9719d911017c592"  # Example MD5
                password = random.choice(["password123", "admin", "123456", "qwerty"])
                output_lines.append(f"{hash_value}:{password}")
        
        output_lines.extend([
            f"",
            f"Session..........: hashcat",
            f"Status...........: {'Exhausted' if success_rate < 30 else 'Cracked'}",
            f"Hash.Name........: {hash_type}",
            f"Hash.Target......: {target}",
            f"Time.Started.....: {datetime.now().strftime('%a %b %d %H:%M:%S %Y')}",
            f"Speed.#1.........:  {random.randint(100, 500)} MH/s",
            f"Recovered........: {num_cracked if 'num_cracked' in locals() else 0}/{random.randint(5, 10)}"
        ])
        
        return "\n".join(output_lines)
    
    def _simulate_john(self, target: str, options: Dict[str, Any]) -> str:
        """Simulate John the Ripper password cracking results"""
        
        wordlist = options.get('wordlist', 'rockyou.txt')
        format_type = options.get('format', 'auto')
        
        output_lines = [
            f"Using default input encoding: UTF-8",
            f"Loaded 1 password hash (Raw-MD5 [MD5 128/128 SSE2 4x3])",
            f"Warning: no OpenMP support for this hash type, consider --fork=8",
            f"Press 'q' or Ctrl-C to abort, almost any other key for status",
        ]
        
        # Simulate cracking progress
        if random.choice([True, False]):  # 50% success rate
            password = random.choice(["password", "123456", "admin", "letmein"])
            output_lines.extend([
                f"{password}             ({target})",
                f"1g 0:00:00:0{random.randint(1, 9)} DONE (2023-01-01 12:00) {random.randint(10, 100)}g/s",
                f"Use the \"--show\" option to display all of the cracked passwords reliably",
                f"Session completed"
            ])
        else:
            output_lines.extend([
                f"0g 0:00:01:23 DONE (2023-01-01 12:00) 0g/s {random.randint(1000, 5000)}p/s",
                f"Session completed"
            ])
        
        return "\n".join(output_lines)
    
    def _simulate_metasploit(self, target: str, options: Dict[str, Any]) -> str:
        """Simulate Metasploit framework results"""
        
        output_lines = [
            f"",
            f"      .:okOOOkdc'           'cdkOOOko:.",
            f"    .xOOOOOOOOOOOOc       cOOOOOOOOOOOOx.",
            f"   :OOOOOOOOOOOOOOOk,   ,kOOOOOOOOOOOOOOO:",
            f"  'OOOOOOOOOkkkkkkkkOOOOOOOOOOOOOkkkkkkkOOOOOOOO'",
            f"  oOOOOOOOOO.MMMM.oOOOOOOOO.MMMM.OOOOOOOOOo",
            f"  dOOOOOOOOO.MMMMMM.cOOOOOc.MMMMMM,OOOOOOOOOx",
            f"  lOOOOOOOOO.MMMMMMMMM;d;MMMMMMMMM.OOOOOOOOOl",
            f"  .OOOOOOOOOO.MMMMMM.OOOOOOOo.MMMMMM,OOOOOOOOOO.",
            f"   cOOOOOOOOO.MMMM.oOOOOOOOOOO.MMMM.OOOOOOOOOc",
            f"    oOOOOOOOO.MM.ooOOOOOOOOOOOOo.MM.OOOOOOOOo",
            f"     lOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOl",
            f"      ,OOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOO,",
            f"       ':OOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOO:'",
            f"         .:oOOOOOOOOOOOOOOOOOOOOOOOOOOOo:.",
            f"           .oOOOOOOOOOOOOOOOOOOOOOOOo.",
            f"             'oOOOOOOOOOOOOOOOOOo'",
            f"               'oOOOOOOOOOOOo'",
            f"                 'oOOOOOo'",
            f"                   'o'",
            f"",
            f"",
            f"       =[ metasploit v6.3.1-dev                           ]",
            f"+ -- --=[ 2294 exploits - 1201 auxiliary - 409 post       ]",
            f"+ -- --=[ 951 payloads - 45 encoders - 11 nops            ]",
            f"+ -- --=[ 9 evasion                                       ]",
            f"",
            f"msf6 > use auxiliary/scanner/portscan/tcp",
            f"msf6 auxiliary(scanner/portscan/tcp) > set RHOSTS {target}",
            f"RHOSTS => {target}",
            f"msf6 auxiliary(scanner/portscan/tcp) > run",
            f"",
            f"[+] {target}:22 - TCP OPEN",
            f"[+] {target}:80 - TCP OPEN", 
            f"[+] {target}:443 - TCP OPEN",
            f"[*] Scanned 1 of 1 hosts (100% complete)",
            f"[*] Auxiliary module execution completed"
        ]
        
        return "\n".join(output_lines)
    
    def _simulate_generic_tool(self, tool_name: str, target: str, options: Dict[str, Any]) -> str:
        """Simulate generic tool execution"""
        
        return f"""
{tool_name.upper()} Simulation Results
Target: {target}
Timestamp: {datetime.now().isoformat()}

[INFO] This is a simulated output for {tool_name}
[INFO] Tool would normally execute against {target}
[INFO] Options provided: {json.dumps(options, indent=2)}

[SIMULATION] Realistic results would appear here based on the tool's capabilities
[SIMULATION] This simulation provides educational insights into tool behavior
[SIMULATION] For actual security testing, ensure proper tool installation and execution

Status: Simulation completed successfully
Duration: {random.uniform(1.0, 10.0):.2f} seconds
"""

    def get_simulation_info(self) -> Dict[str, Any]:
        """Get information about simulation capabilities"""
        
        return {
            "available_tools": list(self.simulation_templates.keys()),
            "simulation_features": [
                "Realistic output generation",
                "Randomized execution delays",
                "Tool-specific result formats",
                "Educational content included",
                "High-quality demonstrations"
            ],
            "use_cases": [
                "Tool demonstration and training",
                "Educational environments",
                "Fallback when tools unavailable",
                "Safe testing without real execution"
            ]
        }