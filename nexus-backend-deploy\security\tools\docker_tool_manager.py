#!/usr/bin/env python3
"""
Enhanced Docker Tool Manager
Comprehensive containerized security tool execution
"""

import asyncio
import json
import logging
import aiohttp
import time
import docker
from typing import Dict, Any, Optional, List
from pathlib import Path

from .environment_detector import environment_detector
from .hybrid_execution_engine import ToolExecutionResult, ExecutionResult

logger = logging.getLogger(__name__)

class DockerToolManager:
    """Enhanced Docker-based tool execution manager"""
    
    def __init__(self):
        self.environment = environment_detector.environment
        self.capabilities = environment_detector.capabilities
        
        # Docker client
        self.docker_client = None
        self.docker_available = False
        
        # Service endpoints
        self.tool_services = {
            "nmap": "http://nmap-service:8080",
            "nuclei": "http://nuclei-service:8081", 
            "sqlmap": "http://sqlmap-service:8082",
            "masscan": "http://masscan-service:8083",
            "gobuster": "http://gobuster-service:8084"
        }
        
        # Container images
        self.tool_images = {
            "nmap": "instrumentisto/nmap:latest",
            "nuclei": "projectdiscovery/nuclei:latest",
            "sqlmap": "paolobasso/sqlmap:latest",
            "masscan": "stanislav/masscan:latest",
            "gobuster": "opsec/gobuster:latest",
            "nikto": "securecodebox/nikto:latest",
            "dirb": "cytopia/dirb:latest",
            "wpscan": "wpscanteam/wpscan:latest",
            "ffuf": "securecodebox/ffuf:latest",
            "feroxbuster": "epi052/feroxbuster:latest",
            "whatweb": "mpepping/whatweb:latest",
            "enum4linux": "l4rm4nd/enum4linux-ng:latest",
            "testssl": "drwetter/testssl.sh:latest",
            "sslyze": "nablac0d3/sslyze:latest",
            "hashcat": "dizcza/docker-hashcat:latest",
            "john": "ghcr.io/openwall/john:latest",
            "metasploit": "metasploitframework/metasploit-framework:latest",
            "searchsploit": "vulnerscom/exploitdb:latest"
        }
        
        # Service status cache
        self.service_status = {}
        
        logger.info("🐳 Docker Tool Manager initialized")
    
    async def initialize(self):
        """Initialize Docker tool manager with enhanced capabilities"""
        
        try:
            # Initialize Docker client
            self.docker_client = docker.from_env()
            self.docker_available = True
            
            # Check Docker daemon
            info = self.docker_client.info()
            logger.info(f"🐳 Docker daemon connected: {info.get('ServerVersion', 'Unknown')}")
            
            # Check service availability
            await self._check_service_status()
            
            # Pull essential images if not available
            await self._ensure_essential_images()
            
            # Initialize containerized tool manager connection
            await self._initialize_containerized_manager()
            
        except Exception as e:
            logger.warning(f"Docker initialization failed: {e}")
            self.docker_available = False
    
    async def _check_service_status(self):
        """Check status of containerized services"""
        
        async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=5)) as session:
            for service_name, service_url in self.tool_services.items():
                try:
                    async with session.get(f"{service_url}/health") as response:
                        if response.status == 200:
                            self.service_status[service_name] = True
                            logger.info(f"✅ {service_name} service available")
                        else:
                            self.service_status[service_name] = False
                except Exception:
                    self.service_status[service_name] = False
                    logger.debug(f"❌ {service_name} service not available")
    
    async def _ensure_essential_images(self):
        """Pull essential Docker images if not available"""
        
        essential_images = ['nmap', 'nuclei', 'sqlmap', 'gobuster', 'nikto']
        
        for tool in essential_images:
            if tool in self.tool_images:
                try:
                    image_name = self.tool_images[tool]
                    
                    # Check if image exists locally
                    try:
                        self.docker_client.images.get(image_name)
                        logger.debug(f"✅ {tool} image available locally")
                    except docker.errors.ImageNotFound:
                        logger.info(f"📥 Pulling {tool} image...")
                        self.docker_client.images.pull(image_name)
                        logger.info(f"✅ {tool} image pulled successfully")
                        
                except Exception as e:
                    logger.warning(f"Failed to pull {tool} image: {e}")
    
    async def is_service_available(self, service_name: str) -> bool:
        """Check if a containerized service is available"""
        return self.service_status.get(service_name, False)
    
    async def execute_service(
        self,
        service_name: str,
        target: str,
        options: Dict[str, Any],
        timeout: int = 300
    ) -> ToolExecutionResult:
        """Execute tool via containerized service"""
        
        start_time = time.time()
        
        if not await self.is_service_available(service_name):
            return ToolExecutionResult(
                status=ExecutionResult.NOT_AVAILABLE,
                method="docker_service",
                error=f"Service {service_name} not available"
            )
        
        service_url = self.tool_services.get(service_name)
        if not service_url:
            return ToolExecutionResult(
                status=ExecutionResult.NOT_AVAILABLE,
                method="docker_service",
                error=f"Service URL not defined for {service_name}"
            )
        
        try:
            # Prepare request payload
            payload = {
                'target': target,
                'options': options,
                'timeout': timeout
            }
            
            logger.info(f"🐳 Executing {service_name} via service: {target}")
            
            async with aiohttp.ClientSession(
                timeout=aiohttp.ClientTimeout(total=timeout + 30)
            ) as session:
                async with session.post(
                    f"{service_url}/scan",
                    json=payload
                ) as response:
                    
                    if response.status == 200:
                        result_data = await response.json()
                        
                        return ToolExecutionResult(
                            status=ExecutionResult.SUCCESS,
                            method="docker_service",
                            output=result_data.get('output', ''),
                            error=result_data.get('error', ''),
                            exit_code=result_data.get('exit_code', 0),
                            execution_time=time.time() - start_time,
                            metadata={
                                'service': service_name,
                                'service_url': service_url,
                                'payload': payload
                            }
                        )
                    else:
                        error_text = await response.text()
                        return ToolExecutionResult(
                            status=ExecutionResult.FAILED,
                            method="docker_service",
                            error=f"Service returned {response.status}: {error_text}",
                            execution_time=time.time() - start_time
                        )
                        
        except asyncio.TimeoutError:
            return ToolExecutionResult(
                status=ExecutionResult.TIMEOUT,
                method="docker_service",
                error=f"Service request timed out after {timeout} seconds",
                execution_time=time.time() - start_time
            )
        except Exception as e:
            return ToolExecutionResult(
                status=ExecutionResult.FAILED,
                method="docker_service",
                error=f"Service execution failed: {str(e)}",
                execution_time=time.time() - start_time
            )
    
    async def execute_container(
        self,
        image_name: str,
        command: str,
        target: str,
        options: Dict[str, Any],
        timeout: int = 300
    ) -> ToolExecutionResult:
        """Execute tool via direct container execution"""
        
        start_time = time.time()
        
        if not self.docker_available:
            return ToolExecutionResult(
                status=ExecutionResult.NOT_AVAILABLE,
                method="docker_container",
                error="Docker not available"
            )
        
        try:
            # Build command
            full_command = self._build_container_command(command, target, options)
            
            logger.info(f"🐳 Executing container {image_name}: {full_command[:100]}...")
            
            # Run container
            container = self.docker_client.containers.run(
                image_name,
                command=full_command,
                remove=True,
                detach=True,
                network_mode='host',  # Allow network access
                cap_add=['NET_ADMIN', 'NET_RAW'],  # For network tools
                security_opt=['seccomp=unconfined']  # For advanced tools
            )
            
            # Wait for completion with timeout
            try:
                result = container.wait(timeout=timeout)
                
                # Get logs
                logs = container.logs(stdout=True, stderr=True).decode('utf-8', errors='replace')
                
                exit_code = result['StatusCode']
                
                if exit_code == 0:
                    return ToolExecutionResult(
                        status=ExecutionResult.SUCCESS,
                        method="docker_container",
                        output=logs,
                        exit_code=exit_code,
                        execution_time=time.time() - start_time,
                        metadata={
                            'image': image_name,
                            'command': full_command,
                            'container_id': container.id
                        }
                    )
                else:
                    return ToolExecutionResult(
                        status=ExecutionResult.FAILED,
                        method="docker_container",
                        output=logs,
                        exit_code=exit_code,
                        execution_time=time.time() - start_time,
                        metadata={
                            'image': image_name,
                            'command': full_command,
                            'container_id': container.id
                        }
                    )
                    
            except Exception as timeout_error:
                # Kill container on timeout
                try:
                    container.kill()
                except:
                    pass
                
                return ToolExecutionResult(
                    status=ExecutionResult.TIMEOUT,
                    method="docker_container",
                    error=f"Container execution timed out after {timeout} seconds",
                    execution_time=time.time() - start_time
                )
                
        except docker.errors.ImageNotFound:
            # Try to pull image
            try:
                logger.info(f"📥 Pulling image {image_name}...")
                self.docker_client.images.pull(image_name)
                
                # Retry execution
                return await self.execute_container(image_name, command, target, options, timeout)
                
            except Exception as pull_error:
                return ToolExecutionResult(
                    status=ExecutionResult.NOT_AVAILABLE,
                    method="docker_container",
                    error=f"Image not found and pull failed: {str(pull_error)}",
                    execution_time=time.time() - start_time
                )
                
        except Exception as e:
            return ToolExecutionResult(
                status=ExecutionResult.FAILED,
                method="docker_container",
                error=f"Container execution failed: {str(e)}",
                execution_time=time.time() - start_time
            )
    
    def _build_container_command(self, base_command: str, target: str, options: Dict[str, Any]) -> str:
        """Build command for container execution"""
        
        # Extract tool name
        tool_name = base_command.split()[0]
        
        # Build tool-specific command
        if tool_name == 'nmap':
            return self._build_nmap_container_command(target, options)
        elif tool_name == 'nuclei':
            return self._build_nuclei_container_command(target, options)
        elif tool_name == 'sqlmap':
            return self._build_sqlmap_container_command(target, options)
        elif tool_name == 'gobuster':
            return self._build_gobuster_container_command(target, options)
        elif tool_name == 'nikto':
            return self._build_nikto_container_command(target, options)
        elif tool_name == 'masscan':
            return self._build_masscan_container_command(target, options)
        else:
            # Generic command building
            return f"{base_command} {target}"
    
    def _build_nmap_container_command(self, target: str, options: Dict[str, Any]) -> str:
        """Build Nmap command for container"""
        cmd = ["nmap"]
        
        # Basic scan options
        scan_type = options.get('scan_type', 'tcp')
        if scan_type == 'syn':
            cmd.append('-sS')
        elif scan_type == 'udp':
            cmd.append('-sU')
        elif scan_type == 'tcp':
            cmd.append('-sT')
        
        # Ports
        ports = options.get('ports')
        if ports:
            cmd.extend(['-p', str(ports)])
        
        # Timing
        timing = options.get('timing', '4')
        cmd.extend(['-T', str(timing)])
        
        # Version detection
        if options.get('version_detection'):
            cmd.append('-sV')
        
        # OS detection
        if options.get('os_detection'):
            cmd.append('-O')
        
        # Scripts
        scripts = options.get('scripts')
        if scripts:
            cmd.extend(['--script', scripts])
        
        cmd.append(target)
        return ' '.join(cmd)
    
    def _build_nuclei_container_command(self, target: str, options: Dict[str, Any]) -> str:
        """Build Nuclei command for container"""
        cmd = ["nuclei"]
        
        # Target
        cmd.extend(['-target', target])
        
        # Templates
        templates = options.get('templates')
        if templates:
            cmd.extend(['-t', templates])
        
        # Severity
        severity = options.get('severity')
        if severity:
            cmd.extend(['-severity', severity])
        
        # Concurrency
        concurrency = options.get('concurrency', '25')
        cmd.extend(['-c', str(concurrency)])
        
        # JSON output
        if options.get('json_output'):
            cmd.append('-json')
        
        # Silent mode
        if options.get('silent'):
            cmd.append('-silent')
        
        return ' '.join(cmd)
    
    def _build_sqlmap_container_command(self, target: str, options: Dict[str, Any]) -> str:
        """Build SQLMap command for container"""
        cmd = ["python", "/sqlmap/sqlmap.py"]
        
        # URL
        cmd.extend(['-u', target])
        
        # Database type
        dbms = options.get('dbms')
        if dbms:
            cmd.extend(['--dbms', dbms])
        
        # Techniques
        technique = options.get('technique')
        if technique:
            cmd.extend(['--technique', technique])
        
        # Level and risk
        level = options.get('level', '1')
        cmd.extend(['--level', str(level)])
        
        risk = options.get('risk', '1')
        cmd.extend(['--risk', str(risk)])
        
        # Batch mode
        cmd.append('--batch')
        
        return ' '.join(cmd)
    
    def _build_gobuster_container_command(self, target: str, options: Dict[str, Any]) -> str:
        """Build Gobuster command for container"""
        cmd = ["gobuster"]
        
        # Mode
        mode = options.get('mode', 'dir')
        cmd.append(mode)
        
        # URL
        cmd.extend(['-u', target])
        
        # Wordlist
        wordlist = options.get('wordlist', '/usr/share/wordlists/dirb/common.txt')
        cmd.extend(['-w', wordlist])
        
        # Threads
        threads = options.get('threads', '10')
        cmd.extend(['-t', str(threads)])
        
        # Extensions
        extensions = options.get('extensions')
        if extensions:
            cmd.extend(['-x', extensions])
        
        return ' '.join(cmd)
    
    def _build_nikto_container_command(self, target: str, options: Dict[str, Any]) -> str:
        """Build Nikto command for container"""
        cmd = ["nikto"]
        
        # Host
        cmd.extend(['-h', target])
        
        # Port
        port = options.get('port')
        if port:
            cmd.extend(['-p', str(port)])
        
        # SSL
        if options.get('ssl'):
            cmd.append('-ssl')
        
        # Format
        cmd.extend(['-Format', 'txt'])
        
        return ' '.join(cmd)
    
    def _build_masscan_container_command(self, target: str, options: Dict[str, Any]) -> str:
        """Build Masscan command for container"""
        cmd = ["masscan"]
        
        # Target
        cmd.append(target)
        
        # Ports
        ports = options.get('ports', '1-65535')
        cmd.extend(['-p', str(ports)])
        
        # Rate
        rate = options.get('rate', '1000')
        cmd.extend(['--rate', str(rate)])
        
        return ' '.join(cmd)
    
    async def get_available_images(self) -> List[str]:
        """Get list of available Docker images"""
        if not self.docker_available:
            return []
        
        try:
            images = self.docker_client.images.list()
            return [tag for image in images for tag in image.tags if tag]
        except Exception as e:
            logger.error(f"Failed to list Docker images: {e}")
            return []
    
    async def pull_image(self, image_name: str) -> bool:
        """Pull a Docker image"""
        if not self.docker_available:
            return False
        
        try:
            logger.info(f"📥 Pulling Docker image: {image_name}")
            self.docker_client.images.pull(image_name)
            logger.info(f"✅ Successfully pulled {image_name}")
            return True
        except Exception as e:
            logger.error(f"Failed to pull image {image_name}: {e}")
            return False
    
    def get_service_status(self) -> Dict[str, bool]:
        """Get status of all services"""
        return self.service_status.copy()
    
    def get_available_tools(self) -> List[str]:
        """Get list of available tools"""
        available_tools = []
        
        # Add tools with available services
        for tool, available in self.service_status.items():
            if available:
                available_tools.append(tool)
        
        # Add tools with available images
        for tool, image in self.tool_images.items():
            if tool not in available_tools:
                try:
                    if self.docker_client:
                        self.docker_client.images.get(image)
                        available_tools.append(tool)
                except:
                    pass
        
        return available_tools
    
    async def _initialize_containerized_manager(self):
        """Initialize connection to containerized tool manager"""
        
        try:
            # Check if containerized tool manager is available
            containerized_manager_url = "http://tool-manager:8090"
            
            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=5)) as session:
                async with session.get(f"{containerized_manager_url}/health") as response:
                    if response.status == 200:
                        self.containerized_manager_url = containerized_manager_url
                        logger.info("✅ Containerized tool manager connected")
                    else:
                        self.containerized_manager_url = None
                        logger.warning("⚠️ Containerized tool manager not available")
                        
        except Exception as e:
            self.containerized_manager_url = None
            logger.debug(f"Containerized tool manager not available: {e}")
    
    async def execute_containerized_campaign(
        self,
        campaign_id: str,
        targets: List[str],
        tools: List[str],
        options: Dict[str, Any] = None
    ) -> ToolExecutionResult:
        """Execute a campaign using containerized tool manager"""
        
        if not self.containerized_manager_url:
            return ToolExecutionResult(
                status=ExecutionResult.NOT_AVAILABLE,
                method="docker_campaign",
                error="Containerized tool manager not available"
            )
        
        start_time = time.time()
        options = options or {}
        
        try:
            payload = {
                "campaign_id": campaign_id,
                "targets": targets,
                "tools": tools,
                "parallel_execution": options.get("parallel_execution", True),
                "timeout_per_tool": options.get("timeout_per_tool", 300)
            }
            
            logger.info(f"🚀 Executing containerized campaign {campaign_id} with tools: {tools}")
            
            async with aiohttp.ClientSession(
                timeout=aiohttp.ClientTimeout(total=options.get("timeout_per_tool", 300) * len(tools) + 60)
            ) as session:
                async with session.post(
                    f"{self.containerized_manager_url}/campaigns/execute",
                    json=payload
                ) as response:
                    
                    if response.status == 200:
                        result_data = await response.json()
                        
                        return ToolExecutionResult(
                            status=ExecutionResult.SUCCESS,
                            method="docker_campaign",
                            output=json.dumps(result_data, indent=2),
                            execution_time=time.time() - start_time,
                            metadata={
                                "campaign_id": campaign_id,
                                "tools_executed": tools,
                                "targets_count": len(targets),
                                "containerized_manager": self.containerized_manager_url
                            }
                        )
                    else:
                        error_text = await response.text()
                        return ToolExecutionResult(
                            status=ExecutionResult.FAILED,
                            method="docker_campaign",
                            error=f"Campaign execution failed with status {response.status}: {error_text}",
                            execution_time=time.time() - start_time
                        )
                        
        except Exception as e:
            return ToolExecutionResult(
                status=ExecutionResult.FAILED,
                method="docker_campaign",
                error=f"Campaign execution failed: {str(e)}",
                execution_time=time.time() - start_time
            )
    
    async def get_campaign_status(self, campaign_id: str) -> Dict[str, Any]:
        """Get status of running campaign"""
        
        if not self.containerized_manager_url:
            return {"error": "Containerized tool manager not available"}
        
        try:
            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=10)) as session:
                async with session.get(
                    f"{self.containerized_manager_url}/campaigns/{campaign_id}/status"
                ) as response:
                    
                    if response.status == 200:
                        return await response.json()
                    else:
                        return {"error": f"Failed to get campaign status: {response.status}"}
                        
        except Exception as e:
            return {"error": f"Campaign status check failed: {str(e)}"}
    
    async def start_docker_services(self, services: List[str] = None) -> Dict[str, bool]:
        """Start Docker services for security tools"""
        
        if not self.docker_available:
            return {"error": "Docker not available"}
        
        services = services or list(self.tool_services.keys())
        results = {}
        
        for service_name in services:
            try:
                # Check if service container exists
                container_name = f"{service_name}-service"
                
                try:
                    container = self.docker_client.containers.get(container_name)
                    
                    if container.status != 'running':
                        container.start()
                        logger.info(f"✅ Started {service_name} service")
                        results[service_name] = True
                    else:
                        logger.info(f"✅ {service_name} service already running")
                        results[service_name] = True
                        
                except docker.errors.NotFound:
                    # Container doesn't exist, try to create it
                    logger.info(f"🔄 Creating {service_name} service container...")
                    
                    if service_name in self.tool_images:
                        image = self.tool_images[service_name]
                        
                        # Pull image if not available
                        try:
                            self.docker_client.images.get(image)
                        except docker.errors.ImageNotFound:
                            logger.info(f"📥 Pulling {image}...")
                            self.docker_client.images.pull(image)
                        
                        # Create and start container
                        container = self.docker_client.containers.run(
                            image,
                            name=container_name,
                            detach=True,
                            network_mode='bridge',
                            restart_policy={"Name": "unless-stopped"},
                            ports={8080: None},  # Auto-assign port
                            environment={
                                'SERVICE_NAME': service_name,
                                'SERVICE_PORT': '8080'
                            }
                        )
                        
                        logger.info(f"✅ Created and started {service_name} service")
                        results[service_name] = True
                    else:
                        logger.warning(f"⚠️ No image defined for {service_name}")
                        results[service_name] = False
                        
            except Exception as e:
                logger.error(f"❌ Failed to start {service_name} service: {e}")
                results[service_name] = False
        
        return results
    
    async def stop_docker_services(self, services: List[str] = None) -> Dict[str, bool]:
        """Stop Docker services"""
        
        if not self.docker_available:
            return {"error": "Docker not available"}
        
        services = services or list(self.tool_services.keys())
        results = {}
        
        for service_name in services:
            try:
                container_name = f"{service_name}-service"
                container = self.docker_client.containers.get(container_name)
                
                if container.status == 'running':
                    container.stop()
                    logger.info(f"🛑 Stopped {service_name} service")
                
                results[service_name] = True
                
            except docker.errors.NotFound:
                logger.info(f"ℹ️ {service_name} service not found")
                results[service_name] = True
                
            except Exception as e:
                logger.error(f"❌ Failed to stop {service_name} service: {e}")
                results[service_name] = False
        
        return results
    
    async def get_docker_stats(self) -> Dict[str, Any]:
        """Get Docker performance statistics"""
        
        if not self.docker_available:
            return {"error": "Docker not available"}
        
        try:
            stats = {
                "docker_info": self.docker_client.info(),
                "containers": {},
                "images": [],
                "networks": [],
                "volumes": []
            }
            
            # Container stats
            for container in self.docker_client.containers.list(all=True):
                container_stats = {
                    "name": container.name,
                    "status": container.status,
                    "image": container.image.tags[0] if container.image.tags else "unknown",
                    "ports": container.ports
                }
                
                if container.status == 'running':
                    try:
                        live_stats = container.stats(stream=False)
                        container_stats["cpu_usage"] = live_stats.get("cpu_stats", {})
                        container_stats["memory_usage"] = live_stats.get("memory_stats", {})
                    except Exception:
                        pass
                
                stats["containers"][container.name] = container_stats
            
            # Image stats
            for image in self.docker_client.images.list():
                stats["images"].append({
                    "tags": image.tags,
                    "size": image.attrs.get("Size", 0),
                    "created": image.attrs.get("Created", "")
                })
            
            # Network stats
            for network in self.docker_client.networks.list():
                stats["networks"].append({
                    "name": network.name,
                    "driver": network.attrs.get("Driver", ""),
                    "containers": len(network.attrs.get("Containers", {}))
                })
            
            # Volume stats
            for volume in self.docker_client.volumes.list():
                stats["volumes"].append({
                    "name": volume.name,
                    "driver": volume.attrs.get("Driver", ""),
                    "mountpoint": volume.attrs.get("Mountpoint", "")
                })
            
            return stats
            
        except Exception as e:
            return {"error": f"Failed to get Docker stats: {str(e)}"}
    
    def is_containerized_manager_available(self) -> bool:
        """Check if containerized tool manager is available"""
        return self.containerized_manager_url is not None