/**
 * Hashcat Tool Component
 * World's fastest and most advanced password recovery utility
 */
import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { 
  Key,
  Play,
  Square,
  Download,
  Settings,
  Target,
  Globe,
  FileText,
  Terminal,
  Copy,
  RotateCcw,
  Clock,
  Zap,
  Filter,
  Eye,
  AlertTriangle,
  CheckCircle,
  TrendingUp,
  Activity,
  Lock,
  Search,
  List,
  BarChart3,
  Database,
  Server,
  Bug,
  Info,
  Code,
  Package,
  Layers,
  Shield,
  Crosshair,
  Cpu,
  Network,
  Gauge,
  Tag,
  Hash,
  Workflow,
  GitBranch,
  Book,
  Users,
  Table,
  FileX,
  Skull,
  Unlock,
  HardDrive,
  Flame,
  Binary,
  Braces,
  FileKey
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useBackendStore } from '@/stores/backend-store';
import { apiClient, ToolExecution } from '@/services/api-client';
import { toast } from 'sonner';

/**
 * Hashcat configuration interface
 */
interface HashcatConfig {
  target: {
    hashFile: string;
    hashString: string;
    hashType: number;
    username: boolean;
    delimiter: string;
  };
  attack: {
    mode: 'dictionary' | 'brute-force' | 'combinator' | 'hybrid' | 'association';
    wordlist: string;
    wordlist2: string;
    customCharset1: string;
    customCharset2: string;
    customCharset3: string;
    customCharset4: string;
    mask: string;
    maskIncrement: boolean;
    incrementMin: number;
    incrementMax: number;
  };
  rules: {
    enabled: boolean;
    ruleFile: string;
    generateRules: number;
    generateRulesFuncMin: number;
    generateRulesFuncMax: number;
    generateRulesFuncSel: string;
  };
  performance: {
    workloadProfile: 1 | 2 | 3 | 4;
    optimized: boolean;
    segmentSize: number;
    bitmapMin: number;
    bitmapMax: number;
    cpuAffinity: string;
    deviceTypes: {
      cpu: boolean;
      gpu: boolean;
    };
    opencl: {
      vectorWidth: number;
      devices: string;
      platforms: string;
    };
  };
  output: {
    outputFile: string;
    outputFormat: number;
    debugMode: number;
    quiet: boolean;
    statusTimer: number;
    machineReadable: boolean;
    logfileDisable: boolean;
    potfileDisable: boolean;
    showCommand: boolean;
  };
  limits: {
    skip: number;
    limit: number;
    removeTimer: number;
    runtimeLimit: number;
    sessionName: string;
    restore: boolean;
    restoreTimer: number;
    restoreFile: string;
  };
  markov: {
    enabled: boolean;
    hcstatFile: string;
    threshold: number;
    classic: boolean;
  };
  misc: {
    hexSalt: boolean;
    hexWordlist: boolean;
    hexCharset: boolean;
    encoding: string;
    selfTest: boolean;
    loopback: boolean;
    force: boolean;
    keepGuessing: boolean;
    customCharsetOptions: string;
  };
  customArgs: string;
}

/**
 * Common hash types
 */
const HASH_TYPES = {
  'md5': { id: 0, name: 'MD5', category: 'Raw Hash' },
  'sha1': { id: 100, name: 'SHA1', category: 'Raw Hash' },
  'sha256': { id: 1400, name: 'SHA-256', category: 'Raw Hash' },
  'sha512': { id: 1700, name: 'SHA-512', category: 'Raw Hash' },
  'md5crypt': { id: 500, name: 'md5crypt', category: 'Unix' },
  'sha512crypt': { id: 1800, name: 'sha512crypt', category: 'Unix' },
  'bcrypt': { id: 3200, name: 'bcrypt', category: 'Unix' },
  'ntlm': { id: 1000, name: 'NTLM', category: 'Windows' },
  'netntlmv2': { id: 5600, name: 'NetNTLMv2', category: 'Windows' },
  'kerberos': { id: 13100, name: 'Kerberos 5 TGS-REP', category: 'Kerberos' },
  'mysql': { id: 300, name: 'MySQL4.1/MySQL5', category: 'Database' },
  'postgres': { id: 12000, name: 'PostgreSQL', category: 'Database' },
  'mssql': { id: 1731, name: 'MSSQL (2012, 2014)', category: 'Database' },
  'oracle': { id: 112, name: 'Oracle S', category: 'Database' },
  'wordpress': { id: 400, name: 'WordPress (MD5)', category: 'Web App' },
  'joomla': { id: 11, name: 'Joomla < 2.5.18', category: 'Web App' },
  'drupal7': { id: 7900, name: 'Drupal7', category: 'Web App' },
  'wpa2': { id: 22000, name: 'WPA-PMKID-PBKDF2', category: 'Network' },
  'wpa3': { id: 22001, name: 'WPA-PMKID-PMK', category: 'Network' },
  'office2013': { id: 9600, name: 'MS Office 2013', category: 'Document' },
  'pdf': { id: 10500, name: 'PDF 1.4 - 1.6', category: 'Document' },
  'zip': { id: 17200, name: 'PKZIP (Compressed)', category: 'Archive' },
  'rar5': { id: 13000, name: 'RAR5', category: 'Archive' },
  '7z': { id: 11600, name: '7-Zip', category: 'Archive' },
  'bitcoin': { id: 11300, name: 'Bitcoin/Litecoin wallet', category: 'Cryptocurrency' },
  'ethereum': { id: 15700, name: 'Ethereum Wallet', category: 'Cryptocurrency' }
};

/**
 * Attack modes
 */
const ATTACK_MODES = {
  'dictionary': { id: 0, name: 'Dictionary Attack', description: 'Use wordlist(s) to crack hashes' },
  'brute-force': { id: 3, name: 'Brute-Force', description: 'Try all possible combinations' },
  'combinator': { id: 1, name: 'Combinator', description: 'Combine two wordlists' },
  'hybrid': { id: 6, name: 'Hybrid Wordlist + Mask', description: 'Wordlist with mask appended' },
  'association': { id: 9, name: 'Association', description: 'Target specific hash:password pairs' }
};

/**
 * Workload profiles
 */
const WORKLOAD_PROFILES = {
  1: { name: 'Low', description: 'Minimal performance, low resource usage' },
  2: { name: 'Default', description: 'Balanced performance and resource usage' },
  3: { name: 'High', description: 'Maximum performance, desktop may lag' },
  4: { name: 'Nightmare', description: 'Extreme performance, system unresponsive' }
};

/**
 * Common wordlists
 */
const WORDLISTS = {
  'rockyou': '/usr/share/wordlists/rockyou.txt',
  'fasttrack': '/usr/share/wordlists/fasttrack.txt',
  'dirb_common': '/usr/share/dirb/wordlists/common.txt',
  'metasploit_passwords': '/usr/share/metasploit-framework/data/wordlists/password.lst',
  'metasploit_usernames': '/usr/share/metasploit-framework/data/wordlists/namelist.txt',
  'seclist_top1000': '/usr/share/seclists/Passwords/Common-Credentials/10-million-password-list-top-1000.txt',
  'seclist_top10000': '/usr/share/seclists/Passwords/Common-Credentials/10-million-password-list-top-10000.txt',
  'seclist_probable': '/usr/share/seclists/Passwords/probable-v2-top12000.txt',
  'john_password': '/usr/share/john/password.lst',
  'custom': ''
};

/**
 * Common masks for brute force
 */
const COMMON_MASKS = {
  'numeric_4': '?d?d?d?d',
  'numeric_6': '?d?d?d?d?d?d',
  'numeric_8': '?d?d?d?d?d?d?d?d',
  'lower_alpha_6': '?l?l?l?l?l?l',
  'lower_alpha_8': '?l?l?l?l?l?l?l?l',
  'upper_alpha_6': '?u?u?u?u?u?u',
  'mixed_alpha_8': '?a?a?a?a?a?a?a?a',
  'alphanum_6': '?1?1?1?1?1?1',
  'alphanum_8': '?1?1?1?1?1?1?1?1',
  'special_8': '?a?a?a?a?a?a?a?s',
  'complex_8': '?u?l?l?l?l?d?d?s',
  'custom': ''
};

/**
 * Hash categories for grouping
 */
const HASH_CATEGORIES = ['Raw Hash', 'Unix', 'Windows', 'Database', 'Web App', 'Network', 'Document', 'Archive', 'Cryptocurrency'];

export default function HashcatTool() {
  const { connectionStatus } = useBackendStore();
  const [config, setConfig] = React.useState<HashcatConfig>({
    target: {
      hashFile: '',
      hashString: '',
      hashType: 0,
      username: false,
      delimiter: ':'
    },
    attack: {
      mode: 'dictionary',
      wordlist: 'rockyou',
      wordlist2: '',
      customCharset1: '?l?d',
      customCharset2: '?l?u?d',
      customCharset3: '?d?s',
      customCharset4: '',
      mask: '',
      maskIncrement: false,
      incrementMin: 1,
      incrementMax: 8
    },
    rules: {
      enabled: false,
      ruleFile: 'best64.rule',
      generateRules: 0,
      generateRulesFuncMin: 1,
      generateRulesFuncMax: 4,
      generateRulesFuncSel: ''
    },
    performance: {
      workloadProfile: 2,
      optimized: true,
      segmentSize: 33,
      bitmapMin: 16,
      bitmapMax: 24,
      cpuAffinity: '',
      deviceTypes: {
        cpu: false,
        gpu: true
      },
      opencl: {
        vectorWidth: 0,
        devices: '',
        platforms: ''
      }
    },
    output: {
      outputFile: '',
      outputFormat: 3,
      debugMode: 0,
      quiet: false,
      statusTimer: 60,
      machineReadable: false,
      logfileDisable: false,
      potfileDisable: false,
      showCommand: false
    },
    limits: {
      skip: 0,
      limit: 0,
      removeTimer: 0,
      runtimeLimit: 0,
      sessionName: '',
      restore: false,
      restoreTimer: 0,
      restoreFile: ''
    },
    markov: {
      enabled: false,
      hcstatFile: '',
      threshold: 0,
      classic: false
    },
    misc: {
      hexSalt: false,
      hexWordlist: false,
      hexCharset: false,
      encoding: '',
      selfTest: false,
      loopback: false,
      force: false,
      keepGuessing: false,
      customCharsetOptions: ''
    },
    customArgs: ''
  });

  const [execution, setExecution] = React.useState<ToolExecution | null>(null);
  const [activeTab, setActiveTab] = React.useState('configure');
  const [benchmarkMode, setBenchmarkMode] = React.useState(false);
  const [showInfo, setShowInfo] = React.useState(false);

  /**
   * Handle configuration changes
   */
  const updateConfig = (path: string, value: any) => {
    setConfig(prev => {
      const keys = path.split('.');
      const newConfig = { ...prev };
      let current = newConfig;
      
      for (let i = 0; i < keys.length - 1; i++) {
        current[keys[i]] = { ...current[keys[i]] };
        current = current[keys[i]];
      }
      
      current[keys[keys.length - 1]] = value;
      return newConfig;
    });
  };

  /**
   * Handle preset configurations
   */
  const applyPreset = (preset: string) => {
    const presets = {
      'quick_crack': {
        attack: { ...config.attack, mode: 'dictionary', wordlist: 'rockyou' },
        rules: { ...config.rules, enabled: true, ruleFile: 'best64.rule' },
        performance: { ...config.performance, workloadProfile: 2 }
      },
      'thorough_dictionary': {
        attack: { ...config.attack, mode: 'dictionary', wordlist: 'rockyou' },
        rules: { ...config.rules, enabled: true, ruleFile: 'dive.rule' },
        performance: { ...config.performance, workloadProfile: 3, optimized: true }
      },
      'brute_force_numeric': {
        attack: { 
          ...config.attack, 
          mode: 'brute-force', 
          mask: '?d?d?d?d?d?d?d?d',
          maskIncrement: true,
          incrementMin: 4,
          incrementMax: 8
        },
        performance: { ...config.performance, workloadProfile: 3 }
      },
      'brute_force_alpha': {
        attack: { 
          ...config.attack, 
          mode: 'brute-force', 
          mask: '?l?l?l?l?l?l?l?l',
          maskIncrement: true,
          incrementMin: 6,
          incrementMax: 8
        },
        performance: { ...config.performance, workloadProfile: 3 }
      },
      'hybrid_attack': {
        attack: { 
          ...config.attack, 
          mode: 'hybrid', 
          wordlist: 'rockyou',
          mask: '?d?d?d?d'
        },
        performance: { ...config.performance, workloadProfile: 2 }
      },
      'combinator_attack': {
        attack: { 
          ...config.attack, 
          mode: 'combinator', 
          wordlist: 'rockyou',
          wordlist2: 'fasttrack'
        },
        performance: { ...config.performance, workloadProfile: 2 }
      },
      'gpu_optimized': {
        performance: { 
          ...config.performance, 
          workloadProfile: 3,
          optimized: true,
          deviceTypes: { cpu: false, gpu: true }
        }
      },
      'cpu_only': {
        performance: { 
          ...config.performance, 
          workloadProfile: 1,
          deviceTypes: { cpu: true, gpu: false }
        }
      }
    };

    if (presets[preset]) {
      setConfig(prev => ({ ...prev, ...presets[preset] }));
    }
  };

  /**
   * Run benchmark
   */
  const runBenchmark = async () => {
    setBenchmarkMode(true);
    try {
      const execution = await apiClient.executeSecurityTool('hashcat-benchmark', { hashType: config.target.hashType });
      setExecution(execution);
      setActiveTab('output');
      toast.success('Hashcat benchmark started');
    } catch (error) {
      toast.error(`Failed to start benchmark: ${error.message}`);
    } finally {
      setBenchmarkMode(false);
    }
  };

  /**
   * Show hash information
   */
  const showHashInfo = async () => {
    setShowInfo(true);
    try {
      const execution = await apiClient.executeSecurityTool('hashcat-info', {});
      setExecution(execution);
      setActiveTab('output');
      toast.success('Fetching hash information');
    } catch (error) {
      toast.error(`Failed to get hash info: ${error.message}`);
    } finally {
      setShowInfo(false);
    }
  };

  /**
   * Start Hashcat attack
   */
  const startAttack = async () => {
    if (!config.target.hashFile.trim() && !config.target.hashString.trim()) {
      toast.error('Please provide a hash file or hash string');
      return;
    }

    if (config.attack.mode === 'dictionary' && !config.attack.wordlist) {
      toast.error('Please select a wordlist for dictionary attack');
      return;
    }

    if (config.attack.mode === 'brute-force' && !config.attack.mask) {
      toast.error('Please provide a mask for brute-force attack');
      return;
    }

    try {
      const execution = await apiClient.executeSecurityTool('hashcat', config);
      setExecution(execution);
      setActiveTab('output');
      toast.success('Hashcat attack started successfully');
    } catch (error) {
      toast.error(`Failed to start Hashcat: ${error.message}`);
    }
  };

  /**
   * Stop attack
   */
  const stopAttack = async () => {
    if (execution) {
      try {
        await apiClient.stopToolExecution(execution.id);
        toast.success('Hashcat attack stopped');
      } catch (error) {
        toast.error(`Failed to stop attack: ${error.message}`);
      }
    }
  };

  /**
   * Generate command line
   */
  const generateCommand = () => {
    let cmd = 'hashcat';
    
    // Attack mode
    cmd += ` -a ${ATTACK_MODES[config.attack.mode].id}`;
    
    // Hash type
    cmd += ` -m ${config.target.hashType}`;
    
    // Target hash
    if (config.target.hashFile) {
      cmd += ` "${config.target.hashFile}"`;
    } else if (config.target.hashString) {
      // Would need to echo hash to file first
      cmd += ` hash.txt`;
    }
    
    // Attack specific options
    if (config.attack.mode === 'dictionary') {
      const wordlistPath = config.attack.wordlist === 'custom' ? config.attack.wordlist : WORDLISTS[config.attack.wordlist];
      cmd += ` "${wordlistPath}"`;
      
      if (config.rules.enabled && config.rules.ruleFile) {
        cmd += ` -r "${config.rules.ruleFile}"`;
      }
    } else if (config.attack.mode === 'brute-force') {
      cmd += ` ${config.attack.mask}`;
      
      if (config.attack.maskIncrement) {
        cmd += ` --increment --increment-min=${config.attack.incrementMin} --increment-max=${config.attack.incrementMax}`;
      }
    } else if (config.attack.mode === 'combinator') {
      const wordlist1 = config.attack.wordlist === 'custom' ? config.attack.wordlist : WORDLISTS[config.attack.wordlist];
      const wordlist2 = config.attack.wordlist2 === 'custom' ? config.attack.wordlist2 : WORDLISTS[config.attack.wordlist2];
      cmd += ` "${wordlist1}" "${wordlist2}"`;
    } else if (config.attack.mode === 'hybrid') {
      const wordlistPath = config.attack.wordlist === 'custom' ? config.attack.wordlist : WORDLISTS[config.attack.wordlist];
      cmd += ` "${wordlistPath}" ${config.attack.mask}`;
    }
    
    // Custom charsets
    if (config.attack.customCharset1) {
      cmd += ` -1 ${config.attack.customCharset1}`;
    }
    if (config.attack.customCharset2) {
      cmd += ` -2 ${config.attack.customCharset2}`;
    }
    if (config.attack.customCharset3) {
      cmd += ` -3 ${config.attack.customCharset3}`;
    }
    if (config.attack.customCharset4) {
      cmd += ` -4 ${config.attack.customCharset4}`;
    }
    
    // Performance options
    cmd += ` -w ${config.performance.workloadProfile}`;
    
    if (config.performance.optimized) {
      cmd += ' -O';
    }
    
    if (config.performance.segmentSize !== 33) {
      cmd += ` -c ${config.performance.segmentSize}`;
    }
    
    if (config.performance.deviceTypes.cpu && !config.performance.deviceTypes.gpu) {
      cmd += ' -D 1';
    } else if (!config.performance.deviceTypes.cpu && config.performance.deviceTypes.gpu) {
      cmd += ' -D 2';
    }
    
    if (config.performance.opencl.devices) {
      cmd += ` -d ${config.performance.opencl.devices}`;
    }
    
    // Output options
    if (config.output.outputFile) {
      cmd += ` -o "${config.output.outputFile}"`;
    }
    
    if (config.output.outputFormat !== 3) {
      cmd += ` --outfile-format=${config.output.outputFormat}`;
    }
    
    if (config.output.quiet) {
      cmd += ' --quiet';
    }
    
    if (config.output.statusTimer !== 60) {
      cmd += ` --status-timer=${config.output.statusTimer}`;
    }
    
    if (config.output.machineReadable) {
      cmd += ' --machine-readable';
    }
    
    if (config.output.potfileDisable) {
      cmd += ' --potfile-disable';
    }
    
    // Limits
    if (config.limits.skip > 0) {
      cmd += ` -s ${config.limits.skip}`;
    }
    
    if (config.limits.limit > 0) {
      cmd += ` -l ${config.limits.limit}`;
    }
    
    if (config.limits.runtimeLimit > 0) {
      cmd += ` --runtime=${config.limits.runtimeLimit}`;
    }
    
    if (config.limits.sessionName) {
      cmd += ` --session="${config.limits.sessionName}"`;
    }
    
    if (config.limits.restore) {
      cmd += ' --restore';
    }
    
    // Markov
    if (config.markov.enabled) {
      cmd += ' --markov-disable=false';
      
      if (config.markov.threshold > 0) {
        cmd += ` --markov-threshold=${config.markov.threshold}`;
      }
    }
    
    // Misc options
    if (config.misc.hexSalt) {
      cmd += ' --hex-salt';
    }
    
    if (config.misc.hexWordlist) {
      cmd += ' --hex-wordlist';
    }
    
    if (config.misc.hexCharset) {
      cmd += ' --hex-charset';
    }
    
    if (config.misc.force) {
      cmd += ' --force';
    }
    
    if (config.misc.keepGuessing) {
      cmd += ' --keep-guessing';
    }
    
    if (config.misc.loopback) {
      cmd += ' --loopback';
    }
    
    // Username
    if (config.target.username) {
      cmd += ' --username';
    }
    
    // Custom arguments
    if (config.customArgs) {
      cmd += ` ${config.customArgs}`;
    }
    
    return cmd;
  };

  const isAttacking = execution?.status === 'running';
  const canStart = connectionStatus === 'connected' && 
    (config.target.hashFile.trim() || config.target.hashString.trim()) && 
    config.target.hashType !== undefined && !isAttacking;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center gap-3">
        <div className="flex items-center justify-center w-10 h-10 rounded-lg bg-gradient-to-br from-orange-500 to-red-600">
          <Key className="h-5 w-5 text-white" />
        </div>
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100">Hashcat</h2>
          <p className="text-gray-600 dark:text-gray-400">World's fastest and most advanced password recovery utility</p>
        </div>
      </div>

      {/* GPU Notice */}
      <Card className="border-blue-200 bg-blue-50 dark:border-blue-800 dark:bg-blue-900/10">
        <CardContent className="pt-6">
          <div className="flex items-start gap-3">
            <Cpu className="h-5 w-5 text-blue-600 mt-0.5" />
            <div>
              <p className="text-sm font-medium text-blue-800 dark:text-blue-200">
                GPU Acceleration Available
              </p>
              <p className="text-sm text-blue-700 dark:text-blue-300 mt-1">
                Hashcat supports GPU acceleration for dramatically faster cracking speeds. 
                Ensure your GPU drivers and OpenCL/CUDA runtime are properly installed for best performance.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Status Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Status</p>
                <p className="text-2xl font-bold">
                  {isAttacking ? 'Cracking' : 'Ready'}
                </p>
              </div>
              <Activity className={cn("h-8 w-8", isAttacking ? "text-green-600" : "text-gray-400")} />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Attack Mode</p>
                <p className="text-2xl font-bold capitalize">{config.attack.mode}</p>
              </div>
              <Crosshair className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Workload</p>
                <p className="text-2xl font-bold">{WORKLOAD_PROFILES[config.performance.workloadProfile].name}</p>
              </div>
              <Gauge className="h-8 w-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Progress</p>
                <p className="text-2xl font-bold">{execution ? `${execution.progress}%` : '0%'}</p>
              </div>
              <BarChart3 className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Interface */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Unlock className="h-5 w-5" />
                Hashcat Configuration
              </CardTitle>
              <CardDescription>
                Configure password recovery parameters and attack options
              </CardDescription>
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={showHashInfo}
                disabled={showInfo}
              >
                <Info className="h-4 w-4 mr-2" />
                Hash Info
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={runBenchmark}
                disabled={benchmarkMode}
              >
                <Gauge className="h-4 w-4 mr-2" />
                Benchmark
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => navigator.clipboard.writeText(generateCommand())}
              >
                <Copy className="h-4 w-4 mr-2" />
                Copy Command
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setConfig(prev => ({ 
                  ...prev, 
                  target: { ...prev.target, hashFile: '', hashString: '' },
                  attack: { ...prev.attack, mask: '', wordlist: 'rockyou' },
                  customArgs: ''
                }))}
              >
                <RotateCcw className="h-4 w-4 mr-2" />
                Reset
              </Button>
              {isAttacking ? (
                <Button variant="destructive" size="sm" onClick={stopAttack}>
                  <Square className="h-4 w-4 mr-2" />
                  Stop Attack
                </Button>
              ) : (
                <Button 
                  size="sm" 
                  onClick={startAttack}
                  disabled={!canStart}
                >
                  <Play className="h-4 w-4 mr-2" />
                  Start Attack
                </Button>
              )}
            </div>
          </div>
        </CardHeader>

        <CardContent>
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="configure">Configure</TabsTrigger>
              <TabsTrigger value="output">Output</TabsTrigger>
              <TabsTrigger value="results">Results</TabsTrigger>
              <TabsTrigger value="command">Command</TabsTrigger>
            </TabsList>

            <TabsContent value="configure" className="space-y-6">
              {/* Quick Presets */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Quick Presets</h3>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                  <Button variant="outline" onClick={() => applyPreset('quick_crack')}>
                    <Zap className="h-4 w-4 mr-2" />
                    Quick Crack
                  </Button>
                  <Button variant="outline" onClick={() => applyPreset('thorough_dictionary')}>
                    <Book className="h-4 w-4 mr-2" />
                    Thorough Dict
                  </Button>
                  <Button variant="outline" onClick={() => applyPreset('brute_force_numeric')}>
                    <Binary className="h-4 w-4 mr-2" />
                    Numeric BF
                  </Button>
                  <Button variant="outline" onClick={() => applyPreset('brute_force_alpha')}>
                    <Code className="h-4 w-4 mr-2" />
                    Alpha BF
                  </Button>
                  <Button variant="outline" onClick={() => applyPreset('hybrid_attack')}>
                    <Layers className="h-4 w-4 mr-2" />
                    Hybrid Attack
                  </Button>
                  <Button variant="outline" onClick={() => applyPreset('combinator_attack')}>
                    <GitBranch className="h-4 w-4 mr-2" />
                    Combinator
                  </Button>
                  <Button variant="outline" onClick={() => applyPreset('gpu_optimized')}>
                    <Cpu className="h-4 w-4 mr-2" />
                    GPU Optimized
                  </Button>
                  <Button variant="outline" onClick={() => applyPreset('cpu_only')}>
                    <Server className="h-4 w-4 mr-2" />
                    CPU Only
                  </Button>
                </div>
              </div>

              {/* Target Configuration */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Target Configuration</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Hash File</label>
                    <Input
                      value={config.target.hashFile}
                      onChange={(e) => updateConfig('target.hashFile', e.target.value)}
                      placeholder="/path/to/hashes.txt"
                    />
                  </div>

                  <div className="space-y-2">
                    <label className="text-sm font-medium">Hash Type</label>
                    <Select value={config.target.hashType.toString()} onValueChange={(value) => updateConfig('target.hashType', parseInt(value))}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {HASH_CATEGORIES.map(category => (
                          <React.Fragment key={category}>
                            <div className="px-2 py-1.5 text-sm font-semibold text-gray-500">{category}</div>
                            {Object.entries(HASH_TYPES)
                              .filter(([_, hash]) => hash.category === category)
                              .map(([key, hash]) => (
                                <SelectItem key={key} value={hash.id.toString()}>
                                  <div className="flex items-center justify-between w-full">
                                    <span>{hash.name}</span>
                                    <Badge variant="outline" className="ml-2 text-xs">{hash.id}</Badge>
                                  </div>
                                </SelectItem>
                              ))}
                          </React.Fragment>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">Hash String (Alternative to file)</label>
                  <Textarea
                    value={config.target.hashString}
                    onChange={(e) => updateConfig('target.hashString', e.target.value)}
                    placeholder="5f4dcc3b5aa765d61d8327deb882cf99&#10;e10adc3949ba59abbe56e057f20f883e"
                    className="min-h-[80px] font-mono"
                  />
                </div>

                <div className="flex items-center space-x-4">
                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="username"
                      checked={config.target.username}
                      onChange={(e) => updateConfig('target.username', e.target.checked)}
                      className="rounded"
                    />
                    <label htmlFor="username" className="text-sm font-medium">Hash includes username</label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <label className="text-sm font-medium">Delimiter:</label>
                    <Input
                      value={config.target.delimiter}
                      onChange={(e) => updateConfig('target.delimiter', e.target.value)}
                      className="w-20"
                    />
                  </div>
                </div>
              </div>

              {/* Attack Mode Configuration */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Attack Mode</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  {Object.entries(ATTACK_MODES).map(([mode, info]) => (
                    <Card 
                      key={mode}
                      className={cn(
                        "cursor-pointer transition-colors",
                        config.attack.mode === mode ? "border-orange-500 bg-orange-50 dark:bg-orange-950" : ""
                      )}
                      onClick={() => updateConfig('attack.mode', mode)}
                    >
                      <CardContent className="pt-6">
                        <div className="space-y-1">
                          <div className="font-medium">{info.name}</div>
                          <div className="text-sm text-gray-500">{info.description}</div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>

              {/* Attack-specific options */}
              {config.attack.mode === 'dictionary' && (
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold">Dictionary Attack Options</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <label className="text-sm font-medium">Wordlist</label>
                      <Select value={config.attack.wordlist} onValueChange={(value) => updateConfig('attack.wordlist', value)}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="rockyou">RockYou (14M passwords)</SelectItem>
                          <SelectItem value="fasttrack">FastTrack</SelectItem>
                          <SelectItem value="seclist_top1000">SecLists Top 1000</SelectItem>
                          <SelectItem value="seclist_top10000">SecLists Top 10000</SelectItem>
                          <SelectItem value="seclist_probable">Probable Passwords</SelectItem>
                          <SelectItem value="john_password">John Password List</SelectItem>
                          <SelectItem value="custom">Custom Wordlist</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    {config.attack.wordlist === 'custom' && (
                      <div className="space-y-2">
                        <label className="text-sm font-medium">Custom Wordlist Path</label>
                        <Input
                          value={WORDLISTS.custom}
                          onChange={(e) => WORDLISTS.custom = e.target.value}
                          placeholder="/path/to/wordlist.txt"
                        />
                      </div>
                    )}
                  </div>

                  <div className="space-y-4">
                    <div className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        id="enableRules"
                        checked={config.rules.enabled}
                        onChange={(e) => updateConfig('rules.enabled', e.target.checked)}
                        className="rounded"
                      />
                      <label htmlFor="enableRules" className="text-sm font-medium">Enable Rule-based Mutations</label>
                    </div>

                    {config.rules.enabled && (
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <label className="text-sm font-medium">Rule File</label>
                          <Select value={config.rules.ruleFile} onValueChange={(value) => updateConfig('rules.ruleFile', value)}>
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="best64.rule">Best64 (Best 64 rules)</SelectItem>
                              <SelectItem value="dive.rule">Dive (Comprehensive)</SelectItem>
                              <SelectItem value="generated.rule">Generated</SelectItem>
                              <SelectItem value="generated2.rule">Generated2</SelectItem>
                              <SelectItem value="hybrid/append_d.rule">Append Digits</SelectItem>
                              <SelectItem value="hybrid/prepend_d.rule">Prepend Digits</SelectItem>
                              <SelectItem value="InsidePro-PasswordsPro.rule">InsidePro</SelectItem>
                              <SelectItem value="T0XlC.rule">T0XlC</SelectItem>
                              <SelectItem value="rockyou-30000.rule">RockYou 30000</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>

                        <div className="space-y-2">
                          <label className="text-sm font-medium">Generate Rules</label>
                          <Input
                            type="number"
                            value={config.rules.generateRules}
                            onChange={(e) => updateConfig('rules.generateRules', parseInt(e.target.value) || 0)}
                            min="0"
                            placeholder="0 = disabled"
                          />
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              )}

              {config.attack.mode === 'brute-force' && (
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold">Brute-Force Options</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <label className="text-sm font-medium">Mask</label>
                      <Select value={config.attack.mask} onValueChange={(value) => updateConfig('attack.mask', value)}>
                        <SelectTrigger>
                          <SelectValue placeholder="Select a mask" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="custom">Custom Mask</SelectItem>
                          <SelectItem value="?d?d?d?d">4 Digits (PIN)</SelectItem>
                          <SelectItem value="?d?d?d?d?d?d">6 Digits</SelectItem>
                          <SelectItem value="?d?d?d?d?d?d?d?d">8 Digits</SelectItem>
                          <SelectItem value="?l?l?l?l?l?l">6 Lowercase</SelectItem>
                          <SelectItem value="?l?l?l?l?l?l?l?l">8 Lowercase</SelectItem>
                          <SelectItem value="?u?u?u?u?u?u">6 Uppercase</SelectItem>
                          <SelectItem value="?a?a?a?a?a?a?a?a">8 Mixed Alpha</SelectItem>
                          <SelectItem value="?1?1?1?1?1?1">6 Alphanumeric</SelectItem>
                          <SelectItem value="?u?l?l?l?l?d?d?s">Complex Pattern</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    {config.attack.mask === 'custom' && (
                      <div className="space-y-2">
                        <label className="text-sm font-medium">Custom Mask</label>
                        <Input
                          value={COMMON_MASKS.custom}
                          onChange={(e) => { COMMON_MASKS.custom = e.target.value; updateConfig('attack.mask', e.target.value); }}
                          placeholder="?u?l?l?l?d?d?d?s"
                        />
                      </div>
                    )}
                  </div>

                  <div className="space-y-2">
                    <p className="text-sm font-medium mb-2">Mask Characters Reference</p>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-2 text-sm">
                      <div><code>?l</code> = lowercase (a-z)</div>
                      <div><code>?u</code> = uppercase (A-Z)</div>
                      <div><code>?d</code> = digit (0-9)</div>
                      <div><code>?s</code> = special chars</div>
                      <div><code>?a</code> = all printable</div>
                      <div><code>?b</code> = 0x00 - 0xff</div>
                      <div><code>?1</code> = custom charset 1</div>
                      <div><code>?2</code> = custom charset 2</div>
                    </div>
                  </div>

                  <div className="space-y-4">
                    <div className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        id="maskIncrement"
                        checked={config.attack.maskIncrement}
                        onChange={(e) => updateConfig('attack.maskIncrement', e.target.checked)}
                        className="rounded"
                      />
                      <label htmlFor="maskIncrement" className="text-sm font-medium">Enable Mask Increment</label>
                    </div>

                    {config.attack.maskIncrement && (
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <label className="text-sm font-medium">Minimum Length</label>
                          <Input
                            type="number"
                            value={config.attack.incrementMin}
                            onChange={(e) => updateConfig('attack.incrementMin', parseInt(e.target.value) || 1)}
                            min="1"
                            max="15"
                          />
                        </div>

                        <div className="space-y-2">
                          <label className="text-sm font-medium">Maximum Length</label>
                          <Input
                            type="number"
                            value={config.attack.incrementMax}
                            onChange={(e) => updateConfig('attack.incrementMax', parseInt(e.target.value) || 8)}
                            min="1"
                            max="15"
                          />
                        </div>
                      </div>
                    )}
                  </div>

                  <div className="space-y-4">
                    <h4 className="font-medium">Custom Character Sets</h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <label className="text-sm font-medium">Custom Charset 1 (?1)</label>
                        <Input
                          value={config.attack.customCharset1}
                          onChange={(e) => updateConfig('attack.customCharset1', e.target.value)}
                          placeholder="?l?d"
                        />
                      </div>

                      <div className="space-y-2">
                        <label className="text-sm font-medium">Custom Charset 2 (?2)</label>
                        <Input
                          value={config.attack.customCharset2}
                          onChange={(e) => updateConfig('attack.customCharset2', e.target.value)}
                          placeholder="?l?u?d"
                        />
                      </div>

                      <div className="space-y-2">
                        <label className="text-sm font-medium">Custom Charset 3 (?3)</label>
                        <Input
                          value={config.attack.customCharset3}
                          onChange={(e) => updateConfig('attack.customCharset3', e.target.value)}
                          placeholder="?d?s"
                        />
                      </div>

                      <div className="space-y-2">
                        <label className="text-sm font-medium">Custom Charset 4 (?4)</label>
                        <Input
                          value={config.attack.customCharset4}
                          onChange={(e) => updateConfig('attack.customCharset4', e.target.value)}
                          placeholder="0123456789"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {config.attack.mode === 'combinator' && (
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold">Combinator Attack Options</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <label className="text-sm font-medium">First Wordlist</label>
                      <Select value={config.attack.wordlist} onValueChange={(value) => updateConfig('attack.wordlist', value)}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="rockyou">RockYou</SelectItem>
                          <SelectItem value="fasttrack">FastTrack</SelectItem>
                          <SelectItem value="seclist_top1000">SecLists Top 1000</SelectItem>
                          <SelectItem value="john_password">John Password List</SelectItem>
                          <SelectItem value="custom">Custom Wordlist</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <label className="text-sm font-medium">Second Wordlist</label>
                      <Select value={config.attack.wordlist2} onValueChange={(value) => updateConfig('attack.wordlist2', value)}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="rockyou">RockYou</SelectItem>
                          <SelectItem value="fasttrack">FastTrack</SelectItem>
                          <SelectItem value="seclist_top1000">SecLists Top 1000</SelectItem>
                          <SelectItem value="john_password">John Password List</SelectItem>
                          <SelectItem value="custom">Custom Wordlist</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </div>
              )}

              {config.attack.mode === 'hybrid' && (
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold">Hybrid Attack Options</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <label className="text-sm font-medium">Wordlist</label>
                      <Select value={config.attack.wordlist} onValueChange={(value) => updateConfig('attack.wordlist', value)}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="rockyou">RockYou</SelectItem>
                          <SelectItem value="fasttrack">FastTrack</SelectItem>
                          <SelectItem value="seclist_top1000">SecLists Top 1000</SelectItem>
                          <SelectItem value="john_password">John Password List</SelectItem>
                          <SelectItem value="custom">Custom Wordlist</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <label className="text-sm font-medium">Mask to Append</label>
                      <Input
                        value={config.attack.mask}
                        onChange={(e) => updateConfig('attack.mask', e.target.value)}
                        placeholder="?d?d?d?d"
                      />
                    </div>
                  </div>
                </div>
              )}

              {/* Performance Configuration */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Performance Configuration</h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Workload Profile</label>
                    <Select value={config.performance.workloadProfile.toString()} onValueChange={(value) => updateConfig('performance.workloadProfile', parseInt(value))}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {Object.entries(WORKLOAD_PROFILES).map(([level, profile]) => (
                          <SelectItem key={level} value={level}>
                            <div>
                              <div className="font-medium">{profile.name}</div>
                              <div className="text-xs text-gray-500">{profile.description}</div>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <label className="text-sm font-medium">Device Types</label>
                    <div className="flex items-center space-x-4">
                      <div className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          id="cpu"
                          checked={config.performance.deviceTypes.cpu}
                          onChange={(e) => updateConfig('performance.deviceTypes.cpu', e.target.checked)}
                          className="rounded"
                        />
                        <label htmlFor="cpu" className="text-sm">CPU</label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          id="gpu"
                          checked={config.performance.deviceTypes.gpu}
                          onChange={(e) => updateConfig('performance.deviceTypes.gpu', e.target.checked)}
                          className="rounded"
                        />
                        <label htmlFor="gpu" className="text-sm">GPU</label>
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="optimized"
                      checked={config.performance.optimized}
                      onChange={(e) => updateConfig('performance.optimized', e.target.checked)}
                      className="rounded"
                    />
                    <label htmlFor="optimized" className="text-sm font-medium">Optimized Kernel (32 char limit)</label>
                  </div>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="output" className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold">Real-time Output</h3>
                <div className="flex items-center gap-2">
                  {isAttacking && (
                    <Badge variant="secondary" className="animate-pulse">
                      <Activity className="h-3 w-3 mr-1" />
                      Cracking in progress...
                    </Badge>
                  )}
                  <Button variant="outline" size="sm">
                    <Download className="h-4 w-4 mr-2" />
                    Export Output
                  </Button>
                </div>
              </div>
              
              {execution?.progress !== undefined && (
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Progress</span>
                    <span>{execution.progress}%</span>
                  </div>
                  <Progress value={execution.progress} className="w-full" />
                </div>
              )}

              <Card>
                <CardContent className="p-4">
                  <div className="bg-gray-900 dark:bg-gray-800 rounded-lg p-4 min-h-[400px] font-mono text-sm overflow-auto">
                    <div className="text-green-400">
                      {execution?.output || 'Hashcat output will appear here when attack starts...'}
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="results" className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold">Cracked Passwords</h3>
                <div className="flex items-center gap-2">
                  <Button variant="outline" size="sm">
                    <Download className="h-4 w-4 mr-2" />
                    Export Results
                  </Button>
                  <Button variant="outline" size="sm">
                    <Filter className="h-4 w-4 mr-2" />
                    Filter Results
                  </Button>
                </div>
              </div>

              {execution?.results ? (
                <div className="space-y-4">
                  {/* Results Summary */}
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <Card>
                      <CardContent className="pt-6">
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Total Hashes</p>
                            <p className="text-2xl font-bold">{execution.results.total_hashes || 0}</p>
                          </div>
                          <Hash className="h-8 w-8 text-blue-600" />
                        </div>
                      </CardContent>
                    </Card>

                    <Card>
                      <CardContent className="pt-6">
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Cracked</p>
                            <p className="text-2xl font-bold text-green-600">{execution.results.cracked || 0}</p>
                          </div>
                          <Unlock className="h-8 w-8 text-green-600" />
                        </div>
                      </CardContent>
                    </Card>

                    <Card>
                      <CardContent className="pt-6">
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Speed</p>
                            <p className="text-2xl font-bold">{execution.results.speed || '0 H/s'}</p>
                          </div>
                          <Gauge className="h-8 w-8 text-purple-600" />
                        </div>
                      </CardContent>
                    </Card>

                    <Card>
                      <CardContent className="pt-6">
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Time</p>
                            <p className="text-2xl font-bold">{execution.results.time || '00:00'}</p>
                          </div>
                          <Clock className="h-8 w-8 text-orange-600" />
                        </div>
                      </CardContent>
                    </Card>
                  </div>

                  {/* Cracked Passwords Table */}
                  <Card>
                    <CardContent className="p-0">
                      <div className="overflow-x-auto">
                        <table className="w-full">
                          <thead className="border-b">
                            <tr className="text-left">
                              <th className="p-4 font-medium">Hash</th>
                              <th className="p-4 font-medium">Password</th>
                              <th className="p-4 font-medium">Username</th>
                              <th className="p-4 font-medium">Time</th>
                            </tr>
                          </thead>
                          <tbody>
                            {execution.results.cracked_passwords?.map((result, index) => (
                              <tr key={index} className="border-b hover:bg-gray-50 dark:hover:bg-gray-800">
                                <td className="p-4 font-mono text-sm max-w-xs truncate">{result.hash}</td>
                                <td className="p-4 font-mono">
                                  <Badge variant="default" className="font-mono">
                                    {result.password}
                                  </Badge>
                                </td>
                                <td className="p-4">{result.username || 'N/A'}</td>
                                <td className="p-4 text-sm">{result.crack_time}</td>
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              ) : (
                <Card>
                  <CardContent className="p-8 text-center">
                    <Key className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">No Results Yet</h3>
                    <p className="text-gray-600 dark:text-gray-400">
                      Start a password cracking attack to see results here. Cracked passwords will be displayed as they are found.
                    </p>
                  </CardContent>
                </Card>
              )}
            </TabsContent>

            <TabsContent value="command" className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold">Generated Command</h3>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => navigator.clipboard.writeText(generateCommand())}
                >
                  <Copy className="h-4 w-4 mr-2" />
                  Copy Command
                </Button>
              </div>

              <Card>
                <CardContent className="p-4">
                  <div className="bg-gray-900 dark:bg-gray-800 rounded-lg p-4 overflow-x-auto">
                    <code className="text-green-400 whitespace-pre-wrap break-all">
                      {generateCommand()}
                    </code>
                  </div>
                </CardContent>
              </Card>

              <div className="space-y-4">
                <h4 className="font-semibold">Command Explanation</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                  <div>
                    <h5 className="font-medium mb-2">Attack Options</h5>
                    <ul className="space-y-1 text-gray-600 dark:text-gray-400">
                      <li>• <code>-a</code>: Attack mode (0-9)</li>
                      <li>• <code>-m</code>: Hash type</li>
                      <li>• <code>-r</code>: Rules file</li>
                      <li>• <code>--increment</code>: Increment mask</li>
                    </ul>
                  </div>
                  <div>
                    <h5 className="font-medium mb-2">Performance</h5>
                    <ul className="space-y-1 text-gray-600 dark:text-gray-400">
                      <li>• <code>-w</code>: Workload profile</li>
                      <li>• <code>-O</code>: Optimized kernel</li>
                      <li>• <code>-D</code>: Device types</li>
                      <li>• <code>-d</code>: OpenCL devices</li>
                    </ul>
                  </div>
                  <div>
                    <h5 className="font-medium mb-2">Mask Characters</h5>
                    <ul className="space-y-1 text-gray-600 dark:text-gray-400">
                      <li>• <code>?l</code>: Lowercase a-z</li>
                      <li>• <code>?u</code>: Uppercase A-Z</li>
                      <li>• <code>?d</code>: Digits 0-9</li>
                      <li>• <code>?s</code>: Special characters</li>
                    </ul>
                  </div>
                  <div>
                    <h5 className="font-medium mb-2">Output & Control</h5>
                    <ul className="space-y-1 text-gray-600 dark:text-gray-400">
                      <li>• <code>-o</code>: Output file</li>
                      <li>• <code>--session</code>: Session name</li>
                      <li>• <code>--restore</code>: Resume session</li>
                      <li>• <code>--status-timer</code>: Status update interval</li>
                    </ul>
                  </div>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}