#!/usr/bin/env python3
"""
🔍 COMPREHENSIVE FRONTEND ANALYSIS
Deep architectural analysis of NexusScan frontend system
Analyzes 125 TypeScript/React files with systematic approach
"""

import os
import re
import json
from pathlib import Path
from typing import Dict, List, Any
from datetime import datetime

class FrontendAnalyzer:
    def __init__(self, frontend_path: str = "/mnt/e/dev/nexusscan-desktop/frontend/src"):
        self.frontend_path = Path(frontend_path)
        self.analysis_results = {
            "architecture_analysis": {},
            "integration_analysis": {},
            "code_quality_analysis": {},
            "security_analysis": {},
            "performance_analysis": {},
            "ui_ux_analysis": {},
            "critical_issues": [],
            "recommendations": []
        }
    
    def analyze_directory_structure(self) -> Dict[str, Any]:
        """Analyze complete frontend directory structure"""
        print("📁 Analyzing Frontend Directory Structure...")
        
        structure = {}
        file_count = 0
        total_lines = 0
        
        for root, dirs, files in os.walk(self.frontend_path):
            rel_path = os.path.relpath(root, self.frontend_path)
            if rel_path == ".":
                rel_path = "src"
            
            structure[rel_path] = {
                "directories": len(dirs),
                "files": [],
                "file_types": {}
            }
            
            for file in files:
                if file.endswith(('.tsx', '.ts', '.jsx', '.js', '.json', '.css')):
                    file_path = os.path.join(root, file)
                    file_info = self._analyze_file(file_path)
                    structure[rel_path]["files"].append(file_info)
                    
                    ext = file.split('.')[-1]
                    structure[rel_path]["file_types"][ext] = structure[rel_path]["file_types"].get(ext, 0) + 1
                    
                    file_count += 1
                    total_lines += file_info.get("lines", 0)
        
        return {
            "structure": structure,
            "total_files": file_count,
            "total_lines": total_lines,
            "average_file_size": total_lines / file_count if file_count > 0 else 0
        }
    
    def _analyze_file(self, file_path: str) -> Dict[str, Any]:
        """Analyze individual file"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                lines = content.split('\n')
                
            return {
                "name": os.path.basename(file_path),
                "size": len(content),
                "lines": len(lines),
                "type": file_path.split('.')[-1],
                "imports": self._extract_imports(content),
                "exports": self._extract_exports(content),
                "components": self._extract_components(content),
                "hooks": self._extract_hooks(content),
                "apis": self._extract_api_calls(content),
                "state_usage": self._extract_state_usage(content),
                "event_handlers": self._extract_event_handlers(content),
                "complexity_score": self._calculate_complexity(content)
            }
        except Exception as e:
            return {"name": os.path.basename(file_path), "error": str(e)}
    
    def _extract_imports(self, content: str) -> List[str]:
        """Extract import statements"""
        import_pattern = r'import\s+.*?from\s+[\'"]([^\'"]+)[\'"]'
        return re.findall(import_pattern, content)
    
    def _extract_exports(self, content: str) -> List[str]:
        """Extract export statements"""
        export_patterns = [
            r'export\s+(?:default\s+)?(?:const|function|class)\s+(\w+)',
            r'export\s+\{\s*([^}]+)\s*\}',
            r'export\s+default\s+(\w+)'
        ]
        exports = []
        for pattern in export_patterns:
            exports.extend(re.findall(pattern, content))
        return exports
    
    def _extract_components(self, content: str) -> List[str]:
        """Extract React components"""
        component_patterns = [
            r'(?:function|const)\s+([A-Z][a-zA-Z0-9]*)\s*(?:\(.*?\))?\s*(?::\s*.*?)?\s*=>?\s*{',
            r'class\s+([A-Z][a-zA-Z0-9]*)\s+extends\s+.*?Component'
        ]
        components = []
        for pattern in component_patterns:
            components.extend(re.findall(pattern, content))
        return components
    
    def _extract_hooks(self, content: str) -> List[str]:
        """Extract React hooks usage"""
        hook_pattern = r'\b(use[A-Z][a-zA-Z0-9]*)\s*\('
        return list(set(re.findall(hook_pattern, content)))
    
    def _extract_api_calls(self, content: str) -> List[str]:
        """Extract API calls and backend integrations"""
        api_patterns = [
            r'fetch\s*\(\s*[\'"]([^\'"]+)[\'"]',
            r'axios\.\w+\s*\(\s*[\'"]([^\'"]+)[\'"]',
            r'api(?:Client)?\.\w+\s*\(',
            r'/api/[a-zA-Z0-9/_-]+',
            r'getAPI\w+\s*\(',
            r'TauriAPI\.\w+\s*\('
        ]
        apis = []
        for pattern in api_patterns:
            apis.extend(re.findall(pattern, content))
        return apis
    
    def _extract_state_usage(self, content: str) -> Dict[str, int]:
        """Extract state management patterns"""
        state_patterns = {
            "useState": len(re.findall(r'useState\s*\(', content)),
            "useEffect": len(re.findall(r'useEffect\s*\(', content)),
            "useStore": len(re.findall(r'useStore\s*\(', content)),
            "zustand": len(re.findall(r'useAppStore\s*\(', content)),
            "context": len(re.findall(r'useContext\s*\(', content)),
            "reducer": len(re.findall(r'useReducer\s*\(', content))
        }
        return state_patterns
    
    def _extract_event_handlers(self, content: str) -> List[str]:
        """Extract event handlers"""
        handler_patterns = [
            r'on\w+\s*=\s*\{([^}]+)\}',
            r'const\s+(handle\w+)\s*=',
            r'function\s+(handle\w+)\s*\(',
            r'onClick\s*=',
            r'onSubmit\s*=',
            r'onChange\s*='
        ]
        handlers = []
        for pattern in handler_patterns:
            handlers.extend(re.findall(pattern, content))
        return handlers
    
    def _calculate_complexity(self, content: str) -> int:
        """Calculate cyclomatic complexity"""
        complexity_keywords = ['if', 'else', 'while', 'for', 'switch', 'case', 'catch', '&&', '||', '?']
        complexity = 1  # Base complexity
        for keyword in complexity_keywords:
            complexity += len(re.findall(rf'\b{keyword}\b', content))
        return complexity
    
    def analyze_advanced_tools_integration(self) -> Dict[str, Any]:
        """Analyze integration with advanced tools endpoints"""
        print("🔗 Analyzing Advanced Tools Integration...")
        
        tools_page_path = self.frontend_path / "pages/tools/index.tsx"
        api_client_path = self.frontend_path / "services/api-client.ts"
        
        integration_analysis = {
            "metasploit_integration": False,
            "execution_integration": False,
            "compliance_integration": False,
            "intelligence_integration": False,
            "mock_data_usage": [],
            "real_api_calls": [],
            "missing_handlers": [],
            "frontend_backend_disconnect": True
        }
        
        # Analyze tools page
        if tools_page_path.exists():
            with open(tools_page_path, 'r') as f:
                tools_content = f.read()
                
            # Check for mock data patterns
            mock_patterns = [
                "Promise.resolve({ success: true, data:",
                "setMetasploitModules([",
                "setComplianceFrameworks(",
                "setIntelligenceData({"
            ]
            
            for pattern in mock_patterns:
                if pattern in tools_content:
                    integration_analysis["mock_data_usage"].append(pattern)
            
            # Check for real API calls
            api_calls = [
                "getMetasploitModules",
                "getExecutionModes", 
                "getComplianceFrameworks",
                "getIntelligenceData"
            ]
            
            for api_call in api_calls:
                if api_call in tools_content:
                    integration_analysis["real_api_calls"].append(api_call)
            
            # Check for event handlers
            handler_patterns = [
                r'onClick\s*=\s*\{([^}]+)\}',
                r'onSubmit\s*=\s*\{([^}]+)\}',
                r'const\s+handle\w+\s*='
            ]
            
            handlers_found = []
            for pattern in handler_patterns:
                handlers_found.extend(re.findall(pattern, tools_content))
            
            # Analyze if buttons have actual functionality
            configure_buttons = len(re.findall(r'<Button[^>]*>Configure</Button>', tools_content))
            execute_buttons = len(re.findall(r'<Button[^>]*>(?:Execute|Run|Start)', tools_content))
            
            integration_analysis["ui_buttons"] = {
                "configure_buttons": configure_buttons,
                "execute_buttons": execute_buttons,
                "functional_handlers": len(handlers_found)
            }
        
        # Analyze API client
        if api_client_path.exists():
            with open(api_client_path, 'r') as f:
                api_content = f.read()
                
            # Check for advanced endpoint methods
            advanced_methods = [
                "getMetasploitModules",
                "getExecutionModes",
                "getComplianceFrameworks", 
                "getIntelligenceData",
                "executeMetasploit",
                "runComplianceTest"
            ]
            
            for method in advanced_methods:
                if method in api_content:
                    integration_analysis["real_api_calls"].append(f"API_CLIENT_{method}")
        
        return integration_analysis
    
    def analyze_component_architecture(self) -> Dict[str, Any]:
        """Analyze component architecture and patterns"""
        print("🏗️ Analyzing Component Architecture...")
        
        components_path = self.frontend_path / "components"
        architecture_analysis = {
            "component_categories": {},
            "design_patterns": {},
            "reusability_score": 0,
            "composition_patterns": [],
            "ui_system_analysis": {}
        }
        
        if components_path.exists():
            for category_dir in components_path.iterdir():
                if category_dir.is_dir():
                    category_name = category_dir.name
                    architecture_analysis["component_categories"][category_name] = {
                        "component_count": 0,
                        "average_complexity": 0,
                        "reusable_components": [],
                        "specialized_components": []
                    }
                    
                    for component_file in category_dir.glob("*.tsx"):
                        file_analysis = self._analyze_file(str(component_file))
                        architecture_analysis["component_categories"][category_name]["component_count"] += 1
                        
                        # Analyze reusability
                        if len(file_analysis.get("exports", [])) > 0:
                            architecture_analysis["component_categories"][category_name]["reusable_components"].append(component_file.name)
        
        return architecture_analysis
    
    def analyze_performance_patterns(self) -> Dict[str, Any]:
        """Analyze performance patterns and optimizations"""
        print("⚡ Analyzing Performance Patterns...")
        
        performance_analysis = {
            "lazy_loading": 0,
            "memoization": 0,
            "virtualization": 0,
            "code_splitting": 0,
            "bundle_optimization": [],
            "performance_hooks": [],
            "inefficient_patterns": []
        }
        
        # Search for performance patterns across all files
        for file_path in self.frontend_path.rglob("*.tsx"):
            try:
                with open(file_path, 'r') as f:
                    content = f.read()
                    
                # Check for performance patterns
                patterns = {
                    "lazy_loading": [r'React\.lazy\(', r'lazy\(', r'Suspense'],
                    "memoization": [r'React\.memo\(', r'useMemo\(', r'useCallback\('],
                    "virtualization": [r'react-window', r'react-virtualized', r'VirtualizedList'],
                    "code_splitting": [r'import\(', r'dynamic\(']
                }
                
                for pattern_type, pattern_list in patterns.items():
                    for pattern in pattern_list:
                        matches = len(re.findall(pattern, content))
                        performance_analysis[pattern_type] += matches
                        
            except Exception as e:
                continue
        
        return performance_analysis
    
    def analyze_security_patterns(self) -> Dict[str, Any]:
        """Analyze security patterns and vulnerabilities"""
        print("🔒 Analyzing Security Patterns...")
        
        security_analysis = {
            "input_sanitization": 0,
            "xss_protection": 0,
            "csrf_protection": 0,
            "authentication_patterns": [],
            "authorization_patterns": [],
            "data_validation": 0,
            "potential_vulnerabilities": [],
            "security_headers": []
        }
        
        # Search for security patterns
        security_patterns = {
            "input_sanitization": [r'sanitize', r'escape', r'DOMPurify'],
            "xss_protection": [r'dangerouslySetInnerHTML', r'innerHTML'],
            "authentication": [r'authenticate', r'login', r'token', r'session'],
            "authorization": [r'hasPermission', r'hasRole', r'authorize'],
            "validation": [r'validate', r'schema', r'yup', r'zod']
        }
        
        for file_path in self.frontend_path.rglob("*.tsx"):
            try:
                with open(file_path, 'r') as f:
                    content = f.read()
                    
                for pattern_type, pattern_list in security_patterns.items():
                    for pattern in pattern_list:
                        matches = len(re.findall(pattern, content, re.IGNORECASE))
                        if pattern_type in security_analysis:
                            security_analysis[pattern_type] += matches
                        elif pattern_type == "authentication":
                            security_analysis["authentication_patterns"].extend(re.findall(pattern, content, re.IGNORECASE))
                        elif pattern_type == "authorization":
                            security_analysis["authorization_patterns"].extend(re.findall(pattern, content, re.IGNORECASE))
                            
            except Exception as e:
                continue
        
        return security_analysis
    
    def identify_critical_issues(self) -> List[Dict[str, Any]]:
        """Identify critical architectural and integration issues"""
        print("🚨 Identifying Critical Issues...")
        
        critical_issues = []
        
        # Check tools page for integration issues
        tools_page = self.frontend_path / "pages/tools/index.tsx"
        if tools_page.exists():
            with open(tools_page, 'r') as f:
                content = f.read()
                
            # Issue 1: Mock data usage in production
            if "Promise.resolve({ success: true, data:" in content:
                critical_issues.append({
                    "severity": "CRITICAL",
                    "category": "Backend Integration",
                    "issue": "Advanced tools using mock data instead of real APIs",
                    "description": "The advanced tools tabs are using hardcoded mock data instead of calling the implemented backend endpoints",
                    "impact": "Users see sophisticated UI but no actual functionality",
                    "file": "pages/tools/index.tsx",
                    "lines": self._find_line_numbers(content, "Promise.resolve")
                })
            
            # Issue 2: Non-functional buttons
            configure_buttons = len(re.findall(r'<Button[^>]*>Configure</Button>', content))
            onclick_handlers = len(re.findall(r'onClick\s*=\s*\{[^}]+\}', content))
            
            if configure_buttons > onclick_handlers:
                critical_issues.append({
                    "severity": "HIGH",
                    "category": "User Experience",
                    "issue": "Non-functional CTA buttons",
                    "description": f"Found {configure_buttons} Configure buttons but only {onclick_handlers} click handlers",
                    "impact": "Users can click buttons but nothing happens",
                    "file": "pages/tools/index.tsx",
                    "recommendation": "Add onClick handlers to all interactive buttons"
                })
        
        # Check API client for missing methods
        api_client = self.frontend_path / "services/api-client.ts"
        if api_client.exists():
            with open(api_client, 'r') as f:
                content = f.read()
                
            required_methods = [
                "getMetasploitModules",
                "getExecutionModes", 
                "getComplianceFrameworks",
                "getIntelligenceData"
            ]
            
            missing_methods = []
            for method in required_methods:
                if method not in content:
                    missing_methods.append(method)
            
            if missing_methods:
                critical_issues.append({
                    "severity": "CRITICAL",
                    "category": "API Integration",
                    "issue": "Missing API methods for advanced tools",
                    "description": f"API client missing methods: {', '.join(missing_methods)}",
                    "impact": "Frontend cannot communicate with new backend endpoints",
                    "file": "services/api-client.ts",
                    "recommendation": "Add missing API methods to integrate with backend"
                })
        
        return critical_issues
    
    def _find_line_numbers(self, content: str, search_term: str) -> List[int]:
        """Find line numbers where a term appears"""
        lines = content.split('\n')
        line_numbers = []
        for i, line in enumerate(lines, 1):
            if search_term in line:
                line_numbers.append(i)
        return line_numbers
    
    def generate_recommendations(self) -> List[Dict[str, Any]]:
        """Generate architectural and improvement recommendations"""
        print("💡 Generating Recommendations...")
        
        recommendations = [
            {
                "priority": "IMMEDIATE",
                "category": "Backend Integration",
                "title": "Connect Advanced Tools to Real APIs",
                "description": "Replace all mock data in advanced tools tabs with real API calls to the implemented backend endpoints",
                "implementation": [
                    "Add missing API methods to api-client.ts",
                    "Update tools page to call real APIs instead of mock data",
                    "Remove hardcoded statistics and use dynamic data",
                    "Add proper error handling for API failures"
                ],
                "estimated_effort": "4-8 hours",
                "business_impact": "HIGH - Platform functionality matches UI promises"
            },
            {
                "priority": "IMMEDIATE", 
                "category": "User Experience",
                "title": "Implement Functional CTA Buttons",
                "description": "Add onClick handlers and functionality to all Configure, Execute, and action buttons",
                "implementation": [
                    "Create handler functions for tool configuration",
                    "Implement execution workflow for each tool",
                    "Add confirmation dialogs for destructive actions",
                    "Provide user feedback during operations"
                ],
                "estimated_effort": "6-12 hours",
                "business_impact": "HIGH - Users can actually use the tools"
            },
            {
                "priority": "HIGH",
                "category": "Architecture",
                "title": "Enhance Error Handling",
                "description": "Implement comprehensive error handling throughout the application",
                "implementation": [
                    "Add try/catch blocks around all API calls",
                    "Implement user-friendly error messages",
                    "Add retry mechanisms for failed operations", 
                    "Create fallback UI states"
                ],
                "estimated_effort": "8-16 hours",
                "business_impact": "MEDIUM - Better user experience and debugging"
            },
            {
                "priority": "MEDIUM",
                "category": "Performance",
                "title": "Optimize Bundle Size and Loading",
                "description": "Implement code splitting and lazy loading for better performance",
                "implementation": [
                    "Add React.lazy for heavy components",
                    "Implement route-based code splitting",
                    "Optimize bundle analysis",
                    "Add loading states and suspense boundaries"
                ],
                "estimated_effort": "12-20 hours",
                "business_impact": "MEDIUM - Faster application loading"
            }
        ]
        
        return recommendations
    
    def run_comprehensive_analysis(self) -> Dict[str, Any]:
        """Run complete frontend analysis"""
        print("🎯 STARTING COMPREHENSIVE FRONTEND ANALYSIS")
        print("=" * 80)
        
        # Run all analysis components
        self.analysis_results["architecture_analysis"] = self.analyze_directory_structure()
        self.analysis_results["integration_analysis"] = self.analyze_advanced_tools_integration()
        self.analysis_results["component_analysis"] = self.analyze_component_architecture()
        self.analysis_results["performance_analysis"] = self.analyze_performance_patterns()
        self.analysis_results["security_analysis"] = self.analyze_security_patterns()
        self.analysis_results["critical_issues"] = self.identify_critical_issues()
        self.analysis_results["recommendations"] = self.generate_recommendations()
        
        # Calculate overall scores
        self.analysis_results["summary"] = self._calculate_summary_scores()
        
        return self.analysis_results
    
    def _calculate_summary_scores(self) -> Dict[str, Any]:
        """Calculate overall architecture scores"""
        integration = self.analysis_results["integration_analysis"]
        performance = self.analysis_results["performance_analysis"]
        security = self.analysis_results["security_analysis"]
        issues = self.analysis_results["critical_issues"]
        
        # Calculate scores (0-100)
        integration_score = max(0, 100 - len(integration.get("mock_data_usage", [])) * 25)
        performance_score = min(100, (performance.get("lazy_loading", 0) + performance.get("memoization", 0)) * 10)
        security_score = min(100, (security.get("input_sanitization", 0) + security.get("authentication_patterns", []).__len__()) * 5)
        
        critical_issue_penalty = len([issue for issue in issues if issue.get("severity") == "CRITICAL"]) * 30
        overall_score = max(0, (integration_score + performance_score + security_score) / 3 - critical_issue_penalty)
        
        return {
            "overall_score": round(overall_score, 1),
            "integration_score": round(integration_score, 1),
            "performance_score": round(performance_score, 1),
            "security_score": round(security_score, 1),
            "critical_issues_count": len([issue for issue in issues if issue.get("severity") == "CRITICAL"]),
            "high_issues_count": len([issue for issue in issues if issue.get("severity") == "HIGH"]),
            "total_files_analyzed": self.analysis_results["architecture_analysis"]["total_files"],
            "total_lines_analyzed": self.analysis_results["architecture_analysis"]["total_lines"]
        }
    
    def generate_report(self) -> str:
        """Generate comprehensive analysis report"""
        results = self.analysis_results
        summary = results["summary"]
        
        report = f"""
# 🔍 COMPREHENSIVE FRONTEND ANALYSIS REPORT
**Analysis Date:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}  
**Scope:** Complete NexusScan Frontend Architecture Analysis  
**Files Analyzed:** {summary['total_files_analyzed']} files ({summary['total_lines_analyzed']:,} lines of code)

---

## 📊 EXECUTIVE SUMMARY

### Overall Architecture Score: **{summary['overall_score']}/100**

| Category | Score | Status |
|----------|-------|---------|
| 🔗 Backend Integration | {summary['integration_score']}/100 | {'🔴 Critical' if summary['integration_score'] < 50 else '🟡 Needs Work' if summary['integration_score'] < 80 else '🟢 Good'} |
| ⚡ Performance | {summary['performance_score']}/100 | {'🔴 Poor' if summary['performance_score'] < 50 else '🟡 Fair' if summary['performance_score'] < 80 else '🟢 Good'} |
| 🔒 Security | {summary['security_score']}/100 | {'🔴 Weak' if summary['security_score'] < 50 else '🟡 Moderate' if summary['security_score'] < 80 else '🟢 Strong'} |

### Critical Issues: **{summary['critical_issues_count']}** • High Issues: **{summary['high_issues_count']}**

---

## 🚨 CRITICAL FINDINGS

"""
        
        # Add critical issues
        critical_issues = [issue for issue in results["critical_issues"] if issue.get("severity") == "CRITICAL"]
        for i, issue in enumerate(critical_issues, 1):
            report += f"""
### {i}. {issue['issue']} 
**Severity:** {issue['severity']} | **Category:** {issue['category']}  
**Description:** {issue['description']}  
**Impact:** {issue['impact']}  
**File:** `{issue['file']}`  
"""
        
        # Add integration analysis
        integration = results["integration_analysis"]
        report += f"""

---

## 🔗 BACKEND INTEGRATION ANALYSIS

### Advanced Tools Integration Status
- **Metasploit Integration:** {'❌ Mock Data' if integration.get('mock_data_usage') else '✅ Real API'}
- **Execution Management:** {'❌ Mock Data' if 'setExecutionModes' in str(integration.get('mock_data_usage', [])) else '✅ Real API'}
- **Compliance Testing:** {'❌ Mock Data' if 'setComplianceFrameworks' in str(integration.get('mock_data_usage', [])) else '✅ Real API'}  
- **Intelligence Platform:** {'❌ Mock Data' if 'setIntelligenceData' in str(integration.get('mock_data_usage', [])) else '✅ Real API'}

### Mock Data Usage Detected:
"""
        for mock_pattern in integration.get("mock_data_usage", []):
            report += f"- `{mock_pattern}`\n"
        
        # Add UI functionality analysis
        ui_buttons = integration.get("ui_buttons", {})
        report += f"""

### UI Functionality Analysis
- **Configure Buttons:** {ui_buttons.get('configure_buttons', 0)} found
- **Execute Buttons:** {ui_buttons.get('execute_buttons', 0)} found  
- **Functional Handlers:** {ui_buttons.get('functional_handlers', 0)} implemented
- **Functionality Gap:** {ui_buttons.get('configure_buttons', 0) + ui_buttons.get('execute_buttons', 0) - ui_buttons.get('functional_handlers', 0)} non-functional buttons

"""
        
        # Add recommendations
        report += """
---

## 💡 IMMEDIATE ACTION RECOMMENDATIONS

"""
        
        immediate_recs = [rec for rec in results["recommendations"] if rec.get("priority") == "IMMEDIATE"]
        for i, rec in enumerate(immediate_recs, 1):
            report += f"""
### {i}. {rec['title']}
**Priority:** {rec['priority']} | **Category:** {rec['category']}  
**Business Impact:** {rec['business_impact']}  
**Estimated Effort:** {rec['estimated_effort']}

**Description:** {rec['description']}

**Implementation Steps:**
"""
            for step in rec.get("implementation", []):
                report += f"- {step}\n"
            report += "\n"
        
        # Add architecture overview
        arch = results["architecture_analysis"]
        report += f"""

---

## 🏗️ ARCHITECTURE OVERVIEW

### Codebase Statistics
- **Total Files:** {arch['total_files']} TypeScript/React files
- **Total Lines:** {arch['total_lines']:,} lines of code
- **Average File Size:** {arch['average_file_size']:.0f} lines per file
- **Directory Structure:** {len(arch['structure'])} main directories

### Component Architecture
"""
        
        components = results.get("component_analysis", {}).get("component_categories", {})
        for category, details in components.items():
            report += f"- **{category.title()}:** {details.get('component_count', 0)} components\n"
        
        # Add performance analysis
        perf = results["performance_analysis"]
        report += f"""

---

## ⚡ PERFORMANCE ANALYSIS

### Optimization Patterns Found
- **Lazy Loading:** {perf.get('lazy_loading', 0)} implementations
- **Memoization:** {perf.get('memoization', 0)} uses (useMemo, useCallback, React.memo)
- **Virtualization:** {perf.get('virtualization', 0)} virtual lists
- **Code Splitting:** {perf.get('code_splitting', 0)} dynamic imports

### Performance Score: {summary['performance_score']}/100
"""
        
        if summary['performance_score'] < 60:
            report += """
**⚠️ Performance Concerns:**
- Limited use of React performance optimizations
- Consider implementing lazy loading for heavy components
- Add memoization for expensive calculations
- Implement virtualization for large lists
"""
        
        # Add security analysis
        security = results["security_analysis"]
        report += f"""

---

## 🔒 SECURITY ANALYSIS

### Security Patterns Found
- **Input Sanitization:** {security.get('input_sanitization', 0)} instances
- **Authentication:** {len(security.get('authentication_patterns', []))} patterns
- **Authorization:** {len(security.get('authorization_patterns', []))} patterns  
- **Data Validation:** {security.get('validation', 0)} validations

### Security Score: {summary['security_score']}/100
"""
        
        if summary['security_score'] < 70:
            report += """
**🚨 Security Recommendations:**
- Implement comprehensive input sanitization
- Add XSS protection for dynamic content
- Enhance authentication and authorization patterns
- Add data validation schemas
"""
        
        report += f"""

---

## 🎯 NEXT STEPS ROADMAP

### Phase 1: Critical Fixes (1-2 weeks)
1. **Fix Backend Integration:** Connect advanced tools to real APIs
2. **Implement Button Functionality:** Add onClick handlers to all CTA buttons
3. **Fix Mock Data:** Replace hardcoded data with dynamic API responses

### Phase 2: Quality Improvements (2-4 weeks)  
1. **Enhance Error Handling:** Comprehensive error boundaries and user feedback
2. **Performance Optimization:** Lazy loading and code splitting
3. **Security Hardening:** Input validation and sanitization

### Phase 3: Advanced Features (4-8 weeks)
1. **Real-time Updates:** WebSocket integration for live data
2. **Advanced UI:** Progressive enhancement and accessibility
3. **Testing Coverage:** Comprehensive unit and E2E tests

---

**Report Generated:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}  
**Analysis Tool:** NexusScan Frontend Architecture Analyzer  
**Recommendation:** Address Critical and High priority issues immediately to ensure platform functionality matches user expectations.
"""
        
        return report

def main():
    """Main analysis execution"""
    print("🎯 STARTING COMPREHENSIVE FRONTEND ANALYSIS")
    print("=" * 80)
    
    analyzer = FrontendAnalyzer()
    results = analyzer.run_comprehensive_analysis()
    report = analyzer.generate_report()
    
    # Save report
    report_path = "/mnt/e/dev/nexusscan-desktop/COMPREHENSIVE_FRONTEND_ANALYSIS_REPORT.md"
    with open(report_path, 'w') as f:
        f.write(report)
    
    # Save raw results as JSON
    results_path = "/mnt/e/dev/nexusscan-desktop/frontend_analysis_results.json"
    with open(results_path, 'w') as f:
        json.dump(results, f, indent=2, default=str)
    
    print(f"\n✅ Analysis Complete!")
    print(f"📄 Report saved: {report_path}")
    print(f"📊 Raw data saved: {results_path}")
    print(f"\n🎯 Overall Score: {results['summary']['overall_score']}/100")
    print(f"🚨 Critical Issues: {results['summary']['critical_issues_count']}")
    print(f"⚠️ High Issues: {results['summary']['high_issues_count']}")
    
    return results

if __name__ == "__main__":
    main()