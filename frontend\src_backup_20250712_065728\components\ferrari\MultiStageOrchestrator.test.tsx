import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen, fireEvent, waitFor } from '@/test/utils'
import userEvent from '@testing-library/user-event'
import { MultiStageOrchestrator } from './MultiStageOrchestrator'
import { createMockElectronAPI, createMockWebSocket, createMockFerrariCapabilities } from '@/test/utils'

// Mock the stores
vi.mock('@/stores/backend-store', () => ({
  useBackendStore: () => ({
    isConnected: true,
    executeAITool: vi.fn().mockResolvedValue({ executionId: 'exec_orchestrator_1' }),
    getAICapabilities: vi.fn().mockResolvedValue(createMockFerrariCapabilities().orchestrator)
  })
}))

vi.mock('@/stores/app-store', () => ({
  useAppStore: () => ({
    addNotification: vi.fn()
  })
}))

describe('MultiStageOrchestrator', () => {
  const user = userEvent.setup()
  
  beforeEach(() => {
    global.electronAPI = createMockElectronAPI()
    global.WebSocket = vi.fn(() => createMockWebSocket())
  })

  describe('Component Rendering', () => {
    it('renders the Multi-Stage Orchestrator interface', () => {
      render(<MultiStageOrchestrator />)
      
      expect(screen.getByText('Multi-Stage Attack Orchestrator')).toBeInTheDocument()
      expect(screen.getByText('AI-Powered Attack Chain Builder with MITRE ATT&CK Integration')).toBeInTheDocument()
      expect(screen.getByRole('tab', { name: 'Chain Builder' })).toBeInTheDocument()
      expect(screen.getByRole('tab', { name: 'Templates' })).toBeInTheDocument()
      expect(screen.getByRole('tab', { name: 'Analytics' })).toBeInTheDocument()
      expect(screen.getByRole('tab', { name: 'Execution' })).toBeInTheDocument()
    })

    it('displays the chain builder by default', () => {
      render(<MultiStageOrchestrator />)
      
      expect(screen.getByText('Attack Chain Configuration')).toBeInTheDocument()
      expect(screen.getByLabelText(/Target Information/)).toBeInTheDocument()
      expect(screen.getByLabelText(/Attack Objective/)).toBeInTheDocument()
      expect(screen.getByText('MITRE ATT&CK Matrix')).toBeInTheDocument()
    })

    it('shows MITRE ATT&CK tactics', () => {
      render(<MultiStageOrchestrator />)
      
      expect(screen.getByText('Reconnaissance')).toBeInTheDocument()
      expect(screen.getByText('Initial Access')).toBeInTheDocument()
      expect(screen.getByText('Execution')).toBeInTheDocument()
      expect(screen.getByText('Persistence')).toBeInTheDocument()
      expect(screen.getByText('Privilege Escalation')).toBeInTheDocument()
      expect(screen.getByText('Defense Evasion')).toBeInTheDocument()
      expect(screen.getByText('Credential Access')).toBeInTheDocument()
      expect(screen.getByText('Discovery')).toBeInTheDocument()
      expect(screen.getByText('Lateral Movement')).toBeInTheDocument()
      expect(screen.getByText('Collection')).toBeInTheDocument()
      expect(screen.getByText('Exfiltration')).toBeInTheDocument()
      expect(screen.getByText('Impact')).toBeInTheDocument()
    })

    it('displays attack objective options', async () => {
      render(<MultiStageOrchestrator />)
      
      const objectiveSelect = screen.getByLabelText(/Attack Objective/)
      await user.click(objectiveSelect)
      
      expect(screen.getByText('Reconnaissance')).toBeInTheDocument()
      expect(screen.getByText('Credential Harvesting')).toBeInTheDocument()
      expect(screen.getByText('Data Exfiltration')).toBeInTheDocument()
      expect(screen.getByText('System Compromise')).toBeInTheDocument()
      expect(screen.getByText('Lateral Movement')).toBeInTheDocument()
      expect(screen.getByText('Persistence')).toBeInTheDocument()
    })
  })

  describe('Attack Chain Builder', () => {
    it('allows adding techniques to attack chain', async () => {
      render(<MultiStageOrchestrator />)
      
      // Click on Reconnaissance tactic
      const reconTactic = screen.getByText('Reconnaissance')
      await user.click(reconTactic)
      
      await waitFor(() => {
        expect(screen.getByText('T1595')).toBeInTheDocument() // Active Scanning
        expect(screen.getByText('T1590')).toBeInTheDocument() // Gather Victim Network Information
      })
      
      // Add a technique
      const activeScanningTechnique = screen.getByText('T1595')
      await user.click(activeScanningTechnique)
      
      await waitFor(() => {
        expect(screen.getByText('Attack Chain (1 techniques)')).toBeInTheDocument()
      })
    })

    it('shows technique details when selected', async () => {
      render(<MultiStageOrchestrator />)
      
      const reconTactic = screen.getByText('Reconnaissance')
      await user.click(reconTactic)
      
      const activeScanningTechnique = screen.getByText('T1595')
      await user.click(activeScanningTechnique)
      
      await waitFor(() => {
        expect(screen.getByText('Active Scanning')).toBeInTheDocument()
        expect(screen.getByText(/Adversaries may execute active reconnaissance/)).toBeInTheDocument()
        expect(screen.getByText('Mitigations')).toBeInTheDocument()
        expect(screen.getByText('Detection')).toBeInTheDocument()
      })
    })

    it('allows reordering attack chain steps', async () => {
      render(<MultiStageOrchestrator />)
      
      // Add multiple techniques
      const reconTactic = screen.getByText('Reconnaissance')
      await user.click(reconTactic)
      
      const technique1 = screen.getByText('T1595')
      await user.click(technique1)
      
      const technique2 = screen.getByText('T1590')
      await user.click(technique2)
      
      await waitFor(() => {
        expect(screen.getByText('Attack Chain (2 techniques)')).toBeInTheDocument()
      })
      
      // Test drag and drop functionality would be implemented here
      // This is a simplified test due to complex drag-drop testing
      const chainContainer = screen.getByTestId('attack-chain-container')
      expect(chainContainer).toBeInTheDocument()
    })

    it('validates attack chain logical flow', async () => {
      render(<MultiStageOrchestrator />)
      
      // Try to add an advanced technique without prerequisites
      const impactTactic = screen.getByText('Impact')
      await user.click(impactTactic)
      
      const dataDestructionTechnique = screen.getByText('T1485')
      await user.click(dataDestructionTechnique)
      
      await waitFor(() => {
        expect(screen.getByText(/Warning: Consider adding reconnaissance/)).toBeInTheDocument()
      })
    })

    it('removes techniques from attack chain', async () => {
      render(<MultiStageOrchestrator />)
      
      // Add a technique
      const reconTactic = screen.getByText('Reconnaissance')
      await user.click(reconTactic)
      
      const technique = screen.getByText('T1595')
      await user.click(technique)
      
      await waitFor(() => {
        expect(screen.getByText('Attack Chain (1 techniques)')).toBeInTheDocument()
      })
      
      // Remove the technique
      const removeButton = screen.getByRole('button', { name: /Remove T1595/ })
      await user.click(removeButton)
      
      await waitFor(() => {
        expect(screen.getByText('Attack Chain (0 techniques)')).toBeInTheDocument()
      })
    })
  })

  describe('Safety Constraints', () => {
    it('shows safety constraints configuration', async () => {
      render(<MultiStageOrchestrator />)
      
      const safetyButton = screen.getByRole('button', { name: /Safety Constraints/ })
      await user.click(safetyButton)
      
      await waitFor(() => {
        expect(screen.getByLabelText(/Maximum Execution Time/)).toBeInTheDocument()
        expect(screen.getByLabelText(/Stop on Detection/)).toBeInTheDocument()
        expect(screen.getByLabelText(/Require Manual Approval/)).toBeInTheDocument()
        expect(screen.getByLabelText(/Simulation Mode Only/)).toBeInTheDocument()
      })
    })

    it('enforces safety constraints during execution', async () => {
      render(<MultiStageOrchestrator />)
      
      // Configure chain
      const targetInput = screen.getByLabelText(/Target Information/)
      await user.type(targetInput, '192.168.1.1')
      
      // Add a technique
      const reconTactic = screen.getByText('Reconnaissance')
      await user.click(reconTactic)
      
      const technique = screen.getByText('T1595')
      await user.click(technique)
      
      // Configure safety constraints
      const safetyButton = screen.getByRole('button', { name: /Safety Constraints/ })
      await user.click(safetyButton)
      
      const simulationMode = screen.getByLabelText(/Simulation Mode Only/)
      await user.click(simulationMode)
      
      // Execute chain
      const executeButton = screen.getByRole('button', { name: /Execute Chain/ })
      await user.click(executeButton)
      
      await waitFor(() => {
        expect(screen.getByText(/Executing in simulation mode/)).toBeInTheDocument()
      })
    })

    it('requires manual approval when configured', async () => {
      render(<MultiStageOrchestrator />)
      
      const targetInput = screen.getByLabelText(/Target Information/)
      await user.type(targetInput, '192.168.1.1')
      
      const reconTactic = screen.getByText('Reconnaissance')
      await user.click(reconTactic)
      
      const technique = screen.getByText('T1595')
      await user.click(technique)
      
      const safetyButton = screen.getByRole('button', { name: /Safety Constraints/ })
      await user.click(safetyButton)
      
      const manualApproval = screen.getByLabelText(/Require Manual Approval/)
      await user.click(manualApproval)
      
      const executeButton = screen.getByRole('button', { name: /Execute Chain/ })
      await user.click(executeButton)
      
      await waitFor(() => {
        expect(screen.getByText(/Waiting for manual approval/)).toBeInTheDocument()
        expect(screen.getByRole('button', { name: /Approve/ })).toBeInTheDocument()
        expect(screen.getByRole('button', { name: /Reject/ })).toBeInTheDocument()
      })
    })
  })

  describe('AI Integration', () => {
    it('uses AI to suggest attack chain optimization', async () => {
      render(<MultiStageOrchestrator />)
      
      const targetInput = screen.getByLabelText(/Target Information/)
      await user.type(targetInput, 'Windows Domain Controller')
      
      const objectiveSelect = screen.getByLabelText(/Attack Objective/)
      await user.click(objectiveSelect)
      await user.click(screen.getByText('System Compromise'))
      
      const optimizeButton = screen.getByRole('button', { name: /AI Optimize/ })
      await user.click(optimizeButton)
      
      await waitFor(() => {
        expect(screen.getByText(/AI is analyzing target/)).toBeInTheDocument()
      })
    })

    it('shows AI confidence scores for technique selection', async () => {
      render(<MultiStageOrchestrator />)
      
      const targetInput = screen.getByLabelText(/Target Information/)
      await user.type(targetInput, 'Linux Web Server')
      
      const reconTactic = screen.getByText('Reconnaissance')
      await user.click(reconTactic)
      
      await waitFor(() => {
        expect(screen.getByText(/Confidence: 95%/)).toBeInTheDocument()
        expect(screen.getByText(/Confidence: 87%/)).toBeInTheDocument()
      })
    })

    it('provides AI-generated technique parameters', async () => {
      render(<MultiStageOrchestrator />)
      
      const targetInput = screen.getByLabelText(/Target Information/)
      await user.type(targetInput, '192.168.1.1')
      
      const reconTactic = screen.getByText('Reconnaissance')
      await user.click(reconTactic)
      
      const technique = screen.getByText('T1595')
      await user.click(technique)
      
      await waitFor(() => {
        expect(screen.getByText('AI-Generated Parameters')).toBeInTheDocument()
        expect(screen.getByText(/Recommended ports/)).toBeInTheDocument()
        expect(screen.getByText(/Suggested timing/)).toBeInTheDocument()
      })
    })
  })

  describe('Templates', () => {
    it('shows available attack chain templates', async () => {
      render(<MultiStageOrchestrator />)
      
      const templatesTab = screen.getByRole('tab', { name: 'Templates' })
      await user.click(templatesTab)
      
      await waitFor(() => {
        expect(screen.getByText('Reconnaissance Chain')).toBeInTheDocument()
        expect(screen.getByText('Web Application Attack')).toBeInTheDocument()
        expect(screen.getByText('Network Penetration')).toBeInTheDocument()
        expect(screen.getByText('Privilege Escalation')).toBeInTheDocument()
      })
    })

    it('loads template into chain builder', async () => {
      render(<MultiStageOrchestrator />)
      
      const templatesTab = screen.getByRole('tab', { name: 'Templates' })
      await user.click(templatesTab)
      
      const reconTemplate = screen.getByText('Reconnaissance Chain')
      await user.click(reconTemplate)
      
      const loadButton = screen.getByRole('button', { name: /Load Template/ })
      await user.click(loadButton)
      
      // Switch back to chain builder
      const builderTab = screen.getByRole('tab', { name: 'Chain Builder' })
      await user.click(builderTab)
      
      await waitFor(() => {
        expect(screen.getByText(/Attack Chain \(\d+ techniques\)/)).toBeInTheDocument()
      })
    })

    it('allows saving custom templates', async () => {
      render(<MultiStageOrchestrator />)
      
      // Build a custom chain
      const targetInput = screen.getByLabelText(/Target Information/)
      await user.type(targetInput, '192.168.1.1')
      
      const reconTactic = screen.getByText('Reconnaissance')
      await user.click(reconTactic)
      
      const technique = screen.getByText('T1595')
      await user.click(technique)
      
      // Save as template
      const saveTemplateButton = screen.getByRole('button', { name: /Save as Template/ })
      await user.click(saveTemplateButton)
      
      const templateNameInput = screen.getByLabelText(/Template Name/)
      await user.type(templateNameInput, 'Custom Recon Chain')
      
      const saveButton = screen.getByRole('button', { name: /Save/ })
      await user.click(saveButton)
      
      await waitFor(() => {
        expect(screen.getByText(/Template saved successfully/)).toBeInTheDocument()
      })
    })
  })

  describe('Execution Monitoring', () => {
    it('shows real-time execution progress', async () => {
      render(<MultiStageOrchestrator />)
      
      // Configure and start execution
      const targetInput = screen.getByLabelText(/Target Information/)
      await user.type(targetInput, '192.168.1.1')
      
      const reconTactic = screen.getByText('Reconnaissance')
      await user.click(reconTactic)
      
      const technique = screen.getByText('T1595')
      await user.click(technique)
      
      const executeButton = screen.getByRole('button', { name: /Execute Chain/ })
      await user.click(executeButton)
      
      const executionTab = screen.getByRole('tab', { name: 'Execution' })
      await user.click(executionTab)
      
      await waitFor(() => {
        expect(screen.getByText('Execution Progress')).toBeInTheDocument()
        expect(screen.getByRole('progressbar')).toBeInTheDocument()
        expect(screen.getByText(/Current Technique: T1595/)).toBeInTheDocument()
      })
    })

    it('displays technique execution results', async () => {
      render(<MultiStageOrchestrator />)
      
      const executionTab = screen.getByRole('tab', { name: 'Execution' })
      await user.click(executionTab)
      
      await waitFor(() => {
        expect(screen.getByText('Technique Results')).toBeInTheDocument()
        expect(screen.getByText(/Success Rate/)).toBeInTheDocument()
        expect(screen.getByText(/Artifacts Collected/)).toBeInTheDocument()
      })
    })

    it('allows pausing and resuming execution', async () => {
      render(<MultiStageOrchestrator />)
      
      const targetInput = screen.getByLabelText(/Target Information/)
      await user.type(targetInput, '192.168.1.1')
      
      const reconTactic = screen.getByText('Reconnaissance')
      await user.click(reconTactic)
      
      const technique = screen.getByText('T1595')
      await user.click(technique)
      
      const executeButton = screen.getByRole('button', { name: /Execute Chain/ })
      await user.click(executeButton)
      
      await waitFor(() => {
        const pauseButton = screen.getByRole('button', { name: /Pause/ })
        expect(pauseButton).toBeInTheDocument()
      })
      
      const pauseButton = screen.getByRole('button', { name: /Pause/ })
      await user.click(pauseButton)
      
      await waitFor(() => {
        expect(screen.getByRole('button', { name: /Resume/ })).toBeInTheDocument()
      })
    })
  })

  describe('Analytics', () => {
    it('displays attack chain analytics', async () => {
      render(<MultiStageOrchestrator />)
      
      const analyticsTab = screen.getByRole('tab', { name: 'Analytics' })
      await user.click(analyticsTab)
      
      await waitFor(() => {
        expect(screen.getByText('Attack Chain Analytics')).toBeInTheDocument()
        expect(screen.getByText('Success Rates by Tactic')).toBeInTheDocument()
        expect(screen.getByText('Execution Timeline')).toBeInTheDocument()
        expect(screen.getByText('Detection Risk Assessment')).toBeInTheDocument()
      })
    })

    it('shows MITRE ATT&CK coverage heat map', async () => {
      render(<MultiStageOrchestrator />)
      
      const analyticsTab = screen.getByRole('tab', { name: 'Analytics' })
      await user.click(analyticsTab)
      
      await waitFor(() => {
        expect(screen.getByText('MITRE ATT&CK Coverage')).toBeInTheDocument()
        expect(screen.getByTestId('mitre-heatmap')).toBeInTheDocument()
      })
    })

    it('provides technique effectiveness metrics', async () => {
      render(<MultiStageOrchestrator />)
      
      const analyticsTab = screen.getByRole('tab', { name: 'Analytics' })
      await user.click(analyticsTab)
      
      await waitFor(() => {
        expect(screen.getByText('Technique Effectiveness')).toBeInTheDocument()
        expect(screen.getByText(/Average Success Rate: 87%/)).toBeInTheDocument()
        expect(screen.getByText(/Detection Rate: 12%/)).toBeInTheDocument()
      })
    })
  })

  describe('Error Handling', () => {
    it('handles AI service unavailable', async () => {
      const mockGetAICapabilities = vi.fn().mockRejectedValue(new Error('AI service unavailable'))
      
      vi.mocked(useBackendStore).mockReturnValue({
        isConnected: true,
        executeAITool: vi.fn(),
        getAICapabilities: mockGetAICapabilities
      })
      
      render(<MultiStageOrchestrator />)
      
      await waitFor(() => {
        expect(screen.getByText(/AI services currently unavailable/)).toBeInTheDocument()
      })
    })

    it('validates target information before execution', async () => {
      render(<MultiStageOrchestrator />)
      
      const reconTactic = screen.getByText('Reconnaissance')
      await user.click(reconTactic)
      
      const technique = screen.getByText('T1595')
      await user.click(technique)
      
      const executeButton = screen.getByRole('button', { name: /Execute Chain/ })
      await user.click(executeButton)
      
      await waitFor(() => {
        expect(screen.getByText(/Target information is required/)).toBeInTheDocument()
      })
    })

    it('shows warning for high-risk attack chains', async () => {
      render(<MultiStageOrchestrator />)
      
      const targetInput = screen.getByLabelText(/Target Information/)
      await user.type(targetInput, '192.168.1.1')
      
      // Add high-risk impact technique
      const impactTactic = screen.getByText('Impact')
      await user.click(impactTactic)
      
      const destructiveTechnique = screen.getByText('T1485')
      await user.click(destructiveTechnique)
      
      await waitFor(() => {
        expect(screen.getByText(/High-Risk Attack Chain Detected/)).toBeInTheDocument()
        expect(screen.getByText(/This chain includes destructive techniques/)).toBeInTheDocument()
      })
    })
  })
})