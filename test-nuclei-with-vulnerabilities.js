/**
 * Test Nuclei JSON Parsing with Vulnerabilities
 * Tests the fix with a response that contains actual vulnerabilities
 */

// Simulate a backend response with vulnerabilities found
const mockBackendResponseWithVulns = {
  success: true,
  data: {
    scan_id: "test-scan-456",
    tool: "nuclei",
    target: "http://vulnerable-site.com",
    status: "completed",
    parsed_results: {
      scan_info: {
        scanner: "nuclei",
        scan_type: "vulnerability_scan",
        template_count: 4000,
        status: "completed"
      },
      vulnerabilities: [
        {
          template_id: "CVE-2021-44228",
          severity: "critical",
          url: "http://vulnerable-site.com/api/log",
          tags: ["cve", "log4j", "rce"],
          name: "Log4j Remote Code Execution",
          info: "Apache Log4j2 JNDI features do not protect against attacker controlled LDAP"
        },
        {
          template_id: "sql-injection-login",
          severity: "high", 
          url: "http://vulnerable-site.com/login",
          tags: ["sqli", "auth-bypass"],
          name: "SQL Injection in Login Form",
          info: "SQL injection vulnerability in login form"
        },
        {
          template_id: "xss-reflected",
          severity: "medium",
          url: "http://vulnerable-site.com/search?q=<script>",
          tags: ["xss", "reflected"],
          name: "Reflected XSS",
          info: "Reflected cross-site scripting vulnerability"
        }
      ],
      detailed_findings: [
        {
          id: "finding-1",
          template_id: "CVE-2021-44228",
          severity: "critical",
          target: "http://vulnerable-site.com/api/log"
        }
      ],
      statistics: {
        total_findings: 3,
        severity_counts: {
          critical: 1,
          high: 1,
          medium: 1,
          low: 0,
          info: 0
        }
      }
    }
  }
};

// Use the same parsing logic from our fix
function parseNucleiResults(progressUpdate) {
  let parsedResults = progressUpdate.results;
  
  // Handle nested backend response structure
  if (progressUpdate.results?.parsed_results) {
    const backendData = progressUpdate.results.parsed_results;
    
    // Extract severity counts from the nested structure
    const severityCounts = backendData.statistics?.severity_counts || {};
    
    // Transform to frontend-expected format
    parsedResults = {
      critical: severityCounts.critical || 0,
      high: severityCounts.high || 0,
      medium: severityCounts.medium || 0,
      low: severityCounts.low || 0,
      info: severityCounts.info || 0,
      vulnerabilities: backendData.vulnerabilities || [],
      detailed_findings: backendData.detailed_findings || [],
      raw_backend_data: progressUpdate.results
    };
  }
  
  return parsedResults;
}

// Test with vulnerabilities
console.log('🚀 Testing Nuclei JSON Parsing with Vulnerabilities');
console.log('===================================================');

try {
  const progressUpdate = {
    id: 'test-scan-456',
    toolId: 'nuclei',
    status: 'completed',
    progress: 100,
    results: mockBackendResponseWithVulns.data
  };
  
  console.log('\n📥 Input (Backend Response with Vulnerabilities):');
  console.log('- Total vulnerabilities:', progressUpdate.results.parsed_results.vulnerabilities.length);
  console.log('- Severity distribution:', progressUpdate.results.parsed_results.statistics.severity_counts);
  
  // Apply our parsing fix
  const parsedResults = parseNucleiResults(progressUpdate);
  
  console.log('\n📤 Output (Frontend Format):');
  console.log('- Critical:', parsedResults.critical);
  console.log('- High:', parsedResults.high);
  console.log('- Medium:', parsedResults.medium);
  console.log('- Low:', parsedResults.low);
  console.log('- Info:', parsedResults.info);
  console.log('- Total vulnerabilities:', parsedResults.vulnerabilities.length);
  console.log('- Total detailed findings:', parsedResults.detailed_findings.length);
  
  console.log('\n🔍 Sample Vulnerability Data:');
  if (parsedResults.vulnerabilities.length > 0) {
    const firstVuln = parsedResults.vulnerabilities[0];
    console.log('- Template ID:', firstVuln.template_id);
    console.log('- Severity:', firstVuln.severity);
    console.log('- URL:', firstVuln.url);
    console.log('- Tags:', firstVuln.tags);
    console.log('- Name:', firstVuln.name);
  }
  
  // Verify the counts match
  const expectedCounts = {
    critical: 1,
    high: 1,
    medium: 1,
    low: 0,
    info: 0
  };
  
  let testPassed = true;
  for (const [severity, expectedCount] of Object.entries(expectedCounts)) {
    if (parsedResults[severity] !== expectedCount) {
      console.error(`❌ FAIL: ${severity} count mismatch. Expected: ${expectedCount}, Got: ${parsedResults[severity]}`);
      testPassed = false;
    }
  }
  
  // Verify vulnerability data is preserved
  if (parsedResults.vulnerabilities.length !== 3) {
    console.error(`❌ FAIL: Expected 3 vulnerabilities, got ${parsedResults.vulnerabilities.length}`);
    testPassed = false;
  }
  
  if (testPassed) {
    console.log('\n✅ SUCCESS: Nuclei JSON parsing works with vulnerabilities!');
    console.log('✅ Severity counts correctly extracted');
    console.log('✅ Vulnerability data properly preserved');
    console.log('✅ Frontend table will display vulnerabilities correctly');
  } else {
    console.log('\n❌ FAILURE: Issues found in vulnerability parsing');
  }
  
} catch (error) {
  console.error('❌ ERROR during testing:', error.message);
}

console.log('\n🎯 Ready for Real Testing:');
console.log('1. ✅ JSON parsing fix implemented');
console.log('2. ✅ Handles empty results (no vulnerabilities)');
console.log('3. ✅ Handles results with vulnerabilities');
console.log('4. ✅ Preserves all vulnerability metadata');
console.log('5. 🔄 Ready to test with live Contabo backend');
