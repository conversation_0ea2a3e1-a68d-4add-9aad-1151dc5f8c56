"""
Integration Ecosystem Package
Comprehensive integration platform with API management, webhooks,
and third-party service connectors
"""

from .ecosystem_framework import (
    IntegrationEcosystem, IntegrationType, EventTrigger, AuthenticationType,
    IntegrationEndpoint, IntegrationRule, AuthenticationConfig, WebhookConfig,
    get_integration_ecosystem, close_integration_ecosystem,
    create_slack_integration, create_jira_integration, create_splunk_integration
)

__all__ = [
    'IntegrationEcosystem', 'IntegrationType', 'EventTrigger', 'AuthenticationType',
    'IntegrationEndpoint', 'IntegrationRule', 'AuthenticationConfig', 'WebhookConfig',
    'get_integration_ecosystem', 'close_integration_ecosystem',
    'create_slack_integration', 'create_jira_integration', 'create_splunk_integration'
]