#!/usr/bin/env python3
"""
Hybrid Execution Pipeline Engine
Universal tool execution across all environments with intelligent fallbacks
"""

import asyncio
import logging
import json
import time
from typing import Dict, Any, Optional, List, Tuple, Union
from dataclasses import dataclass, field
from enum import Enum
from pathlib import Path

from .environment_detector import (
    environment_detector, 
    ExecutionEnvironment, 
    ExecutionStrategy,
    get_execution_priority
)

logger = logging.getLogger(__name__)

class ExecutionResult(Enum):
    """Execution result status"""
    SUCCESS = "success"
    FAILED = "failed"
    TIMEOUT = "timeout"
    NOT_AVAILABLE = "not_available"
    PERMISSION_DENIED = "permission_denied"
    DEPENDENCY_MISSING = "dependency_missing"

@dataclass
class ToolExecutionResult:
    """Result of tool execution"""
    status: ExecutionResult
    method: str  # docker, native, wsl, simulation
    output: str = ""
    error: str = ""
    execution_time: float = 0.0
    exit_code: int = 0
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class ToolDefinition:
    """Tool definition with execution preferences"""
    name: str
    command: str
    windows_command: Optional[str] = None
    docker_image: Optional[str] = None
    docker_service: Optional[str] = None
    install_commands: Dict[str, str] = field(default_factory=dict)
    fallback_tools: List[str] = field(default_factory=list)
    required_capabilities: List[str] = field(default_factory=list)
    timeout: int = 300

class HybridExecutionEngine:
    """Universal tool execution engine with intelligent fallbacks"""
    
    def __init__(self):
        self.environment = environment_detector.environment
        self.capabilities = environment_detector.capabilities
        self.execution_priority = get_execution_priority()
        
        # Import execution managers
        self.docker_manager = None
        self.native_manager = None
        self.wsl_manager = None
        self.simulation_manager = None
        
        # Tool definitions
        self.tool_definitions = self._load_tool_definitions()
        
        # Performance metrics
        self.execution_stats = {
            'total_executions': 0,
            'successful_executions': 0,
            'method_usage': {},
            'average_execution_time': 0.0
        }
        
        logger.info(f"🚀 Hybrid Execution Engine initialized")
        logger.info(f"🌍 Environment: {self.environment.value}")
        logger.info(f"📋 Execution priority: {self.execution_priority}")
    
    async def initialize_managers(self):
        """Initialize execution managers based on available capabilities"""
        
        # Docker Manager
        if self.capabilities.get('docker'):
            try:
                from .docker_tool_manager import DockerToolManager
                self.docker_manager = DockerToolManager()
                await self.docker_manager.initialize()
                logger.info("🐳 Docker manager initialized")
            except Exception as e:
                logger.warning(f"Failed to initialize Docker manager: {e}")
        
        # Native Manager
        if self.capabilities.get('native_linux') or self.capabilities.get('native_windows'):
            try:
                from .native_tool_manager import NativeToolManager
                self.native_manager = NativeToolManager()
                await self.native_manager.initialize()
                logger.info("🐧 Native manager initialized")
            except Exception as e:
                logger.warning(f"Failed to initialize Native manager: {e}")
        
        # WSL Manager
        if self.capabilities.get('wsl'):
            try:
                from .wsl_tool_runner import WSLToolRunner
                self.wsl_manager = WSLToolRunner()
                logger.info("🔗 WSL manager initialized")
            except Exception as e:
                logger.warning(f"Failed to initialize WSL manager: {e}")
        
        # Simulation Manager
        try:
            from .simulation_manager import SimulationManager
            self.simulation_manager = SimulationManager()
            logger.info("🎭 Simulation manager initialized")
        except Exception as e:
            logger.warning(f"Failed to initialize Simulation manager: {e}")
    
    def _load_tool_definitions(self) -> Dict[str, ToolDefinition]:
        """Load comprehensive tool definitions"""
        
        tools = {
            # Network Scanning Tools
            "nmap": ToolDefinition(
                name="nmap",
                command="nmap",
                windows_command="nmap.exe",
                docker_image="nmap:latest",
                docker_service="nmap-service",
                install_commands={
                    "apt": "apt-get update && apt-get install -y nmap",
                    "yum": "yum install -y nmap",
                    "brew": "brew install nmap",
                    "chocolatey": "choco install nmap"
                },
                timeout=600
            ),
            
            "masscan": ToolDefinition(
                name="masscan",
                command="masscan",
                docker_image="masscan:latest",
                docker_service="masscan-service",
                install_commands={
                    "apt": "apt-get update && apt-get install -y masscan",
                    "yum": "yum install -y masscan",
                    "brew": "brew install masscan"
                },
                fallback_tools=["nmap"],
                timeout=300
            ),
            
            # Vulnerability Scanning
            "nuclei": ToolDefinition(
                name="nuclei",
                command="nuclei",
                windows_command="nuclei.exe",
                docker_image="nuclei:latest", 
                docker_service="nuclei-service",
                install_commands={
                    "go": "go install -v github.com/projectdiscovery/nuclei/v2/cmd/nuclei@latest",
                    "brew": "brew install nuclei",
                    "apt": "wget -O nuclei.tar.gz https://github.com/projectdiscovery/nuclei/releases/latest/download/nuclei_*_linux_amd64.tar.gz && tar -xzf nuclei.tar.gz && mv nuclei /usr/local/bin/"
                },
                timeout=600
            ),
            
            "nikto": ToolDefinition(
                name="nikto",
                command="nikto",
                docker_image="nikto:latest",
                install_commands={
                    "apt": "apt-get update && apt-get install -y nikto",
                    "yum": "yum install -y nikto",
                    "brew": "brew install nikto"
                },
                fallback_tools=["nuclei"],
                timeout=300
            ),
            
            # Web Application Testing
            "sqlmap": ToolDefinition(
                name="sqlmap",
                command="sqlmap",
                docker_image="sqlmap:latest",
                docker_service="sqlmap-service",
                install_commands={
                    "apt": "apt-get update && apt-get install -y sqlmap",
                    "pip": "pip install sqlmapapi",
                    "git": "git clone https://github.com/sqlmapproject/sqlmap.git && cd sqlmap"
                },
                timeout=600
            ),
            
            "wpscan": ToolDefinition(
                name="wpscan",
                command="wpscan",
                docker_image="wpscan:latest",
                install_commands={
                    "gem": "gem install wpscan",
                    "apt": "apt-get update && apt-get install -y wpscan",
                    "docker": "docker pull wpscanteam/wpscan"
                },
                fallback_tools=["nuclei"],
                timeout=300
            ),
            
            # Directory/File Discovery
            "gobuster": ToolDefinition(
                name="gobuster",
                command="gobuster",
                windows_command="gobuster.exe",
                docker_image="gobuster:latest",
                docker_service="gobuster-service",
                install_commands={
                    "go": "go install github.com/OJ/gobuster/v3@latest",
                    "apt": "apt-get update && apt-get install -y gobuster",
                    "brew": "brew install gobuster"
                },
                timeout=300
            ),
            
            "dirb": ToolDefinition(
                name="dirb",
                command="dirb",
                docker_image="dirb:latest",
                install_commands={
                    "apt": "apt-get update && apt-get install -y dirb",
                    "yum": "yum install -y dirb"
                },
                fallback_tools=["gobuster"],
                timeout=300
            ),
            
            "ffuf": ToolDefinition(
                name="ffuf",
                command="ffuf",
                docker_image="ffuf:latest",
                install_commands={
                    "go": "go install github.com/ffuf/ffuf@latest",
                    "apt": "wget -O ffuf.tar.gz https://github.com/ffuf/ffuf/releases/latest/download/ffuf_*_linux_amd64.tar.gz && tar -xzf ffuf.tar.gz && mv ffuf /usr/local/bin/"
                },
                fallback_tools=["gobuster"],
                timeout=300
            ),
            
            "feroxbuster": ToolDefinition(
                name="feroxbuster",
                command="feroxbuster",
                docker_image="feroxbuster:latest",
                install_commands={
                    "cargo": "cargo install feroxbuster",
                    "apt": "wget -O feroxbuster.deb https://github.com/epi052/feroxbuster/releases/latest/download/feroxbuster_*_amd64.deb && dpkg -i feroxbuster.deb"
                },
                fallback_tools=["gobuster"],
                timeout=300
            ),
            
            # SSL/TLS Testing
            "testssl": ToolDefinition(
                name="testssl",
                command="testssl.sh",
                docker_image="testssl:latest",
                install_commands={
                    "git": "git clone https://github.com/drwetter/testssl.sh.git && cd testssl.sh && chmod +x testssl.sh",
                    "apt": "apt-get update && apt-get install -y testssl.sh"
                },
                timeout=180
            ),
            
            "sslyze": ToolDefinition(
                name="sslyze",
                command="sslyze",
                docker_image="sslyze:latest",
                install_commands={
                    "pip": "pip install sslyze",
                    "apt": "apt-get update && apt-get install -y sslyze"
                },
                fallback_tools=["testssl"],
                timeout=120
            ),
            
            # SMB/NetBIOS Enumeration
            "enum4linux": ToolDefinition(
                name="enum4linux",
                command="enum4linux",
                docker_image="enum4linux:latest",
                install_commands={
                    "apt": "apt-get update && apt-get install -y enum4linux",
                    "yum": "yum install -y enum4linux"
                },
                timeout=180
            ),
            
            "smbclient": ToolDefinition(
                name="smbclient",
                command="smbclient",
                docker_image="smbclient:latest",
                install_commands={
                    "apt": "apt-get update && apt-get install -y smbclient",
                    "yum": "yum install -y samba-client"
                },
                timeout=120
            ),
            
            # Exploitation Frameworks
            "metasploit": ToolDefinition(
                name="metasploit",
                command="msfconsole",
                docker_image="metasploit:latest",
                install_commands={
                    "curl": "curl https://raw.githubusercontent.com/rapid7/metasploit-omnibus/master/config/templates/metasploit-framework-wrappers/msfupdate.erb | bash",
                    "apt": "curl -sSL https://get.docker.com/ | sh && docker pull metasploitframework/metasploit-framework"
                },
                required_capabilities=["ruby"],
                timeout=1800
            ),
            
            "searchsploit": ToolDefinition(
                name="searchsploit",
                command="searchsploit",
                docker_image="searchsploit:latest",
                install_commands={
                    "git": "git clone https://github.com/offensive-security/exploitdb.git /opt/exploitdb && ln -sf /opt/exploitdb/searchsploit /usr/local/bin/searchsploit",
                    "apt": "apt-get update && apt-get install -y exploitdb"
                },
                timeout=60
            ),
            
            # Password/Hash Cracking
            "hashcat": ToolDefinition(
                name="hashcat",
                command="hashcat",
                windows_command="hashcat.exe",
                docker_image="hashcat:latest",
                install_commands={
                    "apt": "apt-get update && apt-get install -y hashcat",
                    "brew": "brew install hashcat"
                },
                timeout=3600
            ),
            
            "john": ToolDefinition(
                name="john",
                command="john",
                windows_command="john.exe",
                docker_image="john:latest",
                install_commands={
                    "apt": "apt-get update && apt-get install -y john",
                    "brew": "brew install john"
                },
                timeout=3600
            ),
            
            # Web Content Analysis
            "whatweb": ToolDefinition(
                name="whatweb",
                command="whatweb",
                docker_image="whatweb:latest",
                install_commands={
                    "apt": "apt-get update && apt-get install -y whatweb",
                    "gem": "gem install whatweb"
                },
                fallback_tools=["nuclei"],
                timeout=120
            )
        }
        
        return tools
    
    async def execute_tool(
        self, 
        tool_name: str, 
        target: str, 
        options: Dict[str, Any] = None,
        timeout: Optional[int] = None
    ) -> ToolExecutionResult:
        """Execute security tool with hybrid fallback strategy"""
        
        start_time = time.time()
        options = options or {}
        
        if tool_name not in self.tool_definitions:
            return ToolExecutionResult(
                status=ExecutionResult.NOT_AVAILABLE,
                method="none",
                error=f"Tool '{tool_name}' not defined",
                execution_time=time.time() - start_time
            )
        
        tool_def = self.tool_definitions[tool_name]
        timeout = timeout or tool_def.timeout
        
        logger.info(f"🎯 Executing {tool_name} on {target}")
        logger.info(f"📋 Execution priority: {self.execution_priority}")
        
        # Try each execution method in priority order
        last_error = ""
        
        for method in self.execution_priority:
            try:
                logger.info(f"🔄 Trying {method} execution for {tool_name}")
                
                if method == "docker" and self.docker_manager:
                    result = await self._execute_docker(tool_def, target, options, timeout)
                elif method == "native" and self.native_manager:
                    result = await self._execute_native(tool_def, target, options, timeout)
                elif method == "wsl" and self.wsl_manager:
                    result = await self._execute_wsl(tool_def, target, options, timeout)
                elif method == "simulation" and self.simulation_manager:
                    result = await self._execute_simulation(tool_def, target, options, timeout)
                else:
                    continue
                
                if result.status == ExecutionResult.SUCCESS:
                    result.execution_time = time.time() - start_time
                    self._update_stats(method, True, result.execution_time)
                    logger.info(f"✅ {tool_name} executed successfully via {method}")
                    return result
                else:
                    last_error = result.error
                    logger.warning(f"⚠️ {tool_name} failed via {method}: {result.error}")
                    
            except Exception as e:
                last_error = str(e)
                logger.warning(f"❌ {tool_name} execution failed via {method}: {e}")
        
        # All methods failed, try fallback tools
        if tool_def.fallback_tools:
            logger.info(f"🔄 Trying fallback tools for {tool_name}: {tool_def.fallback_tools}")
            for fallback_tool in tool_def.fallback_tools:
                try:
                    result = await self.execute_tool(fallback_tool, target, options, timeout)
                    if result.status == ExecutionResult.SUCCESS:
                        result.metadata['original_tool'] = tool_name
                        result.metadata['fallback_used'] = fallback_tool
                        logger.info(f"✅ Used fallback {fallback_tool} for {tool_name}")
                        return result
                except Exception as e:
                    logger.warning(f"⚠️ Fallback {fallback_tool} failed: {e}")
        
        # Complete failure
        execution_time = time.time() - start_time
        self._update_stats("failed", False, execution_time)
        
        return ToolExecutionResult(
            status=ExecutionResult.FAILED,
            method="none",
            error=f"All execution methods failed. Last error: {last_error}",
            execution_time=execution_time
        )
    
    async def _execute_docker(
        self, 
        tool_def: ToolDefinition, 
        target: str, 
        options: Dict[str, Any], 
        timeout: int
    ) -> ToolExecutionResult:
        """Execute tool via Docker container"""
        
        if not self.docker_manager:
            return ToolExecutionResult(
                status=ExecutionResult.NOT_AVAILABLE,
                method="docker",
                error="Docker manager not available"
            )
        
        try:
            # Check if service is available
            if tool_def.docker_service:
                if await self.docker_manager.is_service_available(tool_def.docker_service):
                    return await self.docker_manager.execute_service(
                        tool_def.docker_service, target, options, timeout
                    )
            
            # Use container image
            if tool_def.docker_image:
                return await self.docker_manager.execute_container(
                    tool_def.docker_image, tool_def.command, target, options, timeout
                )
            
            return ToolExecutionResult(
                status=ExecutionResult.NOT_AVAILABLE,
                method="docker",
                error="No Docker image or service defined"
            )
            
        except Exception as e:
            return ToolExecutionResult(
                status=ExecutionResult.FAILED,
                method="docker",
                error=f"Docker execution failed: {str(e)}"
            )
    
    async def _execute_native(
        self, 
        tool_def: ToolDefinition, 
        target: str, 
        options: Dict[str, Any], 
        timeout: int
    ) -> ToolExecutionResult:
        """Execute tool natively on the OS"""
        
        if not self.native_manager:
            return ToolExecutionResult(
                status=ExecutionResult.NOT_AVAILABLE,
                method="native",
                error="Native manager not available"
            )
        
        try:
            # Determine command based on OS
            if self.capabilities.get('native_windows') and tool_def.windows_command:
                command = tool_def.windows_command
            else:
                command = tool_def.command
            
            return await self.native_manager.execute_command(
                command, target, options, timeout
            )
            
        except Exception as e:
            return ToolExecutionResult(
                status=ExecutionResult.FAILED,
                method="native",
                error=f"Native execution failed: {str(e)}"
            )
    
    async def _execute_wsl(
        self, 
        tool_def: ToolDefinition, 
        target: str, 
        options: Dict[str, Any], 
        timeout: int
    ) -> ToolExecutionResult:
        """Execute tool via WSL"""
        
        if not self.wsl_manager:
            return ToolExecutionResult(
                status=ExecutionResult.NOT_AVAILABLE,
                method="wsl",
                error="WSL manager not available"
            )
        
        try:
            # Use WSL tool runner
            return await self.wsl_manager.run_tool_hybrid(
                tool_def.name, target, options, timeout
            )
            
        except Exception as e:
            return ToolExecutionResult(
                status=ExecutionResult.FAILED,
                method="wsl",
                error=f"WSL execution failed: {str(e)}"
            )
    
    async def _execute_simulation(
        self, 
        tool_def: ToolDefinition, 
        target: str, 
        options: Dict[str, Any], 
        timeout: int
    ) -> ToolExecutionResult:
        """Execute tool in simulation mode"""
        
        if not self.simulation_manager:
            return ToolExecutionResult(
                status=ExecutionResult.NOT_AVAILABLE,
                method="simulation",
                error="Simulation manager not available"
            )
        
        try:
            return await self.simulation_manager.simulate_tool(
                tool_def.name, target, options
            )
            
        except Exception as e:
            return ToolExecutionResult(
                status=ExecutionResult.FAILED,
                method="simulation",
                error=f"Simulation failed: {str(e)}"
            )
    
    def _update_stats(self, method: str, success: bool, execution_time: float):
        """Update execution statistics"""
        self.execution_stats['total_executions'] += 1
        
        if success:
            self.execution_stats['successful_executions'] += 1
        
        if method not in self.execution_stats['method_usage']:
            self.execution_stats['method_usage'][method] = {'count': 0, 'success': 0}
        
        self.execution_stats['method_usage'][method]['count'] += 1
        if success:
            self.execution_stats['method_usage'][method]['success'] += 1
        
        # Update average execution time
        total_time = (self.execution_stats['average_execution_time'] * 
                     (self.execution_stats['total_executions'] - 1) + execution_time)
        self.execution_stats['average_execution_time'] = total_time / self.execution_stats['total_executions']
    
    async def install_tool(self, tool_name: str) -> bool:
        """Auto-install missing tool"""
        
        if tool_name not in self.tool_definitions:
            logger.error(f"Tool {tool_name} not defined")
            return False
        
        tool_def = self.tool_definitions[tool_name]
        package_manager = environment_detector.get_recommended_package_manager()
        
        if not package_manager or package_manager not in tool_def.install_commands:
            logger.warning(f"No installation method available for {tool_name}")
            return False
        
        install_command = tool_def.install_commands[package_manager]
        
        try:
            logger.info(f"🔧 Installing {tool_name} using {package_manager}")
            
            if self.native_manager:
                result = await self.native_manager.execute_shell_command(install_command, timeout=600)
                return result.status == ExecutionResult.SUCCESS
            
            return False
            
        except Exception as e:
            logger.error(f"Failed to install {tool_name}: {e}")
            return False
    
    def get_available_tools(self) -> List[str]:
        """Get list of available tools"""
        return list(self.tool_definitions.keys())
    
    def get_execution_stats(self) -> Dict[str, Any]:
        """Get execution statistics"""
        return self.execution_stats.copy()
    
    def get_tool_info(self, tool_name: str) -> Optional[Dict[str, Any]]:
        """Get information about a specific tool"""
        if tool_name not in self.tool_definitions:
            return None
        
        tool_def = self.tool_definitions[tool_name]
        return {
            'name': tool_def.name,
            'command': tool_def.command,
            'docker_available': bool(tool_def.docker_image or tool_def.docker_service),
            'fallback_tools': tool_def.fallback_tools,
            'install_methods': list(tool_def.install_commands.keys()),
            'timeout': tool_def.timeout
        }

# Global hybrid execution engine
hybrid_engine = None

async def get_hybrid_engine() -> HybridExecutionEngine:
    """Get or create hybrid execution engine"""
    global hybrid_engine
    
    if hybrid_engine is None:
        hybrid_engine = HybridExecutionEngine()
        await hybrid_engine.initialize_managers()
    
    return hybrid_engine

async def execute_security_tool(
    tool_name: str, 
    target: str, 
    options: Dict[str, Any] = None,
    timeout: Optional[int] = None
) -> ToolExecutionResult:
    """Convenience function to execute security tool"""
    engine = await get_hybrid_engine()
    return await engine.execute_tool(tool_name, target, options, timeout)