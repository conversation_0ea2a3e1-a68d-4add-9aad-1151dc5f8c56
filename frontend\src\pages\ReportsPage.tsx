/**
 * Reports Page - Security report generation and management
 */
import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Tabs, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  FileText,
  Download,
  Share,
  Calendar,
  Filter,
  Search,
  Plus,
  AlertTriangle,
  CheckCircle,
  Clock,
  BarChart3,
  <PERSON><PERSON>hart,
  TrendingUp
} from 'lucide-react';

interface SecurityReport {
  id: string;
  title: string;
  type: 'vulnerability' | 'compliance' | 'penetration' | 'network';
  status: 'generated' | 'generating' | 'failed' | 'draft';
  createdAt: string;
  target: string;
  format: 'pdf' | 'html' | 'json' | 'csv';
  size: string;
  vulnerabilities: {
    critical: number;
    high: number;
    medium: number;
    low: number;
  };
  campaignId?: string;
}

export function ReportsPage() {
  const [searchTerm, setSearchTerm] = React.useState('');
  const [selectedType, setSelectedType] = React.useState('all');
  
  const [reports, setReports] = React.useState<SecurityReport[]>([
    {
      id: '1',
      title: 'Production Environment Security Assessment',
      type: 'penetration',
      status: 'generated',
      createdAt: '2024-01-20',
      target: '192.168.1.0/24',
      format: 'pdf',
      size: '2.4 MB',
      vulnerabilities: { critical: 2, high: 5, medium: 12, low: 8 },
      campaignId: 'camp-1'
    },
    {
      id: '2',
      title: 'Web Application OWASP Compliance Report',
      type: 'compliance',
      status: 'generated',
      createdAt: '2024-01-19',
      target: 'web-app.example.com',
      format: 'html',
      size: '1.8 MB',
      vulnerabilities: { critical: 1, high: 3, medium: 7, low: 4 },
      campaignId: 'camp-2'
    },
    {
      id: '3',
      title: 'Network Infrastructure Vulnerability Scan',
      type: 'vulnerability',
      status: 'generating',
      createdAt: '2024-01-20',
      target: '10.0.0.0/16',
      format: 'pdf',
      size: '0 MB',
      vulnerabilities: { critical: 0, high: 0, medium: 0, low: 0 }
    },
    {
      id: '4',
      title: 'Database Security Assessment',
      type: 'vulnerability',
      status: 'generated',
      createdAt: '2024-01-18',
      target: 'db.example.com',
      format: 'json',
      size: '845 KB',
      vulnerabilities: { critical: 0, high: 2, medium: 6, low: 3 }
    }
  ]);

  const reportTypes = [
    { id: 'all', name: 'All Reports', icon: FileText },
    { id: 'vulnerability', name: 'Vulnerability', icon: AlertTriangle },
    { id: 'compliance', name: 'Compliance', icon: CheckCircle },
    { id: 'penetration', name: 'Penetration Test', icon: BarChart3 },
    { id: 'network', name: 'Network', icon: PieChart }
  ];

  const getStatusColor = (status: SecurityReport['status']) => {
    switch (status) {
      case 'generated': return 'text-green-500';
      case 'generating': return 'text-blue-500';
      case 'failed': return 'text-red-500';
      case 'draft': return 'text-yellow-500';
      default: return 'text-gray-500';
    }
  };

  const getStatusIcon = (status: SecurityReport['status']) => {
    switch (status) {
      case 'generated': return <CheckCircle className="h-3 w-3" />;
      case 'generating': return <Clock className="h-3 w-3" />;
      case 'failed': return <AlertTriangle className="h-3 w-3" />;
      case 'draft': return <FileText className="h-3 w-3" />;
      default: return <FileText className="h-3 w-3" />;
    }
  };

  const getTypeIcon = (type: SecurityReport['type']) => {
    switch (type) {
      case 'vulnerability': return <AlertTriangle className="h-4 w-4" />;
      case 'compliance': return <CheckCircle className="h-4 w-4" />;
      case 'penetration': return <BarChart3 className="h-4 w-4" />;
      case 'network': return <PieChart className="h-4 w-4" />;
      default: return <FileText className="h-4 w-4" />;
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return 'text-red-600 bg-red-100 dark:bg-red-900/20';
      case 'high': return 'text-orange-600 bg-orange-100 dark:bg-orange-900/20';
      case 'medium': return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900/20';
      case 'low': return 'text-green-600 bg-green-100 dark:bg-green-900/20';
      default: return 'text-gray-600 bg-gray-100 dark:bg-gray-900/20';
    }
  };

  const filteredReports = reports.filter(report => {
    const matchesSearch = report.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         report.target.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesType = selectedType === 'all' || report.type === selectedType;
    return matchesSearch && matchesType;
  });

  const totalVulnerabilities = reports.reduce((acc, report) => ({
    critical: acc.critical + report.vulnerabilities.critical,
    high: acc.high + report.vulnerabilities.high,
    medium: acc.medium + report.vulnerabilities.medium,
    low: acc.low + report.vulnerabilities.low
  }), { critical: 0, high: 0, medium: 0, low: 0 });

  const handleDownloadReport = (reportId: string) => {
    console.log(`Downloading report: ${reportId}`);
  };

  const handleShareReport = (reportId: string) => {
    console.log(`Sharing report: ${reportId}`);
  };

  const handleGenerateReport = () => {
    console.log('Generating new report...');
  };

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Security Reports</h1>
          <p className="text-muted-foreground">
            Generate and manage security assessment reports
          </p>
        </div>
        <Button onClick={handleGenerateReport}>
          <Plus className="mr-2 h-4 w-4" />
          Generate Report
        </Button>
      </div>

      {/* Summary Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Reports</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{reports.length}</div>
            <p className="text-xs text-muted-foreground">
              {reports.filter(r => r.status === 'generated').length} completed
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Critical Issues</CardTitle>
            <AlertTriangle className="h-4 w-4 text-red-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{totalVulnerabilities.critical}</div>
            <p className="text-xs text-muted-foreground">Across all reports</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">High Severity</CardTitle>
            <TrendingUp className="h-4 w-4 text-orange-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-600">{totalVulnerabilities.high}</div>
            <p className="text-xs text-muted-foreground">Require attention</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Recent Reports</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {reports.filter(r => new Date(r.createdAt) >= new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)).length}
            </div>
            <p className="text-xs text-muted-foreground">This week</p>
          </CardContent>
        </Card>
      </div>

      {/* Search and Filters */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex items-center gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search reports..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Button variant="outline">
              <Filter className="mr-2 h-4 w-4" />
              Filter
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Report Type Tabs */}
      <Tabs value={selectedType} onValueChange={setSelectedType}>
        <TabsList className="grid w-full grid-cols-5">
          {reportTypes.map((type) => {
            const Icon = type.icon;
            return (
              <TabsTrigger key={type.id} value={type.id}>
                <Icon className="mr-2 h-4 w-4" />
                {type.name}
              </TabsTrigger>
            );
          })}
        </TabsList>

        <TabsContent value={selectedType} className="mt-6">
          {/* Reports List */}
          <div className="space-y-4">
            {filteredReports.map((report) => (
              <Card key={report.id} className="hover:shadow-lg transition-shadow">
                <CardContent className="pt-6">
                  <div className="flex items-start justify-between">
                    <div className="flex items-start gap-4 flex-1">
                      <div className="h-10 w-10 rounded-lg bg-primary/10 flex items-center justify-center">
                        {getTypeIcon(report.type)}
                      </div>
                      
                      <div className="flex-1 space-y-3">
                        <div>
                          <div className="flex items-center gap-3">
                            <h3 className="text-lg font-semibold">{report.title}</h3>
                            <Badge variant="outline" className={getStatusColor(report.status)}>
                              {getStatusIcon(report.status)}
                              {report.status}
                            </Badge>
                            <Badge variant="secondary" className="capitalize">
                              {report.type}
                            </Badge>
                          </div>
                          <p className="text-sm text-muted-foreground mt-1">
                            Target: {report.target} • Created: {report.createdAt} • Format: {report.format.toUpperCase()} • Size: {report.size}
                          </p>
                        </div>

                        {/* Vulnerability Summary */}
                        {report.status === 'generated' && (
                          <div className="flex items-center gap-4">
                            <span className="text-sm font-medium">Vulnerabilities:</span>
                            <div className="flex items-center gap-2">
                              {Object.entries(report.vulnerabilities).map(([severity, count]) => (
                                count > 0 && (
                                  <Badge 
                                    key={severity} 
                                    variant="secondary" 
                                    className={`text-xs ${getSeverityColor(severity)}`}
                                  >
                                    {severity}: {count}
                                  </Badge>
                                )
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Actions */}
                    <div className="flex items-center gap-2">
                      {report.status === 'generated' && (
                        <>
                          <Button 
                            size="sm" 
                            variant="outline"
                            onClick={() => handleDownloadReport(report.id)}
                          >
                            <Download className="mr-1 h-3 w-3" />
                            Download
                          </Button>
                          <Button 
                            size="sm" 
                            variant="outline"
                            onClick={() => handleShareReport(report.id)}
                          >
                            <Share className="mr-1 h-3 w-3" />
                            Share
                          </Button>
                        </>
                      )}
                      <Button size="sm" variant="outline">
                        View Details
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Empty State */}
          {filteredReports.length === 0 && (
            <Card>
              <CardContent className="pt-6">
                <div className="text-center py-8">
                  <FileText className="mx-auto h-12 w-12 text-muted-foreground" />
                  <h3 className="mt-2 text-sm font-semibold">No reports found</h3>
                  <p className="mt-1 text-sm text-muted-foreground">
                    {searchTerm ? 'Try adjusting your search terms.' : 'Generate your first security report to get started.'}
                  </p>
                  {!searchTerm && (
                    <div className="mt-6">
                      <Button onClick={handleGenerateReport}>
                        <Plus className="mr-2 h-4 w-4" />
                        Generate Report
                      </Button>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}