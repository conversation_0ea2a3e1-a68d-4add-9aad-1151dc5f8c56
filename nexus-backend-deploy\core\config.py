"""
Configuration Management for NexusScan Desktop Application
Handles application settings, user preferences, and environment variables.
"""

import os
import json
import logging
from pathlib import Path
from typing import Dict, Any, Optional
from dataclasses import dataclass, asdict

# Load environment variables from .env file if it exists
try:
    from dotenv import load_dotenv
    # Try to load .env from project root
    project_root = Path(__file__).parent.parent.parent
    env_file = project_root / ".env"
    if env_file.exists():
        load_dotenv(env_file)
        print(f"SUCCESS: Loaded environment variables from {env_file}")
    else:
        print("WARNING: No .env file found, using system environment variables only")
except ImportError:
    print("WARNING: python-dotenv not installed - .env files will not be loaded")

# Optional import - graceful degradation if not available
try:
    from cryptography.fernet import Fernet
    HAS_CRYPTOGRAPHY = True
except ImportError:
    HAS_CRYPTOGRAPHY = False
    print("WARNING: cryptography not installed - encryption features disabled")

logger = logging.getLogger(__name__)


@dataclass
class UIConfig:
    """UI configuration settings"""
    theme: str = "dark"
    primary_color: str = "#2196F3"  # Blue
    accent_color: str = "#FF5722"   # Orange
    danger_color: str = "#F44336"   # Red
    success_color: str = "#4CAF50"  # Green
    warning_color: str = "#FF9800"  # Amber
    window_width: int = 1200
    window_height: int = 800
    sidebar_width: int = 250


@dataclass
class SecurityConfig:
    """Security-related configuration"""
    encryption_key: Optional[str] = None
    api_timeout: int = 30
    max_scan_threads: int = 5
    default_scan_timeout: int = 300
    # Global execution mode - affects all security tools and AI services
    execution_mode: str = "simulation"  # "simulation" or "real"
    require_real_mode_confirmation: bool = True
    real_mode_confirmed: bool = False
    metrics_port: int = 8001  # Metrics server port (configurable to avoid conflicts)
    
    def __post_init__(self):
        """Load security settings from environment variables if not set"""
        # Load execution mode from environment variable
        env_execution_mode = os.getenv('EXECUTION_MODE')
        if env_execution_mode and env_execution_mode.lower() in ['simulation', 'real']:
            self.execution_mode = env_execution_mode.lower()
        
        # Load real mode confirmation requirement
        env_require_confirmation = os.getenv('REQUIRE_REAL_MODE_CONFIRMATION')
        if env_require_confirmation:
            self.require_real_mode_confirmation = env_require_confirmation.lower() in ['true', '1', 'yes']
        
        # Load metrics port from environment variable
        env_metrics_port = os.getenv('METRICS_PORT')
        if env_metrics_port:
            try:
                self.metrics_port = int(env_metrics_port)
            except ValueError:
                pass


@dataclass
class AIConfig:
    """AI service configuration"""
    openai_api_key: Optional[str] = None
    deepseek_api_key: Optional[str] = None
    anthropic_api_key: Optional[str] = None
    # Latest models as of June 2025 - prioritized by preference
    openai_model: str = "gpt-4o"  # Latest GPT-4 variant (gpt-4.1 available but newer)
    deepseek_model: str = "deepseek-chat"  # DeepSeek-V3-0324 (latest), very affordable
    deepseek_reasoning_model: str = "deepseek-reasoner"  # DeepSeek-R1-0528 for reasoning
    anthropic_model: str = "claude-3-5-sonnet-latest"  # Latest Claude 3.5 Sonnet (stable)
    max_tokens: int = 3000  # Reasonable limit for cost control
    temperature: float = 0.7
    preferred_provider: str = "openai"  # Remove non-existent o3 reference
    use_reasoning_model: bool = False  # Use reasoning model only when needed
    
    def __post_init__(self):
        """Load API keys from environment variables if not set"""
        # Load from environment variables if not already set
        if not self.openai_api_key:
            self.openai_api_key = os.getenv('OPENAI_API_KEY')
        if not self.deepseek_api_key:
            self.deepseek_api_key = os.getenv('DEEPSEEK_API_KEY')
        if not self.anthropic_api_key:
            self.anthropic_api_key = os.getenv('ANTHROPIC_API_KEY')


@dataclass
class DatabaseConfig:
    """Database configuration"""
    database_path: str = "data/nexusscan.db"
    backup_interval_hours: int = 24
    max_backups: int = 7
    enable_wal_mode: bool = True
    
    def __post_init__(self):
        """Apply environment variable overrides for WSL compatibility"""
        # Check for WSL environment and use native filesystem paths
        nexusscan_data_dir = os.getenv('NEXUSSCAN_DATA_DIR')
        nexusscan_db_path = os.getenv('NEXUSSCAN_DB_PATH')
        
        if nexusscan_db_path:
            self.database_path = nexusscan_db_path
        elif nexusscan_data_dir:
            self.database_path = os.path.join(nexusscan_data_dir, "nexusscan.db")
        else:
            # Auto-detect WSL environment and use WSL-native paths
            if os.path.exists('/proc/version'):
                try:
                    with open('/proc/version', 'r') as f:
                        if 'microsoft' in f.read().lower():
                            # WSL detected - use WSL-native path
                            wsl_data_dir = os.path.expanduser("~/.local/share/nexusscan")
                            os.makedirs(wsl_data_dir, exist_ok=True)
                            self.database_path = os.path.join(wsl_data_dir, "nexusscan.db")
                            logger.info(f"WSL detected - using WSL-native database path: {self.database_path}")
                except Exception:
                    pass


class ConfigManager:
    """Main configuration manager for NexusScan Desktop Application"""
    
    def __init__(self, config_dir: Optional[str] = None):
        """Initialize configuration manager"""
        self.config_dir = Path(config_dir) if config_dir else self._get_default_config_dir()
        self.config_file = self.config_dir / "config.json"
        self.secure_config_file = self.config_dir / "secure_config.json"
        
        # Initialize configuration
        self._ensure_config_dir()
        self._load_config()
        
        logger.info(f"Configuration loaded from {self.config_dir}")
    
    def _get_default_config_dir(self) -> Path:
        """Get default configuration directory"""
        if os.name == 'nt':  # Windows
            config_dir = Path(os.environ.get('APPDATA', '')) / "NexusScan"
        else:  # Linux/Mac
            config_dir = Path.home() / ".config" / "nexusscan"
        
        return config_dir
    
    def _ensure_config_dir(self):
        """Ensure configuration directory exists"""
        self.config_dir.mkdir(parents=True, exist_ok=True)
        
        # Create data directory attribute
        self.data_dir = self.config_dir / "data"
        self.data_dir.mkdir(parents=True, exist_ok=True)
        
        # Create data directories
        data_dir = Path("data")
        data_dir.mkdir(exist_ok=True)
        (data_dir / "campaigns").mkdir(exist_ok=True)
        (data_dir / "reports").mkdir(exist_ok=True)
        (data_dir / "templates").mkdir(exist_ok=True)
        (data_dir / "configs").mkdir(exist_ok=True)
        (data_dir / "uploads").mkdir(exist_ok=True)
    
    def _load_config(self):
        """Load configuration from files"""
        # Load main configuration
        if self.config_file.exists():
            with open(self.config_file, 'r') as f:
                config_data = json.load(f)
        else:
            config_data = {}
        
        # Initialize configuration objects
        self.ui = UIConfig(**config_data.get('ui', {}))
        self.security = SecurityConfig(**config_data.get('security', {}))
        self.database = DatabaseConfig(**config_data.get('database', {}))
        
        # Load secure configuration (encrypted)
        self._load_secure_config()
        
        # Generate encryption key if not exists
        if not self.security.encryption_key and HAS_CRYPTOGRAPHY:
            self.security.encryption_key = Fernet.generate_key().decode()
            self.save_config()
    
    def _load_secure_config(self):
        """Load encrypted configuration"""
        if self.secure_config_file.exists() and HAS_CRYPTOGRAPHY:
            try:
                with open(self.secure_config_file, 'rb') as f:
                    encrypted_data = f.read()
                
                if self.security.encryption_key:
                    cipher = Fernet(self.security.encryption_key.encode())
                    decrypted_data = cipher.decrypt(encrypted_data)
                    secure_config = json.loads(decrypted_data.decode())
                    
                    self.ai = AIConfig(**secure_config.get('ai', {}))
                else:
                    self.ai = AIConfig()
            except Exception as e:
                logger.warning(f"Failed to load secure config: {e}")
                self.ai = AIConfig()
        else:
            # Load from plain JSON if encryption not available
            if self.secure_config_file.exists() and not HAS_CRYPTOGRAPHY:
                try:
                    with open(self.secure_config_file, 'r') as f:
                        secure_config = json.load(f)
                    self.ai = AIConfig(**secure_config.get('ai', {}))
                except:
                    self.ai = AIConfig()
            else:
                self.ai = AIConfig()
    
    def save_config(self):
        """Save configuration to files"""
        try:
            # Save main configuration
            config_data = {
                'ui': asdict(self.ui),
                'security': {
                    'encryption_key': self.security.encryption_key,
                    'api_timeout': self.security.api_timeout,
                    'max_scan_threads': self.security.max_scan_threads,
                    'default_scan_timeout': self.security.default_scan_timeout
                },
                'database': asdict(self.database)
            }
            
            with open(self.config_file, 'w') as f:
                json.dump(config_data, f, indent=2)
            
            # Save secure configuration (encrypted)
            self._save_secure_config()
            
            logger.info("Configuration saved successfully")
            
        except Exception as e:
            logger.error(f"Failed to save configuration: {e}")
    
    def _save_secure_config(self):
        """Save encrypted configuration"""
        try:
            secure_data = {
                'ai': asdict(self.ai)
            }
            
            if self.security.encryption_key and HAS_CRYPTOGRAPHY:
                # Save encrypted
                cipher = Fernet(self.security.encryption_key.encode())
                encrypted_data = cipher.encrypt(json.dumps(secure_data).encode())
                
                with open(self.secure_config_file, 'wb') as f:
                    f.write(encrypted_data)
            else:
                # Save as plain JSON if encryption not available
                with open(self.secure_config_file, 'w') as f:
                    json.dump(secure_data, f, indent=2)
                    
        except Exception as e:
            logger.error(f"Failed to save secure configuration: {e}")
    
    def get_database_path(self) -> str:
        """Get database file path"""
        return self.database.database_path
    
    def get_api_key(self, service: str) -> Optional[str]:
        """Get API key for specified service"""
        if service.lower() == 'openai':
            return self.ai.openai_api_key
        elif service.lower() == 'deepseek':
            return self.ai.deepseek_api_key
        return None
    
    def set_api_key(self, service: str, api_key: str):
        """Set API key for specified service"""
        if service.lower() == 'openai':
            self.ai.openai_api_key = api_key
        elif service.lower() == 'deepseek':
            self.ai.deepseek_api_key = api_key
        
        self.save_config()
    
    def get_setting(self, category: str, key: str, default: Any = None) -> Any:
        """Get configuration setting"""
        config_obj = getattr(self, category, None)
        if config_obj:
            return getattr(config_obj, key, default)
        return default
    
    def set_setting(self, category: str, key: str, value: Any):
        """Set configuration setting"""
        config_obj = getattr(self, category, None)
        if config_obj:
            setattr(config_obj, key, value)
            self.save_config()
    
    def get(self, key: str, default: Any = None) -> Any:
        """Get configuration value using dot notation (e.g., 'reporting.output_directory')"""
        try:
            keys = key.split('.')
            if len(keys) == 1:
                # Simple key lookup
                return getattr(self, key, default)
            elif len(keys) == 2:
                # Category.key lookup
                category, setting_key = keys
                config_obj = getattr(self, category, None)
                if config_obj:
                    return getattr(config_obj, setting_key, default)
            return default
        except:
            return default
    
    def is_real_mode(self) -> bool:
        """Check if application is in real execution mode"""
        return self.security.execution_mode.lower() == "real"
    
    def is_simulation_mode(self) -> bool:
        """Check if application is in simulation mode"""
        return self.security.execution_mode.lower() == "simulation"
    
    def set_execution_mode(self, mode: str) -> None:
        """Set global execution mode for entire application"""
        if mode.lower() not in ["simulation", "real"]:
            raise ValueError("Execution mode must be 'simulation' or 'real'")
        
        self.security.execution_mode = mode.lower()
        
        # Reset confirmation when changing modes
        if mode.lower() == "simulation":
            self.security.real_mode_confirmed = False
        
        self.save_config()
        logger.info(f"Global execution mode set to: {mode.upper()}")
    
    def confirm_real_mode(self) -> bool:
        """Confirm real mode execution for entire application"""
        if not self.security.require_real_mode_confirmation:
            self.security.real_mode_confirmed = True
            return True
        
        # In production, this would integrate with frontend confirmation dialog
        logger.warning("Real mode confirmation required for global application settings")
        logger.warning("Set require_real_mode_confirmation=False to bypass (development only)")
        
        # For now, require manual confirmation through config
        return self.security.real_mode_confirmed
    
    def enable_real_mode_bypass(self, bypass: bool = True) -> None:
        """Enable/disable real mode confirmation requirement (development use)"""
        self.security.require_real_mode_confirmation = not bypass
        if bypass:
            self.security.real_mode_confirmed = True
        self.save_config()
        logger.warning(f"Real mode confirmation requirement: {'DISABLED' if bypass else 'ENABLED'}")


# Backwards compatibility alias
Config = ConfigManager