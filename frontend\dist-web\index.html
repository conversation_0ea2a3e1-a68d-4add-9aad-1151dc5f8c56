<!doctype html>
<html lang="en" class="dark">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/assets/icons/nexus-icon.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="NexusScan Desktop - Professional Penetration Testing Platform" />
    <meta name="keywords" content="penetration testing, security tools, vulnerability assessment, AI-powered security" />
    <meta name="author" content="NexusScan Team" />
    
    <!-- CSP for security (will be overridden by Electron in production) -->
    <meta http-equiv="Content-Security-Policy" content="default-src 'self' 'unsafe-inline' data: blob:; script-src 'self' 'unsafe-eval' 'unsafe-inline'; connect-src 'self' ws: wss: http://************:8090 ws://************:8090; img-src 'self' data: blob:; style-src 'self' 'unsafe-inline'; font-src 'self' data:;">
    
    <!-- Theme configuration -->
    <meta name="color-scheme" content="dark light">
    <meta name="theme-color" content="#0ea5e9" />
    
    <!-- Preload critical fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@300;400;500;600&display=swap" rel="stylesheet">
    
    <title>NexusScan Desktop</title>
    
    <style>
      /* Critical CSS for loading state */
      .loading-container {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, #0c4a6e 0%, #075985 50%, #0369a1 100%);
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        z-index: 9999;
        font-family: 'Inter', system-ui, sans-serif;
      }
      
      .loading-logo {
        width: 120px;
        height: 120px;
        margin-bottom: 2rem;
        animation: pulse 2s ease-in-out infinite;
      }
      
      .loading-text {
        color: #e0f2fe;
        font-size: 1.5rem;
        font-weight: 600;
        margin-bottom: 1rem;
        text-align: center;
      }
      
      .loading-subtext {
        color: #bae6fd;
        font-size: 0.875rem;
        text-align: center;
        margin-bottom: 2rem;
      }
      
      .loading-progress {
        width: 300px;
        height: 4px;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 2px;
        overflow: hidden;
      }
      
      .loading-progress-bar {
        height: 100%;
        background: linear-gradient(90deg, #7dd3fc, #38bdf8, #0ea5e9);
        border-radius: 2px;
        animation: loading 2s ease-in-out infinite;
      }
      
      @keyframes pulse {
        0%, 100% { opacity: 0.8; transform: scale(1); }
        50% { opacity: 1; transform: scale(1.05); }
      }
      
      @keyframes loading {
        0% { transform: translateX(-100%); }
        50% { transform: translateX(0%); }
        100% { transform: translateX(100%); }
      }
      
      /* Hide loading when app is ready */
      .app-ready .loading-container {
        display: none;
      }
      
      /* Critical app styles */
      * {
        box-sizing: border-box;
      }
      
      body {
        margin: 0;
        padding: 0;
        font-family: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
        background: #0f172a;
        color: #e2e8f0;
        overflow: hidden;
      }
      
      #root {
        width: 100vw;
        height: 100vh;
        overflow: hidden;
      }
      
      /* Dark theme variables */
      :root.dark {
        --background: 222.2 84% 4.9%;
        --foreground: 210 40% 98%;
        --card: 222.2 84% 4.9%;
        --card-foreground: 210 40% 98%;
        --popover: 222.2 84% 4.9%;
        --popover-foreground: 210 40% 98%;
        --primary: 210 40% 98%;
        --primary-foreground: 222.2 84% 4.9%;
        --secondary: 217.2 32.6% 17.5%;
        --secondary-foreground: 210 40% 98%;
        --muted: 217.2 32.6% 17.5%;
        --muted-foreground: 215 20.2% 65.1%;
        --accent: 217.2 32.6% 17.5%;
        --accent-foreground: 210 40% 98%;
        --destructive: 0 62.8% 30.6%;
        --destructive-foreground: 210 40% 98%;
        --border: 217.2 32.6% 17.5%;
        --input: 217.2 32.6% 17.5%;
        --ring: 212.7 26.8% 83.9%;
        --radius: 0.5rem;
      }
    </style>
    <script type="module" crossorigin src="/assets/index-fxrVuJdX.js"></script>
    <link rel="modulepreload" crossorigin href="/assets/react-D5Q8GhuV.js">
    <link rel="modulepreload" crossorigin href="/assets/ui-PpgiV5M2.js">
    <link rel="modulepreload" crossorigin href="/assets/state-fRPoTWzM.js">
    <link rel="stylesheet" crossorigin href="/assets/index-Cxh0pwfP.css">
  </head>
  <body>
    <!-- Loading screen -->
    <div class="loading-container" id="loading-screen">
      <div class="loading-logo">
        <svg viewBox="0 0 120 120" fill="none" xmlns="http://www.w3.org/2000/svg">
          <circle cx="60" cy="60" r="50" stroke="#38bdf8" stroke-width="3" fill="none"/>
          <circle cx="60" cy="60" r="35" stroke="#7dd3fc" stroke-width="2" fill="none"/>
          <circle cx="60" cy="60" r="20" stroke="#bae6fd" stroke-width="2" fill="none"/>
          <circle cx="60" cy="60" r="8" fill="#0ea5e9"/>
          <path d="M60 10 L70 30 L60 25 L50 30 Z" fill="#38bdf8"/>
          <path d="M110 60 L90 50 L95 60 L90 70 Z" fill="#38bdf8"/>
          <path d="M60 110 L50 90 L60 95 L70 90 Z" fill="#38bdf8"/>
          <path d="M10 60 L30 70 L25 60 L30 50 Z" fill="#38bdf8"/>
        </svg>
      </div>
      <div class="loading-text">NexusScan Desktop</div>
      <div class="loading-subtext">Professional Penetration Testing Platform</div>
      <div class="loading-progress">
        <div class="loading-progress-bar"></div>
      </div>
    </div>
    
    <!-- React app root -->
    <div id="root"></div>
    
    <!-- Development notification -->
    <script>
      // Show development mode notification
      if (window.location.hostname === 'localhost') {
        console.log('🚀 NexusScan Desktop - Development Mode');
        console.log('🔧 Backend:', 'http://************:8090');
        console.log('🔗 WebSocket:', 'wss://************:8090/ws');
        
        // Check if Electron API is available
        if (window.electronAPI) {
          console.log('✅ Electron API available');
        } else {
          console.warn('⚠️ Electron API not available - running in browser mode');
        }
      }
      
      // Hide loading screen when app is ready
      window.addEventListener('appReady', () => {
        document.body.classList.add('app-ready');
      });
      
      // Fallback to hide loading screen after 5 seconds
      setTimeout(() => {
        document.body.classList.add('app-ready');
      }, 5000);
    </script>
    
    <!-- Main application script -->
  </body>
</html>