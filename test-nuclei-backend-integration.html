<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Nuclei Backend Integration Test</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #fafafa;
        }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .info { color: #007bff; }
        .warning { color: #ffc107; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        button:disabled { background: #6c757d; cursor: not-allowed; }
        pre {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            border-left: 4px solid #007bff;
        }
        .severity-counts {
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            gap: 10px;
            margin: 15px 0;
        }
        .severity-card {
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            color: white;
            font-weight: bold;
        }
        .critical { background: #dc3545; }
        .high { background: #fd7e14; }
        .medium { background: #ffc107; color: #000; }
        .low { background: #28a745; }
        .info { background: #17a2b8; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Nuclei Backend Integration Test</h1>
        <p class="info">Testing the fixed Nuclei JSON parsing with live Contabo backend</p>
        
        <div class="test-section">
            <h2>🌐 Backend Connectivity</h2>
            <button onclick="testBackendHealth()">Test Backend Health</button>
            <div id="health-result"></div>
        </div>
        
        <div class="test-section">
            <h2>🔧 Nuclei Tool Test</h2>
            <button onclick="testNucleiTool()" id="nuclei-btn">Test Nuclei Scan</button>
            <button onclick="stopTest()" id="stop-btn" disabled>Stop Test</button>
            <div id="nuclei-result"></div>
        </div>
        
        <div class="test-section">
            <h2>📊 Parsed Results</h2>
            <div id="parsed-results"></div>
        </div>
        
        <div class="test-section">
            <h2>🐛 Debug Information</h2>
            <div id="debug-info"></div>
        </div>
    </div>

    <script>
        const BACKEND_URL = 'http://************:8090';
        let currentTest = null;
        
        function log(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            const timestamp = new Date().toLocaleTimeString();
            const className = type;
            element.innerHTML += `<div class="${className}">[${timestamp}] ${message}</div>`;
            element.scrollTop = element.scrollHeight;
        }
        
        async function testBackendHealth() {
            log('health-result', '🔍 Testing backend health...', 'info');
            
            try {
                const response = await fetch(`${BACKEND_URL}/api/health`);
                const data = await response.json();
                
                if (response.ok) {
                    log('health-result', '✅ Backend is healthy!', 'success');
                    log('health-result', `Response: ${JSON.stringify(data, null, 2)}`, 'info');
                } else {
                    log('health-result', '❌ Backend health check failed', 'error');
                    log('health-result', `Status: ${response.status}`, 'error');
                }
            } catch (error) {
                log('health-result', `❌ Backend connection failed: ${error.message}`, 'error');
            }
        }
        
        async function testNucleiTool() {
            const btn = document.getElementById('nuclei-btn');
            const stopBtn = document.getElementById('stop-btn');
            
            btn.disabled = true;
            stopBtn.disabled = false;
            
            log('nuclei-result', '🚀 Starting Nuclei scan test...', 'info');
            log('debug-info', '🔧 Testing JSON parsing fix implementation', 'info');
            
            try {
                // Test configuration
                const config = {
                    target: 'http://scanme.nmap.org',
                    timeout: 300,
                    threads: 25,
                    output_format: 'json',
                    options: {
                        templates: {
                            categories: ['cves', 'vulnerabilities'],
                            severities: ['critical', 'high', 'medium']
                        },
                        rate_limit: 150,
                        bulk_size: 25
                    }
                };
                
                log('nuclei-result', `📡 Sending request to: ${BACKEND_URL}/api/tools/nuclei/scan`, 'info');
                log('nuclei-result', `🎯 Target: ${config.target}`, 'info');
                
                const response = await fetch(`${BACKEND_URL}/api/tools/nuclei/scan`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(config)
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const result = await response.json();
                log('nuclei-result', '✅ Nuclei scan completed!', 'success');
                
                // Apply our JSON parsing fix
                const parsedResults = parseNucleiResults({ results: result.data });
                
                // Display parsed results
                displayParsedResults(parsedResults);
                
                // Show debug info
                log('debug-info', '📥 Raw Backend Response:', 'info');
                document.getElementById('debug-info').innerHTML += 
                    `<pre>${JSON.stringify(result, null, 2)}</pre>`;
                
                log('debug-info', '📤 Parsed Frontend Format:', 'info');
                document.getElementById('debug-info').innerHTML += 
                    `<pre>${JSON.stringify(parsedResults, null, 2)}</pre>`;
                
            } catch (error) {
                log('nuclei-result', `❌ Test failed: ${error.message}`, 'error');
                log('debug-info', `Error details: ${error.stack}`, 'error');
            } finally {
                btn.disabled = false;
                stopBtn.disabled = true;
            }
        }
        
        function parseNucleiResults(progressUpdate) {
            let parsedResults = progressUpdate.results;
            
            // Handle nested backend response structure (our fix)
            if (progressUpdate.results?.parsed_results) {
                const backendData = progressUpdate.results.parsed_results;
                
                // Extract severity counts from the nested structure
                const severityCounts = backendData.statistics?.severity_counts || {};
                
                // Transform to frontend-expected format
                parsedResults = {
                    critical: severityCounts.critical || 0,
                    high: severityCounts.high || 0,
                    medium: severityCounts.medium || 0,
                    low: severityCounts.low || 0,
                    info: severityCounts.info || 0,
                    vulnerabilities: backendData.vulnerabilities || [],
                    detailed_findings: backendData.detailed_findings || [],
                    raw_backend_data: progressUpdate.results
                };
            }
            
            return parsedResults;
        }
        
        function displayParsedResults(results) {
            const container = document.getElementById('parsed-results');
            
            // Severity counts display
            const severityHtml = `
                <h3>📊 Severity Distribution</h3>
                <div class="severity-counts">
                    <div class="severity-card critical">
                        <div>Critical</div>
                        <div>${results.critical || 0}</div>
                    </div>
                    <div class="severity-card high">
                        <div>High</div>
                        <div>${results.high || 0}</div>
                    </div>
                    <div class="severity-card medium">
                        <div>Medium</div>
                        <div>${results.medium || 0}</div>
                    </div>
                    <div class="severity-card low">
                        <div>Low</div>
                        <div>${results.low || 0}</div>
                    </div>
                    <div class="severity-card info">
                        <div>Info</div>
                        <div>${results.info || 0}</div>
                    </div>
                </div>
            `;
            
            container.innerHTML = severityHtml;
            
            // Vulnerabilities summary
            const vulnCount = (results.vulnerabilities || []).length;
            const findingsCount = (results.detailed_findings || []).length;
            
            container.innerHTML += `
                <h3>🔍 Findings Summary</h3>
                <p class="info">Vulnerabilities: ${vulnCount}</p>
                <p class="info">Detailed Findings: ${findingsCount}</p>
            `;
            
            if (vulnCount > 0) {
                container.innerHTML += `
                    <h3>⚠️ Sample Vulnerabilities</h3>
                    <pre>${JSON.stringify(results.vulnerabilities.slice(0, 3), null, 2)}</pre>
                `;
            } else {
                container.innerHTML += `
                    <p class="success">✅ No vulnerabilities found - target appears secure!</p>
                `;
            }
        }
        
        function stopTest() {
            if (currentTest) {
                currentTest.abort();
                log('nuclei-result', '🛑 Test stopped by user', 'warning');
            }
        }
        
        // Auto-test on load
        window.onload = function() {
            log('health-result', '🚀 Page loaded - ready for testing', 'info');
            log('nuclei-result', '💡 Click "Test Nuclei Scan" to verify the JSON parsing fix', 'info');
        };
    </script>
</body>
</html>
