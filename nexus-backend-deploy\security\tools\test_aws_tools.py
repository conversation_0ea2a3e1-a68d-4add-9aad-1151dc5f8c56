#!/usr/bin/env python3
"""
AWS Tool Verification Script for NexusScan Phase 2E
Tests all 22 tools with real execution on AWS EC2
"""

import sys
import os
import asyncio
import subprocess
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from security.tools.tool_loader import initialize_tools, get_tools_status
from security.tools.tool_registry import tool_registry, ScanOptions

async def test_aws_tools():
    """Test all tools with real execution on AWS"""
    print("=" * 80)
    print("NexusScan Phase 2E - AWS Tool Verification")
    print("=" * 80)
    
    # Initialize tools
    print("\n1. Loading all tools...")
    loading_report = initialize_tools()
    print(f"   - Total modules loaded: {loading_report['total_loaded']}")
    print(f"   - Available tools: {loading_report['available_tools']}")
    print(f"   - Failed imports: {len(loading_report['failed_imports'])}")
    
    if loading_report['failed_imports']:
        print("\n   Failed imports:")
        for failed in loading_report['failed_imports']:
            print(f"   - {failed['module']}: {failed['error']}")
    
    # Get all registered tools
    all_tools = tool_registry.list_all_tools()
    print(f"\n2. Total tools registered: {len(all_tools)}")
    
    # Test binary availability first
    print("\n3. Binary Availability Check:")
    binaries_to_check = {
        'nmap': 'nmap',
        'nuclei': 'nuclei', 
        'sqlmap': 'sqlmap',
        'wpscan': 'wpscan',
        'ffuf': 'ffuf',
        'feroxbuster': 'feroxbuster',
        'enum4linux': 'enum4linux',
        'sslyze': 'sslyze',
        'msfconsole': 'metasploit',
        'searchsploit': 'searchsploit',
        'dirb': 'dirb',
        'whatweb': 'whatweb',
        'hashcat': 'hashcat',
        'john': 'john',
        'smbclient': 'smbclient',
        'testssl': 'testssl'
    }
    
    available_binaries = {}
    for binary, tool_name in binaries_to_check.items():
        try:
            result = subprocess.run(['which', binary], capture_output=True, timeout=5)
            if result.returncode == 0:
                available_binaries[tool_name] = result.stdout.decode().strip()
                print(f"   ✅ {binary}: {available_binaries[tool_name]}")
            else:
                print(f"   ❌ {binary}: Not found")
        except Exception as e:
            print(f"   ❌ {binary}: Error - {e}")
    
    print(f"\n   Binary availability: {len(available_binaries)}/{len(binaries_to_check)} tools")
    
    # Test tool availability through our registry
    print("\n4. Tool Registry Availability:")
    available_tools = []
    for tool_name in all_tools.keys():
        if tool_registry.is_tool_available(tool_name):
            available_tools.append(tool_name)
            print(f"   ✅ {tool_name}")
        else:
            print(f"   ❌ {tool_name}")
    
    print(f"\n   Registry availability: {len(available_tools)}/{len(all_tools)} tools")
    
    # Test real execution for core tools
    print("\n5. Real Execution Tests:")
    execution_tests = [
        {
            'tool': 'nmap',
            'target': '127.0.0.1',
            'options': {'scan_type': 'ping'}
        },
        {
            'tool': 'performance_monitor',
            'target': 'system',
            'options': {'duration': 5}
        },
        {
            'tool': 'environment_detector', 
            'target': 'system',
            'options': {}
        },
        {
            'tool': 'health_checker',
            'target': 'system',
            'options': {}
        }
    ]
    
    successful_executions = 0
    for test in execution_tests:
        tool_name = test['tool']
        print(f"\n   Testing {tool_name}...")
        
        try:
            tool_instance = tool_registry.get_tool(tool_name)
            if tool_instance:
                scan_options = ScanOptions(
                    target=test['target'],
                    custom_options=test['options']
                )
                
                print(f"     - Target: {test['target']}")
                print(f"     - Options: {test['options']}")
                print("     - Executing...")
                
                # Execute the scan
                result = await tool_instance.execute_scan(scan_options)
                
                if result.status.value == "completed":
                    print(f"     ✅ SUCCESS: {tool_name} executed successfully")
                    print(f"     - Duration: {result.duration_seconds:.2f}s")
                    print(f"     - Vulnerabilities found: {len(result.vulnerabilities)}")
                    successful_executions += 1
                else:
                    print(f"     ❌ FAILED: {tool_name} status: {result.status.value}")
                    if result.errors:
                        print(f"     - Errors: {result.errors}")
            else:
                print(f"     ❌ FAILED: Could not get tool instance")
                
        except Exception as e:
            print(f"     ❌ EXCEPTION: {e}")
    
    print(f"\n   Execution tests: {successful_executions}/{len(execution_tests)} successful")
    
    # Environment analysis
    print("\n6. Environment Analysis:")
    
    # Check if we're on AWS
    try:
        result = subprocess.run(['curl', '-s', '--max-time', '2', 
                               'http://***************/latest/meta-data/instance-id'], 
                              capture_output=True)
        if result.returncode == 0:
            instance_id = result.stdout.decode().strip()
            print(f"   ✅ AWS EC2 Instance: {instance_id}")
        else:
            print("   ❌ Not running on AWS EC2")
    except:
        print("   ❌ Could not determine AWS status")
    
    # Check Python environment
    print(f"   - Python version: {sys.version}")
    print(f"   - Working directory: {os.getcwd()}")
    print(f"   - Virtual env: {os.environ.get('VIRTUAL_ENV', 'Not activated')}")
    
    # Check critical Python packages
    critical_packages = ['psutil', 'aiohttp', 'fastapi', 'sqlalchemy']
    for package in critical_packages:
        try:
            __import__(package)
            print(f"   ✅ {package}: Available")
        except ImportError:
            print(f"   ❌ {package}: Missing")
    
    # Final assessment
    print("\n" + "=" * 80)
    print("PHASE 2E ASSESSMENT")
    print("=" * 80)
    
    total_tools = len(all_tools)
    registry_available = len(available_tools)
    binary_available = len(available_binaries)
    
    print(f"📊 Tool Statistics:")
    print(f"   - Total tools registered: {total_tools}")
    print(f"   - Registry available: {registry_available} ({registry_available/total_tools*100:.1f}%)")
    print(f"   - Binaries available: {binary_available}/{len(binaries_to_check)} ({binary_available/len(binaries_to_check)*100:.1f}%)")
    print(f"   - Execution tests passed: {successful_executions}/{len(execution_tests)}")
    
    # Calculate readiness score
    readiness_score = (
        (registry_available / total_tools) * 40 +  # 40% weight for registry
        (binary_available / len(binaries_to_check)) * 40 +  # 40% weight for binaries
        (successful_executions / len(execution_tests)) * 20   # 20% weight for execution
    ) * 100
    
    print(f"\n🎯 AWS Readiness Score: {readiness_score:.1f}%")
    
    if readiness_score >= 80:
        print("✅ STATUS: AWS PRODUCTION READY")
        print("   - Sufficient tools available for real scanning")
        print("   - Binary installation successful")
        print("   - Real execution confirmed")
    elif readiness_score >= 60:
        print("⚠️  STATUS: AWS PARTIALLY READY")
        print("   - Some tools missing, but core functionality available")
        print("   - May need additional binary installations")
    else:
        print("❌ STATUS: AWS NOT READY")
        print("   - Significant tool installation required")
        print("   - Run aws_tool_installation.sh first")
    
    # Recommendations
    print(f"\n📋 Recommendations:")
    if binary_available < len(binaries_to_check):
        missing_binaries = [binary for binary, tool in binaries_to_check.items() 
                          if tool not in available_binaries]
        print(f"   - Install missing binaries: {', '.join(missing_binaries)}")
        print("   - Run: bash aws_tool_installation.sh")
    
    if registry_available < total_tools:
        print("   - Some tools not registering properly")
        print("   - Check tool import errors in loading report")
    
    if successful_executions < len(execution_tests):
        print("   - Some tools failing execution")
        print("   - Check error logs and permissions")
    
    print(f"\n🚀 Next Steps:")
    if readiness_score >= 80:
        print("   1. Proceed with Phase 2F (additional tools)")
        print("   2. Begin frontend development")
        print("   3. Implement production deployment")
    else:
        print("   1. Fix binary installation issues")
        print("   2. Resolve tool registration problems")
        print("   3. Re-run this verification script")
    
    return {
        'total_tools': total_tools,
        'available_tools': registry_available,
        'available_binaries': binary_available,
        'successful_executions': successful_executions,
        'readiness_score': readiness_score,
        'aws_ready': readiness_score >= 80
    }

if __name__ == "__main__":
    results = asyncio.run(test_aws_tools())