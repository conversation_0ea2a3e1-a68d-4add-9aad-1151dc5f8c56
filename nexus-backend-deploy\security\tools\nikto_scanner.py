"""
Nikto Scanner Integration for NexusScan Desktop Application
Web server vulnerability scanner using nikto.
"""

import json
import asyncio
import logging
import subprocess
import re
from datetime import datetime
from typing import Dict, List, Optional, Callable, Any
from dataclasses import dataclass

from security.tools.tool_registry import (
    ToolMetadata, ToolCategory, ToolCapabilities,
    ScanOptions, ScanResult, ToolStatus, register_tool
)
from security.tools.base_scanner import BaseScanner

logger = logging.getLogger(__name__)


@dataclass
class NiktoScanOptions(ScanOptions):
    """Nikto-specific scan options"""
    port: int = 80
    ssl: bool = False
    plugins: List[str] = None
    user_agent: str = ""
    timeout: int = 10
    max_scan_time: int = 3600
    evasion: str = ""
    tuning: str = ""
    format: str = "json"
    output_file: str = ""
    
    def __post_init__(self):
        super().__post_init__()
        if self.plugins is None:
            self.plugins = []


@dataclass
class NiktoVulnerability:
    """Nikto vulnerability finding"""
    id: str
    osvdb: str
    method: str
    uri: str
    description: str
    severity: str = "medium"
    references: List[str] = None
    
    def __post_init__(self):
        if self.references is None:
            self.references = []


class NiktoScanner(BaseScanner):
    """Nikto web server vulnerability scanner"""
    
    def __init__(self):
        self.tool_name = "nikto"
        self.scan_types = ["comprehensive", "fast", "plugins"]
        self.current_process = None
        super().__init__()
        
    def get_metadata(self) -> ToolMetadata:
        """Get tool metadata"""
        return ToolMetadata(
            name="Nikto",
            category=ToolCategory.WEB_SCANNER,
            description="Web server vulnerability scanner",
            version=self._get_version(),
            author="Chris Sullo",
            dependencies=["perl"],
            capabilities=ToolCapabilities(
                scan_types=self.scan_types,
                output_formats=["json", "txt", "csv", "html"],
                authentication_support=True,
                custom_headers_support=True,
                proxy_support=True,
                rate_limiting=True
            )
        )
    
    def _get_version(self) -> str:
        """Get Nikto version"""
        try:
            result = subprocess.run(
                ["nikto", "-Version"],
                capture_output=True,
                text=True,
                timeout=10
            )
            if result.returncode == 0:
                version_match = re.search(r"Nikto v([\d.]+)", result.stdout)
                if version_match:
                    return version_match.group(1)
            return "2.1.5"  # Default fallback
        except Exception as e:
            logger.warning(f"Could not get Nikto version: {e}")
            return "2.1.5"
    
    def is_available(self) -> bool:
        """Check if Nikto is available"""
        try:
            result = subprocess.run(
                ["nikto", "-Version"],
                capture_output=True,
                text=True,
                timeout=10
            )
            return result.returncode == 0
        except (FileNotFoundError, subprocess.TimeoutExpired):
            return False
    
    def check_native_availability(self) -> bool:
        """Check if Nikto native tool is available"""
        return self.is_available()
    
    async def execute_native_scan(self, options: ScanOptions,
                                 progress_callback: Optional[Callable[[float, str], None]] = None) -> ScanResult:
        """Execute native Nikto scan"""
        if not isinstance(options, NiktoScanOptions):
            options = NiktoScanOptions(target=options.target)
        
        return await self.scan(options.target, options, progress_callback)
    
    async def scan(self, 
                   target: str, 
                   options: Optional[NiktoScanOptions] = None,
                   progress_callback: Optional[Callable] = None) -> ScanResult:
        """Perform Nikto scan"""
        if options is None:
            options = NiktoScanOptions(target=target)
        
        scan_id = f"nikto_{target}_{int(datetime.now().timestamp())}"
        start_time = datetime.now()
        
        logger.info(f"Starting Nikto scan: {scan_id}")
        
        if progress_callback:
            await progress_callback(0.1, "Initializing", "Preparing Nikto scan")
        
        try:
            # Build command
            command = self._build_command(target, options)
            
            if progress_callback:
                await progress_callback(0.2, "Scanning", "Running Nikto vulnerability scan")
            
            # Execute scan
            process_result = await self._execute_command(command, progress_callback)
            
            if progress_callback:
                await progress_callback(0.8, "Processing", "Parsing scan results")
            
            # Parse results
            parsed_results = self._parse_output(process_result["stdout"], target)
            
            if progress_callback:
                await progress_callback(1.0, "Complete", "Nikto scan completed")
            
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            return ScanResult(
                scan_id=scan_id,
                tool_name="nikto",
                target=target,
                status=ToolStatus.COMPLETED,
                start_time=start_time,
                end_time=end_time,
                duration_seconds=duration,
                command_executed=" ".join(command),
                exit_code=process_result["exit_code"],
                raw_output=process_result["stdout"],
                error_output=process_result["stderr"],
                parsed_results=parsed_results,
                vulnerabilities=parsed_results.get("vulnerabilities", []),
                summary=parsed_results.get("summary", {}),
                metadata={
                    "nikto_version": self._get_version(),
                    "target_info": parsed_results.get("target_info", {}),
                    "scan_options": {
                        "port": options.port,
                        "ssl": options.ssl,
                        "plugins": options.plugins,
                        "timeout": options.timeout
                    }
                }
            )
            
        except Exception as e:
            logger.error(f"Nikto scan failed: {e}")
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            return ScanResult(
                scan_id=scan_id,
                tool_name="nikto",
                target=target,
                status=ToolStatus.FAILED,
                start_time=start_time,
                end_time=end_time,
                duration_seconds=duration,
                command_executed="",
                exit_code=1,
                raw_output="",
                error_output=str(e),
                parsed_results={},
                vulnerabilities=[],
                summary={"error": str(e)},
                metadata={}
            )
    
    def _build_command(self, target: str, options: NiktoScanOptions) -> List[str]:
        """Build Nikto command"""
        command = ["nikto", "-h", target]
        
        # Port
        if options.port and options.port != 80:
            command.extend(["-p", str(options.port)])
        
        # SSL
        if options.ssl:
            command.append("-ssl")
        
        # Plugins
        if options.plugins:
            command.extend(["-Plugins", ",".join(options.plugins)])
        
        # User agent
        if options.user_agent:
            command.extend(["-useragent", options.user_agent])
        
        # Timeout
        if options.timeout:
            command.extend(["-timeout", str(options.timeout)])
        
        # Evasion
        if options.evasion:
            command.extend(["-evasion", options.evasion])
        
        # Tuning
        if options.tuning:
            command.extend(["-Tuning", options.tuning])
        
        # Output format
        command.extend(["-Format", "json"])
        
        # Disable interactive mode
        command.append("-ask")
        command.append("no")
        
        return command
    
    async def _execute_command(self, command: List[str], progress_callback: Optional[Callable] = None) -> Dict[str, Any]:
        """Execute Nikto command"""
        try:
            process = await asyncio.create_subprocess_exec(
                *command,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            self.current_process = process
            
            # Monitor progress
            if progress_callback:
                monitor_task = asyncio.create_task(
                    self._monitor_progress(process, progress_callback)
                )
            
            stdout, stderr = await process.communicate()
            
            if progress_callback:
                monitor_task.cancel()
            
            return {
                "exit_code": process.returncode,
                "stdout": stdout.decode('utf-8', errors='ignore'),
                "stderr": stderr.decode('utf-8', errors='ignore')
            }
            
        except Exception as e:
            logger.error(f"Command execution failed: {e}")
            return {
                "exit_code": 1,
                "stdout": "",
                "stderr": str(e)
            }
        finally:
            self.current_process = None
    
    async def _monitor_progress(self, process, progress_callback):
        """Monitor scan progress"""
        try:
            progress = 0.2
            while process.returncode is None:
                await asyncio.sleep(5)
                progress = min(0.7, progress + 0.1)
                await progress_callback(progress, "Scanning", "Nikto scan in progress")
        except asyncio.CancelledError:
            pass
    
    def _parse_output(self, output: str, target: str) -> Dict[str, Any]:
        """Parse Nikto output"""
        parsed = {
            "vulnerabilities": [],
            "summary": {
                "total_items": 0,
                "vulnerabilities_found": 0,
                "target": target,
                "scan_time": datetime.now().isoformat()
            },
            "target_info": {
                "host": target,
                "server": "Unknown",
                "banner": ""
            }
        }
        
        try:
            # Try to parse JSON output first
            if output.strip().startswith('{'):
                json_data = json.loads(output)
                if 'vulnerabilities' in json_data:
                    parsed['vulnerabilities'] = json_data['vulnerabilities']
                    parsed['summary']['vulnerabilities_found'] = len(json_data['vulnerabilities'])
                return parsed
        except json.JSONDecodeError:
            pass
        
        # Parse text output
        vulnerabilities = []
        lines = output.split('\n')
        
        for line in lines:
            line = line.strip()
            if not line or line.startswith('-') or line.startswith('+'):
                continue
            
            # Parse vulnerability lines
            if '+ OSVDB-' in line or '+ Server:' in line or '+ Retrieved' in line:
                vuln = self._parse_vulnerability_line(line)
                if vuln:
                    vulnerabilities.append(vuln)
            
            # Extract server information
            if 'Server:' in line:
                server_match = re.search(r'Server: (.+)', line)
                if server_match:
                    parsed['target_info']['server'] = server_match.group(1).strip()
        
        parsed['vulnerabilities'] = vulnerabilities
        parsed['summary']['vulnerabilities_found'] = len(vulnerabilities)
        parsed['summary']['total_items'] = len(vulnerabilities)
        
        return parsed
    
    def _parse_vulnerability_line(self, line: str) -> Optional[Dict[str, Any]]:
        """Parse individual vulnerability line"""
        try:
            # Extract OSVDB ID
            osvdb_match = re.search(r'OSVDB-(\d+)', line)
            osvdb = osvdb_match.group(1) if osvdb_match else "0"
            
            # Extract URI
            uri_match = re.search(r': (/[^:]*)', line)
            uri = uri_match.group(1) if uri_match else "/"
            
            # Description is usually after the URI
            description = line.split(': ', 2)[-1] if ': ' in line else line
            
            # Determine severity based on keywords
            severity = "medium"
            if any(keyword in line.lower() for keyword in ['critical', 'high', 'exploit']):
                severity = "high"
            elif any(keyword in line.lower() for keyword in ['info', 'banner', 'version']):
                severity = "low"
            
            return {
                "id": f"nikto_{osvdb}",
                "osvdb": osvdb,
                "method": "GET",
                "uri": uri,
                "description": description.strip(),
                "severity": severity,
                "references": [f"https://www.osvdb.org/{osvdb}"] if osvdb != "0" else []
            }
            
        except Exception as e:
            logger.warning(f"Failed to parse vulnerability line: {line} - {e}")
            return None
    
    async def stop_scan(self) -> bool:
        """Stop current scan"""
        if self.current_process:
            try:
                self.current_process.terminate()
                await asyncio.sleep(2)
                if self.current_process.returncode is None:
                    self.current_process.kill()
                return True
            except Exception as e:
                logger.error(f"Failed to stop scan: {e}")
                return False
        return True
    
    def get_scan_profiles(self) -> Dict[str, Dict[str, Any]]:
        """Get predefined scan profiles"""
        return {
            "fast": {
                "name": "Fast Scan",
                "description": "Quick vulnerability check",
                "options": {
                    "timeout": 5,
                    "tuning": "1,2,3"
                }
            },
            "comprehensive": {
                "name": "Comprehensive Scan",
                "description": "Full vulnerability assessment",
                "options": {
                    "timeout": 30,
                    "tuning": "1,2,3,4,5,6,7,8,9,0,a,b,c,x"
                }
            },
            "plugins": {
                "name": "Plugin-based Scan",
                "description": "Scan using specific plugins",
                "options": {
                    "plugins": ["outdated", "headers", "robots", "dir_traversal"]
                }
            }
        }
    
    def get_frontend_interface_data(self) -> Dict[str, Any]:
        """Get data for frontend interface"""
        return {
            "tool_info": {
                "name": "Nikto Web Scanner",
                "description": "Web server vulnerability scanner",
                "version": self._get_version(),
                "category": "Web Scanner",
                "status": "available" if self.is_available() else "unavailable"
            },
            "scan_options": {
                "target": {
                    "type": "url",
                    "required": True,
                    "placeholder": "https://example.com",
                    "validation": "url"
                },
                "port": {
                    "type": "number",
                    "default": 80,
                    "min": 1,
                    "max": 65535
                },
                "ssl": {
                    "type": "boolean",
                    "default": False,
                    "label": "Use SSL/HTTPS"
                },
                "timeout": {
                    "type": "number",
                    "default": 10,
                    "min": 5,
                    "max": 300,
                    "label": "Request timeout (seconds)"
                },
                "scan_type": {
                    "type": "select",
                    "options": ["fast", "comprehensive", "plugins"],
                    "default": "fast"
                }
            },
            "scan_profiles": self.get_scan_profiles(),
            "output_formats": ["json", "html", "csv"],
            "capabilities": [
                "SSL/TLS scanning",
                "Plugin-based checks",
                "Custom headers",
                "Directory traversal",
                "Server fingerprinting",
                "OSVDB integration"
            ]
        }


# Register the tool
register_tool(NiktoScanner)