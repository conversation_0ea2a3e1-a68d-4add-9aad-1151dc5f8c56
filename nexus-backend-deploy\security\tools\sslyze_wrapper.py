#!/usr/bin/env python3
"""
SSLyze Wrapper for NexusScan Desktop
SSL/TLS configuration analyzer integration
"""

import asyncio
import logging
from typing import Dict, List, Optional, Callable, Any
from datetime import datetime

from security.tools.tool_registry import (
    ToolMetadata, ToolCategory, ToolCapabilities,
    ScanOptions, ScanResult, ToolStatus, register_tool
)
from security.tools.external_tool_wrapper import ExternalToolWrapper

logger = logging.getLogger(__name__)


@register_tool
class SSLyzeWrapper(ExternalToolWrapper):
    """SSLyze SSL/TLS configuration analyzer wrapper"""
    
    def __init__(self):
        super().__init__()
        self.tool_executable = "sslyze"
    
    def get_metadata(self) -> ToolMetadata:
        return ToolMetadata(
            name="sslyze",
            display_name="SSLyze",
            description="Fast and comprehensive SSL/TLS configuration analyzer",
            version="1.0.0",
            category=ToolCategory.NETWORK_SCANNER,
            capabilities=ToolCapabilities(
                supports_async=True,
                network_access_required=True,
                supported_targets=["hostname", "ip"],
                requires_root=False
            ),
            default_options={
                "port": 443,
                "scan_commands": [
                    "certificate_info",
                    "ssl2",
                    "ssl3",
                    "tls1",
                    "tls1_1",
                    "tls1_2",
                    "tls1_3",
                    "heartbleed",
                    "openssl_ccs",
                    "fallback",
                    "reneg",
                    "compression",
                    "robot"
                ],
                "ca_file": None,
                "json_output": False,
                "timeout": 5,
                "starttls": None  # smtp, xmpp, ldap, pop3, imap, ftp, postgres
            }
        )
    
    def get_tool_command(self, options: ScanOptions) -> List[str]:
        """Get SSLyze command"""
        port = options.custom_options.get("port", 443)
        scan_commands = options.custom_options.get("scan_commands", ["certificate_info", "ssl2", "ssl3", "tls1", "tls1_1", "tls1_2", "tls1_3"])
        
        # Build target with port
        target = f"{options.target}:{port}"
        
        command = ["sslyze", "--regular", target]
        
        # Add specific scan commands
        for scan_cmd in scan_commands:
            if scan_cmd == "certificate_info":
                command.append("--certinfo")
            elif scan_cmd == "heartbleed":
                command.append("--heartbleed")
            elif scan_cmd == "openssl_ccs":
                command.append("--openssl_ccs")
            elif scan_cmd == "fallback":
                command.append("--fallback")
            elif scan_cmd == "reneg":
                command.append("--reneg")
            elif scan_cmd == "compression":
                command.append("--compression")
            elif scan_cmd == "robot":
                command.append("--robot")
            # Protocol scans are included in --regular
        
        # Add CA file if specified
        ca_file = options.custom_options.get("ca_file")
        if ca_file:
            command.extend(["--ca_file", ca_file])
        
        # Add timeout
        timeout = options.custom_options.get("timeout", 5)
        command.extend(["--timeout", str(timeout)])
        
        # Add STARTTLS if needed
        starttls = options.custom_options.get("starttls")
        if starttls:
            command.extend(["--starttls", starttls])
        
        return command
    
    def parse_tool_output(self, output: str, target: str) -> Dict[str, Any]:
        """Parse SSLyze output"""
        lines = output.strip().split('\n')
        
        results = {
            "target": target,
            "certificate_info": {},
            "protocol_support": {},
            "cipher_suites": {},
            "vulnerabilities": [],
            "configuration_issues": []
        }
        
        current_section = None
        current_protocol = None
        
        for line in lines:
            # Certificate information
            if "Certificate Information:" in line:
                current_section = "certificate"
            elif current_section == "certificate":
                if "Common Name:" in line:
                    results["certificate_info"]["common_name"] = line.split(":", 1)[1].strip()
                elif "Issuer:" in line:
                    results["certificate_info"]["issuer"] = line.split(":", 1)[1].strip()
                elif "Not Before:" in line:
                    results["certificate_info"]["not_before"] = line.split(":", 1)[1].strip()
                elif "Not After:" in line:
                    results["certificate_info"]["not_after"] = line.split(":", 1)[1].strip()
                elif "Signature Algorithm:" in line:
                    results["certificate_info"]["signature_algorithm"] = line.split(":", 1)[1].strip()
                elif "Key Size:" in line:
                    results["certificate_info"]["key_size"] = line.split(":", 1)[1].strip()
            
            # Protocol support
            elif "SSLV2 Cipher Suites:" in line:
                current_section = "protocol"
                current_protocol = "SSLv2"
                results["protocol_support"]["SSLv2"] = "Supported" in line
            elif "SSLV3 Cipher Suites:" in line:
                current_section = "protocol"
                current_protocol = "SSLv3"
                results["protocol_support"]["SSLv3"] = "Supported" in line
            elif "TLSV1 Cipher Suites:" in line:
                current_section = "protocol"
                current_protocol = "TLSv1.0"
                results["protocol_support"]["TLSv1.0"] = "Supported" in line
            elif "TLSV1_1 Cipher Suites:" in line:
                current_section = "protocol"
                current_protocol = "TLSv1.1"
                results["protocol_support"]["TLSv1.1"] = "Supported" in line
            elif "TLSV1_2 Cipher Suites:" in line:
                current_section = "protocol"
                current_protocol = "TLSv1.2"
                results["protocol_support"]["TLSv1.2"] = "Supported" in line
            elif "TLSV1_3 Cipher Suites:" in line:
                current_section = "protocol"
                current_protocol = "TLSv1.3"
                results["protocol_support"]["TLSv1.3"] = "Supported" in line
            
            # Cipher suites
            elif current_section == "protocol" and current_protocol and line.strip().startswith("-"):
                cipher = line.strip()[1:].strip()
                if current_protocol not in results["cipher_suites"]:
                    results["cipher_suites"][current_protocol] = []
                results["cipher_suites"][current_protocol].append(cipher)
            
            # Vulnerability checks
            elif "VULNERABLE - Server is vulnerable to Heartbleed" in line:
                results["vulnerabilities"].append({
                    "name": "Heartbleed (CVE-2014-0160)",
                    "severity": "critical",
                    "description": "Server is vulnerable to Heartbleed attack, allowing memory disclosure",
                    "type": "ssl_vulnerability",
                    "cve": "CVE-2014-0160",
                    "remediation": "Update OpenSSL to a patched version"
                })
            elif "VULNERABLE - Server is vulnerable to CCS injection" in line:
                results["vulnerabilities"].append({
                    "name": "CCS Injection (CVE-2014-0224)",
                    "severity": "high",
                    "description": "Server is vulnerable to OpenSSL CCS injection",
                    "type": "ssl_vulnerability",
                    "cve": "CVE-2014-0224",
                    "remediation": "Update OpenSSL to a patched version"
                })
            elif "VULNERABLE - Server is vulnerable to ROBOT" in line:
                results["vulnerabilities"].append({
                    "name": "ROBOT Attack",
                    "severity": "high",
                    "description": "Server is vulnerable to Return Of Bleichenbacher's Oracle Threat",
                    "type": "ssl_vulnerability",
                    "cve": "CVE-2017-13099",
                    "remediation": "Disable RSA encryption cipher suites"
                })
            elif "VULNERABLE" in line and "Client-initiated Renegotiation" in line:
                results["vulnerabilities"].append({
                    "name": "Insecure Renegotiation",
                    "severity": "medium",
                    "description": "Server allows insecure client-initiated renegotiation",
                    "type": "ssl_vulnerability",
                    "cve": "CVE-2009-3555",
                    "remediation": "Disable client-initiated renegotiation"
                })
        
        # Check for weak protocols
        if results["protocol_support"].get("SSLv2", False):
            results["vulnerabilities"].append({
                "name": "SSLv2 Enabled",
                "severity": "high",
                "description": "SSLv2 is enabled, which has known security vulnerabilities",
                "type": "weak_protocol",
                "cve": None,
                "remediation": "Disable SSLv2 support"
            })
        
        if results["protocol_support"].get("SSLv3", False):
            results["vulnerabilities"].append({
                "name": "SSLv3 Enabled (POODLE)",
                "severity": "medium",
                "description": "SSLv3 is enabled, which is vulnerable to POODLE attack",
                "type": "weak_protocol",
                "cve": "CVE-2014-3566",
                "remediation": "Disable SSLv3 support"
            })
        
        if results["protocol_support"].get("TLSv1.0", False):
            results["configuration_issues"].append({
                "issue": "TLSv1.0 Enabled",
                "severity": "low",
                "description": "TLSv1.0 is deprecated and should be disabled",
                "recommendation": "Disable TLSv1.0 and use TLSv1.2 or higher"
            })
        
        # Check for weak ciphers
        weak_ciphers = ["RC4", "DES", "3DES", "MD5", "EXPORT", "NULL", "anon"]
        for protocol, ciphers in results["cipher_suites"].items():
            for cipher in ciphers:
                for weak in weak_ciphers:
                    if weak in cipher.upper():
                        results["configuration_issues"].append({
                            "issue": f"Weak cipher: {cipher}",
                            "severity": "medium",
                            "description": f"Weak cipher suite {cipher} is enabled in {protocol}",
                            "recommendation": "Disable weak cipher suites"
                        })
                        break
        
        # Summary
        results["summary"] = {
            "protocols_enabled": len([p for p, enabled in results["protocol_support"].items() if enabled]),
            "total_ciphers": sum(len(ciphers) for ciphers in results["cipher_suites"].values()),
            "vulnerabilities_found": len(results["vulnerabilities"]),
            "configuration_issues": len(results["configuration_issues"])
        }
        
        return results
    
    async def execute_simulation(self, options: ScanOptions,
                                 progress_callback: Optional[Callable[[float, str], None]] = None) -> ScanResult:
        """Execute simulated scan for Railway/testing"""
        start_time = datetime.now().isoformat()
        
        if progress_callback:
            await progress_callback(0.1, "Connecting to SSL/TLS service...")
        
        await asyncio.sleep(1)
        
        if progress_callback:
            await progress_callback(0.3, "Analyzing certificate information...")
        
        await asyncio.sleep(1)
        
        if progress_callback:
            await progress_callback(0.6, "Testing protocol support and cipher suites...")
        
        await asyncio.sleep(1)
        
        if progress_callback:
            await progress_callback(0.8, "Checking for vulnerabilities...")
        
        # Simulated output
        port = options.custom_options.get("port", 443)
        simulated_output = f"""
 CHECKING HOST(S) AVAILABILITY
 -----------------------------

   {options.target}:{port}                        => {options.target}   {port}


 SCAN RESULTS FOR {options.target}:{port} - {options.target}
 ------------------------------------------------------------

 * Certificate Information:
     Content
       Common Name:                     {options.target}
       Issuer:                          Let's Encrypt Authority X3
       Serial Number:                   04B5E5C9A6B2D8F3E1C7A9F4D2B8
       Not Before:                      2024-01-15 00:00:00
       Not After:                       2024-04-15 23:59:59
       Signature Algorithm:             sha256WithRSAEncryption
       Public Key Algorithm:            RSA
       Key Size:                        2048

     Trust
       Certificate is trusted

 * SSLV2 Cipher Suites:
     Server rejected all cipher suites.

 * SSLV3 Cipher Suites:
     Server rejected all cipher suites.

 * TLSV1 Cipher Suites:
     Supported:
       - TLS_ECDHE_RSA_WITH_AES_256_CBC_SHA
       - TLS_ECDHE_RSA_WITH_AES_128_CBC_SHA
       - TLS_RSA_WITH_AES_256_CBC_SHA
       - TLS_RSA_WITH_AES_128_CBC_SHA

 * TLSV1_1 Cipher Suites:
     Supported:
       - TLS_ECDHE_RSA_WITH_AES_256_CBC_SHA
       - TLS_ECDHE_RSA_WITH_AES_128_CBC_SHA
       - TLS_RSA_WITH_AES_256_CBC_SHA
       - TLS_RSA_WITH_AES_128_CBC_SHA

 * TLSV1_2 Cipher Suites:
     Supported:
       - TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384
       - TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256
       - TLS_ECDHE_RSA_WITH_AES_256_CBC_SHA384
       - TLS_ECDHE_RSA_WITH_AES_128_CBC_SHA256
       - TLS_RSA_WITH_AES_256_GCM_SHA384
       - TLS_RSA_WITH_AES_128_GCM_SHA256

 * TLSV1_3 Cipher Suites:
     Supported:
       - TLS_AES_256_GCM_SHA384
       - TLS_AES_128_GCM_SHA256
       - TLS_CHACHA20_POLY1305_SHA256

 * Deflate Compression:
     OK - Compression disabled

 * OpenSSL CCS Injection:
     OK - Not vulnerable to OpenSSL CCS injection

 * OpenSSL Heartbleed:
     OK - Not vulnerable to Heartbleed

 * Secure Renegotiation:
     OK - Secure renegotiation is supported

 * ROBOT Attack:
     OK - Not vulnerable to ROBOT

 * Session Resumption:
     With Session IDs:           OK - Supported (5 successful resumptions out of 5 attempts)
     With TLS Tickets:           OK - Supported

 SCAN COMPLETED IN 3.21 S
 ------------------------"""
        
        parsed_results = self.parse_tool_output(simulated_output, options.target)
        
        if progress_callback:
            await progress_callback(1.0, "SSL/TLS analysis complete")
        
        return ScanResult(
            tool_name=self.metadata.name,
            target=options.target,
            status=ToolStatus.COMPLETED,
            start_time=start_time,
            end_time=datetime.now().isoformat(),
            duration_seconds=(datetime.now() - datetime.fromisoformat(start_time)).total_seconds(),
            raw_output=simulated_output,
            parsed_results=parsed_results,
            vulnerabilities=parsed_results.get("vulnerabilities", []),
            metadata={
                "port_scanned": port,
                "protocols_tested": list(parsed_results.get("protocol_support", {}).keys()),
                "vulnerabilities_found": len(parsed_results.get("vulnerabilities", [])),
                "certificate_cn": parsed_results.get("certificate_info", {}).get("common_name")
            }
        )