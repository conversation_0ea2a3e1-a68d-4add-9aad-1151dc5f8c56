#!/usr/bin/env python3
"""
Data Export Manager for NexusScan Desktop Application
Handles exporting data in various formats (JSON, CSV, XML, PDF)
"""

import csv
import json
import logging
import os
import xml.etree.ElementTree as ET
from datetime import datetime
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass, asdict
from io import StringIO
import pandas as pd

from .models import (
    Campaign, Scan, Vulnerability, AIAnalysis, ExportHistory,
    DatabaseManager
)
from .data_manager import DataManager, SearchFilters

logger = logging.getLogger(__name__)


@dataclass
class ExportRequest:
    """Request structure for data export"""
    export_type: str  # vulnerabilities, scans, campaigns, full_campaign, ai_analysis
    format: str  # json, csv, xml, excel
    campaign_id: Optional[int] = None
    filters: Optional[SearchFilters] = None
    include_ai_analysis: bool = True
    include_attachments: bool = False
    output_directory: str = "./exports"
    filename_prefix: str = "nexusscan_export"
    exported_by: str = "system"


@dataclass
class ExportResult:
    """Result of export operation"""
    success: bool
    file_path: str
    file_size: int
    record_count: int
    export_time: datetime
    error_message: Optional[str] = None


class ExportManager:
    """Manages data export operations in various formats"""

    def __init__(self, db_manager: DatabaseManager, data_manager: DataManager):
        """Initialize export manager
        
        Args:
            db_manager: Database manager instance
            data_manager: Data manager instance
        """
        self.db_manager = db_manager
        self.data_manager = data_manager
        self.logger = logging.getLogger(__name__)

    def export_data(self, request: ExportRequest) -> ExportResult:
        """Export data based on request parameters
        
        Args:
            request: Export request configuration
            
        Returns:
            Export result with file information
        """
        try:
            # Ensure output directory exists
            os.makedirs(request.output_directory, exist_ok=True)
            
            # Generate filename
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{request.filename_prefix}_{request.export_type}_{timestamp}.{request.format}"
            file_path = os.path.join(request.output_directory, filename)
            
            # Get data based on export type
            data = self._get_export_data(request)
            
            # Export in specified format
            if request.format.lower() == "json":
                self._export_json(data, file_path)
            elif request.format.lower() == "csv":
                self._export_csv(data, file_path, request.export_type)
            elif request.format.lower() == "xml":
                self._export_xml(data, file_path, request.export_type)
            elif request.format.lower() == "excel":
                self._export_excel(data, file_path, request.export_type)
            else:
                raise ValueError(f"Unsupported export format: {request.format}")
            
            # Get file size
            file_size = os.path.getsize(file_path)
            
            # Record export in history
            self._record_export_history(request, file_path, file_size, len(data.get('records', [])))
            
            result = ExportResult(
                success=True,
                file_path=file_path,
                file_size=file_size,
                record_count=len(data.get('records', [])),
                export_time=datetime.now()
            )
            
            self.logger.info(f"Export completed: {file_path} ({file_size} bytes, {result.record_count} records)")
            return result
            
        except Exception as e:
            self.logger.error(f"Export failed: {e}")
            return ExportResult(
                success=False,
                file_path="",
                file_size=0,
                record_count=0,
                export_time=datetime.now(),
                error_message=str(e)
            )

    def _get_export_data(self, request: ExportRequest) -> Dict[str, Any]:
        """Get data for export based on request type
        
        Args:
            request: Export request
            
        Returns:
            Dictionary containing export data
        """
        if request.export_type == "vulnerabilities":
            return self._get_vulnerabilities_data(request)
        elif request.export_type == "scans":
            return self._get_scans_data(request)
        elif request.export_type == "campaigns":
            return self._get_campaigns_data(request)
        elif request.export_type == "full_campaign":
            return self._get_full_campaign_data(request)
        elif request.export_type == "ai_analysis":
            return self._get_ai_analysis_data(request)
        else:
            raise ValueError(f"Unsupported export type: {request.export_type}")

    def _get_vulnerabilities_data(self, request: ExportRequest) -> Dict[str, Any]:
        """Get vulnerabilities data for export"""
        filters = request.filters or SearchFilters()
        if request.campaign_id:
            filters.campaign_id = request.campaign_id
        
        vulnerabilities = self.data_manager.get_vulnerabilities(filters)
        
        records = []
        for vuln in vulnerabilities:
            record = {
                "id": vuln.id,
                "campaign_id": vuln.campaign_id,
                "scan_id": vuln.scan_id,
                "type": vuln.vuln_type,
                "severity": vuln.severity,
                "title": vuln.title,
                "description": vuln.description,
                "affected_url": vuln.affected_url,
                "affected_parameter": vuln.affected_parameter,
                "cvss_score": float(vuln.cvss_score) if vuln.cvss_score else 0.0,
                "cve_id": vuln.cve_id,
                "status": vuln.status,
                "verified": vuln.verified,
                "exploitable": vuln.exploitable,
                "discovered_at": vuln.discovered_at.isoformat(),
                "evidence": vuln.evidence,
                "remediation": vuln.remediation,
                "tags": vuln.tags or []
            }
            
            # Include AI analysis if requested
            if request.include_ai_analysis:
                ai_analyses = self.data_manager.get_ai_analyses(vulnerability_id=vuln.id)
                record["ai_analyses"] = [
                    {
                        "analysis_type": analysis.analysis_type,
                        "ai_provider": analysis.ai_provider,
                        "confidence_score": float(analysis.confidence_score) if analysis.confidence_score else 0.0,
                        "created_at": analysis.created_at.isoformat()
                    }
                    for analysis in ai_analyses
                ]
            
            records.append(record)
        
        return {
            "export_type": "vulnerabilities",
            "exported_at": datetime.now().isoformat(),
            "total_records": len(records),
            "filters_applied": asdict(filters),
            "records": records
        }

    def _get_scans_data(self, request: ExportRequest) -> Dict[str, Any]:
        """Get scans data for export"""
        filters = request.filters or SearchFilters()
        if request.campaign_id:
            filters.campaign_id = request.campaign_id
        
        scans = self.data_manager.get_scans(filters)
        
        records = []
        for scan in scans:
            record = {
                "id": scan.id,
                "campaign_id": scan.campaign_id,
                "name": scan.name,
                "scan_type": scan.scan_type,
                "target": scan.target,
                "status": scan.status,
                "started_at": scan.started_at.isoformat(),
                "completed_at": scan.completed_at.isoformat() if scan.completed_at else None,
                "duration_seconds": scan.duration_seconds,
                "duration_formatted": scan.duration_formatted,
                "tools_used": scan.tools_used or [],
                "summary": scan.summary
            }
            
            # Include vulnerability count
            vuln_filters = SearchFilters(scan_id=scan.id)
            vulnerabilities = self.data_manager.get_vulnerabilities(vuln_filters)
            record["vulnerability_count"] = len(vulnerabilities)
            
            # Include severity breakdown
            severity_breakdown = {}
            for vuln in vulnerabilities:
                severity_breakdown[vuln.severity] = severity_breakdown.get(vuln.severity, 0) + 1
            record["severity_breakdown"] = severity_breakdown
            
            records.append(record)
        
        return {
            "export_type": "scans",
            "exported_at": datetime.now().isoformat(),
            "total_records": len(records),
            "filters_applied": asdict(filters),
            "records": records
        }

    def _get_campaigns_data(self, request: ExportRequest) -> Dict[str, Any]:
        """Get campaigns data for export"""
        session = self.db_manager.get_session()
        try:
            query = session.query(Campaign)
            if request.campaign_id:
                query = query.filter(Campaign.id == request.campaign_id)
            
            campaigns = query.all()
            
            records = []
            for campaign in campaigns:
                # Get campaign statistics
                scan_count = len(campaign.scans)
                vuln_count = len(campaign.vulnerabilities)
                vuln_stats = campaign.vulnerability_stats
                
                record = {
                    "id": campaign.id,
                    "name": campaign.name,
                    "description": campaign.description,
                    "status": campaign.status,
                    "owner": campaign.owner,
                    "created_at": campaign.created_at.isoformat(),
                    "updated_at": campaign.updated_at.isoformat(),
                    "target_scope": campaign.target_scope or [],
                    "target_count": campaign.target_count,
                    "tags": campaign.tags or [],
                    "settings": campaign.settings or {},
                    "scan_count": scan_count,
                    "vulnerability_count": vuln_count,
                    "vulnerability_breakdown": vuln_stats
                }
                
                records.append(record)
            
            return {
                "export_type": "campaigns",
                "exported_at": datetime.now().isoformat(),
                "total_records": len(records),
                "records": records
            }
            
        finally:
            session.close()

    def _get_full_campaign_data(self, request: ExportRequest) -> Dict[str, Any]:
        """Get complete campaign data including all related entities"""
        if not request.campaign_id:
            raise ValueError("Campaign ID required for full campaign export")
        
        session = self.db_manager.get_session()
        try:
            campaign = session.query(Campaign).filter_by(id=request.campaign_id).first()
            if not campaign:
                raise ValueError(f"Campaign {request.campaign_id} not found")
            
            # Get all scans
            scans_data = []
            for scan in campaign.scans:
                scan_record = {
                    "id": scan.id,
                    "name": scan.name,
                    "scan_type": scan.scan_type,
                    "target": scan.target,
                    "status": scan.status,
                    "started_at": scan.started_at.isoformat(),
                    "completed_at": scan.completed_at.isoformat() if scan.completed_at else None,
                    "duration_seconds": scan.duration_seconds,
                    "tools_used": scan.tools_used or [],
                    "summary": scan.summary,
                    "raw_output": scan.raw_output if request.include_attachments else None
                }
                scans_data.append(scan_record)
            
            # Get all vulnerabilities
            vulnerabilities_data = []
            for vuln in campaign.vulnerabilities:
                vuln_record = {
                    "id": vuln.id,
                    "scan_id": vuln.scan_id,
                    "type": vuln.vuln_type,
                    "severity": vuln.severity,
                    "title": vuln.title,
                    "description": vuln.description,
                    "affected_url": vuln.affected_url,
                    "affected_parameter": vuln.affected_parameter,
                    "cvss_score": float(vuln.cvss_score) if vuln.cvss_score else 0.0,
                    "cve_id": vuln.cve_id,
                    "status": vuln.status,
                    "verified": vuln.verified,
                    "exploitable": vuln.exploitable,
                    "discovered_at": vuln.discovered_at.isoformat(),
                    "evidence": vuln.evidence,
                    "remediation": vuln.remediation,
                    "tags": vuln.tags or [],
                    "metadata": vuln.metadata or {}
                }
                vulnerabilities_data.append(vuln_record)
            
            # Get AI analyses if requested
            ai_analyses_data = []
            if request.include_ai_analysis:
                for analysis in campaign.ai_analyses:
                    analysis_record = {
                        "id": analysis.id,
                        "vulnerability_id": analysis.vulnerability_id,
                        "scan_id": analysis.scan_id,
                        "analysis_type": analysis.analysis_type,
                        "ai_provider": analysis.ai_provider,
                        "model_used": analysis.model_used,
                        "confidence_score": float(analysis.confidence_score) if analysis.confidence_score else 0.0,
                        "execution_time_ms": analysis.execution_time_ms,
                        "created_at": analysis.created_at.isoformat(),
                        "input_data": analysis.input_data,
                        "output_data": analysis.output_data
                    }
                    ai_analyses_data.append(analysis_record)
            
            return {
                "export_type": "full_campaign",
                "exported_at": datetime.now().isoformat(),
                "campaign": {
                    "id": campaign.id,
                    "name": campaign.name,
                    "description": campaign.description,
                    "status": campaign.status,
                    "owner": campaign.owner,
                    "created_at": campaign.created_at.isoformat(),
                    "updated_at": campaign.updated_at.isoformat(),
                    "target_scope": campaign.target_scope or [],
                    "tags": campaign.tags or [],
                    "settings": campaign.settings or {}
                },
                "scans": scans_data,
                "vulnerabilities": vulnerabilities_data,
                "ai_analyses": ai_analyses_data,
                "summary_statistics": {
                    "total_scans": len(scans_data),
                    "total_vulnerabilities": len(vulnerabilities_data),
                    "total_ai_analyses": len(ai_analyses_data),
                    "vulnerability_breakdown": campaign.vulnerability_stats
                },
                "records": vulnerabilities_data  # For record count compatibility
            }
            
        finally:
            session.close()

    def _get_ai_analysis_data(self, request: ExportRequest) -> Dict[str, Any]:
        """Get AI analysis data for export"""
        ai_analyses = self.data_manager.get_ai_analyses(
            campaign_id=request.campaign_id,
            limit=request.filters.limit if request.filters else 1000
        )
        
        records = []
        for analysis in ai_analyses:
            record = {
                "id": analysis.id,
                "campaign_id": analysis.campaign_id,
                "scan_id": analysis.scan_id,
                "vulnerability_id": analysis.vulnerability_id,
                "analysis_type": analysis.analysis_type,
                "ai_provider": analysis.ai_provider,
                "model_used": analysis.model_used,
                "confidence_score": float(analysis.confidence_score) if analysis.confidence_score else 0.0,
                "execution_time_ms": analysis.execution_time_ms,
                "created_at": analysis.created_at.isoformat(),
                "status": analysis.status,
                "input_data": analysis.input_data,
                "output_data": analysis.output_data
            }
            records.append(record)
        
        return {
            "export_type": "ai_analysis",
            "exported_at": datetime.now().isoformat(),
            "total_records": len(records),
            "records": records
        }

    def _export_json(self, data: Dict[str, Any], file_path: str):
        """Export data to JSON format"""
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)

    def _export_csv(self, data: Dict[str, Any], file_path: str, export_type: str):
        """Export data to CSV format"""
        records = data.get('records', [])
        if not records:
            # Create empty CSV with headers
            with open(file_path, 'w', newline='', encoding='utf-8') as f:
                writer = csv.writer(f)
                writer.writerow(['No data available'])
            return
        
        # Flatten nested structures for CSV
        flattened_records = []
        for record in records:
            flattened = self._flatten_dict(record)
            flattened_records.append(flattened)
        
        # Get all unique keys for headers
        all_keys = set()
        for record in flattened_records:
            all_keys.update(record.keys())
        
        headers = sorted(list(all_keys))
        
        with open(file_path, 'w', newline='', encoding='utf-8') as f:
            writer = csv.DictWriter(f, fieldnames=headers)
            writer.writeheader()
            writer.writerows(flattened_records)

    def _export_xml(self, data: Dict[str, Any], file_path: str, export_type: str):
        """Export data to XML format"""
        root = ET.Element("nexusscan_export")
        
        # Add metadata
        metadata = ET.SubElement(root, "metadata")
        ET.SubElement(metadata, "export_type").text = data.get("export_type", export_type)
        ET.SubElement(metadata, "exported_at").text = data.get("exported_at", "")
        ET.SubElement(metadata, "total_records").text = str(data.get("total_records", 0))
        
        # Add records
        records_element = ET.SubElement(root, "records")
        
        for record in data.get('records', []):
            record_element = ET.SubElement(records_element, "record")
            self._dict_to_xml(record, record_element)
        
        # Write XML file
        tree = ET.ElementTree(root)
        tree.write(file_path, encoding='utf-8', xml_declaration=True)

    def _export_excel(self, data: Dict[str, Any], file_path: str, export_type: str):
        """Export data to Excel format"""
        try:
            records = data.get('records', [])
            
            if not records:
                # Create empty Excel file
                df = pd.DataFrame([{'message': 'No data available'}])
                df.to_excel(file_path, index=False)
                return
            
            # Flatten records for Excel
            flattened_records = []
            for record in records:
                flattened = self._flatten_dict(record)
                flattened_records.append(flattened)
            
            df = pd.DataFrame(flattened_records)
            
            # Create Excel writer with multiple sheets if needed
            with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
                df.to_excel(writer, sheet_name='Data', index=False)
                
                # Add metadata sheet
                metadata_df = pd.DataFrame([
                    {'Property': 'Export Type', 'Value': data.get('export_type', export_type)},
                    {'Property': 'Exported At', 'Value': data.get('exported_at', '')},
                    {'Property': 'Total Records', 'Value': data.get('total_records', 0)}
                ])
                metadata_df.to_excel(writer, sheet_name='Metadata', index=False)
                
        except ImportError:
            # Fallback to CSV if pandas/openpyxl not available
            self.logger.warning("Excel export requires pandas and openpyxl. Falling back to CSV.")
            csv_path = file_path.replace('.xlsx', '.csv').replace('.xls', '.csv')
            self._export_csv(data, csv_path, export_type)

    def _flatten_dict(self, d: Dict[str, Any], parent_key: str = '', sep: str = '_') -> Dict[str, Any]:
        """Flatten nested dictionary for CSV/Excel export"""
        items = []
        for k, v in d.items():
            new_key = f"{parent_key}{sep}{k}" if parent_key else k
            
            if isinstance(v, dict):
                items.extend(self._flatten_dict(v, new_key, sep=sep).items())
            elif isinstance(v, list):
                # Convert lists to comma-separated strings
                if v and isinstance(v[0], dict):
                    # For list of dicts, create numbered entries
                    for i, item in enumerate(v):
                        items.extend(self._flatten_dict(item, f"{new_key}_{i}", sep=sep).items())
                else:
                    # For simple lists, join with commas
                    items.append((new_key, ', '.join(str(item) for item in v)))
            else:
                items.append((new_key, v))
        
        return dict(items)

    def _dict_to_xml(self, d: Dict[str, Any], parent: ET.Element):
        """Convert dictionary to XML elements"""
        for key, value in d.items():
            # Clean key name for XML
            clean_key = str(key).replace(' ', '_').replace('-', '_')
            
            if isinstance(value, dict):
                element = ET.SubElement(parent, clean_key)
                self._dict_to_xml(value, element)
            elif isinstance(value, list):
                list_element = ET.SubElement(parent, clean_key)
                for i, item in enumerate(value):
                    if isinstance(item, dict):
                        item_element = ET.SubElement(list_element, f"item_{i}")
                        self._dict_to_xml(item, item_element)
                    else:
                        item_element = ET.SubElement(list_element, f"item_{i}")
                        item_element.text = str(item)
            else:
                element = ET.SubElement(parent, clean_key)
                element.text = str(value) if value is not None else ""

    def _record_export_history(self, request: ExportRequest, file_path: str, file_size: int, record_count: int):
        """Record export operation in database history"""
        session = self.db_manager.get_session()
        try:
            export_record = ExportHistory(
                campaign_id=request.campaign_id,
                export_type=request.export_type,
                format=request.format,
                file_path=file_path,
                record_count=record_count,
                file_size=file_size,
                exported_by=request.exported_by
            )
            
            session.add(export_record)
            session.commit()
            
        except Exception as e:
            session.rollback()
            self.logger.error(f"Failed to record export history: {e}")
        finally:
            session.close()

    def get_export_history(self, 
                          campaign_id: Optional[int] = None,
                          export_type: Optional[str] = None,
                          limit: int = 50) -> List[ExportHistory]:
        """Get export history
        
        Args:
            campaign_id: Filter by campaign
            export_type: Filter by export type
            limit: Maximum number of results
            
        Returns:
            List of export history records
        """
        session = self.db_manager.get_session()
        try:
            query = session.query(ExportHistory)
            
            if campaign_id:
                query = query.filter(ExportHistory.campaign_id == campaign_id)
            
            if export_type:
                query = query.filter(ExportHistory.export_type == export_type)
            
            return query.order_by(ExportHistory.exported_at.desc()).limit(limit).all()
            
        finally:
            session.close()

    def delete_export_file(self, export_id: int) -> bool:
        """Delete export file and remove from history
        
        Args:
            export_id: Export history record ID
            
        Returns:
            True if deleted successfully
        """
        session = self.db_manager.get_session()
        try:
            export_record = session.query(ExportHistory).filter_by(id=export_id).first()
            if not export_record:
                return False
            
            # Delete physical file if it exists
            if export_record.file_path and os.path.exists(export_record.file_path):
                os.remove(export_record.file_path)
            
            # Delete database record
            session.delete(export_record)
            session.commit()
            
            self.logger.info(f"Deleted export file: {export_record.file_path}")
            return True
            
        except Exception as e:
            session.rollback()
            self.logger.error(f"Failed to delete export file {export_id}: {e}")
            raise
        finally:
            session.close()

    def cleanup_old_exports(self, days_to_keep: int = 30) -> int:
        """Clean up old export files
        
        Args:
            days_to_keep: Number of days to keep export files
            
        Returns:
            Number of files cleaned up
        """
        session = self.db_manager.get_session()
        try:
            cutoff_date = datetime.now() - timedelta(days=days_to_keep)
            
            old_exports = session.query(ExportHistory).filter(
                ExportHistory.exported_at < cutoff_date
            ).all()
            
            cleaned_count = 0
            for export_record in old_exports:
                # Delete physical file if it exists
                if export_record.file_path and os.path.exists(export_record.file_path):
                    try:
                        os.remove(export_record.file_path)
                        cleaned_count += 1
                    except OSError as e:
                        self.logger.warning(f"Failed to delete file {export_record.file_path}: {e}")
                
                # Delete database record
                session.delete(export_record)
            
            session.commit()
            
            self.logger.info(f"Cleaned up {cleaned_count} old export files")
            return cleaned_count
            
        except Exception as e:
            session.rollback()
            self.logger.error(f"Failed to cleanup old exports: {e}")
            raise
        finally:
            session.close()