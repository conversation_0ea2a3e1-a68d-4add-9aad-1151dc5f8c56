#!/usr/bin/env python3
"""
Reporting Integration Module for NexusScan
Connects report generation with security scanning and database systems
"""

import asyncio
import logging
from datetime import datetime, timezone
from typing import List, Dict, Optional, Any
from pathlib import Path

from .report_generator import ReportGenerator, ReportData, ScanMetadata, VulnerabilityData
from core.database import DatabaseManager
from security.scanner import SecurityScanner

logger = logging.getLogger(__name__)

class ReportingIntegration:
    """Integration layer between reporting and core systems"""
    
    def __init__(self, db_manager: DatabaseManager, security_scanner: SecurityScanner):
        """Initialize reporting integration
        
        Args:
            db_manager: Database manager instance
            security_scanner: Security scanner instance
        """
        self.db_manager = db_manager
        self.security_scanner = security_scanner
        self.report_generator = ReportGenerator()
        
    async def generate_campaign_report(self, 
                                     campaign_id: str,
                                     formats: List[str] = None,
                                     include_all_findings: bool = True,
                                     filename_prefix: str = None) -> Dict[str, str]:
        """Generate comprehensive report for a security campaign
        
        Args:
            campaign_id: ID of the campaign to report on
            formats: List of formats to generate ('pdf', 'docx', 'html', 'json')
            include_all_findings: Whether to include all findings or just high/critical
            filename_prefix: Custom filename prefix
            
        Returns:
            Dictionary mapping format to generated file path
        """
        try:
            logger.info(f"Generating report for campaign: {campaign_id}")
            
            # Get campaign data from database
            campaign_data = await self._get_campaign_data(campaign_id)
            if not campaign_data:
                raise ValueError(f"Campaign {campaign_id} not found")
            
            # Get vulnerabilities
            vulnerabilities = await self._get_campaign_vulnerabilities(campaign_id, include_all_findings)
            
            # Get scan sessions for metadata
            scan_sessions = await self._get_campaign_scan_sessions(campaign_id)
            
            # Build report data
            report_data = await self._build_report_data(campaign_data, vulnerabilities, scan_sessions)
            
            # Generate report in requested formats
            generated_files = await self.report_generator.generate_report(
                report_data,
                formats=formats,
                filename_prefix=filename_prefix
            )
            
            # Store report metadata in database
            await self._store_report_metadata(campaign_id, generated_files, report_data)
            
            logger.info(f"Successfully generated {len(generated_files)} report formats for campaign {campaign_id}")
            return generated_files
            
        except Exception as e:
            logger.error(f"Failed to generate campaign report: {e}")
            raise
    
    async def generate_vulnerability_report(self,
                                          vulnerability_ids: List[str],
                                          formats: List[str] = None,
                                          filename_prefix: str = None) -> Dict[str, str]:
        """Generate report for specific vulnerabilities
        
        Args:
            vulnerability_ids: List of vulnerability IDs to include
            formats: List of formats to generate
            filename_prefix: Custom filename prefix
            
        Returns:
            Dictionary mapping format to generated file path
        """
        try:
            logger.info(f"Generating vulnerability report for {len(vulnerability_ids)} vulnerabilities")
            
            # Get vulnerabilities from database
            vulnerabilities = []
            for vuln_id in vulnerability_ids:
                vuln_data = await self._get_vulnerability_data(vuln_id)
                if vuln_data:
                    vulnerabilities.append(vuln_data)
            
            if not vulnerabilities:
                raise ValueError("No valid vulnerabilities found")
            
            # Create synthetic campaign metadata for vulnerabilities-only report
            scan_start = min(v.discovered_at for v in vulnerabilities if v.discovered_at)
            scan_end = max(v.discovered_at for v in vulnerabilities if v.discovered_at)
            targets = list(set(v.target for v in vulnerabilities))
            tools_used = list(set(v.tool_name for v in vulnerabilities if v.tool_name))
            
            metadata = ScanMetadata(
                campaign_id="vuln_report_" + datetime.now().strftime("%Y%m%d_%H%M%S"),
                campaign_name="Vulnerability-Specific Report",
                scan_start=scan_start,
                scan_end=scan_end,
                targets=targets,
                tools_used=tools_used,
                scan_types=["vulnerability_analysis"],
                total_vulnerabilities=len(vulnerabilities),
                severity_breakdown=self._calculate_severity_breakdown(vulnerabilities),
                operator="NexusScan User"
            )
            
            # Generate intelligent summaries
            executive_summary = await self.report_generator.generate_executive_summary(vulnerabilities, metadata)
            technical_findings = await self.report_generator.generate_technical_findings(vulnerabilities)
            recommendations = await self.report_generator.generate_recommendations(vulnerabilities, metadata)
            
            # Build report data
            report_data = ReportData(
                metadata=metadata,
                vulnerabilities=vulnerabilities,
                executive_summary=executive_summary,
                technical_findings=technical_findings,
                recommendations=recommendations,
                compliance_notes="This report focuses on specific vulnerabilities and may not represent the complete security posture."
            )
            
            # Generate report
            generated_files = await self.report_generator.generate_report(
                report_data=report_data,
                formats=formats,
                filename_prefix=filename_prefix or f"vulnerability_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            )
            
            logger.info(f"Successfully generated vulnerability report with {len(generated_files)} formats")
            return generated_files
            
        except Exception as e:
            logger.error(f"Failed to generate vulnerability report: {e}")
            raise
    
    async def generate_summary_report(self,
                                    date_range: tuple = None,
                                    formats: List[str] = None,
                                    filename_prefix: str = None) -> Dict[str, str]:
        """Generate summary report across multiple campaigns
        
        Args:
            date_range: Tuple of (start_date, end_date) for filtering
            formats: List of formats to generate
            filename_prefix: Custom filename prefix
            
        Returns:
            Dictionary mapping format to generated file path
        """
        try:
            logger.info("Generating summary report across campaigns")
            
            # Get all campaigns in date range
            campaigns = await self._get_campaigns_in_range(date_range)
            if not campaigns:
                raise ValueError("No campaigns found in specified range")
            
            # Aggregate data from all campaigns
            all_vulnerabilities = []
            all_targets = set()
            all_tools = set()
            campaign_names = []
            
            earliest_scan = None
            latest_scan = None
            
            for campaign in campaigns:
                campaign_vulns = await self._get_campaign_vulnerabilities(campaign['id'], include_all_findings=True)
                all_vulnerabilities.extend(campaign_vulns)
                
                # Update aggregates
                if campaign.get('targets'):
                    all_targets.update(campaign['targets'])
                if campaign.get('tools_used'):
                    all_tools.update(campaign['tools_used'])
                campaign_names.append(campaign['name'])
                
                # Track date range
                if campaign.get('created_at'):
                    if earliest_scan is None or campaign['created_at'] < earliest_scan:
                        earliest_scan = campaign['created_at']
                    if latest_scan is None or campaign['created_at'] > latest_scan:
                        latest_scan = campaign['created_at']
            
            # Create summary metadata
            metadata = ScanMetadata(
                campaign_id="summary_" + datetime.now().strftime("%Y%m%d_%H%M%S"),
                campaign_name=f"Summary Report ({len(campaigns)} campaigns)",
                scan_start=earliest_scan or datetime.now(timezone.utc),
                scan_end=latest_scan or datetime.now(timezone.utc),
                targets=list(all_targets),
                tools_used=list(all_tools),
                scan_types=["summary_analysis"],
                total_vulnerabilities=len(all_vulnerabilities),
                severity_breakdown=self._calculate_severity_breakdown(all_vulnerabilities),
                operator="NexusScan User"
            )
            
            # Generate enhanced summaries for multi-campaign report
            executive_summary = await self._generate_summary_executive_summary(campaigns, all_vulnerabilities)
            technical_findings = await self._generate_summary_technical_findings(campaigns, all_vulnerabilities)
            recommendations = await self._generate_summary_recommendations(campaigns, all_vulnerabilities)
            
            # Build report data
            report_data = ReportData(
                metadata=metadata,
                vulnerabilities=all_vulnerabilities[:50],  # Top 50 vulnerabilities for summary
                executive_summary=executive_summary,
                technical_findings=technical_findings,
                recommendations=recommendations,
                compliance_notes=f"This summary report aggregates findings from {len(campaigns)} security campaigns: {', '.join(campaign_names[:5])}{'...' if len(campaign_names) > 5 else ''}"
            )
            
            # Generate report
            generated_files = await self.report_generator.generate_report(
                report_data=report_data,
                formats=formats,
                filename_prefix=filename_prefix or f"summary_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            )
            
            logger.info(f"Successfully generated summary report with {len(generated_files)} formats")
            return generated_files
            
        except Exception as e:
            logger.error(f"Failed to generate summary report: {e}")
            raise
    
    async def _get_campaign_data(self, campaign_id: str) -> Optional[Dict[str, Any]]:
        """Get campaign data from database"""
        try:
            # This would normally query the database
            # For now, return mock data structure
            return {
                'id': campaign_id,
                'name': f'Security Campaign {campaign_id[:8]}',
                'targets': ['127.0.0.1', 'localhost'],
                'created_at': datetime.now(timezone.utc),
                'status': 'completed'
            }
        except Exception as e:
            logger.error(f"Failed to get campaign data: {e}")
            return None
    
    async def _get_campaign_vulnerabilities(self, campaign_id: str, include_all: bool = True) -> List[VulnerabilityData]:
        """Get vulnerabilities for a campaign"""
        try:
            # Mock vulnerability data for demonstration
            # In production, this would query the database
            vulnerabilities = [
                VulnerabilityData(
                    id=f"vuln_{i}",
                    title=f"Sample Vulnerability {i}",
                    severity=['critical', 'high', 'medium', 'low', 'info'][i % 5],
                    description=f"This is a sample vulnerability {i} found during testing",
                    target="127.0.0.1",
                    port=80 + i,
                    protocol="TCP",
                    cvss_score=7.5 - (i * 0.5),
                    cve_ids=[f"CVE-2024-{1000 + i}"],
                    remediation=f"Apply security patch for vulnerability {i}",
                    references=[f"https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2024-{1000 + i}"],
                    tool_name=['nmap', 'nuclei', 'sqlmap'][i % 3],
                    discovered_at=datetime.now(timezone.utc)
                )
                for i in range(10)  # Generate 10 sample vulnerabilities
            ]
            
            if not include_all:
                # Filter to only high/critical
                vulnerabilities = [v for v in vulnerabilities if v.severity.lower() in ['critical', 'high']]
            
            return vulnerabilities
            
        except Exception as e:
            logger.error(f"Failed to get campaign vulnerabilities: {e}")
            return []
    
    async def _get_campaign_scan_sessions(self, campaign_id: str) -> List[Dict[str, Any]]:
        """Get scan sessions for a campaign"""
        try:
            # Mock scan session data
            return [
                {
                    'id': f'session_{i}',
                    'tool_name': ['nmap', 'nuclei', 'sqlmap'][i % 3],
                    'started_at': datetime.now(timezone.utc),
                    'completed_at': datetime.now(timezone.utc),
                    'status': 'completed'
                }
                for i in range(3)
            ]
        except Exception as e:
            logger.error(f"Failed to get scan sessions: {e}")
            return []
    
    async def _get_vulnerability_data(self, vulnerability_id: str) -> Optional[VulnerabilityData]:
        """Get specific vulnerability data"""
        try:
            # Mock single vulnerability data
            return VulnerabilityData(
                id=vulnerability_id,
                title=f"Vulnerability {vulnerability_id}",
                severity="high",
                description="Sample vulnerability for testing",
                target="127.0.0.1",
                port=80,
                protocol="TCP",
                cvss_score=7.5,
                cve_ids=["CVE-2024-1001"],
                remediation="Apply security patches",
                references=["https://example.com/vuln"],
                tool_name="nuclei",
                discovered_at=datetime.now(timezone.utc)
            )
        except Exception as e:
            logger.error(f"Failed to get vulnerability data: {e}")
            return None
    
    async def _get_campaigns_in_range(self, date_range: tuple = None) -> List[Dict[str, Any]]:
        """Get campaigns in date range"""
        try:
            # Mock campaign data
            return [
                {
                    'id': f'campaign_{i}',
                    'name': f'Security Campaign {i}',
                    'targets': ['127.0.0.1'],
                    'tools_used': ['nmap', 'nuclei'],
                    'created_at': datetime.now(timezone.utc)
                }
                for i in range(3)
            ]
        except Exception as e:
            logger.error(f"Failed to get campaigns: {e}")
            return []
    
    async def _build_report_data(self, campaign_data: Dict[str, Any], 
                               vulnerabilities: List[VulnerabilityData],
                               scan_sessions: List[Dict[str, Any]]) -> ReportData:
        """Build complete report data structure"""
        
        # Calculate scan timeframe
        if scan_sessions:
            scan_start = min(session.get('started_at', datetime.now(timezone.utc)) for session in scan_sessions)
            scan_end = max(session.get('completed_at', datetime.now(timezone.utc)) for session in scan_sessions)
        else:
            scan_start = scan_end = datetime.now(timezone.utc)
        
        # Extract tools used
        tools_used = list(set(session.get('tool_name', 'unknown') for session in scan_sessions))
        
        # Create metadata
        metadata = ScanMetadata(
            campaign_id=campaign_data['id'],
            campaign_name=campaign_data['name'],
            scan_start=scan_start,
            scan_end=scan_end,
            targets=campaign_data.get('targets', []),
            tools_used=tools_used,
            scan_types=['network_discovery', 'vulnerability_scan', 'web_application_scan'],
            total_vulnerabilities=len(vulnerabilities),
            severity_breakdown=self._calculate_severity_breakdown(vulnerabilities),
            operator="NexusScan User"
        )
        
        # Generate intelligent content
        executive_summary = await self.report_generator.generate_executive_summary(vulnerabilities, metadata)
        technical_findings = await self.report_generator.generate_technical_findings(vulnerabilities)
        recommendations = await self.report_generator.generate_recommendations(vulnerabilities, metadata)
        
        return ReportData(
            metadata=metadata,
            vulnerabilities=vulnerabilities,
            executive_summary=executive_summary,
            technical_findings=technical_findings,
            recommendations=recommendations,
            compliance_notes="This assessment was conducted using automated security scanning tools. Manual verification is recommended for business-critical findings."
        )
    
    def _calculate_severity_breakdown(self, vulnerabilities: List[VulnerabilityData]) -> Dict[str, int]:
        """Calculate vulnerability count by severity"""
        breakdown = {'critical': 0, 'high': 0, 'medium': 0, 'low': 0, 'info': 0}
        for vuln in vulnerabilities:
            severity = vuln.severity.lower()
            if severity in breakdown:
                breakdown[severity] += 1
        return breakdown
    
    async def _store_report_metadata(self, campaign_id: str, generated_files: Dict[str, str], report_data: ReportData):
        """Store report generation metadata in database"""
        try:
            # This would normally store in the reports table
            logger.info(f"Storing report metadata for campaign {campaign_id}")
            # Implementation would use self.db_manager to store metadata
        except Exception as e:
            logger.error(f"Failed to store report metadata: {e}")
    
    async def _generate_summary_executive_summary(self, campaigns: List[Dict], vulnerabilities: List[VulnerabilityData]) -> str:
        """Generate executive summary for multi-campaign report"""
        total_vulns = len(vulnerabilities)
        total_campaigns = len(campaigns)
        
        if total_vulns == 0:
            return f"""
            This summary report covers {total_campaigns} security campaigns with no vulnerabilities identified. 
            All assessed systems appear to maintain strong security postures across the evaluation period.
            Continued regular assessments are recommended to maintain this security baseline.
            """
        
        critical_high = sum(1 for v in vulnerabilities if v.severity.lower() in ['critical', 'high'])
        avg_vulns_per_campaign = total_vulns / total_campaigns if total_campaigns > 0 else 0
        
        return f"""
        This summary report aggregates findings from {total_campaigns} security campaigns, identifying 
        {total_vulns} total vulnerabilities across all assessed environments. The average vulnerability 
        count per campaign is {avg_vulns_per_campaign:.1f}, with {critical_high} high or critical 
        severity findings requiring immediate attention.
        
        Trend analysis across campaigns indicates recurring security issues that should be addressed 
        through improved security policies, enhanced monitoring, and comprehensive staff training. 
        Organizations should prioritize establishing consistent security baselines across all environments.
        """
    
    async def _generate_summary_technical_findings(self, campaigns: List[Dict], vulnerabilities: List[VulnerabilityData]) -> str:
        """Generate technical findings for multi-campaign report"""
        if not vulnerabilities:
            return "No technical vulnerabilities identified across all campaigns."
        
        # Analyze patterns across campaigns
        tool_usage = {}
        target_analysis = {}
        
        for vuln in vulnerabilities:
            # Track tool effectiveness
            if vuln.tool_name not in tool_usage:
                tool_usage[vuln.tool_name] = {'total': 0, 'critical': 0, 'high': 0}
            tool_usage[vuln.tool_name]['total'] += 1
            if vuln.severity.lower() in ['critical', 'high']:
                tool_usage[vuln.tool_name][vuln.severity.lower()] += 1
            
            # Track target patterns
            if vuln.target not in target_analysis:
                target_analysis[vuln.target] = 0
            target_analysis[vuln.target] += 1
        
        findings = [f"Analysis across {len(campaigns)} campaigns reveals:"]
        findings.append("\nTool Effectiveness:")
        for tool, stats in tool_usage.items():
            findings.append(f"• {tool}: {stats['total']} findings ({stats.get('critical', 0)} critical, {stats.get('high', 0)} high)")
        
        findings.append(f"\nMost Affected Targets:")
        sorted_targets = sorted(target_analysis.items(), key=lambda x: x[1], reverse=True)
        for target, count in sorted_targets[:5]:
            findings.append(f"• {target}: {count} vulnerabilities")
        
        return "\n".join(findings)
    
    async def _generate_summary_recommendations(self, campaigns: List[Dict], vulnerabilities: List[VulnerabilityData]) -> List[str]:
        """Generate recommendations for multi-campaign summary"""
        if not vulnerabilities:
            return [
                "Maintain current security assessment schedule",
                "Continue monitoring for emerging threats",
                "Implement proactive security controls"
            ]
        
        recommendations = [
            f"Establish enterprise-wide vulnerability management program to address {len(vulnerabilities)} identified issues",
            "Implement consistent security baselines across all assessed environments",
            "Develop centralized security monitoring and incident response capabilities",
            "Create security training programs based on recurring vulnerability patterns",
            "Establish regular security assessment cycles with defined remediation SLAs"
        ]
        
        # Add tool-specific recommendations
        tools_used = set(v.tool_name for v in vulnerabilities if v.tool_name)
        if len(tools_used) > 1:
            recommendations.append("Integrate security scanning tools into CI/CD pipelines for continuous assessment")
        
        return recommendations[:8]  # Return top 8 recommendations