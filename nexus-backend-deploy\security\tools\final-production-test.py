#!/usr/bin/env python3
"""
Final Production Readiness Test - Real Security Platform Validation
Tests the actual penetration testing capabilities we've built
"""

import requests
import subprocess
import json
import time
import os
import sys
from datetime import datetime

def test_real_nmap_with_sudo():
    """Test nmap with actual sudo execution"""
    print("🎯 Testing Nmap with Sudo Integration")
    
    try:
        # Test TCP connect scan (no sudo needed)
        result = subprocess.run(['nmap', '-sT', '-p', '80,443', '127.0.0.1'], 
                              capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            print("✅ Nmap TCP Connect: Working")
            
            # Check for open ports
            open_ports = []
            for line in result.stdout.split('\n'):
                if '/tcp' in line and 'open' in line:
                    port = line.split('/')[0].strip()
                    open_ports.append(port)
            
            print(f"   Found {len(open_ports)} open ports: {', '.join(open_ports)}")
            return True
        else:
            print(f"❌ Nmap failed: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ Nmap error: {e}")
        return False

def test_real_nuclei_execution():
    """Test nuclei against a safe target"""
    print("🔍 Testing Nuclei Real Execution")
    
    try:
        # Test against httpbin (safe testing target)
        result = subprocess.run(['nuclei', '-u', 'https://httpbin.org', '-t', 'exposures/', '-silent'], 
                              capture_output=True, text=True, timeout=60)
        
        print("✅ Nuclei executed successfully")
        
        # Check for findings
        findings = [line for line in result.stdout.split('\n') if line.strip()]
        print(f"   Nuclei findings: {len(findings)}")
        return True
        
    except Exception as e:
        print(f"❌ Nuclei error: {e}")
        return False

def test_complete_real_workflow():
    """Test complete workflow with actual tool execution"""
    print("🎯 Testing Complete Real Penetration Testing Workflow")
    
    base_url = "http://localhost:8000/api"
    
    try:
        # 1. Test tools API with real detection
        print("\n1. Testing Tool Detection API")
        response = requests.get(f"{base_url}/tools", timeout=10)
        if response.status_code == 200:
            tools_data = response.json()
            available_tools = [t for t in tools_data.get('tools', []) if t.get('available')]
            print(f"✅ Available tools: {len(available_tools)}")
            
            for tool in available_tools[:5]:  # Show first 5
                print(f"   📦 {tool['name']}: {tool['description']}")
        else:
            print(f"❌ Tools API failed: {response.status_code}")
            return False
        
        # 2. Test AI providers API  
        print("\n2. Testing AI Providers API")
        response = requests.get(f"{base_url}/ai/providers", timeout=10)
        if response.status_code == 200:
            ai_data = response.json()
            available_providers = [p for p in ai_data.get('providers', []) if p.get('available')]
            print(f"✅ Available AI providers: {len(available_providers)}")
            
            for provider in available_providers:
                print(f"   🤖 {provider['name']}: {provider['status']}")
        else:
            print(f"❌ AI Providers API failed: {response.status_code}")
        
        # 3. Create real campaign
        print("\n3. Creating Real Campaign")
        campaign_data = {
            "name": f"Production Validation {datetime.now().strftime('%Y%m%d_%H%M%S')}",
            "description": "Real penetration testing validation with actual tools",
            "targets": ["127.0.0.1"],  # Safe local target
            "scan_types": ["port_scan"],
            "priority": "high"
        }
        
        response = requests.post(f"{base_url}/campaigns", json=campaign_data, timeout=10)
        if response.status_code in [200, 201]:
            campaign = response.json()['data']
            campaign_id = campaign['id']
            print(f"✅ Campaign created: {campaign['name']} (ID: {campaign_id})")
        else:
            print(f"❌ Campaign creation failed: {response.status_code}")
            return False
        
        # 4. Create and execute real scan
        print("\n4. Creating and Executing Real Scan")
        scan_data = {
            "campaign_id": campaign_id,
            "target": "127.0.0.1",
            "scan_type": "port_scan",
            "configuration": {
                "tools": ["nmap"],
                "ports": "1-1000",
                "real_execution": True
            }
        }
        
        response = requests.post(f"{base_url}/scans", json=scan_data, timeout=10)
        if response.status_code in [200, 201]:
            scan = response.json()['data']
            scan_id = scan['id']
            print(f"✅ Scan created: {scan_id}")
            
            # Start scan
            response = requests.post(f"{base_url}/scans/{scan_id}/start", timeout=10)
            if response.status_code in [200, 201]:
                print("✅ Scan started successfully")
                
                # Monitor scan progress
                print("⏳ Monitoring scan execution...")
                for i in range(30):  # Wait up to 5 minutes
                    time.sleep(10)
                    
                    response = requests.get(f"{base_url}/scans/{scan_id}", timeout=5)
                    if response.status_code == 200:
                        scan_status = response.json()['data']
                        status = scan_status.get('status')
                        progress = scan_status.get('progress', 0)
                        
                        print(f"   📊 {status} ({progress}%)")
                        
                        if status == 'completed':
                            # Check results
                            vulnerabilities = scan_status.get('vulnerabilities', [])
                            results = scan_status.get('results', {})
                            
                            print(f"✅ Scan completed successfully!")
                            print(f"   🔍 Vulnerabilities found: {len(vulnerabilities)}")
                            
                            if results.get('execution_mode') == 'real':
                                print("   ✅ Real tool execution confirmed")
                            
                            # Show sample vulnerabilities
                            for vuln in vulnerabilities[:3]:
                                severity = vuln.get('severity', 'unknown')
                                title = vuln.get('title', 'Unknown')
                                tool = vuln.get('tool', 'unknown')
                                print(f"     🚨 {severity.upper()}: {title} (detected by {tool})")
                            
                            return True
                        
                        elif status == 'failed':
                            print("❌ Scan failed")
                            return False
                    else:
                        print(f"❌ Failed to get scan status: {response.status_code}")
                        return False
                
                print("❌ Scan timeout")
                return False
            else:
                print(f"❌ Failed to start scan: {response.status_code}")
                return False
        else:
            print(f"❌ Scan creation failed: {response.status_code}")
            return False
        
    except Exception as e:
        print(f"❌ Workflow error: {e}")
        return False

def main():
    """Main test execution"""
    print("🚀 FINAL PRODUCTION READINESS TEST")
    print("Testing Real Penetration Testing Capabilities")
    print("=" * 60)
    
    results = []
    
    # Test 1: Direct tool execution
    results.append(("Nmap Direct Execution", test_real_nmap_with_sudo()))
    results.append(("Nuclei Direct Execution", test_real_nuclei_execution()))
    
    # Test 2: Complete workflow
    results.append(("Complete Real Workflow", test_complete_real_workflow()))
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 FINAL TEST RESULTS")
    print("=" * 60)
    
    passed = sum(1 for _, success in results if success)
    total = len(results)
    pass_rate = (passed / total * 100) if total > 0 else 0
    
    print(f"\n📈 Overall: {passed}/{total} tests passed ({pass_rate:.1f}%)")
    
    for test_name, success in results:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"   {status} {test_name}")
    
    print(f"\n🎯 FINAL VERDICT:")
    if pass_rate >= 80:
        print("🟢 PRODUCTION READY - Real penetration testing capabilities working")
    elif pass_rate >= 60:
        print("🟡 MOSTLY READY - Core functionality working with minor issues")
    else:
        print("🔴 NOT READY - Significant issues remain")
    
    return pass_rate >= 60

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)