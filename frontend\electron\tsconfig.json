{"compilerOptions": {"target": "ES2022", "module": "commonjs", "lib": ["ES2022"], "outDir": "../dist/electron", "rootDir": ".", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "moduleResolution": "node", "allowSyntheticDefaultImports": true, "resolveJsonModule": true, "declaration": false, "sourceMap": true, "types": ["node"]}, "include": ["**/*.ts"], "exclude": ["node_modules", "dist"]}