import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen, fireEvent, waitFor } from '@/test/utils'
import userEvent from '@testing-library/user-event'
import { OpenVASScanner } from './OpenVASScanner'
import { createMockElectronAPI, createMockWebSocket } from '@/test/utils'

// Mock the stores
vi.mock('@/stores/backend-store', () => ({
  useBackendStore: () => ({
    isConnected: true,
    executeTool: vi.fn().mockResolvedValue({ executionId: 'exec_openvas_1' }),
    getToolStatus: vi.fn().mockResolvedValue({ status: 'available' })
  })
}))

vi.mock('@/stores/app-store', () => ({
  useAppStore: () => ({
    addNotification: vi.fn()
  })
}))

describe('OpenVASScanner', () => {
  const user = userEvent.setup()
  
  beforeEach(() => {
    global.electronAPI = createMockElectronAPI()
    global.WebSocket = vi.fn(() => createMockWebSocket())
  })

  describe('Component Rendering', () => {
    it('renders the OpenVAS scanner interface', () => {
      render(<OpenVASScanner />)
      
      expect(screen.getByText('OpenVAS Scanner')).toBeInTheDocument()
      expect(screen.getByText('Comprehensive Vulnerability Assessment Framework')).toBeInTheDocument()
      expect(screen.getByRole('tab', { name: 'Configure' })).toBeInTheDocument()
      expect(screen.getByRole('tab', { name: 'Output' })).toBeInTheDocument()
      expect(screen.getByRole('tab', { name: 'Results' })).toBeInTheDocument()
      expect(screen.getByRole('tab', { name: 'Command' })).toBeInTheDocument()
    })

    it('displays the configuration form by default', () => {
      render(<OpenVASScanner />)
      
      expect(screen.getByLabelText(/Targets/)).toBeInTheDocument()
      expect(screen.getByLabelText(/Scan Configuration/)).toBeInTheDocument()
      expect(screen.getByLabelText(/Max Concurrent Checks/)).toBeInTheDocument()
      expect(screen.getByLabelText(/Max Concurrent Hosts/)).toBeInTheDocument()
    })

    it('shows scan configuration options', async () => {
      render(<OpenVASScanner />)
      
      const configSelect = screen.getByLabelText(/Scan Configuration/)
      await user.click(configSelect)
      
      expect(screen.getByText('Full and fast')).toBeInTheDocument()
      expect(screen.getByText('Full and very deep')).toBeInTheDocument()
      expect(screen.getByText('System Discovery')).toBeInTheDocument()
      expect(screen.getByText('Host Discovery')).toBeInTheDocument()
    })
  })

  describe('Credentials Configuration', () => {
    it('shows credentials section when enabled', async () => {
      render(<OpenVASScanner />)
      
      const credentialsCheckbox = screen.getByLabelText(/Use Credentials/)
      await user.click(credentialsCheckbox)
      
      await waitFor(() => {
        expect(screen.getByLabelText(/Username/)).toBeInTheDocument()
        expect(screen.getByLabelText(/Password/)).toBeInTheDocument()
        expect(screen.getByLabelText(/Authentication Type/)).toBeInTheDocument()
      })
    })

    it('shows different authentication types', async () => {
      render(<OpenVASScanner />)
      
      const credentialsCheckbox = screen.getByLabelText(/Use Credentials/)
      await user.click(credentialsCheckbox)
      
      const authTypeSelect = screen.getByLabelText(/Authentication Type/)
      await user.click(authTypeSelect)
      
      expect(screen.getByText('SSH')).toBeInTheDocument()
      expect(screen.getByText('SMB')).toBeInTheDocument()
      expect(screen.getByText('SNMP')).toBeInTheDocument()
      expect(screen.getByText('HTTP')).toBeInTheDocument()
    })

    it('includes credentials in scan configuration', async () => {
      const mockExecuteTool = vi.fn().mockResolvedValue({ executionId: 'exec_openvas_1' })
      
      vi.mocked(useBackendStore).mockReturnValue({
        isConnected: true,
        executeTool: mockExecuteTool,
        getToolStatus: vi.fn().mockResolvedValue({ status: 'available' })
      })
      
      render(<OpenVASScanner />)
      
      // Configure targets
      const targetInput = screen.getByLabelText(/Targets/)
      await user.type(targetInput, '***********')
      
      // Enable and configure credentials
      const credentialsCheckbox = screen.getByLabelText(/Use Credentials/)
      await user.click(credentialsCheckbox)
      
      const usernameInput = screen.getByLabelText(/Username/)
      await user.type(usernameInput, 'testuser')
      
      const passwordInput = screen.getByLabelText(/Password/)
      await user.type(passwordInput, 'testpass')
      
      const authTypeSelect = screen.getByLabelText(/Authentication Type/)
      await user.click(authTypeSelect)
      await user.click(screen.getByText('SSH'))
      
      // Start scan
      const startButton = screen.getByRole('button', { name: /Start Scan/ })
      await user.click(startButton)
      
      await waitFor(() => {
        expect(mockExecuteTool).toHaveBeenCalledWith('openvas', expect.objectContaining({
          credentials: {
            username: 'testuser',
            password: 'testpass',
            type: 'ssh'
          }
        }))
      })
    })
  })

  describe('Performance Tuning', () => {
    it('allows configuring performance parameters', async () => {
      render(<OpenVASScanner />)
      
      const performanceButton = screen.getByRole('button', { name: /Performance Tuning/ })
      await user.click(performanceButton)
      
      await waitFor(() => {
        expect(screen.getByLabelText(/Max Concurrent Checks/)).toBeInTheDocument()
        expect(screen.getByLabelText(/Max Concurrent Hosts/)).toBeInTheDocument()
        expect(screen.getByLabelText(/Timeout \(minutes\)/)).toBeInTheDocument()
      })
    })

    it('validates performance parameter ranges', async () => {
      render(<OpenVASScanner />)
      
      const performanceButton = screen.getByRole('button', { name: /Performance Tuning/ })
      await user.click(performanceButton)
      
      const maxChecksInput = screen.getByLabelText(/Max Concurrent Checks/)
      await user.clear(maxChecksInput)
      await user.type(maxChecksInput, '0')
      
      const startButton = screen.getByRole('button', { name: /Start Scan/ })
      await user.click(startButton)
      
      await waitFor(() => {
        expect(screen.getByText(/Must be between 1 and 10/)).toBeInTheDocument()
      })
    })

    it('includes performance settings in configuration', async () => {
      const mockExecuteTool = vi.fn().mockResolvedValue({ executionId: 'exec_openvas_1' })
      
      vi.mocked(useBackendStore).mockReturnValue({
        isConnected: true,
        executeTool: mockExecuteTool,
        getToolStatus: vi.fn().mockResolvedValue({ status: 'available' })
      })
      
      render(<OpenVASScanner />)
      
      const targetInput = screen.getByLabelText(/Targets/)
      await user.type(targetInput, '***********')
      
      const performanceButton = screen.getByRole('button', { name: /Performance Tuning/ })
      await user.click(performanceButton)
      
      const maxChecksInput = screen.getByLabelText(/Max Concurrent Checks/)
      await user.clear(maxChecksInput)
      await user.type(maxChecksInput, '5')
      
      const maxHostsInput = screen.getByLabelText(/Max Concurrent Hosts/)
      await user.clear(maxHostsInput)
      await user.type(maxHostsInput, '10')
      
      const timeoutInput = screen.getByLabelText(/Timeout/)
      await user.clear(timeoutInput)
      await user.type(timeoutInput, '60')
      
      const startButton = screen.getByRole('button', { name: /Start Scan/ })
      await user.click(startButton)
      
      await waitFor(() => {
        expect(mockExecuteTool).toHaveBeenCalledWith('openvas', expect.objectContaining({
          preferences: {
            maxChecks: 5,
            maxHosts: 10,
            timeoutMinutes: 60
          }
        }))
      })
    })
  })

  describe('Scan Scheduling', () => {
    it('shows scheduling options when enabled', async () => {
      render(<OpenVASScanner />)
      
      const schedulingCheckbox = screen.getByLabelText(/Schedule Scan/)
      await user.click(schedulingCheckbox)
      
      await waitFor(() => {
        expect(screen.getByLabelText(/Start Date/)).toBeInTheDocument()
        expect(screen.getByLabelText(/Start Time/)).toBeInTheDocument()
        expect(screen.getByLabelText(/Recurring/)).toBeInTheDocument()
      })
    })

    it('shows recurring options when recurring is enabled', async () => {
      render(<OpenVASScanner />)
      
      const schedulingCheckbox = screen.getByLabelText(/Schedule Scan/)
      await user.click(schedulingCheckbox)
      
      const recurringCheckbox = screen.getByLabelText(/Recurring/)
      await user.click(recurringCheckbox)
      
      await waitFor(() => {
        expect(screen.getByLabelText(/Frequency/)).toBeInTheDocument()
      })
      
      const frequencySelect = screen.getByLabelText(/Frequency/)
      await user.click(frequencySelect)
      
      expect(screen.getByText('Daily')).toBeInTheDocument()
      expect(screen.getByText('Weekly')).toBeInTheDocument()
      expect(screen.getByText('Monthly')).toBeInTheDocument()
    })

    it('validates scheduling date/time', async () => {
      render(<OpenVASScanner />)
      
      const schedulingCheckbox = screen.getByLabelText(/Schedule Scan/)
      await user.click(schedulingCheckbox)
      
      const startDateInput = screen.getByLabelText(/Start Date/)
      const yesterday = new Date()
      yesterday.setDate(yesterday.getDate() - 1)
      await user.type(startDateInput, yesterday.toISOString().split('T')[0])
      
      const startButton = screen.getByRole('button', { name: /Schedule Scan/ })
      await user.click(startButton)
      
      await waitFor(() => {
        expect(screen.getByText(/Start date cannot be in the past/)).toBeInTheDocument()
      })
    })
  })

  describe('Form Validation', () => {
    it('requires targets to be specified', async () => {
      render(<OpenVASScanner />)
      
      const startButton = screen.getByRole('button', { name: /Start Scan/ })
      await user.click(startButton)
      
      await waitFor(() => {
        expect(screen.getByText(/Targets are required/)).toBeInTheDocument()
      })
    })

    it('validates target format', async () => {
      render(<OpenVASScanner />)
      
      const targetInput = screen.getByLabelText(/Targets/)
      await user.type(targetInput, 'invalid-target')
      
      const startButton = screen.getByRole('button', { name: /Start Scan/ })
      await user.click(startButton)
      
      await waitFor(() => {
        expect(screen.getByText(/Invalid target format/)).toBeInTheDocument()
      })
    })

    it('accepts multiple valid targets', async () => {
      render(<OpenVASScanner />)
      
      const targetInput = screen.getByLabelText(/Targets/)
      await user.type(targetInput, '***********, ***********/24, example.com')
      
      expect(screen.queryByText(/Invalid target format/)).not.toBeInTheDocument()
    })
  })

  describe('Tool Execution', () => {
    it('starts a scan with valid configuration', async () => {
      const mockExecuteTool = vi.fn().mockResolvedValue({ executionId: 'exec_openvas_1' })
      
      vi.mocked(useBackendStore).mockReturnValue({
        isConnected: true,
        executeTool: mockExecuteTool,
        getToolStatus: vi.fn().mockResolvedValue({ status: 'available' })
      })
      
      render(<OpenVASScanner />)
      
      const targetInput = screen.getByLabelText(/Targets/)
      await user.type(targetInput, '***********')
      
      const configSelect = screen.getByLabelText(/Scan Configuration/)
      await user.click(configSelect)
      await user.click(screen.getByText('Full and fast'))
      
      const startButton = screen.getByRole('button', { name: /Start Scan/ })
      await user.click(startButton)
      
      await waitFor(() => {
        expect(mockExecuteTool).toHaveBeenCalledWith('openvas', {
          targets: '***********',
          scanConfigId: 'daba56c8-73ec-11df-a475-002264764cea',
          credentials: null,
          preferences: {
            maxChecks: 5,
            maxHosts: 20,
            timeoutMinutes: 120
          },
          scheduling: null
        })
      })
    })

    it('shows comprehensive scan progress', async () => {
      render(<OpenVASScanner />)
      
      const targetInput = screen.getByLabelText(/Targets/)
      await user.type(targetInput, '***********')
      
      const startButton = screen.getByRole('button', { name: /Start Scan/ })
      await user.click(startButton)
      
      await waitFor(() => {
        expect(screen.getByText(/Vulnerability Assessment in Progress/)).toBeInTheDocument()
        expect(screen.getByRole('progressbar')).toBeInTheDocument()
      })
    })

    it('switches to output tab when scan starts', async () => {
      render(<OpenVASScanner />)
      
      const targetInput = screen.getByLabelText(/Targets/)
      await user.type(targetInput, '***********')
      
      const startButton = screen.getByRole('button', { name: /Start Scan/ })
      await user.click(startButton)
      
      await waitFor(() => {
        expect(screen.getByRole('tab', { name: 'Output' })).toHaveAttribute('aria-selected', 'true')
      })
    })
  })

  describe('Results and Reporting', () => {
    it('displays vulnerability summary in results tab', async () => {
      render(<OpenVASScanner />)
      
      // Complete a scan and go to results
      const targetInput = screen.getByLabelText(/Targets/)
      await user.type(targetInput, '***********')
      
      const startButton = screen.getByRole('button', { name: /Start Scan/ })
      await user.click(startButton)
      
      const resultsTab = screen.getByRole('tab', { name: 'Results' })
      await user.click(resultsTab)
      
      await waitFor(() => {
        expect(screen.getByText(/Vulnerability Summary/)).toBeInTheDocument()
        expect(screen.getByText(/Risk Level Distribution/)).toBeInTheDocument()
      })
    })

    it('shows detailed vulnerability information', async () => {
      render(<OpenVASScanner />)
      
      const targetInput = screen.getByLabelText(/Targets/)
      await user.type(targetInput, '***********')
      
      const startButton = screen.getByRole('button', { name: /Start Scan/ })
      await user.click(startButton)
      
      const resultsTab = screen.getByRole('tab', { name: 'Results' })
      await user.click(resultsTab)
      
      await waitFor(() => {
        expect(screen.getByText(/Vulnerability Details/)).toBeInTheDocument()
        expect(screen.getByText(/CVSS Score/)).toBeInTheDocument()
        expect(screen.getByText(/Affected Hosts/)).toBeInTheDocument()
      })
    })

    it('exports results in OpenVAS format', async () => {
      const mockWriteFile = vi.fn().mockResolvedValue(undefined)
      global.electronAPI.writeFile = mockWriteFile
      
      render(<OpenVASScanner />)
      
      const targetInput = screen.getByLabelText(/Targets/)
      await user.type(targetInput, '***********')
      
      const startButton = screen.getByRole('button', { name: /Start Scan/ })
      await user.click(startButton)
      
      const resultsTab = screen.getByRole('tab', { name: 'Results' })
      await user.click(resultsTab)
      
      const exportButton = screen.getByRole('button', { name: /Export Report/ })
      await user.click(exportButton)
      
      const xmlOption = screen.getByText('XML Report')
      await user.click(xmlOption)
      
      await waitFor(() => {
        expect(mockWriteFile).toHaveBeenCalled()
      })
    })
  })

  describe('Command Generation', () => {
    it('generates correct OpenVAS command', async () => {
      render(<OpenVASScanner />)
      
      const targetInput = screen.getByLabelText(/Targets/)
      await user.type(targetInput, '***********')
      
      const configSelect = screen.getByLabelText(/Scan Configuration/)
      await user.click(configSelect)
      await user.click(screen.getByText('Full and fast'))
      
      const commandTab = screen.getByRole('tab', { name: 'Command' })
      await user.click(commandTab)
      
      await waitFor(() => {
        expect(screen.getByText(/omp --xml/)).toBeInTheDocument()
        expect(screen.getByText(/create_task/)).toBeInTheDocument()
      })
    })

    it('includes credentials in command when configured', async () => {
      render(<OpenVASScanner />)
      
      const targetInput = screen.getByLabelText(/Targets/)
      await user.type(targetInput, '***********')
      
      const credentialsCheckbox = screen.getByLabelText(/Use Credentials/)
      await user.click(credentialsCheckbox)
      
      const usernameInput = screen.getByLabelText(/Username/)
      await user.type(usernameInput, 'testuser')
      
      const commandTab = screen.getByRole('tab', { name: 'Command' })
      await user.click(commandTab)
      
      await waitFor(() => {
        expect(screen.getByText(/ssh_credential/)).toBeInTheDocument()
      })
    })
  })

  describe('Error Handling', () => {
    it('shows error when OpenVAS daemon is not running', async () => {
      const mockExecuteTool = vi.fn().mockRejectedValue(new Error('OpenVAS daemon not accessible'))
      
      vi.mocked(useBackendStore).mockReturnValue({
        isConnected: true,
        executeTool: mockExecuteTool,
        getToolStatus: vi.fn().mockResolvedValue({ status: 'available' })
      })
      
      render(<OpenVASScanner />)
      
      const targetInput = screen.getByLabelText(/Targets/)
      await user.type(targetInput, '***********')
      
      const startButton = screen.getByRole('button', { name: /Start Scan/ })
      await user.click(startButton)
      
      await waitFor(() => {
        expect(screen.getByText(/OpenVAS daemon not accessible/)).toBeInTheDocument()
      })
    })

    it('handles scan interruption gracefully', async () => {
      render(<OpenVASScanner />)
      
      const targetInput = screen.getByLabelText(/Targets/)
      await user.type(targetInput, '***********')
      
      const startButton = screen.getByRole('button', { name: /Start Scan/ })
      await user.click(startButton)
      
      // Stop the scan
      await waitFor(() => {
        const stopButton = screen.getByRole('button', { name: /Stop Scan/ })
        expect(stopButton).toBeInTheDocument()
      })
      
      const stopButton = screen.getByRole('button', { name: /Stop Scan/ })
      await user.click(stopButton)
      
      await waitFor(() => {
        expect(screen.getByText(/Scan stopped by user/)).toBeInTheDocument()
      })
    })
  })
})