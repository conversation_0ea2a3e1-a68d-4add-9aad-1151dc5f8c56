#!/usr/bin/env python3
"""
Infrastructure Tools Wrappers for NexusScan Desktop
Wraps infrastructure components as security tools
"""

import asyncio
import logging
from typing import Dict, List, Optional, Callable, Any
from datetime import datetime

from security.tools.tool_registry import (
    ToolMetadata, ToolCategory, ToolCapabilities,
    ScanOptions, ScanResult, ToolStatus, register_tool
)
from security.tools.base_scanner import BaseScanner

logger = logging.getLogger(__name__)


@register_tool
class PerformanceMonitorTool(BaseScanner):
    """Performance monitoring as a security tool"""
    
    def __init__(self):
        super().__init__()
        self.simulation_available = True
    
    def get_metadata(self) -> ToolMetadata:
        return ToolMetadata(
            name="performance_monitor",
            display_name="Performance Monitor",
            description="Real-time performance metrics and system health monitoring",
            version="1.0.0",
            category=ToolCategory.CUSTOM_ANALYZER,
            capabilities=ToolCapabilities(
                supports_async=True,
                network_access_required=False,
                supported_targets=["system"],
                requires_root=False
            ),
            default_options={
                "duration": 30,  # monitoring duration in seconds
                "interval": 1,   # sampling interval
                "metrics": ["cpu", "memory", "disk", "network"]
            }
        )
    
    def check_native_availability(self) -> bool:
        """Check if performance monitoring is available"""
        try:
            import psutil
            return True
        except ImportError:
            return False
    
    async def execute_native_scan(self, options: ScanOptions,
                                  progress_callback: Optional[Callable[[float, str], None]] = None) -> ScanResult:
        """Execute performance monitoring"""
        start_time = datetime.now().isoformat()
        
        try:
            import psutil
            
            if progress_callback:
                await progress_callback(0.1, "Starting performance monitoring...")
            
            duration = options.custom_options.get("duration", 30)
            interval = options.custom_options.get("interval", 1)
            metrics = options.custom_options.get("metrics", ["cpu", "memory", "disk", "network"])
            
            collected_metrics = []
            samples = int(duration / interval)
            
            for i in range(samples):
                if progress_callback:
                    progress = 0.1 + (0.8 * i / samples)
                    await progress_callback(progress, f"Collecting metrics... {i+1}/{samples}")
                
                sample = {
                    "timestamp": datetime.now().isoformat(),
                    "cpu_percent": psutil.cpu_percent(interval=0.1) if "cpu" in metrics else None,
                    "memory_percent": psutil.virtual_memory().percent if "memory" in metrics else None,
                    "disk_usage": psutil.disk_usage('/').percent if "disk" in metrics else None,
                    "network_io": dict(psutil.net_io_counters()._asdict()) if "network" in metrics else None
                }
                collected_metrics.append(sample)
                
                if i < samples - 1:  # Don't sleep on last iteration
                    await asyncio.sleep(interval)
            
            # Analyze metrics for security insights
            vulnerabilities = []
            
            # Check for performance anomalies
            cpu_values = [m["cpu_percent"] for m in collected_metrics if m["cpu_percent"] is not None]
            if cpu_values and max(cpu_values) > 90:
                vulnerabilities.append({
                    "name": "High CPU Usage Detected",
                    "severity": "medium",
                    "description": f"CPU usage peaked at {max(cpu_values):.1f}%",
                    "type": "performance_anomaly",
                    "cve": None,
                    "remediation": "Investigate processes causing high CPU usage"
                })
            
            memory_values = [m["memory_percent"] for m in collected_metrics if m["memory_percent"] is not None]
            if memory_values and max(memory_values) > 85:
                vulnerabilities.append({
                    "name": "High Memory Usage Detected",
                    "severity": "medium",
                    "description": f"Memory usage peaked at {max(memory_values):.1f}%",
                    "type": "performance_anomaly",
                    "cve": None,
                    "remediation": "Check for memory leaks or resource-intensive processes"
                })
            
            if progress_callback:
                await progress_callback(1.0, "Performance monitoring complete")
            
            results = {
                "metrics": collected_metrics,
                "summary": {
                    "duration": duration,
                    "samples_collected": len(collected_metrics),
                    "avg_cpu": sum(cpu_values) / len(cpu_values) if cpu_values else 0,
                    "avg_memory": sum(memory_values) / len(memory_values) if memory_values else 0,
                    "max_cpu": max(cpu_values) if cpu_values else 0,
                    "max_memory": max(memory_values) if memory_values else 0
                },
                "vulnerabilities": vulnerabilities
            }
            
            return ScanResult(
                tool_name=self.metadata.name,
                target="system",
                status=ToolStatus.COMPLETED,
                start_time=start_time,
                end_time=datetime.now().isoformat(),
                duration_seconds=(datetime.now() - datetime.fromisoformat(start_time)).total_seconds(),
                raw_output=f"Collected {len(collected_metrics)} performance samples",
                parsed_results=results,
                vulnerabilities=vulnerabilities,
                metadata={
                    "samples_collected": len(collected_metrics),
                    "monitoring_duration": duration,
                    "anomalies_found": len(vulnerabilities)
                }
            )
            
        except Exception as e:
            logger.error(f"Performance monitoring failed: {e}")
            return ScanResult(
                tool_name=self.metadata.name,
                target="system",
                status=ToolStatus.FAILED,
                start_time=start_time,
                end_time=datetime.now().isoformat(),
                errors=[str(e)]
            )


@register_tool
class EnvironmentDetectorTool(BaseScanner):
    """Environment detection as a security tool"""
    
    def __init__(self):
        super().__init__()
        self.simulation_available = True
    
    def get_metadata(self) -> ToolMetadata:
        return ToolMetadata(
            name="environment_detector",
            display_name="Environment Detector",
            description="Advanced environment detection and security assessment",
            version="1.0.0",
            category=ToolCategory.CUSTOM_ANALYZER,
            capabilities=ToolCapabilities(
                supports_async=True,
                network_access_required=False,
                supported_targets=["system"],
                requires_root=False
            ),
            default_options={
                "deep_scan": True,
                "security_check": True,
                "compliance_check": True
            }
        )
    
    def check_native_availability(self) -> bool:
        """Check if environment detection is available"""
        return True  # Always available
    
    async def execute_native_scan(self, options: ScanOptions,
                                  progress_callback: Optional[Callable[[float, str], None]] = None) -> ScanResult:
        """Execute environment detection"""
        start_time = datetime.now().isoformat()
        
        if progress_callback:
            await progress_callback(0.1, "Detecting environment...")
        
        try:
            import platform
            import os
            
            # Detect environment details
            env_info = {
                "platform": platform.system(),
                "platform_release": platform.release(),
                "platform_version": platform.version(),
                "architecture": platform.machine(),
                "processor": platform.processor(),
                "python_version": platform.python_version(),
                "hostname": platform.node(),
                "user": os.getenv("USER", "unknown"),
                "home_dir": os.getenv("HOME", "unknown"),
                "shell": os.getenv("SHELL", "unknown"),
                "path": os.getenv("PATH", ""),
                "environment_vars": len(os.environ)
            }
            
            if progress_callback:
                await progress_callback(0.5, "Analyzing security implications...")
            
            # Security analysis
            vulnerabilities = []
            
            # Check for insecure environment variables
            sensitive_vars = ["PASSWORD", "SECRET", "KEY", "TOKEN", "API_KEY"]
            exposed_vars = []
            for var in os.environ:
                if any(sensitive in var.upper() for sensitive in sensitive_vars):
                    exposed_vars.append(var)
            
            if exposed_vars:
                vulnerabilities.append({
                    "name": "Sensitive Environment Variables Exposed",
                    "severity": "medium",
                    "description": f"Found {len(exposed_vars)} potentially sensitive environment variables",
                    "type": "information_disclosure",
                    "cve": None,
                    "remediation": "Review and secure sensitive environment variables"
                })
            
            # Check platform security
            if "Windows" in env_info["platform"]:
                vulnerabilities.append({
                    "name": "Windows Environment Detected",
                    "severity": "info",
                    "description": "Running on Windows platform - ensure security updates are current",
                    "type": "platform_assessment",
                    "cve": None,
                    "remediation": "Keep Windows updated and enable security features"
                })
            
            if progress_callback:
                await progress_callback(1.0, "Environment detection complete")
            
            results = {
                "environment": env_info,
                "security_assessment": {
                    "sensitive_vars_found": len(exposed_vars),
                    "platform_security": "review_required" if vulnerabilities else "acceptable",
                    "recommendations": [
                        "Regular security updates",
                        "Environment variable security review",
                        "Access control verification"
                    ]
                },
                "vulnerabilities": vulnerabilities
            }
            
            return ScanResult(
                tool_name=self.metadata.name,
                target="system",
                status=ToolStatus.COMPLETED,
                start_time=start_time,
                end_time=datetime.now().isoformat(),
                duration_seconds=(datetime.now() - datetime.fromisoformat(start_time)).total_seconds(),
                raw_output=f"Environment: {env_info['platform']} {env_info['platform_release']}",
                parsed_results=results,
                vulnerabilities=vulnerabilities,
                metadata={
                    "platform": env_info["platform"],
                    "architecture": env_info["architecture"],
                    "security_issues": len(vulnerabilities)
                }
            )
            
        except Exception as e:
            logger.error(f"Environment detection failed: {e}")
            return ScanResult(
                tool_name=self.metadata.name,
                target="system",
                status=ToolStatus.FAILED,
                start_time=start_time,
                end_time=datetime.now().isoformat(),
                errors=[str(e)]
            )


@register_tool
class HealthCheckerTool(BaseScanner):
    """System health checker as a security tool"""
    
    def __init__(self):
        super().__init__()
        self.simulation_available = True
    
    def get_metadata(self) -> ToolMetadata:
        return ToolMetadata(
            name="health_checker",
            display_name="System Health Checker",
            description="Comprehensive system health and security status monitoring",
            version="1.0.0",
            category=ToolCategory.CUSTOM_ANALYZER,
            capabilities=ToolCapabilities(
                supports_async=True,
                network_access_required=False,
                supported_targets=["system"],
                requires_root=False
            ),
            default_options={
                "check_services": True,
                "check_network": True,
                "check_security": True,
                "check_performance": True
            }
        )
    
    def check_native_availability(self) -> bool:
        """Check if health checking is available"""
        return True  # Always available
    
    async def execute_native_scan(self, options: ScanOptions,
                                  progress_callback: Optional[Callable[[float, str], None]] = None) -> ScanResult:
        """Execute system health check"""
        start_time = datetime.now().isoformat()
        
        if progress_callback:
            await progress_callback(0.1, "Starting health check...")
        
        health_status = {
            "overall_status": "healthy",
            "checks_performed": [],
            "issues_found": [],
            "security_score": 85  # Default score
        }
        
        vulnerabilities = []
        
        try:
            # Check 1: System resources
            if progress_callback:
                await progress_callback(0.3, "Checking system resources...")
            
            try:
                import psutil
                cpu_usage = psutil.cpu_percent(interval=1)
                memory_usage = psutil.virtual_memory().percent
                
                health_status["checks_performed"].append("system_resources")
                
                if cpu_usage > 80:
                    health_status["issues_found"].append(f"High CPU usage: {cpu_usage}%")
                    health_status["security_score"] -= 10
                
                if memory_usage > 85:
                    health_status["issues_found"].append(f"High memory usage: {memory_usage}%")
                    health_status["security_score"] -= 10
                    
            except ImportError:
                health_status["issues_found"].append("psutil not available for resource monitoring")
            
            # Check 2: Network status
            if progress_callback:
                await progress_callback(0.6, "Checking network status...")
            
            if options.custom_options.get("check_network", True):
                import socket
                try:
                    # Test DNS resolution
                    socket.gethostbyname("google.com")
                    health_status["checks_performed"].append("network_connectivity")
                except:
                    health_status["issues_found"].append("Network connectivity issues detected")
                    health_status["security_score"] -= 15
                    vulnerabilities.append({
                        "name": "Network Connectivity Issue",
                        "severity": "medium",
                        "description": "Unable to resolve external DNS",
                        "type": "network_issue",
                        "cve": None,
                        "remediation": "Check network configuration and DNS settings"
                    })
            
            # Check 3: Security status
            if progress_callback:
                await progress_callback(0.8, "Checking security status...")
            
            if options.custom_options.get("check_security", True):
                import os
                
                # Check for common security directories
                security_checks = {
                    "/etc/passwd": "System user file",
                    "/etc/shadow": "Password shadow file",
                    "C:\\Windows\\System32": "Windows system directory"
                }
                
                for path, description in security_checks.items():
                    if os.path.exists(path):
                        health_status["checks_performed"].append(f"security_path_{path}")
                        # Additional security checks could go here
            
            # Determine overall status
            if len(health_status["issues_found"]) == 0:
                health_status["overall_status"] = "healthy"
            elif len(health_status["issues_found"]) <= 2:
                health_status["overall_status"] = "warning"
            else:
                health_status["overall_status"] = "critical"
                vulnerabilities.append({
                    "name": "Multiple System Health Issues",
                    "severity": "high",
                    "description": f"Found {len(health_status['issues_found'])} system health issues",
                    "type": "system_health",
                    "cve": None,
                    "remediation": "Address all identified health issues"
                })
            
            if progress_callback:
                await progress_callback(1.0, "Health check complete")
            
            results = {
                "health_status": health_status,
                "recommendations": [
                    "Monitor system resources regularly",
                    "Keep system updated",
                    "Review security configuration",
                    "Implement automated health monitoring"
                ],
                "vulnerabilities": vulnerabilities
            }
            
            return ScanResult(
                tool_name=self.metadata.name,
                target="system",
                status=ToolStatus.COMPLETED,
                start_time=start_time,
                end_time=datetime.now().isoformat(),
                duration_seconds=(datetime.now() - datetime.fromisoformat(start_time)).total_seconds(),
                raw_output=f"Health Status: {health_status['overall_status']} (Score: {health_status['security_score']})",
                parsed_results=results,
                vulnerabilities=vulnerabilities,
                metadata={
                    "overall_status": health_status["overall_status"],
                    "security_score": health_status["security_score"],
                    "checks_performed": len(health_status["checks_performed"]),
                    "issues_found": len(health_status["issues_found"])
                }
            )
            
        except Exception as e:
            logger.error(f"Health check failed: {e}")
            return ScanResult(
                tool_name=self.metadata.name,
                target="system",
                status=ToolStatus.FAILED,
                start_time=start_time,
                end_time=datetime.now().isoformat(),
                errors=[str(e)]
            )


@register_tool
class ResourceOptimizerTool(BaseScanner):
    """Resource optimization as a security tool"""
    
    def __init__(self):
        super().__init__()
        self.simulation_available = True
    
    def get_metadata(self) -> ToolMetadata:
        return ToolMetadata(
            name="resource_optimizer",
            display_name="Resource Optimizer",
            description="Intelligent resource management and security optimization",
            version="1.0.0",
            category=ToolCategory.CUSTOM_ANALYZER,
            capabilities=ToolCapabilities(
                supports_async=True,
                network_access_required=False,
                supported_targets=["system"],
                requires_root=False
            ),
            default_options={
                "analyze_processes": True,
                "memory_analysis": True,
                "network_analysis": True,
                "security_optimization": True
            }
        )
    
    def check_native_availability(self) -> bool:
        """Check if resource optimization is available"""
        try:
            import psutil
            return True
        except ImportError:
            return False
    
    async def execute_native_scan(self, options: ScanOptions,
                                  progress_callback: Optional[Callable[[float, str], None]] = None) -> ScanResult:
        """Execute resource optimization analysis"""
        start_time = datetime.now().isoformat()
        
        if progress_callback:
            await progress_callback(0.1, "Starting resource analysis...")
        
        try:
            import psutil
            
            optimization_results = {
                "current_usage": {},
                "optimization_opportunities": [],
                "security_recommendations": [],
                "resource_efficiency": 0
            }
            
            vulnerabilities = []
            
            # Analyze current resource usage
            if progress_callback:
                await progress_callback(0.3, "Analyzing current resource usage...")
            
            optimization_results["current_usage"] = {
                "cpu_percent": psutil.cpu_percent(interval=1),
                "memory_percent": psutil.virtual_memory().percent,
                "disk_usage": psutil.disk_usage('/').percent,
                "process_count": len(psutil.pids()),
                "network_connections": len(psutil.net_connections())
            }
            
            # Process analysis
            if progress_callback:
                await progress_callback(0.6, "Analyzing running processes...")
            
            if options.custom_options.get("analyze_processes", True):
                high_cpu_processes = []
                high_memory_processes = []
                
                for proc in psutil.process_iter(['pid', 'name', 'cpu_percent', 'memory_percent']):
                    try:
                        if proc.info['cpu_percent'] > 10:
                            high_cpu_processes.append(proc.info)
                        if proc.info['memory_percent'] > 5:
                            high_memory_processes.append(proc.info)
                    except:
                        continue
                
                if high_cpu_processes:
                    optimization_results["optimization_opportunities"].append(
                        f"Found {len(high_cpu_processes)} high CPU processes"
                    )
                
                if high_memory_processes:
                    optimization_results["optimization_opportunities"].append(
                        f"Found {len(high_memory_processes)} high memory processes"
                    )
            
            # Security optimization analysis
            if progress_callback:
                await progress_callback(0.8, "Analyzing security optimization...")
            
            if options.custom_options.get("security_optimization", True):
                # Check for security-related optimization opportunities
                current_usage = optimization_results["current_usage"]
                
                if current_usage["cpu_percent"] > 70:
                    vulnerabilities.append({
                        "name": "High CPU Usage Security Risk",
                        "severity": "medium",
                        "description": "High CPU usage may indicate malicious processes or DoS conditions",
                        "type": "resource_exhaustion",
                        "cve": None,
                        "remediation": "Investigate high CPU processes and implement resource limits"
                    })
                
                if current_usage["network_connections"] > 100:
                    vulnerabilities.append({
                        "name": "High Network Connection Count",
                        "severity": "low",
                        "description": f"System has {current_usage['network_connections']} active network connections",
                        "type": "network_exposure",
                        "cve": None,
                        "remediation": "Review network connections for suspicious activity"
                    })
                
                # Calculate efficiency score
                efficiency_factors = []
                if current_usage["cpu_percent"] < 50:
                    efficiency_factors.append(20)
                if current_usage["memory_percent"] < 70:
                    efficiency_factors.append(20)
                if current_usage["disk_usage"] < 80:
                    efficiency_factors.append(20)
                if current_usage["process_count"] < 200:
                    efficiency_factors.append(20)
                if current_usage["network_connections"] < 50:
                    efficiency_factors.append(20)
                
                optimization_results["resource_efficiency"] = sum(efficiency_factors)
            
            # Generate recommendations
            optimization_results["security_recommendations"] = [
                "Implement process monitoring and alerting",
                "Set resource limits for critical services",
                "Regular cleanup of temporary files",
                "Monitor network connections for anomalies",
                "Implement automated resource optimization"
            ]
            
            if progress_callback:
                await progress_callback(1.0, "Resource optimization analysis complete")
            
            results = {
                "optimization_analysis": optimization_results,
                "vulnerabilities": vulnerabilities
            }
            
            return ScanResult(
                tool_name=self.metadata.name,
                target="system",
                status=ToolStatus.COMPLETED,
                start_time=start_time,
                end_time=datetime.now().isoformat(),
                duration_seconds=(datetime.now() - datetime.fromisoformat(start_time)).total_seconds(),
                raw_output=f"Resource Efficiency: {optimization_results['resource_efficiency']}%",
                parsed_results=results,
                vulnerabilities=vulnerabilities,
                metadata={
                    "resource_efficiency": optimization_results["resource_efficiency"],
                    "optimization_opportunities": len(optimization_results["optimization_opportunities"]),
                    "security_issues": len(vulnerabilities)
                }
            )
            
        except Exception as e:
            logger.error(f"Resource optimization failed: {e}")
            return ScanResult(
                tool_name=self.metadata.name,
                target="system",
                status=ToolStatus.FAILED,
                start_time=start_time,
                end_time=datetime.now().isoformat(),
                errors=[str(e)]
            )