import React from 'react';
import <PERSON>actD<PERSON> from 'react-dom/client';
import { <PERSON>h<PERSON><PERSON>er } from 'react-router-dom';
import { Toaster } from 'sonner';
import App from './App';
// import App from './App-simple';
import './index.css';
// Import direct backend fix
import './services/direct-backend-fix';

// Web application - no Electron types needed

/**
 * Initialize NexusScan Web Application
 */
async function initializeApp() {
  try {
    console.log('🚀 NexusScan Desktop - Development Mode');
    console.log('🔧 Backend: ' + __BACKEND_URL__);
    console.log('🔗 WebSocket: ' + __BACKEND_URL__.replace('http', 'ws') + '/ws');
    console.log('⚠️ Electron API not available - running in browser mode');

    // Store app info globally for debugging
    (window as any).appInfo = {
      name: __APP_NAME__,
      version: __APP_VERSION__,
      backend: __BACKEND_URL__,
      isElectron: false,
      isWeb: true
    };

    // Mark app as ready
    console.log('✅ NexusScan Desktop initialized successfully');

    // Dispatch app ready event
    window.dispatchEvent(new CustomEvent('appReady'));

  } catch (error) {
    console.error('❌ Failed to initialize NexusScan Desktop:', error);

    // Still dispatch app ready to show the app even if initialization fails
    window.dispatchEvent(new CustomEvent('appReady'));
  }
}

/**
 * Render the React application
 */
function renderApp() {
  const root = ReactDOM.createRoot(
    document.getElementById('root') as HTMLElement
  );
  
  root.render(
    <React.StrictMode>
      <HashRouter>
        <App />
        <Toaster 
          position="top-right"
          toastOptions={{
            duration: 4000,
            style: {
              background: 'hsl(var(--card))',
              color: 'hsl(var(--card-foreground))',
              border: '1px solid hsl(var(--border))',
            },
          }}
        />
      </HashRouter>
    </React.StrictMode>
  );
}

/**
 * Handle unhandled errors
 */
function setupErrorHandling() {
  // Handle uncaught JavaScript errors
  window.addEventListener('error', (event) => {
    console.error('🚨 Uncaught error:', event.error);
    
    // Report to Electron main process if available
    if (window.electronAPI) {
      window.electronAPI.notification.show({
        title: 'Application Error',
        body: 'An unexpected error occurred. Check the console for details.'
      }).catch(() => {
        // Fallback if notification fails
        console.error('Failed to show error notification');
      });
    }
  });
  
  // Handle unhandled promise rejections
  window.addEventListener('unhandledrejection', (event) => {
    console.error('🚨 Unhandled promise rejection:', event.reason);
    
    // Prevent the default browser behavior
    event.preventDefault();
  });
  
  // Handle React errors (will be caught by Error Boundaries)
  console.log('🛡️ Error handling configured');
}

/**
 * Setup development mode helpers
 */
function setupDevelopmentMode() {
  if (process.env.NODE_ENV === 'development') {
    // Enable React DevTools
    if (window.__REACT_DEVTOOLS_GLOBAL_HOOK__) {
      console.log('🔧 React DevTools available');
    }
    
    // Add global helpers for debugging
    (window as any).nexusScan = {
      version: __APP_VERSION__,
      backend: __BACKEND_URL__,
      isWeb: true,
      isElectron: false
    };
    
    console.log('🛠️ Development mode helpers available at window.nexusScan');
  }
}

/**
 * Main application bootstrap
 */
async function bootstrap() {
  try {
    // Setup error handling first
    setupErrorHandling();
    
    // Setup development mode helpers
    setupDevelopmentMode();
    
    // Render the React app
    renderApp();
    
    // Initialize app services
    await initializeApp();
    
    console.log('🎉 NexusScan Desktop ready!');
    
  } catch (error) {
    console.error('💥 Bootstrap failed:', error);
    
    // Try to render the app anyway
    try {
      renderApp();
    } catch (renderError) {
      console.error('💥 Failed to render app:', renderError);
      
      // Last resort: show error message
      document.body.innerHTML = `
        <div style="
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          height: 100vh;
          background: #0f172a;
          color: #e2e8f0;
          font-family: system-ui, sans-serif;
          text-align: center;
          padding: 2rem;
        ">
          <h1 style="color: #ef4444; margin-bottom: 1rem;">Application Error</h1>
          <p style="margin-bottom: 2rem;">Failed to start NexusScan Desktop</p>
          <details style="max-width: 600px;">
            <summary style="cursor: pointer; margin-bottom: 1rem;">Error Details</summary>
            <pre style="
              background: #1e293b;
              padding: 1rem;
              border-radius: 0.5rem;
              text-align: left;
              overflow: auto;
              font-size: 0.875rem;
            ">${error.message}

${error.stack}</pre>
          </details>
          <button onclick="window.location.reload()" style="
            background: #3b82f6;
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 0.5rem;
            cursor: pointer;
            margin-top: 2rem;
          ">Restart Application</button>
        </div>
      `;
    }
  }
}

// Start the application
bootstrap();