"""
Advanced Vulnerability Assessment Engine for NexusScan Desktop
Comprehensive vulnerability analysis with AI-powered risk assessment and correlation.
"""

import asyncio
import logging
import json
import time
import hashlib
from typing import Dict, List, Optional, Any, Set, Tuple, Union
from enum import Enum
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from pathlib import Path
import re
from collections import defaultdict, Counter

from core.config import Config
from core.database import DatabaseManager
from core.events import EventManager, EventTypes
from ai.ai_service import AI<PERSON>erviceManager
from security.unified_tool_manager import UnifiedToolManager
from security.vulnerability_scanner import VulnerabilityScanner

logger = logging.getLogger(__name__)


class VulnerabilityCategory(Enum):
    """Vulnerability categories based on CWE and OWASP"""
    INJECTION = "injection"
    BROKEN_AUTHENTICATION = "broken_authentication"
    SENSITIVE_DATA_EXPOSURE = "sensitive_data_exposure"
    XML_EXTERNAL_ENTITIES = "xml_external_entities"
    BROKEN_ACCESS_CONTROL = "broken_access_control"
    SECURITY_MISCONFIGURATION = "security_misconfiguration"
    CROSS_SITE_SCRIPTING = "cross_site_scripting"
    INSECURE_DESERIALIZATION = "insecure_deserialization"
    COMPONENTS_VULNERABILITIES = "components_vulnerabilities"
    INSUFFICIENT_LOGGING = "insufficient_logging"
    BUFFER_OVERFLOW = "buffer_overflow"
    PRIVILEGE_ESCALATION = "privilege_escalation"
    INFORMATION_DISCLOSURE = "information_disclosure"
    DENIAL_OF_SERVICE = "denial_of_service"
    CRYPTOGRAPHIC_ISSUES = "cryptographic_issues"


class ThreatLevel(Enum):
    """Threat level classification"""
    INFORMATIONAL = 0
    LOW = 1
    MEDIUM = 2
    HIGH = 3
    CRITICAL = 4


class ExploitabilityLevel(Enum):
    """Exploitability assessment levels"""
    THEORETICAL = "theoretical"
    PROOF_OF_CONCEPT = "proof_of_concept"
    WEAPONIZED = "weaponized"
    IN_THE_WILD = "in_the_wild"


@dataclass
class VulnerabilityContext:
    """Context information for vulnerability"""
    asset_type: str  # web_app, network_service, host, etc.
    asset_criticality: str  # low, medium, high, critical
    exposure_level: str  # internal, dmz, external
    business_function: str  # production, development, testing
    compliance_requirements: List[str]  # PCI-DSS, HIPAA, SOX, etc.
    data_classification: str  # public, internal, confidential, restricted


@dataclass
class ThreatIntelligence:
    """Threat intelligence data for vulnerability"""
    cve_id: Optional[str]
    cwe_id: Optional[str]
    mitre_attack_techniques: List[str]
    threat_actors: List[str]
    active_campaigns: List[str]
    exploit_availability: ExploitabilityLevel
    exploit_complexity: str  # low, medium, high
    attack_vector: str  # network, adjacent, local, physical
    first_seen: Optional[datetime]
    last_seen: Optional[datetime]


@dataclass
class RiskMetrics:
    """Comprehensive risk metrics"""
    cvss_v3_score: float
    cvss_v3_vector: str
    business_impact_score: float
    exploitability_score: float
    threat_landscape_score: float
    asset_criticality_score: float
    environmental_score: float
    composite_risk_score: float
    risk_rating: str  # Low, Medium, High, Critical


@dataclass
class VulnerabilityAssessment:
    """Comprehensive vulnerability assessment"""
    vulnerability_id: str
    title: str
    description: str
    category: VulnerabilityCategory
    threat_level: ThreatLevel
    affected_assets: List[str]
    discovery_method: str
    discovery_timestamp: datetime
    context: VulnerabilityContext
    threat_intelligence: ThreatIntelligence
    risk_metrics: RiskMetrics
    technical_details: Dict[str, Any]
    evidence: List[str]
    remediation_recommendations: List[str]
    compensating_controls: List[str]
    false_positive_likelihood: float
    validation_status: str  # unvalidated, validated, false_positive
    ai_analysis: Optional[str] = None


@dataclass
class VulnerabilityCluster:
    """Cluster of related vulnerabilities"""
    cluster_id: str
    cluster_type: str  # similar_root_cause, attack_chain, asset_group
    primary_vulnerability: str
    related_vulnerabilities: List[str]
    combined_risk_score: float
    attack_scenarios: List[str]
    prioritized_remediation: str


class AdvancedVulnerabilityAssessment:
    """Advanced vulnerability assessment engine"""
    
    def __init__(self, config: Config, db_manager: DatabaseManager,
                 event_manager: EventManager, ai_service: AIServiceManager,
                 vuln_scanner: VulnerabilityScanner):
        """Initialize advanced vulnerability assessment engine"""
        self.config = config
        self.db_manager = db_manager
        self.event_manager = event_manager
        self.ai_service = ai_service
        self.vuln_scanner = vuln_scanner
        
        # Assessment state
        self.active_assessments: Dict[str, Dict[str, Any]] = {}
        self.vulnerability_database: Dict[str, VulnerabilityAssessment] = {}
        self.vulnerability_clusters: Dict[str, VulnerabilityCluster] = {}
        
        # Threat intelligence cache
        self.threat_intel_cache: Dict[str, ThreatIntelligence] = {}
        self.cve_database: Dict[str, Dict[str, Any]] = {}
        
        # Risk calculation parameters
        self.business_impact_weights = {
            "confidentiality": 0.4,
            "integrity": 0.3,
            "availability": 0.3
        }
        
        # Load vulnerability patterns and signatures
        self._load_vulnerability_patterns()
        
        logger.info("Advanced vulnerability assessment engine initialized")
    
    def _load_vulnerability_patterns(self):
        """Load vulnerability detection patterns and signatures"""
        # SQL Injection patterns
        self.sql_injection_patterns = [
            r"(?i)(union\s+select|or\s+1\s*=\s*1|'.*or.*'.*=')",
            r"(?i)(sleep\s*\(|benchmark\s*\(|waitfor\s+delay)",
            r"(?i)(information_schema|sysobjects|msysaccessobjects)",
            r"(?i)(\bselect\b.*\bfrom\b.*\bwhere\b.*\bor\b)",
            r"(?i)(concat\s*\(|char\s*\(|ascii\s*\()"
        ]
        
        # XSS patterns
        self.xss_patterns = [
            r"(?i)(<script[^>]*>.*</script>|javascript:|on\w+\s*=)",
            r"(?i)(alert\s*\(|confirm\s*\(|prompt\s*\()",
            r"(?i)(<iframe|<object|<embed|<applet)",
            r"(?i)(expression\s*\(|url\s*\(.*javascript)",
            r"(?i)(onerror\s*=|onload\s*=|onclick\s*=)"
        ]
        
        # Command injection patterns
        self.command_injection_patterns = [
            r"(?i)(;|\|\||&&|\|)(\s*)(cat|ls|dir|type|echo|whoami|id)",
            r"(?i)(nc\s+-|netcat|telnet|\bsh\b|\bbash\b|\bcmd\b)",
            r"(?i)(\$\(.*\)|\`.*\`|\${.*})",
            r"(?i)(wget|curl|powershell|wscript|cscript)",
            r"(?i)(\/bin\/|\/usr\/bin\/|c:\\\\windows\\\\system32\\\\)"
        ]
        
        # Path traversal patterns
        self.path_traversal_patterns = [
            r"(?i)(\.\.\/|\.\.\\|%2e%2e%2f|%2e%2e%5c)",
            r"(?i)(\/etc\/passwd|\/etc\/shadow|boot\.ini|win\.ini)",
            r"(?i)(%252e%252e|%c0%ae%c0%ae|%uff0e%uff0e)",
            r"(?i)(\.\.%2f|\.\.%5c|%2e%2e\/|%2e%2e\\)"
        ]
        
        logger.info("Vulnerability patterns loaded successfully")
    
    async def start_comprehensive_assessment(self, target_config: Dict[str, Any],
                                           assessment_config: Dict[str, Any]) -> str:
        """Start comprehensive vulnerability assessment"""
        try:
            assessment_id = f"assess_{int(time.time())}"
            
            # Initialize assessment state
            assessment_state = {
                "assessment_id": assessment_id,
                "target_config": target_config,
                "assessment_config": assessment_config,
                "status": "running",
                "start_time": datetime.now(),
                "phase": "initialization",
                "discovered_vulnerabilities": [],
                "assessed_vulnerabilities": [],
                "vulnerability_clusters": [],
                "risk_summary": {},
                "ai_insights": []
            }
            
            self.active_assessments[assessment_id] = assessment_state
            
            # Emit assessment started event
            await self.event_manager.emit(
                EventTypes.SECURITY_SCAN_STARTED,
                {
                    "assessment_id": assessment_id,
                    "target": target_config.get("name", "unknown"),
                    "scope": assessment_config.get("scope", "comprehensive")
                },
                "vulnerability_assessment"
            )
            
            logger.info(f"Started comprehensive vulnerability assessment: {assessment_id}")
            return assessment_id
            
        except Exception as e:
            logger.error(f"Failed to start vulnerability assessment: {e}")
            raise
    
    async def execute_assessment(self, assessment_id: str) -> Dict[str, Any]:
        """Execute comprehensive vulnerability assessment"""
        try:
            if assessment_id not in self.active_assessments:
                raise ValueError(f"Unknown assessment: {assessment_id}")
            
            assessment = self.active_assessments[assessment_id]
            
            logger.info(f"Executing vulnerability assessment: {assessment_id}")
            
            # Phase 1: Discovery and Initial Scanning
            assessment["phase"] = "discovery"
            vulnerabilities = await self._discovery_phase(assessment)
            assessment["discovered_vulnerabilities"] = vulnerabilities
            
            # Phase 2: Deep Vulnerability Analysis
            assessment["phase"] = "analysis"
            assessed_vulns = await self._analysis_phase(assessment, vulnerabilities)
            assessment["assessed_vulnerabilities"] = assessed_vulns
            
            # Phase 3: Risk Assessment and Correlation
            assessment["phase"] = "risk_assessment"
            risk_summary = await self._risk_assessment_phase(assessment, assessed_vulns)
            assessment["risk_summary"] = risk_summary
            
            # Phase 4: Vulnerability Clustering
            assessment["phase"] = "clustering"
            clusters = await self._clustering_phase(assessment, assessed_vulns)
            assessment["vulnerability_clusters"] = clusters
            
            # Phase 5: AI-Powered Analysis and Insights
            assessment["phase"] = "ai_analysis"
            ai_insights = await self._ai_analysis_phase(assessment)
            assessment["ai_insights"] = ai_insights
            
            # Finalize assessment
            assessment["status"] = "completed"
            assessment["end_time"] = datetime.now()
            assessment["duration"] = (assessment["end_time"] - assessment["start_time"]).total_seconds()
            
            # Generate comprehensive report
            report = await self._generate_assessment_report(assessment)
            assessment["report"] = report
            
            # Emit assessment completed event
            await self.event_manager.emit(
                EventTypes.SECURITY_SCAN_COMPLETED,
                {
                    "assessment_id": assessment_id,
                    "vulnerabilities_found": len(assessed_vulns),
                    "critical_vulnerabilities": len([v for v in assessed_vulns if v.threat_level == ThreatLevel.CRITICAL]),
                    "high_vulnerabilities": len([v for v in assessed_vulns if v.threat_level == ThreatLevel.HIGH]),
                    "duration": assessment["duration"]
                },
                "vulnerability_assessment"
            )
            
            logger.info(f"Vulnerability assessment completed: {assessment_id}")
            return assessment
            
        except Exception as e:
            logger.error(f"Vulnerability assessment execution failed: {e}")
            if assessment_id in self.active_assessments:
                self.active_assessments[assessment_id]["status"] = "failed"
                self.active_assessments[assessment_id]["error"] = str(e)
            raise
    
    async def _discovery_phase(self, assessment: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Phase 1: Vulnerability discovery and initial scanning"""
        target_config = assessment["target_config"]
        vulnerabilities = []
        
        try:
            # Network-based vulnerability discovery
            if target_config.get("type") in ["network", "host"]:
                network_vulns = await self._discover_network_vulnerabilities(target_config)
                vulnerabilities.extend(network_vulns)
            
            # Web application vulnerability discovery
            if target_config.get("type") in ["web_app", "api"]:
                web_vulns = await self._discover_web_vulnerabilities(target_config)
                vulnerabilities.extend(web_vulns)
            
            # Host-based vulnerability discovery
            if target_config.get("type") in ["host", "server"]:
                host_vulns = await self._discover_host_vulnerabilities(target_config)
                vulnerabilities.extend(host_vulns)
            
            # Configuration vulnerability discovery
            config_vulns = await self._discover_configuration_vulnerabilities(target_config)
            vulnerabilities.extend(config_vulns)
            
            logger.info(f"Discovery phase completed: {len(vulnerabilities)} vulnerabilities found")
            return vulnerabilities
            
        except Exception as e:
            logger.error(f"Discovery phase failed: {e}")
            return vulnerabilities
    
    async def _discover_network_vulnerabilities(self, target_config: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Discover network-based vulnerabilities"""
        vulnerabilities = []
        
        # Check if real scanning is enabled
        if target_config.get("execution_mode") == "real":
            vulnerabilities = await self._real_network_vulnerability_scan(target_config)
        else:
            # Simulate network vulnerability discovery
            network_vulns = [
            {
                "type": "network",
                "title": "Unencrypted Service Discovery",
                "description": "Services running without encryption detected",
                "severity": "medium",
                "affected_ports": [21, 23, 80, 143],
                "evidence": "FTP, Telnet, HTTP, IMAP services without encryption"
            },
            {
                "type": "network",
                "title": "Weak SSL/TLS Configuration",
                "description": "SSL/TLS services with weak cipher suites",
                "severity": "high",
                "affected_ports": [443, 993, 995],
                "evidence": "SSLv3, TLS 1.0, weak cipher suites detected"
            },
            {
                "type": "network",
                "title": "Open Administrative Services",
                "description": "Administrative services exposed to network",
                "severity": "critical",
                "affected_ports": [22, 3389, 5900],
                "evidence": "SSH, RDP, VNC services with default configurations"
            }
        ]
        
            vulnerabilities.extend(network_vulns)
        
        return vulnerabilities
    
    async def _discover_web_vulnerabilities(self, target_config: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Discover web application vulnerabilities"""
        vulnerabilities = []
        
        # Simulate web vulnerability discovery
        web_vulns = [
            {
                "type": "web_app",
                "title": "SQL Injection Vulnerability",
                "description": "SQL injection in user input parameter",
                "severity": "critical",
                "url": f"{target_config.get('url', 'http://target.com')}/login.php",
                "parameter": "username",
                "evidence": "Error-based SQL injection confirmed"
            },
            {
                "type": "web_app",
                "title": "Cross-Site Scripting (XSS)",
                "description": "Reflected XSS in search functionality",
                "severity": "high",
                "url": f"{target_config.get('url', 'http://target.com')}/search.php",
                "parameter": "query",
                "evidence": "JavaScript execution confirmed"
            },
            {
                "type": "web_app",
                "title": "Insecure Direct Object Reference",
                "description": "Access to unauthorized resources via parameter manipulation",
                "severity": "high",
                "url": f"{target_config.get('url', 'http://target.com')}/profile.php",
                "parameter": "user_id",
                "evidence": "Access to other user profiles"
            }
        ]
        
        vulnerabilities.extend(web_vulns)
        return vulnerabilities
    
    async def _discover_host_vulnerabilities(self, target_config: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Discover host-based vulnerabilities"""
        vulnerabilities = []
        
        # Simulate host vulnerability discovery
        host_vulns = [
            {
                "type": "host",
                "title": "Missing Security Updates",
                "description": "Multiple security patches not installed",
                "severity": "high",
                "affected_packages": ["kernel", "openssl", "apache2"],
                "evidence": "CVE-2021-44228 (Log4j), CVE-2021-34527 (PrintNightmare)"
            },
            {
                "type": "host",
                "title": "Weak User Account Policies",
                "description": "Weak password policies and account configurations",
                "severity": "medium",
                "affected_accounts": ["admin", "guest", "service_accounts"],
                "evidence": "No password complexity requirements, shared accounts"
            },
            {
                "type": "host",
                "title": "Insecure File Permissions",
                "description": "Sensitive files with excessive permissions",
                "severity": "medium",
                "affected_files": ["/etc/shadow", "/home/<USER>/.ssh/", "/var/log/"],
                "evidence": "World-readable sensitive files"
            }
        ]
        
        vulnerabilities.extend(host_vulns)
        return vulnerabilities
    
    async def _discover_configuration_vulnerabilities(self, target_config: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Discover configuration vulnerabilities"""
        vulnerabilities = []
        
        # Simulate configuration vulnerability discovery
        config_vulns = [
            {
                "type": "configuration",
                "title": "Insecure Default Configurations",
                "description": "Services running with default configurations",
                "severity": "medium",
                "affected_services": ["database", "web_server", "ssh"],
                "evidence": "Default credentials, excessive permissions"
            },
            {
                "type": "configuration",
                "title": "Information Disclosure",
                "description": "Sensitive information exposed in responses",
                "severity": "low",
                "locations": ["error_messages", "http_headers", "directory_listings"],
                "evidence": "Server version disclosure, directory browsing enabled"
            }
        ]
        
        vulnerabilities.extend(config_vulns)
        return vulnerabilities
    
    async def _analysis_phase(self, assessment: Dict[str, Any], 
                            vulnerabilities: List[Dict[str, Any]]) -> List[VulnerabilityAssessment]:
        """Phase 2: Deep vulnerability analysis"""
        assessed_vulnerabilities = []
        
        for vuln_data in vulnerabilities:
            try:
                # Create comprehensive vulnerability assessment
                vuln_assessment = await self._create_vulnerability_assessment(vuln_data, assessment)
                assessed_vulnerabilities.append(vuln_assessment)
                
                # Store in vulnerability database
                self.vulnerability_database[vuln_assessment.vulnerability_id] = vuln_assessment
                
            except Exception as e:
                logger.error(f"Failed to assess vulnerability: {e}")
                continue
        
        logger.info(f"Analysis phase completed: {len(assessed_vulnerabilities)} vulnerabilities assessed")
        return assessed_vulnerabilities
    
    async def _create_vulnerability_assessment(self, vuln_data: Dict[str, Any],
                                             assessment: Dict[str, Any]) -> VulnerabilityAssessment:
        """Create comprehensive vulnerability assessment"""
        # Generate unique vulnerability ID
        vuln_id = hashlib.md5(
            f"{vuln_data.get('title', '')}{vuln_data.get('url', '')}{vuln_data.get('parameter', '')}".encode()
        ).hexdigest()[:16]
        
        # Determine vulnerability category
        category = self._categorize_vulnerability(vuln_data)
        
        # Determine threat level
        threat_level = self._assess_threat_level(vuln_data)
        
        # Create vulnerability context
        context = VulnerabilityContext(
            asset_type=vuln_data.get("type", "unknown"),
            asset_criticality=assessment["target_config"].get("criticality", "medium"),
            exposure_level=assessment["target_config"].get("exposure", "internal"),
            business_function=assessment["target_config"].get("function", "production"),
            compliance_requirements=assessment["target_config"].get("compliance", []),
            data_classification=assessment["target_config"].get("data_classification", "internal")
        )
        
        # Get threat intelligence
        threat_intel = await self._get_threat_intelligence(vuln_data)
        
        # Calculate risk metrics
        risk_metrics = await self._calculate_risk_metrics(vuln_data, context, threat_intel)
        
        # Generate remediation recommendations
        remediation = await self._generate_remediation_recommendations(vuln_data, context)
        
        # Get AI analysis
        ai_analysis = await self._get_ai_vulnerability_analysis(vuln_data, context)
        
        return VulnerabilityAssessment(
            vulnerability_id=vuln_id,
            title=vuln_data.get("title", "Unknown Vulnerability"),
            description=vuln_data.get("description", ""),
            category=category,
            threat_level=threat_level,
            affected_assets=[vuln_data.get("url", vuln_data.get("host", "unknown"))],
            discovery_method="automated_scanning",
            discovery_timestamp=datetime.now(),
            context=context,
            threat_intelligence=threat_intel,
            risk_metrics=risk_metrics,
            technical_details=vuln_data,
            evidence=[vuln_data.get("evidence", "")],
            remediation_recommendations=remediation,
            compensating_controls=[],
            false_positive_likelihood=self._assess_false_positive_likelihood(vuln_data),
            validation_status="unvalidated",
            ai_analysis=ai_analysis
        )
    
    def _categorize_vulnerability(self, vuln_data: Dict[str, Any]) -> VulnerabilityCategory:
        """Categorize vulnerability based on type and characteristics"""
        title = vuln_data.get("title", "").lower()
        description = vuln_data.get("description", "").lower()
        
        if "sql injection" in title or "sql injection" in description:
            return VulnerabilityCategory.INJECTION
        elif "xss" in title or "cross-site scripting" in title:
            return VulnerabilityCategory.CROSS_SITE_SCRIPTING
        elif "authentication" in title or "login" in title:
            return VulnerabilityCategory.BROKEN_AUTHENTICATION
        elif "access control" in title or "authorization" in title:
            return VulnerabilityCategory.BROKEN_ACCESS_CONTROL
        elif "configuration" in title or "misconfiguration" in title:
            return VulnerabilityCategory.SECURITY_MISCONFIGURATION
        elif "information disclosure" in title or "exposure" in title:
            return VulnerabilityCategory.INFORMATION_DISCLOSURE
        elif "buffer overflow" in title or "memory corruption" in title:
            return VulnerabilityCategory.BUFFER_OVERFLOW
        elif "privilege escalation" in title or "elevation" in title:
            return VulnerabilityCategory.PRIVILEGE_ESCALATION
        elif "denial of service" in title or "dos" in title:
            return VulnerabilityCategory.DENIAL_OF_SERVICE
        elif "cryptographic" in title or "encryption" in title:
            return VulnerabilityCategory.CRYPTOGRAPHIC_ISSUES
        else:
            return VulnerabilityCategory.SECURITY_MISCONFIGURATION
    
    def _assess_threat_level(self, vuln_data: Dict[str, Any]) -> ThreatLevel:
        """Assess threat level based on severity and characteristics"""
        severity = vuln_data.get("severity", "medium").lower()
        
        if severity == "critical":
            return ThreatLevel.CRITICAL
        elif severity == "high":
            return ThreatLevel.HIGH
        elif severity == "medium":
            return ThreatLevel.MEDIUM
        elif severity == "low":
            return ThreatLevel.LOW
        else:
            return ThreatLevel.INFORMATIONAL
    
    async def _get_threat_intelligence(self, vuln_data: Dict[str, Any]) -> ThreatIntelligence:
        """Get threat intelligence for vulnerability"""
        # Simulate threat intelligence lookup
        cve_pattern = r"CVE-\d{4}-\d{4,7}"
        cve_matches = re.findall(cve_pattern, str(vuln_data))
        
        return ThreatIntelligence(
            cve_id=cve_matches[0] if cve_matches else None,
            cwe_id=self._map_to_cwe(vuln_data),
            mitre_attack_techniques=self._map_to_mitre_attack(vuln_data),
            threat_actors=["APT28", "Lazarus Group"] if vuln_data.get("severity") == "critical" else [],
            active_campaigns=["Operation XYZ"] if vuln_data.get("severity") == "critical" else [],
            exploit_availability=ExploitabilityLevel.PROOF_OF_CONCEPT,
            exploit_complexity="medium",
            attack_vector="network",
            first_seen=datetime.now() - timedelta(days=30),
            last_seen=datetime.now() - timedelta(days=1)
        )
    
    def _map_to_cwe(self, vuln_data: Dict[str, Any]) -> Optional[str]:
        """Map vulnerability to CWE identifier"""
        title = vuln_data.get("title", "").lower()
        
        cwe_mapping = {
            "sql injection": "CWE-89",
            "cross-site scripting": "CWE-79",
            "buffer overflow": "CWE-120",
            "path traversal": "CWE-22",
            "command injection": "CWE-78",
            "information disclosure": "CWE-200",
            "authentication": "CWE-287",
            "access control": "CWE-284"
        }
        
        for keyword, cwe in cwe_mapping.items():
            if keyword in title:
                return cwe
        
        return "CWE-Other"
    
    def _map_to_mitre_attack(self, vuln_data: Dict[str, Any]) -> List[str]:
        """Map vulnerability to MITRE ATT&CK techniques"""
        title = vuln_data.get("title", "").lower()
        
        mitre_mapping = {
            "sql injection": ["T1190"],
            "cross-site scripting": ["T1190", "T1559"],
            "authentication": ["T1078", "T1110"],
            "privilege escalation": ["T1068", "T1548"],
            "information disclosure": ["T1083", "T1005"],
            "configuration": ["T1082", "T1201"]
        }
        
        techniques = []
        for keyword, techs in mitre_mapping.items():
            if keyword in title:
                techniques.extend(techs)
        
        return list(set(techniques)) or ["T1190"]
    
    async def _calculate_risk_metrics(self, vuln_data: Dict[str, Any],
                                    context: VulnerabilityContext,
                                    threat_intel: ThreatIntelligence) -> RiskMetrics:
        """Calculate comprehensive risk metrics"""
        # Base CVSS v3 score calculation
        cvss_score = self._calculate_cvss_score(vuln_data, threat_intel)
        
        # Business impact assessment
        business_impact = self._calculate_business_impact(context)
        
        # Exploitability assessment
        exploitability = self._calculate_exploitability(threat_intel)
        
        # Threat landscape assessment
        threat_landscape = self._calculate_threat_landscape(threat_intel)
        
        # Asset criticality assessment
        asset_criticality = self._calculate_asset_criticality(context)
        
        # Environmental factors
        environmental = self._calculate_environmental_factors(context)
        
        # Composite risk score
        composite_score = (
            cvss_score * 0.3 +
            business_impact * 0.25 +
            exploitability * 0.2 +
            threat_landscape * 0.15 +
            asset_criticality * 0.1
        ) * environmental
        
        risk_rating = self._determine_risk_rating(composite_score)
        
        return RiskMetrics(
            cvss_v3_score=cvss_score,
            cvss_v3_vector=self._generate_cvss_vector(vuln_data),
            business_impact_score=business_impact,
            exploitability_score=exploitability,
            threat_landscape_score=threat_landscape,
            asset_criticality_score=asset_criticality,
            environmental_score=environmental,
            composite_risk_score=composite_score,
            risk_rating=risk_rating
        )
    
    def _calculate_cvss_score(self, vuln_data: Dict[str, Any], 
                            threat_intel: ThreatIntelligence) -> float:
        """Calculate CVSS v3 base score"""
        severity = vuln_data.get("severity", "medium").lower()
        
        # Simplified CVSS scoring
        base_scores = {
            "critical": 9.0,
            "high": 7.5,
            "medium": 5.0,
            "low": 2.5,
            "informational": 0.0
        }
        
        return base_scores.get(severity, 5.0)
    
    def _calculate_business_impact(self, context: VulnerabilityContext) -> float:
        """Calculate business impact score"""
        impact_scores = {
            "critical": 1.0,
            "high": 0.8,
            "medium": 0.5,
            "low": 0.2
        }
        
        asset_impact = impact_scores.get(context.asset_criticality, 0.5)
        
        # Adjust for data classification
        data_multipliers = {
            "restricted": 1.0,
            "confidential": 0.8,
            "internal": 0.5,
            "public": 0.2
        }
        
        data_impact = data_multipliers.get(context.data_classification, 0.5)
        
        return (asset_impact + data_impact) / 2 * 10
    
    def _calculate_exploitability(self, threat_intel: ThreatIntelligence) -> float:
        """Calculate exploitability score"""
        exploitability_scores = {
            ExploitabilityLevel.IN_THE_WILD: 10.0,
            ExploitabilityLevel.WEAPONIZED: 8.0,
            ExploitabilityLevel.PROOF_OF_CONCEPT: 6.0,
            ExploitabilityLevel.THEORETICAL: 3.0
        }
        
        return exploitability_scores.get(threat_intel.exploit_availability, 5.0)
    
    def _calculate_threat_landscape(self, threat_intel: ThreatIntelligence) -> float:
        """Calculate threat landscape score"""
        base_score = 5.0
        
        # Increase score for active threat actors
        if threat_intel.threat_actors:
            base_score += len(threat_intel.threat_actors) * 1.0
        
        # Increase score for active campaigns
        if threat_intel.active_campaigns:
            base_score += len(threat_intel.active_campaigns) * 1.5
        
        return min(base_score, 10.0)
    
    def _calculate_asset_criticality(self, context: VulnerabilityContext) -> float:
        """Calculate asset criticality score"""
        criticality_scores = {
            "critical": 10.0,
            "high": 7.5,
            "medium": 5.0,
            "low": 2.5
        }
        
        return criticality_scores.get(context.asset_criticality, 5.0)
    
    def _calculate_environmental_factors(self, context: VulnerabilityContext) -> float:
        """Calculate environmental adjustment factor"""
        base_factor = 1.0
        
        # Adjust for exposure level
        exposure_multipliers = {
            "external": 1.5,
            "dmz": 1.2,
            "internal": 1.0
        }
        
        base_factor *= exposure_multipliers.get(context.exposure_level, 1.0)
        
        # Adjust for business function
        function_multipliers = {
            "production": 1.3,
            "development": 0.8,
            "testing": 0.6
        }
        
        base_factor *= function_multipliers.get(context.business_function, 1.0)
        
        return base_factor
    
    def _determine_risk_rating(self, composite_score: float) -> str:
        """Determine risk rating based on composite score"""
        if composite_score >= 9.0:
            return "Critical"
        elif composite_score >= 7.0:
            return "High"
        elif composite_score >= 4.0:
            return "Medium"
        elif composite_score >= 1.0:
            return "Low"
        else:
            return "Informational"
    
    def _generate_cvss_vector(self, vuln_data: Dict[str, Any]) -> str:
        """Generate CVSS v3 vector string"""
        # Simplified CVSS vector generation
        return "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H"
    
    async def _generate_remediation_recommendations(self, vuln_data: Dict[str, Any],
                                                  context: VulnerabilityContext) -> List[str]:
        """Generate remediation recommendations"""
        recommendations = []
        
        vuln_type = vuln_data.get("type", "")
        title = vuln_data.get("title", "").lower()
        
        # Type-specific recommendations
        if "sql injection" in title:
            recommendations.extend([
                "Implement parameterized queries/prepared statements",
                "Use input validation and sanitization",
                "Apply least privilege principle to database accounts",
                "Enable web application firewall (WAF) rules",
                "Regular security code review and testing"
            ])
        elif "xss" in title:
            recommendations.extend([
                "Implement output encoding/escaping",
                "Use Content Security Policy (CSP) headers",
                "Validate and sanitize all user input",
                "Use secure coding practices",
                "Regular security testing and code review"
            ])
        elif "authentication" in title:
            recommendations.extend([
                "Implement multi-factor authentication (MFA)",
                "Use strong password policies",
                "Implement account lockout mechanisms",
                "Use secure session management",
                "Regular authentication mechanism review"
            ])
        elif "configuration" in title:
            recommendations.extend([
                "Review and harden system configurations",
                "Remove default accounts and passwords",
                "Implement least privilege access controls",
                "Regular configuration audits",
                "Use configuration management tools"
            ])
        
        # Context-specific recommendations
        if context.compliance_requirements:
            recommendations.append(f"Ensure compliance with {', '.join(context.compliance_requirements)}")
        
        if context.exposure_level == "external":
            recommendations.append("Consider additional network security controls for external-facing assets")
        
        return recommendations[:10]  # Limit to top 10 recommendations
    
    def _assess_false_positive_likelihood(self, vuln_data: Dict[str, Any]) -> float:
        """Assess likelihood of false positive"""
        # Simple false positive assessment
        if vuln_data.get("evidence"):
            return 0.1  # Low false positive likelihood with evidence
        else:
            return 0.3  # Higher false positive likelihood without clear evidence
    
    async def _get_ai_vulnerability_analysis(self, vuln_data: Dict[str, Any],
                                           context: VulnerabilityContext) -> str:
        """Get AI analysis of vulnerability"""
        try:
            prompt = f"""
Analyze this vulnerability for comprehensive security assessment:

Vulnerability: {vuln_data.get('title', 'Unknown')}
Description: {vuln_data.get('description', '')}
Type: {vuln_data.get('type', '')}
Severity: {vuln_data.get('severity', '')}
Evidence: {vuln_data.get('evidence', '')}

Asset Context:
- Type: {context.asset_type}
- Criticality: {context.asset_criticality}
- Exposure: {context.exposure_level}
- Business Function: {context.business_function}
- Compliance: {context.compliance_requirements}

Provide analysis covering:
1. Exploitability assessment
2. Business impact evaluation
3. Attack scenarios
4. Defense strategies
5. Priority recommendation

Keep analysis concise and actionable.
"""
            
            response = await self.ai_service.analyze(
                prompt,
                context="vulnerability_analysis",
                provider_preference=["openai", "claude"]
            )
            
            return response.get("analysis", "No AI analysis available")
            
        except Exception as e:
            logger.error(f"Failed to get AI vulnerability analysis: {e}")
            return "AI analysis unavailable"
    
    async def _risk_assessment_phase(self, assessment: Dict[str, Any],
                                   vulnerabilities: List[VulnerabilityAssessment]) -> Dict[str, Any]:
        """Phase 3: Risk assessment and prioritization"""
        risk_summary = {
            "total_vulnerabilities": len(vulnerabilities),
            "risk_distribution": defaultdict(int),
            "category_distribution": defaultdict(int),
            "asset_risk_profile": defaultdict(list),
            "top_risks": [],
            "compliance_impact": defaultdict(list)
        }
        
        # Analyze risk distribution
        for vuln in vulnerabilities:
            risk_summary["risk_distribution"][vuln.risk_metrics.risk_rating] += 1
            risk_summary["category_distribution"][vuln.category.value] += 1
            
            # Group by asset
            for asset in vuln.affected_assets:
                risk_summary["asset_risk_profile"][asset].append({
                    "vulnerability_id": vuln.vulnerability_id,
                    "title": vuln.title,
                    "risk_score": vuln.risk_metrics.composite_risk_score,
                    "risk_rating": vuln.risk_metrics.risk_rating
                })
            
            # Compliance impact
            for compliance in vuln.context.compliance_requirements:
                risk_summary["compliance_impact"][compliance].append(vuln.vulnerability_id)
        
        # Identify top risks
        sorted_vulns = sorted(vulnerabilities,
                            key=lambda v: v.risk_metrics.composite_risk_score,
                            reverse=True)
        
        risk_summary["top_risks"] = [
            {
                "vulnerability_id": v.vulnerability_id,
                "title": v.title,
                "risk_score": v.risk_metrics.composite_risk_score,
                "risk_rating": v.risk_metrics.risk_rating,
                "affected_assets": v.affected_assets
            }
            for v in sorted_vulns[:10]
        ]
        
        logger.info("Risk assessment phase completed")
        return dict(risk_summary)
    
    async def _clustering_phase(self, assessment: Dict[str, Any],
                              vulnerabilities: List[VulnerabilityAssessment]) -> List[VulnerabilityCluster]:
        """Phase 4: Vulnerability clustering and correlation"""
        clusters = []
        
        # Group vulnerabilities by similarity
        similarity_clusters = self._cluster_by_similarity(vulnerabilities)
        clusters.extend(similarity_clusters)
        
        # Group vulnerabilities by attack chains
        attack_chain_clusters = self._cluster_by_attack_chains(vulnerabilities)
        clusters.extend(attack_chain_clusters)
        
        # Group vulnerabilities by affected assets
        asset_clusters = self._cluster_by_assets(vulnerabilities)
        clusters.extend(asset_clusters)
        
        logger.info(f"Clustering phase completed: {len(clusters)} clusters identified")
        return clusters
    
    def _cluster_by_similarity(self, vulnerabilities: List[VulnerabilityAssessment]) -> List[VulnerabilityCluster]:
        """Cluster vulnerabilities by similarity"""
        clusters = []
        category_groups = defaultdict(list)
        
        # Group by category
        for vuln in vulnerabilities:
            category_groups[vuln.category].append(vuln)
        
        # Create clusters for categories with multiple vulnerabilities
        for category, vulns in category_groups.items():
            if len(vulns) > 1:
                primary_vuln = max(vulns, key=lambda v: v.risk_metrics.composite_risk_score)
                
                cluster = VulnerabilityCluster(
                    cluster_id=f"similarity_{category.value}_{int(time.time())}",
                    cluster_type="similar_root_cause",
                    primary_vulnerability=primary_vuln.vulnerability_id,
                    related_vulnerabilities=[v.vulnerability_id for v in vulns if v != primary_vuln],
                    combined_risk_score=sum(v.risk_metrics.composite_risk_score for v in vulns),
                    attack_scenarios=[f"Exploit {category.value} vulnerabilities for system compromise"],
                    prioritized_remediation=f"Address {category.value} vulnerabilities systematically"
                )
                
                clusters.append(cluster)
        
        return clusters
    
    def _cluster_by_attack_chains(self, vulnerabilities: List[VulnerabilityAssessment]) -> List[VulnerabilityCluster]:
        """Cluster vulnerabilities that form attack chains"""
        clusters = []
        
        # Simple attack chain detection based on MITRE techniques
        technique_groups = defaultdict(list)
        
        for vuln in vulnerabilities:
            for technique in vuln.threat_intelligence.mitre_attack_techniques:
                technique_groups[technique].append(vuln)
        
        # Create clusters for techniques with multiple vulnerabilities
        for technique, vulns in technique_groups.items():
            if len(vulns) > 1:
                primary_vuln = max(vulns, key=lambda v: v.risk_metrics.composite_risk_score)
                
                cluster = VulnerabilityCluster(
                    cluster_id=f"attack_chain_{technique}_{int(time.time())}",
                    cluster_type="attack_chain",
                    primary_vulnerability=primary_vuln.vulnerability_id,
                    related_vulnerabilities=[v.vulnerability_id for v in vulns if v != primary_vuln],
                    combined_risk_score=sum(v.risk_metrics.composite_risk_score for v in vulns) * 1.2,  # Higher for chains
                    attack_scenarios=[f"Multi-stage attack using {technique} techniques"],
                    prioritized_remediation=f"Break attack chain by addressing {technique} vulnerabilities"
                )
                
                clusters.append(cluster)
        
        return clusters
    
    def _cluster_by_assets(self, vulnerabilities: List[VulnerabilityAssessment]) -> List[VulnerabilityCluster]:
        """Cluster vulnerabilities by affected assets"""
        clusters = []
        asset_groups = defaultdict(list)
        
        # Group by affected assets
        for vuln in vulnerabilities:
            for asset in vuln.affected_assets:
                asset_groups[asset].append(vuln)
        
        # Create clusters for assets with multiple vulnerabilities
        for asset, vulns in asset_groups.items():
            if len(vulns) > 2:  # Only cluster if 3+ vulnerabilities
                primary_vuln = max(vulns, key=lambda v: v.risk_metrics.composite_risk_score)
                
                cluster = VulnerabilityCluster(
                    cluster_id=f"asset_{hashlib.md5(asset.encode()).hexdigest()[:8]}",
                    cluster_type="asset_group",
                    primary_vulnerability=primary_vuln.vulnerability_id,
                    related_vulnerabilities=[v.vulnerability_id for v in vulns if v != primary_vuln],
                    combined_risk_score=sum(v.risk_metrics.composite_risk_score for v in vulns),
                    attack_scenarios=[f"Comprehensive compromise of {asset}"],
                    prioritized_remediation=f"Systematic remediation of {asset} vulnerabilities"
                )
                
                clusters.append(cluster)
        
        return clusters
    
    async def _ai_analysis_phase(self, assessment: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Phase 5: AI-powered analysis and insights"""
        ai_insights = []
        
        try:
            # Overall assessment analysis
            overall_analysis = await self._get_ai_overall_analysis(assessment)
            ai_insights.append({
                "type": "overall_analysis",
                "timestamp": datetime.now().isoformat(),
                "analysis": overall_analysis
            })
            
            # Strategic recommendations
            strategic_recommendations = await self._get_ai_strategic_recommendations(assessment)
            ai_insights.append({
                "type": "strategic_recommendations",
                "timestamp": datetime.now().isoformat(),
                "recommendations": strategic_recommendations
            })
            
            # Threat landscape analysis
            threat_analysis = await self._get_ai_threat_landscape_analysis(assessment)
            ai_insights.append({
                "type": "threat_landscape",
                "timestamp": datetime.now().isoformat(),
                "analysis": threat_analysis
            })
            
            logger.info("AI analysis phase completed")
            return ai_insights
            
        except Exception as e:
            logger.error(f"AI analysis phase failed: {e}")
            return ai_insights
    
    async def _get_ai_overall_analysis(self, assessment: Dict[str, Any]) -> str:
        """Get AI overall vulnerability assessment analysis"""
        try:
            risk_summary = assessment["risk_summary"]
            vulnerabilities = assessment["assessed_vulnerabilities"]
            
            prompt = f"""
Analyze this comprehensive vulnerability assessment:

Total Vulnerabilities: {risk_summary['total_vulnerabilities']}
Risk Distribution: {dict(risk_summary['risk_distribution'])}
Category Distribution: {dict(risk_summary['category_distribution'])}

Top 5 Critical Vulnerabilities:
{json.dumps([v for v in risk_summary['top_risks'][:5]], indent=2)}

Assessment Target: {assessment['target_config'].get('name', 'Unknown')}
Target Type: {assessment['target_config'].get('type', 'Unknown')}

Provide comprehensive analysis covering:
1. Overall security posture assessment
2. Key vulnerability patterns and trends
3. Business risk implications
4. Attack surface analysis
5. Defensive gap identification

Focus on strategic insights and actionable recommendations.
"""
            
            response = await self.ai_service.analyze(
                prompt,
                context="vulnerability_assessment_analysis",
                provider_preference=["openai", "claude"]
            )
            
            return response.get("analysis", "No overall analysis available")
            
        except Exception as e:
            logger.error(f"Failed to get AI overall analysis: {e}")
            return "AI overall analysis unavailable"
    
    async def _get_ai_strategic_recommendations(self, assessment: Dict[str, Any]) -> str:
        """Get AI strategic security recommendations"""
        try:
            prompt = f"""
Based on this vulnerability assessment, provide strategic security recommendations:

Assessment Summary:
- Target: {assessment['target_config'].get('name', 'Unknown')}
- Total Vulnerabilities: {assessment['risk_summary']['total_vulnerabilities']}
- Critical/High Risk: {assessment['risk_summary']['risk_distribution'].get('Critical', 0) + assessment['risk_summary']['risk_distribution'].get('High', 0)}
- Asset Criticality: {assessment['target_config'].get('criticality', 'Unknown')}
- Compliance Requirements: {assessment['target_config'].get('compliance', [])}

Top Risk Categories:
{dict(list(assessment['risk_summary']['category_distribution'].items())[:5])}

Provide strategic recommendations for:
1. Immediate priority actions (0-30 days)
2. Short-term improvements (1-3 months)
3. Long-term security strategy (3-12 months)
4. Compliance and governance improvements
5. Technology and process enhancements

Focus on business-aligned, practical recommendations.
"""
            
            response = await self.ai_service.analyze(
                prompt,
                context="strategic_security_recommendations",
                provider_preference=["openai", "claude"]
            )
            
            return response.get("analysis", "No strategic recommendations available")
            
        except Exception as e:
            logger.error(f"Failed to get AI strategic recommendations: {e}")
            return "AI strategic recommendations unavailable"
    
    async def _get_ai_threat_landscape_analysis(self, assessment: Dict[str, Any]) -> str:
        """Get AI threat landscape analysis"""
        try:
            # Analyze threat intelligence patterns
            threat_actors = set()
            mitre_techniques = set()
            
            for vuln in assessment["assessed_vulnerabilities"]:
                threat_actors.update(vuln.threat_intelligence.threat_actors)
                mitre_techniques.update(vuln.threat_intelligence.mitre_attack_techniques)
            
            prompt = f"""
Analyze the threat landscape for this vulnerability assessment:

Identified Threat Actors: {list(threat_actors)}
MITRE ATT&CK Techniques: {list(mitre_techniques)}
Assessment Target: {assessment['target_config'].get('name', 'Unknown')}
Industry/Sector: {assessment['target_config'].get('industry', 'Unknown')}

Vulnerability Risk Distribution:
{dict(assessment['risk_summary']['risk_distribution'])}

Provide threat landscape analysis covering:
1. Relevant threat actors and their capabilities
2. Current attack trends and campaigns
3. Industry-specific threat patterns
4. Threat intelligence correlation
5. Defensive strategy recommendations

Focus on actionable threat intelligence insights.
"""
            
            response = await self.ai_service.analyze(
                prompt,
                context="threat_landscape_analysis",
                provider_preference=["openai", "claude"]
            )
            
            return response.get("analysis", "No threat landscape analysis available")
            
        except Exception as e:
            logger.error(f"Failed to get AI threat landscape analysis: {e}")
            return "AI threat landscape analysis unavailable"
    
    async def _generate_assessment_report(self, assessment: Dict[str, Any]) -> Dict[str, Any]:
        """Generate comprehensive assessment report"""
        vulnerabilities = assessment["assessed_vulnerabilities"]
        risk_summary = assessment["risk_summary"]
        clusters = assessment["vulnerability_clusters"]
        
        report = {
            "assessment_id": assessment["assessment_id"],
            "target_name": assessment["target_config"].get("name", "Unknown"),
            "assessment_date": assessment["start_time"].isoformat(),
            "duration": assessment.get("duration", 0),
            "executive_summary": {
                "total_vulnerabilities": len(vulnerabilities),
                "critical_vulnerabilities": risk_summary["risk_distribution"].get("Critical", 0),
                "high_vulnerabilities": risk_summary["risk_distribution"].get("High", 0),
                "medium_vulnerabilities": risk_summary["risk_distribution"].get("Medium", 0),
                "low_vulnerabilities": risk_summary["risk_distribution"].get("Low", 0),
                "overall_risk_rating": self._calculate_overall_risk_rating(vulnerabilities),
                "key_findings": self._extract_key_findings(vulnerabilities, clusters)
            },
            "risk_analysis": {
                "risk_distribution": dict(risk_summary["risk_distribution"]),
                "category_distribution": dict(risk_summary["category_distribution"]),
                "top_risks": risk_summary["top_risks"],
                "asset_risk_profile": dict(risk_summary["asset_risk_profile"]),
                "compliance_impact": dict(risk_summary["compliance_impact"])
            },
            "vulnerability_details": [asdict(vuln) for vuln in vulnerabilities],
            "vulnerability_clusters": [asdict(cluster) for cluster in clusters],
            "remediation_roadmap": self._generate_remediation_roadmap(vulnerabilities, clusters),
            "ai_insights": assessment["ai_insights"],
            "recommendations": self._generate_executive_recommendations(assessment),
            "compliance_status": self._assess_compliance_status(vulnerabilities, assessment["target_config"])
        }
        
        return report
    
    def _calculate_overall_risk_rating(self, vulnerabilities: List[VulnerabilityAssessment]) -> str:
        """Calculate overall risk rating for assessment"""
        if not vulnerabilities:
            return "Low"
        
        # Count by risk rating
        risk_counts = defaultdict(int)
        for vuln in vulnerabilities:
            risk_counts[vuln.risk_metrics.risk_rating] += 1
        
        # Determine overall rating
        if risk_counts["Critical"] > 0:
            return "Critical"
        elif risk_counts["High"] >= 3:
            return "High"
        elif risk_counts["High"] > 0 or risk_counts["Medium"] >= 5:
            return "Medium"
        else:
            return "Low"
    
    def _extract_key_findings(self, vulnerabilities: List[VulnerabilityAssessment],
                            clusters: List[VulnerabilityCluster]) -> List[str]:
        """Extract key findings from assessment"""
        findings = []
        
        # Critical vulnerabilities
        critical_vulns = [v for v in vulnerabilities if v.threat_level == ThreatLevel.CRITICAL]
        if critical_vulns:
            findings.append(f"{len(critical_vulns)} critical vulnerabilities requiring immediate attention")
        
        # Most common category
        category_counts = Counter(v.category for v in vulnerabilities)
        if category_counts:
            top_category = category_counts.most_common(1)[0]
            findings.append(f"{top_category[0].value} vulnerabilities are most prevalent ({top_category[1]} instances)")
        
        # Attack chains
        attack_chain_clusters = [c for c in clusters if c.cluster_type == "attack_chain"]
        if attack_chain_clusters:
            findings.append(f"{len(attack_chain_clusters)} potential attack chains identified")
        
        # Compliance impact
        compliance_vulns = [v for v in vulnerabilities if v.context.compliance_requirements]
        if compliance_vulns:
            findings.append(f"{len(compliance_vulns)} vulnerabilities may impact compliance requirements")
        
        return findings[:5]  # Top 5 findings
    
    def _generate_remediation_roadmap(self, vulnerabilities: List[VulnerabilityAssessment],
                                    clusters: List[VulnerabilityCluster]) -> Dict[str, Any]:
        """Generate remediation roadmap"""
        # Sort vulnerabilities by risk score
        sorted_vulns = sorted(vulnerabilities,
                            key=lambda v: v.risk_metrics.composite_risk_score,
                            reverse=True)
        
        roadmap = {
            "immediate_actions": [],  # 0-30 days
            "short_term_actions": [],  # 1-3 months
            "long_term_actions": []   # 3-12 months
        }
        
        # Immediate actions (Critical and High risk)
        immediate_vulns = [v for v in sorted_vulns[:10] if v.risk_metrics.risk_rating in ["Critical", "High"]]
        for vuln in immediate_vulns:
            roadmap["immediate_actions"].append({
                "vulnerability_id": vuln.vulnerability_id,
                "title": vuln.title,
                "priority": "Critical" if vuln.threat_level == ThreatLevel.CRITICAL else "High",
                "estimated_effort": "Medium",
                "recommendations": vuln.remediation_recommendations[:3]
            })
        
        # Short-term actions (Medium risk and clusters)
        medium_vulns = [v for v in sorted_vulns if v.risk_metrics.risk_rating == "Medium"][:15]
        for vuln in medium_vulns:
            roadmap["short_term_actions"].append({
                "vulnerability_id": vuln.vulnerability_id,
                "title": vuln.title,
                "priority": "Medium",
                "estimated_effort": "Low",
                "recommendations": vuln.remediation_recommendations[:2]
            })
        
        # Add cluster-based actions
        for cluster in clusters:
            if cluster.combined_risk_score > 30:  # High-impact clusters
                roadmap["short_term_actions"].append({
                    "cluster_id": cluster.cluster_id,
                    "title": f"Address {cluster.cluster_type} vulnerabilities",
                    "priority": "Medium",
                    "estimated_effort": "High",
                    "recommendations": [cluster.prioritized_remediation]
                })
        
        # Long-term actions (Low risk and systematic improvements)
        low_vulns = [v for v in sorted_vulns if v.risk_metrics.risk_rating == "Low"]
        roadmap["long_term_actions"].extend([
            {
                "type": "systematic_improvement",
                "title": "Security architecture review",
                "priority": "Low",
                "estimated_effort": "High",
                "recommendations": ["Comprehensive security architecture assessment", "Defense in depth implementation"]
            },
            {
                "type": "process_improvement",
                "title": "Security testing integration",
                "priority": "Low",
                "estimated_effort": "Medium",
                "recommendations": ["Automated vulnerability scanning", "Continuous security monitoring"]
            }
        ])
        
        return roadmap
    
    def _generate_executive_recommendations(self, assessment: Dict[str, Any]) -> List[str]:
        """Generate executive-level recommendations"""
        recommendations = []
        risk_summary = assessment["risk_summary"]
        target_config = assessment["target_config"]
        
        # Risk-based recommendations
        critical_count = risk_summary["risk_distribution"].get("Critical", 0)
        high_count = risk_summary["risk_distribution"].get("High", 0)
        
        if critical_count > 0:
            recommendations.append(f"Immediate action required: {critical_count} critical vulnerabilities pose significant business risk")
        
        if high_count > 3:
            recommendations.append(f"Prioritize remediation of {high_count} high-risk vulnerabilities within 30 days")
        
        # Asset-specific recommendations
        if target_config.get("exposure") == "external":
            recommendations.append("Implement additional security controls for external-facing assets")
        
        # Compliance recommendations
        if target_config.get("compliance"):
            recommendations.append(f"Address compliance gaps for {', '.join(target_config['compliance'])} requirements")
        
        # Strategic recommendations
        recommendations.extend([
            "Implement continuous vulnerability management program",
            "Enhance security awareness training for development teams",
            "Consider security architecture review for long-term risk reduction"
        ])
        
        return recommendations[:8]  # Top 8 recommendations
    
    def _assess_compliance_status(self, vulnerabilities: List[VulnerabilityAssessment],
                                target_config: Dict[str, Any]) -> Dict[str, Any]:
        """Assess compliance status based on vulnerabilities"""
        compliance_requirements = target_config.get("compliance", [])
        compliance_status = {}
        
        for requirement in compliance_requirements:
            affected_vulns = [v for v in vulnerabilities if requirement in v.context.compliance_requirements]
            
            # Simple compliance assessment
            critical_violations = len([v for v in affected_vulns if v.threat_level == ThreatLevel.CRITICAL])
            high_violations = len([v for v in affected_vulns if v.threat_level == ThreatLevel.HIGH])
            
            if critical_violations > 0:
                status = "Non-Compliant"
                risk_level = "High"
            elif high_violations > 2:
                status = "At Risk"
                risk_level = "Medium"
            elif len(affected_vulns) > 0:
                status = "Needs Attention"
                risk_level = "Low"
            else:
                status = "Compliant"
                risk_level = "Low"
            
            compliance_status[requirement] = {
                "status": status,
                "risk_level": risk_level,
                "affected_vulnerabilities": len(affected_vulns),
                "critical_violations": critical_violations,
                "high_violations": high_violations
            }
        
        return compliance_status
    
    def get_assessment_status(self, assessment_id: str) -> Optional[Dict[str, Any]]:
        """Get vulnerability assessment status"""
        if assessment_id not in self.active_assessments:
            return None
        
        assessment = self.active_assessments[assessment_id]
        
        progress_map = {
            "initialization": 0,
            "discovery": 20,
            "analysis": 40,
            "risk_assessment": 60,
            "clustering": 80,
            "ai_analysis": 90,
            "completed": 100
        }
        
        return {
            "assessment_id": assessment_id,
            "status": assessment["status"],
            "phase": assessment["phase"],
            "progress": progress_map.get(assessment["phase"], 0),
            "vulnerabilities_found": len(assessment.get("discovered_vulnerabilities", [])),
            "vulnerabilities_assessed": len(assessment.get("assessed_vulnerabilities", [])),
            "elapsed_time": (datetime.now() - assessment["start_time"]).total_seconds()
        }
    
    def get_vulnerability_details(self, vulnerability_id: str) -> Optional[Dict[str, Any]]:
        """Get detailed vulnerability information"""
        if vulnerability_id not in self.vulnerability_database:
            return None
        
        vuln = self.vulnerability_database[vulnerability_id]
        return asdict(vuln)
    
    def list_active_assessments(self) -> List[Dict[str, Any]]:
        """List all active vulnerability assessments"""
        assessments = []
        
        for assessment_id, assessment in self.active_assessments.items():
            assessments.append({
                "assessment_id": assessment_id,
                "target": assessment["target_config"].get("name", "Unknown"),
                "status": assessment["status"],
                "phase": assessment["phase"],
                "start_time": assessment["start_time"].isoformat(),
                "vulnerabilities_found": len(assessment.get("discovered_vulnerabilities", []))
            })
        
        return assessments
    
    async def _real_network_vulnerability_scan(self, target_config: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Perform real network vulnerability scanning"""
        vulnerabilities = []
        
        try:
            logger.warning("REAL EXECUTION MODE: Performing network vulnerability scan")
            
            target = target_config.get("host", target_config.get("target", "127.0.0.1"))
            
            # Step 1: Network discovery with nmap
            nmap_results = await self._execute_nmap_scan(target)
            
            # Step 2: SSL/TLS assessment
            ssl_vulns = await self._assess_ssl_configuration(target, nmap_results.get("open_ports", []))
            vulnerabilities.extend(ssl_vulns)
            
            # Step 3: Service version vulnerabilities
            service_vulns = await self._assess_service_vulnerabilities(nmap_results)
            vulnerabilities.extend(service_vulns)
            
            # Step 4: Network configuration assessment
            config_vulns = await self._assess_network_configuration(target, nmap_results)
            vulnerabilities.extend(config_vulns)
            
            logger.info(f"Real network scan completed: {len(vulnerabilities)} vulnerabilities found")
            return vulnerabilities
            
        except Exception as e:
            logger.error(f"Real network vulnerability scan failed: {e}")
            return [{
                "type": "network",
                "title": "Network Scan Error",
                "description": f"Network vulnerability scan failed: {str(e)}",
                "severity": "informational",
                "evidence": str(e)
            }]
    
    async def _execute_nmap_scan(self, target: str) -> Dict[str, Any]:
        """Execute comprehensive nmap scan"""
        try:
            import asyncio
            
            # Comprehensive nmap scan with service detection and vulnerability scripts
            nmap_command = f"nmap -sV -sC --script vuln -p- {target}"
            
            process = await asyncio.create_subprocess_shell(
                nmap_command,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await asyncio.wait_for(process.communicate(), timeout=1800)  # 30 min timeout
            
            if process.returncode == 0:
                output = stdout.decode('utf-8', errors='ignore')
                return self._parse_nmap_output(output)
            else:
                error = stderr.decode('utf-8', errors='ignore')
                logger.error(f"Nmap scan failed: {error}")
                return {"error": error, "open_ports": [], "services": []}
                
        except asyncio.TimeoutError:
            logger.error("Nmap scan timed out")
            return {"error": "Scan timeout", "open_ports": [], "services": []}
        except Exception as e:
            logger.error(f"Nmap execution failed: {e}")
            return {"error": str(e), "open_ports": [], "services": []}
    
    def _parse_nmap_output(self, nmap_output: str) -> Dict[str, Any]:
        """Parse nmap scan output"""
        import re
        
        result = {
            "open_ports": [],
            "services": [],
            "vulnerabilities": [],
            "os_detection": "",
            "raw_output": nmap_output
        }
        
        lines = nmap_output.split('\n')
        
        for line in lines:
            # Parse open ports and services
            if '/tcp' in line and 'open' in line:
                parts = line.split()
                if len(parts) >= 3:
                    port = parts[0].split('/')[0]
                    service = parts[2] if len(parts) > 2 else 'unknown'
                    version = ' '.join(parts[3:]) if len(parts) > 3 else ''
                    
                    result["open_ports"].append(port)
                    result["services"].append({
                        "port": port,
                        "service": service,
                        "version": version,
                        "line": line.strip()
                    })
            
            # Parse vulnerabilities from script output
            elif 'CVE-' in line:
                cve_matches = re.findall(r'CVE-\d{4}-\d{4,7}', line)
                for cve in cve_matches:
                    result["vulnerabilities"].append({
                        "cve": cve,
                        "context": line.strip()
                    })
            
            # Parse OS detection
            elif 'OS details:' in line:
                result["os_detection"] = line.replace('OS details:', '').strip()
        
        return result
    
    async def _assess_ssl_configuration(self, target: str, open_ports: List[str]) -> List[Dict[str, Any]]:
        """Assess SSL/TLS configuration"""
        vulnerabilities = []
        
        # Common SSL/TLS ports
        ssl_ports = ['443', '993', '995', '465', '587', '636']
        target_ssl_ports = [port for port in open_ports if port in ssl_ports]
        
        for port in target_ssl_ports:
            try:
                # Use openssl to test SSL configuration
                ssl_command = f"echo | openssl s_client -connect {target}:{port} -servername {target} 2>/dev/null"
                
                process = await asyncio.create_subprocess_shell(
                    ssl_command,
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )
                
                stdout, stderr = await asyncio.wait_for(process.communicate(), timeout=30)
                
                if process.returncode == 0:
                    ssl_output = stdout.decode('utf-8', errors='ignore')
                    ssl_vulns = self._analyze_ssl_output(ssl_output, target, port)
                    vulnerabilities.extend(ssl_vulns)
                    
            except Exception as e:
                logger.warning(f"SSL assessment failed for {target}:{port}: {e}")
                continue
        
        return vulnerabilities
    
    def _analyze_ssl_output(self, ssl_output: str, target: str, port: str) -> List[Dict[str, Any]]:
        """Analyze SSL/TLS configuration output"""
        vulnerabilities = []
        
        # Check for weak protocols
        if 'Protocol  : SSLv3' in ssl_output or 'Protocol  : TLSv1' in ssl_output:
            vulnerabilities.append({
                "type": "network",
                "title": "Weak SSL/TLS Protocol",
                "description": f"Weak SSL/TLS protocol detected on {target}:{port}",
                "severity": "high",
                "affected_ports": [port],
                "evidence": "SSLv3 or TLS 1.0 protocol in use",
                "cve": "CVE-2014-3566"  # POODLE
            })
        
        # Check for weak ciphers
        import re
        cipher_match = re.search(r'Cipher\s*:\s*([^\s]+)', ssl_output)
        if cipher_match:
            cipher = cipher_match.group(1)
            if any(weak in cipher.upper() for weak in ['RC4', 'MD5', 'DES', 'NULL']):
                vulnerabilities.append({
                    "type": "network",
                    "title": "Weak SSL/TLS Cipher",
                    "description": f"Weak cipher suite detected: {cipher}",
                    "severity": "medium",
                    "affected_ports": [port],
                    "evidence": f"Cipher: {cipher}"
                })
        
        # Check certificate validity
        if 'Verify return code: 0 (ok)' not in ssl_output:
            if 'self signed certificate' in ssl_output:
                vulnerabilities.append({
                    "type": "network",
                    "title": "Self-Signed SSL Certificate",
                    "description": f"Self-signed certificate detected on {target}:{port}",
                    "severity": "medium",
                    "affected_ports": [port],
                    "evidence": "Self-signed certificate in use"
                })
        
        return vulnerabilities
    
    async def _assess_service_vulnerabilities(self, nmap_results: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Assess vulnerabilities in detected services"""
        vulnerabilities = []
        
        for service in nmap_results.get("services", []):
            service_name = service.get("service", "")
            version = service.get("version", "")
            port = service.get("port", "")
            
            # Check for known vulnerable services
            if "ssh" in service_name.lower():
                ssh_vulns = self._check_ssh_vulnerabilities(version, port)
                vulnerabilities.extend(ssh_vulns)
            
            elif "http" in service_name.lower():
                http_vulns = self._check_http_vulnerabilities(version, port)
                vulnerabilities.extend(http_vulns)
            
            elif "ftp" in service_name.lower():
                ftp_vulns = self._check_ftp_vulnerabilities(version, port)
                vulnerabilities.extend(ftp_vulns)
            
            elif "telnet" in service_name.lower():
                vulnerabilities.append({
                    "type": "network",
                    "title": "Unencrypted Telnet Service",
                    "description": "Telnet service transmits data in clear text",
                    "severity": "high",
                    "affected_ports": [port],
                    "evidence": f"Telnet service on port {port}",
                    "service": service_name
                })
        
        return vulnerabilities
    
    def _check_ssh_vulnerabilities(self, version: str, port: str) -> List[Dict[str, Any]]:
        """Check for SSH-specific vulnerabilities"""
        vulnerabilities = []
        
        # Check for old SSH versions
        import re
        version_match = re.search(r'OpenSSH[_\s]+(\d+\.\d+)', version)
        if version_match:
            ssh_version = float(version_match.group(1))
            if ssh_version < 7.0:
                vulnerabilities.append({
                    "type": "network",
                    "title": "Outdated SSH Version",
                    "description": f"Outdated SSH version detected: {version}",
                    "severity": "medium",
                    "affected_ports": [port],
                    "evidence": f"SSH version: {version}",
                    "recommendation": "Update to OpenSSH 7.0 or later"
                })
        
        return vulnerabilities
    
    def _check_http_vulnerabilities(self, version: str, port: str) -> List[Dict[str, Any]]:
        """Check for HTTP service vulnerabilities"""
        vulnerabilities = []
        
        # Check for server information disclosure
        if any(server in version.lower() for server in ['apache', 'nginx', 'iis']):
            vulnerabilities.append({
                "type": "network",
                "title": "HTTP Server Information Disclosure",
                "description": "HTTP server version information disclosed",
                "severity": "low",
                "affected_ports": [port],
                "evidence": f"Server: {version}",
                "recommendation": "Configure server to hide version information"
            })
        
        return vulnerabilities
    
    def _check_ftp_vulnerabilities(self, version: str, port: str) -> List[Dict[str, Any]]:
        """Check for FTP service vulnerabilities"""
        vulnerabilities = []
        
        # FTP is inherently insecure
        vulnerabilities.append({
            "type": "network",
            "title": "Unencrypted FTP Service",
            "description": "FTP service transmits credentials and data in clear text",
            "severity": "high",
            "affected_ports": [port],
            "evidence": f"FTP service on port {port}",
            "recommendation": "Replace with SFTP or FTPS"
        })
        
        return vulnerabilities
    
    async def _assess_network_configuration(self, target: str, nmap_results: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Assess network configuration vulnerabilities"""
        vulnerabilities = []
        
        open_ports = nmap_results.get("open_ports", [])
        
        # Check for excessive open ports
        if len(open_ports) > 20:
            vulnerabilities.append({
                "type": "network",
                "title": "Excessive Open Ports",
                "description": f"Large number of open ports detected: {len(open_ports)}",
                "severity": "medium",
                "affected_ports": open_ports,
                "evidence": f"Open ports: {', '.join(open_ports)}",
                "recommendation": "Close unnecessary ports and services"
            })
        
        # Check for dangerous ports
        dangerous_ports = ['23', '69', '135', '139', '445', '1433', '1521', '3306', '5432']
        exposed_dangerous = [port for port in open_ports if port in dangerous_ports]
        
        if exposed_dangerous:
            vulnerabilities.append({
                "type": "network",
                "title": "Dangerous Ports Exposed",
                "description": "Potentially dangerous ports are accessible",
                "severity": "high",
                "affected_ports": exposed_dangerous,
                "evidence": f"Dangerous ports: {', '.join(exposed_dangerous)}",
                "recommendation": "Restrict access to dangerous ports using firewall"
            })
        
        return vulnerabilities