"""
Universal Tool Output Parser for NexusScan Desktop Application
Standardizes output from various security tools into a common format.
"""

import json
import xml.etree.ElementTree as ET
import re
import logging
from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass
from datetime import datetime

logger = logging.getLogger(__name__)


@dataclass
class ParsedVulnerability:
    """Standardized vulnerability representation"""
    id: str
    type: str
    severity: str
    title: str
    description: str
    target: str
    port: Optional[int] = None
    service: Optional[str] = None
    evidence: Optional[Dict[str, Any]] = None
    remediation: Optional[str] = None
    references: Optional[List[str]] = None
    cvss_score: Optional[float] = None
    cve_id: Optional[str] = None
    
    def __post_init__(self):
        if self.evidence is None:
            self.evidence = {}
        if self.references is None:
            self.references = []


@dataclass
class ParsedService:
    """Standardized service representation"""
    port: int
    protocol: str
    service: str
    state: str
    version: Optional[str] = None
    product: Optional[str] = None
    banner: Optional[str] = None
    confidence: Optional[int] = None


@dataclass
class ParsedHost:
    """Standardized host representation"""
    ip: str
    hostname: Optional[str] = None
    status: str = "unknown"
    os: Optional[str] = None
    services: Optional[List[ParsedService]] = None
    vulnerabilities: Optional[List[ParsedVulnerability]] = None
    
    def __post_init__(self):
        if self.services is None:
            self.services = []
        if self.vulnerabilities is None:
            self.vulnerabilities = []


class UniversalParser:
    """Universal parser for security tool outputs"""
    
    def __init__(self):
        """Initialize universal parser"""
        self.severity_mapping = {
            # Common severity levels
            "critical": "critical",
            "high": "high", 
            "medium": "medium",
            "low": "low",
            "info": "low",
            "informational": "low",
            
            # Numeric mappings (CVSS-like)
            "10.0": "critical",
            "9": "critical",
            "8": "high",
            "7": "high",
            "6": "medium",
            "5": "medium",
            "4": "medium",
            "3": "low",
            "2": "low",
            "1": "low",
            "0": "low"
        }
    
    def parse_nmap_output(self, nmap_data: Union[str, Dict]) -> List[ParsedHost]:
        """Parse Nmap output (XML or JSON)"""
        if isinstance(nmap_data, str):
            # Try to parse as XML first, then JSON
            try:
                return self._parse_nmap_xml(nmap_data)
            except:
                try:
                    nmap_data = json.loads(nmap_data)
                    return self._parse_nmap_json(nmap_data)
                except:
                    logger.error("Failed to parse Nmap output")
                    return []
        elif isinstance(nmap_data, dict):
            return self._parse_nmap_json(nmap_data)
        else:
            return []
    
    def _parse_nmap_json(self, data: Dict) -> List[ParsedHost]:
        """Parse Nmap JSON output"""
        hosts = []
        
        for ip, host_data in data.items():
            host = ParsedHost(ip=ip)
            
            # Extract hostname
            hostnames = host_data.get("hostnames", [])
            if hostnames:
                host.hostname = hostnames[0].get("name", "")
            
            # Extract status
            status_info = host_data.get("status", {})
            host.status = status_info.get("state", "unknown")
            
            # Extract OS information
            os_matches = host_data.get("osmatch", [])
            if os_matches:
                host.os = os_matches[0].get("name", "")
            
            # Extract services (TCP)
            if "tcp" in host_data:
                for port, port_data in host_data["tcp"].items():
                    service = ParsedService(
                        port=int(port),
                        protocol="tcp",
                        service=port_data.get("name", "unknown"),
                        state=port_data.get("state", "unknown"),
                        version=port_data.get("version", ""),
                        product=port_data.get("product", ""),
                        confidence=port_data.get("conf", 0)
                    )
                    host.services.append(service)
            
            # Extract services (UDP)
            if "udp" in host_data:
                for port, port_data in host_data["udp"].items():
                    service = ParsedService(
                        port=int(port),
                        protocol="udp",
                        service=port_data.get("name", "unknown"),
                        state=port_data.get("state", "unknown"),
                        version=port_data.get("version", ""),
                        product=port_data.get("product", ""),
                        confidence=port_data.get("conf", 0)
                    )
                    host.services.append(service)
            
            hosts.append(host)
        
        return hosts
    
    def _parse_nmap_xml(self, xml_data: str) -> List[ParsedHost]:
        """Parse Nmap XML output"""
        hosts = []
        
        try:
            root = ET.fromstring(xml_data)
            
            for host_elem in root.findall('.//host'):
                # Extract IP address
                address_elem = host_elem.find('.//address[@addrtype="ipv4"]')
                if address_elem is None:
                    continue
                
                ip = address_elem.get("addr")
                host = ParsedHost(ip=ip)
                
                # Extract hostname
                hostname_elem = host_elem.find('.//hostname')
                if hostname_elem is not None:
                    host.hostname = hostname_elem.get("name")
                
                # Extract status
                status_elem = host_elem.find('.//status')
                if status_elem is not None:
                    host.status = status_elem.get("state", "unknown")
                
                # Extract OS
                os_elem = host_elem.find('.//osmatch')
                if os_elem is not None:
                    host.os = os_elem.get("name")
                
                # Extract ports and services
                for port_elem in host_elem.findall('.//port'):
                    port_id = int(port_elem.get("portid"))
                    protocol = port_elem.get("protocol")
                    
                    state_elem = port_elem.find('.//state')
                    state = state_elem.get("state") if state_elem is not None else "unknown"
                    
                    service_elem = port_elem.find('.//service')
                    if service_elem is not None:
                        service = ParsedService(
                            port=port_id,
                            protocol=protocol,
                            service=service_elem.get("name", "unknown"),
                            state=state,
                            version=service_elem.get("version", ""),
                            product=service_elem.get("product", ""),
                            confidence=int(service_elem.get("conf", "0"))
                        )
                        host.services.append(service)
                
                hosts.append(host)
        
        except ET.ParseError as e:
            logger.error(f"Failed to parse Nmap XML: {e}")
        
        return hosts
    
    def parse_nuclei_output(self, nuclei_data: str) -> List[ParsedVulnerability]:
        """Parse Nuclei JSON output"""
        vulnerabilities = []
        
        try:
            # Nuclei outputs one JSON object per line
            for line in nuclei_data.strip().split('\n'):
                if not line.strip():
                    continue
                
                try:
                    finding = json.loads(line)
                    vuln = self._parse_nuclei_finding(finding)
                    if vuln:
                        vulnerabilities.append(vuln)
                except json.JSONDecodeError:
                    continue
        
        except Exception as e:
            logger.error(f"Failed to parse Nuclei output: {e}")
        
        return vulnerabilities
    
    def _parse_nuclei_finding(self, finding: Dict) -> Optional[ParsedVulnerability]:
        """Parse individual Nuclei finding"""
        try:
            template_id = finding.get("template-id", "unknown")
            info = finding.get("info", {})
            
            # Extract basic information
            title = info.get("name", template_id)
            description = info.get("description", "")
            severity = self._normalize_severity(info.get("severity", "low"))
            
            # Extract target information
            target = finding.get("host", "")
            matched_at = finding.get("matched-at", "")
            
            # Extract evidence
            evidence = {
                "template_id": template_id,
                "matched_at": matched_at,
                "raw_finding": finding
            }
            
            # Extract CVE if present
            cve_id = None
            references = info.get("reference", [])
            for ref in references:
                if "CVE-" in ref:
                    cve_match = re.search(r'CVE-\d{4}-\d+', ref)
                    if cve_match:
                        cve_id = cve_match.group()
                        break
            
            vulnerability = ParsedVulnerability(
                id=f"nuclei_{template_id}_{target}",
                type="nuclei_template",
                severity=severity,
                title=title,
                description=description,
                target=target,
                evidence=evidence,
                references=references,
                cve_id=cve_id
            )
            
            return vulnerability
        
        except Exception as e:
            logger.error(f"Failed to parse Nuclei finding: {e}")
            return None
    
    def parse_sqlmap_output(self, sqlmap_data: str) -> List[ParsedVulnerability]:
        """Parse SQLMap output"""
        vulnerabilities = []
        
        try:
            # Look for SQL injection indicators in text output
            lines = sqlmap_data.split('\n')
            current_target = ""
            
            for line in lines:
                line = line.strip()
                
                # Extract target URL
                if "testing URL" in line or "testing parameter" in line:
                    url_match = re.search(r'https?://[^\s]+', line)
                    if url_match:
                        current_target = url_match.group()
                
                # Look for vulnerability indicators
                if any(indicator in line.lower() for indicator in [
                    "vulnerable", "injectable", "sql injection", "payload"
                ]):
                    vuln = self._create_sqlmap_vulnerability(line, current_target)
                    if vuln:
                        vulnerabilities.append(vuln)
        
        except Exception as e:
            logger.error(f"Failed to parse SQLMap output: {e}")
        
        return vulnerabilities
    
    def _create_sqlmap_vulnerability(self, finding_line: str, target: str) -> Optional[ParsedVulnerability]:
        """Create vulnerability from SQLMap finding"""
        try:
            vulnerability = ParsedVulnerability(
                id=f"sqlmap_{target}_{hash(finding_line)}",
                type="sql_injection",
                severity="high",  # SQL injection is typically high severity
                title="SQL Injection Vulnerability",
                description=f"SQL injection vulnerability detected: {finding_line}",
                target=target,
                evidence={"sqlmap_output": finding_line},
                remediation="Use parameterized queries and input validation"
            )
            return vulnerability
        except:
            return None
    
    def parse_custom_json(self, json_data: Union[str, Dict], tool_name: str) -> List[ParsedVulnerability]:
        """Parse custom JSON format from security tools"""
        vulnerabilities = []
        
        try:
            if isinstance(json_data, str):
                data = json.loads(json_data)
            else:
                data = json_data
            
            # Handle different JSON structures
            if "vulnerabilities" in data:
                for vuln_data in data["vulnerabilities"]:
                    vuln = self._parse_generic_vulnerability(vuln_data, tool_name)
                    if vuln:
                        vulnerabilities.append(vuln)
            
            elif "results" in data:
                for result in data["results"]:
                    if "vulnerabilities" in result:
                        for vuln_data in result["vulnerabilities"]:
                            vuln = self._parse_generic_vulnerability(vuln_data, tool_name)
                            if vuln:
                                vulnerabilities.append(vuln)
        
        except Exception as e:
            logger.error(f"Failed to parse custom JSON: {e}")
        
        return vulnerabilities
    
    def _parse_generic_vulnerability(self, vuln_data: Dict, tool_name: str) -> Optional[ParsedVulnerability]:
        """Parse generic vulnerability structure"""
        try:
            vuln_id = vuln_data.get("id", f"{tool_name}_{hash(str(vuln_data))}")
            
            vulnerability = ParsedVulnerability(
                id=vuln_id,
                type=vuln_data.get("type", "unknown"),
                severity=self._normalize_severity(vuln_data.get("severity", "low")),
                title=vuln_data.get("title", vuln_data.get("name", "Unknown Vulnerability")),
                description=vuln_data.get("description", ""),
                target=vuln_data.get("target", vuln_data.get("host", "")),
                port=vuln_data.get("port"),
                service=vuln_data.get("service"),
                evidence=vuln_data.get("evidence", {}),
                remediation=vuln_data.get("remediation", vuln_data.get("fix", "")),
                references=vuln_data.get("references", []),
                cvss_score=vuln_data.get("cvss_score", vuln_data.get("cvss")),
                cve_id=vuln_data.get("cve_id", vuln_data.get("cve"))
            )
            
            return vulnerability
        
        except Exception as e:
            logger.error(f"Failed to parse generic vulnerability: {e}")
            return None
    
    def _normalize_severity(self, severity: str) -> str:
        """Normalize severity levels"""
        if not severity:
            return "low"
        
        severity_lower = str(severity).lower()
        
        # Check exact matches first
        if severity_lower in self.severity_mapping:
            return self.severity_mapping[severity_lower]
        
        # Check for partial matches
        for key, value in self.severity_mapping.items():
            if key in severity_lower:
                return value
        
        # Default fallback
        return "low"
    
    def merge_results(self, *result_lists: List[Union[ParsedHost, ParsedVulnerability]]) -> Dict[str, Any]:
        """Merge results from multiple tools"""
        merged = {
            "hosts": [],
            "vulnerabilities": [],
            "summary": {
                "total_hosts": 0,
                "total_vulnerabilities": 0,
                "severity_distribution": {
                    "critical": 0,
                    "high": 0,
                    "medium": 0,
                    "low": 0
                }
            }
        }
        
        host_map = {}  # IP -> ParsedHost for deduplication
        vuln_set = set()  # For vulnerability deduplication
        
        for result_list in result_lists:
            for item in result_list:
                if isinstance(item, ParsedHost):
                    if item.ip in host_map:
                        # Merge with existing host
                        existing_host = host_map[item.ip]
                        existing_host.services.extend(item.services)
                        existing_host.vulnerabilities.extend(item.vulnerabilities)
                    else:
                        host_map[item.ip] = item
                
                elif isinstance(item, ParsedVulnerability):
                    # Use ID for deduplication
                    if item.id not in vuln_set:
                        vuln_set.add(item.id)
                        merged["vulnerabilities"].append(item)
                        merged["summary"]["severity_distribution"][item.severity] += 1
        
        # Convert host map to list
        merged["hosts"] = list(host_map.values())
        merged["summary"]["total_hosts"] = len(merged["hosts"])
        merged["summary"]["total_vulnerabilities"] = len(merged["vulnerabilities"])
        
        return merged
    
    def export_to_json(self, data: Dict[str, Any]) -> str:
        """Export parsed data to JSON format"""
        try:
            return json.dumps(data, indent=2, default=str)
        except Exception as e:
            logger.error(f"Failed to export to JSON: {e}")
            return "{}"
    
    def get_supported_formats(self) -> List[str]:
        """Get list of supported input formats"""
        return [
            "nmap_xml",
            "nmap_json", 
            "nuclei_json",
            "sqlmap_text",
            "custom_json"
        ]