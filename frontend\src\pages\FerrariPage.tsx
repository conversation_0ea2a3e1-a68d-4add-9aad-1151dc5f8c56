/**
 * Ferrari AI Page - Advanced AI Capabilities Hub
 * Central interface for all Ferrari AI features and capabilities
 */
import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import {
  Brain,
  Activity,
  Cpu,
  Network,
  Eye,
  AlertTriangle,
  Shield,
  Target,
  Sparkles,
  Workflow,
  RefreshCw
} from 'lucide-react';

// Import stores
import { useBackendStore } from '@/stores/backend-store';
import { useAppStore } from '@/stores/app-store';

interface FerrariCapability {
  id: string;
  name: string;
  description: string;
  icon: React.ComponentType;
  status: 'operational' | 'degraded' | 'offline';
  confidence: number;
  lastUpdate: string;
  aiModels: string[];
  features: string[];
  category: 'orchestration' | 'generation' | 'analysis' | 'evasion' | 'modification' | 'proxy';
  available: boolean;
}

export const FerrariPage: React.FC = () => {
  const { isConnected, aiStatus, refreshAIStatus } = useBackendStore();
  const { theme } = useAppStore();
  const [selectedCapability, setSelectedCapability] = useState<string>('orchestrator');
  const [loading, setLoading] = useState(false);
  const [ferrariData, setFerrariData] = useState<FerrariCapability[]>([]);

  useEffect(() => {
    if (isConnected) {
      refreshAIStatus();
    }
  }, [isConnected, refreshAIStatus]);

  useEffect(() => {
    // Convert backend data to Ferrari capabilities format
    if (aiStatus?.data) {
      const capabilities: FerrariCapability[] = [
        {
          id: 'orchestrator',
          name: 'Multi-Stage Attack Orchestrator',
          description: 'AI-powered attack chain orchestration with MITRE ATT&CK integration',
          icon: Workflow,
          status: aiStatus.data.advanced_capabilities?.multi_stage_orchestrator?.available ? 'operational' : 'offline',
          confidence: aiStatus.data.advanced_capabilities?.multi_stage_orchestrator?.available ? 95 : 0,
          lastUpdate: aiStatus.data.last_updated || new Date().toISOString(),
          aiModels: aiStatus.data.core_ai_services?.active_providers || [],
          features: ['MITRE ATT&CK Matrix', 'Real-time Adaptation', 'Safety Constraints', 'Chain Templates'],
          category: 'orchestration',
          available: aiStatus.data.advanced_capabilities?.multi_stage_orchestrator?.available || false
        },
        {
          id: 'creative-exploits',
          name: 'Creative Exploit Engine',
          description: 'AI-generated novel attack vectors and polyglot payload construction',
          icon: Sparkles,
          status: aiStatus.data.advanced_capabilities?.creative_exploit_engine?.available ? 'operational' : 'offline',
          confidence: aiStatus.data.advanced_capabilities?.creative_exploit_engine?.available ? 92 : 0,
          lastUpdate: aiStatus.data.last_updated || new Date().toISOString(),
          aiModels: aiStatus.data.core_ai_services?.active_providers || [],
          features: ['Polyglot Payloads', 'Mutation Engine', 'Novelty Scoring', 'Confidence Analysis'],
          category: 'generation',
          available: aiStatus.data.advanced_capabilities?.creative_exploit_engine?.available || false
        },
        {
          id: 'behavioral-analysis',
          name: 'Behavioral Analysis Engine',
          description: 'Zero-day style behavioral pattern recognition and anomaly detection',
          icon: Eye,
          status: aiStatus.data.advanced_capabilities?.behavioral_analysis_engine?.available ? 'operational' : 'offline',
          confidence: aiStatus.data.advanced_capabilities?.behavioral_analysis_engine?.available ? 88 : 0,
          lastUpdate: aiStatus.data.last_updated || new Date().toISOString(),
          aiModels: aiStatus.data.core_ai_services?.active_providers || [],
          features: ['Anomaly Detection', 'Pattern Recognition', 'Baseline Establishment', 'Predictive Analysis'],
          category: 'analysis',
          available: aiStatus.data.advanced_capabilities?.behavioral_analysis_engine?.available || false
        },
        {
          id: 'ai-proxy',
          name: 'AI-Powered Proxy Manager',
          description: 'Intelligent proxy rotation and traffic analysis for detection avoidance',
          icon: Network,
          status: 'offline', // Not implemented yet
          confidence: 0,
          lastUpdate: aiStatus.data.last_updated || new Date().toISOString(),
          aiModels: aiStatus.data.core_ai_services?.active_providers || [],
          features: ['Intelligent Rotation', 'Traffic Analysis', 'Detection Avoidance', 'Performance Optimization'],
          category: 'proxy',
          available: false
        },
        {
          id: 'evasion-techniques',
          name: 'Evasion Techniques Generator',
          description: 'Advanced WAF, IPS, and security control bypass generation',
          icon: Shield,
          status: aiStatus.data.advanced_capabilities?.evasion_technique_generator?.available ? 'operational' : 'offline',
          confidence: aiStatus.data.advanced_capabilities?.evasion_technique_generator?.available ? 86 : 0,
          lastUpdate: aiStatus.data.last_updated || new Date().toISOString(),
          aiModels: aiStatus.data.core_ai_services?.active_providers || [],
          features: ['WAF Bypass', 'IPS Evasion', 'Signature Breaking', 'Vendor-Specific Intelligence'],
          category: 'evasion',
          available: aiStatus.data.advanced_capabilities?.evasion_technique_generator?.available || false
        },
        {
          id: 'adaptive-modifier',
          name: 'Adaptive Exploit Modifier',
          description: 'Real-time exploit adaptation based on target environment analysis',
          icon: Target,
          status: aiStatus.data.advanced_capabilities?.adaptive_exploit_modifier?.available ? 'operational' : 'offline',
          confidence: aiStatus.data.advanced_capabilities?.adaptive_exploit_modifier?.available ? 84 : 0,
          lastUpdate: aiStatus.data.last_updated || new Date().toISOString(),
          aiModels: aiStatus.data.core_ai_services?.active_providers || [],
          features: ['Environment Detection', 'Real-time Adaptation', 'Learning System', 'Effectiveness Scoring'],
          category: 'modification',
          available: aiStatus.data.advanced_capabilities?.adaptive_exploit_modifier?.available || false
        }
      ];
      setFerrariData(capabilities);
    }
  }, [aiStatus]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'operational': return 'text-green-600 bg-green-50 border-green-200';
      case 'degraded': return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'offline': return 'text-red-600 bg-red-50 border-red-200';
      default: return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'orchestration': return Workflow;
      case 'generation': return Sparkles;
      case 'analysis': return Eye;
      case 'evasion': return Shield;
      case 'modification': return Target;
      case 'proxy': return Network;
      default: return Brain;
    }
  };

  const selectedCapabilityData = ferrariData.find(cap => cap.id === selectedCapability);

  return (
    <div className="flex flex-col space-y-6 p-6">
      {/* Header Section */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className="p-3 bg-purple-100 rounded-lg">
            <Brain className="h-8 w-8 text-purple-600" />
          </div>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Ferrari AI Capabilities</h1>
            <p className="text-gray-600 mt-1">Advanced AI-powered penetration testing and security analysis</p>
          </div>
        </div>
        <div className="flex items-center space-x-3">
          <Badge variant={isConnected ? 'default' : 'destructive'} className="px-3 py-1">
            <Activity className="h-4 w-4 mr-1" />
            {isConnected ? 'Backend Online' : 'Backend Offline'}
          </Badge>
          <Button onClick={refreshAIStatus} disabled={loading} variant="outline" size="sm">
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Refresh Status
          </Button>
        </div>
      </div>

      {/* Connection Warning */}
      {!isConnected && (
        <Card className="border-yellow-200 bg-yellow-50">
          <CardContent className="pt-6">
            <div className="flex items-center space-x-3">
              <AlertTriangle className="h-5 w-5 text-yellow-600" />
              <div>
                <p className="font-medium text-yellow-800">Backend Connection Required</p>
                <p className="text-sm text-yellow-600 mt-1">
                  Connect to the backend server (161.97.99.62:8090) to access Ferrari AI capabilities.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* AI Capabilities Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {ferrariData.map((capability) => {
          const IconComponent = capability.icon;
          const CategoryIcon = getCategoryIcon(capability.category);

          return (
            <Card
              key={capability.id}
              className={`cursor-pointer transition-all hover:shadow-md ${
                selectedCapability === capability.id ? 'ring-2 ring-purple-500 shadow-md' : ''
              }`}
              onClick={() => setSelectedCapability(capability.id)}
            >
              <CardHeader className="pb-4">
                <div className="flex items-start justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="p-2 bg-purple-100 rounded-lg">
                      <IconComponent className="h-6 w-6 text-purple-600" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <CardTitle className="text-lg font-semibold truncate">
                        {capability.name}
                      </CardTitle>
                      <div className="flex items-center space-x-2 mt-1">
                        <CategoryIcon className="h-4 w-4 text-gray-500" />
                        <span className="text-sm text-gray-500 capitalize">{capability.category}</span>
                      </div>
                    </div>
                  </div>
                  <Badge className={getStatusColor(capability.status)} variant="outline">
                    {capability.status}
                  </Badge>
                </div>
                <CardDescription className="mt-2 text-sm">
                  {capability.description}
                </CardDescription>
              </CardHeader>
              <CardContent className="pt-0">
                {/* Confidence Score */}
                <div className="space-y-2 mb-4">
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-600">AI Confidence</span>
                    <span className="font-medium">{capability.confidence}%</span>
                  </div>
                  <Progress value={capability.confidence} className="h-2" />
                </div>

                {/* AI Models */}
                <div className="space-y-2 mb-4">
                  <p className="text-sm font-medium text-gray-700">AI Models</p>
                  <div className="flex flex-wrap gap-1">
                    {capability.aiModels.length > 0 ? capability.aiModels.map((model) => (
                      <Badge key={model} variant="secondary" className="text-xs">
                        {model}
                      </Badge>
                    )) : (
                      <Badge variant="outline" className="text-xs">
                        No models available
                      </Badge>
                    )}
                  </div>
                </div>

                {/* Key Features */}
                <div className="space-y-2">
                  <p className="text-sm font-medium text-gray-700">Key Features</p>
                  <div className="flex flex-wrap gap-1">
                    {capability.features.slice(0, 2).map((feature) => (
                      <Badge key={feature} variant="outline" className="text-xs">
                        {feature}
                      </Badge>
                    ))}
                    {capability.features.length > 2 && (
                      <Badge variant="outline" className="text-xs">
                        +{capability.features.length - 2} more
                      </Badge>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* AI System Status */}
      {aiStatus?.data && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Cpu className="h-5 w-5" />
              <span>AI System Status</span>
            </CardTitle>
            <CardDescription>Real-time status of AI services and models</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {aiStatus.data.core_ai_services?.active_providers?.map((provider: string) => (
                <div key={provider} className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium capitalize">{provider}</span>
                    <Badge variant={aiStatus.data.core_ai_services?.available ? 'default' : 'destructive'}>
                      {aiStatus.data.core_ai_services?.available ? 'online' : 'offline'}
                    </Badge>
                  </div>
                  <Progress value={aiStatus.data.core_ai_services?.available ? 100 : 0} className="h-2" />
                </div>
              )) || (
                <div className="col-span-3 text-center text-gray-500">
                  No AI providers available
                </div>
              )}
            </div>

            {/* Overall AI Status */}
            <div className="mt-6 pt-4 border-t">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Overall AI Status</span>
                <Badge variant={aiStatus.data.overall_status === 'online' ? 'default' : 'destructive'}>
                  {aiStatus.data.overall_status}
                </Badge>
              </div>
              <div className="mt-2 text-xs text-gray-500">
                Last updated: {aiStatus.data.last_updated ? new Date(aiStatus.data.last_updated).toLocaleString() : 'Never'}
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Selected Capability Details */}
      {selectedCapabilityData && (
        <Card className="mt-6">
          <CardHeader>
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="p-3 bg-purple-100 rounded-lg">
                  <selectedCapabilityData.icon className="h-6 w-6 text-purple-600" />
                </div>
                <div>
                  <CardTitle className="text-xl">{selectedCapabilityData.name}</CardTitle>
                  <CardDescription>{selectedCapabilityData.description}</CardDescription>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <Badge className={getStatusColor(selectedCapabilityData.status)} variant="outline">
                  {selectedCapabilityData.status}
                </Badge>
                <Badge variant="secondary">
                  {selectedCapabilityData.confidence}% confidence
                </Badge>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <h4 className="text-sm font-medium text-gray-700 mb-2">Status</h4>
                <p className="text-sm text-gray-600">
                  {selectedCapabilityData.available
                    ? `This capability is operational and ready to use with ${selectedCapabilityData.confidence}% confidence.`
                    : 'This capability is currently offline. Please check the backend connection and AI service status.'
                  }
                </p>
              </div>

              <div>
                <h4 className="text-sm font-medium text-gray-700 mb-2">Available Features</h4>
                <div className="grid grid-cols-2 gap-2">
                  {selectedCapabilityData.features.map((feature) => (
                    <div key={feature} className="flex items-center space-x-2">
                      <div className={`w-2 h-2 rounded-full ${selectedCapabilityData.available ? 'bg-green-500' : 'bg-gray-300'}`} />
                      <span className="text-sm text-gray-600">{feature}</span>
                    </div>
                  ))}
                </div>
              </div>

              {!selectedCapabilityData.available && (
                <div className="mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                  <div className="flex items-center space-x-2">
                    <AlertTriangle className="h-4 w-4 text-yellow-600" />
                    <span className="text-sm font-medium text-yellow-800">Capability Unavailable</span>
                  </div>
                  <p className="text-sm text-yellow-600 mt-1">
                    This Ferrari AI capability requires backend services to be properly configured.
                    Please ensure the AI services are running and properly initialized.
                  </p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default FerrariPage;