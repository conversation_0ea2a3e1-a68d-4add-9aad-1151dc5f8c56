import { EventEmitter } from 'events';
import log from 'electron-log';
import axios, { AxiosInstance } from 'axios';
import WebSocket from 'ws';

export interface BackendStatus {
  connected: boolean;
  url: string;
  lastPing: Date | null;
  version: string | null;
  health: 'healthy' | 'degraded' | 'unhealthy' | 'unknown';
  toolsCount: number;
  latency: number | null;
}

export interface BackendConfig {
  url: string;
  timeout: number;
  retryAttempts: number;
  retryDelay: number;
  pingInterval: number;
}

export class BackendManager extends EventEmitter {
  private config: BackendConfig;
  private apiClient: AxiosInstance;
  private websocket: WebSocket | null = null;
  private status: BackendStatus;
  private pingInterval: NodeJS.Timeout | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;

  constructor() {
    super();

    this.config = {
      url: 'http://161.97.99.62:8090',
      timeout: 30000,
      retryAttempts: 3,
      retryDelay: 2000,
      pingInterval: 30000 // 30 seconds
    };

    this.status = {
      connected: false,
      url: this.config.url,
      lastPing: null,
      version: null,
      health: 'unknown',
      toolsCount: 0,
      latency: null
    };

    this.apiClient = axios.create({
      baseURL: this.config.url,
      timeout: this.config.timeout,
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'NexusScan-Desktop/1.0.0'
      }
    });

    this.setupAxiosInterceptors();
  }

  /**
   * Initialize backend connection
   */
  async initialize(): Promise<void> {
    // Skip initialization for testing - allows app to start quickly
    if (process.env.SKIP_BACKEND_INIT === 'true' || process.env.OFFLINE_MODE === 'true') {
      log.info('Skipping backend initialization (testing mode)');
      this.status.connected = false;
      this.status.health = 'unknown';
      return;
    }

    log.info('Initializing backend connection...');
    
    try {
      await this.connect();
      this.startPingInterval();
      log.info('Backend manager initialized successfully');
    } catch (error) {
      log.error('Failed to initialize backend manager:', error);
      this.emit('connectionError', error);
    }
  }

  /**
   * Connect to backend
   */
  async connect(): Promise<boolean> {
    try {
      log.info(`Connecting to backend: ${this.config.url}`);
      
      const startTime = Date.now();
      
      // Test basic connectivity
      const healthResponse = await this.apiClient.get('/api/health');
      
      const latency = Date.now() - startTime;
      
      if (healthResponse.status === 200) {
        this.status.connected = true;
        this.status.lastPing = new Date();
        this.status.health = 'healthy';
        this.status.latency = latency;
        this.reconnectAttempts = 0;

        // Get backend version and tools count
        await this.fetchBackendInfo();

        // Setup WebSocket connection
        await this.connectWebSocket();

        this.emit('connected', this.status);
        log.info(`Backend connected successfully (latency: ${latency}ms)`);
        
        return true;
      }
    } catch (error) {
      this.status.connected = false;
      this.status.health = 'unhealthy';
      this.status.latency = null;
      
      log.error('Backend connection failed:', error);
      this.emit('connectionError', error);
    }

    return false;
  }

  /**
   * Disconnect from backend
   */
  async disconnect(): Promise<void> {
    log.info('Disconnecting from backend...');
    
    this.stopPingInterval();
    this.disconnectWebSocket();
    
    this.status.connected = false;
    this.status.health = 'unknown';
    this.status.latency = null;
    
    this.emit('disconnected');
    log.info('Backend disconnected');
  }

  /**
   * Get current backend status
   */
  getStatus(): BackendStatus {
    return { ...this.status };
  }

  /**
   * Update backend configuration
   */
  updateConfig(newConfig: Partial<BackendConfig>): void {
    this.config = { ...this.config, ...newConfig };
    
    // Update API client base URL if changed
    if (newConfig.url) {
      this.apiClient.defaults.baseURL = newConfig.url;
      this.status.url = newConfig.url;
    }
    
    // Update timeout if changed
    if (newConfig.timeout) {
      this.apiClient.defaults.timeout = newConfig.timeout;
    }
    
    log.info('Backend configuration updated:', newConfig);
  }

  /**
   * Execute API request with retry logic
   */
  async executeRequest<T>(
    method: 'GET' | 'POST' | 'PUT' | 'DELETE',
    endpoint: string,
    data?: any,
    config?: any
  ): Promise<T> {
    let lastError: any;
    
    for (let attempt = 1; attempt <= this.config.retryAttempts; attempt++) {
      try {
        const response = await this.apiClient.request({
          method,
          url: endpoint,
          data,
          ...config
        });
        
        return response.data;
      } catch (error) {
        lastError = error;
        
        if (attempt < this.config.retryAttempts) {
          log.warn(`API request failed (attempt ${attempt}/${this.config.retryAttempts}):`, error);
          await new Promise(resolve => setTimeout(resolve, this.config.retryDelay));
        }
      }
    }
    
    throw lastError;
  }

  /**
   * Get available security tools
   */
  async getAvailableTools(): Promise<any[]> {
    try {
      const response = await this.executeRequest<any>('GET', '/api/tools');
      const tools = response.tools || [];
      this.status.toolsCount = tools.length;
      return tools;
    } catch (error) {
      log.error('Failed to fetch available tools:', error);
      throw error;
    }
  }

  /**
   * Execute security tool
   */
  async executeTool(toolId: string, config: any): Promise<any> {
    try {
      return await this.executeRequest('POST', `/api/tools/${toolId}/execute`, config);
    } catch (error) {
      log.error(`Failed to execute tool ${toolId}:`, error);
      throw error;
    }
  }

  /**
   * Get Ferrari AI capabilities
   */
  async getFerrariCapabilities(): Promise<any> {
    try {
      const [orchestrator, creativeExploits, behavioralAnalysis, proxy] = await Promise.all([
        this.executeRequest('GET', '/api/orchestrator/capabilities'),
        this.executeRequest('GET', '/api/ai/creative-exploits/capabilities'),
        this.executeRequest('GET', '/api/ai/behavioral-analysis/capabilities'),
        this.executeRequest('GET', '/api/proxy/configurations')
      ]);

      return {
        orchestrator,
        creativeExploits,
        behavioralAnalysis,
        proxy
      };
    } catch (error) {
      log.error('Failed to fetch Ferrari capabilities:', error);
      throw error;
    }
  }

  /**
   * Setup WebSocket connection for real-time updates
   */
  private async connectWebSocket(): Promise<void> {
    try {
      const wsUrl = this.config.url.replace('http://', 'ws://').replace('https://', 'wss://') + '/ws';
      
      this.websocket = new WebSocket(wsUrl);
      
      this.websocket.on('open', () => {
        log.info('WebSocket connected');
        this.emit('websocketConnected');
      });
      
      this.websocket.on('message', (data) => {
        try {
          const message = JSON.parse(data.toString());
          this.emit('websocketMessage', message);
        } catch (error) {
          log.error('Failed to parse WebSocket message:', error);
        }
      });
      
      this.websocket.on('error', (error) => {
        log.error('WebSocket error:', error);
        this.emit('websocketError', error);
      });
      
      this.websocket.on('close', () => {
        log.warn('WebSocket disconnected');
        this.emit('websocketDisconnected');
        
        // Attempt to reconnect
        if (this.status.connected && this.reconnectAttempts < this.maxReconnectAttempts) {
          this.reconnectAttempts++;
          setTimeout(() => this.connectWebSocket(), this.config.retryDelay);
        }
      });
      
    } catch (error) {
      log.error('Failed to connect WebSocket:', error);
    }
  }

  /**
   * Disconnect WebSocket
   */
  private disconnectWebSocket(): void {
    if (this.websocket) {
      this.websocket.close();
      this.websocket = null;
    }
  }

  /**
   * Fetch backend information
   */
  private async fetchBackendInfo(): Promise<void> {
    try {
      // Try to get version info
      const healthData = await this.executeRequest('GET', '/api/health');
      if (healthData && (healthData as any).version) {
        this.status.version = (healthData as any).version;
      }

      // Get tools count
      const tools = await this.executeRequest('GET', '/api/tools/available');
      this.status.toolsCount = Array.isArray(tools) ? tools.length : 0;
      
    } catch (error) {
      log.warn('Failed to fetch backend info:', error);
    }
  }

  /**
   * Setup Axios interceptors for logging and error handling
   */
  private setupAxiosInterceptors(): void {
    // Request interceptor
    this.apiClient.interceptors.request.use(
      (config) => {
        log.debug(`API Request: ${config.method?.toUpperCase()} ${config.url}`);
        return config;
      },
      (error) => {
        log.error('API Request Error:', error);
        return Promise.reject(error);
      }
    );

    // Response interceptor
    this.apiClient.interceptors.response.use(
      (response) => {
        log.debug(`API Response: ${response.status} ${response.config.url}`);
        return response;
      },
      (error) => {
        log.error('API Response Error:', error.response?.status, error.response?.statusText);
        return Promise.reject(error);
      }
    );
  }

  /**
   * Start ping interval to monitor connection
   */
  private startPingInterval(): void {
    this.pingInterval = setInterval(async () => {
      try {
        const startTime = Date.now();
        await this.apiClient.get('/api/health');
        
        this.status.lastPing = new Date();
        this.status.latency = Date.now() - startTime;
        this.status.health = 'healthy';
        
        this.emit('ping', this.status);
      } catch (error) {
        this.status.health = 'unhealthy';
        this.status.latency = null;
        
        log.warn('Backend ping failed:', error);
        this.emit('pingFailed', error);
      }
    }, this.config.pingInterval);
  }

  /**
   * Stop ping interval
   */
  private stopPingInterval(): void {
    if (this.pingInterval) {
      clearInterval(this.pingInterval);
      this.pingInterval = null;
    }
  }

  /**
   * Cleanup resources
   */
  async cleanup(): Promise<void> {
    log.info('Cleaning up backend manager...');
    
    this.stopPingInterval();
    this.disconnectWebSocket();
    this.removeAllListeners();
    
    log.info('Backend manager cleanup complete');
  }
}