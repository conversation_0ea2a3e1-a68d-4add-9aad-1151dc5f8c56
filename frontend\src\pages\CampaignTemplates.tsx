import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/card';
import { Badge } from '../components/ui/badge';
import { Button } from '../components/ui/button';
import { Input } from '../components/ui/input';
import { Textarea } from '../components/ui/textarea';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '../components/ui/dialog';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../components/ui/tabs';
import { 
  Search, 
  Plus, 
  Play, 
  Clock, 
  Target, 
  Shield, 
  Globe, 
  Server, 
  Lock,
  Users,
  BookOpen,
  Star,
  Filter,
  ChevronRight,
  Settings
} from 'lucide-react';
import { AssessmentWorkflows, WorkflowTemplate } from '../services/assessment-workflows';
import { useBackendStore } from '../stores/backend-store';
import { ApiClient } from '../services/api-client';

interface CampaignTemplatesProps {
  onStartCampaign?: (templateId: string, targets: string[]) => void;
}

export const CampaignTemplates: React.FC<CampaignTemplatesProps> = ({
  onStartCampaign
}) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [selectedTemplate, setSelectedTemplate] = useState<WorkflowTemplate | null>(null);
  const [customTargets, setCustomTargets] = useState<string>('');
  const [isCreateCampaignOpen, setIsCreateCampaignOpen] = useState(false);
  
  const backendStore = useBackendStore();
  const apiClient = new ApiClient();
  const assessmentWorkflows = new AssessmentWorkflows(apiClient, backendStore);

  const templates = assessmentWorkflows.getWorkflowTemplates();

  const filteredTemplates = templates.filter(template => {
    const matchesSearch = searchQuery === '' || 
      template.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      template.description.toLowerCase().includes(searchQuery.toLowerCase());
    
    const matchesCategory = selectedCategory === 'all' || template.category === selectedCategory;
    
    return matchesSearch && matchesCategory;
  });

  const categories = Array.from(new Set(templates.map(t => t.category)));

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'reconnaissance':
        return <Search className="h-4 w-4" />;
      case 'enumeration':
        return <Target className="h-4 w-4" />;
      case 'vulnerability_scanning':
        return <Shield className="h-4 w-4" />;
      case 'exploitation':
        return <Users className="h-4 w-4" />;
      case 'post_exploitation':
        return <Lock className="h-4 w-4" />;
      default:
        return <BookOpen className="h-4 w-4" />;
    }
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'beginner':
        return 'bg-green-500';
      case 'intermediate':
        return 'bg-yellow-500';
      case 'advanced':
        return 'bg-orange-500';
      case 'expert':
        return 'bg-red-500';
      default:
        return 'bg-gray-500';
    }
  };

  const formatDuration = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    if (hours > 0) {
      return `${hours}h ${mins}m`;
    }
    return `${mins}m`;
  };

  const handleStartCampaign = () => {
    if (selectedTemplate && customTargets.trim()) {
      const targets = customTargets.split('\n').map(t => t.trim()).filter(t => t.length > 0);
      onStartCampaign?.(selectedTemplate.id, targets);
      setIsCreateCampaignOpen(false);
      setSelectedTemplate(null);
      setCustomTargets('');
    }
  };

  return (
    <div className="space-y-6 p-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold">Campaign Templates</h1>
          <p className="text-muted-foreground">Professional penetration testing workflows</p>
        </div>
        <div className="flex items-center space-x-3">
          <Badge variant="outline" className="text-sm">
            {filteredTemplates.length} templates
          </Badge>
        </div>
      </div>

      {/* Search and Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search templates..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <Filter className="h-4 w-4 text-muted-foreground" />
              <select 
                value={selectedCategory} 
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="px-3 py-2 border rounded-md bg-background"
              >
                <option value="all">All Categories</option>
                {categories.map(cat => (
                  <option key={cat} value={cat}>
                    {cat.charAt(0).toUpperCase() + cat.slice(1).replace('_', ' ')}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Templates Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredTemplates.map((template) => (
          <Card key={template.id} className="hover:shadow-lg transition-shadow cursor-pointer group">
            <CardHeader className="pb-3">
              <div className="flex items-start justify-between">
                <div className="flex items-center space-x-2">
                  {getCategoryIcon(template.category)}
                  <Badge variant="outline" className="text-xs">
                    {template.category.replace('_', ' ')}
                  </Badge>
                </div>
                <Badge className={`text-xs text-white ${getDifficultyColor(template.difficulty)}`}>
                  {template.difficulty}
                </Badge>
              </div>
              <CardTitle className="text-lg">{template.name}</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <p className="text-sm text-muted-foreground line-clamp-3">
                {template.description}
              </p>
              
              {/* Stats */}
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div className="flex items-center space-x-2">
                  <Clock className="h-3 w-3 text-muted-foreground" />
                  <span>{formatDuration(template.estimatedDuration)}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Settings className="h-3 w-3 text-muted-foreground" />
                  <span>{template.tools.length} tools</span>
                </div>
              </div>

              {/* Tools Preview */}
              <div className="space-y-2">
                <div className="text-xs font-medium text-muted-foreground">Tools:</div>
                <div className="flex flex-wrap gap-1">
                  {template.tools.slice(0, 4).map((tool, idx) => (
                    <Badge key={idx} variant="secondary" className="text-xs">
                      {tool.toolId}
                    </Badge>
                  ))}
                  {template.tools.length > 4 && (
                    <Badge variant="secondary" className="text-xs">
                      +{template.tools.length - 4} more
                    </Badge>
                  )}
                </div>
              </div>

              {/* Prerequisites */}
              {template.prerequisites.length > 0 && (
                <div className="space-y-2">
                  <div className="text-xs font-medium text-muted-foreground">Prerequisites:</div>
                  <div className="text-xs space-y-1">
                    {template.prerequisites.slice(0, 2).map((prereq, idx) => (
                      <div key={idx} className="flex items-center space-x-1">
                        <div className="w-1 h-1 bg-muted-foreground rounded-full" />
                        <span className="text-muted-foreground">{prereq}</span>
                      </div>
                    ))}
                    {template.prerequisites.length > 2 && (
                      <div className="text-muted-foreground">
                        +{template.prerequisites.length - 2} more requirements
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* Actions */}
              <div className="flex items-center justify-between pt-2">
                <Dialog>
                  <DialogTrigger asChild>
                    <Button variant="outline" size="sm" className="text-xs">
                      View Details
                    </Button>
                  </DialogTrigger>
                  <DialogContent className="max-w-2xl">
                    <DialogHeader>
                      <DialogTitle className="flex items-center space-x-2">
                        {getCategoryIcon(template.category)}
                        <span>{template.name}</span>
                      </DialogTitle>
                    </DialogHeader>
                    <div className="space-y-4">
                      <p className="text-muted-foreground">{template.description}</p>
                      
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <h4 className="font-medium mb-2">Details</h4>
                          <div className="space-y-2 text-sm">
                            <div>Category: {template.category.replace('_', ' ')}</div>
                            <div>Difficulty: {template.difficulty}</div>
                            <div>Duration: {formatDuration(template.estimatedDuration)}</div>
                            <div>Tools: {template.tools.length}</div>
                          </div>
                        </div>
                        <div>
                          <h4 className="font-medium mb-2">Requirements</h4>
                          <div className="space-y-1 text-sm">
                            {template.prerequisites.map((prereq, idx) => (
                              <div key={idx} className="flex items-center space-x-1">
                                <div className="w-1 h-1 bg-muted-foreground rounded-full" />
                                <span>{prereq}</span>
                              </div>
                            ))}
                          </div>
                        </div>
                      </div>

                      <div>
                        <h4 className="font-medium mb-2">Execution Flow</h4>
                        <div className="space-y-2">
                          {template.tools.map((tool, idx) => (
                            <div key={idx} className="flex items-center space-x-3 p-2 border rounded">
                              <div className="w-6 h-6 rounded-full bg-primary text-primary-foreground text-xs flex items-center justify-center">
                                {tool.order}
                              </div>
                              <div className="flex-1">
                                <div className="font-medium">{tool.name}</div>
                                <div className="text-xs text-muted-foreground">Tool: {tool.toolId}</div>
                              </div>
                              {tool.optional && (
                                <Badge variant="outline" className="text-xs">Optional</Badge>
                              )}
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  </DialogContent>
                </Dialog>

                <Button 
                  size="sm"
                  onClick={() => {
                    setSelectedTemplate(template);
                    setIsCreateCampaignOpen(true);
                  }}
                  className="text-xs"
                >
                  <Play className="h-3 w-3 mr-1" />
                  Start Campaign
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {filteredTemplates.length === 0 && (
        <Card className="p-8 text-center">
          <BookOpen className="h-12 w-12 mx-auto mb-4 text-muted-foreground opacity-50" />
          <h3 className="text-lg font-semibold mb-2">No templates found</h3>
          <p className="text-muted-foreground">
            No templates match your current search criteria. Try adjusting your filters.
          </p>
        </Card>
      )}

      {/* Create Campaign Dialog */}
      <Dialog open={isCreateCampaignOpen} onOpenChange={setIsCreateCampaignOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle className="flex items-center space-x-2">
              <Target className="h-5 w-5" />
              <span>Start New Campaign</span>
            </DialogTitle>
          </DialogHeader>
          
          {selectedTemplate && (
            <div className="space-y-4">
              <Card className="p-4 bg-muted/50">
                <div className="flex items-center space-x-2 mb-2">
                  {getCategoryIcon(selectedTemplate.category)}
                  <h3 className="font-semibold">{selectedTemplate.name}</h3>
                  <Badge className={`text-xs text-white ${getDifficultyColor(selectedTemplate.difficulty)}`}>
                    {selectedTemplate.difficulty}
                  </Badge>
                </div>
                <p className="text-sm text-muted-foreground">{selectedTemplate.description}</p>
                
                <div className="mt-3 grid grid-cols-3 gap-4 text-sm">
                  <div>Duration: {formatDuration(selectedTemplate.estimatedDuration)}</div>
                  <div>Tools: {selectedTemplate.tools.length}</div>
                  <div>Category: {selectedTemplate.category.replace('_', ' ')}</div>
                </div>
              </Card>

              <div className="space-y-2">
                <label className="text-sm font-medium">
                  Target Configuration
                </label>
                <Textarea
                  placeholder="Enter targets (one per line):&#10;***********&#10;example.com&#10;https://webapp.example.com"
                  value={customTargets}
                  onChange={(e) => setCustomTargets(e.target.value)}
                  rows={6}
                  className="font-mono text-sm"
                />
                <div className="text-xs text-muted-foreground">
                  Enter IP addresses, domain names, or URLs. One target per line.
                </div>
              </div>

              {selectedTemplate.prerequisites.length > 0 && (
                <div className="space-y-2">
                  <label className="text-sm font-medium">Prerequisites Checklist</label>
                  <div className="space-y-2 p-3 border rounded-md bg-muted/30">
                    {selectedTemplate.prerequisites.map((prereq, idx) => (
                      <div key={idx} className="flex items-center space-x-2">
                        <input type="checkbox" className="rounded" />
                        <span className="text-sm">{prereq}</span>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              <div className="flex justify-end space-x-3">
                <Button 
                  variant="outline" 
                  onClick={() => setIsCreateCampaignOpen(false)}
                >
                  Cancel
                </Button>
                <Button 
                  onClick={handleStartCampaign}
                  disabled={!customTargets.trim()}
                >
                  <Play className="h-4 w-4 mr-2" />
                  Start Campaign
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};