#!/usr/bin/env python3
"""
Full System Integration Test for NexusScan Desktop
Tests complete backend-frontend integration workflow
"""

import os
import sys
import time
import subprocess
import requests
import json
import signal
import psutil

class Colors:
    GREEN = '\033[92m'
    YELLOW = '\033[93m'
    RED = '\033[91m'
    BLUE = '\033[94m'
    CYAN = '\033[96m'
    ENDC = '\033[0m'
    BOLD = '\033[1m'

def print_status(message, status="INFO", color=Colors.BLUE):
    """Print colored status message"""
    timestamp = time.strftime("%H:%M:%S")
    print(f"{color}[{timestamp} {status}]{Colors.ENDC} {message}")

def cleanup_processes():
    """Clean up any existing backend processes"""
    print_status("Cleaning up existing processes...", "CLEANUP", Colors.YELLOW)
    
    # Kill processes on ports
    for port in [8000, 8001, 8002, 1420]:
        try:
            result = subprocess.run(
                f"lsof -ti:{port}", 
                shell=True, 
                capture_output=True, 
                text=True
            )
            if result.stdout.strip():
                pids = result.stdout.strip().split('\n')
                for pid in pids:
                    try:
                        subprocess.run(f"kill -9 {pid}", shell=True)
                        print_status(f"Killed process {pid} on port {port}", "KILLED", Colors.YELLOW)
                    except:
                        pass
        except:
            pass
    
    # Kill Python processes running main.py
    for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
        try:
            if proc.info['name'] == 'python' or proc.info['name'] == 'python3':
                cmdline = proc.info.get('cmdline', [])
                if any('main.py' in arg for arg in cmdline):
                    proc.terminate()
                    proc.wait(timeout=5)
                    print_status(f"Terminated Python process {proc.info['pid']}", "KILLED", Colors.YELLOW)
        except:
            pass
    
    time.sleep(2)

def start_backend():
    """Start the backend server"""
    print_status("Starting backend server...", "BACKEND", Colors.CYAN)
    
    # Activate virtual environment and start backend
    env = os.environ.copy()
    
    backend_proc = subprocess.Popen(
        ['bash', '-c', 'source venv/bin/activate && python src/main.py'],
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        text=True,
        env=env,
        preexec_fn=os.setsid
    )
    
    # Wait for backend to be ready
    start_time = time.time()
    backend_ready = False
    
    while time.time() - start_time < 60:
        # Check process is still running
        if backend_proc.poll() is not None:
            stdout, stderr = backend_proc.communicate()
            print_status("Backend process died!", "ERROR", Colors.RED)
            print(f"STDOUT:\n{stdout}")
            print(f"STDERR:\n{stderr}")
            return None, False
        
        # Check for ready message in output
        try:
            # Check stderr
            line = backend_proc.stderr.readline()
            if line:
                print(f"  Backend: {line.strip()}")
                if "Backend services are ready" in line:
                    backend_ready = True
                    break
            
            # Also check stdout
            line = backend_proc.stdout.readline()
            if line:
                print(f"  Backend: {line.strip()}")
                if "Backend services are ready" in line:
                    backend_ready = True
                    break
        except:
            pass
        
        # Check if API is responding
        try:
            response = requests.get('http://localhost:8000/api/health', timeout=1)
            if response.status_code == 200:
                backend_ready = True
                break
        except:
            pass
        
        time.sleep(1)
    
    if backend_ready:
        print_status("Backend is ready!", "SUCCESS", Colors.GREEN)
        return backend_proc, True
    else:
        print_status("Backend failed to start in time", "ERROR", Colors.RED)
        try:
            os.killpg(os.getpgid(backend_proc.pid), signal.SIGTERM)
        except:
            pass
        return None, False

def test_backend_api():
    """Test backend API endpoints"""
    print_status("Testing backend API endpoints...", "API TEST", Colors.CYAN)
    
    endpoints = [
        ("GET", "/api/health", None),
        ("GET", "/api/campaigns", None),
        ("GET", "/api/scans", None),
        ("GET", "/api/vulnerabilities", None),
        ("GET", "/api/ai/providers", None),
        ("GET", "/api/tools", None),
    ]
    
    all_passed = True
    
    for method, endpoint, data in endpoints:
        try:
            url = f"http://localhost:8000{endpoint}"
            if method == "GET":
                response = requests.get(url, timeout=5)
            else:
                response = requests.post(url, json=data, timeout=5)
            
            if response.status_code in [200, 201]:
                print_status(f"✓ {method} {endpoint} - {response.status_code}", "PASS", Colors.GREEN)
            else:
                print_status(f"✗ {method} {endpoint} - {response.status_code}", "FAIL", Colors.RED)
                all_passed = False
        except Exception as e:
            print_status(f"✗ {method} {endpoint} - {str(e)}", "ERROR", Colors.RED)
            all_passed = False
    
    return all_passed

def start_frontend():
    """Start the frontend development server"""
    print_status("Starting frontend development server...", "FRONTEND", Colors.CYAN)
    
    # Change to frontend directory
    os.chdir('frontend')
    
    frontend_proc = subprocess.Popen(
        ['npm', 'run', 'dev'],
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        text=True,
        preexec_fn=os.setsid
    )
    
    # Wait for frontend to be ready
    start_time = time.time()
    frontend_ready = False
    
    while time.time() - start_time < 30:
        # Check process is still running
        if frontend_proc.poll() is not None:
            stdout, stderr = frontend_proc.communicate()
            print_status("Frontend process died!", "ERROR", Colors.RED)
            print(f"STDOUT:\n{stdout}")
            print(f"STDERR:\n{stderr}")
            return None, False
        
        # Check for ready message
        try:
            line = frontend_proc.stdout.readline()
            if line:
                print(f"  Frontend: {line.strip()}")
                if "ready in" in line.lower() or "Local:" in line:
                    frontend_ready = True
                    break
        except:
            pass
        
        # Check if frontend is responding
        try:
            response = requests.get('http://localhost:1420', timeout=1)
            if response.status_code == 200:
                frontend_ready = True
                break
        except:
            pass
        
        time.sleep(1)
    
    # Change back to root directory
    os.chdir('..')
    
    if frontend_ready:
        print_status("Frontend is ready!", "SUCCESS", Colors.GREEN)
        return frontend_proc, True
    else:
        print_status("Frontend failed to start in time", "ERROR", Colors.RED)
        try:
            os.killpg(os.getpgid(frontend_proc.pid), signal.SIGTERM)
        except:
            pass
        return None, False

def test_frontend_backend_integration():
    """Test frontend-backend integration"""
    print_status("Testing frontend-backend integration...", "INTEGRATION", Colors.CYAN)
    
    # Test if frontend can reach backend
    try:
        # This would normally test through the frontend proxy
        # For now, we'll just verify both are running
        frontend_response = requests.get('http://localhost:1420', timeout=5)
        backend_response = requests.get('http://localhost:8000/api/health', timeout=5)
        
        if frontend_response.status_code == 200 and backend_response.status_code == 200:
            print_status("✓ Frontend and backend are both accessible", "PASS", Colors.GREEN)
            return True
        else:
            print_status("✗ Frontend or backend not accessible", "FAIL", Colors.RED)
            return False
    except Exception as e:
        print_status(f"✗ Integration test failed: {str(e)}", "ERROR", Colors.RED)
        return False

def main():
    """Main test execution"""
    print_status("Starting Full System Integration Test", "START", Colors.BOLD + Colors.CYAN)
    print_status("=" * 60, "", Colors.CYAN)
    
    # Cleanup existing processes
    cleanup_processes()
    
    backend_proc = None
    frontend_proc = None
    success = True
    
    try:
        # Start backend
        backend_proc, backend_success = start_backend()
        if not backend_success:
            success = False
            return
        
        # Test backend API
        api_success = test_backend_api()
        if not api_success:
            success = False
        
        # Start frontend
        frontend_proc, frontend_success = start_frontend()
        if not frontend_success:
            success = False
            return
        
        # Test integration
        integration_success = test_frontend_backend_integration()
        if not integration_success:
            success = False
        
        # Summary
        print_status("=" * 60, "", Colors.CYAN)
        if success:
            print_status("All tests passed! System integration successful.", "SUCCESS", Colors.BOLD + Colors.GREEN)
        else:
            print_status("Some tests failed. Check the output above.", "FAILED", Colors.BOLD + Colors.RED)
        
        # Keep running for manual testing
        if success:
            print_status("\nSystem is running. Press Ctrl+C to stop.", "INFO", Colors.CYAN)
            print_status(f"Frontend: http://localhost:1420", "URL", Colors.BLUE)
            print_status(f"Backend API: http://localhost:8000/api/", "URL", Colors.BLUE)
            print_status(f"Metrics: http://localhost:8001/metrics", "URL", Colors.BLUE)
            
            while True:
                time.sleep(1)
        
    except KeyboardInterrupt:
        print_status("\nShutting down...", "SHUTDOWN", Colors.YELLOW)
    finally:
        # Cleanup
        if backend_proc:
            try:
                os.killpg(os.getpgid(backend_proc.pid), signal.SIGTERM)
                print_status("Backend stopped", "STOPPED", Colors.YELLOW)
            except:
                pass
        
        if frontend_proc:
            try:
                os.killpg(os.getpgid(frontend_proc.pid), signal.SIGTERM)
                print_status("Frontend stopped", "STOPPED", Colors.YELLOW)
            except:
                pass
        
        # Final cleanup
        cleanup_processes()

if __name__ == "__main__":
    main()