/**
 * Desktop Application Header Component
 * Provides navigation, status indicators, and global actions
 */
import React from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Menu,
  X,
  Terminal as TerminalIcon,
  Wifi,
  WifiOff,
  AlertTriangle,
  CheckCircle,
  Settings,
  HelpCircle,
  ExternalLink
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useAppStore } from '@/stores/app-store';
import { useBackendStore } from '@/stores/backend-store';
import { electronService } from '@/services/electron-service';
import { MobileNav } from './MobileNav';

/**
 * Header component props
 */
interface HeaderProps {
  className?: string;
}

/**
 * Main header component for the desktop application
 */
export function Header({ className }: HeaderProps) {
  const location = useLocation();
  const navigate = useNavigate();
  
  // App state
  const {
    sidebarOpen,
    terminalOpen,
    toggleSidebar,
    toggleTerminal,
    theme,
    toggleTheme
  } = useAppStore();
  
  // Backend state (remote mode)
  const { status: backendStatus } = useBackendStore();
  
  // Navigation items for breadcrumb
  const getPageTitle = () => {
    const path = location.pathname;
    
    if (path.startsWith('/ferrari')) {
      if (path.includes('orchestrator')) return 'Ferrari AI - Orchestrator';
      if (path.includes('creative-exploits')) return 'Ferrari AI - Creative Exploits';
      if (path.includes('behavioral-analysis')) return 'Ferrari AI - Behavioral Analysis';
      if (path.includes('ai-proxy')) return 'Ferrari AI - AI Proxy';
      return 'Ferrari AI Dashboard';
    }
    
    if (path.startsWith('/tools')) {
      if (path.includes('network-scanning')) return 'Tools - Network Scanning';
      if (path.includes('web-testing')) return 'Tools - Web Testing';
      if (path.includes('vulnerability-assessment')) return 'Tools - Vulnerability Assessment';
      if (path.includes('password-tools')) return 'Tools - Password Tools';
      if (path.includes('ssl-testing')) return 'Tools - SSL Testing';
      if (path.includes('exploitation')) return 'Tools - Exploitation';
      return 'Security Tools';
    }
    
    switch (path) {
      case '/dashboard': return 'Dashboard';
      case '/campaigns': return 'Campaigns';
      case '/reports': return 'Reports';
      case '/settings': return 'Settings';
      default: return 'NexusScan Desktop';
    }
  };
  
  const handleExternalLink = (url: string) => {
    window.open(url, '_blank', 'noopener,noreferrer');
  };
  
  const getConnectionStatus = () => {
    if (backendStatus.connected) {
      return {
        icon: <CheckCircle className="h-4 w-4" />,
        text: 'Connected',
        variant: 'default' as const,
        className: 'status-online'
      };
    } else {
      return {
        icon: <WifiOff className="h-4 w-4" />,
        text: 'Offline',
        variant: 'destructive' as const,
        className: 'status-offline'
      };
    }
  };
  
  const getRemoteBackendStatus = () => {
    return {
      icon: <CheckCircle className="h-4 w-4" />,
      text: 'Remote Backend',
      variant: 'default' as const,
      className: 'status-remote'
    };
  };

  const connectionStatus = getConnectionStatus();
  const remoteBackendInfo = getRemoteBackendStatus();
  
  return (
    <header className={cn(
      'flex items-center justify-between h-14 px-4 border-b border-border bg-background/95 backdrop-blur-sm',
      'supports-[backdrop-filter]:bg-background/60',
      className
    )}>
      {/* Left section */}
      <div className="flex items-center gap-4">
        {/* Mobile navigation (visible on mobile only) */}
        <MobileNav className="md:hidden" />

        {/* Desktop sidebar toggle (hidden on mobile) */}
        <Button
          variant="ghost"
          size="sm"
          onClick={() => {
            console.log('🔘 Sidebar toggle clicked!');
            toggleSidebar();
          }}
          className="p-2 hidden md:flex"
          title={sidebarOpen ? 'Hide sidebar' : 'Show sidebar'}
        >
          {sidebarOpen ? (
            <X className="h-4 w-4" />
          ) : (
            <Menu className="h-4 w-4" />
          )}
        </Button>
        
        {/* Page title */}
        <div className="flex items-center gap-2">
          <h1 className="text-lg font-semibold text-foreground">
            {getPageTitle()}
          </h1>
        </div>
      </div>
      
      {/* Center section - Connection status */}
      <div className="flex items-center gap-3">
        {/* Backend connection status */}
        <Badge
          variant={connectionStatus.variant}
          className={cn('flex items-center gap-1.5', connectionStatus.className)}
        >
          {connectionStatus.icon}
          <span className="text-xs">{connectionStatus.text}</span>
        </Badge>
        
        {/* Remote backend mode indicator */}
        <Badge variant="default" className="flex items-center gap-1.5 bg-blue-500 hover:bg-blue-600">
          {remoteBackendInfo.icon}
          <span className="text-xs">{remoteBackendInfo.text}</span>
        </Badge>
        
        {/* Tools count */}
        {backendStatus.connected && backendStatus.tools && (
          <Badge variant="outline" className="flex items-center gap-1.5">
            <span className="text-xs">
              {backendStatus.tools.operational}/{backendStatus.tools.total} Tools
            </span>
          </Badge>
        )}
      </div>
      
      {/* Right section */}
      <div className="flex items-center gap-2">
        {/* Terminal toggle */}
        <Button
          variant="ghost"
          size="sm"
          onClick={() => {
            console.log('🔘 Terminal toggle clicked!');
            toggleTerminal();
          }}
          className={cn(
            'p-2',
            terminalOpen && 'bg-accent text-accent-foreground'
          )}
          title={terminalOpen ? 'Hide terminal' : 'Show terminal'}
        >
          <TerminalIcon className="h-4 w-4" />
        </Button>
        
        {/* Settings */}
        <Button
          variant="ghost"
          size="sm"
          onClick={() => navigate('/settings')}
          className="p-2"
          title="Settings"
        >
          <Settings className="h-4 w-4" />
        </Button>
        
        {/* Help */}
        <Button
          variant="ghost"
          size="sm"
          onClick={() => {
            // TODO: Show help dialog or navigate to help page
            console.log('Help clicked');
          }}
          className="p-2"
          title="Help"
        >
          <HelpCircle className="h-4 w-4" />
        </Button>
        
        {/* Web actions */}
        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleExternalLink('https://docs.nexusscan.com')}
            className="p-2"
            title="Documentation"
          >
            <ExternalLink className="h-4 w-4" />
          </Button>

          <Button
            variant="ghost"
            size="sm"
            onClick={() => navigate('/settings')}
            className="p-2"
            title="Settings"
          >
            <Settings className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </header>
  );
}