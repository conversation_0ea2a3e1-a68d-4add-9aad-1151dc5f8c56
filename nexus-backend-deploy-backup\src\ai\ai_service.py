#!/usr/bin/env python3
"""
AI Service Core for NexusScan Desktop Application
Provides centralized AI capabilities for vulnerability assessment, threat analysis, and intelligent recommendations
"""

import asyncio
import logging
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union, Callable
from dataclasses import dataclass, field
from enum import Enum
from abc import ABC, abstractmethod
import hashlib

logger = logging.getLogger(__name__)


# Compatibility layer for different vulnerability models
@dataclass
class VulnerabilityAdapter:
    """Adapter to normalize different vulnerability data structures"""
    id: Union[int, str]
    vuln_type: str
    severity: str
    title: str
    description: str
    target: str
    evidence: Optional[str] = None
    remediation: Optional[str] = None
    cvss_score: Optional[float] = None
    cve_id: Optional[str] = None
    
    @classmethod
    def from_orm_model(cls, vuln_model):
        """Create adapter from SQLAlchemy ORM model"""
        return cls(
            id=vuln_model.id,
            vuln_type=getattr(vuln_model, 'vuln_type', getattr(vuln_model, 'type', 'unknown')),
            severity=vuln_model.severity,
            title=vuln_model.title,
            description=vuln_model.description or '',
            target=getattr(vuln_model, 'affected_url', getattr(vuln_model, 'target', '')),
            evidence=getattr(vuln_model, 'evidence', None),
            remediation=getattr(vuln_model, 'remediation', None),
            cvss_score=getattr(vuln_model, 'cvss_score', None),
            cve_id=getattr(vuln_model, 'cve_id', None)
        )
    
    @classmethod
    def from_dict(cls, vuln_dict):
        """Create adapter from dictionary"""
        return cls(
            id=vuln_dict.get('id', ''),
            vuln_type=vuln_dict.get('vuln_type', vuln_dict.get('type', 'unknown')),
            severity=vuln_dict.get('severity', 'medium'),
            title=vuln_dict.get('title', ''),
            description=vuln_dict.get('description', ''),
            target=vuln_dict.get('target', vuln_dict.get('affected_url', '')),
            evidence=vuln_dict.get('evidence'),
            remediation=vuln_dict.get('remediation'),
            cvss_score=vuln_dict.get('cvss_score'),
            cve_id=vuln_dict.get('cve_id')
        )
    
    @classmethod  
    def from_any(cls, vulnerability):
        """Create adapter from any vulnerability type"""
        if vulnerability is None:
            return None
        
        if hasattr(vulnerability, '__dict__'):
            # It's an object (ORM model or dataclass)
            return cls.from_orm_model(vulnerability)
        elif isinstance(vulnerability, dict):
            # It's a dictionary
            return cls.from_dict(vulnerability)
        else:
            logger.warning(f"Unknown vulnerability type: {type(vulnerability)}")
            return None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert adapter to dictionary for UI compatibility"""
        return {
            'id': self.id,
            'vuln_type': self.vuln_type,
            'type': self.vuln_type,  # Alternative field name
            'severity': self.severity,
            'title': self.title,
            'description': self.description,
            'target': self.target,
            'affected_url': self.target,  # Alternative field name
            'evidence': self.evidence,
            'remediation': self.remediation,
            'cvss_score': self.cvss_score,
            'cve_id': self.cve_id
        }


class AICapability(Enum):
    """AI service capabilities"""
    VULNERABILITY_ASSESSMENT = "vulnerability_assessment"
    THREAT_ANALYSIS = "threat_analysis"
    SCAN_RECOMMENDATION = "scan_recommendation"
    REMEDIATION_SUGGESTION = "remediation_suggestion"
    RISK_SCORING = "risk_scoring"
    ATTACK_PATH_ANALYSIS = "attack_path_analysis"
    COMPLIANCE_ANALYSIS = "compliance_analysis"
    THREAT_INTELLIGENCE = "threat_intelligence"
    REPORT_GENERATION = "report_generation"
    BEHAVIORAL_ANALYSIS = "behavioral_analysis"


class AIModelType(Enum):
    """Types of AI models"""
    LANGUAGE_MODEL = "language_model"
    CLASSIFICATION_MODEL = "classification_model"
    CLUSTERING_MODEL = "clustering_model"
    REGRESSION_MODEL = "regression_model"
    CUSTOM_MODEL = "custom_model"


class AnalysisType(Enum):
    """Types of analysis performed by AI"""
    VULNERABILITY_ANALYSIS = "vulnerability_analysis"
    THREAT_ASSESSMENT = "threat_assessment"
    RISK_EVALUATION = "risk_evaluation"
    IMPACT_ANALYSIS = "impact_analysis"
    EXPLOIT_PREDICTION = "exploit_prediction"
    BEHAVIORAL_PATTERN = "behavioral_pattern"


@dataclass
class AIServiceConfig:
    """Configuration for AI services"""
    service_name: str
    model_type: AIModelType
    capabilities: List[AICapability]
    api_endpoint: Optional[str] = None
    api_key: Optional[str] = None
    model_version: str = "latest"
    max_tokens: int = 4096
    temperature: float = 0.1
    timeout: int = 30
    rate_limit: int = 60  # requests per minute
    priority: int = 1
    enabled: bool = True
    custom_config: Dict[str, Any] = field(default_factory=dict)


@dataclass
class AIAnalysisRequest:
    """Request for AI analysis"""
    analysis_type: AnalysisType
    capability: AICapability
    context: Dict[str, Any]
    data: Any
    target_id: str = ""
    priority: int = 1
    metadata: Dict[str, Any] = field(default_factory=dict)
    timeout: Optional[int] = None
    request_id: str = field(default_factory=lambda: hashlib.md5(str(datetime.now()).encode()).hexdigest()[:16])


@dataclass
class AIAnalysisResult:
    """Result from AI analysis"""
    request_id: str
    analysis_type: AnalysisType
    capability: AICapability
    success: bool
    confidence: float
    result: Dict[str, Any]
    recommendations: List[str] = field(default_factory=list)
    insights: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)
    processing_time: float = 0.0
    model_used: str = ""
    error_message: str = ""
    created_at: datetime = field(default_factory=datetime.now)


@dataclass
class VulnerabilityContext:
    """Context for vulnerability analysis"""
    vulnerability_id: str
    cve_id: str = ""
    severity: str = "unknown"
    affected_service: str = ""
    target_host: str = ""
    port: Optional[int] = None
    evidence: str = ""
    tool_output: str = ""
    scan_results: Dict[str, Any] = field(default_factory=dict)
    network_context: Dict[str, Any] = field(default_factory=dict)
    historical_data: List[Dict[str, Any]] = field(default_factory=list)


@dataclass
class ThreatContext:
    """Context for threat analysis"""
    threat_indicators: List[str]
    attack_vectors: List[str] = field(default_factory=list)
    affected_assets: List[str] = field(default_factory=list)
    network_topology: Dict[str, Any] = field(default_factory=dict)
    security_controls: List[str] = field(default_factory=list)
    threat_landscape: Dict[str, Any] = field(default_factory=dict)
    historical_attacks: List[Dict[str, Any]] = field(default_factory=list)


def create_ai_config_from_regular_config(config, service_name: str, capabilities: List[AICapability]) -> AIServiceConfig:
    """Convert a regular Config object to AIServiceConfig"""
    if hasattr(config, 'service_name'):
        return config
    else:
        return AIServiceConfig(
            service_name=service_name,
            model_type=AIModelType.LANGUAGE_MODEL,
            capabilities=capabilities
        )


class AIServiceProvider(ABC):
    """Abstract base class for AI service providers"""

    def __init__(self, config):
        # Convert regular Config to AIServiceConfig if needed
        if not hasattr(config, 'service_name'):
            self.config = AIServiceConfig(
                service_name="base_ai_service",
                model_type=AIModelType.LANGUAGE_MODEL,
                capabilities=[]
            )
        else:
            self.config = config
        self.logger = logging.getLogger(f"{__name__}.{self.config.service_name}")
        self.stats = {
            "requests_processed": 0,
            "successful_analyses": 0,
            "failed_analyses": 0,
            "total_processing_time": 0.0,
            "average_processing_time": 0.0,
            "last_request_time": None
        }

    @abstractmethod
    async def analyze(self, request: AIAnalysisRequest) -> AIAnalysisResult:
        """Perform AI analysis"""
        pass

    @abstractmethod
    async def is_available(self) -> bool:
        """Check if service is available"""
        pass

    @abstractmethod
    async def health_check(self) -> Dict[str, Any]:
        """Perform health check"""
        pass

    def supports_capability(self, capability: AICapability) -> bool:
        """Check if provider supports a capability"""
        return capability in self.config.capabilities

    def get_statistics(self) -> Dict[str, Any]:
        """Get service statistics"""
        return self.stats.copy()

    def _update_statistics(self, result: AIAnalysisResult):
        """Update service statistics"""
        self.stats["requests_processed"] += 1
        self.stats["last_request_time"] = datetime.now()
        
        if result.success:
            self.stats["successful_analyses"] += 1
        else:
            self.stats["failed_analyses"] += 1
        
        self.stats["total_processing_time"] += result.processing_time
        self.stats["average_processing_time"] = (
            self.stats["total_processing_time"] / self.stats["requests_processed"]
        )


class AIServiceManager:
    """Central manager for AI services"""

    def __init__(self, config=None):
        self.providers: Dict[str, AIServiceProvider] = {}
        self.capability_map: Dict[AICapability, List[str]] = {}
        self.request_queue: asyncio.Queue = asyncio.Queue()
        self.processing = False
        self.logger = logging.getLogger(__name__)
        
        # Rate limiting
        self.rate_limiters: Dict[str, Dict[str, Any]] = {}
        
        # Caching
        self.analysis_cache: Dict[str, AIAnalysisResult] = {}
        self.cache_ttl = timedelta(hours=1)
        
        # Statistics
        self.stats = {
            "total_requests": 0,
            "cache_hits": 0,
            "cache_misses": 0,
            "providers_registered": 0,
            "capabilities_available": 0
        }

    def register_provider(self, provider: AIServiceProvider):
        """Register an AI service provider
        
        Args:
            provider: AI service provider to register
        """
        provider_name = provider.config.service_name
        self.providers[provider_name] = provider
        
        # Update capability mapping
        for capability in provider.config.capabilities:
            if capability not in self.capability_map:
                self.capability_map[capability] = []
            self.capability_map[capability].append(provider_name)
        
        # Initialize rate limiter
        self.rate_limiters[provider_name] = {
            "requests": [],
            "limit": provider.config.rate_limit
        }
        
        self.stats["providers_registered"] += 1
        self.stats["capabilities_available"] = len(self.capability_map)
        self.logger.info(f"Registered AI provider: {provider_name}")

    def unregister_provider(self, provider_name: str):
        """Unregister an AI service provider
        
        Args:
            provider_name: Name of provider to unregister
        """
        if provider_name in self.providers:
            provider = self.providers[provider_name]
            
            # Remove from capability mapping
            for capability in provider.config.capabilities:
                if capability in self.capability_map:
                    self.capability_map[capability].remove(provider_name)
                    if not self.capability_map[capability]:
                        del self.capability_map[capability]
            
            # Cleanup
            del self.providers[provider_name]
            if provider_name in self.rate_limiters:
                del self.rate_limiters[provider_name]
            
            self.stats["providers_registered"] -= 1
            self.stats["capabilities_available"] = len(self.capability_map)
            self.logger.info(f"Unregistered AI provider: {provider_name}")

    async def analyze(self, request: AIAnalysisRequest) -> AIAnalysisResult:
        """Request AI analysis
        
        Args:
            request: Analysis request
            
        Returns:
            Analysis result
            
        Raises:
            ValueError: If no provider supports the capability
            RuntimeError: If analysis fails
        """
        self.stats["total_requests"] += 1
        
        # Check cache first
        cache_key = self._generate_cache_key(request)
        cached_result = self._get_cached_result(cache_key)
        if cached_result:
            self.stats["cache_hits"] += 1
            return cached_result
        
        self.stats["cache_misses"] += 1
        
        # Find suitable provider
        provider = await self._select_provider(request.capability)
        if not provider:
            raise ValueError(f"No provider available for capability: {request.capability}")
        
        # Check rate limiting
        if not await self._check_rate_limit(provider.config.service_name):
            raise RuntimeError("Rate limit exceeded for provider")
        
        try:
            # Perform analysis
            result = await provider.analyze(request)
            
            # Cache successful results
            if result.success:
                self._cache_result(cache_key, result)
            
            return result
            
        except Exception as e:
            self.logger.error(f"Analysis failed with provider {provider.config.service_name}: {e}")
            
            # Try fallback provider
            fallback_provider = await self._select_fallback_provider(request.capability, provider.config.service_name)
            if fallback_provider:
                try:
                    result = await fallback_provider.analyze(request)
                    if result.success:
                        self._cache_result(cache_key, result)
                    return result
                except Exception as fallback_error:
                    self.logger.error(f"Fallback analysis also failed: {fallback_error}")
            
            # Return error result
            return AIAnalysisResult(
                request_id=request.request_id,
                analysis_type=request.analysis_type,
                capability=request.capability,
                success=False,
                confidence=0.0,
                result={},
                error_message=str(e)
            )

    async def analyze_vulnerability(self, context: VulnerabilityContext) -> AIAnalysisResult:
        """Analyze vulnerability with AI
        
        Args:
            context: Vulnerability context
            
        Returns:
            Analysis result
        """
        request = AIAnalysisRequest(
            analysis_type=AnalysisType.VULNERABILITY_ANALYSIS,
            capability=AICapability.VULNERABILITY_ASSESSMENT,
            context={"vulnerability": context.__dict__},
            data=context,
            target_id=context.vulnerability_id
        )
        
        return await self.analyze(request)

    async def analyze_threat(self, context: ThreatContext) -> AIAnalysisResult:
        """Analyze threat with AI
        
        Args:
            context: Threat context
            
        Returns:
            Analysis result
        """
        request = AIAnalysisRequest(
            analysis_type=AnalysisType.THREAT_ASSESSMENT,
            capability=AICapability.THREAT_ANALYSIS,
            context={"threat": context.__dict__},
            data=context,
            target_id=f"threat_{datetime.now().isoformat()}"
        )
        
        return await self.analyze(request)

    async def get_scan_recommendations(self, target_context: Dict[str, Any]) -> AIAnalysisResult:
        """Get scan recommendations from AI
        
        Args:
            target_context: Target context information
            
        Returns:
            Analysis result with recommendations
        """
        request = AIAnalysisRequest(
            analysis_type=AnalysisType.RISK_EVALUATION,
            capability=AICapability.SCAN_RECOMMENDATION,
            context=target_context,
            data=target_context,
            target_id=target_context.get("target_id", "unknown")
        )
        
        return await self.analyze(request)

    async def get_remediation_suggestions(self, vulnerability_context: VulnerabilityContext) -> AIAnalysisResult:
        """Get remediation suggestions from AI
        
        Args:
            vulnerability_context: Vulnerability context
            
        Returns:
            Analysis result with remediation suggestions
        """
        request = AIAnalysisRequest(
            analysis_type=AnalysisType.IMPACT_ANALYSIS,
            capability=AICapability.REMEDIATION_SUGGESTION,
            context={"vulnerability": vulnerability_context.__dict__},
            data=vulnerability_context,
            target_id=vulnerability_context.vulnerability_id
        )
        
        return await self.analyze(request)

    def get_available_capabilities(self) -> List[AICapability]:
        """Get list of available AI capabilities
        
        Returns:
            List of available capabilities
        """
        return list(self.capability_map.keys())

    def get_providers_for_capability(self, capability: AICapability) -> List[str]:
        """Get providers that support a capability
        
        Args:
            capability: AI capability
            
        Returns:
            List of provider names
        """
        return self.capability_map.get(capability, [])

    async def health_check_all(self) -> Dict[str, Dict[str, Any]]:
        """Perform health check on all providers
        
        Returns:
            Health status for all providers
        """
        health_results = {}
        
        for provider_name, provider in self.providers.items():
            try:
                health_results[provider_name] = await provider.health_check()
            except Exception as e:
                health_results[provider_name] = {
                    "status": "unhealthy",
                    "error": str(e)
                }
        
        return health_results

    def get_service_statistics(self) -> Dict[str, Any]:
        """Get comprehensive service statistics
        
        Returns:
            Service statistics
        """
        provider_stats = {}
        for name, provider in self.providers.items():
            provider_stats[name] = provider.get_statistics()
        
        return {
            "global_stats": self.stats,
            "provider_stats": provider_stats,
            "cache_size": len(self.analysis_cache),
            "available_capabilities": len(self.capability_map),
            "total_providers": len(self.providers)
        }

    # Private methods

    async def _select_provider(self, capability: AICapability) -> Optional[AIServiceProvider]:
        """Select best provider for capability"""
        providers = self.capability_map.get(capability, [])
        if not providers:
            return None
        
        # Filter enabled and available providers
        available_providers = []
        for provider_name in providers:
            provider = self.providers[provider_name]
            if provider.config.enabled and await provider.is_available():
                available_providers.append((provider_name, provider))
        
        if not available_providers:
            return None
        
        # Sort by priority (higher priority first)
        available_providers.sort(key=lambda x: x[1].config.priority, reverse=True)
        
        return available_providers[0][1]

    async def _select_fallback_provider(self, capability: AICapability, exclude_provider: str) -> Optional[AIServiceProvider]:
        """Select fallback provider excluding the failed one"""
        providers = self.capability_map.get(capability, [])
        providers = [p for p in providers if p != exclude_provider]
        
        if not providers:
            return None
        
        for provider_name in providers:
            provider = self.providers[provider_name]
            if provider.config.enabled and await provider.is_available():
                return provider
        
        return None

    async def _check_rate_limit(self, provider_name: str) -> bool:
        """Check if provider is within rate limits"""
        if provider_name not in self.rate_limiters:
            return True
        
        rate_data = self.rate_limiters[provider_name]
        now = datetime.now()
        
        # Remove old requests (older than 1 minute)
        rate_data["requests"] = [
            req_time for req_time in rate_data["requests"]
            if now - req_time < timedelta(minutes=1)
        ]
        
        # Check if under limit
        if len(rate_data["requests"]) < rate_data["limit"]:
            rate_data["requests"].append(now)
            return True
        
        return False

    def _generate_cache_key(self, request: AIAnalysisRequest) -> str:
        """Generate cache key for request"""
        content = f"{request.analysis_type.value}_{request.capability.value}_{hash(str(request.context))}"
        return hashlib.md5(content.encode()).hexdigest()

    def _get_cached_result(self, cache_key: str) -> Optional[AIAnalysisResult]:
        """Get cached result if valid"""
        if cache_key in self.analysis_cache:
            result = self.analysis_cache[cache_key]
            if datetime.now() - result.created_at < self.cache_ttl:
                return result
            else:
                # Remove expired cache entry
                del self.analysis_cache[cache_key]
        return None

    def _cache_result(self, cache_key: str, result: AIAnalysisResult):
        """Cache analysis result"""
        self.analysis_cache[cache_key] = result
        
        # Cleanup old cache entries
        now = datetime.now()
        expired_keys = [
            key for key, cached_result in self.analysis_cache.items()
            if now - cached_result.created_at > self.cache_ttl
        ]
        for key in expired_keys:
            del self.analysis_cache[key]

    async def is_available(self) -> bool:
        """Check if AI service manager has available providers"""
        try:
            # Check if we have any registered providers
            if not self.providers:
                return False
            
            # Check if any providers are available
            for provider in self.providers.values():
                if provider.config.enabled and await provider.is_available():
                    return True
            
            return False
        except Exception as e:
            self.logger.error(f"Error checking AI service availability: {e}")
            return False

    def is_available_sync(self) -> bool:
        """Synchronous version of is_available for use in sync contexts"""
        try:
            # Check if we have any registered providers
            if not self.providers:
                return False
            
            # For sync version, just check if providers are enabled
            # This is a simplified check that doesn't do full async validation
            for provider in self.providers.values():
                if provider.config.enabled:
                    return True
            
            return False
        except Exception as e:
            self.logger.error(f"Error checking AI service availability (sync): {e}")
            return False


# Global AI service manager instance
ai_service_manager = AIServiceManager()