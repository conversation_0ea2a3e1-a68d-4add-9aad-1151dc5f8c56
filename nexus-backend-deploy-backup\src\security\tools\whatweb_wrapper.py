"""
WhatWeb Scanner Wrapper for NexusScan Desktop
Web technology identification tool wrapper.
"""

import logging
from typing import Dict, List, Optional, Callable, Any
from datetime import datetime

from security.tools.tool_registry import (
    ToolMetadata, ToolCategory, ToolCapabilities,
    ScanOptions, ScanResult, ToolStatus, register_tool
)
from security.tools.external_tool_wrapper import ExternalToolWrapper

logger = logging.getLogger(__name__)


@register_tool  
class WhatWebScanner(ExternalToolWrapper):
    """WhatWeb technology fingerprinting scanner wrapper"""
    
    def get_metadata(self) -> ToolMetadata:
        """Get tool metadata"""
        return ToolMetadata(
            name="whatweb",
            display_name="WhatWeb Scanner",
            description="Web technology identification and fingerprinting tool",
            version="0.5.5",
            category=ToolCategory.WEB_SCANNER,
            author="Andrew Horton",
            website="https://github.com/urbanadventurer/WhatWeb",
            capabilities=ToolCapabilities(
                supports_async=True,
                supports_progress=True,
                supports_cancellation=True,
                requires_root=False,
                network_access_required=True,
                output_formats=["json", "xml", "text"],
                supported_targets=["url", "domain"]
            ),
            default_options={
                "aggression": 1,  # 1=passive, 2=polite, 3=aggressive, 4=heavy
                "follow_redirects": 10,
                "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
                "plugins": []  # Auto-detect all by default
            }
        )
    
    def get_command_path(self) -> str:
        """Get WhatWeb command path"""
        return "whatweb"
    
    def build_command(self, options: ScanOptions) -> List[str]:
        """Build WhatWeb command"""
        cmd = [self.get_command_path()]
        
        # Target
        cmd.append(options.target)
        
        # Aggression level
        aggression = options.custom_options.get("aggression", 1)
        cmd.extend(["-a", str(aggression)])
        
        # Output format
        if options.output_format == "json":
            cmd.extend(["--log-json", "-"])
        elif options.output_format == "xml":
            cmd.extend(["--log-xml", "-"])
        
        # Follow redirects
        redirects = options.custom_options.get("follow_redirects", 10)
        cmd.extend(["--max-redirects", str(redirects)])
        
        # User agent
        if "user_agent" in options.custom_options:
            cmd.extend(["-U", options.custom_options["user_agent"]])
        
        # Specific plugins
        if options.custom_options.get("plugins"):
            for plugin in options.custom_options["plugins"]:
                cmd.extend(["-p", plugin])
        
        # Proxy
        if options.custom_options.get("proxy"):
            cmd.extend(["--proxy", options.custom_options["proxy"]])
        
        # Quiet mode for cleaner output
        cmd.append("-q")
        
        return cmd
    
    def parse_output(self, output: str, options: ScanOptions) -> Dict[str, Any]:
        """Parse WhatWeb output"""
        parsed_results = {
            "target": options.target,
            "technologies": [],
            "plugins_detected": [],
            "summary": {}
        }
        
        try:
            if options.output_format == "json":
                import json
                # WhatWeb JSON format is one result per line
                for line in output.strip().split('\n'):
                    if line:
                        result = json.loads(line)
                        if "target" in result:
                            parsed_results["target"] = result["target"]
                        if "plugins" in result:
                            for plugin_name, plugin_data in result["plugins"].items():
                                tech_info = {
                                    "name": plugin_name,
                                    "version": plugin_data.get("version", []),
                                    "string": plugin_data.get("string", []),
                                    "account": plugin_data.get("account", []),
                                    "model": plugin_data.get("model", []),
                                    "firmware": plugin_data.get("firmware", []),
                                    "modules": plugin_data.get("modules", []),
                                    "confidence": 100  # WhatWeb doesn't provide confidence
                                }
                                parsed_results["technologies"].append(tech_info)
                                parsed_results["plugins_detected"].append(plugin_name)
            else:
                # Parse text output
                lines = output.strip().split('\n')
                for line in lines:
                    if line and "http" in line:
                        # Extract technologies from line
                        parts = line.split(']')
                        if len(parts) > 1:
                            tech_string = parts[1].strip()
                            technologies = tech_string.split(',')
                            for tech in technologies:
                                tech = tech.strip()
                                if tech:
                                    parsed_results["technologies"].append({
                                        "name": tech,
                                        "confidence": 100
                                    })
            
            # Create summary
            parsed_results["summary"] = {
                "total_technologies": len(parsed_results["technologies"]),
                "categories": self._categorize_technologies(parsed_results["technologies"])
            }
            
        except Exception as e:
            logger.error(f"Failed to parse WhatWeb output: {e}")
            parsed_results["parse_error"] = str(e)
            parsed_results["raw_output"] = output
        
        return parsed_results
    
    def _categorize_technologies(self, technologies: List[Dict]) -> Dict[str, List[str]]:
        """Categorize detected technologies"""
        categories = {
            "web_servers": [],
            "programming_languages": [],
            "frameworks": [],
            "cms": [],
            "javascript_libraries": [],
            "analytics": [],
            "other": []
        }
        
        # Simple categorization based on common patterns
        for tech in technologies:
            name = tech.get("name", "").lower()
            
            if any(srv in name for srv in ["apache", "nginx", "iis", "server"]):
                categories["web_servers"].append(tech["name"])
            elif any(lang in name for lang in ["php", "python", "ruby", "java", "asp"]):
                categories["programming_languages"].append(tech["name"])
            elif any(fw in name for fw in ["django", "rails", "spring", "express"]):
                categories["frameworks"].append(tech["name"])
            elif any(cms in name for cms in ["wordpress", "drupal", "joomla", "magento"]):
                categories["cms"].append(tech["name"])
            elif any(js in name for js in ["jquery", "react", "angular", "vue"]):
                categories["javascript_libraries"].append(tech["name"])
            elif any(analytics in name for analytics in ["google", "analytics", "tag"]):
                categories["analytics"].append(tech["name"])
            else:
                categories["other"].append(tech["name"])
        
        # Remove empty categories
        return {k: v for k, v in categories.items() if v}
    
    def get_tool_command(self, options: ScanOptions) -> List[str]:
        """Get tool command - required by base class"""
        return self.build_command(options)
    
    def parse_tool_output(self, output: str, options: ScanOptions) -> Dict[str, Any]:
        """Parse tool output - required by base class"""
        return self.parse_output(output, options)