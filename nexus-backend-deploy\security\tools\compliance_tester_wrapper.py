"""
Automated Compliance Tester Wrapper for NexusScan Desktop
Wraps the AutomatedComplianceTester as a registered security tool.
"""

import logging
from typing import Dict, List, Optional, Callable, Any
from datetime import datetime

from security.tools.tool_registry import (
    ToolMetadata, ToolCategory, Tool<PERSON><PERSON>bilities,
    ScanOptions, ScanResult, ToolStatus, register_tool
)
from security.tools.base_scanner import BaseScanner
from security.tools.custom.compliance.automated_compliance_tester import (
    AutomatedComplianceTester,
    ComplianceFramework,
    ComplianceTestingRequest
)
from ai.services import AIServiceManager
from core.config import Config
from core.database import DatabaseManager

logger = logging.getLogger(__name__)


@register_tool
class ComplianceTesterTool(BaseScanner):
    """Automated compliance testing tool wrapper"""
    
    def __init__(self):
        """Initialize compliance tester tool"""
        super().__init__()
        self.compliance_tester = None
        
    def get_metadata(self) -> ToolMetadata:
        """Get tool metadata"""
        return ToolMetadata(
            name="automated_compliance_tester",
            display_name="Automated Compliance Tester",
            description="Multi-framework compliance testing (SOC2, PCI-DSS, HIPAA, GDPR, ISO27001, NIST, CIS)",
            version="1.0.0",
            category=ToolCategory.COMPLIANCE_CHECKER,
            author="NexusScan Compliance Team",
            capabilities=ToolCapabilities(
                supports_async=True,
                supports_progress=True,
                supports_cancellation=False,
                requires_root=False,
                network_access_required=True,
                output_formats=["json", "html", "pdf"],
                supported_targets=["url", "ip", "domain", "organization"]
            ),
            default_options={
                "frameworks": ["soc2", "pci_dss"],
                "include_evidence_collection": True,
                "automated_remediation": True,
                "depth_level": "comprehensive",
                "compliance_year": 2025
            }
        )
    
    def check_availability(self) -> bool:
        """Check if compliance tester is available"""
        try:
            # Test initialization with required dependencies
            config = Config()
            db_manager = DatabaseManager()
            ai_manager = AIServiceManager()
            self.compliance_tester = AutomatedComplianceTester(config, db_manager, ai_manager)
            return True
        except Exception as e:
            logger.error(f"Automated Compliance Tester not available: {e}")
            return False
    
    async def scan(self, options: ScanOptions,
                   progress_callback: Optional[Callable[[float, str], None]] = None) -> ScanResult:
        """Execute compliance testing"""
        start_time = datetime.now()
        
        try:
            if not self.compliance_tester:
                config = Config()
                db_manager = DatabaseManager()
                ai_manager = AIServiceManager()
                self.compliance_tester = AutomatedComplianceTester(config, db_manager, ai_manager)
            
            # Extract compliance parameters from options
            target = options.target
            frameworks = options.custom_options.get("frameworks", ["soc2"])
            include_evidence = options.custom_options.get("include_evidence_collection", True)
            automated_remediation = options.custom_options.get("automated_remediation", True)
            depth_level = options.custom_options.get("depth_level", "comprehensive")
            
            # Convert framework names to enums
            framework_enums = []
            for fw in frameworks:
                try:
                    framework_enums.append(ComplianceFramework(fw.lower()))
                except ValueError:
                    logger.warning(f"Unknown compliance framework: {fw}")
            
            if not framework_enums:
                framework_enums = [ComplianceFramework.SOC2]
            
            # Create testing request
            request = ComplianceTestingRequest(
                target=target,
                frameworks=framework_enums,
                include_evidence_collection=include_evidence,
                automated_remediation=automated_remediation,
                depth_level=depth_level,
                organization_context=options.custom_options.get("organization_context", {}),
                exclude_categories=options.custom_options.get("exclude_categories", [])
            )
            
            if progress_callback:
                progress_callback(0.1, "Initializing compliance testing frameworks...")
            
            # Execute compliance testing
            if progress_callback:
                progress_callback(0.3, f"Testing {len(framework_enums)} compliance frameworks...")
            
            testing_result = await self.compliance_tester.test_compliance(request)
            
            if progress_callback:
                progress_callback(0.8, "Generating compliance reports and recommendations...")
            
            # Format results
            parsed_results = {
                "compliance_summary": {
                    "target": target,
                    "frameworks_tested": [fw.value for fw in framework_enums],
                    "overall_compliance_score": testing_result.overall_compliance_score,
                    "total_controls_tested": testing_result.total_controls_tested,
                    "compliant_controls": testing_result.compliant_controls,
                    "non_compliant_controls": testing_result.non_compliant_controls,
                    "testing_timestamp": testing_result.testing_timestamp
                },
                "framework_results": {
                    fw.value: {
                        "compliance_score": result.compliance_score,
                        "controls_tested": result.controls_tested,
                        "compliant_controls": result.compliant_controls,
                        "failed_controls": result.failed_controls,
                        "evidence_collected": len(result.evidence_collected),
                        "critical_findings": result.critical_findings,
                        "recommendations": result.recommendations
                    }
                    for fw, result in testing_result.framework_results.items()
                },
                "detailed_findings": [
                    {
                        "control_id": finding.control_id,
                        "framework": finding.framework.value,
                        "category": finding.category.value,
                        "status": finding.status.value,
                        "severity": finding.severity.value,
                        "description": finding.description,
                        "evidence": finding.evidence,
                        "remediation_steps": finding.remediation_steps,
                        "compliance_gap": finding.compliance_gap
                    }
                    for finding in testing_result.detailed_findings
                ],
                "compliance_recommendations": testing_result.compliance_recommendations,
                "audit_readiness": testing_result.audit_readiness,
                "certification_gaps": testing_result.certification_gaps
            }
            
            # Extract vulnerabilities for standard format
            vulnerabilities = []
            for finding in testing_result.detailed_findings:
                if finding.status.value == "non_compliant":
                    vulnerabilities.append({
                        "type": f"Compliance - {finding.framework.value.upper()}",
                        "severity": finding.severity.value,
                        "description": f"{finding.control_id}: {finding.description}",
                        "remediation": "; ".join(finding.remediation_steps)
                    })
            
            if progress_callback:
                progress_callback(1.0, "Compliance testing complete")
            
            end_time = datetime.now()
            
            return ScanResult(
                tool_name=self.metadata.name,
                target=options.target,
                status=ToolStatus.COMPLETED,
                start_time=start_time.isoformat(),
                end_time=end_time.isoformat(),
                duration_seconds=(end_time - start_time).total_seconds(),
                parsed_results=parsed_results,
                vulnerabilities=vulnerabilities,
                metadata={
                    "testing_id": testing_result.testing_id,
                    "frameworks_tested": len(framework_enums),
                    "compliance_score": testing_result.overall_compliance_score
                }
            )
            
        except Exception as e:
            logger.error(f"Compliance testing failed: {e}")
            end_time = datetime.now()
            
            return ScanResult(
                tool_name=self.metadata.name,
                target=options.target,
                status=ToolStatus.FAILED,
                start_time=start_time.isoformat(),
                end_time=end_time.isoformat(),
                duration_seconds=(end_time - start_time).total_seconds(),
                errors=[str(e)]
            )
    
    def check_native_availability(self) -> bool:
        """Check if native tool is available"""
        return self.check_availability()
    
    async def execute_native_scan(self, options: ScanOptions,
                                 progress_callback: Optional[Callable[[float, str], None]] = None) -> ScanResult:
        """Execute native scan"""
        return await self.scan(options, progress_callback)