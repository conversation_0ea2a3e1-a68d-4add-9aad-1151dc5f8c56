
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Technical Report - {{ report_data.target }}</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 20px; background-color: #f8f9fa; line-height: 1.6; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { border-bottom: 3px solid #1976d2; padding-bottom: 20px; margin-bottom: 30px; }
        .title { color: #1976d2; font-size: 28px; font-weight: bold; margin: 0; }
        .subtitle { color: #666; font-size: 16px; margin: 5px 0 0 0; }
        .toc { background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0; }
        .toc ul { list-style: none; padding-left: 0; }
        .toc li { margin: 5px 0; }
        .toc a { color: #1976d2; text-decoration: none; }
        .section { margin: 30px 0; }
        .section-title { font-size: 20px; font-weight: bold; color: #333; border-bottom: 2px solid #e0e0e0; padding-bottom: 10px; margin-bottom: 15px; }
        .vulnerability-table { width: 100%; border-collapse: collapse; margin: 20px 0; }
        .vulnerability-table th, .vulnerability-table td { border: 1px solid #ddd; padding: 12px; text-align: left; }
        .vulnerability-table th { background-color: #1976d2; color: white; }
        .vulnerability-table tr:nth-child(even) { background-color: #f9f9f9; }
        .severity-critical { background-color: #ffebee; color: #d32f2f; font-weight: bold; }
        .severity-high { background-color: #fff3e0; color: #f57c00; font-weight: bold; }
        .severity-medium { background-color: #fffde7; color: #fbc02d; font-weight: bold; }
        .severity-low { background-color: #e8f5e8; color: #388e3c; font-weight: bold; }
        .code-block { background: #f5f5f5; padding: 15px; border-radius: 4px; font-family: 'Courier New', monospace; font-size: 14px; overflow-x: auto; }
        .footer { margin-top: 40px; padding-top: 20px; border-top: 1px solid #e0e0e0; font-size: 12px; color: #666; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">Technical Security Assessment Report</h1>
            <p class="subtitle">Target: {{ report_data.target }}</p>
            <p class="subtitle">Scan Type: {{ report_data.scan_type }}</p>
            <p class="subtitle">Generated: {{ generation_time.strftime('%Y-%m-%d %H:%M:%S') }}</p>
        </div>
        
        <div class="toc">
            <h3>Table of Contents</h3>
            <ul>
                <li><a href="#executive-summary">Executive Summary</a></li>
                <li><a href="#methodology">Methodology</a></li>
                <li><a href="#findings">Detailed Findings</a></li>
                <li><a href="#ai-analysis">AI Analysis</a></li>
                <li><a href="#remediation">Remediation Plans</a></li>
                <li><a href="#appendices">Appendices</a></li>
            </ul>
        </div>
        
        <div class="section" id="executive-summary">
            <h2 class="section-title">Executive Summary</h2>
            <p>This comprehensive security assessment identified <strong>{{ report_data.vulnerabilities|length }} vulnerabilities</strong> 
            across the target environment. The AI-enhanced analysis resulted in an overall risk score of 
            <strong>{{ "%.1f"|format(report_data.ai_risk_score) }}/10</strong> with 
            <strong>{{ "%.1f"|format(report_data.ai_confidence) }}%</strong> confidence.</p>
        </div>
        
        <div class="section" id="methodology">
            <h2 class="section-title">Assessment Methodology</h2>
            <p>The security assessment was conducted using NexusScan's AI-enhanced scanning capabilities, 
            incorporating multiple security tools and advanced artificial intelligence analysis.</p>
            
            <h3>Scanning Tools Used:</h3>
            <ul>
                {% for tool, results in report_data.tool_results.items() %}
                <li><strong>{{ tool.title() }}</strong> - {{ results.get('description', 'Security scanning tool') }}</li>
                {% endfor %}
            </ul>
        </div>
        
        <div class="section" id="findings">
            <h2 class="section-title">Detailed Security Findings</h2>
            
            <h3>Vulnerability Summary by Severity</h3>
            <table class="vulnerability-table">
                <thead>
                    <tr>
                        <th>Severity</th>
                        <th>Count</th>
                        <th>Percentage</th>
                    </tr>
                </thead>
                <tbody>
                    {% for severity, vulns in vulnerabilities_by_severity.items() %}
                    <tr>
                        <td class="severity-{{ severity }}">{{ severity.title() }}</td>
                        <td>{{ vulns|length }}</td>
                        <td>{{ "%.1f"|format((vulns|length / report_data.vulnerabilities|length * 100) if report_data.vulnerabilities else 0) }}%</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
            
            <h3>Critical Vulnerabilities</h3>
            <table class="vulnerability-table">
                <thead>
                    <tr>
                        <th>Vulnerability</th>
                        <th>Severity</th>
                        <th>CVSS Score</th>
                        <th>Description</th>
                    </tr>
                </thead>
                <tbody>
                    {% for vuln in top_vulnerabilities[:20] %}
                    <tr>
                        <td>{{ vuln.get('title', 'Unknown Vulnerability') }}</td>
                        <td class="severity-{{ vuln.get('severity', 'unknown').lower() }}">{{ vuln.get('severity', 'Unknown') }}</td>
                        <td>{{ vuln.get('cvss_score', 'N/A') }}</td>
                        <td>{{ vuln.get('description', 'No description available') }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        
        {% if config.include_ai_insights and report_data.ai_insights %}
        <div class="section" id="ai-analysis">
            <h2 class="section-title">AI-Enhanced Security Analysis</h2>
            {% for insight in report_data.ai_insights %}
            <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 10px 0;">
                <h4>{{ insight.get('title', 'AI Security Insight') }}</h4>
                <p>{{ insight.get('summary', 'No summary available') }}</p>
                {% if insight.get('recommendations') %}
                <strong>Recommendations:</strong>
                <ul>
                    {% for rec in insight.get('recommendations', [])[:5] %}
                    <li>{{ rec }}</li>
                    {% endfor %}
                </ul>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        {% endif %}
        
        {% if config.include_remediation and report_data.remediation_plans %}
        <div class="section" id="remediation">
            <h2 class="section-title">Remediation Action Plan</h2>
            {% for plan in report_data.remediation_plans %}
            <div style="background: #fff3e0; padding: 15px; border-radius: 8px; margin: 10px 0; border-left: 4px solid #f57c00;">
                <h4>{{ plan.get('title', 'Remediation Item') }}</h4>
                <p><strong>Priority:</strong> {{ plan.get('priority', 'Medium') }}</p>
                <p><strong>Effort Estimate:</strong> {{ plan.get('effort_estimate', 'Unknown') }}</p>
                <p>{{ plan.get('description', 'No description available') }}</p>
                {% if plan.get('steps') %}
                <strong>Implementation Steps:</strong>
                <ol>
                    {% for step in plan.get('steps', [])[:10] %}
                    <li>{{ step }}</li>
                    {% endfor %}
                </ol>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        {% endif %}
        
        <div class="section" id="appendices">
            <h2 class="section-title">Technical Appendices</h2>
            <h3>Raw Scan Data</h3>
            <div class="code-block">
                <pre>{{ report_data.scan_summary | tojson(indent=2) }}</pre>
            </div>
        </div>
        
        <div class="footer">
            <p>This report was generated by NexusScan AI-Enhanced Security Scanner v{{ report_data.report_version }}</p>
            <p>Report ID: {{ report_data.scan_id }} | Generated: {{ report_data.report_generated_at.strftime('%Y-%m-%d %H:%M:%S') }}</p>
            <p>Confidence Score: {{ "%.1f"|format(report_data.ai_confidence) }}% | AI Risk Score: {{ "%.1f"|format(report_data.ai_risk_score) }}/10</p>
        </div>
    </div>
</body>
</html>
        