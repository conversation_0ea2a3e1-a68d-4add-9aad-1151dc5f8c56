"""
Scanning types and enums to avoid circular imports
"""

from enum import Enum


class ScanType(Enum):
    """Types of scans"""
    NETWORK = "network"
    NETWORK_DISCOVERY = "network_discovery"
    PORT = "port"
    VULNERABILITY = "vulnerability"
    WEB = "web"
    DNS = "dns"
    SSL = "ssl"
    SERVICE = "service"
    CUSTOM = "custom"


class ScanStatus(Enum):
    """Scan execution status"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
