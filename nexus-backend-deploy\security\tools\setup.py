#!/usr/bin/env python3
"""
NexusScan Desktop Application Setup
AI-Powered Penetration Testing Tool
"""

from setuptools import setup, find_packages

with open("README.md", "r", encoding="utf-8") as fh:
    long_description = fh.read()

with open("requirements.txt", "r", encoding="utf-8") as fh:
    requirements = [line.strip() for line in fh if line.strip() and not line.startswith("#")]

setup(
    name="nexusscan-desktop",
    version="1.0.0",
    author="NexusScan Team",
    author_email="<EMAIL>",
    description="AI-Powered Penetration Testing Desktop Application",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://github.com/nexusscan/nexusscan-desktop",
    packages=find_packages(where="src"),
    package_dir={"": "src"},
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Information Technology",
        "Topic :: Security",
        "License :: OSI Approved :: MIT License",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.12",
        "Operating System :: OS Independent",
    ],
    python_requires=">=3.12",
    install_requires=requirements,
    entry_points={
        "console_scripts": [
            "nexusscan=src.main:main",
        ],
    },
    include_package_data=True,
    zip_safe=False,
)