#!/usr/bin/env python3
"""
Debug ToolStatus issue - check if the fix is actually in the running code
"""
import requests
import json

def check_toolstatus_in_aws():
    """Check if ToolStatus fix is loaded in AWS backend"""
    
    print("🔍 Debugging ToolStatus issue...")
    
    # Test a simple request to see the exact error
    try:
        response = requests.post(
            "http://ec2-3-89-91-209.compute-1.amazonaws.com:8000/api/tools/nmap/scan",
            json={"target": "127.0.0.1", "options": {"scan_type": "quick"}},
            timeout=10
        )
        
        print(f"Status: {response.status_code}")
        print(f"Response: {response.text}")
        
        if "JSON serializable" in response.text:
            print("\n❌ ToolStatus fix NOT loaded - backend needs updated code")
            print("\n🔧 Solutions:")
            print("1. Make sure you cloned the LATEST repository")
            print("2. Check if you have the __str__ methods in ToolStatus enum")
            print("3. <PERSON>art backend after pulling latest code")
        else:
            print("\n✅ ToolStatus fix appears to be working")
            
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    check_toolstatus_in_aws()