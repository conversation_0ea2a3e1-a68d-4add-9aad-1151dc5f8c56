/**
 * PWA Icon Generation Script
 * Generates all required PWA icons from the base SVG
 */

const fs = require('fs');
const path = require('path');

// Icon sizes required for PWA
const iconSizes = [
  { size: 72, name: 'icon-72x72.png' },
  { size: 96, name: 'icon-96x96.png' },
  { size: 128, name: 'icon-128x128.png' },
  { size: 144, name: 'icon-144x144.png' },
  { size: 152, name: 'icon-152x152.png' },
  { size: 192, name: 'icon-192x192.png' },
  { size: 384, name: 'icon-384x384.png' },
  { size: 512, name: 'icon-512x512.png' }
];

// Create simple colored square icons as placeholders
function generatePlaceholderIcon(size, filename) {
  const svgContent = `
<svg width="${size}" height="${size}" viewBox="0 0 ${size} ${size}" fill="none" xmlns="http://www.w3.org/2000/svg">
  <rect width="${size}" height="${size}" fill="#3b82f6"/>
  <circle cx="${size/2}" cy="${size/2}" r="${size/3}" fill="#ffffff" opacity="0.9"/>
  <circle cx="${size/2}" cy="${size/2}" r="${size/5}" fill="#1e40af"/>
  <text x="${size/2}" y="${size/2 + 6}" text-anchor="middle" fill="#ffffff" font-family="Arial" font-size="${size/8}" font-weight="bold">N</text>
</svg>`;
  
  return svgContent;
}

// Ensure icons directory exists
const iconsDir = path.join(__dirname, '../public/icons');
if (!fs.existsSync(iconsDir)) {
  fs.mkdirSync(iconsDir, { recursive: true });
}

// Generate placeholder icons
iconSizes.forEach(({ size, name }) => {
  const svgContent = generatePlaceholderIcon(size, name);
  const svgPath = path.join(iconsDir, name.replace('.png', '.svg'));
  
  fs.writeFileSync(svgPath, svgContent);
  console.log(`Generated placeholder icon: ${name} (${size}x${size})`);
});

// Create apple-touch-icon
const appleTouchIcon = generatePlaceholderIcon(180, 'apple-touch-icon.png');
fs.writeFileSync(path.join(iconsDir, 'apple-touch-icon.svg'), appleTouchIcon);

// Create shortcut icons
const shortcutIcons = [
  { name: 'shortcut-network.svg', color: '#10b981' },
  { name: 'shortcut-web.svg', color: '#f59e0b' },
  { name: 'shortcut-dashboard.svg', color: '#8b5cf6' }
];

shortcutIcons.forEach(({ name, color }) => {
  const shortcutSvg = `
<svg width="96" height="96" viewBox="0 0 96 96" fill="none" xmlns="http://www.w3.org/2000/svg">
  <rect width="96" height="96" rx="16" fill="${color}"/>
  <circle cx="48" cy="48" r="24" fill="#ffffff" opacity="0.9"/>
  <circle cx="48" cy="48" r="16" fill="${color}"/>
</svg>`;
  
  fs.writeFileSync(path.join(iconsDir, name), shortcutSvg);
  console.log(`Generated shortcut icon: ${name}`);
});

console.log('\n✅ PWA icons generated successfully!');
console.log('📝 Note: These are placeholder icons. For production, consider using a proper icon generation tool.');
