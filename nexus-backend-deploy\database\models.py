#!/usr/bin/env python3
"""
Database Models for NexusScan Desktop Application
SQLAlchemy ORM models matching the database schema
"""

import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass
from enum import Enum
from decimal import Decimal
from sqlalchemy import (
    create_engine, Column, Integer, String, Text, DateTime, 
    Boolean, ForeignKey, Float, Index, event, DECIMAL
)
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship, sessionmaker, Session
from sqlalchemy.types import TypeDecorator, VARCHAR

Base = declarative_base()


class JSONType(TypeDecorator):
    """Custom JSON type for SQLite compatibility"""
    impl = VARCHAR
    
    def process_bind_param(self, value, dialect):
        if value is not None:
            return json.dumps(value)
        return value
    
    def process_result_value(self, value, dialect):
        if value is not None:
            return json.loads(value)
        return value


class CampaignStatus(Enum):
    """Campaign status enumeration"""
    DRAFT = "draft"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    PAUSED = "paused"


class ScanStatus(Enum):
    """Scan status enumeration"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class VulnerabilitySeverity(Enum):
    """Vulnerability severity levels"""
    CRITICAL = "critical"
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"
    INFO = "info"


class VulnerabilityStatus(Enum):
    """Vulnerability status"""
    OPEN = "open"
    FIXED = "fixed"
    FALSE_POSITIVE = "false_positive"
    ACCEPTED_RISK = "accepted_risk"


# =============================================================================
# CORE MODELS
# =============================================================================

class Campaign(Base):
    """Campaign model - Main scanning campaigns/projects"""
    __tablename__ = 'campaigns'
    
    id = Column(Integer, primary_key=True)
    name = Column(String(255), nullable=False)
    description = Column(Text)
    target_scope = Column(JSONType, nullable=False)  # List of targets
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    status = Column(String(50), default=CampaignStatus.DRAFT.value)
    owner = Column(String(100))
    tags = Column(JSONType)  # List of tags
    settings = Column(JSONType)  # Campaign-specific settings
    
    __table_args__ = (
        Index('idx_campaigns_status', 'status'),
        Index('idx_campaigns_created_at', 'created_at'),
        Index('idx_campaigns_owner', 'owner'),
    )
    
    # Relationships
    scans = relationship("Scan", back_populates="campaign", cascade="all, delete-orphan")
    vulnerabilities = relationship("Vulnerability", back_populates="campaign", cascade="all, delete-orphan")
    ai_analyses = relationship("AIAnalysis", back_populates="campaign", cascade="all, delete-orphan")
    reports = relationship("Report", back_populates="campaign", cascade="all, delete-orphan")
    file_attachments = relationship("FileAttachment", back_populates="campaign", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<Campaign(id={self.id}, name='{self.name}', status='{self.status}')>"
    
    @property
    def target_count(self) -> int:
        """Get number of targets in scope"""
        return len(self.target_scope) if self.target_scope else 0
    
    @property
    def vulnerability_stats(self) -> Dict[str, int]:
        """Get vulnerability statistics"""
        stats = {"critical": 0, "high": 0, "medium": 0, "low": 0, "info": 0}
        for vuln in self.vulnerabilities:
            stats[vuln.severity] = stats.get(vuln.severity, 0) + 1
        return stats


class Scan(Base):
    """Scan model - Individual scan sessions within campaigns"""
    __tablename__ = 'scans'
    
    id = Column(Integer, primary_key=True)
    campaign_id = Column(Integer, ForeignKey('campaigns.id'), nullable=False)
    name = Column(String(255), nullable=False)
    scan_type = Column(String(100), nullable=False)  # network, web, api, mobile
    target = Column(String(500), nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    started_at = Column(DateTime)
    completed_at = Column(DateTime)
    status = Column(String(50), default=ScanStatus.PENDING.value)
    priority = Column(String(20), default='medium')  # low, medium, high, critical
    progress = Column(Integer, default=0)  # Progress percentage 0-100
    duration_seconds = Column(Integer, default=0)
    tools_used = Column(JSONType)  # List of tools used
    scan_config = Column(JSONType)  # Configuration used
    raw_output = Column(Text)  # Raw tool output
    summary = Column(Text)  # Brief scan summary
    results = Column(JSONType)  # Scan results in structured format
    
    __table_args__ = (
        Index('idx_scans_campaign_id', 'campaign_id'),
        Index('idx_scans_status', 'status'),
        Index('idx_scans_started_at', 'started_at'),
        Index('idx_scans_scan_type', 'scan_type'),
        Index('idx_scans_campaign_status', 'campaign_id', 'status'),
        Index('idx_scans_type_status', 'scan_type', 'status'),
    )
    
    # Relationships
    campaign = relationship("Campaign", back_populates="scans")
    vulnerabilities = relationship("Vulnerability", back_populates="scan", cascade="all, delete-orphan")
    ai_analyses = relationship("AIAnalysis", back_populates="scan", cascade="all, delete-orphan")
    file_attachments = relationship("FileAttachment", back_populates="scan", cascade="all, delete-orphan")
    scan_results = relationship("ScanResult", back_populates="scan", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<Scan(id={self.id}, name='{self.name}', status='{self.status}')>"
    
    @property
    def duration_formatted(self) -> str:
        """Get formatted duration string"""
        if self.duration_seconds:
            hours = self.duration_seconds // 3600
            minutes = (self.duration_seconds % 3600) // 60
            seconds = self.duration_seconds % 60
            return f"{hours:02d}:{minutes:02d}:{seconds:02d}"
        return "00:00:00"
    
    def complete_scan(self):
        """Mark scan as completed and calculate duration"""
        self.completed_at = datetime.utcnow()
        self.status = ScanStatus.COMPLETED.value
        if self.started_at:
            delta = self.completed_at - self.started_at
            self.duration_seconds = int(delta.total_seconds())


class Vulnerability(Base):
    """Vulnerability model - Discovered security vulnerabilities"""
    __tablename__ = 'vulnerabilities'
    
    id = Column(Integer, primary_key=True)
    scan_id = Column(Integer, ForeignKey('scans.id'), nullable=False)
    campaign_id = Column(Integer, ForeignKey('campaigns.id'), nullable=False)
    vuln_type = Column(String(100), nullable=False)  # sql_injection, xss, rce, etc.
    severity = Column(String(20), nullable=False)
    title = Column(String(500), nullable=False)
    description = Column(Text, nullable=False)
    affected_url = Column(String(1000))
    affected_parameter = Column(String(200))
    evidence = Column(Text)  # Proof of concept
    remediation = Column(Text)  # Fix recommendations
    cvss_score = Column(DECIMAL(3,1), default=0.0)
    cve_id = Column(String(50))  # CVE identifier
    discovered_at = Column(DateTime, default=datetime.utcnow)
    status = Column(String(50), default=VulnerabilityStatus.OPEN.value)
    verified = Column(Boolean, default=False)
    exploitable = Column(Boolean, default=False)
    tags = Column(JSONType)  # List of tags
    vuln_metadata = Column(JSONType)  # Additional metadata (matches schema.sql metadata column)
    
    __table_args__ = (
        Index('idx_vulnerabilities_scan_id', 'scan_id'),
        Index('idx_vulnerabilities_campaign_id', 'campaign_id'),
        Index('idx_vulnerabilities_severity', 'severity'),
        Index('idx_vulnerabilities_vuln_type', 'vuln_type'),
        Index('idx_vulnerabilities_status', 'status'),
        Index('idx_vulnerabilities_discovered_at', 'discovered_at'),
        Index('idx_vulnerabilities_cvss_score', 'cvss_score'),
        Index('idx_vulnerabilities_severity_date', 'severity', 'discovered_at'),
        Index('idx_vulnerabilities_type_severity', 'vuln_type', 'severity'),
        Index('idx_vulnerabilities_status_severity', 'status', 'severity'),
    )
    
    # Relationships
    scan = relationship("Scan", back_populates="vulnerabilities")
    campaign = relationship("Campaign", back_populates="vulnerabilities")
    ai_analyses = relationship("AIAnalysis", back_populates="vulnerability", cascade="all, delete-orphan")
    file_attachments = relationship("FileAttachment", back_populates="vulnerability", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<Vulnerability(id={self.id}, type='{self.vuln_type}', severity='{self.severity}')>"
    
    @property
    def severity_score(self) -> int:
        """Get numeric severity score for sorting"""
        severity_map = {
            VulnerabilitySeverity.CRITICAL.value: 5,
            VulnerabilitySeverity.HIGH.value: 4,
            VulnerabilitySeverity.MEDIUM.value: 3,
            VulnerabilitySeverity.LOW.value: 2,
            VulnerabilitySeverity.INFO.value: 1
        }
        return severity_map.get(self.severity, 0)


class AIAnalysis(Base):
    """AI Analysis model - AI-generated analysis and insights"""
    __tablename__ = 'ai_analysis'
    
    id = Column(Integer, primary_key=True)
    vulnerability_id = Column(Integer, ForeignKey('vulnerabilities.id'))
    scan_id = Column(Integer, ForeignKey('scans.id'))
    campaign_id = Column(Integer, ForeignKey('campaigns.id'))
    analysis_type = Column(String(100), nullable=False)  # payload_generation, risk_assessment, etc.
    ai_provider = Column(String(50))  # openai_o3, deepseek, anthropic
    model_used = Column(String(100))
    input_data = Column(JSONType, nullable=False)  # Input sent to AI
    output_data = Column(JSONType, nullable=False)  # AI response
    confidence_score = Column(DECIMAL(3,2), default=0.0)
    execution_time_ms = Column(Integer, default=0)
    created_at = Column(DateTime, default=datetime.utcnow)
    status = Column(String(50), default='completed')
    error_message = Column(Text)
    
    # Relationships
    vulnerability = relationship("Vulnerability", back_populates="ai_analyses")
    scan = relationship("Scan", back_populates="ai_analyses")
    campaign = relationship("Campaign", back_populates="ai_analyses")
    
    def __repr__(self):
        return f"<AIAnalysis(id={self.id}, type='{self.analysis_type}', provider='{self.ai_provider}')>"


# =============================================================================
# CONFIGURATION MODELS
# =============================================================================

class AppConfig(Base):
    """Application configuration model"""
    __tablename__ = 'app_config'
    
    id = Column(Integer, primary_key=True)
    section = Column(String(100), nullable=False)
    key = Column(String(200), nullable=False)
    value = Column(Text, nullable=False)
    value_type = Column(String(20), default='string')  # string, integer, boolean, json
    description = Column(Text)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    __table_args__ = (
        Index('idx_app_config_section_key', 'section', 'key', unique=True),
        Index('idx_app_config_section', 'section'),
    )
    
    def __repr__(self):
        return f"<AppConfig(section='{self.section}', key='{self.key}')>"
    
    @property
    def typed_value(self) -> Union[str, int, bool, Dict, List]:
        """Get value converted to appropriate type"""
        if self.value_type == 'integer':
            return int(self.value)
        elif self.value_type == 'boolean':
            return self.value.lower() in ('true', '1', 'yes', 'on')
        elif self.value_type == 'json':
            return json.loads(self.value)
        return self.value


class ScanTemplate(Base):
    """Scan template model - Reusable scan configurations"""
    __tablename__ = 'scan_templates'
    
    id = Column(Integer, primary_key=True)
    name = Column(String(255), nullable=False, unique=True)
    description = Column(Text)
    scan_type = Column(String(100), nullable=False)
    tools_config = Column(JSONType, nullable=False)  # Tools configuration
    ai_config = Column(JSONType)  # AI settings
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    is_default = Column(Boolean, default=False)
    category = Column(String(100))  # web, network, api, mobile
    
    def __repr__(self):
        return f"<ScanTemplate(id={self.id}, name='{self.name}', type='{self.scan_type}')>"


# =============================================================================
# REPORTING & EXPORT MODELS
# =============================================================================

class Report(Base):
    """Report model - Generated reports"""
    __tablename__ = 'reports'
    
    id = Column(Integer, primary_key=True)
    campaign_id = Column(Integer, ForeignKey('campaigns.id'), nullable=False)
    name = Column(String(255), nullable=False)
    report_type = Column(String(100), nullable=False)  # executive, technical, compliance
    format = Column(String(50), nullable=False)  # pdf, html, json, csv
    template_used = Column(String(200))
    generated_at = Column(DateTime, default=datetime.utcnow)
    file_path = Column(String(500))  # Path to generated file
    file_size = Column(Integer, default=0)
    status = Column(String(50), default='generated')
    report_metadata = Column(JSONType)  # Report metadata (matches schema.sql metadata column)
    
    # Relationships
    campaign = relationship("Campaign", back_populates="reports")
    
    def __repr__(self):
        return f"<Report(id={self.id}, name='{self.name}', type='{self.report_type}')>"


class ExportHistory(Base):
    """Export history model - Track data exports"""
    __tablename__ = 'export_history'
    
    id = Column(Integer, primary_key=True)
    campaign_id = Column(Integer, ForeignKey('campaigns.id'))
    export_type = Column(String(100), nullable=False)  # vulnerabilities, scans, full_campaign
    format = Column(String(50), nullable=False)  # json, csv, xml
    file_path = Column(String(500))
    exported_at = Column(DateTime, default=datetime.utcnow)
    record_count = Column(Integer, default=0)
    file_size = Column(Integer, default=0)
    exported_by = Column(String(100))  # User who initiated export
    
    def __repr__(self):
        return f"<ExportHistory(id={self.id}, type='{self.export_type}', format='{self.format}')>"


# =============================================================================
# SECURITY & AUDIT MODELS
# =============================================================================

class ActivityLog(Base):
    """Activity log model - Audit trail of user actions"""
    __tablename__ = 'activity_log'
    
    id = Column(Integer, primary_key=True)
    user_id = Column(String(100))
    action = Column(String(200), nullable=False)
    resource_type = Column(String(100))  # campaign, scan, vulnerability, etc.
    resource_id = Column(Integer)
    details = Column(JSONType)  # Additional details
    ip_address = Column(String(45))
    user_agent = Column(Text)
    timestamp = Column(DateTime, default=datetime.utcnow)
    session_id = Column(String(255))
    
    def __repr__(self):
        return f"<ActivityLog(id={self.id}, action='{self.action}', user='{self.user_id}')>"


class ScanResult(Base):
    """Scan result model - Individual tool results within scans"""
    __tablename__ = 'scan_results'
    
    id = Column(Integer, primary_key=True)
    scan_id = Column(Integer, ForeignKey('scans.id'), nullable=False)
    tool_name = Column(String(100), nullable=False)
    result_type = Column(String(100))  # vulnerability, info, error
    raw_output = Column(Text)
    parsed_data = Column(JSONType)  # Structured result data
    execution_time_ms = Column(Integer, default=0)
    exit_code = Column(Integer, default=0)
    created_at = Column(DateTime, default=datetime.utcnow)
    
    __table_args__ = (
        Index('idx_scan_results_scan', 'scan_id', 'tool_name'),
        Index('idx_scan_results_tool', 'tool_name'),
        Index('idx_scan_results_created', 'created_at'),
    )
    
    # Relationships
    scan = relationship("Scan", back_populates="scan_results")
    
    def __repr__(self):
        return f"<ScanResult(id={self.id}, tool='{self.tool_name}', scan_id={self.scan_id})>"


class FileAttachment(Base):
    """File attachment model - Store evidence files, screenshots, etc."""
    __tablename__ = 'file_attachments'
    
    id = Column(Integer, primary_key=True)
    vulnerability_id = Column(Integer, ForeignKey('vulnerabilities.id'))
    scan_id = Column(Integer, ForeignKey('scans.id'))
    campaign_id = Column(Integer, ForeignKey('campaigns.id'))
    filename = Column(String(500), nullable=False)
    original_filename = Column(String(500), nullable=False)
    file_path = Column(String(1000), nullable=False)
    file_size = Column(Integer, nullable=False)
    mime_type = Column(String(200))
    file_hash = Column(String(64))  # SHA256 hash
    uploaded_at = Column(DateTime, default=datetime.utcnow)
    description = Column(Text)
    
    # Relationships
    vulnerability = relationship("Vulnerability", back_populates="file_attachments")
    scan = relationship("Scan", back_populates="file_attachments")
    campaign = relationship("Campaign", back_populates="file_attachments")
    
    def __repr__(self):
        return f"<FileAttachment(id={self.id}, filename='{self.filename}')>"


class DatabaseVersion(Base):
    """Database version tracking"""
    __tablename__ = 'db_version'
    
    version = Column(Integer, primary_key=True)
    applied_at = Column(DateTime, default=datetime.utcnow)
    description = Column(Text)
    
    def __repr__(self):
        return f"<DatabaseVersion(version={self.version})>"


# =============================================================================
# DATABASE HELPER FUNCTIONS
# =============================================================================

@dataclass
class DatabaseConfig:
    """Database configuration"""
    database_url: str = "sqlite:///nexusscan.db"
    echo: bool = False
    pool_pre_ping: bool = True
    pool_recycle: int = 3600


class DatabaseManager:
    """Database management class"""
    
    def __init__(self, config: DatabaseConfig):
        self.config = config
        self.engine = create_engine(
            config.database_url,
            echo=config.echo,
            pool_pre_ping=config.pool_pre_ping,
            pool_recycle=config.pool_recycle
        )
        self.SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=self.engine)
    
    def create_tables(self):
        """Create all database tables"""
        Base.metadata.create_all(bind=self.engine)
    
    def get_session(self) -> Session:
        """Get database session"""
        return self.SessionLocal()
    
    def init_database(self):
        """Initialize database with default data"""
        self.create_tables()
        
        session = self.get_session()
        try:
            # Check if database is already initialized
            version = session.query(DatabaseVersion).filter_by(version=1).first()
            if not version:
                # Run schema.sql initialization
                self._run_sql_file(session, "src/database/schema.sql")
                session.commit()
        finally:
            session.close()
    
    def _run_sql_file(self, session: Session, file_path: str):
        """Execute SQL file"""
        try:
            with open(file_path, 'r') as f:
                sql_content = f.read()
                # Split by semicolon and execute each statement
                statements = [stmt.strip() for stmt in sql_content.split(';') if stmt.strip()]
                for statement in statements:
                    if statement and not statement.startswith('--'):
                        session.execute(statement)
        except FileNotFoundError:
            # SQL file not found, skip
            pass


# =============================================================================
# QUERY HELPERS
# =============================================================================

class DatabaseQueries:
    """Common database queries"""
    
    def __init__(self, session: Session):
        self.session = session
    
    def get_campaign_summary(self, campaign_id: int) -> Dict[str, Any]:
        """Get campaign summary with statistics"""
        campaign = self.session.query(Campaign).filter_by(id=campaign_id).first()
        if not campaign:
            return {}
        
        total_scans = self.session.query(Scan).filter_by(campaign_id=campaign_id).count()
        total_vulns = self.session.query(Vulnerability).filter_by(campaign_id=campaign_id).count()
        
        # Vulnerability severity breakdown
        vuln_stats = {}
        for severity in VulnerabilitySeverity:
            count = self.session.query(Vulnerability).filter_by(
                campaign_id=campaign_id, 
                severity=severity.value
            ).count()
            vuln_stats[severity.value] = count
        
        return {
            "id": campaign.id,
            "name": campaign.name,
            "description": campaign.description,
            "status": campaign.status,
            "created_at": campaign.created_at,
            "updated_at": campaign.updated_at,
            "total_scans": total_scans,
            "total_vulnerabilities": total_vulns,
            "vulnerability_breakdown": vuln_stats,
            "target_count": campaign.target_count
        }
    
    def get_recent_activity(self, days: int = 7) -> List[Dict[str, Any]]:
        """Get recent activity across all campaigns"""
        cutoff_date = datetime.utcnow() - timedelta(days=days)
        
        activities = []
        
        # Recent campaigns
        campaigns = self.session.query(Campaign).filter(
            Campaign.updated_at >= cutoff_date
        ).order_by(Campaign.updated_at.desc()).all()
        
        for campaign in campaigns:
            activities.append({
                "type": "campaign",
                "id": campaign.id,
                "name": campaign.name,
                "status": campaign.status,
                "timestamp": campaign.updated_at,
                "description": f"Campaign {campaign.status}"
            })
        
        # Recent scans
        scans = self.session.query(Scan).filter(
            Scan.started_at >= cutoff_date
        ).order_by(Scan.started_at.desc()).all()
        
        for scan in scans:
            activities.append({
                "type": "scan",
                "id": scan.id,
                "name": scan.name,
                "status": scan.status,
                "timestamp": scan.completed_at or scan.started_at,
                "description": f"Scan {scan.status}"
            })
        
        # Sort by timestamp
        activities.sort(key=lambda x: x["timestamp"], reverse=True)
        return activities[:50]  # Return top 50 recent activities