# Dependency Fix Plan for NexusScan Desktop

## 🚨 Critical Issues Found

### 1. **No package-lock.json**
- Security vulnerabilities cannot be audited
- Inconsistent dependency versions across environments
- No reproducible builds

### 2. **Major Version Gaps**
- Testing framework (Vitest) is 2 major versions behind
- Mock Service Worker (MSW) has breaking changes between v1 and v2
- Many core dependencies are outdated

### 3. **Missing Electron Dependencies**
- Electron packages listed in `electronDependencies` but not installed
- This breaks the desktop application functionality

### 4. **Deprecated APIs**
- Vite CJS Node API deprecation warnings

## 🔧 Recommended Fix Strategy

### Option 1: Conservative Update (Recommended for Stability)
```bash
# 1. Create package-lock.json
npm i --package-lock-only

# 2. Audit and fix vulnerabilities
npm audit
npm audit fix

# 3. Update patch versions only
npm update

# 4. Install missing Electron dependencies
npm install --save-dev electron@^28.0.0 electron-builder@^24.6.4
npm install electron-updater@^6.1.4 electron-store@^8.1.0 @electron/remote@^2.0.12 electron-log@^5.0.0 electron-context-menu@^3.6.1 electron-window-state@^5.0.3 fs-extra@^11.1.1 archiver@^6.0.1 extract-zip@^2.0.1 node-pty@^1.0.0

# 5. Run tests to ensure nothing breaks
npm test
```

### Option 2: Gradual Major Updates
```bash
# 1. Update testing infrastructure first (will require test file updates)
npm install --save-dev vitest@^1.0.0 @vitest/ui@^1.0.0
# Fix any breaking test changes

# 2. Update MSW (requires handler updates)
npm install --save-dev msw@^2.0.0
# Update MSW handlers from v1 to v2 syntax

# 3. Update other major versions one by one
# Test after each update
```

### Option 3: Full Modernization (High Risk)
```bash
# Update all dependencies to latest
npm update --save
npm update --save-dev

# This will require significant code changes
```

## 📋 Immediate Actions Needed

1. **Generate package-lock.json**:
   ```bash
   npm i --package-lock-only
   ```

2. **Install Electron dependencies**:
   ```bash
   npm install --save-dev electron@^28.0.0 electron-builder@^24.6.4
   ```

3. **Fix Vite CJS warning** by updating `vite.config.ts` to use ESM imports

4. **Run security audit**:
   ```bash
   npm audit
   ```

## ⚠️ Breaking Changes to Watch

### MSW v1 → v2
- Handler syntax changed from `rest` to `http`
- Setup syntax changed
- Request/response handling updated

### Vitest 0.34 → 1.0+
- Configuration changes
- API updates
- Coverage reporting changes

### React Router v6 → v7
- Route configuration changes
- New features but mostly backward compatible

## 🎯 Recommended Approach

Given that all tests are currently passing, I recommend:

1. **First**: Create package-lock.json and install Electron dependencies
2. **Second**: Fix security vulnerabilities with `npm audit fix`
3. **Third**: Update minor/patch versions only
4. **Fourth**: Plan major version updates for after Week 3-4 testing

This ensures stability while addressing critical issues.