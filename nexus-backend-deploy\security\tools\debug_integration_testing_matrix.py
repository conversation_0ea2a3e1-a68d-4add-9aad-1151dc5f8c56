#!/usr/bin/env python3
"""
🕸️ Integration Testing Matrix Debugger 🕸️
Comprehensive validation of all component interactions in NexusScan

This script creates and validates:
- Frontend ↔ Backend API communication
- Backend ↔ Database interactions
- AI Services ↔ Security Tools integration
- WebSocket real-time communication flows
- Authentication ↔ Authorization workflows
- Tool Manager ↔ Individual Security Tools
- Reporting Engine ↔ Data Sources
- Event System ↔ All Components
- Cross-platform component compatibility
- Error propagation and handling across layers
"""

import asyncio
import json
import logging
import os
import sys
import time
import traceback
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
import requests
import websockets
import subprocess
import sqlite3
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root / 'src'))

# Color codes for magical output
class Colors:
    HEADER = '\033[95m'
    OKBLUE = '\033[94m'
    OKCYAN = '\033[96m'
    OKGREEN = '\033[92m'
    WARNING = '\033[93m'
    FAIL = '\033[91m'
    ENDC = '\033[0m'
    BOLD = '\033[1m'
    UNDERLINE = '\033[4m'

class IntegrationTestResult:
    """Represents the result of an integration test"""
    
    def __init__(self, test_name: str, components: List[str], status: str, 
                 message: str, duration: float = 0, details: Dict = None):
        self.test_name = test_name
        self.components = components
        self.status = status  # 'passed', 'failed', 'warning', 'skipped'
        self.message = message
        self.duration = duration
        self.details = details or {}
        self.timestamp = datetime.now().isoformat()

class ComponentInteractionTester:
    """Test interactions between specific components"""
    
    def __init__(self, backend_url: str, project_root: Path):
        self.backend_url = backend_url
        self.project_root = project_root
        
    async def test_api_database_flow(self) -> IntegrationTestResult:
        """Test API → Database interaction flow"""
        start_time = time.time()
        
        try:
            # Create a test campaign via API
            campaign_data = {
                "name": f"Integration Test Campaign {int(time.time())}",
                "description": "Automated integration test campaign",
                "target_scope": ["127.0.0.1"],
                "tags": ["integration_test"]
            }
            
            # POST to campaigns API
            response = requests.post(
                f"{self.backend_url}/api/campaigns",
                json=campaign_data,
                timeout=10
            )
            
            if response.status_code not in [200, 201]:
                return IntegrationTestResult(
                    "API → Database Flow",
                    ["API Server", "Database"],
                    "failed",
                    f"Campaign creation failed: HTTP {response.status_code}",
                    time.time() - start_time
                )
            
            campaign_id = response.json().get('id')
            
            # Verify data was written to database
            db_paths = [
                self.project_root / 'data/nexusscan.db',
                self.project_root / 'nexusscan.db'
            ]
            
            db_path = None
            for path in db_paths:
                if path.exists():
                    db_path = path
                    break
            
            if not db_path:
                return IntegrationTestResult(
                    "API → Database Flow",
                    ["API Server", "Database"],
                    "failed",
                    "Database file not found",
                    time.time() - start_time
                )
            
            # Check database directly
            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()
            
            cursor.execute("SELECT * FROM campaigns WHERE id = ?", (campaign_id,))
            db_campaign = cursor.fetchone()
            
            conn.close()
            
            if not db_campaign:
                return IntegrationTestResult(
                    "API → Database Flow",
                    ["API Server", "Database"],
                    "failed",
                    "Campaign not found in database",
                    time.time() - start_time
                )
            
            # Clean up - delete test campaign
            requests.delete(f"{self.backend_url}/api/campaigns/{campaign_id}")
            
            return IntegrationTestResult(
                "API → Database Flow",
                ["API Server", "Database"],
                "passed",
                "API successfully wrote to database",
                time.time() - start_time,
                {"campaign_id": campaign_id}
            )
            
        except Exception as e:
            return IntegrationTestResult(
                "API → Database Flow",
                ["API Server", "Database"],
                "failed",
                f"Exception: {str(e)}",
                time.time() - start_time
            )
    
    async def test_ai_security_tools_flow(self) -> IntegrationTestResult:
        """Test AI Services → Security Tools integration"""
        start_time = time.time()
        
        try:
            # Check AI services status
            ai_response = requests.get(f"{self.backend_url}/api/ai/status", timeout=10)
            if ai_response.status_code != 200:
                return IntegrationTestResult(
                    "AI → Security Tools Flow",
                    ["AI Services", "Security Tools"],
                    "failed",
                    f"AI services not available: HTTP {ai_response.status_code}",
                    time.time() - start_time
                )
            
            # Check security tools status
            tools_response = requests.get(f"{self.backend_url}/api/tools/status", timeout=10)
            if tools_response.status_code != 200:
                return IntegrationTestResult(
                    "AI → Security Tools Flow",
                    ["AI Services", "Security Tools"],
                    "failed",
                    f"Security tools not available: HTTP {tools_response.status_code}",
                    time.time() - start_time
                )
            
            ai_status = ai_response.json()
            tools_status = tools_response.json()
            
            # Check if both systems are operational
            ai_healthy = ai_status.get('status') == 'healthy'
            tools_healthy = tools_status.get('status') == 'healthy'
            
            if not ai_healthy:
                return IntegrationTestResult(
                    "AI → Security Tools Flow",
                    ["AI Services", "Security Tools"],
                    "warning",
                    "AI services not healthy",
                    time.time() - start_time
                )
            
            if not tools_healthy:
                return IntegrationTestResult(
                    "AI → Security Tools Flow",
                    ["AI Services", "Security Tools"],
                    "warning",
                    "Security tools not healthy",
                    time.time() - start_time
                )
            
            # Test AI-enhanced scan request
            scan_data = {
                "name": "AI Integration Test Scan",
                "scan_type": "web",
                "target": "http://127.0.0.1",
                "ai_enhanced": True
            }
            
            scan_response = requests.post(
                f"{self.backend_url}/api/scans",
                json=scan_data,
                timeout=15
            )
            
            if scan_response.status_code in [200, 201]:
                return IntegrationTestResult(
                    "AI → Security Tools Flow",
                    ["AI Services", "Security Tools"],
                    "passed",
                    "AI-enhanced scan request successful",
                    time.time() - start_time,
                    {"ai_providers": list(ai_status.get('providers', {}).keys())}
                )
            else:
                return IntegrationTestResult(
                    "AI → Security Tools Flow",
                    ["AI Services", "Security Tools"],
                    "warning",
                    f"AI-enhanced scan failed: HTTP {scan_response.status_code}",
                    time.time() - start_time
                )
                
        except Exception as e:
            return IntegrationTestResult(
                "AI → Security Tools Flow",
                ["AI Services", "Security Tools"],
                "failed",
                f"Exception: {str(e)}",
                time.time() - start_time
            )
    
    async def test_websocket_event_flow(self) -> IntegrationTestResult:
        """Test WebSocket → Event System integration"""
        start_time = time.time()
        
        try:
            ws_url = self.backend_url.replace('http://', 'ws://') + '/ws'
            
            async with websockets.connect(ws_url, timeout=10) as websocket:
                # Send test message
                test_message = {
                    "type": "integration_test",
                    "timestamp": datetime.now().isoformat(),
                    "test_id": f"test_{int(time.time())}"
                }
                
                await websocket.send(json.dumps(test_message))
                
                # Wait for response
                try:
                    response = await asyncio.wait_for(websocket.recv(), timeout=5)
                    response_data = json.loads(response)
                    
                    return IntegrationTestResult(
                        "WebSocket → Event System",
                        ["WebSocket Server", "Event System"],
                        "passed",
                        "WebSocket communication successful",
                        time.time() - start_time,
                        {"response_type": response_data.get("type")}
                    )
                    
                except asyncio.TimeoutError:
                    return IntegrationTestResult(
                        "WebSocket → Event System",
                        ["WebSocket Server", "Event System"],
                        "warning",
                        "WebSocket connected but no response received",
                        time.time() - start_time
                    )
                    
        except Exception as e:
            return IntegrationTestResult(
                "WebSocket → Event System",
                ["WebSocket Server", "Event System"],
                "failed",
                f"WebSocket connection failed: {str(e)}",
                time.time() - start_time
            )
    
    async def test_auth_api_flow(self) -> IntegrationTestResult:
        """Test Authentication → API Authorization flow"""
        start_time = time.time()
        
        try:
            # Test protected endpoint without auth
            protected_response = requests.get(
                f"{self.backend_url}/api/campaigns",
                timeout=5
            )
            
            # Test with demo credentials
            auth_data = {
                "username": "admin",
                "password": "password"
            }
            
            auth_response = requests.post(
                f"{self.backend_url}/api/auth/login",
                json=auth_data,
                timeout=5
            )
            
            if auth_response.status_code == 200:
                token = auth_response.json().get('token')
                
                # Test protected endpoint with auth
                headers = {"Authorization": f"Bearer {token}"}
                authed_response = requests.get(
                    f"{self.backend_url}/api/campaigns",
                    headers=headers,
                    timeout=5
                )
                
                return IntegrationTestResult(
                    "Authentication → API Flow",
                    ["Authentication", "API Server"],
                    "passed",
                    "Authentication and authorization working",
                    time.time() - start_time,
                    {
                        "unauth_status": protected_response.status_code,
                        "auth_status": authed_response.status_code
                    }
                )
            else:
                return IntegrationTestResult(
                    "Authentication → API Flow",
                    ["Authentication", "API Server"],
                    "warning",
                    f"Authentication failed: HTTP {auth_response.status_code}",
                    time.time() - start_time
                )
                
        except Exception as e:
            return IntegrationTestResult(
                "Authentication → API Flow",
                ["Authentication", "API Server"],
                "failed",
                f"Exception: {str(e)}",
                time.time() - start_time
            )
    
    async def test_reporting_data_flow(self) -> IntegrationTestResult:
        """Test Reporting Engine → Data Sources integration"""
        start_time = time.time()
        
        try:
            # Test report generation endpoint
            report_request = {
                "campaign_id": 1,  # Assuming test campaign exists
                "report_type": "technical",
                "format": "json"
            }
            
            report_response = requests.post(
                f"{self.backend_url}/api/reports/generate",
                json=report_request,
                timeout=15
            )
            
            if report_response.status_code in [200, 201, 202]:
                return IntegrationTestResult(
                    "Reporting → Data Sources",
                    ["Reporting Engine", "Database", "AI Services"],
                    "passed",
                    "Report generation successful",
                    time.time() - start_time,
                    {"status_code": report_response.status_code}
                )
            elif report_response.status_code == 404:
                return IntegrationTestResult(
                    "Reporting → Data Sources",
                    ["Reporting Engine", "Database", "AI Services"],
                    "warning",
                    "No data available for reporting",
                    time.time() - start_time
                )
            else:
                return IntegrationTestResult(
                    "Reporting → Data Sources",
                    ["Reporting Engine", "Database", "AI Services"],
                    "failed",
                    f"Report generation failed: HTTP {report_response.status_code}",
                    time.time() - start_time
                )
                
        except Exception as e:
            return IntegrationTestResult(
                "Reporting → Data Sources",
                ["Reporting Engine", "Database", "AI Services"],
                "failed",
                f"Exception: {str(e)}",
                time.time() - start_time
            )

class IntegrationTestingMatrix:
    """🕸️ Comprehensive integration testing matrix"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.backend_url = "http://localhost:8000"
        self.frontend_url = "http://localhost:3000"
        self.results: List[IntegrationTestResult] = []
        self.component_tester = ComponentInteractionTester(self.backend_url, self.project_root)
        
        print(f"{Colors.HEADER}{Colors.BOLD}")
        print("🕸️" * 50)
        print("    INTEGRATION TESTING MATRIX DEBUGGER")
        print("    Comprehensive Component Interaction Validation")
        print("🕸️" * 50)
        print(f"{Colors.ENDC}")
    
    async def run_full_integration_matrix(self) -> Dict[str, Any]:
        """Execute all integration tests in the matrix"""
        print(f"{Colors.OKCYAN}🚀 Starting comprehensive integration testing...{Colors.ENDC}\n")
        
        # Phase 1: Core Infrastructure Tests
        await self._test_infrastructure_integrations()
        
        # Phase 2: API Layer Tests
        await self._test_api_layer_integrations()
        
        # Phase 3: Service Integration Tests
        await self._test_service_integrations()
        
        # Phase 4: Real-time Communication Tests
        await self._test_realtime_integrations()
        
        # Phase 5: Security & Authentication Tests
        await self._test_security_integrations()
        
        # Phase 6: Data Flow Tests
        await self._test_data_flow_integrations()
        
        # Phase 7: Frontend Integration Tests
        await self._test_frontend_integrations()
        
        # Phase 8: Cross-Platform Tests
        await self._test_cross_platform_integrations()
        
        # Generate comprehensive report
        return self._generate_integration_report()
    
    async def _test_infrastructure_integrations(self):
        """Test core infrastructure component integrations"""
        print(f"{Colors.BOLD}🏗️ PHASE 1: INFRASTRUCTURE INTEGRATIONS{Colors.ENDC}")
        
        # Test API → Database
        result = await self.component_tester.test_api_database_flow()
        self.results.append(result)
        self._print_test_result(result)
        
        # Test Database → Configuration
        await self._test_database_config_integration()
        
        # Test Event System → Components
        await self._test_event_system_integration()
    
    async def _test_api_layer_integrations(self):
        """Test API layer integrations"""
        print(f"\n{Colors.BOLD}🔌 PHASE 2: API LAYER INTEGRATIONS{Colors.ENDC}")
        
        # Test REST API endpoints
        await self._test_rest_api_integration()
        
        # Test API → Authentication
        result = await self.component_tester.test_auth_api_flow()
        self.results.append(result)
        self._print_test_result(result)
        
        # Test API → Rate Limiting
        await self._test_api_rate_limiting()
    
    async def _test_service_integrations(self):
        """Test service-to-service integrations"""
        print(f"\n{Colors.BOLD}⚙️ PHASE 3: SERVICE INTEGRATIONS{Colors.ENDC}")
        
        # Test AI → Security Tools
        result = await self.component_tester.test_ai_security_tools_flow()
        self.results.append(result)
        self._print_test_result(result)
        
        # Test Tool Manager → Individual Tools
        await self._test_tool_manager_integration()
        
        # Test Security Tools → Vulnerability Detection
        await self._test_security_vulnerability_flow()
    
    async def _test_realtime_integrations(self):
        """Test real-time communication integrations"""
        print(f"\n{Colors.BOLD}📡 PHASE 4: REAL-TIME INTEGRATIONS{Colors.ENDC}")
        
        # Test WebSocket → Event System
        result = await self.component_tester.test_websocket_event_flow()
        self.results.append(result)
        self._print_test_result(result)
        
        # Test Event Broadcasting
        await self._test_event_broadcasting()
        
        # Test Real-time Updates
        await self._test_realtime_updates()
    
    async def _test_security_integrations(self):
        """Test security component integrations"""
        print(f"\n{Colors.BOLD}🛡️ PHASE 5: SECURITY INTEGRATIONS{Colors.ENDC}")
        
        # Test Authentication → Authorization
        await self._test_auth_authorization_flow()
        
        # Test Security Tools → AI Analysis
        await self._test_security_ai_analysis()
        
        # Test Vulnerability → Remediation Flow
        await self._test_vulnerability_remediation_flow()
    
    async def _test_data_flow_integrations(self):
        """Test data flow integrations"""
        print(f"\n{Colors.BOLD}📊 PHASE 6: DATA FLOW INTEGRATIONS{Colors.ENDC}")
        
        # Test Reporting → Data Sources
        result = await self.component_tester.test_reporting_data_flow()
        self.results.append(result)
        self._print_test_result(result)
        
        # Test Data Export Flow
        await self._test_data_export_flow()
        
        # Test Campaign → Scan → Vulnerability Flow
        await self._test_campaign_scan_vulnerability_flow()
    
    async def _test_frontend_integrations(self):
        """Test frontend integrations"""
        print(f"\n{Colors.BOLD}⚛️ PHASE 7: FRONTEND INTEGRATIONS{Colors.ENDC}")
        
        # Test Frontend → Backend API
        await self._test_frontend_backend_integration()
        
        # Test Tauri → System Integration
        await self._test_tauri_system_integration()
        
        # Test React → State Management
        await self._test_react_state_integration()
    
    async def _test_cross_platform_integrations(self):
        """Test cross-platform integrations"""
        print(f"\n{Colors.BOLD}🌐 PHASE 8: CROSS-PLATFORM INTEGRATIONS{Colors.ENDC}")
        
        # Test WSL → Windows Integration
        await self._test_wsl_windows_integration()
        
        # Test Docker → Host Integration
        await self._test_docker_host_integration()
        
        # Test Path Resolution Across Platforms
        await self._test_cross_platform_paths()
    
    async def _test_database_config_integration(self):
        """Test database configuration integration"""
        start_time = time.time()
        
        try:
            # Check if database exists and has configuration
            db_paths = [
                self.project_root / 'data/nexusscan.db',
                self.project_root / 'nexusscan.db'
            ]
            
            db_path = None
            for path in db_paths:
                if path.exists():
                    db_path = path
                    break
            
            if not db_path:
                result = IntegrationTestResult(
                    "Database → Configuration",
                    ["Database", "Configuration Manager"],
                    "failed",
                    "Database file not found",
                    time.time() - start_time
                )
            else:
                conn = sqlite3.connect(str(db_path))
                cursor = conn.cursor()
                
                # Check for app_config table
                cursor.execute("SELECT COUNT(*) FROM app_config")
                config_count = cursor.fetchone()[0]
                
                conn.close()
                
                if config_count > 0:
                    result = IntegrationTestResult(
                        "Database → Configuration",
                        ["Database", "Configuration Manager"],
                        "passed",
                        f"Configuration loaded: {config_count} settings",
                        time.time() - start_time,
                        {"config_count": config_count}
                    )
                else:
                    result = IntegrationTestResult(
                        "Database → Configuration",
                        ["Database", "Configuration Manager"],
                        "warning",
                        "No configuration data found",
                        time.time() - start_time
                    )
            
        except Exception as e:
            result = IntegrationTestResult(
                "Database → Configuration",
                ["Database", "Configuration Manager"],
                "failed",
                f"Exception: {str(e)}",
                time.time() - start_time
            )
        
        self.results.append(result)
        self._print_test_result(result)
    
    async def _test_event_system_integration(self):
        """Test event system integration"""
        start_time = time.time()
        
        try:
            # Test event system availability via API
            response = requests.get(f"{self.backend_url}/api/events/status", timeout=5)
            
            if response.status_code == 200:
                event_status = response.json()
                result = IntegrationTestResult(
                    "Event System → Components",
                    ["Event System", "All Components"],
                    "passed",
                    "Event system operational",
                    time.time() - start_time,
                    event_status
                )
            elif response.status_code == 404:
                result = IntegrationTestResult(
                    "Event System → Components",
                    ["Event System", "All Components"],
                    "warning",
                    "Event system endpoint not implemented",
                    time.time() - start_time
                )
            else:
                result = IntegrationTestResult(
                    "Event System → Components",
                    ["Event System", "All Components"],
                    "failed",
                    f"Event system error: HTTP {response.status_code}",
                    time.time() - start_time
                )
        except Exception as e:
            result = IntegrationTestResult(
                "Event System → Components",
                ["Event System", "All Components"],
                "failed",
                f"Exception: {str(e)}",
                time.time() - start_time
            )
        
        self.results.append(result)
        self._print_test_result(result)
    
    async def _test_rest_api_integration(self):
        """Test REST API integration"""
        start_time = time.time()
        
        try:
            # Test multiple endpoints
            endpoints = [
                ("/api/health", "Health Check"),
                ("/api/campaigns", "Campaigns"),
                ("/api/scans", "Scans"),
                ("/api/tools/status", "Tools"),
                ("/api/dashboard/metrics", "Dashboard")
            ]
            
            successful_endpoints = 0
            total_endpoints = len(endpoints)
            
            for endpoint, name in endpoints:
                try:
                    response = requests.get(f"{self.backend_url}{endpoint}", timeout=5)
                    if response.status_code in [200, 401]:  # 401 is acceptable for auth-protected endpoints
                        successful_endpoints += 1
                except:
                    pass
            
            if successful_endpoints == total_endpoints:
                status = "passed"
                message = "All API endpoints responding"
            elif successful_endpoints > total_endpoints / 2:
                status = "warning"
                message = f"{successful_endpoints}/{total_endpoints} endpoints responding"
            else:
                status = "failed"
                message = f"Only {successful_endpoints}/{total_endpoints} endpoints responding"
            
            result = IntegrationTestResult(
                "REST API Integration",
                ["API Server", "Backend Services"],
                status,
                message,
                time.time() - start_time,
                {"successful_endpoints": successful_endpoints, "total_endpoints": total_endpoints}
            )
            
        except Exception as e:
            result = IntegrationTestResult(
                "REST API Integration",
                ["API Server", "Backend Services"],
                "failed",
                f"Exception: {str(e)}",
                time.time() - start_time
            )
        
        self.results.append(result)
        self._print_test_result(result)
    
    async def _test_api_rate_limiting(self):
        """Test API rate limiting integration"""
        start_time = time.time()
        
        try:
            # Make rapid requests to test rate limiting
            responses = []
            for i in range(10):
                response = requests.get(f"{self.backend_url}/api/health", timeout=1)
                responses.append(response.status_code)
            
            # Check if any requests were rate limited (429)
            rate_limited = any(status == 429 for status in responses)
            successful = sum(1 for status in responses if status == 200)
            
            if rate_limited:
                status = "passed"
                message = "Rate limiting is active"
            elif successful == len(responses):
                status = "warning"
                message = "No rate limiting detected (may be disabled)"
            else:
                status = "failed"
                message = "API requests failing unexpectedly"
            
            result = IntegrationTestResult(
                "API Rate Limiting",
                ["API Server", "Rate Limiter"],
                status,
                message,
                time.time() - start_time,
                {"successful_requests": successful, "total_requests": len(responses)}
            )
            
        except Exception as e:
            result = IntegrationTestResult(
                "API Rate Limiting",
                ["API Server", "Rate Limiter"],
                "failed",
                f"Exception: {str(e)}",
                time.time() - start_time
            )
        
        self.results.append(result)
        self._print_test_result(result)
    
    async def _test_tool_manager_integration(self):
        """Test tool manager integration with individual tools"""
        start_time = time.time()
        
        try:
            # Check tool manager status
            response = requests.get(f"{self.backend_url}/api/tools/status", timeout=10)
            
            if response.status_code == 200:
                tools_status = response.json()
                tools = tools_status.get('tools', {})
                
                available_tools = sum(1 for tool, status in tools.items() if status == 'available')
                total_tools = len(tools)
                
                if available_tools > 0:
                    status = "passed"
                    message = f"Tool manager coordinating {available_tools}/{total_tools} tools"
                elif total_tools > 0:
                    status = "warning"
                    message = f"Tool manager found {total_tools} tools but none available"
                else:
                    status = "failed"
                    message = "No tools registered with tool manager"
                
                result = IntegrationTestResult(
                    "Tool Manager → Individual Tools",
                    ["Tool Manager", "Security Tools"],
                    status,
                    message,
                    time.time() - start_time,
                    {"available_tools": available_tools, "total_tools": total_tools}
                )
            else:
                result = IntegrationTestResult(
                    "Tool Manager → Individual Tools",
                    ["Tool Manager", "Security Tools"],
                    "failed",
                    f"Tool manager not responding: HTTP {response.status_code}",
                    time.time() - start_time
                )
                
        except Exception as e:
            result = IntegrationTestResult(
                "Tool Manager → Individual Tools",
                ["Tool Manager", "Security Tools"],
                "failed",
                f"Exception: {str(e)}",
                time.time() - start_time
            )
        
        self.results.append(result)
        self._print_test_result(result)
    
    async def _test_security_vulnerability_flow(self):
        """Test security tools to vulnerability detection flow"""
        start_time = time.time()
        
        # This is a placeholder - would require running actual security tools
        result = IntegrationTestResult(
            "Security Tools → Vulnerability Detection",
            ["Security Tools", "Vulnerability Database"],
            "skipped",
            "Requires actual security tool execution",
            time.time() - start_time,
            {"reason": "needs_real_tools"}
        )
        
        self.results.append(result)
        self._print_test_result(result)
    
    async def _test_event_broadcasting(self):
        """Test event broadcasting system"""
        start_time = time.time()
        
        # Placeholder for event broadcasting test
        result = IntegrationTestResult(
            "Event Broadcasting",
            ["Event System", "WebSocket", "Frontend"],
            "skipped",
            "Event broadcasting test requires WebSocket clients",
            time.time() - start_time
        )
        
        self.results.append(result)
        self._print_test_result(result)
    
    async def _test_realtime_updates(self):
        """Test real-time update system"""
        start_time = time.time()
        
        # Placeholder for real-time updates test
        result = IntegrationTestResult(
            "Real-time Updates",
            ["Backend", "WebSocket", "Frontend"],
            "skipped",
            "Real-time updates test requires active frontend",
            time.time() - start_time
        )
        
        self.results.append(result)
        self._print_test_result(result)
    
    async def _test_auth_authorization_flow(self):
        """Test authentication to authorization flow"""
        start_time = time.time()
        
        # This test is covered by the auth_api_flow test
        result = IntegrationTestResult(
            "Authentication → Authorization",
            ["Authentication", "Authorization"],
            "skipped",
            "Covered by Authentication → API Flow test",
            time.time() - start_time
        )
        
        self.results.append(result)
        self._print_test_result(result)
    
    async def _test_security_ai_analysis(self):
        """Test security tools to AI analysis flow"""
        start_time = time.time()
        
        # Placeholder for security AI analysis test
        result = IntegrationTestResult(
            "Security Tools → AI Analysis",
            ["Security Tools", "AI Services"],
            "skipped",
            "Requires vulnerability data for AI analysis",
            time.time() - start_time
        )
        
        self.results.append(result)
        self._print_test_result(result)
    
    async def _test_vulnerability_remediation_flow(self):
        """Test vulnerability to remediation flow"""
        start_time = time.time()
        
        # Placeholder for vulnerability remediation test
        result = IntegrationTestResult(
            "Vulnerability → Remediation",
            ["Vulnerability Database", "AI Services", "Reporting"],
            "skipped",
            "Requires existing vulnerability data",
            time.time() - start_time
        )
        
        self.results.append(result)
        self._print_test_result(result)
    
    async def _test_data_export_flow(self):
        """Test data export flow"""
        start_time = time.time()
        
        try:
            # Test data export endpoint
            export_response = requests.get(f"{self.backend_url}/api/export/campaigns", timeout=10)
            
            if export_response.status_code == 200:
                result = IntegrationTestResult(
                    "Data Export Flow",
                    ["Database", "Export Manager", "File System"],
                    "passed",
                    "Data export working",
                    time.time() - start_time
                )
            elif export_response.status_code == 404:
                result = IntegrationTestResult(
                    "Data Export Flow",
                    ["Database", "Export Manager", "File System"],
                    "warning",
                    "Export endpoint not implemented",
                    time.time() - start_time
                )
            else:
                result = IntegrationTestResult(
                    "Data Export Flow",
                    ["Database", "Export Manager", "File System"],
                    "failed",
                    f"Export failed: HTTP {export_response.status_code}",
                    time.time() - start_time
                )
                
        except Exception as e:
            result = IntegrationTestResult(
                "Data Export Flow",
                ["Database", "Export Manager", "File System"],
                "failed",
                f"Exception: {str(e)}",
                time.time() - start_time
            )
        
        self.results.append(result)
        self._print_test_result(result)
    
    async def _test_campaign_scan_vulnerability_flow(self):
        """Test full campaign → scan → vulnerability flow"""
        start_time = time.time()
        
        # This would require a full end-to-end test
        result = IntegrationTestResult(
            "Campaign → Scan → Vulnerability Flow",
            ["Campaign Manager", "Scan Engine", "Vulnerability Database"],
            "skipped",
            "Requires full end-to-end security scan",
            time.time() - start_time
        )
        
        self.results.append(result)
        self._print_test_result(result)
    
    async def _test_frontend_backend_integration(self):
        """Test frontend to backend integration"""
        start_time = time.time()
        
        try:
            # Check if frontend is running
            frontend_response = requests.get(self.frontend_url, timeout=5)
            frontend_running = frontend_response.status_code == 200
            
            # Check if backend is running
            backend_response = requests.get(f"{self.backend_url}/api/health", timeout=5)
            backend_running = backend_response.status_code == 200
            
            if frontend_running and backend_running:
                status = "passed"
                message = "Both frontend and backend running"
            elif backend_running:
                status = "warning"
                message = "Backend running, frontend not accessible"
            elif frontend_running:
                status = "warning"
                message = "Frontend running, backend not accessible"
            else:
                status = "failed"
                message = "Neither frontend nor backend accessible"
            
            result = IntegrationTestResult(
                "Frontend ↔ Backend",
                ["React Frontend", "Python Backend"],
                status,
                message,
                time.time() - start_time,
                {"frontend_running": frontend_running, "backend_running": backend_running}
            )
            
        except Exception as e:
            result = IntegrationTestResult(
                "Frontend ↔ Backend",
                ["React Frontend", "Python Backend"],
                "failed",
                f"Exception: {str(e)}",
                time.time() - start_time
            )
        
        self.results.append(result)
        self._print_test_result(result)
    
    async def _test_tauri_system_integration(self):
        """Test Tauri system integration"""
        start_time = time.time()
        
        # Check for Tauri configuration
        tauri_config = self.project_root / 'frontend/src-tauri/tauri.conf.json'
        
        if tauri_config.exists():
            result = IntegrationTestResult(
                "Tauri → System Integration",
                ["Tauri Desktop", "Operating System"],
                "passed",
                "Tauri configuration found",
                time.time() - start_time,
                {"config_path": str(tauri_config)}
            )
        else:
            result = IntegrationTestResult(
                "Tauri → System Integration",
                ["Tauri Desktop", "Operating System"],
                "failed",
                "Tauri configuration not found",
                time.time() - start_time
            )
        
        self.results.append(result)
        self._print_test_result(result)
    
    async def _test_react_state_integration(self):
        """Test React state management integration"""
        start_time = time.time()
        
        # Check for state management files
        state_files = [
            self.project_root / 'frontend/src/stores/app-store.ts',
            self.project_root / 'frontend/src/stores/auth-store.ts'
        ]
        
        existing_files = [f for f in state_files if f.exists()]
        
        if existing_files:
            result = IntegrationTestResult(
                "React → State Management",
                ["React Components", "Zustand Stores"],
                "passed",
                f"State management files found: {len(existing_files)}",
                time.time() - start_time,
                {"state_files": len(existing_files)}
            )
        else:
            result = IntegrationTestResult(
                "React → State Management",
                ["React Components", "Zustand Stores"],
                "warning",
                "No state management files found",
                time.time() - start_time
            )
        
        self.results.append(result)
        self._print_test_result(result)
    
    async def _test_wsl_windows_integration(self):
        """Test WSL Windows integration"""
        start_time = time.time()
        
        import platform
        
        if platform.system() == "Windows":
            try:
                # Test WSL availability
                result = subprocess.run(['wsl', '--status'], 
                                      capture_output=True, text=True, timeout=5)
                
                if result.returncode == 0:
                    status = "passed"
                    message = "WSL integration available"
                else:
                    status = "warning"
                    message = "WSL not available or not configured"
                
                integration_result = IntegrationTestResult(
                    "WSL ↔ Windows Integration",
                    ["WSL Subsystem", "Windows Host"],
                    status,
                    message,
                    time.time() - start_time
                )
                
            except Exception as e:
                integration_result = IntegrationTestResult(
                    "WSL ↔ Windows Integration",
                    ["WSL Subsystem", "Windows Host"],
                    "failed",
                    f"WSL test failed: {str(e)}",
                    time.time() - start_time
                )
        else:
            integration_result = IntegrationTestResult(
                "WSL ↔ Windows Integration",
                ["WSL Subsystem", "Windows Host"],
                "skipped",
                f"Not on Windows (running on {platform.system()})",
                time.time() - start_time
            )
        
        self.results.append(integration_result)
        self._print_test_result(integration_result)
    
    async def _test_docker_host_integration(self):
        """Test Docker host integration"""
        start_time = time.time()
        
        try:
            # Test Docker availability
            result = subprocess.run(['docker', '--version'], 
                                  capture_output=True, text=True, timeout=5)
            
            if result.returncode == 0:
                status = "passed"
                message = "Docker integration available"
            else:
                status = "warning"
                message = "Docker not available"
            
            integration_result = IntegrationTestResult(
                "Docker ↔ Host Integration",
                ["Docker Container", "Host System"],
                status,
                message,
                time.time() - start_time
            )
            
        except Exception as e:
            integration_result = IntegrationTestResult(
                "Docker ↔ Host Integration",
                ["Docker Container", "Host System"],
                "failed",
                f"Docker test failed: {str(e)}",
                time.time() - start_time
            )
        
        self.results.append(integration_result)
        self._print_test_result(integration_result)
    
    async def _test_cross_platform_paths(self):
        """Test cross-platform path resolution"""
        start_time = time.time()
        
        try:
            # Test path resolution for key directories
            test_paths = [
                self.project_root / 'src',
                self.project_root / 'frontend',
                self.project_root / 'data',
                self.project_root / 'logs'
            ]
            
            accessible_paths = [p for p in test_paths if p.exists()]
            
            if len(accessible_paths) == len(test_paths):
                status = "passed"
                message = "All paths accessible"
            elif len(accessible_paths) > len(test_paths) / 2:
                status = "warning"
                message = f"{len(accessible_paths)}/{len(test_paths)} paths accessible"
            else:
                status = "failed"
                message = f"Only {len(accessible_paths)}/{len(test_paths)} paths accessible"
            
            result = IntegrationTestResult(
                "Cross-Platform Path Resolution",
                ["File System", "Path Handler"],
                status,
                message,
                time.time() - start_time,
                {"accessible_paths": len(accessible_paths), "total_paths": len(test_paths)}
            )
            
        except Exception as e:
            result = IntegrationTestResult(
                "Cross-Platform Path Resolution",
                ["File System", "Path Handler"],
                "failed",
                f"Exception: {str(e)}",
                time.time() - start_time
            )
        
        self.results.append(result)
        self._print_test_result(result)
    
    def _print_test_result(self, result: IntegrationTestResult):
        """Print test result with color coding"""
        status_colors = {
            'passed': Colors.OKGREEN,
            'failed': Colors.FAIL,
            'warning': Colors.WARNING,
            'skipped': Colors.OKCYAN
        }
        
        status_symbols = {
            'passed': '✅',
            'failed': '❌',
            'warning': '⚠️',
            'skipped': '➡️'
        }
        
        color = status_colors.get(result.status, Colors.ENDC)
        symbol = status_symbols.get(result.status, '?')
        
        print(f"  {symbol} {color}{result.test_name:.<40} {result.message} ({result.duration:.2f}s){Colors.ENDC}")
    
    def _generate_integration_report(self) -> Dict[str, Any]:
        """Generate comprehensive integration testing report"""
        print(f"\n{Colors.HEADER}{Colors.BOLD}")
        print("🕸️" * 50)
        print("           INTEGRATION TESTING REPORT")
        print("🕸️" * 50)
        print(f"{Colors.ENDC}")
        
        # Calculate statistics
        total_tests = len(self.results)
        passed_tests = len([r for r in self.results if r.status == 'passed'])
        failed_tests = len([r for r in self.results if r.status == 'failed'])
        warning_tests = len([r for r in self.results if r.status == 'warning'])
        skipped_tests = len([r for r in self.results if r.status == 'skipped'])
        
        total_duration = sum(r.duration for r in self.results)
        
        # Calculate integration health score
        # Weight: passed=1, warning=0.5, failed=0, skipped=0.3
        weighted_score = (passed_tests * 1.0 + warning_tests * 0.5 + skipped_tests * 0.3)
        integration_health = (weighted_score / max(total_tests, 1)) * 100
        
        print(f"\n{Colors.BOLD}🕸️ INTEGRATION HEALTH SCORE: {integration_health:.1f}%{Colors.ENDC}")
        print(f"{Colors.BOLD}⏱️  TOTAL TEST DURATION: {total_duration:.2f} seconds{Colors.ENDC}")
        print(f"{Colors.BOLD}🔬 TOTAL INTEGRATION TESTS: {total_tests}{Colors.ENDC}\n")
        
        print(f"{Colors.OKGREEN}✅ Passed: {passed_tests}{Colors.ENDC}")
        print(f"{Colors.WARNING}⚠️  Warnings: {warning_tests}{Colors.ENDC}")
        print(f"{Colors.FAIL}❌ Failed: {failed_tests}{Colors.ENDC}")
        print(f"{Colors.OKCYAN}➡️  Skipped: {skipped_tests}{Colors.ENDC}")
        
        # Show critical failures
        critical_failures = [r for r in self.results if r.status == 'failed']
        if critical_failures:
            print(f"\n{Colors.FAIL}{Colors.BOLD}🚨 CRITICAL INTEGRATION FAILURES:{Colors.ENDC}")
            for failure in critical_failures:
                print(f"  {Colors.FAIL}❌ {failure.test_name}: {failure.message}{Colors.ENDC}")
        
        # Show warnings
        warnings = [r for r in self.results if r.status == 'warning']
        if warnings:
            print(f"\n{Colors.WARNING}{Colors.BOLD}⚠️  INTEGRATION WARNINGS:{Colors.ENDC}")
            for warning in warnings:
                print(f"  {Colors.WARNING}⚠️  {warning.test_name}: {warning.message}{Colors.ENDC}")
        
        # Component interaction matrix
        print(f"\n{Colors.BOLD}🕸️ COMPONENT INTERACTION MATRIX:{Colors.ENDC}")
        component_pairs = set()
        for result in self.results:
            for i, comp1 in enumerate(result.components):
                for comp2 in result.components[i+1:]:
                    component_pairs.add((comp1, comp2))
        
        print(f"   Tested interactions: {len(component_pairs)}")
        for comp1, comp2 in sorted(component_pairs):
            print(f"   • {comp1} ↔ {comp2}")
        
        print(f"\n{Colors.HEADER}🕸️ Integration testing matrix complete! 🕸️{Colors.ENDC}")
        
        # Generate detailed report
        report = {
            'timestamp': datetime.now().isoformat(),
            'total_tests': total_tests,
            'passed_tests': passed_tests,
            'failed_tests': failed_tests,
            'warning_tests': warning_tests,
            'skipped_tests': skipped_tests,
            'integration_health_score': integration_health,
            'total_duration_seconds': total_duration,
            'component_interactions': len(component_pairs),
            'test_results': [
                {
                    'test_name': r.test_name,
                    'components': r.components,
                    'status': r.status,
                    'message': r.message,
                    'duration': r.duration,
                    'details': r.details,
                    'timestamp': r.timestamp
                }
                for r in self.results
            ]
        }
        
        # Save report
        report_file = self.project_root / f"integration_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w') as f:
            json.dump(report, f, indent=2, default=str)
        
        print(f"\n📋 Detailed report saved to: {report_file}")
        
        return report

async def main():
    """Main entry point"""
    tester = IntegrationTestingMatrix()
    report = await tester.run_full_integration_matrix()
    return report

if __name__ == "__main__":
    asyncio.run(main())