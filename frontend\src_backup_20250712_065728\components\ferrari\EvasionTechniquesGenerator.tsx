/**
 * Evasion Techniques Generator Component
 * Advanced WAF, IPS, and security control bypass generation
 */
import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { 
  Shield,
  Play,
  Square,
  Download,
  Settings,
  Target,
  Globe,
  FileText,
  Terminal,
  Copy,
  RotateCcw,
  Clock,
  Zap,
  Filter,
  Eye,
  AlertTriangle,
  CheckCircle,
  TrendingUp,
  Activity,
  Lock,
  Search,
  List,
  BarChart3,
  Database,
  Server,
  Bug,
  Info,
  Code,
  Package,
  Layers,
  Crosshair,
  Cpu,
  Network,
  Gauge,
  Tag,
  Hash,
  Workflow,
  GitBranch,
  Book,
  Users,
  Table,
  Binary,
  Braces,
  FileKey,
  History,
  Folder,
  Star,
  Crown,
  Award,
  Timer,
  FastForward,
  SkipForward,
  RefreshCw,
  ExternalLink,
  FileCheck,
  FileWarning,
  Fingerprint,
  Key as KeyIcon,
  LockOpen,
  ShieldOff,
  Verified,
  ScanLine,
  Scan,
  FileJson,
  FileCode,
  Brain,
  Lightbulb,
  ShieldAlert,
  ShieldCheck,
  ShieldX,
  Skull,
  Crosshair as CrosshairIcon,
  Flame,
  Lightning,
  Atom,
  Dna,
  Beaker,
  FlaskConical,
  TestTube,
  Microscope,
  Rocket,
  Wand2,
  Sparkles,
  Plus,
  Minus,
  Edit,
  Trash2,
  Save,
  Upload,
  FileUp,
  FolderOpen,
  Maximize2,
  Minimize2,
  MoreHorizontal,
  Move,
  Shuffle,
  RotateCw,
  Repeat,
  PlayCircle,
  StopCircle,
  PauseCircle,
  ArrowRight,
  ArrowDown,
  ChevronRight,
  ChevronDown
} from 'lucide-react';

interface EvasionTechnique {
  id: string;
  name: string;
  description: string;
  category: 'waf_bypass' | 'ips_evasion' | 'signature_breaking' | 'encoding' | 'obfuscation' | 'protocol_manipulation';
  target_systems: string[];
  technique_type: 'payload_modification' | 'request_splitting' | 'encoding_chain' | 'timing_manipulation' | 'protocol_abuse';
  payload_example: string;
  effectiveness_score: number;
  detection_probability: number;
  complexity: 'low' | 'medium' | 'high' | 'extreme';
  vendor_specific: boolean;
  target_vendors: string[];
  ai_generated: boolean;
  created_at: string;
  test_results?: TestResult[];
  educational_notes: string;
  countermeasures: string[];
}

interface TestResult {
  waf_vendor: string;
  bypass_success: boolean;
  detection_rate: number;
  response_analysis: string;
  false_positive_rate: number;
}

interface SecurityControl {
  id: string;
  name: string;
  vendor: string;
  type: 'waf' | 'ips' | 'antivirus' | 'dlp' | 'sandbox' | 'behavioral_analysis';
  version: string;
  known_bypasses: string[];
  signature_patterns: string[];
  behavioral_rules: string[];
  effectiveness_rating: number;
  last_updated: string;
}

interface EncodingChain {
  id: string;
  name: string;
  description: string;
  steps: EncodingStep[];
  effectiveness: number;
  complexity: number;
  reversibility: boolean;
}

interface EncodingStep {
  id: string;
  name: string;
  type: 'url_encode' | 'base64' | 'hex_encode' | 'unicode_escape' | 'html_entity' | 'double_encode' | 'custom';
  parameters: Record<string, any>;
  order: number;
}

// Predefined security controls database
const SECURITY_CONTROLS: SecurityControl[] = [
  {
    id: 'cloudflare_waf',
    name: 'Cloudflare WAF',
    vendor: 'Cloudflare',
    type: 'waf',
    version: '2025.1',
    known_bypasses: ['Double encoding', 'Case variation', 'Comment injection'],
    signature_patterns: ['union.*select', 'script.*alert', 'eval.*function'],
    behavioral_rules: ['Rapid requests', 'Unusual user agents', 'Geographic anomalies'],
    effectiveness_rating: 85,
    last_updated: '2025-07-10'
  },
  {
    id: 'aws_waf',
    name: 'AWS WAF',
    vendor: 'Amazon',
    type: 'waf',
    version: 'v2',
    known_bypasses: ['Unicode normalization', 'Path traversal', 'Protocol downgrade'],
    signature_patterns: ['<script', 'javascript:', 'expression('],
    behavioral_rules: ['Request rate limiting', 'IP reputation', 'Bot detection'],
    effectiveness_rating: 82,
    last_updated: '2025-07-08'
  },
  {
    id: 'modsecurity',
    name: 'ModSecurity',
    vendor: 'Trustwave',
    type: 'waf',
    version: '3.0.8',
    known_bypasses: ['Rule chaining', 'Variable confusion', 'Encoding variations'],
    signature_patterns: ['(?i)union.*select', '(?i)script.*>.*alert', '(?i)eval.*\\('],
    behavioral_rules: ['Anomaly scoring', 'Request correlation', 'Pattern matching'],
    effectiveness_rating: 78,
    last_updated: '2025-07-05'
  }
];

// Evasion technique templates
const EVASION_TEMPLATES: Partial<EvasionTechnique>[] = [
  {
    name: 'Unicode Normalization Bypass',
    description: 'Exploits Unicode normalization differences between WAF and backend',
    category: 'waf_bypass',
    technique_type: 'encoding_chain',
    payload_example: 'uni＼on sel＼ect 1,2,3',
    effectiveness_score: 75,
    detection_probability: 25,
    complexity: 'medium',
    target_vendors: ['Cloudflare', 'AWS WAF', 'Azure WAF'],
    educational_notes: 'Uses Unicode fullwidth characters that normalize to ASCII in backend but may bypass WAF parsing',
    countermeasures: ['Unicode normalization in WAF', 'Character filtering', 'Strict encoding validation']
  },
  {
    name: 'HTTP Parameter Pollution',
    description: 'Exploits differences in parameter parsing between WAF and application',
    category: 'waf_bypass',
    technique_type: 'protocol_abuse',
    payload_example: 'param=safe&param=<script>alert(1)</script>',
    effectiveness_score: 80,
    detection_probability: 20,
    complexity: 'low',
    target_vendors: ['ModSecurity', 'F5 BIG-IP', 'Imperva'],
    educational_notes: 'Different systems may parse duplicate parameters differently, leading to bypasses',
    countermeasures: ['Consistent parameter parsing', 'Parameter validation', 'Request normalization']
  },
  {
    name: 'Protocol Downgrade Attack',
    description: 'Forces communication over less secure protocols to bypass controls',
    category: 'ips_evasion',
    technique_type: 'protocol_manipulation',
    payload_example: 'HTTP/1.0 request with malicious payload',
    effectiveness_score: 65,
    detection_probability: 35,
    complexity: 'high',
    target_vendors: ['Snort', 'Suricata', 'Cisco ASA'],
    educational_notes: 'Older protocol versions may have fewer security controls or different parsing logic',
    countermeasures: ['Protocol enforcement', 'Version validation', 'Deep packet inspection']
  }
];

// Encoding chains for obfuscation
const ENCODING_CHAINS: EncodingChain[] = [
  {
    id: 'double_url_encode',
    name: 'Double URL Encoding',
    description: 'Apply URL encoding twice to bypass simple decoders',
    steps: [
      { id: '1', name: 'URL Encode', type: 'url_encode', parameters: {}, order: 1 },
      { id: '2', name: 'URL Encode Again', type: 'url_encode', parameters: {}, order: 2 }
    ],
    effectiveness: 70,
    complexity: 30,
    reversibility: true
  },
  {
    id: 'mixed_encoding_chain',
    name: 'Mixed Encoding Chain',
    description: 'Combine multiple encoding types for maximum obfuscation',
    steps: [
      { id: '1', name: 'Unicode Escape', type: 'unicode_escape', parameters: {}, order: 1 },
      { id: '2', name: 'Base64 Encode', type: 'base64', parameters: {}, order: 2 },
      { id: '3', name: 'URL Encode', type: 'url_encode', parameters: {}, order: 3 }
    ],
    effectiveness: 85,
    complexity: 75,
    reversibility: true
  }
];

export const EvasionTechniquesGenerator: React.FC = () => {
  const [activeTab, setActiveTab] = useState('generator');
  const [techniques, setTechniques] = useState<EvasionTechnique[]>([]);
  const [selectedTechnique, setSelectedTechnique] = useState<EvasionTechnique | null>(null);
  const [isGenerating, setIsGenerating] = useState(false);
  const [generationProgress, setGenerationProgress] = useState(0);
  
  // Generator configuration
  const [targetWAF, setTargetWAF] = useState('');
  const [payloadType, setPayloadType] = useState('');
  const [evasionCategory, setEvasionCategory] = useState('');
  const [complexityLevel, setComplexityLevel] = useState('medium');
  const [targetDescription, setTargetDescription] = useState('');
  const [aiModel, setAiModel] = useState('gpt-4');

  // Testing state
  const [testPayload, setTestPayload] = useState('');
  const [testResults, setTestResults] = useState<TestResult[]>([]);
  const [isTesting, setIsTesting] = useState(false);

  useEffect(() => {
    loadSavedTechniques();
  }, []);

  const loadSavedTechniques = () => {
    const saved = localStorage.getItem('nexusscan_evasion_techniques');
    if (saved) {
      setTechniques(JSON.parse(saved));
    }
  };

  const saveTechniques = (newTechniques: EvasionTechnique[]) => {
    localStorage.setItem('nexusscan_evasion_techniques', JSON.stringify(newTechniques));
    setTechniques(newTechniques);
  };

  const generateEvasionTechniques = async () => {
    if (!targetDescription) {
      alert('Please provide a target description');
      return;
    }

    setIsGenerating(true);
    setGenerationProgress(0);

    try {
      const response = await fetch('http://ec2-3-89-91-209.compute-1.amazonaws.com:8000/api/ai/evasion-techniques/generate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          target_waf: targetWAF,
          payload_type: payloadType,
          evasion_category: evasionCategory,
          complexity_level: complexityLevel,
          target_description: targetDescription,
          ai_model: aiModel,
          count: 5,
          include_vendor_specific: true,
          include_educational: true
        })
      });

      if (response.ok) {
        // Simulate progressive generation
        const progressInterval = setInterval(() => {
          setGenerationProgress(prev => {
            if (prev >= 100) {
              clearInterval(progressInterval);
              return 100;
            }
            return prev + 10;
          });
        }, 500);

        const data = await response.json();
        const newTechniques: EvasionTechnique[] = data.techniques.map((t: any, index: number) => ({
          id: Date.now() + index,
          name: t.name || `Generated Technique ${index + 1}`,
          description: t.description,
          category: t.category,
          target_systems: t.target_systems || [],
          technique_type: t.technique_type,
          payload_example: t.payload_example,
          effectiveness_score: t.effectiveness_score || 0,
          detection_probability: t.detection_probability || 0,
          complexity: t.complexity || 'medium',
          vendor_specific: t.vendor_specific || false,
          target_vendors: t.target_vendors || [],
          ai_generated: true,
          created_at: new Date().toISOString(),
          educational_notes: t.educational_notes || '',
          countermeasures: t.countermeasures || []
        }));

        const updatedTechniques = [...techniques, ...newTechniques];
        saveTechniques(updatedTechniques);
        
        if (newTechniques.length > 0) {
          setSelectedTechnique(newTechniques[0]);
        }
      }
    } catch (error) {
      console.error('Failed to generate evasion techniques:', error);
    } finally {
      setTimeout(() => {
        setIsGenerating(false);
        setGenerationProgress(0);
      }, 3000);
    }
  };

  const testTechnique = async (technique: EvasionTechnique) => {
    setIsTesting(true);
    
    try {
      const response = await fetch('http://ec2-3-89-91-209.compute-1.amazonaws.com:8000/api/ai/evasion-techniques/test', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          technique: technique,
          test_environments: ['cloudflare', 'modsecurity', 'aws_waf'],
          payload: technique.payload_example
        })
      });

      if (response.ok) {
        const results = await response.json();
        const updatedTechnique = { ...technique, test_results: results.test_results };
        
        const updatedTechniques = techniques.map(t => 
          t.id === technique.id ? updatedTechnique : t
        );
        saveTechniques(updatedTechniques);
        setSelectedTechnique(updatedTechnique);
      }
    } catch (error) {
      console.error('Failed to test technique:', error);
    } finally {
      setIsTesting(false);
    }
  };

  const createTechniqueFromTemplate = (template: Partial<EvasionTechnique>) => {
    const newTechnique: EvasionTechnique = {
      id: Date.now().toString(),
      name: template.name || 'Custom Technique',
      description: template.description || '',
      category: template.category || 'waf_bypass',
      target_systems: template.target_systems || [],
      technique_type: template.technique_type || 'payload_modification',
      payload_example: template.payload_example || '',
      effectiveness_score: template.effectiveness_score || 0,
      detection_probability: template.detection_probability || 0,
      complexity: template.complexity || 'medium',
      vendor_specific: template.vendor_specific || false,
      target_vendors: template.target_vendors || [],
      ai_generated: false,
      created_at: new Date().toISOString(),
      educational_notes: template.educational_notes || '',
      countermeasures: template.countermeasures || []
    };

    const updatedTechniques = [...techniques, newTechnique];
    saveTechniques(updatedTechniques);
    setSelectedTechnique(newTechnique);
  };

  const exportTechnique = (technique: EvasionTechnique) => {
    const exportData = {
      technique: technique,
      security_controls: SECURITY_CONTROLS.filter(sc => 
        technique.target_vendors.includes(sc.vendor)
      ),
      encoding_chains: ENCODING_CHAINS,
      exported_at: new Date().toISOString()
    };

    const dataStr = JSON.stringify(exportData, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `evasion_technique_${technique.name.replace(/\s+/g, '_')}.json`;
    link.click();
  };

  const getComplexityColor = (complexity: string) => {
    switch (complexity) {
      case 'low': return 'text-green-600 bg-green-50 border-green-200';
      case 'medium': return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'high': return 'text-orange-600 bg-orange-50 border-orange-200';
      case 'extreme': return 'text-red-600 bg-red-50 border-red-200';
      default: return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'waf_bypass': return ShieldOff;
      case 'ips_evasion': return ShieldAlert;
      case 'signature_breaking': return ShieldX;
      case 'encoding': return Code;
      case 'obfuscation': return Shuffle;
      case 'protocol_manipulation': return Network;
      default: return Shield;
    }
  };

  const getEffectivenessColor = (score: number) => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-600';
    if (score >= 40) return 'text-orange-600';
    return 'text-red-600';
  };

  return (
    <div className="space-y-6">
      {/* Header Actions */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button 
            onClick={generateEvasionTechniques} 
            disabled={isGenerating}
            className="bg-red-600 hover:bg-red-700"
          >
            <Brain className="h-4 w-4 mr-2" />
            {isGenerating ? 'Generating...' : 'Generate Techniques'}
          </Button>
          {selectedTechnique && (
            <Button 
              onClick={() => testTechnique(selectedTechnique)} 
              disabled={isTesting}
              variant="outline"
            >
              <TestTube className="h-4 w-4 mr-2" />
              {isTesting ? 'Testing...' : 'Test Technique'}
            </Button>
          )}
        </div>
        
        {isGenerating && (
          <div className="flex items-center space-x-3">
            <div className="text-sm text-gray-600">Generating: {generationProgress}%</div>
            <Progress value={generationProgress} className="w-32" />
          </div>
        )}
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-6">
          <TabsTrigger value="generator">AI Generator</TabsTrigger>
          <TabsTrigger value="techniques">Technique Library</TabsTrigger>
          <TabsTrigger value="controls">Security Controls</TabsTrigger>
          <TabsTrigger value="testing">Testing Lab</TabsTrigger>
          <TabsTrigger value="encoding">Encoding Chains</TabsTrigger>
          <TabsTrigger value="education">Education</TabsTrigger>
        </TabsList>

        {/* AI Generator Tab */}
        <TabsContent value="generator" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Brain className="h-5 w-5" />
                <span>AI Evasion Technique Generator</span>
              </CardTitle>
              <CardDescription>Generate advanced evasion techniques using AI models</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Target WAF/Security Control</label>
                    <Select value={targetWAF} onValueChange={setTargetWAF}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select target" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="">Auto-detect</SelectItem>
                        <SelectItem value="cloudflare">Cloudflare WAF</SelectItem>
                        <SelectItem value="aws_waf">AWS WAF</SelectItem>
                        <SelectItem value="modsecurity">ModSecurity</SelectItem>
                        <SelectItem value="f5_asm">F5 ASM</SelectItem>
                        <SelectItem value="imperva">Imperva WAF</SelectItem>
                        <SelectItem value="barracuda">Barracuda WAF</SelectItem>
                        <SelectItem value="akamai">Akamai Kona</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <label className="text-sm font-medium">Payload Type</label>
                    <Select value={payloadType} onValueChange={setPayloadType}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select payload type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="">Auto-detect</SelectItem>
                        <SelectItem value="sql_injection">SQL Injection</SelectItem>
                        <SelectItem value="xss">Cross-Site Scripting</SelectItem>
                        <SelectItem value="command_injection">Command Injection</SelectItem>
                        <SelectItem value="path_traversal">Path Traversal</SelectItem>
                        <SelectItem value="xxe">XML External Entity</SelectItem>
                        <SelectItem value="ssrf">Server-Side Request Forgery</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <label className="text-sm font-medium">Evasion Category</label>
                    <Select value={evasionCategory} onValueChange={setEvasionCategory}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select category" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="">All Categories</SelectItem>
                        <SelectItem value="waf_bypass">WAF Bypass</SelectItem>
                        <SelectItem value="ips_evasion">IPS Evasion</SelectItem>
                        <SelectItem value="signature_breaking">Signature Breaking</SelectItem>
                        <SelectItem value="encoding">Encoding Techniques</SelectItem>
                        <SelectItem value="obfuscation">Obfuscation Methods</SelectItem>
                        <SelectItem value="protocol_manipulation">Protocol Manipulation</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <label className="text-sm font-medium">Complexity Level</label>
                    <Select value={complexityLevel} onValueChange={setComplexityLevel}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="low">Low - Simple techniques</SelectItem>
                        <SelectItem value="medium">Medium - Moderate complexity</SelectItem>
                        <SelectItem value="high">High - Advanced techniques</SelectItem>
                        <SelectItem value="extreme">Extreme - Research-level</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="space-y-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Target Description</label>
                    <Textarea
                      placeholder="Describe the target security controls, known signatures, or specific challenges..."
                      value={targetDescription}
                      onChange={(e) => setTargetDescription(e.target.value)}
                      rows={8}
                    />
                  </div>

                  <div className="space-y-2">
                    <label className="text-sm font-medium">AI Model</label>
                    <Select value={aiModel} onValueChange={setAiModel}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="gpt-4">GPT-4 (Creative)</SelectItem>
                        <SelectItem value="deepseek-v3">DeepSeek-V3 (Analytical)</SelectItem>
                        <SelectItem value="claude-4">Claude-4 (Balanced)</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>

              {/* Quick Templates */}
              <div className="space-y-4 pt-4 border-t">
                <h3 className="text-lg font-semibold">Quick Templates</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {EVASION_TEMPLATES.map((template, index) => (
                    <Card key={index} 
                          className="p-4 cursor-pointer hover:shadow-md transition-shadow"
                          onClick={() => createTechniqueFromTemplate(template)}>
                      <div className="space-y-2">
                        <h4 className="font-semibold">{template.name}</h4>
                        <p className="text-sm text-gray-600">{template.description}</p>
                        <div className="flex items-center justify-between">
                          <Badge variant="outline" className="text-xs capitalize">
                            {template.category?.replace('_', ' ')}
                          </Badge>
                          <Badge className={getComplexityColor(template.complexity || 'medium')} variant="outline">
                            {template.complexity}
                          </Badge>
                        </div>
                      </div>
                    </Card>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Technique Library Tab */}
        <TabsContent value="techniques" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Shield className="h-5 w-5" />
                <span>Evasion Technique Library</span>
              </CardTitle>
              <CardDescription>Collection of generated and curated evasion techniques</CardDescription>
            </CardHeader>
            <CardContent>
              {techniques.length > 0 ? (
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                  {techniques.map((technique) => {
                    const CategoryIcon = getCategoryIcon(technique.category);
                    return (
                      <Card key={technique.id} 
                            className={`p-4 cursor-pointer transition-all hover:shadow-md ${
                              selectedTechnique?.id === technique.id ? 'ring-2 ring-red-500' : ''
                            }`}
                            onClick={() => setSelectedTechnique(technique)}>
                        <div className="space-y-4">
                          <div className="flex items-start justify-between">
                            <div className="flex items-start space-x-3">
                              <div className="p-2 bg-red-100 rounded-lg">
                                <CategoryIcon className="h-5 w-5 text-red-600" />
                              </div>
                              <div>
                                <h4 className="font-semibold">{technique.name}</h4>
                                <p className="text-sm text-gray-600">{technique.description}</p>
                              </div>
                            </div>
                            <div className="flex flex-col items-end space-y-1">
                              <Badge className={getComplexityColor(technique.complexity)} variant="outline">
                                {technique.complexity}
                              </Badge>
                              {technique.ai_generated && (
                                <Badge variant="secondary" className="text-xs">
                                  AI Generated
                                </Badge>
                              )}
                            </div>
                          </div>

                          <div className="space-y-2">
                            <div className="p-3 bg-gray-50 rounded-lg font-mono text-sm">
                              {technique.payload_example}
                            </div>
                            
                            <div className="grid grid-cols-2 gap-4 text-sm">
                              <div>
                                <span className="text-gray-500">Effectiveness:</span>
                                <span className={`ml-2 font-medium ${getEffectivenessColor(technique.effectiveness_score)}`}>
                                  {technique.effectiveness_score}%
                                </span>
                              </div>
                              <div>
                                <span className="text-gray-500">Detection Risk:</span>
                                <span className="ml-2 font-medium text-red-600">
                                  {technique.detection_probability}%
                                </span>
                              </div>
                            </div>

                            <div className="flex items-center justify-between">
                              <div className="flex flex-wrap gap-1">
                                {technique.target_vendors.slice(0, 2).map((vendor) => (
                                  <Badge key={vendor} variant="outline" className="text-xs">
                                    {vendor}
                                  </Badge>
                                ))}
                                {technique.target_vendors.length > 2 && (
                                  <Badge variant="outline" className="text-xs">
                                    +{technique.target_vendors.length - 2} more
                                  </Badge>
                                )}
                              </div>
                              <div className="flex space-x-2">
                                <Button size="sm" variant="outline" onClick={(e) => {
                                  e.stopPropagation();
                                  testTechnique(technique);
                                }}>
                                  <TestTube className="h-4 w-4" />
                                </Button>
                                <Button size="sm" variant="outline" onClick={(e) => {
                                  e.stopPropagation();
                                  navigator.clipboard.writeText(technique.payload_example);
                                }}>
                                  <Copy className="h-4 w-4" />
                                </Button>
                                <Button size="sm" variant="outline" onClick={(e) => {
                                  e.stopPropagation();
                                  exportTechnique(technique);
                                }}>
                                  <Download className="h-4 w-4" />
                                </Button>
                              </div>
                            </div>
                          </div>
                        </div>
                      </Card>
                    );
                  })}
                </div>
              ) : (
                <div className="text-center py-12">
                  <Shield className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">No Techniques Generated</h3>
                  <p className="text-gray-600 mb-4">Generate your first AI-powered evasion techniques</p>
                  <Button onClick={() => setActiveTab('generator')} className="bg-red-600 hover:bg-red-700">
                    <Brain className="h-4 w-4 mr-2" />
                    Generate Techniques
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Security Controls Tab */}
        <TabsContent value="controls" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <ShieldCheck className="h-5 w-5" />
                <span>Security Controls Database</span>
              </CardTitle>
              <CardDescription>Known security controls and their bypass techniques</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {SECURITY_CONTROLS.map((control) => (
                  <Card key={control.id} className="p-4">
                    <div className="space-y-4">
                      <div className="flex items-start justify-between">
                        <div>
                          <h4 className="font-semibold">{control.name}</h4>
                          <div className="flex items-center space-x-3 mt-1 text-sm text-gray-600">
                            <span>{control.vendor}</span>
                            <span>•</span>
                            <span>Version: {control.version}</span>
                            <span>•</span>
                            <span className="capitalize">{control.type}</span>
                          </div>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Badge variant="outline">
                            {control.effectiveness_rating}% effective
                          </Badge>
                          <Badge variant="secondary" className="text-xs">
                            Updated: {control.last_updated}
                          </Badge>
                        </div>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                          <h5 className="font-medium text-sm mb-2">Known Bypasses</h5>
                          <div className="space-y-1">
                            {control.known_bypasses.map((bypass, index) => (
                              <Badge key={index} variant="outline" className="text-xs mr-1 mb-1">
                                {bypass}
                              </Badge>
                            ))}
                          </div>
                        </div>

                        <div>
                          <h5 className="font-medium text-sm mb-2">Signature Patterns</h5>
                          <div className="space-y-1">
                            {control.signature_patterns.map((pattern, index) => (
                              <div key={index} className="text-xs font-mono bg-gray-50 p-1 rounded">
                                {pattern}
                              </div>
                            ))}
                          </div>
                        </div>

                        <div>
                          <h5 className="font-medium text-sm mb-2">Behavioral Rules</h5>
                          <div className="space-y-1">
                            {control.behavioral_rules.map((rule, index) => (
                              <Badge key={index} variant="secondary" className="text-xs mr-1 mb-1">
                                {rule}
                              </Badge>
                            ))}
                          </div>
                        </div>
                      </div>
                    </div>
                  </Card>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Testing Lab Tab */}
        <TabsContent value="testing" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <FlaskConical className="h-5 w-5" />
                <span>Evasion Testing Lab</span>
              </CardTitle>
              <CardDescription>Test evasion techniques against security controls</CardDescription>
            </CardHeader>
            <CardContent>
              {selectedTechnique ? (
                <div className="space-y-6">
                  {/* Selected Technique */}
                  <Card className="p-4 bg-red-50 border-red-200">
                    <h3 className="font-semibold mb-2">{selectedTechnique.name}</h3>
                    <p className="text-sm text-gray-600 mb-3">{selectedTechnique.description}</p>
                    <div className="p-3 bg-white rounded border font-mono text-sm">
                      {selectedTechnique.payload_example}
                    </div>
                    <div className="flex items-center justify-between mt-3">
                      <div className="flex space-x-4 text-sm text-gray-600">
                        <span>Category: {selectedTechnique.category.replace('_', ' ')}</span>
                        <span>Effectiveness: {selectedTechnique.effectiveness_score}%</span>
                        <span>Complexity: {selectedTechnique.complexity}</span>
                      </div>
                      <Button onClick={() => testTechnique(selectedTechnique)} disabled={isTesting}>
                        <TestTube className="h-4 w-4 mr-2" />
                        {isTesting ? 'Testing...' : 'Run Tests'}
                      </Button>
                    </div>
                  </Card>

                  {/* Test Results */}
                  {selectedTechnique.test_results && (
                    <div className="space-y-4">
                      <h3 className="text-lg font-semibold">Test Results</h3>
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        {selectedTechnique.test_results.map((result, index) => (
                          <Card key={index} className="p-4">
                            <div className="space-y-3">
                              <div className="flex items-center justify-between">
                                <h4 className="font-semibold">{result.waf_vendor}</h4>
                                <Badge variant={result.bypass_success ? 'default' : 'destructive'}>
                                  {result.bypass_success ? 'Bypassed' : 'Blocked'}
                                </Badge>
                              </div>
                              
                              <div className="space-y-2">
                                <div>
                                  <div className="text-sm text-gray-600">Detection Rate</div>
                                  <div className="flex items-center space-x-2">
                                    <Progress value={result.detection_rate} className="h-2 flex-1" />
                                    <span className="text-sm font-medium">{result.detection_rate}%</span>
                                  </div>
                                </div>
                                
                                <div>
                                  <div className="text-sm text-gray-600">False Positive Rate</div>
                                  <div className="flex items-center space-x-2">
                                    <Progress value={result.false_positive_rate} className="h-2 flex-1" />
                                    <span className="text-sm font-medium">{result.false_positive_rate}%</span>
                                  </div>
                                </div>
                              </div>
                              
                              <div>
                                <div className="text-sm text-gray-600 mb-1">Response Analysis</div>
                                <p className="text-sm bg-gray-50 p-2 rounded">{result.response_analysis}</p>
                              </div>
                            </div>
                          </Card>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              ) : (
                <div className="text-center py-12">
                  <FlaskConical className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">No Technique Selected</h3>
                  <p className="text-gray-600 mb-4">Select a technique from the library to run tests</p>
                  <Button onClick={() => setActiveTab('techniques')} variant="outline">
                    <Shield className="h-4 w-4 mr-2" />
                    View Library
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Encoding Chains Tab */}
        <TabsContent value="encoding" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Code className="h-5 w-5" />
                <span>Encoding Chains</span>
              </CardTitle>
              <CardDescription>Advanced encoding and obfuscation techniques</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {ENCODING_CHAINS.map((chain) => (
                  <Card key={chain.id} className="p-4">
                    <div className="space-y-4">
                      <div className="flex items-start justify-between">
                        <div>
                          <h4 className="font-semibold">{chain.name}</h4>
                          <p className="text-sm text-gray-600">{chain.description}</p>
                        </div>
                        <div className="flex flex-col items-end space-y-1">
                          <Badge variant="outline">{chain.steps.length} steps</Badge>
                          <Badge variant={chain.reversibility ? 'default' : 'secondary'} className="text-xs">
                            {chain.reversibility ? 'Reversible' : 'One-way'}
                          </Badge>
                        </div>
                      </div>

                      <div className="space-y-2">
                        <div className="grid grid-cols-2 gap-4 text-sm">
                          <div>
                            <span className="text-gray-500">Effectiveness:</span>
                            <span className="ml-2 font-medium">{chain.effectiveness}%</span>
                          </div>
                          <div>
                            <span className="text-gray-500">Complexity:</span>
                            <span className="ml-2 font-medium">{chain.complexity}%</span>
                          </div>
                        </div>
                        
                        <div className="space-y-2">
                          <h5 className="font-medium text-sm">Encoding Steps</h5>
                          <div className="flex items-center space-x-2">
                            {chain.steps.map((step, index) => (
                              <div key={step.id} className="flex items-center space-x-2">
                                {index > 0 && <ArrowRight className="h-4 w-4 text-gray-400" />}
                                <Badge variant="outline" className="text-xs">
                                  {step.name}
                                </Badge>
                              </div>
                            ))}
                          </div>
                        </div>
                      </div>

                      <div className="flex items-center space-x-2">
                        <Button size="sm" variant="outline">
                          <TestTube className="h-4 w-4 mr-2" />
                          Test Chain
                        </Button>
                        <Button size="sm" variant="outline">
                          <Copy className="h-4 w-4 mr-2" />
                          Copy Config
                        </Button>
                      </div>
                    </div>
                  </Card>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Education Tab */}
        <TabsContent value="education" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Book className="h-5 w-5" />
                <span>Educational Resources</span>
              </CardTitle>
              <CardDescription>Learn about evasion techniques and defensive measures</CardDescription>
            </CardHeader>
            <CardContent>
              {selectedTechnique ? (
                <div className="space-y-6">
                  {/* Educational Notes */}
                  <Card className="p-4 bg-blue-50 border-blue-200">
                    <h3 className="font-semibold mb-2 flex items-center space-x-2">
                      <Lightbulb className="h-5 w-5 text-blue-600" />
                      <span>How This Technique Works</span>
                    </h3>
                    <p className="text-sm text-blue-800">
                      {selectedTechnique.educational_notes || 'This technique demonstrates advanced evasion methods for educational purposes. Always ensure proper authorization before testing.'}
                    </p>
                  </Card>

                  {/* Countermeasures */}
                  <Card className="p-4 bg-green-50 border-green-200">
                    <h3 className="font-semibold mb-2 flex items-center space-x-2">
                      <ShieldCheck className="h-5 w-5 text-green-600" />
                      <span>Defensive Countermeasures</span>
                    </h3>
                    <div className="space-y-2">
                      {selectedTechnique.countermeasures.map((countermeasure, index) => (
                        <div key={index} className="flex items-start space-x-3">
                          <div className="p-1 bg-green-100 rounded">
                            <CheckCircle className="h-4 w-4 text-green-600" />
                          </div>
                          <p className="text-sm text-green-800">{countermeasure}</p>
                        </div>
                      ))}
                    </div>
                  </Card>

                  {/* Technical Details */}
                  <Card className="p-4">
                    <h3 className="font-semibold mb-3">Technical Analysis</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <h4 className="font-medium text-sm mb-2">Technique Details</h4>
                        <div className="space-y-1 text-sm">
                          <div><span className="text-gray-500">Category:</span> <span className="ml-1 capitalize">{selectedTechnique.category.replace('_', ' ')}</span></div>
                          <div><span className="text-gray-500">Type:</span> <span className="ml-1 capitalize">{selectedTechnique.technique_type.replace('_', ' ')}</span></div>
                          <div><span className="text-gray-500">Complexity:</span> <span className="ml-1 capitalize">{selectedTechnique.complexity}</span></div>
                          <div><span className="text-gray-500">Vendor Specific:</span> <span className="ml-1">{selectedTechnique.vendor_specific ? 'Yes' : 'No'}</span></div>
                        </div>
                      </div>
                      
                      <div>
                        <h4 className="font-medium text-sm mb-2">Effectiveness Metrics</h4>
                        <div className="space-y-2">
                          <div>
                            <div className="flex justify-between text-sm">
                              <span>Effectiveness</span>
                              <span>{selectedTechnique.effectiveness_score}%</span>
                            </div>
                            <Progress value={selectedTechnique.effectiveness_score} className="h-2" />
                          </div>
                          <div>
                            <div className="flex justify-between text-sm">
                              <span>Detection Risk</span>
                              <span>{selectedTechnique.detection_probability}%</span>
                            </div>
                            <Progress value={selectedTechnique.detection_probability} className="h-2" />
                          </div>
                        </div>
                      </div>
                    </div>
                  </Card>
                </div>
              ) : (
                <div className="space-y-6">
                  <Card className="p-6 bg-amber-50 border-amber-200">
                    <div className="flex items-start space-x-3">
                      <AlertTriangle className="h-6 w-6 text-amber-600 mt-1" />
                      <div>
                        <h3 className="font-semibold text-amber-800 mb-2">Educational Use Only</h3>
                        <p className="text-sm text-amber-700">
                          The Evasion Techniques Generator is designed for educational purposes and authorized security testing only. 
                          All generated techniques include detailed explanations and countermeasures to promote responsible security research.
                        </p>
                      </div>
                    </div>
                  </Card>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <Card className="p-4">
                      <h3 className="font-semibold mb-3 flex items-center space-x-2">
                        <Target className="h-5 w-5" />
                        <span>Responsible Testing</span>
                      </h3>
                      <ul className="text-sm text-gray-600 space-y-1">
                        <li>• Always obtain proper authorization</li>
                        <li>• Test only against your own systems</li>
                        <li>• Follow responsible disclosure practices</li>
                        <li>• Document findings for defensive improvements</li>
                        <li>• Respect scope and boundaries</li>
                      </ul>
                    </Card>

                    <Card className="p-4">
                      <h3 className="font-semibold mb-3 flex items-center space-x-2">
                        <ShieldCheck className="h-5 w-5" />
                        <span>Defensive Mindset</span>
                      </h3>
                      <ul className="text-sm text-gray-600 space-y-1">
                        <li>• Understand attacks to defend better</li>
                        <li>• Implement layered security controls</li>
                        <li>• Regular security assessments</li>
                        <li>• Keep signatures up to date</li>
                        <li>• Monitor for evasion attempts</li>
                      </ul>
                    </Card>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};