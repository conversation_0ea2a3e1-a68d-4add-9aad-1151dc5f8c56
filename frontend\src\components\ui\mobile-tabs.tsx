/**
 * Mobile-Responsive Tabs Component
 * Provides scrollable tabs for mobile and standard tabs for desktop
 */
import React from 'react';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { cn } from '@/lib/utils';

interface MobileTabsProps {
  value: string;
  onValueChange: (value: string) => void;
  className?: string;
  children: React.ReactNode;
}

interface MobileTabsListProps {
  className?: string;
  children: React.ReactNode;
}

interface MobileTabsTriggerProps {
  value: string;
  className?: string;
  children: React.ReactNode;
}

interface MobileTabsContentProps {
  value: string;
  className?: string;
  children: React.ReactNode;
}

export function MobileTabs({ value, onValueChange, className, children }: MobileTabsProps) {
  return (
    <Tabs value={value} onValueChange={onValueChange} className={cn("space-y-6", className)}>
      {children}
    </Tabs>
  );
}

export function MobileTabsList({ className, children }: MobileTabsListProps) {
  return (
    <TabsList className={cn(
      // Mobile: Scrollable horizontal tabs
      "w-full justify-start overflow-x-auto scrollbar-hide",
      "md:grid md:w-full md:grid-cols-4", // Desktop: Grid layout
      "gap-1 p-1",
      className
    )}>
      <div className="flex md:contents">
        {children}
      </div>
    </TabsList>
  );
}

export function MobileTabsTrigger({ value, className, children }: MobileTabsTriggerProps) {
  return (
    <TabsTrigger 
      value={value} 
      className={cn(
        // Mobile: Fixed width, no shrinking
        "flex-shrink-0 min-w-[120px] justify-center",
        "md:flex-shrink md:min-w-0", // Desktop: Normal behavior
        "flex items-center gap-2 text-sm",
        className
      )}
    >
      {children}
    </TabsTrigger>
  );
}

export function MobileTabsContent({ value, className, children }: MobileTabsContentProps) {
  return (
    <TabsContent value={value} className={cn("space-y-6", className)}>
      {children}
    </TabsContent>
  );
}

// Export individual components for convenience
export { MobileTabs as Tabs, MobileTabsList as TabsList, MobileTabsTrigger as TabsTrigger, MobileTabsContent as TabsContent } from './mobile-tabs';
