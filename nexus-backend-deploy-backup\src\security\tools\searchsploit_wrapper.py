#!/usr/bin/env python3
"""
SearchSploit Wrapper for NexusScan Desktop
Exploit Database search tool integration
"""

import asyncio
import logging
from typing import Dict, List, Optional, Callable, Any
from datetime import datetime

from security.tools.tool_registry import (
    ToolMetadata, ToolCategory, ToolCapabilities,
    ScanOptions, ScanResult, ToolStatus, register_tool
)
from security.tools.external_tool_wrapper import ExternalToolWrapper

logger = logging.getLogger(__name__)


@register_tool
class SearchSploitWrapper(ExternalToolWrapper):
    """SearchSploit exploit database search wrapper"""
    
    def __init__(self):
        super().__init__()
        self.tool_executable = "searchsploit"
    
    def get_metadata(self) -> ToolMetadata:
        return ToolMetadata(
            name="searchsploit",
            display_name="SearchSploit",
            description="Offline exploit database search utility",
            version="1.0.0",
            category=ToolCategory.INTELLIGENCE,
            capabilities=ToolCapabilities(
                supports_async=True,
                network_access_required=False,  # Works offline
                supported_targets=["keyword", "service", "version"],
                requires_root=False
            ),
            default_options={
                "search_terms": [],
                "case_sensitive": False,
                "exact_match": False,
                "exclude_terms": [],
                "json_output": False,
                "show_path": True,
                "update_db": False,
                "platform_filter": None,  # windows, linux, multiple, etc.
                "type_filter": None,  # local, remote, webapps, dos, shellcode
                "verified_only": False
            }
        )
    
    def get_tool_command(self, options: ScanOptions) -> List[str]:
        """Get SearchSploit command"""
        # For SearchSploit, the target is the search term(s)
        search_terms = options.custom_options.get("search_terms", [options.target])
        if isinstance(search_terms, str):
            search_terms = [search_terms]
        
        command = ["searchsploit"]
        
        # Add flags
        if options.custom_options.get("case_sensitive", False):
            command.append("-s")
        
        if options.custom_options.get("exact_match", False):
            command.append("-e")
        
        if options.custom_options.get("show_path", True):
            command.append("-p")
        
        if options.custom_options.get("json_output", False):
            command.append("-j")
        
        # Add platform filter
        platform = options.custom_options.get("platform_filter")
        if platform:
            command.extend(["--platform", platform])
        
        # Add type filter
        exploit_type = options.custom_options.get("type_filter")
        if exploit_type:
            command.extend(["--type", exploit_type])
        
        # Add exclude terms
        exclude_terms = options.custom_options.get("exclude_terms", [])
        for term in exclude_terms:
            command.extend(["--exclude", term])
        
        # Add search terms
        command.extend(search_terms)
        
        return command
    
    def parse_tool_output(self, output: str, target: str) -> Dict[str, Any]:
        """Parse SearchSploit output"""
        lines = output.strip().split('\n')
        
        results = {
            "search_query": target,
            "exploits_found": [],
            "total_results": 0,
            "by_platform": {},
            "by_type": {},
            "vulnerabilities": []
        }
        
        # Skip header lines
        in_results = False
        header_line = None
        
        for line in lines:
            # Find the separator line
            if "---------" in line and "--------" in line:
                in_results = True
                continue
            
            # Parse result lines
            if in_results and line.strip():
                # Parse exploit entry (format: Title | Path)
                if " | " in line:
                    parts = line.split(" | ", 1)
                    if len(parts) == 2:
                        title = parts[0].strip()
                        path = parts[1].strip()
                        
                        # Extract platform and type from path
                        platform = "unknown"
                        exploit_type = "unknown"
                        
                        if "/" in path:
                            path_parts = path.split("/")
                            if len(path_parts) >= 2:
                                platform = path_parts[0]
                                exploit_type = path_parts[1]
                        
                        # Determine severity based on exploit type
                        severity = "info"
                        if exploit_type == "remote":
                            severity = "high"
                        elif exploit_type == "local":
                            severity = "medium"
                        elif exploit_type == "dos":
                            severity = "low"
                        
                        exploit_info = {
                            "title": title,
                            "path": path,
                            "platform": platform,
                            "type": exploit_type,
                            "severity": severity
                        }
                        
                        results["exploits_found"].append(exploit_info)
                        
                        # Count by platform
                        if platform not in results["by_platform"]:
                            results["by_platform"][platform] = 0
                        results["by_platform"][platform] += 1
                        
                        # Count by type
                        if exploit_type not in results["by_type"]:
                            results["by_type"][exploit_type] = 0
                        results["by_type"][exploit_type] += 1
        
        results["total_results"] = len(results["exploits_found"])
        
        # Create vulnerability entries for high-severity exploits
        for exploit in results["exploits_found"]:
            if exploit["severity"] in ["high", "critical"]:
                # Extract CVE if present in title
                cve = None
                if "CVE-" in exploit["title"]:
                    import re
                    cve_match = re.search(r'CVE-\d{4}-\d{4,}', exploit["title"])
                    if cve_match:
                        cve = cve_match.group(0)
                
                results["vulnerabilities"].append({
                    "name": f"Exploit Available: {exploit['title']}",
                    "severity": exploit["severity"],
                    "description": f"Public exploit available for {exploit['platform']}/{exploit['type']} vulnerability",
                    "type": "known_exploit",
                    "cve": cve,
                    "remediation": "Apply vendor patches and security updates to address this vulnerability",
                    "exploit_path": exploit["path"]
                })
        
        # Add summary statistics
        results["summary"] = {
            "total_exploits": results["total_results"],
            "remote_exploits": results["by_type"].get("remote", 0),
            "local_exploits": results["by_type"].get("local", 0),
            "dos_exploits": results["by_type"].get("dos", 0),
            "webapps_exploits": results["by_type"].get("webapps", 0),
            "high_severity": len([e for e in results["exploits_found"] if e["severity"] == "high"]),
            "platforms": list(results["by_platform"].keys())
        }
        
        return results
    
    async def execute_simulation(self, options: ScanOptions,
                                 progress_callback: Optional[Callable[[float, str], None]] = None) -> ScanResult:
        """Execute simulated scan for Railway/testing"""
        start_time = datetime.now().isoformat()
        
        if progress_callback:
            await progress_callback(0.1, "Searching exploit database...")
        
        await asyncio.sleep(1)
        
        if progress_callback:
            await progress_callback(0.5, "Analyzing exploit results...")
        
        await asyncio.sleep(1)
        
        # Simulated output based on search term
        search_term = options.target
        simulated_output = f"""--------------------------------------------------------------------------- ---------------------------------
 Exploit Title                                                              |  Path
--------------------------------------------------------------------------- ---------------------------------
Apache Struts 2.3.x/2.5.x - Remote Code Execution                         | multiple/webapps/41614.py
Apache Struts 2.3.x - 'ParameterInterceptor' Remote Code Execution        | multiple/remote/41690.txt
Apache Struts 2.0.0 < 2.3.15 - OGNL Remote Code Execution (CVE-2013-2251) | multiple/remote/25980.py
Apache Struts 2 - DefaultActionMapper Prefixes OGNL Code Execution        | multiple/remote/45262.py
Apache Struts 2.3.x - Showcase Remote Code Execution                      | multiple/webapps/42324.py
Apache HTTP Server 2.4.49 - Path Traversal & RCE (CVE-2021-41773)        | multiple/webapps/50383.sh
WordPress Plugin WP < 5.8.2 - SQL Injection                               | php/webapps/50515.txt
Microsoft Windows - SMB Remote Code Execution (MS17-010)                  | windows/remote/42315.py
Linux Kernel 4.4.x - Local Privilege Escalation                          | linux/local/40871.c
Oracle WebLogic Server - Remote Code Execution (CVE-2019-2725)           | multiple/webapps/46780.py
Microsoft Exchange Server - ProxyLogon RCE (CVE-2021-26855)              | windows/remote/49485.py
VMware vCenter - Remote Code Execution (CVE-2021-21972)                  | multiple/remote/49602.py
Drupal < 7.58 / < 8.5.1 - 'Drupalgeddon2' RCE (CVE-2018-7600)          | php/webapps/44449.rb
Joomla! < 3.6.4 - Account Creation / Privilege Escalation                | php/webapps/40637.txt
--------------------------------------------------------------------------- ---------------------------------
Shellcodes: No Results
Papers: No Results"""
        
        parsed_results = self.parse_tool_output(simulated_output, search_term)
        
        if progress_callback:
            await progress_callback(1.0, "Exploit search complete")
        
        return ScanResult(
            tool_name=self.metadata.name,
            target=options.target,
            status=ToolStatus.COMPLETED,
            start_time=start_time,
            end_time=datetime.now().isoformat(),
            duration_seconds=(datetime.now() - datetime.fromisoformat(start_time)).total_seconds(),
            raw_output=simulated_output,
            parsed_results=parsed_results,
            vulnerabilities=parsed_results.get("vulnerabilities", []),
            metadata={
                "search_query": search_term,
                "total_exploits": parsed_results.get("total_results", 0),
                "exploit_categories": list(parsed_results.get("by_type", {}).keys()),
                "platforms_affected": list(parsed_results.get("by_platform", {}).keys())
            }
        )