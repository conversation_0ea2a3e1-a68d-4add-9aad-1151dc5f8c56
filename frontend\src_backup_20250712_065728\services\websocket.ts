/**
 * WebSocket Service for Real-time Communication
 * Handles tool execution progress, AI updates, and system events
 */

// ===== TYPE DEFINITIONS =====

export interface WebSocketMessage {
  type: string;
  id?: string;
  timestamp: string;
  data: any;
}

export interface ToolProgressMessage extends WebSocketMessage {
  type: 'tool_progress';
  data: {
    executionId: string;
    toolId: string;
    progress: number;
    status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';
    output?: string[];
    error?: string;
  };
}

export interface ToolOutputMessage extends WebSocketMessage {
  type: 'tool_output';
  data: {
    executionId: string;
    toolId: string;
    output: string;
    stream: 'stdout' | 'stderr';
  };
}

export interface AIUpdateMessage extends WebSocketMessage {
  type: 'ai_update';
  data: {
    provider: string;
    model: string;
    status: 'processing' | 'completed' | 'error';
    result?: any;
    error?: string;
  };
}

export interface SystemEventMessage extends WebSocketMessage {
  type: 'system_event';
  data: {
    event: 'backend_status' | 'tool_availability' | 'campaign_update' | 'notification';
    details: any;
  };
}

export type WSMessage = ToolProgressMessage | ToolOutputMessage | AIUpdateMessage | SystemEventMessage;

export interface WebSocketConfig {
  url: string;
  reconnectAttempts: number;
  reconnectDelay: number;
  heartbeatInterval: number;
  maxReconnectDelay: number;
}

export interface WebSocketEventCallbacks {
  onConnect?: () => void;
  onDisconnect?: (reason: string) => void;
  onError?: (error: Error) => void;
  onMessage?: (message: WSMessage) => void;
  onToolProgress?: (progress: ToolProgressMessage) => void;
  onToolOutput?: (output: ToolOutputMessage) => void;
  onAIUpdate?: (update: AIUpdateMessage) => void;
  onSystemEvent?: (event: SystemEventMessage) => void;
}

// ===== WEBSOCKET SERVICE CLASS =====

class WebSocketService {
  private ws: WebSocket | null = null;
  private config: WebSocketConfig;
  private callbacks: WebSocketEventCallbacks = {};
  private reconnectAttempts: number = 0;
  private reconnectTimer: NodeJS.Timeout | null = null;
  private heartbeatTimer: NodeJS.Timeout | null = null;
  private isConnecting: boolean = false;
  private isConnected: boolean = false;
  private messageQueue: WSMessage[] = [];
  private subscriptions: Set<string> = new Set();
  
  constructor(config?: Partial<WebSocketConfig>) {
    this.config = {
      url: import.meta.env.VITE_WS_URL || 'wss://ec2-3-89-91-209.compute-1.amazonaws.com:8000/ws',
      reconnectAttempts: 5,
      reconnectDelay: 1000, // 1 second
      heartbeatInterval: 30000, // 30 seconds
      maxReconnectDelay: 30000, // 30 seconds
      ...config,
    };
    
    console.log(`🔌 WebSocket Service initialized for: ${this.config.url}`);
  }
  
  // ===== CONNECTION MANAGEMENT =====
  
  async connect(): Promise<boolean> {
    if (this.isConnecting || this.isConnected) {
      console.log('🔌 WebSocket already connecting or connected');
      return this.isConnected;
    }
    
    try {
      this.isConnecting = true;
      console.log(`🔌 Connecting to WebSocket: ${this.config.url}`);
      
      this.ws = new WebSocket(this.config.url);
      
      return new Promise((resolve) => {
        if (!this.ws) {
          resolve(false);
          return;
        }
        
        const connectTimeout = setTimeout(() => {
          console.error('❌ WebSocket connection timeout');
          this.cleanup();
          resolve(false);
        }, 10000); // 10 second timeout
        
        this.ws.onopen = () => {
          clearTimeout(connectTimeout);
          this.isConnecting = false;
          this.isConnected = true;
          this.reconnectAttempts = 0;
          
          console.log('✅ WebSocket connected successfully');
          
          // Start heartbeat
          this.startHeartbeat();
          
          // Process queued messages
          this.processMessageQueue();
          
          // Resubscribe to previous subscriptions
          this.resubscribe();
          
          this.callbacks.onConnect?.();
          resolve(true);
        };
        
        this.ws.onclose = (event) => {
          clearTimeout(connectTimeout);
          this.isConnecting = false;
          this.isConnected = false;
          this.stopHeartbeat();
          
          const reason = event.reason || `Code: ${event.code}`;
          console.log(`🔌 WebSocket disconnected: ${reason}`);\n          
          this.callbacks.onDisconnect?.(reason);
          
          // Attempt reconnection if not a clean close
          if (event.code !== 1000 && this.reconnectAttempts < this.config.reconnectAttempts) {\n            this.scheduleReconnect();\n          }\n          \n          resolve(false);\n        };\n        \n        this.ws.onerror = (error) => {\n          clearTimeout(connectTimeout);\n          console.error('❌ WebSocket error:', error);\n          \n          const wsError = new Error('WebSocket connection error');\n          this.callbacks.onError?.(wsError);\n          \n          resolve(false);\n        };\n        \n        this.ws.onmessage = (event) => {\n          try {\n            const message: WSMessage = JSON.parse(event.data);\n            this.handleMessage(message);\n          } catch (error) {\n            console.error('❌ Failed to parse WebSocket message:', error, event.data);\n          }\n        };\n      });\n    } catch (error) {\n      this.isConnecting = false;\n      console.error('❌ Failed to create WebSocket connection:', error);\n      return false;\n    }\n  }\n  \n  disconnect(): void {\n    console.log('🔌 Disconnecting WebSocket...');\n    \n    this.cleanup();\n    \n    if (this.ws) {\n      this.ws.close(1000, 'Client disconnect');\n      this.ws = null;\n    }\n  }\n  \n  private cleanup(): void {\n    this.isConnecting = false;\n    this.isConnected = false;\n    this.stopHeartbeat();\n    this.stopReconnectTimer();\n  }\n  \n  private scheduleReconnect(): void {\n    if (this.reconnectTimer) {\n      return;\n    }\n    \n    this.reconnectAttempts++;\n    const delay = Math.min(\n      this.config.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1),\n      this.config.maxReconnectDelay\n    );\n    \n    console.log(`🔄 Scheduling reconnect attempt ${this.reconnectAttempts}/${this.config.reconnectAttempts} in ${delay}ms`);\n    \n    this.reconnectTimer = setTimeout(async () => {\n      this.reconnectTimer = null;\n      await this.connect();\n    }, delay);\n  }\n  \n  private stopReconnectTimer(): void {\n    if (this.reconnectTimer) {\n      clearTimeout(this.reconnectTimer);\n      this.reconnectTimer = null;\n    }\n  }\n  \n  // ===== HEARTBEAT MANAGEMENT =====\n  \n  private startHeartbeat(): void {\n    this.stopHeartbeat();\n    \n    this.heartbeatTimer = setInterval(() => {\n      if (this.isConnected && this.ws?.readyState === WebSocket.OPEN) {\n        this.send({\n          type: 'ping',\n          timestamp: new Date().toISOString(),\n          data: {},\n        });\n      }\n    }, this.config.heartbeatInterval);\n  }\n  \n  private stopHeartbeat(): void {\n    if (this.heartbeatTimer) {\n      clearInterval(this.heartbeatTimer);\n      this.heartbeatTimer = null;\n    }\n  }\n  \n  // ===== MESSAGE HANDLING =====\n  \n  private handleMessage(message: WSMessage): void {\n    console.log('📨 WebSocket message received:', message.type, message);\n    \n    // Call generic message callback\n    this.callbacks.onMessage?.(message);\n    \n    // Call specific callbacks based on message type\n    switch (message.type) {\n      case 'tool_progress':\n        this.callbacks.onToolProgress?.(message as ToolProgressMessage);\n        break;\n        \n      case 'tool_output':\n        this.callbacks.onToolOutput?.(message as ToolOutputMessage);\n        break;\n        \n      case 'ai_update':\n        this.callbacks.onAIUpdate?.(message as AIUpdateMessage);\n        break;\n        \n      case 'system_event':\n        this.callbacks.onSystemEvent?.(message as SystemEventMessage);\n        break;\n        \n      case 'pong':\n        // Heartbeat response - connection is alive\n        break;\n        \n      default:\n        console.warn('🤷 Unknown WebSocket message type:', message.type);\n    }\n  }\n  \n  private send(message: Partial<WSMessage>): boolean {\n    if (!this.isConnected || !this.ws || this.ws.readyState !== WebSocket.OPEN) {\n      console.warn('📤 WebSocket not connected, queueing message:', message);\n      this.messageQueue.push(message as WSMessage);\n      return false;\n    }\n    \n    try {\n      const fullMessage = {\n        ...message,\n        timestamp: message.timestamp || new Date().toISOString(),\n        id: message.id || crypto.randomUUID(),\n      };\n      \n      this.ws.send(JSON.stringify(fullMessage));\n      return true;\n    } catch (error) {\n      console.error('❌ Failed to send WebSocket message:', error);\n      return false;\n    }\n  }\n  \n  private processMessageQueue(): void {\n    console.log(`📤 Processing ${this.messageQueue.length} queued messages`);\n    \n    while (this.messageQueue.length > 0 && this.isConnected) {\n      const message = this.messageQueue.shift();\n      if (message) {\n        this.send(message);\n      }\n    }\n  }\n  \n  // ===== SUBSCRIPTION MANAGEMENT =====\n  \n  subscribe(channel: string): boolean {\n    console.log(`📺 Subscribing to channel: ${channel}`);\n    \n    this.subscriptions.add(channel);\n    \n    return this.send({\n      type: 'subscribe',\n      data: { channel },\n    });\n  }\n  \n  unsubscribe(channel: string): boolean {\n    console.log(`📺 Unsubscribing from channel: ${channel}`);\n    \n    this.subscriptions.delete(channel);\n    \n    return this.send({\n      type: 'unsubscribe',\n      data: { channel },\n    });\n  }\n  \n  private resubscribe(): void {\n    if (this.subscriptions.size > 0) {\n      console.log(`📺 Resubscribing to ${this.subscriptions.size} channels`);\n      \n      this.subscriptions.forEach(channel => {\n        this.send({\n          type: 'subscribe',\n          data: { channel },\n        });\n      });\n    }\n  }\n  \n  // ===== EVENT CALLBACKS =====\n  \n  on(event: keyof WebSocketEventCallbacks, callback: any): void {\n    this.callbacks[event] = callback;\n  }\n  \n  off(event: keyof WebSocketEventCallbacks): void {\n    delete this.callbacks[event];\n  }\n  \n  // ===== CONVENIENCE METHODS =====\n  \n  subscribeToToolProgress(executionId: string): boolean {\n    return this.subscribe(`tool_progress:${executionId}`);\n  }\n  \n  subscribeToToolOutput(executionId: string): boolean {\n    return this.subscribe(`tool_output:${executionId}`);\n  }\n  \n  subscribeToAIUpdates(): boolean {\n    return this.subscribe('ai_updates');\n  }\n  \n  subscribeToSystemEvents(): boolean {\n    return this.subscribe('system_events');\n  }\n  \n  // ===== STATUS METHODS =====\n  \n  isConnectedToBackend(): boolean {\n    return this.isConnected && this.ws?.readyState === WebSocket.OPEN;\n  }\n  \n  getConnectionState(): 'connecting' | 'connected' | 'disconnected' | 'error' {\n    if (this.isConnecting) return 'connecting';\n    if (this.isConnected) return 'connected';\n    if (this.ws?.readyState === WebSocket.CLOSED || this.ws?.readyState === WebSocket.CLOSING) {\n      return 'disconnected';\n    }\n    return 'error';\n  }\n  \n  getReconnectAttempts(): number {\n    return this.reconnectAttempts;\n  }\n  \n  getSubscriptions(): string[] {\n    return Array.from(this.subscriptions);\n  }\n  \n  // ===== CONFIGURATION =====\n  \n  updateConfig(config: Partial<WebSocketConfig>): void {\n    this.config = { ...this.config, ...config };\n    console.log('🔧 WebSocket config updated:', this.config);\n  }\n  \n  getConfig(): WebSocketConfig {\n    return { ...this.config };\n  }\n}\n\n// ===== SINGLETON EXPORT =====\n\nexport const webSocketService = new WebSocketService();