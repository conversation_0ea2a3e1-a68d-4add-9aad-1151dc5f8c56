#!/usr/bin/env python3
"""
Enum4Linux Wrapper for NexusScan Desktop
SMB enumeration tool integration
"""

import asyncio
import logging
from typing import Dict, List, Optional, Callable, Any
from datetime import datetime

from security.tools.tool_registry import (
    ToolMetadata, ToolCategory, ToolCapabilities,
    ScanOptions, ScanResult, ToolStatus, register_tool
)
from security.tools.external_tool_wrapper import ExternalToolWrapper

logger = logging.getLogger(__name__)


@register_tool
class Enum4LinuxWrapper(ExternalToolWrapper):
    """Enum4Linux SMB enumeration tool wrapper"""
    
    def __init__(self):
        super().__init__()
        self.tool_executable = "enum4linux"
    
    def get_metadata(self) -> ToolMetadata:
        return ToolMetadata(
            name="enum4linux",
            display_name="Enum4Linux",
            description="SMB enumeration tool for Linux/Unix",
            version="1.0.0",
            category=ToolCategory.NETWORK_SCANNER,
            capabilities=ToolCapabilities(
                supports_async=True,
                network_access_required=True,
                supported_targets=["ip", "hostname"],
                requires_root=False
            ),
            default_options={
                "enumerate_all": True,
                "users": True,
                "shares": True,
                "groups": True,
                "password_policy": True,
                "os_info": True,
                "printers": True,
                "services": True,
                "username": "",
                "password": "",
                "workgroup": "WORKGROUP"
            }
        )
    
    def get_tool_command(self, options: ScanOptions) -> List[str]:
        """Get Enum4Linux command"""
        command = ["enum4linux"]
        
        # Add authentication if provided
        username = options.custom_options.get("username", "")
        password = options.custom_options.get("password", "")
        if username:
            command.extend(["-u", username])
        if password:
            command.extend(["-p", password])
        
        # Add workgroup
        workgroup = options.custom_options.get("workgroup", "WORKGROUP")
        command.extend(["-w", workgroup])
        
        # Add enumeration options
        if options.custom_options.get("enumerate_all", True):
            command.append("-a")  # Do all simple enumeration
        else:
            # Add specific enumeration flags
            if options.custom_options.get("users", True):
                command.append("-U")
            if options.custom_options.get("shares", True):
                command.append("-S")
            if options.custom_options.get("groups", True):
                command.append("-G")
            if options.custom_options.get("password_policy", True):
                command.append("-P")
            if options.custom_options.get("os_info", True):
                command.append("-o")
            if options.custom_options.get("printers", True):
                command.append("-i")
            if options.custom_options.get("services", True):
                command.append("-r")
        
        # Add target
        command.append(options.target)
        
        return command
    
    def parse_tool_output(self, output: str, target: str) -> Dict[str, Any]:
        """Parse Enum4Linux output"""
        lines = output.strip().split('\n')
        
        results = {
            "target": target,
            "os_info": {},
            "domain_info": {},
            "users": [],
            "groups": [],
            "shares": [],
            "password_policy": {},
            "printers": [],
            "services": [],
            "vulnerabilities": []
        }
        
        current_section = None
        
        for line in lines:
            # OS Information
            if "OS information on" in line:
                current_section = "os_info"
            elif current_section == "os_info" and line.strip().startswith("[+]"):
                info = line.strip()[3:].strip()
                if "OS:" in info:
                    results["os_info"]["os"] = info.split("OS:", 1)[1].strip()
                elif "Server:" in info:
                    results["os_info"]["server"] = info.split("Server:", 1)[1].strip()
                elif "Domain:" in info:
                    results["os_info"]["domain"] = info.split("Domain:", 1)[1].strip()
            
            # Domain/Workgroup Info
            elif "Domain/Workgroup name:" in line:
                domain_name = line.split(":", 1)[1].strip()
                results["domain_info"]["name"] = domain_name
            elif "Domain SID:" in line:
                domain_sid = line.split(":", 1)[1].strip()
                results["domain_info"]["sid"] = domain_sid
            
            # Users
            elif "index:" in line and "RID:" in line and "acb:" in line:
                parts = line.split()
                user_info = {}
                for i, part in enumerate(parts):
                    if part == "user:" and i + 1 < len(parts):
                        user_info["username"] = parts[i + 1].strip("[]")
                    elif part == "RID:" and i + 1 < len(parts):
                        user_info["rid"] = parts[i + 1].strip("[]")
                if user_info:
                    results["users"].append(user_info)
            
            # Groups
            elif "group:" in line and "RID:" in line:
                parts = line.split()
                group_info = {}
                for i, part in enumerate(parts):
                    if part == "group:" and i + 1 < len(parts):
                        group_info["name"] = parts[i + 1].strip("[]")
                    elif part == "RID:" and i + 1 < len(parts):
                        group_info["rid"] = parts[i + 1].strip("[]")
                if group_info:
                    results["groups"].append(group_info)
            
            # Shares
            elif "Sharename" in line and "Type" in line and "Comment" in line:
                current_section = "shares"
            elif current_section == "shares" and line.strip() and not line.startswith("---"):
                parts = line.split(None, 2)
                if len(parts) >= 2:
                    share_info = {
                        "name": parts[0],
                        "type": parts[1],
                        "comment": parts[2] if len(parts) > 2 else ""
                    }
                    results["shares"].append(share_info)
            
            # Password Policy
            elif "Password Info for Domain:" in line:
                current_section = "password_policy"
            elif current_section == "password_policy" and "[+]" in line:
                info = line.strip()[3:].strip()
                if "Minimum password length:" in info:
                    results["password_policy"]["min_length"] = info.split(":", 1)[1].strip()
                elif "Password history length:" in info:
                    results["password_policy"]["history_length"] = info.split(":", 1)[1].strip()
                elif "Maximum password age:" in info:
                    results["password_policy"]["max_age"] = info.split(":", 1)[1].strip()
                elif "Password Complexity Flags:" in info:
                    results["password_policy"]["complexity"] = info.split(":", 1)[1].strip()
        
        # Generate vulnerabilities based on findings
        # Check for null sessions
        if results["users"] or results["shares"]:
            results["vulnerabilities"].append({
                "name": "SMB Null Session Enumeration",
                "severity": "medium",
                "description": "The SMB service allows null session enumeration, exposing user and share information",
                "type": "information_disclosure",
                "cve": None,
                "remediation": "Disable null sessions in SMB configuration"
            })
        
        # Check for weak password policy
        if results["password_policy"]:
            min_length = results["password_policy"].get("min_length", "")
            if min_length and min_length.isdigit() and int(min_length) < 8:
                results["vulnerabilities"].append({
                    "name": "Weak Password Policy",
                    "severity": "medium",
                    "description": f"Minimum password length is only {min_length} characters",
                    "type": "configuration_weakness",
                    "cve": None,
                    "remediation": "Increase minimum password length to at least 8 characters"
                })
        
        # Check for exposed shares
        for share in results["shares"]:
            if share["name"].upper() not in ["IPC$", "PRINT$", "ADMIN$", "C$"]:
                results["vulnerabilities"].append({
                    "name": f"Exposed SMB Share: {share['name']}",
                    "severity": "low",
                    "description": f"SMB share '{share['name']}' is exposed: {share['comment']}",
                    "type": "information_disclosure",
                    "cve": None,
                    "remediation": "Review share permissions and remove unnecessary shares"
                })
        
        # Create summary
        results["summary"] = {
            "users_found": len(results["users"]),
            "groups_found": len(results["groups"]),
            "shares_found": len(results["shares"]),
            "vulnerabilities_found": len(results["vulnerabilities"])
        }
        
        return results
    
    async def execute_simulation(self, options: ScanOptions,
                                 progress_callback: Optional[Callable[[float, str], None]] = None) -> ScanResult:
        """Execute simulated scan for Railway/testing"""
        start_time = datetime.now().isoformat()
        
        if progress_callback:
            await progress_callback(0.1, "Starting SMB enumeration...")
        
        await asyncio.sleep(1)
        
        if progress_callback:
            await progress_callback(0.3, "Enumerating users and groups...")
        
        await asyncio.sleep(1)
        
        if progress_callback:
            await progress_callback(0.6, "Enumerating shares and services...")
        
        await asyncio.sleep(1)
        
        # Simulated output
        simulated_output = f"""Starting enum4linux v0.9.1 ( http://labs.portcullis.co.uk/application/enum4linux/ ) on {start_time}

 ========================== 
|    Target Information    |
 ========================== 
Target ........... {options.target}
RID Range ........ 500-550,1000-1050
Username ......... ''
Password ......... ''
Known Usernames .. administrator, guest, krbtgt, domain admins, root, bin, none

 ==================================================== 
|    Enumerating Workgroup/Domain on {options.target}    |
 ==================================================== 
[+] Got domain/workgroup name: WORKGROUP

 ============================================ 
|    Nbtstat Information for {options.target}    |
 ============================================ 
Looking up status of {options.target}
	{options.target.split('.')[-1]}         <00> -         B <ACTIVE>  Workstation Service
	{options.target.split('.')[-1]}         <03> -         B <ACTIVE>  Messenger Service
	{options.target.split('.')[-1]}         <20> -         B <ACTIVE>  File Server Service
	WORKGROUP      <00> - <GROUP> B <ACTIVE>  Domain/Workgroup Name
	WORKGROUP      <1e> - <GROUP> B <ACTIVE>  Browser Service Elections

	MAC Address = 00-50-56-XX-XX-XX

 ===================================== 
|    Session Check on {options.target}    |
 ===================================== 
[+] Server {options.target} allows sessions using username '', password ''

 =========================================== 
|    Getting domain SID for {options.target}    |
 =========================================== 
Domain Name: WORKGROUP
Domain Sid: (NULL SID)
[+] Can't determine if host is part of domain or part of a workgroup

 ====================================== 
|    OS information on {options.target}    |
 ====================================== 
[+] Got OS info for {options.target} from srvinfo: 
	{options.target.split('.')[-1]}         Wk Sv PrQ Unx NT SNT Samba Server
	platform_id     :	500
	os version      :	6.1
	server type     :	0x809a03

[+] Got OS info for {options.target} from smbclient: 
	Domain=[WORKGROUP] OS=[Windows 6.1] Server=[Samba 4.7.6-Ubuntu]

 ============================= 
|    Users on {options.target}    |
 ============================= 
index: 0x1 RID: 0x3e8 acb: 0x00000010 Account: admin	Name: Administrator	Desc: Built-in account for administering the computer/domain
index: 0x2 RID: 0x3e9 acb: 0x00000010 Account: guest	Name: Guest	Desc: Built-in account for guest access to the computer/domain
index: 0x3 RID: 0x3ea acb: 0x00000010 Account: user1	Name: Test User 1	Desc: Regular user account
index: 0x4 RID: 0x3eb acb: 0x00000010 Account: user2	Name: Test User 2	Desc: Regular user account

 ========================================= 
|    Share Enumeration on {options.target}    |
 ========================================= 
	Sharename       Type      Comment
	---------       ----      -------
	print$          Disk      Printer Drivers
	public          Disk      Public Share
	IPC$            IPC       IPC Service (Samba Server)
	admin           Disk      Admin Share
SMB1 disabled -- no workgroup available

[+] Attempting to map shares on {options.target}
//{options.target}/print$	Mapping: DENIED, Listing: N/A
//{options.target}/public	Mapping: OK, Listing: OK
//{options.target}/IPC$	[E] Can't understand response:
//{options.target}/admin	Mapping: DENIED, Listing: N/A

 ==================================================== 
|    Password Policy Information for {options.target}    |
 ==================================================== 
[+] Got password policy for domain WORKGROUP:
	[+] Minimum password length: 7
	[+] Password history length: None
	[+] Maximum password age: Not Set
	[+] Password Complexity Flags: 000000

 ============================== 
|    Groups on {options.target}    |
 ============================== 
[+] Getting groups:
group:[Administrators] rid:[0x220]
group:[Users] rid:[0x221]
group:[Guests] rid:[0x222]
group:[Power Users] rid:[0x223]

enum4linux complete on {datetime.now().isoformat()}"""
        
        parsed_results = self.parse_tool_output(simulated_output, options.target)
        
        if progress_callback:
            await progress_callback(1.0, "SMB enumeration complete")
        
        return ScanResult(
            tool_name=self.metadata.name,
            target=options.target,
            status=ToolStatus.COMPLETED,
            start_time=start_time,
            end_time=datetime.now().isoformat(),
            duration_seconds=(datetime.now() - datetime.fromisoformat(start_time)).total_seconds(),
            raw_output=simulated_output,
            parsed_results=parsed_results,
            vulnerabilities=parsed_results.get("vulnerabilities", []),
            metadata={
                "domain": parsed_results.get("domain_info", {}).get("name"),
                "users_found": len(parsed_results.get("users", [])),
                "shares_found": len(parsed_results.get("shares", [])),
                "groups_found": len(parsed_results.get("groups", []))
            }
        )