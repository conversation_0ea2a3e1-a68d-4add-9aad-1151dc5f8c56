/**
 * Nmap Scanner Component
 * Advanced network discovery and port scanning interface
 */
import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { 
  Network,
  Play,
  Square,
  Download,
  Settings,
  Target,
  Shield,
  Zap,
  Clock,
  Terminal,
  FileText,
  AlertTriangle,
  CheckCircle,
  Copy,
  RotateCcw
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useBackendStore } from '@/stores/backend-store';
import { apiClient, ToolExecution } from '@/services/api-client';
import { toast } from 'sonner';

/**
 * Nmap scan configuration interface
 */
interface NmapConfig {
  target: string;
  scanType: 'syn' | 'tcp' | 'udp' | 'comprehensive' | 'stealth' | 'aggressive';
  portRange: string;
  timing: 0 | 1 | 2 | 3 | 4 | 5;
  detectOS: boolean;
  detectVersion: boolean;
  enableScripts: boolean;
  scriptCategories: string[];
  outputFormat: 'normal' | 'xml' | 'grepable' | 'json';
  customArgs: string;
}

/**
 * Scan preset configurations
 */
const SCAN_PRESETS: Record<string, Partial<NmapConfig>> = {
  'quick': {
    scanType: 'syn',
    portRange: '1-1000',
    timing: 4,
    detectOS: false,
    detectVersion: false,
    enableScripts: false
  },
  'comprehensive': {
    scanType: 'comprehensive',
    portRange: '1-65535',
    timing: 3,
    detectOS: true,
    detectVersion: true,
    enableScripts: true,
    scriptCategories: ['default', 'safe']
  },
  'stealth': {
    scanType: 'stealth',
    portRange: '1-1000',
    timing: 1,
    detectOS: false,
    detectVersion: false,
    enableScripts: false
  },
  'vulnerability': {
    scanType: 'comprehensive',
    portRange: '1-1000',
    timing: 3,
    detectOS: true,
    detectVersion: true,
    enableScripts: true,
    scriptCategories: ['vuln', 'safe', 'default']
  }
};

/**
 * Available NSE script categories
 */
const SCRIPT_CATEGORIES = [
  { value: 'auth', label: 'Authentication' },
  { value: 'broadcast', label: 'Broadcast' },
  { value: 'brute', label: 'Brute Force' },
  { value: 'default', label: 'Default' },
  { value: 'discovery', label: 'Discovery' },
  { value: 'dos', label: 'Denial of Service' },
  { value: 'exploit', label: 'Exploit' },
  { value: 'external', label: 'External' },
  { value: 'fuzzer', label: 'Fuzzer' },
  { value: 'intrusive', label: 'Intrusive' },
  { value: 'malware', label: 'Malware' },
  { value: 'safe', label: 'Safe' },
  { value: 'version', label: 'Version' },
  { value: 'vuln', label: 'Vulnerability' }
];

/**
 * Nmap Scanner Component
 */
export function NmapScanner() {
  // State management
  const [config, setConfig] = React.useState<NmapConfig>({
    target: '',
    scanType: 'syn',
    portRange: '1-1000',
    timing: 3,
    detectOS: false,
    detectVersion: false,
    enableScripts: false,
    scriptCategories: ['safe'],
    outputFormat: 'normal',
    customArgs: ''
  });
  
  const [isScanning, setIsScanning] = React.useState(false);
  const [currentExecution, setCurrentExecution] = React.useState<ToolExecution | null>(null);
  const [progress, setProgress] = React.useState(0);
  const [output, setOutput] = React.useState<string[]>([]);
  const [results, setResults] = React.useState<any>(null);
  const [activeTab, setActiveTab] = React.useState('configure');
  
  // Backend connection
  const { status: backendStatus } = useBackendStore();
  
  // Update configuration
  const updateConfig = (updates: Partial<NmapConfig>) => {
    setConfig(prev => ({ ...prev, ...updates }));
  };
  
  // Apply scan preset
  const applyPreset = (presetName: string) => {
    const preset = SCAN_PRESETS[presetName];
    if (preset) {
      updateConfig(preset);
      toast.success(`Applied ${presetName} scan preset`);
    }
  };
  
  // Generate Nmap command
  const generateCommand = (): string => {
    let command = 'nmap';
    
    // Scan type
    switch (config.scanType) {
      case 'syn':
        command += ' -sS';
        break;
      case 'tcp':
        command += ' -sT';
        break;
      case 'udp':
        command += ' -sU';
        break;
      case 'comprehensive':
        command += ' -sS -sU';
        break;
      case 'stealth':
        command += ' -sS -f';
        break;
      case 'aggressive':
        command += ' -A';
        break;
    }
    
    // Port range
    if (config.portRange && config.portRange !== '1-65535') {
      command += ` -p ${config.portRange}`;
    }
    
    // Timing
    command += ` -T${config.timing}`;
    
    // OS detection
    if (config.detectOS) {
      command += ' -O';
    }
    
    // Version detection
    if (config.detectVersion) {
      command += ' -sV';
    }
    
    // Scripts
    if (config.enableScripts && config.scriptCategories.length > 0) {
      command += ` --script=${config.scriptCategories.join(',')}`;
    }
    
    // Output format
    switch (config.outputFormat) {
      case 'xml':
        command += ' -oX -';
        break;
      case 'grepable':
        command += ' -oG -';
        break;
      case 'json':
        command += ' -oJ -';
        break;
    }
    
    // Custom arguments
    if (config.customArgs.trim()) {
      command += ` ${config.customArgs.trim()}`;
    }
    
    // Target
    command += ` ${config.target}`;
    
    return command;
  };
  
  // Start scan
  const startScan = async () => {
    if (!config.target.trim()) {
      toast.error('Please specify a target');
      return;
    }
    
    if (!backendStatus.connected) {
      toast.error('Backend not connected');
      return;
    }
    
    try {
      setIsScanning(true);
      setProgress(0);
      setOutput([]);
      setResults(null);
      setActiveTab('output');
      
      const execution = await apiClient.executeTool('nmap', config, (progressUpdate) => {
        setCurrentExecution(progressUpdate);
        setProgress(progressUpdate.progress);
        if (progressUpdate.output) {
          setOutput(prev => [...prev, ...progressUpdate.output!]);
        }
        
        if (progressUpdate.status === 'completed') {
          setIsScanning(false);
          setResults(progressUpdate.results);
          toast.success('Nmap scan completed');
        } else if (progressUpdate.status === 'failed') {
          setIsScanning(false);
          toast.error(`Scan failed: ${progressUpdate.error}`);
        }
      });
      
      setCurrentExecution(execution);
      toast.info('Nmap scan started');
    } catch (error) {
      setIsScanning(false);
      console.error('Failed to start Nmap scan:', error);
      toast.error('Failed to start scan');
    }
  };
  
  // Stop scan
  const stopScan = async () => {
    if (currentExecution) {
      try {
        await apiClient.cancelExecution(currentExecution.id);
        setIsScanning(false);
        setCurrentExecution(null);
        toast.info('Scan cancelled');
      } catch (error) {
        console.error('Failed to stop scan:', error);
        toast.error('Failed to stop scan');
      }
    }
  };
  
  // Copy command to clipboard
  const copyCommand = async () => {
    try {
      await navigator.clipboard.writeText(generateCommand());
      toast.success('Command copied to clipboard');
    } catch (error) {
      toast.error('Failed to copy command');
    }
  };
  
  // Export results
  const exportResults = () => {
    if (!results && output.length === 0) {
      toast.error('No results to export');
      return;
    }
    
    const data = results || output.join('\n');
    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `nmap-scan-${new Date().toISOString().slice(0, 19)}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    
    toast.success('Results exported');
  };
  
  return (
    <div className="container-desktop py-6 space-y-6">
      {/* Tool Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className="p-2 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
            <Network className="h-6 w-6 text-blue-600 dark:text-blue-400" />
          </div>
          <div>
            <h1 className="text-2xl font-bold">Nmap Scanner</h1>
            <p className="text-muted-foreground">
              Network discovery and port scanning with OS and service detection
            </p>
          </div>
        </div>
        
        {/* Status indicators */}
        <div className="flex items-center gap-2">
          <Badge variant={backendStatus.connected ? 'default' : 'destructive'}>
            {backendStatus.connected ? 'Connected' : 'Offline'}
          </Badge>
          {isScanning && (
            <Badge variant="secondary" className="animate-pulse">
              Scanning...
            </Badge>
          )}
        </div>
      </div>
      
      {/* Main Interface */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="configure" className="flex items-center gap-2">
            <Settings className="h-4 w-4" />
            Configure
          </TabsTrigger>
          <TabsTrigger value="output" className="flex items-center gap-2">
            <Terminal className="h-4 w-4" />
            Output
          </TabsTrigger>
          <TabsTrigger value="results" className="flex items-center gap-2">
            <FileText className="h-4 w-4" />
            Results
          </TabsTrigger>
          <TabsTrigger value="command" className="flex items-center gap-2">
            <Target className="h-4 w-4" />
            Command
          </TabsTrigger>
        </TabsList>
        
        {/* Configuration Tab */}
        <TabsContent value="configure" className="space-y-6">
          <div className="grid gap-6 lg:grid-cols-2">
            {/* Basic Configuration */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Target className="h-5 w-5" />
                  Target Configuration
                </CardTitle>
                <CardDescription>
                  Specify the target hosts and basic scan parameters
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Target */}
                <div className="space-y-2">
                  <label className="text-sm font-medium">Target(s)</label>
                  <Input
                    placeholder="e.g., ***********, 10.0.0.0/24, example.com"
                    value={config.target}
                    onChange={(e) => updateConfig({ target: e.target.value })}
                  />
                  <p className="text-xs text-muted-foreground">
                    IP addresses, CIDR ranges, or hostnames
                  </p>
                </div>
                
                {/* Scan Type */}
                <div className="space-y-2">
                  <label className="text-sm font-medium">Scan Type</label>
                  <Select 
                    value={config.scanType} 
                    onValueChange={(value) => updateConfig({ scanType: value as any })}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="syn">SYN Scan (-sS)</SelectItem>
                      <SelectItem value="tcp">TCP Connect (-sT)</SelectItem>
                      <SelectItem value="udp">UDP Scan (-sU)</SelectItem>
                      <SelectItem value="comprehensive">Comprehensive (TCP+UDP)</SelectItem>
                      <SelectItem value="stealth">Stealth (Fragmented)</SelectItem>
                      <SelectItem value="aggressive">Aggressive (-A)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                {/* Port Range */}
                <div className="space-y-2">
                  <label className="text-sm font-medium">Port Range</label>
                  <Input
                    placeholder="e.g., 1-1000, 22,80,443, -"
                    value={config.portRange}
                    onChange={(e) => updateConfig({ portRange: e.target.value })}
                  />
                  <p className="text-xs text-muted-foreground">
                    Specific ports, ranges, or '-' for all ports
                  </p>
                </div>
                
                {/* Timing */}
                <div className="space-y-2">
                  <label className="text-sm font-medium">Timing Template</label>
                  <Select 
                    value={config.timing.toString()} 
                    onValueChange={(value) => updateConfig({ timing: parseInt(value) as any })}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="0">Paranoid (T0) - Very Slow</SelectItem>
                      <SelectItem value="1">Sneaky (T1) - Slow</SelectItem>
                      <SelectItem value="2">Polite (T2) - Normal</SelectItem>
                      <SelectItem value="3">Normal (T3) - Default</SelectItem>
                      <SelectItem value="4">Aggressive (T4) - Fast</SelectItem>
                      <SelectItem value="5">Insane (T5) - Very Fast</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </CardContent>
            </Card>
            
            {/* Advanced Options */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Shield className="h-5 w-5" />
                  Detection Options
                </CardTitle>
                <CardDescription>
                  Enable additional detection and enumeration features
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Detection Options */}
                <div className="space-y-3">
                  <label className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      checked={config.detectOS}
                      onChange={(e) => updateConfig({ detectOS: e.target.checked })}
                      className="rounded border-gray-300"
                    />
                    <span className="text-sm">OS Detection (-O)</span>
                  </label>
                  
                  <label className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      checked={config.detectVersion}
                      onChange={(e) => updateConfig({ detectVersion: e.target.checked })}
                      className="rounded border-gray-300"
                    />
                    <span className="text-sm">Version Detection (-sV)</span>
                  </label>
                  
                  <label className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      checked={config.enableScripts}
                      onChange={(e) => updateConfig({ enableScripts: e.target.checked })}
                      className="rounded border-gray-300"
                    />
                    <span className="text-sm">Enable NSE Scripts</span>
                  </label>
                </div>
                
                {/* Script Categories */}
                {config.enableScripts && (
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Script Categories</label>
                    <div className="grid grid-cols-2 gap-2">
                      {SCRIPT_CATEGORIES.map((category) => (
                        <label key={category.value} className="flex items-center space-x-2">
                          <input
                            type="checkbox"
                            checked={config.scriptCategories.includes(category.value)}
                            onChange={(e) => {
                              if (e.target.checked) {
                                updateConfig({
                                  scriptCategories: [...config.scriptCategories, category.value]
                                });
                              } else {
                                updateConfig({
                                  scriptCategories: config.scriptCategories.filter(c => c !== category.value)
                                });
                              }
                            }}
                            className="rounded border-gray-300"
                          />
                          <span className="text-xs">{category.label}</span>
                        </label>
                      ))}
                    </div>
                  </div>
                )}
                
                {/* Output Format */}
                <div className="space-y-2">
                  <label className="text-sm font-medium">Output Format</label>
                  <Select 
                    value={config.outputFormat} 
                    onValueChange={(value) => updateConfig({ outputFormat: value as any })}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="normal">Normal Text</SelectItem>
                      <SelectItem value="xml">XML Format</SelectItem>
                      <SelectItem value="grepable">Grepable Format</SelectItem>
                      <SelectItem value="json">JSON Format</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                {/* Custom Arguments */}
                <div className="space-y-2">
                  <label className="text-sm font-medium">Custom Arguments</label>
                  <Input
                    placeholder="Additional nmap arguments"
                    value={config.customArgs}
                    onChange={(e) => updateConfig({ customArgs: e.target.value })}
                  />
                </div>
              </CardContent>
            </Card>
          </div>
          
          {/* Presets and Actions */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Zap className="h-5 w-5" />
                Quick Presets & Actions
              </CardTitle>
              <CardDescription>
                Use predefined configurations or start your scan
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex flex-wrap gap-3">
                {/* Preset buttons */}
                {Object.keys(SCAN_PRESETS).map((preset) => (
                  <Button
                    key={preset}
                    variant="outline"
                    size="sm"
                    onClick={() => applyPreset(preset)}
                    className="capitalize"
                  >
                    {preset} Scan
                  </Button>
                ))}
                
                {/* Action buttons */}
                <div className="flex gap-2 ml-auto">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={copyCommand}
                  >
                    <Copy className="h-4 w-4 mr-1" />
                    Copy Command
                  </Button>
                  
                  {isScanning ? (
                    <Button
                      variant="destructive"
                      onClick={stopScan}
                    >
                      <Square className="h-4 w-4 mr-2" />
                      Stop Scan
                    </Button>
                  ) : (
                    <Button
                      onClick={startScan}
                      disabled={!config.target.trim() || !backendStatus.connected}
                    >
                      <Play className="h-4 w-4 mr-2" />
                      Start Scan
                    </Button>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        {/* Output Tab */}
        <TabsContent value="output" className="space-y-6">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="flex items-center gap-2">
                    <Terminal className="h-5 w-5" />
                    Real-time Output
                  </CardTitle>
                  <CardDescription>
                    Live output from the Nmap scan process
                  </CardDescription>
                </div>
                
                {/* Progress and controls */}
                <div className="flex items-center gap-4">
                  {isScanning && (
                    <div className="flex items-center gap-2">
                      <Progress value={progress} className="w-32" />
                      <span className="text-sm text-muted-foreground">
                        {progress}%
                      </span>
                    </div>
                  )}
                  
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setOutput([])}
                    disabled={isScanning}
                  >
                    <RotateCcw className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="bg-gray-900 text-gray-100 p-4 rounded-lg h-96 overflow-y-auto font-mono text-sm">
                {output.length === 0 ? (
                  <div className="text-gray-500 italic">
                    {isScanning ? 'Waiting for output...' : 'No output yet. Start a scan to see results here.'}
                  </div>
                ) : (
                  output.map((line, index) => (
                    <div key={index} className="mb-1">
                      {line}
                    </div>
                  ))
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        {/* Results Tab */}
        <TabsContent value="results" className="space-y-6">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="flex items-center gap-2">
                    <FileText className="h-5 w-5" />
                    Scan Results
                  </CardTitle>
                  <CardDescription>
                    Structured results and analysis from the completed scan
                  </CardDescription>
                </div>
                
                <Button
                  variant="outline"
                  onClick={exportResults}
                  disabled={!results && output.length === 0}
                >
                  <Download className="h-4 w-4 mr-2" />
                  Export Results
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              {results ? (
                <div className="space-y-4">
                  {/* Results will be displayed here */}
                  <pre className="bg-muted p-4 rounded-lg overflow-auto text-sm">
                    {JSON.stringify(results, null, 2)}
                  </pre>
                </div>
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  <FileText className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>No structured results available yet.</p>
                  <p className="text-sm">Complete a scan to see parsed results here.</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
        
        {/* Command Tab */}
        <TabsContent value="command" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Target className="h-5 w-5" />
                Generated Command
              </CardTitle>
              <CardDescription>
                The Nmap command that will be executed based on your configuration
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="bg-muted p-4 rounded-lg">
                  <code className="text-sm font-mono break-all">
                    {generateCommand()}
                  </code>
                </div>
                
                <div className="flex justify-between items-center">
                  <p className="text-sm text-muted-foreground">
                    This command will be executed on the backend server
                  </p>
                  
                  <Button variant="outline" onClick={copyCommand}>
                    <Copy className="h-4 w-4 mr-2" />
                    Copy Command
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}"