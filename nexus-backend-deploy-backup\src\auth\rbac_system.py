#!/usr/bin/env python3
"""
Advanced Role-Based Access Control (RBAC) System
Enterprise-grade access control with fine-grained permissions,
hierarchical roles, and comprehensive audit logging
"""

import asyncio
import json
import logging
import hashlib
import time
import secrets
from typing import Any, Dict, List, Optional, Set, Tuple, Union
from dataclasses import dataclass, field, asdict
from datetime import datetime, timedelta
from enum import Enum
from pathlib import Path
import sqlite3
import threading
# Try to import PyJWT, provide fallback if not available
try:
    import jwt
except ImportError:
    # Minimal JWT implementation for testing
    import base64
    import json
    import hmac
    import hashlib
    
    class jwt:
        @staticmethod
        def encode(payload: dict, key: str, algorithm: str = 'HS256') -> str:
            """Minimal JWT encoding for testing"""
            header = {"alg": algorithm, "typ": "JWT"}
            
            # Encode header and payload
            header_b64 = base64.urlsafe_b64encode(json.dumps(header).encode()).decode().rstrip('=')
            payload_b64 = base64.urlsafe_b64encode(json.dumps(payload).encode()).decode().rstrip('=')
            
            # Create signature
            message = f"{header_b64}.{payload_b64}"
            signature = hmac.new(key.encode(), message.encode(), hashlib.sha256).digest()
            signature_b64 = base64.urlsafe_b64encode(signature).decode().rstrip('=')
            
            return f"{header_b64}.{payload_b64}.{signature_b64}"
        
        @staticmethod
        def decode(token: str, key: str, algorithms: list = None) -> dict:
            """Minimal JWT decoding for testing"""
            try:
                parts = token.split('.')
                if len(parts) != 3:
                    raise ValueError("Invalid token format")
                
                header_b64, payload_b64, signature_b64 = parts
                
                # Verify signature
                message = f"{header_b64}.{payload_b64}"
                expected_sig = hmac.new(key.encode(), message.encode(), hashlib.sha256).digest()
                expected_sig_b64 = base64.urlsafe_b64encode(expected_sig).decode().rstrip('=')
                
                if signature_b64 != expected_sig_b64:
                    raise ValueError("Invalid signature")
                
                # Decode payload
                payload_json = base64.urlsafe_b64decode(payload_b64 + '===').decode()
                payload = json.loads(payload_json)
                
                # Check expiration
                import time
                if 'exp' in payload and payload['exp'] < time.time():
                    raise ValueError("Token expired")
                
                return payload
                
            except Exception:
                raise ValueError("Invalid token")
        
        class ExpiredSignatureError(Exception):
            pass
        
        class InvalidTokenError(Exception):
            pass

# Try to import werkzeug, fallback to hashlib if not available
try:
    from werkzeug.security import generate_password_hash, check_password_hash
except ImportError:
    import hashlib
    
    def generate_password_hash(password: str) -> str:
        """Fallback password hashing using hashlib"""
        return hashlib.sha256(password.encode()).hexdigest()
    
    def check_password_hash(password_hash: str, password: str) -> bool:
        """Fallback password verification using hashlib"""
        return password_hash == hashlib.sha256(password.encode()).hexdigest()

logger = logging.getLogger(__name__)

class PermissionType(Enum):
    """Types of permissions in the system"""
    # System Administration
    SYSTEM_ADMIN = "system:admin"
    SYSTEM_CONFIG = "system:config"
    SYSTEM_AUDIT = "system:audit"
    
    # User Management
    USER_CREATE = "user:create"
    USER_READ = "user:read"
    USER_UPDATE = "user:update"
    USER_DELETE = "user:delete"
    USER_ROLE_ASSIGN = "user:role:assign"
    
    # Campaign Management
    CAMPAIGN_CREATE = "campaign:create"
    CAMPAIGN_READ = "campaign:read"
    CAMPAIGN_UPDATE = "campaign:update"
    CAMPAIGN_DELETE = "campaign:delete"
    CAMPAIGN_EXECUTE = "campaign:execute"
    
    # Scan Operations
    SCAN_CREATE = "scan:create"
    SCAN_READ = "scan:read"
    SCAN_UPDATE = "scan:update"
    SCAN_DELETE = "scan:delete"
    SCAN_EXECUTE = "scan:execute"
    SCAN_RESULTS = "scan:results"
    
    # Tool Management
    TOOL_CONFIGURE = "tool:configure"
    TOOL_EXECUTE = "tool:execute"
    TOOL_READ = "tool:read"
    
    # AI Services
    AI_USE = "ai:use"
    AI_CONFIGURE = "ai:configure"
    AI_ADMIN = "ai:admin"
    
    # Reporting
    REPORT_CREATE = "report:create"
    REPORT_READ = "report:read"
    REPORT_EXPORT = "report:export"
    REPORT_ADMIN = "report:admin"
    
    # Threat Intelligence
    INTEL_READ = "intel:read"
    INTEL_CREATE = "intel:create"
    INTEL_ADMIN = "intel:admin"
    
    # Organization Management
    ORG_ADMIN = "org:admin"
    ORG_READ = "org:read"
    ORG_UPDATE = "org:update"

class RoleType(Enum):
    """Predefined role types"""
    SUPER_ADMIN = "super_admin"
    ORG_ADMIN = "org_admin"
    SECURITY_ANALYST = "security_analyst"
    PENETRATION_TESTER = "penetration_tester"
    SECURITY_MANAGER = "security_manager"
    AUDITOR = "auditor"
    VIEWER = "viewer"
    API_USER = "api_user"

class ActionType(Enum):
    """Types of actions for audit logging"""
    LOGIN = "login"
    LOGOUT = "logout"
    ACCESS_GRANTED = "access_granted"
    ACCESS_DENIED = "access_denied"
    PERMISSION_CHECK = "permission_check"
    ROLE_ASSIGNED = "role_assigned"
    ROLE_REVOKED = "role_revoked"
    USER_CREATED = "user_created"
    USER_UPDATED = "user_updated"
    USER_DELETED = "user_deleted"
    CAMPAIGN_CREATED = "campaign_created"
    SCAN_EXECUTED = "scan_executed"
    REPORT_GENERATED = "report_generated"

@dataclass
class Permission:
    """Individual permission definition"""
    permission_id: str
    permission_type: PermissionType
    resource: str = "*"  # Specific resource or wildcard
    conditions: Dict[str, Any] = field(default_factory=dict)
    description: str = ""
    
    def matches(self, resource: str, context: Optional[Dict[str, Any]] = None) -> bool:
        """Check if permission matches given resource and context"""
        # Check resource match
        if self.resource != "*" and self.resource != resource:
            return False
        
        # Check conditions
        if self.conditions and context:
            for key, value in self.conditions.items():
                if key not in context or context[key] != value:
                    return False
        
        return True
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            "permission_id": self.permission_id,
            "permission_type": self.permission_type.value,
            "resource": self.resource,
            "conditions": self.conditions,
            "description": self.description
        }

@dataclass
class Role:
    """Role definition with permissions"""
    role_id: str
    role_name: str
    role_type: RoleType
    permissions: List[Permission] = field(default_factory=list)
    parent_roles: List[str] = field(default_factory=list)  # Role inheritance
    organization_id: Optional[str] = None
    is_system_role: bool = False
    created_at: datetime = field(default_factory=datetime.now)
    description: str = ""
    
    def has_permission(self, permission_type: PermissionType, resource: str = "*", context: Optional[Dict[str, Any]] = None) -> bool:
        """Check if role has specific permission"""
        for permission in self.permissions:
            if (permission.permission_type == permission_type and 
                permission.matches(resource, context)):
                return True
        return False
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            "role_id": self.role_id,
            "role_name": self.role_name,
            "role_type": self.role_type.value,
            "permissions": [p.to_dict() for p in self.permissions],
            "parent_roles": self.parent_roles,
            "organization_id": self.organization_id,
            "is_system_role": self.is_system_role,
            "created_at": self.created_at.isoformat(),
            "description": self.description
        }

@dataclass
class User:
    """User with roles and authentication"""
    user_id: str
    username: str
    email: str
    password_hash: str
    roles: List[str] = field(default_factory=list)  # Role IDs
    organization_id: Optional[str] = None
    is_active: bool = True
    is_verified: bool = False
    last_login: Optional[datetime] = None
    created_at: datetime = field(default_factory=datetime.now)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def add_role(self, role_id: str):
        """Add role to user"""
        if role_id not in self.roles:
            self.roles.append(role_id)
    
    def remove_role(self, role_id: str):
        """Remove role from user"""
        if role_id in self.roles:
            self.roles.remove(role_id)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary (without password)"""
        return {
            "user_id": self.user_id,
            "username": self.username,
            "email": self.email,
            "roles": self.roles,
            "organization_id": self.organization_id,
            "is_active": self.is_active,
            "is_verified": self.is_verified,
            "last_login": self.last_login.isoformat() if self.last_login else None,
            "created_at": self.created_at.isoformat(),
            "metadata": self.metadata
        }

@dataclass
class Organization:
    """Organization for multi-tenancy"""
    org_id: str
    org_name: str
    org_type: str = "enterprise"
    parent_org_id: Optional[str] = None
    settings: Dict[str, Any] = field(default_factory=dict)
    is_active: bool = True
    created_at: datetime = field(default_factory=datetime.now)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return asdict(self)

@dataclass
class AuditLog:
    """Audit log entry"""
    log_id: str
    user_id: str
    action: ActionType
    resource: str
    organization_id: Optional[str] = None
    success: bool = True
    details: Dict[str, Any] = field(default_factory=dict)
    ip_address: Optional[str] = None
    user_agent: Optional[str] = None
    timestamp: datetime = field(default_factory=datetime.now)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        result = asdict(self)
        result['action'] = self.action.value
        result['timestamp'] = self.timestamp.isoformat()
        return result

class RBACDatabase:
    """Database manager for RBAC system"""
    
    def __init__(self, db_path: str = "data/rbac.db"):
        self.db_path = db_path
        self._lock = threading.RLock()
        self._initialize_database()
    
    def _initialize_database(self):
        """Initialize RBAC database schema"""
        Path(self.db_path).parent.mkdir(parents=True, exist_ok=True)
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Organizations table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS organizations (
                org_id TEXT PRIMARY KEY,
                org_name TEXT NOT NULL,
                org_type TEXT DEFAULT 'enterprise',
                parent_org_id TEXT,
                settings TEXT,
                is_active BOOLEAN DEFAULT 1,
                created_at TEXT NOT NULL
            )
        """)
        
        # Users table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS users (
                user_id TEXT PRIMARY KEY,
                username TEXT UNIQUE NOT NULL,
                email TEXT UNIQUE NOT NULL,
                password_hash TEXT NOT NULL,
                organization_id TEXT,
                is_active BOOLEAN DEFAULT 1,
                is_verified BOOLEAN DEFAULT 0,
                last_login TEXT,
                created_at TEXT NOT NULL,
                metadata TEXT,
                FOREIGN KEY (organization_id) REFERENCES organizations (org_id)
            )
        """)
        
        # Roles table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS roles (
                role_id TEXT PRIMARY KEY,
                role_name TEXT NOT NULL,
                role_type TEXT NOT NULL,
                parent_roles TEXT,
                organization_id TEXT,
                is_system_role BOOLEAN DEFAULT 0,
                created_at TEXT NOT NULL,
                description TEXT,
                FOREIGN KEY (organization_id) REFERENCES organizations (org_id)
            )
        """)
        
        # Permissions table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS permissions (
                permission_id TEXT PRIMARY KEY,
                permission_type TEXT NOT NULL,
                resource TEXT DEFAULT '*',
                conditions TEXT,
                description TEXT
            )
        """)
        
        # Role-Permission mapping
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS role_permissions (
                role_id TEXT,
                permission_id TEXT,
                PRIMARY KEY (role_id, permission_id),
                FOREIGN KEY (role_id) REFERENCES roles (role_id),
                FOREIGN KEY (permission_id) REFERENCES permissions (permission_id)
            )
        """)
        
        # User-Role mapping
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS user_roles (
                user_id TEXT,
                role_id TEXT,
                assigned_at TEXT NOT NULL,
                assigned_by TEXT,
                PRIMARY KEY (user_id, role_id),
                FOREIGN KEY (user_id) REFERENCES users (user_id),
                FOREIGN KEY (role_id) REFERENCES roles (role_id)
            )
        """)
        
        # Audit logs table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS audit_logs (
                log_id TEXT PRIMARY KEY,
                user_id TEXT NOT NULL,
                action TEXT NOT NULL,
                resource TEXT NOT NULL,
                organization_id TEXT,
                success BOOLEAN DEFAULT 1,
                details TEXT,
                ip_address TEXT,
                user_agent TEXT,
                timestamp TEXT NOT NULL,
                FOREIGN KEY (user_id) REFERENCES users (user_id)
            )
        """)
        
        # Create indexes
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_users_org ON users(organization_id)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_users_username ON users(username)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_audit_user ON audit_logs(user_id)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_audit_timestamp ON audit_logs(timestamp)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_roles_org ON roles(organization_id)")
        
        conn.commit()
        conn.close()
    
    def create_organization(self, organization: Organization) -> bool:
        """Create new organization"""
        with self._lock:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            try:
                cursor.execute("""
                    INSERT INTO organizations 
                    (org_id, org_name, org_type, parent_org_id, settings, is_active, created_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                """, (
                    organization.org_id,
                    organization.org_name,
                    organization.org_type,
                    organization.parent_org_id,
                    json.dumps(organization.settings),
                    organization.is_active,
                    organization.created_at.isoformat()
                ))
                
                conn.commit()
                return True
                
            except sqlite3.IntegrityError:
                return False
            finally:
                conn.close()
    
    def create_user(self, user: User) -> bool:
        """Create new user"""
        with self._lock:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            try:
                cursor.execute("""
                    INSERT INTO users 
                    (user_id, username, email, password_hash, organization_id, 
                     is_active, is_verified, created_at, metadata)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    user.user_id,
                    user.username,
                    user.email,
                    user.password_hash,
                    user.organization_id,
                    user.is_active,
                    user.is_verified,
                    user.created_at.isoformat(),
                    json.dumps(user.metadata)
                ))
                
                conn.commit()
                return True
                
            except sqlite3.IntegrityError:
                return False
            finally:
                conn.close()
    
    def get_user_by_username(self, username: str) -> Optional[User]:
        """Get user by username"""
        with self._lock:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT user_id, username, email, password_hash, organization_id,
                       is_active, is_verified, last_login, created_at, metadata
                FROM users WHERE username = ?
            """, (username,))
            
            row = cursor.fetchone()
            conn.close()
            
            if row:
                user = User(
                    user_id=row[0],
                    username=row[1],
                    email=row[2],
                    password_hash=row[3],
                    organization_id=row[4],
                    is_active=bool(row[5]),
                    is_verified=bool(row[6]),
                    last_login=datetime.fromisoformat(row[7]) if row[7] else None,
                    created_at=datetime.fromisoformat(row[8]),
                    metadata=json.loads(row[9]) if row[9] else {}
                )
                
                # Load user roles
                user.roles = self._get_user_roles(user.user_id)
                return user
            
            return None
    
    def _get_user_roles(self, user_id: str) -> List[str]:
        """Get roles for user"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT role_id FROM user_roles WHERE user_id = ?
        """, (user_id,))
        
        roles = [row[0] for row in cursor.fetchall()]
        conn.close()
        return roles
    
    def create_role(self, role: Role) -> bool:
        """Create new role"""
        with self._lock:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            try:
                cursor.execute("""
                    INSERT INTO roles 
                    (role_id, role_name, role_type, parent_roles, organization_id,
                     is_system_role, created_at, description)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    role.role_id,
                    role.role_name,
                    role.role_type.value,
                    json.dumps(role.parent_roles),
                    role.organization_id,
                    role.is_system_role,
                    role.created_at.isoformat(),
                    role.description
                ))
                
                conn.commit()
                return True
                
            except sqlite3.IntegrityError:
                return False
            finally:
                conn.close()
    
    def get_role(self, role_id: str) -> Optional[Role]:
        """Get role by ID"""
        with self._lock:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT role_id, role_name, role_type, parent_roles, organization_id,
                       is_system_role, created_at, description
                FROM roles WHERE role_id = ?
            """, (role_id,))
            
            row = cursor.fetchone()
            if row:
                role = Role(
                    role_id=row[0],
                    role_name=row[1],
                    role_type=RoleType(row[2]),
                    parent_roles=json.loads(row[3]) if row[3] else [],
                    organization_id=row[4],
                    is_system_role=bool(row[5]),
                    created_at=datetime.fromisoformat(row[6]),
                    description=row[7] or ""
                )
                
                # Load permissions
                role.permissions = self._get_role_permissions(role_id)
                conn.close()
                return role
            
            conn.close()
            return None
    
    def _get_role_permissions(self, role_id: str) -> List[Permission]:
        """Get permissions for role"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT p.permission_id, p.permission_type, p.resource, p.conditions, p.description
            FROM permissions p
            JOIN role_permissions rp ON p.permission_id = rp.permission_id
            WHERE rp.role_id = ?
        """, (role_id,))
        
        permissions = []
        for row in cursor.fetchall():
            permission = Permission(
                permission_id=row[0],
                permission_type=PermissionType(row[1]),
                resource=row[2],
                conditions=json.loads(row[3]) if row[3] else {},
                description=row[4] or ""
            )
            permissions.append(permission)
        
        conn.close()
        return permissions
    
    def assign_role_to_user(self, user_id: str, role_id: str, assigned_by: str) -> bool:
        """Assign role to user"""
        with self._lock:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            try:
                cursor.execute("""
                    INSERT INTO user_roles (user_id, role_id, assigned_at, assigned_by)
                    VALUES (?, ?, ?, ?)
                """, (user_id, role_id, datetime.now().isoformat(), assigned_by))
                
                conn.commit()
                return True
                
            except sqlite3.IntegrityError:
                return False
            finally:
                conn.close()
    
    def log_audit_event(self, audit_log: AuditLog) -> bool:
        """Log audit event"""
        with self._lock:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            try:
                cursor.execute("""
                    INSERT INTO audit_logs 
                    (log_id, user_id, action, resource, organization_id, success,
                     details, ip_address, user_agent, timestamp)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    audit_log.log_id,
                    audit_log.user_id,
                    audit_log.action.value,
                    audit_log.resource,
                    audit_log.organization_id,
                    audit_log.success,
                    json.dumps(audit_log.details),
                    audit_log.ip_address,
                    audit_log.user_agent,
                    audit_log.timestamp.isoformat()
                ))
                
                conn.commit()
                return True
                
            except Exception as e:
                logger.error(f"Failed to log audit event: {e}")
                return False
            finally:
                conn.close()

class RBACManager:
    """Main RBAC management system"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        self.config = config or {}
        self.db = RBACDatabase(self.config.get('db_path', 'data/rbac.db'))
        self.jwt_secret = self.config.get('jwt_secret', secrets.token_urlsafe(32))
        self.token_expiry_hours = self.config.get('token_expiry_hours', 24)
        
        # Permission cache
        self._permission_cache: Dict[str, Dict[str, bool]] = {}
        self._cache_lock = threading.RLock()
        
        # Initialize system roles and permissions
        self._initialize_system_roles()
    
    def _initialize_system_roles(self):
        """Initialize predefined system roles"""
        system_roles = self._get_system_role_definitions()
        
        for role_data in system_roles:
            # Check if role already exists
            existing_role = self.db.get_role(role_data['role_id'])
            if not existing_role:
                # Create permissions first
                for perm_data in role_data['permissions']:
                    self._create_permission_if_not_exists(perm_data)
                
                # Create role
                role = Role(
                    role_id=role_data['role_id'],
                    role_name=role_data['role_name'],
                    role_type=RoleType(role_data['role_type']),
                    permissions=[Permission(**p) for p in role_data['permissions']],
                    is_system_role=True,
                    description=role_data['description']
                )
                
                self.db.create_role(role)
                
                # Link permissions to role
                for perm_data in role_data['permissions']:
                    self._link_permission_to_role(role.role_id, perm_data['permission_id'])
    
    def _get_system_role_definitions(self) -> List[Dict[str, Any]]:
        """Get predefined system role definitions"""
        return [
            {
                'role_id': 'super_admin',
                'role_name': 'Super Administrator',
                'role_type': 'super_admin',
                'description': 'Full system access',
                'permissions': [
                    {
                        'permission_id': 'system_all',
                        'permission_type': PermissionType.SYSTEM_ADMIN,
                        'resource': '*',
                        'conditions': {},
                        'description': 'Full system administration'
                    }
                ]
            },
            {
                'role_id': 'security_analyst',
                'role_name': 'Security Analyst',
                'role_type': 'security_analyst',
                'description': 'Security analysis and reporting',
                'permissions': [
                    {
                        'permission_id': 'scan_read',
                        'permission_type': PermissionType.SCAN_READ,
                        'resource': '*',
                        'conditions': {},
                        'description': 'Read scan results'
                    },
                    {
                        'permission_id': 'report_create',
                        'permission_type': PermissionType.REPORT_CREATE,
                        'resource': '*',
                        'conditions': {},
                        'description': 'Create reports'
                    },
                    {
                        'permission_id': 'intel_read',
                        'permission_type': PermissionType.INTEL_READ,
                        'resource': '*',
                        'conditions': {},
                        'description': 'Read threat intelligence'
                    }
                ]
            },
            {
                'role_id': 'penetration_tester',
                'role_name': 'Penetration Tester',
                'role_type': 'penetration_tester',
                'description': 'Execute penetration tests',
                'permissions': [
                    {
                        'permission_id': 'scan_execute',
                        'permission_type': PermissionType.SCAN_EXECUTE,
                        'resource': '*',
                        'conditions': {},
                        'description': 'Execute scans'
                    },
                    {
                        'permission_id': 'tool_execute',
                        'permission_type': PermissionType.TOOL_EXECUTE,
                        'resource': '*',
                        'conditions': {},
                        'description': 'Execute security tools'
                    },
                    {
                        'permission_id': 'ai_use',
                        'permission_type': PermissionType.AI_USE,
                        'resource': '*',
                        'conditions': {},
                        'description': 'Use AI services'
                    }
                ]
            },
            {
                'role_id': 'viewer',
                'role_name': 'Viewer',
                'role_type': 'viewer',
                'description': 'Read-only access',
                'permissions': [
                    {
                        'permission_id': 'campaign_read',
                        'permission_type': PermissionType.CAMPAIGN_READ,
                        'resource': '*',
                        'conditions': {},
                        'description': 'View campaigns'
                    },
                    {
                        'permission_id': 'scan_read_basic',
                        'permission_type': PermissionType.SCAN_READ,
                        'resource': '*',
                        'conditions': {},
                        'description': 'View scan results'
                    }
                ]
            }
        ]
    
    def _create_permission_if_not_exists(self, perm_data: Dict[str, Any]):
        """Create permission if it doesn't exist"""
        conn = sqlite3.connect(self.db.db_path)
        cursor = conn.cursor()
        
        cursor.execute("SELECT permission_id FROM permissions WHERE permission_id = ?", 
                      (perm_data['permission_id'],))
        
        if not cursor.fetchone():
            cursor.execute("""
                INSERT INTO permissions (permission_id, permission_type, resource, conditions, description)
                VALUES (?, ?, ?, ?, ?)
            """, (
                perm_data['permission_id'],
                perm_data['permission_type'].value,
                perm_data['resource'],
                json.dumps(perm_data['conditions']),
                perm_data['description']
            ))
        
        conn.commit()
        conn.close()
    
    def _link_permission_to_role(self, role_id: str, permission_id: str):
        """Link permission to role"""
        conn = sqlite3.connect(self.db.db_path)
        cursor = conn.cursor()
        
        try:
            cursor.execute("""
                INSERT OR IGNORE INTO role_permissions (role_id, permission_id)
                VALUES (?, ?)
            """, (role_id, permission_id))
            
            conn.commit()
        except Exception as e:
            logger.error(f"Failed to link permission {permission_id} to role {role_id}: {e}")
        finally:
            conn.close()
    
    async def create_organization(self, org_name: str, org_type: str = "enterprise", 
                                parent_org_id: Optional[str] = None) -> Organization:
        """Create new organization"""
        org_id = hashlib.md5(f"{org_name}_{time.time()}".encode()).hexdigest()
        
        organization = Organization(
            org_id=org_id,
            org_name=org_name,
            org_type=org_type,
            parent_org_id=parent_org_id
        )
        
        success = self.db.create_organization(organization)
        if not success:
            raise ValueError(f"Failed to create organization: {org_name}")
        
        logger.info(f"Created organization: {org_name} ({org_id})")
        return organization
    
    async def create_user(self, username: str, email: str, password: str,
                         organization_id: Optional[str] = None, 
                         roles: Optional[List[str]] = None) -> User:
        """Create new user"""
        user_id = hashlib.md5(f"{username}_{email}_{time.time()}".encode()).hexdigest()
        password_hash = generate_password_hash(password)
        
        user = User(
            user_id=user_id,
            username=username,
            email=email,
            password_hash=password_hash,
            organization_id=organization_id,
            roles=roles or []
        )
        
        success = self.db.create_user(user)
        if not success:
            raise ValueError(f"Failed to create user: {username}")
        
        # Assign roles
        if roles:
            for role_id in roles:
                self.db.assign_role_to_user(user_id, role_id, "system")
        
        # Log audit event
        await self._log_audit(user_id, ActionType.USER_CREATED, f"user:{user_id}", 
                            organization_id, True, {"username": username})
        
        logger.info(f"Created user: {username} ({user_id})")
        return user
    
    async def authenticate_user(self, username: str, password: str, 
                              ip_address: Optional[str] = None) -> Optional[str]:
        """Authenticate user and return JWT token"""
        user = self.db.get_user_by_username(username)
        
        if not user or not user.is_active:
            await self._log_audit(user.user_id if user else "unknown", 
                                ActionType.LOGIN, "authentication", 
                                user.organization_id if user else None, 
                                False, {"reason": "user_not_found_or_inactive"}, ip_address)
            return None
        
        if not check_password_hash(user.password_hash, password):
            await self._log_audit(user.user_id, ActionType.LOGIN, "authentication", 
                                user.organization_id, False, 
                                {"reason": "invalid_password"}, ip_address)
            return None
        
        # Generate JWT token
        payload = {
            'user_id': user.user_id,
            'username': user.username,
            'organization_id': user.organization_id,
            'roles': user.roles,
            'exp': datetime.utcnow() + timedelta(hours=self.token_expiry_hours),
            'iat': datetime.utcnow()
        }
        
        token = jwt.encode(payload, self.jwt_secret, algorithm='HS256')
        
        # Update last login
        conn = sqlite3.connect(self.db.db_path)
        cursor = conn.cursor()
        cursor.execute("UPDATE users SET last_login = ? WHERE user_id = ?", 
                      (datetime.now().isoformat(), user.user_id))
        conn.commit()
        conn.close()
        
        # Log successful login
        await self._log_audit(user.user_id, ActionType.LOGIN, "authentication", 
                            user.organization_id, True, 
                            {"ip_address": ip_address}, ip_address)
        
        logger.info(f"User authenticated: {username}")
        return token
    
    def verify_token(self, token: str) -> Optional[Dict[str, Any]]:
        """Verify JWT token and return payload"""
        try:
            payload = jwt.decode(token, self.jwt_secret, algorithms=['HS256'])
            return payload
        except (ValueError, Exception) as e:
            if "expired" in str(e).lower():
                logger.warning("Token expired")
            else:
                logger.warning("Invalid token")
            return None
    
    async def check_permission(self, user_id: str, permission_type: PermissionType,
                             resource: str = "*", context: Optional[Dict[str, Any]] = None) -> bool:
        """Check if user has specific permission"""
        
        # Check cache first
        cache_key = f"{user_id}:{permission_type.value}:{resource}"
        with self._cache_lock:
            if cache_key in self._permission_cache:
                result = self._permission_cache[cache_key].get('result', False)
                # Check cache expiry (5 minutes)
                if time.time() - self._permission_cache[cache_key]['timestamp'] < 300:
                    return result
        
        # Get user
        user = self.db.get_user_by_username(self._get_username_by_id(user_id))
        if not user or not user.is_active:
            await self._log_audit(user_id, ActionType.ACCESS_DENIED, resource, 
                                user.organization_id if user else None, False,
                                {"reason": "user_not_found_or_inactive"})
            return False
        
        # Check permissions through roles
        has_permission = False
        
        for role_id in user.roles:
            role = self.db.get_role(role_id)
            if role and role.has_permission(permission_type, resource, context):
                has_permission = True
                break
        
        # Cache result
        with self._cache_lock:
            self._permission_cache[cache_key] = {
                'result': has_permission,
                'timestamp': time.time()
            }
        
        # Log permission check
        action = ActionType.ACCESS_GRANTED if has_permission else ActionType.ACCESS_DENIED
        await self._log_audit(user_id, action, resource, user.organization_id, 
                            has_permission, {"permission": permission_type.value})
        
        return has_permission
    
    def _get_username_by_id(self, user_id: str) -> Optional[str]:
        """Get username by user ID"""
        conn = sqlite3.connect(self.db.db_path)
        cursor = conn.cursor()
        cursor.execute("SELECT username FROM users WHERE user_id = ?", (user_id,))
        result = cursor.fetchone()
        conn.close()
        return result[0] if result else None
    
    async def assign_role(self, user_id: str, role_id: str, assigned_by: str) -> bool:
        """Assign role to user"""
        success = self.db.assign_role_to_user(user_id, role_id, assigned_by)
        
        if success:
            # Clear permission cache for user
            self._clear_user_cache(user_id)
            
            # Get user for organization context
            user = self.db.get_user_by_username(self._get_username_by_id(user_id))
            
            await self._log_audit(assigned_by, ActionType.ROLE_ASSIGNED, f"user:{user_id}",
                                user.organization_id if user else None, True,
                                {"role_id": role_id, "target_user": user_id})
            
            logger.info(f"Assigned role {role_id} to user {user_id}")
        
        return success
    
    def _clear_user_cache(self, user_id: str):
        """Clear permission cache for user"""
        with self._cache_lock:
            keys_to_remove = [key for key in self._permission_cache.keys() 
                            if key.startswith(f"{user_id}:")]
            for key in keys_to_remove:
                del self._permission_cache[key]
    
    async def _log_audit(self, user_id: str, action: ActionType, resource: str,
                        organization_id: Optional[str], success: bool,
                        details: Optional[Dict[str, Any]] = None,
                        ip_address: Optional[str] = None,
                        user_agent: Optional[str] = None):
        """Log audit event"""
        log_id = hashlib.md5(f"{user_id}_{action.value}_{time.time()}".encode()).hexdigest()
        
        audit_log = AuditLog(
            log_id=log_id,
            user_id=user_id,
            action=action,
            resource=resource,
            organization_id=organization_id,
            success=success,
            details=details or {},
            ip_address=ip_address,
            user_agent=user_agent
        )
        
        self.db.log_audit_event(audit_log)
    
    def get_user_permissions(self, user_id: str) -> List[Dict[str, Any]]:
        """Get all permissions for user"""
        user = self.db.get_user_by_username(self._get_username_by_id(user_id))
        if not user:
            return []
        
        all_permissions = []
        for role_id in user.roles:
            role = self.db.get_role(role_id)
            if role:
                for permission in role.permissions:
                    all_permissions.append({
                        'permission_type': permission.permission_type.value,
                        'resource': permission.resource,
                        'role': role.role_name,
                        'description': permission.description
                    })
        
        return all_permissions
    
    def get_system_statistics(self) -> Dict[str, Any]:
        """Get RBAC system statistics"""
        conn = sqlite3.connect(self.db.db_path)
        cursor = conn.cursor()
        
        # Count users
        cursor.execute("SELECT COUNT(*) FROM users WHERE is_active = 1")
        active_users = cursor.fetchone()[0]
        
        # Count organizations
        cursor.execute("SELECT COUNT(*) FROM organizations WHERE is_active = 1")
        active_orgs = cursor.fetchone()[0]
        
        # Count roles
        cursor.execute("SELECT COUNT(*) FROM roles")
        total_roles = cursor.fetchone()[0]
        
        # Recent logins (last 24 hours)
        yesterday = (datetime.now() - timedelta(hours=24)).isoformat()
        cursor.execute("""
            SELECT COUNT(*) FROM audit_logs 
            WHERE action = 'login' AND success = 1 AND timestamp > ?
        """, (yesterday,))
        recent_logins = cursor.fetchone()[0]
        
        conn.close()
        
        return {
            'active_users': active_users,
            'active_organizations': active_orgs,
            'total_roles': total_roles,
            'recent_logins_24h': recent_logins,
            'cache_size': len(self._permission_cache)
        }

# Global RBAC manager instance
rbac_manager: Optional[RBACManager] = None

def get_rbac_manager() -> RBACManager:
    """Get global RBAC manager instance"""
    global rbac_manager
    
    if rbac_manager is None:
        rbac_manager = RBACManager()
    
    return rbac_manager

def close_rbac_manager():
    """Close global RBAC manager"""
    global rbac_manager
    rbac_manager = None