/**
 * Masscan Scanner Component
 * High-speed port scanner for large-scale network scanning
 */
import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { 
  Zap,
  Play,
  Square,
  Download,
  Settings,
  Target,
  Gauge,
  Network,
  Clock,
  Terminal,
  FileText,
  AlertTriangle,
  CheckCircle,
  Copy,
  RotateCcw,
  TrendingUp
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useBackendStore } from '@/stores/backend-store';
import { apiClient, ToolExecution } from '@/services/api-client';
import { toast } from 'sonner';

/**
 * Masscan configuration interface
 */
interface MasscanConfig {
  targets: string;
  ports: string;
  rate: number;
  maxRuntime: number;
  excludeRanges: string;
  sourcePort: string;
  sourceIP: string;
  interface: string;
  outputFormat: 'list' | 'json' | 'xml' | 'binary';
  banners: boolean;
  includeClosed: boolean;
  ping: boolean;
  randomizeTargets: boolean;
  customArgs: string;
}

/**
 * Rate presets for different scanning scenarios
 */
const RATE_PRESETS = {
  'conservative': { rate: 100, label: 'Conservative (100 pps)', description: 'Safe for most networks' },
  'moderate': { rate: 1000, label: 'Moderate (1K pps)', description: 'Good balance of speed and safety' },
  'aggressive': { rate: 10000, label: 'Aggressive (10K pps)', description: 'Fast scanning, may trigger IDS' },
  'maximum': { rate: 100000, label: 'Maximum (100K pps)', description: 'Maximum speed, high risk' },
  'internet': { rate: 1000000, label: 'Internet Scale (1M pps)', description: 'For Internet-wide scans' }
};

/**
 * Common port configurations
 */
const PORT_PRESETS = {
  'top100': '7,9,13,21-23,25-26,37,53,79-81,88,106,110-111,113,119,135,139,143-144,179,199,389,427,443-445,465,513-515,543-544,548,554,587,631,646,873,990,993,995,1025-1029,1110,1433,1720,1723,1755,1900,2000-2001,2049,2121,2717,3000,3128,3306,3389,3986,4899,5000,5009,5051,5060,5101,5190,5357,5432,5631,5666,5800,5900,6000-6001,6646,7070,8000,8008-8009,8080-8081,8443,8888,9100,9999-10000,32768,49152-49157',
  'top1000': '1,3-4,6-7,9,13,17,19-26,30,32-33,37,42-43,49,53,70,79-85,88-90,99-100,106,109-111,113,119,125,135,139,143-144,146,161,163,179,199,211-212,222,254-256,259,264,280,301,306,311,340,366,389,406-407,416,417,425,427,443-445,458,464-465,481,497,500,512-515,524,541,543-545,548,554-555,563,587,593,616-617,625,631,636,646,648,666-668,683,687,691,700,705,711,714,720,722,726,749,765,777,783,787,800-801,808,843,873,880,888,898,900-903,911-912,981,987,990,992-995,999-1002,1007,1009-1011,1021-1100,1102,1104-1108,1110-1114,1117,1119,1121-1124,1126,1130-1132,1137-1138,1141,1145,1147-1149,1151-1152,1154,1163-1166,1169,1174-1175,1183,1185-1187,1192,1198-1199,1201,1213,1216-1218,1233-1234,1236,1244,1247-1248,1259,1271-1272,1277,1287,1296,1300-1301,1309-1311,1322,1328,1334,1352,1417,1433-1434,1443,1455,1461,1494,1500-1501,1503,1521,1524,1533,1556,1580,1583,1594,1600,1641,1658,1666,1687-1688,1700,1717-1721,1723,1755,1761,1782-1783,1801,1805,1812,1839-1840,1862-1864,1875,1900,1914,1935,1947,1971-1972,1974,1984,1998-2010,2013,2020-2022,2030,2033-2035,2038,2040-2043,2045-2049,2065,2068,2099-2100,2103,2105-2107,2111,2119,2121,2126,2135,2144,2160-2161,2170,2179,2190-2191,2196,2200,2222,2251,2260,2288,2301,2323,2366,2381-2383,2393-2394,2399,2401,2492,2500,2522,2525,2557,2601-2602,2604-2605,2607-2608,2638,2701-2702,2710,2717-2718,2725,2800,2809,2811,2869,2875,2909-2910,2920,2967-2968,2998,3000-3001,3003,3005-3007,3011,3013,3017,3030-3031,3052,3071,3077,3128,3168,3211,3221,3260-3261,3268-3269,3283,3300-3301,3306,3322-3325,3333,3351,3367,3369-3372,3389-3390,3404,3476,3493,3517,3527,3546,3551,3580,3659,3689-3690,3703,3737,3766,3784,3800-3801,3809,3814,3826-3828,3851,3869,3871,3878,3880,3889,3905,3914,3918,3920,3945,3971,3986,3995,3998,4000-4006,4045,4111,4125-4126,4129,4224,4242,4279,4321,4343,4443-4446,4449,4550,4567,4662,4848,4899-4900,4998,5000-5004,5009,5030,5033,5050-5051,5054,5060-5061,5080,5087,5100-5102,5120,5190,5200,5214,5221-5222,5225-5226,5269,5280,5298,5357,5405,5414,5431-5432,5440,5500,5510,5544,5550,5555,5560,5566,5631,5633,5666,5678-5679,5718,5730,5800-5802,5810-5811,5815,5822,5825,5850,5859,5862,5877,5900-5904,5906-5907,5910-5911,5915,5922,5925,5950,5952,5959-5963,5987-5989,5998-6007,6009,6025,6059,6100-6101,6106,6112,6123,6129,6156,6346,6389,6502,6510,6543,6547,6565-6567,6580,6646,6666-6669,6689,6692,6699,6779,6788-6789,6792,6839,6881,6901,6969,7000-7002,7004,7007,7019,7025,7070,7100,7103,7106,7200-7201,7402,7435,7443,7496,7512,7625,7627,7676,7741,7777-7778,7800,7911,7920-7921,7937-7938,7999-8002,8007-8011,8021-8022,8031,8042,8045,8080-8090,8093,8099-8100,8180-8181,8192-8194,8200,8222,8254,8290-8292,8300,8333,8383,8400,8402,8443,8500,8600,8649,8651-8652,8654,8701,8800,8873,8888,8899,8994,9000-9003,9009-9011,9040,9050,9071,9080-9081,9090-9091,9099-9103,9110-9111,9200,9207,9220,9290,9415,9418,9485,9500,9502-9503,9535,9575,9593-9595,9618,9666,9876-9878,9898,9900,9917,9929,9943-9944,9968,9998-10004,10009-10010,10012,10024-10025,10082,10180,10215,10243,10566,10616-10617,10621,10626,10628-10629,10778,11110-11111,11967,12000,12174,12265,12345,13456,13722,13782-13783,14000,14238,14441-14442,15000,15002-15004,15660,15742,16000-16001,16012,16016,16018,16080,16113,16992-16993,17877,17988,18040,18101,18988,19101,19283,19315,19350,19780,19801,19842,20000,20005,20031,20221-20222,20828,21571,22939,23502,24444,24800,25734-25735,26214,27000,27352-27353,27355-27356,27715,28201,30000,30718,30951,31038,31337,32768-32785,33354,33899,34571-34573,35500,38292,40193,40911,41511,42510,44176,44442-44443,44501,45100,48080,49152-49161,49163,49165,49167,49175-49176,49400,49999-50003,50006,50300,50389,50500,50636,50800,51103,51493,52673,52822,52848,52869,54045,54328,55055-55056,55555,55600,56737-56738,57294,57797,58080,60020,60443,61532,61900,62078,63331,64623,64680,65000,65129,65389',
  'web': '80,443,8080,8443,8000,8888,9000,3000,5000',
  'common': '21,22,23,25,53,80,110,111,135,139,143,443,993,995,1723,3389,5900',
  'database': '1433,1521,3306,5432,6379,27017,27018,27019'
};

/**
 * Masscan Scanner Component
 */
export function MasscanScanner() {
  // State management
  const [config, setConfig] = React.useState<MasscanConfig>({
    targets: '',
    ports: '80,443',
    rate: 1000,
    maxRuntime: 3600,
    excludeRanges: '',
    sourcePort: '',
    sourceIP: '',
    interface: '',
    outputFormat: 'list',
    banners: false,
    includeClosed: false,
    ping: true,
    randomizeTargets: true,
    customArgs: ''
  });
  
  const [isScanning, setIsScanning] = React.useState(false);
  const [currentExecution, setCurrentExecution] = React.useState<ToolExecution | null>(null);
  const [progress, setProgress] = React.useState(0);
  const [output, setOutput] = React.useState<string[]>([]);
  const [results, setResults] = React.useState<any>(null);
  const [activeTab, setActiveTab] = React.useState('configure');
  const [scanStats, setScanStats] = React.useState({
    targetCount: 0,
    portCount: 0,
    estimatedTime: 0,
    totalCombinations: 0
  });
  
  // Backend connection
  const { status: backendStatus } = useBackendStore();
  
  // Calculate scan statistics
  React.useEffect(() => {
    const calculateStats = () => {
      // Simple target counting (rough estimation)
      const targetCount = config.targets.includes('/') ? 
        Math.pow(2, 32 - parseInt(config.targets.split('/')[1] || '32')) :
        config.targets.split(',').length;
      
      // Port counting
      const portCount = config.ports.includes('-') ?
        config.ports.split(',').reduce((total, range) => {
          if (range.includes('-')) {
            const [start, end] = range.split('-').map(Number);
            return total + (end - start + 1);
          }
          return total + 1;
        }, 0) :
        config.ports.split(',').length;
      
      const totalCombinations = targetCount * portCount;
      const estimatedTime = totalCombinations / config.rate; // seconds
      
      setScanStats({
        targetCount,
        portCount,
        estimatedTime,
        totalCombinations
      });
    };
    
    if (config.targets && config.ports) {
      calculateStats();
    }
  }, [config.targets, config.ports, config.rate]);
  
  // Update configuration
  const updateConfig = (updates: Partial<MasscanConfig>) => {
    setConfig(prev => ({ ...prev, ...updates }));
  };
  
  // Apply rate preset
  const applyRatePreset = (presetName: string) => {
    const preset = RATE_PRESETS[presetName as keyof typeof RATE_PRESETS];
    if (preset) {
      updateConfig({ rate: preset.rate });
      toast.success(`Applied ${preset.label}`);
    }
  };
  
  // Apply port preset
  const applyPortPreset = (presetName: string) => {
    const preset = PORT_PRESETS[presetName as keyof typeof PORT_PRESETS];
    if (preset) {
      updateConfig({ ports: preset });
      toast.success(`Applied ${presetName} port preset`);
    }
  };
  
  // Generate Masscan command
  const generateCommand = (): string => {
    let command = 'masscan';
    
    // Targets
    command += ` ${config.targets}`;
    
    // Ports
    command += ` -p${config.ports}`;
    
    // Rate
    command += ` --rate=${config.rate}`;
    
    // Max runtime
    if (config.maxRuntime > 0) {
      command += ` --max-runtime=${config.maxRuntime}`;
    }
    
    // Exclude ranges
    if (config.excludeRanges.trim()) {
      command += ` --exclude=${config.excludeRanges}`;
    }
    
    // Source port
    if (config.sourcePort.trim()) {
      command += ` --source-port=${config.sourcePort}`;
    }
    
    // Source IP
    if (config.sourceIP.trim()) {
      command += ` --source-ip=${config.sourceIP}`;
    }
    
    // Interface
    if (config.interface.trim()) {
      command += ` --interface=${config.interface}`;
    }
    
    // Output format
    switch (config.outputFormat) {
      case 'json':
        command += ' --output-format=json';
        break;
      case 'xml':
        command += ' --output-format=xml';
        break;
      case 'binary':
        command += ' --output-format=binary';
        break;
      default:
        command += ' --output-format=list';
    }
    
    // Banners
    if (config.banners) {
      command += ' --banners';
    }
    
    // Include closed ports
    if (config.includeClosed) {
      command += ' --include-closed';
    }
    
    // Ping
    if (!config.ping) {
      command += ' --ping=false';
    }
    
    // Randomize targets
    if (!config.randomizeTargets) {
      command += ' --randomize-hosts=false';
    }
    
    // Custom arguments
    if (config.customArgs.trim()) {
      command += ` ${config.customArgs.trim()}`;
    }
    
    return command;
  };
  
  // Format duration
  const formatDuration = (seconds: number): string => {
    if (seconds < 60) return `${Math.round(seconds)}s`;
    if (seconds < 3600) return `${Math.round(seconds / 60)}m`;
    return `${Math.round(seconds / 3600)}h ${Math.round((seconds % 3600) / 60)}m`;
  };
  
  // Format number with commas
  const formatNumber = (num: number): string => {
    return num.toLocaleString();
  };
  
  // Start scan
  const startScan = async () => {
    if (!config.targets.trim()) {
      toast.error('Please specify targets');
      return;
    }
    
    if (!config.ports.trim()) {
      toast.error('Please specify ports');
      return;
    }
    
    if (!backendStatus.connected) {
      toast.error('Backend not connected');
      return;
    }
    
    try {
      setIsScanning(true);
      setProgress(0);
      setOutput([]);
      setResults(null);
      setActiveTab('output');
      
      const execution = await apiClient.executeTool('masscan', config, (progressUpdate) => {
        setCurrentExecution(progressUpdate);
        setProgress(progressUpdate.progress);
        if (progressUpdate.output) {
          setOutput(prev => [...prev, ...progressUpdate.output!]);
        }
        
        if (progressUpdate.status === 'completed') {
          setIsScanning(false);
          setResults(progressUpdate.results);
          toast.success('Masscan scan completed');
        } else if (progressUpdate.status === 'failed') {
          setIsScanning(false);
          toast.error(`Scan failed: ${progressUpdate.error}`);
        }
      });
      
      setCurrentExecution(execution);
      toast.info('Masscan scan started');
    } catch (error) {
      setIsScanning(false);
      console.error('Failed to start Masscan scan:', error);
      toast.error('Failed to start scan');
    }
  };
  
  // Stop scan
  const stopScan = async () => {
    if (currentExecution) {
      try {
        await apiClient.cancelExecution(currentExecution.id);
        setIsScanning(false);
        setCurrentExecution(null);
        toast.info('Scan cancelled');
      } catch (error) {
        console.error('Failed to stop scan:', error);
        toast.error('Failed to stop scan');
      }
    }
  };
  
  // Copy command to clipboard
  const copyCommand = async () => {
    try {
      await navigator.clipboard.writeText(generateCommand());
      toast.success('Command copied to clipboard');
    } catch (error) {
      toast.error('Failed to copy command');
    }
  };
  
  // Export results
  const exportResults = () => {
    if (!results && output.length === 0) {
      toast.error('No results to export');
      return;
    }
    
    const data = results || output.join('\n');
    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `masscan-scan-${new Date().toISOString().slice(0, 19)}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    
    toast.success('Results exported');
  };
  
  return (
    <div className="container-desktop py-6 space-y-6">
      {/* Tool Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className="p-2 bg-orange-100 dark:bg-orange-900/20 rounded-lg">
            <Zap className="h-6 w-6 text-orange-600 dark:text-orange-400" />
          </div>
          <div>
            <h1 className="text-2xl font-bold">Masscan Scanner</h1>
            <p className="text-muted-foreground">
              High-speed asynchronous port scanner for large-scale network scanning
            </p>
          </div>
        </div>
        
        {/* Status indicators */}
        <div className="flex items-center gap-2">
          <Badge variant={backendStatus.connected ? 'default' : 'destructive'}>
            {backendStatus.connected ? 'Connected' : 'Offline'}
          </Badge>
          {isScanning && (
            <Badge variant="secondary" className="animate-pulse">
              Scanning at {formatNumber(config.rate)} pps
            </Badge>
          )}
        </div>
      </div>
      
      {/* Scan Statistics */}
      {(config.targets && config.ports) && (
        <Card className="border-orange-200 dark:border-orange-800">
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-orange-600 dark:text-orange-400">
              <TrendingUp className="h-5 w-5" />
              Scan Statistics
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-orange-600 dark:text-orange-400">
                  {formatNumber(scanStats.targetCount)}
                </div>
                <div className="text-xs text-muted-foreground">Targets</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-orange-600 dark:text-orange-400">
                  {formatNumber(scanStats.portCount)}
                </div>
                <div className="text-xs text-muted-foreground">Ports</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-orange-600 dark:text-orange-400">
                  {formatNumber(scanStats.totalCombinations)}
                </div>
                <div className="text-xs text-muted-foreground">Total Combinations</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-orange-600 dark:text-orange-400">
                  {formatDuration(scanStats.estimatedTime)}
                </div>
                <div className="text-xs text-muted-foreground">Estimated Time</div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
      
      {/* Main Interface */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="configure" className="flex items-center gap-2">
            <Settings className="h-4 w-4" />
            Configure
          </TabsTrigger>
          <TabsTrigger value="output" className="flex items-center gap-2">
            <Terminal className="h-4 w-4" />
            Output
          </TabsTrigger>
          <TabsTrigger value="results" className="flex items-center gap-2">
            <FileText className="h-4 w-4" />
            Results
          </TabsTrigger>
          <TabsTrigger value="command" className="flex items-center gap-2">
            <Target className="h-4 w-4" />
            Command
          </TabsTrigger>
        </TabsList>
        
        {/* Configuration Tab */}
        <TabsContent value="configure" className="space-y-6">
          <div className="grid gap-6 lg:grid-cols-2">
            {/* Target Configuration */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Target className="h-5 w-5" />
                  Target Configuration
                </CardTitle>
                <CardDescription>
                  Specify targets and basic scan parameters
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Targets */}
                <div className="space-y-2">
                  <label className="text-sm font-medium">Targets</label>
                  <Textarea
                    placeholder="e.g., ***********/24, ********-**********, example.com"
                    value={config.targets}
                    onChange={(e) => updateConfig({ targets: e.target.value })}
                    className="min-h-[80px]"
                  />
                  <p className="text-xs text-muted-foreground">
                    IP addresses, CIDR ranges, or hostnames (one per line or comma-separated)
                  </p>
                </div>
                
                {/* Ports */}
                <div className="space-y-2">
                  <label className="text-sm font-medium">Ports</label>
                  <Input
                    placeholder="e.g., 80,443, 1-1000, 22,80,443,8080"
                    value={config.ports}
                    onChange={(e) => updateConfig({ ports: e.target.value })}
                  />
                  <div className="flex flex-wrap gap-1">
                    {Object.entries(PORT_PRESETS).slice(0, 4).map(([name]) => (
                      <Button
                        key={name}
                        variant="outline"
                        size="sm"
                        className="text-xs h-6"
                        onClick={() => applyPortPreset(name)}
                      >
                        {name}
                      </Button>
                    ))}
                  </div>
                </div>
                
                {/* Exclude Ranges */}
                <div className="space-y-2">
                  <label className="text-sm font-medium">Exclude Ranges</label>
                  <Input
                    placeholder="e.g., ***********, 10.0.0.0/8"
                    value={config.excludeRanges}
                    onChange={(e) => updateConfig({ excludeRanges: e.target.value })}
                  />
                  <p className="text-xs text-muted-foreground">
                    IP ranges to exclude from scanning
                  </p>
                </div>
                
                {/* Max Runtime */}
                <div className="space-y-2">
                  <label className="text-sm font-medium">Max Runtime (seconds)</label>
                  <Input
                    type="number"
                    placeholder="3600"
                    value={config.maxRuntime}
                    onChange={(e) => updateConfig({ maxRuntime: parseInt(e.target.value) || 0 })}
                  />
                  <p className="text-xs text-muted-foreground">
                    Maximum scan duration (0 for unlimited)
                  </p>
                </div>
              </CardContent>
            </Card>
            
            {/* Performance Configuration */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Gauge className="h-5 w-5" />
                  Performance Settings
                </CardTitle>
                <CardDescription>
                  Configure scan speed and network parameters
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Scan Rate */}
                <div className="space-y-2">
                  <label className="text-sm font-medium">
                    Scan Rate: {formatNumber(config.rate)} packets/second
                  </label>
                  <input
                    type="range"
                    min="100"
                    max="1000000"
                    step="100"
                    value={config.rate}
                    onChange={(e) => updateConfig({ rate: parseInt(e.target.value) })}
                    className="w-full"
                  />
                  <div className="flex flex-wrap gap-1">
                    {Object.entries(RATE_PRESETS).map(([name, preset]) => (
                      <Button
                        key={name}
                        variant="outline"
                        size="sm"
                        className="text-xs h-6"
                        onClick={() => applyRatePreset(name)}
                        title={preset.description}
                      >
                        {preset.label.split(' ')[0]}
                      </Button>
                    ))}
                  </div>
                </div>
                
                {/* Source Configuration */}
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Source Port</label>
                    <Input
                      placeholder="Random"
                      value={config.sourcePort}
                      onChange={(e) => updateConfig({ sourcePort: e.target.value })}
                    />
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Source IP</label>
                    <Input
                      placeholder="Auto-detect"
                      value={config.sourceIP}
                      onChange={(e) => updateConfig({ sourceIP: e.target.value })}
                    />
                  </div>
                </div>
                
                {/* Network Interface */}
                <div className="space-y-2">
                  <label className="text-sm font-medium">Network Interface</label>
                  <Input
                    placeholder="Auto-detect (eth0, wlan0, etc.)"
                    value={config.interface}
                    onChange={(e) => updateConfig({ interface: e.target.value })}
                  />
                </div>
                
                {/* Output Format */}
                <div className="space-y-2">
                  <label className="text-sm font-medium">Output Format</label>
                  <Select 
                    value={config.outputFormat} 
                    onValueChange={(value) => updateConfig({ outputFormat: value as any })}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="list">List Format</SelectItem>
                      <SelectItem value="json">JSON Format</SelectItem>
                      <SelectItem value="xml">XML Format</SelectItem>
                      <SelectItem value="binary">Binary Format</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </CardContent>
            </Card>
          </div>
          
          {/* Advanced Options */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Network className="h-5 w-5" />
                Advanced Options
              </CardTitle>
              <CardDescription>
                Additional scanning options and custom arguments
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Boolean Options */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <label className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    checked={config.banners}
                    onChange={(e) => updateConfig({ banners: e.target.checked })}
                    className="rounded border-gray-300"
                  />
                  <span className="text-sm">Banner Grabbing</span>
                </label>
                
                <label className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    checked={config.includeClosed}
                    onChange={(e) => updateConfig({ includeClosed: e.target.checked })}
                    className="rounded border-gray-300"
                  />
                  <span className="text-sm">Include Closed</span>
                </label>
                
                <label className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    checked={config.ping}
                    onChange={(e) => updateConfig({ ping: e.target.checked })}
                    className="rounded border-gray-300"
                  />
                  <span className="text-sm">Ping Hosts</span>
                </label>
                
                <label className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    checked={config.randomizeTargets}
                    onChange={(e) => updateConfig({ randomizeTargets: e.target.checked })}
                    className="rounded border-gray-300"
                  />
                  <span className="text-sm">Randomize Order</span>
                </label>
              </div>
              
              {/* Custom Arguments */}
              <div className="space-y-2">
                <label className="text-sm font-medium">Custom Arguments</label>
                <Input
                  placeholder="Additional masscan arguments"
                  value={config.customArgs}
                  onChange={(e) => updateConfig({ customArgs: e.target.value })}
                />
              </div>
              
              {/* Action buttons */}
              <div className="flex justify-between items-center pt-4">
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={copyCommand}
                  >
                    <Copy className="h-4 w-4 mr-1" />
                    Copy Command
                  </Button>
                </div>
                
                <div className="flex gap-2">
                  {isScanning ? (
                    <Button
                      variant="destructive"
                      onClick={stopScan}
                    >
                      <Square className="h-4 w-4 mr-2" />
                      Stop Scan
                    </Button>
                  ) : (
                    <Button
                      onClick={startScan}
                      disabled={!config.targets.trim() || !config.ports.trim() || !backendStatus.connected}
                    >
                      <Play className="h-4 w-4 mr-2" />
                      Start Scan
                    </Button>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        {/* Output Tab */}
        <TabsContent value="output" className="space-y-6">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="flex items-center gap-2">
                    <Terminal className="h-5 w-5" />
                    Real-time Output
                  </CardTitle>
                  <CardDescription>
                    Live output from the Masscan process
                  </CardDescription>
                </div>
                
                {/* Progress and controls */}
                <div className="flex items-center gap-4">
                  {isScanning && (
                    <div className="flex items-center gap-2">
                      <Progress value={progress} className="w-32" />
                      <span className="text-sm text-muted-foreground">
                        {progress}%
                      </span>
                    </div>
                  )}
                  
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setOutput([])}
                    disabled={isScanning}
                  >
                    <RotateCcw className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="bg-gray-900 text-gray-100 p-4 rounded-lg h-96 overflow-y-auto font-mono text-sm">
                {output.length === 0 ? (
                  <div className="text-gray-500 italic">
                    {isScanning ? 'Waiting for output...' : 'No output yet. Start a scan to see results here.'}
                  </div>
                ) : (
                  output.map((line, index) => (
                    <div key={index} className="mb-1">
                      {line}
                    </div>
                  ))
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        {/* Results Tab */}
        <TabsContent value="results" className="space-y-6">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="flex items-center gap-2">
                    <FileText className="h-5 w-5" />
                    Scan Results
                  </CardTitle>
                  <CardDescription>
                    Structured results from the completed scan
                  </CardDescription>
                </div>
                
                <Button
                  variant="outline"
                  onClick={exportResults}
                  disabled={!results && output.length === 0}
                >
                  <Download className="h-4 w-4 mr-2" />
                  Export Results
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              {results ? (
                <div className="space-y-4">
                  <pre className="bg-muted p-4 rounded-lg overflow-auto text-sm">
                    {JSON.stringify(results, null, 2)}
                  </pre>
                </div>
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  <FileText className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>No structured results available yet.</p>
                  <p className="text-sm">Complete a scan to see parsed results here.</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
        
        {/* Command Tab */}
        <TabsContent value="command" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Target className="h-5 w-5" />
                Generated Command
              </CardTitle>
              <CardDescription>
                The Masscan command that will be executed based on your configuration
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="bg-muted p-4 rounded-lg">
                  <code className="text-sm font-mono break-all">
                    {generateCommand()}
                  </code>
                </div>
                
                <div className="flex justify-between items-center">
                  <p className="text-sm text-muted-foreground">
                    This command will be executed on the backend server
                  </p>
                  
                  <Button variant="outline" onClick={copyCommand}>
                    <Copy className="h-4 w-4 mr-2" />
                    Copy Command
                  </Button>
                </div>
                
                {/* Performance warning */}
                {config.rate > 10000 && (
                  <div className="flex items-start gap-2 p-3 bg-yellow-50 dark:bg-yellow-900/10 rounded-md border border-yellow-200 dark:border-yellow-800">
                    <AlertTriangle className="h-4 w-4 text-yellow-600 dark:text-yellow-400 mt-0.5 flex-shrink-0" />
                    <div className="text-sm text-yellow-700 dark:text-yellow-300">
                      <p className="font-medium">High Rate Warning</p>
                      <p>Scanning at {formatNumber(config.rate)} pps may trigger intrusion detection systems and could impact network performance.</p>
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}"