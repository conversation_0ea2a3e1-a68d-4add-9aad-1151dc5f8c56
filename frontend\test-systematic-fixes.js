/**
 * Systematic Status Bar Fixes Test
 * 
 * This script tests all four main areas systematically:
 * 1. Correct API endpoints
 * 2. Backend tools refresh
 * 3. Mock data removal
 * 4. Data flow debugging
 * 
 * Run this in the browser console when the Electron app is running.
 */

console.log('🔬 SYSTEMATIC STATUS BAR FIXES TEST');
console.log('=====================================');

// Test 1: API Endpoint Verification
console.log('\n1️⃣ TESTING API ENDPOINTS...');

if (window.electronAPI && window.electronAPI.backend) {
  // Test the correct endpoint
  window.electronAPI.backend.getAvailableTools()
    .then(tools => {
      console.log('✅ API Endpoint Test - Success');
      console.log(`📊 Tools returned: ${tools.length} total`);
      console.log('📋 First 3 tools:', tools.slice(0, 3).map(t => ({name: t.name, status: t.status})));
      
      // Verify no AI analyzer duplicates
      const aiAnalyzers = tools.filter(t => t.category === 'ai_analyzer');
      if (aiAnalyzers.length === 0) {
        console.log('✅ No AI analyzer duplicates found');
      } else {
        console.log(`⚠️ Found ${aiAnalyzers.length} AI analyzer duplicates`);
      }
    })
    .catch(error => {
      console.error('❌ API Endpoint Test - Failed:', error);
    });
} else {
  console.error('❌ Electron API not available');
}

// Test 2: Backend Store Status
console.log('\n2️⃣ TESTING BACKEND STORE STATUS...');

// Use our debug helper to check backend status
if (window.nexusScan && window.nexusScan.getBackendStatus) {
  window.nexusScan.getBackendStatus()
    .then(status => {
      console.log('📡 Backend Status:', {
        connected: status.connected,
        hasTools: !!status.tools,
        toolsCount: status.tools ? status.tools.total : 'N/A'
      });
      
      if (status.tools) {
        console.log('✅ Backend has tools count in status');
        console.log('🔧 Tools breakdown:', {
          total: status.tools.total,
          operational: status.tools.operational,
          failed: status.tools.failed
        });
      } else {
        console.log('❌ Backend status missing tools count');
      }
    })
    .catch(error => {
      console.error('❌ Backend Status Test - Failed:', error);
    });
}

// Test 3: WSL Status
console.log('\n3️⃣ TESTING WSL STATUS...');

if (window.electronAPI && window.electronAPI.wsl) {
  window.electronAPI.wsl.getStatus()
    .then(status => {
      console.log('🐧 WSL Status:', {
        status: status.status,
        version: status.version,
        toolsInstalled: status.toolsInstalled,
        distributions: status.distributions?.length || 0
      });
      
      if (status.status === 'available') {
        console.log('✅ WSL detected as available');
      } else {
        console.log(`⚠️ WSL status: ${status.status}`);
      }
    })
    .catch(error => {
      console.error('❌ WSL Status Test - Failed:', error);
    });
}

// Test 4: Mock Data Verification
console.log('\n4️⃣ TESTING MOCK DATA REMOVAL...');

// Check for hardcoded mock data patterns
const checkForMockData = () => {
  // This would need to be run from within the React app context
  console.log('📝 Mock data check:');
  console.log('  - DashboardPage recent activity should be empty array');
  console.log('  - Ferrari components should start with empty states');
  console.log('  - No SAMPLE_ data should be visible in UI');
};

checkForMockData();

// Test 5: Force Backend Refresh
console.log('\n5️⃣ TESTING FORCED BACKEND REFRESH...');

if (window.electronAPI && window.electronAPI.backend) {
  // Test refresh capability
  console.log('🔄 Testing manual tools refresh...');
  
  window.electronAPI.backend.getAvailableTools()
    .then(tools => {
      console.log(`🔧 Manual refresh successful: ${tools.length} tools loaded`);
      
      // Check if tools have proper status fields
      const toolStatuses = tools.map(t => t.status);
      const uniqueStatuses = [...new Set(toolStatuses)];
      console.log('📊 Tool statuses found:', uniqueStatuses);
      
      const available = tools.filter(t => t.status === 'available').length;
      const failed = tools.filter(t => t.status === 'error').length;
      
      console.log('🎯 Expected StatusBar display:', `${available}/${tools.length} tools available`);
    })
    .catch(error => {
      console.error('❌ Manual refresh failed:', error);
    });
}

// Summary
setTimeout(() => {
  console.log('\n📋 SYSTEMATIC TEST SUMMARY');
  console.log('==========================');
  console.log('Check the logs above for:');
  console.log('✅ API endpoints returning tools');
  console.log('✅ Backend status containing tools count');
  console.log('✅ WSL status showing available');
  console.log('✅ No mock data in UI');
  console.log('✅ Manual refresh working');
  console.log('');
  console.log('If all tests pass, StatusBar should show:');
  console.log('- "Backend Connected" with tools count');
  console.log('- "WSL Available" (if on Windows)');
  console.log('- No fake recent activity');
}, 3000);