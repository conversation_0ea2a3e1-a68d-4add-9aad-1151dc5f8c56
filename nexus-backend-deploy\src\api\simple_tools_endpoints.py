#!/usr/bin/env python3
"""
Simple Tools Endpoints - No Progress Callbacks
Direct tool execution without complex progress tracking
"""

import asyncio
import logging
import json
from typing import Optional, Dict, Any
from aiohttp import web, web_request, web_response
from datetime import datetime, timezone
import uuid

from core.config import Config
from security.unified_tool_manager import UnifiedToolManager

logger = logging.getLogger(__name__)


class SimpleToolsEndpoints:
    """Simple tools endpoints without progress callbacks"""
    
    def __init__(self, config=None):
        self.config = config or Config()
        self.tool_manager = UnifiedToolManager(self.config)
        self.active_scans = {}
        
    async def initialize(self):
        """Initialize tools"""
        try:
            result = await self.tool_manager.setup_environment()
            logger.info(f"Simple tools initialized: {result.get('total_tools', 0)} tools")
            return result
        except Exception as e:
            logger.error(f"Simple tools initialization failed: {e}")
            return {"success": False, "error": str(e)}
    
    async def execute_simple_scan(self, request: web_request.Request) -> web_response.Response:
        """Execute tool scan without progress callbacks - UNIVERSAL for all v1 tools"""
        try:
            tool_name = request.match_info.get('tool_name')
            if not tool_name:
                return web.json_response({
                    "success": False,
                    "error": "Tool name is required"
                }, status=400)
            
            # Validate tool exists in v1 manager
            available_tools = self.tool_manager.get_available_tools()
            if tool_name not in available_tools:
                return web.json_response({
                    "success": False,
                    "error": f"Tool '{tool_name}' not available. Available tools: {', '.join(available_tools)}"
                }, status=400)
            
            # Parse request body
            try:
                body = await request.json()
            except Exception:
                return web.json_response({
                    "success": False,
                    "error": "Invalid JSON in request body"
                }, status=400)
            
            target = body.get('target')
            if not target:
                return web.json_response({
                    "success": False,
                    "error": "Target is required"
                }, status=400)
            
            scan_id = str(uuid.uuid4())
            start_time = datetime.now(timezone.utc).isoformat()
            
            # Track scan
            self.active_scans[scan_id] = {
                "id": scan_id,
                "tool": tool_name,
                "target": target,
                "status": "running",
                "start_time": start_time
            }
            
            try:
                # Execute using v1 interface directly - NO PROGRESS CALLBACKS
                scan_type = body.get('options', {}).get('scan_type', 'basic')
                timeout = body.get('timeout', 300)
                
                logger.info(f"[UNIVERSAL] Executing {tool_name} on {target} (simple mode)")
                
                # Direct execution without progress callbacks - works for ALL v1 tools
                result = await self.tool_manager.execute_scan(
                    tool_name,
                    target, 
                    scan_type,
                    timeout
                )
                
                end_time = datetime.now(timezone.utc).isoformat()
                
                # Remove from active scans
                if scan_id in self.active_scans:
                    del self.active_scans[scan_id]
                
                # Enhanced result with tool info
                tool_info = self.tool_manager.get_tool_info(tool_name)
                
                # Return simplified result
                return web.json_response({
                    "success": result.get("success", False),
                    "data": {
                        "scan_id": scan_id,
                        "tool": tool_name,
                        "target": target,
                        "status": "completed" if result.get("success") else "failed",
                        "start_time": start_time,
                        "end_time": end_time,
                        "output": result.get("output", ""),
                        "error": result.get("error"),
                        "execution_time": result.get("execution_time"),
                        "tool_info": {
                            "platform": tool_info.get("primary_platform") if tool_info else "auto",
                            "description": tool_info.get("description") if tool_info else "Security tool"
                        },
                        "metadata": {
                            "simple_mode": True,
                            "v1_direct": True,
                            "universal_execution": True
                        }
                    }
                })
                
            except Exception as e:
                # Update scan with error
                if scan_id in self.active_scans:
                    del self.active_scans[scan_id]
                
                logger.error(f"Universal tool execution failed for {tool_name}: {e}")
                return web.json_response({
                    "success": False,
                    "error": f"Tool execution failed: {str(e)}",
                    "scan_id": scan_id,
                    "tool": tool_name
                }, status=500)
                
        except Exception as e:
            logger.error(f"Failed to execute universal tool scan: {e}")
            return web.json_response({
                "success": False,
                "error": str(e)
            }, status=500)
    
    async def get_available_tools_simple(self, request: web_request.Request) -> web_response.Response:
        """Get available tools (simple) - dynamically from v1 manager"""
        try:
            # Get tools directly from v1 UnifiedToolManager
            tools = self.tool_manager.get_available_tools()
            
            # Get tool catalog for detailed info
            tool_info = []
            for tool_name in tools:
                info = self.tool_manager.get_tool_info(tool_name)
                if info:
                    tool_info.append({
                        "name": tool_name,
                        "id": tool_name,
                        "description": info.get("description", "Security tool"),
                        "platform": info.get("primary_platform", "unknown"),
                        "available": True
                    })
                else:
                    tool_info.append({
                        "name": tool_name,
                        "id": tool_name,
                        "description": "Security tool",
                        "platform": "auto",
                        "available": True
                    })
            
            logger.info(f"Simple tools available: {len(tool_info)} tools")
            
            return web.json_response({
                "success": True,
                "data": tool_info,
                "metadata": {
                    "total": len(tool_info), 
                    "simple_mode": True,
                    "v1_manager": True,
                    "dynamic_discovery": True
                }
            })
        except Exception as e:
            logger.error(f"Failed to get available tools: {e}")
            return web.json_response({
                "success": False,
                "error": str(e)
            }, status=500)


# Global instance
simple_tools = SimpleToolsEndpoints()


def setup_simple_tools_routes(app: web.Application):
    """Setup simple tools routes - UNIVERSAL for all tools"""
    # Primary simple tools endpoints
    app.router.add_get('/api/simple-tools/available', simple_tools.get_available_tools_simple)
    app.router.add_post('/api/simple-tools/{tool_name}/scan', simple_tools.execute_simple_scan)
    
    # Alternative routes for testing
    app.router.add_post('/api/tools-simple/{tool_name}/scan', simple_tools.execute_simple_scan)
    
    # CRITICAL: Route main tools endpoints to simple execution
    # This makes ALL tools use the working simple method instead of broken v2 system
    app.router.add_post('/api/tools/{tool_name}/scan', simple_tools.execute_simple_scan)
    
    # Legacy specific tool routes (redirect to universal simple execution)
    app.router.add_post('/api/tools/nmap/scan', simple_tools.execute_simple_scan)
    app.router.add_post('/api/tools/nuclei/scan', simple_tools.execute_simple_scan)
    app.router.add_post('/api/tools/sqlmap/scan', simple_tools.execute_simple_scan)
    app.router.add_post('/api/tools/nikto/scan', simple_tools.execute_simple_scan)
    app.router.add_post('/api/tools/gobuster/scan', simple_tools.execute_simple_scan)
    app.router.add_post('/api/tools/dirb/scan', simple_tools.execute_simple_scan)
    app.router.add_post('/api/tools/wpscan/scan', simple_tools.execute_simple_scan)
    app.router.add_post('/api/tools/ffuf/scan', simple_tools.execute_simple_scan)
    app.router.add_post('/api/tools/feroxbuster/scan', simple_tools.execute_simple_scan)
    app.router.add_post('/api/tools/whatweb/scan', simple_tools.execute_simple_scan)
    app.router.add_post('/api/tools/hashcat/scan', simple_tools.execute_simple_scan)
    app.router.add_post('/api/tools/john/scan', simple_tools.execute_simple_scan)
    app.router.add_post('/api/tools/enum4linux-ng/scan', simple_tools.execute_simple_scan)
    app.router.add_post('/api/tools/smbclient/scan', simple_tools.execute_simple_scan)
    app.router.add_post('/api/tools/testssl/scan', simple_tools.execute_simple_scan)
    app.router.add_post('/api/tools/sslyze/scan', simple_tools.execute_simple_scan)
    app.router.add_post('/api/tools/metasploit/scan', simple_tools.execute_simple_scan)
    app.router.add_post('/api/tools/searchsploit/scan', simple_tools.execute_simple_scan)
    
    logger.info("✅ UNIVERSAL TOOLS ROUTING: All tools now use simple execution (no progress callbacks)")