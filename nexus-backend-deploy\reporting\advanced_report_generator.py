#!/usr/bin/env python3
"""
Advanced Report Generator for NexusScan Desktop Application
Comprehensive reporting system with multiple formats and AI-enhanced insights
"""

import asyncio
import logging
import json
import os
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass, field
from enum import Enum
import base64
from pathlib import Path

# PDF generation
try:
    from reportlab.pdfgen import canvas
    from reportlab.lib.pagesizes import letter, A4
    from reportlab.lib import colors
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
    from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle, PageBreak
    from reportlab.platypus import Image as RLImage
    from reportlab.lib.units import inch
    PDF_AVAILABLE = True
except ImportError:
    PDF_AVAILABLE = False

# HTML generation
from jinja2 import Template, Environment, FileSystemLoader

from core.config import Config
from core.database import DatabaseManager
from ai.ai_service import ai_service_manager
from ai.reporting_insights import ReportingInsightsEngine

logger = logging.getLogger(__name__)


class ReportFormat(Enum):
    """Supported report formats"""
    PDF = "pdf"
    HTML = "html"
    JSON = "json"
    CSV = "csv"
    MARKDOWN = "markdown"


class ReportType(Enum):
    """Report types"""
    EXECUTIVE_SUMMARY = "executive_summary"
    TECHNICAL_DETAILED = "technical_detailed"
    COMPLIANCE_OWASP = "compliance_owasp"
    COMPLIANCE_NIST = "compliance_nist"
    VULNERABILITY_ASSESSMENT = "vulnerability_assessment"
    THREAT_INTELLIGENCE = "threat_intelligence"
    CUSTOM = "custom"


@dataclass
class ReportTemplate:
    """Report template configuration"""
    template_id: str
    name: str
    description: str
    report_type: ReportType
    format: ReportFormat
    sections: List[str] = field(default_factory=list)
    custom_fields: Dict[str, Any] = field(default_factory=dict)
    ai_enhanced: bool = True
    compliance_framework: Optional[str] = None


@dataclass
class ReportConfiguration:
    """Report generation configuration"""
    template: ReportTemplate
    output_path: str
    include_raw_data: bool = False
    include_screenshots: bool = True
    include_remediation: bool = True
    include_executive_summary: bool = True
    include_ai_insights: bool = True
    custom_branding: Optional[Dict[str, str]] = None
    schedule: Optional[Dict[str, Any]] = None


@dataclass
class ReportData:
    """Comprehensive report data structure"""
    scan_id: str
    target: str
    scan_type: str
    start_time: datetime
    end_time: datetime
    duration: timedelta
    
    # Scan results
    vulnerabilities: List[Dict[str, Any]] = field(default_factory=list)
    scan_summary: Dict[str, Any] = field(default_factory=dict)
    tool_results: Dict[str, Any] = field(default_factory=dict)
    
    # AI analysis
    ai_risk_score: float = 0.0
    ai_confidence: float = 0.0
    ai_insights: List[Dict[str, Any]] = field(default_factory=list)
    threat_intelligence: List[Dict[str, Any]] = field(default_factory=list)
    remediation_plans: List[Dict[str, Any]] = field(default_factory=list)
    
    # Compliance
    compliance_status: Dict[str, Any] = field(default_factory=dict)
    risk_matrix: Dict[str, Any] = field(default_factory=dict)
    
    # Metadata
    report_generated_at: datetime = field(default_factory=datetime.now)
    report_version: str = "1.0"
    analyst_notes: str = ""


class AdvancedReportGenerator:
    """Advanced report generator with AI-enhanced insights and multiple formats"""
    
    def __init__(self, config: Config, db_manager: DatabaseManager):
        """Initialize advanced report generator"""
        self.config = config
        self.db_manager = db_manager
        self.reporting_engine = ReportingInsightsEngine(config)
        
        # Report templates
        self.templates: Dict[str, ReportTemplate] = {}
        self._initialize_templates()
        
        # Output directory
        self.output_dir = Path(config.get("reporting.output_directory", "./reports"))
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # Template directory
        self.template_dir = Path(__file__).parent / "templates"
        self.template_dir.mkdir(exist_ok=True)
        
        # Jinja2 environment
        self.jinja_env = Environment(
            loader=FileSystemLoader(str(self.template_dir)),
            autoescape=True
        )
        
        # Create default templates
        self._create_default_templates()
    
    def _initialize_templates(self):
        """Initialize report templates"""
        
        # Executive Summary Template
        self.templates["executive_summary"] = ReportTemplate(
            template_id="executive_summary",
            name="Executive Summary Report",
            description="High-level security assessment summary for executives",
            report_type=ReportType.EXECUTIVE_SUMMARY,
            format=ReportFormat.PDF,
            sections=[
                "executive_summary",
                "risk_overview",
                "key_findings",
                "recommendations",
                "next_steps"
            ],
            ai_enhanced=True
        )
        
        # Technical Detailed Template
        self.templates["technical_detailed"] = ReportTemplate(
            template_id="technical_detailed",
            name="Technical Detailed Report",
            description="Comprehensive technical security assessment report",
            report_type=ReportType.TECHNICAL_DETAILED,
            format=ReportFormat.HTML,
            sections=[
                "executive_summary",
                "methodology",
                "scope",
                "findings",
                "vulnerability_details",
                "threat_analysis",
                "remediation_plans",
                "appendices"
            ],
            ai_enhanced=True
        )
        
        # OWASP Compliance Template
        self.templates["compliance_owasp"] = ReportTemplate(
            template_id="compliance_owasp",
            name="OWASP Compliance Report",
            description="OWASP Top 10 compliance assessment report",
            report_type=ReportType.COMPLIANCE_OWASP,
            format=ReportFormat.PDF,
            sections=[
                "compliance_overview",
                "owasp_top10_assessment",
                "gap_analysis",
                "remediation_roadmap",
                "compliance_score"
            ],
            compliance_framework="OWASP",
            ai_enhanced=True
        )
        
        # NIST Compliance Template
        self.templates["compliance_nist"] = ReportTemplate(
            template_id="compliance_nist",
            name="NIST Cybersecurity Framework Report",
            description="NIST CSF compliance assessment report",
            report_type=ReportType.COMPLIANCE_NIST,
            format=ReportFormat.PDF,
            sections=[
                "framework_overview",
                "identify_assessment",
                "protect_assessment",
                "detect_assessment",
                "respond_assessment",
                "recover_assessment",
                "compliance_roadmap"
            ],
            compliance_framework="NIST",
            ai_enhanced=True
        )
        
        # Vulnerability Assessment Template
        self.templates["vulnerability_assessment"] = ReportTemplate(
            template_id="vulnerability_assessment",
            name="Vulnerability Assessment Report",
            description="Detailed vulnerability assessment with AI analysis",
            report_type=ReportType.VULNERABILITY_ASSESSMENT,
            format=ReportFormat.HTML,
            sections=[
                "assessment_overview",
                "vulnerability_summary",
                "critical_vulnerabilities",
                "ai_risk_analysis",
                "exploitation_scenarios",
                "remediation_priorities"
            ],
            ai_enhanced=True
        )
    
    async def generate_report(self, 
                            report_data: ReportData, 
                            config: ReportConfiguration) -> str:
        """Generate comprehensive security report"""
        
        logger.info(f"Generating {config.template.report_type.value} report for {report_data.target}")
        
        try:
            # Enhance report data with AI insights
            if config.template.ai_enhanced and config.include_ai_insights:
                await self._enhance_report_with_ai(report_data)
            
            # Generate report based on format
            if config.template.format == ReportFormat.PDF:
                return await self._generate_pdf_report(report_data, config)
            elif config.template.format == ReportFormat.HTML:
                return await self._generate_html_report(report_data, config)
            elif config.template.format == ReportFormat.JSON:
                return await self._generate_json_report(report_data, config)
            elif config.template.format == ReportFormat.MARKDOWN:
                return await self._generate_markdown_report(report_data, config)
            else:
                raise ValueError(f"Unsupported report format: {config.template.format}")
                
        except Exception as e:
            logger.error(f"Report generation failed: {e}")
            raise
    
    async def _enhance_report_with_ai(self, report_data: ReportData):
        """Enhance report data with AI-generated insights"""
        try:
            # Generate executive insights
            executive_insights = await self.reporting_engine.generate_executive_insights({
                "scan_results": {
                    "vulnerabilities": report_data.vulnerabilities,
                    "scan_summary": report_data.scan_summary,
                    "ai_risk_score": report_data.ai_risk_score
                },
                "target": report_data.target,
                "scan_duration": report_data.duration.total_seconds()
            })
            
            if executive_insights:
                report_data.ai_insights.extend(executive_insights)
            
            # Generate technical insights
            technical_insights = await self.reporting_engine.generate_technical_insights({
                "vulnerabilities": report_data.vulnerabilities,
                "tool_results": report_data.tool_results,
                "threat_intelligence": report_data.threat_intelligence
            })
            
            if technical_insights:
                report_data.ai_insights.extend(technical_insights)
            
            # Generate compliance assessment if applicable
            if report_data.compliance_status:
                compliance_insights = await self.reporting_engine.generate_compliance_insights({
                    "vulnerabilities": report_data.vulnerabilities,
                    "compliance_status": report_data.compliance_status
                })
                
                if compliance_insights:
                    report_data.ai_insights.extend(compliance_insights)
            
        except Exception as e:
            logger.warning(f"AI enhancement failed: {e}")
    
    async def _generate_pdf_report(self, 
                                 report_data: ReportData, 
                                 config: ReportConfiguration) -> str:
        """Generate PDF report"""
        if not PDF_AVAILABLE:
            raise RuntimeError("PDF generation not available. Install reportlab: pip install reportlab")
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"{config.template.template_id}_{report_data.scan_id}_{timestamp}.pdf"
        output_path = self.output_dir / filename
        
        # Create PDF document
        doc = SimpleDocTemplate(str(output_path), pagesize=A4)
        styles = getSampleStyleSheet()
        story = []
        
        # Title page
        story.extend(self._create_pdf_title_page(report_data, config, styles))
        story.append(PageBreak())
        
        # Executive summary
        if "executive_summary" in config.template.sections:
            story.extend(self._create_pdf_executive_summary(report_data, styles))
            story.append(PageBreak())
        
        # Findings and vulnerabilities
        if "findings" in config.template.sections or "vulnerability_details" in config.template.sections:
            story.extend(self._create_pdf_findings_section(report_data, styles))
            story.append(PageBreak())
        
        # AI insights
        if config.include_ai_insights and report_data.ai_insights:
            story.extend(self._create_pdf_ai_insights(report_data, styles))
            story.append(PageBreak())
        
        # Remediation recommendations
        if config.include_remediation and report_data.remediation_plans:
            story.extend(self._create_pdf_remediation_section(report_data, styles))
            story.append(PageBreak())
        
        # Compliance section
        if config.template.compliance_framework and report_data.compliance_status:
            story.extend(self._create_pdf_compliance_section(report_data, config, styles))
        
        # Build PDF
        doc.build(story)
        
        logger.info(f"PDF report generated: {output_path}")
        return str(output_path)
    
    async def _generate_html_report(self, 
                                  report_data: ReportData, 
                                  config: ReportConfiguration) -> str:
        """Generate HTML report"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"{config.template.template_id}_{report_data.scan_id}_{timestamp}.html"
        output_path = self.output_dir / filename
        
        # Load template
        template = self.jinja_env.get_template(f"{config.template.template_id}.html")
        
        # Prepare template data
        template_data = {
            "report_data": report_data,
            "config": config,
            "generation_time": datetime.now(),
            "vulnerabilities_by_severity": self._group_vulnerabilities_by_severity(report_data.vulnerabilities),
            "risk_score_level": self._get_risk_level(report_data.ai_risk_score),
            "top_vulnerabilities": sorted(report_data.vulnerabilities, 
                                        key=lambda v: v.get("severity_score", 0), reverse=True)[:10]
        }
        
        # Render template
        html_content = template.render(**template_data)
        
        # Write HTML file
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        logger.info(f"HTML report generated: {output_path}")
        return str(output_path)
    
    async def _generate_json_report(self, 
                                  report_data: ReportData, 
                                  config: ReportConfiguration) -> str:
        """Generate JSON report"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"{config.template.template_id}_{report_data.scan_id}_{timestamp}.json"
        output_path = self.output_dir / filename
        
        # Convert to JSON-serializable format
        report_dict = {
            "report_metadata": {
                "scan_id": report_data.scan_id,
                "target": report_data.target,
                "scan_type": report_data.scan_type,
                "start_time": report_data.start_time.isoformat(),
                "end_time": report_data.end_time.isoformat(),
                "duration_seconds": report_data.duration.total_seconds(),
                "report_generated_at": report_data.report_generated_at.isoformat(),
                "report_version": report_data.report_version,
                "template_used": config.template.template_id
            },
            "scan_summary": report_data.scan_summary,
            "vulnerabilities": report_data.vulnerabilities,
            "ai_analysis": {
                "risk_score": report_data.ai_risk_score,
                "confidence": report_data.ai_confidence,
                "insights": report_data.ai_insights,
                "threat_intelligence": report_data.threat_intelligence
            },
            "remediation_plans": report_data.remediation_plans,
            "compliance_status": report_data.compliance_status,
            "tool_results": report_data.tool_results if config.include_raw_data else {},
            "analyst_notes": report_data.analyst_notes
        }
        
        # Write JSON file
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(report_dict, f, indent=2, ensure_ascii=False)
        
        logger.info(f"JSON report generated: {output_path}")
        return str(output_path)
    
    async def _generate_markdown_report(self, 
                                      report_data: ReportData, 
                                      config: ReportConfiguration) -> str:
        """Generate Markdown report"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"{config.template.template_id}_{report_data.scan_id}_{timestamp}.md"
        output_path = self.output_dir / filename
        
        # Build markdown content
        markdown_content = []
        
        # Title and metadata
        markdown_content.extend([
            f"# Security Assessment Report: {report_data.target}",
            "",
            f"**Scan ID:** {report_data.scan_id}",
            f"**Target:** {report_data.target}",
            f"**Scan Type:** {report_data.scan_type}",
            f"**Duration:** {report_data.duration}",
            f"**Report Generated:** {report_data.report_generated_at.strftime('%Y-%m-%d %H:%M:%S')}",
            "",
            "---",
            ""
        ])
        
        # Executive Summary
        markdown_content.extend([
            "## Executive Summary",
            "",
            f"This security assessment identified **{len(report_data.vulnerabilities)} vulnerabilities** ",
            f"with an overall AI risk score of **{report_data.ai_risk_score:.1f}/10**.",
            ""
        ])
        
        # Vulnerability Summary
        vuln_by_severity = self._group_vulnerabilities_by_severity(report_data.vulnerabilities)
        markdown_content.extend([
            "## Vulnerability Summary",
            "",
            "| Severity | Count |",
            "|----------|-------|"
        ])
        
        for severity, vulns in vuln_by_severity.items():
            markdown_content.append(f"| {severity.title()} | {len(vulns)} |")
        
        markdown_content.extend(["", ""])
        
        # Top vulnerabilities
        if report_data.vulnerabilities:
            markdown_content.extend([
                "## Critical Findings",
                ""
            ])
            
            top_vulns = sorted(report_data.vulnerabilities, 
                             key=lambda v: v.get("severity_score", 0), reverse=True)[:5]
            
            for i, vuln in enumerate(top_vulns, 1):
                markdown_content.extend([
                    f"### {i}. {vuln.get('title', 'Unknown Vulnerability')}",
                    "",
                    f"**Severity:** {vuln.get('severity', 'Unknown')}",
                    f"**CVSS Score:** {vuln.get('cvss_score', 'N/A')}",
                    f"**Description:** {vuln.get('description', 'No description available')}",
                    ""
                ])
        
        # AI Insights
        if config.include_ai_insights and report_data.ai_insights:
            markdown_content.extend([
                "## AI-Generated Insights",
                ""
            ])
            
            for insight in report_data.ai_insights[:5]:  # Top 5 insights
                markdown_content.extend([
                    f"### {insight.get('title', 'AI Insight')}",
                    "",
                    insight.get('summary', 'No summary available'),
                    ""
                ])
        
        # Recommendations
        if config.include_remediation and report_data.remediation_plans:
            markdown_content.extend([
                "## Remediation Recommendations",
                ""
            ])
            
            for i, plan in enumerate(report_data.remediation_plans[:10], 1):
                markdown_content.extend([
                    f"{i}. **{plan.get('title', 'Remediation Item')}**",
                    f"   - Priority: {plan.get('priority', 'Medium')}",
                    f"   - Effort: {plan.get('effort_estimate', 'Unknown')}",
                    f"   - Description: {plan.get('description', 'No description')}",
                    ""
                ])
        
        # Write markdown file
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write('\n'.join(markdown_content))
        
        logger.info(f"Markdown report generated: {output_path}")
        return str(output_path)
    
    def _create_pdf_title_page(self, report_data: ReportData, config: ReportConfiguration, styles):
        """Create PDF title page"""
        story = []
        
        # Title
        title_style = ParagraphStyle(
            'CustomTitle',
            parent=styles['Title'],
            fontSize=24,
            spaceAfter=30,
            textColor=colors.HexColor('#1976d2')
        )
        
        story.append(Paragraph(f"Security Assessment Report", title_style))
        story.append(Paragraph(f"Target: {report_data.target}", styles['Heading2']))
        story.append(Spacer(1, 20))
        
        # Metadata table
        metadata = [
            ['Scan ID', report_data.scan_id],
            ['Target', report_data.target],
            ['Scan Type', report_data.scan_type],
            ['Start Time', report_data.start_time.strftime('%Y-%m-%d %H:%M:%S')],
            ['End Time', report_data.end_time.strftime('%Y-%m-%d %H:%M:%S')],
            ['Duration', str(report_data.duration)],
            ['Report Generated', report_data.report_generated_at.strftime('%Y-%m-%d %H:%M:%S')]
        ]
        
        metadata_table = Table(metadata, colWidths=[2*inch, 4*inch])
        metadata_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, -1), colors.HexColor('#f5f5f5')),
            ('TEXTCOLOR', (0, 0), (-1, -1), colors.black),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
        ]))
        
        story.append(metadata_table)
        story.append(Spacer(1, 30))
        
        # Summary statistics
        story.append(Paragraph("Assessment Summary", styles['Heading2']))
        story.append(Spacer(1, 12))
        
        summary_data = [
            ['Vulnerabilities Found', str(len(report_data.vulnerabilities))],
            ['AI Risk Score', f"{report_data.ai_risk_score:.1f}/10"],
            ['AI Confidence', f"{report_data.ai_confidence:.1f}%"],
            ['Remediation Plans', str(len(report_data.remediation_plans))]
        ]
        
        summary_table = Table(summary_data, colWidths=[2*inch, 2*inch])
        summary_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, -1), colors.HexColor('#e3f2fd')),
            ('TEXTCOLOR', (0, 0), (-1, -1), colors.black),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (-1, -1), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, -1), 12),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 8),
        ]))
        
        story.append(summary_table)
        
        return story
    
    def _create_pdf_executive_summary(self, report_data: ReportData, styles):
        """Create PDF executive summary section"""
        story = []
        
        story.append(Paragraph("Executive Summary", styles['Heading1']))
        story.append(Spacer(1, 12))
        
        # AI-generated summary if available
        executive_insights = [insight for insight in report_data.ai_insights 
                            if insight.get('type') == 'executive_summary']
        
        if executive_insights:
            summary_text = executive_insights[0].get('summary', 
                'This security assessment has been completed with AI-enhanced analysis.')
        else:
            summary_text = self._generate_default_executive_summary(report_data)
        
        story.append(Paragraph(summary_text, styles['Normal']))
        story.append(Spacer(1, 20))
        
        # Risk overview
        story.append(Paragraph("Risk Overview", styles['Heading2']))
        story.append(Spacer(1, 12))
        
        risk_level = self._get_risk_level(report_data.ai_risk_score)
        risk_color = self._get_risk_color(risk_level)
        
        risk_style = ParagraphStyle(
            'RiskStyle',
            parent=styles['Normal'],
            textColor=risk_color,
            fontSize=14,
            spaceAfter=12
        )
        
        story.append(Paragraph(f"Overall Risk Level: {risk_level.upper()}", risk_style))
        story.append(Paragraph(f"AI Risk Score: {report_data.ai_risk_score:.1f}/10", styles['Normal']))
        
        return story
    
    def _create_pdf_findings_section(self, report_data: ReportData, styles):
        """Create PDF findings section"""
        story = []
        
        story.append(Paragraph("Security Findings", styles['Heading1']))
        story.append(Spacer(1, 12))
        
        # Vulnerability summary table
        vuln_by_severity = self._group_vulnerabilities_by_severity(report_data.vulnerabilities)
        
        summary_data = [['Severity', 'Count', 'Percentage']]
        total_vulns = len(report_data.vulnerabilities)
        
        for severity in ['critical', 'high', 'medium', 'low', 'info']:
            count = len(vuln_by_severity.get(severity, []))
            percentage = (count / total_vulns * 100) if total_vulns > 0 else 0
            summary_data.append([severity.title(), str(count), f"{percentage:.1f}%"])
        
        summary_table = Table(summary_data, colWidths=[1.5*inch, 1*inch, 1.5*inch])
        summary_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor('#1976d2')),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 12),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.HexColor('#f5f5f5')),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))
        
        story.append(summary_table)
        story.append(Spacer(1, 20))
        
        # Top vulnerabilities
        story.append(Paragraph("Critical Vulnerabilities", styles['Heading2']))
        story.append(Spacer(1, 12))
        
        critical_vulns = sorted(report_data.vulnerabilities, 
                              key=lambda v: v.get("severity_score", 0), reverse=True)[:5]
        
        for i, vuln in enumerate(critical_vulns, 1):
            story.append(Paragraph(f"{i}. {vuln.get('title', 'Unknown Vulnerability')}", styles['Heading3']))
            story.append(Paragraph(f"Severity: {vuln.get('severity', 'Unknown')}", styles['Normal']))
            story.append(Paragraph(f"CVSS Score: {vuln.get('cvss_score', 'N/A')}", styles['Normal']))
            story.append(Paragraph(vuln.get('description', 'No description available'), styles['Normal']))
            story.append(Spacer(1, 12))
        
        return story
    
    def _create_pdf_ai_insights(self, report_data: ReportData, styles):
        """Create PDF AI insights section"""
        story = []
        
        story.append(Paragraph("AI-Generated Security Insights", styles['Heading1']))
        story.append(Spacer(1, 12))
        
        for insight in report_data.ai_insights[:10]:  # Top 10 insights
            story.append(Paragraph(insight.get('title', 'AI Insight'), styles['Heading3']))
            story.append(Paragraph(insight.get('summary', 'No summary available'), styles['Normal']))
            story.append(Spacer(1, 10))
        
        return story
    
    def _create_pdf_remediation_section(self, report_data: ReportData, styles):
        """Create PDF remediation section"""
        story = []
        
        story.append(Paragraph("Remediation Recommendations", styles['Heading1']))
        story.append(Spacer(1, 12))
        
        for i, plan in enumerate(report_data.remediation_plans[:15], 1):
            story.append(Paragraph(f"{i}. {plan.get('title', 'Remediation Item')}", styles['Heading3']))
            story.append(Paragraph(f"Priority: {plan.get('priority', 'Medium')}", styles['Normal']))
            story.append(Paragraph(f"Effort: {plan.get('effort_estimate', 'Unknown')}", styles['Normal']))
            story.append(Paragraph(plan.get('description', 'No description'), styles['Normal']))
            story.append(Spacer(1, 12))
        
        return story
    
    def _create_pdf_compliance_section(self, report_data: ReportData, config: ReportConfiguration, styles):
        """Create PDF compliance section"""
        story = []
        
        framework = config.template.compliance_framework
        story.append(Paragraph(f"{framework} Compliance Assessment", styles['Heading1']))
        story.append(Spacer(1, 12))
        
        # Compliance status summary
        if report_data.compliance_status:
            compliance_data = [['Control', 'Status', 'Score']]
            
            for control, status in report_data.compliance_status.items():
                score = status.get('score', 'N/A')
                compliance_status = status.get('status', 'Unknown')
                compliance_data.append([control, compliance_status, str(score)])
            
            compliance_table = Table(compliance_data, colWidths=[2*inch, 1.5*inch, 1*inch])
            compliance_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor('#1976d2')),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 12),
                ('GRID', (0, 0), (-1, -1), 1, colors.black)
            ]))
            
            story.append(compliance_table)
        
        return story
    
    def _create_default_templates(self):
        """Create default HTML templates"""
        
        # Executive Summary HTML Template
        executive_template = """
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Executive Summary - {{ report_data.target }}</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 20px; background-color: #f8f9fa; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { border-bottom: 3px solid #1976d2; padding-bottom: 20px; margin-bottom: 30px; }
        .title { color: #1976d2; font-size: 28px; font-weight: bold; margin: 0; }
        .subtitle { color: #666; font-size: 16px; margin: 5px 0 0 0; }
        .summary-cards { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin: 30px 0; }
        .card { background: #f8f9fa; padding: 20px; border-radius: 8px; border-left: 4px solid #1976d2; }
        .card-title { font-weight: bold; color: #333; margin-bottom: 10px; }
        .card-value { font-size: 24px; font-weight: bold; color: #1976d2; }
        .risk-high { color: #d32f2f; }
        .risk-medium { color: #f57c00; }
        .risk-low { color: #388e3c; }
        .section { margin: 30px 0; }
        .section-title { font-size: 20px; font-weight: bold; color: #333; border-bottom: 2px solid #e0e0e0; padding-bottom: 10px; margin-bottom: 15px; }
        .vulnerability-list { list-style: none; padding: 0; }
        .vulnerability-item { background: #fff; border: 1px solid #e0e0e0; border-radius: 4px; padding: 12px; margin-bottom: 8px; }
        .vulnerability-title { font-weight: bold; color: #333; }
        .vulnerability-severity { display: inline-block; padding: 2px 8px; border-radius: 12px; font-size: 12px; color: white; margin-left: 10px; }
        .severity-critical { background-color: #d32f2f; }
        .severity-high { background-color: #f57c00; }
        .severity-medium { background-color: #fbc02d; }
        .severity-low { background-color: #388e3c; }
        .footer { margin-top: 40px; padding-top: 20px; border-top: 1px solid #e0e0e0; font-size: 12px; color: #666; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">Security Assessment Report</h1>
            <p class="subtitle">Target: {{ report_data.target }}</p>
            <p class="subtitle">Generated: {{ generation_time.strftime('%Y-%m-%d %H:%M:%S') }}</p>
        </div>
        
        <div class="summary-cards">
            <div class="card">
                <div class="card-title">Vulnerabilities Found</div>
                <div class="card-value">{{ report_data.vulnerabilities|length }}</div>
            </div>
            <div class="card">
                <div class="card-title">Risk Score</div>
                <div class="card-value {{ 'risk-high' if report_data.ai_risk_score >= 7 else 'risk-medium' if report_data.ai_risk_score >= 4 else 'risk-low' }}">
                    {{ "%.1f"|format(report_data.ai_risk_score) }}/10
                </div>
            </div>
            <div class="card">
                <div class="card-title">AI Confidence</div>
                <div class="card-value">{{ "%.1f"|format(report_data.ai_confidence) }}%</div>
            </div>
            <div class="card">
                <div class="card-title">Scan Duration</div>
                <div class="card-value">{{ report_data.duration }}</div>
            </div>
        </div>
        
        <div class="section">
            <h2 class="section-title">Critical Findings</h2>
            <ul class="vulnerability-list">
                {% for vuln in top_vulnerabilities[:10] %}
                <li class="vulnerability-item">
                    <div class="vulnerability-title">
                        {{ vuln.get('title', 'Unknown Vulnerability') }}
                        <span class="vulnerability-severity severity-{{ vuln.get('severity', 'unknown').lower() }}">
                            {{ vuln.get('severity', 'Unknown').upper() }}
                        </span>
                    </div>
                    <div>{{ vuln.get('description', 'No description available') }}</div>
                </li>
                {% endfor %}
            </ul>
        </div>
        
        {% if config.include_ai_insights and report_data.ai_insights %}
        <div class="section">
            <h2 class="section-title">AI Security Insights</h2>
            {% for insight in report_data.ai_insights[:5] %}
            <div class="vulnerability-item">
                <div class="vulnerability-title">{{ insight.get('title', 'AI Insight') }}</div>
                <div>{{ insight.get('summary', 'No summary available') }}</div>
            </div>
            {% endfor %}
        </div>
        {% endif %}
        
        <div class="footer">
            <p>This report was generated by NexusScan AI-Enhanced Security Scanner v{{ report_data.report_version }}</p>
            <p>Report ID: {{ report_data.scan_id }} | Generated: {{ report_data.report_generated_at.strftime('%Y-%m-%d %H:%M:%S') }}</p>
        </div>
    </div>
</body>
</html>
        """
        
        # Write template files
        with open(self.template_dir / "executive_summary.html", "w", encoding="utf-8") as f:
            f.write(executive_template)
        
        # Technical Detailed HTML Template
        technical_template = """
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Technical Report - {{ report_data.target }}</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 20px; background-color: #f8f9fa; line-height: 1.6; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { border-bottom: 3px solid #1976d2; padding-bottom: 20px; margin-bottom: 30px; }
        .title { color: #1976d2; font-size: 28px; font-weight: bold; margin: 0; }
        .subtitle { color: #666; font-size: 16px; margin: 5px 0 0 0; }
        .toc { background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0; }
        .toc ul { list-style: none; padding-left: 0; }
        .toc li { margin: 5px 0; }
        .toc a { color: #1976d2; text-decoration: none; }
        .section { margin: 30px 0; }
        .section-title { font-size: 20px; font-weight: bold; color: #333; border-bottom: 2px solid #e0e0e0; padding-bottom: 10px; margin-bottom: 15px; }
        .vulnerability-table { width: 100%; border-collapse: collapse; margin: 20px 0; }
        .vulnerability-table th, .vulnerability-table td { border: 1px solid #ddd; padding: 12px; text-align: left; }
        .vulnerability-table th { background-color: #1976d2; color: white; }
        .vulnerability-table tr:nth-child(even) { background-color: #f9f9f9; }
        .severity-critical { background-color: #ffebee; color: #d32f2f; font-weight: bold; }
        .severity-high { background-color: #fff3e0; color: #f57c00; font-weight: bold; }
        .severity-medium { background-color: #fffde7; color: #fbc02d; font-weight: bold; }
        .severity-low { background-color: #e8f5e8; color: #388e3c; font-weight: bold; }
        .code-block { background: #f5f5f5; padding: 15px; border-radius: 4px; font-family: 'Courier New', monospace; font-size: 14px; overflow-x: auto; }
        .footer { margin-top: 40px; padding-top: 20px; border-top: 1px solid #e0e0e0; font-size: 12px; color: #666; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">Technical Security Assessment Report</h1>
            <p class="subtitle">Target: {{ report_data.target }}</p>
            <p class="subtitle">Scan Type: {{ report_data.scan_type }}</p>
            <p class="subtitle">Generated: {{ generation_time.strftime('%Y-%m-%d %H:%M:%S') }}</p>
        </div>
        
        <div class="toc">
            <h3>Table of Contents</h3>
            <ul>
                <li><a href="#executive-summary">Executive Summary</a></li>
                <li><a href="#methodology">Methodology</a></li>
                <li><a href="#findings">Detailed Findings</a></li>
                <li><a href="#ai-analysis">AI Analysis</a></li>
                <li><a href="#remediation">Remediation Plans</a></li>
                <li><a href="#appendices">Appendices</a></li>
            </ul>
        </div>
        
        <div class="section" id="executive-summary">
            <h2 class="section-title">Executive Summary</h2>
            <p>This comprehensive security assessment identified <strong>{{ report_data.vulnerabilities|length }} vulnerabilities</strong> 
            across the target environment. The AI-enhanced analysis resulted in an overall risk score of 
            <strong>{{ "%.1f"|format(report_data.ai_risk_score) }}/10</strong> with 
            <strong>{{ "%.1f"|format(report_data.ai_confidence) }}%</strong> confidence.</p>
        </div>
        
        <div class="section" id="methodology">
            <h2 class="section-title">Assessment Methodology</h2>
            <p>The security assessment was conducted using NexusScan's AI-enhanced scanning capabilities, 
            incorporating multiple security tools and advanced artificial intelligence analysis.</p>
            
            <h3>Scanning Tools Used:</h3>
            <ul>
                {% for tool, results in report_data.tool_results.items() %}
                <li><strong>{{ tool.title() }}</strong> - {{ results.get('description', 'Security scanning tool') }}</li>
                {% endfor %}
            </ul>
        </div>
        
        <div class="section" id="findings">
            <h2 class="section-title">Detailed Security Findings</h2>
            
            <h3>Vulnerability Summary by Severity</h3>
            <table class="vulnerability-table">
                <thead>
                    <tr>
                        <th>Severity</th>
                        <th>Count</th>
                        <th>Percentage</th>
                    </tr>
                </thead>
                <tbody>
                    {% for severity, vulns in vulnerabilities_by_severity.items() %}
                    <tr>
                        <td class="severity-{{ severity }}">{{ severity.title() }}</td>
                        <td>{{ vulns|length }}</td>
                        <td>{{ "%.1f"|format((vulns|length / report_data.vulnerabilities|length * 100) if report_data.vulnerabilities else 0) }}%</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
            
            <h3>Critical Vulnerabilities</h3>
            <table class="vulnerability-table">
                <thead>
                    <tr>
                        <th>Vulnerability</th>
                        <th>Severity</th>
                        <th>CVSS Score</th>
                        <th>Description</th>
                    </tr>
                </thead>
                <tbody>
                    {% for vuln in top_vulnerabilities[:20] %}
                    <tr>
                        <td>{{ vuln.get('title', 'Unknown Vulnerability') }}</td>
                        <td class="severity-{{ vuln.get('severity', 'unknown').lower() }}">{{ vuln.get('severity', 'Unknown') }}</td>
                        <td>{{ vuln.get('cvss_score', 'N/A') }}</td>
                        <td>{{ vuln.get('description', 'No description available') }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        
        {% if config.include_ai_insights and report_data.ai_insights %}
        <div class="section" id="ai-analysis">
            <h2 class="section-title">AI-Enhanced Security Analysis</h2>
            {% for insight in report_data.ai_insights %}
            <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 10px 0;">
                <h4>{{ insight.get('title', 'AI Security Insight') }}</h4>
                <p>{{ insight.get('summary', 'No summary available') }}</p>
                {% if insight.get('recommendations') %}
                <strong>Recommendations:</strong>
                <ul>
                    {% for rec in insight.get('recommendations', [])[:5] %}
                    <li>{{ rec }}</li>
                    {% endfor %}
                </ul>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        {% endif %}
        
        {% if config.include_remediation and report_data.remediation_plans %}
        <div class="section" id="remediation">
            <h2 class="section-title">Remediation Action Plan</h2>
            {% for plan in report_data.remediation_plans %}
            <div style="background: #fff3e0; padding: 15px; border-radius: 8px; margin: 10px 0; border-left: 4px solid #f57c00;">
                <h4>{{ plan.get('title', 'Remediation Item') }}</h4>
                <p><strong>Priority:</strong> {{ plan.get('priority', 'Medium') }}</p>
                <p><strong>Effort Estimate:</strong> {{ plan.get('effort_estimate', 'Unknown') }}</p>
                <p>{{ plan.get('description', 'No description available') }}</p>
                {% if plan.get('steps') %}
                <strong>Implementation Steps:</strong>
                <ol>
                    {% for step in plan.get('steps', [])[:10] %}
                    <li>{{ step }}</li>
                    {% endfor %}
                </ol>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        {% endif %}
        
        <div class="section" id="appendices">
            <h2 class="section-title">Technical Appendices</h2>
            <h3>Raw Scan Data</h3>
            <div class="code-block">
                <pre>{{ report_data.scan_summary | tojson(indent=2) }}</pre>
            </div>
        </div>
        
        <div class="footer">
            <p>This report was generated by NexusScan AI-Enhanced Security Scanner v{{ report_data.report_version }}</p>
            <p>Report ID: {{ report_data.scan_id }} | Generated: {{ report_data.report_generated_at.strftime('%Y-%m-%d %H:%M:%S') }}</p>
            <p>Confidence Score: {{ "%.1f"|format(report_data.ai_confidence) }}% | AI Risk Score: {{ "%.1f"|format(report_data.ai_risk_score) }}/10</p>
        </div>
    </div>
</body>
</html>
        """
        
        with open(self.template_dir / "technical_detailed.html", "w", encoding="utf-8") as f:
            f.write(technical_template)
    
    def _group_vulnerabilities_by_severity(self, vulnerabilities: List[Dict[str, Any]]) -> Dict[str, List[Dict[str, Any]]]:
        """Group vulnerabilities by severity level"""
        grouped = {'critical': [], 'high': [], 'medium': [], 'low': [], 'info': []}
        
        for vuln in vulnerabilities:
            severity = vuln.get('severity', 'unknown').lower()
            if severity in grouped:
                grouped[severity].append(vuln)
            else:
                grouped['info'].append(vuln)
        
        return grouped
    
    def _get_risk_level(self, risk_score: float) -> str:
        """Get risk level from numeric score"""
        if risk_score >= 8.0:
            return "critical"
        elif risk_score >= 6.0:
            return "high"
        elif risk_score >= 4.0:
            return "medium"
        elif risk_score >= 2.0:
            return "low"
        else:
            return "minimal"
    
    def _get_risk_color(self, risk_level: str):
        """Get color for risk level"""
        colors_map = {
            'critical': colors.HexColor('#d32f2f'),
            'high': colors.HexColor('#f57c00'),
            'medium': colors.HexColor('#fbc02d'),
            'low': colors.HexColor('#388e3c'),
            'minimal': colors.HexColor('#1976d2')
        }
        return colors_map.get(risk_level, colors.black)
    
    def _generate_default_executive_summary(self, report_data: ReportData) -> str:
        """Generate default executive summary"""
        vuln_count = len(report_data.vulnerabilities)
        risk_level = self._get_risk_level(report_data.ai_risk_score)
        
        return f"""
        This security assessment of {report_data.target} identified {vuln_count} vulnerabilities 
        with an overall risk rating of {risk_level.upper()}. The AI-enhanced analysis indicates 
        a risk score of {report_data.ai_risk_score:.1f} out of 10, with {report_data.ai_confidence:.1f}% confidence.
        
        Key findings include critical security issues that require immediate attention, as well as 
        recommendations for improving the overall security posture. This assessment was conducted 
        using advanced AI-powered security tools to provide comprehensive coverage and intelligent 
        risk analysis.
        """
    
    async def get_available_templates(self) -> List[ReportTemplate]:
        """Get list of available report templates"""
        return list(self.templates.values())
    
    async def create_custom_template(self, template: ReportTemplate) -> bool:
        """Create custom report template"""
        try:
            self.templates[template.template_id] = template
            logger.info(f"Custom template created: {template.name}")
            return True
        except Exception as e:
            logger.error(f"Failed to create custom template: {e}")
            return False
    
    async def schedule_report(self, 
                            report_data: ReportData,
                            config: ReportConfiguration,
                            schedule: Dict[str, Any]) -> bool:
        """Schedule automatic report generation"""
        # This would integrate with a task scheduler
        # For now, just log the scheduling request
        logger.info(f"Report scheduled: {config.template.name} for {report_data.target}")
        return True