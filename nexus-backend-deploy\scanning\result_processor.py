#!/usr/bin/env python3
"""
Result Processor for NexusScan Security Scanning Engine
Aggregates, analyzes, and processes security scan results from multiple tools
"""

import logging
import re
import hashlib
from datetime import datetime
from typing import Dict, List, Optional, Any, Union, Tuple
from dataclasses import dataclass, field
from enum import Enum
import ipaddress
from collections import defaultdict, Counter

# ToolResult and ToolFinding dataclasses defined below
# (replaces non-existent tool_manager module)
from .types import ScanType

logger = logging.getLogger(__name__)


@dataclass
class ToolResult:
    """Result from a security tool execution"""
    tool_name: str
    target: str
    status: str = "completed"
    start_time: datetime = field(default_factory=datetime.now)
    end_time: datetime = field(default_factory=datetime.now)
    raw_output: str = ""
    parsed_findings: List[Dict[str, Any]] = field(default_factory=list)
    error_message: str = ""
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class ToolConfig:
    """Configuration for a security tool"""
    tool_name: str
    enabled: bool = True
    timeout: int = 300
    max_concurrent: int = 1
    options: Dict[str, Any] = field(default_factory=dict)
    environment: Dict[str, str] = field(default_factory=dict)


class ToolManager:
    """Basic tool manager for scanning orchestration"""
    
    def __init__(self):
        self.available_tools = ["nmap", "nuclei", "sqlmap"]
        self.tool_configs: Dict[str, ToolConfig] = {}
        
    def get_available_tools(self) -> List[str]:
        """Get list of available tools"""
        return self.available_tools.copy()
        
    def get_tool_config(self, tool_name: str) -> Optional[ToolConfig]:
        """Get configuration for a tool"""
        return self.tool_configs.get(tool_name)
        
    def set_tool_config(self, tool_name: str, config: ToolConfig):
        """Set configuration for a tool"""
        self.tool_configs[tool_name] = config


@dataclass  
class ToolFinding:
    """Individual finding from a security tool"""
    tool_name: str
    finding_id: str
    title: str
    description: str
    severity: str
    target: str
    location: str = ""
    evidence: str = ""
    raw_data: Dict[str, Any] = field(default_factory=dict)


class VulnerabilitySeverity(Enum):
    """Standardized vulnerability severity levels"""
    CRITICAL = "critical"
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"
    INFO = "info"
    UNKNOWN = "unknown"


class ConfidenceLevel(Enum):
    """Confidence levels for findings"""
    CONFIRMED = "confirmed"      # High confidence (90-100%)
    LIKELY = "likely"           # Medium-high confidence (70-89%)
    POSSIBLE = "possible"       # Medium confidence (50-69%)
    UNLIKELY = "unlikely"       # Low confidence (30-49%)
    UNVERIFIED = "unverified"   # Very low confidence (0-29%)


class FindingCategory(Enum):
    """Categories for vulnerability findings"""
    NETWORK = "network"
    WEB_APPLICATION = "web_application"
    AUTHENTICATION = "authentication"
    AUTHORIZATION = "authorization"
    CONFIGURATION = "configuration"
    CRYPTOGRAPHY = "cryptography"
    INPUT_VALIDATION = "input_validation"
    INJECTION = "injection"
    DISCLOSURE = "disclosure"
    DENIAL_OF_SERVICE = "denial_of_service"
    MALWARE = "malware"
    COMPLIANCE = "compliance"
    OTHER = "other"


@dataclass
class VulnerabilityFinding:
    """Standardized vulnerability finding"""
    id: str
    title: str
    description: str
    severity: VulnerabilitySeverity
    confidence: ConfidenceLevel
    category: FindingCategory
    target: str
    affected_service: str = ""
    affected_port: Optional[int] = None
    location: str = ""
    evidence: str = ""
    impact: str = ""
    remediation: str = ""
    references: List[str] = field(default_factory=list)
    tags: List[str] = field(default_factory=list)
    cvss_score: Optional[float] = None
    cve_ids: List[str] = field(default_factory=list)
    cwe_ids: List[str] = field(default_factory=list)
    source_tools: List[str] = field(default_factory=list)
    source_findings: List[str] = field(default_factory=list)
    first_seen: datetime = field(default_factory=datetime.now)
    last_seen: datetime = field(default_factory=datetime.now)
    occurrence_count: int = 1
    false_positive: bool = False
    risk_score: float = 0.0
    exploitability: str = "unknown"
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class HostInformation:
    """Information about a scanned host"""
    ip_address: str
    hostnames: List[str] = field(default_factory=list)
    mac_address: str = ""
    vendor: str = ""
    operating_system: str = ""
    os_confidence: float = 0.0
    open_ports: List[int] = field(default_factory=list)
    services: Dict[int, str] = field(default_factory=dict)
    vulnerabilities: List[str] = field(default_factory=list)  # Vulnerability IDs
    distance: int = 0
    status: str = "up"
    response_time: float = 0.0
    first_seen: datetime = field(default_factory=datetime.now)
    last_seen: datetime = field(default_factory=datetime.now)


@dataclass
class ScanStatistics:
    """Statistics for processed scan results"""
    total_targets: int
    responsive_targets: int
    total_ports_discovered: int
    total_services_identified: int
    total_vulnerabilities: int
    severity_breakdown: Dict[str, int] = field(default_factory=dict)
    category_breakdown: Dict[str, int] = field(default_factory=dict)
    confidence_breakdown: Dict[str, int] = field(default_factory=dict)
    tools_used: List[str] = field(default_factory=list)
    scan_duration: float = 0.0
    coverage_percentage: float = 0.0
    false_positive_rate: float = 0.0
    risk_score_distribution: Dict[str, int] = field(default_factory=dict)


@dataclass
class ProcessedResult:
    """Complete processed result from security scan"""
    scan_id: str
    scan_type: ScanType
    targets: List[str]
    hosts: List[HostInformation]
    vulnerabilities: List[VulnerabilityFinding]
    statistics: ScanStatistics
    recommendations: List[str] = field(default_factory=list)
    executive_summary: str = ""
    technical_summary: str = ""
    risk_assessment: Dict[str, Any] = field(default_factory=dict)
    compliance_status: Dict[str, Any] = field(default_factory=dict)
    processing_time: float = 0.0
    processed_at: datetime = field(default_factory=datetime.now)


class ResultProcessor:
    """Processes and aggregates security scan results"""

    def __init__(self):
        """Initialize result processor"""
        self.logger = logging.getLogger(__name__)
        
        # Severity mapping for different tools
        self.severity_mappings = {
            "nuclei": {
                "critical": VulnerabilitySeverity.CRITICAL,
                "high": VulnerabilitySeverity.HIGH,
                "medium": VulnerabilitySeverity.MEDIUM,
                "low": VulnerabilitySeverity.LOW,
                "info": VulnerabilitySeverity.INFO,
                "unknown": VulnerabilitySeverity.UNKNOWN
            },
            "nmap": {
                "critical": VulnerabilitySeverity.CRITICAL,
                "high": VulnerabilitySeverity.HIGH,
                "medium": VulnerabilitySeverity.MEDIUM,
                "low": VulnerabilitySeverity.LOW,
                "info": VulnerabilitySeverity.INFO
            }
        }
        
        # Category mapping keywords
        self.category_keywords = {
            FindingCategory.INJECTION: ["injection", "sqli", "xss", "command", "ldap", "xpath", "nosql"],
            FindingCategory.AUTHENTICATION: ["auth", "login", "password", "credential", "session"],
            FindingCategory.AUTHORIZATION: ["access", "privilege", "permission", "acl", "rbac"],
            FindingCategory.CONFIGURATION: ["config", "misconfiguration", "default", "hardening"],
            FindingCategory.CRYPTOGRAPHY: ["ssl", "tls", "crypto", "encryption", "certificate", "hash"],
            FindingCategory.DISCLOSURE: ["disclosure", "exposure", "leak", "information", "sensitive"],
            FindingCategory.DENIAL_OF_SERVICE: ["dos", "ddos", "resource", "exhaustion", "flooding"],
            FindingCategory.WEB_APPLICATION: ["web", "http", "https", "webapp", "application"],
            FindingCategory.NETWORK: ["network", "tcp", "udp", "port", "protocol", "firewall"]
        }
        
        # Statistics
        self.processing_stats = {
            "scans_processed": 0,
            "vulnerabilities_processed": 0,
            "hosts_processed": 0,
            "false_positives_detected": 0,
            "duplicates_removed": 0,
            "total_processing_time": 0.0
        }

    async def process_scan_results(self, 
                                 scan_id: str,
                                 tool_results: Dict[str, ToolResult],
                                 scan_type: ScanType,
                                 targets: List[str]) -> ProcessedResult:
        """Process complete scan results from multiple tools
        
        Args:
            scan_id: Unique scan identifier
            tool_results: Results from different tools
            scan_type: Type of scan performed
            targets: Original scan targets
            
        Returns:
            Processed and aggregated result
        """
        start_time = datetime.now()
        
        try:
            self.logger.info(f"Processing scan results for {scan_id} with {len(tool_results)} tools")
            
            # Extract findings from all tools
            all_findings = self._extract_all_findings(tool_results)
            
            # Extract host information
            hosts = self._extract_host_information(tool_results, targets)
            
            # Normalize and standardize findings
            normalized_findings = self._normalize_findings(all_findings)
            
            # Remove duplicates and merge similar findings
            unique_findings = self._deduplicate_findings(normalized_findings)
            
            # Categorize and enrich findings
            enriched_findings = self._enrich_findings(unique_findings, hosts)
            
            # Calculate risk scores
            scored_findings = self._calculate_risk_scores(enriched_findings)
            
            # Detect false positives
            validated_findings = self._validate_findings(scored_findings)
            
            # Generate statistics
            statistics = self._generate_statistics(validated_findings, hosts, tool_results, targets)
            
            # Generate recommendations
            recommendations = self._generate_recommendations(validated_findings, scan_type)
            
            # Generate summaries
            executive_summary = self._generate_executive_summary(validated_findings, statistics)
            technical_summary = self._generate_technical_summary(validated_findings, statistics)
            
            # Assess overall risk
            risk_assessment = self._assess_overall_risk(validated_findings, statistics)
            
            # Check compliance status
            compliance_status = self._check_compliance_status(validated_findings, scan_type)
            
            processing_time = (datetime.now() - start_time).total_seconds()
            
            # Create processed result
            processed_result = ProcessedResult(
                scan_id=scan_id,
                scan_type=scan_type,
                targets=targets,
                hosts=hosts,
                vulnerabilities=validated_findings,
                statistics=statistics,
                recommendations=recommendations,
                executive_summary=executive_summary,
                technical_summary=technical_summary,
                risk_assessment=risk_assessment,
                compliance_status=compliance_status,
                processing_time=processing_time
            )
            
            # Update statistics
            self._update_processing_statistics(processed_result)
            
            self.logger.info(f"Processed scan {scan_id}: {len(validated_findings)} vulnerabilities, {len(hosts)} hosts")
            return processed_result
            
        except Exception as e:
            self.logger.error(f"Failed to process scan results for {scan_id}: {e}")
            raise

    def _extract_all_findings(self, tool_results: Dict[str, ToolResult]) -> List[ToolFinding]:
        """Extract all findings from tool results
        
        Args:
            tool_results: Results from different tools
            
        Returns:
            List of all findings
        """
        all_findings = []
        
        for tool_name, result in tool_results.items():
            if result.findings:
                # Add tool name to each finding for tracking
                for finding in result.findings:
                    finding.metadata["source_tool"] = tool_name
                    all_findings.append(finding)
        
        self.logger.debug(f"Extracted {len(all_findings)} findings from {len(tool_results)} tools")
        return all_findings

    def _extract_host_information(self, tool_results: Dict[str, ToolResult], targets: List[str]) -> List[HostInformation]:
        """Extract host information from tool results
        
        Args:
            tool_results: Results from different tools
            targets: Original scan targets
            
        Returns:
            List of host information
        """
        hosts_data = defaultdict(lambda: {
            "hostnames": set(),
            "ports": set(),
            "services": {},
            "vulnerabilities": set(),
            "os_info": [],
            "mac_address": "",
            "vendor": "",
            "distance": 0,
            "response_time": 0.0
        })
        
        # Process Nmap results for host discovery
        if "nmap" in tool_results:
            nmap_result = tool_results["nmap"]
            # Would integrate with actual NmapScanner results
            # For now, create basic host entries from targets
            for target in targets:
                try:
                    # Try to parse as IP
                    ip = str(ipaddress.ip_address(target))
                    hosts_data[ip]["status"] = "up"
                except ipaddress.AddressValueError:
                    # Could be hostname or network
                    hosts_data[target]["status"] = "unknown"
        
        # Convert to HostInformation objects
        hosts = []
        for ip, data in hosts_data.items():
            host = HostInformation(
                ip_address=ip,
                hostnames=list(data["hostnames"]),
                mac_address=data["mac_address"],
                vendor=data["vendor"],
                open_ports=list(data["ports"]),
                services=data["services"],
                vulnerabilities=list(data["vulnerabilities"]),
                distance=data["distance"],
                response_time=data["response_time"]
            )
            hosts.append(host)
        
        return hosts

    def _normalize_findings(self, findings: List[ToolFinding]) -> List[VulnerabilityFinding]:
        """Normalize findings to standard format
        
        Args:
            findings: Raw tool findings
            
        Returns:
            List of normalized vulnerability findings
        """
        normalized = []
        
        for finding in findings:
            # Generate unique ID
            finding_id = self._generate_finding_id(finding)
            
            # Map severity
            severity = self._map_severity(finding.severity, finding.tool_name)
            
            # Map confidence
            confidence = self._map_confidence(finding.confidence)
            
            # Categorize finding
            category = self._categorize_finding(finding)
            
            # Extract CVE and CWE IDs
            cve_ids = self._extract_cve_ids(finding.description + " " + finding.evidence)
            cwe_ids = self._extract_cwe_ids(finding.description + " " + finding.evidence)
            
            normalized_finding = VulnerabilityFinding(
                id=finding_id,
                title=finding.title or f"{finding.tool_name} Finding",
                description=finding.description,
                severity=severity,
                confidence=confidence,
                category=category,
                target=finding.target,
                location=finding.location,
                evidence=finding.evidence,
                references=finding.references,
                tags=finding.tags,
                cve_ids=cve_ids,
                cwe_ids=cwe_ids,
                source_tools=[finding.tool_name],
                source_findings=[finding.finding_id],
                metadata=finding.metadata
            )
            
            normalized.append(normalized_finding)
        
        return normalized

    def _deduplicate_findings(self, findings: List[VulnerabilityFinding]) -> List[VulnerabilityFinding]:
        """Remove duplicate findings and merge similar ones
        
        Args:
            findings: Normalized findings
            
        Returns:
            List of unique findings
        """
        # Group findings by similarity
        finding_groups = defaultdict(list)
        
        for finding in findings:
            # Create similarity key based on target, title, and category
            similarity_key = (
                finding.target,
                finding.category,
                finding.title.lower().strip(),
                finding.severity
            )
            finding_groups[similarity_key].append(finding)
        
        unique_findings = []
        duplicates_removed = 0
        
        for group in finding_groups.values():
            if len(group) == 1:
                unique_findings.append(group[0])
            else:
                # Merge similar findings
                merged_finding = self._merge_findings(group)
                unique_findings.append(merged_finding)
                duplicates_removed += len(group) - 1
        
        self.logger.debug(f"Removed {duplicates_removed} duplicate findings")
        return unique_findings

    def _merge_findings(self, findings: List[VulnerabilityFinding]) -> VulnerabilityFinding:
        """Merge multiple similar findings into one
        
        Args:
            findings: List of similar findings to merge
            
        Returns:
            Merged finding
        """
        base_finding = findings[0]
        
        # Combine information from all findings
        all_tools = set()
        all_source_findings = []
        all_evidence = []
        all_references = set()
        all_tags = set()
        all_cve_ids = set()
        all_cwe_ids = set()
        
        for finding in findings:
            all_tools.update(finding.source_tools)
            all_source_findings.extend(finding.source_findings)
            if finding.evidence:
                all_evidence.append(finding.evidence)
            all_references.update(finding.references)
            all_tags.update(finding.tags)
            all_cve_ids.update(finding.cve_ids)
            all_cwe_ids.update(finding.cwe_ids)
        
        # Create merged finding
        merged = VulnerabilityFinding(
            id=base_finding.id,
            title=base_finding.title,
            description=base_finding.description,
            severity=base_finding.severity,
            confidence=base_finding.confidence,
            category=base_finding.category,
            target=base_finding.target,
            location=base_finding.location,
            evidence="\n".join(all_evidence),
            references=list(all_references),
            tags=list(all_tags),
            cve_ids=list(all_cve_ids),
            cwe_ids=list(all_cwe_ids),
            source_tools=list(all_tools),
            source_findings=all_source_findings,
            occurrence_count=len(findings),
            first_seen=min(f.first_seen for f in findings),
            last_seen=max(f.last_seen for f in findings)
        )
        
        return merged

    def _enrich_findings(self, findings: List[VulnerabilityFinding], hosts: List[HostInformation]) -> List[VulnerabilityFinding]:
        """Enrich findings with additional context
        
        Args:
            findings: Findings to enrich
            hosts: Host information for context
            
        Returns:
            Enriched findings
        """
        # Create host lookup
        host_lookup = {host.ip_address: host for host in hosts}
        
        for finding in findings:
            # Add host information
            if finding.target in host_lookup:
                host = host_lookup[finding.target]
                finding.metadata["host_os"] = host.operating_system
                finding.metadata["open_ports"] = host.open_ports
                finding.metadata["services"] = host.services
            
            # Add impact assessment
            finding.impact = self._assess_finding_impact(finding)
            
            # Add remediation advice
            finding.remediation = self._generate_remediation_advice(finding)
            
            # Determine exploitability
            finding.exploitability = self._assess_exploitability(finding)
        
        return findings

    def _calculate_risk_scores(self, findings: List[VulnerabilityFinding]) -> List[VulnerabilityFinding]:
        """Calculate risk scores for findings
        
        Args:
            findings: Findings to score
            
        Returns:
            Findings with risk scores
        """
        for finding in findings:
            # Base score from severity
            severity_scores = {
                VulnerabilitySeverity.CRITICAL: 10.0,
                VulnerabilitySeverity.HIGH: 8.0,
                VulnerabilitySeverity.MEDIUM: 6.0,
                VulnerabilitySeverity.LOW: 4.0,
                VulnerabilitySeverity.INFO: 2.0,
                VulnerabilitySeverity.UNKNOWN: 1.0
            }
            
            base_score = severity_scores.get(finding.severity, 1.0)
            
            # Adjust for confidence
            confidence_multipliers = {
                ConfidenceLevel.CONFIRMED: 1.0,
                ConfidenceLevel.LIKELY: 0.9,
                ConfidenceLevel.POSSIBLE: 0.7,
                ConfidenceLevel.UNLIKELY: 0.5,
                ConfidenceLevel.UNVERIFIED: 0.3
            }
            
            confidence_multiplier = confidence_multipliers.get(finding.confidence, 0.5)
            
            # Adjust for exploitability
            exploitability_multipliers = {
                "high": 1.2,
                "medium": 1.0,
                "low": 0.8,
                "unknown": 0.9
            }
            
            exploitability_multiplier = exploitability_multipliers.get(finding.exploitability, 1.0)
            
            # Calculate final risk score
            risk_score = base_score * confidence_multiplier * exploitability_multiplier
            finding.risk_score = min(risk_score, 10.0)  # Cap at 10.0
        
        return findings

    def _validate_findings(self, findings: List[VulnerabilityFinding]) -> List[VulnerabilityFinding]:
        """Validate findings and detect false positives
        
        Args:
            findings: Findings to validate
            
        Returns:
            Validated findings
        """
        validated = []
        false_positives = 0
        
        for finding in findings:
            # Simple false positive detection based on confidence and evidence
            if (finding.confidence == ConfidenceLevel.UNVERIFIED and 
                finding.risk_score < 3.0 and
                not finding.evidence):
                finding.false_positive = True
                false_positives += 1
            
            # Additional validation rules can be added here
            
            validated.append(finding)
        
        self.logger.debug(f"Detected {false_positives} potential false positives")
        return validated

    def _generate_statistics(self, 
                           findings: List[VulnerabilityFinding],
                           hosts: List[HostInformation],
                           tool_results: Dict[str, ToolResult],
                           targets: List[str]) -> ScanStatistics:
        """Generate scan statistics
        
        Args:
            findings: Processed findings
            hosts: Host information
            tool_results: Original tool results
            targets: Scan targets
            
        Returns:
            Scan statistics
        """
        # Severity breakdown
        severity_breakdown = Counter()
        for finding in findings:
            severity_breakdown[finding.severity.value] += 1
        
        # Category breakdown
        category_breakdown = Counter()
        for finding in findings:
            category_breakdown[finding.category.value] += 1
        
        # Confidence breakdown
        confidence_breakdown = Counter()
        for finding in findings:
            confidence_breakdown[finding.confidence.value] += 1
        
        # Risk score distribution
        risk_score_distribution = {"low": 0, "medium": 0, "high": 0, "critical": 0}
        for finding in findings:
            if finding.risk_score >= 8.0:
                risk_score_distribution["critical"] += 1
            elif finding.risk_score >= 6.0:
                risk_score_distribution["high"] += 1
            elif finding.risk_score >= 4.0:
                risk_score_distribution["medium"] += 1
            else:
                risk_score_distribution["low"] += 1
        
        # Calculate scan duration
        scan_duration = 0.0
        for result in tool_results.values():
            scan_duration += result.execution_time.total_seconds()
        
        # Calculate false positive rate
        total_findings = len(findings)
        false_positives = sum(1 for f in findings if f.false_positive)
        false_positive_rate = (false_positives / total_findings * 100) if total_findings > 0 else 0
        
        statistics = ScanStatistics(
            total_targets=len(targets),
            responsive_targets=len([h for h in hosts if h.status == "up"]),
            total_ports_discovered=sum(len(h.open_ports) for h in hosts),
            total_services_identified=sum(len(h.services) for h in hosts),
            total_vulnerabilities=total_findings,
            severity_breakdown=dict(severity_breakdown),
            category_breakdown=dict(category_breakdown),
            confidence_breakdown=dict(confidence_breakdown),
            tools_used=list(tool_results.keys()),
            scan_duration=scan_duration,
            false_positive_rate=false_positive_rate,
            risk_score_distribution=risk_score_distribution
        )
        
        return statistics

    def _generate_recommendations(self, findings: List[VulnerabilityFinding], scan_type: ScanType) -> List[str]:
        """Generate security recommendations
        
        Args:
            findings: Processed findings
            scan_type: Type of scan performed
            
        Returns:
            List of recommendations
        """
        recommendations = []
        
        # Priority recommendations based on critical/high findings
        critical_high = [f for f in findings if f.severity in [VulnerabilitySeverity.CRITICAL, VulnerabilitySeverity.HIGH]]
        
        if critical_high:
            recommendations.append(f"Address {len(critical_high)} critical/high severity vulnerabilities immediately")
        
        # Category-specific recommendations
        categories = Counter(f.category for f in findings)
        for category, count in categories.most_common(3):
            if count > 0:
                recommendations.append(f"Review {count} {category.value.replace('_', ' ')} related issues")
        
        # Scan type specific recommendations
        if scan_type == ScanType.WEB:
            web_findings = [f for f in findings if f.category == FindingCategory.WEB_APPLICATION]
            if web_findings:
                recommendations.append("Implement web application firewall (WAF) protection")
        
        elif scan_type == ScanType.NETWORK:
            network_findings = [f for f in findings if f.category == FindingCategory.NETWORK]
            if network_findings:
                recommendations.append("Review network segmentation and firewall rules")
        
        return recommendations

    def _generate_executive_summary(self, findings: List[VulnerabilityFinding], statistics: ScanStatistics) -> str:
        """Generate executive summary
        
        Args:
            findings: Processed findings
            statistics: Scan statistics
            
        Returns:
            Executive summary string
        """
        total_vulns = len(findings)
        critical_count = statistics.severity_breakdown.get("critical", 0)
        high_count = statistics.severity_breakdown.get("high", 0)
        
        summary = f"Security scan identified {total_vulns} vulnerabilities across {statistics.responsive_targets} targets. "
        
        if critical_count > 0:
            summary += f"CRITICAL: {critical_count} critical severity issues require immediate attention. "
        
        if high_count > 0:
            summary += f"HIGH: {high_count} high severity issues should be addressed promptly. "
        
        if total_vulns == 0:
            summary = "Security scan completed with no significant vulnerabilities identified. "
        
        summary += f"Scan covered {statistics.total_services_identified} services across {statistics.total_ports_discovered} ports."
        
        return summary

    def _generate_technical_summary(self, findings: List[VulnerabilityFinding], statistics: ScanStatistics) -> str:
        """Generate technical summary
        
        Args:
            findings: Processed findings
            statistics: Scan statistics
            
        Returns:
            Technical summary string
        """
        summary = f"Technical Analysis: {len(findings)} vulnerabilities discovered using {len(statistics.tools_used)} scanning tools. "
        
        top_categories = Counter(f.category.value for f in findings).most_common(3)
        if top_categories:
            cat_summary = ", ".join([f"{cat}: {count}" for cat, count in top_categories])
            summary += f"Primary vulnerability categories: {cat_summary}. "
        
        avg_risk = sum(f.risk_score for f in findings) / len(findings) if findings else 0
        summary += f"Average risk score: {avg_risk:.1f}/10. "
        
        summary += f"False positive rate: {statistics.false_positive_rate:.1f}%."
        
        return summary

    def _assess_overall_risk(self, findings: List[VulnerabilityFinding], statistics: ScanStatistics) -> Dict[str, Any]:
        """Assess overall security risk
        
        Args:
            findings: Processed findings
            statistics: Scan statistics
            
        Returns:
            Risk assessment dictionary
        """
        if not findings:
            return {"risk_level": "low", "risk_score": 0.0, "risk_factors": []}
        
        # Calculate overall risk score
        critical_count = statistics.severity_breakdown.get("critical", 0)
        high_count = statistics.severity_breakdown.get("high", 0)
        medium_count = statistics.severity_breakdown.get("medium", 0)
        
        # Weighted risk calculation
        risk_score = (critical_count * 10 + high_count * 7 + medium_count * 4) / len(findings)
        
        # Determine risk level
        if risk_score >= 8.0:
            risk_level = "critical"
        elif risk_score >= 6.0:
            risk_level = "high"
        elif risk_score >= 4.0:
            risk_level = "medium"
        else:
            risk_level = "low"
        
        # Identify risk factors
        risk_factors = []
        if critical_count > 0:
            risk_factors.append(f"{critical_count} critical vulnerabilities")
        if high_count > 5:
            risk_factors.append(f"High volume of serious vulnerabilities ({high_count})")
        
        return {
            "risk_level": risk_level,
            "risk_score": risk_score,
            "risk_factors": risk_factors,
            "recommended_action": self._get_risk_action(risk_level)
        }

    def _check_compliance_status(self, findings: List[VulnerabilityFinding], scan_type: ScanType) -> Dict[str, Any]:
        """Check compliance status against common frameworks
        
        Args:
            findings: Processed findings
            scan_type: Scan type
            
        Returns:
            Compliance status dictionary
        """
        compliance_status = {}
        
        # Basic compliance checks
        critical_vulns = [f for f in findings if f.severity == VulnerabilitySeverity.CRITICAL]
        
        # PCI DSS compliance (basic check)
        if scan_type == ScanType.WEB:
            pci_issues = len([f for f in findings if any(tag in f.tags for tag in ["payment", "card", "pci"])])
            compliance_status["pci_dss"] = "non_compliant" if critical_vulns or pci_issues > 0 else "compliant"
        
        # OWASP Top 10 compliance
        owasp_categories = ["injection", "authentication", "exposure", "xxe", "access_control"]
        owasp_issues = len([f for f in findings if any(cat in f.tags for cat in owasp_categories)])
        compliance_status["owasp_top10"] = "issues_found" if owasp_issues > 0 else "compliant"
        
        return compliance_status

    # Helper methods for processing

    def _generate_finding_id(self, finding: ToolFinding) -> str:
        """Generate unique finding ID"""
        content = f"{finding.tool_name}_{finding.target}_{finding.title}_{finding.description}"
        return hashlib.md5(content.encode()).hexdigest()[:16]

    def _map_severity(self, severity: str, tool_name: str) -> VulnerabilitySeverity:
        """Map tool-specific severity to standard severity"""
        severity_lower = severity.lower()
        
        if tool_name in self.severity_mappings:
            return self.severity_mappings[tool_name].get(severity_lower, VulnerabilitySeverity.UNKNOWN)
        
        # Generic mapping
        if severity_lower in ["critical", "high", "medium", "low", "info"]:
            return VulnerabilitySeverity(severity_lower)
        
        return VulnerabilitySeverity.UNKNOWN

    def _map_confidence(self, confidence: float) -> ConfidenceLevel:
        """Map confidence score to confidence level"""
        if confidence >= 0.9:
            return ConfidenceLevel.CONFIRMED
        elif confidence >= 0.7:
            return ConfidenceLevel.LIKELY
        elif confidence >= 0.5:
            return ConfidenceLevel.POSSIBLE
        elif confidence >= 0.3:
            return ConfidenceLevel.UNLIKELY
        else:
            return ConfidenceLevel.UNVERIFIED

    def _categorize_finding(self, finding: ToolFinding) -> FindingCategory:
        """Categorize finding based on content"""
        content = (finding.title + " " + finding.description + " " + " ".join(finding.tags)).lower()
        
        for category, keywords in self.category_keywords.items():
            if any(keyword in content for keyword in keywords):
                return category
        
        return FindingCategory.OTHER

    def _extract_cve_ids(self, text: str) -> List[str]:
        """Extract CVE IDs from text"""
        cve_pattern = r'CVE-\d{4}-\d{4,7}'
        return re.findall(cve_pattern, text, re.IGNORECASE)

    def _extract_cwe_ids(self, text: str) -> List[str]:
        """Extract CWE IDs from text"""
        cwe_pattern = r'CWE-\d+'
        return re.findall(cwe_pattern, text, re.IGNORECASE)

    def _assess_finding_impact(self, finding: VulnerabilityFinding) -> str:
        """Assess potential impact of finding"""
        if finding.severity == VulnerabilitySeverity.CRITICAL:
            return "Critical business impact - immediate remediation required"
        elif finding.severity == VulnerabilitySeverity.HIGH:
            return "High business impact - remediation should be prioritized"
        elif finding.severity == VulnerabilitySeverity.MEDIUM:
            return "Medium business impact - should be addressed in normal cycle"
        else:
            return "Low business impact - address as resources allow"

    def _generate_remediation_advice(self, finding: VulnerabilityFinding) -> str:
        """Generate remediation advice for finding"""
        # Basic remediation advice based on category
        remediation_templates = {
            FindingCategory.INJECTION: "Implement input validation and parameterized queries",
            FindingCategory.AUTHENTICATION: "Strengthen authentication mechanisms and password policies",
            FindingCategory.AUTHORIZATION: "Review and restrict access controls",
            FindingCategory.CONFIGURATION: "Harden system configuration according to security baselines",
            FindingCategory.CRYPTOGRAPHY: "Update to strong encryption protocols and algorithms",
            FindingCategory.DISCLOSURE: "Remove or restrict access to sensitive information"
        }
        
        return remediation_templates.get(finding.category, "Review and address according to security best practices")

    def _assess_exploitability(self, finding: VulnerabilityFinding) -> str:
        """Assess exploitability of finding"""
        if finding.category in [FindingCategory.INJECTION, FindingCategory.AUTHENTICATION]:
            return "high"
        elif finding.category in [FindingCategory.CONFIGURATION, FindingCategory.DISCLOSURE]:
            return "medium"
        else:
            return "low"

    def _get_risk_action(self, risk_level: str) -> str:
        """Get recommended action for risk level"""
        actions = {
            "critical": "Immediate remediation required - suspend operations if necessary",
            "high": "Urgent remediation required within 24-48 hours",
            "medium": "Remediation should be completed within 1-2 weeks",
            "low": "Remediation can be scheduled in normal maintenance cycle"
        }
        return actions.get(risk_level, "Review findings and determine appropriate action")

    def _update_processing_statistics(self, result: ProcessedResult):
        """Update processing statistics"""
        self.processing_stats["scans_processed"] += 1
        self.processing_stats["vulnerabilities_processed"] += len(result.vulnerabilities)
        self.processing_stats["hosts_processed"] += len(result.hosts)
        self.processing_stats["total_processing_time"] += result.processing_time
        
        false_positives = sum(1 for v in result.vulnerabilities if v.false_positive)
        self.processing_stats["false_positives_detected"] += false_positives

    def get_processing_statistics(self) -> Dict[str, Any]:
        """Get processing statistics"""
        return self.processing_stats.copy()