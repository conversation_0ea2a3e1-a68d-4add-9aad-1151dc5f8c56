# NexusScan Contabo Server SSH Connection Script
# This script connects to the Contabo server and runs diagnostic commands

param(
    [string]$Command = "",
    [switch]$Interactive = $false
)

$SERVER = "************"
$USERNAME = "root"
$PASSWORD = "292827sSNex"

Write-Host "🔗 Connecting to Contabo Server: $SERVER" -ForegroundColor Cyan
Write-Host "👤 Username: $USERNAME" -ForegroundColor Yellow

if ($Interactive) {
    Write-Host "🔧 Starting interactive SSH session..." -ForegroundColor Green
    Write-Host "💡 You'll need to manually enter the password: $PASSWORD" -ForegroundColor Yellow
    Write-Host ""
    
    # Start interactive SSH session
    ssh -o StrictHostKeyChecking=no $USERNAME@$SERVER
} elseif ($Command -ne "") {
    Write-Host "🚀 Executing command: $Command" -ForegroundColor Green
    Write-Host "💡 You'll need to manually enter the password: $PASSWORD" -ForegroundColor Yellow
    Write-Host ""
    
    # Execute specific command
    ssh -o StrictHostKeyChecking=no $USERNAME@$SERVER $Command
} else {
    Write-Host "🔍 Running NexusScan Backend Diagnostics..." -ForegroundColor Green
    Write-Host "💡 You'll need to manually enter the password: $PASSWORD" -ForegroundColor Yellow
    Write-Host ""
    
    # Create a diagnostic script to run on the server
    $DiagnosticScript = @"
echo '🔍 NexusScan Backend Diagnostic Report'
echo '====================================='
echo ''
echo '1. 🖥️  System Information:'
echo '   Hostname:' `$(hostname)
echo '   Uptime:' `$(uptime)
echo '   Memory:' `$(free -h | grep Mem)
echo ''
echo '2. 🐍 Python Processes:'
ps aux | grep python | grep -v grep || echo '   No Python processes found'
echo ''
echo '3. 🌐 Network Ports:'
echo '   Port 8090 (NexusScan Backend):'
netstat -tlnp | grep 8090 || echo '   Port 8090 not in use'
echo '   Port 8000 (Alternative Backend):'
netstat -tlnp | grep 8000 || echo '   Port 8000 not in use'
echo '   Port 22 (SSH):'
netstat -tlnp | grep :22 || echo '   SSH port not found'
echo ''
echo '4. 📁 Directory Search:'
echo '   Looking for NexusScan directories...'
find / -name "*nexus*" -type d 2>/dev/null | head -5 || echo '   No nexus directories found'
echo ''
echo '5. 🔧 System Services:'
echo '   NexusScan service status:'
systemctl status nexusscan 2>/dev/null || echo '   No nexusscan service found'
echo '   All running services with nexus:'
systemctl list-units --type=service --state=running | grep -i nexus || echo '   No nexus services running'
echo ''
echo '6. 📂 Current Directory Contents:'
echo '   Current location:' `$(pwd)
echo '   Directory contents:'
ls -la
echo ''
echo '7. 🔍 Backend Search:'
echo '   Looking for main.py files:'
find / -name "main.py" 2>/dev/null | head -5 || echo '   No main.py files found'
echo '   Looking for Python backend files:'
find / -name "*backend*" -type f 2>/dev/null | head -5 || echo '   No backend files found'
echo ''
echo '✅ Diagnostic complete!'
"@

    # Execute the diagnostic script
    ssh -o StrictHostKeyChecking=no $USERNAME@$SERVER $DiagnosticScript
}

Write-Host ""
Write-Host "🏁 SSH session completed." -ForegroundColor Green
