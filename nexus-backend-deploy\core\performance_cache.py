#!/usr/bin/env python3
"""
Performance-Oriented Caching System
Advanced caching for high-performance operations with pre-loading, predictive caching,
and performance optimization strategies
"""

import asyncio
import time
import logging
import pickle
import gzip
from typing import Any, Dict, List, Optional, Callable, Set, Tuple
from dataclasses import dataclass, field
from datetime import datetime, timed<PERSON>ta
from enum import Enum
from collections import defaultdict, deque
import threading
from concurrent.futures import ThreadPoolExecutor

from core.cache import <PERSON>ache<PERSON>anager, get_cache_manager, CacheStrategy

logger = logging.getLogger(__name__)

class PerformanceCacheType(Enum):
    """Performance cache types"""
    HOT = "hot"  # Frequently accessed data
    WARM = "warm"  # Moderately accessed data
    COLD = "cold"  # Infrequently accessed data
    PREDICTIVE = "predictive"  # Predicted future needs

@dataclass
class AccessPattern:
    """Track access patterns for predictive caching"""
    key: str
    access_times: deque = field(default_factory=lambda: deque(maxlen=100))
    access_frequency: int = 0
    last_access: float = 0
    predicted_next_access: float = 0
    cache_tier: PerformanceCacheType = PerformanceCacheType.COLD
    
    def record_access(self):
        """Record an access event"""
        now = time.time()
        self.access_times.append(now)
        self.access_frequency += 1
        self.last_access = now
        self._update_predictions()
    
    def _update_predictions(self):
        """Update predicted next access time"""
        if len(self.access_times) < 2:
            return
        
        # Calculate average interval between accesses
        intervals = []
        for i in range(1, len(self.access_times)):
            intervals.append(self.access_times[i] - self.access_times[i-1])
        
        if intervals:
            avg_interval = sum(intervals) / len(intervals)
            self.predicted_next_access = self.last_access + avg_interval
    
    def should_promote(self) -> bool:
        """Determine if item should be promoted to higher tier"""
        if self.cache_tier == PerformanceCacheType.COLD and self.access_frequency > 5:
            return True
        elif self.cache_tier == PerformanceCacheType.WARM and self.access_frequency > 20:
            return True
        return False
    
    def should_demote(self) -> bool:
        """Determine if item should be demoted to lower tier"""
        time_since_access = time.time() - self.last_access
        
        if self.cache_tier == PerformanceCacheType.HOT and time_since_access > 3600:  # 1 hour
            return True
        elif self.cache_tier == PerformanceCacheType.WARM and time_since_access > 86400:  # 24 hours
            return True
        return False

@dataclass
class PerformanceMetrics:
    """Performance metrics for cache optimization"""
    total_requests: int = 0
    cache_hits: int = 0
    cache_misses: int = 0
    tier_hits: Dict[PerformanceCacheType, int] = field(default_factory=dict)
    avg_response_time: float = 0.0
    predictive_accuracy: float = 0.0
    prefetch_hits: int = 0
    compression_ratio: float = 0.0

class TieredCache:
    """Multi-tiered cache with performance optimization"""
    
    def __init__(self, 
                 hot_cache_size: int = 100,
                 warm_cache_size: int = 500,
                 cold_cache_size: int = 2000,
                 enable_compression: bool = True,
                 enable_prefetching: bool = True):
        
        self.hot_cache_size = hot_cache_size
        self.warm_cache_size = warm_cache_size
        self.cold_cache_size = cold_cache_size
        self.enable_compression = enable_compression
        self.enable_prefetching = enable_prefetching
        
        # Tier-specific caches
        self.hot_cache: Dict[str, Any] = {}
        self.warm_cache: Dict[str, Any] = {}
        self.cold_cache: Dict[str, Any] = {}
        
        # Access pattern tracking
        self.access_patterns: Dict[str, AccessPattern] = {}
        self.metrics = PerformanceMetrics()
        
        # Prefetching
        self.prefetch_queue: Set[str] = set()
        self.prefetch_executor = ThreadPoolExecutor(max_workers=2)
        
        # Threading
        self._lock = threading.RLock()
        
        # Start background optimization task
        if self.enable_prefetching:
            asyncio.create_task(self._prefetch_task())
        asyncio.create_task(self._optimization_task())
    
    async def get(self, key: str, loader: Optional[Callable] = None) -> Optional[Any]:
        """Get value with tier-aware lookup"""
        start_time = time.time()
        
        with self._lock:
            self.metrics.total_requests += 1
            
            # Check hot cache first
            if key in self.hot_cache:
                self.metrics.cache_hits += 1
                self.metrics.tier_hits[PerformanceCacheType.HOT] = \
                    self.metrics.tier_hits.get(PerformanceCacheType.HOT, 0) + 1
                self._record_access(key, PerformanceCacheType.HOT)
                value = self.hot_cache[key]
            # Check warm cache
            elif key in self.warm_cache:
                self.metrics.cache_hits += 1
                self.metrics.tier_hits[PerformanceCacheType.WARM] = \
                    self.metrics.tier_hits.get(PerformanceCacheType.WARM, 0) + 1
                value = self.warm_cache[key]
                self._record_access(key, PerformanceCacheType.WARM)
                
                # Promote to hot cache if frequently accessed
                if key in self.access_patterns and self.access_patterns[key].should_promote():
                    await self._promote_to_hot(key, value)
            # Check cold cache
            elif key in self.cold_cache:
                self.metrics.cache_hits += 1
                self.metrics.tier_hits[PerformanceCacheType.COLD] = \
                    self.metrics.tier_hits.get(PerformanceCacheType.COLD, 0) + 1
                value = self._decompress_if_needed(self.cold_cache[key])
                self._record_access(key, PerformanceCacheType.COLD)
                
                # Promote to warm cache if frequently accessed
                if key in self.access_patterns and self.access_patterns[key].should_promote():
                    await self._promote_to_warm(key, value)
            else:
                # Cache miss - load if loader provided
                self.metrics.cache_misses += 1
                if loader:
                    value = await self._load_and_cache(key, loader)
                else:
                    value = None
        
        # Update response time metrics
        response_time = time.time() - start_time
        self._update_response_time(response_time)
        
        # Trigger predictive prefetching
        if value is not None and self.enable_prefetching:
            await self._trigger_predictive_prefetch(key)
        
        return value
    
    async def set(self, key: str, value: Any, tier: PerformanceCacheType = PerformanceCacheType.WARM) -> bool:
        """Set value in specified tier"""
        with self._lock:
            if tier == PerformanceCacheType.HOT:
                await self._set_hot(key, value)
            elif tier == PerformanceCacheType.WARM:
                await self._set_warm(key, value)
            else:
                await self._set_cold(key, value)
            
            self._record_access(key, tier)
            return True
    
    async def _set_hot(self, key: str, value: Any):
        """Set value in hot cache"""
        if len(self.hot_cache) >= self.hot_cache_size:
            await self._evict_from_hot()
        self.hot_cache[key] = value
    
    async def _set_warm(self, key: str, value: Any):
        """Set value in warm cache"""
        if len(self.warm_cache) >= self.warm_cache_size:
            await self._evict_from_warm()
        self.warm_cache[key] = value
    
    async def _set_cold(self, key: str, value: Any):
        """Set value in cold cache with compression"""
        if len(self.cold_cache) >= self.cold_cache_size:
            await self._evict_from_cold()
        
        compressed_value = self._compress_if_needed(value)
        self.cold_cache[key] = compressed_value
    
    def _compress_if_needed(self, value: Any) -> Any:
        """Compress value if compression is enabled"""
        if not self.enable_compression:
            return value
        
        try:
            # Serialize and compress
            serialized = pickle.dumps(value)
            if len(serialized) > 1024:  # Only compress if > 1KB
                compressed = gzip.compress(serialized)
                compression_ratio = len(compressed) / len(serialized)
                
                # Update compression metrics
                self.metrics.compression_ratio = (
                    self.metrics.compression_ratio * 0.9 + compression_ratio * 0.1
                )
                
                return {'compressed': True, 'data': compressed}
            return value
        except Exception as e:
            logger.warning(f"Compression failed: {e}")
            return value
    
    def _decompress_if_needed(self, value: Any) -> Any:
        """Decompress value if it was compressed"""
        if isinstance(value, dict) and value.get('compressed'):
            try:
                decompressed = gzip.decompress(value['data'])
                return pickle.loads(decompressed)
            except Exception as e:
                logger.warning(f"Decompression failed: {e}")
                return value
        return value
    
    def _record_access(self, key: str, tier: PerformanceCacheType):
        """Record access pattern"""
        if key not in self.access_patterns:
            self.access_patterns[key] = AccessPattern(key=key, cache_tier=tier)
        
        pattern = self.access_patterns[key]
        pattern.record_access()
        pattern.cache_tier = tier
    
    async def _promote_to_hot(self, key: str, value: Any):
        """Promote item from warm to hot cache"""
        if key in self.warm_cache:
            del self.warm_cache[key]
        await self._set_hot(key, value)
        logger.debug(f"Promoted {key} to hot cache")
    
    async def _promote_to_warm(self, key: str, value: Any):
        """Promote item from cold to warm cache"""
        if key in self.cold_cache:
            del self.cold_cache[key]
        await self._set_warm(key, value)
        logger.debug(f"Promoted {key} to warm cache")
    
    async def _evict_from_hot(self):
        """Evict item from hot cache using LRU"""
        if not self.hot_cache:
            return
        
        # Find least recently used item
        lru_key = min(self.access_patterns.keys(), 
                     key=lambda k: self.access_patterns[k].last_access 
                     if k in self.hot_cache else float('inf'))
        
        if lru_key in self.hot_cache:
            value = self.hot_cache.pop(lru_key)
            # Demote to warm cache
            await self._set_warm(lru_key, value)
    
    async def _evict_from_warm(self):
        """Evict item from warm cache"""
        if not self.warm_cache:
            return
        
        lru_key = min(self.access_patterns.keys(),
                     key=lambda k: self.access_patterns[k].last_access
                     if k in self.warm_cache else float('inf'))
        
        if lru_key in self.warm_cache:
            value = self.warm_cache.pop(lru_key)
            # Demote to cold cache
            await self._set_cold(lru_key, value)
    
    async def _evict_from_cold(self):
        """Evict item from cold cache"""
        if not self.cold_cache:
            return
        
        lru_key = min(self.access_patterns.keys(),
                     key=lambda k: self.access_patterns[k].last_access
                     if k in self.cold_cache else float('inf'))
        
        if lru_key in self.cold_cache:
            del self.cold_cache[lru_key]
            if lru_key in self.access_patterns:
                del self.access_patterns[lru_key]
    
    async def _load_and_cache(self, key: str, loader: Callable) -> Any:
        """Load value using loader and cache appropriately"""
        try:
            if asyncio.iscoroutinefunction(loader):
                value = await loader(key)
            else:
                value = loader(key)
            
            # Cache in warm tier by default
            await self.set(key, value, PerformanceCacheType.WARM)
            return value
        except Exception as e:
            logger.error(f"Failed to load value for key {key}: {e}")
            return None
    
    async def _trigger_predictive_prefetch(self, accessed_key: str):
        """Trigger predictive prefetching based on access patterns"""
        if not self.enable_prefetching:
            return
        
        # Simple prediction: prefetch related keys
        related_keys = self._predict_related_keys(accessed_key)
        for key in related_keys:
            if key not in self.prefetch_queue and not self._is_cached(key):
                self.prefetch_queue.add(key)
    
    def _predict_related_keys(self, key: str) -> List[str]:
        """Predict related keys that might be accessed next"""
        # Simple pattern-based prediction
        related = []
        
        # If key contains patterns, predict variations
        if ':' in key:
            base, suffix = key.rsplit(':', 1)
            # Predict sequential keys
            if suffix.isdigit():
                num = int(suffix)
                related.extend([f"{base}:{num+1}", f"{base}:{num+2}"])
        
        return related[:5]  # Limit predictions
    
    def _is_cached(self, key: str) -> bool:
        """Check if key is cached in any tier"""
        return key in self.hot_cache or key in self.warm_cache or key in self.cold_cache
    
    async def _prefetch_task(self):
        """Background task for prefetching predicted data"""
        while True:
            try:
                await asyncio.sleep(1)  # Check every second
                
                if self.prefetch_queue:
                    # Process prefetch queue
                    key = self.prefetch_queue.pop()
                    await self._prefetch_key(key)
            except Exception as e:
                logger.error(f"Prefetch task error: {e}")
    
    async def _prefetch_key(self, key: str):
        """Prefetch a specific key"""
        # This is a placeholder - in practice, you'd have loaders for different key types
        logger.debug(f"Prefetching key: {key}")
        # Actual prefetching would be implemented based on your data sources
    
    async def _optimization_task(self):
        """Background task for cache optimization"""
        while True:
            try:
                await asyncio.sleep(60)  # Optimize every minute
                await self._optimize_tiers()
                await self._cleanup_expired_patterns()
            except Exception as e:
                logger.error(f"Optimization task error: {e}")
    
    async def _optimize_tiers(self):
        """Optimize cache tier assignments"""
        current_time = time.time()
        
        # Review access patterns and adjust tier assignments
        for key, pattern in list(self.access_patterns.items()):
            if pattern.should_promote():
                if pattern.cache_tier == PerformanceCacheType.COLD and key in self.cold_cache:
                    value = self._decompress_if_needed(self.cold_cache[key])
                    del self.cold_cache[key]
                    await self._set_warm(key, value)
                elif pattern.cache_tier == PerformanceCacheType.WARM and key in self.warm_cache:
                    value = self.warm_cache[key]
                    del self.warm_cache[key]
                    await self._set_hot(key, value)
            
            elif pattern.should_demote():
                if pattern.cache_tier == PerformanceCacheType.HOT and key in self.hot_cache:
                    value = self.hot_cache[key]
                    del self.hot_cache[key]
                    await self._set_warm(key, value)
                elif pattern.cache_tier == PerformanceCacheType.WARM and key in self.warm_cache:
                    value = self.warm_cache[key]
                    del self.warm_cache[key]
                    await self._set_cold(key, value)
    
    async def _cleanup_expired_patterns(self):
        """Clean up old access patterns"""
        current_time = time.time()
        expired_keys = []
        
        for key, pattern in self.access_patterns.items():
            # Remove patterns for keys not accessed in 24 hours
            if current_time - pattern.last_access > 86400:
                expired_keys.append(key)
        
        for key in expired_keys:
            del self.access_patterns[key]
    
    def _update_response_time(self, response_time: float):
        """Update average response time"""
        self.metrics.avg_response_time = (
            self.metrics.avg_response_time * 0.9 + response_time * 0.1
        )
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """Get comprehensive performance statistics"""
        total_requests = self.metrics.total_requests
        hit_rate = (self.metrics.cache_hits / total_requests * 100) if total_requests > 0 else 0
        
        return {
            "hit_rate": hit_rate,
            "total_requests": total_requests,
            "cache_hits": self.metrics.cache_hits,
            "cache_misses": self.metrics.cache_misses,
            "avg_response_time": self.metrics.avg_response_time,
            "tier_distribution": {
                "hot": len(self.hot_cache),
                "warm": len(self.warm_cache),
                "cold": len(self.cold_cache)
            },
            "tier_hits": dict(self.metrics.tier_hits),
            "compression_ratio": self.metrics.compression_ratio,
            "prefetch_queue_size": len(self.prefetch_queue),
            "access_patterns": len(self.access_patterns)
        }

class PerformanceCacheManager:
    """High-level performance cache manager"""
    
    def __init__(self, 
                 base_cache_manager: Optional[CacheManager] = None,
                 enable_tiered_cache: bool = True,
                 tiered_cache_config: Optional[Dict[str, Any]] = None):
        
        self.base_cache = base_cache_manager or get_cache_manager()
        self.enable_tiered_cache = enable_tiered_cache
        
        if self.enable_tiered_cache:
            config = tiered_cache_config or {}
            self.tiered_cache = TieredCache(**config)
        else:
            self.tiered_cache = None
    
    async def get(self, key: str, loader: Optional[Callable] = None) -> Optional[Any]:
        """Get value with performance optimization"""
        if self.tiered_cache:
            return await self.tiered_cache.get(key, loader)
        else:
            # Fallback to base cache
            value = await self.base_cache.get(key)
            if value is None and loader:
                if asyncio.iscoroutinefunction(loader):
                    value = await loader(key)
                else:
                    value = loader(key)
                if value is not None:
                    await self.base_cache.set(key, value)
            return value
    
    async def set(self, key: str, value: Any, tier: Optional[PerformanceCacheType] = None) -> bool:
        """Set value with performance optimization"""
        if self.tiered_cache:
            tier = tier or PerformanceCacheType.WARM
            return await self.tiered_cache.set(key, value, tier)
        else:
            return await self.base_cache.set(key, value)
    
    def performance_cached(self, 
                          tier: PerformanceCacheType = PerformanceCacheType.WARM,
                          enable_prefetch: bool = True):
        """Decorator for performance-optimized caching"""
        def decorator(func: Callable):
            async def wrapper(*args, **kwargs):
                # Create cache key
                cache_key = f"{func.__name__}:{hash(str(args) + str(sorted(kwargs.items())))}"
                
                # Try to get from cache
                async def loader(key):
                    if asyncio.iscoroutinefunction(func):
                        return await func(*args, **kwargs)
                    else:
                        return func(*args, **kwargs)
                
                return await self.get(cache_key, loader)
            
            return wrapper
        return decorator
    
    def get_stats(self) -> Dict[str, Any]:
        """Get performance cache statistics"""
        stats = {"base_cache": "available"}
        
        if self.tiered_cache:
            stats["tiered_cache"] = self.tiered_cache.get_performance_stats()
        
        return stats

# Global performance cache manager
performance_cache_manager: Optional[PerformanceCacheManager] = None

def get_performance_cache_manager() -> PerformanceCacheManager:
    """Get global performance cache manager"""
    global performance_cache_manager
    if performance_cache_manager is None:
        performance_cache_manager = PerformanceCacheManager()
    return performance_cache_manager

def performance_cached(tier: PerformanceCacheType = PerformanceCacheType.WARM):
    """Convenience decorator for performance caching"""
    return get_performance_cache_manager().performance_cached(tier)