#!/usr/bin/env python3
"""
Unified Tool Manager V2 for NexusScan Desktop
Railway-compatible tool management with simulation support
"""

import asyncio
import logging
import os
from typing import Dict, List, Optional, Any, Union
from datetime import datetime

from security.tools.tool_registry import tool_registry, ScanOptions, ScanResult
from security.tools.tool_loader import initialize_tools, get_tools_status
from security.tools.railway_adapter import railway_adapter
from core.config import Config

logger = logging.getLogger(__name__)


class UnifiedToolManagerV2:
    """Enhanced unified manager for all security tools"""
    
    def __init__(self, config: Optional[Config] = None):
        """Initialize unified tool manager v2"""
        self.config = config or Config()
        self.railway_mode = railway_adapter.environment.is_railway
        self.tools_initialized = False
        self.tool_catalog = {}
        
        logger.info(f"UnifiedToolManagerV2 initialized - Railway: {self.railway_mode}")
    
    async def initialize(self) -> Dict[str, Any]:
        """Initialize all tools and build catalog"""
        if self.tools_initialized:
            return self.get_status()
        
        logger.info("Initializing security tools...")
        
        # Load all tools
        loading_report = initialize_tools()
        
        # Build tool catalog
        self._build_tool_catalog()
        
        self.tools_initialized = True
        
        initialization_report = {
            "timestamp": datetime.now().isoformat(),
            "railway_mode": self.railway_mode,
            "tools_loaded": loading_report["total_loaded"],
            "tools_available": loading_report["available_tools"],
            "failed_imports": loading_report["failed_imports"],
            "catalog_built": len(self.tool_catalog),
            "status": "success"
        }
        
        logger.info(f"Tool initialization complete: {loading_report['available_tools']} tools available")
        return initialization_report
    
    def _build_tool_catalog(self):
        """Build comprehensive tool catalog"""
        all_tools = tool_registry.list_all_tools()
        
        for tool_name, tool_info in all_tools.items():
            metadata = tool_info.get("metadata", {})
            available = tool_info.get("available", False)
            
            # Get tool instance for additional info
            tool_instance = tool_registry.get_tool(tool_name)
            
            self.tool_catalog[tool_name] = {
                "id": tool_name,
                "name": metadata.get("display_name", tool_name),
                "category": metadata.get("category", "unknown"),
                "description": metadata.get("description", "No description available"),
                "icon": self._get_tool_icon(metadata.get("category", "")),
                "supported_targets": metadata.get("capabilities", {}).get("supported_targets", []),
                "scan_types": self._get_scan_types(tool_name),
                "status": "available" if available else "unavailable",
                "platform": self._get_platform_info(tool_instance),
                "available_on": self._get_available_platforms(tool_instance),
                "primary_platform": "railway" if self.railway_mode else "windows",
                "fallback_tool": self._get_fallback_tool(tool_name),
                "capabilities": metadata.get("capabilities", {}),
                "enterprise_features": self._get_enterprise_features(tool_name),
                "version": metadata.get("version", "1.0.0"),
                "detected_path": self._get_tool_path(tool_instance)
            }
        
        logger.info(f"Built catalog for {len(self.tool_catalog)} tools")
    
    def get_available_tools(self) -> List[Dict[str, Any]]:
        """Get list of all available tools"""
        if not self.tools_initialized:
            asyncio.create_task(self.initialize())
            return []
        
        return [
            {
                "success": True,
                "data": list(self.tool_catalog.values()),
                "metadata": {
                    "total_tools": len(self.tool_catalog),
                    "available_tools": len([t for t in self.tool_catalog.values() if t["status"] == "available"]),
                    "categories": list(set(t["category"] for t in self.tool_catalog.values())),
                    "professional_grade": True,
                    "wsl_integration": not self.railway_mode,
                    "docker_support": True,
                    "enterprise_features": True,
                    "railway_mode": self.railway_mode
                }
            }
        ]
    
    async def execute_tool(self, tool_name: str, options: ScanOptions,
                          progress_callback: Optional[callable] = None) -> ScanResult:
        """Execute a security tool"""
        if not self.tools_initialized:
            await self.initialize()
        
        if not tool_registry.is_tool_available(tool_name):
            logger.error(f"Tool {tool_name} is not available")
            return ScanResult(
                tool_name=tool_name,
                target=options.target,
                status="failed",
                start_time=datetime.now().isoformat(),
                errors=[f"Tool {tool_name} is not available"]
            )
        
        try:
            logger.info(f"Executing {tool_name} on target: {options.target}")
            result = await tool_registry.execute_scan(tool_name, options, progress_callback)
            
            if result:
                logger.info(f"Tool {tool_name} completed with status: {result.status}")
                return result
            else:
                logger.error(f"Tool {tool_name} returned no result")
                return ScanResult(
                    tool_name=tool_name,
                    target=options.target,
                    status="failed",
                    start_time=datetime.now().isoformat(),
                    errors=["Tool execution returned no result"]
                )
                
        except Exception as e:
            logger.error(f"Tool execution failed for {tool_name}: {e}")
            return ScanResult(
                tool_name=tool_name,
                target=options.target,
                status="failed",
                start_time=datetime.now().isoformat(),
                errors=[str(e)]
            )
    
    def get_tool_info(self, tool_name: str) -> Optional[Dict[str, Any]]:
        """Get detailed information about a specific tool"""
        if not self.tools_initialized:
            return None
        
        return self.tool_catalog.get(tool_name)
    
    def get_status(self) -> Dict[str, Any]:
        """Get comprehensive manager status"""
        if not self.tools_initialized:
            return {
                "initialized": False,
                "message": "Tools not yet initialized"
            }
        
        tools_status = get_tools_status()
        
        return {
            "initialized": True,
            "railway_mode": self.railway_mode,
            "total_tools": len(self.tool_catalog),
            "available_tools": len([t for t in self.tool_catalog.values() if t["status"] == "available"]),
            "unavailable_tools": len([t for t in self.tool_catalog.values() if t["status"] == "unavailable"]),
            "tools_by_category": tools_status.get("tools_by_category", {}),
            "registry_status": tools_status,
            "execution_mode": "railway_simulation" if self.railway_mode else "native_hybrid",
            "capabilities": {
                "ai_tools": True,
                "external_tools": True,
                "simulation_mode": True,
                "real_time_progress": True,
                "batch_execution": True
            }
        }
    
    def _get_tool_icon(self, category: str) -> str:
        """Get appropriate icon for tool category"""
        icon_map = {
            "network_scanner": "network",
            "vulnerability_scanner": "security", 
            "web_scanner": "web",
            "database_scanner": "database",
            "ai_analyzer": "brain",
            "custom_analyzer": "cog",
            "payload_generator": "code"
        }
        return icon_map.get(category, "tool")
    
    def _get_scan_types(self, tool_name: str) -> List[str]:
        """Get available scan types for a tool"""
        scan_types_map = {
            "nmap": ["quick", "comprehensive", "stealth", "aggressive"],
            "nuclei": ["critical", "high", "medium", "low", "info"],
            "sqlmap": ["detection", "enumeration", "exploitation"],
            "gobuster": ["dir", "vhost", "dns"],
            "nikto": ["comprehensive", "fast", "plugins"],
            "vulnerability_agent": ["comprehensive", "targeted", "quick"]
        }
        return scan_types_map.get(tool_name, ["default"])
    
    def _get_platform_info(self, tool_instance) -> Optional[str]:
        """Get platform information for tool"""
        if not tool_instance:
            return None
        
        if self.railway_mode:
            return "railway"
        elif hasattr(tool_instance, 'get_execution_mode'):
            return tool_instance.get_execution_mode()
        else:
            return "unknown"
    
    def _get_available_platforms(self, tool_instance) -> List[str]:
        """Get list of platforms where tool is available"""
        platforms = []
        
        if self.railway_mode:
            platforms.append("railway")
        
        if tool_instance and hasattr(tool_instance, 'check_native_availability'):
            if tool_instance.check_native_availability():
                platforms.append("native")
        
        return platforms
    
    def _get_fallback_tool(self, tool_name: str) -> Optional[str]:
        """Get fallback tool for a given tool"""
        fallback_map = {
            "gobuster": "dirb",
            "dirb": "gobuster",
            "nikto": "nuclei",
            "wpscan": "nuclei"
        }
        return fallback_map.get(tool_name)
    
    def _get_enterprise_features(self, tool_name: str) -> List[str]:
        """Get enterprise features for a tool"""
        enterprise_features = {
            "nmap": ["script_engine", "timing_templates", "output_formats"],
            "nuclei": ["custom_templates", "ci_cd_integration", "reporting"],
            "vulnerability_agent": ["ai_analysis", "cvss_scoring", "remediation_guidance"],
            "hashcat": ["distributed_cracking", "rule_optimization", "reporting"],
            "testssl": ["compliance_reporting", "batch_scanning", "json_output"]
        }
        return enterprise_features.get(tool_name, [])
    
    def _get_tool_path(self, tool_instance) -> Optional[str]:
        """Get detected tool path"""
        if not tool_instance:
            return None
        
        if self.railway_mode:
            return "built-in"
        
        # Try to get tool path from instance
        if hasattr(tool_instance, 'tool_executable'):
            return getattr(tool_instance, 'tool_executable', None)
        
        return None


# Global instance
unified_tool_manager = UnifiedToolManagerV2()