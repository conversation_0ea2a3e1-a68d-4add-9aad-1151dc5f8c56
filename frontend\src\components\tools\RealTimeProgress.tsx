import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Progress } from '../ui/progress';
import { Badge } from '../ui/badge';
import { Button } from '../ui/button';
import { 
  Play, 
  Pause, 
  Square, 
  CheckCircle, 
  XCircle, 
  Clock, 
  AlertTriangle,
  Activity,
  Target,
  Shield,
  Zap
} from 'lucide-react';
import { WorkflowExecution, WorkflowStepResult } from '../../services/assessment-workflows';

interface RealTimeProgressProps {
  execution: WorkflowExecution;
  onCancel?: () => void;
  onPause?: () => void;
  onResume?: () => void;
  className?: string;
}

export const RealTimeProgress: React.FC<RealTimeProgressProps> = ({
  execution,
  onCancel,
  onPause,
  onResume,
  className = ''
}) => {
  const [timeElapsed, setTimeElapsed] = useState<string>('00:00');
  const [estimatedTimeRemaining, setEstimatedTimeRemaining] = useState<string>('--:--');

  useEffect(() => {
    const interval = setInterval(() => {
      if (execution.startedAt) {
        const elapsed = Date.now() - execution.startedAt.getTime();
        setTimeElapsed(formatDuration(elapsed));

        // Calculate estimated time remaining based on progress
        if (execution.status === 'running' && execution.currentStep > 0) {
          const avgTimePerStep = elapsed / execution.currentStep;
          const remainingSteps = execution.totalSteps - execution.currentStep;
          const estimatedRemaining = avgTimePerStep * remainingSteps;
          setEstimatedTimeRemaining(formatDuration(estimatedRemaining));
        }
      }
    }, 1000);

    return () => clearInterval(interval);
  }, [execution]);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'running':
        return <Activity className="h-4 w-4 text-blue-500 animate-pulse" />;
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'failed':
        return <XCircle className="h-4 w-4 text-red-500" />;
      case 'cancelled':
        return <Square className="h-4 w-4 text-gray-500" />;
      case 'pending':
        return <Clock className="h-4 w-4 text-yellow-500" />;
      default:
        return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStepStatusIcon = (status: string) => {
    switch (status) {
      case 'running':
        return <Activity className="h-3 w-3 text-blue-500 animate-pulse" />;
      case 'completed':
        return <CheckCircle className="h-3 w-3 text-green-500" />;
      case 'failed':
        return <XCircle className="h-3 w-3 text-red-500" />;
      case 'skipped':
        return <AlertTriangle className="h-3 w-3 text-yellow-500" />;
      case 'pending':
        return <Clock className="h-3 w-3 text-gray-400" />;
      default:
        return <Clock className="h-3 w-3 text-gray-400" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'running':
        return 'bg-blue-500';
      case 'completed':
        return 'bg-green-500';
      case 'failed':
        return 'bg-red-500';
      case 'cancelled':
        return 'bg-gray-500';
      case 'pending':
        return 'bg-yellow-500';
      default:
        return 'bg-gray-500';
    }
  };

  const progressPercentage = (execution.currentStep / execution.totalSteps) * 100;
  const completedSteps = execution.results.filter(r => r.status === 'completed').length;
  const failedSteps = execution.results.filter(r => r.status === 'failed').length;
  const skippedSteps = execution.results.filter(r => r.status === 'skipped').length;
  const totalVulnerabilities = execution.results.reduce((sum, r) => sum + (r.vulnerabilities?.length || 0), 0);

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Main Progress Card */}
      <Card>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              {getStatusIcon(execution.status)}
              <CardTitle className="text-lg">{execution.metadata.template}</CardTitle>
              <Badge variant={execution.status === 'running' ? 'default' : 'secondary'}>
                {execution.status.charAt(0).toUpperCase() + execution.status.slice(1)}
              </Badge>
            </div>
            <div className="flex items-center space-x-2">
              {execution.status === 'running' && (
                <>
                  {onPause && (
                    <Button variant="outline" size="sm" onClick={onPause}>
                      <Pause className="h-4 w-4" />
                    </Button>
                  )}
                  {onCancel && (
                    <Button variant="destructive" size="sm" onClick={onCancel}>
                      <Square className="h-4 w-4" />
                    </Button>
                  )}
                </>
              )}
              {execution.status === 'pending' && onResume && (
                <Button variant="default" size="sm" onClick={onResume}>
                  <Play className="h-4 w-4" />
                </Button>
              )}
            </div>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Progress Bar */}
          <div className="space-y-2">
            <div className="flex justify-between text-sm text-muted-foreground">
              <span>Progress: {execution.currentStep} / {execution.totalSteps} steps</span>
              <span>{Math.round(progressPercentage)}%</span>
            </div>
            <Progress value={progressPercentage} className="h-2" />
          </div>

          {/* Statistics Grid */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="flex items-center space-x-2 p-2 rounded-lg bg-muted/50">
              <CheckCircle className="h-4 w-4 text-green-500" />
              <div>
                <div className="text-lg font-semibold">{completedSteps}</div>
                <div className="text-xs text-muted-foreground">Completed</div>
              </div>
            </div>
            <div className="flex items-center space-x-2 p-2 rounded-lg bg-muted/50">
              <XCircle className="h-4 w-4 text-red-500" />
              <div>
                <div className="text-lg font-semibold">{failedSteps}</div>
                <div className="text-xs text-muted-foreground">Failed</div>
              </div>
            </div>
            <div className="flex items-center space-x-2 p-2 rounded-lg bg-muted/50">
              <Shield className="h-4 w-4 text-orange-500" />
              <div>
                <div className="text-lg font-semibold">{totalVulnerabilities}</div>
                <div className="text-xs text-muted-foreground">Vulnerabilities</div>
              </div>
            </div>
            <div className="flex items-center space-x-2 p-2 rounded-lg bg-muted/50">
              <Clock className="h-4 w-4 text-blue-500" />
              <div>
                <div className="text-lg font-semibold">{timeElapsed}</div>
                <div className="text-xs text-muted-foreground">Elapsed</div>
              </div>
            </div>
          </div>

          {/* Time Information */}
          <div className="flex justify-between text-sm text-muted-foreground">
            <span>Started: {execution.startedAt.toLocaleTimeString()}</span>
            {execution.status === 'running' && (
              <span>ETA: {estimatedTimeRemaining}</span>
            )}
            {execution.completedAt && (
              <span>Completed: {execution.completedAt.toLocaleTimeString()}</span>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Steps Progress */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-base flex items-center space-x-2">
            <Target className="h-4 w-4" />
            <span>Execution Steps</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {execution.results.map((step, index) => (
              <StepProgressItem 
                key={`${step.toolId}-${step.stepNumber}`}
                step={step}
                isActive={execution.currentStep === step.stepNumber}
              />
            ))}
            
            {/* Show pending steps */}
            {Array.from({ length: execution.totalSteps - execution.results.length }).map((_, index) => (
              <div 
                key={`pending-${execution.results.length + index + 1}`}
                className="flex items-center space-x-3 p-2 rounded-lg border border-dashed border-muted-foreground/30"
              >
                <Clock className="h-3 w-3 text-gray-400" />
                <div className="flex-1">
                  <div className="text-sm text-muted-foreground">
                    Step {execution.results.length + index + 1} - Pending
                  </div>
                </div>
                <Badge variant="outline">Pending</Badge>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Vulnerabilities Summary */}
      {totalVulnerabilities > 0 && (
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-base flex items-center space-x-2">
              <Zap className="h-4 w-4 text-orange-500" />
              <span>Vulnerabilities Found</span>
              <Badge variant="destructive">{totalVulnerabilities}</Badge>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {execution.results
                .filter(r => r.vulnerabilities && r.vulnerabilities.length > 0)
                .map((step, index) => (
                  <div key={index} className="flex items-center justify-between p-2 rounded border">
                    <span className="text-sm font-medium">{step.toolId}</span>
                    <Badge variant="destructive">{step.vulnerabilities?.length} issues</Badge>
                  </div>
                ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

interface StepProgressItemProps {
  step: WorkflowStepResult;
  isActive: boolean;
}

const StepProgressItem: React.FC<StepProgressItemProps> = ({ step, isActive }) => {
  const getStepStatusIcon = (status: string) => {
    switch (status) {
      case 'running':
        return <Activity className="h-3 w-3 text-blue-500 animate-pulse" />;
      case 'completed':
        return <CheckCircle className="h-3 w-3 text-green-500" />;
      case 'failed':
        return <XCircle className="h-3 w-3 text-red-500" />;
      case 'skipped':
        return <AlertTriangle className="h-3 w-3 text-yellow-500" />;
      case 'pending':
        return <Clock className="h-3 w-3 text-gray-400" />;
      default:
        return <Clock className="h-3 w-3 text-gray-400" />;
    }
  };

  const getStepStatusColor = (status: string) => {
    switch (status) {
      case 'running':
        return 'border-blue-500 bg-blue-50/10';
      case 'completed':
        return 'border-green-500 bg-green-50/10';
      case 'failed':
        return 'border-red-500 bg-red-50/10';
      case 'skipped':
        return 'border-yellow-500 bg-yellow-50/10';
      default:
        return 'border-muted';
    }
  };

  const duration = step.startedAt && step.completedAt 
    ? step.completedAt.getTime() - step.startedAt.getTime()
    : null;

  return (
    <div className={`flex items-center space-x-3 p-3 rounded-lg border transition-all duration-200 ${
      isActive ? 'ring-2 ring-blue-500 ring-offset-1' : ''
    } ${getStepStatusColor(step.status)}`}>
      {getStepStatusIcon(step.status)}
      <div className="flex-1 min-w-0">
        <div className="flex items-center justify-between">
          <span className="text-sm font-medium truncate">
            Step {step.stepNumber}: {step.toolId}
          </span>
          <div className="flex items-center space-x-2">
            {step.vulnerabilities && step.vulnerabilities.length > 0 && (
              <Badge variant="destructive" className="text-xs">
                {step.vulnerabilities.length} issues
              </Badge>
            )}
            <Badge 
              variant={step.status === 'completed' ? 'default' : 'secondary'}
              className="text-xs"
            >
              {step.status}
            </Badge>
          </div>
        </div>
        {step.error && (
          <div className="text-xs text-red-500 mt-1 truncate">{step.error}</div>
        )}
        {duration && (
          <div className="text-xs text-muted-foreground mt-1">
            Duration: {formatDuration(duration)}
          </div>
        )}
      </div>
    </div>
  );
};

function formatDuration(ms: number): string {
  const seconds = Math.floor(ms / 1000);
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);
  
  if (hours > 0) {
    return `${hours}:${(minutes % 60).toString().padStart(2, '0')}:${(seconds % 60).toString().padStart(2, '0')}`;
  }
  return `${minutes}:${(seconds % 60).toString().padStart(2, '0')}`;
}