
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Executive Summary - {{ report_data.target }}</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 20px; background-color: #f8f9fa; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { border-bottom: 3px solid #1976d2; padding-bottom: 20px; margin-bottom: 30px; }
        .title { color: #1976d2; font-size: 28px; font-weight: bold; margin: 0; }
        .subtitle { color: #666; font-size: 16px; margin: 5px 0 0 0; }
        .summary-cards { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin: 30px 0; }
        .card { background: #f8f9fa; padding: 20px; border-radius: 8px; border-left: 4px solid #1976d2; }
        .card-title { font-weight: bold; color: #333; margin-bottom: 10px; }
        .card-value { font-size: 24px; font-weight: bold; color: #1976d2; }
        .risk-high { color: #d32f2f; }
        .risk-medium { color: #f57c00; }
        .risk-low { color: #388e3c; }
        .section { margin: 30px 0; }
        .section-title { font-size: 20px; font-weight: bold; color: #333; border-bottom: 2px solid #e0e0e0; padding-bottom: 10px; margin-bottom: 15px; }
        .vulnerability-list { list-style: none; padding: 0; }
        .vulnerability-item { background: #fff; border: 1px solid #e0e0e0; border-radius: 4px; padding: 12px; margin-bottom: 8px; }
        .vulnerability-title { font-weight: bold; color: #333; }
        .vulnerability-severity { display: inline-block; padding: 2px 8px; border-radius: 12px; font-size: 12px; color: white; margin-left: 10px; }
        .severity-critical { background-color: #d32f2f; }
        .severity-high { background-color: #f57c00; }
        .severity-medium { background-color: #fbc02d; }
        .severity-low { background-color: #388e3c; }
        .footer { margin-top: 40px; padding-top: 20px; border-top: 1px solid #e0e0e0; font-size: 12px; color: #666; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">Security Assessment Report</h1>
            <p class="subtitle">Target: {{ report_data.target }}</p>
            <p class="subtitle">Generated: {{ generation_time.strftime('%Y-%m-%d %H:%M:%S') }}</p>
        </div>
        
        <div class="summary-cards">
            <div class="card">
                <div class="card-title">Vulnerabilities Found</div>
                <div class="card-value">{{ report_data.vulnerabilities|length }}</div>
            </div>
            <div class="card">
                <div class="card-title">Risk Score</div>
                <div class="card-value {{ 'risk-high' if report_data.ai_risk_score >= 7 else 'risk-medium' if report_data.ai_risk_score >= 4 else 'risk-low' }}">
                    {{ "%.1f"|format(report_data.ai_risk_score) }}/10
                </div>
            </div>
            <div class="card">
                <div class="card-title">AI Confidence</div>
                <div class="card-value">{{ "%.1f"|format(report_data.ai_confidence) }}%</div>
            </div>
            <div class="card">
                <div class="card-title">Scan Duration</div>
                <div class="card-value">{{ report_data.duration }}</div>
            </div>
        </div>
        
        <div class="section">
            <h2 class="section-title">Critical Findings</h2>
            <ul class="vulnerability-list">
                {% for vuln in top_vulnerabilities[:10] %}
                <li class="vulnerability-item">
                    <div class="vulnerability-title">
                        {{ vuln.get('title', 'Unknown Vulnerability') }}
                        <span class="vulnerability-severity severity-{{ vuln.get('severity', 'unknown').lower() }}">
                            {{ vuln.get('severity', 'Unknown').upper() }}
                        </span>
                    </div>
                    <div>{{ vuln.get('description', 'No description available') }}</div>
                </li>
                {% endfor %}
            </ul>
        </div>
        
        {% if config.include_ai_insights and report_data.ai_insights %}
        <div class="section">
            <h2 class="section-title">AI Security Insights</h2>
            {% for insight in report_data.ai_insights[:5] %}
            <div class="vulnerability-item">
                <div class="vulnerability-title">{{ insight.get('title', 'AI Insight') }}</div>
                <div>{{ insight.get('summary', 'No summary available') }}</div>
            </div>
            {% endfor %}
        </div>
        {% endif %}
        
        <div class="footer">
            <p>This report was generated by NexusScan AI-Enhanced Security Scanner v{{ report_data.report_version }}</p>
            <p>Report ID: {{ report_data.scan_id }} | Generated: {{ report_data.report_generated_at.strftime('%Y-%m-%d %H:%M:%S') }}</p>
        </div>
    </div>
</body>
</html>
        