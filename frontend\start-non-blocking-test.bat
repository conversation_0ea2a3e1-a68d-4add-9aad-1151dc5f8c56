@echo off
echo ==========================================
echo    NEXUSSCAN DESKTOP - NON-BLOCKING TEST
echo ==========================================
echo.
echo This version tests the new non-blocking startup:
echo - UI launches immediately
echo - WSL initializes in background
echo - Real-time status updates in header
echo - Tool pages show overlay when WSL not ready
echo.

cd frontend

echo [1/3] Cleaning up old processes...
taskkill /F /IM electron.exe 2>nul
taskkill /F /IM node.exe 2>nul

echo [2/3] Building latest Electron files...
call npm run build:electron

echo [3/3] Starting non-blocking version...
echo.
echo Starting NexusScan Desktop with non-blocking WSL initialization...
echo The UI should appear immediately while WSL initializes in background.
echo Watch the WSL status indicator in the top navigation bar!
echo.

set NODE_ENV=development
set ELECTRON_ENABLE_LOGGING=1

npm run dev:electron

pause
