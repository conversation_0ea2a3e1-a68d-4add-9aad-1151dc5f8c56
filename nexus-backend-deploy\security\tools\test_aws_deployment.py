#!/usr/bin/env python3
"""
Test AWS EC2 Deployment - Real Tools Verification
Verify all security tools are working on your AWS instance
"""

import asyncio
import aiohttp
import json
import time
from datetime import datetime
from typing import Dict, List, Any, Optional

# Your AWS EC2 instance URL
AWS_URL = "http://ec2-3-89-91-209.compute-1.amazonaws.com:8000"

# Expected tools from our backend
EXPECTED_TOOLS = {
    "Core Security Scanners": [
        "nmap",
        "nuclei", 
        "sqlmap"
    ],
    
    "AI-Powered Tools": [
        "vulnerability_agent"
    ],
    
    "External Tool Integration": [
        "gobuster",
        "nikto",
        "dirb",
        "whatweb",
        "hashcat",
        "john",
        "enum4linux",
        "smbclient",
        "testssl",
        "sslyze",
        "metasploit",
        "searchsploit"
    ]
}


class AWSDeploymentTester:
    """Test AWS deployment for all documented tools"""
    
    def __init__(self):
        self.base_url = AWS_URL
        self.results = {
            "test_timestamp": datetime.now().isoformat(),
            "base_url": self.base_url,
            "backend_status": "unknown",
            "tools_availability": {},
            "tool_execution_tests": {},
            "summary": {
                "total_expected_tools": 0,
                "tools_found": 0,
                "tools_available": 0,
                "tools_working": 0,
                "missing_tools": [],
                "broken_tools": [],
                "success_rate": 0.0
            }
        }
        
        # Count total expected tools
        for category, tools in EXPECTED_TOOLS.items():
            self.results["summary"]["total_expected_tools"] += len(tools)
    
    async def run_comprehensive_test(self):
        """Run comprehensive test of AWS deployment"""
        print(f"\n🧪 Testing AWS EC2 Deployment at: {self.base_url}")
        print("=" * 80)
        print(f"🕐 Test started: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=60)) as session:
            # Test 1: Backend Health
            await self._test_backend_health(session)
            
            # Test 2: Tools Availability
            await self._test_tools_availability(session)
            
            # Test 3: Real Tool Execution
            await self._test_real_tool_execution(session)
            
            # Test 4: API Endpoints
            await self._test_api_endpoints(session)
        
        # Generate final report
        self._generate_final_report()
    
    async def _test_backend_health(self, session: aiohttp.ClientSession):
        """Test basic backend health"""
        print("\n🏥 Testing Backend Health...")
        print("-" * 60)
        
        try:
            # Test health endpoint
            async with session.get(f"{self.base_url}/api/health") as response:
                if response.status == 200:
                    health_data = await response.json()
                    print(f"✅ Health endpoint: {response.status}")
                    print(f"   Backend status: {health_data.get('status', 'unknown')}")
                    print(f"   Database: {health_data.get('database', {}).get('status', 'unknown')}")
                    print(f"   Services: {len(health_data.get('services', []))}")
                    self.results["backend_status"] = "healthy"
                else:
                    print(f"❌ Health endpoint failed: {response.status}")
                    self.results["backend_status"] = "unhealthy"
        except Exception as e:
            print(f"❌ Health check failed: {e}")
            self.results["backend_status"] = "unreachable"
    
    async def _test_tools_availability(self, session: aiohttp.ClientSession):
        """Test tools availability endpoint"""
        print("\n🔧 Testing Tools Availability...")
        print("-" * 60)
        
        try:
            async with session.get(f"{self.base_url}/api/tools/available") as response:
                if response.status == 200:
                    tools_data = await response.json()
                    print(f"✅ Tools endpoint: {response.status}")
                    
                    if tools_data.get("success", False):
                        available_tools = tools_data.get("data", [])
                        metadata = tools_data.get("metadata", {})
                        
                        print(f"📊 Tools Summary:")
                        print(f"   Total tools: {metadata.get('total_tools', 0)}")
                        print(f"   Available tools: {metadata.get('available_tools', 0)}")
                        print(f"   Execution mode: {metadata.get('execution_mode', 'unknown')}")
                        print(f"   AWS mode: {metadata.get('aws_mode', False)}")
                        print(f"   Real mode: {not metadata.get('simulation_mode', True)}")
                        
                        # Check each expected tool
                        found_tools = {tool["id"]: tool for tool in available_tools}
                        
                        for category, expected_tools in EXPECTED_TOOLS.items():
                            print(f"\n📂 {category}:")
                            for tool_name in expected_tools:
                                if tool_name in found_tools:
                                    tool_info = found_tools[tool_name]
                                    status = tool_info.get("status", "unknown")
                                    platform = tool_info.get("platform", "unknown")
                                    
                                    if status == "available":
                                        print(f"   ✅ {tool_name} - Available on {platform}")
                                        self.results["summary"]["tools_available"] += 1
                                    else:
                                        print(f"   ⚠️  {tool_name} - Found but {status}")
                                    
                                    self.results["summary"]["tools_found"] += 1
                                    self.results["tools_availability"][tool_name] = {
                                        "found": True,
                                        "status": status,
                                        "platform": platform,
                                        "details": tool_info
                                    }
                                else:
                                    print(f"   ❌ {tool_name} - Missing")
                                    self.results["summary"]["missing_tools"].append(tool_name)
                                    self.results["tools_availability"][tool_name] = {
                                        "found": False,
                                        "status": "missing"
                                    }
                        
                        # Check for unexpected tools
                        all_expected = []
                        for tools in EXPECTED_TOOLS.values():
                            all_expected.extend(tools)
                        
                        unexpected_tools = [tool["id"] for tool in available_tools if tool["id"] not in all_expected]
                        if unexpected_tools:
                            print(f"\n🆕 Unexpected tools found: {unexpected_tools}")
                    
                    else:
                        print(f"❌ Tools endpoint returned success=false")
                        print(f"   Error: {tools_data.get('error', 'Unknown error')}")
                
                else:
                    print(f"❌ Tools endpoint failed: {response.status}")
                    error_text = await response.text()
                    print(f"   Error: {error_text[:200]}...")
        
        except Exception as e:
            print(f"❌ Tools availability test failed: {e}")
    
    async def _test_real_tool_execution(self, session: aiohttp.ClientSession):
        """Test actual tool execution with REAL tools"""
        print("\n⚡ Testing REAL Tool Execution...")
        print("-" * 60)
        
        # Test key tools that should work on AWS
        test_tools = [
            ("nuclei", "Vulnerability Scanner", "http://testphp.vulnweb.com"),
            ("sqlmap", "SQL Injection Tester", "http://testphp.vulnweb.com/artists.php?artist=1"),
            ("gobuster", "Directory Brute Forcer", "http://testphp.vulnweb.com"),
            ("vulnerability_agent", "AI Vulnerability Agent", "example.com")
        ]
        
        for tool_name, category, test_target in test_tools:
            print(f"\n🔍 Testing {tool_name} ({category})...")
            
            try:
                # Prepare scan request
                scan_data = {
                    "target": test_target,
                    "options": {
                        "scan_type": "quick",
                        "timeout": 30
                    }
                }
                
                # Execute tool scan
                async with session.post(
                    f"{self.base_url}/api/tools/{tool_name}/scan",
                    json=scan_data,
                    headers={"Content-Type": "application/json"},
                    timeout=aiohttp.ClientTimeout(total=120)  # Longer timeout for real scans
                ) as response:
                    
                    if response.status == 200:
                        result_data = await response.json()
                        
                        if result_data.get("success", False):
                            scan_result = result_data.get("data", {})
                            print(f"   ✅ {tool_name} execution successful")
                            print(f"      Status: {scan_result.get('status', 'unknown')}")
                            print(f"      Duration: {scan_result.get('duration_seconds', 0):.2f}s")
                            print(f"      Scan type: {scan_result.get('scan_type', 'unknown')}")
                            
                            # Check for results
                            if scan_result.get("results"):
                                results_size = len(str(scan_result["results"]))
                                print(f"      Results: Present ({results_size} chars)")
                            
                            if scan_result.get("vulnerabilities"):
                                vuln_count = len(scan_result["vulnerabilities"])
                                print(f"      Vulnerabilities: {vuln_count} found")
                            
                            # Detect if this is real or simulation
                            execution_mode = scan_result.get("execution_mode", "unknown")
                            if execution_mode == "real" or "real" in str(scan_result).lower():
                                print(f"      ✅ REAL TOOL EXECUTION CONFIRMED")
                            else:
                                print(f"      ⚠️  Execution mode: {execution_mode}")
                            
                            self.results["summary"]["tools_working"] += 1
                            self.results["tool_execution_tests"][tool_name] = {
                                "status": "success",
                                "execution_time": scan_result.get("duration_seconds", 0),
                                "has_results": bool(scan_result.get("results")),
                                "execution_mode": execution_mode,
                                "real_execution": execution_mode == "real"
                            }
                        else:
                            print(f"   ❌ {tool_name} execution failed")
                            print(f"      Error: {result_data.get('error', 'Unknown error')}")
                            self.results["summary"]["broken_tools"].append(tool_name)
                            self.results["tool_execution_tests"][tool_name] = {
                                "status": "failed",
                                "error": result_data.get("error", "Unknown error")
                            }
                    
                    else:
                        print(f"   ❌ {tool_name} HTTP error: {response.status}")
                        error_text = await response.text()
                        print(f"      Error: {error_text[:100]}...")
                        self.results["summary"]["broken_tools"].append(tool_name)
                        self.results["tool_execution_tests"][tool_name] = {
                            "status": "http_error",
                            "status_code": response.status,
                            "error": error_text[:200]
                        }
            
            except Exception as e:
                print(f"   ❌ {tool_name} execution exception: {e}")
                self.results["summary"]["broken_tools"].append(tool_name)
                self.results["tool_execution_tests"][tool_name] = {
                    "status": "exception", 
                    "error": str(e)
                }
    
    async def _test_api_endpoints(self, session: aiohttp.ClientSession):
        """Test various API endpoints"""
        print("\n🌐 Testing API Endpoints...")
        print("-" * 60)
        
        endpoints_to_test = [
            ("GET", "/api/tools/health", "Tools health check"),
            ("GET", "/api/orchestrator/capabilities", "Multi-Stage Orchestrator"),
            ("GET", "/api/ai/capabilities/status", "AI capabilities status"),
            ("GET", "/api/proxy/configurations", "AI Proxy configurations"),
            ("GET", "/api/ai/creative-exploits/capabilities", "Creative Exploit Engine")
        ]
        
        for method, endpoint, description in endpoints_to_test:
            try:
                if method == "GET":
                    async with session.get(f"{self.base_url}{endpoint}") as response:
                        if response.status == 200:
                            print(f"   ✅ {description}: {response.status}")
                        else:
                            print(f"   ❌ {description}: {response.status}")
                
            except Exception as e:
                print(f"   ❌ {description}: Exception - {e}")
    
    def _generate_final_report(self):
        """Generate comprehensive final report"""
        print("\n" + "=" * 80)
        print("📊 AWS EC2 DEPLOYMENT TEST RESULTS")
        print("=" * 80)
        
        # Calculate success rate
        total_expected = self.results["summary"]["total_expected_tools"]
        tools_available = self.results["summary"]["tools_available"]
        tools_working = self.results["summary"]["tools_working"]
        
        if total_expected > 0:
            availability_rate = (tools_available / total_expected) * 100
            working_rate = (tools_working / 4) * 100  # Out of 4 tested tools
            self.results["summary"]["success_rate"] = availability_rate
        
        # Print summary
        print(f"🎯 Test Summary:")
        print(f"   Backend Status: {self.results['backend_status']}")
        print(f"   Total Expected Tools: {total_expected}")
        print(f"   Tools Found: {self.results['summary']['tools_found']}")
        print(f"   Tools Available: {tools_available}")
        print(f"   Tools Tested: 4")
        print(f"   Tools Working: {tools_working}")
        print(f"   Availability Rate: {availability_rate:.1f}%")
        print(f"   Execution Success Rate: {working_rate:.1f}%")
        
        # Missing tools
        missing_tools = self.results["summary"]["missing_tools"]
        if missing_tools:
            print(f"\n❌ Missing Tools ({len(missing_tools)}):")
            for tool in missing_tools[:10]:  # Show first 10
                print(f"   - {tool}")
            if len(missing_tools) > 10:
                print(f"   ... and {len(missing_tools) - 10} more")
        
        # Broken tools
        broken_tools = self.results["summary"]["broken_tools"]
        if broken_tools:
            print(f"\n⚠️  Broken Tools ({len(broken_tools)}):")
            for tool in broken_tools:
                print(f"   - {tool}")
        
        # Working tools
        working_tools = [name for name, result in self.results["tool_execution_tests"].items() 
                        if result.get("status") == "success"]
        if working_tools:
            print(f"\n✅ Working Tools ({len(working_tools)}):")
            for tool in working_tools:
                result = self.results["tool_execution_tests"][tool]
                execution_mode = result.get("execution_mode", "unknown")
                real_mode = "REAL" if result.get("real_execution") else "SIM"
                print(f"   - {tool} ({real_mode}) - {result.get('execution_time', 0):.1f}s")
        
        # Overall status
        print(f"\n🏆 Overall Status:")
        if availability_rate >= 80 and working_rate >= 75:
            print("   🎉 EXCELLENT - Production-ready SaaS platform!")
            print("   ✅ Real security tools working on AWS")
            print("   ✅ Ready for customer deployment")
        elif availability_rate >= 60 and working_rate >= 50:
            print("   ⚠️  GOOD - Most tools working, minor fixes needed")
        elif availability_rate >= 40:
            print("   ❌ NEEDS WORK - Many tools missing or broken")
        else:
            print("   🚨 CRITICAL - Major issues, backend needs fixing")
        
        # Recommendations
        print(f"\n📝 Recommendations:")
        if availability_rate >= 80 and working_rate >= 75:
            print("   • ✅ Platform ready for SaaS customers")
            print("   • ✅ Real security tools confirmed working")
            print("   • ✅ AWS deployment successful")
            print("   • 🚀 Ready to build frontend and scale")
        else:
            print("   • Fix missing tools (install missing packages)")
            print("   • Verify tool execution paths")
            print("   • Check AWS security group settings")
            print("   • Add missing Python modules")
        
        # Save detailed results
        with open("aws_deployment_test_results.json", "w") as f:
            json.dump(self.results, f, indent=2)
        
        print(f"\n💾 Detailed results saved to: aws_deployment_test_results.json")
        
        # Comparison with Railway
        print(f"\n🆚 AWS vs Railway Comparison:")
        print(f"   Railway (Simulation): 0% tools working")
        print(f"   AWS EC2 (Real): {working_rate:.1f}% tools working")
        print(f"   🎯 AWS provides REAL security testing capability!")


async def main():
    """Main test function"""
    print("🚀 AWS EC2 Deployment Comprehensive Test")
    print(f"Testing real security tools deployment")
    print(f"Target: {AWS_URL}")
    
    tester = AWSDeploymentTester()
    await tester.run_comprehensive_test()


if __name__ == "__main__":
    asyncio.run(main())