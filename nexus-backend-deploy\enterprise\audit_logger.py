"""
Comprehensive Audit Logging System for NexusScan Desktop
Enterprise-grade audit logging with compliance reporting and security monitoring.
"""

import asyncio
import logging
import json
import time
import uuid
import hashlib
import gzip
from typing import Dict, List, Optional, Any, Set, Tuple, Union
from enum import Enum
from datetime import datetime, timed<PERSON><PERSON>
from dataclasses import dataclass, asdict
from pathlib import Path
import sqlite3
from contextlib import asynccontextmanager

from core.config import Config
from core.database import DatabaseManager
from core.events import EventManager, EventTypes
from ai.ai_service import AIServiceManager

logger = logging.getLogger(__name__)


class AuditEventType(Enum):
    """Types of audit events"""
    # Authentication Events
    LOGIN_SUCCESS = "login_success"
    LOGIN_FAILURE = "login_failure"
    LOGOUT = "logout"
    PASSWORD_CHANGE = "password_change"
    ACCOUNT_LOCKED = "account_locked"
    SESSION_EXPIRED = "session_expired"
    
    # Authorization Events
    ACCESS_GRANTED = "access_granted"
    ACCESS_DENIED = "access_denied"
    PERMISSION_CHANGED = "permission_changed"
    ROLE_ASSIGNED = "role_assigned"
    PRIVILEGE_ESCALATION = "privilege_escalation"
    
    # Data Events
    DATA_CREATED = "data_created"
    DATA_READ = "data_read"
    DATA_UPDATED = "data_updated"
    DATA_DELETED = "data_deleted"
    DATA_EXPORTED = "data_exported"
    DATA_IMPORTED = "data_imported"
    
    # System Events
    SYSTEM_STARTUP = "system_startup"
    SYSTEM_SHUTDOWN = "system_shutdown"
    CONFIG_CHANGED = "config_changed"
    BACKUP_CREATED = "backup_created"
    UPDATE_INSTALLED = "update_installed"
    
    # Security Events
    SCAN_STARTED = "scan_started"
    SCAN_COMPLETED = "scan_completed"
    VULNERABILITY_FOUND = "vulnerability_found"
    EXPLOIT_EXECUTED = "exploit_executed"
    COMPLIANCE_CHECK = "compliance_check"
    POLICY_VIOLATION = "policy_violation"
    
    # API Events
    API_CALL = "api_call"
    API_KEY_CREATED = "api_key_created"
    API_KEY_REVOKED = "api_key_revoked"
    RATE_LIMIT_EXCEEDED = "rate_limit_exceeded"
    
    # Administrative Events
    USER_CREATED = "user_created"
    USER_DELETED = "user_deleted"
    USER_MODIFIED = "user_modified"
    CAMPAIGN_CREATED = "campaign_created"
    REPORT_GENERATED = "report_generated"


class RiskLevel(Enum):
    """Risk level for audit events"""
    CRITICAL = "critical"
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"
    INFORMATIONAL = "informational"


class ComplianceCoverage(Enum):
    """Compliance frameworks coverage"""
    SOX = "sox"
    HIPAA = "hipaa"
    PCI_DSS = "pci_dss"
    GDPR = "gdpr"
    ISO_27001 = "iso_27001"
    NIST = "nist"
    SOC2 = "soc2"


@dataclass
class AuditEvent:
    """Individual audit log entry"""
    event_id: str
    timestamp: datetime
    event_type: AuditEventType
    actor_id: str  # User who performed the action
    actor_type: str  # user, system, api_key
    target_id: Optional[str]  # Resource being acted upon
    target_type: Optional[str]  # Type of target resource
    action: str  # Specific action performed
    outcome: str  # success, failure, partial
    risk_level: RiskLevel
    session_id: Optional[str]
    ip_address: str
    user_agent: str
    location: Optional[str]  # Geographic location
    details: Dict[str, Any]  # Event-specific details
    compliance_tags: List[ComplianceCoverage]
    data_classification: str  # public, internal, confidential, restricted
    retention_period: int  # Days to retain this log
    integrity_hash: str  # For tamper detection


@dataclass
class AuditQuery:
    """Audit log query parameters"""
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    event_types: Optional[List[AuditEventType]] = None
    actor_id: Optional[str] = None
    target_id: Optional[str] = None
    risk_levels: Optional[List[RiskLevel]] = None
    compliance_frameworks: Optional[List[ComplianceCoverage]] = None
    outcome_filter: Optional[str] = None
    limit: int = 1000
    offset: int = 0


@dataclass
class AuditMetrics:
    """Audit logging system metrics"""
    total_events: int
    events_by_type: Dict[str, int]
    events_by_risk_level: Dict[str, int]
    failed_events: int
    security_incidents: int
    compliance_violations: int
    unique_users: int
    data_access_events: int
    system_events: int
    retention_policy_violations: int


class AuditLogger:
    """Comprehensive enterprise audit logging system"""
    
    def __init__(self, config: Config, db_manager: DatabaseManager,
                 event_manager: EventManager, ai_service: AIServiceManager):
        """Initialize audit logging system"""
        self.config = config
        self.db_manager = db_manager
        self.event_manager = event_manager
        self.ai_service = ai_service
        
        # Audit storage
        self.audit_db_path = Path(config.data_dir) / "audit_logs.db"
        self.archive_path = Path(config.data_dir) / "audit_archives"
        self.archive_path.mkdir(exist_ok=True)
        
        # Configuration
        self.max_events_per_file = 10000
        self.default_retention_days = 2555  # 7 years
        self.high_risk_retention_days = 3650  # 10 years
        self.archive_after_days = 90
        self.compression_enabled = True
        
        # Runtime tracking
        self.failed_log_attempts = 0
        self.total_events_logged = 0
        self.last_archival = datetime.now()
        
        # Compliance mappings
        self.compliance_event_mapping = self._initialize_compliance_mapping()
        
        # Initialize database
        self._initialize_audit_database()
        
        # Start background tasks
        asyncio.create_task(self._periodic_archival())
        asyncio.create_task(self._periodic_integrity_check())
        
        logger.info("Comprehensive audit logging system initialized")
    
    def _initialize_compliance_mapping(self) -> Dict[AuditEventType, List[ComplianceCoverage]]:
        """Map audit events to compliance frameworks"""
        return {
            # Authentication events are critical for most frameworks
            AuditEventType.LOGIN_SUCCESS: [ComplianceCoverage.SOX, ComplianceCoverage.HIPAA, ComplianceCoverage.PCI_DSS, ComplianceCoverage.SOC2],
            AuditEventType.LOGIN_FAILURE: [ComplianceCoverage.SOX, ComplianceCoverage.HIPAA, ComplianceCoverage.PCI_DSS, ComplianceCoverage.SOC2],
            AuditEventType.ACCOUNT_LOCKED: [ComplianceCoverage.SOX, ComplianceCoverage.HIPAA, ComplianceCoverage.PCI_DSS, ComplianceCoverage.SOC2],
            
            # Data access events
            AuditEventType.DATA_READ: [ComplianceCoverage.GDPR, ComplianceCoverage.HIPAA, ComplianceCoverage.SOX],
            AuditEventType.DATA_UPDATED: [ComplianceCoverage.GDPR, ComplianceCoverage.HIPAA, ComplianceCoverage.SOX, ComplianceCoverage.SOC2],
            AuditEventType.DATA_DELETED: [ComplianceCoverage.GDPR, ComplianceCoverage.HIPAA, ComplianceCoverage.SOX, ComplianceCoverage.SOC2],
            AuditEventType.DATA_EXPORTED: [ComplianceCoverage.GDPR, ComplianceCoverage.HIPAA, ComplianceCoverage.PCI_DSS],
            
            # System administration
            AuditEventType.PERMISSION_CHANGED: [ComplianceCoverage.SOX, ComplianceCoverage.SOC2, ComplianceCoverage.ISO_27001],
            AuditEventType.CONFIG_CHANGED: [ComplianceCoverage.SOX, ComplianceCoverage.SOC2, ComplianceCoverage.ISO_27001, ComplianceCoverage.NIST],
            AuditEventType.USER_CREATED: [ComplianceCoverage.SOX, ComplianceCoverage.SOC2, ComplianceCoverage.ISO_27001],
            
            # Security events
            AuditEventType.POLICY_VIOLATION: [ComplianceCoverage.SOX, ComplianceCoverage.ISO_27001, ComplianceCoverage.NIST],
            AuditEventType.VULNERABILITY_FOUND: [ComplianceCoverage.ISO_27001, ComplianceCoverage.NIST, ComplianceCoverage.PCI_DSS],
        }
    
    def _initialize_audit_database(self):
        """Initialize SQLite database for audit logs"""
        with sqlite3.connect(self.audit_db_path) as conn:
            conn.execute("""
                CREATE TABLE IF NOT EXISTS audit_events (
                    event_id TEXT PRIMARY KEY,
                    timestamp DATETIME NOT NULL,
                    event_type TEXT NOT NULL,
                    actor_id TEXT NOT NULL,
                    actor_type TEXT NOT NULL,
                    target_id TEXT,
                    target_type TEXT,
                    action TEXT NOT NULL,
                    outcome TEXT NOT NULL,
                    risk_level TEXT NOT NULL,
                    session_id TEXT,
                    ip_address TEXT NOT NULL,
                    user_agent TEXT,
                    location TEXT,
                    details TEXT,  -- JSON
                    compliance_tags TEXT,  -- JSON
                    data_classification TEXT NOT NULL,
                    retention_period INTEGER NOT NULL,
                    integrity_hash TEXT NOT NULL,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            conn.execute("""
                CREATE INDEX IF NOT EXISTS idx_timestamp 
                ON audit_events(timestamp)
            """)
            
            conn.execute("""
                CREATE INDEX IF NOT EXISTS idx_event_type 
                ON audit_events(event_type)
            """)
            
            conn.execute("""
                CREATE INDEX IF NOT EXISTS idx_actor_id 
                ON audit_events(actor_id)
            """)
            
            conn.execute("""
                CREATE INDEX IF NOT EXISTS idx_risk_level 
                ON audit_events(risk_level)
            """)
            
            conn.execute("""
                CREATE TABLE IF NOT EXISTS audit_integrity (
                    check_id TEXT PRIMARY KEY,
                    check_date DATETIME NOT NULL,
                    events_checked INTEGER NOT NULL,
                    integrity_violations INTEGER NOT NULL,
                    last_event_timestamp DATETIME,
                    checksum TEXT NOT NULL
                )
            """)
    
    async def log_event(self, event_type: AuditEventType, actor_id: str,
                       action: str, outcome: str = "success",
                       target_id: Optional[str] = None, target_type: Optional[str] = None,
                       details: Optional[Dict[str, Any]] = None,
                       risk_level: Optional[RiskLevel] = None,
                       session_id: Optional[str] = None,
                       ip_address: str = "127.0.0.1",
                       user_agent: str = "NexusScan Desktop",
                       data_classification: str = "internal") -> str:
        """Log an audit event"""
        try:
            event_id = f"audit_{uuid.uuid4().hex}"
            timestamp = datetime.now()
            
            # Determine risk level if not provided
            if risk_level is None:
                risk_level = self._determine_risk_level(event_type, outcome)
            
            # Get compliance tags
            compliance_tags = self.compliance_event_mapping.get(event_type, [])
            
            # Determine retention period
            retention_period = self._calculate_retention_period(risk_level, compliance_tags)
            
            # Create audit event
            event = AuditEvent(
                event_id=event_id,
                timestamp=timestamp,
                event_type=event_type,
                actor_id=actor_id,
                actor_type=self._determine_actor_type(actor_id),
                target_id=target_id,
                target_type=target_type,
                action=action,
                outcome=outcome,
                risk_level=risk_level,
                session_id=session_id,
                ip_address=ip_address,
                user_agent=user_agent,
                location=await self._get_location(ip_address),
                details=details or {},
                compliance_tags=compliance_tags,
                data_classification=data_classification,
                retention_period=retention_period,
                integrity_hash=""  # Will be calculated
            )
            
            # Calculate integrity hash
            event.integrity_hash = self._calculate_integrity_hash(event)
            
            # Store event
            await self._store_event(event)
            
            # Check for security incidents
            await self._analyze_security_pattern(event)
            
            # Emit real-time event for monitoring
            await self.event_manager.emit(
                EventTypes.AUDIT_EVENT_LOGGED,
                {
                    "event_id": event_id,
                    "event_type": event_type.value,
                    "risk_level": risk_level.value,
                    "actor_id": actor_id
                },
                "audit_logger"
            )
            
            self.total_events_logged += 1
            return event_id
            
        except Exception as e:
            self.failed_log_attempts += 1
            logger.error(f"Failed to log audit event: {e}")
            
            # Critical: If audit logging fails, we need to handle this carefully
            await self._handle_logging_failure(event_type, actor_id, str(e))
            raise
    
    def _determine_risk_level(self, event_type: AuditEventType, outcome: str) -> RiskLevel:
        """Determine risk level based on event type and outcome"""
        # Failed events are generally higher risk
        if outcome == "failure":
            if event_type in [AuditEventType.LOGIN_FAILURE, AuditEventType.ACCESS_DENIED]:
                return RiskLevel.MEDIUM
            elif event_type in [AuditEventType.PRIVILEGE_ESCALATION, AuditEventType.POLICY_VIOLATION]:
                return RiskLevel.HIGH
            else:
                return RiskLevel.MEDIUM
        
        # Event-specific risk levels
        high_risk_events = [
            AuditEventType.PRIVILEGE_ESCALATION,
            AuditEventType.DATA_DELETED,
            AuditEventType.EXPLOIT_EXECUTED,
            AuditEventType.POLICY_VIOLATION,
            AuditEventType.ACCOUNT_LOCKED
        ]
        
        medium_risk_events = [
            AuditEventType.DATA_EXPORTED,
            AuditEventType.PERMISSION_CHANGED,
            AuditEventType.CONFIG_CHANGED,
            AuditEventType.USER_CREATED,
            AuditEventType.API_KEY_CREATED
        ]
        
        if event_type in high_risk_events:
            return RiskLevel.HIGH
        elif event_type in medium_risk_events:
            return RiskLevel.MEDIUM
        else:
            return RiskLevel.LOW
    
    def _determine_actor_type(self, actor_id: str) -> str:
        """Determine the type of actor performing the action"""
        if actor_id.startswith("api_"):
            return "api_key"
        elif actor_id == "system":
            return "system"
        else:
            return "user"
    
    def _calculate_retention_period(self, risk_level: RiskLevel, 
                                  compliance_tags: List[ComplianceCoverage]) -> int:
        """Calculate retention period based on risk and compliance requirements"""
        # Compliance-driven retention
        if ComplianceCoverage.SOX in compliance_tags:
            return 2555  # 7 years for SOX
        elif ComplianceCoverage.HIPAA in compliance_tags:
            return 2190  # 6 years for HIPAA
        elif ComplianceCoverage.PCI_DSS in compliance_tags:
            return 365   # 1 year for PCI-DSS
        
        # Risk-based retention
        if risk_level in [RiskLevel.CRITICAL, RiskLevel.HIGH]:
            return self.high_risk_retention_days
        else:
            return self.default_retention_days
    
    async def _get_location(self, ip_address: str) -> Optional[str]:
        """Get geographic location from IP address (mock implementation)"""
        # In production, this would use a GeoIP service
        if ip_address.startswith("192.168.") or ip_address == "127.0.0.1":
            return "Local Network"
        return "Unknown Location"
    
    def _calculate_integrity_hash(self, event: AuditEvent) -> str:
        """Calculate SHA-256 hash for tamper detection"""
        # Create a canonical representation for hashing
        hash_data = {
            "event_id": event.event_id,
            "timestamp": event.timestamp.isoformat(),
            "event_type": event.event_type.value,
            "actor_id": event.actor_id,
            "action": event.action,
            "outcome": event.outcome,
            "details": json.dumps(event.details, sort_keys=True)
        }
        
        hash_string = json.dumps(hash_data, sort_keys=True)
        return hashlib.sha256(hash_string.encode()).hexdigest()
    
    async def _store_event(self, event: AuditEvent):
        """Store audit event in database"""
        with sqlite3.connect(self.audit_db_path) as conn:
            conn.execute("""
                INSERT INTO audit_events VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                event.event_id,
                event.timestamp,
                event.event_type.value,
                event.actor_id,
                event.actor_type,
                event.target_id,
                event.target_type,
                event.action,
                event.outcome,
                event.risk_level.value,
                event.session_id,
                event.ip_address,
                event.user_agent,
                event.location,
                json.dumps(event.details),
                json.dumps([tag.value for tag in event.compliance_tags]),
                event.data_classification,
                event.retention_period,
                event.integrity_hash,
                datetime.now()
            ))
    
    async def _analyze_security_pattern(self, event: AuditEvent):
        """Analyze event for security patterns and anomalies"""
        try:
            # Check for failed login patterns
            if event.event_type == AuditEventType.LOGIN_FAILURE:
                await self._check_brute_force_pattern(event.actor_id, event.ip_address)
            
            # Check for privilege escalation
            if event.event_type == AuditEventType.PRIVILEGE_ESCALATION:
                await self._alert_privilege_escalation(event)
            
            # Check for unusual data access patterns
            if event.event_type in [AuditEventType.DATA_READ, AuditEventType.DATA_EXPORTED]:
                await self._check_data_access_pattern(event.actor_id)
            
        except Exception as e:
            logger.error(f"Security pattern analysis failed: {e}")
    
    async def _check_brute_force_pattern(self, actor_id: str, ip_address: str):
        """Check for brute force attack patterns"""
        # Look for multiple failed logins in the last 10 minutes
        ten_minutes_ago = datetime.now() - timedelta(minutes=10)
        
        with sqlite3.connect(self.audit_db_path) as conn:
            cursor = conn.execute("""
                SELECT COUNT(*) FROM audit_events 
                WHERE event_type = ? AND actor_id = ? AND ip_address = ? 
                AND timestamp > ? AND outcome = 'failure'
            """, (AuditEventType.LOGIN_FAILURE.value, actor_id, ip_address, ten_minutes_ago))
            
            failed_attempts = cursor.fetchone()[0]
            
            if failed_attempts >= 5:
                # Potential brute force attack
                await self.log_event(
                    AuditEventType.POLICY_VIOLATION,
                    "system",
                    "brute_force_detection",
                    "alert_generated",
                    details={
                        "violation_type": "brute_force_attempt",
                        "target_user": actor_id,
                        "source_ip": ip_address,
                        "failed_attempts": failed_attempts
                    },
                    risk_level=RiskLevel.HIGH
                )
    
    async def _alert_privilege_escalation(self, event: AuditEvent):
        """Alert on privilege escalation events"""
        await self.event_manager.emit(
            EventTypes.SECURITY_ALERT,
            {
                "alert_type": "privilege_escalation",
                "actor_id": event.actor_id,
                "target_id": event.target_id,
                "timestamp": event.timestamp.isoformat(),
                "risk_level": "high"
            },
            "audit_logger"
        )
    
    async def _check_data_access_pattern(self, actor_id: str):
        """Check for unusual data access patterns"""
        # Look for excessive data access in the last hour
        one_hour_ago = datetime.now() - timedelta(hours=1)
        
        with sqlite3.connect(self.audit_db_path) as conn:
            cursor = conn.execute("""
                SELECT COUNT(*) FROM audit_events 
                WHERE event_type IN (?, ?) AND actor_id = ? AND timestamp > ?
            """, (AuditEventType.DATA_READ.value, AuditEventType.DATA_EXPORTED.value, actor_id, one_hour_ago))
            
            access_count = cursor.fetchone()[0]
            
            if access_count >= 100:  # Threshold for unusual access
                await self.log_event(
                    AuditEventType.POLICY_VIOLATION,
                    "system",
                    "excessive_data_access_detection",
                    "alert_generated",
                    details={
                        "violation_type": "excessive_data_access",
                        "actor_id": actor_id,
                        "access_count": access_count,
                        "time_window": "1_hour"
                    },
                    risk_level=RiskLevel.MEDIUM
                )
    
    async def _handle_logging_failure(self, event_type: AuditEventType, actor_id: str, error: str):
        """Handle audit logging failures"""
        # Write to backup log file
        backup_log = Path(self.config.data_dir) / "audit_failures.log"
        with open(backup_log, "a") as f:
            f.write(f"{datetime.now().isoformat()} AUDIT_FAILURE {event_type.value} {actor_id} {error}\n")
        
        # Emit critical system event
        await self.event_manager.emit(
            EventTypes.SYSTEM_ERROR,
            {
                "error_type": "audit_logging_failure",
                "event_type": event_type.value,
                "actor_id": actor_id,
                "error": error
            },
            "audit_logger"
        )
    
    async def query_events(self, query: AuditQuery) -> List[Dict[str, Any]]:
        """Query audit events with filtering"""
        try:
            sql = "SELECT * FROM audit_events WHERE 1=1"
            params = []
            
            if query.start_date:
                sql += " AND timestamp >= ?"
                params.append(query.start_date)
            
            if query.end_date:
                sql += " AND timestamp <= ?"
                params.append(query.end_date)
            
            if query.event_types:
                placeholders = ",".join("?" * len(query.event_types))
                sql += f" AND event_type IN ({placeholders})"
                params.extend([et.value for et in query.event_types])
            
            if query.actor_id:
                sql += " AND actor_id = ?"
                params.append(query.actor_id)
            
            if query.target_id:
                sql += " AND target_id = ?"
                params.append(query.target_id)
            
            if query.risk_levels:
                placeholders = ",".join("?" * len(query.risk_levels))
                sql += f" AND risk_level IN ({placeholders})"
                params.extend([rl.value for rl in query.risk_levels])
            
            if query.outcome_filter:
                sql += " AND outcome = ?"
                params.append(query.outcome_filter)
            
            sql += " ORDER BY timestamp DESC LIMIT ? OFFSET ?"
            params.extend([query.limit, query.offset])
            
            with sqlite3.connect(self.audit_db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.execute(sql, params)
                rows = cursor.fetchall()
                
                events = []
                for row in rows:
                    event_dict = dict(row)
                    event_dict["details"] = json.loads(event_dict["details"] or "{}")
                    event_dict["compliance_tags"] = json.loads(event_dict["compliance_tags"] or "[]")
                    events.append(event_dict)
                
                return events
        
        except Exception as e:
            logger.error(f"Failed to query audit events: {e}")
            return []
    
    async def generate_compliance_report(self, framework: ComplianceCoverage,
                                       start_date: datetime, end_date: datetime) -> Dict[str, Any]:
        """Generate compliance-specific audit report"""
        try:
            # Query events relevant to the compliance framework
            query = AuditQuery(
                start_date=start_date,
                end_date=end_date,
                compliance_frameworks=[framework]
            )
            
            events = await self.query_events(query)
            
            # Analyze compliance coverage
            total_events = len(events)
            failed_events = len([e for e in events if e["outcome"] == "failure"])
            high_risk_events = len([e for e in events if e["risk_level"] in ["high", "critical"]])
            
            # Event type breakdown
            event_types = {}
            for event in events:
                event_type = event["event_type"]
                event_types[event_type] = event_types.get(event_type, 0) + 1
            
            # Generate AI-powered compliance insights
            compliance_insights = await self._generate_compliance_insights(framework, events)
            
            return {
                "framework": framework.value,
                "period": {
                    "start_date": start_date.isoformat(),
                    "end_date": end_date.isoformat()
                },
                "summary": {
                    "total_events": total_events,
                    "failed_events": failed_events,
                    "failure_rate": (failed_events / total_events * 100) if total_events > 0 else 0,
                    "high_risk_events": high_risk_events,
                    "risk_percentage": (high_risk_events / total_events * 100) if total_events > 0 else 0
                },
                "event_breakdown": event_types,
                "compliance_insights": compliance_insights,
                "recommendations": await self._generate_compliance_recommendations(framework, events)
            }
        
        except Exception as e:
            logger.error(f"Failed to generate compliance report: {e}")
            return {"error": str(e)}
    
    async def _generate_compliance_insights(self, framework: ComplianceCoverage, 
                                          events: List[Dict[str, Any]]) -> str:
        """Generate AI insights for compliance reporting"""
        try:
            event_summary = {
                "total_events": len(events),
                "failed_events": len([e for e in events if e["outcome"] == "failure"]),
                "event_types": list(set(e["event_type"] for e in events)),
                "unique_users": len(set(e["actor_id"] for e in events))
            }
            
            prompt = f"""
Analyze audit log data for {framework.value.upper()} compliance:

Event Summary:
- Total Events: {event_summary['total_events']}
- Failed Events: {event_summary['failed_events']}
- Event Types: {', '.join(event_summary['event_types'][:10])}
- Unique Users: {event_summary['unique_users']}

Provide insights on:
1. Compliance posture assessment
2. Key risk areas identified
3. Control effectiveness evaluation
4. Audit trail completeness

Keep analysis concise and compliance-focused.
"""
            
            response = await self.ai_service.analyze(
                prompt,
                context="compliance_analysis",
                provider_preference=["openai", "claude"]
            )
            
            return response.get("analysis", "No compliance insights available")
        
        except Exception as e:
            logger.error(f"Failed to generate compliance insights: {e}")
            return "Compliance insights unavailable"
    
    async def _generate_compliance_recommendations(self, framework: ComplianceCoverage,
                                                 events: List[Dict[str, Any]]) -> List[str]:
        """Generate compliance improvement recommendations"""
        recommendations = []
        
        failed_events = [e for e in events if e["outcome"] == "failure"]
        if len(failed_events) > len(events) * 0.1:  # More than 10% failure rate
            recommendations.append("High failure rate detected - review access controls and user training")
        
        login_failures = [e for e in events if e["event_type"] == "login_failure"]
        if len(login_failures) > 50:
            recommendations.append("Excessive login failures - implement account lockout policies")
        
        privilege_escalations = [e for e in events if e["event_type"] == "privilege_escalation"]
        if privilege_escalations:
            recommendations.append("Privilege escalation events detected - review role assignments")
        
        data_exports = [e for e in events if e["event_type"] == "data_exported"]
        if len(data_exports) > 20:
            recommendations.append("High data export activity - implement data loss prevention controls")
        
        if not recommendations:
            recommendations.append("Audit logs show good compliance posture - maintain current controls")
        
        return recommendations
    
    async def get_audit_metrics(self) -> AuditMetrics:
        """Get comprehensive audit logging metrics"""
        try:
            with sqlite3.connect(self.audit_db_path) as conn:
                # Total events
                cursor = conn.execute("SELECT COUNT(*) FROM audit_events")
                total_events = cursor.fetchone()[0]
                
                # Events by type
                cursor = conn.execute("""
                    SELECT event_type, COUNT(*) FROM audit_events 
                    GROUP BY event_type ORDER BY COUNT(*) DESC
                """)
                events_by_type = dict(cursor.fetchall())
                
                # Events by risk level
                cursor = conn.execute("""
                    SELECT risk_level, COUNT(*) FROM audit_events 
                    GROUP BY risk_level ORDER BY COUNT(*) DESC
                """)
                events_by_risk_level = dict(cursor.fetchall())
                
                # Failed events
                cursor = conn.execute("SELECT COUNT(*) FROM audit_events WHERE outcome = 'failure'")
                failed_events = cursor.fetchone()[0]
                
                # Security incidents (high/critical risk failures)
                cursor = conn.execute("""
                    SELECT COUNT(*) FROM audit_events 
                    WHERE risk_level IN ('high', 'critical') AND outcome = 'failure'
                """)
                security_incidents = cursor.fetchone()[0]
                
                # Compliance violations
                cursor = conn.execute("SELECT COUNT(*) FROM audit_events WHERE event_type = 'policy_violation'")
                compliance_violations = cursor.fetchone()[0]
                
                # Unique users
                cursor = conn.execute("SELECT COUNT(DISTINCT actor_id) FROM audit_events WHERE actor_type = 'user'")
                unique_users = cursor.fetchone()[0]
                
                # Data access events
                cursor = conn.execute("""
                    SELECT COUNT(*) FROM audit_events 
                    WHERE event_type IN ('data_read', 'data_updated', 'data_deleted', 'data_exported')
                """)
                data_access_events = cursor.fetchone()[0]
                
                # System events
                cursor = conn.execute("SELECT COUNT(*) FROM audit_events WHERE actor_type = 'system'")
                system_events = cursor.fetchone()[0]
                
                return AuditMetrics(
                    total_events=total_events,
                    events_by_type=events_by_type,
                    events_by_risk_level=events_by_risk_level,
                    failed_events=failed_events,
                    security_incidents=security_incidents,
                    compliance_violations=compliance_violations,
                    unique_users=unique_users,
                    data_access_events=data_access_events,
                    system_events=system_events,
                    retention_policy_violations=0  # Would be calculated based on retention policies
                )
        
        except Exception as e:
            logger.error(f"Failed to get audit metrics: {e}")
            return AuditMetrics(0, {}, {}, 0, 0, 0, 0, 0, 0, 0)
    
    async def _periodic_archival(self):
        """Periodically archive old audit logs"""
        while True:
            try:
                await asyncio.sleep(86400)  # Check daily
                
                # Archive logs older than configured threshold
                archive_date = datetime.now() - timedelta(days=self.archive_after_days)
                
                with sqlite3.connect(self.audit_db_path) as conn:
                    # Get events to archive
                    conn.row_factory = sqlite3.Row
                    cursor = conn.execute("""
                        SELECT * FROM audit_events WHERE timestamp < ?
                    """, (archive_date,))
                    
                    events_to_archive = cursor.fetchall()
                    
                    if events_to_archive:
                        # Create archive file
                        archive_file = self.archive_path / f"audit_archive_{datetime.now().strftime('%Y%m%d')}.json"
                        
                        # Convert to JSON and optionally compress
                        archive_data = [dict(row) for row in events_to_archive]
                        
                        if self.compression_enabled:
                            with gzip.open(f"{archive_file}.gz", "wt") as f:
                                json.dump(archive_data, f, default=str)
                        else:
                            with open(archive_file, "w") as f:
                                json.dump(archive_data, f, default=str)
                        
                        # Remove archived events from main database
                        conn.execute("DELETE FROM audit_events WHERE timestamp < ?", (archive_date,))
                        
                        logger.info(f"Archived {len(events_to_archive)} audit events")
                        self.last_archival = datetime.now()
            
            except Exception as e:
                logger.error(f"Audit archival failed: {e}")
    
    async def _periodic_integrity_check(self):
        """Periodically check audit log integrity"""
        while True:
            try:
                await asyncio.sleep(3600)  # Check hourly
                
                violations = 0
                events_checked = 0
                
                with sqlite3.connect(self.audit_db_path) as conn:
                    conn.row_factory = sqlite3.Row
                    cursor = conn.execute("""
                        SELECT * FROM audit_events 
                        WHERE timestamp > ? 
                        ORDER BY timestamp DESC LIMIT 1000
                    """, (datetime.now() - timedelta(hours=24),))
                    
                    for row in cursor.fetchall():
                        events_checked += 1
                        
                        # Reconstruct event and verify hash
                        event_data = dict(row)
                        stored_hash = event_data.pop("integrity_hash")
                        
                        # Create temporary event object for hash calculation
                        temp_event = AuditEvent(
                            event_id=event_data["event_id"],
                            timestamp=datetime.fromisoformat(event_data["timestamp"]),
                            event_type=AuditEventType(event_data["event_type"]),
                            actor_id=event_data["actor_id"],
                            actor_type=event_data["actor_type"],
                            target_id=event_data["target_id"],
                            target_type=event_data["target_type"],
                            action=event_data["action"],
                            outcome=event_data["outcome"],
                            risk_level=RiskLevel(event_data["risk_level"]),
                            session_id=event_data["session_id"],
                            ip_address=event_data["ip_address"],
                            user_agent=event_data["user_agent"],
                            location=event_data["location"],
                            details=json.loads(event_data["details"] or "{}"),
                            compliance_tags=[ComplianceCoverage(tag) for tag in json.loads(event_data["compliance_tags"] or "[]")],
                            data_classification=event_data["data_classification"],
                            retention_period=event_data["retention_period"],
                            integrity_hash=""
                        )
                        
                        calculated_hash = self._calculate_integrity_hash(temp_event)
                        
                        if calculated_hash != stored_hash:
                            violations += 1
                            logger.warning(f"Integrity violation detected for event {event_data['event_id']}")
                
                # Store integrity check results
                check_id = f"integrity_{uuid.uuid4().hex[:8]}"
                with sqlite3.connect(self.audit_db_path) as conn:
                    conn.execute("""
                        INSERT INTO audit_integrity VALUES (?, ?, ?, ?, ?, ?)
                    """, (
                        check_id,
                        datetime.now(),
                        events_checked,
                        violations,
                        datetime.now(),
                        "checksum_placeholder"  # Would be overall checksum
                    ))
                
                if violations > 0:
                    await self.event_manager.emit(
                        EventTypes.SECURITY_ALERT,
                        {
                            "alert_type": "audit_integrity_violation",
                            "violations": violations,
                            "events_checked": events_checked
                        },
                        "audit_logger"
                    )
            
            except Exception as e:
                logger.error(f"Integrity check failed: {e}")