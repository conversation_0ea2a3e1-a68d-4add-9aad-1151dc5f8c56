#!/usr/bin/env python3
"""
🔧 Individual Tools E2E Testing - Deep Analysis
Test each tool separately with backend-frontend integration and CTA button functionality
"""

import requests
import json
import time
from datetime import datetime
from typing import Dict, List, Any

class ToolE2ETester:
    def __init__(self):
        self.backend_url = "http://localhost:8000"
        self.frontend_url = "http://localhost:1420"
        self.test_results = {}
        
    def test_individual_tool(self, tool_id: str, tool_name: str) -> Dict[str, Any]:
        """Test a single tool comprehensively"""
        print(f"\n🔧 TESTING TOOL: {tool_name} ({tool_id})")
        print("=" * 60)
        
        results = {
            'tool_id': tool_id,
            'tool_name': tool_name,
            'backend_status': False,
            'tool_info': None,
            'execution_test': False,
            'configuration_test': False,
            'expected_functionality': '',
            'actual_functionality': '',
            'cta_buttons': {
                'execute': False,
                'configure': False,
                'info': False
            },
            'scan_capabilities': {},
            'issues_found': []
        }
        
        # 1. Test Backend Tool Status
        try:
            response = requests.get(f"{self.backend_url}/api/tools/status", timeout=5)
            if response.status_code == 200:
                status_data = response.json()
                tool_status = status_data.get('data', {}).get(tool_id, {})
                results['backend_status'] = tool_status.get('status') == 'available'
                results['tool_info'] = tool_status
                print(f"✅ Backend Status: {tool_status.get('status', 'unknown')}")
                print(f"   Version: {tool_status.get('version', 'unknown')}")
                print(f"   Path: {tool_status.get('path', 'unknown')}")
            else:
                print(f"❌ Backend status check failed: {response.status_code}")
                results['issues_found'].append(f"Backend status API failed: {response.status_code}")
        except Exception as e:
            print(f"❌ Backend status error: {e}")
            results['issues_found'].append(f"Backend status error: {e}")
        
        # 2. Test Tool Information Endpoint
        try:
            response = requests.get(f"{self.backend_url}/api/tools/{tool_id}/info", timeout=5)
            if response.status_code == 200:
                info_data = response.json()
                results['cta_buttons']['info'] = True
                print(f"✅ Tool Info API: Working")
                print(f"   Description: {info_data.get('data', {}).get('description', 'N/A')}")
            elif response.status_code == 404:
                print(f"⚠️ Tool Info API: Not implemented (404)")
                results['issues_found'].append("Tool info endpoint not implemented")
            else:
                print(f"❌ Tool Info API: Failed ({response.status_code})")
                results['issues_found'].append(f"Tool info API failed: {response.status_code}")
        except Exception as e:
            print(f"❌ Tool Info API error: {e}")
            results['issues_found'].append(f"Tool info API error: {e}")
        
        # 3. Test Tool Execution Capability
        self._test_tool_execution(tool_id, results)
        
        # 4. Test Tool Configuration
        self._test_tool_configuration(tool_id, results)
        
        # 5. Define Expected vs Actual Functionality
        self._analyze_tool_functionality(tool_id, tool_name, results)
        
        return results
    
    def _test_tool_execution(self, tool_id: str, results: Dict[str, Any]):
        """Test tool execution functionality"""
        print(f"\n🔥 Testing {tool_id} Execution")
        print("-" * 40)
        
        # Test basic execution endpoint
        try:
            execution_config = {
                "tool_name": tool_id,
                "target": "127.0.0.1",
                "scan_type": "basic",
                "options": {"timeout": 30}
            }
            
            response = requests.post(
                f"{self.backend_url}/api/tools/execute", 
                json=execution_config,
                timeout=5
            )
            
            if response.status_code == 200:
                execution_data = response.json()
                results['execution_test'] = True
                results['cta_buttons']['execute'] = True
                print(f"✅ Execution API: Working")
                print(f"   Scan ID: {execution_data.get('data', {}).get('scan_id', 'N/A')}")
            elif response.status_code == 404:
                print(f"⚠️ Execution API: Not implemented (404)")
                results['issues_found'].append("Tool execution endpoint not implemented")
            else:
                print(f"❌ Execution API: Failed ({response.status_code})")
                print(f"   Response: {response.text}")
                results['issues_found'].append(f"Tool execution failed: {response.status_code}")
                
        except Exception as e:
            print(f"❌ Execution test error: {e}")
            results['issues_found'].append(f"Tool execution error: {e}")
        
        # Test tool-specific execution patterns
        self._test_tool_specific_execution(tool_id, results)
    
    def _test_tool_specific_execution(self, tool_id: str, results: Dict[str, Any]):
        """Test tool-specific execution patterns"""
        
        tool_specific_tests = {
            'nmap': {
                'endpoints': ['/api/scans/nmap', '/api/tools/nmap/scan'],
                'params': {'target': '127.0.0.1', 'scan_type': 'tcp_connect'},
                'expected_options': ['port_range', 'scan_type', 'timing', 'output_format']
            },
            'nuclei': {
                'endpoints': ['/api/scans/nuclei', '/api/tools/nuclei/scan'],
                'params': {'target': 'http://127.0.0.1', 'templates': 'basic'},
                'expected_options': ['templates', 'severity', 'tags', 'rate_limit']
            },
            'sqlmap': {
                'endpoints': ['/api/scans/sqlmap', '/api/tools/sqlmap/scan'],
                'params': {'target': 'http://127.0.0.1?id=1', 'method': 'GET'},
                'expected_options': ['technique', 'risk', 'level', 'database']
            },
            'masscan': {
                'endpoints': ['/api/scans/masscan', '/api/tools/masscan/scan'],
                'params': {'target': '127.0.0.1/24', 'ports': '1-1000'},
                'expected_options': ['port_range', 'rate', 'interface', 'output_format']
            },
            'gobuster': {
                'endpoints': ['/api/scans/gobuster', '/api/tools/gobuster/scan'],
                'params': {'target': 'http://127.0.0.1', 'mode': 'dir'},
                'expected_options': ['wordlist', 'extensions', 'threads', 'timeout']
            }
        }
        
        if tool_id in tool_specific_tests:
            test_config = tool_specific_tests[tool_id]
            
            print(f"   Testing {tool_id}-specific endpoints:")
            for endpoint in test_config['endpoints']:
                try:
                    response = requests.post(
                        f"{self.backend_url}{endpoint}",
                        json=test_config['params'],
                        timeout=5
                    )
                    
                    if response.status_code == 200:
                        print(f"   ✅ {endpoint}: Working")
                        results['scan_capabilities'][endpoint] = True
                    elif response.status_code == 404:
                        print(f"   ⚠️ {endpoint}: Not implemented")
                        results['scan_capabilities'][endpoint] = False
                    else:
                        print(f"   ❌ {endpoint}: Failed ({response.status_code})")
                        results['scan_capabilities'][endpoint] = False
                        
                except Exception as e:
                    print(f"   ❌ {endpoint}: Error ({e})")
                    results['scan_capabilities'][endpoint] = False
    
    def _test_tool_configuration(self, tool_id: str, results: Dict[str, Any]):
        """Test tool configuration functionality"""
        print(f"\n⚙️ Testing {tool_id} Configuration")
        print("-" * 40)
        
        # Test configuration endpoints
        config_endpoints = [
            f'/api/tools/{tool_id}/config',
            f'/api/tools/{tool_id}/options',
            f'/api/tools/{tool_id}/presets'
        ]
        
        working_configs = 0
        for endpoint in config_endpoints:
            try:
                response = requests.get(f"{self.backend_url}{endpoint}", timeout=5)
                if response.status_code == 200:
                    print(f"   ✅ {endpoint}: Working")
                    working_configs += 1
                elif response.status_code == 404:
                    print(f"   ⚠️ {endpoint}: Not implemented")
                else:
                    print(f"   ❌ {endpoint}: Failed ({response.status_code})")
            except Exception as e:
                print(f"   ❌ {endpoint}: Error ({e})")
        
        results['cta_buttons']['configure'] = working_configs > 0
        if working_configs == 0:
            results['issues_found'].append("No configuration endpoints implemented")
    
    def _analyze_tool_functionality(self, tool_id: str, tool_name: str, results: Dict[str, Any]):
        """Analyze expected vs actual functionality"""
        
        expected_functionality = {
            'nmap': {
                'description': 'Network discovery and security auditing',
                'core_features': [
                    'Port scanning (TCP/UDP)',
                    'Service version detection',
                    'OS fingerprinting',
                    'NSE script execution',
                    'Multiple scan types (SYN, Connect, ACK, etc.)',
                    'Custom timing templates',
                    'Output in multiple formats (XML, JSON, Nmap)'
                ],
                'expected_cta': ['Execute scan', 'Configure options', 'View scan types', 'Export results'],
                'integration_points': ['Campaign scans', 'Vulnerability correlation', 'Report generation']
            },
            'nuclei': {
                'description': 'Vulnerability scanner based on templates',
                'core_features': [
                    'Template-based scanning',
                    'CVE detection',
                    'Misconfigurations discovery',
                    'Web application security testing',
                    'Custom template support',
                    'Severity filtering',
                    'Rate limiting'
                ],
                'expected_cta': ['Execute scan', 'Select templates', 'Configure severity', 'Custom templates'],
                'integration_points': ['Vulnerability database', 'CVE correlation', 'Report integration']
            },
            'sqlmap': {
                'description': 'Automatic SQL injection testing tool',
                'core_features': [
                    'SQL injection detection',
                    'Database fingerprinting',
                    'Data extraction',
                    'File system access',
                    'OS command execution',
                    'Multiple injection techniques',
                    'Database-specific payloads'
                ],
                'expected_cta': ['Execute scan', 'Configure techniques', 'Set risk level', 'Database options'],
                'integration_points': ['Web app scanning', 'Vulnerability reports', 'Exploit correlation']
            },
            'masscan': {
                'description': 'High-speed network port scanner',
                'core_features': [
                    'High-speed port scanning',
                    'Large network scanning',
                    'Custom packet rates',
                    'Banner grabbing',
                    'Multiple output formats',
                    'Interface selection',
                    'Exclude ranges'
                ],
                'expected_cta': ['Execute scan', 'Set port range', 'Configure rate', 'Select interface'],
                'integration_points': ['Network discovery', 'Service enumeration', 'Attack surface mapping']
            },
            'gobuster': {
                'description': 'Directory/file brute-forcer',
                'core_features': [
                    'Directory brute-forcing',
                    'File discovery',
                    'DNS subdomain enumeration',
                    'Custom wordlists',
                    'Extension filtering',
                    'Multi-threading',
                    'Status code filtering'
                ],
                'expected_cta': ['Execute scan', 'Select wordlist', 'Set extensions', 'Configure threads'],
                'integration_points': ['Web reconnaissance', 'Attack surface discovery', 'Content enumeration']
            }
        }
        
        if tool_id in expected_functionality:
            expected = expected_functionality[tool_id]
            results['expected_functionality'] = expected
            
            # Determine actual functionality based on test results
            actual_features = []
            if results['backend_status']:
                actual_features.append('Tool available and detected')
            if results['execution_test']:
                actual_features.append('Basic execution capability')
            if results['cta_buttons']['configure']:
                actual_features.append('Configuration support')
            if results['cta_buttons']['info']:
                actual_features.append('Information retrieval')
            
            working_endpoints = sum(1 for v in results['scan_capabilities'].values() if v)
            if working_endpoints > 0:
                actual_features.append(f'{working_endpoints} specialized endpoints working')
            
            results['actual_functionality'] = {
                'implemented_features': actual_features,
                'missing_features': len(expected['core_features']) - len(actual_features),
                'functionality_score': len(actual_features) / len(expected['core_features']) * 100
            }
            
            print(f"\n📊 Functionality Analysis")
            print("-" * 40)
            print(f"Expected: {expected['description']}")
            print(f"Core features expected: {len(expected['core_features'])}")
            print(f"Features implemented: {len(actual_features)}")
            print(f"Functionality score: {results['actual_functionality']['functionality_score']:.1f}%")
            
            if results['actual_functionality']['functionality_score'] < 50:
                results['issues_found'].append("Tool has low functionality implementation")
    
    def test_advanced_tabs_functionality(self):
        """Test advanced tab functionality"""
        print(f"\n🎯 TESTING ADVANCED TABS FUNCTIONALITY")
        print("=" * 60)
        
        tab_tests = {
            'metasploit': self._test_metasploit_tab,
            'execution': self._test_execution_tab,
            'compliance': self._test_compliance_tab,
            'intelligence': self._test_intelligence_tab
        }
        
        tab_results = {}
        for tab_name, test_func in tab_tests.items():
            print(f"\n🔥 Testing {tab_name.upper()} Tab")
            print("-" * 40)
            tab_results[tab_name] = test_func()
        
        return tab_results
    
    def _test_metasploit_tab(self):
        """Test Metasploit tab functionality"""
        results = {
            'module_listing': False,
            'exploit_execution': False,
            'payload_generation': False,
            'configuration': False,
            'issues': []
        }
        
        # Test Metasploit module endpoints
        endpoints = [
            '/api/metasploit/modules',
            '/api/metasploit/exploits',
            '/api/metasploit/auxiliary',
            '/api/metasploit/payloads',
            '/api/metasploit/search'
        ]
        
        for endpoint in endpoints:
            try:
                response = requests.get(f"{self.backend_url}{endpoint}", timeout=5)
                if response.status_code == 200:
                    print(f"✅ {endpoint}: Working")
                    if 'modules' in endpoint:
                        results['module_listing'] = True
                elif response.status_code == 404:
                    print(f"⚠️ {endpoint}: Not implemented")
                else:
                    print(f"❌ {endpoint}: Failed ({response.status_code})")
                    results['issues'].append(f"{endpoint} failed: {response.status_code}")
            except Exception as e:
                print(f"❌ {endpoint}: Error ({e})")
                results['issues'].append(f"{endpoint} error: {e}")
        
        # Test exploit execution
        try:
            execution_data = {
                'module': 'exploit/windows/smb/ms17_010_eternalblue',
                'target': '127.0.0.1',
                'payload': 'windows/x64/meterpreter/reverse_tcp',
                'options': {'LHOST': '127.0.0.1', 'LPORT': 4444}
            }
            
            response = requests.post(
                f"{self.backend_url}/api/metasploit/execute",
                json=execution_data,
                timeout=5
            )
            
            if response.status_code == 200:
                print("✅ Metasploit execution: Working")
                results['exploit_execution'] = True
            elif response.status_code == 404:
                print("⚠️ Metasploit execution: Not implemented")
            else:
                print(f"❌ Metasploit execution: Failed ({response.status_code})")
                results['issues'].append(f"Execution failed: {response.status_code}")
                
        except Exception as e:
            print(f"❌ Metasploit execution error: {e}")
            results['issues'].append(f"Execution error: {e}")
        
        return results
    
    def _test_execution_tab(self):
        """Test Execution tab functionality"""
        results = {
            'execution_modes': False,
            'safety_controls': False,
            'advanced_options': False,
            'real_time_monitoring': False,
            'issues': []
        }
        
        # Test execution management endpoints
        endpoints = [
            '/api/execution/modes',
            '/api/execution/safety',
            '/api/execution/monitor',
            '/api/execution/advanced'
        ]
        
        for endpoint in endpoints:
            try:
                response = requests.get(f"{self.backend_url}{endpoint}", timeout=5)
                if response.status_code == 200:
                    print(f"✅ {endpoint}: Working")
                    if 'modes' in endpoint:
                        results['execution_modes'] = True
                    elif 'safety' in endpoint:
                        results['safety_controls'] = True
                    elif 'advanced' in endpoint:
                        results['advanced_options'] = True
                elif response.status_code == 404:
                    print(f"⚠️ {endpoint}: Not implemented")
                else:
                    print(f"❌ {endpoint}: Failed ({response.status_code})")
                    results['issues'].append(f"{endpoint} failed: {response.status_code}")
            except Exception as e:
                print(f"❌ {endpoint}: Error ({e})")
                results['issues'].append(f"{endpoint} error: {e}")
        
        return results
    
    def _test_compliance_tab(self):
        """Test Compliance tab functionality"""
        results = {
            'framework_listing': False,
            'automated_testing': False,
            'report_generation': False,
            'compliance_scoring': False,
            'issues': []
        }
        
        # Test compliance endpoints
        endpoints = [
            '/api/compliance/frameworks',
            '/api/compliance/test',
            '/api/compliance/report',
            '/api/compliance/score'
        ]
        
        for endpoint in endpoints:
            try:
                response = requests.get(f"{self.backend_url}{endpoint}", timeout=5)
                if response.status_code == 200:
                    print(f"✅ {endpoint}: Working")
                    if 'frameworks' in endpoint:
                        results['framework_listing'] = True
                    elif 'test' in endpoint:
                        results['automated_testing'] = True
                    elif 'report' in endpoint:
                        results['report_generation'] = True
                    elif 'score' in endpoint:
                        results['compliance_scoring'] = True
                elif response.status_code == 404:
                    print(f"⚠️ {endpoint}: Not implemented")
                else:
                    print(f"❌ {endpoint}: Failed ({response.status_code})")
                    results['issues'].append(f"{endpoint} failed: {response.status_code}")
            except Exception as e:
                print(f"❌ {endpoint}: Error ({e})")
                results['issues'].append(f"{endpoint} error: {e}")
        
        return results
    
    def _test_intelligence_tab(self):
        """Test Intelligence tab functionality"""
        results = {
            'mitre_mapping': False,
            'cve_database': False,
            'threat_feeds': False,
            'ai_correlation': False,
            'issues': []
        }
        
        # Test intelligence endpoints
        endpoints = [
            '/api/intelligence/mitre',
            '/api/intelligence/cve',
            '/api/intelligence/threats',
            '/api/intelligence/ai'
        ]
        
        for endpoint in endpoints:
            try:
                response = requests.get(f"{self.backend_url}{endpoint}", timeout=5)
                if response.status_code == 200:
                    print(f"✅ {endpoint}: Working")
                    if 'mitre' in endpoint:
                        results['mitre_mapping'] = True
                    elif 'cve' in endpoint:
                        results['cve_database'] = True
                    elif 'threats' in endpoint:
                        results['threat_feeds'] = True
                    elif 'ai' in endpoint:
                        results['ai_correlation'] = True
                elif response.status_code == 404:
                    print(f"⚠️ {endpoint}: Not implemented")
                else:
                    print(f"❌ {endpoint}: Failed ({response.status_code})")
                    results['issues'].append(f"{endpoint} failed: {response.status_code}")
            except Exception as e:
                print(f"❌ {endpoint}: Error ({e})")
                results['issues'].append(f"{endpoint} error: {e}")
        
        return results
    
    def test_frontend_integration(self):
        """Test frontend-backend integration"""
        print(f"\n🌐 TESTING FRONTEND-BACKEND INTEGRATION")
        print("=" * 60)
        
        # Test if frontend is accessible
        try:
            response = requests.get(self.frontend_url, timeout=3)
            if response.status_code in [200, 404]:  # 404 is OK for SPA
                print("✅ Frontend accessible")
                frontend_accessible = True
            else:
                print(f"⚠️ Frontend status: {response.status_code}")
                frontend_accessible = False
        except Exception as e:
            print(f"⚠️ Frontend not accessible: {e}")
            frontend_accessible = False
        
        # Test critical backend endpoints used by frontend
        critical_endpoints = [
            '/api/tools/available',
            '/api/tools/status',
            '/api/campaigns',
            '/api/dashboard/metrics',
            '/api/security/advanced-tools/status'
        ]
        
        working_endpoints = 0
        for endpoint in critical_endpoints:
            try:
                response = requests.get(f"{self.backend_url}{endpoint}", timeout=5)
                if response.status_code == 200:
                    print(f"✅ {endpoint}: Working")
                    working_endpoints += 1
                else:
                    print(f"❌ {endpoint}: Failed ({response.status_code})")
            except Exception as e:
                print(f"❌ {endpoint}: Error ({e})")
        
        integration_score = working_endpoints / len(critical_endpoints) * 100
        print(f"\n📊 Integration Score: {integration_score:.1f}%")
        
        return {
            'frontend_accessible': frontend_accessible,
            'backend_endpoints_working': working_endpoints,
            'total_endpoints': len(critical_endpoints),
            'integration_score': integration_score
        }
    
    def run_comprehensive_test(self):
        """Run comprehensive E2E test of all tools"""
        start_time = datetime.now()
        
        print("🔧 COMPREHENSIVE E2E TOOLS TESTING")
        print("=" * 80)
        print(f"Test started: {start_time.strftime('%Y-%m-%d %H:%M:%S')}")
        
        # Get available tools first
        try:
            response = requests.get(f"{self.backend_url}/api/tools/available", timeout=5)
            if response.status_code == 200:
                tools_data = response.json()
                available_tools = tools_data.get('data', [])
                print(f"\n📋 Found {len(available_tools)} tools to test")
            else:
                print(f"❌ Failed to get available tools: {response.status_code}")
                return
        except Exception as e:
            print(f"❌ Error getting available tools: {e}")
            return
        
        # Test each individual tool
        tool_results = {}
        for tool in available_tools:
            tool_id = tool.get('id')
            tool_name = tool.get('name')
            if tool_id and tool_name:
                tool_results[tool_id] = self.test_individual_tool(tool_id, tool_name)
        
        # Test advanced tabs
        tab_results = self.test_advanced_tabs_functionality()
        
        # Test frontend integration
        integration_results = self.test_frontend_integration()
        
        # Generate comprehensive report
        self._generate_comprehensive_report(tool_results, tab_results, integration_results, start_time)
        
        return {
            'tools': tool_results,
            'tabs': tab_results,
            'integration': integration_results
        }
    
    def _generate_comprehensive_report(self, tool_results, tab_results, integration_results, start_time):
        """Generate comprehensive test report"""
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        print(f"\n\n🔧 COMPREHENSIVE E2E TEST REPORT")
        print("=" * 80)
        
        # Summary statistics
        total_tools = len(tool_results)
        working_tools = sum(1 for r in tool_results.values() if r['backend_status'])
        executable_tools = sum(1 for r in tool_results.values() if r['execution_test'])
        configurable_tools = sum(1 for r in tool_results.values() if r['cta_buttons']['configure'])
        
        print(f"Test Duration: {duration:.2f} seconds")
        print(f"Tools Tested: {total_tools}")
        print(f"Tools Available: {working_tools}/{total_tools} ({working_tools/total_tools*100:.1f}%)")
        print(f"Tools Executable: {executable_tools}/{total_tools} ({executable_tools/total_tools*100:.1f}%)")
        print(f"Tools Configurable: {configurable_tools}/{total_tools} ({configurable_tools/total_tools*100:.1f}%)")
        
        # Individual tool reports
        print(f"\n📊 INDIVIDUAL TOOL ANALYSIS")
        print("-" * 60)
        
        for tool_id, results in tool_results.items():
            print(f"\n🔧 {results['tool_name']} ({tool_id})")
            print(f"   Backend Status: {'✅' if results['backend_status'] else '❌'}")
            print(f"   Execution: {'✅' if results['execution_test'] else '❌'}")
            print(f"   Configuration: {'✅' if results['cta_buttons']['configure'] else '❌'}")
            print(f"   Info API: {'✅' if results['cta_buttons']['info'] else '❌'}")
            
            if 'actual_functionality' in results:
                score = results['actual_functionality']['functionality_score']
                print(f"   Functionality Score: {score:.1f}%")
            
            if results['issues_found']:
                print(f"   Issues: {len(results['issues_found'])}")
                for issue in results['issues_found'][:2]:  # Show first 2 issues
                    print(f"     - {issue}")
        
        # Advanced tabs report
        print(f"\n🎯 ADVANCED TABS ANALYSIS")
        print("-" * 60)
        
        for tab_name, tab_result in tab_results.items():
            working_features = sum(1 for v in tab_result.values() if isinstance(v, bool) and v)
            total_features = sum(1 for v in tab_result.values() if isinstance(v, bool))
            score = working_features / total_features * 100 if total_features > 0 else 0
            
            print(f"{tab_name.upper()}: {working_features}/{total_features} features ({score:.1f}%)")
            if tab_result.get('issues'):
                print(f"   Issues: {len(tab_result['issues'])}")
        
        # Integration report
        print(f"\n🌐 FRONTEND-BACKEND INTEGRATION")
        print("-" * 60)
        print(f"Frontend Accessible: {'✅' if integration_results['frontend_accessible'] else '❌'}")
        print(f"Backend Endpoints: {integration_results['backend_endpoints_working']}/{integration_results['total_endpoints']}")
        print(f"Integration Score: {integration_results['integration_score']:.1f}%")
        
        # Overall assessment
        overall_score = (
            (working_tools / total_tools * 30) +
            (executable_tools / total_tools * 25) +
            (configurable_tools / total_tools * 15) +
            (integration_results['integration_score'] * 0.30)
        )
        
        print(f"\n🎯 OVERALL ASSESSMENT")
        print("-" * 60)
        print(f"Overall System Score: {overall_score:.1f}%")
        
        if overall_score >= 80:
            print("🎉 EXCELLENT - System is highly functional")
        elif overall_score >= 60:
            print("✅ GOOD - System is mostly functional with minor issues")
        elif overall_score >= 40:
            print("⚠️ FAIR - System has significant functionality gaps")
        else:
            print("❌ POOR - System requires major improvements")
        
        # Recommendations
        print(f"\n💡 RECOMMENDATIONS")
        print("-" * 60)
        
        if executable_tools < total_tools:
            print("• Implement missing tool execution endpoints")
        if configurable_tools < total_tools:
            print("• Add configuration interfaces for tools")
        if integration_results['integration_score'] < 90:
            print("• Fix failing backend API endpoints")
        
        # Critical issues
        critical_issues = []
        for results in tool_results.values():
            critical_issues.extend(results['issues_found'])
        
        if critical_issues:
            print(f"\n🚨 CRITICAL ISSUES ({len(critical_issues)})")
            print("-" * 60)
            for issue in critical_issues[:5]:  # Show top 5 issues
                print(f"• {issue}")

def main():
    """Main test execution"""
    tester = ToolE2ETester()
    results = tester.run_comprehensive_test()
    return results

if __name__ == "__main__":
    main()