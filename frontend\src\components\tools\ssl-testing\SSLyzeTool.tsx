/**
 * SSLyze Tool Component
 * Fast and powerful SSL/TLS scanner and configuration analyzer
 */
import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { 
  Key,
  Play,
  Square,
  Download,
  Settings,
  Target,
  Globe,
  FileText,
  Terminal,
  Copy,
  RotateCcw,
  Clock,
  Zap,
  Filter,
  Eye,
  AlertTriangle,
  CheckCircle,
  TrendingUp,
  Activity,
  Lock,
  Search,
  List,
  BarChart3,
  Database,
  Server,
  Bug,
  Info,
  Code,
  Package,
  Layers,
  Shield,
  Crosshair,
  Cpu,
  Network,
  Gauge,
  Tag,
  Hash,
  Workflow,
  GitBranch,
  Book,
  Users,
  Table,
  FileX,
  Skull,
  Unlock,
  HardDrive,
  Flame,
  Binary,
  Braces,
  FileKey,
  History,
  Folder,
  Star,
  Crown,
  Award,
  Sparkles,
  Wifi,
  Router,
  Mail,
  CloudRain,
  Rss,
  UserCheck,
  UserX,
  Timer,
  Pause,
  FastForward,
  Rewind,
  SkipForward,
  Certificate,
  ShieldCheck,
  ShieldAlert,
  AlertCircle,
  XCircle,
  RefreshCw,
  ExternalLink,
  FileCheck,
  FileWarning,
  Fingerprint,
  Key as KeyIcon,
  LockOpen,
  ShieldOff,
  Verified,
  ScanLine,
  Scan,
  FileJson,
  FileCode,
  Clipboard,
  Monitor,
  Globe2,
  Radar,
  Smartphone,
  Laptop,
  Tablet
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useBackendStore } from '@/stores/backend-store';
import { apiClient, ToolExecution } from '@/services/api-client';
import { toast } from 'sonner';

/**
 * SSLyze configuration interface
 */
interface SSLyzeConfig {
  targets: {
    hostnames: string[];
    hostsFile: string;
    networkTimeout: number;
    startTLS: string;
    xmppTo: string;
    sni: string;
  };
  scanning: {
    cipherSuites: boolean;
    protocols: boolean;
    certificates: boolean;
    vulnerabilities: boolean;
    compression: boolean;
    renegotiation: boolean;
    resumption: boolean;
    heartbleed: boolean;
    openSSLCcs: boolean;
    fallbackScsv: boolean;
    earlyData: boolean;
    robotVulnerability: boolean;
  };
  certificates: {
    trustStores: string[];
    customCaFile: string;
    certInfoAll: boolean;
    certInfoBasic: boolean;
    ocspStapling: boolean;
  };
  output: {
    format: 'console' | 'json' | 'xml';
    outputFile: string;
    jsonFile: string;
    xmlFile: string;
    quiet: boolean;
    verbose: boolean;
  };
  performance: {
    concurrentConnections: number;
    concurrentScans: number;
    slowConnection: boolean;
    httpsTunnel: string;
    clientCertificate: string;
    clientKey: string;
    clientKeyPassword: string;
    clientKeyFormat: 'pem' | 'der';
  };
  advanced: {
    cipherListFormat: 'openssl' | 'rfc';
    tlsWrappedProtocol: string;
    regularExpression: string;
    updateTrustStores: boolean;
    checkAgainstMozillaPolicy: boolean;
  };
}

/**
 * SSL/TLS scan plugins available in SSLyze
 */
const SSLYZE_PLUGINS = {
  'ssl_2_0_cipher_suites': {
    name: 'SSL 2.0 Cipher Suites',
    description: 'List supported SSL 2.0 cipher suites',
    category: 'protocols',
    severity: 'high',
    enabled: true
  },
  'ssl_3_0_cipher_suites': {
    name: 'SSL 3.0 Cipher Suites',
    description: 'List supported SSL 3.0 cipher suites',
    category: 'protocols',
    severity: 'high',
    enabled: true
  },
  'tls_1_0_cipher_suites': {
    name: 'TLS 1.0 Cipher Suites',
    description: 'List supported TLS 1.0 cipher suites',
    category: 'protocols',
    severity: 'medium',
    enabled: true
  },
  'tls_1_1_cipher_suites': {
    name: 'TLS 1.1 Cipher Suites',
    description: 'List supported TLS 1.1 cipher suites',
    category: 'protocols',
    severity: 'medium',
    enabled: true
  },
  'tls_1_2_cipher_suites': {
    name: 'TLS 1.2 Cipher Suites',
    description: 'List supported TLS 1.2 cipher suites',
    category: 'protocols',
    severity: 'low',
    enabled: true
  },
  'tls_1_3_cipher_suites': {
    name: 'TLS 1.3 Cipher Suites',
    description: 'List supported TLS 1.3 cipher suites',
    category: 'protocols',
    severity: 'low',
    enabled: true
  },
  'certificate_info': {
    name: 'Certificate Information',
    description: 'Retrieve and analyze certificate chain',
    category: 'certificates',
    severity: 'medium',
    enabled: true
  },
  'ssl_compression': {
    name: 'SSL/TLS Compression',
    description: 'Test for SSL/TLS compression (CRIME vulnerability)',
    category: 'vulnerabilities',
    severity: 'medium',
    enabled: true
  },
  'tls_1_3_early_data': {
    name: 'TLS 1.3 Early Data',
    description: 'Test TLS 1.3 0-RTT support',
    category: 'protocols',
    severity: 'low',
    enabled: false
  },
  'fallback_scsv': {
    name: 'TLS Fallback SCSV',
    description: 'Test for TLS_FALLBACK_SCSV support',
    category: 'vulnerabilities',
    severity: 'medium',
    enabled: true
  },
  'heartbleed': {
    name: 'Heartbleed',
    description: 'Test for OpenSSL Heartbleed vulnerability (CVE-2014-0160)',
    category: 'vulnerabilities',
    severity: 'critical',
    enabled: true
  },
  'openssl_ccs_injection': {
    name: 'OpenSSL CCS Injection',
    description: 'Test for OpenSSL CCS injection vulnerability (CVE-2014-0224)',
    category: 'vulnerabilities',
    severity: 'high',
    enabled: true
  },
  'tls_renegotiation': {
    name: 'TLS Renegotiation',
    description: 'Test for secure renegotiation support',
    category: 'vulnerabilities',
    severity: 'medium',
    enabled: true
  },
  'session_resumption': {
    name: 'Session Resumption',
    description: 'Test session resumption with session IDs and tickets',
    category: 'performance',
    severity: 'low',
    enabled: false
  },
  'session_resumption_rate': {
    name: 'Session Resumption Rate',
    description: 'Measure session resumption rate',
    category: 'performance',
    severity: 'low',
    enabled: false
  },
  'robot': {
    name: 'ROBOT Vulnerability',
    description: 'Test for Return Of Bleichenbacher Oracle Threat (CVE-2017-13099)',
    category: 'vulnerabilities',
    severity: 'medium',
    enabled: true
  }
};

/**
 * STARTTLS protocols supported by SSLyze
 */
const STARTTLS_PROTOCOLS = {
  '': { name: 'None (Direct TLS)', description: 'Direct TLS connection (HTTPS, IMAPS, etc.)' },
  'smtp': { name: 'SMTP', description: 'SMTP with STARTTLS' },
  'xmpp': { name: 'XMPP', description: 'XMPP with STARTTLS' },
  'xmpp_server': { name: 'XMPP Server', description: 'XMPP server-to-server' },
  'pop3': { name: 'POP3', description: 'POP3 with STARTTLS' },
  'imap': { name: 'IMAP', description: 'IMAP with STARTTLS' },
  'ftp': { name: 'FTP', description: 'FTP with AUTH TLS' },
  'ldap': { name: 'LDAP', description: 'LDAP with STARTTLS' },
  'rdp': { name: 'RDP', description: 'RDP with TLS' },
  'postgres': { name: 'PostgreSQL', description: 'PostgreSQL with TLS' }
};

/**
 * Certificate trust stores for validation
 */
const TRUST_STORES = {
  'mozilla': { name: 'Mozilla NSS', description: 'Mozilla certificate trust store' },
  'microsoft': { name: 'Microsoft', description: 'Microsoft certificate trust store' },
  'apple': { name: 'Apple', description: 'Apple certificate trust store' },
  'java': { name: 'Java', description: 'Java certificate trust store' },
  'android': { name: 'Android', description: 'Android certificate trust store' }
};

/**
 * Cipher suite formats
 */
const CIPHER_FORMATS = {
  'openssl': { name: 'OpenSSL Format', description: 'Traditional OpenSSL cipher names' },
  'rfc': { name: 'RFC Format', description: 'RFC standard cipher suite names' }
};

/**
 * Scanning presets for different use cases
 */
const SCANNING_PRESETS = {
  'basic': {
    name: 'Basic Scan',
    description: 'Essential SSL/TLS checks for quick assessment',
    plugins: {
      cipherSuites: true,
      protocols: true,
      certificates: true,
      vulnerabilities: false,
      compression: false,
      renegotiation: false,
      resumption: false,
      heartbleed: true,
      openSSLCcs: true,
      fallbackScsv: true,
      earlyData: false,
      robotVulnerability: false
    }
  },
  'security_focused': {
    name: 'Security Focused',
    description: 'Comprehensive vulnerability and security analysis',
    plugins: {
      cipherSuites: true,
      protocols: true,
      certificates: true,
      vulnerabilities: true,
      compression: true,
      renegotiation: true,
      resumption: false,
      heartbleed: true,
      openSSLCcs: true,
      fallbackScsv: true,
      earlyData: false,
      robotVulnerability: true
    }
  },
  'compliance': {
    name: 'Compliance Check',
    description: 'Detailed analysis for compliance requirements',
    plugins: {
      cipherSuites: true,
      protocols: true,
      certificates: true,
      vulnerabilities: true,
      compression: true,
      renegotiation: true,
      resumption: true,
      heartbleed: true,
      openSSLCcs: true,
      fallbackScsv: true,
      earlyData: true,
      robotVulnerability: true
    }
  },
  'performance': {
    name: 'Performance Analysis',
    description: 'Focus on session resumption and performance',
    plugins: {
      cipherSuites: false,
      protocols: false,
      certificates: false,
      vulnerabilities: false,
      compression: false,
      renegotiation: false,
      resumption: true,
      heartbleed: false,
      openSSLCcs: false,
      fallbackScsv: false,
      earlyData: true,
      robotVulnerability: false
    }
  }
};

export default function SSLyzeTool() {
  const { isConnected } = useBackendStore();
  const [config, setConfig] = React.useState<SSLyzeConfig>({
    targets: {
      hostnames: [''],
      hostsFile: '',
      networkTimeout: 5,
      startTLS: '',
      xmppTo: '',
      sni: ''
    },
    scanning: {
      cipherSuites: true,
      protocols: true,
      certificates: true,
      vulnerabilities: true,
      compression: true,
      renegotiation: true,
      resumption: false,
      heartbleed: true,
      openSSLCcs: true,
      fallbackScsv: true,
      earlyData: false,
      robotVulnerability: true
    },
    certificates: {
      trustStores: ['mozilla'],
      customCaFile: '',
      certInfoAll: true,
      certInfoBasic: false,
      ocspStapling: true
    },
    output: {
      format: 'console',
      outputFile: '',
      jsonFile: '',
      xmlFile: '',
      quiet: false,
      verbose: false
    },
    performance: {
      concurrentConnections: 5,
      concurrentScans: 1,
      slowConnection: false,
      httpsTunnel: '',
      clientCertificate: '',
      clientKey: '',
      clientKeyPassword: '',
      clientKeyFormat: 'pem'
    },
    advanced: {
      cipherListFormat: 'openssl',
      tlsWrappedProtocol: '',
      regularExpression: '',
      updateTrustStores: false,
      checkAgainstMozillaPolicy: false
    }
  });

  const [isRunning, setIsRunning] = React.useState(false);
  const [output, setOutput] = React.useState<string[]>([]);
  const [results, setResults] = React.useState<any[]>([]);
  const [currentExecution, setCurrentExecution] = React.useState<ToolExecution | null>(null);
  const [progress, setProgress] = React.useState(0);
  const [scanResults, setScanResults] = React.useState<any>({
    summary: null,
    vulnerabilities: [],
    certificates: [],
    cipherSuites: [],
    protocols: []
  });

  /**
   * Apply scanning preset
   */
  const applyPreset = (preset: keyof typeof SCANNING_PRESETS) => {
    const presetConfig = SCANNING_PRESETS[preset];
    setConfig(prev => ({
      ...prev,
      scanning: presetConfig.plugins
    }));
    toast.success(`Applied ${presetConfig.name} preset`);
  };

  /**
   * Add hostname to targets
   */
  const addHostname = () => {
    setConfig(prev => ({
      ...prev,
      targets: {
        ...prev.targets,
        hostnames: [...prev.targets.hostnames, '']
      }
    }));
  };

  /**
   * Remove hostname from targets
   */
  const removeHostname = (index: number) => {
    setConfig(prev => ({
      ...prev,
      targets: {
        ...prev.targets,
        hostnames: prev.targets.hostnames.filter((_, i) => i !== index)
      }
    }));
  };

  /**
   * Update hostname at specific index
   */
  const updateHostname = (index: number, value: string) => {
    setConfig(prev => ({
      ...prev,
      targets: {
        ...prev.targets,
        hostnames: prev.targets.hostnames.map((hostname, i) => 
          i === index ? value : hostname
        )
      }
    }));
  };

  /**
   * Generate SSLyze command
   */
  const generateCommand = (): string => {
    const parts = ['sslyze'];

    // Output format
    if (config.output.format === 'json') {
      parts.push('--json_out', config.output.jsonFile || '/tmp/sslyze.json');
    } else if (config.output.format === 'xml') {
      parts.push('--xml_out', config.output.xmlFile || '/tmp/sslyze.xml');
    }

    if (config.output.quiet) {
      parts.push('--quiet');
    }

    if (config.output.verbose) {
      parts.push('--verbose');
    }

    // Performance options
    if (config.performance.concurrentConnections !== 5) {
      parts.push('--max_processes_nb', config.performance.concurrentConnections.toString());
    }

    if (config.performance.slowConnection) {
      parts.push('--slow_connection');
    }

    if (config.performance.httpsTunnel) {
      parts.push('--https_tunnel', config.performance.httpsTunnel);
    }

    if (config.performance.clientCertificate && config.performance.clientKey) {
      parts.push('--cert', config.performance.clientCertificate);
      parts.push('--key', config.performance.clientKey);
      if (config.performance.clientKeyPassword) {
        parts.push('--keypassword', config.performance.clientKeyPassword);
      }
      parts.push('--keyform', config.performance.clientKeyFormat);
    }

    // Network options
    if (config.targets.networkTimeout !== 5) {
      parts.push('--timeout', config.targets.networkTimeout.toString());
    }

    if (config.targets.startTLS) {
      parts.push('--starttls', config.targets.startTLS);
    }

    if (config.targets.xmppTo) {
      parts.push('--xmpp_to', config.targets.xmppTo);
    }

    if (config.targets.sni) {
      parts.push('--sni', config.targets.sni);
    }

    // Certificate options
    if (config.certificates.customCaFile) {
      parts.push('--ca_file', config.certificates.customCaFile);
    }

    // Advanced options
    if (config.advanced.updateTrustStores) {
      parts.push('--update_trust_stores');
    }

    if (config.advanced.checkAgainstMozillaPolicy) {
      parts.push('--mozilla_config');
    }

    if (config.advanced.regularExpression) {
      parts.push('--regular', config.advanced.regularExpression);
    }

    // Scanning plugins
    const scanArgs: string[] = [];

    if (config.scanning.protocols || config.scanning.cipherSuites) {
      if (config.scanning.cipherSuites) {
        scanArgs.push('--sslv2', '--sslv3', '--tlsv1', '--tlsv1_1', '--tlsv1_2', '--tlsv1_3');
      }
    }

    if (config.scanning.certificates) {
      scanArgs.push('--certinfo');
    }

    if (config.scanning.compression) {
      scanArgs.push('--compression');
    }

    if (config.scanning.renegotiation) {
      scanArgs.push('--reneg');
    }

    if (config.scanning.resumption) {
      scanArgs.push('--resum', '--resum_rate');
    }

    if (config.scanning.heartbleed) {
      scanArgs.push('--heartbleed');
    }

    if (config.scanning.openSSLCcs) {
      scanArgs.push('--openssl_ccs');
    }

    if (config.scanning.fallbackScsv) {
      scanArgs.push('--fallback');
    }

    if (config.scanning.earlyData) {
      scanArgs.push('--early_data');
    }

    if (config.scanning.robotVulnerability) {
      scanArgs.push('--robot');
    }

    parts.push(...scanArgs);

    // Targets
    if (config.targets.hostsFile) {
      parts.push('--targets_in', config.targets.hostsFile);
    } else {
      const validHostnames = config.targets.hostnames.filter(h => h.trim());
      parts.push(...validHostnames);
    }

    return parts.join(' ');
  };

  /**
   * Start SSLyze execution
   */
  const startExecution = async () => {
    if (!isConnected) {
      toast.error('Backend not connected');
      return;
    }

    // Validation
    const validHostnames = config.targets.hostnames.filter(h => h.trim());
    if (validHostnames.length === 0 && !config.targets.hostsFile) {
      toast.error('Please specify at least one hostname or hosts file');
      return;
    }

    try {
      setIsRunning(true);
      setOutput([]);
      setResults([]);
      setProgress(0);
      setScanResults({
        summary: null,
        vulnerabilities: [],
        certificates: [],
        cipherSuites: [],
        protocols: []
      });

      const execution = await apiClient.startTool('sslyze', {
        ...config,
        command: generateCommand()
      });

      setCurrentExecution(execution);
      toast.success('SSLyze scan started');

      // Listen for progress updates
      const eventSource = new EventSource(`/api/tools/sslyze/execution/${execution.id}/stream`);
      
      eventSource.onmessage = (event) => {
        const data = JSON.parse(event.data);
        
        if (data.type === 'output') {
          setOutput(prev => [...prev, data.content]);
        } else if (data.type === 'progress') {
          setProgress(data.progress);
        } else if (data.type === 'result') {
          setResults(prev => [...prev, data.result]);
        } else if (data.type === 'scan_results') {
          setScanResults(data.results);
        } else if (data.type === 'complete') {
          setIsRunning(false);
          eventSource.close();
          toast.success('SSLyze scan completed');
        } else if (data.type === 'error') {
          setIsRunning(false);
          eventSource.close();
          toast.error(`Error: ${data.message}`);
        }
      };

      eventSource.onerror = () => {
        setIsRunning(false);
        eventSource.close();
        toast.error('Connection to execution stream lost');
      };

    } catch (error) {
      setIsRunning(false);
      console.error('Failed to start SSLyze:', error);
      toast.error('Failed to start SSLyze');
    }
  };

  /**
   * Stop execution
   */
  const stopExecution = async () => {
    if (!currentExecution) return;

    try {
      await apiClient.stopTool('sslyze', currentExecution.id);
      setIsRunning(false);
      toast.success('SSLyze scan stopped');
    } catch (error) {
      console.error('Failed to stop SSLyze:', error);
      toast.error('Failed to stop SSLyze');
    }
  };

  /**
   * Export results
   */
  const exportResults = () => {
    const exportData = {
      config,
      results,
      scanResults,
      command: generateCommand(),
      timestamp: new Date().toISOString()
    };

    const blob = new Blob([JSON.stringify(exportData, null, 2)], {
      type: 'application/json'
    });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `sslyze-results-${Date.now()}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    
    toast.success('Results exported successfully');
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className="flex items-center justify-center w-10 h-10 bg-purple-100 rounded-lg">
            <Scan className="w-5 h-5 text-purple-600" />
          </div>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">SSLyze</h1>
            <p className="text-sm text-gray-500">Fast and powerful SSL/TLS scanner and configuration analyzer</p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Badge variant={isConnected ? "default" : "secondary"}>
            {isConnected ? "Connected" : "Disconnected"}
          </Badge>
          <Badge variant="outline" className="font-mono">v5.0.8</Badge>
        </div>
      </div>

      {/* Quick Start Presets */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Zap className="w-5 h-5" />
            <span>Scanning Presets</span>
          </CardTitle>
          <CardDescription>
            Pre-configured scanning profiles optimized for different scenarios
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-3">
            {Object.entries(SCANNING_PRESETS).map(([key, preset]) => (
              <Button
                key={key}
                variant="outline"
                size="sm"
                onClick={() => applyPreset(key as keyof typeof SCANNING_PRESETS)}
                className="h-auto p-3 text-left justify-start"
              >
                <div>
                  <div className="font-medium text-sm">{preset.name}</div>
                  <div className="text-xs text-gray-500">{preset.description}</div>
                </div>
              </Button>
            ))}
          </div>
        </CardContent>
      </Card>

      <Tabs defaultValue="configure" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="configure" className="flex items-center space-x-2">
            <Settings className="w-4 h-4" />
            <span>Configure</span>
          </TabsTrigger>
          <TabsTrigger value="output" className="flex items-center space-x-2">
            <Terminal className="w-4 h-4" />
            <span>Output</span>
          </TabsTrigger>
          <TabsTrigger value="results" className="flex items-center space-x-2">
            <Target className="w-4 h-4" />
            <span>Results</span>
          </TabsTrigger>
          <TabsTrigger value="command" className="flex items-center space-x-2">
            <Code className="w-4 h-4" />
            <span>Command</span>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="configure" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Target Configuration */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Target className="w-5 h-5" />
                  <span>Target Configuration</span>
                </CardTitle>
                <CardDescription>
                  Specify the SSL/TLS endpoints to scan
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <label className="text-sm font-medium">Target Hostnames</label>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={addHostname}
                    >
                      Add Target
                    </Button>
                  </div>
                  <div className="space-y-2">
                    {config.targets.hostnames.map((hostname, index) => (
                      <div key={index} className="flex items-center space-x-2">
                        <Input
                          placeholder="example.com:443"
                          value={hostname}
                          onChange={(e) => updateHostname(index, e.target.value)}
                          className="flex-1"
                        />
                        {config.targets.hostnames.length > 1 && (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => removeHostname(index)}
                          >
                            <XCircle className="w-4 h-4" />
                          </Button>
                        )}
                      </div>
                    ))}
                  </div>
                  <p className="text-xs text-gray-500">
                    Format: hostname:port (port optional, defaults to 443)
                  </p>
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">Hosts File (Alternative)</label>
                  <Input
                    placeholder="/path/to/hosts.txt"
                    value={config.targets.hostsFile}
                    onChange={(e) => setConfig(prev => ({
                      ...prev,
                      targets: { ...prev.targets, hostsFile: e.target.value }
                    }))}
                  />
                  <p className="text-xs text-gray-500">File with targets, one per line</p>
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">STARTTLS Protocol</label>
                  <Select 
                    value={config.targets.startTLS} 
                    onValueChange={(value) => setConfig(prev => ({
                      ...prev,
                      targets: { ...prev.targets, startTLS: value }
                    }))}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select protocol..." />
                    </SelectTrigger>
                    <SelectContent>
                      {Object.entries(STARTTLS_PROTOCOLS).map(([key, protocol]) => (
                        <SelectItem key={key} value={key}>
                          <div>
                            <div className="font-medium">{protocol.name}</div>
                            <div className="text-xs text-gray-500">{protocol.description}</div>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Network Timeout (s)</label>
                    <Input
                      type="number"
                      min="1"
                      max="60"
                      value={config.targets.networkTimeout}
                      onChange={(e) => setConfig(prev => ({
                        ...prev,
                        targets: { ...prev.targets, networkTimeout: parseInt(e.target.value) || 5 }
                      }))}
                    />
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium">SNI Override</label>
                    <Input
                      placeholder="sni.example.com"
                      value={config.targets.sni}
                      onChange={(e) => setConfig(prev => ({
                        ...prev,
                        targets: { ...prev.targets, sni: e.target.value }
                      }))}
                    />
                  </div>
                </div>

                {config.targets.startTLS === 'xmpp' && (
                  <div className="space-y-2">
                    <label className="text-sm font-medium">XMPP To Domain</label>
                    <Input
                      placeholder="domain.com"
                      value={config.targets.xmppTo}
                      onChange={(e) => setConfig(prev => ({
                        ...prev,
                        targets: { ...prev.targets, xmppTo: e.target.value }
                      }))}
                    />
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Scanning Configuration */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <ScanLine className="w-5 h-5" />
                  <span>Scanning Options</span>
                </CardTitle>
                <CardDescription>
                  Select SSL/TLS tests to perform
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid gap-3">
                  <div className="flex items-center space-x-3 p-3 border rounded-lg">
                    <input
                      type="checkbox"
                      id="protocols"
                      checked={config.scanning.protocols}
                      onChange={(e) => setConfig(prev => ({
                        ...prev,
                        scanning: { ...prev.scanning, protocols: e.target.checked }
                      }))}
                      className="rounded"
                    />
                    <div className="flex-1">
                      <div className="flex items-center space-x-2">
                        <Network className="w-4 h-4" />
                        <label htmlFor="protocols" className="font-medium text-sm cursor-pointer">
                          Protocol Support
                        </label>
                        <Badge variant="outline" className="text-xs border-blue-200 text-blue-700">
                          medium
                        </Badge>
                      </div>
                      <p className="text-xs text-gray-500 mt-1">Test SSL/TLS protocol versions</p>
                    </div>
                  </div>

                  <div className="flex items-center space-x-3 p-3 border rounded-lg">
                    <input
                      type="checkbox"
                      id="cipher-suites"
                      checked={config.scanning.cipherSuites}
                      onChange={(e) => setConfig(prev => ({
                        ...prev,
                        scanning: { ...prev.scanning, cipherSuites: e.target.checked }
                      }))}
                      className="rounded"
                    />
                    <div className="flex-1">
                      <div className="flex items-center space-x-2">
                        <Lock className="w-4 h-4" />
                        <label htmlFor="cipher-suites" className="font-medium text-sm cursor-pointer">
                          Cipher Suites
                        </label>
                        <Badge variant="outline" className="text-xs border-blue-200 text-blue-700">
                          medium
                        </Badge>
                      </div>
                      <p className="text-xs text-gray-500 mt-1">Enumerate supported cipher suites</p>
                    </div>
                  </div>

                  <div className="flex items-center space-x-3 p-3 border rounded-lg">
                    <input
                      type="checkbox"
                      id="certificates"
                      checked={config.scanning.certificates}
                      onChange={(e) => setConfig(prev => ({
                        ...prev,
                        scanning: { ...prev.scanning, certificates: e.target.checked }
                      }))}
                      className="rounded"
                    />
                    <div className="flex-1">
                      <div className="flex items-center space-x-2">
                        <Certificate className="w-4 h-4" />
                        <label htmlFor="certificates" className="font-medium text-sm cursor-pointer">
                          Certificate Analysis
                        </label>
                        <Badge variant="outline" className="text-xs border-yellow-200 text-yellow-700">
                          high
                        </Badge>
                      </div>
                      <p className="text-xs text-gray-500 mt-1">Analyze certificate chain and properties</p>
                    </div>
                  </div>

                  <div className="flex items-center space-x-3 p-3 border rounded-lg">
                    <input
                      type="checkbox"
                      id="heartbleed"
                      checked={config.scanning.heartbleed}
                      onChange={(e) => setConfig(prev => ({
                        ...prev,
                        scanning: { ...prev.scanning, heartbleed: e.target.checked }
                      }))}
                      className="rounded"
                    />
                    <div className="flex-1">
                      <div className="flex items-center space-x-2">
                        <ShieldAlert className="w-4 h-4" />
                        <label htmlFor="heartbleed" className="font-medium text-sm cursor-pointer">
                          Heartbleed
                        </label>
                        <Badge variant="outline" className="text-xs border-red-200 text-red-700">
                          critical
                        </Badge>
                      </div>
                      <p className="text-xs text-gray-500 mt-1">Test for CVE-2014-0160 (Heartbleed)</p>
                    </div>
                  </div>

                  <div className="flex items-center space-x-3 p-3 border rounded-lg">
                    <input
                      type="checkbox"
                      id="openssl-ccs"
                      checked={config.scanning.openSSLCcs}
                      onChange={(e) => setConfig(prev => ({
                        ...prev,
                        scanning: { ...prev.scanning, openSSLCcs: e.target.checked }
                      }))}
                      className="rounded"
                    />
                    <div className="flex-1">
                      <div className="flex items-center space-x-2">
                        <Bug className="w-4 h-4" />
                        <label htmlFor="openssl-ccs" className="font-medium text-sm cursor-pointer">
                          OpenSSL CCS Injection
                        </label>
                        <Badge variant="outline" className="text-xs border-orange-200 text-orange-700">
                          high
                        </Badge>
                      </div>
                      <p className="text-xs text-gray-500 mt-1">Test for CVE-2014-0224</p>
                    </div>
                  </div>

                  <div className="flex items-center space-x-3 p-3 border rounded-lg">
                    <input
                      type="checkbox"
                      id="robot"
                      checked={config.scanning.robotVulnerability}
                      onChange={(e) => setConfig(prev => ({
                        ...prev,
                        scanning: { ...prev.scanning, robotVulnerability: e.target.checked }
                      }))}
                      className="rounded"
                    />
                    <div className="flex-1">
                      <div className="flex items-center space-x-2">
                        <Skull className="w-4 h-4" />
                        <label htmlFor="robot" className="font-medium text-sm cursor-pointer">
                          ROBOT Attack
                        </label>
                        <Badge variant="outline" className="text-xs border-yellow-200 text-yellow-700">
                          medium
                        </Badge>
                      </div>
                      <p className="text-xs text-gray-500 mt-1">Test for Return Of Bleichenbacher Oracle Threat</p>
                    </div>
                  </div>

                  <div className="flex items-center space-x-3 p-3 border rounded-lg">
                    <input
                      type="checkbox"
                      id="compression"
                      checked={config.scanning.compression}
                      onChange={(e) => setConfig(prev => ({
                        ...prev,
                        scanning: { ...prev.scanning, compression: e.target.checked }
                      }))}
                      className="rounded"
                    />
                    <div className="flex-1">
                      <div className="flex items-center space-x-2">
                        <Package className="w-4 h-4" />
                        <label htmlFor="compression" className="font-medium text-sm cursor-pointer">
                          SSL/TLS Compression
                        </label>
                        <Badge variant="outline" className="text-xs border-yellow-200 text-yellow-700">
                          medium
                        </Badge>
                      </div>
                      <p className="text-xs text-gray-500 mt-1">Test for CRIME vulnerability</p>
                    </div>
                  </div>

                  <div className="flex items-center space-x-3 p-3 border rounded-lg">
                    <input
                      type="checkbox"
                      id="renegotiation"
                      checked={config.scanning.renegotiation}
                      onChange={(e) => setConfig(prev => ({
                        ...prev,
                        scanning: { ...prev.scanning, renegotiation: e.target.checked }
                      }))}
                      className="rounded"
                    />
                    <div className="flex-1">
                      <div className="flex items-center space-x-2">
                        <RefreshCw className="w-4 h-4" />
                        <label htmlFor="renegotiation" className="font-medium text-sm cursor-pointer">
                          TLS Renegotiation
                        </label>
                        <Badge variant="outline" className="text-xs border-yellow-200 text-yellow-700">
                          medium
                        </Badge>
                      </div>
                      <p className="text-xs text-gray-500 mt-1">Test secure renegotiation support</p>
                    </div>
                  </div>

                  <div className="flex items-center space-x-3 p-3 border rounded-lg">
                    <input
                      type="checkbox"
                      id="resumption"
                      checked={config.scanning.resumption}
                      onChange={(e) => setConfig(prev => ({
                        ...prev,
                        scanning: { ...prev.scanning, resumption: e.target.checked }
                      }))}
                      className="rounded"
                    />
                    <div className="flex-1">
                      <div className="flex items-center space-x-2">
                        <Timer className="w-4 h-4" />
                        <label htmlFor="resumption" className="font-medium text-sm cursor-pointer">
                          Session Resumption
                        </label>
                        <Badge variant="outline" className="text-xs border-blue-200 text-blue-700">
                          low
                        </Badge>
                      </div>
                      <p className="text-xs text-gray-500 mt-1">Test session resumption mechanisms</p>
                    </div>
                  </div>

                  <div className="flex items-center space-x-3 p-3 border rounded-lg">
                    <input
                      type="checkbox"
                      id="early-data"
                      checked={config.scanning.earlyData}
                      onChange={(e) => setConfig(prev => ({
                        ...prev,
                        scanning: { ...prev.scanning, earlyData: e.target.checked }
                      }))}
                      className="rounded"
                    />
                    <div className="flex-1">
                      <div className="flex items-center space-x-2">
                        <FastForward className="w-4 h-4" />
                        <label htmlFor="early-data" className="font-medium text-sm cursor-pointer">
                          TLS 1.3 Early Data
                        </label>
                        <Badge variant="outline" className="text-xs border-blue-200 text-blue-700">
                          low
                        </Badge>
                      </div>
                      <p className="text-xs text-gray-500 mt-1">Test TLS 1.3 0-RTT support</p>
                    </div>
                  </div>

                  <div className="flex items-center space-x-3 p-3 border rounded-lg">
                    <input
                      type="checkbox"
                      id="fallback-scsv"
                      checked={config.scanning.fallbackScsv}
                      onChange={(e) => setConfig(prev => ({
                        ...prev,
                        scanning: { ...prev.scanning, fallbackScsv: e.target.checked }
                      }))}
                      className="rounded"
                    />
                    <div className="flex-1">
                      <div className="flex items-center space-x-2">
                        <ShieldOff className="w-4 h-4" />
                        <label htmlFor="fallback-scsv" className="font-medium text-sm cursor-pointer">
                          TLS Fallback SCSV
                        </label>
                        <Badge variant="outline" className="text-xs border-yellow-200 text-yellow-700">
                          medium
                        </Badge>
                      </div>
                      <p className="text-xs text-gray-500 mt-1">Test TLS_FALLBACK_SCSV support</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Certificate Configuration */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Certificate className="w-5 h-5" />
                  <span>Certificate Validation</span>
                </CardTitle>
                <CardDescription>
                  Configure certificate trust stores and validation
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium">Trust Stores</label>
                  <div className="grid gap-2">
                    {Object.entries(TRUST_STORES).map(([key, store]) => (
                      <div key={key} className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          id={`trust-${key}`}
                          checked={config.certificates.trustStores.includes(key)}
                          onChange={(e) => {
                            if (e.target.checked) {
                              setConfig(prev => ({
                                ...prev,
                                certificates: {
                                  ...prev.certificates,
                                  trustStores: [...prev.certificates.trustStores, key]
                                }
                              }));
                            } else {
                              setConfig(prev => ({
                                ...prev,
                                certificates: {
                                  ...prev.certificates,
                                  trustStores: prev.certificates.trustStores.filter(t => t !== key)
                                }
                              }));
                            }
                          }}
                          className="rounded"
                        />
                        <label htmlFor={`trust-${key}`} className="text-sm">
                          <span className="font-medium">{store.name}</span>
                          <span className="text-gray-500 ml-2">{store.description}</span>
                        </label>
                      </div>
                    ))}
                  </div>
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">Custom CA File</label>
                  <Input
                    placeholder="/path/to/ca-bundle.pem"
                    value={config.certificates.customCaFile}
                    onChange={(e) => setConfig(prev => ({
                      ...prev,
                      certificates: { ...prev.certificates, customCaFile: e.target.value }
                    }))}
                  />
                </div>

                <div className="space-y-3">
                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="cert-info-all"
                      checked={config.certificates.certInfoAll}
                      onChange={(e) => setConfig(prev => ({
                        ...prev,
                        certificates: { ...prev.certificates, certInfoAll: e.target.checked }
                      }))}
                      className="rounded"
                    />
                    <label htmlFor="cert-info-all" className="text-sm font-medium">
                      Detailed certificate information
                    </label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="ocsp-stapling"
                      checked={config.certificates.ocspStapling}
                      onChange={(e) => setConfig(prev => ({
                        ...prev,
                        certificates: { ...prev.certificates, ocspStapling: e.target.checked }
                      }))}
                      className="rounded"
                    />
                    <label htmlFor="ocsp-stapling" className="text-sm font-medium">
                      Check OCSP stapling
                    </label>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Performance Configuration */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Gauge className="w-5 h-5" />
                  <span>Performance & Advanced</span>
                </CardTitle>
                <CardDescription>
                  Configure scan performance and advanced options
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Concurrent Connections</label>
                    <Input
                      type="number"
                      min="1"
                      max="50"
                      value={config.performance.concurrentConnections}
                      onChange={(e) => setConfig(prev => ({
                        ...prev,
                        performance: { ...prev.performance, concurrentConnections: parseInt(e.target.value) || 5 }
                      }))}
                    />
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Concurrent Scans</label>
                    <Input
                      type="number"
                      min="1"
                      max="10"
                      value={config.performance.concurrentScans}
                      onChange={(e) => setConfig(prev => ({
                        ...prev,
                        performance: { ...prev.performance, concurrentScans: parseInt(e.target.value) || 1 }
                      }))}
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">HTTPS Tunnel</label>
                  <Input
                    placeholder="proxy.example.com:8080"
                    value={config.performance.httpsTunnel}
                    onChange={(e) => setConfig(prev => ({
                      ...prev,
                      performance: { ...prev.performance, httpsTunnel: e.target.value }
                    }))}
                  />
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">Client Certificate</label>
                  <Input
                    placeholder="/path/to/client.pem"
                    value={config.performance.clientCertificate}
                    onChange={(e) => setConfig(prev => ({
                      ...prev,
                      performance: { ...prev.performance, clientCertificate: e.target.value }
                    }))}
                  />
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">Client Private Key</label>
                  <Input
                    placeholder="/path/to/client.key"
                    value={config.performance.clientKey}
                    onChange={(e) => setConfig(prev => ({
                      ...prev,
                      performance: { ...prev.performance, clientKey: e.target.value }
                    }))}
                  />
                </div>

                <div className="space-y-3">
                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="slow-connection"
                      checked={config.performance.slowConnection}
                      onChange={(e) => setConfig(prev => ({
                        ...prev,
                        performance: { ...prev.performance, slowConnection: e.target.checked }
                      }))}
                      className="rounded"
                    />
                    <label htmlFor="slow-connection" className="text-sm font-medium">
                      Slow connection mode
                    </label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="mozilla-policy"
                      checked={config.advanced.checkAgainstMozillaPolicy}
                      onChange={(e) => setConfig(prev => ({
                        ...prev,
                        advanced: { ...prev.advanced, checkAgainstMozillaPolicy: e.target.checked }
                      }))}
                      className="rounded"
                    />
                    <label htmlFor="mozilla-policy" className="text-sm font-medium">
                      Check against Mozilla SSL Configuration
                    </label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="update-trust-stores"
                      checked={config.advanced.updateTrustStores}
                      onChange={(e) => setConfig(prev => ({
                        ...prev,
                        advanced: { ...prev.advanced, updateTrustStores: e.target.checked }
                      }))}
                      className="rounded"
                    />
                    <label htmlFor="update-trust-stores" className="text-sm font-medium">
                      Update trust stores before scanning
                    </label>
                  </div>
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">Output Format</label>
                  <Select 
                    value={config.output.format} 
                    onValueChange={(value: any) => setConfig(prev => ({
                      ...prev,
                      output: { ...prev.output, format: value }
                    }))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="console">Console Output</SelectItem>
                      <SelectItem value="json">JSON Format</SelectItem>
                      <SelectItem value="xml">XML Format</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {config.output.format === 'json' && (
                  <div className="space-y-2">
                    <label className="text-sm font-medium">JSON Output File</label>
                    <Input
                      placeholder="/tmp/sslyze.json"
                      value={config.output.jsonFile}
                      onChange={(e) => setConfig(prev => ({
                        ...prev,
                        output: { ...prev.output, jsonFile: e.target.value }
                      }))}
                    />
                  </div>
                )}

                {config.output.format === 'xml' && (
                  <div className="space-y-2">
                    <label className="text-sm font-medium">XML Output File</label>
                    <Input
                      placeholder="/tmp/sslyze.xml"
                      value={config.output.xmlFile}
                      onChange={(e) => setConfig(prev => ({
                        ...prev,
                        output: { ...prev.output, xmlFile: e.target.value }
                      }))}
                    />
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Execution Controls */}
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <Button
                    onClick={startExecution}
                    disabled={!isConnected || isRunning}
                    size="lg"
                    className="bg-purple-600 hover:bg-purple-700"
                  >
                    <Play className="w-4 h-4 mr-2" />
                    Start SSLyze Scan
                  </Button>
                  
                  {isRunning && (
                    <Button
                      onClick={stopExecution}
                      variant="outline"
                      size="lg"
                    >
                      <Square className="w-4 h-4 mr-2" />
                      Stop
                    </Button>
                  )}
                </div>

                <div className="flex items-center space-x-2">
                  {isRunning && (
                    <div className="flex items-center space-x-2">
                      <Activity className="w-4 h-4 text-purple-600 animate-pulse" />
                      <span className="text-sm text-purple-600">Scanning...</span>
                    </div>
                  )}
                  <Badge variant="outline">
                    {scanResults.vulnerabilities?.length || 0} vulnerabilities
                  </Badge>
                </div>
              </div>

              {isRunning && (
                <div className="mt-4">
                  <div className="flex items-center justify-between text-sm text-gray-600 mb-2">
                    <span>Scan Progress</span>
                    <span>{progress.toFixed(1)}%</span>
                  </div>
                  <Progress value={progress} className="h-2" />
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="output" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Terminal className="w-5 h-5" />
                <span>Real-time Output</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="bg-black rounded-lg p-4 h-96 overflow-y-auto font-mono text-sm">
                {output.length === 0 ? (
                  <div className="text-gray-500 italic">
                    No output yet. Start SSLyze scan to see analysis progress.
                  </div>
                ) : (
                  output.map((line, index) => (
                    <div key={index} className={cn(
                      "text-green-400",
                      line.includes('VULNERABLE') && "text-red-400 font-bold",
                      line.includes('WARNING') && "text-yellow-400",
                      line.includes('ERROR') && "text-red-400",
                      line.includes('OK') && "text-green-400",
                      line.includes('INFO') && "text-blue-400"
                    )}>
                      {line}
                    </div>
                  ))
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="results" className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold">SSL/TLS Analysis Results</h3>
            {(results.length > 0 || scanResults.summary) && (
              <Button onClick={exportResults} variant="outline">
                <Download className="w-4 h-4 mr-2" />
                Export Results
              </Button>
            )}
          </div>

          {!scanResults.summary && results.length === 0 ? (
            <Card>
              <CardContent className="pt-6">
                <div className="text-center py-8">
                  <Scan className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500">No scan results yet</p>
                  <p className="text-sm text-gray-400">Start a scan to see SSL/TLS analysis</p>
                </div>
              </CardContent>
            </Card>
          ) : (
            <div className="space-y-6">
              {/* Scan Summary */}
              {scanResults.summary && (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2">
                      <BarChart3 className="w-5 h-5" />
                      <span>Scan Summary</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                      <div className="text-center">
                        <div className="text-2xl font-bold text-blue-600">
                          {scanResults.summary.targetsScanned || 0}
                        </div>
                        <div className="text-sm text-gray-600">Targets Scanned</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-green-600">
                          {scanResults.summary.vulnerabilitiesFound || 0}
                        </div>
                        <div className="text-sm text-gray-600">Vulnerabilities</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-purple-600">
                          {scanResults.summary.certificatesAnalyzed || 0}
                        </div>
                        <div className="text-sm text-gray-600">Certificates</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-orange-600">
                          {scanResults.summary.cipherSuitesTested || 0}
                        </div>
                        <div className="text-sm text-gray-600">Cipher Suites</div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Vulnerabilities Found */}
              {scanResults.vulnerabilities && scanResults.vulnerabilities.length > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2">
                      <ShieldAlert className="w-5 h-5" />
                      <span>Vulnerabilities Detected</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {scanResults.vulnerabilities.map((vuln: any, index: number) => (
                        <div key={index} className={cn(
                          "p-4 rounded-lg border-l-4",
                          vuln.severity === 'critical' && "border-red-500 bg-red-50",
                          vuln.severity === 'high' && "border-orange-500 bg-orange-50",
                          vuln.severity === 'medium' && "border-yellow-500 bg-yellow-50",
                          vuln.severity === 'low' && "border-blue-500 bg-blue-50"
                        )}>
                          <div className="flex items-start justify-between">
                            <div className="flex-1">
                              <div className="flex items-center space-x-2 mb-2">
                                <h4 className="font-semibold">{vuln.name}</h4>
                                <Badge variant={vuln.severity === 'critical' ? 'destructive' : 'outline'}>
                                  {vuln.severity.toUpperCase()}
                                </Badge>
                                {vuln.target && (
                                  <Badge variant="outline" className="text-xs">
                                    {vuln.target}
                                  </Badge>
                                )}
                              </div>
                              <p className="text-sm text-gray-600 mb-2">{vuln.description}</p>
                              {vuln.recommendation && (
                                <p className="text-sm text-blue-600">
                                  <strong>Recommendation:</strong> {vuln.recommendation}
                                </p>
                              )}
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Certificate Information */}
              {scanResults.certificates && scanResults.certificates.length > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2">
                      <Certificate className="w-5 h-5" />
                      <span>Certificate Analysis</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {scanResults.certificates.map((cert: any, index: number) => (
                        <div key={index} className="border rounded-lg p-4">
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                              <h4 className="font-semibold mb-2">Certificate Details</h4>
                              <div className="space-y-1 text-sm">
                                <div><span className="text-gray-600">Subject:</span> {cert.subject}</div>
                                <div><span className="text-gray-600">Issuer:</span> {cert.issuer}</div>
                                <div><span className="text-gray-600">Valid From:</span> {cert.validFrom}</div>
                                <div><span className="text-gray-600">Valid Until:</span> {cert.validUntil}</div>
                                <div><span className="text-gray-600">Serial:</span> <code className="text-xs">{cert.serial}</code></div>
                              </div>
                            </div>
                            <div>
                              <h4 className="font-semibold mb-2">Security Properties</h4>
                              <div className="space-y-1 text-sm">
                                <div><span className="text-gray-600">Key Size:</span> {cert.keySize} bits</div>
                                <div><span className="text-gray-600">Signature:</span> {cert.signatureAlgorithm}</div>
                                <div><span className="text-gray-600">Trust:</span> 
                                  <Badge variant={cert.trusted ? "default" : "destructive"} className="ml-2">
                                    {cert.trusted ? "Trusted" : "Untrusted"}
                                  </Badge>
                                </div>
                                {cert.ocspStapling !== undefined && (
                                  <div><span className="text-gray-600">OCSP Stapling:</span> 
                                    <Badge variant={cert.ocspStapling ? "default" : "outline"} className="ml-2">
                                      {cert.ocspStapling ? "Enabled" : "Disabled"}
                                    </Badge>
                                  </div>
                                )}
                              </div>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Protocol and Cipher Suite Results */}
              {scanResults.protocols && scanResults.protocols.length > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2">
                      <Network className="w-5 h-5" />
                      <span>Supported Protocols & Cipher Suites</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {scanResults.protocols.map((protocol: any, index: number) => (
                        <div key={index} className="border rounded-lg p-3">
                          <div className="flex items-center justify-between mb-2">
                            <h4 className="font-semibold">{protocol.name}</h4>
                            <Badge variant={protocol.supported ? "default" : "outline"}>
                              {protocol.supported ? "Supported" : "Not Supported"}
                            </Badge>
                          </div>
                          {protocol.cipherSuites && protocol.cipherSuites.length > 0 && (
                            <div className="text-sm">
                              <p className="text-gray-600 mb-1">
                                {protocol.cipherSuites.length} cipher suites supported
                              </p>
                              <div className="flex flex-wrap gap-1">
                                {protocol.cipherSuites.slice(0, 5).map((cipher: string, i: number) => (
                                  <Badge key={i} variant="outline" className="text-xs">
                                    {cipher}
                                  </Badge>
                                ))}
                                {protocol.cipherSuites.length > 5 && (
                                  <Badge variant="outline" className="text-xs">
                                    +{protocol.cipherSuites.length - 5} more
                                  </Badge>
                                )}
                              </div>
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Individual Results */}
              {results.length > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2">
                      <List className="w-5 h-5" />
                      <span>Detailed Test Results</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      {results.map((result, index) => (
                        <div key={index} className="flex items-center justify-between p-2 border rounded">
                          <div className="flex items-center space-x-2">
                            {result.status === 'PASS' && <CheckCircle className="w-4 h-4 text-green-600" />}
                            {result.status === 'WARN' && <AlertTriangle className="w-4 h-4 text-yellow-600" />}
                            {result.status === 'FAIL' && <XCircle className="w-4 h-4 text-red-600" />}
                            <span className="text-sm">{result.test}</span>
                          </div>
                          <div className="flex items-center space-x-2">
                            <span className="text-sm text-gray-600">{result.result}</span>
                            {result.target && (
                              <Badge variant="outline" className="text-xs">
                                {result.target}
                              </Badge>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}
            </div>
          )}
        </TabsContent>

        <TabsContent value="command" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Code className="w-5 h-5" />
                <span>Generated Command</span>
              </CardTitle>
              <CardDescription>
                Review and copy the SSLyze command
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="bg-gray-900 rounded-lg p-4">
                  <code className="text-green-400 font-mono text-sm whitespace-pre-wrap">
                    {generateCommand()}
                  </code>
                </div>
                <div className="flex space-x-2">
                  <Button
                    variant="outline"
                    onClick={() => {
                      navigator.clipboard.writeText(generateCommand());
                      toast.success('Command copied to clipboard');
                    }}
                  >
                    <Copy className="w-4 h-4 mr-2" />
                    Copy Command
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* SSLyze Plugin Reference */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Book className="w-5 h-5" />
                <span>SSLyze Plugin Reference</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {Object.entries(SSLYZE_PLUGINS).map(([key, plugin]) => (
                  <div key={key} className="flex items-start justify-between p-3 border rounded">
                    <div className="flex-1">
                      <div className="font-semibold text-sm">{plugin.name}</div>
                      <div className="text-xs text-gray-500 mt-1">{plugin.description}</div>
                      <div className="text-xs text-gray-400 mt-1">Category: {plugin.category}</div>
                    </div>
                    <Badge variant={plugin.severity === 'critical' ? 'destructive' : 'outline'} className="text-xs">
                      {plugin.severity}
                    </Badge>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}