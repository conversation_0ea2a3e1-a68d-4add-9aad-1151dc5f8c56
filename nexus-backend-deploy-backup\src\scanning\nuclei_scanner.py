#!/usr/bin/env python3
"""
Nuclei Scanner Integration for NexusScan
Fast vulnerability scanner using Nuclei templates
"""

import asyncio
import json
import logging
import subprocess
import yaml
from datetime import datetime
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass, field
from enum import Enum
import os
import tempfile

# Note: ToolResult, ToolStatus functionality handled by dataclasses below
# from .tool_manager import ToolResult, ToolStatus

logger = logging.getLogger(__name__)


class NucleiSeverity(Enum):
    """Nuclei vulnerability severity levels"""
    INFO = "info"
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"
    UNKNOWN = "unknown"


class NucleiProtocol(Enum):
    """Nuclei supported protocols"""
    HTTP = "http"
    HTTPS = "https"
    DNS = "dns"
    TCP = "tcp"
    UDP = "udp"
    SSL = "ssl"
    WEBSOCKET = "websocket"
    WHOIS = "whois"
    FILE = "file"
    NETWORK = "network"
    HEADLESS = "headless"


@dataclass
class NucleiTemplate:
    """Nuclei template information"""
    id: str
    name: str
    author: List[str]
    tags: List[str]
    description: str
    severity: NucleiSeverity
    protocol: NucleiProtocol
    references: List[str] = field(default_factory=list)
    classification: Dict[str, Any] = field(default_factory=dict)
    metadata: Dict[str, Any] = field(default_factory=dict)
    file_path: str = ""


@dataclass
class NucleiFinding:
    """Nuclei vulnerability finding"""
    template_id: str
    template_name: str
    template_author: List[str]
    template_tags: List[str]
    template_description: str
    severity: NucleiSeverity
    protocol: NucleiProtocol
    host: str
    matched_at: str
    extracted_results: List[str] = field(default_factory=list)
    ip: str = ""
    timestamp: datetime = field(default_factory=datetime.now)
    curl_command: str = ""
    matcher_status: bool = True
    matcher_name: str = ""
    classification: Dict[str, Any] = field(default_factory=dict)
    interaction: Dict[str, Any] = field(default_factory=dict)
    request: str = ""
    response: str = ""


@dataclass
class NucleiScanRequest:
    """Nuclei scan request configuration"""
    targets: List[str]
    templates: List[str] = field(default_factory=list)  # Template paths or IDs
    template_tags: List[str] = field(default_factory=list)  # Filter by tags
    template_authors: List[str] = field(default_factory=list)  # Filter by authors
    severity_filter: List[NucleiSeverity] = field(default_factory=list)
    protocols: List[NucleiProtocol] = field(default_factory=list)
    exclude_templates: List[str] = field(default_factory=list)
    exclude_tags: List[str] = field(default_factory=list)
    include_tags: List[str] = field(default_factory=list)
    template_directory: Optional[str] = None
    custom_headers: Dict[str, str] = field(default_factory=dict)
    user_agent: str = "NexusScan-Nuclei-Scanner"
    timeout: int = 10  # Request timeout in seconds
    retries: int = 1
    rate_limit: int = 150  # Requests per second
    bulk_size: int = 25  # Bulk HTTP request size
    concurrent_templates: int = 25
    concurrent_hosts: int = 50
    follow_redirects: bool = False
    max_redirects: int = 10
    disable_clustering: bool = False
    passive_scan: bool = False
    force_http2: bool = False
    force_http1: bool = False
    max_host_error: int = 30
    track_error: bool = True
    output_format: str = "jsonl"  # json, jsonl, sarif, markdown
    include_response: bool = False
    include_request: bool = False
    store_response: bool = False
    store_response_dir: str = ""
    proxy_url: Optional[str] = None
    proxy_socks_url: Optional[str] = None
    random_agent: bool = False
    custom_config_dir: Optional[str] = None
    system_resolvers: bool = True
    offline_scan: bool = False
    scan_all_ips: bool = False
    ip_version: str = "4"  # 4, 6, or auto


@dataclass
class NucleiScanResult:
    """Nuclei scan execution result"""
    request: NucleiScanRequest
    findings: List[NucleiFinding]
    templates_loaded: int
    templates_executed: int
    targets_scanned: int
    requests_made: int
    matched_templates: int
    scan_duration: float
    start_time: datetime
    end_time: datetime
    command_line: str
    nuclei_version: str
    raw_output: str = ""
    errors: List[str] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)
    statistics: Dict[str, Any] = field(default_factory=dict)


class NucleiScanner:
    """Nuclei integration for fast vulnerability scanning"""

    def __init__(self, 
                 nuclei_path: str = "nuclei",
                 templates_directory: str = "./nuclei-templates",
                 default_timeout: int = 300,
                 max_concurrent_scans: int = 3):
        """Initialize Nuclei scanner
        
        Args:
            nuclei_path: Path to nuclei binary
            templates_directory: Path to nuclei templates directory
            default_timeout: Default scan timeout in seconds
            max_concurrent_scans: Maximum concurrent scans
        """
        self.nuclei_path = nuclei_path
        self.templates_directory = templates_directory
        self.default_timeout = default_timeout
        self.max_concurrent_scans = max_concurrent_scans
        self.logger = logging.getLogger(__name__)
        
        # Validate nuclei installation
        self._validate_nuclei_installation()
        
        # Template cache
        self.template_cache: Dict[str, NucleiTemplate] = {}
        self.template_cache_loaded = False
        
        # Statistics
        self.stats = {
            "scans_executed": 0,
            "templates_executed": 0,
            "findings_discovered": 0,
            "targets_scanned": 0,
            "total_scan_time": 0.0,
            "vulnerability_types": {}
        }

    async def scan(self, request: NucleiScanRequest) -> NucleiScanResult:
        """Execute Nuclei vulnerability scan
        
        Args:
            request: Nuclei scan configuration
            
        Returns:
            Nuclei scan result
            
        Raises:
            RuntimeError: If scan execution fails
        """
        start_time = datetime.now()
        
        try:
            # Build nuclei command
            command, temp_files = await self._build_nuclei_command(request)
            
            self.logger.info(f"Executing Nuclei scan: {' '.join(command[:10])}...")
            
            # Execute nuclei
            process = await asyncio.create_subprocess_exec(
                *command,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            # Wait for completion with timeout
            try:
                stdout, stderr = await asyncio.wait_for(
                    process.communicate(),
                    timeout=self.default_timeout
                )
            except asyncio.TimeoutError:
                process.kill()
                raise RuntimeError("Nuclei scan timed out")
            finally:
                # Cleanup temporary files
                await self._cleanup_temp_files(temp_files)
            
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            # Check exit code
            if process.returncode != 0:
                error_message = stderr.decode('utf-8', errors='ignore')
                self.logger.error(f"Nuclei scan failed: {error_message}")
                # Nuclei may return non-zero on some warnings, check if we got output
                if not stdout:
                    raise RuntimeError(f"Nuclei scan failed with exit code {process.returncode}: {error_message}")
            
            # Parse results
            raw_output = stdout.decode('utf-8', errors='ignore')
            findings = self._parse_nuclei_output(raw_output, request.output_format)
            
            # Extract statistics from stderr
            stats = self._parse_nuclei_stats(stderr.decode('utf-8', errors='ignore'))
            
            # Build result
            result = NucleiScanResult(
                request=request,
                findings=findings,
                templates_loaded=stats.get("templates_loaded", 0),
                templates_executed=stats.get("templates_executed", 0),
                targets_scanned=len(request.targets),
                requests_made=stats.get("requests_made", 0),
                matched_templates=len(findings),
                scan_duration=duration,
                start_time=start_time,
                end_time=end_time,
                command_line=' '.join(command),
                nuclei_version=await self._get_nuclei_version(),
                raw_output=raw_output,
                statistics=stats
            )
            
            # Update statistics
            self._update_statistics(result)
            
            self.logger.info(f"Nuclei scan completed: {len(findings)} findings in {duration:.2f}s")
            return result
            
        except Exception as e:
            self.logger.error(f"Nuclei scan failed: {e}")
            raise

    async def quick_scan(self, targets: List[str], tags: List[str] = None) -> NucleiScanResult:
        """Perform quick vulnerability scan with common templates
        
        Args:
            targets: List of target URLs/hosts
            tags: Optional template tags to filter by
            
        Returns:
            Nuclei scan result
        """
        if not tags:
            tags = ["cve", "exposure", "misconfiguration", "takeover"]
        
        request = NucleiScanRequest(
            targets=targets,
            template_tags=tags,
            severity_filter=[NucleiSeverity.MEDIUM, NucleiSeverity.HIGH, NucleiSeverity.CRITICAL],
            rate_limit=300,
            concurrent_templates=50,
            timeout=5
        )
        
        return await self.scan(request)

    async def comprehensive_scan(self, targets: List[str]) -> NucleiScanResult:
        """Perform comprehensive vulnerability scan with all templates
        
        Args:
            targets: List of target URLs/hosts
            
        Returns:
            Nuclei scan result
        """
        request = NucleiScanRequest(
            targets=targets,
            template_directory=self.templates_directory,
            rate_limit=150,
            concurrent_templates=25,
            timeout=15,
            include_response=True,
            store_response=True
        )
        
        return await self.scan(request)

    async def cve_scan(self, targets: List[str], year: Optional[str] = None) -> NucleiScanResult:
        """Perform CVE-focused vulnerability scan
        
        Args:
            targets: List of target URLs/hosts
            year: Optional year to filter CVEs (e.g., "2023")
            
        Returns:
            Nuclei scan result
        """
        tags = ["cve"]
        if year:
            tags.append(f"cve{year}")
        
        request = NucleiScanRequest(
            targets=targets,
            template_tags=tags,
            severity_filter=[NucleiSeverity.HIGH, NucleiSeverity.CRITICAL],
            rate_limit=100,
            timeout=10
        )
        
        return await self.scan(request)

    async def web_scan(self, targets: List[str]) -> NucleiScanResult:
        """Perform web application vulnerability scan
        
        Args:
            targets: List of target URLs
            
        Returns:
            Nuclei scan result
        """
        web_tags = [
            "web", "xss", "sqli", "lfi", "rfi", "ssti", "ssrf", 
            "redirect", "rce", "traversal", "injection"
        ]
        
        request = NucleiScanRequest(
            targets=targets,
            template_tags=web_tags,
            protocols=[NucleiProtocol.HTTP, NucleiProtocol.HTTPS],
            follow_redirects=True,
            max_redirects=5,
            timeout=10
        )
        
        return await self.scan(request)

    async def load_template_cache(self):
        """Load template information into cache"""
        if self.template_cache_loaded:
            return
        
        try:
            # Get template list from nuclei
            command = [self.nuclei_path, "-tl", "-json"]
            
            process = await asyncio.create_subprocess_exec(
                *command,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await process.communicate()
            
            if process.returncode == 0:
                output = stdout.decode('utf-8', errors='ignore')
                for line in output.strip().split('\n'):
                    if line.strip():
                        try:
                            template_info = json.loads(line)
                            template = self._parse_template_info(template_info)
                            self.template_cache[template.id] = template
                        except json.JSONDecodeError:
                            continue
            
            self.template_cache_loaded = True
            self.logger.info(f"Loaded {len(self.template_cache)} templates into cache")
            
        except Exception as e:
            self.logger.error(f"Failed to load template cache: {e}")

    async def get_template_info(self, template_id: str) -> Optional[NucleiTemplate]:
        """Get information about a specific template
        
        Args:
            template_id: Template ID to get info for
            
        Returns:
            Template information or None if not found
        """
        if not self.template_cache_loaded:
            await self.load_template_cache()
        
        return self.template_cache.get(template_id)

    async def search_templates(self, 
                             tags: List[str] = None,
                             authors: List[str] = None,
                             severity: List[NucleiSeverity] = None,
                             protocols: List[NucleiProtocol] = None) -> List[NucleiTemplate]:
        """Search templates by criteria
        
        Args:
            tags: Template tags to search for
            authors: Template authors to search for
            severity: Severity levels to filter by
            protocols: Protocols to filter by
            
        Returns:
            List of matching templates
        """
        if not self.template_cache_loaded:
            await self.load_template_cache()
        
        results = []
        
        for template in self.template_cache.values():
            # Check tags
            if tags and not any(tag in template.tags for tag in tags):
                continue
            
            # Check authors
            if authors and not any(author in template.author for author in authors):
                continue
            
            # Check severity
            if severity and template.severity not in severity:
                continue
            
            # Check protocols
            if protocols and template.protocol not in protocols:
                continue
            
            results.append(template)
        
        return results

    def _validate_nuclei_installation(self):
        """Validate that Nuclei is installed and accessible"""
        try:
            result = subprocess.run(
                [self.nuclei_path, "-version"],
                capture_output=True,
                text=True,
                timeout=10
            )
            
            if result.returncode != 0:
                raise RuntimeError("Nuclei is not properly installed")
            
            self.logger.info(f"Nuclei version: {result.stdout.strip()}")
            
        except FileNotFoundError:
            raise RuntimeError(f"Nuclei not found at path: {self.nuclei_path}")
        except subprocess.TimeoutExpired:
            raise RuntimeError("Nuclei version check timed out")

    async def _build_nuclei_command(self, request: NucleiScanRequest) -> tuple:
        """Build Nuclei command line arguments
        
        Args:
            request: Nuclei scan request
            
        Returns:
            Tuple of (command list, temporary files list)
        """
        command = [self.nuclei_path]
        temp_files = []
        
        # Create temporary target file
        if request.targets:
            target_file = tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.txt')
            for target in request.targets:
                target_file.write(f"{target}\n")
            target_file.close()
            temp_files.append(target_file.name)
            command.extend(["-l", target_file.name])
        
        # Templates
        if request.templates:
            command.extend(["-t", ",".join(request.templates)])
        elif request.template_directory:
            command.extend(["-t", request.template_directory])
        
        # Template filtering
        if request.template_tags:
            command.extend(["-tags", ",".join(request.template_tags)])
        
        if request.template_authors:
            command.extend(["-author", ",".join(request.template_authors)])
        
        if request.severity_filter:
            severities = [s.value for s in request.severity_filter]
            command.extend(["-severity", ",".join(severities)])
        
        if request.protocols:
            protocols = [p.value for p in request.protocols]
            command.extend(["-protocols", ",".join(protocols)])
        
        # Exclusions
        if request.exclude_templates:
            command.extend(["-exclude-templates", ",".join(request.exclude_templates)])
        
        if request.exclude_tags:
            command.extend(["-exclude-tags", ",".join(request.exclude_tags)])
        
        if request.include_tags:
            command.extend(["-include-tags", ",".join(request.include_tags)])
        
        # HTTP options
        if request.custom_headers:
            for key, value in request.custom_headers.items():
                command.extend(["-H", f"{key}: {value}"])
        
        if request.user_agent:
            command.extend(["-H", f"User-Agent: {request.user_agent}"])
        
        if request.timeout:
            command.extend(["-timeout", str(request.timeout)])
        
        if request.retries:
            command.extend(["-retries", str(request.retries)])
        
        if request.rate_limit:
            command.extend(["-rate-limit", str(request.rate_limit)])
        
        if request.bulk_size:
            command.extend(["-bulk-size", str(request.bulk_size)])
        
        if request.concurrent_templates:
            command.extend(["-c", str(request.concurrent_templates)])
        
        if request.concurrent_hosts:
            command.extend(["-parallel", str(request.concurrent_hosts)])
        
        # Redirect options
        if request.follow_redirects:
            command.append("-follow-redirects")
        
        if request.max_redirects:
            command.extend(["-max-redirects", str(request.max_redirects)])
        
        # Advanced options
        if request.disable_clustering:
            command.append("-disable-clustering")
        
        if request.passive_scan:
            command.append("-passive")
        
        if request.force_http2:
            command.append("-force-http2")
        
        if request.force_http1:
            command.append("-disable-http2")
        
        if request.max_host_error:
            command.extend(["-max-host-error", str(request.max_host_error)])
        
        if request.track_error:
            command.append("-track-error")
        
        # Output options
        if request.output_format == "json":
            command.append("-json")
        elif request.output_format == "jsonl":
            command.append("-jsonl")
        elif request.output_format == "sarif":
            command.append("-sarif-export")
        
        if request.include_response:
            command.append("-include-response")
        
        if request.include_request:
            command.append("-include-request")
        
        if request.store_response:
            command.append("-store-resp")
            if request.store_response_dir:
                command.extend(["-store-resp-dir", request.store_response_dir])
        
        # Proxy options
        if request.proxy_url:
            command.extend(["-proxy-url", request.proxy_url])
        
        if request.proxy_socks_url:
            command.extend(["-proxy-socks-url", request.proxy_socks_url])
        
        # Misc options
        if request.random_agent:
            command.append("-random-agent")
        
        if request.custom_config_dir:
            command.extend(["-config-directory", request.custom_config_dir])
        
        if not request.system_resolvers:
            command.append("-disable-system-resolvers")
        
        if request.offline_scan:
            command.append("-offline")
        
        if request.scan_all_ips:
            command.append("-scan-all-ips")
        
        if request.ip_version != "4":
            command.extend(["-ip-version", request.ip_version])
        
        # Silent mode for clean output
        command.append("-silent")
        
        return command, temp_files

    def _parse_nuclei_output(self, output: str, format: str) -> List[NucleiFinding]:
        """Parse Nuclei output into findings
        
        Args:
            output: Raw output from Nuclei
            format: Output format (json, jsonl, etc.)
            
        Returns:
            List of vulnerability findings
        """
        findings = []
        
        try:
            if format in ["json", "jsonl"]:
                for line in output.strip().split('\n'):
                    if line.strip():
                        try:
                            finding_data = json.loads(line)
                            finding = self._parse_finding_json(finding_data)
                            if finding:
                                findings.append(finding)
                        except json.JSONDecodeError:
                            continue
            else:
                # Parse other formats if needed
                pass
        
        except Exception as e:
            self.logger.error(f"Failed to parse Nuclei output: {e}")
        
        return findings

    def _parse_finding_json(self, data: Dict[str, Any]) -> Optional[NucleiFinding]:
        """Parse a single finding from JSON data
        
        Args:
            data: JSON data for a finding
            
        Returns:
            NucleiFinding object or None if parsing fails
        """
        try:
            template_info = data.get('template', {})
            info = data.get('info', {})
            
            # Parse severity
            severity_str = info.get('severity', 'unknown').lower()
            severity = NucleiSeverity.UNKNOWN
            for sev in NucleiSeverity:
                if sev.value == severity_str:
                    severity = sev
                    break
            
            # Parse protocol
            protocol_str = template_info.get('protocol', 'http').lower()
            protocol = NucleiProtocol.HTTP
            for prot in NucleiProtocol:
                if prot.value == protocol_str:
                    protocol = prot
                    break
            
            # Parse timestamp
            timestamp = datetime.now()
            if 'timestamp' in data:
                try:
                    timestamp = datetime.fromisoformat(data['timestamp'].replace('Z', '+00:00'))
                except ValueError:
                    pass
            
            finding = NucleiFinding(
                template_id=template_info.get('id', ''),
                template_name=info.get('name', ''),
                template_author=info.get('author', []),
                template_tags=info.get('tags', []),
                template_description=info.get('description', ''),
                severity=severity,
                protocol=protocol,
                host=data.get('host', ''),
                matched_at=data.get('matched-at', ''),
                extracted_results=data.get('extracted-results', []),
                ip=data.get('ip', ''),
                timestamp=timestamp,
                curl_command=data.get('curl-command', ''),
                matcher_status=data.get('matcher-status', True),
                matcher_name=data.get('matcher-name', ''),
                classification=info.get('classification', {}),
                interaction=data.get('interaction', {}),
                request=data.get('request', ''),
                response=data.get('response', '')
            )
            
            return finding
            
        except Exception as e:
            self.logger.error(f"Failed to parse finding: {e}")
            return None

    def _parse_template_info(self, data: Dict[str, Any]) -> NucleiTemplate:
        """Parse template information from JSON
        
        Args:
            data: Template JSON data
            
        Returns:
            NucleiTemplate object
        """
        info = data.get('info', {})
        
        # Parse severity
        severity_str = info.get('severity', 'unknown').lower()
        severity = NucleiSeverity.UNKNOWN
        for sev in NucleiSeverity:
            if sev.value == severity_str:
                severity = sev
                break
        
        # Parse protocol
        protocol_str = data.get('protocol', 'http').lower()
        protocol = NucleiProtocol.HTTP
        for prot in NucleiProtocol:
            if prot.value == protocol_str:
                protocol = prot
                break
        
        return NucleiTemplate(
            id=data.get('id', ''),
            name=info.get('name', ''),
            author=info.get('author', []),
            tags=info.get('tags', []),
            description=info.get('description', ''),
            severity=severity,
            protocol=protocol,
            references=info.get('reference', []),
            classification=info.get('classification', {}),
            metadata=info.get('metadata', {}),
            file_path=data.get('path', '')
        )

    def _parse_nuclei_stats(self, stderr_output: str) -> Dict[str, Any]:
        """Parse statistics from Nuclei stderr output
        
        Args:
            stderr_output: stderr output from Nuclei
            
        Returns:
            Dictionary with scan statistics
        """
        stats = {}
        
        try:
            for line in stderr_output.split('\n'):
                if 'Templates loaded' in line:
                    # Extract number of templates loaded
                    import re
                    match = re.search(r'(\d+)', line)
                    if match:
                        stats['templates_loaded'] = int(match.group(1))
                
                elif 'Requests' in line and 'made' in line:
                    # Extract number of requests made
                    import re
                    match = re.search(r'(\d+)', line)
                    if match:
                        stats['requests_made'] = int(match.group(1))
        
        except Exception as e:
            self.logger.error(f"Failed to parse Nuclei stats: {e}")
        
        return stats

    async def _get_nuclei_version(self) -> str:
        """Get Nuclei version string
        
        Returns:
            Nuclei version
        """
        try:
            process = await asyncio.create_subprocess_exec(
                self.nuclei_path, "-version",
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await process.communicate()
            
            if process.returncode == 0:
                output = stdout.decode('utf-8', errors='ignore').strip()
                return output
            
        except Exception:
            pass
        
        return "unknown"

    async def _cleanup_temp_files(self, temp_files: List[str]):
        """Clean up temporary files
        
        Args:
            temp_files: List of temporary file paths to clean up
        """
        for temp_file in temp_files:
            try:
                os.unlink(temp_file)
            except OSError:
                pass

    def _update_statistics(self, result: NucleiScanResult):
        """Update scanner statistics
        
        Args:
            result: Scan result to process
        """
        self.stats["scans_executed"] += 1
        self.stats["templates_executed"] += result.templates_executed
        self.stats["findings_discovered"] += len(result.findings)
        self.stats["targets_scanned"] += result.targets_scanned
        self.stats["total_scan_time"] += result.scan_duration
        
        # Count vulnerability types
        for finding in result.findings:
            for tag in finding.template_tags:
                self.stats["vulnerability_types"][tag] = self.stats["vulnerability_types"].get(tag, 0) + 1

    def get_scanner_statistics(self) -> Dict[str, Any]:
        """Get scanner statistics
        
        Returns:
            Dictionary with scanner statistics
        """
        return self.stats.copy()

    def is_available(self) -> bool:
        """Check if Nuclei is available
        
        Returns:
            True if Nuclei is available
        """
        try:
            result = subprocess.run(
                [self.nuclei_path, "-version"],
                capture_output=True,
                timeout=5
            )
            return result.returncode == 0
        except:
            return False

    async def update_templates(self) -> bool:
        """Update Nuclei templates to latest version
        
        Returns:
            True if update was successful
        """
        try:
            process = await asyncio.create_subprocess_exec(
                self.nuclei_path, "-update-templates",
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await process.communicate()
            
            if process.returncode == 0:
                self.logger.info("Nuclei templates updated successfully")
                # Clear template cache to force reload
                self.template_cache.clear()
                self.template_cache_loaded = False
                return True
            else:
                self.logger.error(f"Failed to update templates: {stderr.decode()}")
                return False
                
        except Exception as e:
            self.logger.error(f"Template update failed: {e}")
            return False