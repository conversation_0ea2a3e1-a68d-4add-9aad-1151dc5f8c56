import { Menu, MenuItemConstructorOptions, BrowserWindow, shell, dialog, app } from 'electron';
import log from 'electron-log';

export class MenuBuilder {
  private mainWindow: BrowserWindow;
  private isDevelopment: boolean;

  constructor(mainWindow: BrowserWindow, isDevelopment: boolean = false) {
    this.mainWindow = mainWindow;
    this.isDevelopment = isDevelopment;
  }

  /**
   * Build application menu
   */
  buildMenu(): Menu {
    const template = process.platform === 'darwin' 
      ? this.buildDarwinTemplate() 
      : this.buildDefaultTemplate();

    const menu = Menu.buildFromTemplate(template);
    return menu;
  }

  /**
   * Build menu template for macOS
   */
  private buildDarwinTemplate(): MenuItemConstructorOptions[] {
    return [
      this.buildAppMenu(),
      this.buildFileMenu(),
      this.buildEditMenu(),
      this.buildViewMenu(),
      this.buildToolsMenu(),
      this.buildFerrariMenu(),
      this.buildWindowMenu(),
      this.buildHelpMenu()
    ];
  }

  /**
   * Build menu template for Windows/Linux
   */
  private buildDefaultTemplate(): MenuItemConstructorOptions[] {
    return [
      this.buildFileMenu(),
      this.buildEditMenu(),
      this.buildViewMenu(),
      this.buildToolsMenu(),
      this.buildFerrariMenu(),
      this.buildWindowMenu(),
      this.buildHelpMenu()
    ];
  }

  /**
   * Build App menu (macOS only)
   */
  private buildAppMenu(): MenuItemConstructorOptions {
    return {
      label: 'NexusScan',
      submenu: [
        {
          label: 'About NexusScan',
          click: () => this.showAboutDialog()
        },
        { type: 'separator' },
        {
          label: 'Preferences...',
          accelerator: 'Cmd+,',
          click: () => this.openSettings()
        },
        { type: 'separator' },
        {
          label: 'Services',
          role: 'services',
          submenu: []
        },
        { type: 'separator' },
        {
          label: 'Hide NexusScan',
          accelerator: 'Cmd+H',
          role: 'hide'
        },
        {
          label: 'Hide Others',
          accelerator: 'Cmd+Alt+H',
          role: 'hideOthers'
        },
        {
          label: 'Show All',
          role: 'unhide'
        },
        { type: 'separator' },
        {
          label: 'Quit',
          accelerator: 'Cmd+Q',
          click: () => app.quit()
        }
      ]
    };
  }

  /**
   * Build File menu
   */
  private buildFileMenu(): MenuItemConstructorOptions {
    const submenu: MenuItemConstructorOptions[] = [
      {
        label: 'New Campaign',
        accelerator: 'CmdOrCtrl+N',
        click: () => this.navigateTo('/campaigns/new')
      },
      {
        label: 'Open Campaign...',
        accelerator: 'CmdOrCtrl+O',
        click: () => this.openCampaign()
      },
      { type: 'separator' },
      {
        label: 'Import Configuration...',
        click: () => this.importConfiguration()
      },
      {
        label: 'Export Configuration...',
        click: () => this.exportConfiguration()
      },
      { type: 'separator' },
      {
        label: 'Export Report...',
        accelerator: 'CmdOrCtrl+E',
        click: () => this.exportReport()
      }
    ];

    // Add platform-specific items
    if (process.platform !== 'darwin') {
      submenu.push(
        { type: 'separator' },
        {
          label: 'Settings',
          accelerator: 'CmdOrCtrl+,',
          click: () => this.openSettings()
        },
        { type: 'separator' },
        {
          label: 'Exit',
          accelerator: 'CmdOrCtrl+Q',
          click: () => app.quit()
        }
      );
    }

    return {
      label: 'File',
      submenu
    };
  }

  /**
   * Build Edit menu
   */
  private buildEditMenu(): MenuItemConstructorOptions {
    return {
      label: 'Edit',
      submenu: [
        {
          label: 'Undo',
          accelerator: 'CmdOrCtrl+Z',
          role: 'undo'
        },
        {
          label: 'Redo',
          accelerator: 'Shift+CmdOrCtrl+Z',
          role: 'redo'
        },
        { type: 'separator' },
        {
          label: 'Cut',
          accelerator: 'CmdOrCtrl+X',
          role: 'cut'
        },
        {
          label: 'Copy',
          accelerator: 'CmdOrCtrl+C',
          role: 'copy'
        },
        {
          label: 'Paste',
          accelerator: 'CmdOrCtrl+V',
          role: 'paste'
        },
        {
          label: 'Select All',
          accelerator: 'CmdOrCtrl+A',
          role: 'selectAll'
        },
        { type: 'separator' },
        {
          label: 'Find',
          accelerator: 'CmdOrCtrl+F',
          click: () => this.openSearch()
        }
      ]
    };
  }

  /**
   * Build View menu
   */
  private buildViewMenu(): MenuItemConstructorOptions {
    const submenu: MenuItemConstructorOptions[] = [
      {
        label: 'Dashboard',
        accelerator: 'CmdOrCtrl+1',
        click: () => this.navigateTo('/dashboard')
      },
      {
        label: 'Campaigns',
        accelerator: 'CmdOrCtrl+2',
        click: () => this.navigateTo('/campaigns')
      },
      {
        label: 'Reports',
        accelerator: 'CmdOrCtrl+3',
        click: () => this.navigateTo('/reports')
      },
      { type: 'separator' },
      {
        label: 'Toggle Terminal',
        accelerator: 'CmdOrCtrl+T',
        click: () => this.toggleTerminal()
      },
      {
        label: 'Toggle Sidebar',
        accelerator: 'CmdOrCtrl+B',
        click: () => this.toggleSidebar()
      },
      { type: 'separator' },
      {
        label: 'Reload',
        accelerator: 'CmdOrCtrl+R',
        click: () => this.mainWindow.webContents.reload()
      },
      {
        label: 'Force Reload',
        accelerator: 'CmdOrCtrl+Shift+R',
        click: () => this.mainWindow.webContents.reloadIgnoringCache()
      },
      { type: 'separator' },
      {
        label: 'Actual Size',
        accelerator: 'CmdOrCtrl+0',
        click: () => this.mainWindow.webContents.setZoomLevel(0)
      },
      {
        label: 'Zoom In',
        accelerator: 'CmdOrCtrl+=',
        click: () => this.zoomIn()
      },
      {
        label: 'Zoom Out',
        accelerator: 'CmdOrCtrl+-',
        click: () => this.zoomOut()
      },
      { type: 'separator' },
      {
        label: 'Toggle Fullscreen',
        accelerator: process.platform === 'darwin' ? 'Ctrl+Cmd+F' : 'F11',
        click: () => this.mainWindow.setFullScreen(!this.mainWindow.isFullScreen())
      }
    ];

    // Add developer tools in development
    if (this.isDevelopment) {
      submenu.push(
        { type: 'separator' },
        {
          label: 'Toggle Developer Tools',
          accelerator: process.platform === 'darwin' ? 'Alt+Cmd+I' : 'Ctrl+Shift+I',
          click: () => this.mainWindow.webContents.toggleDevTools()
        }
      );
    }

    return {
      label: 'View',
      submenu
    };
  }

  /**
   * Build Tools menu
   */
  private buildToolsMenu(): MenuItemConstructorOptions {
    return {
      label: 'Tools',
      submenu: [
        {
          label: 'Network Scanning',
          submenu: [
            {
              label: 'Nmap Scanner',
              accelerator: 'CmdOrCtrl+Shift+N',
              click: () => this.navigateTo('/tools/nmap')
            },
            {
              label: 'Masscan',
              click: () => this.navigateTo('/tools/masscan')
            },
            {
              label: 'Zmap',
              click: () => this.navigateTo('/tools/zmap')
            },
            { type: 'separator' },
            {
              label: 'SMB Enumeration',
              click: () => this.navigateTo('/tools/smb')
            }
          ]
        },
        {
          label: 'Web Application Testing',
          submenu: [
            {
              label: 'SQLMap',
              accelerator: 'CmdOrCtrl+Shift+S',
              click: () => this.navigateTo('/tools/sqlmap')
            },
            {
              label: 'Gobuster',
              click: () => this.navigateTo('/tools/gobuster')
            },
            {
              label: 'Nikto',
              click: () => this.navigateTo('/tools/nikto')
            },
            {
              label: 'WPScan',
              click: () => this.navigateTo('/tools/wpscan')
            },
            { type: 'separator' },
            {
              label: 'FFUF Fuzzer',
              click: () => this.navigateTo('/tools/ffuf')
            },
            {
              label: 'Burp Integration',
              click: () => this.navigateTo('/tools/burp')
            }
          ]
        },
        {
          label: 'Vulnerability Assessment',
          submenu: [
            {
              label: 'Nuclei Scanner',
              accelerator: 'CmdOrCtrl+Shift+U',
              click: () => this.navigateTo('/tools/nuclei')
            },
            {
              label: 'OpenVAS',
              click: () => this.navigateTo('/tools/openvas')
            }
          ]
        },
        {
          label: 'Password & Authentication',
          submenu: [
            {
              label: 'Hashcat',
              click: () => this.navigateTo('/tools/hashcat')
            },
            {
              label: 'John the Ripper',
              click: () => this.navigateTo('/tools/john')
            },
            {
              label: 'Hydra',
              click: () => this.navigateTo('/tools/hydra')
            }
          ]
        },
        {
          label: 'SSL/TLS Testing',
          submenu: [
            {
              label: 'TestSSL',
              click: () => this.navigateTo('/tools/testssl')
            },
            {
              label: 'SSLyze',
              click: () => this.navigateTo('/tools/sslyze')
            }
          ]
        },
        {
          label: 'Exploitation',
          submenu: [
            {
              label: 'Metasploit',
              click: () => this.navigateTo('/tools/metasploit')
            },
            {
              label: 'SearchSploit',
              click: () => this.navigateTo('/tools/searchsploit')
            }
          ]
        }
      ]
    };
  }

  /**
   * Build Ferrari AI menu
   */
  private buildFerrariMenu(): MenuItemConstructorOptions {
    return {
      label: 'Ferrari AI',
      submenu: [
        {
          label: 'Multi-Stage Orchestrator',
          accelerator: 'CmdOrCtrl+Shift+O',
          click: () => this.navigateTo('/ferrari/orchestrator')
        },
        {
          label: 'Creative Exploit Engine',
          accelerator: 'CmdOrCtrl+Shift+E',
          click: () => this.navigateTo('/ferrari/creative-exploits')
        },
        {
          label: 'Behavioral Analysis',
          accelerator: 'CmdOrCtrl+Shift+B',
          click: () => this.navigateTo('/ferrari/behavioral-analysis')
        },
        {
          label: 'AI Proxy Manager',
          accelerator: 'CmdOrCtrl+Shift+P',
          click: () => this.navigateTo('/ferrari/ai-proxy')
        },
        { type: 'separator' },
        {
          label: 'AI Capabilities Status',
          click: () => this.showAIStatus()
        },
        {
          label: 'Model Configuration',
          click: () => this.navigateTo('/settings/ai')
        }
      ]
    };
  }

  /**
   * Build Window menu
   */
  private buildWindowMenu(): MenuItemConstructorOptions {
    const submenu: MenuItemConstructorOptions[] = [
      {
        label: 'Minimize',
        accelerator: 'CmdOrCtrl+M',
        role: 'minimize'
      },
      {
        label: 'Close',
        accelerator: 'CmdOrCtrl+W',
        role: 'close'
      }
    ];

    if (process.platform === 'darwin') {
      submenu.push(
        { type: 'separator' },
        {
          label: 'Bring All to Front',
          role: 'front'
        }
      );
    } else {
      submenu.push(
        { type: 'separator' },
        {
          label: 'Cascade Windows',
          click: () => this.cascadeWindows()
        },
        {
          label: 'Tile Windows',
          click: () => this.tileWindows()
        }
      );
    }

    return {
      label: 'Window',
      submenu
    };
  }

  /**
   * Build Help menu
   */
  private buildHelpMenu(): MenuItemConstructorOptions {
    const submenu: MenuItemConstructorOptions[] = [
      {
        label: 'Documentation',
        click: () => shell.openExternal('https://docs.nexusscan.io')
      },
      {
        label: 'Keyboard Shortcuts',
        accelerator: 'CmdOrCtrl+?',
        click: () => this.showKeyboardShortcuts()
      },
      { type: 'separator' },
      {
        label: 'Check for Updates',
        click: () => this.checkForUpdates()
      },
      {
        label: 'Report Issue',
        click: () => shell.openExternal('https://github.com/nexusscan/desktop/issues')
      },
      { type: 'separator' },
      {
        label: 'Debug Information',
        click: () => this.showDebugInfo()
      }
    ];

    // Add About on Windows/Linux
    if (process.platform !== 'darwin') {
      submenu.push(
        { type: 'separator' },
        {
          label: 'About NexusScan',
          click: () => this.showAboutDialog()
        }
      );
    }

    return {
      label: 'Help',
      submenu
    };
  }

  /**
   * Navigate to specific route
   */
  private navigateTo(route: string): void {
    this.mainWindow.webContents.send('navigate', route);
  }

  /**
   * Show about dialog
   */
  private showAboutDialog(): void {
    dialog.showMessageBox(this.mainWindow, {
      type: 'info',
      title: 'About NexusScan Desktop',
      message: 'NexusScan Desktop',
      detail: `Version: ${app.getVersion()}\nPlatform: ${process.platform}\nElectron: ${process.versions.electron}\nNode.js: ${process.versions.node}\n\nProfessional Penetration Testing Platform\nPowered by AI and 22+ Security Tools`,
      buttons: ['OK']
    });
  }

  /**
   * Open settings
   */
  private openSettings(): void {
    this.navigateTo('/settings');
  }

  /**
   * Open campaign file
   */
  private async openCampaign(): Promise<void> {
    const result = await dialog.showOpenDialog(this.mainWindow, {
      title: 'Open Campaign',
      filters: [
        { name: 'NexusScan Campaign', extensions: ['nxcamp'] },
        { name: 'JSON Files', extensions: ['json'] },
        { name: 'All Files', extensions: ['*'] }
      ],
      properties: ['openFile']
    });

    if (!result.canceled && result.filePaths.length > 0) {
      this.mainWindow.webContents.send('open-campaign', result.filePaths[0]);
    }
  }

  /**
   * Import configuration
   */
  private async importConfiguration(): Promise<void> {
    const result = await dialog.showOpenDialog(this.mainWindow, {
      title: 'Import Configuration',
      filters: [
        { name: 'Configuration Files', extensions: ['json', 'yaml', 'yml'] },
        { name: 'All Files', extensions: ['*'] }
      ],
      properties: ['openFile']
    });

    if (!result.canceled && result.filePaths.length > 0) {
      this.mainWindow.webContents.send('import-config', result.filePaths[0]);
    }
  }

  /**
   * Export configuration
   */
  private async exportConfiguration(): Promise<void> {
    const result = await dialog.showSaveDialog(this.mainWindow, {
      title: 'Export Configuration',
      defaultPath: 'nexusscan-config.json',
      filters: [
        { name: 'JSON Files', extensions: ['json'] },
        { name: 'YAML Files', extensions: ['yaml', 'yml'] }
      ]
    });

    if (!result.canceled && result.filePath) {
      this.mainWindow.webContents.send('export-config', result.filePath);
    }
  }

  /**
   * Export report
   */
  private async exportReport(): Promise<void> {
    const result = await dialog.showSaveDialog(this.mainWindow, {
      title: 'Export Report',
      defaultPath: 'nexusscan-report.pdf',
      filters: [
        { name: 'PDF Files', extensions: ['pdf'] },
        { name: 'HTML Files', extensions: ['html'] },
        { name: 'JSON Files', extensions: ['json'] },
        { name: 'Excel Files', extensions: ['xlsx'] }
      ]
    });

    if (!result.canceled && result.filePath) {
      this.mainWindow.webContents.send('export-report', result.filePath);
    }
  }

  /**
   * Open search
   */
  private openSearch(): void {
    this.mainWindow.webContents.send('open-search');
  }

  /**
   * Toggle terminal visibility
   */
  private toggleTerminal(): void {
    this.mainWindow.webContents.send('toggle-terminal');
  }

  /**
   * Toggle sidebar visibility
   */
  private toggleSidebar(): void {
    this.mainWindow.webContents.send('toggle-sidebar');
  }

  /**
   * Zoom in
   */
  private zoomIn(): void {
    const currentLevel = this.mainWindow.webContents.getZoomLevel();
    this.mainWindow.webContents.setZoomLevel(currentLevel + 0.5);
  }

  /**
   * Zoom out
   */
  private zoomOut(): void {
    const currentLevel = this.mainWindow.webContents.getZoomLevel();
    this.mainWindow.webContents.setZoomLevel(currentLevel - 0.5);
  }

  /**
   * Show AI status
   */
  private showAIStatus(): void {
    this.mainWindow.webContents.send('show-ai-status');
  }

  /**
   * Cascade windows
   */
  private cascadeWindows(): void {
    this.mainWindow.webContents.send('cascade-windows');
  }

  /**
   * Tile windows
   */
  private tileWindows(): void {
    this.mainWindow.webContents.send('tile-windows');
  }

  /**
   * Show keyboard shortcuts
   */
  private showKeyboardShortcuts(): void {
    this.mainWindow.webContents.send('show-shortcuts');
  }

  /**
   * Check for updates
   */
  private checkForUpdates(): void {
    this.mainWindow.webContents.send('check-updates');
  }

  /**
   * Show debug information
   */
  private showDebugInfo(): void {
    const debugInfo = {
      version: app.getVersion(),
      platform: process.platform,
      arch: process.arch,
      electron: process.versions.electron,
      node: process.versions.node,
      chrome: process.versions.chrome,
      memory: process.getSystemMemoryInfo(),
      uptime: process.uptime()
    };

    dialog.showMessageBox(this.mainWindow, {
      type: 'info',
      title: 'Debug Information',
      message: 'NexusScan Desktop Debug Info',
      detail: JSON.stringify(debugInfo, null, 2),
      buttons: ['Copy to Clipboard', 'OK']
    }).then((result) => {
      if (result.response === 0) {
        require('electron').clipboard.writeText(JSON.stringify(debugInfo, null, 2));
      }
    });
  }
}