#!/usr/bin/env python3
"""
Master Orchestrator for Hybrid Execution Pipeline
Universal tool orchestration across all environments and execution methods
"""

import asyncio
import logging
import time
import json
from typing import Dict, Any, List, Optional, Union
from dataclasses import dataclass, field
from enum import Enum
from pathlib import Path

from .environment_detector import environment_detector, get_execution_priority
from .hybrid_execution_engine import get_hybrid_engine, ToolExecutionResult, ExecutionResult
from .self_healing_manager import get_self_healing_manager, InstallationStatus
from .docker_tool_manager import DockerToolManager
from .native_tool_manager import NativeToolManager
from .simulation_manager import SimulationManager
from .performance_monitor import get_performance_monitor, MetricType
from .health_checker import get_health_checker, HealthStatus
from .resource_optimizer import get_resource_optimizer, OptimizationStrategy

logger = logging.getLogger(__name__)

class OrchestratorMode(Enum):
    """Orchestrator operation modes"""
    PRODUCTION = "production"          # Real tools, real execution
    TESTING = "testing"               # Real tools, safe targets
    SIMULATION = "simulation"         # Simulated execution
    HYBRID = "hybrid"                 # Mixed real and simulated
    DEVELOPMENT = "development"       # Development mode

class CampaignStatus(Enum):
    """Campaign execution status"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    PARTIAL = "partial"

@dataclass
class ToolTarget:
    """Tool execution target definition"""
    tool_name: str
    target: str
    options: Dict[str, Any] = field(default_factory=dict)
    priority: int = 1
    timeout: int = 300
    required: bool = True

@dataclass
class CampaignDefinition:
    """Security testing campaign definition"""
    campaign_id: str
    name: str
    description: str
    targets: List[str]
    tool_targets: List[ToolTarget]
    mode: OrchestratorMode = OrchestratorMode.PRODUCTION
    parallel_execution: bool = True
    auto_install: bool = True
    max_concurrent_tools: int = 5
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class CampaignResult:
    """Campaign execution result"""
    campaign_id: str
    status: CampaignStatus
    start_time: float
    end_time: Optional[float] = None
    total_execution_time: float = 0.0
    tool_results: Dict[str, ToolExecutionResult] = field(default_factory=dict)
    installed_tools: List[str] = field(default_factory=list)
    failed_installations: List[str] = field(default_factory=list)
    error_message: str = ""
    warnings: List[str] = field(default_factory=list)
    statistics: Dict[str, Any] = field(default_factory=dict)

class MasterOrchestrator:
    """Master orchestrator for hybrid security tool execution"""
    
    def __init__(self):
        self.environment = environment_detector.environment
        self.execution_priority = get_execution_priority()
        
        # Managers
        self.hybrid_engine = None
        self.self_healing_manager = None
        self.docker_manager = None
        self.native_manager = None
        self.simulation_manager = None
        self.performance_monitor = None
        self.health_checker = None
        self.resource_optimizer = None
        
        # Campaign tracking
        self.active_campaigns: Dict[str, CampaignResult] = {}
        self.campaign_history: List[CampaignResult] = []
        
        # Configuration
        self.mode = OrchestratorMode.HYBRID
        self.auto_install_tools = True
        self.max_concurrent_campaigns = 3
        self.statistics_enabled = True
        
        # Performance tracking
        self.orchestrator_stats = {
            'campaigns_executed': 0,
            'tools_executed': 0,
            'successful_executions': 0,
            'failed_executions': 0,
            'auto_installations': 0,
            'average_campaign_time': 0.0,
            'execution_methods': {},
            'environment_info': environment_detector.get_environment_info()
        }
        
        logger.info("🎭 Master Orchestrator initialized")
        logger.info(f"🌍 Environment: {self.environment.value}")
        logger.info(f"📋 Execution priority: {self.execution_priority}")
    
    async def initialize(self):
        """Initialize all managers and components"""
        
        logger.info("🚀 Initializing Master Orchestrator components...")
        
        # Initialize hybrid execution engine
        try:
            self.hybrid_engine = await get_hybrid_engine()
            logger.info("✅ Hybrid execution engine initialized")
        except Exception as e:
            logger.error(f"❌ Failed to initialize hybrid engine: {e}")
        
        # Initialize self-healing manager
        try:
            self.self_healing_manager = await get_self_healing_manager()
            logger.info("✅ Self-healing manager initialized")
        except Exception as e:
            logger.error(f"❌ Failed to initialize self-healing manager: {e}")
        
        # Initialize Docker manager
        try:
            self.docker_manager = DockerToolManager()
            await self.docker_manager.initialize()
            logger.info("✅ Docker manager initialized")
        except Exception as e:
            logger.warning(f"⚠️ Docker manager initialization failed: {e}")
        
        # Initialize native manager
        try:
            self.native_manager = NativeToolManager()
            await self.native_manager.initialize()
            logger.info("✅ Native manager initialized")
        except Exception as e:
            logger.warning(f"⚠️ Native manager initialization failed: {e}")
        
        # Initialize simulation manager
        try:
            self.simulation_manager = SimulationManager()
            logger.info("✅ Simulation manager initialized")
        except Exception as e:
            logger.warning(f"⚠️ Simulation manager initialization failed: {e}")
        
        # Initialize performance monitor
        try:
            self.performance_monitor = await get_performance_monitor()
            logger.info("✅ Performance monitor initialized")
        except Exception as e:
            logger.warning(f"⚠️ Performance monitor initialization failed: {e}")
        
        # Initialize health checker
        try:
            self.health_checker = get_health_checker()
            logger.info("✅ Health checker initialized")
        except Exception as e:
            logger.warning(f"⚠️ Health checker initialization failed: {e}")
        
        # Initialize resource optimizer
        try:
            self.resource_optimizer = await get_resource_optimizer()
            logger.info("✅ Resource optimizer initialized")
        except Exception as e:
            logger.warning(f"⚠️ Resource optimizer initialization failed: {e}")
        
        logger.info("🎯 Master Orchestrator fully initialized")
    
    async def execute_campaign(self, campaign_def: CampaignDefinition) -> CampaignResult:
        """Execute a comprehensive security testing campaign"""
        
        start_time = time.time()
        
        # Check concurrent campaign limit
        if len(self.active_campaigns) >= self.max_concurrent_campaigns:
            return CampaignResult(
                campaign_id=campaign_def.campaign_id,
                status=CampaignStatus.FAILED,
                start_time=start_time,
                error_message="Maximum concurrent campaigns reached"
            )
        
        # Initialize campaign result
        campaign_result = CampaignResult(
            campaign_id=campaign_def.campaign_id,
            status=CampaignStatus.RUNNING,
            start_time=start_time
        )
        
        self.active_campaigns[campaign_def.campaign_id] = campaign_result
        
        logger.info(f"🚀 Starting campaign: {campaign_def.name} ({campaign_def.campaign_id})")
        logger.info(f"📊 Targets: {len(campaign_def.targets)}, Tools: {len(campaign_def.tool_targets)}")
        
        try:
            # Phase 1: Tool Availability Check and Auto-Installation
            if campaign_def.auto_install:
                await self._ensure_tools_available(campaign_def, campaign_result)
            
            # Phase 2: Execution Strategy Planning
            execution_plan = await self._plan_execution(campaign_def, campaign_result)
            
            # Phase 3: Campaign Execution
            if campaign_def.parallel_execution:
                await self._execute_parallel_campaign(campaign_def, campaign_result, execution_plan)
            else:
                await self._execute_sequential_campaign(campaign_def, campaign_result, execution_plan)
            
            # Phase 4: Results Analysis and Statistics
            await self._analyze_campaign_results(campaign_def, campaign_result)
            
            # Mark campaign as completed
            campaign_result.status = CampaignStatus.COMPLETED
            campaign_result.end_time = time.time()
            campaign_result.total_execution_time = campaign_result.end_time - campaign_result.start_time
            
            logger.info(f"✅ Campaign completed: {campaign_def.name} in {campaign_result.total_execution_time:.2f}s")
            
        except Exception as e:
            campaign_result.status = CampaignStatus.FAILED
            campaign_result.error_message = str(e)
            campaign_result.end_time = time.time()
            campaign_result.total_execution_time = campaign_result.end_time - campaign_result.start_time
            
            logger.error(f"❌ Campaign failed: {campaign_def.name} - {e}")
        
        finally:
            # Move to history and cleanup
            self.active_campaigns.pop(campaign_def.campaign_id, None)
            self.campaign_history.append(campaign_result)
            
            # Update orchestrator statistics
            self._update_orchestrator_stats(campaign_result)
        
        return campaign_result
    
    async def _ensure_tools_available(self, campaign_def: CampaignDefinition, campaign_result: CampaignResult):
        """Ensure all required tools are available, auto-installing if needed"""
        
        logger.info("🔧 Checking tool availability and auto-installing missing tools...")
        
        required_tools = list(set(tt.tool_name for tt in campaign_def.tool_targets))
        
        for tool_name in required_tools:
            try:
                # Check if tool is already available
                if self.native_manager and self.native_manager.is_tool_available(tool_name):
                    logger.info(f"✅ {tool_name} already available")
                    continue
                
                # Check Docker availability
                if self.docker_manager and tool_name in self.docker_manager.get_available_tools():
                    logger.info(f"✅ {tool_name} available via Docker")
                    continue
                
                # Auto-install if enabled
                if self.auto_install_tools and self.self_healing_manager:
                    logger.info(f"🔧 Auto-installing {tool_name}...")
                    
                    install_result = await self.self_healing_manager.auto_install_tool(tool_name)
                    
                    if install_result.status == InstallationStatus.SUCCESS:
                        campaign_result.installed_tools.append(tool_name)
                        logger.info(f"✅ Successfully installed {tool_name}")
                    else:
                        campaign_result.failed_installations.append(tool_name)
                        logger.warning(f"⚠️ Failed to install {tool_name}: {install_result.error_message}")
                        
                        # Check if tool is required
                        tool_target = next((tt for tt in campaign_def.tool_targets if tt.tool_name == tool_name), None)
                        if tool_target and tool_target.required:
                            campaign_result.warnings.append(f"Required tool {tool_name} could not be installed")
                
            except Exception as e:
                logger.error(f"❌ Tool availability check failed for {tool_name}: {e}")
                campaign_result.warnings.append(f"Tool check failed for {tool_name}: {str(e)}")
    
    async def _plan_execution(self, campaign_def: CampaignDefinition, campaign_result: CampaignResult) -> Dict[str, Any]:
        """Plan optimal execution strategy"""
        
        execution_plan = {
            'tool_execution_order': [],
            'parallel_groups': [],
            'execution_methods': {},
            'estimated_time': 0
        }
        
        # Sort tools by priority
        sorted_tools = sorted(campaign_def.tool_targets, key=lambda x: x.priority, reverse=True)
        
        # Determine execution method for each tool
        for tool_target in sorted_tools:
            best_method = await self._determine_best_execution_method(tool_target.tool_name)
            execution_plan['execution_methods'][tool_target.tool_name] = best_method
            
            # Estimate execution time
            base_time = tool_target.timeout
            target_count = len(campaign_def.targets)
            
            if campaign_def.parallel_execution:
                estimated_time = base_time * (target_count / campaign_def.max_concurrent_tools)
            else:
                estimated_time = base_time * target_count
            
            execution_plan['estimated_time'] += estimated_time
        
        # Create execution groups for parallel execution
        if campaign_def.parallel_execution:
            high_priority = [tt for tt in sorted_tools if tt.priority >= 3]
            medium_priority = [tt for tt in sorted_tools if tt.priority == 2]
            low_priority = [tt for tt in sorted_tools if tt.priority <= 1]
            
            execution_plan['parallel_groups'] = [high_priority, medium_priority, low_priority]
        else:
            execution_plan['tool_execution_order'] = sorted_tools
        
        logger.info(f"📋 Execution plan created: {len(sorted_tools)} tools, estimated time: {execution_plan['estimated_time']:.1f}s")
        
        return execution_plan
    
    async def _determine_best_execution_method(self, tool_name: str) -> str:
        """Determine the best execution method for a tool"""
        
        # Check availability in order of preference
        if self.docker_manager and tool_name in self.docker_manager.get_available_tools():
            return "docker"
        elif self.native_manager and self.native_manager.is_tool_available(tool_name):
            return "native"
        elif self.hybrid_engine:
            return "hybrid"
        else:
            return "simulation"
    
    async def _execute_parallel_campaign(
        self, 
        campaign_def: CampaignDefinition, 
        campaign_result: CampaignResult, 
        execution_plan: Dict[str, Any]
    ):
        """Execute campaign with parallel tool execution"""
        
        logger.info("🔄 Executing campaign in parallel mode...")
        
        for group_index, tool_group in enumerate(execution_plan['parallel_groups']):
            if not tool_group:
                continue
            
            logger.info(f"📦 Executing tool group {group_index + 1}: {[tt.tool_name for tt in tool_group]}")
            
            # Create tasks for parallel execution
            tasks = []
            
            for tool_target in tool_group:
                for target in campaign_def.targets:
                    task = self._execute_tool_on_target(tool_target, target, campaign_result)
                    tasks.append(task)
            
            # Execute tools in parallel with concurrency limit
            semaphore = asyncio.Semaphore(campaign_def.max_concurrent_tools)
            
            async def limited_task(task):
                async with semaphore:
                    return await task
            
            limited_tasks = [limited_task(task) for task in tasks]
            
            # Wait for group completion
            await asyncio.gather(*limited_tasks, return_exceptions=True)
            
            logger.info(f"✅ Tool group {group_index + 1} completed")
    
    async def _execute_sequential_campaign(
        self, 
        campaign_def: CampaignDefinition, 
        campaign_result: CampaignResult, 
        execution_plan: Dict[str, Any]
    ):
        """Execute campaign with sequential tool execution"""
        
        logger.info("🔄 Executing campaign in sequential mode...")
        
        for tool_target in execution_plan['tool_execution_order']:
            logger.info(f"🔧 Executing {tool_target.tool_name} on {len(campaign_def.targets)} targets")
            
            for target in campaign_def.targets:
                await self._execute_tool_on_target(tool_target, target, campaign_result)
            
            logger.info(f"✅ {tool_target.tool_name} execution completed")
    
    async def _execute_tool_on_target(
        self, 
        tool_target: ToolTarget, 
        target: str, 
        campaign_result: CampaignResult
    ):
        """Execute a specific tool on a specific target"""
        
        result_key = f"{tool_target.tool_name}_{target}"
        
        try:
            # Optimize execution with resource optimizer
            if self.resource_optimizer:
                optimization_info = await self.resource_optimizer.optimize_tool_execution(
                    tool_target.tool_name,
                    target,
                    tool_target.options
                )
                optimized_options = optimization_info.get('options', tool_target.options)
            else:
                optimized_options = tool_target.options
            
            if self.hybrid_engine:
                # Use hybrid execution engine
                result = await self.hybrid_engine.execute_tool(
                    tool_target.tool_name,
                    target,
                    optimized_options,
                    tool_target.timeout
                )
            else:
                # Fallback to simulation
                result = await self.simulation_manager.simulate_tool(
                    tool_target.tool_name,
                    target,
                    optimized_options
                )
            
            # Record performance metrics
            if self.performance_monitor:
                self.performance_monitor.record_tool_execution(tool_target.tool_name, result)
            
            # Cache result if optimization enabled
            if self.resource_optimizer and 'cache_key' in optimization_info:
                cache_key = optimization_info['cache_key']
                self.resource_optimizer.cache_execution_result(cache_key, {
                    'result': result,
                    'timestamp': time.time()
                })
            
            campaign_result.tool_results[result_key] = result
            
            if result.status == ExecutionResult.SUCCESS:
                logger.info(f"✅ {tool_target.tool_name} on {target}: SUCCESS")
            else:
                logger.warning(f"⚠️ {tool_target.tool_name} on {target}: {result.status.value}")
                
        except Exception as e:
            # Create error result
            error_result = ToolExecutionResult(
                status=ExecutionResult.FAILED,
                method="error",
                error=str(e)
            )
            campaign_result.tool_results[result_key] = error_result
            logger.error(f"❌ {tool_target.tool_name} on {target}: EXCEPTION - {e}")
    
    async def _analyze_campaign_results(self, campaign_def: CampaignDefinition, campaign_result: CampaignResult):
        """Analyze campaign results and generate statistics"""
        
        logger.info("📊 Analyzing campaign results...")
        
        # Calculate statistics
        total_executions = len(campaign_result.tool_results)
        successful_executions = len([r for r in campaign_result.tool_results.values() if r.status == ExecutionResult.SUCCESS])
        failed_executions = total_executions - successful_executions
        
        # Method usage statistics
        method_usage = {}
        for result in campaign_result.tool_results.values():
            method = result.method
            if method not in method_usage:
                method_usage[method] = 0
            method_usage[method] += 1
        
        # Tool-specific statistics
        tool_stats = {}
        for tool_target in campaign_def.tool_targets:
            tool_name = tool_target.tool_name
            tool_results = [r for key, r in campaign_result.tool_results.items() if key.startswith(tool_name)]
            
            tool_stats[tool_name] = {
                'total_executions': len(tool_results),
                'successful_executions': len([r for r in tool_results if r.status == ExecutionResult.SUCCESS]),
                'average_execution_time': sum(r.execution_time for r in tool_results) / len(tool_results) if tool_results else 0,
                'methods_used': list(set(r.method for r in tool_results))
            }
        
        # Performance metrics
        performance_metrics = {
            'total_execution_time': campaign_result.total_execution_time,
            'average_tool_execution_time': sum(r.execution_time for r in campaign_result.tool_results.values()) / total_executions if total_executions > 0 else 0,
            'parallelization_efficiency': 1.0 if campaign_def.parallel_execution else 0.5,
            'auto_installation_success_rate': len(campaign_result.installed_tools) / (len(campaign_result.installed_tools) + len(campaign_result.failed_installations)) if (campaign_result.installed_tools or campaign_result.failed_installations) else 1.0
        }
        
        campaign_result.statistics = {
            'execution_summary': {
                'total_executions': total_executions,
                'successful_executions': successful_executions,
                'failed_executions': failed_executions,
                'success_rate': (successful_executions / total_executions) * 100 if total_executions > 0 else 0
            },
            'method_usage': method_usage,
            'tool_statistics': tool_stats,
            'performance_metrics': performance_metrics,
            'environment_info': {
                'environment': self.environment.value,
                'execution_priority': self.execution_priority,
                'auto_install_enabled': campaign_def.auto_install,
                'parallel_execution': campaign_def.parallel_execution
            }
        }
        
        logger.info(f"📈 Campaign analysis complete: {successful_executions}/{total_executions} successful executions")
    
    def _update_orchestrator_stats(self, campaign_result: CampaignResult):
        """Update global orchestrator statistics"""
        
        self.orchestrator_stats['campaigns_executed'] += 1
        
        for result in campaign_result.tool_results.values():
            self.orchestrator_stats['tools_executed'] += 1
            
            if result.status == ExecutionResult.SUCCESS:
                self.orchestrator_stats['successful_executions'] += 1
            else:
                self.orchestrator_stats['failed_executions'] += 1
            
            # Track method usage
            method = result.method
            if method not in self.orchestrator_stats['execution_methods']:
                self.orchestrator_stats['execution_methods'][method] = 0
            self.orchestrator_stats['execution_methods'][method] += 1
        
        # Update average campaign time
        total_campaigns = self.orchestrator_stats['campaigns_executed']
        current_avg = self.orchestrator_stats['average_campaign_time']
        new_time = campaign_result.total_execution_time
        
        self.orchestrator_stats['average_campaign_time'] = (
            (current_avg * (total_campaigns - 1) + new_time) / total_campaigns
        )
        
        # Track auto-installations
        self.orchestrator_stats['auto_installations'] += len(campaign_result.installed_tools)
    
    async def execute_single_tool(
        self, 
        tool_name: str, 
        target: str, 
        options: Dict[str, Any] = None,
        auto_install: bool = True
    ) -> ToolExecutionResult:
        """Execute a single tool (convenience method)"""
        
        # Create simple campaign
        tool_target = ToolTarget(
            tool_name=tool_name,
            target=target,
            options=options or {}
        )
        
        campaign_def = CampaignDefinition(
            campaign_id=f"single_{tool_name}_{int(time.time())}",
            name=f"Single {tool_name} execution",
            description=f"Single tool execution: {tool_name} on {target}",
            targets=[target],
            tool_targets=[tool_target],
            auto_install=auto_install,
            parallel_execution=False
        )
        
        campaign_result = await self.execute_campaign(campaign_def)
        
        # Return the single tool result
        result_key = f"{tool_name}_{target}"
        return campaign_result.tool_results.get(result_key, ToolExecutionResult(
            status=ExecutionResult.FAILED,
            method="orchestrator",
            error="Tool execution not found in campaign results"
        ))
    
    def get_campaign_status(self, campaign_id: str) -> Optional[CampaignResult]:
        """Get status of active or completed campaign"""
        
        # Check active campaigns
        if campaign_id in self.active_campaigns:
            return self.active_campaigns[campaign_id]
        
        # Check campaign history
        for campaign in self.campaign_history:
            if campaign.campaign_id == campaign_id:
                return campaign
        
        return None
    
    def get_active_campaigns(self) -> List[CampaignResult]:
        """Get all active campaigns"""
        return list(self.active_campaigns.values())
    
    def get_orchestrator_stats(self) -> Dict[str, Any]:
        """Get comprehensive orchestrator statistics"""
        return self.orchestrator_stats.copy()
    
    async def system_health_check(self) -> Dict[str, Any]:
        """Comprehensive system health check"""
        
        health_report = {
            'orchestrator_status': 'healthy',
            'environment': self.environment.value,
            'execution_priority': self.execution_priority,
            'active_campaigns': len(self.active_campaigns),
            'managers': {},
            'statistics': self.orchestrator_stats,
            'capabilities': {},
            'recommendations': []
        }
        
        # Check manager health
        if self.hybrid_engine:
            health_report['managers']['hybrid_engine'] = 'available'
            health_report['capabilities']['hybrid_execution'] = True
        else:
            health_report['managers']['hybrid_engine'] = 'unavailable'
            health_report['capabilities']['hybrid_execution'] = False
        
        if self.self_healing_manager:
            health_report['managers']['self_healing'] = 'available'
            health_report['capabilities']['auto_installation'] = True
        else:
            health_report['managers']['self_healing'] = 'unavailable'
            health_report['capabilities']['auto_installation'] = False
        
        if self.docker_manager and self.docker_manager.docker_available:
            health_report['managers']['docker'] = 'available'
            health_report['capabilities']['containerized_execution'] = True
        else:
            health_report['managers']['docker'] = 'unavailable'
            health_report['capabilities']['containerized_execution'] = False
        
        if self.native_manager:
            health_report['managers']['native'] = 'available'
            health_report['capabilities']['native_execution'] = True
        else:
            health_report['managers']['native'] = 'unavailable'
            health_report['capabilities']['native_execution'] = False
        
        if self.simulation_manager:
            health_report['managers']['simulation'] = 'available'
            health_report['capabilities']['simulation'] = True
        else:
            health_report['managers']['simulation'] = 'unavailable'
            health_report['capabilities']['simulation'] = False
        
        # Performance monitoring status
        if self.performance_monitor:
            health_report['managers']['performance_monitor'] = 'available'
            health_report['capabilities']['performance_monitoring'] = True
        else:
            health_report['managers']['performance_monitor'] = 'unavailable'
            health_report['capabilities']['performance_monitoring'] = False
        
        # Health checking status
        if self.health_checker:
            health_report['managers']['health_checker'] = 'available'
            health_report['capabilities']['health_checking'] = True
        else:
            health_report['managers']['health_checker'] = 'unavailable'
            health_report['capabilities']['health_checking'] = False
        
        # Resource optimization status
        if self.resource_optimizer:
            health_report['managers']['resource_optimizer'] = 'available'
            health_report['capabilities']['resource_optimization'] = True
            
            # Include optimization status
            optimization_status = self.resource_optimizer.get_resource_status()
            health_report['optimization'] = {
                'strategy': optimization_status['current_strategy'],
                'profile': optimization_status['current_profile']['name'],
                'cache_hit_rate': optimization_status['cache_stats']['cache_hit_rate']
            }
        else:
            health_report['managers']['resource_optimizer'] = 'unavailable'
            health_report['capabilities']['resource_optimization'] = False
        
        # Generate recommendations
        if not health_report['capabilities']['containerized_execution']:
            health_report['recommendations'].append("Install Docker for containerized tool execution")
        
        if not health_report['capabilities']['auto_installation']:
            health_report['recommendations'].append("Enable self-healing manager for automatic tool installation")
        
        if not health_report['capabilities']['performance_monitoring']:
            health_report['recommendations'].append("Enable performance monitoring for better insights")
        
        if not health_report['capabilities']['resource_optimization']:
            health_report['recommendations'].append("Enable resource optimization for better performance")
        
        if self.orchestrator_stats['failed_executions'] > self.orchestrator_stats['successful_executions']:
            health_report['recommendations'].append("High failure rate detected - check tool availability and system health")
        
        # Add optimization recommendations if available
        if self.resource_optimizer:
            opt_recommendations = self.resource_optimizer.get_optimization_recommendations()
            health_report['recommendations'].extend(opt_recommendations[:3])  # Top 3 optimization recommendations
        
        return health_report
    
    async def generate_performance_report(self, hours: float = 1.0) -> Dict[str, Any]:
        """Generate comprehensive performance report"""
        
        if not self.performance_monitor:
            return {
                'error': 'Performance monitoring not available',
                'recommendations': ['Enable performance monitoring for detailed reports']
            }
        
        # Generate performance report
        perf_report = await self.performance_monitor.generate_performance_report(hours)
        
        # Add orchestrator-specific metrics
        orchestrator_metrics = {
            'campaigns_executed': self.orchestrator_stats['campaigns_executed'],
            'active_campaigns': len(self.active_campaigns),
            'campaign_history_size': len(self.campaign_history),
            'average_campaign_time': self.orchestrator_stats['average_campaign_time'],
            'tools_executed': self.orchestrator_stats['tools_executed'],
            'execution_methods': self.orchestrator_stats['execution_methods']
        }
        
        # Combine reports
        combined_report = {
            'report_id': perf_report.report_id,
            'generated_at': perf_report.generated_at,
            'time_period_hours': hours,
            'orchestrator_metrics': orchestrator_metrics,
            'system_overview': perf_report.system_overview,
            'tool_performance': perf_report.tool_performance,
            'execution_methods': perf_report.execution_methods,
            'alerts': [
                {
                    'level': alert.level.value,
                    'message': alert.message,
                    'timestamp': alert.timestamp,
                    'recommendations': alert.recommendations
                }
                for alert in perf_report.alerts
            ],
            'recommendations': perf_report.recommendations,
            'trends': perf_report.trends
        }
        
        return combined_report
    
    async def perform_comprehensive_health_check(self) -> Dict[str, Any]:
        """Perform comprehensive health check of entire system"""
        
        if not self.health_checker:
            return {
                'error': 'Health checking not available',
                'overall_status': 'unknown',
                'recommendations': ['Enable health checking for comprehensive diagnostics']
            }
        
        # Perform health check
        health_report = await self.health_checker.perform_comprehensive_health_check()
        
        # Add orchestrator-specific health information
        orchestrator_health = {
            'active_campaigns': len(self.active_campaigns),
            'total_campaigns_executed': self.orchestrator_stats['campaigns_executed'],
            'success_rate': (
                self.orchestrator_stats['successful_executions'] / 
                max(1, self.orchestrator_stats['tools_executed'])
            ) * 100,
            'average_campaign_duration': self.orchestrator_stats['average_campaign_time'],
            'environment': self.environment.value,
            'execution_priority': self.execution_priority
        }
        
        # Combine health reports
        combined_health = {
            'overall_status': health_report.overall_status.value,
            'timestamp': health_report.timestamp,
            'orchestrator_health': orchestrator_health,
            'component_checks': [
                {
                    'component': check.component.value,
                    'status': check.status.value,
                    'message': check.message,
                    'execution_time': check.execution_time,
                    'recommendations': check.recommendations
                }
                for check in health_report.checks
            ],
            'summary': health_report.summary,
            'recommendations': health_report.recommendations,
            'performance_metrics': health_report.performance_metrics
        }
        
        return combined_health
    
    async def optimize_system_performance(self, strategy: str = "balanced") -> Dict[str, Any]:
        """Optimize system performance using specified strategy"""
        
        if not self.resource_optimizer:
            return {
                'error': 'Resource optimization not available',
                'recommendations': ['Enable resource optimization for performance tuning']
            }
        
        # Map strategy names to optimization strategies
        strategy_mapping = {
            'conservative': OptimizationStrategy.CONSERVATIVE,
            'balanced': OptimizationStrategy.BALANCED,
            'aggressive': OptimizationStrategy.AGGRESSIVE,
            'performance': OptimizationStrategy.PERFORMANCE,
            'memory_efficient': OptimizationStrategy.MEMORY_EFFICIENT,
            'cpu_efficient': OptimizationStrategy.CPU_EFFICIENT
        }
        
        optimization_strategy = strategy_mapping.get(strategy.lower(), OptimizationStrategy.BALANCED)
        
        # Apply optimization strategy
        old_strategy = self.resource_optimizer.current_strategy
        self.resource_optimizer.set_optimization_strategy(optimization_strategy)
        
        # Get current resource status
        resource_status = self.resource_optimizer.get_resource_status()
        
        # Generate optimization report
        optimization_report = {
            'action': 'optimization_applied',
            'old_strategy': old_strategy.value,
            'new_strategy': optimization_strategy.value,
            'resource_status': resource_status,
            'recommendations': self.resource_optimizer.get_optimization_recommendations(),
            'expected_benefits': self._get_strategy_benefits(optimization_strategy)
        }
        
        logger.info(f"🔧 System optimization applied: {old_strategy.value} -> {optimization_strategy.value}")
        
        return optimization_report
    
    def _get_strategy_benefits(self, strategy: OptimizationStrategy) -> List[str]:
        """Get expected benefits for optimization strategy"""
        
        benefits = {
            OptimizationStrategy.CONSERVATIVE: [
                "Lower resource usage",
                "Improved system stability",
                "Reduced risk of system overload"
            ],
            OptimizationStrategy.BALANCED: [
                "Good balance of performance and resource usage",
                "Suitable for most workloads",
                "Automatic adaptation to system conditions"
            ],
            OptimizationStrategy.AGGRESSIVE: [
                "Maximum performance",
                "Higher concurrency",
                "Faster campaign execution"
            ],
            OptimizationStrategy.PERFORMANCE: [
                "Optimized for speed",
                "Maximum tool concurrency",
                "Reduced execution times"
            ],
            OptimizationStrategy.MEMORY_EFFICIENT: [
                "Lower memory usage",
                "Better for memory-constrained systems",
                "Reduced cache overhead"
            ],
            OptimizationStrategy.CPU_EFFICIENT: [
                "Lower CPU usage",
                "Better for CPU-constrained systems",
                "Improved thermal management"
            ]
        }
        
        return benefits.get(strategy, ["Improved system performance"])
    
    async def get_real_time_dashboard(self) -> Dict[str, Any]:
        """Get real-time dashboard data"""
        
        dashboard_data = {
            'timestamp': time.time(),
            'orchestrator_status': {
                'active_campaigns': len(self.active_campaigns),
                'total_campaigns': self.orchestrator_stats['campaigns_executed'],
                'tools_executed': self.orchestrator_stats['tools_executed'],
                'success_rate': (
                    self.orchestrator_stats['successful_executions'] / 
                    max(1, self.orchestrator_stats['tools_executed'])
                ) * 100,
                'environment': self.environment.value
            },
            'system_resources': {},
            'performance_metrics': {},
            'optimization_status': {},
            'recent_alerts': [],
            'quick_health_status': 'unknown'
        }
        
        # Get system resources
        try:
            import psutil
            dashboard_data['system_resources'] = {
                'cpu_percent': psutil.cpu_percent(),
                'memory_percent': psutil.virtual_memory().percent,
                'disk_usage_percent': psutil.disk_usage('/').percent if hasattr(psutil, 'disk_usage') else 0
            }
        except Exception:
            pass
        
        # Get performance metrics
        if self.performance_monitor:
            dashboard_data['performance_metrics'] = self.performance_monitor.get_real_time_metrics()
        
        # Get optimization status
        if self.resource_optimizer:
            opt_status = self.resource_optimizer.get_resource_status()
            dashboard_data['optimization_status'] = {
                'strategy': opt_status['current_strategy'],
                'profile': opt_status['current_profile']['name'],
                'cache_hit_rate': opt_status['cache_stats']['cache_hit_rate']
            }
        
        # Get quick health status
        if self.health_checker:
            quick_health = await self.health_checker.quick_health_check()
            dashboard_data['quick_health_status'] = quick_health['overall_status']
            dashboard_data['recent_alerts'] = quick_health.get('critical_issues', []) + quick_health.get('warnings', [])
        
        return dashboard_data

# Global master orchestrator
master_orchestrator = None

async def get_master_orchestrator() -> MasterOrchestrator:
    """Get or create master orchestrator"""
    global master_orchestrator
    
    if master_orchestrator is None:
        master_orchestrator = MasterOrchestrator()
        await master_orchestrator.initialize()
    
    return master_orchestrator

async def execute_security_campaign(campaign_def: CampaignDefinition) -> CampaignResult:
    """Convenience function to execute security campaign"""
    orchestrator = await get_master_orchestrator()
    return await orchestrator.execute_campaign(campaign_def)